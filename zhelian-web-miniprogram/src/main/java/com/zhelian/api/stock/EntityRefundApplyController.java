package com.zhelian.api.stock;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.psi.form.input.*;
import com.zhelian.service.psi.entityRefundProductApply.EntityRefundProductApplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 功能描述:
 * <>
 *
 * @return:
 * @exception:
 * @author: WangXianXian
 * @date: 2020/3/12 1:49 PM
 */
@RestController
@RequestMapping(value = "/api/entityRefundApply")
public class EntityRefundApplyController {

    @Autowired
    EntityRefundProductApplyService entityRefundProductApplyService;

    @RequestMapping(value = "/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody InventoryRefundApplyQueryForm form) {
        return entityRefundProductApplyService.entityRefundApplyList(form);
    }

    @RequestMapping(value = "/info", method = {RequestMethod.POST})
    public BaseOutput info(@RequestBody InventoryApplyInfoQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("门店退货单号不能为空");

        return entityRefundProductApplyService.entityRefundApplyInfo(form.ID);
    }

    @RequestMapping(value = "/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody InventoryRefundApplyAddForm form) {
        if (form.InventoryRefundApplyDetail == null || form.InventoryRefundApplyDetail.size() == 0) {
            return BaseOutput.failed("退货单产品明细不能为空");
        }
        if (form.OutboundEntityID == null) {
            return BaseOutput.failed("申请仓库不能为空");
        }
        for (InventoryRefundApplyDetailAddForm inventoryRefundApplyDetailAddForm : form.InventoryRefundApplyDetail) {
            if (inventoryRefundApplyDetailAddForm.ProductID == null) {
                return BaseOutput.failed("产品编号不能为空");
            }
            if (inventoryRefundApplyDetailAddForm.UnitID == null) {
                return BaseOutput.failed("产品单位不能为空");
            }
            if (inventoryRefundApplyDetailAddForm.ApplyQuantity <= 0 || inventoryRefundApplyDetailAddForm.ApplyMinimumUnitQuantity <= 0) {
                return BaseOutput.failed("退货数量必须大于0");
            }
        }
        return entityRefundProductApplyService.create(form);
    }

    @RequestMapping(value = "/approved", method = {RequestMethod.POST})
    public BaseOutput approved(@RequestBody InventoryRefundApplyApprovedForm form) {
        if (form.ID == null) {
            return BaseOutput.failed("退货单ID不能为空");
        }
        if (form.BillStatus == null) {
            return BaseOutput.failed("审核状态不能为空");
        }
        if (form.BillStatus.equals("20")) {
            if (form.InventoryRefundApplyDetail == null || form.InventoryRefundApplyDetail.size() == 0) {
                return BaseOutput.failed("产品明细不能为空");
            }
            if (form.InboundEntityID == null) {
                return BaseOutput.failed("入库仓库不能为空");
            }
            if (form.ApprovedTotalAmount == null) {
                return BaseOutput.failed("单据合计金额不能为空");
            }
            for (InventoryRefundApplyApprovedDetailForm inventoryRefundApplyApprovedDetailForm : form.InventoryRefundApplyDetail) {
                if (inventoryRefundApplyApprovedDetailForm.ID == null) {
                    return BaseOutput.failed("明细ID不能为空");
                }
                if (inventoryRefundApplyApprovedDetailForm.ApprovedPrice == null) {
                    return BaseOutput.failed("产品单价不能为空");
                }
                if (inventoryRefundApplyApprovedDetailForm.ApprovedTotalAmount == null) {
                    return BaseOutput.failed("合计金额不能为空");
                }
            }
        }
        return entityRefundProductApplyService.approved(form);
    }

    @RequestMapping(value = "/pay", method = {RequestMethod.POST})
    public BaseOutput pay(@RequestBody InventoryRefundApplyPayForm form) {
        if (form.ID == null) {
            return BaseOutput.failed("退货单ID不能为空");
        }
        if (form.BillStatus == null) {
            return BaseOutput.failed("审核状态不能为空");
        }

        return entityRefundProductApplyService.pay(form);
    }


    @RequestMapping(value = "/outbound", method = {RequestMethod.POST})
    public BaseOutput outbound(@RequestBody InventoryRefundApplyOutboundForm form) {
        if (form.InventoryRefundApplyDetail == null || form.InventoryRefundApplyDetail.size() == 0) {
            return BaseOutput.failed("产品明细不能为空");
        }
        if (form.OutboundTotalAmount == null) {
            return BaseOutput.failed("单据合计金额不能为空");
        }
        for (InventoryRefundApplyOutboundDetailForm inventoryApplyOutboundDetailForm : form.InventoryRefundApplyDetail) {
            if (inventoryApplyOutboundDetailForm.ID == null) {
                return BaseOutput.failed("明细ID不能为空");
            }
            if (inventoryApplyOutboundDetailForm.OutboundPrice == null) {
                return BaseOutput.failed("产品单价不能为空");
            }
            if (inventoryApplyOutboundDetailForm.OutboundTotalAmount == null) {
                return BaseOutput.failed("合计金额不能为空");
            }
        }
        return entityRefundProductApplyService.outbound(form);
    }

    @RequestMapping(value = "/inbound", method = {RequestMethod.POST})
    public BaseOutput inbound(@RequestBody InventoryRefundApplyInboundForm form) {
        if (form.InventoryRefundApplyDetail == null || form.InventoryRefundApplyDetail.size() == 0) {
            return BaseOutput.failed("产品明细不能为空");
        }
        if (form.InboundTotalAmount == null) {
            return BaseOutput.failed("单据合计金额不能为空");
        }
        for (InventoryRefundApplyInboundDetailForm inventoryRefundApplyInboundDetailForm : form.InventoryRefundApplyDetail) {
            if (inventoryRefundApplyInboundDetailForm.ID == null) {
                return BaseOutput.failed("明细ID不能为空");
            }
            if (inventoryRefundApplyInboundDetailForm.InboundPrice == null) {
                return BaseOutput.failed("产品单价不能为空");
            }
            if (inventoryRefundApplyInboundDetailForm.InboundTotalAmount == null) {
                return BaseOutput.failed("合计金额不能为空");
            }
        }
        return entityRefundProductApplyService.inbound(form);
    }

    @RequestMapping(value = "/cancel", method = {RequestMethod.POST})
    public BaseOutput cancel(@RequestBody InventoryRefundApplyCancelForm form) {
        if (form.ID == null) {
            return BaseOutput.failed("门店退货单ID不能为空");
        }
        return entityRefundProductApplyService.cancel(form);
    }

    @RequestMapping(value = "/cancelRejectApply", method = {RequestMethod.POST})
    public BaseOutput cancelRejectApply(@RequestBody InventoryRefundApplyCancelForm form) {
        if (form.ID == null) {
            return BaseOutput.failed("门店退货单ID不能为空");
        }
        return entityRefundProductApplyService.cancelRejectApply(form);
    }

    @RequestMapping(value = "/billStatusNumber", method = {RequestMethod.POST})
    public BaseOutput billStatusNumber(@RequestBody InventoryRefundApplyBillStatusNumberForm form) {
        return entityRefundProductApplyService.billStatusNumber(form);
    }

    @RequestMapping(value = "/detail", method = {RequestMethod.POST})
    public BaseOutput detail(@RequestBody InventoryRefundApplyDetailQueryForm form) {
        return entityRefundProductApplyService.detail(form);
    }

//    @PostMapping(value = "/excel")
//    public BaseOutput excelEntitySaleDetailStatement(@RequestBody InventoryApplyDetailQueryForm form, HttpServletResponse response) {
//        if (form.StartTime == null || form.EndTime == null) {
//            return BaseOutput.failed("请选择查询的时间段");
//        }
//        return entityRefundProductApplyService.excelInventoryApplyStatement(form,response);
//    }

    @PostMapping(value = "/prepay")
    public BaseOutput prepay(@RequestBody InventoryApplyDetailQueryForm form) {
        if (form.EntityID == null) {
            return BaseOutput.failed("门店不能为空");
        }
        return entityRefundProductApplyService.getEntityPrepay(form);
    }

    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody InventoryRefundApplyUpdateForm form) {
        if (form.ID == null || form.ID == null) {
            return BaseOutput.failed("订单编号不能为空");
        }
        return entityRefundProductApplyService.update(form);
    }
}