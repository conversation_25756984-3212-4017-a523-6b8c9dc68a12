package com.zhelian.api.performance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.miniprogram.form.input.EmployeePerformanceQueryForm;
import com.zhelian.model.miniprogram.form.input.PerformanceQueryForm;
import com.zhelian.service.miniprogram.performance.EmployeePerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api/employeePerformance")
public class EmployeePerformanceController {

    @Autowired
    EmployeePerformanceService employeePerformance;

    @RequestMapping(value = "/performance", method = {RequestMethod.POST})
    public BaseOutput employeePerformance(@RequestBody PerformanceQueryForm form) {
        return employeePerformance.employeePerformance(form);
    }

    @RequestMapping(value = "/saleBill", method = {RequestMethod.POST})
    public BaseOutput saleBill(@RequestBody EmployeePerformanceQueryForm form) {
        return employeePerformance.getSaleBill(form);
    }

    /**
     * 2021-03-19
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/allPerformance", method = {RequestMethod.POST})
    public BaseOutput emplpyeeAllPerformance(@RequestBody PerformanceQueryForm form) {
        return employeePerformance.emplpyeeAllPerformance(form);
    }

    /**
     * 2021-03-19
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/salePerformance", method = {RequestMethod.POST})
    public BaseOutput emplpyeeSalePerformance(@RequestBody PerformanceQueryForm form) {
        return employeePerformance.emplpyeeSalePerformance(form);
    }

    /**
     * 2021-03-19
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/treatPerformance", method = {RequestMethod.POST})
    public BaseOutput emplpyeeTreatPerformance(@RequestBody PerformanceQueryForm form) {
        return employeePerformance.emplpyeeTreatPerformance(form);
    }
}