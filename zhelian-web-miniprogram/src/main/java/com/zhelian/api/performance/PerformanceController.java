package com.zhelian.api.performance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.miniprogram.form.input.AllEntityPerformanceQueryForm;
import com.zhelian.model.miniprogram.form.input.PerformanceQueryForm;
import com.zhelian.service.miniprogram.performance.PerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class PerformanceController {

    @Autowired
    PerformanceService performanceService;

    @RequestMapping(value = "/performance/employeePerformance", method = {RequestMethod.POST})
    public BaseOutput employeePerformance(@RequestBody PerformanceQueryForm form) {
        return performanceService.employeePerformance(form);
    }

    @RequestMapping(value = "/performance/personPerformance", method = {RequestMethod.POST})
    public BaseOutput personPerformance(@RequestBody PerformanceQueryForm form) {
        return performanceService.getPersonPerformance(form);
    }

    @RequestMapping(value = "/performance/entityPerformance", method = {RequestMethod.POST})
    public BaseOutput entityPerformance(@RequestBody PerformanceQueryForm form) {
        return performanceService.getEntityPerformance(form);
    }

    @RequestMapping(value = "/performance/entityAllPerformance", method = {RequestMethod.POST})
    public BaseOutput entityAllPerformance(@RequestBody AllEntityPerformanceQueryForm form) {
        return performanceService.getEntityAllPerformance(form);
    }

    @RequestMapping(value = "/performance/entityAllPerformanceDetail", method = {RequestMethod.POST})
    public BaseOutput entityAllPerformanceDetail(@RequestBody AllEntityPerformanceQueryForm form) {
        return performanceService.getEntityAllPerformanceDetail(form);
    }

}