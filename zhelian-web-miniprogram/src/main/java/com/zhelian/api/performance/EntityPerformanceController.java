package com.zhelian.api.performance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.miniprogram.form.input.EmployeePerformanceQueryForm;
import com.zhelian.model.miniprogram.form.input.EntityPerformanceQueryForm;
import com.zhelian.model.miniprogram.form.input.PerformanceQueryForm;
import com.zhelian.service.miniprogram.performance.EntityPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api/entityPerformance")
public class EntityPerformanceController {

    @Autowired
    EntityPerformanceService entityPerformanceService;

    @RequestMapping(value = "/performance", method = {RequestMethod.POST})
    public BaseOutput employeePerformance(@RequestBody PerformanceQueryForm form) {
        return entityPerformanceService.entityPerformance(form);
    }

    @RequestMapping(value = "/saleBill", method = {RequestMethod.POST})
    public BaseOutput saleBill(@RequestBody EmployeePerformanceQueryForm form) {
        return entityPerformanceService.getSaleBill(form);
    }

    @RequestMapping(value = "/performanceByName", method = {RequestMethod.POST})
    public BaseOutput performanceByName(@RequestBody EntityPerformanceQueryForm form) {
        return entityPerformanceService.performanceByName(form);
    }


    /**
     * 2021-03-18
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/allPerformance", method = {RequestMethod.POST})
    public BaseOutput entityPerformance(@RequestBody PerformanceQueryForm form) {
        return entityPerformanceService.entityAllPerformance(form);
    }

    /**
     * 2021-03-18
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/payPerformance", method = {RequestMethod.POST})
    public BaseOutput entityPayPerformance(@RequestBody PerformanceQueryForm form) {
        return entityPerformanceService.entityPayPerformance(form);
    }

    /**
     * 2021-03-18
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/cardDeductionPerformance", method = {RequestMethod.POST})
    public BaseOutput entityCardDeductionPerformance(@RequestBody PerformanceQueryForm form) {
        return entityPerformanceService.entityCardDeductionPerformance(form);
    }
}