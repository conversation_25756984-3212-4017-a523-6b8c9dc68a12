package com.zhelian.api.customer;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.CustomerFileAddForm;
import com.zhelian.service.CRM.customerFile.CustomerFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;


@RestController
@RequestMapping(value = "/api")
public class CustomerFileController {

    @Autowired
    CustomerFileService customerFileService;


    @RequestMapping(value = "/customerFile/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody CustomerFileAddForm form) throws IOException {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客ID不能为空");
        return customerFileService.createCustomerFile(form);
    }

    @RequestMapping(value = "/customerFile/uploadVideoAndAudio", method = RequestMethod.POST)
    public BaseOutput uploadVideoAndAudio(@RequestParam("file") MultipartFile multipartFile) throws Exception {
        return customerFileService.uploadVideoUtil(multipartFile);
    }


    @RequestMapping(value = "/customerFile/uploadFile", method = RequestMethod.POST)
    public BaseOutput uploadFile(@RequestParam("file") MultipartFile multipartFile) throws Exception {
        return customerFileService.uploadFile(multipartFile);
    }
}