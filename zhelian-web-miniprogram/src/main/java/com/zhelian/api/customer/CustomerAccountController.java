package com.zhelian.api.customer;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.CustomerAccountQueryForm;
import com.zhelian.model.ibeauty.form.input.CustomerPackageCardAccountQueryForm;
import com.zhelian.service.ibeauty.customer.CustomerAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api/customerAccount")
public class CustomerAccountController {

    @Autowired
    CustomerAccountService customerAccountService;

    @RequestMapping(value = "/productAccount", method = {RequestMethod.POST})
    public BaseOutput productAccount(@RequestBody CustomerAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        if (form.Active == null)
            return BaseOutput.failed("是否显示已耗尽账户不能为空");
        return customerAccountService.miniprogramProductAccount(form);
    }

    @RequestMapping(value = "/projectAccount", method = {RequestMethod.POST})
    public BaseOutput projectAccount(@RequestBody CustomerAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        if (form.Active == null)
            return BaseOutput.failed("是否显示已耗尽账户不能为空");
        return customerAccountService.miniprogramProjectAccount(form);
    }

    @RequestMapping(value = "/generalCardAccount", method = {RequestMethod.POST})
    public BaseOutput generalCardAccount(@RequestBody CustomerAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        if (form.Active == null)
            return BaseOutput.failed("是否显示已耗尽账户不能为空");
        return customerAccountService.miniprogramGeneralCardAccount(form);
    }

    @RequestMapping(value = "/timeCardAccount", method = {RequestMethod.POST})
    public BaseOutput timeCardAccount(@RequestBody CustomerAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        if (form.Active == null)
            return BaseOutput.failed("是否显示已耗尽账户不能为空");
        return customerAccountService.miniprogramTimeCardAccount(form);
    }

    @RequestMapping(value = "/savingCardAccount", method = {RequestMethod.POST})
    public BaseOutput savingCardAccount(@RequestBody CustomerAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        if (form.Active == null)
            return BaseOutput.failed("是否显示已耗尽账户不能为空");
        return customerAccountService.miniprogramSavingCardAccount(form);
    }

    @RequestMapping(value = "/packageCardAccount", method = {RequestMethod.POST})
    public BaseOutput packageCardAccount(@RequestBody CustomerAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        if (form.Active == null)
            return BaseOutput.failed("是否显示已耗尽账户不能为空");
        return customerAccountService.packageCardAccount(form);
    }

    @RequestMapping(value = "/packageCardAccountDetails", method = {RequestMethod.POST})
    public BaseOutput packageCardAccountDetails(@RequestBody CustomerPackageCardAccountQueryForm form) {
        if (form.PackageCardAccountID == null)
            return BaseOutput.failed("套餐卡账户编号不能为空");
        return customerAccountService.packageCardAccountDetails(form);
    }

    @RequestMapping(value = "/rechargeSavingCardAccount", method = {RequestMethod.POST})
    public BaseOutput rechargeSavingCardAccount(@RequestBody CustomerAccountQueryForm form) {

        return customerAccountService.rechargeSavingCardAccount(form);
    }
}