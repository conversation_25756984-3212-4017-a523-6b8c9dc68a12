package com.zhelian.api.customer;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.ibeauty.form.output.CustomerFileDownloadQueryForm;
import com.zhelian.model.miniprogram.form.input.*;
import com.zhelian.service.CRM.customer.CustomerService;
import com.zhelian.service.CRM.customerLevel.CustomerLevelService;
import com.zhelian.service.CRM.customerSource.CustomerSourceService;
import com.zhelian.service.miniprogram.nursingLog.NursingLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;


@RestController
@RequestMapping(value = "/api/customer")
public class CustomerController {

    @Autowired
    CustomerService customerService;
    @Autowired
    CustomerSourceService customerSourceService;
    @Autowired
    CustomerLevelService customerLevelService;
    @Autowired
    NursingLogService nursingLogService;

    @RequestMapping(value = "/consultant", method = {RequestMethod.POST})
    public BaseOutput consultant(@RequestBody TodayCustomerQueryForm form) {
        return customerService.consultant(form.Name);
    }

    @RequestMapping(value = "/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody CustomerQueryForm form) {
        return customerService.customerList(form);
    }

    @RequestMapping(value = "/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody CustomerQueryForm form) {
        return customerService.allCustomer(form);
    }

    @RequestMapping(value = "/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody CustomerAddExtendForm form) throws Exception {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("顾客名称不能为空");
        if (form.PhoneNumber == null)
            return BaseOutput.failed("手机号不能为空");
        if (form.Gender == null)
            return BaseOutput.failed("性别不能为空");
        if (form.IsMember == null)
            return BaseOutput.failed("是否会员不能为空");
        if (form.IsLockMemberLevel == null)
            return BaseOutput.failed("是否锁定会员等级不能为空");
        if (form.IsMember && form.CustomerLevelID == null) {
            return BaseOutput.failed("请选择等级");
        }
        return customerService.createMiniCustomer(form);
    }

    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody CustomerUpdateExtendForm form) throws Exception {
        if (form.ID == null)
            return BaseOutput.failed("顾客编号不能为空");
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("顾客名称不能为空");
        if (form.PhoneNumber == null)
            return BaseOutput.failed("手机号不能为空");
        if (form.Gender == null)
            return BaseOutput.failed("性别不能为空");
        return customerService.updateMiniCustomer(form);
    }

    @RequestMapping(value = "/getCustomerTagLibrary", method = {RequestMethod.POST})
    public BaseOutput getCustomerTagLibrary(@RequestBody CustTagLibraryQueryForm form) {
        return customerService.getCustomerTagLibrary(form);
    }

    @RequestMapping(value = "/updateCustomerTagLibrary", method = {RequestMethod.POST})
    public BaseOutput updateCustomerTagLibrary(@RequestBody CustTagLibraryUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return customerService.updateCustomerTagLibrary(form);
    }

    @RequestMapping(value = "/todayNursingLog", method = {RequestMethod.POST})
    public BaseOutput getTodayNursingLog(@RequestBody TodayCustomerQueryForm form) {
        return nursingLogService.getTodayNursingLog(form);
    }

    @RequestMapping(value = "/customerDetailList", method = {RequestMethod.POST})
    public BaseOutput getCustomerDetailList(@RequestBody MiniCustomerQueryForm form) {
        return customerService.getCustomerDetailEntityList(form);
    }

    @RequestMapping(value = "/customerDetailSelfList", method = {RequestMethod.POST})
    public BaseOutput getCustomerDetailSelfList(@RequestBody MiniCustomerQueryForm form) {
        return customerService.getCustomerDetailSelfList(form);
    }

    @RequestMapping(value = "/customerDetailEntityList", method = {RequestMethod.POST})
    public BaseOutput getCustomerDetailEntityList(@RequestBody MiniCustomerQueryForm form) {
        return customerService.getCustomerDetailEntityList(form);
    }

    /**
     * 推荐项目
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/recommendProject", method = {RequestMethod.POST})
    public BaseOutput recommendProject(@RequestBody RecommendProjectQueryForm form) {
        return customerService.recommendProject(form);
    }

    /**
     * 添加顾客推荐项目
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/addCustomerRecommendProject", method = {RequestMethod.POST})
    public BaseOutput addRecommendProject(@RequestBody RecommendProjectAddForm form) {
        return customerService.addRecommendProject(form);
    }

    /**
     * 删除顾客推荐项目
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/deleteCustomerRecommendProject", method = {RequestMethod.POST})
    public BaseOutput deleteRecommendProject(@RequestBody RecommendProjectAddForm form) {
        return customerService.deleteRecommendProject(form);
    }

    /**
     * 添加顾客标签
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/addCustomerTag", method = {RequestMethod.POST})
    public BaseOutput addCustomerTag(@RequestBody CustomerTagAddForm form) {
        return customerService.addCustomerTag(form);
    }

    /**
     * 顾客推荐项目使用次数
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/recommendProjectConsumeTime", method = {RequestMethod.POST})
    public BaseOutput recommendProjectConsumeTime(@RequestBody RecommendProjectConsumeTimeQueryForm form) {
        return customerService.recommendProjectConsumeTime(form);
    }

    /**
     * 顾客详情
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/customerDetail", method = {RequestMethod.POST})
    public BaseOutput customerDetail(@RequestBody CustomerDetailQueryForm form) {
        return customerService.getCustomerDetail(form);
    }

    /**
     * 顾客来源
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/customerSource", method = {RequestMethod.POST})
    public BaseOutput getCustomerSource() {
        return customerSourceService.getCustomerSource();
    }


    /**
     * 生日列表
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/birthdayList", method = {RequestMethod.POST})
    public BaseOutput birthdayList(@RequestBody CustomerBirthdayQueryForm form) {
        return customerService.getBirthdayList(form);
    }

    /**
     * 上传头像
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/uploadImage", method = {RequestMethod.POST})
    public BaseOutput uploadImage(@RequestBody CustomerImageAddForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return customerService.uploadImage(form);
    }

    @RequestMapping(value = "/accountInfo", method = {RequestMethod.POST})
    public BaseOutput accountInfo(@RequestBody CustomerImageAddForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return customerService.accountInfo(form);
    }


    @RequestMapping(value = "/getBasicFile", method = {RequestMethod.POST})
    public BaseOutput getBasicFile(@RequestBody CustomerBasicFileInfoForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return customerService.getBasicFile(form);
    }

    @RequestMapping(value = "/createBasicFile", method = {RequestMethod.POST})
    public BaseOutput createBasicFile(@RequestBody CustomerBasicFileInfoForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");

        return customerService.createBasicFile(form);
    }


    @RequestMapping(value = "/file", method = {RequestMethod.POST})
    public BaseOutput getFile(@RequestBody CustomerDetailQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return customerService.getFile(form);
    }

    @RequestMapping(value = "/deleteFile", method = {RequestMethod.POST})
    public BaseOutput deleteFile(@RequestBody CustomerFileDeleteForm form) throws Exception {
        if (form.ID == null)
            return BaseOutput.failed("编号不能为空");
        return customerService.deleteFile(form);
    }

    @RequestMapping(value = "/customer/getFile", method = {RequestMethod.POST})
    public BaseOutput getFile(@RequestBody CustomerFileDownloadQueryForm form, HttpServletResponse response) {
        return customerService.getFile(form, response);

    }

    @RequestMapping(value = "/getFile", method = {RequestMethod.POST})
    public BaseOutput getFiles(@RequestBody CustomerFileDownloadQueryForm form, HttpServletResponse response) {
        return customerService.getFile(form, response);

    }

    /**
     * 顾客跟进
     *
     * @param form
     * @param
     * @return
     */
    @RequestMapping(value = "/followUp", method = {RequestMethod.POST})
    public BaseOutput followUpLog(@RequestBody CustomerDetailQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return customerService.followUpLog(form);

    }

    /**
     * 项目列表
     *
     * @param form
     * @param
     * @return
     */
    @RequestMapping(value = "/project", method = {RequestMethod.POST})
    public BaseOutput project(@RequestBody CustomerProjectQueryForm form) {
        return customerService.project(form);

    }

    /**
     * 照片对比新增
     *
     * @param form
     * @param
     * @return
     */
    @RequestMapping(value = "/createPhotoCompare", method = {RequestMethod.POST})
    public BaseOutput createPhotoCompare(@RequestBody CustomerPhotoCompareAddForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        if (form.ProjectID == null)
            return BaseOutput.failed("项目编号不能为空");
        return customerService.createPhotoCompare(form);

    }

    /**
     * 照片对比
     *
     * @param form
     * @param
     * @return
     */
    @RequestMapping(value = "/photoCompare", method = {RequestMethod.POST})
    public BaseOutput photoCompare(@RequestBody CustomerPhotoCompareQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return customerService.photoCompare(form);

    }

    /**
     * 追加照片
     *
     * @param form
     * @param
     * @return
     */
    @RequestMapping(value = "/addToPhoto", method = {RequestMethod.POST})
    public BaseOutput addToPhoto(@RequestBody CustomerAddToPhotoAddForm form) {
        if (form.PhotoCompareID == null)
            return BaseOutput.failed("照片对比ID不能为空");
        if (form.PhotoType == null)
            return BaseOutput.failed("照片类型不能为空");
        if (form.AttachmentURL == null)
            return BaseOutput.failed("附件URL不能为空");
        return customerService.addToPhoto(form);

    }

    /**
     * 删除图片
     *
     * @param form
     * @param
     * @return
     */
    @RequestMapping(value = "/deletePhoto", method = {RequestMethod.POST})
    public BaseOutput deletePhoto(@RequestBody CustomerPhotoCompareDeleteForm form) {
        if (form.ID == null)
            return BaseOutput.failed("照片ID不能为空");
        return customerService.deletePhoto(form);

    }

    @RequestMapping(value = "/record", method = {RequestMethod.POST})
    public BaseOutput record(@RequestBody CustomerRecordSessionQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客ID不能为空");
        if (form.Type == null)
            return BaseOutput.failed("类型不能为空");
        if (form.PageIndex == null)
            return BaseOutput.failed("当前页码不能为空");
        if (form.PageSize == null)
            return BaseOutput.failed("当前页展示数不能为空");
        return customerService.record(form);

    }

    @RequestMapping(value = "/yingxiaoyunLabel", method = {RequestMethod.POST})
    public BaseOutput label(@RequestBody CustomerRecordSessionQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客ID不能为空");

        return customerService.label(form);

    }

    @RequestMapping(value = "/customerBelongEntity", method = {RequestMethod.POST})
    public BaseOutput customerBelongEntity(@RequestBody CustomerBasicFileInfoForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");

        return customerService.customerBelongEntity(form);
    }

    @RequestMapping(value = "/updateCustomerBelongEntity", method = {RequestMethod.POST})
    public BaseOutput updateCustomerBelongEntity(@RequestBody CustomerBelongEntityUpdateForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        if (form.EntityID == null)
            return BaseOutput.failed("门店编号不能为空");
        return customerService.updateCustomerBelongEntity(form);
    }

    /**
     * 设置会员等级
     *
     * @param form
     * @param
     * @return
     */
    @RequestMapping(value = "/saveLevel", method = {RequestMethod.POST})
    public BaseOutput saveLevel(@RequestBody CustomerLevelSetAddForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客ID不能为空");
        if (form.CustomerLevelID == null)
            return BaseOutput.failed("顾客等级ID不能为空");
        if (form.IsLockMemberLevel == null)
            return BaseOutput.failed("是否锁定会员等级不能为空");
        return customerService.saveLevel(form);
    }

    /**
     * 移除会员等级
     *
     * @param form
     * @param
     * @return
     */
    @RequestMapping(value = "/deleteLevel", method = {RequestMethod.POST})
    public BaseOutput deleteLevel(@RequestBody CustomerLevelSetAddForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客ID不能为空");

        return customerService.deleteLevel(form);

    }
}