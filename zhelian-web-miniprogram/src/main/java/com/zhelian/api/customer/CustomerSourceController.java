package com.zhelian.api.customer;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.CustomerSourceQueryForm;
import com.zhelian.service.CRM.customerSource.CustomerSourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
public class CustomerSourceController {

    @Autowired
    CustomerSourceService customerSourceService;

    @RequestMapping(value = "/customerSource/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody CustomerSourceQueryForm form) {
        return customerSourceService.allCustomerSource(form);
    }
}