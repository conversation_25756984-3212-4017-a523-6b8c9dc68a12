package com.zhelian.api.customer;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.miniprogram.form.input.CustomerNursingLogQueryForm;
import com.zhelian.model.miniprogram.form.input.MiniCustomerAppointmentBillQueryForm;
import com.zhelian.service.ibeauty.customer.CustomerBillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api/customerBill")
public class CustomerBillController {

    @Autowired
    CustomerBillService customerBillService;

    @RequestMapping(value = "/saleBill", method = {RequestMethod.POST})
    public BaseOutput saleBill(@RequestBody CustomerSaleBillQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        return customerBillService.saleBill(form);
    }

    @RequestMapping(value = "/saleDetailBill", method = {RequestMethod.POST})
    public BaseOutput saleDetailBill(@RequestBody CustomerSaleDetailBillQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        return customerBillService.saleDetailBill(form);
    }

    @RequestMapping(value = "/salePayDetail", method = {RequestMethod.POST})
    public BaseOutput salePayDetail(@RequestBody CustomerSalePayDetailQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        return customerBillService.salePayDetail(form);
    }

    @RequestMapping(value = "/treatBill", method = {RequestMethod.POST})
    public BaseOutput treatBill(@RequestBody CustomerTreatBillQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        return customerBillService.treatBill(form);
    }

    @RequestMapping(value = "/treatDetailBill", method = {RequestMethod.POST})
    public BaseOutput treatDetailBill(@RequestBody CustomerTreatDetailBillQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        return customerBillService.treatDetailBill(form);
    }

    @RequestMapping(value = "/refundBill", method = {RequestMethod.POST})
    public BaseOutput refundBill(@RequestBody CustomerRefundBillQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        return customerBillService.refundBill(form);
    }

    @RequestMapping(value = "/refundTreatBill", method = {RequestMethod.POST})
    public BaseOutput refundTreatBill(@RequestBody CustomerRefundBillQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        return customerBillService.refundTreatBill(form);
    }

    @RequestMapping(value = "/transer", method = {RequestMethod.POST})
    public BaseOutput transer(@RequestBody CustomerTranserBillQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        return customerBillService.transer(form);
    }

    @RequestMapping(value = "/postponeBill", method = {RequestMethod.POST})
    public BaseOutput postponeBill(@RequestBody CustomerPostponeBillQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        return customerBillService.postponeBill(form);
    }

    @RequestMapping(value = "/appointmentBill", method = {RequestMethod.POST})
    public BaseOutput appointmentBill(@RequestBody MiniCustomerAppointmentBillQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        return customerBillService.customerAppointmentBill(form);
    }

    @RequestMapping(value = "/nursingLog", method = {RequestMethod.POST})
    public BaseOutput nursingLog(@RequestBody CustomerNursingLogQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        return customerBillService.nursingLog(form);
    }
}