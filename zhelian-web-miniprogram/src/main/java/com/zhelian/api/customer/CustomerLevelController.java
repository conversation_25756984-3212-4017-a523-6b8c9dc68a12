package com.zhelian.api.customer;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.entity.CustomerLevelEntity;
import com.zhelian.model.ibeauty.form.input.CustomerLevelQueryForm;
import com.zhelian.service.CRM.customerLevel.CustomerLevelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping(value = "/api")
public class CustomerLevelController {

    @Autowired
    CustomerLevelService customerLevelService;

    @RequestMapping(value = "/customerLevel/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody CustomerLevelQueryForm form) {
        return customerLevelService.allCustomerLevel(form);
    }

    //获取用户的等级
    @PostMapping(value = "/customerLevel/all2")
    public BaseOutput getAllLevel(@RequestBody CustomerLevelEntity form) {
        return customerLevelService.getAllLevel(form);
    }

}