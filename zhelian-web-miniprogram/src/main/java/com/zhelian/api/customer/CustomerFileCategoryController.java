package com.zhelian.api.customer;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.CustomerFileCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.CustomerFileCategoryMoveForm;
import com.zhelian.model.ibeauty.form.input.CustomerFileCategoryQueryForm;
import com.zhelian.model.ibeauty.form.input.CustomerFileCategoryUpdateForm;
import com.zhelian.service.CRM.customerFile.CustomerFileCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class CustomerFileCategoryController {

    @Autowired
    CustomerFileCategoryService customerFileCategoryService;

    @RequestMapping(value = "/customerFileCategory/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody CustomerFileCategoryQueryForm form) {
        return customerFileCategoryService.allCustomerFileCategory(form);
    }

    @RequestMapping(value = "/customerFileCategory/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody CustomerFileCategoryAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("分类名称不能为空");

        return customerFileCategoryService.createCustomerFileCategory(form);
    }

    @RequestMapping(value = "/customerFileCategory/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody CustomerFileCategoryUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("分类名称不能为空");
        if (form.Active == null)
            return BaseOutput.failed("有效性不能为空");

        return customerFileCategoryService.updateCustomerFileCategory(form);
    }

    @RequestMapping(value = "/customerFileCategory/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody CustomerFileCategoryMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");
        return customerFileCategoryService.moveCustomerFileCategory(form);
    }
}