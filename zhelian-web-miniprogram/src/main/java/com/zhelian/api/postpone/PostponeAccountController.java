package com.zhelian.api.postpone;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.PostponeAccountQueryForm;
import com.zhelian.service.ibeauty.postpone.PostPoneAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class PostponeAccountController {

    @Autowired
    PostPoneAccountService postPoneAccountService;

    @RequestMapping(value = "/postponeAccount/savingCardAccount", method = {RequestMethod.POST})
    public BaseOutput savingCardAccount(@RequestBody PostponeAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return postPoneAccountService.getPostPoneSavingCardAccount(form);
    }

    @RequestMapping(value = "/postponeAccount/generalCardAccount", method = {RequestMethod.POST})
    public BaseOutput generalCardAccount(@RequestBody PostponeAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return postPoneAccountService.getPostPoneGeneralCardAccount(form);
    }

    @RequestMapping(value = "/postponeAccount/timeCardAccount", method = {RequestMethod.POST})
    public BaseOutput timeCardAccount(@RequestBody PostponeAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return postPoneAccountService.getPostPoneTimeCardAccount(form);
    }

    @RequestMapping(value = "/postponeAccount/packageCardAccount", method = {RequestMethod.POST})
    public BaseOutput packageCardAccount(@RequestBody PostponeAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return postPoneAccountService.getPostPonePackageCardAccount(form);
    }

}