package com.zhelian.api.transer;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TranserAccountQueryForm;
import com.zhelian.model.ibeauty.form.input.TranserPackageCardAccountQueryForm;
import com.zhelian.service.ibeauty.transer.TranserAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class TranserAccountController {

    @Autowired
    TranserAccountService transerAccountService;

    @RequestMapping(value = "/transerAccount/productAccount", method = {RequestMethod.POST})
    public BaseOutput productAccount(@RequestBody TranserAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return transerAccountService.getTranserProductAccount(form);
    }

    @RequestMapping(value = "/transerAccount/projectAccount", method = {RequestMethod.POST})
    public BaseOutput projectAccount(@RequestBody TranserAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return transerAccountService.getTranserProjectAccount(form);
    }

    @RequestMapping(value = "/transerAccount/savingCardAccount", method = {RequestMethod.POST})
    public BaseOutput savingCardAccount(@RequestBody TranserAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return transerAccountService.getTranserSavingCardAccount(form);
    }

    @RequestMapping(value = "/transerAccount/generalCardAccount", method = {RequestMethod.POST})
    public BaseOutput generalCardAccount(@RequestBody TranserAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return transerAccountService.getTranserGeneralCardAccount(form);
    }

    @RequestMapping(value = "/transerAccount/timeCardAccount", method = {RequestMethod.POST})
    public BaseOutput timeCardAccount(@RequestBody TranserAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return transerAccountService.getTranserTimeCardAccount(form);
    }

    @RequestMapping(value = "/transerAccount/packageCardAccount", method = {RequestMethod.POST})
    public BaseOutput packageCardAccount(@RequestBody TranserAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return transerAccountService.getTranserPackageCardAccount(form);
    }

    @RequestMapping(value = "/transerAccount/packageCardAccountDetails", method = {RequestMethod.POST})
    public BaseOutput packageCardAccountDetails(@RequestBody TranserPackageCardAccountQueryForm form) {
        if (form.PackageCardAccountID == null)
            return BaseOutput.failed("套餐卡账户编号不能为空");
        return transerAccountService.packageCardAccountDetails(form);
    }

}