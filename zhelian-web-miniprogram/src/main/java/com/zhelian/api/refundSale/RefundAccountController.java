package com.zhelian.api.refundSale;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.RefundAccountQueryForm;
import com.zhelian.model.ibeauty.form.input.TranserPackageCardAccountQueryForm;
import com.zhelian.service.ibeauty.refund.RefundAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class RefundAccountController {

    @Autowired
    RefundAccountService refundAccountService;

    @RequestMapping(value = "/refundAccount/productAccount", method = {RequestMethod.POST})
    public BaseOutput productAccount(@RequestBody RefundAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return refundAccountService.getRefundProductAccount(form);
    }

    @RequestMapping(value = "/refundAccount/projectAccount", method = {RequestMethod.POST})
    public BaseOutput projectAccount(@RequestBody RefundAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return refundAccountService.getRefundProjectAccount(form);
    }

    @RequestMapping(value = "/refundAccount/savingCardAccount", method = {RequestMethod.POST})
    public BaseOutput savingCardAccount(@RequestBody RefundAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return refundAccountService.getRefundSavingCardAccount(form);
    }

    @RequestMapping(value = "/refundAccount/generalCardAccount", method = {RequestMethod.POST})
    public BaseOutput generalCardAccount(@RequestBody RefundAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return refundAccountService.getRefundGeneralCardAccount(form);
    }

    @RequestMapping(value = "/refundAccount/timeCardAccount", method = {RequestMethod.POST})
    public BaseOutput timeCardAccount(@RequestBody RefundAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return refundAccountService.getRefundTimeCardAccount(form);
    }

    @RequestMapping(value = "/refundAccount/packageCardAccount", method = {RequestMethod.POST})
    public BaseOutput packageCardAccount(@RequestBody RefundAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return refundAccountService.getRefundPackageCardAccount(form);
    }

    @RequestMapping(value = "/refundAccount/packageCardAccountDetails", method = {RequestMethod.POST})
    public BaseOutput packageCardAccountDetails(@RequestBody TranserPackageCardAccountQueryForm form) {
        if (form.PackageCardAccountID == null)
            return BaseOutput.failed("套餐卡账户编号不能为空");
        return refundAccountService.packageCardAccountDetails(form);
    }
}