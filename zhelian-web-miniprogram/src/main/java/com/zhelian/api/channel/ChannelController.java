package com.zhelian.api.channel;


import com.zhelian.model.CRM.form.input.ChannelAddForm;
import com.zhelian.model.CRM.form.input.ChannelQueryForm;
import com.zhelian.model.CRM.form.input.ChannelUpdateForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.miniprogram.form.input.ChannelDetailQueryForm;
import com.zhelian.model.miniprogram.form.input.MiniChannelQueryForm;
import com.zhelian.model.miniprogram.form.input.MiniCustomerQueryForm;
import com.zhelian.service.CRM.channel.ChannelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
public class ChannelController {

    @Autowired
    ChannelService channelService;


    @RequestMapping(value = "/channel/all", method = {RequestMethod.POST})
    public BaseOutput all() {
        return channelService.channelList(null);
    }

    @RequestMapping(value = "/channel/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody ChannelAddForm form) throws Exception {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("名称不能为空");
        if (form.ChannelTypeID == null)
            return BaseOutput.failed("渠道类型不能为空");
        return channelService.createMiniChannel(form);
    }

    /**
     * 查询自己的
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/channel/miniSelfList", method = {RequestMethod.POST})
    public BaseOutput miniSelfList(@RequestBody MiniChannelQueryForm form) {
        return channelService.miniSelfList(form);
    }

    /**
     * 查询权限下的
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/channel/miniAllList", method = {RequestMethod.POST})
    public BaseOutput miniAllList(@RequestBody MiniChannelQueryForm form) {
        return channelService.miniAllList(form);
    }

    @RequestMapping(value = "/channel/detail", method = {RequestMethod.POST})
    public BaseOutput detail(@RequestBody ChannelDetailQueryForm form) {
        return channelService.detail(form);
    }

    @RequestMapping(value = "/channel/employeeAll", method = {RequestMethod.POST})
    public BaseOutput employeeAll() {
        return channelService.empList();
    }


    @RequestMapping(value = "/channel/info", method = {RequestMethod.POST})
    public BaseOutput info(@RequestBody ChannelQueryForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("渠道名称不能为空");
        return channelService.all(form);
    }

    @RequestMapping(value = "/channel/customerInfo", method = {RequestMethod.POST})
    public BaseOutput customerInfo(@RequestBody ChannelQueryForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("渠道名称不能为空");
        return channelService.customerInfo(form);
    }


    @RequestMapping(value = "/channel/data", method = {RequestMethod.POST})
    public BaseOutput data(@RequestBody ChannelQueryForm form) {

        return channelService.data(form);
    }

    @RequestMapping(value = "/channel/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody ChannelQueryForm form) {
        return channelService.list(form);
    }

    @RequestMapping(value = "/channel/customer", method = {RequestMethod.POST})
    public BaseOutput customer(@RequestBody MiniCustomerQueryForm form) {

        return channelService.customer(form);
    }

    @RequestMapping(value = "/channel/infoPage", method = {RequestMethod.POST})
    public BaseOutput infoPage(@RequestBody ChannelQueryForm form) {

        return channelService.infoPage(form);
    }

    /**
     * 渠道顾客时使用
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/channel/current", method = {RequestMethod.POST})
    public BaseOutput current(@RequestBody ChannelQueryForm form) {

        return channelService.currentChannel(form);
    }

    /**
     * 渠道拜访时使用
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/channel/visitChannel", method = {RequestMethod.POST})
    public BaseOutput visitChannel(@RequestBody ChannelQueryForm form) {

        return channelService.visitChannel(form);
    }

    @RequestMapping(value = "/channel/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody ChannelUpdateForm form) throws Exception {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("名称不能为空");

        return channelService.updateChannel(form);
    }
}
