package com.zhelian.api.tagLibrary;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TagLibraryAddForm;
import com.zhelian.service.CRM.tagLibrary.TagLibraryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api/tagLibrary")
public class TagLibraryController {

    @Autowired
    TagLibraryService tagLibraryService;

    @RequestMapping(value = "/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody TagLibraryAddForm form) {
        return tagLibraryService.allTagLibrary(form.Name);
    }

    @RequestMapping(value = "/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody TagLibraryAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("标签名称不能为空");

        return tagLibraryService.createTagLibrary(form);
    }

}