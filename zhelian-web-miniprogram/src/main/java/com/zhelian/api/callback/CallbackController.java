package com.zhelian.api.callback;

import com.zhelian.model.CRM.form.input.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.service.CRM.callback.CallbackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api/callback")
public class CallbackController {

    @Autowired
    CallbackService callbackService;

    @RequestMapping(value = "/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody CallbackQueryForm form) {
        return callbackService.list(form);
    }

    @RequestMapping(value = "/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody CallbackQueryForm form) {
        return callbackService.all(form);
    }

    @RequestMapping(value = "/detail", method = {RequestMethod.POST})
    public BaseOutput detail(@RequestBody CallbackDetailQueryForm form) {

        if (form.CallbackRecordID == null)
            return BaseOutput.failed("ID不能为空");
        return callbackService.detail(form);
    }

    @RequestMapping(value = "/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody CallbackAddForm form) {
        if (form.CallbackRecordID == null)
            return BaseOutput.failed("回访ID不能为空");
        if (form.CallbackMethodID == null)
            return BaseOutput.failed("回访方式ID不能为空");
        return callbackService.create(form);
    }

    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody CallbackAddForm form) {
        if (form.CallbackRecordID == null)
            return BaseOutput.failed("回访ID不能为空");
        if (form.CallbackMethodID == null)
            return BaseOutput.failed("回访方式ID不能为空");
        return callbackService.update(form);
    }

    @RequestMapping(value = "/delete", method = {RequestMethod.POST})
    public BaseOutput delete(@RequestBody CallbackDeleteForm form) {
        if (form.CallbackRecordID == null)
            return BaseOutput.failed("回访方式ID不能为空");
        return callbackService.delete(form);
    }

    @RequestMapping(value = "/massDistribution", method = {RequestMethod.POST})
    public BaseOutput massDistribution(@RequestBody CallbackMassDistributionForm form) {
        return callbackService.massDistribution(form);
    }

}