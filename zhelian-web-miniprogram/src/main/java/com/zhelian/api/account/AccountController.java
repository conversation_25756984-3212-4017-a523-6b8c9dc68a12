package com.zhelian.api.account;

import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.CRM.CustomerMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.khs.form.input.*;
import com.zhelian.service.khs.account.AccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
public class AccountController {

    @Autowired
    AccountService accountService;
    @Autowired
    CustomerMapper customerMapper;

    @RequestMapping(value = "/login", method = {RequestMethod.POST})
    public BaseOutput login(@RequestBody LoginInputForm loginInputForm) {
//        if (!StringUtils.hasText(loginInputForm.EnterpriseCode)) {
//            return BaseOutput.failed("商户号不能为空");
//        }

        if (!StringUtils.hasText(loginInputForm.Username)) {
            return BaseOutput.failed("用户名不能为空");
        }

        if (!StringUtils.hasText(loginInputForm.Password)) {
            return BaseOutput.failed("密码不能为空");
        }
        return accountService.loginVerification(loginInputForm, "miniprogram");
    }

    @RequestMapping(value = "/account/entity", method = {RequestMethod.POST})
    public BaseOutput entity() {
        return accountService.getEmployeeEntityPermission(SystemUserUtil.getSystemUserID());
    }

    @RequestMapping(value = "/account/switchEntity", method = {RequestMethod.POST})
    public BaseOutput switchMiniprogramEntity(@RequestBody SwitchEntityForm form) {
        return accountService.switchMiniprogramEntity(form);
    }

    @RequestMapping(value = "/account/changePassword", method = {RequestMethod.POST})
    public BaseOutput changePassword(@RequestBody ChangePasswordForm form) {
        if (!StringUtils.hasText(form.OldPassword)) {
            return BaseOutput.failed("旧密码不能为空");
        }
        if (!StringUtils.hasText(form.NewPassword)) {
            return BaseOutput.failed("新密码不能为空");
        }

        return accountService.changePassword(form);
    }

    @RequestMapping(value = "/account/sendVerificationCode", method = {RequestMethod.POST})
    public BaseOutput sendVerificationCode(@RequestBody SendVerificationCodeForm form) {
        if (!StringUtils.hasText(form.EnterpriseCode)) {
            return BaseOutput.failed("商户号不能为空");
        }
        if (!StringUtils.hasText(form.Username)) {
            return BaseOutput.failed("用户名不能为空");
        }
        return accountService.sendVerificationCode(form);
    }

    @RequestMapping(value = "/account/resetPassword", method = {RequestMethod.POST})
    public BaseOutput resetPassword(@RequestBody ResetPasswordForm form) {
        if (!StringUtils.hasText(form.EnterpriseCode)) {
            return BaseOutput.failed("商户号不能为空");
        }
        if (!StringUtils.hasText(form.Username)) {
            return BaseOutput.failed("用户名不能为空");
        }
        if (!StringUtils.hasText(form.VerificationCode)) {
            return BaseOutput.failed("验证码不能为空");
        }
        if (!StringUtils.hasText(form.Password)) {
            return BaseOutput.failed("新密码不能为空");
        }
        return accountService.resetPassword(form);
    }

    @RequestMapping(value = "/account/getMiniprogramMenuPermission", method = {RequestMethod.POST})
    public BaseOutput getMiniprogramMenuPermission() {
        return accountService.getMiniprogramMenuPermission();
    }

}
