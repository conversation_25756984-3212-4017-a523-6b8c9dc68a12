package com.zhelian.api.customerCondition;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.CustomerConditionAddForm;
import com.zhelian.service.CRM.customerCondition.CustomerConditionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class CustomerConditionController {

    @Autowired
    CustomerConditionService customerConditionService;

    @RequestMapping(value = "/customerCondition/all", method = {RequestMethod.POST})
    public BaseOutput all() {
        return customerConditionService.all();
    }

    @RequestMapping(value = "/customerCondition/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody CustomerConditionAddForm form) {
        if (form.Type == null)
            return BaseOutput.failed("类型不能为空");
        return customerConditionService.create(form);
    }

}