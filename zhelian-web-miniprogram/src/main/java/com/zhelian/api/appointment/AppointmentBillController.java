package com.zhelian.api.appointment;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.miniprogram.form.input.*;
import com.zhelian.model.miniprogram.form.output.MiniAppointmentBillQueryForm;
import com.zhelian.service.ibeauty.appointment.AppointmentBillCommonService;
import com.zhelian.service.ibeauty.appointment.AppointmentBillService;
import com.zhelian.service.ibeauty.arrear.AppointmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping(value = "/api/appointmentBill")
public class AppointmentBillController {

    @Autowired
    AppointmentBillService appointmentBillService;
    @Autowired
    AppointmentBillCommonService appointmentBillCommonService;
    @Autowired
    AppointmentService appointmentService;


    @RequestMapping(value = "/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody MiniAppointmentBillAddForm form) throws Exception {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        if (form.AppointmentDate == null)
            return BaseOutput.failed("预约日期不能为空");
        if (form.Period == null)
            return BaseOutput.failed("时长不能为空");
        if (!checkProject(form.Project)) {
            return BaseOutput.failed("项目ID不能为空");
        }
        return appointmentBillCommonService.miniCreate(form);
    }

    @RequestMapping(value = "/newCustomerCreate", method = {RequestMethod.POST})
    public BaseOutput newCustomerCreate(@RequestBody MiniAppointmentBillAddExtendForm form) throws Exception {
        if (form.Name == null)
            return BaseOutput.failed("新客姓名不能为空");
        if (form.PhoneNumber == null)
            return BaseOutput.failed("新客手机号不能为空");
        if (form.AppointmentDate == null)
            return BaseOutput.failed("预约日期不能为空");
        if (form.Period == null)
            return BaseOutput.failed("时长不能为空");
        if (form.IsMember == null)
            return BaseOutput.failed("是否为会员不能为空");
        if (form.IsLockMemberLevel == null)
            return BaseOutput.failed("是否锁定会员不能为空");
        if (!checkProject(form.Project)) {
            return BaseOutput.failed("项目ID不能为空");
        }
        return appointmentBillCommonService.newCustomerCreate(form);
    }

    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody MiniAppointmentBillUpdateForm form) throws Exception {
        if (form.ID == null)
            return BaseOutput.failed("预约单不能为空");
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        if (form.AppointmentDate == null)
            return BaseOutput.failed("预约日期不能为空");
        if (form.Period == null)
            return BaseOutput.failed("时长不能为空");
        if (form.Status == null)
            return BaseOutput.failed("状态不能为空");
        if (!checkProject(form.Project)) {
            return BaseOutput.failed("项目ID不能为空");
        }
        return appointmentBillCommonService.miniUpdate(form);
    }

    @RequestMapping(value = "/updateStatus", method = {RequestMethod.POST})
    public BaseOutput updateStatus(@RequestBody AppointmentBillUpdateStatusForm form) {
        if (form.ID == null)
            return BaseOutput.failed("预约单不能为空");
        if (form.Status == null)
            return BaseOutput.failed("状态不能为空");

        return appointmentBillCommonService.updateStatus(form);
    }

    static boolean checkProject(List<AppointmentBillProjectAddForm> list) {
        for (AppointmentBillProjectAddForm appointmentBillProjectAddForm : list) {
            if (appointmentBillProjectAddForm.ProjectID == null)
                return false;
        }
        return true;
    }

    @RequestMapping(value = "/selfList", method = {RequestMethod.POST})
    public BaseOutput selfList(@RequestBody MiniAppointmentBillQueryForm form) {
        return appointmentBillService.appointmentBillInfoSelfList(form);
    }

    @RequestMapping(value = "/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody MiniAppointmentBillQueryForm form) {
        return appointmentBillService.appointmentBillInfoEntityList(form);
    }

    @RequestMapping(value = "/entityList", method = {RequestMethod.POST})
    public BaseOutput entityList(@RequestBody MiniAppointmentBillQueryForm form) {
        return appointmentBillService.appointmentBillInfoEntityList(form);
    }

    @RequestMapping(value = "/info", method = {RequestMethod.POST})
    public BaseOutput info(@RequestBody AppointmentBillInfoQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("预约单不能为空");
        return appointmentBillService.appointmentBillInfo(form);
    }

    @RequestMapping(value = "/config", method = {RequestMethod.POST})
    public BaseOutput config() {
        return appointmentService.appointmentConfig();
    }

    @RequestMapping(value = "/findCategoryAndProject", method = {RequestMethod.POST})
    public BaseOutput findCategoryAndProject(@RequestBody AppointmentBillProjectQueryForm form) {
        return appointmentService.findCategoryAndProject(form);
    }

    @RequestMapping(value = "/findProject", method = {RequestMethod.POST})
    public BaseOutput findProject(@RequestBody AppointmentBillProjectQueryForm form) {
        return appointmentService.findProject(form);
    }

    @RequestMapping(value = "/unToShopCount", method = {RequestMethod.POST})
    public BaseOutput unToShopCount(@RequestBody AppointmentCountQueryForm form) {
        if (form.StartDate == null)
            return BaseOutput.failed("开始时间不能为空");
        if (form.EndDate == null)
            return BaseOutput.failed("结束时间不能为空");
        return appointmentBillService.getUnToShopCustomerCount(form);
    }

    @RequestMapping(value = "/unToShopCountSelf", method = {RequestMethod.POST})
    public BaseOutput unToShopCountSelf(@RequestBody AppointmentCountQueryForm form) {
        if (form.StartDate == null)
            return BaseOutput.failed("开始时间不能为空");
        if (form.EndDate == null)
            return BaseOutput.failed("结束时间不能为空");
        return appointmentBillService.getUnToShopCustomerCountSelf(form);
    }

    @RequestMapping(value = "/unToShopCountEntity", method = {RequestMethod.POST})
    public BaseOutput unToShopCountEntity(@RequestBody AppointmentCountQueryForm form) {
        if (form.StartDate == null)
            return BaseOutput.failed("开始时间不能为空");
        if (form.EndDate == null)
            return BaseOutput.failed("结束时间不能为空");
        return appointmentBillService.getUnToShopCustomerCountEntity(form);
    }

    @RequestMapping(value = "/remainList", method = {RequestMethod.POST})
    public BaseOutput remainList(@RequestBody AppointmentRemainQueryForm form) {
        if (form.AppointmentDate == null)
            return BaseOutput.failed("预约时间不能为空");
        return appointmentBillService.remainEntityList(form);
    }

    @RequestMapping(value = "/remainSelfList", method = {RequestMethod.POST})
    public BaseOutput remainSelfList(@RequestBody AppointmentRemainQueryForm form) {
        if (form.AppointmentDate == null)
            return BaseOutput.failed("预约时间不能为空");
        return appointmentBillService.remainSelfList(form);
    }

    @RequestMapping(value = "/remainEntityList", method = {RequestMethod.POST})
    public BaseOutput remainEntityList(@RequestBody AppointmentRemainQueryForm form) {
        if (form.AppointmentDate == null)
            return BaseOutput.failed("预约时间不能为空");
        return appointmentBillService.remainEntityList(form);
    }

    @RequestMapping(value = "/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody AppointmentBillEmpQueryForm form) {
        if (form.AppointmentDate == null)
            return BaseOutput.failed("预约日期不能为空");
        if (form.ServicerID == null)
            return BaseOutput.failed("预约角色编号不能为空");
        return appointmentBillService.getAppointmentBillEmpAndCust(form);
    }

    @RequestMapping(value = "/servicer", method = {RequestMethod.POST})
    public BaseOutput servicer() {

        return appointmentBillService.getAppointmentBillServicer();
    }

    @RequestMapping(value = "/servicerEmployee", method = {RequestMethod.POST})
    public BaseOutput servicerEmployee(@RequestBody AppointmentBillEmpQueryForm form) {
        if (form.AppointmentDate == null)
            return BaseOutput.failed("预约日期不能为空");
        return appointmentBillService.getAppointmentBillServicerEmployee(form);
    }

    @RequestMapping(value = "/servicerEmployeeAll", method = {RequestMethod.POST})
    public BaseOutput servicerEmployeeAll() {
        return appointmentBillService.getAppointmentBillServicerEmployeeAll();
    }


    @RequestMapping(value = "/customer", method = {RequestMethod.POST})
    public BaseOutput customerList(@RequestBody SaleCustomerQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return appointmentBillService.customerList(form);
    }

    @RequestMapping(value = "/getCustomerAppointmentAll", method = {RequestMethod.POST})
    public BaseOutput getCustomerAppointmentAll(@RequestBody AppointmentCustomerForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("客户编号不能为空");
        return appointmentBillService.getCustomerAppointmentAll(form);
    }

    @RequestMapping(value = "/getCustomerAppointmentNumber", method = {RequestMethod.POST})
    public BaseOutput getCustomerAppointmentNumber(@RequestBody AppointmentCustomerNumberForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("客户编号不能为空");
        return appointmentBillService.getCustomerAppointmentNumber(form);
    }
}