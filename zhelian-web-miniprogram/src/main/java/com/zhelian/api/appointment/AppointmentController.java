package com.zhelian.api.appointment;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.AppointmentBillProjectQueryForm;
import com.zhelian.model.ibeauty.form.input.AppointmentEmployeeScheduleQueryForm;
import com.zhelian.service.ibeauty.arrear.AppointmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/appointment")
public class AppointmentController {

    @Autowired
    AppointmentService appointmentService;

    @RequestMapping(value = "/config", method = {RequestMethod.POST})
    public BaseOutput config() {
        return appointmentService.appointmentConfig();
    }


    @RequestMapping(value = "/schedule", method = {RequestMethod.POST})
    public BaseOutput schedule() {
        return appointmentService.schedule();
    }


    @RequestMapping(value = "/employeeSchedule", method = {RequestMethod.POST})
    public BaseOutput employeeSchedule(@RequestBody AppointmentEmployeeScheduleQueryForm form) {
        return appointmentService.employeeSchedule(form);
    }

    @RequestMapping(value = "/findCategoryAndProject", method = {RequestMethod.POST})
    public BaseOutput findCategoryAndProject(@RequestBody AppointmentBillProjectQueryForm form) {
        return appointmentService.findCategoryAndProject(form);
    }

    @RequestMapping(value = "/projectCategory", method = {RequestMethod.POST})
    public BaseOutput projectCategory(@RequestBody AppointmentBillProjectQueryForm form) {
        return appointmentService.projectCategory(form);
    }

    @RequestMapping(value = "/projectByCategory", method = {RequestMethod.POST})
    public BaseOutput projectByCategory(@RequestBody AppointmentBillProjectQueryForm form) {
        return appointmentService.projectByCategory(form);
    }
}