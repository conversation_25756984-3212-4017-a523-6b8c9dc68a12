package com.zhelian.api.InventoryApply;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.psi.form.input.*;
import com.zhelian.service.psi.inventoryApply.InventoryApplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 功能描述:
 * <>
 *
 * @return:
 * @exception:
 * @author: WangXianXian
 * @date: 2020/3/12 1:49 PM
 */
@RestController
@RequestMapping(value = "/api/inventoryApply")
public class InventoryApplyController {


    @Autowired
    InventoryApplyService inventoryApplyService;

    @RequestMapping(value = "/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody InventoryApplyQueryForm form) {
        return inventoryApplyService.inventoryApplyList(form);
    }

    @RequestMapping(value = "/info", method = {RequestMethod.POST})
    public BaseOutput info(@RequestBody InventoryApplyInfoQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("门店要货单号不能为空");

        return inventoryApplyService.inventoryApplyInfo(form.ID);
    }

    @RequestMapping(value = "/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody InventoryApplyAddForm form) {
        if (form.InventoryApplyDetail == null || form.InventoryApplyDetail.size() == 0) {
            return BaseOutput.failed("要货单产品明细不能为空");
        }
        if (form.InboundEntityID == null) {
            return BaseOutput.failed("申请仓库不能为空");
        }
        for (InventoryApplyDetailAddForm inventoryApplyDetailAddForm : form.InventoryApplyDetail) {
            if (inventoryApplyDetailAddForm.ProductID == null) {
                return BaseOutput.failed("产品编号不能为空");
            }
            if (inventoryApplyDetailAddForm.UnitID == null) {
                return BaseOutput.failed("产品单位不能为空");
            }
            if (inventoryApplyDetailAddForm.ApplyQuantity <= 0 || inventoryApplyDetailAddForm.ApplyMinimumUnitQuantity <= 0) {
                return BaseOutput.failed("要货数量必须大于0");
            }
        }
        return inventoryApplyService.create(form);
    }

    @RequestMapping(value = "/approved", method = {RequestMethod.POST})
    public BaseOutput approved(@RequestBody InventoryApplyApprovedForm form) {
        if (form.ID == null) {
            return BaseOutput.failed("要货单ID不能为空");
        }
        if (form.BillStatus == null) {
            return BaseOutput.failed("审核状态不能为空");
        }
        if (form.BillStatus.equals("20")) {
            if (form.InventoryApplyDetail == null || form.InventoryApplyDetail.size() == 0) {
                return BaseOutput.failed("产品明细不能为空");
            }
            if (form.OutboundEntityID == null) {
                return BaseOutput.failed("发货仓库不能为空");
            }
            if (form.ApprovedTotalAmount == null) {
                return BaseOutput.failed("单据合计金额不能为空");
            }
            for (InventoryApplyApprovedDetailForm inventoryApplyApprovedDetailForm : form.InventoryApplyDetail) {
                if (inventoryApplyApprovedDetailForm.ID == null) {
                    return BaseOutput.failed("明细ID不能为空");
                }
                if (inventoryApplyApprovedDetailForm.ApprovedPrice == null) {
                    return BaseOutput.failed("产品单价不能为空");
                }
                if (inventoryApplyApprovedDetailForm.ApprovedTotalAmount == null) {
                    return BaseOutput.failed("合计金额不能为空");
                }
            }
        }
        return inventoryApplyService.approved(form);
    }

    @RequestMapping(value = "/pay", method = {RequestMethod.POST})
    public BaseOutput pay(@RequestBody InventoryApplyPayForm form) {
        if (form.ID == null) {
            return BaseOutput.failed("要货单ID不能为空");
        }
        if (form.BillStatus == null) {
            return BaseOutput.failed("审核状态不能为空");
        }

        return inventoryApplyService.pay(form);
    }


    @RequestMapping(value = "/outbound", method = {RequestMethod.POST})
    public BaseOutput outbound(@RequestBody InventoryApplyOutboundForm form) {
        if (form.InventoryApplyDetail == null || form.InventoryApplyDetail.size() == 0) {
            return BaseOutput.failed("产品明细不能为空");
        }
        if (form.OutboundTotalAmount == null) {
            return BaseOutput.failed("单据合计金额不能为空");
        }
        for (InventoryApplyOutboundDetailForm inventoryApplyOutboundDetailForm : form.InventoryApplyDetail) {
            if (inventoryApplyOutboundDetailForm.ID == null) {
                return BaseOutput.failed("明细ID不能为空");
            }
            if (inventoryApplyOutboundDetailForm.OutboundPrice == null) {
                return BaseOutput.failed("产品单价不能为空");
            }
            if (inventoryApplyOutboundDetailForm.OutboundTotalAmount == null) {
                return BaseOutput.failed("合计金额不能为空");
            }
        }
        return inventoryApplyService.outbound(form);
    }

    @RequestMapping(value = "/inbound", method = {RequestMethod.POST})
    public BaseOutput inbound(@RequestBody InventoryApplyInboundForm form) {
        if (form.InventoryApplyDetail == null || form.InventoryApplyDetail.size() == 0) {
            return BaseOutput.failed("产品明细不能为空");
        }
        if (form.InboundTotalAmount == null) {
            return BaseOutput.failed("单据合计金额不能为空");
        }
        for (InventoryApplyInboundDetailForm inventoryApplyInboundDetailForm : form.InventoryApplyDetail) {
            if (inventoryApplyInboundDetailForm.ID == null) {
                return BaseOutput.failed("明细ID不能为空");
            }
            if (inventoryApplyInboundDetailForm.InboundPrice == null) {
                return BaseOutput.failed("产品单价不能为空");
            }
            if (inventoryApplyInboundDetailForm.InboundTotalAmount == null) {
                return BaseOutput.failed("合计金额不能为空");
            }
        }
        return inventoryApplyService.inbound(form);
    }

    @RequestMapping(value = "/cancel", method = {RequestMethod.POST})
    public BaseOutput cancel(@RequestBody InventoryApplyCancelForm form) {
        if (form.ID == null) {
            return BaseOutput.failed("门店要货单ID不能为空");
        }
        return inventoryApplyService.cancel(form);
    }

    @RequestMapping(value = "/cancelRejectApply", method = {RequestMethod.POST})
    public BaseOutput cancelRejectApply(@RequestBody InventoryApplyCancelForm form) {
        if (form.ID == null) {
            return BaseOutput.failed("门店要货单ID不能为空");
        }
        return inventoryApplyService.cancelRejectApply(form);
    }

    @RequestMapping(value = "/billStatusNumber", method = {RequestMethod.POST})
    public BaseOutput billStatusNumber(@RequestBody InventoryApplyBillStatusNumberForm form) {
        return inventoryApplyService.billStatusNumber(form);
    }

    @RequestMapping(value = "/detail", method = {RequestMethod.POST})
    public BaseOutput detail(@RequestBody InventoryApplyDetailInfoQueryForm form) {
        return inventoryApplyService.detail(form);
    }

    @PostMapping(value = "/prepay")
    public BaseOutput prepay(@RequestBody InventoryApplyDetailQueryForm form) {
        if (form.EntityID == null) {
            return BaseOutput.failed("门店不能为空");
        }
        return inventoryApplyService.getEntityPrepay(form);
    }

    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody InventoryApplyUpdateForm form) {
        if (form.ID == null || form.ID == null) {
            return BaseOutput.failed("订单编号不能为空");
        }
        return inventoryApplyService.update(form);
    }

    @RequestMapping(value = "/dataDetail", method = {RequestMethod.POST})
    public BaseOutput detail(@RequestBody InventoryApplyInfoQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("门店要货单号不能为空");

        return inventoryApplyService.inventoryApplyDetail(form.ID);
    }

    @RequestMapping(value = "/reject", method = {RequestMethod.POST})
    public BaseOutput reject(@RequestBody InventoryApplyPayForm form) {
        if (form.ID == null) {
            return BaseOutput.failed("门店要货单ID不能为空");
        }
        return inventoryApplyService.reject(form);
    }

}