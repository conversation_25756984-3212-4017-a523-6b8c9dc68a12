package com.zhelian.api.callbackMethod;

import com.zhelian.model.CRM.form.input.CallbackMethodAddForm;
import com.zhelian.model.CRM.form.input.CallbackMethodUpdateForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.PayMethodMoveForm;
import com.zhelian.model.ibeauty.form.input.PayMethodQueryForm;
import com.zhelian.service.CRM.callbackMethod.CallbackMethodService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api/callbackMethod")
public class CallbackMethodController {

    @Autowired
    CallbackMethodService callbackMethodService;

    @RequestMapping(value = "/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody PayMethodQueryForm form) {
        return callbackMethodService.all(form);
    }

    @RequestMapping(value = "/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody CallbackMethodAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("回访方式名称不能为空");

        return callbackMethodService.create(form);
    }

    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody CallbackMethodUpdateForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("方式名称不能为空");
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (form.Active == null)
            return BaseOutput.failed("有效性不能为空");

        return callbackMethodService.update(form);
    }

    @RequestMapping(value = "/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody PayMethodMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");
        return callbackMethodService.move(form);
    }

}