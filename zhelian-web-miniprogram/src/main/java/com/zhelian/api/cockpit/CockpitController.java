package com.zhelian.api.cockpit;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.miniprogram.form.input.*;
import com.zhelian.service.miniprogram.cockpit.CockpitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api/cockpit")
public class CockpitController {

    @Autowired
    CockpitService cockpitService;


    @RequestMapping(value = "/entityRank", method = {RequestMethod.POST})
    public BaseOutput entityRank(@RequestBody EntityRankQueryForm form) {

        return cockpitService.entityRank(form);
    }

    @RequestMapping(value = "/payOverview", method = {RequestMethod.POST})
    public BaseOutput payOverview(@RequestBody OverviewQueryForm form) {

        return cockpitService.payOverview(form);
    }

    @RequestMapping(value = "/projectData", method = {RequestMethod.POST})
    public BaseOutput projectData(@RequestBody ProjectDataQueryForm form) {

        return cockpitService.projectData(form);
    }

    @RequestMapping(value = "/productData", method = {RequestMethod.POST})
    public BaseOutput productData(@RequestBody ProductDataQueryForm form) {

        return cockpitService.productData(form);
    }

    @RequestMapping(value = "/savingCardData", method = {RequestMethod.POST})
    public BaseOutput savingCardData(@RequestBody SavingCardDataQueryForm form) {

        return cockpitService.savingCardData(form);
    }

    @RequestMapping(value = "/customerData", method = {RequestMethod.POST})
    public BaseOutput customerData(@RequestBody CustomerDataQueryForm form) {

        return cockpitService.customerData(form);
    }

    @RequestMapping(value = "/employeeData", method = {RequestMethod.POST})
    public BaseOutput employeeData(@RequestBody EmployeeDataQueryForm form) {

        return cockpitService.employeeData(form);
    }
}