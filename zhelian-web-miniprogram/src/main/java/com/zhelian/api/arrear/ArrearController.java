package com.zhelian.api.arrear;


import com.zhelian.core.utils.enumeration.BillChannel;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.service.ibeauty.arrear.ArrearCommonService;
import com.zhelian.service.ibeauty.arrear.ArrearService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class ArrearController {

    @Autowired
    ArrearService arrearService;
    @Autowired
    ArrearCommonService arrearCommonService;

    @RequestMapping(value = "/arrear/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody ArrearAddForm form) throws Exception {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        if (form.BillDate == null)
            return BaseOutput.failed("开单日期不能为空");
        if (form.Amount == null)
            return BaseOutput.failed("补尾款金额不能为空");

        if (form.Amount.compareTo(form.PayAmount.add(form.CardDeductionAmount)) != 0) {
            return BaseOutput.failed("订单金额不正确");
        }

        BigDecimal Amount = BigDecimal.ZERO;
        for (ArrearProductAddForm arrearProductAddForm : form.Product) {
            if (arrearProductAddForm.ProductAccountID == null)
                return BaseOutput.failed("产品账户编号不能为空");
            if (arrearProductAddForm.Amount == null)
                return BaseOutput.failed("产品补尾款金额不能为空");

            Amount = Amount.add(arrearProductAddForm.Amount);
        }
        for (ArrearProjectAddForm arrearProjectAddForm : form.Project) {
            if (arrearProjectAddForm.ProjectAccountID == null)
                return BaseOutput.failed("项目账户编号不能为空");
            if (arrearProjectAddForm.Amount == null)
                return BaseOutput.failed("项目补尾款金额不能为空");

            Amount = Amount.add(arrearProjectAddForm.Amount);
        }
        for (ArrearGeneralCardAddForm arrearGeneralCardAddForm : form.GeneralCard) {
            if (arrearGeneralCardAddForm.GeneralCardAccountID == null)
                return BaseOutput.failed("通用次卡账户编号不能为空");
            if (arrearGeneralCardAddForm.Amount == null)
                return BaseOutput.failed("通用次卡补尾款金额不能为空");

            Amount = Amount.add(arrearGeneralCardAddForm.Amount);
        }
        for (ArrearTimeCardAddForm arrearTimeCardAddForm : form.TimeCard) {
            if (arrearTimeCardAddForm.TimeCardAccountID == null)
                return BaseOutput.failed("时效卡账户编号不能为空");
            if (arrearTimeCardAddForm.Amount == null)
                return BaseOutput.failed("时效卡补尾款金额不能为空");

            Amount = Amount.add(arrearTimeCardAddForm.Amount);
        }
        for (ArrearSavingCardAddForm arrearSavingCardAddForm : form.SavingCard) {
            if (arrearSavingCardAddForm.SavingCardAccountID == null)
                return BaseOutput.failed("储值卡账户编号不能为空");
            if (arrearSavingCardAddForm.Amount == null)
                return BaseOutput.failed("储值卡补尾款金额不能为空");

            Amount = Amount.add(arrearSavingCardAddForm.Amount);
        }
        for (ArrearPackageCardAddForm arrearPackageCardAddForm : form.PackageCard) {
            if (arrearPackageCardAddForm.PackageCardAccountID == null)
                return BaseOutput.failed("套餐卡账户编号不能为空");
            if (arrearPackageCardAddForm.Amount == null)
                return BaseOutput.failed("套餐卡补尾款金额不能为空");
            for (ArrearProductAddForm arrearProductAddForm : arrearPackageCardAddForm.Product) {
                if (arrearProductAddForm.ProductAccountID == null)
                    return BaseOutput.failed("产品账户编号不能为空");
                if (arrearProductAddForm.Amount == null)
                    return BaseOutput.failed("产品补尾款金额不能为空");

                Amount = Amount.add(arrearProductAddForm.Amount);
            }
            for (ArrearProjectAddForm arrearProjectAddForm : arrearPackageCardAddForm.Project) {
                if (arrearProjectAddForm.ProjectAccountID == null)
                    return BaseOutput.failed("项目账户编号不能为空");
                if (arrearProjectAddForm.Amount == null)
                    return BaseOutput.failed("项目补尾款金额不能为空");

                Amount = Amount.add(arrearProjectAddForm.Amount);
            }
            for (ArrearGeneralCardAddForm arrearGeneralCardAddForm : arrearPackageCardAddForm.GeneralCard) {
                if (arrearGeneralCardAddForm.GeneralCardAccountID == null)
                    return BaseOutput.failed("通用次卡账户编号不能为空");
                if (arrearGeneralCardAddForm.Amount == null)
                    return BaseOutput.failed("通用次卡补尾款金额不能为空");

                Amount = Amount.add(arrearGeneralCardAddForm.Amount);
            }
            for (ArrearTimeCardAddForm arrearTimeCardAddForm : arrearPackageCardAddForm.TimeCard) {
                if (arrearTimeCardAddForm.TimeCardAccountID == null)
                    return BaseOutput.failed("时效卡账户编号不能为空");
                if (arrearTimeCardAddForm.Amount == null)
                    return BaseOutput.failed("时效卡补尾款金额不能为空");

                Amount = Amount.add(arrearTimeCardAddForm.Amount);
            }
            for (ArrearSavingCardAddForm arrearSavingCardAddForm : arrearPackageCardAddForm.SavingCard) {
                if (arrearSavingCardAddForm.SavingCardAccountID == null)
                    return BaseOutput.failed("储值卡账户编号不能为空");
                if (arrearSavingCardAddForm.Amount == null)
                    return BaseOutput.failed("储值卡补尾款金额不能为空");

                Amount = Amount.add(arrearSavingCardAddForm.Amount);
            }
        }

        if (form.Amount.compareTo(Amount) != 0) {
            return BaseOutput.failed("订单金额不正确");
        }

        return arrearCommonService.create(form, BillChannel.BillChannelMiniprogram.getID());
    }

    /**
     * 挂单
     *
     * @param form
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/arrear/createPendingOrder", method = {RequestMethod.POST})
    public BaseOutput createPendingOrder(@RequestBody ArrearAddForm form) throws Exception {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        if (form.BillDate == null)
            return BaseOutput.failed("开单日期不能为空");
        if (form.Amount == null)
            return BaseOutput.failed("补尾款金额不能为空");
        for (ArrearProductAddForm arrearProductAddForm : form.Product) {
            if (arrearProductAddForm.ProductAccountID == null)
                return BaseOutput.failed("产品账户编号不能为空");
            if (arrearProductAddForm.Amount == null)
                return BaseOutput.failed("产品补尾款金额不能为空");
        }
        for (ArrearProjectAddForm arrearProjectAddForm : form.Project) {
            if (arrearProjectAddForm.ProjectAccountID == null)
                return BaseOutput.failed("项目账户编号不能为空");
            if (arrearProjectAddForm.Amount == null)
                return BaseOutput.failed("项目补尾款金额不能为空");
        }
        for (ArrearGeneralCardAddForm arrearGeneralCardAddForm : form.GeneralCard) {
            if (arrearGeneralCardAddForm.GeneralCardAccountID == null)
                return BaseOutput.failed("通用次卡账户编号不能为空");
            if (arrearGeneralCardAddForm.Amount == null)
                return BaseOutput.failed("通用次卡补尾款金额不能为空");
        }
        for (ArrearTimeCardAddForm arrearTimeCardAddForm : form.TimeCard) {
            if (arrearTimeCardAddForm.TimeCardAccountID == null)
                return BaseOutput.failed("时效卡账户编号不能为空");
            if (arrearTimeCardAddForm.Amount == null)
                return BaseOutput.failed("时效卡补尾款金额不能为空");
        }
        for (ArrearSavingCardAddForm arrearSavingCardAddForm : form.SavingCard) {
            if (arrearSavingCardAddForm.SavingCardAccountID == null)
                return BaseOutput.failed("储值卡账户编号不能为空");
            if (arrearSavingCardAddForm.Amount == null)
                return BaseOutput.failed("储值卡补尾款金额不能为空");
        }
        for (ArrearPackageCardAddForm arrearPackageCardAddForm : form.PackageCard) {
            if (arrearPackageCardAddForm.PackageCardAccountID == null)
                return BaseOutput.failed("套餐卡账户编号不能为空");
            if (arrearPackageCardAddForm.Amount == null)
                return BaseOutput.failed("套餐卡补尾款金额不能为空");
            for (ArrearProductAddForm arrearProductAddForm : arrearPackageCardAddForm.Product) {
                if (arrearProductAddForm.ProductAccountID == null)
                    return BaseOutput.failed("产品账户编号不能为空");
                if (arrearProductAddForm.Amount == null)
                    return BaseOutput.failed("产品补尾款金额不能为空");
            }
            for (ArrearProjectAddForm arrearProjectAddForm : arrearPackageCardAddForm.Project) {
                if (arrearProjectAddForm.ProjectAccountID == null)
                    return BaseOutput.failed("项目账户编号不能为空");
                if (arrearProjectAddForm.Amount == null)
                    return BaseOutput.failed("项目补尾款金额不能为空");
            }
            for (ArrearGeneralCardAddForm arrearGeneralCardAddForm : arrearPackageCardAddForm.GeneralCard) {
                if (arrearGeneralCardAddForm.GeneralCardAccountID == null)
                    return BaseOutput.failed("通用次卡账户编号不能为空");
                if (arrearGeneralCardAddForm.Amount == null)
                    return BaseOutput.failed("通用次卡补尾款金额不能为空");
            }
            for (ArrearTimeCardAddForm arrearTimeCardAddForm : arrearPackageCardAddForm.TimeCard) {
                if (arrearTimeCardAddForm.TimeCardAccountID == null)
                    return BaseOutput.failed("时效卡账户编号不能为空");
                if (arrearTimeCardAddForm.Amount == null)
                    return BaseOutput.failed("时效卡补尾款金额不能为空");
            }
            for (ArrearSavingCardAddForm arrearSavingCardAddForm : arrearPackageCardAddForm.SavingCard) {
                if (arrearSavingCardAddForm.SavingCardAccountID == null)
                    return BaseOutput.failed("储值卡账户编号不能为空");
                if (arrearSavingCardAddForm.Amount == null)
                    return BaseOutput.failed("储值卡补尾款金额不能为空");
            }
        }
        return arrearService.createPendingOrder(form, BillChannel.BillChannelMiniprogram.getID());
    }
}