package com.zhelian.api.reception;

import com.zhelian.model.CRM.form.input.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.service.CRM.reception.ReceptionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class ReceptionController {

    @Autowired
    ReceptionService receptionService;

    @RequestMapping(value = "/reception/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody ReceptionQueryForm form) {
        return receptionService.list(form);
    }

    @RequestMapping(value = "/reception/confirm", method = {RequestMethod.POST})
    public BaseOutput confirm(@RequestBody ReceptionQueryExtendForm form) {
        if (form.AppointmentBillID == null)
            return BaseOutput.failed("预约ID不能为空");
        return receptionService.confirm(form);
    }

    /**
     * 確認並指派
     *
     * @param form
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/reception/confirmAssign", method = {RequestMethod.POST})
    public BaseOutput confirmAssign(@RequestBody ReceptionAddForm form) {
        if (form.AppointmentBillID == null)
            return BaseOutput.failed("预约ID不能为空");
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客ID不能为空");
        if (form.DiagnosisBy == null)
            return BaseOutput.failed("员工ID不能为空");
        return receptionService.confirmAssign(form);
    }

    /**
     * 指派
     *
     * @param form
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/reception/assign", method = {RequestMethod.POST})
    public BaseOutput assign(@RequestBody ReceptionAddForm form) {
        if (form.AppointmentBillID == null)
            return BaseOutput.failed("预约ID不能为空");
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客ID不能为空");
        if (form.DiagnosisBy == null)
            return BaseOutput.failed("员工ID不能为空");
        return receptionService.assign(form);
    }

    /**
     * 重新指派
     *
     * @param form
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/reception/againAssign", method = {RequestMethod.POST})
    public BaseOutput againAssign(@RequestBody ReceptionUpdateForm form) {
        if (form.DiagnosisRecordID == null)
            return BaseOutput.failed("指派ID不能为空");
        if (form.DiagnosisBy == null)
            return BaseOutput.failed("员工ID不能为空");

        return receptionService.againAssign(form);
    }

    /**
     * 到客登记确认到访
     *
     * @param form
     * @return
     * @throws
     */
    @RequestMapping(value = "/reception/confirmVist", method = {RequestMethod.POST})
    public BaseOutput confirmVist(@RequestBody ReceptionConfirmAddForm form) {
        return receptionService.confirmVist(form);
    }
}