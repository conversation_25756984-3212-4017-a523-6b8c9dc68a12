package com.zhelian.api.entity;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.QueryForm;
import com.zhelian.model.ibeauty.form.input.QueryPageForm;
import com.zhelian.model.miniprogram.form.input.MiniEntityQueryForm;
import com.zhelian.service.khs.entity.EntityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class EntityController {

    @Autowired
    EntityService entityService;


    @RequestMapping(value = "/entity/allEntity", method = {RequestMethod.POST})
    public BaseOutput allEntity(@RequestBody MiniEntityQueryForm form) {
        return entityService.all(form);
    }

    @RequestMapping(value = "/entity/permissionEtityByPage", method = {RequestMethod.POST})
    public BaseOutput permissionEtityByPage(@RequestBody QueryPageForm form) {
        return entityService.permissionEtityByPage(form);
    }

    @RequestMapping(value = "/entity/permissionEtity", method = {RequestMethod.POST})
    public BaseOutput permissionEtity(@RequestBody QueryForm form) {
        return entityService.permissionEtity(form);
    }
}
