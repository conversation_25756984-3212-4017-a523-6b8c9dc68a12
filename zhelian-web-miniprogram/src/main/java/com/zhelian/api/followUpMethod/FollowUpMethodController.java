package com.zhelian.api.followUpMethod;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.PayMethodAddForm;
import com.zhelian.model.ibeauty.form.input.PayMethodMoveForm;
import com.zhelian.model.ibeauty.form.input.PayMethodQueryForm;
import com.zhelian.model.ibeauty.form.input.PayMethodUpdateForm;
import com.zhelian.service.CRM.followUpMethod.FollowUpMethodService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class FollowUpMethodController {

    @Autowired
    FollowUpMethodService followUpMethodService;

    @RequestMapping(value = "/followUpMethod/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody PayMethodQueryForm form) {
        return followUpMethodService.all(form);
    }

    @RequestMapping(value = "/followUpMethod/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody PayMethodAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("支付方式名称不能为空");

        return followUpMethodService.create(form);
    }

    @RequestMapping(value = "/followUpMethod/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody PayMethodUpdateForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("支付方式名称不能为空");
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (form.Active == null)
            return BaseOutput.failed("有效性不能为空");

        return followUpMethodService.update(form);
    }

    @RequestMapping(value = "/followUpMethod/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody PayMethodMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");
        return followUpMethodService.move(form);
    }

}