package com.zhelian.api.crm;

import com.zhelian.model.CRM.form.output.ArtSpeakOutputForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.service.CRM.artSpeak.ArtSpeakService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class ArtSpeakController {

    @Autowired
    ArtSpeakService artSpeakService;


    @RequestMapping(value = "/artSpeak/list", method = {RequestMethod.POST})
    public BaseOutput artSpeakList(@RequestBody ArtSpeakOutputForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return artSpeakService.getArtSpeakList(form);
    }

}