package com.zhelian.api.customerFileApplicationScene;

import com.zhelian.model.CRM.form.input.CustomerFileApplicationSceneUpdateForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.service.CRM.customerFileApplicationScene.CustomerFileApplicationSceneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class CustomerFileApplicationSceneController {

    @Autowired
    CustomerFileApplicationSceneService customerFileApplicationSceneService;


    @RequestMapping(value = "/customerFileApplicationScene/all", method = {RequestMethod.POST})
    public BaseOutput all() {
        return customerFileApplicationSceneService.all();
    }


    @RequestMapping(value = "/customerFileApplicationScene/updateStatus", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody CustomerFileApplicationSceneUpdateForm form) {
        if (!StringUtils.hasText(form.Code))
            return BaseOutput.failed("档案编码不能为空");
        if (form.IsRequired == null)
            return BaseOutput.failed("档案状态不能为空");
        return customerFileApplicationSceneService.updateStatus(form);
    }
}