package com.zhelian.api.channelVisit;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.miniprogram.form.input.*;
import com.zhelian.service.CRM.channelVisit.ChannelVisitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
public class ChannelVisitController {

    @Autowired
    ChannelVisitService channelVisitService;


    @RequestMapping(value = "/channelVisit/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody ChannelVisitQueryForm form) {

        return channelVisitService.list(form);
    }

    @RequestMapping(value = "/channelVisit/subordinatesList", method = {RequestMethod.POST})
    public BaseOutput subordinatesList(@RequestBody ChannelVisitQueryForm form) {

        return channelVisitService.subordinatesList(form);
    }

    @RequestMapping(value = "/channelVisit/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody ChannelVisitAddForm form) throws Exception {
        if (!StringUtils.hasText(form.VisitType))
            return BaseOutput.failed("拜访不能为空");
        if (!StringUtils.hasText(form.VisitBy))
            return BaseOutput.failed("拜访人不能为空");
        if (form.PlannedOn == null)
            return BaseOutput.failed("计划时间不能为空");
        if (form.Channel.length == 0)
            return BaseOutput.failed("渠道不能为空");
        return channelVisitService.create(form);
    }

    @RequestMapping(value = "/channelVisit/createTemporaryRemote", method = {RequestMethod.POST})
    public BaseOutput createTemporary(@RequestBody ChannelVisitTemporaryAddForm form) throws Exception {

        if (form.PlannedOn == null)
            return BaseOutput.failed("计划时间不能为空");

        return channelVisitService.createTemporary(form);
    }

    @RequestMapping(value = "/channelVisit/createTemporaryOffline", method = {RequestMethod.POST})
    public BaseOutput createTemporaryOffline(@RequestBody ChannelVisitTemporaryOfflineAddForm form) throws Exception {

        if (form.PlannedOn == null)
            return BaseOutput.failed("计划时间不能为空");
        if (form.SignOn == null)
            return BaseOutput.failed("签到时间不能为空");

        return channelVisitService.createTemporaryOffline(form);
    }

    @RequestMapping(value = "/channelVisit/cancel", method = {RequestMethod.POST})
    public BaseOutput cancel(@RequestBody ChannelVisitCnannelAddForm form) {

        if (form.ID == null)
            return BaseOutput.failed("计划ID不能为空");

        return channelVisitService.cancel(form);
    }

    @RequestMapping(value = "/channelVisit/channel", method = {RequestMethod.POST})
    public BaseOutput channel(@RequestBody ChannelVisitQueryForm form) {

        return channelVisitService.channel(form);
    }

    @RequestMapping(value = "/channelVisit/detail", method = {RequestMethod.POST})
    public BaseOutput detail(@RequestBody ChannelVisitCnannelAddForm form) {
        return channelVisitService.detail(form);
    }

    @RequestMapping(value = "/channelVisit/signIn", method = {RequestMethod.POST})
    public BaseOutput SignIn(@RequestBody ChannelVisitSignAddForm form) throws Exception {
        return channelVisitService.SignIn(form);
    }

    @RequestMapping(value = "/channelVisit/signOut", method = {RequestMethod.POST})
    public BaseOutput SignOut(@RequestBody ChannelVisitSignAddForm form) throws Exception {
        return channelVisitService.SignOut(form);
    }

    @RequestMapping(value = "/channelVisit/createVisitLog", method = {RequestMethod.POST})
    public BaseOutput createVisitLog(@RequestBody ChannelVisitLogAddForm form) {
        if (form.ID == null)
            return BaseOutput.failed("计划ID不能为空");
        if (!StringUtils.hasText(form.Remark))
            return BaseOutput.failed("记录不能为空");
        return channelVisitService.createVisitLog(form);
    }


    @RequestMapping(value = "/channelVisit/report", method = {RequestMethod.POST})
    public BaseOutput report(@RequestBody ChannelVisitReportQueryForm form) {
        return channelVisitService.report(form);
    }


    @RequestMapping(value = "/channelVisit/createAppointment", method = {RequestMethod.POST})
    public BaseOutput ceateAppointment(@RequestBody ChannelVisitAppointmentAddForm form) throws Exception {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客ID不能为空");
        if (form.EntityID == null)
            return BaseOutput.failed("门店ID不能为空");
        if (!StringUtils.hasText(form.AppointmentDate))
            return BaseOutput.failed("预约时间不能为空");
        return channelVisitService.ceateAppointment(form);
    }

    @RequestMapping(value = "/channelVisit/updateAppointment", method = {RequestMethod.POST})
    public BaseOutput updateAppointment(@RequestBody ChannelVisitAppointmentUpdateForm form) throws Exception {
        if (form.ID == null)
            return BaseOutput.failed("预约ID不能为空");
        if (form.EntityID == null)
            return BaseOutput.failed("门店ID不能为空");
        if (!StringUtils.hasText(form.AppointmentDate))
            return BaseOutput.failed("预约时间不能为空");
        return channelVisitService.updateAppointment(form);
    }

    @RequestMapping(value = "/channelVisit/cancelAppointment", method = {RequestMethod.POST})
    public BaseOutput cancelAppointment(@RequestBody ChannelVisitAppointmentUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("预约ID不能为空");

        return channelVisitService.cancelAppointment(form);
    }

    @RequestMapping(value = "/channelVisit/appointmentInfo", method = {RequestMethod.POST})
    public BaseOutput appointmentInfo(@RequestBody ChannelVisitAppointmentUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("预约ID不能为空");

        return channelVisitService.appointmentInfo(form);
    }

    @RequestMapping(value = "/channelVisit/appointmentList", method = {RequestMethod.POST})
    public BaseOutput appointmentList(@RequestBody ChannelVisitAppointmentQueryForm form) {
        return channelVisitService.appointmentList(form);
    }
}
