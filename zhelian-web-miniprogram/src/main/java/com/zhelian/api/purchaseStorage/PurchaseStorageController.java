package com.zhelian.api.purchaseStorage;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.service.psi.purchaseStorage.PurchaseStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/purchaseStorage")
public class PurchaseStorageController {

    @Autowired
    PurchaseStorageService purchaseStorageService;


    @RequestMapping(value = "/entity", method = {RequestMethod.POST})
    public BaseOutput entity() {
        return purchaseStorageService.warehouseList();
    }


}