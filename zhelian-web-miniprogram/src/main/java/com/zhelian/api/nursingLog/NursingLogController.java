package com.zhelian.api.nursingLog;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.miniprogram.form.input.*;
import com.zhelian.service.miniprogram.nursingLog.NursingLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


@RestController
@RequestMapping(value = "/api/nursingLog")
public class NursingLogController {

    @Autowired
    NursingLogService nursingLogService;

    @RequestMapping(value = "/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody NursingLogQueryForm form) {
        return nursingLogService.nursingLogList(form);
    }

    @RequestMapping(value = "/add", method = {RequestMethod.POST})
    public BaseOutput add(@RequestBody NursingLogAddForm form) {
        return nursingLogService.addNursingLog(form);
    }

    @RequestMapping(value = "/addComment", method = {RequestMethod.POST})
    public BaseOutput addComment(@RequestBody NursingLogAddCommentForm form) {
        if (form.NursingLogID == null)
            return BaseOutput.failed("护理日志编号不能为空");
        return nursingLogService.addComment(form);
    }

    @RequestMapping(value = "/addThumbsUp", method = {RequestMethod.POST})
    public BaseOutput addThumbsUp(@RequestBody NursingLogAddThumbsUpForm form) {
        return nursingLogService.addThumbsUp(form);
    }

    /**
     * @Description: 视频上传
     * @param:
     * @return:
     */
    @RequestMapping(value = "/uploadVideoAndAudio", method = RequestMethod.POST)
    public BaseOutput uploadVideoAndAudio(@RequestParam("file") MultipartFile multipartFile) throws Exception {
        return nursingLogService.uploadVideoUtil(multipartFile);
    }

    /**
     * 删除视频文件
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/deleteVideoAndAudio", method = RequestMethod.POST)
    public BaseOutput deleteVideoAndAudio(@RequestBody VideoForm form) throws Exception {
        if (form.address == null || form.address.size() == 0)
            return BaseOutput.failed("视频地址不能为空");
        return nursingLogService.deleteVideoAndAudio(form.address);
    }

    @RequestMapping(value = "/employee", method = {RequestMethod.POST})
    public BaseOutput employee() {
        return nursingLogService.employee();
    }


    @RequestMapping(value = "/customer", method = {RequestMethod.POST})
    public BaseOutput customer(@RequestBody NursingLogQueryForm form) {
        if (form.Name == null)
            return BaseOutput.failed("搜索内容不能为空");
        return nursingLogService.customer(form);
    }

    @RequestMapping(value = "/deleteComment", method = {RequestMethod.POST})
    public BaseOutput deleteComment(@RequestBody NursingLogCommentDeleteForm form) {
        if (form.ID == null)
            return BaseOutput.failed("护理日志评论ID不能为空");
        return nursingLogService.deleteComment(form);
    }

    /**
     * 上传图片
     * @param form
     * @return
     */
    @RequestMapping(value = "/uploadImage", method = {RequestMethod.POST})
    public BaseOutput uploadImage(@RequestBody NursingLogUploadImageForm form) {
        return nursingLogService.uploadImage(form);
    }
}