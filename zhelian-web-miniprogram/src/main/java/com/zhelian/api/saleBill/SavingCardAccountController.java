package com.zhelian.api.saleBill;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.service.ibeauty.savingCardAccount.SavingCardAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class SavingCardAccountController {

    @Autowired
    SavingCardAccountService savingCardAccountService;

    @RequestMapping(value = "/savingCardAccount/allGoods", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody SavingCardAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return savingCardAccountService.allSavingCardAccount(form);
    }

    @RequestMapping(value = "/savingCardAccount/someGoods", method = {RequestMethod.POST})
    public BaseOutput someGoods(@RequestBody SavingCardAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return savingCardAccountService.allSomeGoodsSavingCardAccount(form);
    }

    @RequestMapping(value = "/savingCardAccount/product", method = {RequestMethod.POST})
    public BaseOutput product(@RequestBody SavingCardProductAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        if (form.ProductID == null)
            return BaseOutput.failed("产品编号不能为空");
        return savingCardAccountService.savingCardProductAccount(form);
    }

    @RequestMapping(value = "/savingCardAccount/project", method = {RequestMethod.POST})
    public BaseOutput project(@RequestBody SavingCardProjectAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        if (form.ProjectID == null)
            return BaseOutput.failed("项目编号不能为空");
        return savingCardAccountService.savingCardProjectAccount(form);
    }

    @RequestMapping(value = "/savingCardAccount/generalCard", method = {RequestMethod.POST})
    public BaseOutput generalCard(@RequestBody SavingCardGeneralCardAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        if (form.GeneralCardID == null)
            return BaseOutput.failed("通用次卡编号不能为空");
        return savingCardAccountService.savingCardGeneralCardAccount(form);
    }

    @RequestMapping(value = "/savingCardAccount/timeCard", method = {RequestMethod.POST})
    public BaseOutput timeCard(@RequestBody SavingCardTimeCardAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        if (form.TimeCardID == null)
            return BaseOutput.failed("时效卡编号不能为空");
        return savingCardAccountService.savingCardTimeCardAccount(form);
    }

    @RequestMapping(value = "/savingCardAccount/packageCard", method = {RequestMethod.POST})
    public BaseOutput packageCard(@RequestBody SavingCardPackageCardAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        if (form.PackageCardID == null)
            return BaseOutput.failed("套餐卡编号不能为空");
        return savingCardAccountService.savingCardPackageCardAccount(form);
    }
}