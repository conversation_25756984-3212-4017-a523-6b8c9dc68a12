package com.zhelian.api.saleBill;


import com.zhelian.core.utils.enumeration.BillChannel;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.service.ibeauty.saleBill.SaleBillCommonService;
import com.zhelian.service.ibeauty.saleBill.SaleBillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api/fastSaleBill")
public class FastSaleBillController {

    @Autowired
    SaleBillService saleBillService;

    @Autowired
    SaleBillCommonService saleBillCommonService;

    @RequestMapping(value = "/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody SaleBillAddForm form) throws Exception {

        if (form.Amount.compareTo(form.PayAmount.add(form.CardDeductionAmount)) != 0) {
            return BaseOutput.failed("订单金额不正确");
        }

        BigDecimal PayAmount = BigDecimal.ZERO;
        BigDecimal ArrearAmount = BigDecimal.ZERO;
        BigDecimal CardDeductionAmount = BigDecimal.ZERO;

        for (SaleBillProductForm productForm : form.Product) {
            if (!productForm.IsLargess) {
                if (productForm.TotalAmount.compareTo(productForm.PayAmount.add(productForm.ArrearAmount).add(productForm.SavingCardDeductionAmount)) != 0) {
                    return BaseOutput.failed("产品金额不正确");
                }
                PayAmount = PayAmount.add(productForm.PayAmount);
                ArrearAmount = ArrearAmount.add(productForm.ArrearAmount);
                CardDeductionAmount = CardDeductionAmount.add(productForm.SavingCardDeductionAmount);
            }
        }
        for (SaleBillProjectForm projectForm : form.Project) {
            if (!projectForm.IsLargess) {
                if (projectForm.TotalAmount.compareTo(projectForm.PayAmount.add(projectForm.ArrearAmount).add(projectForm.SavingCardDeductionAmount)) != 0) {
                    return BaseOutput.failed("项目金额不正确");
                }
                PayAmount = PayAmount.add(projectForm.PayAmount);
                ArrearAmount = ArrearAmount.add(projectForm.ArrearAmount);
                CardDeductionAmount = CardDeductionAmount.add(projectForm.SavingCardDeductionAmount);
            }
        }

        if (form.SavingCardDeduction != null) {
            //明细 PayAmount 包含储值卡抵扣金额，这里减去
            for (SaleBillSavingCardDeductionForm deductionForm : form.SavingCardDeduction) {
                PayAmount = PayAmount.subtract(deductionForm.DeductionAmount);
                CardDeductionAmount = CardDeductionAmount.add(deductionForm.DeductionAmount);
            }
        }
        if (form.PayAmount.compareTo(PayAmount) != 0) {
            return BaseOutput.failed("订单实付金额不正确");
        }

        if (form.CardDeductionAmount.compareTo(CardDeductionAmount) != 0) {
            return BaseOutput.failed("订单储值卡抵扣金额不正确");
        }

        return saleBillCommonService.create(form, true, BillChannel.BillChannelMiniprogram.getID());
    }

    @RequestMapping(value = "/saleBillBindCutsomer", method = {RequestMethod.POST})
    public BaseOutput saleBillBindCutsomer(@RequestBody SaleBillBindCustomerForm form) throws Exception {

        if (form.BillID == null)
            return BaseOutput.failed("订单编号不能为空");
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");

        return saleBillService.saleBillBindCutsomer(form);
    }


}