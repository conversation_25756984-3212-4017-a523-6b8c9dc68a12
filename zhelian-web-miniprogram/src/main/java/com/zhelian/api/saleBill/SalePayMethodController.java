package com.zhelian.api.saleBill;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.service.ibeauty.salePayMethod.SalePayMethodService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class SalePayMethodController {

    @Autowired
    SalePayMethodService salePayMethodService;

    @RequestMapping(value = "/salePayMethod/all", method = {RequestMethod.POST})
    public BaseOutput all() {
        return salePayMethodService.allSalePayMethod();
    }

}