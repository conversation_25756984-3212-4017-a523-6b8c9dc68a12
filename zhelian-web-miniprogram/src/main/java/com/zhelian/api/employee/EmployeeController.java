package com.zhelian.api.employee;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.QueryPageForm;
import com.zhelian.model.khs.form.input.EmployeeQueryForm;
import com.zhelian.model.miniprogram.form.input.EmployeeAvatarAddForm;
import com.zhelian.model.miniprogram.form.input.EmployeeDetailQueryForm;
import com.zhelian.service.khs.employee.EmployeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class EmployeeController {

    @Autowired
    EmployeeService employeeService;


    /**
     * @Description:
     * @Param: [form]
     * @returns: com.zhelian.model.base.BaseOutput
     * @Author: 86158
     * @Date: 2020/10/29 9:11
     */
    @RequestMapping(value = "/employee/employeeDetail", method = {RequestMethod.POST})
    public BaseOutput employeeDetail(@RequestBody EmployeeDetailQueryForm form) {
        if (!StringUtils.hasText(form.EmployeeID))
            return BaseOutput.failed("员工不能为空");
        return employeeService.getEmployeeDetail(form);
    }

    @RequestMapping(value = "/employee/all", method = {RequestMethod.POST})
    public BaseOutput list() {
        return employeeService.empList();
    }


    @RequestMapping(value = "/employee/avatar", method = {RequestMethod.POST})
    public BaseOutput avatar(@RequestBody EmployeeAvatarAddForm form) {
        return employeeService.avatar(form);
    }

    @RequestMapping(value = "/employee/uploadAvatar", method = {RequestMethod.POST})
    public BaseOutput uploadAvatar(@RequestBody EmployeeAvatarAddForm form) {
        return employeeService.uploadAvatar(form);
    }


    @RequestMapping(value = "/employee/data", method = {RequestMethod.POST})
    public BaseOutput data(@RequestBody EmployeeQueryForm form) {
        return employeeService.data(form);
    }

    /**
     * 权限下的员工带分页
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/employee/permissionEmployeeByPage", method = {RequestMethod.POST})
    public BaseOutput permissionEmployeeByPage(@RequestBody QueryPageForm form) {
        return employeeService.permissionEmployeeByPage(form);
    }

    /**
     * 权限下的员工
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/employee/permissionEmployee", method = {RequestMethod.POST})
    public BaseOutput permissionEmployee(@RequestBody QueryPageForm form) {
        return employeeService.permissionEmployee(form);
    }
}