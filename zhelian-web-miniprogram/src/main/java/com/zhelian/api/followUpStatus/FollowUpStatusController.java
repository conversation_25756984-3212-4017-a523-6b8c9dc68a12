package com.zhelian.api.followUpStatus;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.PayMethodAddForm;
import com.zhelian.model.ibeauty.form.input.PayMethodMoveForm;
import com.zhelian.model.ibeauty.form.input.PayMethodQueryForm;
import com.zhelian.model.ibeauty.form.input.PayMethodUpdateForm;
import com.zhelian.service.CRM.followUpStatus.FollowUpStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class FollowUpStatusController {

    @Autowired
    FollowUpStatusService followUpStatusService;

    @RequestMapping(value = "/followUpStatus/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody PayMethodQueryForm form) {
        return followUpStatusService.all(form);
    }

    @RequestMapping(value = "/followUpStatus/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody PayMethodAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("支付方式名称不能为空");

        return followUpStatusService.create(form);
    }

    @RequestMapping(value = "/followUpStatus/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody PayMethodUpdateForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("支付方式名称不能为空");
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (form.Active == null)
            return BaseOutput.failed("有效性不能为空");

        return followUpStatusService.update(form);
    }

    @RequestMapping(value = "/followUpStatus/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody PayMethodMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");
        return followUpStatusService.move(form);
    }

}