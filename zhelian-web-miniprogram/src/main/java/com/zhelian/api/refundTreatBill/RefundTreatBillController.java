package com.zhelian.api.refundTreatBill;


import com.zhelian.core.utils.enumeration.BillChannel;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.service.ibeauty.refundTreat.RefundTreatBillCommonService;
import com.zhelian.service.ibeauty.refundTreat.RefundTreatBillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class RefundTreatBillController {

    @Autowired
    RefundTreatBillService refundTreatBillService;

    @Autowired
    RefundTreatBillCommonService refundTreatBillCommonService;

    @RequestMapping(value = "/refundTreatBill/treatList", method = {RequestMethod.POST})
    public BaseOutput treatList(@RequestBody RefundTreatBillQueryForm form) {
        return refundTreatBillService.treatList(form);
    }

    @RequestMapping(value = "/refundTreatBill/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody RefundTreatBillAddForm form) throws Exception {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        if (form.BillDate == null)
            return BaseOutput.failed("开单日期不能为空");
        if (form.Amount == null)
            return BaseOutput.failed("订单金额不能为空");
        return refundTreatBillService.create(form, BillChannel.BillChannelMiniprogram.getID());
    }

    @RequestMapping(value = "/refundTreatBill/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody RefundTreatBillListQueryForm form) {
        return refundTreatBillService.refundTreatBillList(form);
    }

    @RequestMapping(value = "/refundTreatBill/info", method = {RequestMethod.POST})
    public BaseOutput info(@RequestBody RefundTreatBillInfoQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("订单编号不能为空");
        return refundTreatBillService.refundTreatBillInfo(form);
    }

    @RequestMapping(value = "/refundTreatBill/approvalList", method = {RequestMethod.POST})
    public BaseOutput approvalList(@RequestBody RefundTreatBillListQueryForm form) {
        return refundTreatBillService.approvalList(form);
    }

    @RequestMapping(value = "/refundTreatBill/revoke", method = {RequestMethod.POST})
    public BaseOutput revoke(@RequestBody RefundTreatBillInfoQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("订单编号不能为空");
        return refundTreatBillService.revoke(form);
    }

    @RequestMapping(value = "/refundTreatBill/approval", method = {RequestMethod.POST})
    public BaseOutput approval(@RequestBody RefundTreatBillApprovalForm form) {
        if (form.ID == null)
            return BaseOutput.failed("订单编号不能为空");
        if (form.ApprovalStatus == null)
            return BaseOutput.failed("退款状态不能为空");
        return refundTreatBillCommonService.approval(form, BillChannel.BillChannelMiniprogram.getID());
    }
}