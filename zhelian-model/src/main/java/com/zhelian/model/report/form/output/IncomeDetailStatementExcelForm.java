package com.zhelian.model.report.form.output;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@EqualsAndHashCode
@Data
public class IncomeDetailStatementExcelForm {
    @ExcelProperty(value = "门店名")
    public String entityName;
    @ExcelProperty(value = "美容现金业绩")
    public BigDecimal beautyTotal;
    @ExcelProperty(value = "美容划卡合计")
    public BigDecimal beautyTreatTotal;
    @ExcelProperty(value = "美容卡金消耗合计")
    public BigDecimal beautyTotalMoney;
    @ExcelProperty(value = "美发现金业绩")
    public BigDecimal hairTotal;
    @ExcelProperty(value = "美发划卡合计")
    public BigDecimal hairTreatTotal;
    @ExcelProperty(value = "美发卡金消耗合计")
    public BigDecimal hairTotalMoney;
    @ExcelProperty(value = "进店率")
    public String toShopRate;
    @ExcelProperty(value = "复购率")
    public String againBuyRate;
    @ExcelProperty(value = "转化率")
    public String conversionRate;
}
