package com.zhelian.model.report.form.output;


import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@EqualsAndHashCode
@Data
public class EntitySalePayDetailStatementEasyExcelForm {

    @ExcelProperty(value = "订单编号")
    public String id;
    @ExcelProperty(value = "下单门店")
    public String entityName;
    @ExcelProperty(value = "订单类型")
    public String billType;
    @ExcelProperty(value = "订单金额")
    public BigDecimal amount;
    @ExcelProperty(value = "收银员")
    public String createdBy;
    @ExcelProperty(value = "下单日期")
    public String billDate;
    @ExcelProperty(value = "录单日期")
    public String createdOn;
    @ExcelProperty({"客户信息", "客户姓名"})
    public String name;
    @ExcelProperty({"客户信息", "客户编号"})
    public String code;
    @ExcelProperty({"客户信息", "手机号"})
    public String customerPhoneNumber;
    @ExcelProperty({"客户信息", "会员等级"})
    public String customerLevelName;
    @ExcelProperty({"客户信息", "信息来源"})
    public String customerSourceName;
    @ExcelProperty({"客户信息", "渠道来源"})
    public String channelName;
    @ExcelProperty({"客户信息", "介绍人"})
    public String introducerName;
    @ExcelProperty({"支付信息", "支付类型"})
    public String payTypeName;
    @ExcelProperty({"支付信息", "支付方式"})
    public String payMethodName;
    @ExcelProperty({"支付信息", "支付金额"})
    public BigDecimal payAmount;


}
