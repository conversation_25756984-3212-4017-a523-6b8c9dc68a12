package com.zhelian.model.report.form.output;

import java.math.BigDecimal;

public class ChannelSaleDetailStatementForm {
    public String BillID;
    public String BillDate;
    public String EntityName;
    public String CustomerName;
    public String CustomerCode;
    public String CustomerPhoneNumber;
    public String CustomerLevelName;
    public String CustomerSourceName;
    public String IntroducerName;
    public String CreatedOn;
    public String CustomerEntityName;
    public String EmployeeName;
    public String GoodsTypeName;
    public String CategoryName;
    public String GoodName;
    public BigDecimal Price;
    public Integer Quantity;
    public Boolean IsLargess;
    public BigDecimal PreferentialAmount;
    public BigDecimal TotalAmount;
    public BigDecimal LargessAmount;
    public BigDecimal ArrearAmount;
    public BigDecimal PayAmount;
    public BigDecimal SavingCardDeductionAmount;
    public BigDecimal LargessSavingCardDeductionAmount;
    public String Channel;
    public String ChannelName;
}
