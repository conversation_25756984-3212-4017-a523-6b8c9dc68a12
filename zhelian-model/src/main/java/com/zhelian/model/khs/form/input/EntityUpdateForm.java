package com.zhelian.model.khs.form.input;

import com.zhelian.model.khs.form.output.EntityPhotoUrlOutputForm;

import java.math.BigDecimal;
import java.util.List;

public class EntityUpdateForm {
    public Integer ID;
    public String EntityName;
    public Integer ParentID;
    public Boolean IsStore;
    public Boolean IsWarehouse;
    public Boolean Active;
    public String ProvinceCode;
    public String CityCode;
    public String AreaCode;
    public String AddressDetail;
    public BigDecimal Longitude;
    public BigDecimal Latitude;
    public String Remark;

    //后加
    public String EntityLogoURL;//门店logo
    public String ServiceTelephoneNumber;//客服电话
    public String BusinessStartTime;//营业开始时间
    public String BusinessEndTime;//营业结束时间
    public String Description;//门店描述
    public Boolean MarketingEntityActive;//是否启用网店
    public List<EntityBusinessDateForm> EntityBusinessDate;
    public List<EntityPhotoUrlOutputForm> PhotoURLList;
}
