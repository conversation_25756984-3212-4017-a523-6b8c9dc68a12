package com.zhelian.model.ibeauty.form.input;


import com.zhelian.model.base.BasePage;

public class CustomerQueryForm extends BasePage {

    public String Name;
    public String EmployeeID;
    public Integer CustomerLevelID;
    public Integer CustomerSourceID;
    public String StartDate;
    public String EndDate;
    public String BillStatus;
    public Boolean HideOfflineSaleBill;
    public Integer ServicerID;
    public String ServicerEmployeeID;
    public String ToShopStartDate;
    public String ToShopEndDate;
    public String MemberOnStartDate;
    public String MemberOnEndDate;
    public String IntroducerName;
    public String ChannelName;
    public String CreatedOnStartDate;
    public String CreatedOnEndDate;
}
