package com.zhelian.model.ibeauty.form.output;

import java.math.BigDecimal;
import java.util.List;

public class SaleBillEntityPerformancePackageCardOutputForm {

    public Integer SaleBillPackageCardID;
    public Integer PackageCardID;
    public String PackageCardName;
    public String Alias;
    public Integer Quantity;
    public BigDecimal Price;
    public BigDecimal TotalAmount;
    public BigDecimal ArrearAmount;
    public BigDecimal PricePreferentialAmount;
    public BigDecimal MemberPreferentialAmount;
    public BigDecimal CardPreferentialAmount;
    public BigDecimal PreferentialTotalAmount;
    public BigDecimal PayAmount;
    public BigDecimal CardDeductionTotalAmount;
    public BigDecimal CardDeductionAmount;
    public BigDecimal CardDeductionLargessAmount;
    public Boolean IsLargess;
    public BigDecimal AccountTotalAmount;
    public List<SaleBillEntityPerformancePackageCardProductListOutputForm> Product;
    public List<SaleBillEntityPerformancePackageCardProjectListOutputForm> Project;
    public List<SaleBillEntityPerformancePackageCardGeneralCardListOutputForm> GeneralCard;
    public List<SaleBillEntityPerformancePackageCardTimeCardListOutputForm> TimeCard;
    public List<SaleBillEntityPerformancePackageCardSavingCardListOutputForm> SavingCard;
}
