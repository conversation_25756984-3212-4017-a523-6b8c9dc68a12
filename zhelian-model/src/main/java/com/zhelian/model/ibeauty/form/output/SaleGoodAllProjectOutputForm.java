package com.zhelian.model.ibeauty.form.output;


import com.alibaba.fastjson.annotation.JSONField;

import java.math.BigDecimal;

public class SaleGoodAllProjectOutputForm {

    @JSONField(name = "ID")
    public Integer ID;
    @J<PERSON><PERSON>ield(name = "Name")
    public String Name;
    @JSONField(name = "Alias")
    public String Alias;
    @JSONField(name = "Price")
    public BigDecimal Price;
    @JSONField(name = "IsAllowLargess")
    public Boolean IsAllowLargess;
    @JSONField(name = "IsModifyPrice")
    public Boolean IsModifyPrice;
}
