package com.zhelian.model.ibeauty.form.input;

import java.util.List;

public class CustomerSkinReportReceiveForm {
    // 客户档案编号
    public String customer_id;
    
    // 报告编号
    public String report_id;
    
    // 机构编号
    public String channel_id;
    
    // 门店编号
    public String store_id;
    
    // 扫描设备的编号
    public String scanner_id;
    
    // 拍摄数据版本 1: P1设备拍摄数据 2: P2设备拍摄数据
    public Integer edition;
    
    // 扫描时间 时间戳 秒
    public Integer created_time;
    
    // 皮肤预览ICON图片
    public String icon;
    
    // 皮肤报告，pc端适配
    public String view_url;
    
    // 皮肤报告分享H5链接
    public String share_url;
    
    // 皮肤资源文件列表
    public List<ResourceItem> resources;
    
    // 皮肤报告相貌特征
    public List<ContentItem> contents;
    
    // P2拍摄测量数据，仅P2拍摄报告推送该数据
    public MeasuredResults measured_results;
    
    // 资源文件项
    public static class ResourceItem {
        // 资源文件类型 (目前只有zip和jpg类型)
        public String type;
        
        // 资源文件标志 (参考资源标志枚举)
        public String flag;
        
        // 资源文件云端访问链接
        public String url;
    }
    
    // 相貌特征项
    public static class ContentItem {
        
        public String number;

        // 相貌特征
        public String name;
        
        // 缺陷等级
        public String degree;
        
        // 缺陷等级值 0-3 整数值，值越大则越严重，与degree对应
        public Integer level;
        
        // 缺陷分值，值越大则越严重：P1范围0-5分 P2范围0-4分
        public Float result;
    }
    
    // 测量结果
    public static class MeasuredResults {
        // 品项列表
        public List<CategoryItem> categories;
        
        // 视觉特征列表
        public List<FeatureItem> features;
        
        // 品项项
        public static class CategoryItem {
            // 品项编号
            public String id;
            
            // 品项名称
            public String name;
            
            // 视觉特征编号列表
            public List<String> features;
            
            // 品项分值 100 分制
            public Float score;
            
            // 品项分值 4 分制 （值越高缺陷越严重）
            public Float result;
            
            // 程度
            public String degree;
        }
        
        // 视觉特征项
        public static class FeatureItem {
            // 视觉特征编号
            public String id;
            
            // 视觉特征名称
            public String name;
            
            // 测量特征列表
            public List<MeasureItem> measures;
            
            // 测量特征项
            public static class MeasureItem {
                // 测量特征编号
                public String id;
                
                // 测量特征标题
                public String title;
                
                // 测量特征得分 4 分制
                public Float degree;
                
                // 测量特征得分 100 分制
                public Float score;
                
                // 测量参数列表
                public List<ResultItem> results;
                
                // 测量参数项
                public static class ResultItem {
                    // 参数编号
                    public String id;
                    
                    // 参数标题
                    public String title;
                    
                    // 参数类型 详见测量参数类型说明
                    public String type;
                    
                    // 参数得分 4 分制（值越高缺陷越严重）
                    public Float degree;
                    
                    // 参数得分 100 分制
                    public Float score;
                    
                    // 参数值详见 测量参数类型说明
                    public Object value;
                    
                    // 参数描述
                    public String describe;
                }
            }
        }
    }
}