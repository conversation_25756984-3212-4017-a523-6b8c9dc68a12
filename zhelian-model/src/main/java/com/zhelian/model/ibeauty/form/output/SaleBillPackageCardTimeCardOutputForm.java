package com.zhelian.model.ibeauty.form.output;

import java.math.BigDecimal;


public class SaleBillPackageCardTimeCardOutputForm {

    public Integer SaleBillPackageCardID;
    public Integer SaleBillPackageCardTimeCardID;
    public Integer TimeCardID;
    public String Name;
    public String Alias;
    public Integer Quantity;
    public Integer PackageQuantity;
    public BigDecimal PackagePrice;
    public BigDecimal PackageTotalPrice;
    public BigDecimal ArrearAmount;
    public BigDecimal TotalAmount;
    public BigDecimal PayAmount;
    public BigDecimal PreferentialTotalAmount;
    public BigDecimal PricePreferentialAmount;
    public BigDecimal MemberPreferentialAmount;
    public BigDecimal CardPreferentialAmount;
    public Boolean IsLargess;
    public BigDecimal AccountTotalAmount;
    public BigDecimal BalanceArrearAmount;
    public Integer PackageCardID;
    public Integer PackageCardAccountID;
    public Integer PerformanceAmount;
    public Integer ValidType;
    public Integer ValidDay;
    public String Remark;
}
