package com.zhelian.model.ibeauty.entity;

import java.math.BigDecimal;
import java.util.Date;

public class SavingCardEntity {

    public Integer ID;
    public String Name;
    public String Alias;
    public Integer SavingCardCategoryID;
    public BigDecimal Price;
    public BigDecimal LargessPrice;
    public Date SaleStartDate;
    public Date SaleEndingDate;
    public Integer ValidType;
    public Integer ValidDay;
    public Boolean IsAllowSell;
    public Boolean IsSettingLargess;
    public Boolean IsAllowSaleDeduct;
    public Boolean IsAllowConsumeDeduct;
    public Boolean IsGoodsRange;
    public Boolean IsLargessGoodsRange;
    public Boolean IsUseRechargeRules;
    public Boolean IsModifyPrice;
    public Boolean IsModifyLargessPrice;
    public String Remark;
    public String Memo;
}
