package com.zhelian.model.ibeauty.form.output;


import java.math.BigDecimal;
import java.util.List;

public class SaleBillPackageCardSavingCardListOutputForm {
    public Integer SaleBillPackageCardSavingCardID;
    public Integer SaleBillPackageCardID;
    public Integer SavingCardID;
    public String SavingCardName;
    public String Alias;
    public Integer Quantity;
    public BigDecimal Amount;
    public BigDecimal TotalAmount;
    public BigDecimal AccountTotalAmount;
    public BigDecimal AccountLargessAmount;
    public BigDecimal ArrearAmount;
    public BigDecimal LargessAmount;
    public BigDecimal Price;
    public BigDecimal LargessPrice;
    public BigDecimal PayAmount;
    public Boolean IsLargess;
    public List<SaleBillSavingCardHandlerListOutputForm> SaleBillHandler;
}
