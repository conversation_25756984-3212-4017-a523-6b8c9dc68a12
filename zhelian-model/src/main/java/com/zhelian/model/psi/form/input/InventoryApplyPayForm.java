package com.zhelian.model.psi.form.input;

import java.math.BigDecimal;

public class InventoryApplyPayForm {

    public String ID;
    public String BillStatus;//单据状态(10：待审核、15：预付款,20：待配送、30：待入库、40：已驳回、50：已完成、60：已取消)
    public BigDecimal Balance;
    public BigDecimal LargessBalance;
    public String PaymentWay;//付款方式（10：离线转账，20：余额支付）
    public String ReceiptNumber;
    public String PaymentAccountName;
    public String Remark;
    public String RejectReason;
}