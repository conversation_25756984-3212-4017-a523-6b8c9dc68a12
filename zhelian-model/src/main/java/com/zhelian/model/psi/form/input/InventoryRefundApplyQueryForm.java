/**
 * fileName: ProductQueryForm.java
 * author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * date: 2020/3/14 2:24 PM
 * copyright(c) 2019-2020 xxx公司
 */
package com.zhelian.model.psi.form.input;

import com.zhelian.model.base.BasePage;

/**
 * @className: ProductQueryForm
 * @description:
 * @version: V1.0
 * @author: <PERSON><PERSON>ian<PERSON><PERSON>
 * @date: 2020/3/14 2:24 PM
 */
public class InventoryRefundApplyQueryForm extends BasePage {

    public String ID;
    public Integer EntityID;
    public String BillStatus;//单据状态(10：待审核、,15：代付款20：待配送、30：待入库、40：已驳回、50：已完成、60：已取消)
    public String StartDate;
    public String EndDate;
    public String ProductName;
}