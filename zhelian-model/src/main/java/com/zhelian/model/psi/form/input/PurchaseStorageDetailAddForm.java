/**
 * fileName: ProductQueryForm.java
 * author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * date: 2020/3/14 2:24 PM
 * copyright(c) 2019-2020 xxx公司
 */
package com.zhelian.model.psi.form.input;

import java.math.BigDecimal;

/**
 * @className: ProductQueryForm
 * @description:
 * @version: V1.0
 * @author: <PERSON><PERSON>ian<PERSON><PERSON>
 * @date: 2020/3/14 2:24 PM
 */
public class PurchaseStorageDetailAddForm {

    public Integer ProductID;
    public Integer UnitID;
    public BigDecimal UnitPrice;
    public Integer Quantity;
    public BigDecimal Amount;
    public Integer MinimumUnitID;
    public Integer MinimumUnitQuantity;
}