/**
 * fileName: PurchaseStorageQueryForm.java
 * author: WangXianXian
 * date: 2020/3/14 2:24 PM
 * copyright(c) 2019-2020 xxx公司
 */
package com.zhelian.model.psi.form.input;

import com.zhelian.model.base.BasePage;

/**
 * @className: PurchaseStorageQueryForm
 * @description: 采购入库查询表单
 * @version: V1.0
 * @author: WangXianXian
 * @date: 2020/3/14 2:24 PM
 */
public class PurchaseStorageQueryForm extends BasePage {

    public String ID;
    public Integer EntityID;
    public Integer SupplierID;
    public String StartDate;
    public String EndDate;
    public String ProductName;

    // 新增字段 - 采购入库流程优化
    /**
     * 单据状态筛选：10-待确认，15-部分确认，20-已确认
     */
    public String BillStatus;
}