package com.zhelian.model.psi.form.output;

import java.math.BigDecimal;
import java.util.List;

public class entityRefundApplyInfoOutputForm {

    public String ID;

    public String BillStatus;//单据状态(10：待审核、20：待配送、30：待入库、40：已驳回、50：已完成、60：已取消)

    public String CreatedBy;//创建人/申请人

    public String CreatedByName;//创建人/申请人

    public String CreatedOn;//创建日期

    public String SettlementWay;//结算方式（10：先货后款；20：先款后货）

    public String ApprovedBy;//审核人

    public String ApprovedByName;//审核人

    public String ApprovedOn;//审核时间

    public String RefundInboundBy;//配送人

    public String RefundInboundByName;//配送人

    public String RefundInboundOn;//入库日期

    public String RefundOutboundBy;//退货出库操作人

    public String RefundOutboundByName;//退货出库操作人

    public String RefundOutboundOn;//退货出库日期

    public String Remark;//备注

    public String RejectReason;//拒绝理由

    public BigDecimal ApprovedTotalAmount;//出库总额

    public String RefundOutboundBillID;//退货出库单号

    public Integer RefundOutboundEntityID;//申请仓库/退货仓库

    public String RefundOutboundEntityName;//申请仓库/退货仓库

    public BigDecimal RefundOutboundTotalAmount;//退货总额

    public String RefundInboundBillID;//配送入库单号

    public Integer RefundInboundEntityID;//配送仓库

    public String RefundInboundEntityName;//配送仓库

    public BigDecimal RefundInboundTotalAmount;//入库总额

    public BigDecimal Balance;

    public BigDecimal LargessBalance;

    public List<InventoryRefundInfoDetailOutputForm> Detail;

}