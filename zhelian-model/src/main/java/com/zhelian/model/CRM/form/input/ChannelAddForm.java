package com.zhelian.model.CRM.form.input;

import java.math.BigDecimal;
import java.util.List;

public class ChannelAddForm {
    public String Name;
    public Integer ChannelTypeID;
    public Integer ChannelLevelID;
    public List<ChannelDeveloperAddForm> DeveloperList;
    public List<ChannelConsultantAddForm> ConsultantList;
    public Integer ParentID;
    public String ContactPersonName;
    public String ContactPersonMobile;
    public String ContactPersonIDNumber;
    public String ProvinceCode;
    public String CityCode;
    public String ApprovalStatus;
    public String AreaCode;
    public String AddressDetail;
    public String Remark;
    public Boolean Active;
    public BigDecimal Latitude;
    public BigDecimal Longitude;
    public List<IntroducerAddForm> IntroducerList;
    public List<ChannelCompanyAddForm> Company;
    public List<ChannelContractAddForm> Contract;
}
