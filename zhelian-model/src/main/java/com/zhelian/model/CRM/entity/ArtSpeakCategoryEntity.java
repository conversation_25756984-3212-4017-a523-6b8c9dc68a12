/**
 * fileName: Unit.java
 * author: <PERSON>XianX<PERSON>
 * date: 2020/3/10 4:21 PM
 * copyright(c) 2019-2020 xxx公司
 */
package com.zhelian.model.CRM.entity;

import com.alibaba.fastjson2.annotation.JSONField;

/**
 * @className: Unit
 * @description:
 * @version: V1.0
 * @author: zhoutianxiang
 * @date: 2020/4/17 4:21 PM
 */
public class ArtSpeakCategoryEntity {

    @JSONField(name = "ID")
    public Integer ID;
    @JSONField(name = "Name")
    public String Name;
    @JSONField(name = "Sequence")
    public Integer Sequence;
    @JSONField(name = "ParentID")
    public Integer ParentID;
    @JSONField(name = "Active")
    public Boolean Active;

}