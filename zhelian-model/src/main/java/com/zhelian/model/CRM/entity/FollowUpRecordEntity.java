package com.zhelian.model.CRM.entity;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-01-15
 * @description 跟进记录实体类
 * @version 1.0
 */
public class FollowUpRecordEntity {

    public Integer ID;
    public Integer CustomerID;
    public Integer FollowUpMethodID;
    public Integer FollowUpStatusID;
    public String FollowUpContent;
    public Boolean IsFollowUp;
    public Date PlannedOn;
    public String PlannedRemark;
    public Date FollowUpOn;
    public String FollowUpBy;
    public Date CreatedOn;
    public String CreatedBy;

    /**
     * 线索来源
     * 用于标识该跟进记录的线索来源渠道
     * 对应 LeadSourceEnum 中的 code 值
     */
    public String LeadSource;
}
