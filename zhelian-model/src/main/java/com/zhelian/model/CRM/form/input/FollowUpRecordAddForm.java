package com.zhelian.model.CRM.form.input;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-01-15
 * @description 跟进记录添加表单
 * @version 1.0
 */
public class FollowUpRecordAddForm {

    public Integer CustomerID;
    public Integer FollowUpMethodID;
    public Integer FollowUpStatusID;
    public String FollowUpContent;
    public String PlannedOn;
    public String PlannedRemark;
    public String FollowUpBy;
    public Boolean IsNextFollowUp;
    public List<FollowUpRecordAttachmentAddForm> Attachment;

    /**
     * 线索来源
     * 对应 LeadSourceEnum 中的 code 值
     */
    public String LeadSource;

}
