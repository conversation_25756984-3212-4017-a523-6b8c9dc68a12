package com.zhelian.model.CRM.form.output;


import java.math.BigDecimal;
import java.util.List;

public class ChannelVisitDetailOutputForm {

    public Integer ID;
    public Integer ChannelID;
    public String ChannelName;
    public String ChannelTypeName;
    public String ChannelLevelName;
    public String AddressDetail;
    public String ApprovalStatus;
    public String VisitType;
    public String VisitBy;
    public String VisitByName;
    public String VisitStatus;
    public String PlannedOn;
    public String PlannedRemark;
    public String SignInOn;
    public String SignInAddress;
    public BigDecimal SignInLongitude;
    public BigDecimal SignInLatitude;
    public String SignInRemark;
    public String VisitRecord;
    public String SignOutOn;
    public String SignOutAddress;
    public BigDecimal SignOutLongitude;
    public BigDecimal SignOutLatitude;
    public String SignOutRemark;
    public Integer VisitQuantity;
    public String RecentVisitDate;
    public BigDecimal SignInDistance;
    public BigDecimal SignOutDistance;

    public List<ChannelVisitAssistorOutputForm> Assistor;
    public List<ChannelVisitAttachmentOutputForm> VisitRecordAttachment;
    public List<ChannelVisitAttachmentOutputForm> SignInAttachment;
    public List<ChannelVisitAttachmentOutputForm> SignOutAttachment;

}
