package com.zhelian.model.CRM.form.input;


import java.math.BigDecimal;
import java.util.List;

public class FollowUpConditionQueryForm {

    public Integer AgeStart;
    public Integer AgeEnd;
    public String AgeStartDate;
    public String AgeEndDate;
    public String BirthDayStartDate;
    public String BirthDayEndDate;
    public String LunarBirthDayStartDate;
    public String LunarBirthDayEndDate;

    public String ExtendBirthDayStartDate;
    public String ExtendBirthDayEndDate;
    public String ExtendLunarBirthDayStartDate;
    public String ExtendLunarBirthDayEndDate;
    public BigDecimal CardBalanceAmountStart;
    public BigDecimal CardBalanceAmountEnd;
    public Integer CardBalanceTimesStart;
    public Integer CardBalanceTimesEnd;
    public String CardExpiredDateStartDate;
    public String CardExpiredDateEndDate;
    public Integer CardExpiredDayStart;
    public Integer CardExpiredDayEnd;

    public String CreatedOnDateStartDate;
    public String CreatedOnDateEndDate;
    public Integer CreatedOnDayStart;
    public Integer CreatedOnDayEnd;

    public Integer[] CustomerLevel;
    public Integer[] CustomerSource;
    public Integer[] CustomerTag;
    public String Gender;


    public BigDecimal SaleAmountStart;
    public BigDecimal SaleAmountEnd;
    public BigDecimal SaleAverageStart;
    public BigDecimal SaleAverageEnd;
    public Integer SaleCountStart;
    public Integer SaleCountEnd;
    public Integer SaleNoRecent;
    public Integer SaleRecent;


    public BigDecimal TreatAmountStart;
    public BigDecimal TreatAmountEnd;
    public BigDecimal TreatAverageStart;
    public BigDecimal TreatAverageEnd;
    public Integer TreatCountStart;
    public Integer TreatCountEnd;
    public Integer TreatNoRecent;
    public Integer TreatRecent;


    public List<Integer> Entity;
}