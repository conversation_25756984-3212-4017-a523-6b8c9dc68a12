/**
 * <AUTHOR>
 * @date 2025-01-15
 * @description 线索来源枚举类
 * @version 1.0
 */
package com.zhelian.model.CRM.enums;

/**
 * 线索来源枚举
 * 用于标识跟进记录的线索来源渠道
 */
public enum LeadSourceEnum {

    /**
     * 抖音信息流
     */
    DOUYIN_XINXILIU("DOUYIN_XINXILIU", "抖音信息流"),

    /**
     * 抖音团购
     */
    DOUYIN_TUANGOU("DOUYIN_TUANGOU", "抖音团购"),

    /**
     * 本地线索推广
     */
    BENDI_XIANSUO_TUIGUANG("BENDI_XIANSUO_TUIGUANG", "本地线索推广"),

    /**
     * 美团点评
     */
    MEITUAN_DIANPING("MEITUAN_DIANPING", "美团点评"),

    /**
     * 本地生活直播
     */
    BENDI_SHENGHUO_ZHIBO("BENDI_SHENGHUO_ZHIBO", "本地生活直播"),

    /**
     * 抖音本地推-私信
     */
    DOUYIN_BENDITUISIXIN("DOUYIN_BENDITUISIXIN", "抖音本地推-私信"),

    /**
     * 微信朋友圈
     */
    WEIXIN_PENGYOUQUAN("WEIXIN_PENGYOUQUAN", "微信朋友圈"),

    /**
     * 抖音-在线咨询
     */
    DOUYIN_ZAIXIANZIXUN("DOUYIN_ZAIXIANZIXUN", "抖音-在线咨询"),

    /**
     * 高德地图
     */
    GAODE_DITU("GAODE_DITU", "高德地图"),

    /**
     * 小红书
     */
    XIAOHONGSHU("XIAOHONGSHU", "小红书"),

    /**
     * 自然到店
     */
    ZIRAN_DAODIAN("ZIRAN_DAODIAN", "【自然到店】"),

    /**
     * 老带新
     */
    LAO_DAI_XIN("LAO_DAI_XIN", "【老带新】");
    
    private final String code;
    private final String description;
    
    /**
     * 构造函数
     * @param code 来源代码
     * @param description 来源描述
     */
    LeadSourceEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    /**
     * 获取来源代码
     * @return 来源代码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 获取来源描述
     * @return 来源描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举
     * @param code 来源代码
     * @return 对应的枚举值，如果找不到返回null
     */
    public static LeadSourceEnum getByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        
        for (LeadSourceEnum leadSource : LeadSourceEnum.values()) {
            if (leadSource.getCode().equals(code)) {
                return leadSource;
            }
        }
        return null;
    }
    
    /**
     * 检查代码是否有效
     * @param code 来源代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
}
