package com.zhelian.model.CRM.form.input;

import java.util.List;

public class ConsultationForm {
    private Integer id; // 用于更新时指定ID，新增时不需要
    private String name;
    private String phoneNumber;
    private String gender;
    private Integer age;
    private Boolean isMarried;
    private String provinceCode;
    private String cityCode;
    private String areaCode;
    private String address;
    private List<String> spotRemoval;
    private List<String> cosmetic;
    private List<String> skinCare;
    private String brand;
    private String stainsDuration;
    private List<String> demand;
    private Integer entityId;
    private String employeeId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public Boolean getIsMarried() {
        return isMarried;
    }

    public void setIsMarried(Boolean isMarried) {
        this.isMarried = isMarried;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public List<String> getSpotRemoval() {
        return spotRemoval;
    }

    public void setSpotRemoval(List<String> spotRemoval) {
        this.spotRemoval = spotRemoval;
    }

    public List<String> getCosmetic() {
        return cosmetic;
    }

    public void setCosmetic(List<String> cosmetic) {
        this.cosmetic = cosmetic;
    }

    public List<String> getSkinCare() {
        return skinCare;
    }

    public void setSkinCare(List<String> skinCare) {
        this.skinCare = skinCare;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getStainsDuration() {
        return stainsDuration;
    }

    public void setStainsDuration(String stainsDuration) {
        this.stainsDuration = stainsDuration;
    }

    public List<String> getDemand() {
        return demand;
    }

    public void setDemand(List<String> demand) {
        this.demand = demand;
    }

    public Integer getEntityId() {
        return entityId;
    }

    public void setEntityId(Integer entityId) {
        this.entityId = entityId;
    }

    public String getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }
}