/**
 * fileName: ProductQueryForm.java
 * author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * date: 2020/3/14 2:24 PM
 * copyright(c) 2019-2020 xxx公司
 */
package com.zhelian.model.CRM.form.input;

import com.zhelian.model.base.BasePage;

/**
 * @className: ProductQueryForm
 * @description:
 * @version: V1.0
 * @author: zhoutianxiang
 * @date: 2020/4/17 10:48 PM
 */
public class ArtSpeakQueryForm extends BasePage {

    public String Name;

    public Integer ArtSpeakCategoryID;

    public Boolean Active;

}