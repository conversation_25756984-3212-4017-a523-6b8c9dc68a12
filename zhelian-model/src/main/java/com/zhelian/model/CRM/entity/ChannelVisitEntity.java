package com.zhelian.model.CRM.entity;


import java.math.BigDecimal;
import java.util.Date;

public class ChannelVisitEntity {

    public Integer ID;
    public Integer ChannelID;
    public String VisitType;//拜访类型（10：线下拜访、20：远程拜访）
    public String VisitBy;
    public String VisitStatus;//拜访状态（10：待拜访、20：拜访中、30：已拜访、40：已取消）
    public Date PlannedOn;
    public String PlannedRemark;
    public Date SignInOn;
    public Boolean IsPlan;
    public String SignInAddress;
    public BigDecimal SignInLongitude;
    public BigDecimal SignInLatitude;
    public String SignInRemark;
    public String VisitRecord;
    public Date SignOutOn;
    public String SignOutAddress;
    public BigDecimal SignOutLongitude;
    public BigDecimal SignOutLatitude;
    public String SignOutRemark;
}