package com.zhelian.model.CRM.form.output;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
@Data
public class ChannelOutputExcelForm {

    @ExcelProperty(value = "渠道名称")
    public String name;

    @ExcelProperty(value = "渠道类型")
    public String channelTypeName;

    @ExcelProperty(value = "渠道等级")
    public String channelLevelName;

    @ExcelProperty(value = "上级渠道")
    public String parentName;

    @ExcelProperty(value = "开发人员")
    public String developerName;

    @ExcelProperty(value = "市场咨询")
    public String consultantName;

    @ExcelProperty(value = "创建时间")
    public String createdOn;

    @ExcelProperty(value = "有效性")
    public String active;
}
