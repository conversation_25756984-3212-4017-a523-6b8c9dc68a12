package com.zhelian.model.CRM.entity;


import com.alibaba.fastjson2.annotation.JSONField;

import java.math.BigDecimal;
import java.util.Date;

public class ChannelEntity {
    @JSONField(name = "ID")
    public Integer ID;
    @JSONField(name = "Name")
    public String Name;
    @J<PERSON><PERSON>ield(name = "Sequence")
    public Integer Sequence;
    @JSONField(name = "ChannelTypeID")
    public Integer ChannelTypeID;
    @JSONField(name = "ChannelLevelID")
    public Integer ChannelLevelID;
    @JSONField(name = "ApprovalStatus")
    public String ApprovalStatus;
    @JSONField(name = "ParentID")
    public Integer ParentID;
    @JSONField(name = "ContactPersonName")
    public String ContactPersonName;
    @JSONField(name = "ContactPersonMobile")
    public String ContactPersonMobile;
    @JSONField(name = "ProvinceCode")
    public String ProvinceCode;
    @JSONField(name = "CityCode")
    public String CityCode;
    @JSONField(name = "AreaCode")
    public String AreaCode;
    @J<PERSON><PERSON>ield(name = "AddressDetail")
    public String AddressDetail;
    @J<PERSON>NField(name = "Remark")
    public String Remark;
    @J<PERSON><PERSON>ield(name = "Active")
    public Boolean Active;
    @JSONField(name = "Longitude")
    public BigDecimal Longitude;
    @JSONField(name = "Latitude")
    public BigDecimal Latitude;
    @JSONField(name = "CreatedBy")
    public String CreatedBy;
    @JSONField(name = "CreatedOn")
    public Date CreatedOn;
    @JSONField(name = "ContactPersonIDNumber")
    public String ContactPersonIDNumber;
}