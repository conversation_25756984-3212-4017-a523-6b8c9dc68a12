package com.zhelian.service.weixin;

import com.zhelian.core.authorization.customer.SystemCustomerUtil;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.utils.DateTimeUtil;
import com.zhelian.core.utils.enumeration.WeiXinOfficialAccountMessage;
import com.zhelian.core.weixin.open.service.WxOpenService;
import com.zhelian.mapper.enterprise.EnterpriseMapper;
import com.zhelian.mapper.enterprise.EnterpriseOplatformMapper;
import com.zhelian.mapper.ibeauty.*;
import com.zhelian.mapper.micromall.MessageNotificationMapper;
import com.zhelian.model.MC.entity.EnterpriseOffiaccountEntity;
import com.zhelian.model.ibeauty.form.output.*;
import com.zhelian.model.micromall.entity.MessageConfigEntity;
import me.chanjar.weixin.mp.api.WxMpTemplateMsgService;
import me.chanjar.weixin.mp.bean.template.WxMpTemplate;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class WeiXinMessageService {
    @Autowired
    SaleBillMapper saleBillMapper;
    @Autowired
    EnterpriseOplatformMapper enterpriseOplatformMapper;
    @Autowired
    WxOpenService wxOpenService;
    @Autowired
    MessageNotificationMapper messageNotificationMapper;
    @Autowired
    TreatBillMapper treatBillMapper;
    @Autowired
    EnterpriseMapper enterpriseMapper;
    @Autowired
    TranserMapper transerMapper;
    @Autowired
    AppointmentBillMapper appointmentBillMapper;

    @Autowired
    PostponeBillMapper postponeBillMapper;
    private static final Logger logger = LoggerFactory.getLogger(WeiXinMessageService.class);

    /**
     * 销售
     *
     * @param saleBillID
     */
    public void setSaleBillWxMessage(String saleBillID, String code) {
        try {

            WXSaleBillInfoOutputForm wxSaleBill = saleBillMapper.getWXSaleBill(saleBillID);
            EnterpriseOffiaccountEntity enterpriseOffiaccountEntity = enterpriseOplatformMapper.getEnterpriseOffiaccount(SystemUserUtil.getSystemEnterpriseCode());
            if (enterpriseOffiaccountEntity != null && StringUtils.isNotBlank(wxSaleBill.OffiaccountOpenID)) {
                MessageConfigEntity messageConfigEntity = messageNotificationMapper.getMessageConfigEntity(code);
                if (messageConfigEntity.IsSendWechat && org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatPublicLibraryTemplate)) {
                    WxMpTemplateMsgService templateMsgService = wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(enterpriseOffiaccountEntity.AuthorizerAppID).getTemplateMsgService();
                    if (!org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatEnterpriseTemplate)) {
                        List<String> list = Arrays.asList("门店名称","交易金额","交易订单","交易时间");
                        messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate,list);
                        messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                    } else {
                        List<WxMpTemplate> allPrivateTemplate = templateMsgService.getAllPrivateTemplate();
                        if (!allPrivateTemplate.stream().filter(m -> m.getTemplateId().equals(messageConfigEntity.WechatEnterpriseTemplate)).findAny().isPresent()) {
                            List<String> list = Arrays.asList("门店名称","交易金额","交易订单","交易时间");
                            messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate,list);

                            messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                        }
                    }
                    WxMpTemplateMessage wxMpTemplateMessage = new WxMpTemplateMessage();
                    wxMpTemplateMessage.setTemplateId(messageConfigEntity.WechatEnterpriseTemplate);
                    wxMpTemplateMessage.setToUser(wxSaleBill.OffiaccountOpenID);
                    List<WxMpTemplateData> wxMpTemplateDataList = Arrays.asList(
                            new WxMpTemplateData("thing1", wxSaleBill.EntityName),
                            new WxMpTemplateData("amount3", wxSaleBill.PayAmount.toString()),
                            new WxMpTemplateData("character_string6", wxSaleBill.ID),
                            new WxMpTemplateData("time5", wxSaleBill.BillDate)
                    );
                    wxMpTemplateMessage.setData(wxMpTemplateDataList);
                    templateMsgService.sendTemplateMsg(wxMpTemplateMessage);
                }

            }
        } catch (Exception e) {
            logger.error("发送模板消息异常：{}", e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 销售取消
     *
     * @param saleBillID
     */
    public void setCancelSaleBillWxMessage(String saleBillID, String code) {
        try {
            WXCancelSaleBillInfoOutputForm wxSaleBill = saleBillMapper.getWXCancelSaleBill(saleBillID);
            EnterpriseOffiaccountEntity enterpriseOffiaccountEntity = enterpriseOplatformMapper.getEnterpriseOffiaccount(SystemUserUtil.getSystemEnterpriseCode());
            if (enterpriseOffiaccountEntity != null && StringUtils.isNotBlank(wxSaleBill.OffiaccountOpenID)) {
                MessageConfigEntity messageConfigEntity = messageNotificationMapper.getMessageConfigEntity(code);
                if (messageConfigEntity.IsSendWechat && org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatPublicLibraryTemplate)) {
                    WxMpTemplateMsgService templateMsgService = wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(enterpriseOffiaccountEntity.AuthorizerAppID).getTemplateMsgService();
                    if (!org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatEnterpriseTemplate)) {
                        List<String> list = Arrays.asList("门店名称","交易金额","交易时间","撤销原因");
                        messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate,list);
                        ;
                        messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                    } else {
                        List<WxMpTemplate> allPrivateTemplate = templateMsgService.getAllPrivateTemplate();
                        if (!allPrivateTemplate.stream().filter(m -> m.getTemplateId().equals(messageConfigEntity.WechatEnterpriseTemplate)).findAny().isPresent()) {
                            List<String> list = Arrays.asList("门店名称","交易金额","交易时间","撤销原因");
                            messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate,list);

                            messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                        }
                    }
                    WxMpTemplateMessage wxMpTemplateMessage = new WxMpTemplateMessage();
                    wxMpTemplateMessage.setTemplateId(messageConfigEntity.WechatEnterpriseTemplate);
                    wxMpTemplateMessage.setToUser(wxSaleBill.OffiaccountOpenID);
                    List<WxMpTemplateData> wxMpTemplateDataList = Arrays.asList(
                            new WxMpTemplateData("thing6", wxSaleBill.EntityName),
                            new WxMpTemplateData("amount2", wxSaleBill.Amount.toString()),
                            new WxMpTemplateData("time3", wxSaleBill.BillDate),
                            new WxMpTemplateData("thing5", wxSaleBill.Remark)
                    );
                    wxMpTemplateMessage.setData(wxMpTemplateDataList);
                    templateMsgService.sendTemplateMsg(wxMpTemplateMessage);
                }

            }
        } catch (Exception e) {
            logger.error("发送模板消息异常：{}", e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 销售退款
     *
     * @param saleBillID
     */
    public void setRefundSaleBillWxMessage(String saleBillID, String code) {
        try {
            WXRefundSaleBillInfoOutputForm wxSaleBill = saleBillMapper.getWXRefundSaleBill(saleBillID);
            EnterpriseOffiaccountEntity enterpriseOffiaccountEntity = enterpriseOplatformMapper.getEnterpriseOffiaccount(SystemUserUtil.getSystemEnterpriseCode());
            if (enterpriseOffiaccountEntity != null && StringUtils.isNotBlank(wxSaleBill.OffiaccountOpenID)) {
                MessageConfigEntity messageConfigEntity = messageNotificationMapper.getMessageConfigEntity(code);
                if (messageConfigEntity.IsSendWechat && org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatPublicLibraryTemplate)) {
                    WxMpTemplateMsgService templateMsgService = wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(enterpriseOffiaccountEntity.AuthorizerAppID).getTemplateMsgService();
                    if (!org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatEnterpriseTemplate)) {
                        List<String> list = Arrays.asList("门店名称","退款金额","订单号","操作时间");
                        messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate,list);
                        ;
                        messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                    } else {
                        List<WxMpTemplate> allPrivateTemplate = templateMsgService.getAllPrivateTemplate();
                        if (!allPrivateTemplate.stream().filter(m -> m.getTemplateId().equals(messageConfigEntity.WechatEnterpriseTemplate)).findAny().isPresent()) {
                            List<String> list = Arrays.asList("门店名称","退款金额","订单号","操作时间");
                            messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate,list);

                            messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                        }
                    }
                    WxMpTemplateMessage wxMpTemplateMessage = new WxMpTemplateMessage();
                    wxMpTemplateMessage.setTemplateId(messageConfigEntity.WechatEnterpriseTemplate);
                    wxMpTemplateMessage.setToUser(wxSaleBill.OffiaccountOpenID);
                    List<WxMpTemplateData> wxMpTemplateDataList = Arrays.asList(
                            new WxMpTemplateData("thing5", wxSaleBill.EntityName),
                            new WxMpTemplateData("amount2", wxSaleBill.PayAmount.toString()),
                            new WxMpTemplateData("character_string8", wxSaleBill.ID),
                            new WxMpTemplateData("time4", wxSaleBill.BillDate)
                    );
                    wxMpTemplateMessage.setData(wxMpTemplateDataList);
                    templateMsgService.sendTemplateMsg(wxMpTemplateMessage);
                }

            }
        } catch (Exception e) {
            logger.error("发送模板消息异常：{}", e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 消耗
     *
     * @param treatBillID
     */
    public void setTreatBillWxMessage(String treatBillID, String code) {
        try {

            List<WXTreatBillInfoOutputForm> wxTreatBillList = treatBillMapper.getWXTreatBill(treatBillID);
            EnterpriseOffiaccountEntity enterpriseOffiaccountEntity = enterpriseOplatformMapper.getEnterpriseOffiaccount(SystemUserUtil.getSystemEnterpriseCode());
            if (enterpriseOffiaccountEntity != null && wxTreatBillList.size() > 0) {
                MessageConfigEntity messageConfigEntity = messageNotificationMapper.getMessageConfigEntity(code);
                if (messageConfigEntity.IsSendWechat && org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatPublicLibraryTemplate)) {
                    WxMpTemplateMsgService templateMsgService = wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(enterpriseOffiaccountEntity.AuthorizerAppID).getTemplateMsgService();
                    if (!org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatEnterpriseTemplate)) {
                        List<String> list = Arrays.asList("门店名称","服务项目","消费数量","服务时间");
                        messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate,list);
                        ;
                        messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                    } else {
                        List<WxMpTemplate> allPrivateTemplate = templateMsgService.getAllPrivateTemplate();
                        if (!allPrivateTemplate.stream().filter(m -> m.getTemplateId().equals(messageConfigEntity.WechatEnterpriseTemplate)).findAny().isPresent()) {
                            List<String> list = Arrays.asList("门店名称","服务项目","消费数量","服务时间");
                            messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate,list);

                            messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                        }
                    }

                    for (WXTreatBillInfoOutputForm wxTreatBill : wxTreatBillList) {
                        if (StringUtils.isNotBlank(wxTreatBill.OffiaccountOpenID)) {
                            WxMpTemplateMessage wxMpTemplateMessage = new WxMpTemplateMessage();
                            wxMpTemplateMessage.setTemplateId(messageConfigEntity.WechatEnterpriseTemplate);
                            wxMpTemplateMessage.setToUser(wxTreatBill.OffiaccountOpenID);
                            List<WxMpTemplateData> wxMpTemplateDataList = Arrays.asList(
                                    new WxMpTemplateData("thing5", wxTreatBill.EntityName),
                                    new WxMpTemplateData("thing3", wxTreatBill.TreatMessage),
                                    new WxMpTemplateData("short_thing4", wxTreatBill.Quantity),
                                    new WxMpTemplateData("time11", wxTreatBill.BillDate)

                            );
                            wxMpTemplateMessage.setData(wxMpTemplateDataList);
                            try{
                                templateMsgService.sendTemplateMsg(wxMpTemplateMessage);
                            }catch (Exception e){
                                logger.error("发送模板消息异常：{}", e.getMessage());
                                e.printStackTrace();
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("发送模板消息异常：{}", e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 消耗取消
     *
     * @param treatBillID
     */
    public void setCancelTreatBillWxMessage(String treatBillID, String code) {
        try {

            List<WXCancelTreatBillInfoOutputForm> wxCancelTreatBillList = treatBillMapper.getWXCancelTreatBill(treatBillID);
            EnterpriseOffiaccountEntity enterpriseOffiaccountEntity = enterpriseOplatformMapper.getEnterpriseOffiaccount(SystemUserUtil.getSystemEnterpriseCode());
            if (enterpriseOffiaccountEntity != null && wxCancelTreatBillList.size() > 0) {
                MessageConfigEntity messageConfigEntity = messageNotificationMapper.getMessageConfigEntity(code);
                if (messageConfigEntity.IsSendWechat && org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatPublicLibraryTemplate)) {
                    WxMpTemplateMsgService templateMsgService = wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(enterpriseOffiaccountEntity.AuthorizerAppID).getTemplateMsgService();
                    if (!org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatEnterpriseTemplate)) {
                        List<String> list = Arrays.asList("门店名称","服务项目","撤销时间");
                        messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate,list);
                        ;
                        messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                    } else {
                        List<WxMpTemplate> allPrivateTemplate = templateMsgService.getAllPrivateTemplate();
                        if (!allPrivateTemplate.stream().filter(m -> m.getTemplateId().equals(messageConfigEntity.WechatEnterpriseTemplate)).findAny().isPresent()) {
                            List<String> list = Arrays.asList("门店名称","服务项目","撤销时间");
                            messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate,list);

                            messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                        }
                    }


                    for (WXCancelTreatBillInfoOutputForm wxCancelTreatBill : wxCancelTreatBillList) {
                        if (StringUtils.isNotBlank(wxCancelTreatBill.OffiaccountOpenID)) {
                            WxMpTemplateMessage wxMpTemplateMessage = new WxMpTemplateMessage();
                            wxMpTemplateMessage.setTemplateId(messageConfigEntity.WechatEnterpriseTemplate);
                            wxMpTemplateMessage.setToUser(wxCancelTreatBill.OffiaccountOpenID);
                            List<WxMpTemplateData> wxMpTemplateDataList = Arrays.asList(
                                    new WxMpTemplateData("thing3", wxCancelTreatBill.EntityName),
                                    new WxMpTemplateData("thing1", wxCancelTreatBill.TreatMessage),
                                    new WxMpTemplateData("time2", wxCancelTreatBill.BillDate)
                            );
                            wxMpTemplateMessage.setData(wxMpTemplateDataList);
                            try{
                                templateMsgService.sendTemplateMsg(wxMpTemplateMessage);
                            }catch (Exception e){
                                logger.error("发送模板消息异常：{}", e.getMessage());
                                e.printStackTrace();
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("发送模板消息异常：{}", e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 退消耗
     *
     * @param treatBillID
     */
    public void setRefundTreatBillWxMessage(String treatBillID, String code) {
        try {
            List<WXRefundTreatBillInfoOutputForm> wxRefundTreatBillList = treatBillMapper.getWXRefundTreatBill(treatBillID);
            EnterpriseOffiaccountEntity enterpriseOffiaccountEntity = enterpriseOplatformMapper.getEnterpriseOffiaccount(SystemUserUtil.getSystemEnterpriseCode());
            if (enterpriseOffiaccountEntity != null && wxRefundTreatBillList.size() > 0) {
                MessageConfigEntity messageConfigEntity = messageNotificationMapper.getMessageConfigEntity(code);
                if (messageConfigEntity.IsSendWechat && org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatPublicLibraryTemplate)) {
                    WxMpTemplateMsgService templateMsgService = wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(enterpriseOffiaccountEntity.AuthorizerAppID).getTemplateMsgService();
                    if (!org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatEnterpriseTemplate)) {
                        List<String> list = Arrays.asList("项目名称","数量","退单时间");
                        messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate,list);
                        ;
                        messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                    } else {
                        List<WxMpTemplate> allPrivateTemplate = templateMsgService.getAllPrivateTemplate();
                        if (!allPrivateTemplate.stream().filter(m -> m.getTemplateId().equals(messageConfigEntity.WechatEnterpriseTemplate)).findAny().isPresent()) {
                            List<String> list = Arrays.asList("项目名称","数量","退单时间");
                            messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate,list);

                            messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                        }
                    }

                    for (WXRefundTreatBillInfoOutputForm wxRefundTreatBill : wxRefundTreatBillList) {
                        if (StringUtils.isNotBlank(wxRefundTreatBill.OffiaccountOpenID)) {
                            WxMpTemplateMessage wxMpTemplateMessage = new WxMpTemplateMessage();
                            wxMpTemplateMessage.setTemplateId(messageConfigEntity.WechatEnterpriseTemplate);
                            wxMpTemplateMessage.setToUser(wxRefundTreatBill.OffiaccountOpenID);
                            List<WxMpTemplateData> wxMpTemplateDataList = Arrays.asList(
                                    new WxMpTemplateData("thing2", wxRefundTreatBill.TreatMessage),
                                    new WxMpTemplateData("character_string3", wxRefundTreatBill.Quantity),
                                    new WxMpTemplateData("time5", wxRefundTreatBill.BillDate)
                            );
                            wxMpTemplateMessage.setData(wxMpTemplateDataList);
                            try{
                                templateMsgService.sendTemplateMsg(wxMpTemplateMessage);
                            }catch (Exception e){
                                logger.error("发送模板消息异常：{}", e.getMessage());
                                e.printStackTrace();
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("发送模板消息异常：{}", e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 转账
     *
     * @param treatBillID
     */
    public void setTransBillWxMessage(String treatBillID, String outCode, String inCode) {
        try {

            WXTransBillInfoOutputForm wxTransBill = transerMapper.getWXTranserBillInfo(treatBillID);
            EnterpriseOffiaccountEntity enterpriseOffiaccountEntity = enterpriseOplatformMapper.getEnterpriseOffiaccount(SystemUserUtil.getSystemEnterpriseCode());
            if (enterpriseOffiaccountEntity != null && (StringUtils.isNotBlank(wxTransBill.OutOffiaccountOpenID) || StringUtils.isNotBlank(wxTransBill.InOffiaccountOpenID))) {
                MessageConfigEntity messageConfigEntity = messageNotificationMapper.getMessageConfigEntity(outCode);
                MessageConfigEntity configEntity = messageNotificationMapper.getMessageConfigEntity(inCode);
                if (messageConfigEntity.IsSendWechat && org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatPublicLibraryTemplate)) {
                    WxMpTemplateMsgService templateMsgService = wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(enterpriseOffiaccountEntity.AuthorizerAppID).getTemplateMsgService();
                    if (!org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatEnterpriseTemplate)) {
                        messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate);
                        messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                    } else {
                        List<WxMpTemplate> allPrivateTemplate = templateMsgService.getAllPrivateTemplate();
                        if (!allPrivateTemplate.stream().filter(m -> m.getTemplateId().equals(messageConfigEntity.WechatEnterpriseTemplate)).findAny().isPresent()) {
                            messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate);

                            messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                        }
                    }
                    WxMpTemplateMessage wxOutMpTemplateMessage = new WxMpTemplateMessage();
                    wxOutMpTemplateMessage.setTemplateId(messageConfigEntity.WechatEnterpriseTemplate);
                    wxOutMpTemplateMessage.setToUser(wxTransBill.OutOffiaccountOpenID);

                    List<WxMpTemplateData> wxMpTemplateDataOutList = Arrays.asList(
                            new WxMpTemplateData("first", "亲爱的客户您好，您已转账成功，请注意查收"),
                            new WxMpTemplateData("keyword1", wxTransBill.TransOutCustomer),
                            new WxMpTemplateData("keyword2", wxTransBill.TransInCustomer),
                            new WxMpTemplateData("keyword3", wxTransBill.Messasage),
                            new WxMpTemplateData("keyword4", wxTransBill.CreatedOn),
                            new WxMpTemplateData("remark", "欢迎光临")
                    );
                    wxOutMpTemplateMessage.setData(wxMpTemplateDataOutList);

                    try {
                        templateMsgService.sendTemplateMsg(wxOutMpTemplateMessage);
                    } catch (Exception e) {
                        logger.error("发送模板消息异常：{}", e.getMessage());
                        e.printStackTrace();
                    }

                }

                if (configEntity.IsSendWechat && org.springframework.util.StringUtils.hasText(configEntity.WechatPublicLibraryTemplate)) {
                    WxMpTemplateMsgService templateMsgService = wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(enterpriseOffiaccountEntity.AuthorizerAppID).getTemplateMsgService();
                    if (!org.springframework.util.StringUtils.hasText(configEntity.WechatEnterpriseTemplate)) {
                        configEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(configEntity.WechatPublicLibraryTemplate);
                        messageNotificationMapper.updateMessageNotification(configEntity.Code, configEntity.WechatEnterpriseTemplate, configEntity.IsSendWechat);
                    } else {
                        List<WxMpTemplate> allPrivateTemplate = templateMsgService.getAllPrivateTemplate();
                        if (!allPrivateTemplate.stream().filter(m -> m.getTemplateId().equals(configEntity.WechatEnterpriseTemplate)).findAny().isPresent()) {
                            configEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(configEntity.WechatPublicLibraryTemplate);

                            messageNotificationMapper.updateMessageNotification(configEntity.Code, configEntity.WechatEnterpriseTemplate, configEntity.IsSendWechat);
                        }
                    }
                    WxMpTemplateMessage wxInMpTemplateMessage = new WxMpTemplateMessage();
                    wxInMpTemplateMessage.setTemplateId(configEntity.WechatEnterpriseTemplate);
                    wxInMpTemplateMessage.setToUser(wxTransBill.InOffiaccountOpenID);

                    List<WxMpTemplateData> wxMpTemplateDataInList = Arrays.asList(
                            new WxMpTemplateData("first", "亲爱的客户您好，您已成功转账！"),
                            new WxMpTemplateData("keyword1", wxTransBill.TransOutCustomer),
                            new WxMpTemplateData("keyword2", wxTransBill.TransInCustomer),
                            new WxMpTemplateData("keyword3", wxTransBill.Messasage),
                            new WxMpTemplateData("keyword4", wxTransBill.CreatedOn),
                            new WxMpTemplateData("remark", "欢迎光临")
                    );
                    wxInMpTemplateMessage.setData(wxMpTemplateDataInList);
                    templateMsgService.sendTemplateMsg(wxInMpTemplateMessage);
                }
            }
        } catch (Exception e) {
            logger.error("发送模板消息异常：{}", e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 转账取消
     *
     * @param treatBillID
     */
    public void setCancelTransBillWxMessage(String treatBillID, String outCode, String inCode) {
        try {

            WXTransBillInfoOutputForm wxTransBill = transerMapper.getWXTranserBillInfo(treatBillID);
            EnterpriseOffiaccountEntity enterpriseOffiaccountEntity = enterpriseOplatformMapper.getEnterpriseOffiaccount(SystemUserUtil.getSystemEnterpriseCode());
            if (enterpriseOffiaccountEntity != null && (StringUtils.isNotBlank(wxTransBill.OutOffiaccountOpenID) || StringUtils.isNotBlank(wxTransBill.InOffiaccountOpenID))) {
                MessageConfigEntity messageConfigEntity = messageNotificationMapper.getMessageConfigEntity(outCode);
                MessageConfigEntity configEntity = messageNotificationMapper.getMessageConfigEntity(inCode);
                if (messageConfigEntity.IsSendWechat && org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatPublicLibraryTemplate)) {
                    WxMpTemplateMsgService templateMsgService = wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(enterpriseOffiaccountEntity.AuthorizerAppID).getTemplateMsgService();
                    if (!org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatEnterpriseTemplate)) {
                        messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate);
                        messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                    } else {
                        List<WxMpTemplate> allPrivateTemplate = templateMsgService.getAllPrivateTemplate();
                        if (!allPrivateTemplate.stream().filter(m -> m.getTemplateId().equals(messageConfigEntity.WechatEnterpriseTemplate)).findAny().isPresent()) {
                            messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate);

                            messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                        }
                    }
                    WxMpTemplateMessage wxOutMpTemplateMessage = new WxMpTemplateMessage();
                    wxOutMpTemplateMessage.setTemplateId(messageConfigEntity.WechatEnterpriseTemplate);
                    wxOutMpTemplateMessage.setToUser(wxTransBill.OutOffiaccountOpenID);

                    List<WxMpTemplateData> wxMpTemplateDataOutList = Arrays.asList(
                            new WxMpTemplateData("first", "亲爱的客户您好，您的转账已取消，请注意查收"),
                            new WxMpTemplateData("keyword1", wxTransBill.TransOutCustomer),
                            new WxMpTemplateData("keyword2", wxTransBill.TransInCustomer),
                            new WxMpTemplateData("keyword3", wxTransBill.Messasage),
                            new WxMpTemplateData("keyword4", wxTransBill.CreatedOn),
                            new WxMpTemplateData("remark", "欢迎光临")
                    );
                    wxOutMpTemplateMessage.setData(wxMpTemplateDataOutList);

                    try {
                        templateMsgService.sendTemplateMsg(wxOutMpTemplateMessage);
                    } catch (Exception e) {
                        logger.error("发送模板消息异常：{}", e.getMessage());
                        e.printStackTrace();
                    }
                }

                if (configEntity.IsSendWechat && org.springframework.util.StringUtils.hasText(configEntity.WechatPublicLibraryTemplate)) {
                    WxMpTemplateMsgService templateMsgService = wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(enterpriseOffiaccountEntity.AuthorizerAppID).getTemplateMsgService();
                    if (!org.springframework.util.StringUtils.hasText(configEntity.WechatEnterpriseTemplate)) {
                        configEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(configEntity.WechatPublicLibraryTemplate);
                        messageNotificationMapper.updateMessageNotification(configEntity.Code, configEntity.WechatEnterpriseTemplate, configEntity.IsSendWechat);
                    } else {
                        List<WxMpTemplate> allPrivateTemplate = templateMsgService.getAllPrivateTemplate();
                        if (!allPrivateTemplate.stream().filter(m -> m.getTemplateId().equals(configEntity.WechatEnterpriseTemplate)).findAny().isPresent()) {
                            configEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(configEntity.WechatPublicLibraryTemplate);

                            messageNotificationMapper.updateMessageNotification(configEntity.Code, configEntity.WechatEnterpriseTemplate, configEntity.IsSendWechat);
                        }
                    }
                    WxMpTemplateMessage wxInMpTemplateMessage = new WxMpTemplateMessage();
                    wxInMpTemplateMessage.setTemplateId(configEntity.WechatEnterpriseTemplate);
                    wxInMpTemplateMessage.setToUser(wxTransBill.InOffiaccountOpenID);

                    List<WxMpTemplateData> wxMpTemplateDataInList = Arrays.asList(
                            new WxMpTemplateData("first", "转账取消！"),
                            new WxMpTemplateData("keyword1", wxTransBill.TransOutCustomer),
                            new WxMpTemplateData("keyword2", wxTransBill.TransInCustomer),
                            new WxMpTemplateData("keyword3", wxTransBill.Messasage),
                            new WxMpTemplateData("keyword4", wxTransBill.CreatedOn),
                            new WxMpTemplateData("remark", "欢迎光临")
                    );

                    wxInMpTemplateMessage.setData(wxMpTemplateDataInList);
                    templateMsgService.sendTemplateMsg(wxInMpTemplateMessage);
                }
            }
        } catch (Exception e) {
            logger.error("发送模板消息异常：{}", e.getMessage());
            e.printStackTrace();
        }
    }


    /**
     * 预约成功
     *
     * @param appointmentBillID
     */
    public void setAppointmentBillWxMessage(String appointmentBillID) {
        try {
            List<WXAppointmentBillInfoOutputForm> wxAppointmentBillInfoOutputFormList = appointmentBillMapper.getWXAppointmentBill(appointmentBillID);
            EnterpriseOffiaccountEntity enterpriseOffiaccountEntity = enterpriseOplatformMapper.getEnterpriseOffiaccount(SystemUserUtil.getSystemEnterpriseCode());
            if (enterpriseOffiaccountEntity != null && wxAppointmentBillInfoOutputFormList.size() > 0) {
                MessageConfigEntity messageConfigEntity = messageNotificationMapper.getMessageConfigEntity(WeiXinOfficialAccountMessage.AppointmentNotice);
                if (messageConfigEntity.IsSendWechat && org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatPublicLibraryTemplate)) {
                    WxMpTemplateMsgService templateMsgService = wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(enterpriseOffiaccountEntity.AuthorizerAppID).getTemplateMsgService();
                    if (!org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatEnterpriseTemplate)) {
                        List<String> list = Arrays.asList("预约门店","预约时间","服务项目","订单号","门店地址");
                        messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate,list);
                        messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                    } else {
                        List<WxMpTemplate> allPrivateTemplate = templateMsgService.getAllPrivateTemplate();
                        if (!allPrivateTemplate.stream().filter(m -> m.getTemplateId().equals(messageConfigEntity.WechatEnterpriseTemplate)).findAny().isPresent()) {
                            List<String> list = Arrays.asList("预约门店","预约时间","服务项目","订单号","门店地址");
                            messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate,list);

                            messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                        }
                    }

                    for (WXAppointmentBillInfoOutputForm wxAppointmentBillInfoOutputForm : wxAppointmentBillInfoOutputFormList) {
                        if (StringUtils.isNotBlank(wxAppointmentBillInfoOutputForm.OffiaccountOpenID)) {
                            WxMpTemplateMessage wxMpTemplateMessage = new WxMpTemplateMessage();
                            wxMpTemplateMessage.setTemplateId(messageConfigEntity.WechatEnterpriseTemplate);
                            wxMpTemplateMessage.setToUser(wxAppointmentBillInfoOutputForm.OffiaccountOpenID);
                            List<WxMpTemplateData> wxMpTemplateDataList = Arrays.asList(
                                    new WxMpTemplateData("thing2", wxAppointmentBillInfoOutputForm.EntityName),
                                    new WxMpTemplateData("time9", wxAppointmentBillInfoOutputForm.AppointmentDate),
                                    new WxMpTemplateData("thing3", wxAppointmentBillInfoOutputForm.Content),
                                    new WxMpTemplateData("character_string16", wxAppointmentBillInfoOutputForm.ID),
                                    new WxMpTemplateData("thing18", wxAppointmentBillInfoOutputForm.Address)
                            );
                            wxMpTemplateMessage.setData(wxMpTemplateDataList);
                            try{
                                templateMsgService.sendTemplateMsg(wxMpTemplateMessage);
                            }catch (Exception e){
                                logger.error("发送模板消息异常：{}", e.getMessage());
                                e.printStackTrace();
                            }
                        }
                    }
                }

            }
        } catch (Exception e) {
            logger.error("发送模板消息异常：{}", e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 预约修改
     *
     * @param appointmentBillID
     */
    public void setUpdateAppointmentBillWxMessage(String appointmentBillID, Date originalAppointmentDate) {
        try {

            List<WXAppointmentBillInfoOutputForm> wxAppointmentBillInfoOutputFormList = appointmentBillMapper.getWXAppointmentBill(appointmentBillID);
            EnterpriseOffiaccountEntity enterpriseOffiaccountEntity = enterpriseOplatformMapper.getEnterpriseOffiaccount(SystemUserUtil.getSystemEnterpriseCode());
            if (enterpriseOffiaccountEntity != null && wxAppointmentBillInfoOutputFormList.size() > 0) {
                MessageConfigEntity messageConfigEntity = messageNotificationMapper.getMessageConfigEntity(WeiXinOfficialAccountMessage.AppointmentModifyNotice);
                if (messageConfigEntity.IsSendWechat && org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatPublicLibraryTemplate)) {
                    WxMpTemplateMsgService templateMsgService = wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(enterpriseOffiaccountEntity.AuthorizerAppID).getTemplateMsgService();
                    if (!org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatEnterpriseTemplate)) {
                        List<String> list = Arrays.asList("预约门店","服务项目","原定时间","修改后时间","门店地址");
                        messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate,list);
                        ;
                        messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                    } else {
                        List<WxMpTemplate> allPrivateTemplate = templateMsgService.getAllPrivateTemplate();
                        if (!allPrivateTemplate.stream().filter(m -> m.getTemplateId().equals(messageConfigEntity.WechatEnterpriseTemplate)).findAny().isPresent()) {
                            List<String> list = Arrays.asList("预约门店","服务项目","原定时间","修改后时间","门店地址");
                            messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate,list);

                            messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                        }
                    }

                    for (WXAppointmentBillInfoOutputForm wxAppointmentBillInfoOutputForm : wxAppointmentBillInfoOutputFormList) {
                        if (StringUtils.isNotBlank(wxAppointmentBillInfoOutputForm.OffiaccountOpenID)) {
                            WxMpTemplateMessage wxMpTemplateMessage = new WxMpTemplateMessage();
                            wxMpTemplateMessage.setTemplateId(messageConfigEntity.WechatEnterpriseTemplate);
                            wxMpTemplateMessage.setToUser(wxAppointmentBillInfoOutputForm.OffiaccountOpenID);
                            List<WxMpTemplateData> wxMpTemplateDataList = Arrays.asList(
                                    new WxMpTemplateData("thing5", wxAppointmentBillInfoOutputForm.EntityName),
                                    new WxMpTemplateData("thing1", wxAppointmentBillInfoOutputForm.Content),
                                    new WxMpTemplateData("time2", DateTimeUtil.toDate(originalAppointmentDate)),
                                    new WxMpTemplateData("time3", wxAppointmentBillInfoOutputForm.AppointmentDate),
                                    new WxMpTemplateData("thing6", wxAppointmentBillInfoOutputForm.Address)
                            );
                            wxMpTemplateMessage.setData(wxMpTemplateDataList);
                            try{
                                templateMsgService.sendTemplateMsg(wxMpTemplateMessage);
                            }catch (Exception e){
                                logger.error("发送模板消息异常：{}", e.getMessage());
                                e.printStackTrace();
                            }
                        }
                    }
                }

            }
        } catch (Exception e) {
            logger.error("发送模板消息异常：{}", e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 预约取消
     *
     * @param appointmentBillID
     */
    public void setCancelAppointmentBillWxMessage(String appointmentBillID) {
        try {

            List<WXAppointmentBillInfoOutputForm> wxAppointmentBillInfoOutputFormList = appointmentBillMapper.getWXAppointmentBill(appointmentBillID);
            EnterpriseOffiaccountEntity enterpriseOffiaccountEntity = enterpriseOplatformMapper.getEnterpriseOffiaccount(SystemUserUtil.getSystemEnterpriseCode());
            if (enterpriseOffiaccountEntity != null && wxAppointmentBillInfoOutputFormList.size() > 0) {
                MessageConfigEntity messageConfigEntity = messageNotificationMapper.getMessageConfigEntity(WeiXinOfficialAccountMessage.AppointmentCancelNotice);
                if (messageConfigEntity.IsSendWechat && org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatPublicLibraryTemplate)) {
                    WxMpTemplateMsgService templateMsgService = wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(enterpriseOffiaccountEntity.AuthorizerAppID).getTemplateMsgService();
                    if (!org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatEnterpriseTemplate)) {
                        List<String> list = Arrays.asList("预约门店","预约时间","预约项目","订单号");
                        messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate,list);
                        ;
                        messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                    } else {
                        List<WxMpTemplate> allPrivateTemplate = templateMsgService.getAllPrivateTemplate();
                        if (!allPrivateTemplate.stream().filter(m -> m.getTemplateId().equals(messageConfigEntity.WechatEnterpriseTemplate)).findAny().isPresent()) {
                            List<String> list = Arrays.asList("预约门店","预约时间","预约项目","订单号");
                            messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate,list);

                            messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                        }
                    }

                    for (WXAppointmentBillInfoOutputForm wxAppointmentBillInfoOutputForm : wxAppointmentBillInfoOutputFormList) {
                        if (StringUtils.isNotBlank(wxAppointmentBillInfoOutputForm.OffiaccountOpenID)) {
                            WxMpTemplateMessage wxMpTemplateMessage = new WxMpTemplateMessage();
                            wxMpTemplateMessage.setTemplateId(messageConfigEntity.WechatEnterpriseTemplate);
                            wxMpTemplateMessage.setToUser(wxAppointmentBillInfoOutputForm.OffiaccountOpenID);
                            List<WxMpTemplateData> wxMpTemplateDataList = Arrays.asList(
                                    new WxMpTemplateData("thing5", wxAppointmentBillInfoOutputForm.EntityName),
                                    new WxMpTemplateData("time3", wxAppointmentBillInfoOutputForm.AppointmentDate),
                                    new WxMpTemplateData("thing6", wxAppointmentBillInfoOutputForm.Content),
                                    new WxMpTemplateData("character_string14", wxAppointmentBillInfoOutputForm.ID)
                            );
                            wxMpTemplateMessage.setData(wxMpTemplateDataList);
                            try{
                                templateMsgService.sendTemplateMsg(wxMpTemplateMessage);
                            }catch (Exception e){
                                logger.error("发送模板消息异常：{}", e.getMessage());
                                e.printStackTrace();
                            }
                        }
                    }
                }

            }
        } catch (Exception e) {
            logger.error("发送模板消息异常：{}", e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 预约提醒
     */
    public void setAppointmentBillRemaid(String enterpriseCode) {
        try {
            List<WXAppointmentBillRemindInfoOutputForm> list = appointmentBillMapper.getWXAppointmentBillRemind();
            EnterpriseOffiaccountEntity enterpriseOffiaccountEntity = enterpriseOplatformMapper.getEnterpriseOffiaccount(enterpriseCode);
            if (enterpriseOffiaccountEntity != null && list.size() > 0) {
                MessageConfigEntity messageConfigEntity = messageNotificationMapper.getMessageConfigEntity(WeiXinOfficialAccountMessage.AppointmentCancelRemind);
                if (messageConfigEntity.IsSendWechat && org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatPublicLibraryTemplate)) {
                    WxMpTemplateMsgService templateMsgService = wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(enterpriseOffiaccountEntity.AuthorizerAppID).getTemplateMsgService();
                    if (!org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatEnterpriseTemplate)) {
                        List<String> list1 = Arrays.asList("距离开始时间","预约时间","预约门店","预约项目","门店地址");
                        messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate,list1);
                        messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                    } else {
                        List<WxMpTemplate> allPrivateTemplate = templateMsgService.getAllPrivateTemplate();
                        if (!allPrivateTemplate.stream().filter(m -> m.getTemplateId().equals(messageConfigEntity.WechatEnterpriseTemplate)).findAny().isPresent()) {
                            List<String> list1 = Arrays.asList("距离开始时间","预约时间","预约门店","预约项目","门店地址");
                            messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate,list1);

                            messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                        }
                    }

                    for (WXAppointmentBillRemindInfoOutputForm wxAppointmentBillInfoOutputForm : list) {
                        if (StringUtils.isNotBlank(wxAppointmentBillInfoOutputForm.OffiaccountOpenID)) {
                            WxMpTemplateMessage wxMpTemplateMessage = new WxMpTemplateMessage();
                            wxMpTemplateMessage.setTemplateId(messageConfigEntity.WechatEnterpriseTemplate);
                            wxMpTemplateMessage.setToUser(wxAppointmentBillInfoOutputForm.OffiaccountOpenID);
                            List<WxMpTemplateData> wxMpTemplateDataList = Arrays.asList(
                                    new WxMpTemplateData("const11", "2小时"),
                                    new WxMpTemplateData("time7", wxAppointmentBillInfoOutputForm.AppointmentDate),
                                    new WxMpTemplateData("thing5", wxAppointmentBillInfoOutputForm.EntityName),
                                    new WxMpTemplateData("thing9", wxAppointmentBillInfoOutputForm.Content),
                                    new WxMpTemplateData("thing6", wxAppointmentBillInfoOutputForm.Address)
                            );
                            wxMpTemplateMessage.setData(wxMpTemplateDataList);
                            try{
                                templateMsgService.sendTemplateMsg(wxMpTemplateMessage);
                            }catch (Exception e){
                                logger.error("发送模板消息异常：{}", e.getMessage());
                                e.printStackTrace();
                            }
                        }
                    }
                    List<String> collect = list.stream().map(i -> i.ID).collect(Collectors.toList());
                    appointmentBillMapper.updateAppointmentBillIsSendRemindMessage(collect);
                }
            }
        } catch (Exception e) {
            logger.error("发送模板消息异常：{}", e.getMessage());
            e.printStackTrace();
        }
    }


    /**
     * 延期
     */
    public void setPostponeBill(String billID) {
        try {

            WXPostponeBillIInfoOutputForm wxPostponeBill = postponeBillMapper.getWXPostponeBill(billID);
            EnterpriseOffiaccountEntity enterpriseOffiaccountEntity = enterpriseOplatformMapper.getEnterpriseOffiaccount(SystemUserUtil.getSystemEnterpriseCode());
            if (enterpriseOffiaccountEntity != null && StringUtils.isNotBlank(wxPostponeBill.OffiaccountOpenID)) {
                MessageConfigEntity messageConfigEntity = messageNotificationMapper.getMessageConfigEntity(WeiXinOfficialAccountMessage.PostponeNotice);
                if (messageConfigEntity.IsSendWechat && org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatPublicLibraryTemplate)) {
                    WxMpTemplateMsgService templateMsgService = wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(enterpriseOffiaccountEntity.AuthorizerAppID).getTemplateMsgService();
                    if (!org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatEnterpriseTemplate)) {
                        messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate);
                        messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                    } else {
                        List<WxMpTemplate> allPrivateTemplate = templateMsgService.getAllPrivateTemplate();
                        if (!allPrivateTemplate.stream().filter(m -> m.getTemplateId().equals(messageConfigEntity.WechatEnterpriseTemplate)).findAny().isPresent()) {
                            messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate);

                            messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                        }
                    }

                    WxMpTemplateMessage wxMpTemplateMessage = new WxMpTemplateMessage();
                    wxMpTemplateMessage.setTemplateId(messageConfigEntity.WechatEnterpriseTemplate);
                    wxMpTemplateMessage.setToUser(wxPostponeBill.OffiaccountOpenID);
                    List<WxMpTemplateData> wxMpTemplateDataList = Arrays.asList(
                            new WxMpTemplateData("first", "亲爱的客户您好，您的卡项已延期成功，请及时到店使用"),
                            new WxMpTemplateData("keyword1", wxPostponeBill.ID),
                            new WxMpTemplateData("keyword2", wxPostponeBill.Content),
                            new WxMpTemplateData("keyword3", wxPostponeBill.CreatedOn),
                            new WxMpTemplateData("keyword4", wxPostponeBill.EntityName),
                            new WxMpTemplateData("remark", "欢迎光临")
                    );
                    wxMpTemplateMessage.setData(wxMpTemplateDataList);
                    templateMsgService.sendTemplateMsg(wxMpTemplateMessage);
                }

            }
        } catch (Exception e) {
            logger.error("发送模板消息异常：{}", e.getMessage());
            e.printStackTrace();
        }
    }

    public void setMicroMallAppointmentBillWxMessage(String appointmentBillID) {
        try {
            List<WXAppointmentBillInfoOutputForm> wxAppointmentBillInfoOutputFormList = appointmentBillMapper.getWXAppointmentBill(appointmentBillID);
            EnterpriseOffiaccountEntity enterpriseOffiaccountEntity = enterpriseOplatformMapper.getEnterpriseOffiaccount(SystemCustomerUtil.getSystemEnterpriseCode());
            if (enterpriseOffiaccountEntity != null && wxAppointmentBillInfoOutputFormList.size() > 0) {
                MessageConfigEntity messageConfigEntity = messageNotificationMapper.getMessageConfigEntity(WeiXinOfficialAccountMessage.AppointmentNotice);
                if (messageConfigEntity.IsSendWechat && org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatPublicLibraryTemplate)) {
                    WxMpTemplateMsgService templateMsgService = wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(enterpriseOffiaccountEntity.AuthorizerAppID).getTemplateMsgService();
                    if (!org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatEnterpriseTemplate)) {
                        List<String> list = Arrays.asList("预约门店","预约时间","服务项目","订单号","门店地址");
                        messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate,list);
                        messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                    } else {
                        List<WxMpTemplate> allPrivateTemplate = templateMsgService.getAllPrivateTemplate();
                        if (!allPrivateTemplate.stream().filter(m -> m.getTemplateId().equals(messageConfigEntity.WechatEnterpriseTemplate)).findAny().isPresent()) {
                            List<String> list = Arrays.asList("预约门店","预约时间","服务项目","订单号","门店地址");
                            messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate,list);

                            messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                        }
                    }

                    for (WXAppointmentBillInfoOutputForm wxAppointmentBillInfoOutputForm : wxAppointmentBillInfoOutputFormList) {
                        if (StringUtils.isNotBlank(wxAppointmentBillInfoOutputForm.OffiaccountOpenID)) {
                            WxMpTemplateMessage wxMpTemplateMessage = new WxMpTemplateMessage();
                            wxMpTemplateMessage.setTemplateId(messageConfigEntity.WechatEnterpriseTemplate);
                            wxMpTemplateMessage.setToUser(wxAppointmentBillInfoOutputForm.OffiaccountOpenID);
                            List<WxMpTemplateData> wxMpTemplateDataList = Arrays.asList(
                                    new WxMpTemplateData("thing2", wxAppointmentBillInfoOutputForm.EntityName),
                                    new WxMpTemplateData("time9", wxAppointmentBillInfoOutputForm.AppointmentDate),
                                    new WxMpTemplateData("thing3", wxAppointmentBillInfoOutputForm.Content),
                                    new WxMpTemplateData("character_string16", wxAppointmentBillInfoOutputForm.ID),
                                    new WxMpTemplateData("thing18", wxAppointmentBillInfoOutputForm.Address)
                            );
                            wxMpTemplateMessage.setData(wxMpTemplateDataList);
                            try{
                                templateMsgService.sendTemplateMsg(wxMpTemplateMessage);
                            }catch (Exception e){
                                logger.error("发送模板消息异常：{}", e.getMessage());
                                e.printStackTrace();
                            }
                        }
                    }
                }

            }
        } catch (Exception e) {
            logger.error("发送模板消息异常：{}", e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 预约修改
     *
     * @param appointmentBillID
     */
    public void setMicroMallUpdateAppointmentBillWxMessage(String appointmentBillID,Date originalAppointmentDate) {
        try {

            List<WXAppointmentBillInfoOutputForm> wxAppointmentBillInfoOutputFormList = appointmentBillMapper.getWXAppointmentBill(appointmentBillID);
            EnterpriseOffiaccountEntity enterpriseOffiaccountEntity = enterpriseOplatformMapper.getEnterpriseOffiaccount(SystemCustomerUtil.getSystemEnterpriseCode());
            if (enterpriseOffiaccountEntity != null && wxAppointmentBillInfoOutputFormList.size() > 0) {
                MessageConfigEntity messageConfigEntity = messageNotificationMapper.getMessageConfigEntity(WeiXinOfficialAccountMessage.AppointmentModifyNotice);
                if (messageConfigEntity.IsSendWechat && org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatPublicLibraryTemplate)) {
                    WxMpTemplateMsgService templateMsgService = wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(enterpriseOffiaccountEntity.AuthorizerAppID).getTemplateMsgService();
                    if (!org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatEnterpriseTemplate)) {
                        List<String> list = Arrays.asList("预约门店","服务项目","原定时间","修改后时间","门店地址");
                        messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate,list);
                        ;
                        messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                    } else {
                        List<WxMpTemplate> allPrivateTemplate = templateMsgService.getAllPrivateTemplate();
                        if (!allPrivateTemplate.stream().filter(m -> m.getTemplateId().equals(messageConfigEntity.WechatEnterpriseTemplate)).findAny().isPresent()) {
                            List<String> list = Arrays.asList("预约门店","服务项目","原定时间","修改后时间","门店地址");
                            messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate,list);

                            messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                        }
                    }

                    for (WXAppointmentBillInfoOutputForm wxAppointmentBillInfoOutputForm : wxAppointmentBillInfoOutputFormList) {
                        if (StringUtils.isNotBlank(wxAppointmentBillInfoOutputForm.OffiaccountOpenID)) {
                            WxMpTemplateMessage wxMpTemplateMessage = new WxMpTemplateMessage();
                            wxMpTemplateMessage.setTemplateId(messageConfigEntity.WechatEnterpriseTemplate);
                            wxMpTemplateMessage.setToUser(wxAppointmentBillInfoOutputForm.OffiaccountOpenID);
                            List<WxMpTemplateData> wxMpTemplateDataList = Arrays.asList(
                                    new WxMpTemplateData("thing5", wxAppointmentBillInfoOutputForm.EntityName),
                                    new WxMpTemplateData("thing1", wxAppointmentBillInfoOutputForm.Content),
                                    new WxMpTemplateData("time2", DateTimeUtil.toDate(originalAppointmentDate)),
                                    new WxMpTemplateData("time3", wxAppointmentBillInfoOutputForm.AppointmentDate),
                                    new WxMpTemplateData("thing6", wxAppointmentBillInfoOutputForm.Address)
                            );
                            wxMpTemplateMessage.setData(wxMpTemplateDataList);
                            try{
                                templateMsgService.sendTemplateMsg(wxMpTemplateMessage);
                            }catch (Exception e){
                                logger.error("发送模板消息异常：{}", e.getMessage());
                                e.printStackTrace();
                            }
                        }
                    }
                }

            }
        } catch (Exception e) {
            logger.error("发送模板消息异常：{}", e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 预约取消
     *
     * @param appointmentBillID
     */
    public void setMicroMallCancelAppointmentBillWxMessage(String appointmentBillID) {
        try {

            List<WXAppointmentBillInfoOutputForm> wxAppointmentBillInfoOutputFormList = appointmentBillMapper.getWXAppointmentBill(appointmentBillID);
            EnterpriseOffiaccountEntity enterpriseOffiaccountEntity = enterpriseOplatformMapper.getEnterpriseOffiaccount(SystemCustomerUtil.getSystemEnterpriseCode());
            if (enterpriseOffiaccountEntity != null && wxAppointmentBillInfoOutputFormList.size()>0) {
                MessageConfigEntity messageConfigEntity = messageNotificationMapper.getMessageConfigEntity(WeiXinOfficialAccountMessage.AppointmentCancelNotice);
                if (messageConfigEntity.IsSendWechat && org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatPublicLibraryTemplate)) {
                    WxMpTemplateMsgService templateMsgService = wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(enterpriseOffiaccountEntity.AuthorizerAppID).getTemplateMsgService();
                    if (!org.springframework.util.StringUtils.hasText(messageConfigEntity.WechatEnterpriseTemplate)) {
                        List<String> list = Arrays.asList("预约门店","预约时间","预约项目","订单号");
                        messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate,list);
                        ;
                        messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                    } else {
                        List<WxMpTemplate> allPrivateTemplate = templateMsgService.getAllPrivateTemplate();
                        if (!allPrivateTemplate.stream().filter(m -> m.getTemplateId().equals(messageConfigEntity.WechatEnterpriseTemplate)).findAny().isPresent()) {
                            List<String> list = Arrays.asList("预约门店","预约时间","预约项目","订单号");
                            messageConfigEntity.WechatEnterpriseTemplate = templateMsgService.addTemplate(messageConfigEntity.WechatPublicLibraryTemplate,list);

                            messageNotificationMapper.updateMessageNotification(messageConfigEntity.Code, messageConfigEntity.WechatEnterpriseTemplate, messageConfigEntity.IsSendWechat);
                        }
                    }

                    for (WXAppointmentBillInfoOutputForm wxAppointmentBillInfoOutputForm : wxAppointmentBillInfoOutputFormList) {
                        if (StringUtils.isNotBlank(wxAppointmentBillInfoOutputForm.OffiaccountOpenID)) {
                            WxMpTemplateMessage wxMpTemplateMessage = new WxMpTemplateMessage();
                            wxMpTemplateMessage.setTemplateId(messageConfigEntity.WechatEnterpriseTemplate);
                            wxMpTemplateMessage.setToUser(wxAppointmentBillInfoOutputForm.OffiaccountOpenID);
                            List<WxMpTemplateData> wxMpTemplateDataList = Arrays.asList(
                                    new WxMpTemplateData("thing5", wxAppointmentBillInfoOutputForm.EntityName),
                                    new WxMpTemplateData("time3", wxAppointmentBillInfoOutputForm.AppointmentDate),
                                    new WxMpTemplateData("thing6", wxAppointmentBillInfoOutputForm.Content),
                                    new WxMpTemplateData("character_string14", wxAppointmentBillInfoOutputForm.ID)
                            );
                            wxMpTemplateMessage.setData(wxMpTemplateDataList);
                            try{
                                templateMsgService.sendTemplateMsg(wxMpTemplateMessage);
                            }catch (Exception e){
                                logger.error("发送模板消息异常：{}", e.getMessage());
                                e.printStackTrace();
                            }
                        }
                    }
                }

            }
        } catch (Exception e) {
            logger.error("发送模板消息异常：{}", e.getMessage());
            e.printStackTrace();
        }
    }
}
