package com.zhelian.service.ibeauty.saleTimeCardCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.SalePackageCardTimeCardCategoryHandlerCommissionMapper;
import com.zhelian.mapper.ibeauty.SaleTimeCardCategoryHandlerCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SalePackageCardTimeCardCategoryHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SalePackageCardTimeCardCategoryHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleTimeCardCategoryHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleTimeCardCategoryHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.SaleTimeCardCategoryHandlerCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleTimeCardCategoryHandlerCommissionService {

    @Autowired
    SaleTimeCardCategoryHandlerCommissionMapper saleTimeCardCategoryHandlerCommissionMapper;
    @Autowired
    SalePackageCardTimeCardCategoryHandlerCommissionMapper salePackageCardTimeCardCategoryHandlerCommissionMapper;


    public BaseOutput allSaleTimeCardCategoryHandlerCommission(SaleTimeCardCategoryHandlerCommissionQueryForm form) {
        List<SaleTimeCardCategoryHandlerCommissionOutputForm> list = saleTimeCardCategoryHandlerCommissionMapper.allSaleTimeCardCategoryHandlerCommission(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSalePackageCardTimeCardCategoryHandlerCommission(SaleTimeCardCategoryHandlerCommissionQueryForm form) {
        List<SaleTimeCardCategoryHandlerCommissionOutputForm> list = salePackageCardTimeCardCategoryHandlerCommissionMapper.allSalePackageCardTimeCardCategoryHandlerCommission(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleTimeCardCategoryHandlerCommission(SaleTimeCardCategoryHandlerCommissionAddForm form) {


        saleTimeCardCategoryHandlerCommissionMapper.deleteSaleTimeCardCategoryHandler(form.EntityID, form.CategoryID);
        saleTimeCardCategoryHandlerCommissionMapper.deleteSaleTimeCardCategoryHandlerJobType(form.EntityID, form.CategoryID);
        List list = new ArrayList();
        List handlerJobTypeCommissionList = new ArrayList<>();
        if (form.TimeCardHandler != null && form.TimeCardHandler.size() > 0) {
            for (SaleTimeCardCategoryHandlerAddForm saleTimeCardCategoryHandlerAddForm : form.TimeCardHandler) {
                if (saleTimeCardCategoryHandlerAddForm.PayRate != null || saleTimeCardCategoryHandlerAddForm.PayFixed != null ||
                        saleTimeCardCategoryHandlerAddForm.SavingCardRate != null || saleTimeCardCategoryHandlerAddForm.SavingCardFixed != null ||
                        saleTimeCardCategoryHandlerAddForm.SavingCardLargessRate != null || saleTimeCardCategoryHandlerAddForm.SavingCardLargessFixed != null ||
                        saleTimeCardCategoryHandlerAddForm.SpecialBenefit != null) {
                    SaleTimeCardCategoryHandlerCommissionEntity saleTimeCardCategoryHandlerCommissionEntity = new SaleTimeCardCategoryHandlerCommissionEntity();
                    saleTimeCardCategoryHandlerCommissionEntity.EntityID = form.EntityID;
                    saleTimeCardCategoryHandlerCommissionEntity.TimeCardCategoryID = form.CategoryID;
                    saleTimeCardCategoryHandlerCommissionEntity.TimeCardSaleHandlerID = saleTimeCardCategoryHandlerAddForm.SaleHandlerID;
                    saleTimeCardCategoryHandlerCommissionEntity.PayFixed = saleTimeCardCategoryHandlerAddForm.PayFixed;
                    saleTimeCardCategoryHandlerCommissionEntity.PayRate = saleTimeCardCategoryHandlerAddForm.PayRate;
                    saleTimeCardCategoryHandlerCommissionEntity.SavingCardFixed = saleTimeCardCategoryHandlerAddForm.SavingCardFixed;
                    saleTimeCardCategoryHandlerCommissionEntity.SavingCardRate = saleTimeCardCategoryHandlerAddForm.SavingCardRate;
                    saleTimeCardCategoryHandlerCommissionEntity.SavingCardLargessFixed = saleTimeCardCategoryHandlerAddForm.SavingCardLargessFixed;
                    saleTimeCardCategoryHandlerCommissionEntity.SavingCardLargessRate = saleTimeCardCategoryHandlerAddForm.SavingCardLargessRate;
                    saleTimeCardCategoryHandlerCommissionEntity.SpecialBenefit = saleTimeCardCategoryHandlerAddForm.SpecialBenefit;
                    list.add(saleTimeCardCategoryHandlerCommissionEntity);
                }
                if (saleTimeCardCategoryHandlerAddForm.HandlerJobTypeCommission != null && saleTimeCardCategoryHandlerAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (SaleTimeCardCategoryHandlerJobTypeCommissionAddForm saleTimeCardCategoryHandlerJobTypeCommissionAddForm : saleTimeCardCategoryHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleTimeCardCategoryHandlerJobTypeCommissionAddForm.PayRate != null || saleTimeCardCategoryHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardRate != null || saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed != null ||
                                saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null || saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed != null ||
                                saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SaleTimeCardCategoryHandlerJobTypeCommissionEntity saleTimeCardCategoryHandlerJobTypeCommissionEntity = new SaleTimeCardCategoryHandlerJobTypeCommissionEntity();
                            saleTimeCardCategoryHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            saleTimeCardCategoryHandlerJobTypeCommissionEntity.TimeCardCategoryID = form.CategoryID;
                            saleTimeCardCategoryHandlerJobTypeCommissionEntity.TimeCardSaleHandlerID = saleTimeCardCategoryHandlerAddForm.SaleHandlerID;
                            saleTimeCardCategoryHandlerJobTypeCommissionEntity.JobTypeID = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.JobTypeID;
                            saleTimeCardCategoryHandlerJobTypeCommissionEntity.PayFixed = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.PayFixed;
                            saleTimeCardCategoryHandlerJobTypeCommissionEntity.PayRate = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.PayRate;
                            saleTimeCardCategoryHandlerJobTypeCommissionEntity.SavingCardFixed = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed;
                            saleTimeCardCategoryHandlerJobTypeCommissionEntity.SavingCardRate = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardRate;
                            saleTimeCardCategoryHandlerJobTypeCommissionEntity.SavingCardLargessFixed = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed;
                            saleTimeCardCategoryHandlerJobTypeCommissionEntity.SavingCardLargessRate = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            saleTimeCardCategoryHandlerJobTypeCommissionEntity.SpecialBenefit = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            handlerJobTypeCommissionList.add(saleTimeCardCategoryHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
        }

        salePackageCardTimeCardCategoryHandlerCommissionMapper.deleteSalePackageCardTimeCardCategoryHandler(form.EntityID, form.CategoryID);
        salePackageCardTimeCardCategoryHandlerCommissionMapper.deleteSalePackageCardTimeCardCategoryHandlerJobType(form.EntityID, form.CategoryID);
        List packageList = new ArrayList();
        List packageHandlerJobTypeCommissionList = new ArrayList<>();
        if (form.PackageCardTimeCardHandler != null && form.PackageCardTimeCardHandler.size() > 0) {
            for (SaleTimeCardCategoryHandlerAddForm saleTimeCardCategoryHandlerAddForm : form.PackageCardTimeCardHandler) {
                if (saleTimeCardCategoryHandlerAddForm.PayRate != null || saleTimeCardCategoryHandlerAddForm.PayFixed != null ||
                        saleTimeCardCategoryHandlerAddForm.SavingCardRate != null || saleTimeCardCategoryHandlerAddForm.SavingCardFixed != null ||
                        saleTimeCardCategoryHandlerAddForm.SavingCardLargessRate != null || saleTimeCardCategoryHandlerAddForm.SavingCardLargessFixed != null ||
                        saleTimeCardCategoryHandlerAddForm.SpecialBenefit != null) {
                    SalePackageCardTimeCardCategoryHandlerCommissionEntity salePackageCardTimeCardCategoryHandlerCommissionEntity = new SalePackageCardTimeCardCategoryHandlerCommissionEntity();
                    salePackageCardTimeCardCategoryHandlerCommissionEntity.EntityID = form.EntityID;
                    salePackageCardTimeCardCategoryHandlerCommissionEntity.TimeCardCategoryID = form.CategoryID;
                    salePackageCardTimeCardCategoryHandlerCommissionEntity.PackageCardSaleHandlerID = saleTimeCardCategoryHandlerAddForm.SaleHandlerID;
                    salePackageCardTimeCardCategoryHandlerCommissionEntity.PayFixed = saleTimeCardCategoryHandlerAddForm.PayFixed;
                    salePackageCardTimeCardCategoryHandlerCommissionEntity.PayRate = saleTimeCardCategoryHandlerAddForm.PayRate;
                    salePackageCardTimeCardCategoryHandlerCommissionEntity.SavingCardFixed = saleTimeCardCategoryHandlerAddForm.SavingCardFixed;
                    salePackageCardTimeCardCategoryHandlerCommissionEntity.SavingCardRate = saleTimeCardCategoryHandlerAddForm.SavingCardRate;
                    salePackageCardTimeCardCategoryHandlerCommissionEntity.SavingCardLargessFixed = saleTimeCardCategoryHandlerAddForm.SavingCardLargessFixed;
                    salePackageCardTimeCardCategoryHandlerCommissionEntity.SavingCardLargessRate = saleTimeCardCategoryHandlerAddForm.SavingCardLargessRate;
                    salePackageCardTimeCardCategoryHandlerCommissionEntity.SpecialBenefit = saleTimeCardCategoryHandlerAddForm.SpecialBenefit;
                    packageList.add(salePackageCardTimeCardCategoryHandlerCommissionEntity);
                }
                if (saleTimeCardCategoryHandlerAddForm.HandlerJobTypeCommission != null && saleTimeCardCategoryHandlerAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (SaleTimeCardCategoryHandlerJobTypeCommissionAddForm saleTimeCardCategoryHandlerJobTypeCommissionAddForm : saleTimeCardCategoryHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleTimeCardCategoryHandlerJobTypeCommissionAddForm.PayRate != null || saleTimeCardCategoryHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardRate != null || saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed != null ||
                                saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null || saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed != null ||
                                saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SalePackageCardTimeCardCategoryHandlerJobTypeCommissionEntity salePackageCardTimeCardCategoryHandlerJobTypeCommissionEntity = new SalePackageCardTimeCardCategoryHandlerJobTypeCommissionEntity();
                            salePackageCardTimeCardCategoryHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            salePackageCardTimeCardCategoryHandlerJobTypeCommissionEntity.TimeCardCategoryID = form.CategoryID;
                            salePackageCardTimeCardCategoryHandlerJobTypeCommissionEntity.PackageCardSaleHandlerID = saleTimeCardCategoryHandlerAddForm.SaleHandlerID;
                            salePackageCardTimeCardCategoryHandlerJobTypeCommissionEntity.JobTypeID = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.JobTypeID;
                            salePackageCardTimeCardCategoryHandlerJobTypeCommissionEntity.PayFixed = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.PayFixed;
                            salePackageCardTimeCardCategoryHandlerJobTypeCommissionEntity.PayRate = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.PayRate;
                            salePackageCardTimeCardCategoryHandlerJobTypeCommissionEntity.SavingCardFixed = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed;
                            salePackageCardTimeCardCategoryHandlerJobTypeCommissionEntity.SavingCardRate = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardRate;
                            salePackageCardTimeCardCategoryHandlerJobTypeCommissionEntity.SavingCardLargessFixed = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed;
                            salePackageCardTimeCardCategoryHandlerJobTypeCommissionEntity.SavingCardLargessRate = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            salePackageCardTimeCardCategoryHandlerJobTypeCommissionEntity.SpecialBenefit = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            packageHandlerJobTypeCommissionList.add(salePackageCardTimeCardCategoryHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
        }
        if (list != null && list.size() > 0) {
            List<List<SaleTimeCardCategoryHandlerCommissionEntity>> subs = Lists.partition(list, 20);
            for (List<SaleTimeCardCategoryHandlerCommissionEntity> entityList : subs) {
                saleTimeCardCategoryHandlerCommissionMapper.insertSaleTimeCardCategoryHandlerCommission(entityList);
            }
        }
        if (handlerJobTypeCommissionList != null && handlerJobTypeCommissionList.size() > 0) {
            List<List<SaleTimeCardCategoryHandlerJobTypeCommissionEntity>> subs = Lists.partition(handlerJobTypeCommissionList, 20);
            for (List<SaleTimeCardCategoryHandlerJobTypeCommissionEntity> entityList : subs) {
                saleTimeCardCategoryHandlerCommissionMapper.insertSaleTimeCardCategoryHandlerJobTypeCommission(entityList);
            }
        }
        if (packageList != null && packageList.size() > 0) {
            List<List<SalePackageCardTimeCardCategoryHandlerCommissionEntity>> subs = Lists.partition(packageList, 20);
            for (List<SalePackageCardTimeCardCategoryHandlerCommissionEntity> entityList : subs) {
                salePackageCardTimeCardCategoryHandlerCommissionMapper.insertSalePackageCardTimeCardCategoryHandlerCommission(entityList);
            }
        }
        if (packageHandlerJobTypeCommissionList != null && packageHandlerJobTypeCommissionList.size() > 0) {
            List<List<SalePackageCardTimeCardCategoryHandlerJobTypeCommissionEntity>> subs = Lists.partition(packageHandlerJobTypeCommissionList, 20);
            for (List<SalePackageCardTimeCardCategoryHandlerJobTypeCommissionEntity> entityList : subs) {
                salePackageCardTimeCardCategoryHandlerCommissionMapper.insertSalePackageCardTimeCardCategoryHandlerJobTypeCommission(entityList);
            }
        }

        return BaseOutput.success();
    }
}