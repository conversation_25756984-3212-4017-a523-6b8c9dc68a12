package com.zhelian.service.ibeauty.salePackageCardCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.SalePackageCardSchemeHandlerCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SalePackageCardSchemeHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SalePackageCardSchemeHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.input.SalePackageCardSchemeHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SalePackageCardSchemeHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SalePackageCardSchemeHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SalePackageCardsSchemeHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.SalePackageCardSchemeHandlerCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SalePackageCardSchemeHandlerCommissionService {

    @Autowired
    SalePackageCardSchemeHandlerCommissionMapper salePackageCardSchemeHandlerCommissionMapper;

    public BaseOutput allSalePackageCardSchemeHandlerCommission(SalePackageCardSchemeHandlerCommissionQueryForm form) {
        List<SalePackageCardSchemeHandlerCommissionOutputForm> list = salePackageCardSchemeHandlerCommissionMapper.allSalePackageCardSchemeHandlerCommission(form.EntityID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSalePackageCardSchemeHandlerCommission(SalePackageCardsSchemeHandlerCommissionAddForm form) {

        salePackageCardSchemeHandlerCommissionMapper.deleteSalePackageCardSchemeHandler(form.EntityID);
        salePackageCardSchemeHandlerCommissionMapper.deleteSalePackageCardSchemeHandlerJobType(form.EntityID);

        if (form.Handler != null && form.Handler.size() > 0) {

            List<SalePackageCardSchemeHandlerCommissionEntity> listSalePackageCardSchemeHandlerCommissionEntity = new ArrayList<>();

            List<SalePackageCardSchemeHandlerJobTypeCommissionEntity> listSalePackageCardSchemeHandlerJobTypeCommissionEntity = new ArrayList<>();

            for (SalePackageCardSchemeHandlerCommissionAddForm salePackageCardSchemeHandlerAddForm : form.Handler) {
                if (salePackageCardSchemeHandlerAddForm.PayRate != null || salePackageCardSchemeHandlerAddForm.PayFixed != null ||
                        salePackageCardSchemeHandlerAddForm.SavingCardRate != null || salePackageCardSchemeHandlerAddForm.SavingCardFixed != null ||
                        salePackageCardSchemeHandlerAddForm.SavingCardLargessRate != null || salePackageCardSchemeHandlerAddForm.SavingCardLargessFixed != null ||
                        salePackageCardSchemeHandlerAddForm.SpecialBenefit != null) {
                    SalePackageCardSchemeHandlerCommissionEntity salePackageCardSchemeHandlerCommissionEntity = new SalePackageCardSchemeHandlerCommissionEntity();
                    salePackageCardSchemeHandlerCommissionEntity.EntityID = form.EntityID;
                    salePackageCardSchemeHandlerCommissionEntity.PackageCardSaleHandlerID = salePackageCardSchemeHandlerAddForm.SaleHandlerID;
                    salePackageCardSchemeHandlerCommissionEntity.PayFixed = salePackageCardSchemeHandlerAddForm.PayFixed;
                    salePackageCardSchemeHandlerCommissionEntity.PayRate = salePackageCardSchemeHandlerAddForm.PayRate;
                    salePackageCardSchemeHandlerCommissionEntity.SavingCardFixed = salePackageCardSchemeHandlerAddForm.SavingCardFixed;
                    salePackageCardSchemeHandlerCommissionEntity.SavingCardRate = salePackageCardSchemeHandlerAddForm.SavingCardRate;
                    salePackageCardSchemeHandlerCommissionEntity.SavingCardLargessFixed = salePackageCardSchemeHandlerAddForm.SavingCardLargessFixed;
                    salePackageCardSchemeHandlerCommissionEntity.SavingCardLargessRate = salePackageCardSchemeHandlerAddForm.SavingCardLargessRate;
                    salePackageCardSchemeHandlerCommissionEntity.SpecialBenefit = salePackageCardSchemeHandlerAddForm.SpecialBenefit;
                    listSalePackageCardSchemeHandlerCommissionEntity.add(salePackageCardSchemeHandlerCommissionEntity);
                }

                if (salePackageCardSchemeHandlerAddForm.HandlerJobTypeCommission != null && salePackageCardSchemeHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SalePackageCardSchemeHandlerJobTypeCommissionAddForm salePackageCardSchemeHandlerJobTypeCommissionAddForm : salePackageCardSchemeHandlerAddForm.HandlerJobTypeCommission) {
                        if (salePackageCardSchemeHandlerJobTypeCommissionAddForm.PayRate != null || salePackageCardSchemeHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                salePackageCardSchemeHandlerJobTypeCommissionAddForm.SavingCardRate != null || salePackageCardSchemeHandlerJobTypeCommissionAddForm.SavingCardFixed != null ||
                                salePackageCardSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null || salePackageCardSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessFixed != null ||
                                salePackageCardSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SalePackageCardSchemeHandlerJobTypeCommissionEntity salePackageCardSchemeHandlerJobTypeCommissionEntity = new SalePackageCardSchemeHandlerJobTypeCommissionEntity();
                            salePackageCardSchemeHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            salePackageCardSchemeHandlerJobTypeCommissionEntity.PackageCardSaleHandlerID = salePackageCardSchemeHandlerAddForm.SaleHandlerID;
                            salePackageCardSchemeHandlerJobTypeCommissionEntity.JobTypeID = salePackageCardSchemeHandlerJobTypeCommissionAddForm.JobTypeID;
                            salePackageCardSchemeHandlerJobTypeCommissionEntity.PayFixed = salePackageCardSchemeHandlerJobTypeCommissionAddForm.PayFixed;
                            salePackageCardSchemeHandlerJobTypeCommissionEntity.PayRate = salePackageCardSchemeHandlerJobTypeCommissionAddForm.PayRate;
                            salePackageCardSchemeHandlerJobTypeCommissionEntity.SavingCardFixed = salePackageCardSchemeHandlerJobTypeCommissionAddForm.SavingCardFixed;
                            salePackageCardSchemeHandlerJobTypeCommissionEntity.SavingCardRate = salePackageCardSchemeHandlerJobTypeCommissionAddForm.SavingCardRate;
                            salePackageCardSchemeHandlerJobTypeCommissionEntity.SavingCardLargessFixed = salePackageCardSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessFixed;
                            salePackageCardSchemeHandlerJobTypeCommissionEntity.SavingCardLargessRate = salePackageCardSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            salePackageCardSchemeHandlerJobTypeCommissionEntity.SpecialBenefit = salePackageCardSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit;

                            listSalePackageCardSchemeHandlerJobTypeCommissionEntity.add(salePackageCardSchemeHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
            if (listSalePackageCardSchemeHandlerCommissionEntity != null && listSalePackageCardSchemeHandlerCommissionEntity.size() > 0) {
                List<List<SalePackageCardSchemeHandlerCommissionEntity>> subs = Lists.partition(listSalePackageCardSchemeHandlerCommissionEntity, 20);
                for (List<SalePackageCardSchemeHandlerCommissionEntity> entityList : subs) {
                    salePackageCardSchemeHandlerCommissionMapper.updateSalePackageCardSchemeHandlerCommission(entityList);
                }
            }
            if (listSalePackageCardSchemeHandlerJobTypeCommissionEntity != null && listSalePackageCardSchemeHandlerJobTypeCommissionEntity.size() > 0) {
                List<List<SalePackageCardSchemeHandlerJobTypeCommissionEntity>> subs = Lists.partition(listSalePackageCardSchemeHandlerJobTypeCommissionEntity, 20);
                for (List<SalePackageCardSchemeHandlerJobTypeCommissionEntity> entityList : subs) {
                    salePackageCardSchemeHandlerCommissionMapper.insertSalePackageCardSchemeHandlerJobTypeCommission(entityList);
                }
            }
        }
        return BaseOutput.success();
    }
}