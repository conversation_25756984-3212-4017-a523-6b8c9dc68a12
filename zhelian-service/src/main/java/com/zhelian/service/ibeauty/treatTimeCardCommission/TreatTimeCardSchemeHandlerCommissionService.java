package com.zhelian.service.ibeauty.treatTimeCardCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.TreatTimeCardSchemeHandlerCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.TreatTimeCardSchemeHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.TreatTimeCardSchemeHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatCardSchemeHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardSchemeHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardsSchemeHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.TreatCardSchemeHandlerCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatTimeCardSchemeHandlerCommissionService {

    @Autowired
    TreatTimeCardSchemeHandlerCommissionMapper treatTimeCardSchemeHandlerCommissionMapper;

    public BaseOutput allTreatProSchemeHandlerCommission(TreatCardCategoryHandlerCommissionQueryForm form) {
        List<TreatCardSchemeHandlerCommissionOutputForm> list = treatTimeCardSchemeHandlerCommissionMapper.allTreatTimeCardSchemeHandlerCommission(form.EntityID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatTimeCardSchemeHandlerCommission(TreatCardsSchemeHandlerCommissionAddForm form) {

        treatTimeCardSchemeHandlerCommissionMapper.deleteTreatTimeCardSchemeHandler(form.EntityID);
        treatTimeCardSchemeHandlerCommissionMapper.deleteTreatTimeCardSchemeHandlerJobType(form.EntityID);
        List<TreatTimeCardSchemeHandlerCommissionEntity> listTreatTimeCardSchemeHandlerCommissionEntity = new ArrayList<>();
        List<TreatTimeCardSchemeHandlerJobTypeCommissionEntity> listTreatTimeCardSchemeHandlerJobTypeCommissionEntity = new ArrayList<>();
        if (form.CardHandler != null && form.CardHandler.size() > 0) {
            for (TreatCardSchemeHandlerCommissionAddForm treatCardSchemeHandlerCommissionAddForm : form.CardHandler) {
                if (treatCardSchemeHandlerCommissionAddForm.PayRate != null || treatCardSchemeHandlerCommissionAddForm.PayFixed != null ||
                        treatCardSchemeHandlerCommissionAddForm.CardRate != null || treatCardSchemeHandlerCommissionAddForm.CardFixed != null ||
                        treatCardSchemeHandlerCommissionAddForm.CardLargessRate != null || treatCardSchemeHandlerCommissionAddForm.CardLargessFixed != null ||
                        treatCardSchemeHandlerCommissionAddForm.LargessRate != null || treatCardSchemeHandlerCommissionAddForm.LargessFixed != null ||
                        treatCardSchemeHandlerCommissionAddForm.SpecialBenefit != null) {
                    TreatTimeCardSchemeHandlerCommissionEntity treatTimeCardSchemeHandlerCommissionEntity = new TreatTimeCardSchemeHandlerCommissionEntity();
                    treatTimeCardSchemeHandlerCommissionEntity.EntityID = form.EntityID;
                    treatTimeCardSchemeHandlerCommissionEntity.TimeCardProjectTreatHandlerID = treatCardSchemeHandlerCommissionAddForm.TreatHandlerID;
                    treatTimeCardSchemeHandlerCommissionEntity.PayRate = treatCardSchemeHandlerCommissionAddForm.PayRate;
                    treatTimeCardSchemeHandlerCommissionEntity.PayFixed = treatCardSchemeHandlerCommissionAddForm.PayFixed;
                    treatTimeCardSchemeHandlerCommissionEntity.CardRate = treatCardSchemeHandlerCommissionAddForm.CardRate;
                    treatTimeCardSchemeHandlerCommissionEntity.CardFixed = treatCardSchemeHandlerCommissionAddForm.CardFixed;
                    treatTimeCardSchemeHandlerCommissionEntity.CardLargessRate = treatCardSchemeHandlerCommissionAddForm.CardLargessRate;
                    treatTimeCardSchemeHandlerCommissionEntity.CardLargessFixed = treatCardSchemeHandlerCommissionAddForm.CardLargessFixed;
                    treatTimeCardSchemeHandlerCommissionEntity.LargessFixed = treatCardSchemeHandlerCommissionAddForm.LargessFixed;
                    treatTimeCardSchemeHandlerCommissionEntity.LargessRate = treatCardSchemeHandlerCommissionAddForm.LargessRate;
                    treatTimeCardSchemeHandlerCommissionEntity.SpecialBenefit = treatCardSchemeHandlerCommissionAddForm.SpecialBenefit;
                    listTreatTimeCardSchemeHandlerCommissionEntity.add(treatTimeCardSchemeHandlerCommissionEntity);
                }

                if (treatCardSchemeHandlerCommissionAddForm.HandlerJobTypeCommission != null && treatCardSchemeHandlerCommissionAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (TreatCardSchemeHandlerJobTypeCommissionAddForm cardSchemeHandlerJobTypeCommissionAddForm : treatCardSchemeHandlerCommissionAddForm.HandlerJobTypeCommission) {
                        if (cardSchemeHandlerJobTypeCommissionAddForm.PayRate != null || cardSchemeHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                cardSchemeHandlerJobTypeCommissionAddForm.CardRate != null || cardSchemeHandlerJobTypeCommissionAddForm.CardFixed != null ||
                                cardSchemeHandlerJobTypeCommissionAddForm.CardLargessRate != null || cardSchemeHandlerJobTypeCommissionAddForm.CardLargessFixed != null ||
                                cardSchemeHandlerJobTypeCommissionAddForm.LargessRate != null || cardSchemeHandlerJobTypeCommissionAddForm.LargessFixed != null ||
                                cardSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            TreatTimeCardSchemeHandlerJobTypeCommissionEntity commissionEntity = new TreatTimeCardSchemeHandlerJobTypeCommissionEntity();
                            commissionEntity.EntityID = form.EntityID;
                            commissionEntity.TimeCardProjectTreatHandlerID = treatCardSchemeHandlerCommissionAddForm.TreatHandlerID;
                            commissionEntity.JobTypeID = cardSchemeHandlerJobTypeCommissionAddForm.JobTypeID;
                            commissionEntity.PayRate = cardSchemeHandlerJobTypeCommissionAddForm.PayRate;
                            commissionEntity.PayFixed = cardSchemeHandlerJobTypeCommissionAddForm.PayFixed;
                            commissionEntity.CardRate = cardSchemeHandlerJobTypeCommissionAddForm.CardRate;
                            commissionEntity.CardFixed = cardSchemeHandlerJobTypeCommissionAddForm.CardFixed;
                            commissionEntity.CardLargessRate = cardSchemeHandlerJobTypeCommissionAddForm.CardLargessRate;
                            commissionEntity.CardLargessFixed = cardSchemeHandlerJobTypeCommissionAddForm.CardLargessFixed;
                            commissionEntity.LargessFixed = cardSchemeHandlerJobTypeCommissionAddForm.LargessFixed;
                            commissionEntity.LargessRate = cardSchemeHandlerJobTypeCommissionAddForm.LargessRate;
                            commissionEntity.SpecialBenefit = cardSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            listTreatTimeCardSchemeHandlerJobTypeCommissionEntity.add(commissionEntity);
                        }
                    }
                }
            }
        }
        if (listTreatTimeCardSchemeHandlerCommissionEntity != null && listTreatTimeCardSchemeHandlerCommissionEntity.size() > 0) {
            List<List<TreatTimeCardSchemeHandlerCommissionEntity>> subs = Lists.partition(listTreatTimeCardSchemeHandlerCommissionEntity, 20);
            for (List<TreatTimeCardSchemeHandlerCommissionEntity> entityList : subs) {
                treatTimeCardSchemeHandlerCommissionMapper.updateTreatTimeCardSchemeHandlerCommission(entityList);
            }
        }
        if (listTreatTimeCardSchemeHandlerJobTypeCommissionEntity != null && listTreatTimeCardSchemeHandlerJobTypeCommissionEntity.size() > 0) {
            List<List<TreatTimeCardSchemeHandlerJobTypeCommissionEntity>> subs = Lists.partition(listTreatTimeCardSchemeHandlerJobTypeCommissionEntity, 20);
            for (List<TreatTimeCardSchemeHandlerJobTypeCommissionEntity> entityList : subs) {
                treatTimeCardSchemeHandlerCommissionMapper.insertTreatTimeCardSchemeHandlerJobTypeCommission(entityList);
            }
        }
        return BaseOutput.success();
    }
}