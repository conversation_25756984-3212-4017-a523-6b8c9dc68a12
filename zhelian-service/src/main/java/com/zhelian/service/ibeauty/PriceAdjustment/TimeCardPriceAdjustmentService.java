package com.zhelian.service.ibeauty.PriceAdjustment;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.ibeauty.TimeCardPriceAdjustmentMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.TimeCardPriceAdjustmentEntity;
import com.zhelian.model.ibeauty.form.input.TimeCardPriceAdjustmentAddForm;
import com.zhelian.model.ibeauty.form.input.TimeCardPriceAdjustmentDeleteForm;
import com.zhelian.model.ibeauty.form.input.TimeCardPriceAdjustmentQueryForm;
import com.zhelian.model.ibeauty.form.output.TimeCardPriceAdjustmentOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class TimeCardPriceAdjustmentService {

    @Autowired
    TimeCardPriceAdjustmentMapper timeCardPriceAdjustmentMapper;

    public BaseOutput timeCardPriceAdjustmentList(TimeCardPriceAdjustmentQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<TimeCardPriceAdjustmentOutputForm> list = timeCardPriceAdjustmentMapper.timeCardPriceAdjustmentList(form.TimeCardName, form.EntityName);
        return BasePageInfo.success(list);
    }

    public BaseOutput createTimeCardPriceAdjustment(TimeCardPriceAdjustmentAddForm form) {

        Integer count = timeCardPriceAdjustmentMapper.timeCardPriceAdjustmentCount(form.EntityID, form.TimeCardID);
        if (count > 0)
            return BaseOutput.failed("时效卡在该组织单位已经设置调整价格");

        TimeCardPriceAdjustmentEntity timeCardPriceAdjustmentEntity = new TimeCardPriceAdjustmentEntity();
        timeCardPriceAdjustmentEntity.EntityID = form.EntityID;
        timeCardPriceAdjustmentEntity.Price = form.Price;
        timeCardPriceAdjustmentEntity.TimeCardID = form.TimeCardID;
        timeCardPriceAdjustmentMapper.insertTimeCardPriceAdjustmentEntity(timeCardPriceAdjustmentEntity);
        return BaseOutputForm.success();
    }


    public BaseOutput updateTimeCardPriceAdjustmentEntity(TimeCardPriceAdjustmentAddForm form) {

        TimeCardPriceAdjustmentEntity timeCardPriceAdjustmentEntity = new TimeCardPriceAdjustmentEntity();
        timeCardPriceAdjustmentEntity.EntityID = form.EntityID;
        timeCardPriceAdjustmentEntity.Price = form.Price;
        timeCardPriceAdjustmentEntity.TimeCardID = form.TimeCardID;
        timeCardPriceAdjustmentMapper.updateTimeCardPriceAdjustmentEntity(timeCardPriceAdjustmentEntity);

        return BaseOutput.success();
    }

    public BaseOutput deleteTimeCardPriceAdjustmentEntity(TimeCardPriceAdjustmentDeleteForm form) {

        timeCardPriceAdjustmentMapper.deleteTimeCardPriceAdjustmentEntity(form.EntityID, form.TimeCardID);
        return BaseOutput.success();
    }
}