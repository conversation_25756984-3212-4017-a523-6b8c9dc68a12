package com.zhelian.service.ibeauty.saleGeneralCardCommission;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.ibeauty.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.SaleGeneralCardCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardCommissionSchemeDeleteForm;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardCommissionSchemeQueryForm;
import com.zhelian.model.ibeauty.form.output.SaleGeneralCardCommissionSchemeOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class SaleGeneralCardCommissionSchemeService {

    @Autowired
    SaleGeneralCardCommissionSchemeMapper saleGeneralCardCommissionSchemeMapper;
    @Autowired
    SaleGeneralCardCategoryCommissionMapper saleGeneralCardCategoryCommissionMapper;
    @Autowired
    SaleGeneralCardCategoryHandlerCommissionMapper saleGeneralCardCategoryHandlerCommissionMapper;
    @Autowired
    SaleGeneralCardCommissionMapper saleGeneralCardCommissionMapper;
    @Autowired
    SaleGeneralCardHandlerCommissionMapper saleGeneralCardHandlerCommissionMapper;
    @Autowired
    SaleGeneralCardSchemeHandlerCommissionMapper saleGeneralCardSchemeHandlerCommissionMapper;

    public BaseOutput saleGeneralCardCommissionSchemeList(SaleGeneralCardCommissionSchemeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleGeneralCardCommissionSchemeOutputForm> list = saleGeneralCardCommissionSchemeMapper.saleGeneralCardCommissionSchemeList(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput createSaleGeneralCardCommissionScheme(SaleGeneralCardCommissionSchemeAddForm form) {

        if (saleGeneralCardCommissionSchemeMapper.findSaleGeneralCardCommissionSchemeByEntityID(form.EntityID) != null)
            return BaseOutput.failed("该组织单位已经创建提佣方案");

        SaleGeneralCardCommissionSchemeEntity saleGeneralCardCommissionSchemeEntity = new SaleGeneralCardCommissionSchemeEntity();
        saleGeneralCardCommissionSchemeEntity.EntityID = form.EntityID;

        saleGeneralCardCommissionSchemeMapper.createSaleGeneralCardCommissionScheme(saleGeneralCardCommissionSchemeEntity);
        return BaseOutput.success();

    }

    @Transactional
    public BaseOutput deleteSaleGeneralCardCommissionScheme(SaleGeneralCardCommissionSchemeDeleteForm form) {
        saleGeneralCardCommissionSchemeMapper.deleteSaleGeneralCardCommissionScheme(form.EntityID);
        saleGeneralCardCategoryCommissionMapper.deleteSaleGeneralCardCategoryCommission(form.EntityID);
        saleGeneralCardCategoryHandlerCommissionMapper.deleteSaleGeneralCardCategoryHandlerCommission(form.EntityID);
        saleGeneralCardCategoryHandlerCommissionMapper.deleteSaleGeneralCardCategoryHandlerJobTypeCommission(form.EntityID);
        saleGeneralCardCommissionMapper.deleteSaleGeneralCardCommissionByEntityID(form.EntityID);
        saleGeneralCardHandlerCommissionMapper.deleteSaleGeneralCardHandlerCommission(form.EntityID);
        saleGeneralCardHandlerCommissionMapper.deleteSaleGeneralCardHandlerJobTypeCommission(form.EntityID);
        saleGeneralCardSchemeHandlerCommissionMapper.deleteSaleGeneralCardSchemeHandler(form.EntityID);
        saleGeneralCardSchemeHandlerCommissionMapper.deleteSaleGeneralCardSchemeHandlerJobType(form.EntityID);
        return BaseOutput.success();
    }

}