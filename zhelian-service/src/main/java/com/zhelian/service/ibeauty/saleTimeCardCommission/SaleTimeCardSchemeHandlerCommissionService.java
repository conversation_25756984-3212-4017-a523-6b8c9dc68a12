package com.zhelian.service.ibeauty.saleTimeCardCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.SalePackageCardTimeCardSchemeHandlerCommissionMapper;
import com.zhelian.mapper.ibeauty.SaleTimeCardSchemeHandlerCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SalePackageCardTimeCardSchemeHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SalePackageCardTimeCardSchemeHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleTimeCardSchemeHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleTimeCardSchemeHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardSchemeHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardSchemeHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardSchemeHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardsSchemeHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.SaleTimeCardSchemeHandlerCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleTimeCardSchemeHandlerCommissionService {

    @Autowired
    SaleTimeCardSchemeHandlerCommissionMapper saleTimeCardSchemeHandlerCommissionMapper;

    @Autowired
    SalePackageCardTimeCardSchemeHandlerCommissionMapper salePackageCardTimeCardSchemeHandlerCommissionMapper;

    public BaseOutput allSaleTimeCardSchemeHandlerCommission(SaleTimeCardSchemeHandlerCommissionQueryForm form) {
        List<SaleTimeCardSchemeHandlerCommissionOutputForm> list = saleTimeCardSchemeHandlerCommissionMapper.allSaleTimeCardSchemeHandlerCommission(form.EntityID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSalePackageCardTimeCardSchemeHandlerCommission(SaleTimeCardSchemeHandlerCommissionQueryForm form) {
        List<SaleTimeCardSchemeHandlerCommissionOutputForm> list = salePackageCardTimeCardSchemeHandlerCommissionMapper.allSalePackageCardTimeCardSchemeHandlerCommission(form.EntityID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleTimeCardSchemeHandlerCommission(SaleTimeCardsSchemeHandlerCommissionAddForm form) {

        saleTimeCardSchemeHandlerCommissionMapper.deleteSaleTimeCardSchemeHandler(form.EntityID);
        saleTimeCardSchemeHandlerCommissionMapper.deleteSaleTimeCardSchemeHandlerJobType(form.EntityID);
        List<SaleTimeCardSchemeHandlerCommissionEntity> listSaleTimeCardSchemeHandlerCommissionEntity = new ArrayList<>();
        List<SaleTimeCardSchemeHandlerJobTypeCommissionEntity> listSaleTimeCardSchemeHandlerJobTypeCommissionEntity = new ArrayList<>();
        if (form.TimeCardHandler != null && form.TimeCardHandler.size() > 0) {
            for (SaleTimeCardSchemeHandlerCommissionAddForm saleTimeCardSchemeHandlerAddForm : form.TimeCardHandler) {
                if (saleTimeCardSchemeHandlerAddForm.PayRate != null || saleTimeCardSchemeHandlerAddForm.PayFixed != null ||
                        saleTimeCardSchemeHandlerAddForm.SavingCardRate != null || saleTimeCardSchemeHandlerAddForm.SavingCardFixed != null ||
                        saleTimeCardSchemeHandlerAddForm.SavingCardLargessRate != null || saleTimeCardSchemeHandlerAddForm.SavingCardLargessFixed != null ||
                        saleTimeCardSchemeHandlerAddForm.SpecialBenefit != null) {
                    SaleTimeCardSchemeHandlerCommissionEntity saleTimeCardSchemeHandlerCommissionEntity = new SaleTimeCardSchemeHandlerCommissionEntity();
                    saleTimeCardSchemeHandlerCommissionEntity.EntityID = form.EntityID;
                    saleTimeCardSchemeHandlerCommissionEntity.TimeCardSaleHandlerID = saleTimeCardSchemeHandlerAddForm.SaleHandlerID;
                    saleTimeCardSchemeHandlerCommissionEntity.PayFixed = saleTimeCardSchemeHandlerAddForm.PayFixed;
                    saleTimeCardSchemeHandlerCommissionEntity.PayRate = saleTimeCardSchemeHandlerAddForm.PayRate;
                    saleTimeCardSchemeHandlerCommissionEntity.SavingCardFixed = saleTimeCardSchemeHandlerAddForm.SavingCardFixed;
                    saleTimeCardSchemeHandlerCommissionEntity.SavingCardRate = saleTimeCardSchemeHandlerAddForm.SavingCardRate;
                    saleTimeCardSchemeHandlerCommissionEntity.SavingCardLargessFixed = saleTimeCardSchemeHandlerAddForm.SavingCardLargessFixed;
                    saleTimeCardSchemeHandlerCommissionEntity.SavingCardLargessRate = saleTimeCardSchemeHandlerAddForm.SavingCardLargessRate;
                    saleTimeCardSchemeHandlerCommissionEntity.SpecialBenefit = saleTimeCardSchemeHandlerAddForm.SpecialBenefit;
                    listSaleTimeCardSchemeHandlerCommissionEntity.add(saleTimeCardSchemeHandlerCommissionEntity);
                }

                if (saleTimeCardSchemeHandlerAddForm.HandlerJobTypeCommission != null && saleTimeCardSchemeHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleTimeCardSchemeHandlerJobTypeCommissionAddForm saleTimeCardSchemeHandlerJobTypeCommissionAddForm : saleTimeCardSchemeHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleTimeCardSchemeHandlerJobTypeCommissionAddForm.PayRate != null || saleTimeCardSchemeHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                saleTimeCardSchemeHandlerJobTypeCommissionAddForm.SavingCardRate != null || saleTimeCardSchemeHandlerJobTypeCommissionAddForm.SavingCardFixed != null ||
                                saleTimeCardSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null || saleTimeCardSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessFixed != null ||
                                saleTimeCardSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SaleTimeCardSchemeHandlerJobTypeCommissionEntity saleTimeCardSchemeHandlerJobTypeCommissionEntity = new SaleTimeCardSchemeHandlerJobTypeCommissionEntity();
                            saleTimeCardSchemeHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            saleTimeCardSchemeHandlerJobTypeCommissionEntity.TimeCardSaleHandlerID = saleTimeCardSchemeHandlerAddForm.SaleHandlerID;
                            saleTimeCardSchemeHandlerJobTypeCommissionEntity.JobTypeID = saleTimeCardSchemeHandlerJobTypeCommissionAddForm.JobTypeID;
                            saleTimeCardSchemeHandlerJobTypeCommissionEntity.PayFixed = saleTimeCardSchemeHandlerJobTypeCommissionAddForm.PayFixed;
                            saleTimeCardSchemeHandlerJobTypeCommissionEntity.PayRate = saleTimeCardSchemeHandlerJobTypeCommissionAddForm.PayRate;
                            saleTimeCardSchemeHandlerJobTypeCommissionEntity.SavingCardFixed = saleTimeCardSchemeHandlerJobTypeCommissionAddForm.SavingCardFixed;
                            saleTimeCardSchemeHandlerJobTypeCommissionEntity.SavingCardRate = saleTimeCardSchemeHandlerJobTypeCommissionAddForm.SavingCardRate;
                            saleTimeCardSchemeHandlerJobTypeCommissionEntity.SavingCardLargessFixed = saleTimeCardSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessFixed;
                            saleTimeCardSchemeHandlerJobTypeCommissionEntity.SavingCardLargessRate = saleTimeCardSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            saleTimeCardSchemeHandlerJobTypeCommissionEntity.SpecialBenefit = saleTimeCardSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            listSaleTimeCardSchemeHandlerJobTypeCommissionEntity.add(saleTimeCardSchemeHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
        }


        salePackageCardTimeCardSchemeHandlerCommissionMapper.deleteSalePackageCardTimeCardSchemeHandler(form.EntityID);
        salePackageCardTimeCardSchemeHandlerCommissionMapper.deleteSalePackageCardTimeCardSchemeHandlerJobType(form.EntityID);
        List<SalePackageCardTimeCardSchemeHandlerCommissionEntity> listSalePackageCardTimeCardSchemeHandlerCommissionEntity = new ArrayList<>();
        List<SalePackageCardTimeCardSchemeHandlerJobTypeCommissionEntity> listSalePackageCardTimeCardSchemeHandlerJobTypeCommissionEntity = new ArrayList<>();
        if (form.PackageCardTimeCardHandler != null && form.PackageCardTimeCardHandler.size() > 0) {
            for (SaleTimeCardSchemeHandlerCommissionAddForm saleTimeCardSchemeHandlerAddForm : form.PackageCardTimeCardHandler) {
                if (saleTimeCardSchemeHandlerAddForm.PayRate != null || saleTimeCardSchemeHandlerAddForm.PayFixed != null ||
                        saleTimeCardSchemeHandlerAddForm.SavingCardRate != null || saleTimeCardSchemeHandlerAddForm.SavingCardFixed != null ||
                        saleTimeCardSchemeHandlerAddForm.SavingCardLargessRate != null || saleTimeCardSchemeHandlerAddForm.SavingCardLargessFixed != null ||
                        saleTimeCardSchemeHandlerAddForm.SpecialBenefit != null) {
                    SalePackageCardTimeCardSchemeHandlerCommissionEntity salePackageCardTimeCardSchemeHandlerCommissionEntity = new SalePackageCardTimeCardSchemeHandlerCommissionEntity();
                    salePackageCardTimeCardSchemeHandlerCommissionEntity.EntityID = form.EntityID;
                    salePackageCardTimeCardSchemeHandlerCommissionEntity.PackageCardSaleHandlerID = saleTimeCardSchemeHandlerAddForm.SaleHandlerID;
                    salePackageCardTimeCardSchemeHandlerCommissionEntity.PayFixed = saleTimeCardSchemeHandlerAddForm.PayFixed;
                    salePackageCardTimeCardSchemeHandlerCommissionEntity.PayRate = saleTimeCardSchemeHandlerAddForm.PayRate;
                    salePackageCardTimeCardSchemeHandlerCommissionEntity.SavingCardFixed = saleTimeCardSchemeHandlerAddForm.SavingCardFixed;
                    salePackageCardTimeCardSchemeHandlerCommissionEntity.SavingCardRate = saleTimeCardSchemeHandlerAddForm.SavingCardRate;
                    salePackageCardTimeCardSchemeHandlerCommissionEntity.SavingCardLargessFixed = saleTimeCardSchemeHandlerAddForm.SavingCardLargessFixed;
                    salePackageCardTimeCardSchemeHandlerCommissionEntity.SavingCardLargessRate = saleTimeCardSchemeHandlerAddForm.SavingCardLargessRate;
                    salePackageCardTimeCardSchemeHandlerCommissionEntity.SpecialBenefit = saleTimeCardSchemeHandlerAddForm.SpecialBenefit;
                    listSalePackageCardTimeCardSchemeHandlerCommissionEntity.add(salePackageCardTimeCardSchemeHandlerCommissionEntity);
                }

                if (saleTimeCardSchemeHandlerAddForm.HandlerJobTypeCommission != null && saleTimeCardSchemeHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleTimeCardSchemeHandlerJobTypeCommissionAddForm saleTimeCardSchemeHandlerJobTypeCommissionAddForm : saleTimeCardSchemeHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleTimeCardSchemeHandlerJobTypeCommissionAddForm.PayRate != null || saleTimeCardSchemeHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                saleTimeCardSchemeHandlerJobTypeCommissionAddForm.SavingCardRate != null || saleTimeCardSchemeHandlerJobTypeCommissionAddForm.SavingCardFixed != null ||
                                saleTimeCardSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null || saleTimeCardSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessFixed != null ||
                                saleTimeCardSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SalePackageCardTimeCardSchemeHandlerJobTypeCommissionEntity salePackageCardTimeCardSchemeHandlerJobTypeCommissionEntity = new SalePackageCardTimeCardSchemeHandlerJobTypeCommissionEntity();
                            salePackageCardTimeCardSchemeHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            salePackageCardTimeCardSchemeHandlerJobTypeCommissionEntity.PackageCardSaleHandlerID = saleTimeCardSchemeHandlerAddForm.SaleHandlerID;
                            salePackageCardTimeCardSchemeHandlerJobTypeCommissionEntity.JobTypeID = saleTimeCardSchemeHandlerJobTypeCommissionAddForm.JobTypeID;
                            salePackageCardTimeCardSchemeHandlerJobTypeCommissionEntity.PayFixed = saleTimeCardSchemeHandlerJobTypeCommissionAddForm.PayFixed;
                            salePackageCardTimeCardSchemeHandlerJobTypeCommissionEntity.PayRate = saleTimeCardSchemeHandlerJobTypeCommissionAddForm.PayRate;
                            salePackageCardTimeCardSchemeHandlerJobTypeCommissionEntity.SavingCardFixed = saleTimeCardSchemeHandlerJobTypeCommissionAddForm.SavingCardFixed;
                            salePackageCardTimeCardSchemeHandlerJobTypeCommissionEntity.SavingCardRate = saleTimeCardSchemeHandlerJobTypeCommissionAddForm.SavingCardRate;
                            salePackageCardTimeCardSchemeHandlerJobTypeCommissionEntity.SavingCardLargessFixed = saleTimeCardSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessFixed;
                            salePackageCardTimeCardSchemeHandlerJobTypeCommissionEntity.SavingCardLargessRate = saleTimeCardSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            salePackageCardTimeCardSchemeHandlerJobTypeCommissionEntity.SpecialBenefit = saleTimeCardSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            listSalePackageCardTimeCardSchemeHandlerJobTypeCommissionEntity.add(salePackageCardTimeCardSchemeHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
        }
        if (listSaleTimeCardSchemeHandlerCommissionEntity != null && listSaleTimeCardSchemeHandlerCommissionEntity.size() > 0) {
            List<List<SaleTimeCardSchemeHandlerCommissionEntity>> subs = Lists.partition(listSaleTimeCardSchemeHandlerCommissionEntity, 20);
            for (List<SaleTimeCardSchemeHandlerCommissionEntity> entityList : subs) {
                saleTimeCardSchemeHandlerCommissionMapper.updateSaleTimeCardSchemeHandlerCommission(entityList);
            }
        }
        if (listSaleTimeCardSchemeHandlerJobTypeCommissionEntity != null && listSaleTimeCardSchemeHandlerJobTypeCommissionEntity.size() > 0) {
            List<List<SaleTimeCardSchemeHandlerJobTypeCommissionEntity>> subs = Lists.partition(listSaleTimeCardSchemeHandlerJobTypeCommissionEntity, 20);
            for (List<SaleTimeCardSchemeHandlerJobTypeCommissionEntity> entityList : subs) {
                saleTimeCardSchemeHandlerCommissionMapper.insertSaleTimeCardSchemeHandlerJobTypeCommission(entityList);
            }
        }
        if (listSalePackageCardTimeCardSchemeHandlerCommissionEntity != null && listSalePackageCardTimeCardSchemeHandlerCommissionEntity.size() > 0) {
            List<List<SalePackageCardTimeCardSchemeHandlerCommissionEntity>> subs = Lists.partition(listSalePackageCardTimeCardSchemeHandlerCommissionEntity, 20);
            for (List<SalePackageCardTimeCardSchemeHandlerCommissionEntity> entityList : subs) {
                salePackageCardTimeCardSchemeHandlerCommissionMapper.updateSalePackageCardTimeCardSchemeHandlerCommission(entityList);
            }
        }
        if (listSalePackageCardTimeCardSchemeHandlerJobTypeCommissionEntity != null && listSalePackageCardTimeCardSchemeHandlerJobTypeCommissionEntity.size() > 0) {
            List<List<SalePackageCardTimeCardSchemeHandlerJobTypeCommissionEntity>> subs = Lists.partition(listSalePackageCardTimeCardSchemeHandlerJobTypeCommissionEntity, 20);
            for (List<SalePackageCardTimeCardSchemeHandlerJobTypeCommissionEntity> entityList : subs) {
                salePackageCardTimeCardSchemeHandlerCommissionMapper.insertSalePackageCardTimeCardSchemeHandlerJobTypeCommission(entityList);
            }
        }

        return BaseOutput.success();
    }
}