package com.zhelian.service.ibeauty.refundTreat;

import com.zhelian.core.utils.enumeration.WeiXinOfficialAccountMessage;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.RefundTreatBillApprovalForm;
import com.zhelian.service.weixin.WeiXinMessageService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class RefundTreatBillCommonService {
    @Autowired
    RefundTreatBillService refundTreatBillService;
    @Autowired
    WeiXinMessageService weiXinMessageService;

    public BaseOutput approval(RefundTreatBillApprovalForm form, String channel) {

        String bill = refundTreatBillService.approvalBill(form, channel);
        if (form.ApprovalStatus.equals("30") && StringUtils.isNotBlank(bill)) {
            weiXinMessageService.setRefundTreatBillWxMessage(bill, WeiXinOfficialAccountMessage.RefundTreatNotice);
        }
        return BaseOutput.success();
    }
}
