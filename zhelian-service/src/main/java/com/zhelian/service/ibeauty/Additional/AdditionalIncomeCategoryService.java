package com.zhelian.service.ibeauty.Additional;

import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.AdditionalIncomeCategoryMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.AdditionalIncomeCategoryEntity;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingOrIncomeOrPaymentsCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingOrIncomeOrPaymentsCategoryQueryForm;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingOrIncomeOrPaymentsCategoryUpdateForm;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingOrIncomeOrPaymentsMoveForm;
import com.zhelian.model.ibeauty.form.output.AdditionalSpendingOrIncomeOrPaymentsCategoryOutputForm;
import com.zhelian.model.ibeauty.form.output.SpendingCategoryOrIncomeOrPaymentsOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * @className: AdditionalIncomeCategoryService
 * @description:
 * @version: V1.0
 * @author: zhoutianxiang
 * @date: 2020/4/25 4:17 PM
 */
@Service
public class AdditionalIncomeCategoryService {

    @Autowired
    AdditionalIncomeCategoryMapper additionalIncomeCategoryMapper;

    public BaseOutput all(AdditionalSpendingOrIncomeOrPaymentsCategoryQueryForm form) {

        List<SpendingCategoryOrIncomeOrPaymentsOutputForm> list = additionalIncomeCategoryMapper.additionalIncomeCategoryList(form.Name, form.Active);

        return BaseOutputForm.success(list);
    }


    public BaseOutput create(AdditionalSpendingOrIncomeOrPaymentsCategoryAddForm form) {

        Integer Sequence = additionalIncomeCategoryMapper.getMaxSequence();
        AdditionalIncomeCategoryEntity additionalIncomeCategoryEntity = new AdditionalIncomeCategoryEntity();
        additionalIncomeCategoryEntity.Name = form.Name;
        additionalIncomeCategoryEntity.Sequence = Sequence != null ? Sequence + 1 : 1;

        additionalIncomeCategoryMapper.createAdditionalIncomeCategory(additionalIncomeCategoryEntity);

        return BaseOutput.success();
    }

    public BaseOutput update(AdditionalSpendingOrIncomeOrPaymentsCategoryUpdateForm form) {

        AdditionalIncomeCategoryEntity additionalIncomeCategoryEntity = new AdditionalIncomeCategoryEntity();
        additionalIncomeCategoryEntity.ID = form.ID;
        additionalIncomeCategoryEntity.Name = form.Name;
        additionalIncomeCategoryEntity.Active = form.Active;

        additionalIncomeCategoryMapper.updateAdditionalIncomeCategory(additionalIncomeCategoryEntity);

        return BaseOutput.success();
    }

    public BaseOutput move(AdditionalSpendingOrIncomeOrPaymentsMoveForm form) {

        //重新排序
        LinkedList<Integer> linkedList = additionalIncomeCategoryMapper.getIDOrderBySequenceASC(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<AdditionalIncomeCategoryEntity> list = new ArrayList<>();

        for (Integer ID : linkedList) {
            AdditionalIncomeCategoryEntity additionalIncomeCategoryEntity = new AdditionalIncomeCategoryEntity();
            additionalIncomeCategoryEntity.ID = ID;
            additionalIncomeCategoryEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(additionalIncomeCategoryEntity);
        }
        additionalIncomeCategoryMapper.updateSequence(list);
        return BaseOutput.success();
    }

    public BaseOutput getAdditionalIncomeCategory() {
        List<AdditionalSpendingOrIncomeOrPaymentsCategoryOutputForm> list = additionalIncomeCategoryMapper.getAdditionalIncomeCategory();
        return BaseOutputForm.success(list);
    }
}