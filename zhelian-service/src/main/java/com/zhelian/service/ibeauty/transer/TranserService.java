package com.zhelian.service.ibeauty.transer;

import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.autoNumber.AutoNumberUtils;
import com.zhelian.core.utils.DateTimeUtil;
import com.zhelian.core.utils.OSSClientUtil;
import com.zhelian.core.utils.enumeration.AccountChannel;
import com.zhelian.core.utils.enumeration.BillCode;
import com.zhelian.mapper.ibeauty.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.*;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.ibeauty.form.output.*;
import com.zhelian.model.miniprogram.form.input.MiniprogramTranserQueryForm;
import com.zhelian.model.miniprogram.form.input.SaleBillSignatureAddForm;
import com.zhelian.model.miniprogram.form.output.MiniprogramTranserBillOutputForm;
import com.zhelian.model.miniprogram.form.output.SignatureOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class TranserService {

    @Autowired
    private TranserMapper transerMapper;
    @Autowired
    private ProjectAccountMapper projectAccountMapper;
    @Autowired
    private ProductAccountMapper productAccountMapper;
    @Autowired
    private SavingCardAccountMapper savingCardAccountMapper;
    @Autowired
    private GeneralCardAccountMapper generalCardAccountMapper;
    @Autowired
    private TimeCardAccountMapper timeCardAccountMapper;
    @Autowired
    private PackageCardAccountMapper packageCardAccountMapper;

    @Transactional
    public String createBill(TranserAddForm form, String channel) throws Exception {

        TransferBillEntity transferBillEntity = new TransferBillEntity();
        transferBillEntity.ID = AutoNumberUtils.getIBeautyTransferBill();
        transferBillEntity.EntityID = SystemUserUtil.getSystemEntityID();
        transferBillEntity.BillStatus = BillCode.BillStatus_20.getID();
        transferBillEntity.TransferInCustomerID = form.TransferInCustomerID;
        transferBillEntity.TransferOutCustomerID = form.TransferOutCustomerID;
        transferBillEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        transferBillEntity.CreatedOn = new Date();
        transferBillEntity.Remark = form.Remark;
        transferBillEntity.Channel = channel;
        transerMapper.createTransferBill(transferBillEntity);

        List<TransferBillProductAccountEntity> transerProduct = new ArrayList<>();
        List<TransferBillProjectAccountEntity> transerProject = new ArrayList<>();
        List<TransferBillGeneralCardAccountEntity> transerGeneralCard = new ArrayList<>();
        List<TransferBillTimeCardAccountEntity> transerTimeCard = new ArrayList<>();
        List<TransferBillSavingCardAccountEntity> transerSavingCard = new ArrayList<>();
        if (form.Product != null && form.Product.size() > 0) {
            for (TranserProductAddForm transerProductAddForm : form.Product) {
                TranserProductAccountOutputForm productAccountEntity = productAccountMapper.getProductAccountEntity(transerProductAddForm.ProductAccountID);
                if (productAccountEntity.ValidBalance.intValue() < transerProductAddForm.Quantity.intValue()) {
                    throw new RuntimeException("产品没有足够的账户进行转账，请修改数量");
                }

                ProductAccountEntity newProductAccountEntity = new ProductAccountEntity();
                newProductAccountEntity.CustomerID = form.TransferInCustomerID;
                newProductAccountEntity.EntityID = SystemUserUtil.getSystemEntityID();
                newProductAccountEntity.BuyDate = DateTimeUtil.toDate(DateTimeUtil.toyyyyMMdd2(new Date()));
                newProductAccountEntity.ProductID = productAccountEntity.ProductID;
                newProductAccountEntity.Price = productAccountEntity.IsLargess == true ? productAccountEntity.Price : productAccountEntity.TotalAmount.divide(new BigDecimal(productAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                newProductAccountEntity.Quantity = transerProductAddForm.Quantity;
                newProductAccountEntity.TotalAmount = newProductAccountEntity.Price.multiply(new BigDecimal(transerProductAddForm.Quantity)).setScale(2, BigDecimal.ROUND_HALF_UP);
                newProductAccountEntity.Balance = transerProductAddForm.Quantity;
                newProductAccountEntity.IsLargess = productAccountEntity.IsLargess;
                newProductAccountEntity.ArrearAmount = BigDecimal.ZERO;
                if (productAccountEntity.ValidBalance.equals(transerProductAddForm.Quantity) && productAccountEntity.ValidBalance.intValue() == productAccountEntity.Balance.intValue()) {
                    newProductAccountEntity.SavingCardDeductionAmount = productAccountEntity.CardPerformanceBalanceAmount;
                    newProductAccountEntity.SavingCardDeductionLargessAmount = productAccountEntity.CardLargessPerformanceBalanceAmount;
                    newProductAccountEntity.PayAmount = productAccountEntity.PayPerformanceBalanceAmount;
                    newProductAccountEntity.CardPerformanceBalanceAmount = productAccountEntity.CardPerformanceBalanceAmount;
                    newProductAccountEntity.CardLargessPerformanceBalanceAmount = productAccountEntity.CardLargessPerformanceBalanceAmount;
                    newProductAccountEntity.PayPerformanceBalanceAmount = productAccountEntity.PayPerformanceBalanceAmount;
                    newProductAccountEntity.LargessPerformanceBalanceAmount = productAccountEntity.LargessPerformanceBalanceAmount;
                } else {
                    newProductAccountEntity.SavingCardDeductionAmount = productAccountEntity.IsLargess == true ? BigDecimal.ZERO : productAccountEntity.SavingCardDeductionAmount.multiply(new BigDecimal(transerProductAddForm.Quantity)).divide(new BigDecimal(productAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                    newProductAccountEntity.SavingCardDeductionLargessAmount = productAccountEntity.IsLargess == true ? BigDecimal.ZERO : productAccountEntity.SavingCardDeductionLargessAmount.multiply(new BigDecimal(transerProductAddForm.Quantity)).divide(new BigDecimal(productAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                    newProductAccountEntity.PayAmount = productAccountEntity.IsLargess == true ? BigDecimal.ZERO : newProductAccountEntity.TotalAmount.subtract(newProductAccountEntity.SavingCardDeductionAmount).subtract(newProductAccountEntity.SavingCardDeductionLargessAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
                    newProductAccountEntity.CardPerformanceBalanceAmount = productAccountEntity.IsLargess == true ? BigDecimal.ZERO : productAccountEntity.SavingCardDeductionAmount.multiply(new BigDecimal(transerProductAddForm.Quantity)).divide(new BigDecimal(productAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                    newProductAccountEntity.CardLargessPerformanceBalanceAmount = productAccountEntity.IsLargess == true ? BigDecimal.ZERO : productAccountEntity.SavingCardDeductionLargessAmount.multiply(new BigDecimal(transerProductAddForm.Quantity)).divide(new BigDecimal(productAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                    newProductAccountEntity.PayPerformanceBalanceAmount = productAccountEntity.IsLargess == true ? BigDecimal.ZERO : newProductAccountEntity.TotalAmount.subtract(newProductAccountEntity.SavingCardDeductionAmount).subtract(newProductAccountEntity.SavingCardDeductionLargessAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
                    newProductAccountEntity.LargessPerformanceBalanceAmount = productAccountEntity.IsLargess == true ? productAccountEntity.TotalAmount.multiply(new BigDecimal(transerProductAddForm.Quantity)).divide(new BigDecimal(productAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO;
                }
                newProductAccountEntity.Channel = AccountChannel.AccountChannel_20.getID();
                newProductAccountEntity.Remark = productAccountEntity.Remark;
                productAccountMapper.createTranserProductAccount(newProductAccountEntity);

                ProductAccountEntity updateProductAccountEntity = new ProductAccountEntity();
                updateProductAccountEntity.ID = productAccountEntity.ID;
                updateProductAccountEntity.Balance = productAccountEntity.Balance.intValue() - transerProductAddForm.Quantity;
                updateProductAccountEntity.PayPerformanceBalanceAmount = productAccountEntity.PayPerformanceBalanceAmount.subtract(newProductAccountEntity.PayPerformanceBalanceAmount);
                updateProductAccountEntity.CardPerformanceBalanceAmount = productAccountEntity.CardPerformanceBalanceAmount.subtract(newProductAccountEntity.CardPerformanceBalanceAmount);
                updateProductAccountEntity.CardLargessPerformanceBalanceAmount = productAccountEntity.CardLargessPerformanceBalanceAmount.subtract(newProductAccountEntity.CardLargessPerformanceBalanceAmount);
                updateProductAccountEntity.LargessPerformanceBalanceAmount = productAccountEntity.LargessPerformanceBalanceAmount.subtract(newProductAccountEntity.LargessPerformanceBalanceAmount);
                productAccountMapper.updateProductAccountBalance(updateProductAccountEntity);

                TransferBillProductAccountEntity transferBillProductAccountEntity = new TransferBillProductAccountEntity();
                transferBillProductAccountEntity.TransferBillID = transferBillEntity.ID;
                transferBillProductAccountEntity.TransferOutProductAccountID = transerProductAddForm.ProductAccountID;
                transferBillProductAccountEntity.TransferInProductAccountID = newProductAccountEntity.ID;
                transferBillProductAccountEntity.ProductID = productAccountEntity.ProductID;
                transferBillProductAccountEntity.Quantity = transerProductAddForm.Quantity;
                transerProduct.add(transferBillProductAccountEntity);
            }
        }
        if (form.Project != null && form.Project.size() > 0) {
            for (TranserProjectAddForm transerProjectAddForm : form.Project) {
                TranserProjectAccountOutputForm projectAccountEntity = projectAccountMapper.getProjectAccountEntity(transerProjectAddForm.ProjectAccountID);
                if (projectAccountEntity.ValidBalance.intValue() < transerProjectAddForm.Quantity.intValue())
                    throw new RuntimeException("项目没有足够的账户进行转账，请修改数量");

                ProjectAccountEntity newProjectAccountEntity = new ProjectAccountEntity();
                newProjectAccountEntity.CustomerID = form.TransferInCustomerID;
                newProjectAccountEntity.EntityID = SystemUserUtil.getSystemEntityID();
                newProjectAccountEntity.BuyDate = DateTimeUtil.toDate(DateTimeUtil.toyyyyMMdd2(new Date()));
                newProjectAccountEntity.ProjectID = projectAccountEntity.ProjectID;
                newProjectAccountEntity.Price = projectAccountEntity.IsLargess == true ? projectAccountEntity.Price : projectAccountEntity.TotalAmount.divide(new BigDecimal(projectAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                newProjectAccountEntity.Quantity = transerProjectAddForm.Quantity;
                newProjectAccountEntity.TotalAmount = newProjectAccountEntity.Price.multiply(new BigDecimal(transerProjectAddForm.Quantity)).setScale(2, BigDecimal.ROUND_HALF_UP);
                newProjectAccountEntity.Balance = transerProjectAddForm.Quantity;
                newProjectAccountEntity.IsLargess = projectAccountEntity.IsLargess;
                newProjectAccountEntity.ArrearAmount = BigDecimal.ZERO;
                if (projectAccountEntity.ValidBalance.equals(transerProjectAddForm.Quantity) && projectAccountEntity.ValidBalance.intValue() == projectAccountEntity.Balance.intValue()) {
                    newProjectAccountEntity.SavingCardDeductionAmount = projectAccountEntity.CardPerformanceBalanceAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                    newProjectAccountEntity.SavingCardDeductionLargessAmount = projectAccountEntity.CardLargessPerformanceBalanceAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                    newProjectAccountEntity.PayAmount = projectAccountEntity.PayPerformanceBalanceAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                    newProjectAccountEntity.CardPerformanceBalanceAmount = projectAccountEntity.CardPerformanceBalanceAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                    newProjectAccountEntity.CardLargessPerformanceBalanceAmount = projectAccountEntity.CardLargessPerformanceBalanceAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                    newProjectAccountEntity.PayPerformanceBalanceAmount = projectAccountEntity.PayPerformanceBalanceAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                    newProjectAccountEntity.LargessPerformanceBalanceAmount = projectAccountEntity.LargessPerformanceBalanceAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                } else {
                    newProjectAccountEntity.SavingCardDeductionAmount = projectAccountEntity.IsLargess == true ? BigDecimal.ZERO : projectAccountEntity.SavingCardDeductionAmount.multiply(new BigDecimal(transerProjectAddForm.Quantity)).divide(new BigDecimal(projectAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                    newProjectAccountEntity.SavingCardDeductionLargessAmount = projectAccountEntity.IsLargess == true ? BigDecimal.ZERO : projectAccountEntity.SavingCardDeductionLargessAmount.multiply(new BigDecimal(transerProjectAddForm.Quantity)).divide(new BigDecimal(projectAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                    newProjectAccountEntity.PayAmount = projectAccountEntity.IsLargess == true ? BigDecimal.ZERO : newProjectAccountEntity.TotalAmount.subtract(newProjectAccountEntity.SavingCardDeductionAmount).subtract(newProjectAccountEntity.SavingCardDeductionLargessAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
                    newProjectAccountEntity.CardPerformanceBalanceAmount = projectAccountEntity.IsLargess == true ? BigDecimal.ZERO : projectAccountEntity.SavingCardDeductionAmount.multiply(new BigDecimal(transerProjectAddForm.Quantity)).divide(new BigDecimal(projectAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                    newProjectAccountEntity.CardLargessPerformanceBalanceAmount = projectAccountEntity.IsLargess == true ? BigDecimal.ZERO : projectAccountEntity.SavingCardDeductionLargessAmount.multiply(new BigDecimal(transerProjectAddForm.Quantity)).divide(new BigDecimal(projectAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                    newProjectAccountEntity.PayPerformanceBalanceAmount = projectAccountEntity.IsLargess == true ? BigDecimal.ZERO : newProjectAccountEntity.TotalAmount.subtract(newProjectAccountEntity.SavingCardDeductionAmount).subtract(newProjectAccountEntity.SavingCardDeductionLargessAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
                    newProjectAccountEntity.LargessPerformanceBalanceAmount = projectAccountEntity.IsLargess == true ? projectAccountEntity.TotalAmount.multiply(new BigDecimal(transerProjectAddForm.Quantity)).divide(new BigDecimal(projectAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO;
                }

                newProjectAccountEntity.Channel = AccountChannel.AccountChannel_20.getID();
                newProjectAccountEntity.Remark = projectAccountEntity.Remark;
                projectAccountMapper.createTranserProjectAccount(newProjectAccountEntity);

                ProjectAccountEntity updateProjectAccountEntity = new ProjectAccountEntity();
                updateProjectAccountEntity.ID = projectAccountEntity.ID;
                updateProjectAccountEntity.Balance = projectAccountEntity.Balance.intValue() - transerProjectAddForm.Quantity;
                updateProjectAccountEntity.PayPerformanceBalanceAmount = projectAccountEntity.PayPerformanceBalanceAmount.subtract(newProjectAccountEntity.PayPerformanceBalanceAmount);
                updateProjectAccountEntity.CardPerformanceBalanceAmount = projectAccountEntity.CardPerformanceBalanceAmount.subtract(newProjectAccountEntity.CardPerformanceBalanceAmount);
                updateProjectAccountEntity.CardLargessPerformanceBalanceAmount = projectAccountEntity.CardLargessPerformanceBalanceAmount.subtract(newProjectAccountEntity.CardLargessPerformanceBalanceAmount);
                updateProjectAccountEntity.LargessPerformanceBalanceAmount = projectAccountEntity.LargessPerformanceBalanceAmount.subtract(newProjectAccountEntity.LargessPerformanceBalanceAmount);
                projectAccountMapper.updateProjectAccountBalance(updateProjectAccountEntity);

                TransferBillProjectAccountEntity transferBillProjectAccountEntity = new TransferBillProjectAccountEntity();
                transferBillProjectAccountEntity.TransferBillID = transferBillEntity.ID;
                transferBillProjectAccountEntity.TransferOutProjectAccountID = transerProjectAddForm.ProjectAccountID;
                transferBillProjectAccountEntity.TransferInProjectAccountID = newProjectAccountEntity.ID;
                transferBillProjectAccountEntity.ProjectID = projectAccountEntity.ProjectID;
                transferBillProjectAccountEntity.Quantity = transerProjectAddForm.Quantity;
                transerProject.add(transferBillProjectAccountEntity);
            }
        }
        if (form.GeneralCard != null && form.GeneralCard.size() > 0) {
            for (TranserGeneralCardAddForm transerGeneralCardAddForm : form.GeneralCard) {
                TranserGeneralCardAccountOutputForm generalCardAccountEntity = generalCardAccountMapper.getGeneralCardAccountEntity(transerGeneralCardAddForm.GeneralCardAccountID);
                if (generalCardAccountEntity.ValidBalance.intValue() < transerGeneralCardAddForm.Quantity.intValue())
                    throw new RuntimeException("通用次卡没有足够的账户进行转账，请修改数量");

                GeneralCardAccountEntity newGeneralCardAccountEntity = new GeneralCardAccountEntity();
                newGeneralCardAccountEntity.CustomerID = form.TransferInCustomerID;
                newGeneralCardAccountEntity.EntityID = SystemUserUtil.getSystemEntityID();
                newGeneralCardAccountEntity.BuyDate = DateTimeUtil.toDate(DateTimeUtil.toyyyyMMdd2(new Date()));
                newGeneralCardAccountEntity.GeneralCardID = generalCardAccountEntity.GeneralCardID;
                newGeneralCardAccountEntity.Price = generalCardAccountEntity.IsLargess == true ? generalCardAccountEntity.Price.multiply(new BigDecimal(transerGeneralCardAddForm.Quantity)).divide(new BigDecimal(generalCardAccountEntity.CardTimes), 2, BigDecimal.ROUND_HALF_UP).setScale(2, BigDecimal.ROUND_HALF_UP) : generalCardAccountEntity.TotalAmount.multiply(new BigDecimal(transerGeneralCardAddForm.Quantity)).divide(new BigDecimal(generalCardAccountEntity.CardTimes), 2, BigDecimal.ROUND_HALF_UP);
                newGeneralCardAccountEntity.CardTimes = transerGeneralCardAddForm.Quantity;
                newGeneralCardAccountEntity.Balance = transerGeneralCardAddForm.Quantity;
                newGeneralCardAccountEntity.IsLargess = generalCardAccountEntity.IsLargess;
                newGeneralCardAccountEntity.ValidType = generalCardAccountEntity.ValidType;
                newGeneralCardAccountEntity.ValidDay = generalCardAccountEntity.ValidDay;
                newGeneralCardAccountEntity.ValidDate = generalCardAccountEntity.ValidDate;
                newGeneralCardAccountEntity.ArrearAmount = BigDecimal.ZERO;
                newGeneralCardAccountEntity.TotalAmount = generalCardAccountEntity.TotalAmount.multiply(new BigDecimal(transerGeneralCardAddForm.Quantity)).divide(new BigDecimal(generalCardAccountEntity.CardTimes), 2, BigDecimal.ROUND_HALF_UP);
                if (generalCardAccountEntity.ValidBalance.equals(transerGeneralCardAddForm.Quantity) && generalCardAccountEntity.ValidBalance.intValue() == generalCardAccountEntity.Balance.intValue()) {
                    newGeneralCardAccountEntity.SavingCardDeductionAmount = generalCardAccountEntity.CardPerformanceBalanceAmount;
                    newGeneralCardAccountEntity.SavingCardDeductionLargessAmount = generalCardAccountEntity.CardLargessPerformanceBalanceAmount;
                    newGeneralCardAccountEntity.PayAmount = generalCardAccountEntity.PayPerformanceBalanceAmount;
                    newGeneralCardAccountEntity.CardPerformanceBalanceAmount = generalCardAccountEntity.CardPerformanceBalanceAmount;
                    newGeneralCardAccountEntity.CardLargessPerformanceBalanceAmount = generalCardAccountEntity.CardLargessPerformanceBalanceAmount;
                    newGeneralCardAccountEntity.PayPerformanceBalanceAmount = generalCardAccountEntity.PayPerformanceBalanceAmount;
                    newGeneralCardAccountEntity.LargessPerformanceBalanceAmount = generalCardAccountEntity.LargessPerformanceBalanceAmount;
                } else {
                    newGeneralCardAccountEntity.SavingCardDeductionAmount = generalCardAccountEntity.IsLargess == true ? BigDecimal.ZERO : generalCardAccountEntity.SavingCardDeductionAmount.divide(new BigDecimal(generalCardAccountEntity.CardTimes), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(transerGeneralCardAddForm.Quantity));
                    newGeneralCardAccountEntity.SavingCardDeductionLargessAmount = generalCardAccountEntity.IsLargess == true ? BigDecimal.ZERO : generalCardAccountEntity.SavingCardDeductionLargessAmount.divide(new BigDecimal(generalCardAccountEntity.CardTimes), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(transerGeneralCardAddForm.Quantity));
                    newGeneralCardAccountEntity.PayAmount = generalCardAccountEntity.IsLargess == true ? BigDecimal.ZERO : newGeneralCardAccountEntity.TotalAmount.subtract(newGeneralCardAccountEntity.SavingCardDeductionAmount).subtract(newGeneralCardAccountEntity.SavingCardDeductionLargessAmount);
                    newGeneralCardAccountEntity.CardPerformanceBalanceAmount = generalCardAccountEntity.IsLargess == true ? BigDecimal.ZERO : generalCardAccountEntity.SavingCardDeductionAmount.divide(new BigDecimal(generalCardAccountEntity.CardTimes), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(transerGeneralCardAddForm.Quantity));
                    newGeneralCardAccountEntity.CardLargessPerformanceBalanceAmount = generalCardAccountEntity.IsLargess == true ? BigDecimal.ZERO : generalCardAccountEntity.SavingCardDeductionLargessAmount.divide(new BigDecimal(generalCardAccountEntity.CardTimes), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(transerGeneralCardAddForm.Quantity));
                    newGeneralCardAccountEntity.PayPerformanceBalanceAmount = generalCardAccountEntity.IsLargess == true ? BigDecimal.ZERO : newGeneralCardAccountEntity.TotalAmount.subtract(newGeneralCardAccountEntity.SavingCardDeductionAmount).subtract(newGeneralCardAccountEntity.SavingCardDeductionLargessAmount);
                    newGeneralCardAccountEntity.LargessPerformanceBalanceAmount = generalCardAccountEntity.IsLargess == true ? generalCardAccountEntity.TotalAmount.multiply(new BigDecimal(transerGeneralCardAddForm.Quantity)).divide(new BigDecimal(generalCardAccountEntity.CardTimes), 2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO;
                }
                newGeneralCardAccountEntity.Channel = AccountChannel.AccountChannel_20.getID();
                newGeneralCardAccountEntity.Remark = generalCardAccountEntity.Remark;
                generalCardAccountMapper.createTranserGeneralCardAccount(newGeneralCardAccountEntity);

                GeneralCardAccountEntity updateGeneralCardAccountEntity = new GeneralCardAccountEntity();
                updateGeneralCardAccountEntity.ID = generalCardAccountEntity.ID;
                updateGeneralCardAccountEntity.Balance = generalCardAccountEntity.Balance - transerGeneralCardAddForm.Quantity;
                updateGeneralCardAccountEntity.PayPerformanceBalanceAmount = generalCardAccountEntity.PayPerformanceBalanceAmount.subtract(newGeneralCardAccountEntity.PayPerformanceBalanceAmount);
                updateGeneralCardAccountEntity.CardPerformanceBalanceAmount = generalCardAccountEntity.CardPerformanceBalanceAmount.subtract(newGeneralCardAccountEntity.CardPerformanceBalanceAmount);
                updateGeneralCardAccountEntity.ValidDate = generalCardAccountEntity.ValidDate;
                updateGeneralCardAccountEntity.CardLargessPerformanceBalanceAmount = generalCardAccountEntity.CardLargessPerformanceBalanceAmount.subtract(newGeneralCardAccountEntity.CardLargessPerformanceBalanceAmount);
                updateGeneralCardAccountEntity.LargessPerformanceBalanceAmount = generalCardAccountEntity.LargessPerformanceBalanceAmount.subtract(newGeneralCardAccountEntity.LargessPerformanceBalanceAmount);
                generalCardAccountMapper.updateGeneralCardAccountBalance(updateGeneralCardAccountEntity);

                TransferBillGeneralCardAccountEntity transferBillGeneralCardAccountEntity = new TransferBillGeneralCardAccountEntity();
                transferBillGeneralCardAccountEntity.TransferBillID = transferBillEntity.ID;
                transferBillGeneralCardAccountEntity.TransferOutGeneralCardAccountID = transerGeneralCardAddForm.GeneralCardAccountID;
                transferBillGeneralCardAccountEntity.TransferInGeneralCardAccountID = newGeneralCardAccountEntity.ID;
                transferBillGeneralCardAccountEntity.GeneralCardID = generalCardAccountEntity.GeneralCardID;
                transferBillGeneralCardAccountEntity.Quantity = transerGeneralCardAddForm.Quantity;
                transerGeneralCard.add(transferBillGeneralCardAccountEntity);
            }
        }
        if (form.TimeCard != null && form.TimeCard.size() > 0) {
            for (TranserTimeCardAddForm transerTimeCardAddForm : form.TimeCard) {
                TimeCardAccountEntity timeCardAccountEntity = timeCardAccountMapper.getTimeCardAccountByAccountID(transerTimeCardAddForm.TimeCardAccountID);
                TimeCardAccountEntity newTimeCardAccountEntity = new TimeCardAccountEntity();
                newTimeCardAccountEntity.CustomerID = form.TransferInCustomerID;
                newTimeCardAccountEntity.EntityID = SystemUserUtil.getSystemEntityID();
                newTimeCardAccountEntity.BuyDate = DateTimeUtil.toDate(DateTimeUtil.toyyyyMMdd2(new Date()));
                newTimeCardAccountEntity.TimeCardID = timeCardAccountEntity.TimeCardID;
                newTimeCardAccountEntity.Price = timeCardAccountEntity.Price;
                newTimeCardAccountEntity.TotalAmount = timeCardAccountEntity.TotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                newTimeCardAccountEntity.ArrearAmount = timeCardAccountEntity.ArrearAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                newTimeCardAccountEntity.IsLargess = timeCardAccountEntity.IsLargess;
                newTimeCardAccountEntity.PayAmount = timeCardAccountEntity.PayAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                newTimeCardAccountEntity.PerformanceBalance = timeCardAccountEntity.PerformanceBalance;
                newTimeCardAccountEntity.PerformanceTimes = timeCardAccountEntity.PerformanceTimes;
                newTimeCardAccountEntity.ValidType = timeCardAccountEntity.ValidType;
                newTimeCardAccountEntity.ValidDay = timeCardAccountEntity.ValidDay;
                newTimeCardAccountEntity.ValidDate = timeCardAccountEntity.ValidDate;
                newTimeCardAccountEntity.SavingCardDeductionAmount = timeCardAccountEntity.SavingCardDeductionAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                newTimeCardAccountEntity.SavingCardDeductionLargessAmount = timeCardAccountEntity.SavingCardDeductionLargessAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                newTimeCardAccountEntity.PayPerformanceBalanceAmount = timeCardAccountEntity.PayPerformanceBalanceAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                newTimeCardAccountEntity.CardPerformanceBalanceAmount = timeCardAccountEntity.CardPerformanceBalanceAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                newTimeCardAccountEntity.CardLargessPerformanceBalanceAmount = timeCardAccountEntity.CardLargessPerformanceBalanceAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                newTimeCardAccountEntity.LargessPerformanceBalanceAmount = timeCardAccountEntity.LargessPerformanceBalanceAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                newTimeCardAccountEntity.Channel = AccountChannel.AccountChannel_20.getID();
                newTimeCardAccountEntity.Remark = timeCardAccountEntity.Remark;
                timeCardAccountMapper.createTranserTimeCardAccount(newTimeCardAccountEntity);

                TimeCardAccountEntity updateTimeCardAccountEntity = new TimeCardAccountEntity();
                updateTimeCardAccountEntity.ID = timeCardAccountEntity.ID;
                updateTimeCardAccountEntity.PayPerformanceBalanceAmount = timeCardAccountEntity.PayPerformanceBalanceAmount.subtract(newTimeCardAccountEntity.PayPerformanceBalanceAmount);
                updateTimeCardAccountEntity.CardPerformanceBalanceAmount = timeCardAccountEntity.CardPerformanceBalanceAmount.subtract(newTimeCardAccountEntity.CardPerformanceBalanceAmount);
                updateTimeCardAccountEntity.CardLargessPerformanceBalanceAmount = timeCardAccountEntity.CardLargessPerformanceBalanceAmount.subtract(newTimeCardAccountEntity.CardLargessPerformanceBalanceAmount);
                updateTimeCardAccountEntity.LargessPerformanceBalanceAmount = timeCardAccountEntity.LargessPerformanceBalanceAmount.subtract(newTimeCardAccountEntity.LargessPerformanceBalanceAmount);
                updateTimeCardAccountEntity.ValidType = timeCardAccountEntity.ValidType == 3 ? 1 : timeCardAccountEntity.ValidType;
                Calendar calendar = Calendar.getInstance();
                calendar.set(Calendar.HOUR_OF_DAY, -24);
                updateTimeCardAccountEntity.ValidDate = calendar.getTime();
                timeCardAccountMapper.updateTimeCardAccountEntity(updateTimeCardAccountEntity);

                TransferBillTimeCardAccountEntity transferBillTimeCardAccountEntity = new TransferBillTimeCardAccountEntity();
                transferBillTimeCardAccountEntity.TransferBillID = transferBillEntity.ID;
                transferBillTimeCardAccountEntity.TransferOutTimeCardAccountID = transerTimeCardAddForm.TimeCardAccountID;
                transferBillTimeCardAccountEntity.TransferInTimeCardAccountID = newTimeCardAccountEntity.ID;
                transferBillTimeCardAccountEntity.TimeCardID = timeCardAccountEntity.TimeCardID;
                transferBillTimeCardAccountEntity.Quantity = 1;
                transerTimeCard.add(transferBillTimeCardAccountEntity);
            }
        }
        if (form.SavingCard != null && form.SavingCard.size() > 0) {
            for (TranserSavingCardAddForm transerSavingCardAddForm : form.SavingCard) {
                TranserSavingCardAccountOutputForm savingCardAccountEntity = savingCardAccountMapper.getSavingCardAccountEntity(transerSavingCardAddForm.SavingCardAccountID);

                SavingCardAccountEntity newSavingCardAccountEntity = new SavingCardAccountEntity();
                newSavingCardAccountEntity.CustomerID = form.TransferInCustomerID;
                newSavingCardAccountEntity.EntityID = SystemUserUtil.getSystemEntityID();
                newSavingCardAccountEntity.BuyDate = DateTimeUtil.toDate(DateTimeUtil.toyyyyMMdd2(new Date()));
                newSavingCardAccountEntity.SavingCardID = savingCardAccountEntity.SavingCardID;
                newSavingCardAccountEntity.Price = transerSavingCardAddForm.Amount;
                newSavingCardAccountEntity.Balance = transerSavingCardAddForm.Amount;
                newSavingCardAccountEntity.ArrearAmount = BigDecimal.ZERO;
                newSavingCardAccountEntity.LargessPrice = transerSavingCardAddForm.LargessAmount;
                newSavingCardAccountEntity.LargessBalance = transerSavingCardAddForm.LargessAmount;
                newSavingCardAccountEntity.ValidType = savingCardAccountEntity.ValidType;
                newSavingCardAccountEntity.ValidDay = savingCardAccountEntity.ValidDay;
                newSavingCardAccountEntity.ValidDate = savingCardAccountEntity.ValidDate;
                newSavingCardAccountEntity.Amount = transerSavingCardAddForm.Amount;
                newSavingCardAccountEntity.LargessAmount = transerSavingCardAddForm.LargessAmount;
                newSavingCardAccountEntity.Channel = AccountChannel.AccountChannel_20.getID();
                newSavingCardAccountEntity.Remark = savingCardAccountEntity.Remark;
                savingCardAccountMapper.createTranserSavingCardAccount(newSavingCardAccountEntity);
                savingCardAccountMapper.updateSavingCardAccountEntityBalance(transerSavingCardAddForm.SavingCardAccountID, savingCardAccountEntity.Balance.subtract(transerSavingCardAddForm.Amount), savingCardAccountEntity.LargessBalance.subtract(transerSavingCardAddForm.LargessAmount));
                TransferBillSavingCardAccountEntity transferBillSavingCardAccountEntity = new TransferBillSavingCardAccountEntity();
                transferBillSavingCardAccountEntity.TransferBillID = transferBillEntity.ID;
                transferBillSavingCardAccountEntity.TransferOutSavingCardAccountID = transerSavingCardAddForm.SavingCardAccountID;
                transferBillSavingCardAccountEntity.TransferInSavingCardAccountID = newSavingCardAccountEntity.ID;
                transferBillSavingCardAccountEntity.SavingCardID = savingCardAccountEntity.SavingCardID;
                transferBillSavingCardAccountEntity.Amount = transerSavingCardAddForm.Amount;
                transferBillSavingCardAccountEntity.LargessAmount = transerSavingCardAddForm.LargessAmount;
                transerSavingCard.add(transferBillSavingCardAccountEntity);
            }
        }

        List<PackageCardPriceUpdateForm> list = new ArrayList<>();
        if (form.PackageCard != null && form.PackageCard.size() > 0) {
            for (TranserPackageCardAddForm transerPackageCardAddForm : form.PackageCard) {
                BigDecimal totalAmount = BigDecimal.ZERO;
                BigDecimal payAmount = BigDecimal.ZERO;
                BigDecimal savingCardDeductionAmount = BigDecimal.ZERO;
                BigDecimal savingCardDeductionLargessAmount = BigDecimal.ZERO;
                PackageCardAccountEntity packageCardAccountEntity = packageCardAccountMapper.getPackageCardAccount(transerPackageCardAddForm.PackageCardAccountID);
                PackageCardAccountEntity newPackageCardAccountEntity = new PackageCardAccountEntity();
                newPackageCardAccountEntity.CustomerID = form.TransferInCustomerID;
                newPackageCardAccountEntity.EntityID = SystemUserUtil.getSystemEntityID();
                newPackageCardAccountEntity.BuyDate = DateTimeUtil.toDate(DateTimeUtil.toyyyyMMdd2(new Date()));
                newPackageCardAccountEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                newPackageCardAccountEntity.Price = BigDecimal.ZERO;
                newPackageCardAccountEntity.TotalAmount = BigDecimal.ZERO;
                newPackageCardAccountEntity.IsLargess = packageCardAccountEntity.IsLargess;
                newPackageCardAccountEntity.ValidDay = packageCardAccountEntity.ValidDay;
                newPackageCardAccountEntity.ValidType = packageCardAccountEntity.ValidType;
                newPackageCardAccountEntity.ValidDate = packageCardAccountEntity.ValidDate;
                newPackageCardAccountEntity.PayAmount = BigDecimal.ZERO;
                newPackageCardAccountEntity.ArrearAmount = BigDecimal.ZERO;
                newPackageCardAccountEntity.SavingCardDeductionAmount = BigDecimal.ZERO;
                newPackageCardAccountEntity.SavingCardDeductionLargessAmount = BigDecimal.ZERO;
                newPackageCardAccountEntity.Channel = AccountChannel.AccountChannel_20.getID();
                packageCardAccountMapper.createPackageCardAccount(newPackageCardAccountEntity);
                if (transerPackageCardAddForm.Product != null && transerPackageCardAddForm.Product.size() > 0) {
                    for (TranserProductAddForm transerProductAddForm : transerPackageCardAddForm.Product) {
                        TranserProductAccountOutputForm productAccountEntity = productAccountMapper.getProductAccountEntity(transerProductAddForm.ProductAccountID);
                        if (productAccountEntity.ValidBalance.intValue() < transerProductAddForm.Quantity.intValue())
                            throw new RuntimeException("产品没有足够的账户进行转账，请修改数量");

                        ProductAccountEntity newProductAccountEntity = new ProductAccountEntity();
                        newProductAccountEntity.CustomerID = form.TransferInCustomerID;
                        newProductAccountEntity.EntityID = SystemUserUtil.getSystemEntityID();
                        newProductAccountEntity.BuyDate = DateTimeUtil.toDate(DateTimeUtil.toyyyyMMdd2(new Date()));
                        newProductAccountEntity.ProductID = productAccountEntity.ProductID;
                        newProductAccountEntity.Price = productAccountEntity.IsLargess == true ? productAccountEntity.Price : productAccountEntity.TotalAmount.divide(new BigDecimal(productAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                        newProductAccountEntity.Quantity = transerProductAddForm.Quantity;
                        newProductAccountEntity.TotalAmount = newProductAccountEntity.Price.multiply(new BigDecimal(transerProductAddForm.Quantity)).setScale(2, BigDecimal.ROUND_HALF_UP);
                        newProductAccountEntity.Balance = transerProductAddForm.Quantity;
                        newProductAccountEntity.IsLargess = productAccountEntity.IsLargess;
                        newProductAccountEntity.ArrearAmount = BigDecimal.ZERO;
                        newProductAccountEntity.PackageCardAccountID = newPackageCardAccountEntity.ID;
                        if (productAccountEntity.ValidBalance.equals(transerProductAddForm.Quantity) && productAccountEntity.ValidBalance.intValue() == productAccountEntity.Balance.intValue()) {
                            newProductAccountEntity.SavingCardDeductionAmount = productAccountEntity.SavingCardDeductionAmount;
                            newProductAccountEntity.SavingCardDeductionLargessAmount = productAccountEntity.SavingCardDeductionLargessAmount;
                            newProductAccountEntity.PayAmount = productAccountEntity.PayAmount;
                            newProductAccountEntity.CardPerformanceBalanceAmount = productAccountEntity.CardPerformanceBalanceAmount;
                            newProductAccountEntity.CardLargessPerformanceBalanceAmount = productAccountEntity.CardLargessPerformanceBalanceAmount;
                            newProductAccountEntity.PayPerformanceBalanceAmount = productAccountEntity.PayPerformanceBalanceAmount;
                            newProductAccountEntity.LargessPerformanceBalanceAmount = productAccountEntity.LargessPerformanceBalanceAmount;
                        } else {
                            newProductAccountEntity.SavingCardDeductionAmount = productAccountEntity.IsLargess == true ? BigDecimal.ZERO : productAccountEntity.SavingCardDeductionAmount.multiply(new BigDecimal(transerProductAddForm.Quantity)).divide(new BigDecimal(productAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            newProductAccountEntity.SavingCardDeductionLargessAmount = productAccountEntity.IsLargess == true ? BigDecimal.ZERO : productAccountEntity.SavingCardDeductionLargessAmount.multiply(new BigDecimal(transerProductAddForm.Quantity)).divide(new BigDecimal(productAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            newProductAccountEntity.PayAmount = productAccountEntity.IsLargess == true ? BigDecimal.ZERO : newProductAccountEntity.TotalAmount.subtract(newProductAccountEntity.SavingCardDeductionAmount).subtract(newProductAccountEntity.SavingCardDeductionLargessAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
                            newProductAccountEntity.CardPerformanceBalanceAmount = productAccountEntity.IsLargess == true ? BigDecimal.ZERO : productAccountEntity.SavingCardDeductionAmount.multiply(new BigDecimal(transerProductAddForm.Quantity)).divide(new BigDecimal(productAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            newProductAccountEntity.CardLargessPerformanceBalanceAmount = productAccountEntity.IsLargess == true ? BigDecimal.ZERO : productAccountEntity.SavingCardDeductionLargessAmount.multiply(new BigDecimal(transerProductAddForm.Quantity)).divide(new BigDecimal(productAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            newProductAccountEntity.PayPerformanceBalanceAmount = productAccountEntity.IsLargess == true ? BigDecimal.ZERO : newProductAccountEntity.TotalAmount.subtract(newProductAccountEntity.SavingCardDeductionAmount).subtract(newProductAccountEntity.SavingCardDeductionLargessAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
                            newProductAccountEntity.LargessPerformanceBalanceAmount = productAccountEntity.IsLargess == true ? productAccountEntity.TotalAmount.multiply(new BigDecimal(transerProductAddForm.Quantity)).divide(new BigDecimal(productAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO;
                        }
                        newProductAccountEntity.Channel = AccountChannel.AccountChannel_20.getID();
                        newProductAccountEntity.Remark = productAccountEntity.Remark;
                        productAccountMapper.createTranserProductAccount(newProductAccountEntity);

                        ProductAccountEntity updateProductAccountEntity = new ProductAccountEntity();
                        updateProductAccountEntity.ID = productAccountEntity.ID;
                        updateProductAccountEntity.Balance = productAccountEntity.Balance.intValue() - transerProductAddForm.Quantity;
                        updateProductAccountEntity.PayPerformanceBalanceAmount = productAccountEntity.PayPerformanceBalanceAmount.subtract(newProductAccountEntity.PayPerformanceBalanceAmount);
                        updateProductAccountEntity.CardPerformanceBalanceAmount = productAccountEntity.CardPerformanceBalanceAmount.subtract(newProductAccountEntity.CardPerformanceBalanceAmount);
                        updateProductAccountEntity.CardLargessPerformanceBalanceAmount = productAccountEntity.CardLargessPerformanceBalanceAmount.subtract(newProductAccountEntity.CardLargessPerformanceBalanceAmount);
                        updateProductAccountEntity.LargessPerformanceBalanceAmount = productAccountEntity.LargessPerformanceBalanceAmount.subtract(newProductAccountEntity.LargessPerformanceBalanceAmount);
                        productAccountMapper.updateProductAccountBalance(updateProductAccountEntity);

                        TransferBillProductAccountEntity transferBillProductAccountEntity = new TransferBillProductAccountEntity();
                        transferBillProductAccountEntity.TransferBillID = transferBillEntity.ID;
                        transferBillProductAccountEntity.TransferOutProductAccountID = transerProductAddForm.ProductAccountID;
                        transferBillProductAccountEntity.TransferOutPackageCardAccountID = transerPackageCardAddForm.PackageCardAccountID;
                        transferBillProductAccountEntity.TransferInProductAccountID = newProductAccountEntity.ID;
                        transferBillProductAccountEntity.TransferInPackageCardAccountID = newPackageCardAccountEntity.ID;
                        transferBillProductAccountEntity.ProductID = productAccountEntity.ProductID;
                        transferBillProductAccountEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                        transferBillProductAccountEntity.Quantity = transerProductAddForm.Quantity;
                        transerProduct.add(transferBillProductAccountEntity);
                        if (productAccountEntity.IsLargess == false) {
                            totalAmount = totalAmount.add(newProductAccountEntity.TotalAmount);
                            payAmount = payAmount.add(newProductAccountEntity.PayAmount);
                            savingCardDeductionAmount = savingCardDeductionAmount.add(newProductAccountEntity.SavingCardDeductionAmount);
                            savingCardDeductionLargessAmount = savingCardDeductionLargessAmount.add(newProductAccountEntity.SavingCardDeductionLargessAmount);
                        }
                    }
                }
                if (transerPackageCardAddForm.Project != null && transerPackageCardAddForm.Project.size() > 0) {
                    for (TranserProjectAddForm transerProjectAddForm : transerPackageCardAddForm.Project) {
                        TranserProjectAccountOutputForm projectAccountEntity = projectAccountMapper.getProjectAccountEntity(transerProjectAddForm.ProjectAccountID);
                        if (projectAccountEntity.ValidBalance.intValue() < transerProjectAddForm.Quantity.intValue())
                            throw new RuntimeException("项目没有足够的账户进行转账，请修改数量");


                        ProjectAccountEntity newProjectAccountEntity = new ProjectAccountEntity();
                        newProjectAccountEntity.CustomerID = form.TransferInCustomerID;
                        newProjectAccountEntity.EntityID = SystemUserUtil.getSystemEntityID();
                        newProjectAccountEntity.BuyDate = DateTimeUtil.toDate(DateTimeUtil.toyyyyMMdd2(new Date()));
                        newProjectAccountEntity.ProjectID = projectAccountEntity.ProjectID;
                        newProjectAccountEntity.Price = projectAccountEntity.IsLargess == true ? projectAccountEntity.Price : projectAccountEntity.TotalAmount.divide(new BigDecimal(projectAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                        newProjectAccountEntity.Quantity = transerProjectAddForm.Quantity;
                        newProjectAccountEntity.TotalAmount = newProjectAccountEntity.Price.multiply(new BigDecimal(transerProjectAddForm.Quantity)).setScale(2, BigDecimal.ROUND_HALF_UP);
                        newProjectAccountEntity.Balance = transerProjectAddForm.Quantity;
                        newProjectAccountEntity.IsLargess = projectAccountEntity.IsLargess;
                        newProjectAccountEntity.ArrearAmount = BigDecimal.ZERO;
                        if (projectAccountEntity.ValidBalance.equals(transerProjectAddForm.Quantity) && projectAccountEntity.ValidBalance.intValue() == projectAccountEntity.Balance.intValue()) {
                            newProjectAccountEntity.SavingCardDeductionAmount = projectAccountEntity.CardPerformanceBalanceAmount;
                            newProjectAccountEntity.SavingCardDeductionLargessAmount = projectAccountEntity.CardLargessPerformanceBalanceAmount;
                            newProjectAccountEntity.PayAmount = projectAccountEntity.PayPerformanceBalanceAmount;
                            newProjectAccountEntity.CardPerformanceBalanceAmount = projectAccountEntity.CardPerformanceBalanceAmount;
                            newProjectAccountEntity.CardLargessPerformanceBalanceAmount = projectAccountEntity.CardLargessPerformanceBalanceAmount;
                            newProjectAccountEntity.PayPerformanceBalanceAmount = projectAccountEntity.PayPerformanceBalanceAmount;
                            newProjectAccountEntity.LargessPerformanceBalanceAmount = projectAccountEntity.LargessPerformanceBalanceAmount;
                        } else {
                            newProjectAccountEntity.SavingCardDeductionAmount = projectAccountEntity.IsLargess == true ? BigDecimal.ZERO : projectAccountEntity.SavingCardDeductionAmount.multiply(new BigDecimal(transerProjectAddForm.Quantity)).divide(new BigDecimal(projectAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            newProjectAccountEntity.SavingCardDeductionLargessAmount = projectAccountEntity.IsLargess == true ? BigDecimal.ZERO : projectAccountEntity.SavingCardDeductionLargessAmount.multiply(new BigDecimal(transerProjectAddForm.Quantity)).divide(new BigDecimal(projectAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            newProjectAccountEntity.PayAmount = projectAccountEntity.IsLargess == true ? BigDecimal.ZERO : newProjectAccountEntity.TotalAmount.subtract(newProjectAccountEntity.SavingCardDeductionAmount).subtract(newProjectAccountEntity.SavingCardDeductionLargessAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
                            newProjectAccountEntity.CardPerformanceBalanceAmount = projectAccountEntity.IsLargess == true ? BigDecimal.ZERO : projectAccountEntity.SavingCardDeductionAmount.multiply(new BigDecimal(transerProjectAddForm.Quantity)).divide(new BigDecimal(projectAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            newProjectAccountEntity.CardLargessPerformanceBalanceAmount = projectAccountEntity.IsLargess == true ? BigDecimal.ZERO : projectAccountEntity.SavingCardDeductionLargessAmount.multiply(new BigDecimal(transerProjectAddForm.Quantity)).divide(new BigDecimal(projectAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            newProjectAccountEntity.PayPerformanceBalanceAmount = projectAccountEntity.IsLargess == true ? BigDecimal.ZERO : newProjectAccountEntity.TotalAmount.subtract(newProjectAccountEntity.SavingCardDeductionAmount).subtract(newProjectAccountEntity.SavingCardDeductionLargessAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
                            newProjectAccountEntity.LargessPerformanceBalanceAmount = projectAccountEntity.IsLargess == true ? projectAccountEntity.TotalAmount.multiply(new BigDecimal(transerProjectAddForm.Quantity)).divide(new BigDecimal(projectAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO;
                        }
                        newProjectAccountEntity.PackageCardAccountID = newPackageCardAccountEntity.ID;
                        newProjectAccountEntity.Channel = AccountChannel.AccountChannel_20.getID();
                        newProjectAccountEntity.Remark = projectAccountEntity.Remark;
                        projectAccountMapper.createTranserProjectAccount(newProjectAccountEntity);

                        ProjectAccountEntity updateProjectAccountEntity = new ProjectAccountEntity();
                        updateProjectAccountEntity.ID = projectAccountEntity.ID;
                        updateProjectAccountEntity.Balance = projectAccountEntity.Balance.intValue() - transerProjectAddForm.Quantity;
                        updateProjectAccountEntity.PayPerformanceBalanceAmount = projectAccountEntity.PayPerformanceBalanceAmount.subtract(newProjectAccountEntity.PayPerformanceBalanceAmount);
                        updateProjectAccountEntity.CardPerformanceBalanceAmount = projectAccountEntity.CardPerformanceBalanceAmount.subtract(newProjectAccountEntity.CardPerformanceBalanceAmount);
                        updateProjectAccountEntity.CardLargessPerformanceBalanceAmount = projectAccountEntity.CardLargessPerformanceBalanceAmount.subtract(newProjectAccountEntity.CardLargessPerformanceBalanceAmount);
                        updateProjectAccountEntity.LargessPerformanceBalanceAmount = projectAccountEntity.LargessPerformanceBalanceAmount.subtract(newProjectAccountEntity.LargessPerformanceBalanceAmount);
                        projectAccountMapper.updateProjectAccountBalance(updateProjectAccountEntity);

                        TransferBillProjectAccountEntity transferBillProjectAccountEntity = new TransferBillProjectAccountEntity();
                        transferBillProjectAccountEntity.TransferBillID = transferBillEntity.ID;
                        transferBillProjectAccountEntity.TransferOutProjectAccountID = transerProjectAddForm.ProjectAccountID;
                        transferBillProjectAccountEntity.TransferOutPackageCardAccountID = transerPackageCardAddForm.PackageCardAccountID;
                        transferBillProjectAccountEntity.TransferInProjectAccountID = newProjectAccountEntity.ID;
                        transferBillProjectAccountEntity.TransferInPackageCardAccountID = newPackageCardAccountEntity.ID;
                        transferBillProjectAccountEntity.ProjectID = projectAccountEntity.ProjectID;
                        transferBillProjectAccountEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                        transferBillProjectAccountEntity.Quantity = transerProjectAddForm.Quantity;
                        transerProject.add(transferBillProjectAccountEntity);
                        if (projectAccountEntity.IsLargess == false) {
                            totalAmount = totalAmount.add(newProjectAccountEntity.TotalAmount);
                            payAmount = payAmount.add(newProjectAccountEntity.PayAmount);
                            savingCardDeductionAmount = savingCardDeductionAmount.add(newProjectAccountEntity.SavingCardDeductionAmount);
                            savingCardDeductionLargessAmount = savingCardDeductionLargessAmount.add(newProjectAccountEntity.SavingCardDeductionLargessAmount);
                        }
                    }
                }

                if (transerPackageCardAddForm.GeneralCard != null && transerPackageCardAddForm.GeneralCard.size() > 0) {
                    for (TranserGeneralCardAddForm transerGeneralCardAddForm : transerPackageCardAddForm.GeneralCard) {
                        TranserGeneralCardAccountOutputForm generalCardAccountEntity = generalCardAccountMapper.getGeneralCardAccountEntity(transerGeneralCardAddForm.GeneralCardAccountID);
                        if (generalCardAccountEntity.ValidBalance.intValue() < transerGeneralCardAddForm.Quantity.intValue())
                            throw new RuntimeException("通用次卡没有足够的账户进行转账，请修改数量");

                        GeneralCardAccountEntity newGeneralCardAccountEntity = new GeneralCardAccountEntity();
                        newGeneralCardAccountEntity.CustomerID = form.TransferInCustomerID;
                        newGeneralCardAccountEntity.EntityID = SystemUserUtil.getSystemEntityID();
                        newGeneralCardAccountEntity.BuyDate = DateTimeUtil.toDate(DateTimeUtil.toyyyyMMdd2(new Date()));
                        newGeneralCardAccountEntity.GeneralCardID = generalCardAccountEntity.GeneralCardID;
                        newGeneralCardAccountEntity.Price = generalCardAccountEntity.IsLargess == true ? generalCardAccountEntity.Price.multiply(new BigDecimal(transerGeneralCardAddForm.Quantity)).divide(new BigDecimal(generalCardAccountEntity.CardTimes), 2, BigDecimal.ROUND_HALF_UP).setScale(2, BigDecimal.ROUND_HALF_UP) : generalCardAccountEntity.TotalAmount.multiply(new BigDecimal(transerGeneralCardAddForm.Quantity)).divide(new BigDecimal(generalCardAccountEntity.CardTimes), 2, BigDecimal.ROUND_HALF_UP);
                        newGeneralCardAccountEntity.CardTimes = transerGeneralCardAddForm.Quantity;
                        newGeneralCardAccountEntity.Balance = transerGeneralCardAddForm.Quantity;
                        newGeneralCardAccountEntity.IsLargess = generalCardAccountEntity.IsLargess;
                        newGeneralCardAccountEntity.ValidType = generalCardAccountEntity.ValidType;
                        newGeneralCardAccountEntity.ValidDay = generalCardAccountEntity.ValidDay;
                        newGeneralCardAccountEntity.ValidDate = generalCardAccountEntity.ValidDate;
                        newGeneralCardAccountEntity.ArrearAmount = BigDecimal.ZERO;
                        newGeneralCardAccountEntity.TotalAmount = generalCardAccountEntity.TotalAmount.multiply(new BigDecimal(transerGeneralCardAddForm.Quantity)).divide(new BigDecimal(generalCardAccountEntity.CardTimes), 2, BigDecimal.ROUND_HALF_UP);
                        if (generalCardAccountEntity.ValidBalance.equals(transerGeneralCardAddForm.Quantity) && generalCardAccountEntity.ValidBalance.intValue() == generalCardAccountEntity.Balance.intValue()) {
                            newGeneralCardAccountEntity.SavingCardDeductionAmount = generalCardAccountEntity.CardPerformanceBalanceAmount;
                            newGeneralCardAccountEntity.SavingCardDeductionLargessAmount = generalCardAccountEntity.CardLargessPerformanceBalanceAmount;
                            newGeneralCardAccountEntity.PayAmount = generalCardAccountEntity.PayPerformanceBalanceAmount;
                            newGeneralCardAccountEntity.CardPerformanceBalanceAmount = generalCardAccountEntity.CardPerformanceBalanceAmount;
                            newGeneralCardAccountEntity.CardLargessPerformanceBalanceAmount = generalCardAccountEntity.CardLargessPerformanceBalanceAmount;
                            newGeneralCardAccountEntity.PayPerformanceBalanceAmount = generalCardAccountEntity.PayPerformanceBalanceAmount;
                            newGeneralCardAccountEntity.LargessPerformanceBalanceAmount = generalCardAccountEntity.LargessPerformanceBalanceAmount;
                        } else {
                            newGeneralCardAccountEntity.SavingCardDeductionAmount = generalCardAccountEntity.IsLargess == true ? BigDecimal.ZERO : generalCardAccountEntity.SavingCardDeductionAmount.multiply(new BigDecimal(transerGeneralCardAddForm.Quantity)).divide(new BigDecimal(generalCardAccountEntity.CardTimes), 2, BigDecimal.ROUND_HALF_UP);
                            newGeneralCardAccountEntity.SavingCardDeductionLargessAmount = generalCardAccountEntity.IsLargess == true ? BigDecimal.ZERO : generalCardAccountEntity.SavingCardDeductionLargessAmount.multiply(new BigDecimal(transerGeneralCardAddForm.Quantity)).divide(new BigDecimal(generalCardAccountEntity.CardTimes), 2, BigDecimal.ROUND_HALF_UP);
                            newGeneralCardAccountEntity.PayAmount = generalCardAccountEntity.IsLargess == true ? BigDecimal.ZERO : newGeneralCardAccountEntity.TotalAmount.subtract(newGeneralCardAccountEntity.SavingCardDeductionAmount).subtract(newGeneralCardAccountEntity.SavingCardDeductionLargessAmount);
                            newGeneralCardAccountEntity.CardPerformanceBalanceAmount = generalCardAccountEntity.IsLargess == true ? BigDecimal.ZERO : generalCardAccountEntity.SavingCardDeductionAmount.multiply(new BigDecimal(transerGeneralCardAddForm.Quantity)).divide(new BigDecimal(generalCardAccountEntity.CardTimes), 2, BigDecimal.ROUND_HALF_UP);
                            newGeneralCardAccountEntity.CardLargessPerformanceBalanceAmount = generalCardAccountEntity.IsLargess == true ? BigDecimal.ZERO : generalCardAccountEntity.SavingCardDeductionLargessAmount.multiply(new BigDecimal(transerGeneralCardAddForm.Quantity)).divide(new BigDecimal(generalCardAccountEntity.CardTimes), 2, BigDecimal.ROUND_HALF_UP);
                            newGeneralCardAccountEntity.PayPerformanceBalanceAmount = generalCardAccountEntity.IsLargess == true ? BigDecimal.ZERO : newGeneralCardAccountEntity.TotalAmount.subtract(newGeneralCardAccountEntity.SavingCardDeductionAmount).subtract(newGeneralCardAccountEntity.SavingCardDeductionLargessAmount);
                            newGeneralCardAccountEntity.LargessPerformanceBalanceAmount = generalCardAccountEntity.IsLargess == true ? generalCardAccountEntity.TotalAmount.multiply(new BigDecimal(transerGeneralCardAddForm.Quantity)).divide(new BigDecimal(generalCardAccountEntity.CardTimes), 2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO;
                        }
                        newGeneralCardAccountEntity.PackageCardAccountID = newPackageCardAccountEntity.ID;
                        newGeneralCardAccountEntity.Channel = AccountChannel.AccountChannel_20.getID();
                        newGeneralCardAccountEntity.Remark = generalCardAccountEntity.Remark;
                        generalCardAccountMapper.createTranserGeneralCardAccount(newGeneralCardAccountEntity);

                        GeneralCardAccountEntity updateGeneralCardAccountEntity = new GeneralCardAccountEntity();
                        updateGeneralCardAccountEntity.ID = generalCardAccountEntity.ID;
                        updateGeneralCardAccountEntity.Balance = generalCardAccountEntity.Balance - transerGeneralCardAddForm.Quantity;
                        updateGeneralCardAccountEntity.PayPerformanceBalanceAmount = generalCardAccountEntity.PayPerformanceBalanceAmount.subtract(newGeneralCardAccountEntity.PayPerformanceBalanceAmount);
                        updateGeneralCardAccountEntity.CardPerformanceBalanceAmount = generalCardAccountEntity.CardPerformanceBalanceAmount.subtract(newGeneralCardAccountEntity.CardPerformanceBalanceAmount);
                        updateGeneralCardAccountEntity.ValidDate = generalCardAccountEntity.ValidDate;
                        updateGeneralCardAccountEntity.CardLargessPerformanceBalanceAmount = generalCardAccountEntity.CardLargessPerformanceBalanceAmount.subtract(newGeneralCardAccountEntity.CardLargessPerformanceBalanceAmount);
                        updateGeneralCardAccountEntity.LargessPerformanceBalanceAmount = generalCardAccountEntity.LargessPerformanceBalanceAmount.subtract(newGeneralCardAccountEntity.LargessPerformanceBalanceAmount);
                        generalCardAccountMapper.updateGeneralCardAccountBalance(updateGeneralCardAccountEntity);

                        TransferBillGeneralCardAccountEntity transferBillGeneralCardAccountEntity = new TransferBillGeneralCardAccountEntity();
                        transferBillGeneralCardAccountEntity.TransferBillID = transferBillEntity.ID;
                        transferBillGeneralCardAccountEntity.TransferOutGeneralCardAccountID = transerGeneralCardAddForm.GeneralCardAccountID;
                        transferBillGeneralCardAccountEntity.TransferOutPackageCardAccountID = transerPackageCardAddForm.PackageCardAccountID;
                        transferBillGeneralCardAccountEntity.TransferInGeneralCardAccountID = newGeneralCardAccountEntity.ID;
                        transferBillGeneralCardAccountEntity.TransferInPackageCardAccountID = newPackageCardAccountEntity.ID;
                        transferBillGeneralCardAccountEntity.GeneralCardID = generalCardAccountEntity.GeneralCardID;
                        transferBillGeneralCardAccountEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                        transferBillGeneralCardAccountEntity.Quantity = transerGeneralCardAddForm.Quantity;
                        transerGeneralCard.add(transferBillGeneralCardAccountEntity);
                        if (generalCardAccountEntity.IsLargess == false) {
                            totalAmount = totalAmount.add(newGeneralCardAccountEntity.TotalAmount);
                            payAmount = payAmount.add(newGeneralCardAccountEntity.PayAmount);
                            savingCardDeductionAmount = savingCardDeductionAmount.add(newGeneralCardAccountEntity.SavingCardDeductionAmount);
                            savingCardDeductionLargessAmount = savingCardDeductionLargessAmount.add(newGeneralCardAccountEntity.SavingCardDeductionLargessAmount);
                        }
                    }
                }

                if (transerPackageCardAddForm.TimeCard != null && transerPackageCardAddForm.TimeCard.size() > 0) {
                    for (TranserTimeCardAddForm transerTimeCardAddForm : transerPackageCardAddForm.TimeCard) {
                        TimeCardAccountEntity timeCardAccountEntity = timeCardAccountMapper.getTimeCardAccountByAccountID(transerTimeCardAddForm.TimeCardAccountID);
                        TimeCardAccountEntity newTimeCardAccountEntity = new TimeCardAccountEntity();
                        newTimeCardAccountEntity.CustomerID = form.TransferInCustomerID;
                        newTimeCardAccountEntity.EntityID = SystemUserUtil.getSystemEntityID();
                        newTimeCardAccountEntity.BuyDate = DateTimeUtil.toDate(DateTimeUtil.toyyyyMMdd2(new Date()));
                        newTimeCardAccountEntity.TimeCardID = timeCardAccountEntity.TimeCardID;
                        newTimeCardAccountEntity.Price = timeCardAccountEntity.Price;
                        newTimeCardAccountEntity.TotalAmount = timeCardAccountEntity.TotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                        newTimeCardAccountEntity.ArrearAmount = timeCardAccountEntity.ArrearAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                        newTimeCardAccountEntity.IsLargess = timeCardAccountEntity.IsLargess;
                        newTimeCardAccountEntity.PayAmount = timeCardAccountEntity.PayAmount;
                        newTimeCardAccountEntity.PerformanceBalance = timeCardAccountEntity.PerformanceBalance;
                        newTimeCardAccountEntity.PerformanceTimes = timeCardAccountEntity.PerformanceTimes;
                        newTimeCardAccountEntity.ValidType = timeCardAccountEntity.ValidType;
                        newTimeCardAccountEntity.ValidDay = timeCardAccountEntity.ValidDay;
                        newTimeCardAccountEntity.ValidDate = timeCardAccountEntity.ValidDate;
                        newTimeCardAccountEntity.PackageCardAccountID = newPackageCardAccountEntity.ID;
                        newTimeCardAccountEntity.SavingCardDeductionAmount = timeCardAccountEntity.SavingCardDeductionAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                        newTimeCardAccountEntity.SavingCardDeductionLargessAmount = timeCardAccountEntity.SavingCardDeductionLargessAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                        newTimeCardAccountEntity.PayPerformanceBalanceAmount = timeCardAccountEntity.PayPerformanceBalanceAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                        newTimeCardAccountEntity.CardPerformanceBalanceAmount = timeCardAccountEntity.CardPerformanceBalanceAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                        newTimeCardAccountEntity.CardLargessPerformanceBalanceAmount = timeCardAccountEntity.CardLargessPerformanceBalanceAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                        newTimeCardAccountEntity.LargessPerformanceBalanceAmount = timeCardAccountEntity.LargessPerformanceBalanceAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                        newTimeCardAccountEntity.Channel = AccountChannel.AccountChannel_20.getID();
                        newTimeCardAccountEntity.Remark = timeCardAccountEntity.Remark;
                        timeCardAccountMapper.createTranserTimeCardAccount(newTimeCardAccountEntity);

                        TimeCardAccountEntity updateTimeCardAccountEntity = new TimeCardAccountEntity();
                        updateTimeCardAccountEntity.ID = timeCardAccountEntity.ID;
                        updateTimeCardAccountEntity.PayPerformanceBalanceAmount = timeCardAccountEntity.PayPerformanceBalanceAmount.subtract(newTimeCardAccountEntity.PayPerformanceBalanceAmount);
                        updateTimeCardAccountEntity.CardPerformanceBalanceAmount = timeCardAccountEntity.CardPerformanceBalanceAmount.subtract(newTimeCardAccountEntity.CardPerformanceBalanceAmount);
                        updateTimeCardAccountEntity.CardLargessPerformanceBalanceAmount = timeCardAccountEntity.CardLargessPerformanceBalanceAmount.subtract(newTimeCardAccountEntity.CardLargessPerformanceBalanceAmount);
                        updateTimeCardAccountEntity.LargessPerformanceBalanceAmount = timeCardAccountEntity.LargessPerformanceBalanceAmount.subtract(newTimeCardAccountEntity.LargessPerformanceBalanceAmount);
                        updateTimeCardAccountEntity.ValidType = timeCardAccountEntity.ValidType == 3 ? 1 : timeCardAccountEntity.ValidType;
                        Calendar calendar = Calendar.getInstance();
                        calendar.set(Calendar.HOUR_OF_DAY, -24);
                        updateTimeCardAccountEntity.ValidDate = calendar.getTime();
                        timeCardAccountMapper.updateTimeCardAccountEntity(updateTimeCardAccountEntity);

                        TransferBillTimeCardAccountEntity transferBillTimeCardAccountEntity = new TransferBillTimeCardAccountEntity();
                        transferBillTimeCardAccountEntity.TransferBillID = transferBillEntity.ID;
                        transferBillTimeCardAccountEntity.TransferOutTimeCardAccountID = transerTimeCardAddForm.TimeCardAccountID;
                        transferBillTimeCardAccountEntity.TransferOutPackageCardAccountID = transerPackageCardAddForm.PackageCardAccountID;
                        transferBillTimeCardAccountEntity.TransferInTimeCardAccountID = newTimeCardAccountEntity.ID;
                        transferBillTimeCardAccountEntity.TransferInPackageCardAccountID = newPackageCardAccountEntity.ID;
                        transferBillTimeCardAccountEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                        transferBillTimeCardAccountEntity.TimeCardID = timeCardAccountEntity.TimeCardID;
                        transferBillTimeCardAccountEntity.Quantity = 1;
                        transerTimeCard.add(transferBillTimeCardAccountEntity);
                        if (timeCardAccountEntity.IsLargess == false) {
                            totalAmount = totalAmount.add(newTimeCardAccountEntity.TotalAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
                            payAmount = payAmount.add(newTimeCardAccountEntity.PayAmount);
                            savingCardDeductionAmount = savingCardDeductionAmount.add(newTimeCardAccountEntity.SavingCardDeductionAmount);
                            savingCardDeductionLargessAmount = savingCardDeductionLargessAmount.add(newTimeCardAccountEntity.SavingCardDeductionLargessAmount);
                        }
                    }
                }

                if (transerPackageCardAddForm.SavingCard != null && transerPackageCardAddForm.SavingCard.size() > 0) {
                    for (TranserSavingCardAddForm transerSavingCardAddForm : transerPackageCardAddForm.SavingCard) {
                        TranserSavingCardAccountOutputForm savingCardAccountEntity = savingCardAccountMapper.getSavingCardAccountEntity(transerSavingCardAddForm.SavingCardAccountID);

                        SavingCardAccountEntity newSavingCardAccountEntity = new SavingCardAccountEntity();
                        newSavingCardAccountEntity.CustomerID = form.TransferInCustomerID;
                        newSavingCardAccountEntity.EntityID = SystemUserUtil.getSystemEntityID();
                        newSavingCardAccountEntity.BuyDate = DateTimeUtil.toDate(DateTimeUtil.toyyyyMMdd2(new Date()));
                        newSavingCardAccountEntity.SavingCardID = savingCardAccountEntity.SavingCardID;
                        newSavingCardAccountEntity.Price = transerSavingCardAddForm.Amount;
                        newSavingCardAccountEntity.Balance = transerSavingCardAddForm.Amount;
                        newSavingCardAccountEntity.ArrearAmount = BigDecimal.ZERO;
                        newSavingCardAccountEntity.LargessPrice = transerSavingCardAddForm.LargessAmount;
                        newSavingCardAccountEntity.LargessBalance = transerSavingCardAddForm.LargessAmount;
                        newSavingCardAccountEntity.ValidType = savingCardAccountEntity.ValidType;
                        newSavingCardAccountEntity.ValidDay = savingCardAccountEntity.ValidDay;
                        newSavingCardAccountEntity.ValidDate = savingCardAccountEntity.ValidDate;
                        newSavingCardAccountEntity.Amount = transerSavingCardAddForm.Amount;
                        newSavingCardAccountEntity.LargessAmount = transerSavingCardAddForm.LargessAmount;
                        newSavingCardAccountEntity.Channel = AccountChannel.AccountChannel_20.getID();
                        newSavingCardAccountEntity.Remark = savingCardAccountEntity.Remark;
                        newSavingCardAccountEntity.PackageCardAccountID = newPackageCardAccountEntity.ID;
                        savingCardAccountMapper.createTranserSavingCardAccount(newSavingCardAccountEntity);
                        savingCardAccountMapper.updateSavingCardAccountEntityBalance(transerSavingCardAddForm.SavingCardAccountID, savingCardAccountEntity.Balance.subtract(transerSavingCardAddForm.Amount), savingCardAccountEntity.LargessBalance.subtract(transerSavingCardAddForm.LargessAmount));
                        TransferBillSavingCardAccountEntity transferBillSavingCardAccountEntity = new TransferBillSavingCardAccountEntity();
                        transferBillSavingCardAccountEntity.TransferBillID = transferBillEntity.ID;
                        transferBillSavingCardAccountEntity.TransferOutSavingCardAccountID = transerSavingCardAddForm.SavingCardAccountID;
                        transferBillSavingCardAccountEntity.TransferOutPackageCardAccountID = transerPackageCardAddForm.PackageCardAccountID;
                        transferBillSavingCardAccountEntity.TransferInSavingCardAccountID = newSavingCardAccountEntity.ID;
                        transferBillSavingCardAccountEntity.TransferInPackageCardAccountID = newPackageCardAccountEntity.ID;
                        transferBillSavingCardAccountEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                        transferBillSavingCardAccountEntity.SavingCardID = savingCardAccountEntity.SavingCardID;
                        transferBillSavingCardAccountEntity.Amount = transerSavingCardAddForm.Amount;
                        transferBillSavingCardAccountEntity.LargessAmount = transerSavingCardAddForm.LargessAmount;
                        transerSavingCard.add(transferBillSavingCardAccountEntity);
                        totalAmount = totalAmount.add(newSavingCardAccountEntity.Amount);
                        payAmount = payAmount.add(newSavingCardAccountEntity.Amount);
                    }
                }
                PackageCardPriceUpdateForm packageCardPriceUpdateForm = new PackageCardPriceUpdateForm();
                packageCardPriceUpdateForm.PackageCardAccountID = newPackageCardAccountEntity.ID;
                packageCardPriceUpdateForm.totalPrice = totalAmount;
                packageCardPriceUpdateForm.payAmount = payAmount;
                packageCardPriceUpdateForm.savingCardDeductionAmount = savingCardDeductionAmount;
                packageCardPriceUpdateForm.savingCardDeductionLargessAmount = savingCardDeductionLargessAmount;
                list.add(packageCardPriceUpdateForm);
            }
        }

        if (list != null && list.size() > 0) {
            for (PackageCardPriceUpdateForm packageCardPriceUpdateForm : list) {
                packageCardAccountMapper.updatePackageCardAccountPrice(packageCardPriceUpdateForm.PackageCardAccountID, packageCardPriceUpdateForm.totalPrice, packageCardPriceUpdateForm.payAmount, packageCardPriceUpdateForm.savingCardDeductionAmount, packageCardPriceUpdateForm.savingCardDeductionLargessAmount);
            }
        }

        if (transerProduct != null && transerProduct.size() > 0)
            transerMapper.insertTranserProduct(transerProduct);
        if (transerProject != null && transerProject.size() > 0)
            transerMapper.insertTranserProject(transerProject);
        if (transerGeneralCard != null && transerGeneralCard.size() > 0)
            transerMapper.insertTranserGeneralCard(transerGeneralCard);
        if (transerTimeCard != null && transerTimeCard.size() > 0)
            transerMapper.insertTranserTimeCard(transerTimeCard);
        if (transerSavingCard != null && transerSavingCard.size() > 0)
            transerMapper.insertTranserSavingCard(transerSavingCard);


        return transferBillEntity.ID;
    }

    public BaseOutput transerList(TranserQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<TranserBillOutputForm> list = transerMapper.getTranserBill(SystemUserUtil.getSystemEntityID(), form.TranserBillID, form.TransferOutCustomerID, form.StartDate, form.EndDate, form.BillStatus);
        return BasePageInfo.success(list);
    }

    public BaseOutput transerInfo(TranserInfoQueryForm form) {
        TranserBillInfoOutputForm transerBillOutputForm = transerMapper.getTranserBillInfo(form.ID);
        if (transerBillOutputForm.BillStatus != null) {
            if (transerBillOutputForm.BillStatus.equals("30")) {//已取消
                transerBillOutputForm.Cancel = false;
            } else {
                if (transerMapper.getBillStockIntegrityStatus(form.ID)) {
                    transerBillOutputForm.Cancel = true;
                } else {
                    transerBillOutputForm.Cancel = false;
                }
            }
        } else {
            transerBillOutputForm.Cancel = false;
        }

        transerBillOutputForm.Product = transerMapper.getTranserBillProductInfo(form.ID);
        transerBillOutputForm.Project = transerMapper.getTranserBillProjectInfo(form.ID);
        transerBillOutputForm.GeneralCard = transerMapper.getTranserBillGeneralCardInfo(form.ID);
        transerBillOutputForm.TimeCard = transerMapper.getTranserBillTimeCardInfo(form.ID);
        transerBillOutputForm.SavingCard = transerMapper.getTranserBillSavingCardInfo(form.ID);
        List<TranserBillPackageCardOutputForm> packageCard = transerMapper.getTranserBillPackageCardInfo(form.ID);
        List<TranserBillProductOutputForm> product = transerMapper.getTranserBillPackageCardProductInfo(form.ID);
        List<TranserBillProjectOutputForm> project = transerMapper.getTranserBillPackageCardProjectInfo(form.ID);
        List<TranserBillGeneralCardOutputForm> generalCard = transerMapper.getTranserBillPackageCardGeneralCardInfo(form.ID);
        List<TranserBillTimeCardOutputForm> timeCard = transerMapper.getTranserBillPackageCardTimeCardInfo(form.ID);
        List<TranserBillSavingCardOutputForm> savingCard = transerMapper.getTranserBillPackageCardSavingCardInfo(form.ID);
        for (TranserBillPackageCardOutputForm packageCardForm : packageCard) {
            packageCardForm.Product = product.stream().filter(packageCardProductListOutputForm -> packageCardProductListOutputForm.PackageCardAccountID.intValue() == packageCardForm.PackageCardAccountID.intValue()).collect(Collectors.toList());
            packageCardForm.Project = project.stream().filter(packageCardProjectListOutputForm -> packageCardProjectListOutputForm.PackageCardAccountID.intValue() == packageCardForm.PackageCardAccountID.intValue()).collect(Collectors.toList());
            packageCardForm.GeneralCard = generalCard.stream().filter(packageCardGeneralCardListOutputForm -> packageCardGeneralCardListOutputForm.PackageCardAccountID.intValue() == packageCardForm.PackageCardAccountID.intValue()).collect(Collectors.toList());
            packageCardForm.TimeCard = timeCard.stream().filter(packageCardTimeCardListOutputForm -> packageCardTimeCardListOutputForm.PackageCardAccountID.intValue() == packageCardForm.PackageCardAccountID.intValue()).collect(Collectors.toList());
            packageCardForm.SavingCard = savingCard.stream().filter(packageCardSavingCardListOutputForm -> packageCardSavingCardListOutputForm.PackageCardAccountID.intValue() == packageCardForm.PackageCardAccountID.intValue()).collect(Collectors.toList());
        }
        transerBillOutputForm.PackageCard = packageCard;
        return BaseOutputForm.success(transerBillOutputForm);
    }

    public BaseOutput miniProgramTranserList(MiniprogramTranserQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        Integer entityID = form.EntityID == null ? SystemUserUtil.getSystemEntityID() : form.EntityID;
        List<MiniprogramTranserBillOutputForm> list = transerMapper.miniProgramTranserList(entityID, form.Name, form.StartDate, form.EndDate, form.BillStatus);
        return BasePageInfo.success(list);
    }

    @Transactional
    public BaseOutput addTransferBillAttachment(TransferBillAttachmentAddForm form) {
        TransferBillAttachmentEntity transferBillAttachmentEntity = new TransferBillAttachmentEntity();
        transferBillAttachmentEntity.TransferBillID = form.TransferBillID;
        transferBillAttachmentEntity.AttachmentType = form.imgAttachment.AttachmentType;
        if (form.imgAttachment.AttachmentType == 10) {
            transferBillAttachmentEntity.AttachmentURL = OSSClientUtil.putOSSBase64Img(form.imgAttachment.AttachmentURL);
        } else {
            transferBillAttachmentEntity.AttachmentURL = form.imgAttachment.AttachmentURL;
        }
        transerMapper.addTransferBillAttachment(transferBillAttachmentEntity);
        SaleBillOrTreatBillAttachmentOutputForm saleBillOrTreatBillAttachmentOutputForm = new SaleBillOrTreatBillAttachmentOutputForm();
        saleBillOrTreatBillAttachmentOutputForm.AttachmentURL = transferBillAttachmentEntity.AttachmentURL;
        saleBillOrTreatBillAttachmentOutputForm.ID = transferBillAttachmentEntity.ID;
        return BaseOutputForm.success(saleBillOrTreatBillAttachmentOutputForm);
    }

    public BaseOutput deleteTransferBillAttachment(DeleteTransferBillBillAttachmentForm form) {
        if (form.AttachmentType == 10) {
            String url = transerMapper.getTransferBillAttachmentByID(form.ID);
            String[] split = url.split("/");
            OSSClientUtil.deleteObject(split[split.length - 1]);
            transerMapper.deleteTransferBillAttachment(form.ID);
        }
        return BaseOutput.success();
    }

    public BaseOutput getTranserBillAttachment(TransferBillAttachmentQueryForm form) {
        List<TransferBillAttachmentForm> list = transerMapper.getTransferBillAttachment(form.TransferBillID);
        return BaseOutputForm.success(list);
    }


    @Transactional
    public BaseOutput addSignature(SaleBillSignatureAddForm form) {
        SignatureOutputForm signatureOutputForm = transerMapper.getTransferBillSignatureByID(form.ID);
        if (signatureOutputForm != null) {
            String[] split = signatureOutputForm.ImageURL.split("/");
            OSSClientUtil.deleteObject(split[split.length - 1]);
            transerMapper.deleteTransferBillSignature(form.ID);
        }
        TransferBillSignatureEntity transferBillSignatureEntity = new TransferBillSignatureEntity();
        transferBillSignatureEntity.TransferBillID = form.ID;
        transferBillSignatureEntity.ImageURL = OSSClientUtil.putOSSBase64Img(form.ImageURL);
        transerMapper.addSignature(transferBillSignatureEntity);
        signatureOutputForm = new SignatureOutputForm();
        signatureOutputForm.ImageURL = transferBillSignatureEntity.ImageURL;
        signatureOutputForm.CreatedOn = DateTimeUtil.toDate(new Date());
        return BaseOutputForm.success(signatureOutputForm);
    }

    public BaseOutput deleteSignature(SaleBillSignatureAddForm form) {
        SignatureOutputForm signatureOutputForm = transerMapper.getTransferBillSignatureByID(form.ID);
        String[] split = signatureOutputForm.ImageURL.split("/");
        OSSClientUtil.deleteObject(split[split.length - 1]);
        transerMapper.deleteTransferBillSignature(form.ID);
        return BaseOutput.success();
    }

    public BaseOutput getSignature(SaleBillSignatureAddForm form) {
        return BaseOutputForm.success(transerMapper.getTransferBillSignatureByID(form.ID));
    }


    @Transactional
    public void cancelBill(TranserCancelForm form) {

        Boolean flag = transerMapper.getBillStockIntegrityStatus(form.ID);
        if (flag == false)
            throw new RuntimeException("该订单账户不完整，不可取消");


        TransferBillCancelProcessEntity transferBillCancelProcessEntity = new TransferBillCancelProcessEntity();
        transferBillCancelProcessEntity.BillID = form.ID;
        transferBillCancelProcessEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        transferBillCancelProcessEntity.Remark = form.Remark;
        transerMapper.createTransferBillCancelProcess(transferBillCancelProcessEntity);

        productAccountMapper.updateTransferProductAccountActiveFalse(form.ID);
        projectAccountMapper.updateTransferProjectAccountActiveFalse(form.ID);
        generalCardAccountMapper.updateTransferGeneralCardAccountActiveFalse(form.ID);
        timeCardAccountMapper.updateTransferTimeCardAccountActiveFalse(form.ID);
        savingCardAccountMapper.updateTransferSavingCardAccountActiveFalse(form.ID);

        List<TransferBillProductAccountEntity> transerBillProduct = transerMapper.getTranserBillProduct(form.ID);
        List<TransferBillProjectAccountEntity> transerBillProject = transerMapper.getTranserBillProject(form.ID);
        List<TransferBillGeneralCardAccountEntity> transerBillGeneralCard = transerMapper.getTranserBillGeneralCard(form.ID);
        List<TransferBillTimeCardAccountEntity> transerBillTimeCard = transerMapper.getTranserBillTimeCard(form.ID);
        List<TransferBillSavingCardAccountEntity> transerBillSavingCard = transerMapper.getTranserBillSavingCard(form.ID);
        for (TransferBillProductAccountEntity transferBillProductAccountEntity : transerBillProduct) {
            ProductAccountExcendEntity outProduct = productAccountMapper.getProductAccountByAccountID(transferBillProductAccountEntity.TransferOutProductAccountID);
            ProductAccountExcendEntity inProduct = productAccountMapper.getProductAccountByAccountID(transferBillProductAccountEntity.TransferInProductAccountID);
            ProductAccountEntity updateProductAccountEntity = new ProductAccountEntity();
            updateProductAccountEntity.ID = outProduct.ID;
            updateProductAccountEntity.Balance = outProduct.Balance.intValue() + inProduct.Balance.intValue();
            updateProductAccountEntity.PayPerformanceBalanceAmount = outProduct.PayPerformanceBalanceAmount.add(inProduct.PayPerformanceBalanceAmount);
            updateProductAccountEntity.CardPerformanceBalanceAmount = outProduct.CardPerformanceBalanceAmount.add(inProduct.CardPerformanceBalanceAmount);
            updateProductAccountEntity.CardLargessPerformanceBalanceAmount = outProduct.CardLargessPerformanceBalanceAmount.add(inProduct.CardLargessPerformanceBalanceAmount);
            updateProductAccountEntity.LargessPerformanceBalanceAmount = outProduct.LargessPerformanceBalanceAmount.add(inProduct.LargessPerformanceBalanceAmount);
            productAccountMapper.updateProductAccountBalance(updateProductAccountEntity);

        }
        for (TransferBillProjectAccountEntity transferBillProjectAccountEntity : transerBillProject) {
            ProjectAccountExcendEntity outProject = projectAccountMapper.getProjectAccountByAccountID(transferBillProjectAccountEntity.TransferOutProjectAccountID);
            ProjectAccountExcendEntity inProject = projectAccountMapper.getProjectAccountByAccountID(transferBillProjectAccountEntity.TransferInProjectAccountID);
            ProjectAccountEntity updateProjectAccountEntity = new ProjectAccountEntity();
            updateProjectAccountEntity.ID = outProject.ID;
            updateProjectAccountEntity.Balance = outProject.Balance.intValue() + inProject.Balance.intValue();
            updateProjectAccountEntity.PayPerformanceBalanceAmount = outProject.PayPerformanceBalanceAmount.add(inProject.PayPerformanceBalanceAmount);
            updateProjectAccountEntity.CardPerformanceBalanceAmount = outProject.CardPerformanceBalanceAmount.add(inProject.CardPerformanceBalanceAmount);
            updateProjectAccountEntity.CardLargessPerformanceBalanceAmount = outProject.CardLargessPerformanceBalanceAmount.add(inProject.CardLargessPerformanceBalanceAmount);
            updateProjectAccountEntity.LargessPerformanceBalanceAmount = outProject.LargessPerformanceBalanceAmount.add(inProject.LargessPerformanceBalanceAmount);
            projectAccountMapper.updateProjectAccountBalance(updateProjectAccountEntity);

        }
        for (TransferBillGeneralCardAccountEntity transferBillGeneralCardAccountEntity : transerBillGeneralCard) {
            GeneralCardAccountExcendEntity outGeneralCard = generalCardAccountMapper.getGeneralCardAccountByAccountID(transferBillGeneralCardAccountEntity.TransferOutGeneralCardAccountID);
            GeneralCardAccountExcendEntity inGeneralCard = generalCardAccountMapper.getGeneralCardAccountByAccountID(transferBillGeneralCardAccountEntity.TransferInGeneralCardAccountID);
            GeneralCardAccountEntity updateGeneralCardAccountEntity = new GeneralCardAccountEntity();
            updateGeneralCardAccountEntity.ID = outGeneralCard.ID;
            updateGeneralCardAccountEntity.Balance = outGeneralCard.Balance.intValue() + inGeneralCard.Balance.intValue();
            updateGeneralCardAccountEntity.PayPerformanceBalanceAmount = outGeneralCard.PayPerformanceBalanceAmount.add(inGeneralCard.PayPerformanceBalanceAmount);
            updateGeneralCardAccountEntity.CardPerformanceBalanceAmount = outGeneralCard.CardPerformanceBalanceAmount.add(inGeneralCard.CardPerformanceBalanceAmount);
            updateGeneralCardAccountEntity.CardLargessPerformanceBalanceAmount = outGeneralCard.CardLargessPerformanceBalanceAmount.add(inGeneralCard.CardLargessPerformanceBalanceAmount);
            updateGeneralCardAccountEntity.LargessPerformanceBalanceAmount = outGeneralCard.LargessPerformanceBalanceAmount.add(inGeneralCard.LargessPerformanceBalanceAmount);
            generalCardAccountMapper.updateGeneralCardAccountBalance(updateGeneralCardAccountEntity);

        }
        for (TransferBillTimeCardAccountEntity transferBillTimeCardAccountEntity : transerBillTimeCard) {
            TimeCardAccountEntity outTimeCard = timeCardAccountMapper.getTimeCardAccountByAccountID(transferBillTimeCardAccountEntity.TransferOutTimeCardAccountID);
            TimeCardAccountEntity inTimeCard = timeCardAccountMapper.getTimeCardAccountByAccountID(transferBillTimeCardAccountEntity.TransferInTimeCardAccountID);
            TimeCardAccountEntity updateTimeCardAccountEntity = new TimeCardAccountEntity();
            updateTimeCardAccountEntity.ID = outTimeCard.ID;
            updateTimeCardAccountEntity.PayPerformanceBalanceAmount = outTimeCard.PayPerformanceBalanceAmount.add(inTimeCard.PayPerformanceBalanceAmount);
            updateTimeCardAccountEntity.CardPerformanceBalanceAmount = outTimeCard.CardPerformanceBalanceAmount.add(inTimeCard.CardPerformanceBalanceAmount);
            updateTimeCardAccountEntity.CardLargessPerformanceBalanceAmount = outTimeCard.CardLargessPerformanceBalanceAmount.add(inTimeCard.CardLargessPerformanceBalanceAmount);
            updateTimeCardAccountEntity.LargessPerformanceBalanceAmount = outTimeCard.LargessPerformanceBalanceAmount.add(inTimeCard.LargessPerformanceBalanceAmount);
            updateTimeCardAccountEntity.ValidDate = inTimeCard.ValidDate;
            updateTimeCardAccountEntity.ValidType = inTimeCard.ValidType;
            timeCardAccountMapper.updateTimeCardAccountEntity(updateTimeCardAccountEntity);

        }
        for (TransferBillSavingCardAccountEntity transferBillSavingCardAccount : transerBillSavingCard) {
            SavingCardAccountEntity outSavingCard = savingCardAccountMapper.getSavingCardAccountByID(transferBillSavingCardAccount.TransferOutSavingCardAccountID);
            SavingCardAccountEntity inSavingCard = savingCardAccountMapper.getSavingCardAccountByID(transferBillSavingCardAccount.TransferInSavingCardAccountID);
            SavingCardAccountEntity savingCardAccountEntity = new SavingCardAccountEntity();
            savingCardAccountEntity.ID = outSavingCard.ID;
            savingCardAccountEntity.Balance = outSavingCard.Balance.add(inSavingCard.Balance);
            savingCardAccountEntity.LargessBalance = outSavingCard.LargessBalance.add(inSavingCard.LargessBalance);
            savingCardAccountMapper.updateSavingCardAccountBalance(savingCardAccountEntity);

        }

        transerMapper.updateTransferBillStatus(form.ID, BillCode.BillStatus_30.getID());
    }
}