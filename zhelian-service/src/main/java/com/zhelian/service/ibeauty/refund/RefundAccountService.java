package com.zhelian.service.ibeauty.refund;

import com.zhelian.mapper.ibeauty.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.RefundAccountQueryForm;
import com.zhelian.model.ibeauty.form.input.TranserPackageCardAccountQueryForm;
import com.zhelian.model.ibeauty.form.output.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class RefundAccountService {
    @Autowired
    private ProjectAccountMapper projectAccountMapper;
    @Autowired
    private ProductAccountMapper productAccountMapper;
    @Autowired
    private SavingCardAccountMapper savingCardAccountMapper;
    @Autowired
    private GeneralCardAccountMapper generalCardAccountMapper;
    @Autowired
    private TimeCardAccountMapper timeCardAccountMapper;
    @Autowired
    private PackageCardAccountMapper packageCardAccountMapper;

    public BaseOutput getRefundProductAccount(RefundAccountQueryForm form) {
        List<ProductAccountOutputForm> list = productAccountMapper.getRefundProductAccount(form.CustomerID, form.Name);
        return BaseOutputForm.success(list);
    }

    public BaseOutput getRefundProjectAccount(RefundAccountQueryForm form) {
        List<ProjectAccountOutputForm> list = projectAccountMapper.getRefundProjectAccount(form.CustomerID, form.Name);
        return BaseOutputForm.success(list);
    }

    public BaseOutput getRefundSavingCardAccount(RefundAccountQueryForm form) {
        List<SavingCardAccountForm> list = savingCardAccountMapper.getRefundSavingCardAccount(form.CustomerID, form.Name);
        return BaseOutputForm.success(list);
    }

    public BaseOutput getRefundGeneralCardAccount(RefundAccountQueryForm form) {
        List<GeneralCardAccountOutputForm> list = generalCardAccountMapper.getRefundGeneralCardAccount(form.CustomerID, form.Name);
        return BaseOutputForm.success(list);
    }

    public BaseOutput getRefundTimeCardAccount(RefundAccountQueryForm form) {
        List<TimeCardAccountOutputForm> list = timeCardAccountMapper.getRefundTimeCardAccount(form.CustomerID, form.Name);
        return BaseOutputForm.success(list);
    }

    public BaseOutput getRefundPackageCardAccount(RefundAccountQueryForm form) {
        List<PackageCardAccountOutputForm> list = packageCardAccountMapper.getRefundPackageCardAccount(form.CustomerID, form.Name);
        return BaseOutputForm.success(list);
    }

    public BaseOutput packageCardAccountDetails(TranserPackageCardAccountQueryForm form) {
        PackageCardAccountDetailsOutputForm packageCardAccountDetailsOutputForm = new PackageCardAccountDetailsOutputForm();
        String ValidDayName = packageCardAccountMapper.getPackageCardAccountValidDayName(form.PackageCardAccountID);
        packageCardAccountDetailsOutputForm.Product = packageCardAccountMapper.refundPackageCardProductAccount(form.PackageCardAccountID);
        if (packageCardAccountDetailsOutputForm.Product != null)
            packageCardAccountDetailsOutputForm.Product.stream().forEach(i -> i.ValidDayName = ValidDayName);
        packageCardAccountDetailsOutputForm.Project = packageCardAccountMapper.refundPackageCardProjectAccount(form.PackageCardAccountID);
        if (packageCardAccountDetailsOutputForm.Project != null)
            packageCardAccountDetailsOutputForm.Project.stream().forEach(i -> i.ValidDayName = ValidDayName);
        packageCardAccountDetailsOutputForm.GeneralCard = packageCardAccountMapper.refundPackageCardGeneralCardAccount(form.PackageCardAccountID);
        if (packageCardAccountDetailsOutputForm.GeneralCard != null)
            packageCardAccountDetailsOutputForm.GeneralCard.stream().forEach(i -> i.ValidDayName = ValidDayName);
        packageCardAccountDetailsOutputForm.TimeCard = packageCardAccountMapper.refundPackageCardTimeCardAccount(form.PackageCardAccountID);
        if (packageCardAccountDetailsOutputForm.TimeCard != null)
            packageCardAccountDetailsOutputForm.TimeCard.stream().forEach(i -> i.ValidDayName = ValidDayName);
        packageCardAccountDetailsOutputForm.SavingCard = packageCardAccountMapper.refundPackageCardSavingCardAccount(form.PackageCardAccountID);
        if (packageCardAccountDetailsOutputForm.SavingCard != null)
            packageCardAccountDetailsOutputForm.SavingCard.stream().forEach(i -> i.ValidDayName = ValidDayName);
        return BaseOutputForm.success(packageCardAccountDetailsOutputForm);
    }
}