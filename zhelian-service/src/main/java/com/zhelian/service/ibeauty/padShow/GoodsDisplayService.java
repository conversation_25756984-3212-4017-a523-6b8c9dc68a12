package com.zhelian.service.ibeauty.padShow;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.utils.OSSClientUtil;
import com.zhelian.mapper.ibeauty.GoodsDisplayMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.pad.entity.GoodsDisplayEntity;
import com.zhelian.model.pad.form.input.GoodsDisplayGoodsAddForm;
import com.zhelian.model.pad.form.input.GoodsDisplayQueryForm;
import com.zhelian.model.pad.form.input.GoodsDisplayUpdateForm;
import com.zhelian.model.pad.form.output.GoodsDisplayDetailOutputForm;
import com.zhelian.model.pad.form.output.GoodsDisplayOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.zhelian.core.utils.ListUtil.listToTree;


@Service
public class GoodsDisplayService {

    @Autowired
    GoodsDisplayMapper goodsDisplayMapper;


    public BaseOutput allGoodsDisplay(GoodsDisplayQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<GoodsDisplayOutputForm> list = goodsDisplayMapper.allGoodsDisplay(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput getMemo(GoodsDisplayQueryForm form) {
        return BaseOutputForm.success(goodsDisplayMapper.getMemo(form.ID));

    }

    public BaseOutput createGoodsDisplay(GoodsDisplayGoodsAddForm form) {

        GoodsDisplayEntity goodsDisplayEntity = new GoodsDisplayEntity();
        goodsDisplayEntity.Name = form.Name;
        goodsDisplayEntity.CategoryID = form.CategoryID;
        goodsDisplayEntity.ImageURL = form.ImageURL == null || form.ImageURL.equals("") ? null : form.ImageURL;
        goodsDisplayEntity.Memo = form.Memo;
        goodsDisplayEntity.Price = form.Price;
        goodsDisplayMapper.createGoodsDisplay(goodsDisplayEntity);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateGoodsDisplay(GoodsDisplayUpdateForm form) {

        GoodsDisplayEntity goodsDisplayEntity = goodsDisplayMapper.getGoodsDisplay(form.ID);
        if (goodsDisplayEntity.ImageURL != null && !goodsDisplayEntity.ImageURL.equals("")) {
            String[] split = goodsDisplayEntity.ImageURL.split("/");
            OSSClientUtil.deleteObject(split[split.length - 1]);
        }
        goodsDisplayEntity.Name = form.Name;
        goodsDisplayEntity.CategoryID = form.CategoryID;
        goodsDisplayEntity.ImageURL = form.ImageURL == null || form.ImageURL.equals("") ? null : form.ImageURL;
        goodsDisplayEntity.Memo = form.Memo;
        goodsDisplayEntity.Price = form.Price;
        goodsDisplayMapper.updateGoodsDisplay(goodsDisplayEntity);

        return BaseOutput.success();
    }


    @Transactional
    public BaseOutput delete(GoodsDisplayUpdateForm form) {
        GoodsDisplayEntity goodsDisplayEntity = goodsDisplayMapper.getGoodsDisplay(form.ID);
        if (goodsDisplayEntity.ImageURL != null && !goodsDisplayEntity.ImageURL.equals("")) {
            String[] split = goodsDisplayEntity.ImageURL.split("/");
            OSSClientUtil.deleteObject(split[split.length - 1]);
        }

        goodsDisplayMapper.deleteGoods(form.ID);
        return BaseOutput.success();
    }

    public BaseOutput list() {
        List<GoodsDisplayDetailOutputForm> list = goodsDisplayMapper.getGoods();
        JSONArray result = listToTree(JSONArray.parseArray(JSON.toJSONString(list, JSONWriter.Feature.WriteMapNullValue)), "ID", "ParentID", "Child");
        return BaseOutputForm.success(result);
    }
}
