package com.zhelian.service.ibeauty.treatProjectCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.TreatProjectSchemeHandlerCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.TreatProjectSchemeHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.TreatProjectSchemeHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.input.TreatProjectCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectSchemeHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectSchemeHandlersCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.TreatProjectHandlerCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatProjectSchemeHandlerCommissionService {

    @Autowired
    TreatProjectSchemeHandlerCommissionMapper treatProjectSchemeHandlerCommissionMapper;

    public BaseOutput allTreatProjectSchemeHandlerCommission(TreatProjectSchemeHandlerCommissionQueryForm form) {
        List<TreatProjectHandlerCommissionOutputForm> list = treatProjectSchemeHandlerCommissionMapper.allTreatProjectSchemeHandlerCommission(form.EntityID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatProjectSchemeHandlerCommission(TreatProjectSchemeHandlersCommissionAddForm form) {

        treatProjectSchemeHandlerCommissionMapper.deleteTreatProjectSchemeHandler(form.EntityID);
        treatProjectSchemeHandlerCommissionMapper.deleteTreatProjectSchemeHandlerJobType(form.EntityID);

        if (form.Handler != null && form.Handler.size() > 0) {

            List listTreatProjectSchemeHandlerCommissionEntity = new ArrayList<>();

            List listTreatProjectSchemeHandlerJobTypeCommissionEntity = new ArrayList<>();

            for (TreatProjectHandlerCommissionAddForm treatProjectSchemeHandlerAddForm : form.Handler) {
                if (treatProjectSchemeHandlerAddForm.PayRate != null || treatProjectSchemeHandlerAddForm.PayFixed != null ||
                        treatProjectSchemeHandlerAddForm.CardRate != null || treatProjectSchemeHandlerAddForm.CardFixed != null ||
                        treatProjectSchemeHandlerAddForm.CardLargessRate != null || treatProjectSchemeHandlerAddForm.CardLargessFixed != null ||
                        treatProjectSchemeHandlerAddForm.LargessRate != null || treatProjectSchemeHandlerAddForm.LargessFixed != null ||
                        treatProjectSchemeHandlerAddForm.SpecialBenefit != null) {
                    TreatProjectSchemeHandlerCommissionEntity treatProjectSchemeHandlerCommissionEntity = new TreatProjectSchemeHandlerCommissionEntity();
                    treatProjectSchemeHandlerCommissionEntity.EntityID = form.EntityID;
                    treatProjectSchemeHandlerCommissionEntity.ProjectTreatHandlerID = treatProjectSchemeHandlerAddForm.TreatHandlerID;
                    treatProjectSchemeHandlerCommissionEntity.PayRate = treatProjectSchemeHandlerAddForm.PayRate;
                    treatProjectSchemeHandlerCommissionEntity.PayFixed = treatProjectSchemeHandlerAddForm.PayFixed;
                    treatProjectSchemeHandlerCommissionEntity.CardRate = treatProjectSchemeHandlerAddForm.CardRate;
                    treatProjectSchemeHandlerCommissionEntity.CardFixed = treatProjectSchemeHandlerAddForm.CardFixed;
                    treatProjectSchemeHandlerCommissionEntity.CardLargessRate = treatProjectSchemeHandlerAddForm.CardLargessRate;
                    treatProjectSchemeHandlerCommissionEntity.CardLargessFixed = treatProjectSchemeHandlerAddForm.CardLargessFixed;
                    treatProjectSchemeHandlerCommissionEntity.LargessFixed = treatProjectSchemeHandlerAddForm.LargessFixed;
                    treatProjectSchemeHandlerCommissionEntity.LargessRate = treatProjectSchemeHandlerAddForm.LargessRate;
                    treatProjectSchemeHandlerCommissionEntity.SpecialBenefit = treatProjectSchemeHandlerAddForm.SpecialBenefit;
                    listTreatProjectSchemeHandlerCommissionEntity.add(treatProjectSchemeHandlerCommissionEntity);
                }

                if (treatProjectSchemeHandlerAddForm.HandlerJobTypeCommission != null && treatProjectSchemeHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (TreatProjectCategoryHandlerJobTypeCommissionAddForm treatProjectSchemeHandlerJobTypeCommissionAddForm : treatProjectSchemeHandlerAddForm.HandlerJobTypeCommission) {
                        if (treatProjectSchemeHandlerJobTypeCommissionAddForm.PayRate != null || treatProjectSchemeHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                treatProjectSchemeHandlerJobTypeCommissionAddForm.CardRate != null || treatProjectSchemeHandlerJobTypeCommissionAddForm.CardFixed != null ||
                                treatProjectSchemeHandlerJobTypeCommissionAddForm.CardLargessRate != null || treatProjectSchemeHandlerJobTypeCommissionAddForm.CardLargessFixed != null ||
                                treatProjectSchemeHandlerJobTypeCommissionAddForm.LargessRate != null || treatProjectSchemeHandlerJobTypeCommissionAddForm.LargessFixed != null ||
                                treatProjectSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            TreatProjectSchemeHandlerJobTypeCommissionEntity treatProjectSchemeHandlerJobTypeCommissionEntity = new TreatProjectSchemeHandlerJobTypeCommissionEntity();
                            treatProjectSchemeHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            treatProjectSchemeHandlerJobTypeCommissionEntity.ProjectTreatHandlerID = treatProjectSchemeHandlerAddForm.TreatHandlerID;
                            treatProjectSchemeHandlerJobTypeCommissionEntity.JobTypeID = treatProjectSchemeHandlerJobTypeCommissionAddForm.JobTypeID;
                            treatProjectSchemeHandlerJobTypeCommissionEntity.PayRate = treatProjectSchemeHandlerJobTypeCommissionAddForm.PayRate;
                            treatProjectSchemeHandlerJobTypeCommissionEntity.PayFixed = treatProjectSchemeHandlerJobTypeCommissionAddForm.PayFixed;
                            treatProjectSchemeHandlerJobTypeCommissionEntity.CardRate = treatProjectSchemeHandlerJobTypeCommissionAddForm.CardRate;
                            treatProjectSchemeHandlerJobTypeCommissionEntity.CardFixed = treatProjectSchemeHandlerJobTypeCommissionAddForm.CardFixed;
                            treatProjectSchemeHandlerJobTypeCommissionEntity.CardLargessRate = treatProjectSchemeHandlerJobTypeCommissionAddForm.CardLargessRate;
                            treatProjectSchemeHandlerJobTypeCommissionEntity.CardLargessFixed = treatProjectSchemeHandlerJobTypeCommissionAddForm.CardLargessFixed;
                            treatProjectSchemeHandlerJobTypeCommissionEntity.LargessFixed = treatProjectSchemeHandlerJobTypeCommissionAddForm.LargessFixed;
                            treatProjectSchemeHandlerJobTypeCommissionEntity.LargessRate = treatProjectSchemeHandlerJobTypeCommissionAddForm.LargessRate;
                            treatProjectSchemeHandlerJobTypeCommissionEntity.SpecialBenefit = treatProjectSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            listTreatProjectSchemeHandlerJobTypeCommissionEntity.add(treatProjectSchemeHandlerJobTypeCommissionEntity);
                        }
                    }

                }
            }
            if (listTreatProjectSchemeHandlerCommissionEntity != null && listTreatProjectSchemeHandlerCommissionEntity.size() > 0) {
                List<List<TreatProjectSchemeHandlerCommissionEntity>> subs = Lists.partition(listTreatProjectSchemeHandlerCommissionEntity, 20);
                for (List<TreatProjectSchemeHandlerCommissionEntity> entityList : subs) {
                    treatProjectSchemeHandlerCommissionMapper.updateTreatProjectSchemeHandlerCommission(entityList);
                }
            }
            if (listTreatProjectSchemeHandlerJobTypeCommissionEntity != null && listTreatProjectSchemeHandlerJobTypeCommissionEntity.size() > 0) {
                List<List<TreatProjectSchemeHandlerJobTypeCommissionEntity>> subs = Lists.partition(listTreatProjectSchemeHandlerJobTypeCommissionEntity, 20);
                for (List<TreatProjectSchemeHandlerJobTypeCommissionEntity> entityList : subs) {
                    treatProjectSchemeHandlerCommissionMapper.insertTreatProjectSchemeHandlerJobTypeCommission(entityList);
                }
            }
        }
        return BaseOutput.success();
    }
}