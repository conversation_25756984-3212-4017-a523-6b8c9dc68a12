package com.zhelian.service.ibeauty.treatTimeCardCommission;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.ibeauty.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.TreatTimeCardProjectCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.input.TreatProjectCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCommissionSchemeDeleteForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCommissionSchemeQueryForm;
import com.zhelian.model.ibeauty.form.output.TreatProjectCommissionSchemeOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class TreatTimeCardCommissionSchemeService {

    @Autowired
    TreatTimeCardCommissionSchemeMapper treatTimeCardCommissionSchemeMapper;
    @Autowired
    TreatTimeCardCategoryCommissionMapper treatTimeCardCategoryCommissionMapper;
    @Autowired
    TreatTimeCardCategoryHandlerCommissionMapper treatTimeCardCategoryHandlerCommissionMapper;
    @Autowired
    TreatTimeCardCommissionMapper treatTimeCardCommissionMapper;
    @Autowired
    TreatTimeCardHandlerCommissionMapper treatTimeCardHandlerCommissionMapper;
    @Autowired
    TreatTimeCardProjectCommissionMapper treatTimeCardProjectCommissionMapper;
    @Autowired
    TreatTimeCardProjectHandlerCommissionMapper treatTimeCardProjectHandlerCommissionMapper;
    @Autowired
    TreatTimeCardSchemeHandlerCommissionMapper treatTimeCardSchemeHandlerCommissionMapper;


    public BaseOutput treatTimeCardCommissionSchemeList(TreatProjectCommissionSchemeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<TreatProjectCommissionSchemeOutputForm> list = treatTimeCardCommissionSchemeMapper.treatTimeCardCommissionSchemeList(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput createTreatTimeCardCommissionScheme(TreatProjectCommissionSchemeAddForm form) {
        Integer count = treatTimeCardCommissionSchemeMapper.getTreatTimeCardCommissionScheme(form.EntityID);
        if (count > 0)
            return BaseOutput.failed("组织单位已经存在");
        TreatTimeCardProjectCommissionSchemeEntity treatTimeCardProjectCommissionSchemeEntity = new TreatTimeCardProjectCommissionSchemeEntity();
        treatTimeCardProjectCommissionSchemeEntity.EntityID = form.EntityID;
        treatTimeCardCommissionSchemeMapper.createTreatTimeCardCommissionScheme(treatTimeCardProjectCommissionSchemeEntity);
        return BaseOutput.success();

    }

    public BaseOutput deleteTreatTimeCardCommissionScheme(TreatProjectCommissionSchemeDeleteForm form) {
        treatTimeCardCommissionSchemeMapper.deleteTreatTimeCardCommissionScheme(form.EntityID);
        treatTimeCardCategoryCommissionMapper.deleteTreatTimeCardCategoryCommission(form.EntityID);
        treatTimeCardCategoryHandlerCommissionMapper.deleteTreatTimeCardCategoryHandlerByEntityID(form.EntityID);
        treatTimeCardCategoryHandlerCommissionMapper.deleteTreatTimeCardCategoryHandlerJobTypeByEntityID(form.EntityID);
        treatTimeCardCommissionMapper.deleteTreatTimeCardCommissionByEntityID(form.EntityID);
        treatTimeCardHandlerCommissionMapper.deleteTreatTimeCardHandlerCommissionByEntityID(form.EntityID);
        treatTimeCardHandlerCommissionMapper.deleteTreatTimeCardHandlerJobTypeCommissionByEntityID(form.EntityID);
        treatTimeCardProjectCommissionMapper.deleteTreatTimeCardProjectCommissionByEntityID(form.EntityID);
        treatTimeCardProjectHandlerCommissionMapper.deleteTreatTimeCardProjectHandlerByEntityID(form.EntityID);
        treatTimeCardProjectHandlerCommissionMapper.deleteTreatTimeCardProjectHandlerJobTypeByEntityID(form.EntityID);
        treatTimeCardSchemeHandlerCommissionMapper.deleteTreatTimeCardSchemeHandler(form.EntityID);
        treatTimeCardSchemeHandlerCommissionMapper.deleteTreatTimeCardSchemeHandlerJobType(form.EntityID);
        return BaseOutput.success();
    }
}