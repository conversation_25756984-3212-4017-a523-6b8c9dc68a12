package com.zhelian.service.ibeauty.Additional;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.utils.DateTimeUtil;
import com.zhelian.core.utils.OSSClientUtil;
import com.zhelian.mapper.ibeauty.AdditionalSpendingMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.AdditionalSpendingAttachmentEntity;
import com.zhelian.model.ibeauty.entity.AdditionalSpendingEntity;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingAddForm;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingQueryForm;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingUpdateForm;
import com.zhelian.model.ibeauty.form.output.AdditionalSpendingOutputEasyExcelForm;
import com.zhelian.model.ibeauty.form.output.AdditionalSpendingSumOutputForm;
import com.zhelian.model.ibeauty.form.output.AdditionalSpendingTotalOutputForm;
import com.zhelian.service.ibeauty.SealingAccount.SealingAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.ZoneId;
import java.util.List;


@Service
public class AdditionalSpendingService {

    @Autowired
    AdditionalSpendingMapper additionalSpendingMapper;
    @Autowired
    SealingAccountService sealingAccountService;

    public BaseOutput additionalSpendingList(AdditionalSpendingQueryForm form) {

        AdditionalSpendingTotalOutputForm additionalSpendingTotalOutputForm = new AdditionalSpendingTotalOutputForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        additionalSpendingTotalOutputForm.spendingOutputFormBasePageInfo = BasePageInfo.success(additionalSpendingMapper.additionalSpendingList(SystemUserUtil.getSystemUserID(), form.Payer, form.EntityID, form.SpendingCategoryID, form.StartDate, form.EndDate));
        additionalSpendingTotalOutputForm.spendingSumOutputForm = additionalSpendingMapper.additionalSpendingSum(SystemUserUtil.getSystemUserID(), form.Payer, form.EntityID, form.SpendingCategoryID, form.StartDate, form.EndDate);
        return BaseOutputForm.success(additionalSpendingTotalOutputForm);
    }

    @Transactional
    public BaseOutput insertAdditionalSpending(AdditionalSpendingAddForm form) {
        Boolean modifyIncomeAndSpendingRestriction = sealingAccountService.getModifyIncomeAndSpendingRestriction(DateTimeUtil.toDate(form.SpendingDate));
        if(modifyIncomeAndSpendingRestriction){
            return BaseOutput.failed("付款时间在关账时间内，请选择其他时间");
        }

        AdditionalSpendingEntity additionalSpendingEntity = new AdditionalSpendingEntity();
        additionalSpendingEntity.SpendingCategoryID = form.SpendingCategoryID;
        additionalSpendingEntity.EntityID = form.EntityID;
        additionalSpendingEntity.SpendingDate = form.SpendingDate;
        additionalSpendingEntity.PaymentsID = form.PaymentsID;
        additionalSpendingEntity.Amount = form.Amount;
        additionalSpendingEntity.Payer = form.Payer;
        additionalSpendingEntity.Content = form.Content;
        additionalSpendingEntity.EmployeeID = SystemUserUtil.getSystemUserID();
        additionalSpendingMapper.insertAdditionalSpending(additionalSpendingEntity);

        if (form.ImageList.size() > 0) {
            for (int a = 0; a < form.ImageList.size(); a++) {
                additionalSpendingMapper.addAttachment(additionalSpendingEntity.ID, form.ImageList.get(a).AttachmentURL, form.ImageList.get(a).AttachmentType);
            }
        }
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateAdditionalSpending(AdditionalSpendingUpdateForm form) {
        AdditionalSpendingEntity additionalSpendingEntity = additionalSpendingMapper.getAdditionalSpendingEntity(form.ID);
        Boolean modifyIncomeAndSpendingRestriction = sealingAccountService.getModifyIncomeAndSpendingRestriction(DateTimeUtil.toDate(additionalSpendingEntity.SpendingDate));
        if(modifyIncomeAndSpendingRestriction &&  !form.SpendingDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().equals( additionalSpendingEntity.SpendingDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())){
            return BaseOutput.failed("付款时间在关账时间内，请选择其他时间");
        }

        Boolean incomeAndSpendingRestriction = sealingAccountService.getModifyIncomeAndSpendingRestriction(DateTimeUtil.toDate(form.SpendingDate));
        if(incomeAndSpendingRestriction &&  !form.SpendingDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().equals( additionalSpendingEntity.SpendingDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())){
            return BaseOutput.failed("付款时间在关账时间内，请选择其他时间");
        }

        additionalSpendingEntity.ID = form.ID;
        additionalSpendingEntity.SpendingCategoryID = form.SpendingCategoryID;
        additionalSpendingEntity.EntityID = form.EntityID;
        additionalSpendingEntity.SpendingDate = form.SpendingDate;
        additionalSpendingEntity.PaymentsID = form.PaymentsID;
        additionalSpendingEntity.Amount = form.Amount;
        additionalSpendingEntity.Payer = form.Payer;
        additionalSpendingEntity.Content = form.Content;
        additionalSpendingMapper.updateAdditionalSpending(additionalSpendingEntity);


        List<AdditionalSpendingAttachmentEntity> attachmentEntityList = additionalSpendingMapper.getAttachment(form.ID);
        if (attachmentEntityList.size() > 0) {
            additionalSpendingMapper.deleteAttachment(form.ID);
        }

        if (form.ImageList.size() > 0) {
            for (int a = 0; a < form.ImageList.size(); a++) {
                additionalSpendingMapper.addAttachment(form.ID, form.ImageList.get(a).AttachmentURL, form.ImageList.get(a).AttachmentType);
            }
        }
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput deleteAdditionalSpendingByID(AdditionalSpendingUpdateForm form) {
        AdditionalSpendingEntity additionalSpendingEntity = additionalSpendingMapper.getAdditionalSpendingEntity(form.ID);
        Boolean modifyIncomeAndSpendingRestriction = sealingAccountService.getModifyIncomeAndSpendingRestriction(DateTimeUtil.toDate(additionalSpendingEntity.SpendingDate));
        if(modifyIncomeAndSpendingRestriction){
            return BaseOutput.failed("付款时间在关账时间内，请选择其他订单");
        }

        additionalSpendingMapper.deleteAdditionalSpendingByID(form.ID);
        List<AdditionalSpendingAttachmentEntity> attachmentEntityList = additionalSpendingMapper.getAttachment(form.ID);
        if (attachmentEntityList.size() > 0) {
            for (AdditionalSpendingAttachmentEntity urlForm : attachmentEntityList) {
                String url = urlForm.AttachmentURL;
                String[] split = url.split("/");
                OSSClientUtil.deleteObject(split[split.length - 1]);
            }
            additionalSpendingMapper.deleteAttachment(form.ID);
        }
        return BaseOutput.success();
    }

    public BaseOutput getAttachment(AdditionalSpendingUpdateForm form) {
        List<AdditionalSpendingAttachmentEntity> attachmentEntityList = additionalSpendingMapper.getAttachment(form.ID);
        return BaseOutputForm.success(attachmentEntityList);
    }

    public BaseOutput easyExcelAttachment(AdditionalSpendingQueryForm form, HttpServletResponse response) {
        List<AdditionalSpendingOutputEasyExcelForm> list = additionalSpendingMapper.additionalSpendingEasyExcel(SystemUserUtil.getSystemUserID(), form.Payer, form.EntityID, form.SpendingCategoryID, form.StartDate, form.EndDate);
        AdditionalSpendingSumOutputForm additionalSpendingSum = additionalSpendingMapper.additionalSpendingSum(SystemUserUtil.getSystemUserID(), form.Payer, form.EntityID, form.SpendingCategoryID, form.StartDate, form.EndDate);
        if (additionalSpendingSum != null) {
            AdditionalSpendingOutputEasyExcelForm additionalSpendingOutputForm = new AdditionalSpendingOutputEasyExcelForm();
            additionalSpendingOutputForm.categoryName = "合计";
            additionalSpendingOutputForm.paymentsName = "";
            additionalSpendingOutputForm.amount = additionalSpendingSum.TotalAmount.doubleValue();
            additionalSpendingOutputForm.payer = "";
            additionalSpendingOutputForm.spendingDate = "";
            additionalSpendingOutputForm.empName = "";
            additionalSpendingOutputForm.createdOn = "";
            additionalSpendingOutputForm.entityName = "";
            additionalSpendingOutputForm.content = "";
            list.add(additionalSpendingOutputForm);
        }

        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("支出管理", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), AdditionalSpendingOutputEasyExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("支出管理").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return null;
    }
}
