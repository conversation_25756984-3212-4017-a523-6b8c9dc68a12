package com.zhelian.service.ibeauty.appointment;

import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.AppointmentServicerMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.AppointmentServicerEntity;
import com.zhelian.model.ibeauty.entity.AppointmentServicerJobTypeEntity;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.ibeauty.form.output.AppointmentServicerDetailOutputForm;
import com.zhelian.model.ibeauty.form.output.AppointmentServicerOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

@Service
public class AppointmentServicerService {

    @Autowired
    AppointmentServicerMapper appointmentServicerMapper;

    public BaseOutput all(AppointmentServicerQueryForm form) {
        List<AppointmentServicerOutputForm> list = appointmentServicerMapper.all(form);
        return BaseOutputForm.success(list);
    }

    public BaseOutput detail(AppointmentServicerQueryForm form) {
        AppointmentServicerDetailOutputForm outputForm = appointmentServicerMapper.detail(form);
        return BaseOutputForm.success(outputForm);
    }

    @Transactional
    public BaseOutput create(AppointmentServicerAddForm form) {
        Integer appointmentServicerCount = appointmentServicerMapper.getCount();
        AppointmentServicerEntity appointmentServicerEntity = new AppointmentServicerEntity();
        appointmentServicerEntity.Name = form.Name;
        appointmentServicerEntity.AppointmentNumber = form.AppointmentNumber == null ? 0 : form.AppointmentNumber;
        appointmentServicerEntity.Sequence = appointmentServicerCount + 1;
        appointmentServicerEntity.Active = true;

        appointmentServicerMapper.addAppointmentServicer(appointmentServicerEntity);

        List<AppointmentServicerJobTypeEntity> servicerList = new ArrayList<>();
        if (form.JobTypeList != null && form.JobTypeList.size() > 0) {
            for (AppointmentServicerJobTypeAddForm appointmentServicerJobTypeAddForm : form.JobTypeList) {
                AppointmentServicerJobTypeEntity appointmentServicerJobTypeEntity = new AppointmentServicerJobTypeEntity();
                appointmentServicerJobTypeEntity.AppointmentServicerID = appointmentServicerEntity.ID;
                appointmentServicerJobTypeEntity.JobType = appointmentServicerJobTypeAddForm.JobTypeID;
                appointmentServicerJobTypeEntity.Sequence = appointmentServicerJobTypeAddForm.Sequence;
                servicerList.add(appointmentServicerJobTypeEntity);
            }

            appointmentServicerMapper.addAppointmentServicerJobType(servicerList);
        }

        if (form.EntityList != null && form.EntityList.size() > 0) {
            appointmentServicerMapper.addAppointmentServicerEntity(appointmentServicerEntity.ID, form.EntityList);
        }
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput update(AppointmentServicerUpdateForm form) {

        AppointmentServicerEntity appointmentServicerEntity = new AppointmentServicerEntity();
        appointmentServicerEntity.ID = form.ID;
        appointmentServicerEntity.Name = form.Name;
        appointmentServicerEntity.AppointmentNumber = form.AppointmentNumber == null ? 0 : form.AppointmentNumber;
        appointmentServicerEntity.Active = form.Active;

        appointmentServicerMapper.updateAppointmentServicer(appointmentServicerEntity);

        appointmentServicerMapper.deleteAppointmentServicerJobType(form.ID);
        List<AppointmentServicerJobTypeEntity> servicerList = new ArrayList<>();
        if (form.JobTypeList != null && form.JobTypeList.size() > 0) {
            for (AppointmentServicerJobTypeAddForm appointmentServicerJobTypeAddForm : form.JobTypeList) {
                AppointmentServicerJobTypeEntity appointmentServicerJobTypeEntity = new AppointmentServicerJobTypeEntity();
                appointmentServicerJobTypeEntity.AppointmentServicerID = appointmentServicerEntity.ID;
                appointmentServicerJobTypeEntity.JobType = appointmentServicerJobTypeAddForm.JobTypeID;
                appointmentServicerJobTypeEntity.Sequence = appointmentServicerJobTypeAddForm.Sequence;
                servicerList.add(appointmentServicerJobTypeEntity);
            }

            appointmentServicerMapper.addAppointmentServicerJobType(servicerList);
        }

        appointmentServicerMapper.deleteAppointmentServicerEntity(form.ID);
        if (form.EntityList != null && form.EntityList.size() > 0) {
            appointmentServicerMapper.addAppointmentServicerEntity(appointmentServicerEntity.ID, form.EntityList);
        }
        return BaseOutput.success();
    }

    public BaseOutput move(AppointmentServicerMoveForm form) {
        LinkedList<Integer> linkedList = appointmentServicerMapper.getASCList(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<AppointmentServicerEntity> list = new ArrayList<>();
        for (Integer ID : linkedList) {
            AppointmentServicerEntity appointmentServicerEntity = new AppointmentServicerEntity();
            appointmentServicerEntity.ID = ID;
            appointmentServicerEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(appointmentServicerEntity);
        }
        appointmentServicerMapper.updateSequence(list);
        return BaseOutput.success();
    }

    public BaseOutput appointmentServicerByEntity() {
        return BaseOutputForm.success(appointmentServicerMapper.appointmentServicerByEntity(SystemUserUtil.getSystemEntityID()));
    }
}
