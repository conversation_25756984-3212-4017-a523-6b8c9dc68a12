package com.zhelian.service.ibeauty.treatTimeCardCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.TreatTimeCardProjectCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.TreatTimeCardProjectCommissionEntity;
import com.zhelian.model.ibeauty.form.input.TreatCardCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatCardProjectCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardProjectsCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.TreatCardCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatTimeCardProjectCommissionService {

    @Autowired
    TreatTimeCardProjectCommissionMapper treatTimeCardProjectCommissionMapper;


    public BaseOutput allTreatTimeCardProjectCommission(TreatCardCommissionQueryForm form) {
        List<TreatCardCommissionOutputForm> list = treatTimeCardProjectCommissionMapper.allTreatTimeCardProjectCommission(form.EntityID, form.CardID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatTimeCardProjectCommission(TreatCardProjectsCommissionAddForm form) {

        treatTimeCardProjectCommissionMapper.deleteTreatTimeCardProjectCommissionByEntityIDAndCard(form.EntityID, form.CardID);
        List<TreatTimeCardProjectCommissionEntity> list = new ArrayList<>();
        if (form.Project != null && form.Project.size() > 0) {
            for (TreatCardProjectCommissionAddForm treatCardCommissionAddForm : form.Project) {
                if (treatCardCommissionAddForm.PayRate != null || treatCardCommissionAddForm.PayFixed != null ||
                        treatCardCommissionAddForm.CardRate != null || treatCardCommissionAddForm.CardFixed != null ||
                        treatCardCommissionAddForm.CardLargessRate != null || treatCardCommissionAddForm.CardLargessFixed != null ||
                        treatCardCommissionAddForm.LargessRate != null || treatCardCommissionAddForm.LargessFixed != null ||
                        treatCardCommissionAddForm.SpecialBenefit != null) {
                    TreatTimeCardProjectCommissionEntity treatTimeCardCommissionEntity = new TreatTimeCardProjectCommissionEntity();
                    treatTimeCardCommissionEntity.EntityID = form.EntityID;
                    treatTimeCardCommissionEntity.TimeCardID = form.CardID;
                    treatTimeCardCommissionEntity.ProjectID = treatCardCommissionAddForm.ProjectID;
                    treatTimeCardCommissionEntity.PayRate = treatCardCommissionAddForm.PayRate;
                    treatTimeCardCommissionEntity.PayFixed = treatCardCommissionAddForm.PayFixed;
                    treatTimeCardCommissionEntity.CardRate = treatCardCommissionAddForm.CardRate;
                    treatTimeCardCommissionEntity.CardFixed = treatCardCommissionAddForm.CardFixed;
                    treatTimeCardCommissionEntity.CardLargessRate = treatCardCommissionAddForm.CardLargessRate;
                    treatTimeCardCommissionEntity.CardLargessFixed = treatCardCommissionAddForm.CardLargessFixed;
                    treatTimeCardCommissionEntity.LargessFixed = treatCardCommissionAddForm.LargessFixed;
                    treatTimeCardCommissionEntity.LargessRate = treatCardCommissionAddForm.LargessRate;
                    treatTimeCardCommissionEntity.SpecialBenefit = treatCardCommissionAddForm.SpecialBenefit;
                    list.add(treatTimeCardCommissionEntity);
                }
            }
            if (list != null && list.size() > 0) {
                List<List<TreatTimeCardProjectCommissionEntity>> subs = Lists.partition(list, 20);
                for (List<TreatTimeCardProjectCommissionEntity> entityList : subs) {
                    treatTimeCardProjectCommissionMapper.insertTreatTimeCardProjectCommission(entityList);
                }
            }
        }
        return BaseOutput.success();
    }

}