package com.zhelian.service.ibeauty.PriceAdjustment;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.ibeauty.ProjectPriceAdjustmentMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.ProjectPriceAdjustmentEntity;
import com.zhelian.model.ibeauty.form.input.ProjectPriceAdjustmentAddForm;
import com.zhelian.model.ibeauty.form.input.ProjectPriceAdjustmentDeleteForm;
import com.zhelian.model.ibeauty.form.input.ProjectPriceAdjustmentQueryForm;
import com.zhelian.model.ibeauty.form.output.ProjectPriceAdjustmentOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class ProjectPriceAdjustmentService {

    @Autowired
    ProjectPriceAdjustmentMapper projectPriceAdjustmentMapper;

    public BaseOutput projectPriceAdjustmentList(ProjectPriceAdjustmentQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<ProjectPriceAdjustmentOutputForm> list = projectPriceAdjustmentMapper.projectPriceAdjustmentList(form.ProjectName, form.EntityName);
        return BasePageInfo.success(list);
    }

    public BaseOutput createProjectPriceAdjustment(ProjectPriceAdjustmentAddForm form) {

        Integer count = projectPriceAdjustmentMapper.projectPriceAdjustmentCount(form.EntityID, form.ProjectID);
        if (count > 0)
            return BaseOutput.failed("项目在该组织单位已经设置调整价格");

        ProjectPriceAdjustmentEntity projectPriceAdjustmentEntity = new ProjectPriceAdjustmentEntity();
        projectPriceAdjustmentEntity.EntityID = form.EntityID;
        projectPriceAdjustmentEntity.Price = form.Price;
        projectPriceAdjustmentEntity.ProjectID = form.ProjectID;
        projectPriceAdjustmentMapper.insertProjectPriceAdjustmentEntity(projectPriceAdjustmentEntity);
        return BaseOutputForm.success();
    }


    public BaseOutput updateProjectPriceAdjustmentEntity(ProjectPriceAdjustmentAddForm form) {

        ProjectPriceAdjustmentEntity projectPriceAdjustmentEntity = new ProjectPriceAdjustmentEntity();
        projectPriceAdjustmentEntity.EntityID = form.EntityID;
        projectPriceAdjustmentEntity.Price = form.Price;
        projectPriceAdjustmentEntity.ProjectID = form.ProjectID;
        projectPriceAdjustmentMapper.updateProjectPriceAdjustmentEntity(projectPriceAdjustmentEntity);

        return BaseOutput.success();
    }

    public BaseOutput deleteProjectPriceAdjustmentEntity(ProjectPriceAdjustmentDeleteForm form) {

        projectPriceAdjustmentMapper.deleteProjectPriceAdjustmentEntity(form.EntityID, form.ProjectID);
        return BaseOutput.success();
    }
}