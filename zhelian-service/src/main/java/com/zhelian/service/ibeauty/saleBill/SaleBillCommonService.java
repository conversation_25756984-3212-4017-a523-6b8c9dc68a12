package com.zhelian.service.ibeauty.saleBill;

import com.zhelian.core.utils.DateTimeUtil;
import com.zhelian.core.utils.enumeration.WeiXinOfficialAccountMessage;
import com.zhelian.mapper.ibeauty.SaleBillMapper;
import com.zhelian.mapper.ibeauty.SealingAccountMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.RefundTreatBillEntity;
import com.zhelian.model.ibeauty.entity.SaleBillEntity;
import com.zhelian.model.ibeauty.form.input.SaleBillAddForm;
import com.zhelian.model.ibeauty.form.input.SaleBillInfoQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleBillRechargeSavingCardAddForm;
import com.zhelian.model.ibeauty.form.output.FastBillOutputForm;
import com.zhelian.service.ibeauty.SealingAccount.SealingAccountService;
import com.zhelian.service.weixin.WeiXinMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SaleBillCommonService {
    @Autowired
    SaleBillService saleBillService;
    @Autowired
    SaleBillMapper saleBillMapper;
    @Autowired
    SealingAccountService sealingAccountService;

    @Autowired
    WeiXinMessageService weiXinMessageService;

    public BaseOutput create(SaleBillAddForm form, Boolean IsFastBill, String channel) throws Exception {
        Boolean replacementOrderRestriction = sealingAccountService.getReplacementOrderRestriction(form.BillDate);
        if(replacementOrderRestriction){
            return BaseOutput.failed("补单时间处于关账时间内，请选择其他时间");
        }
        FastBillOutputForm fastBillOutputForm = saleBillService.createSaleBill(form, IsFastBill, channel);

        weiXinMessageService.setSaleBillWxMessage(fastBillOutputForm.SaleBillID, WeiXinOfficialAccountMessage.SaleNotice);

        if (IsFastBill) {
            weiXinMessageService.setTreatBillWxMessage(fastBillOutputForm.TreatBillID, WeiXinOfficialAccountMessage.TreatNotice);
            return BaseOutputForm.success(fastBillOutputForm);
        } else {
            return BaseOutputForm.success(fastBillOutputForm.SaleBillID);
        }

    }


    public BaseOutput cancelBill(SaleBillInfoQueryForm form) {
        SaleBillEntity saleBill = saleBillMapper.getSaleBill(form.SaleBillID);
        Boolean replacementOrderRestriction = sealingAccountService.getCancelOrderRestriction(DateTimeUtil.toDate(saleBill.BillDate));
        if(replacementOrderRestriction){
            return BaseOutput.failed("订单取消的时间处于关账时间内，请选择其他订单");
        }

        if (saleBill.BillStatus.equals("30"))
            return BaseOutput.failed("该订单已取消");

        saleBillService.cancel(form, saleBill);

        if (saleBill.BillType.equals("10")) {//销售单
            weiXinMessageService.setCancelSaleBillWxMessage(form.SaleBillID, WeiXinOfficialAccountMessage.SaleCancelNotice);
        } else if (saleBill.BillType.equals("30")) {//欠款单
            weiXinMessageService.setCancelSaleBillWxMessage(form.SaleBillID, WeiXinOfficialAccountMessage.ArrearsCancelNotice);
        } else if (saleBill.BillType.equals("40")) {//充值储值卡单
            weiXinMessageService.setCancelSaleBillWxMessage(form.SaleBillID, WeiXinOfficialAccountMessage.RechargeCancelNotice);
        }

        return BaseOutput.success();
    }

    public BaseOutput recharge(SaleBillRechargeSavingCardAddForm form, String channel) throws Exception {
        Boolean replacementOrderRestriction = sealingAccountService.getReplacementOrderRestriction(form.BillDate);
        if(replacementOrderRestriction){
            return BaseOutput.failed("补单时间处于关账时间内，请选择其他时间");
        }

        String saleBillID = saleBillService.rechargeBill(form, channel);
        //消息提醒
        weiXinMessageService.setSaleBillWxMessage(saleBillID, WeiXinOfficialAccountMessage.RechargeNotice);

        return BaseOutputForm.success(saleBillID);
    }
}
