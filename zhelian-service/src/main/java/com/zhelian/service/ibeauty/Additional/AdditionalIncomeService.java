package com.zhelian.service.ibeauty.Additional;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.utils.DateTimeUtil;
import com.zhelian.core.utils.OSSClientUtil;
import com.zhelian.mapper.ibeauty.AdditionalIncomeMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.AdditionalIncomeAttachmentEntity;
import com.zhelian.model.ibeauty.entity.AdditionalIncomeEntity;
import com.zhelian.model.ibeauty.form.input.AdditionalIncomeAddForm;
import com.zhelian.model.ibeauty.form.input.AdditionalIncomeQueryForm;
import com.zhelian.model.ibeauty.form.input.AdditionalIncomeUpdateForm;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingUpdateForm;
import com.zhelian.model.ibeauty.form.output.AdditionalIncomeOutputEasyExcelForm;
import com.zhelian.model.ibeauty.form.output.AdditionalIncomeSumOutputForm;
import com.zhelian.model.ibeauty.form.output.AdditionalIncomeTotalOutputForm;
import com.zhelian.service.ibeauty.SealingAccount.SealingAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.ZoneId;
import java.util.List;

@Service
public class AdditionalIncomeService {

    @Autowired
    AdditionalIncomeMapper additionalIncomeMapper;
    @Autowired
    SealingAccountService sealingAccountService;

    public BaseOutput additionalIncomeList(AdditionalIncomeQueryForm form) {

        AdditionalIncomeTotalOutputForm additionalSpendingTotalOutputForm = new AdditionalIncomeTotalOutputForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        additionalSpendingTotalOutputForm.additionalIncomeOutputFormBasePageInfo = BasePageInfo.success(additionalIncomeMapper.additionalIncomeList(SystemUserUtil.getSystemUserID(), form.IncomeSource, form.EntityID, form.IncomeCategoryID, form.StartDate, form.EndDate));
        additionalSpendingTotalOutputForm.additionalIncomeSumOutputForm = additionalIncomeMapper.additionalIncomeSum(SystemUserUtil.getSystemUserID(), form.IncomeSource, form.EntityID, form.IncomeCategoryID, form.StartDate, form.EndDate);
        return BaseOutputForm.success(additionalSpendingTotalOutputForm);
    }

    @Transactional
    public BaseOutput insertAdditionalIncome(AdditionalIncomeAddForm form) {
        Boolean modifyIncomeAndSpendingRestriction = sealingAccountService.getModifyIncomeAndSpendingRestriction(DateTimeUtil.toDate(form.InComeDate));
        if(modifyIncomeAndSpendingRestriction){
            return BaseOutput.failed("付款时间在关账时间内，请选择其他时间");
        }
        AdditionalIncomeEntity additionalIncomeEntity = new AdditionalIncomeEntity();
        additionalIncomeEntity.IncomeCategoryID = form.IncomeCategoryID;
        additionalIncomeEntity.EntityID = form.EntityID;
        additionalIncomeEntity.InComeDate = form.InComeDate;
        additionalIncomeEntity.PaymentsID = form.PaymentsID;
        additionalIncomeEntity.Amount = form.Amount;
        additionalIncomeEntity.IncomeSource = form.IncomeSource;
        additionalIncomeEntity.Content = form.Content;
        additionalIncomeEntity.EmployeeID = SystemUserUtil.getSystemUserID();
        additionalIncomeMapper.insertAdditionalIncome(additionalIncomeEntity);

        if (form.ImageList.size() > 0) {
            for (int a = 0; a < form.ImageList.size(); a++) {
                additionalIncomeMapper.addAttachment(additionalIncomeEntity.ID, form.ImageList.get(a).AttachmentURL, form.ImageList.get(a).AttachmentType);
            }
        }
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateAdditionalIncome(AdditionalIncomeUpdateForm form) {
        AdditionalIncomeEntity additionalIncomeEntity= additionalIncomeMapper.getAdditionalIncome(form.ID);
        Boolean modifyIncomeAndSpendingRestriction = sealingAccountService.getModifyIncomeAndSpendingRestriction(DateTimeUtil.toDate(additionalIncomeEntity.InComeDate));
        if(modifyIncomeAndSpendingRestriction &&  !form.InComeDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().equals( additionalIncomeEntity.InComeDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())){
            return BaseOutput.failed("付款时间在关账时间内，请选择其他时间");
        }
        Boolean incomeAndSpendingRestriction = sealingAccountService.getModifyIncomeAndSpendingRestriction(DateTimeUtil.toDate(form.InComeDate));
        if(incomeAndSpendingRestriction &&  !form.InComeDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().equals( additionalIncomeEntity.InComeDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())){
            return BaseOutput.failed("付款时间在关账时间内，请选择其他时间");
        }
        additionalIncomeEntity.ID = form.ID;
        additionalIncomeEntity.IncomeCategoryID = form.IncomeCategoryID;
        additionalIncomeEntity.EntityID = form.EntityID;
        additionalIncomeEntity.InComeDate = form.InComeDate;
        additionalIncomeEntity.PaymentsID = form.PaymentsID;
        additionalIncomeEntity.Amount = form.Amount;
        additionalIncomeEntity.IncomeSource = form.IncomeSource;
        additionalIncomeEntity.Content = form.Content;
        additionalIncomeMapper.updateAdditionalIncome(additionalIncomeEntity);

        List<AdditionalIncomeAttachmentEntity> attachmentEntityList = additionalIncomeMapper.getAttachment(form.ID);
        if (attachmentEntityList.size() > 0) {

            additionalIncomeMapper.deleteAttachment(form.ID);
        }

        if (form.ImageList.size() > 0) {
            for (int a = 0; a < form.ImageList.size(); a++) {
                additionalIncomeMapper.addAttachment(additionalIncomeEntity.ID, form.ImageList.get(a).AttachmentURL, form.ImageList.get(a).AttachmentType);
            }
        }
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput deleteAdditionalIncomeByID(AdditionalSpendingUpdateForm form) {
        AdditionalIncomeEntity additionalIncomeEntity= additionalIncomeMapper.getAdditionalIncome(form.ID);
        Boolean modifyIncomeAndSpendingRestriction = sealingAccountService.getModifyIncomeAndSpendingRestriction(DateTimeUtil.toDate(additionalIncomeEntity.InComeDate));
        if(modifyIncomeAndSpendingRestriction){
            return BaseOutput.failed("付款时间在关账时间内，请选择其他订单");
        }


        additionalIncomeMapper.deleteAdditionalIncomeByID(form.ID);
        List<AdditionalIncomeAttachmentEntity> attachmentEntityList = additionalIncomeMapper.getAttachment(form.ID);
        if (attachmentEntityList.size() > 0) {
            for (AdditionalIncomeAttachmentEntity urlForm : attachmentEntityList) {
                String url = urlForm.AttachmentURL;
                String[] split = url.split("/");
                OSSClientUtil.deleteObject(split[split.length - 1]);
            }
            additionalIncomeMapper.deleteAttachment(form.ID);
        }
        return BaseOutput.success();
    }

    public BaseOutput getAttachment(AdditionalSpendingUpdateForm form) {
        List<AdditionalIncomeAttachmentEntity> attachmentEntityList = additionalIncomeMapper.getAttachment(form.ID);
        return BaseOutputForm.success(attachmentEntityList);
    }

    public BaseOutput easyExcelAdditionalIncomeList(AdditionalIncomeQueryForm form, HttpServletResponse response) {
        List<AdditionalIncomeOutputEasyExcelForm> list = additionalIncomeMapper.easyExcelAdditionalIncomeList(SystemUserUtil.getSystemUserID(), form.IncomeSource, form.EntityID, form.IncomeCategoryID, form.StartDate, form.EndDate);
        AdditionalIncomeSumOutputForm additionalIncomeSum = additionalIncomeMapper.additionalIncomeSum(SystemUserUtil.getSystemUserID(), form.IncomeSource, form.EntityID, form.IncomeCategoryID, form.StartDate, form.EndDate);
        if (additionalIncomeSum != null) {
            AdditionalIncomeOutputEasyExcelForm additionalIncomeOutputEasyExcelForm = new AdditionalIncomeOutputEasyExcelForm();
            additionalIncomeOutputEasyExcelForm.categoryName = "合计";
            additionalIncomeOutputEasyExcelForm.paymentsName = "";
            additionalIncomeOutputEasyExcelForm.amount = additionalIncomeSum.TotalAmount.doubleValue();
            additionalIncomeOutputEasyExcelForm.incomeSource = "";
            additionalIncomeOutputEasyExcelForm.inComeDate = "";
            additionalIncomeOutputEasyExcelForm.empName = "";
            additionalIncomeOutputEasyExcelForm.createdOn = "";
            additionalIncomeOutputEasyExcelForm.entityName = "";
            additionalIncomeOutputEasyExcelForm.content = "";
            list.add(additionalIncomeOutputEasyExcelForm);
        }
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("额外收入管理", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), AdditionalIncomeOutputEasyExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("额外收入管理").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }
}
