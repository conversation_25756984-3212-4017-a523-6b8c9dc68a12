package com.zhelian.service.ibeauty.product;

import com.github.pagehelper.PageHelper;
import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.ProductSaleMapper;
import com.zhelian.mapper.psi.ProductCategoryMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.TbProductEntity;
import com.zhelian.model.ibeauty.entity.TbProductImageEntity;
import com.zhelian.model.ibeauty.entity.TbProductSaleEntity;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.ibeauty.form.output.AllProductOutputForm;
import com.zhelian.model.ibeauty.form.output.ProductDetailsOutputForm;
import com.zhelian.model.ibeauty.form.output.ProductOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * @Description:
 * @Param:
 * @returns:
 * @Author: 86158
 * @Date: 2020/3/21 9:34
 */
@Service
public class ProductSaleService {

    @Autowired
    ProductSaleMapper productMapper;

    @Autowired
    ProductCategoryMapper productCategoryMapper;

    public BaseOutput productList(ProductQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<ProductOutputForm> list = productMapper.productList(form.Name, form.PCategoryID, form.IsAllowSell, form.ProductBrandID);
        return BasePageInfo.success(list);
    }

    public BaseOutput getProductDetails(ProductSaleEntityQueryForm form) {
        ProductDetailsOutputForm productDetails = productMapper.getProductDetails(form.ID);
        return BaseOutputForm.success(productDetails);
    }

    public BaseOutput productSaleEntity(ProductSaleEntityQueryForm form) {
        return BaseOutputForm.success(productMapper.productSaleEntity(form.ID));
    }

    public BaseOutput productModifyPriceEntity(ProductSaleEntityQueryForm form) {
        return BaseOutputForm.success(productMapper.productModifyPriceEntity(form.ID));
    }


    @Transactional
    public BaseOutput update(ProductUpdateForm form) {

        TbProductEntity tbProductEntity = new TbProductEntity();
        tbProductEntity.IsAllowLargess = form.IsAllowLargess;
        tbProductEntity.IsAllowSell = form.IsAllowSell;
        tbProductEntity.Price = form.Price;
        tbProductEntity.ProductID = form.ID;
        tbProductEntity.OriginalText = form.OriginalText;
        tbProductEntity.Memo = form.Memo;

        productMapper.deleteProduct(form.ID);
        productMapper.insertProduct(tbProductEntity);
        productMapper.deleteSaleEntity(form.ID);
        productMapper.deleteModifyPriceEntity(form.ID);

        if (form.EntityID != null && form.EntityID.length > 0) {
            List<TbProductSaleEntity> list = new ArrayList<>();
            for (Integer ID : form.EntityID) {
                TbProductSaleEntity tbProductSaleEntity = new TbProductSaleEntity();
                tbProductSaleEntity.EntityID = ID;
                tbProductSaleEntity.ProductID = form.ID;
                list.add(tbProductSaleEntity);
            }
            productMapper.updateSaleEntity(list);
        }

        if (form.ModifyPriceEntity != null && form.ModifyPriceEntity.length > 0) {
            List<TbProductSaleEntity> list = new ArrayList<>();
            for (Integer ID : form.ModifyPriceEntity) {
                TbProductSaleEntity tbProductSaleEntity = new TbProductSaleEntity();
                tbProductSaleEntity.EntityID = ID;
                tbProductSaleEntity.ProductID = form.ID;
                list.add(tbProductSaleEntity);
            }
            productMapper.updateModifyPriceEntity(list);
        }

        List<GoodsImageUrlForm> productImageList = productMapper.getProductImageList(form.ID);
        if (productImageList.size() > 0) {
            productMapper.deleteProductImageByID(form.ID);
        }

        if (form.ImageList.size() > 0) {
            for (int a = 0; a < form.ImageList.size(); a++) {
                productMapper.insertProductImageURL(form.ID, form.ImageList.get(a).ImageUrl, a + 1);
            }
        }

        return BaseOutput.success();
    }

    public BaseOutput allProduct() {
        List<AllProductOutputForm> list = productMapper.allProduct();
        return BaseOutputForm.success(list);
    }

    public BaseOutput move(GoodsMoveForm form) {

        LinkedList<Integer> linkedList = productMapper.getASCList(form.MoveID, form.ID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<TbProductImageEntity> list = new ArrayList<>();
        for (Integer ID : linkedList) {
            TbProductImageEntity productImageEntity = new TbProductImageEntity();
            productImageEntity.ID = ID;
            productImageEntity.ProductID = form.ID;
            productImageEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(productImageEntity);
        }

        productMapper.updateSequence(list);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput bitchUpdateProductSaleEntity(GoodsBitchUpdateForm form) {
        productMapper.deleteBitchSaleEntity(form.GoodsID);
        List<TbProductSaleEntity> list = new ArrayList<>();
        for (Integer goodsID : form.GoodsID) {
            for (Integer entityID : form.EntityID) {
                TbProductSaleEntity tbProductSaleEntity = new TbProductSaleEntity();
                tbProductSaleEntity.ProductID = goodsID;
                tbProductSaleEntity.EntityID = entityID;
                list.add(tbProductSaleEntity);
            }
        }

        if (list.size() > 0)
            productMapper.updateSaleEntity(list);

        return BaseOutput.success();
    }
}