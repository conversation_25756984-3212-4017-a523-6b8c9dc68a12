package com.zhelian.service.ibeauty.saleSavingCardCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.SalePackageCardSavingCardSchemeHandlerCommissionMapper;
import com.zhelian.mapper.ibeauty.SaleSavingCardSchemeHandlerCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SalePackageCardSavingCardSchemeHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SalePackageCardSavingCardSchemeHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleSavingCardSchemeHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleSavingCardSchemeHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardSchemeHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardSchemeHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardSchemeHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardsSchemeHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.SaleSavingCardSchemeHandlerCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleSavingCardSchemeHandlerCommissionService {

    @Autowired
    SaleSavingCardSchemeHandlerCommissionMapper saleSavingCardSchemeHandlerCommissionMapper;

    @Autowired
    SalePackageCardSavingCardSchemeHandlerCommissionMapper salePackageCardSavingCardSchemeHandlerCommissionMapper;

    public BaseOutput allSaleSavingCardSchemeHandlerCommission(SaleSavingCardSchemeHandlerCommissionQueryForm form) {
        List<SaleSavingCardSchemeHandlerCommissionOutputForm> list = saleSavingCardSchemeHandlerCommissionMapper.allSaleSavingCardSchemeHandlerCommission(form.EntityID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSalePackageCardSavingCardSchemeHandlerCommission(SaleSavingCardSchemeHandlerCommissionQueryForm form) {
        List<SaleSavingCardSchemeHandlerCommissionOutputForm> list = salePackageCardSavingCardSchemeHandlerCommissionMapper.allSalePackageCardSavingCardSchemeHandlerCommission(form.EntityID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleSavingCardSchemeHandlerCommission(SaleSavingCardsSchemeHandlerCommissionAddForm form) {

        saleSavingCardSchemeHandlerCommissionMapper.deleteSaleSavingCardSchemeHandler(form.EntityID);
        saleSavingCardSchemeHandlerCommissionMapper.deleteSaleSavingCardSchemeHandlerJobType(form.EntityID);
        List<SaleSavingCardSchemeHandlerCommissionEntity> listSaleSavingCardSchemeHandlerCommissionEntity = new ArrayList<>();
        List<SaleSavingCardSchemeHandlerJobTypeCommissionEntity> listSaleSavingCardSchemeHandlerJobTypeCommissionEntity = new ArrayList<>();
        if (form.SavingCardHandler != null && form.SavingCardHandler.size() > 0) {
            for (SaleSavingCardSchemeHandlerCommissionAddForm saleSavingCardSchemeHandlerAddForm : form.SavingCardHandler) {
                if (saleSavingCardSchemeHandlerAddForm.PayFixed != null || saleSavingCardSchemeHandlerAddForm.PayRate != null ||
                        saleSavingCardSchemeHandlerAddForm.SpecialBenefit != null) {
                    SaleSavingCardSchemeHandlerCommissionEntity saleSavingCardSchemeHandlerCommissionEntity = new SaleSavingCardSchemeHandlerCommissionEntity();
                    saleSavingCardSchemeHandlerCommissionEntity.EntityID = form.EntityID;
                    saleSavingCardSchemeHandlerCommissionEntity.SavingCardSaleHandlerID = saleSavingCardSchemeHandlerAddForm.SaleHandlerID;
                    saleSavingCardSchemeHandlerCommissionEntity.PayFixed = saleSavingCardSchemeHandlerAddForm.PayFixed;
                    saleSavingCardSchemeHandlerCommissionEntity.PayRate = saleSavingCardSchemeHandlerAddForm.PayRate;
                    saleSavingCardSchemeHandlerCommissionEntity.SpecialBenefit = saleSavingCardSchemeHandlerAddForm.SpecialBenefit;
                    listSaleSavingCardSchemeHandlerCommissionEntity.add(saleSavingCardSchemeHandlerCommissionEntity);
                }
                if (saleSavingCardSchemeHandlerAddForm.HandlerJobTypeCommission != null && saleSavingCardSchemeHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleSavingCardSchemeHandlerJobTypeCommissionAddForm saleSavingCardSchemeHandlerJobTypeCommissionAddForm : saleSavingCardSchemeHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleSavingCardSchemeHandlerJobTypeCommissionAddForm.PayFixed != null || saleSavingCardSchemeHandlerJobTypeCommissionAddForm.PayRate != null ||
                                saleSavingCardSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SaleSavingCardSchemeHandlerJobTypeCommissionEntity saleSavingCardSchemeHandlerJobTypeCommissionEntity = new SaleSavingCardSchemeHandlerJobTypeCommissionEntity();
                            saleSavingCardSchemeHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            saleSavingCardSchemeHandlerJobTypeCommissionEntity.SavingCardSaleHandlerID = saleSavingCardSchemeHandlerAddForm.SaleHandlerID;
                            saleSavingCardSchemeHandlerJobTypeCommissionEntity.JobTypeID = saleSavingCardSchemeHandlerJobTypeCommissionAddForm.JobTypeID;
                            saleSavingCardSchemeHandlerJobTypeCommissionEntity.PayFixed = saleSavingCardSchemeHandlerJobTypeCommissionAddForm.PayFixed;
                            saleSavingCardSchemeHandlerJobTypeCommissionEntity.PayRate = saleSavingCardSchemeHandlerJobTypeCommissionAddForm.PayRate;
                            saleSavingCardSchemeHandlerJobTypeCommissionEntity.SpecialBenefit = saleSavingCardSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            listSaleSavingCardSchemeHandlerJobTypeCommissionEntity.add(saleSavingCardSchemeHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
        }

        salePackageCardSavingCardSchemeHandlerCommissionMapper.deleteSalePackageCardSavingCardSchemeHandler(form.EntityID);
        salePackageCardSavingCardSchemeHandlerCommissionMapper.deleteSalePackageCardSavingCardSchemeHandlerJobType(form.EntityID);
        List<SalePackageCardSavingCardSchemeHandlerCommissionEntity> listSalePackageCardSavingCardSchemeHandlerCommissionEntity = new ArrayList<>();
        List<SalePackageCardSavingCardSchemeHandlerJobTypeCommissionEntity> listSalePackageCardSavingCardSchemeHandlerJobTypeCommissionEntity = new ArrayList<>();
        if (form.PackageCardSavingCardHandler != null && form.PackageCardSavingCardHandler.size() > 0) {
            for (SaleSavingCardSchemeHandlerCommissionAddForm saleSavingCardSchemeHandlerAddForm : form.PackageCardSavingCardHandler) {
                if (saleSavingCardSchemeHandlerAddForm.PayFixed != null || saleSavingCardSchemeHandlerAddForm.PayRate != null ||
                        saleSavingCardSchemeHandlerAddForm.SpecialBenefit != null) {
                    SalePackageCardSavingCardSchemeHandlerCommissionEntity salePackageCardSavingCardSchemeHandlerCommissionEntity = new SalePackageCardSavingCardSchemeHandlerCommissionEntity();
                    salePackageCardSavingCardSchemeHandlerCommissionEntity.EntityID = form.EntityID;
                    salePackageCardSavingCardSchemeHandlerCommissionEntity.PackageCardSaleHandlerID = saleSavingCardSchemeHandlerAddForm.SaleHandlerID;
                    salePackageCardSavingCardSchemeHandlerCommissionEntity.PayFixed = saleSavingCardSchemeHandlerAddForm.PayFixed;
                    salePackageCardSavingCardSchemeHandlerCommissionEntity.PayRate = saleSavingCardSchemeHandlerAddForm.PayRate;
                    salePackageCardSavingCardSchemeHandlerCommissionEntity.SpecialBenefit = saleSavingCardSchemeHandlerAddForm.SpecialBenefit;
                    listSalePackageCardSavingCardSchemeHandlerCommissionEntity.add(salePackageCardSavingCardSchemeHandlerCommissionEntity);
                }
                if (saleSavingCardSchemeHandlerAddForm.HandlerJobTypeCommission != null && saleSavingCardSchemeHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleSavingCardSchemeHandlerJobTypeCommissionAddForm saleSavingCardSchemeHandlerJobTypeCommissionAddForm : saleSavingCardSchemeHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleSavingCardSchemeHandlerJobTypeCommissionAddForm.PayFixed != null || saleSavingCardSchemeHandlerJobTypeCommissionAddForm.PayRate != null ||
                                saleSavingCardSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SalePackageCardSavingCardSchemeHandlerJobTypeCommissionEntity salePackageCardSavingCardSchemeHandlerJobTypeCommissionEntity = new SalePackageCardSavingCardSchemeHandlerJobTypeCommissionEntity();
                            salePackageCardSavingCardSchemeHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            salePackageCardSavingCardSchemeHandlerJobTypeCommissionEntity.PackageCardSaleHandlerID = saleSavingCardSchemeHandlerAddForm.SaleHandlerID;
                            salePackageCardSavingCardSchemeHandlerJobTypeCommissionEntity.JobTypeID = saleSavingCardSchemeHandlerJobTypeCommissionAddForm.JobTypeID;
                            salePackageCardSavingCardSchemeHandlerJobTypeCommissionEntity.PayFixed = saleSavingCardSchemeHandlerJobTypeCommissionAddForm.PayFixed;
                            salePackageCardSavingCardSchemeHandlerJobTypeCommissionEntity.PayRate = saleSavingCardSchemeHandlerJobTypeCommissionAddForm.PayRate;
                            salePackageCardSavingCardSchemeHandlerJobTypeCommissionEntity.SpecialBenefit = saleSavingCardSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            listSalePackageCardSavingCardSchemeHandlerJobTypeCommissionEntity.add(salePackageCardSavingCardSchemeHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
        }
        if (listSaleSavingCardSchemeHandlerCommissionEntity != null && listSaleSavingCardSchemeHandlerCommissionEntity.size() > 0) {
            List<List<SaleSavingCardSchemeHandlerCommissionEntity>> subs = Lists.partition(listSaleSavingCardSchemeHandlerCommissionEntity, 20);
            for (List<SaleSavingCardSchemeHandlerCommissionEntity> entityList : subs) {
                saleSavingCardSchemeHandlerCommissionMapper.updateSaleSavingCardSchemeHandlerCommission(entityList);
            }
        }
        if (listSaleSavingCardSchemeHandlerJobTypeCommissionEntity != null && listSaleSavingCardSchemeHandlerJobTypeCommissionEntity.size() > 0) {
            List<List<SaleSavingCardSchemeHandlerJobTypeCommissionEntity>> subs = Lists.partition(listSaleSavingCardSchemeHandlerJobTypeCommissionEntity, 20);
            for (List<SaleSavingCardSchemeHandlerJobTypeCommissionEntity> entityList : subs) {
                saleSavingCardSchemeHandlerCommissionMapper.insertSaleSavingCardSchemeHandlerJobTypeCommission(entityList);
            }
        }
        if (listSalePackageCardSavingCardSchemeHandlerCommissionEntity != null && listSalePackageCardSavingCardSchemeHandlerCommissionEntity.size() > 0) {
            List<List<SalePackageCardSavingCardSchemeHandlerCommissionEntity>> subs = Lists.partition(listSalePackageCardSavingCardSchemeHandlerCommissionEntity, 20);
            for (List<SalePackageCardSavingCardSchemeHandlerCommissionEntity> entityList : subs) {
                salePackageCardSavingCardSchemeHandlerCommissionMapper.updateSalePackageCardSavingCardSchemeHandlerCommission(entityList);
            }
        }
        if (listSalePackageCardSavingCardSchemeHandlerJobTypeCommissionEntity != null && listSalePackageCardSavingCardSchemeHandlerJobTypeCommissionEntity.size() > 0) {
            List<List<SalePackageCardSavingCardSchemeHandlerJobTypeCommissionEntity>> subs = Lists.partition(listSalePackageCardSavingCardSchemeHandlerJobTypeCommissionEntity, 20);
            for (List<SalePackageCardSavingCardSchemeHandlerJobTypeCommissionEntity> entityList : subs) {
                salePackageCardSavingCardSchemeHandlerCommissionMapper.insertSalePackageCardSavingCardSchemeHandlerJobTypeCommission(entityList);
            }
        }

        return BaseOutput.success();
    }
}