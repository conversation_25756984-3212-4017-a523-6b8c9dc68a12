package com.zhelian.service.ibeauty.saleHandler;


import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.GeneralCardSaleHandlerMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.GeneralCardSaleHandlerEntity;
import com.zhelian.model.ibeauty.entity.GeneralCardSaleHandlerJobTypeEntity;
import com.zhelian.model.ibeauty.form.input.SaleHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerMoveForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerUpdateForm;
import com.zhelian.model.ibeauty.form.output.SaleHandlerOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * @Description:
 * @Param: No such property: code for class: Script1
 * @returns:
 * @Author: xuehao
 * @Date: 2020/3/31 9:01
 */
@Service
public class GeneralCardSaleHandlerService {

    @Autowired
    GeneralCardSaleHandlerMapper generalCardSaleHandlerMapper;


    public BaseOutput allGeneralCardSaleHandler(SaleHandlerQueryForm form) {

        List<SaleHandlerOutputForm> list = generalCardSaleHandlerMapper.allGeneralCardSaleHandler(form.Name, form.EntityID, form.Active);
        return BaseOutputForm.success(list);

    }

    @Transactional
    public BaseOutput createGeneralCardSaleHandler(SaleHandlerAddForm form) {
        Integer Sequence = generalCardSaleHandlerMapper.getMaxSequence();
        GeneralCardSaleHandlerEntity generalCardSaleHandlerEntity = new GeneralCardSaleHandlerEntity();
        generalCardSaleHandlerEntity.Name = form.Name;
        generalCardSaleHandlerEntity.EntityID = form.EntityID;
        generalCardSaleHandlerEntity.Sequence = Sequence != null ? Sequence + 1 : 1;

        generalCardSaleHandlerMapper.createGeneralCardSaleHandler(generalCardSaleHandlerEntity);

        insertSaleHandlerJobType(form.JobType, generalCardSaleHandlerEntity.ID);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateGeneralCardSaleHandler(SaleHandlerUpdateForm form) {

        GeneralCardSaleHandlerEntity generalCardSaleHandlerEntity = new GeneralCardSaleHandlerEntity();
        generalCardSaleHandlerEntity.Name = form.Name;
        generalCardSaleHandlerEntity.ID = form.ID;
        generalCardSaleHandlerEntity.EntityID = form.EntityID;
        generalCardSaleHandlerEntity.Active = form.Active;

        generalCardSaleHandlerMapper.updateGeneralCardSaleHandler(generalCardSaleHandlerEntity);

        generalCardSaleHandlerMapper.deleteSaleHandlerJobType(form.ID);

        insertSaleHandlerJobType(form.JobType, form.ID);

        return BaseOutput.success();
    }


    public void insertSaleHandlerJobType(Integer[] JobType, Integer ID) {
        List<GeneralCardSaleHandlerJobTypeEntity> list = new ArrayList<>();
        for (Integer typeID : JobType) {
            GeneralCardSaleHandlerJobTypeEntity generalCardSaleHandlerJobTypeEntity = new GeneralCardSaleHandlerJobTypeEntity();
            generalCardSaleHandlerJobTypeEntity.GeneralCardSaleHandlerID = ID;
            generalCardSaleHandlerJobTypeEntity.JobTypeID = typeID;
            list.add(generalCardSaleHandlerJobTypeEntity);
        }
        generalCardSaleHandlerMapper.insertGeneralCardSaleHandlerJobType(list);
    }

    public BaseOutput moveGeneralCardSaleHandler(SaleHandlerMoveForm form) {
        //重新排序
        LinkedList<Integer> linkedList = generalCardSaleHandlerMapper.getIDOrderBySequenceASC(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<GeneralCardSaleHandlerEntity> list = new ArrayList<>();

        for (Integer ID : linkedList) {
            GeneralCardSaleHandlerEntity generalCardSaleHandlerEntity = new GeneralCardSaleHandlerEntity();
            generalCardSaleHandlerEntity.ID = ID;
            generalCardSaleHandlerEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(generalCardSaleHandlerEntity);
        }
        generalCardSaleHandlerMapper.updateSequence(list);

        return BaseOutput.success();
    }

}