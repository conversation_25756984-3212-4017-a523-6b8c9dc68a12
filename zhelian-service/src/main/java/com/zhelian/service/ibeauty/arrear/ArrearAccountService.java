package com.zhelian.service.ibeauty.arrear;

import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.ibeauty.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.ArrearAccountQueryForm;
import com.zhelian.model.ibeauty.form.input.ArrearPackageCardAccountQueryForm;
import com.zhelian.model.ibeauty.form.input.ArrearTimeAccountQueryForm;
import com.zhelian.model.ibeauty.form.output.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ArrearAccountService {

    @Autowired
    private ProjectAccountMapper projectAccountMapper;
    @Autowired
    private ProductAccountMapper productAccountMapper;
    @Autowired
    private SavingCardAccountMapper savingCardAccountMapper;
    @Autowired
    private GeneralCardAccountMapper generalCardAccountMapper;
    @Autowired
    private TimeCardAccountMapper timeCardAccountMapper;
    @Autowired
    private PackageCardAccountMapper packageCardAccountMapper;

    public BaseOutput projectAccount(ArrearAccountQueryForm form) {
        List<ProjectAccountOutputForm> list = projectAccountMapper.arrearProjectAccount(form.CustomerID, form.Name, SystemUserUtil.getSystemEntityID());
        return BaseOutputForm.success(list);
    }

    public BaseOutput productAccount(ArrearAccountQueryForm form) {
        List<ProductAccountOutputForm> list = productAccountMapper.arrearProductAccount(form.CustomerID, form.Name, SystemUserUtil.getSystemEntityID());
        return BaseOutputForm.success(list);
    }

    public BaseOutput savingCardAccount(ArrearAccountQueryForm form) {
        List<SavingCardAccountForm> list = savingCardAccountMapper.arrearSavingCardAccount(form.CustomerID, form.Name, SystemUserUtil.getSystemEntityID());
        return BaseOutputForm.success(list);
    }

    public BaseOutput generalCardAccount(ArrearAccountQueryForm form) {
        List<GeneralCardAccountOutputForm> list = generalCardAccountMapper.arrearGeneralCardAccount(form.CustomerID, form.Name, SystemUserUtil.getSystemEntityID());
        return BaseOutputForm.success(list);
    }

    public BaseOutput timeCardAccount(ArrearTimeAccountQueryForm form) {
        List<TimeCardAccountOutputForm> list = timeCardAccountMapper.arrearTimeCardAccount(form.CustomerID, form.Name, form.BillDate, SystemUserUtil.getSystemEntityID());
        return BaseOutputForm.success(list);
    }

    public BaseOutput packageCardAccount(ArrearAccountQueryForm form) {
        List<PackageCardAccountOutputForm> list = packageCardAccountMapper.arrearPackageCardAccount(form.CustomerID, form.Name, SystemUserUtil.getSystemEntityID());
        return BaseOutputForm.success(list);
    }

    public BaseOutput packageCardAccountDetails(ArrearPackageCardAccountQueryForm form) {
        PackageCardAccountDetailsOutputForm packageCardAccountDetailsOutputForm = new PackageCardAccountDetailsOutputForm();
        String ValidDayName = packageCardAccountMapper.getPackageCardAccountValidDayName(form.PackageCardAccountID);
        packageCardAccountDetailsOutputForm.Product = packageCardAccountMapper.arrearPackageCardProductAccount(form.PackageCardAccountID);
        if (packageCardAccountDetailsOutputForm.Product != null)
            packageCardAccountDetailsOutputForm.Product.stream().forEach(i -> i.ValidDayName = ValidDayName);
        packageCardAccountDetailsOutputForm.Project = packageCardAccountMapper.arrearPackageCardProjectAccount(form.PackageCardAccountID);
        if (packageCardAccountDetailsOutputForm.Project != null)
            packageCardAccountDetailsOutputForm.Project.stream().forEach(i -> i.ValidDayName = ValidDayName);
        packageCardAccountDetailsOutputForm.GeneralCard = packageCardAccountMapper.arrearPackageCardGeneralCardAccount(form.PackageCardAccountID);
        if (packageCardAccountDetailsOutputForm.GeneralCard != null)
            packageCardAccountDetailsOutputForm.GeneralCard.stream().forEach(i -> i.ValidDayName = ValidDayName);
        packageCardAccountDetailsOutputForm.TimeCard = packageCardAccountMapper.arrearPackageCardTimeCardAccount(form.PackageCardAccountID, form.BillDate);
        if (packageCardAccountDetailsOutputForm.TimeCard != null)
            packageCardAccountDetailsOutputForm.TimeCard.stream().forEach(i -> i.ValidDayName = ValidDayName);
        packageCardAccountDetailsOutputForm.SavingCard = packageCardAccountMapper.arrearPackageCardSavingCardAccount(form.PackageCardAccountID);
        if (packageCardAccountDetailsOutputForm.SavingCard != null)
            packageCardAccountDetailsOutputForm.SavingCard.stream().forEach(i -> i.ValidDayName = ValidDayName);
        return BaseOutputForm.success(packageCardAccountDetailsOutputForm);
    }
}