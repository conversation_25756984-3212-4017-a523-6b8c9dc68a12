package com.zhelian.service.ibeauty.appointment;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.autoNumber.AutoNumberUtils;
import com.zhelian.core.utils.DateTimeUtil;
import com.zhelian.core.utils.UploadVideoUtil;
import com.zhelian.core.utils.YingXiaoYunUtil;
import com.zhelian.core.utils.enumeration.BillChannel;
import com.zhelian.mapper.CRM.CustomerGrowthMapper;
import com.zhelian.mapper.CRM.CustomerLevelMapper;
import com.zhelian.mapper.CRM.CustomerMapper;
import com.zhelian.mapper.CRM.CustomerReduceConfigMapper;
import com.zhelian.mapper.ibeauty.AppointmentBillMapper;
import com.zhelian.mapper.ibeauty.AppointmentMapper;
import com.zhelian.mapper.ibeauty.AppointmentServicerMapper;
import com.zhelian.mapper.khs.EmployeeMapper;
import com.zhelian.mapper.yingxiaoyun.YingXiaoYunAppointmentBillMapper;
import com.zhelian.mapper.yingxiaoyun.YingXiaoYunTenantMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.*;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.ibeauty.form.output.*;
import com.zhelian.model.khs.entity.EmployeeEntity;
import com.zhelian.model.miniprogram.form.input.*;
import com.zhelian.model.miniprogram.form.output.MiniAppointmentBillAppointmentAudioOutputForm;
import com.zhelian.model.miniprogram.form.output.MiniAppointmentBillOutputForm;
import com.zhelian.model.miniprogram.form.output.MiniAppointmentBillQueryForm;
import com.zhelian.model.miniprogram.form.output.MiniAppointmentCountOutputForm;
import com.zhelian.model.yingxiaoyun.entity.YingXiaoYunAppointmentBillEntity;
import com.zhelian.model.yingxiaoyun.entity.YingXiaoYunTenantEntity;
import com.zhelian.model.yingxiaoyun.form.input.YXYAppointmentInputForm;
import com.zhelian.service.weixin.WeiXinMessageService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class AppointmentBillService {

    @Autowired
    private AppointmentBillMapper appointmentBillMapper;
    @Autowired
    private YingXiaoYunTenantMapper yingXiaoYunTenantMapper;
    @Autowired
    private AppointmentMapper appointmentMapper;
    @Autowired
    private CustomerMapper customerMapper;
    @Autowired
    private CustomerLevelMapper customerLevelMapper;
    @Autowired
    private CustomerGrowthMapper customerGrowthMapper;
    @Autowired
    private CustomerReduceConfigMapper customerReduceConfigMapper;
    @Autowired
    private YingXiaoYunAppointmentBillMapper yingXiaoYunAppointmentBillMapper;

    @Autowired
    private EmployeeMapper employeeMapper;
    @Autowired
    private AppointmentServicerMapper appointmentServicerMapper;
    @Autowired
    private WeiXinMessageService weiXinMessageService;
    private static final Logger logger = LoggerFactory.getLogger(AppointmentBillService.class);

    public BaseOutput getAppointmentBillServicer() {
        List<AppointmentBillServicerInfoOutputExtendForm> list = appointmentBillMapper.getAppointmentBillServicer(SystemUserUtil.getSystemEntityID());
        return BaseOutputForm.success(list);
    }

    public BaseOutput getAppointmentBillServicerEmployee(AppointmentBillEmpQueryForm form) {
        AppointmentConfigEntity appointmentConfigEntity = appointmentMapper.appointmentConfig(SystemUserUtil.getSystemEntityID());
        if (appointmentConfigEntity == null) {
            return BaseOutput.failed("该门店没有配置预约信息，请前往预约配置");
        }
        List<AppointmentBillServicerInfoOutputForm> list = appointmentBillMapper.getAppointmentBillServicerEmployee(SystemUserUtil.getSystemEntityID(), form.AppointmentDate, appointmentConfigEntity.CanChooseRestEmployee);
        return BaseOutputForm.success(list);
    }

    public BaseOutput getAppointmentBillServicerEmployeeAll() {
        List<AppointmentBillServicerInfoOutputForm> list = appointmentBillMapper.getAppointmentBillServicerEmployeeAll(SystemUserUtil.getSystemEntityID());
        return BaseOutputForm.success(list);
    }

    public BaseOutput getAppointmentBillEmpAndCust(AppointmentBillEmpQueryForm form) {
        AppointmentConfigEntity appointmentConfigEntity = appointmentMapper.appointmentConfig(SystemUserUtil.getSystemEntityID());
        if (appointmentConfigEntity == null) {
            return BaseOutput.failed("该门店没有配置预约信息，请前往预约配置");
        }
        if (appointmentConfigEntity.AppointmentServicerIsRequired) {
            List<AppointmentBillEmpOutputForm> list = appointmentBillMapper.employeeScheduleAppointment(SystemUserUtil.getSystemEntityID(), form.AppointmentDate, form.ServicerID);
            return BaseOutputForm.success(list);
        } else {
            List<AppointmentBillAppointmentOutputForm> undistributedScheduleList = appointmentBillMapper.employeeUndistributedScheduleAppointment(SystemUserUtil.getSystemEntityID(), form.AppointmentDate);
            AppointmentBillEmpOutputForm appointmentBillEmpOutputForm = new AppointmentBillEmpOutputForm();
            appointmentBillEmpOutputForm.EmployeeID = "";
            appointmentBillEmpOutputForm.EmployeeName = "未分配";
            appointmentBillEmpOutputForm.Appointment = undistributedScheduleList;
            List<AppointmentBillEmpOutputForm> list = appointmentBillMapper.employeeScheduleAppointment(SystemUserUtil.getSystemEntityID(), form.AppointmentDate, form.ServicerID);
            list.add(0, appointmentBillEmpOutputForm);
            return BaseOutputForm.success(list);
        }
    }

    @Transactional
    public BaseOutput createAppointmentBil(AppointmentBillAddForm form) throws Exception {
        AppointmentConfigEntity appointmentConfigEntity = appointmentMapper.appointmentConfig(SystemUserUtil.getSystemEntityID());
        if (appointmentConfigEntity.AppointmentServicerIsRequired && (form.Servicer == null || form.Servicer.size() == 0)) {
            return BaseOutput.failed("预约角色必填，请选择员工");
        }

        Date endDate = DateTimeUtil.addMinute(form.AppointmentDate, form.Period);
        if (form.Servicer != null && form.Servicer.size() > 0) {
            for (AppointmentBillServicerAddForm appointmentBillServicerAddForm : form.Servicer) {
                AppointmentServicerEntity servicerEntity = appointmentServicerMapper.getAppointmentServicerEntity(appointmentBillServicerAddForm.ServicerID);
                if (servicerEntity.Active == false) {
                    return BaseOutput.failed(servicerEntity.Name + "该预约角色已被无效，请刷新页面");
                }
                EmployeeEntity employee = employeeMapper.getEmployee(appointmentBillServicerAddForm.EmployeeID);
                if (employee.State == false) {
                    return BaseOutput.failed(employee.Name + "员工已离职，请选择其他员工");
                }
                Boolean isRest = appointmentMapper.appointmentScheduleEmployee(SystemUserUtil.getSystemEntityID(), appointmentBillServicerAddForm.EmployeeID, form.AppointmentDate.trim().substring(0, 10), appointmentBillServicerAddForm.ServicerID);
                if (!appointmentConfigEntity.CanChooseRestEmployee && isRest != null && isRest) {
                    return BaseOutput.failed(employee.Name + "员工在预约时间内休息，请选择其他员工");
                }
                AppointmentServicerEntity appointmentServicerEntity = appointmentServicerMapper.getAppointmentServicerEntity(appointmentBillServicerAddForm.ServicerID);
                System.out.println(DateTimeUtil.toDate(endDate));
                Integer count = appointmentMapper.getScheduleEmployee(appointmentBillServicerAddForm.EmployeeID, form.AppointmentDate, DateTimeUtil.toDate(endDate), null, appointmentBillServicerAddForm.ServicerID);
                if (appointmentServicerEntity.AppointmentNumber.intValue() != 0 && count.intValue() >= appointmentServicerEntity.AppointmentNumber.intValue()) {
                    return BaseOutput.failed(employee.Name + "员工在预约时间内预约的顾客超出数量限制，请选择其他员工或选择其他时间");
                }
            }
        }
        AppointmentBillEntity appointmentBillEntity = new AppointmentBillEntity();
        appointmentBillEntity.ID = AutoNumberUtils.getIBeautyAppointmentBill();
        // 判断参数EntityID是否有值，有直接使用，没有使用当前登录人的EntityID
        appointmentBillEntity.EntityID = form.EntityID != null ? form.EntityID : SystemUserUtil.getSystemEntityID();
        appointmentBillEntity.CustomerID = form.CustomerID;
        appointmentBillEntity.Status = "10";
        appointmentBillEntity.AppointmentDate = DateTimeUtil.toDate(form.AppointmentDate);
        appointmentBillEntity.Period = form.Period;
        appointmentBillEntity.Remark = form.Remark;
        appointmentBillEntity.AppointmentTypeID = form.AppointmentTypeID;
        appointmentBillEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        appointmentBillEntity.Channel = BillChannel.BillChannelPC.getID();
        appointmentBillEntity.CreatedOn = new Date();
        appointmentBillMapper.createAppointmentBill(appointmentBillEntity);

        List<AppointmentBillEmployeeEntity> servicerList = new ArrayList<>();
        if (form.Servicer != null && form.Servicer.size() > 0) {
            for (AppointmentBillServicerAddForm appointmentBillServicerAddForm : form.Servicer) {
                AppointmentBillEmployeeEntity appointmentBillEmployeeEntity = new AppointmentBillEmployeeEntity();
                appointmentBillEmployeeEntity.AppointmentBillID = appointmentBillEntity.ID;
                appointmentBillEmployeeEntity.EmployeeID = appointmentBillServicerAddForm.EmployeeID;
                appointmentBillEmployeeEntity.AppointmentServicerID = appointmentBillServicerAddForm.ServicerID;

                servicerList.add(appointmentBillEmployeeEntity);
            }
        }
        if (servicerList.size() > 0)
            appointmentBillMapper.createAppointmentBillEmployeeList(servicerList);


        AppointmentBillProcessEntity appointmentBillProcessEntity = new AppointmentBillProcessEntity();
        appointmentBillProcessEntity.AppointmentBillID = appointmentBillEntity.ID;
        appointmentBillProcessEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        appointmentBillProcessEntity.CreatedOn = new Date();
        appointmentBillProcessEntity.ActionType = "10";
        appointmentBillMapper.createAppointmentBillProcess(appointmentBillProcessEntity);

        List<AppointmentBillProjectEntity> project = new ArrayList<>();
        if (form.Project != null && form.Project.size() > 0) {
            for (AppointmentBillProjectAddForm appointmentBillProjectAddForm : form.Project) {
                AppointmentBillProjectEntity appointmentBillProjectEntity = new AppointmentBillProjectEntity();
                appointmentBillProjectEntity.AppointmentBillID = appointmentBillEntity.ID;
                appointmentBillProjectEntity.ProjectID = appointmentBillProjectAddForm.ProjectID;
                project.add(appointmentBillProjectEntity);
            }
        }

        if (project != null && project.size() > 0)
            appointmentBillMapper.createAppointmentBillProject(project);

        // 返回预约创建结果
        AppointmentBillCreateOutputForm result = new AppointmentBillCreateOutputForm();
        result.ID = appointmentBillEntity.ID;
        result.AppointmentDate = form.AppointmentDate;
        return BaseOutputForm.success(result);
    }

    public AppointmentBillEntity getAppointmentBill(String ID){
        return appointmentBillMapper.getAppointmentBill(ID);
    }

    @Transactional
    public BaseOutput updateAppointmentBill(AppointmentBillUpdateForm form) throws Exception {
        AppointmentBillEntity appointmentBillEntity = appointmentBillMapper.getAppointmentBill(form.ID);
        if (appointmentBillEntity == null)
            return BaseOutput.failed("该预约单不存在，请选择其他单据");

        if (appointmentBillEntity.Status.equals("20")) {
            return BaseOutput.failed("当前预约单据顾客已经到店，无需再次确认到店");
        }
        LocalDate localDate1 = LocalDate.parse(form.AppointmentDate.trim().substring(0, 10));
        LocalDate localDate2 = LocalDate.now();

        if (form.Status.equals("20") && localDate1.isAfter(localDate2)) {
            return BaseOutput.failed("没有到期的预约，不可以确认到店");
        }
        AppointmentConfigEntity appointmentConfigEntity = appointmentMapper.appointmentConfig(SystemUserUtil.getSystemEntityID());

        if (appointmentConfigEntity.AppointmentServicerIsRequired && (form.Servicer == null || form.Servicer.size() == 0)) {
            return BaseOutput.failed("预约角色必填，请选择员工");
        }
        Date endDate = DateTimeUtil.addMinute(form.AppointmentDate, form.Period);
        Boolean isSend = !form.Status.equals(appointmentBillEntity.Status) ? true : false;

        if (form.Servicer != null && form.Servicer.size() > 0) {
            for (AppointmentBillServicerAddForm appointmentBillServicerAddForm : form.Servicer) {
                AppointmentServicerEntity servicerEntity = appointmentServicerMapper.getAppointmentServicerEntity(appointmentBillServicerAddForm.ServicerID);
                if (servicerEntity.Active == false) {
                    return BaseOutput.failed(servicerEntity.Name + "该预约角色已被无效，请刷新页面");
                }
                EmployeeEntity employee = employeeMapper.getEmployee(appointmentBillServicerAddForm.EmployeeID);
                if (employee.State == false) {
                    return BaseOutput.failed(employee.Name + "员工已离职，请选择其他员工");
                }
                Boolean isRest = appointmentMapper.appointmentScheduleEmployee(SystemUserUtil.getSystemEntityID(), appointmentBillServicerAddForm.EmployeeID, form.AppointmentDate.trim().substring(0, 10), appointmentBillServicerAddForm.ServicerID);
                if (!appointmentConfigEntity.CanChooseRestEmployee && isRest != null && isRest) {
                    return BaseOutput.failed(employee.Name + "员工在预约时间内休息，请选择其他员工");
                }
                AppointmentServicerEntity appointmentServicerEntity = appointmentServicerMapper.getAppointmentServicerEntity(appointmentBillServicerAddForm.ServicerID);
                System.out.println(DateTimeUtil.toDate(endDate));
                Integer count = appointmentMapper.getScheduleEmployee(appointmentBillServicerAddForm.EmployeeID, form.AppointmentDate, DateTimeUtil.toDate(endDate), form.ID, appointmentBillServicerAddForm.ServicerID);
                if (appointmentServicerEntity.AppointmentNumber.intValue() != 0 && count.intValue() >= appointmentServicerEntity.AppointmentNumber.intValue()) {
                    return BaseOutput.failed(employee.Name + "员工在预约时间内预约的顾客超出数量限制，请选择其他员工或选择其他时间");
                }
            }
        }
        appointmentBillEntity.CustomerID = form.CustomerID;
        appointmentBillEntity.AppointmentDate = DateTimeUtil.toDate(form.AppointmentDate);
        appointmentBillEntity.Period = form.Period;
        appointmentBillEntity.Remark = form.Remark;
        appointmentBillEntity.Status = form.Status;
        appointmentBillEntity.AppointmentTypeID = form.AppointmentTypeID;
        appointmentBillMapper.updateAppointmentBill(appointmentBillEntity);

        // 当顾客确认到店时，设置预约门店为顾客所属门店（如果顾客还没有所属门店）
        if (form.Status.equals("20")) {
            // 检查顾客是否已有所属门店
            CustomerEntityEntity existingBelongEntity = customerMapper.getBelongEntity(appointmentBillEntity.CustomerID);
            if (existingBelongEntity == null) {
                // 顾客没有所属门店，将当前预约的门店设为所属门店
                Integer existingRelationID = customerMapper.getCustomerEntityID(appointmentBillEntity.CustomerID, appointmentBillEntity.EntityID);
                if (existingRelationID == null) {
                    // 如果顾客与该门店没有关联关系，创建新的关联并设为所属门店
                    CustomerEntityEntity customerEntityEntity = new CustomerEntityEntity();
                    customerEntityEntity.CustomerID = appointmentBillEntity.CustomerID;
                    customerEntityEntity.EntityID = appointmentBillEntity.EntityID;
                    customerEntityEntity.IsBelongEntity = true;
                    customerEntityEntity.IsCreateEntity = false;
                    customerMapper.insertCustomerEntity(customerEntityEntity);
                } else {
                    // 如果已有关联关系，直接更新为所属门店
                    customerMapper.updateCustomerEntityEntity(appointmentBillEntity.CustomerID, appointmentBillEntity.EntityID);
                }
            }
        }

        appointmentBillMapper.deleteAppointmentBillServicer(form.ID);
        List<AppointmentBillEmployeeEntity> servicerList = new ArrayList<>();
        if (form.Servicer != null && form.Servicer.size() > 0) {
            for (AppointmentBillServicerAddForm appointmentBillServicerAddForm : form.Servicer) {
                AppointmentBillEmployeeEntity appointmentBillEmployeeEntity = new AppointmentBillEmployeeEntity();
                appointmentBillEmployeeEntity.AppointmentBillID = appointmentBillEntity.ID;
                appointmentBillEmployeeEntity.EmployeeID = appointmentBillServicerAddForm.EmployeeID;
                appointmentBillEmployeeEntity.AppointmentServicerID = appointmentBillServicerAddForm.ServicerID;

                servicerList.add(appointmentBillEmployeeEntity);
            }
        }
        if (servicerList.size() > 0)
            appointmentBillMapper.createAppointmentBillEmployeeList(servicerList);

        appointmentBillMapper.deleteAppointmentBill(form.ID);
        appointmentBillMapper.deleteAppointmentBillProject(form.ID);
        appointmentBillMapper.deleteAppointmentBillAudio(form.ID);

        List<AppointmentBillProjectEntity> project = new ArrayList<>();
        if (form.Project != null && form.Project.size() > 0) {
            for (AppointmentBillProjectAddForm appointmentBillProjectAddForm : form.Project) {
                AppointmentBillProjectEntity appointmentBillProjectEntity = new AppointmentBillProjectEntity();
                appointmentBillProjectEntity.AppointmentBillID = appointmentBillEntity.ID;
                appointmentBillProjectEntity.ProjectID = appointmentBillProjectAddForm.ProjectID;
                project.add(appointmentBillProjectEntity);
            }
        }

        if (project != null && project.size() > 0)
            appointmentBillMapper.createAppointmentBillProject(project);


        AppointmentBillProcessEntity appointmentBillProcessEntity = new AppointmentBillProcessEntity();
        appointmentBillProcessEntity.AppointmentBillID = form.ID;
        appointmentBillProcessEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        appointmentBillProcessEntity.CreatedOn = new Date();
        appointmentBillProcessEntity.ActionType = form.Status;
        appointmentBillMapper.createAppointmentBillProcess(appointmentBillProcessEntity);


        if (isSend) {
            send(form.Status, appointmentBillEntity.ID);
        }


        return BaseOutput.success();
    }

    public BaseOutput updateStatus(AppointmentBillUpdateStatusForm form) {
        AppointmentBillEntity appointmentBill = appointmentBillMapper.getAppointmentBill(form.ID);
        if (appointmentBill.Status.equals("20")) {
            return BaseOutput.failed("当前预约单据顾客已经到店，请刷新页面");
        }
        LocalDate localDate1 = appointmentBill.AppointmentDate.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        LocalDate localDate2 = LocalDate.now();

        if (form.Status.equals("20") && localDate1.isAfter(localDate2)) {
            return BaseOutput.failed("没有到期的预约，不可以确认到店");
        }
        Boolean isSend = !form.Status.equals(appointmentBill.Status) ? true : false;

        appointmentBill.Status = form.Status;
        appointmentBillMapper.updateAppointmentBill(appointmentBill);

        // 当顾客确认到店时，设置预约门店为顾客所属门店（如果顾客还没有所属门店）
        if (form.Status.equals("20")) {
            // 检查顾客是否已有所属门店
            CustomerEntityEntity existingBelongEntity = customerMapper.getBelongEntity(appointmentBill.CustomerID);
            if (existingBelongEntity == null) {
                // 顾客没有所属门店，将当前预约的门店设为所属门店
                Integer existingRelationID = customerMapper.getCustomerEntityID(appointmentBill.CustomerID, appointmentBill.EntityID);
                if (existingRelationID == null) {
                    // 如果顾客与该门店没有关联关系，创建新的关联并设为所属门店
                    CustomerEntityEntity customerEntityEntity = new CustomerEntityEntity();
                    customerEntityEntity.CustomerID = appointmentBill.CustomerID;
                    customerEntityEntity.EntityID = appointmentBill.EntityID;
                    customerEntityEntity.IsBelongEntity = true;
                    customerEntityEntity.IsCreateEntity = false;
                    customerMapper.insertCustomerEntity(customerEntityEntity);
                } else {
                    // 如果已有关联关系，直接更新为所属门店
                    customerMapper.updateCustomerEntityEntity(appointmentBill.CustomerID, appointmentBill.EntityID);
                }
            }
        }

        AppointmentBillProcessEntity appointmentBillProcessEntity = new AppointmentBillProcessEntity();
        appointmentBillProcessEntity.AppointmentBillID = form.ID;
        appointmentBillProcessEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        appointmentBillProcessEntity.CreatedOn = new Date();
        appointmentBillProcessEntity.ActionType = form.Status;
        appointmentBillMapper.createAppointmentBillProcess(appointmentBillProcessEntity);


        if (isSend) {
            send(form.Status, appointmentBill.ID);
        }
        return BaseOutput.success();
    }


    public BaseOutput appointmentBillList(AppointmentBillQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        Integer entityID = SystemUserUtil.getSystemEntityID();
        return BasePageInfo.success(appointmentBillMapper.appointmentBillList(entityID, form.AppointmentBillID, form.Name, form.StartDate, form.EndDate, form.Type, form.Status, form.CustomerID, form.CreatedBy, form.CreatedOnStartDate, form.CreatedOnEndDate, form.ServicerID, form.ServicerEmployeeID, form.Channel, form.AppointmentTypeID, form.CustomerSourceID,form.CustomerLevelID,form.CustomerChannel));
    }

    public BaseOutput appointmentBillInfo(AppointmentBillInfoQueryForm form) {
        return BaseOutputForm.success(appointmentBillMapper.appointmentBillInfo(form.ID));
    }

    public BaseOutput appointmentBillInfoSelfList(MiniAppointmentBillQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<MiniAppointmentBillOutputForm> appointmentBillInfoList = appointmentBillMapper.appointmentBillInfoList(SystemUserUtil.getSystemEntityID(), SystemUserUtil.getSystemUserID(), form.Name, form.AppointmentDate.trim().substring(0, 10), form.Status, form.CustomerID, form.AppointmentTypeID, form.ServicerID, form.ServicerEmployeeID, form.Channel, form.CreatedBy, form.CreatedOnStartDate, form.CreatedOnEndDate, form.CustomerSourceID);
        for (MiniAppointmentBillOutputForm miniAppointmentBillOutputForm : appointmentBillInfoList) {
            List<MiniAppointmentBillAppointmentAudioOutputForm> newAudio = new ArrayList<>();
            if (miniAppointmentBillOutputForm.Audio.size() > 0) {
                for (MiniAppointmentBillAppointmentAudioOutputForm miniAppointmentBillAppointmentAudioOutputForm : miniAppointmentBillOutputForm.Audio) {
                    if (miniAppointmentBillAppointmentAudioOutputForm.AudioID != null && !miniAppointmentBillAppointmentAudioOutputForm.AudioID.equals("")) {
                        JSONObject jsonObject = UploadVideoUtil.getPlayInfoAdress(miniAppointmentBillAppointmentAudioOutputForm.AudioID);
                        if (jsonObject != null && jsonObject.size() > 0) {
                            String duration = jsonObject.getString("duration");
                            if (StringUtils.isNotBlank(duration)) {
                                miniAppointmentBillAppointmentAudioOutputForm.Duration = duration.substring(0, duration.indexOf("."));
                                miniAppointmentBillAppointmentAudioOutputForm.AudioURL = UploadVideoUtil.getPlayInfoAdress(miniAppointmentBillAppointmentAudioOutputForm.AudioID).getString("url");
                                newAudio.add(miniAppointmentBillAppointmentAudioOutputForm);
                            }
                        }
                    }
                }
            }
            miniAppointmentBillOutputForm.Audio = newAudio;
        }
        return BasePageInfo.success(appointmentBillInfoList);
    }

    public BaseOutput appointmentBillInfoEntityList(MiniAppointmentBillQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<MiniAppointmentBillOutputForm> appointmentBillInfoList = appointmentBillMapper.appointmentBillInfoList(SystemUserUtil.getSystemEntityID(), form.EmployeeID, form.Name, form.AppointmentDate.trim().substring(0, 10), form.Status, form.CustomerID, form.AppointmentTypeID, form.ServicerID, form.ServicerEmployeeID, form.Channel, form.CreatedBy, form.CreatedOnStartDate, form.CreatedOnEndDate, form.CustomerSourceID);
        for (MiniAppointmentBillOutputForm miniAppointmentBillOutputForm : appointmentBillInfoList) {
            List<MiniAppointmentBillAppointmentAudioOutputForm> newAudio = new ArrayList<>();
            if (miniAppointmentBillOutputForm.Audio.size() > 0) {
                for (MiniAppointmentBillAppointmentAudioOutputForm miniAppointmentBillAppointmentAudioOutputForm : miniAppointmentBillOutputForm.Audio) {
                    if (miniAppointmentBillAppointmentAudioOutputForm.AudioID != null && !miniAppointmentBillAppointmentAudioOutputForm.AudioID.equals("")) {
                        JSONObject jsonObject = UploadVideoUtil.getPlayInfoAdress(miniAppointmentBillAppointmentAudioOutputForm.AudioID);
                        if (jsonObject != null && jsonObject.size() > 0) {
                            String duration = jsonObject.getString("duration");
                            if (StringUtils.isNotBlank(duration)) {
                                miniAppointmentBillAppointmentAudioOutputForm.Duration = duration.substring(0, duration.indexOf("."));
                                miniAppointmentBillAppointmentAudioOutputForm.AudioURL = UploadVideoUtil.getPlayInfoAdress(miniAppointmentBillAppointmentAudioOutputForm.AudioID).getString("url");
                                newAudio.add(miniAppointmentBillAppointmentAudioOutputForm);
                            }

                        }
                    }
                }
            }
            miniAppointmentBillOutputForm.Audio = newAudio;
        }
        return BasePageInfo.success(appointmentBillInfoList);
    }

    @Transactional
    public BaseOutput miniCreateBill(MiniAppointmentBillAddForm form) throws Exception {
        AppointmentConfigEntity appointmentConfigEntity = appointmentMapper.appointmentConfig(SystemUserUtil.getSystemEntityID());
        if (appointmentConfigEntity.AppointmentServicerIsRequired && (form.Servicer == null || form.Servicer.size() == 0)) {
            return BaseOutput.failed("预约角色必填，请选择员工");
        }
        Date endDate = DateTimeUtil.addMinute(form.AppointmentDate, form.Period);
        if (form.Servicer != null && form.Servicer.size() > 0) {
            for (AppointmentBillServicerAddForm appointmentBillServicerAddForm : form.Servicer) {
                AppointmentServicerEntity servicerEntity = appointmentServicerMapper.getAppointmentServicerEntity(appointmentBillServicerAddForm.ServicerID);
                if (servicerEntity.Active == false) {
                    return BaseOutput.failed(servicerEntity.Name + "该预约角色已被无效，请刷新页面");
                }
                EmployeeEntity employee = employeeMapper.getEmployee(appointmentBillServicerAddForm.EmployeeID);
                if (employee.State == false) {
                    return BaseOutput.failed(employee.Name + "员工已离职，请选择其他员工");
                }
                Boolean isRest = appointmentMapper.appointmentScheduleEmployee(SystemUserUtil.getSystemEntityID(), appointmentBillServicerAddForm.EmployeeID, form.AppointmentDate.trim().substring(0, 10), appointmentBillServicerAddForm.ServicerID);
                if (!appointmentConfigEntity.CanChooseRestEmployee && isRest != null && isRest) {
                    return BaseOutput.failed(employee.Name + "员工在预约时间内休息，请选择其他员工");
                }
                AppointmentServicerEntity appointmentServicerEntity = appointmentServicerMapper.getAppointmentServicerEntity(appointmentBillServicerAddForm.ServicerID);
                System.out.println(DateTimeUtil.toDate(endDate));
                Integer count = appointmentMapper.getScheduleEmployee(appointmentBillServicerAddForm.EmployeeID, form.AppointmentDate, DateTimeUtil.toDate(endDate), null, appointmentBillServicerAddForm.ServicerID);
                if (appointmentServicerEntity.AppointmentNumber.intValue() != 0 && count.intValue() >= appointmentServicerEntity.AppointmentNumber.intValue()) {
                    return BaseOutput.failed(employee.Name + "员工在预约时间内预约的顾客超出数量限制，请选择其他员工或选择其他时间");
                }
            }
        }


        AppointmentBillEntity appointmentBillEntity = new AppointmentBillEntity();
        appointmentBillEntity.ID = AutoNumberUtils.getIBeautyAppointmentBill();
        appointmentBillEntity.EntityID = SystemUserUtil.getSystemEntityID();
        appointmentBillEntity.CustomerID = form.CustomerID;
        appointmentBillEntity.Status = "10";
        appointmentBillEntity.AppointmentDate = DateTimeUtil.toDate(form.AppointmentDate);
        appointmentBillEntity.Period = form.Period;
        appointmentBillEntity.Remark = form.Remark;
        appointmentBillEntity.AppointmentTypeID = form.AppointmentTypeID;
        appointmentBillEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        appointmentBillEntity.Channel = BillChannel.BillChannelMiniprogram.getID();
        appointmentBillEntity.CreatedOn = new Date();
        appointmentBillMapper.createAppointmentBill(appointmentBillEntity);

        List<AppointmentBillEmployeeEntity> servicerList = new ArrayList<>();
        if (form.Servicer != null && form.Servicer.size() > 0) {
            for (AppointmentBillServicerAddForm appointmentBillServicerAddForm : form.Servicer) {
                AppointmentBillEmployeeEntity appointmentBillEmployeeEntity = new AppointmentBillEmployeeEntity();
                appointmentBillEmployeeEntity.AppointmentBillID = appointmentBillEntity.ID;
                appointmentBillEmployeeEntity.EmployeeID = appointmentBillServicerAddForm.EmployeeID;
                appointmentBillEmployeeEntity.AppointmentServicerID = appointmentBillServicerAddForm.ServicerID;

                servicerList.add(appointmentBillEmployeeEntity);
            }
        }
        if (servicerList.size() > 0)
            appointmentBillMapper.createAppointmentBillEmployeeList(servicerList);

        AppointmentBillProcessEntity appointmentBillProcessEntity = new AppointmentBillProcessEntity();
        appointmentBillProcessEntity.AppointmentBillID = appointmentBillEntity.ID;
        appointmentBillProcessEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        appointmentBillProcessEntity.CreatedOn = new Date();
        appointmentBillProcessEntity.ActionType = "10";
        appointmentBillMapper.createAppointmentBillProcess(appointmentBillProcessEntity);

        List<AppointmentBillProjectEntity> project = new ArrayList<>();
        if (form.Project != null && form.Project.size() > 0) {
            for (AppointmentBillProjectAddForm appointmentBillProjectAddForm : form.Project) {
                AppointmentBillProjectEntity appointmentBillProjectEntity = new AppointmentBillProjectEntity();
                appointmentBillProjectEntity.AppointmentBillID = appointmentBillEntity.ID;
                appointmentBillProjectEntity.ProjectID = appointmentBillProjectAddForm.ProjectID;
                project.add(appointmentBillProjectEntity);
            }
        }
        List<AppointmentBillAudioEntity> audio = new ArrayList<>();
        if (form.Audio != null && form.Audio.size() > 0) {
            for (MiniAppointmentAudioForm miniAppointmentAudioForm : form.Audio) {
                AppointmentBillAudioEntity appointmentBillAudioEntity = new AppointmentBillAudioEntity();
                appointmentBillAudioEntity.AppointmentBillID = appointmentBillEntity.ID;
                appointmentBillAudioEntity.AudioURL = miniAppointmentAudioForm.AudioURL;
                audio.add(appointmentBillAudioEntity);
            }
        }

        if (project != null && project.size() > 0)
            appointmentBillMapper.createAppointmentBillProject(project);
        if (audio != null && audio.size() > 0)
            appointmentBillMapper.createAppointmentBillAudio(audio);
        return BaseOutputForm.success(appointmentBillEntity.ID);
    }

    @Transactional
    public BaseOutput newCustomerCreateBill(MiniAppointmentBillAddExtendForm form) throws Exception {
        if (form.IsMember && form.CustomerLevelID == null) {
            return BaseOutput.failed("请选择等级");
        }

        if (form.PhoneNumber != null && !form.PhoneNumber.equals("")) {
            Integer count = customerMapper.customerCount(form.PhoneNumber);
            if (count > 0)
                return BaseOutput.failed("手机号已存在，不用新建客户");
        }


        AppointmentConfigEntity appointmentConfigEntity = appointmentMapper.appointmentConfig(SystemUserUtil.getSystemEntityID());
        if (appointmentConfigEntity.AppointmentServicerIsRequired && (form.Servicer == null || form.Servicer.size() == 0)) {
            return BaseOutput.failed("预约角色必填，请选择员工");
        }
        Date endDate = DateTimeUtil.addMinute(form.AppointmentDate, form.Period);
        if (form.Servicer != null && form.Servicer.size() > 0) {
            for (AppointmentBillServicerAddForm appointmentBillServicerAddForm : form.Servicer) {
                AppointmentServicerEntity servicerEntity = appointmentServicerMapper.getAppointmentServicerEntity(appointmentBillServicerAddForm.ServicerID);
                if (servicerEntity.Active == false) {
                    return BaseOutput.failed(servicerEntity.Name + "该预约角色已被无效，请刷新页面");
                }
                EmployeeEntity employee = employeeMapper.getEmployee(appointmentBillServicerAddForm.EmployeeID);
                if (employee.State == false) {
                    return BaseOutput.failed(employee.Name + "员工已离职，请选择其他员工");
                }
                Boolean isRest = appointmentMapper.appointmentScheduleEmployee(SystemUserUtil.getSystemEntityID(), appointmentBillServicerAddForm.EmployeeID, form.AppointmentDate.trim().substring(0, 10), appointmentBillServicerAddForm.ServicerID);
                if (!appointmentConfigEntity.CanChooseRestEmployee && isRest != null && isRest) {
                    return BaseOutput.failed(employee.Name + "员工在预约时间内休息，请选择其他员工");
                }
                AppointmentServicerEntity appointmentServicerEntity = appointmentServicerMapper.getAppointmentServicerEntity(appointmentBillServicerAddForm.ServicerID);
                System.out.println(DateTimeUtil.toDate(endDate));
                Integer countNumber = appointmentMapper.getScheduleEmployee(appointmentBillServicerAddForm.EmployeeID, form.AppointmentDate, DateTimeUtil.toDate(endDate), null, appointmentBillServicerAddForm.ServicerID);
                if (appointmentServicerEntity.AppointmentNumber.intValue() != 0 && countNumber.intValue() >= appointmentServicerEntity.AppointmentNumber.intValue()) {
                    return BaseOutput.failed(employee.Name + "员工在预约时间内预约的顾客超出数量限制，请选择其他员工或选择其他时间");
                }
            }
        }


        CustomerEntity customerEntity = new CustomerEntity();
        customerEntity.Name = form.Name;
        customerEntity.PhoneNumber = form.PhoneNumber;
        customerEntity.Gender = "2";
        customerEntity.CreatedOn = new Date();
        customerEntity.IsMember = form.IsMember;
        customerEntity.IsLockMemberLevel = form.IsLockMemberLevel;
        customerEntity.CustomerLevelID = form.IsMember == false  ? null : form.CustomerLevelID;
        customerEntity.ChannelID = form.ChannelID;
        customerEntity.CustomerSourceID = form.CustomerSourceID;
        customerEntity.MemberOn = form.IsMember == false  ? null : new Date();
        customerMapper.insertCustomer(customerEntity);

        CustomerEntityEntity customerEntityEntity = new CustomerEntityEntity();
        customerEntityEntity.CustomerID = customerEntity.ID;
        customerEntityEntity.EntityID = SystemUserUtil.getSystemEntityID();
        customerEntityEntity.IsBelongEntity = true;
        customerEntityEntity.IsCreateEntity = true;
        customerMapper.insertCustomerEntity(customerEntityEntity);

        if (form.ServicerList != null && form.ServicerList.size() > 0) {
            for (ServicerEmployeeAddForm ServicerList : form.ServicerList) {
                CustomerServicerEntity entity = new CustomerServicerEntity();
                entity.CustomerID = customerEntity.ID;
                entity.ServicerID = ServicerList.ServicerID;
                entity.EmployeeID = ServicerList.EmployeeID;
                customerMapper.insertCustomerServicer(entity);
            }
        }

        if (form.IsMember) {
            CustomerLevelEntity customerLevel = customerLevelMapper.getCustomerLevel(form.CustomerLevelID);
            if (customerLevel != null && new BigDecimal(customerLevel.LevelValue).compareTo(BigDecimal.ZERO) > 0) {
                CustomerGrowthEntity customerGrowthEntity = new CustomerGrowthEntity();
                customerGrowthEntity.CustomerID = customerEntity.ID;
                customerGrowthEntity.CustomerGrowthTypeID = "AdjustGrowth";
                customerGrowthEntity.GrowthValue = new BigDecimal(customerLevel.LevelValue);
                customerGrowthEntity.CreatedOn = new Date();
                customerGrowthEntity.Remark = form.Remark;
                customerGrowthMapper.insertCustomerGrowth(customerGrowthEntity);
            }

            CustomerReduceConfigOutputForm customerReduceConfig = customerReduceConfigMapper.getCustomerReduceConfig();
            if (customerReduceConfig.IsReduce) {
                CustomerLevelValidityEntity customerLevelValidityEntity = new CustomerLevelValidityEntity();
                customerLevelValidityEntity.CustomerID = customerEntity.ID;
                customerLevelValidityEntity.ModifyOn = new Date();
                if (customerReduceConfig.ReduceRule.equals("10")) {
                    customerLevelValidityEntity.ValidityOn = DateTimeUtil.addDay(new Date(), customerReduceConfig.VaildModeDay);

                } else {
                    customerLevelValidityEntity.ValidityOn = DateTimeUtil.addDay(customerReduceConfig.RegularModeUpdateDate, -1);
                }
                customerLevelMapper.insertCustomerLevelValidity(customerLevelValidityEntity);
            }
        }

        AppointmentBillEntity appointmentBillEntity = new AppointmentBillEntity();
        appointmentBillEntity.ID = AutoNumberUtils.getIBeautyAppointmentBill();
        appointmentBillEntity.EntityID = SystemUserUtil.getSystemEntityID();
        appointmentBillEntity.CustomerID = customerEntity.ID;
        appointmentBillEntity.Status = "10";
        appointmentBillEntity.AppointmentDate = DateTimeUtil.toDate(form.AppointmentDate);
        appointmentBillEntity.Period = form.Period;
        appointmentBillEntity.Remark = form.Remark;
        appointmentBillEntity.AppointmentTypeID = form.AppointmentTypeID;
        appointmentBillEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        appointmentBillEntity.Channel = BillChannel.BillChannelMiniprogram.getID();
        appointmentBillEntity.CreatedOn = new Date();
        appointmentBillMapper.createAppointmentBill(appointmentBillEntity);

        List<AppointmentBillEmployeeEntity> servicerList = new ArrayList<>();
        if (form.Servicer != null && form.Servicer.size() > 0) {
            for (AppointmentBillServicerAddForm appointmentBillServicerAddForm : form.Servicer) {
                AppointmentBillEmployeeEntity appointmentBillEmployeeEntity = new AppointmentBillEmployeeEntity();
                appointmentBillEmployeeEntity.AppointmentBillID = appointmentBillEntity.ID;
                appointmentBillEmployeeEntity.EmployeeID = appointmentBillServicerAddForm.EmployeeID;
                appointmentBillEmployeeEntity.AppointmentServicerID = appointmentBillServicerAddForm.ServicerID;

                servicerList.add(appointmentBillEmployeeEntity);
            }
        }
        if (servicerList.size() > 0)
            appointmentBillMapper.createAppointmentBillEmployeeList(servicerList);

        AppointmentBillProcessEntity appointmentBillProcessEntity = new AppointmentBillProcessEntity();
        appointmentBillProcessEntity.AppointmentBillID = appointmentBillEntity.ID;
        appointmentBillProcessEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        appointmentBillProcessEntity.CreatedOn = new Date();
        appointmentBillProcessEntity.ActionType = "10";
        appointmentBillMapper.createAppointmentBillProcess(appointmentBillProcessEntity);

        List<AppointmentBillProjectEntity> project = new ArrayList<>();
        if (form.Project != null && form.Project.size() > 0) {
            for (AppointmentBillProjectAddForm appointmentBillProjectAddForm : form.Project) {
                AppointmentBillProjectEntity appointmentBillProjectEntity = new AppointmentBillProjectEntity();
                appointmentBillProjectEntity.AppointmentBillID = appointmentBillEntity.ID;
                appointmentBillProjectEntity.ProjectID = appointmentBillProjectAddForm.ProjectID;
                project.add(appointmentBillProjectEntity);
            }
        }
        List<AppointmentBillAudioEntity> audio = new ArrayList<>();
        if (form.Audio != null && form.Audio.size() > 0) {
            for (MiniAppointmentAudioForm miniAppointmentAudioForm : form.Audio) {
                AppointmentBillAudioEntity appointmentBillAudioEntity = new AppointmentBillAudioEntity();
                appointmentBillAudioEntity.AppointmentBillID = appointmentBillEntity.ID;
                appointmentBillAudioEntity.AudioURL = miniAppointmentAudioForm.AudioURL;
                audio.add(appointmentBillAudioEntity);
            }
        }

        if (project != null && project.size() > 0)
            appointmentBillMapper.createAppointmentBillProject(project);
        if (audio != null && audio.size() > 0)
            appointmentBillMapper.createAppointmentBillAudio(audio);

        return BaseOutputForm.success(appointmentBillEntity.ID);
    }

    @Transactional
    public BaseOutput miniUpdateBill(MiniAppointmentBillUpdateForm form) throws Exception {
        AppointmentBillEntity appointmentBillEntity = appointmentBillMapper.getAppointmentBill(form.ID);
        if (appointmentBillEntity == null)
            return BaseOutput.failed("该预约单不存在，请选择其他单据");

        if (appointmentBillEntity.Status.equals("20")) {
            return BaseOutput.failed("当前预约单据顾客已经到店，无需再次确认到店");
        }
        LocalDate localDate1 = LocalDate.parse(form.AppointmentDate.trim().substring(0, 10));
        LocalDate localDate2 = LocalDate.now();

        if (form.Status.equals("20") && localDate1.isAfter(localDate2)) {
            return BaseOutput.failed("没有到期的预约，不可以确认到店");
        }
        AppointmentConfigEntity appointmentConfigEntity = appointmentMapper.appointmentConfig(SystemUserUtil.getSystemEntityID());
        if (appointmentConfigEntity.AppointmentServicerIsRequired && (form.Servicer == null || form.Servicer.size() == 0)) {
            return BaseOutput.failed("预约角色必填，请选择员工");
        }
        Date endDate = DateTimeUtil.addMinute(form.AppointmentDate, form.Period);

        Boolean isSend = !form.Status.equals(appointmentBillEntity.Status) ? true : false;
        if (form.Servicer != null && form.Servicer.size() > 0) {
            for (AppointmentBillServicerAddForm appointmentBillServicerAddForm : form.Servicer) {
                AppointmentServicerEntity servicerEntity = appointmentServicerMapper.getAppointmentServicerEntity(appointmentBillServicerAddForm.ServicerID);
                if (servicerEntity.Active == false) {
                    return BaseOutput.failed(servicerEntity.Name + "该预约角色已被无效，请刷新页面");
                }
                EmployeeEntity employee = employeeMapper.getEmployee(appointmentBillServicerAddForm.EmployeeID);
                if (employee.State == false) {
                    return BaseOutput.failed(employee.Name + "员工已离职，请选择其他员工");
                }
                Boolean isRest = appointmentMapper.appointmentScheduleEmployee(SystemUserUtil.getSystemEntityID(), appointmentBillServicerAddForm.EmployeeID, form.AppointmentDate.trim().substring(0, 10), appointmentBillServicerAddForm.ServicerID);
                if (!appointmentConfigEntity.CanChooseRestEmployee && isRest != null && isRest) {
                    return BaseOutput.failed(employee.Name + "员工在预约时间内休息，请选择其他员工");
                }
                AppointmentServicerEntity appointmentServicerEntity = appointmentServicerMapper.getAppointmentServicerEntity(appointmentBillServicerAddForm.ServicerID);
                System.out.println(DateTimeUtil.toDate(endDate));
                Integer count = appointmentMapper.getScheduleEmployee(appointmentBillServicerAddForm.EmployeeID, form.AppointmentDate, DateTimeUtil.toDate(endDate), form.ID, appointmentBillServicerAddForm.ServicerID);
                if (appointmentServicerEntity.AppointmentNumber.intValue() != 0 && count.intValue() >= appointmentServicerEntity.AppointmentNumber.intValue()) {
                    return BaseOutput.failed(employee.Name + "员工在预约时间内预约的顾客超出数量限制，请选择其他员工或选择其他时间");
                }
            }
        }

        appointmentBillEntity.CustomerID = form.CustomerID;
        appointmentBillEntity.AppointmentDate = DateTimeUtil.toDate(form.AppointmentDate);
        appointmentBillEntity.Period = form.Period;
        appointmentBillEntity.Remark = form.Remark;
        appointmentBillEntity.Status = form.Status;
        appointmentBillEntity.AppointmentTypeID = form.AppointmentTypeID;
        appointmentBillMapper.updateAppointmentBill(appointmentBillEntity);

        // 当顾客确认到店时，设置预约门店为顾客所属门店（如果顾客还没有所属门店）
        if (form.Status.equals("20")) {
            // 检查顾客是否已有所属门店
            CustomerEntityEntity existingBelongEntity = customerMapper.getBelongEntity(appointmentBillEntity.CustomerID);
            if (existingBelongEntity == null) {
                // 顾客没有所属门店，将当前预约的门店设为所属门店
                Integer existingRelationID = customerMapper.getCustomerEntityID(appointmentBillEntity.CustomerID, appointmentBillEntity.EntityID);
                if (existingRelationID == null) {
                    // 如果顾客与该门店没有关联关系，创建新的关联并设为所属门店
                    CustomerEntityEntity customerEntityEntity = new CustomerEntityEntity();
                    customerEntityEntity.CustomerID = appointmentBillEntity.CustomerID;
                    customerEntityEntity.EntityID = appointmentBillEntity.EntityID;
                    customerEntityEntity.IsBelongEntity = true;
                    customerEntityEntity.IsCreateEntity = false;
                    customerMapper.insertCustomerEntity(customerEntityEntity);
                } else {
                    // 如果已有关联关系，直接更新为所属门店
                    customerMapper.updateCustomerEntityEntity(appointmentBillEntity.CustomerID, appointmentBillEntity.EntityID);
                }
            }
        }

        appointmentBillMapper.deleteAppointmentBillServicer(form.ID);
        List<AppointmentBillEmployeeEntity> servicerList = new ArrayList<>();
        if (form.Servicer != null && form.Servicer.size() > 0) {
            for (AppointmentBillServicerAddForm appointmentBillServicerAddForm : form.Servicer) {
                AppointmentBillEmployeeEntity appointmentBillEmployeeEntity = new AppointmentBillEmployeeEntity();
                appointmentBillEmployeeEntity.AppointmentBillID = appointmentBillEntity.ID;
                appointmentBillEmployeeEntity.EmployeeID = appointmentBillServicerAddForm.EmployeeID;
                appointmentBillEmployeeEntity.AppointmentServicerID = appointmentBillServicerAddForm.ServicerID;

                servicerList.add(appointmentBillEmployeeEntity);
            }
        }
        if (servicerList.size() > 0)
            appointmentBillMapper.createAppointmentBillEmployeeList(servicerList);

        appointmentBillMapper.deleteAppointmentBill(form.ID);
        appointmentBillMapper.deleteAppointmentBillProject(form.ID);
        appointmentBillMapper.deleteAppointmentBillAudio(form.ID);

        List<AppointmentBillProjectEntity> project = new ArrayList<>();
        if (form.Project != null && form.Project.size() > 0) {
            for (AppointmentBillProjectAddForm appointmentBillProjectAddForm : form.Project) {
                AppointmentBillProjectEntity appointmentBillProjectEntity = new AppointmentBillProjectEntity();
                appointmentBillProjectEntity.AppointmentBillID = appointmentBillEntity.ID;
                appointmentBillProjectEntity.ProjectID = appointmentBillProjectAddForm.ProjectID;
                project.add(appointmentBillProjectEntity);
            }
        }

        List<AppointmentBillAudioEntity> audio = new ArrayList<>();
        if (form.Audio != null && form.Audio.size() > 0) {
            for (MiniAppointmentAudioForm miniAppointmentAudioForm : form.Audio) {
                AppointmentBillAudioEntity appointmentBillAudioEntity = new AppointmentBillAudioEntity();
                appointmentBillAudioEntity.AppointmentBillID = appointmentBillEntity.ID;
                appointmentBillAudioEntity.AudioURL = miniAppointmentAudioForm.AudioURL;
                audio.add(appointmentBillAudioEntity);
            }
        }

        if (project != null && project.size() > 0)
            appointmentBillMapper.createAppointmentBillProject(project);
        if (audio != null && audio.size() > 0)
            appointmentBillMapper.createAppointmentBillAudio(audio);

        AppointmentBillProcessEntity appointmentBillProcessEntity = new AppointmentBillProcessEntity();
        appointmentBillProcessEntity.AppointmentBillID = form.ID;
        appointmentBillProcessEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        appointmentBillProcessEntity.CreatedOn = new Date();
        appointmentBillProcessEntity.ActionType = form.Status;
        appointmentBillMapper.createAppointmentBillProcess(appointmentBillProcessEntity);


        if (isSend) {
            send(form.Status, appointmentBillEntity.ID);
        }

        return BaseOutput.success();
    }

    public BaseOutput getUnToShopCustomerCount(AppointmentCountQueryForm form) {
        List<MiniAppointmentCountOutputForm> list = appointmentBillMapper.getUnToShopCustomerCount(form.StartDate, form.EndDate, SystemUserUtil.getSystemEntityID(), null);
        return BaseOutputForm.success(list);
    }

    public BaseOutput getUnToShopCustomerCountSelf(AppointmentCountQueryForm form) {
        List<MiniAppointmentCountOutputForm> list = appointmentBillMapper.getUnToShopCustomerCount(form.StartDate, form.EndDate, SystemUserUtil.getSystemEntityID(), SystemUserUtil.getSystemUserID());
        return BaseOutputForm.success(list);
    }

    public BaseOutput getUnToShopCustomerCountEntity(AppointmentCountQueryForm form) {
        List<MiniAppointmentCountOutputForm> list = appointmentBillMapper.getUnToShopCustomerCount(form.StartDate, form.EndDate, SystemUserUtil.getSystemEntityID(), null);
        return BaseOutputForm.success(list);
    }


    public BaseOutput remainSelfList(AppointmentRemainQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<MiniAppointmentBillOutputForm> appointmentBillInfoList = appointmentBillMapper.appointmentBillInfoList(SystemUserUtil.getSystemEntityID(), SystemUserUtil.getSystemUserID(), null, form.AppointmentDate.trim().substring(0, 10), null, null, null, null, null, null, null, null, null, null);
        for (MiniAppointmentBillOutputForm miniAppointmentBillOutputForm : appointmentBillInfoList) {
            List<MiniAppointmentBillAppointmentAudioOutputForm> newAudio = new ArrayList<>();
            if (miniAppointmentBillOutputForm.Audio.size() > 0) {
                for (MiniAppointmentBillAppointmentAudioOutputForm miniAppointmentBillAppointmentAudioOutputForm : miniAppointmentBillOutputForm.Audio) {
                    if (miniAppointmentBillAppointmentAudioOutputForm.AudioID != null && !miniAppointmentBillAppointmentAudioOutputForm.AudioID.equals("")) {
                        JSONObject jsonObject = UploadVideoUtil.getPlayInfoAdress(miniAppointmentBillAppointmentAudioOutputForm.AudioID);
                        if (jsonObject != null && jsonObject.size() > 0) {
                            String duration = jsonObject.getString("duration");
                            if (StringUtils.isNotBlank(duration)) {
                                miniAppointmentBillAppointmentAudioOutputForm.Duration = duration.substring(0, duration.indexOf("."));
                                miniAppointmentBillAppointmentAudioOutputForm.AudioURL = UploadVideoUtil.getPlayInfoAdress(miniAppointmentBillAppointmentAudioOutputForm.AudioID).getString("url");
                                newAudio.add(miniAppointmentBillAppointmentAudioOutputForm);
                            }


                        }
                    }
                }
            }
            miniAppointmentBillOutputForm.Audio = newAudio;
        }
        return BasePageInfo.success(appointmentBillInfoList);
    }

    public BaseOutput remainEntityList(AppointmentRemainQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<MiniAppointmentBillOutputForm> appointmentBillInfoList = appointmentBillMapper.appointmentBillInfoList(SystemUserUtil.getSystemEntityID(), null, null, form.AppointmentDate.trim().substring(0, 10), null, null, null, null, null, null, null, null, null, null);
        for (MiniAppointmentBillOutputForm miniAppointmentBillOutputForm : appointmentBillInfoList) {
            List<MiniAppointmentBillAppointmentAudioOutputForm> newAudio = new ArrayList<>();
            if (miniAppointmentBillOutputForm.Audio.size() > 0) {
                for (MiniAppointmentBillAppointmentAudioOutputForm miniAppointmentBillAppointmentAudioOutputForm : miniAppointmentBillOutputForm.Audio) {
                    if (miniAppointmentBillAppointmentAudioOutputForm.AudioID != null && !miniAppointmentBillAppointmentAudioOutputForm.AudioID.equals("")) {
                        JSONObject jsonObject = UploadVideoUtil.getPlayInfoAdress(miniAppointmentBillAppointmentAudioOutputForm.AudioID);
                        if (jsonObject != null && jsonObject.size() > 0) {
                            String duration = jsonObject.getString("duration");
                            if (StringUtils.isNotBlank(duration)) {
                                miniAppointmentBillAppointmentAudioOutputForm.Duration = duration.substring(0, duration.indexOf("."));
                                miniAppointmentBillAppointmentAudioOutputForm.AudioURL = UploadVideoUtil.getPlayInfoAdress(miniAppointmentBillAppointmentAudioOutputForm.AudioID).getString("url");
                                newAudio.add(miniAppointmentBillAppointmentAudioOutputForm);
                            }

                        }
                    }
                }
            }
            miniAppointmentBillOutputForm.Audio = newAudio;
        }
        return BasePageInfo.success(appointmentBillInfoList);
    }

    public void send(String status, String id) {
        try {
            YingXiaoYunTenantEntity yingXiaoYunTenant = yingXiaoYunTenantMapper.getYingXiaoYunTenantEntity();
            if (yingXiaoYunTenant != null && yingXiaoYunTenant.Active) {
                YingXiaoYunAppointmentBillEntity yingXiaoYunCode = yingXiaoYunAppointmentBillMapper.getYingXiaoYunCode(id);
                if (yingXiaoYunCode != null) {
                    YXYAppointmentInputForm yxyInputForm = new YXYAppointmentInputForm();
                    yxyInputForm.EnterpriseCode = SystemUserUtil.getSystemEnterpriseCode();
                    yxyInputForm.TenantCode = yingXiaoYunTenant.TenantID;
                    yxyInputForm.AppointmentID = yingXiaoYunCode.YinXiaoYunID;
                    yxyInputForm.Status = status;

                    JSONObject param = new JSONObject();
                    param.put("code", "ZL_ChangeAppointmentStatus");
                    param.put("content", JSON.toJSONString(yxyInputForm));
                    JSONObject param2 = new JSONObject();
                    param2.put("request", param);
                    YingXiaoYunUtil.sendHttpPost(param2);
                }
            }
        } catch (Exception e) {
            logger.error("预约单据状态传输失败！", e);
            e.printStackTrace();
        }
    }

    public BaseOutput excel(AppointmentBillQueryForm form, HttpServletResponse response) {

        List<AppointmentBillOutputExcelForm> list = appointmentBillMapper.excel(SystemUserUtil.getSystemEntityID(), form.AppointmentBillID, form.Name, form.StartDate, form.EndDate, form.Type, form.Status, form.CustomerID, form.CreatedBy, form.CreatedOnStartDate, form.CreatedOnEndDate, form.ServicerID, form.ServicerEmployeeID, form.Channel, form.AppointmentTypeID, form.CustomerSourceID,form.CustomerLevelID,form.CustomerChannel);
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("预约列表", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), AppointmentBillOutputExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("预约列表").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    public BaseOutput process(AppointmentBillQueryForm form) {
        return BaseOutputForm.success(appointmentBillMapper.appointmentBillProcess(form.AppointmentBillID));
    }

    public BaseOutput customerList(SaleCustomerQueryForm form) {

        List<CustomerShortInfoOutputExtendForm> list = appointmentBillMapper.customerList(form.Name, SystemUserUtil.getSystemEntityID());
        return BaseOutputForm.success(list);
    }

    public BaseOutput getCustomerAppointmentAll(AppointmentCustomerForm form) {
        String startDate = DateTimeUtil.toyyyyMMdd2(form.AppointmentDate);
        List<AppointmentCustomerBillOutputForm> list = appointmentBillMapper.getCustomerAppointmentAll(form.CustomerID,SystemUserUtil.getSystemEntityID(),startDate);
        return BaseOutputForm.success(list);
    }

    public BaseOutput getCustomerAppointmentNumber(AppointmentCustomerNumberForm form) {
        String startDate = DateTimeUtil.toyyyyMMdd2(form.AppointmentDate);
        AppointmentCustomerNumberOutputForm outputForm = appointmentBillMapper.getCustomerAppointmentNumber(form.CustomerID,SystemUserUtil.getSystemEntityID(),startDate);
        return BaseOutputForm.success(outputForm);
    }
}