package com.zhelian.service.ibeauty.saleProjectCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.SalePackageCardProjectSchemeHandlerCommissionMapper;
import com.zhelian.mapper.ibeauty.SaleProjectSchemeHandlerCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SalePackageCardProjectSchemeHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SalePackageCardProjectSchemeHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleProjectSchemeHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleProjectSchemeHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.input.SaleProjectSchemeHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectSchemeHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectSchemeHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectsSchemeHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.SaleProjectSchemeHandlerCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleProjectSchemeHandlerCommissionService {

    @Autowired
    SaleProjectSchemeHandlerCommissionMapper saleProjectSchemeHandlerCommissionMapper;
    @Autowired
    SalePackageCardProjectSchemeHandlerCommissionMapper salePackageCardProjectSchemeHandlerCommissionMapper;

    public BaseOutput allSaleProjectSchemeHandlerCommission(SaleProjectSchemeHandlerCommissionQueryForm form) {
        List<SaleProjectSchemeHandlerCommissionOutputForm> list = saleProjectSchemeHandlerCommissionMapper.allSaleProjectSchemeHandlerCommission(form.EntityID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSalePackageCardProjectSchemeHandlerCommission(SaleProjectSchemeHandlerCommissionQueryForm form) {
        List<SaleProjectSchemeHandlerCommissionOutputForm> list = salePackageCardProjectSchemeHandlerCommissionMapper.allSalePackageCardProjectSchemeHandlerCommission(form.EntityID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleProjectSchemeHandlerCommission(SaleProjectsSchemeHandlerCommissionAddForm form) {

        saleProjectSchemeHandlerCommissionMapper.deleteSaleProjectSchemeHandler(form.EntityID);
        saleProjectSchemeHandlerCommissionMapper.deleteSaleProjectSchemeHandlerJobType(form.EntityID);
        List<SaleProjectSchemeHandlerCommissionEntity> listSaleProjectSchemeHandlerCommissionEntity = new ArrayList<>();
        List<SaleProjectSchemeHandlerJobTypeCommissionEntity> listSaleProjectSchemeHandlerJobTypeCommissionEntity = new ArrayList<>();
        if (form.ProjectHandler != null && form.ProjectHandler.size() > 0) {
            for (SaleProjectSchemeHandlerCommissionAddForm saleProjectSchemeHandlerAddForm : form.ProjectHandler) {
                if (saleProjectSchemeHandlerAddForm.PayRate != null || saleProjectSchemeHandlerAddForm.PayFixed != null ||
                        saleProjectSchemeHandlerAddForm.SavingCardRate != null || saleProjectSchemeHandlerAddForm.SavingCardFixed != null ||
                        saleProjectSchemeHandlerAddForm.SavingCardLargessRate != null || saleProjectSchemeHandlerAddForm.SavingCardLargessFixed != null ||
                        saleProjectSchemeHandlerAddForm.SpecialBenefit != null) {
                    SaleProjectSchemeHandlerCommissionEntity saleProjectSchemeHandlerCommissionEntity = new SaleProjectSchemeHandlerCommissionEntity();
                    saleProjectSchemeHandlerCommissionEntity.EntityID = form.EntityID;
                    saleProjectSchemeHandlerCommissionEntity.ProjectSaleHandlerID = saleProjectSchemeHandlerAddForm.SaleHandlerID;
                    saleProjectSchemeHandlerCommissionEntity.PayFixed = saleProjectSchemeHandlerAddForm.PayFixed;
                    saleProjectSchemeHandlerCommissionEntity.PayRate = saleProjectSchemeHandlerAddForm.PayRate;
                    saleProjectSchemeHandlerCommissionEntity.SavingCardFixed = saleProjectSchemeHandlerAddForm.SavingCardFixed;
                    saleProjectSchemeHandlerCommissionEntity.SavingCardRate = saleProjectSchemeHandlerAddForm.SavingCardRate;
                    saleProjectSchemeHandlerCommissionEntity.SavingCardLargessFixed = saleProjectSchemeHandlerAddForm.SavingCardLargessFixed;
                    saleProjectSchemeHandlerCommissionEntity.SavingCardLargessRate = saleProjectSchemeHandlerAddForm.SavingCardLargessRate;
                    saleProjectSchemeHandlerCommissionEntity.SpecialBenefit = saleProjectSchemeHandlerAddForm.SpecialBenefit;
                    listSaleProjectSchemeHandlerCommissionEntity.add(saleProjectSchemeHandlerCommissionEntity);
                }

                if (saleProjectSchemeHandlerAddForm.HandlerJobTypeCommission != null && saleProjectSchemeHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleProjectSchemeHandlerJobTypeCommissionAddForm saleProjectSchemeHandlerJobTypeCommissionAddForm : saleProjectSchemeHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleProjectSchemeHandlerJobTypeCommissionAddForm.PayRate != null || saleProjectSchemeHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                saleProjectSchemeHandlerJobTypeCommissionAddForm.SavingCardRate != null || saleProjectSchemeHandlerJobTypeCommissionAddForm.SavingCardFixed != null ||
                                saleProjectSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null || saleProjectSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessFixed != null ||
                                saleProjectSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SaleProjectSchemeHandlerJobTypeCommissionEntity saleProjectSchemeHandlerJobTypeCommissionEntity = new SaleProjectSchemeHandlerJobTypeCommissionEntity();
                            saleProjectSchemeHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            saleProjectSchemeHandlerJobTypeCommissionEntity.ProjectSaleHandlerID = saleProjectSchemeHandlerAddForm.SaleHandlerID;
                            saleProjectSchemeHandlerJobTypeCommissionEntity.JobTypeID = saleProjectSchemeHandlerJobTypeCommissionAddForm.JobTypeID;
                            saleProjectSchemeHandlerJobTypeCommissionEntity.PayFixed = saleProjectSchemeHandlerJobTypeCommissionAddForm.PayFixed;
                            saleProjectSchemeHandlerJobTypeCommissionEntity.PayRate = saleProjectSchemeHandlerJobTypeCommissionAddForm.PayRate;
                            saleProjectSchemeHandlerJobTypeCommissionEntity.SavingCardFixed = saleProjectSchemeHandlerJobTypeCommissionAddForm.SavingCardFixed;
                            saleProjectSchemeHandlerJobTypeCommissionEntity.SavingCardRate = saleProjectSchemeHandlerJobTypeCommissionAddForm.SavingCardRate;
                            saleProjectSchemeHandlerJobTypeCommissionEntity.SavingCardLargessFixed = saleProjectSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessFixed;
                            saleProjectSchemeHandlerJobTypeCommissionEntity.SavingCardLargessRate = saleProjectSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            saleProjectSchemeHandlerJobTypeCommissionEntity.SpecialBenefit = saleProjectSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            listSaleProjectSchemeHandlerJobTypeCommissionEntity.add(saleProjectSchemeHandlerJobTypeCommissionEntity);
                        }
                    }

                }
            }


        }

        salePackageCardProjectSchemeHandlerCommissionMapper.deleteSalePackageCardProjectSchemeHandler(form.EntityID);
        salePackageCardProjectSchemeHandlerCommissionMapper.deleteSalePackageCardProjectSchemeHandlerJobType(form.EntityID);
        List<SalePackageCardProjectSchemeHandlerCommissionEntity> listSalePackageCardProjectSchemeHandlerCommissionEntity = new ArrayList<>();
        List<SalePackageCardProjectSchemeHandlerJobTypeCommissionEntity> listSalePackageCardProjectSchemeHandlerJobTypeCommissionEntity = new ArrayList<>();
        if (form.PackageCardProjectHandler != null && form.PackageCardProjectHandler.size() > 0) {
            for (SaleProjectSchemeHandlerCommissionAddForm saleProjectSchemeHandlerCommissionAddForm : form.PackageCardProjectHandler) {
                if (saleProjectSchemeHandlerCommissionAddForm.PayRate != null || saleProjectSchemeHandlerCommissionAddForm.PayFixed != null ||
                        saleProjectSchemeHandlerCommissionAddForm.SavingCardRate != null || saleProjectSchemeHandlerCommissionAddForm.SavingCardFixed != null ||
                        saleProjectSchemeHandlerCommissionAddForm.SavingCardLargessRate != null || saleProjectSchemeHandlerCommissionAddForm.SavingCardLargessFixed != null ||
                        saleProjectSchemeHandlerCommissionAddForm.SpecialBenefit != null) {
                    SalePackageCardProjectSchemeHandlerCommissionEntity salePackageCardProjectSchemeHandlerCommissionEntity = new SalePackageCardProjectSchemeHandlerCommissionEntity();
                    salePackageCardProjectSchemeHandlerCommissionEntity.EntityID = form.EntityID;
                    salePackageCardProjectSchemeHandlerCommissionEntity.PackageCardSaleHandlerID = saleProjectSchemeHandlerCommissionAddForm.SaleHandlerID;
                    salePackageCardProjectSchemeHandlerCommissionEntity.PayFixed = saleProjectSchemeHandlerCommissionAddForm.PayFixed;
                    salePackageCardProjectSchemeHandlerCommissionEntity.PayRate = saleProjectSchemeHandlerCommissionAddForm.PayRate;
                    salePackageCardProjectSchemeHandlerCommissionEntity.SavingCardFixed = saleProjectSchemeHandlerCommissionAddForm.SavingCardFixed;
                    salePackageCardProjectSchemeHandlerCommissionEntity.SavingCardRate = saleProjectSchemeHandlerCommissionAddForm.SavingCardRate;
                    salePackageCardProjectSchemeHandlerCommissionEntity.SavingCardLargessFixed = saleProjectSchemeHandlerCommissionAddForm.SavingCardLargessFixed;
                    salePackageCardProjectSchemeHandlerCommissionEntity.SavingCardLargessRate = saleProjectSchemeHandlerCommissionAddForm.SavingCardLargessRate;
                    salePackageCardProjectSchemeHandlerCommissionEntity.SpecialBenefit = saleProjectSchemeHandlerCommissionAddForm.SpecialBenefit;
                    listSalePackageCardProjectSchemeHandlerCommissionEntity.add(salePackageCardProjectSchemeHandlerCommissionEntity);
                }
                if (saleProjectSchemeHandlerCommissionAddForm.HandlerJobTypeCommission != null && saleProjectSchemeHandlerCommissionAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleProjectSchemeHandlerJobTypeCommissionAddForm saleProjectSchemeHandlerJobTypeCommissionAddForm : saleProjectSchemeHandlerCommissionAddForm.HandlerJobTypeCommission) {
                        if (saleProjectSchemeHandlerJobTypeCommissionAddForm.PayRate != null || saleProjectSchemeHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                saleProjectSchemeHandlerJobTypeCommissionAddForm.SavingCardRate != null || saleProjectSchemeHandlerJobTypeCommissionAddForm.SavingCardFixed != null ||
                                saleProjectSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null || saleProjectSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessFixed != null ||
                                saleProjectSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SalePackageCardProjectSchemeHandlerJobTypeCommissionEntity salePackageCardProjectSchemeHandlerJobTypeCommissionEntity = new SalePackageCardProjectSchemeHandlerJobTypeCommissionEntity();
                            salePackageCardProjectSchemeHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            salePackageCardProjectSchemeHandlerJobTypeCommissionEntity.PackageCardSaleHandlerID = saleProjectSchemeHandlerCommissionAddForm.SaleHandlerID;
                            salePackageCardProjectSchemeHandlerJobTypeCommissionEntity.JobTypeID = saleProjectSchemeHandlerJobTypeCommissionAddForm.JobTypeID;
                            salePackageCardProjectSchemeHandlerJobTypeCommissionEntity.PayFixed = saleProjectSchemeHandlerJobTypeCommissionAddForm.PayFixed;
                            salePackageCardProjectSchemeHandlerJobTypeCommissionEntity.PayRate = saleProjectSchemeHandlerJobTypeCommissionAddForm.PayRate;
                            salePackageCardProjectSchemeHandlerJobTypeCommissionEntity.SavingCardFixed = saleProjectSchemeHandlerJobTypeCommissionAddForm.SavingCardFixed;
                            salePackageCardProjectSchemeHandlerJobTypeCommissionEntity.SavingCardRate = saleProjectSchemeHandlerJobTypeCommissionAddForm.SavingCardRate;
                            salePackageCardProjectSchemeHandlerJobTypeCommissionEntity.SavingCardLargessFixed = saleProjectSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessFixed;
                            salePackageCardProjectSchemeHandlerJobTypeCommissionEntity.SavingCardLargessRate = saleProjectSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            salePackageCardProjectSchemeHandlerJobTypeCommissionEntity.SpecialBenefit = saleProjectSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            listSalePackageCardProjectSchemeHandlerJobTypeCommissionEntity.add(salePackageCardProjectSchemeHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
        }
        if (listSaleProjectSchemeHandlerCommissionEntity != null && listSaleProjectSchemeHandlerCommissionEntity.size() > 0) {
            List<List<SaleProjectSchemeHandlerCommissionEntity>> subs = Lists.partition(listSaleProjectSchemeHandlerCommissionEntity, 20);
            for (List<SaleProjectSchemeHandlerCommissionEntity> entityList : subs) {
                saleProjectSchemeHandlerCommissionMapper.updateSaleProjectSchemeHandlerCommission(entityList);
            }
        }
        if (listSaleProjectSchemeHandlerJobTypeCommissionEntity != null && listSaleProjectSchemeHandlerJobTypeCommissionEntity.size() > 0) {
            List<List<SaleProjectSchemeHandlerJobTypeCommissionEntity>> subs = Lists.partition(listSaleProjectSchemeHandlerJobTypeCommissionEntity, 20);
            for (List<SaleProjectSchemeHandlerJobTypeCommissionEntity> entityList : subs) {
                saleProjectSchemeHandlerCommissionMapper.insertSaleProjectSchemeHandlerJobTypeCommission(entityList);
            }
        }
        if (listSalePackageCardProjectSchemeHandlerCommissionEntity != null && listSalePackageCardProjectSchemeHandlerCommissionEntity.size() > 0) {
            List<List<SalePackageCardProjectSchemeHandlerCommissionEntity>> subs = Lists.partition(listSalePackageCardProjectSchemeHandlerCommissionEntity, 20);
            for (List<SalePackageCardProjectSchemeHandlerCommissionEntity> entityList : subs) {
                salePackageCardProjectSchemeHandlerCommissionMapper.insertSalePackageCardProjectSchemeHandlerCommission(entityList);
            }
        }
        if (listSalePackageCardProjectSchemeHandlerJobTypeCommissionEntity != null && listSalePackageCardProjectSchemeHandlerJobTypeCommissionEntity.size() > 0) {
            List<List<SalePackageCardProjectSchemeHandlerJobTypeCommissionEntity>> subs = Lists.partition(listSalePackageCardProjectSchemeHandlerJobTypeCommissionEntity, 20);
            for (List<SalePackageCardProjectSchemeHandlerJobTypeCommissionEntity> entityList : subs) {
                salePackageCardProjectSchemeHandlerCommissionMapper.insertSalePackageCardProjectSchemeHandlerJobTypeCommission(entityList);
            }
        }

        return BaseOutput.success();
    }
}