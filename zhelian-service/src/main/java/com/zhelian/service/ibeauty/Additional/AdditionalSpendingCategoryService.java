package com.zhelian.service.ibeauty.Additional;

import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.AdditionalSpendingCategoryMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.AdditionalSpendingCategoryEntity;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingOrIncomeOrPaymentsCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingOrIncomeOrPaymentsCategoryQueryForm;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingOrIncomeOrPaymentsCategoryUpdateForm;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingOrIncomeOrPaymentsMoveForm;
import com.zhelian.model.ibeauty.form.output.AdditionalSpendingOrIncomeOrPaymentsCategoryOutputForm;
import com.zhelian.model.ibeauty.form.output.SpendingCategoryOrIncomeOrPaymentsOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * @className: SpendingCategoryService
 * @description:
 * @version: V1.0
 * @author: zhoutianxiang
 * @date: 2020/4/25 4:17 PM
 */
@Service
public class AdditionalSpendingCategoryService {

    @Autowired
    AdditionalSpendingCategoryMapper additionalSpendingCategoryMapper;

    public BaseOutput all(AdditionalSpendingOrIncomeOrPaymentsCategoryQueryForm form) {

        List<SpendingCategoryOrIncomeOrPaymentsOutputForm> list = additionalSpendingCategoryMapper.additionalSpendingCategoryList(form.Name, form.Active);

        return BaseOutputForm.success(list);
    }


    public BaseOutput create(AdditionalSpendingOrIncomeOrPaymentsCategoryAddForm form) {

        Integer Sequence = additionalSpendingCategoryMapper.getMaxSequence();
        AdditionalSpendingCategoryEntity additionalSpendingCategoryEntity = new AdditionalSpendingCategoryEntity();
        additionalSpendingCategoryEntity.Name = form.Name;
        additionalSpendingCategoryEntity.Sequence = Sequence != null ? Sequence + 1 : 1;

        additionalSpendingCategoryMapper.createAdditionalSpendingCategory(additionalSpendingCategoryEntity);

        return BaseOutput.success();
    }

    public BaseOutput update(AdditionalSpendingOrIncomeOrPaymentsCategoryUpdateForm form) {

        AdditionalSpendingCategoryEntity additionalSpendingCategoryEntity = new AdditionalSpendingCategoryEntity();
        additionalSpendingCategoryEntity.ID = form.ID;
        additionalSpendingCategoryEntity.Name = form.Name;
        additionalSpendingCategoryEntity.Active = form.Active;

        additionalSpendingCategoryMapper.updateAdditionalSpendingCategory(additionalSpendingCategoryEntity);

        return BaseOutput.success();
    }

    public BaseOutput move(AdditionalSpendingOrIncomeOrPaymentsMoveForm form) {

        //重新排序
        LinkedList<Integer> linkedList = additionalSpendingCategoryMapper.getIDOrderBySequenceASC(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<AdditionalSpendingCategoryEntity> list = new ArrayList<>();

        for (Integer ID : linkedList) {
            AdditionalSpendingCategoryEntity additionalSpendingCategoryEntity = new AdditionalSpendingCategoryEntity();
            additionalSpendingCategoryEntity.ID = ID;
            additionalSpendingCategoryEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(additionalSpendingCategoryEntity);
        }
        additionalSpendingCategoryMapper.updateSequence(list);
        return BaseOutput.success();
    }

    public BaseOutput getAdditionalSpendingCategory() {
        List<AdditionalSpendingOrIncomeOrPaymentsCategoryOutputForm> list = additionalSpendingCategoryMapper.getAdditionalSpendingCategory();
        return BaseOutputForm.success(list);
    }

}