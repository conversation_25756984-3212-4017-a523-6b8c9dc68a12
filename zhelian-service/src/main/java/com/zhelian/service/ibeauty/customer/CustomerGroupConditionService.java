package com.zhelian.service.ibeauty.customer;

import com.zhelian.mapper.ibeauty.CustomerGroupConditionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.CustomerGroupConditionEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Description:
 * @author: zhanglongyang
 * @date: 2021/05/17
 * @time: 16:48
 */
@Transactional
@Service
public class CustomerGroupConditionService {

    @Autowired
    private CustomerGroupConditionMapper customerGroupConditionMapper;

    //分群条件增加
    public BaseOutput addGroupCondition(CustomerGroupConditionEntity customerGroupCondition) {
        List<CustomerGroupConditionEntity> list = customerGroupConditionMapper.getGroupCondition(customerGroupCondition);
        if (list.size() >= 1) {
            return BaseOutput.failed("分群类型下的分群条件信息已存在!");
        } else {
            customerGroupConditionMapper.addGroupCondition(customerGroupCondition);
            return BaseOutput.success();
        }
    }

    //分群条件删除
    public BaseOutput delGroupCondition(CustomerGroupConditionEntity customerGroupCondition) {
        customerGroupConditionMapper.delGroupCondition(customerGroupCondition);
        return BaseOutput.success();
    }

    //查询获取一个分群类型下的条件
    public BaseOutput getManyGroupCondition(Integer Cg_Id) {
        List<CustomerGroupConditionEntity> list = customerGroupConditionMapper.getManyGroupCondition(Cg_Id);
        return BasePageInfo.success(list);
    }

    //根据条件id进行修改其中规则数值
    public BaseOutput updateGroupCondition(CustomerGroupConditionEntity groupCondition) {
        customerGroupConditionMapper.updateGroupCondition(groupCondition);
        return BaseOutput.success();
    }


}
