package com.zhelian.service.ibeauty.saleProjectCommission;


import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.SaleProjectCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SaleProjectCommissionEntity;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectsCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.SaleProjectCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleProjectCommissionService {

    @Autowired
    SaleProjectCommissionMapper saleProjectCommissionMapper;

    public BaseOutput allSaleProjectCommission(SaleProjectCategoryHandlerCommissionQueryForm form) {
        List<SaleProjectCommissionOutputForm> list = saleProjectCommissionMapper.allSaleProjectCommission(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleProjectCommission(SaleProjectsCommissionAddForm form) {

        saleProjectCommissionMapper.deleteSaleProjectCommissionByEntityAndCategory(form.EntityID, form.CategoryID);
        List<SaleProjectCommissionEntity> list = new ArrayList<>();
        if (form.Project != null && form.Project.size() > 0) {
            for (SaleProjectCommissionAddForm saleProjectCommissionAddForm : form.Project) {
                if (saleProjectCommissionAddForm.PayRate != null || saleProjectCommissionAddForm.PayFixed != null ||
                        saleProjectCommissionAddForm.SavingCardRate != null || saleProjectCommissionAddForm.SavingCardFixed != null ||
                        saleProjectCommissionAddForm.SavingCardLargessRate != null || saleProjectCommissionAddForm.SavingCardLargessFixed != null ||
                        saleProjectCommissionAddForm.SpecialBenefit != null) {
                    SaleProjectCommissionEntity saleProjectCommissionEntity = new SaleProjectCommissionEntity();
                    saleProjectCommissionEntity.EntityID = form.EntityID;
                    saleProjectCommissionEntity.ProjectID = saleProjectCommissionAddForm.ProjectID;
                    saleProjectCommissionEntity.PayFixed = saleProjectCommissionAddForm.PayFixed;
                    saleProjectCommissionEntity.PayRate = saleProjectCommissionAddForm.PayRate;
                    saleProjectCommissionEntity.SavingCardFixed = saleProjectCommissionAddForm.SavingCardFixed;
                    saleProjectCommissionEntity.SavingCardRate = saleProjectCommissionAddForm.SavingCardRate;
                    saleProjectCommissionEntity.SavingCardLargessFixed = saleProjectCommissionAddForm.SavingCardLargessFixed;
                    saleProjectCommissionEntity.SavingCardLargessRate = saleProjectCommissionAddForm.SavingCardLargessRate;
                    saleProjectCommissionEntity.SpecialBenefit = saleProjectCommissionAddForm.SpecialBenefit;
                    list.add(saleProjectCommissionEntity);
                }
            }
            List<List<SaleProjectCommissionEntity>> subs = Lists.partition(list, 20);
            for (List<SaleProjectCommissionEntity> entityList : subs) {
                saleProjectCommissionMapper.insertSaleProjectCommission(entityList);
            }
        }
        return BaseOutput.success();
    }

}