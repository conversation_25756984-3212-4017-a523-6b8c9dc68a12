package com.zhelian.service.ibeauty.savingCardAccount;

import com.zhelian.core.utils.DateTimeUtil;
import com.zhelian.mapper.ibeauty.SavingCardAccountMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SavingCardAccountEntity;
import com.zhelian.model.ibeauty.entity.SavingCardAccountExtendEntity;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.ibeauty.form.output.AllSavingCardAccountOutputForm;
import com.zhelian.model.ibeauty.form.output.SavingCardAccountOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @Param:
 * @returns:
 * @Author: xuehao
 * @Date: 2020/3/19 10:44
 */
@Service
public class SavingCardAccountService {

    @Autowired
    SavingCardAccountMapper savingCardAccountMapper;

    public BaseOutput allSavingCardAccount(SavingCardAccountQueryForm form) {

        List<AllSavingCardAccountOutputForm> list = savingCardAccountMapper.allSavingCardAccount(form.CustomerID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSomeGoodsSavingCardAccount(SavingCardAccountQueryForm form) {

        List<AllSavingCardAccountOutputForm> list = savingCardAccountMapper.allSomeGoodsSavingCardAccount(form.CustomerID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput savingCardProductAccount(SavingCardProductAccountQueryForm form) {
        List<SavingCardAccountOutputForm> list = savingCardAccountMapper.savingCardProductAccount(form.CustomerID, form.ProductID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput savingCardProjectAccount(SavingCardProjectAccountQueryForm form) {
        List<SavingCardAccountOutputForm> list = savingCardAccountMapper.savingCardProjectAccount(form.CustomerID, form.ProjectID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput savingCardGeneralCardAccount(SavingCardGeneralCardAccountQueryForm form) {
        List<SavingCardAccountOutputForm> list = savingCardAccountMapper.savingCardGeneralCardAccount(form.CustomerID, form.GeneralCardID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput savingCardTimeCardAccount(SavingCardTimeCardAccountQueryForm form) {
        List<SavingCardAccountOutputForm> list = savingCardAccountMapper.savingCardTimeCardAccount(form.CustomerID, form.TimeCardID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput savingCardPackageCardAccount(SavingCardPackageCardAccountQueryForm form) {
        List<SavingCardAccountOutputForm> list = savingCardAccountMapper.savingCardPackageCardAccount(form.CustomerID, form.PackageCardID);
        return BaseOutputForm.success(list);
    }

    /**
     * 功能描述:
     * <更新储值卡有效期>
     *
     * @param: SaleOrTreat  销售或消耗，1：销售，2：消耗
     * @param: savingCardAccountExtendEntity
     * @return: void
     * @exception:
     * @author: WangXianXian
     * @date: 2020/6/3 5:04 PM
     */
    public void updateSavingCardAccountValidDate(int SaleOrTreat, SavingCardAccountExtendEntity savingCardAccountExtendEntity, Date date) {

        if (SaleOrTreat == 1) {
            if (savingCardAccountExtendEntity.ValidType.equals(1)) {
                savingCardAccountExtendEntity.ValidDate = DateTimeUtil.getDateByAddDay(date, savingCardAccountExtendEntity.ValidDay);
                savingCardAccountMapper.updateSavingCardAccountValidDate(savingCardAccountExtendEntity);
            }
        } else if (SaleOrTreat == 2) {
            if (savingCardAccountExtendEntity.ValidType.equals(2)) {
                if (savingCardAccountExtendEntity.ValidDate == null) {
                    savingCardAccountExtendEntity.ValidDate = DateTimeUtil.getDateByAddDay(date, savingCardAccountExtendEntity.ValidDay);
                    savingCardAccountMapper.updateSavingCardAccountValidDate(savingCardAccountExtendEntity);
                }
            }
        }
    }

    public SavingCardAccountExtendEntity getSavingCardAccountByID(Integer ID) {
        return savingCardAccountMapper.getSavingCardAccountByID(ID);
    }

    public void updateSavingCardAccountList(List<SavingCardAccountExtendEntity> list) {
        savingCardAccountMapper.updateSavingCardAccountList(list);
    }

    public void createSavingCardAccount(List<SavingCardAccountEntity> list) {
        savingCardAccountMapper.createSavingCardAccount(list);
    }

}