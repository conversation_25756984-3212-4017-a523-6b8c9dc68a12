package com.zhelian.service.ibeauty.treatTimeCardCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.TreatTimeCardCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.TreatTimeCardCommissionEntity;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardsCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.TreatCardCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatTimeCardCommissionService {

    @Autowired
    TreatTimeCardCommissionMapper treatTimeCardCommissionMapper;


    public BaseOutput allTreatTimeCardCommission(TreatCardCategoryHandlerCommissionQueryForm form) {
        List<TreatCardCommissionOutputForm> list = treatTimeCardCommissionMapper.allTreatTimeCardCommission(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatTimeCardCommission(TreatCardsCommissionAddForm form) {

        treatTimeCardCommissionMapper.deleteTreatTimeCardCommissionByEntityIDAndCategory(form.EntityID, form.CategoryID);
        List<TreatTimeCardCommissionEntity> list = new ArrayList<>();
        if (form.Card != null && form.Card.size() > 0) {
            for (TreatCardCommissionAddForm treatCardCommissionAddForm : form.Card) {
                if (treatCardCommissionAddForm.PayRate != null || treatCardCommissionAddForm.PayFixed != null ||
                        treatCardCommissionAddForm.CardRate != null || treatCardCommissionAddForm.CardFixed != null ||
                        treatCardCommissionAddForm.CardLargessRate != null || treatCardCommissionAddForm.CardLargessFixed != null ||
                        treatCardCommissionAddForm.LargessRate != null || treatCardCommissionAddForm.LargessFixed != null ||
                        treatCardCommissionAddForm.SpecialBenefit != null) {
                    TreatTimeCardCommissionEntity treatTimeCardCommissionEntity = new TreatTimeCardCommissionEntity();
                    treatTimeCardCommissionEntity.EntityID = form.EntityID;
                    treatTimeCardCommissionEntity.TimeCardID = treatCardCommissionAddForm.CardID;
                    treatTimeCardCommissionEntity.PayRate = treatCardCommissionAddForm.PayRate;
                    treatTimeCardCommissionEntity.PayFixed = treatCardCommissionAddForm.PayFixed;
                    treatTimeCardCommissionEntity.CardRate = treatCardCommissionAddForm.CardRate;
                    treatTimeCardCommissionEntity.CardFixed = treatCardCommissionAddForm.CardFixed;
                    treatTimeCardCommissionEntity.CardLargessRate = treatCardCommissionAddForm.CardLargessRate;
                    treatTimeCardCommissionEntity.CardLargessFixed = treatCardCommissionAddForm.CardLargessFixed;
                    treatTimeCardCommissionEntity.LargessFixed = treatCardCommissionAddForm.LargessFixed;
                    treatTimeCardCommissionEntity.LargessRate = treatCardCommissionAddForm.LargessRate;
                    treatTimeCardCommissionEntity.SpecialBenefit = treatCardCommissionAddForm.SpecialBenefit;
                    list.add(treatTimeCardCommissionEntity);
                }
            }
            if (list != null && list.size() > 0) {
                List<List<TreatTimeCardCommissionEntity>> subs = Lists.partition(list, 20);
                for (List<TreatTimeCardCommissionEntity> entityList : subs) {
                    treatTimeCardCommissionMapper.insertTreatTimeCardCommission(entityList);
                }
            }
        }
        return BaseOutput.success();
    }

}