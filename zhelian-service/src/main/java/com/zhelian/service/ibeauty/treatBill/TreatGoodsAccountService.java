package com.zhelian.service.ibeauty.treatBill;

import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.ibeauty.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.SavingCardEntity;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.ibeauty.form.output.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TreatGoodsAccountService {
    @Autowired
    private ProjectAccountMapper projectAccountMapper;
    @Autowired
    private ProductAccountMapper productAccountMapper;
    @Autowired
    private SavingCardAccountMapper savingCardAccountMapper;
    @Autowired
    private GeneralCardAccountMapper generalCardAccountMapper;
    @Autowired
    private TimeCardAccountMapper timeCardAccountMapper;
    @Autowired
    private PackageCardAccountMapper packageCardAccountMapper;
    @Autowired
    SavingCardMapper savingCardMapper;

    /**
     * 功能描述：项目账户列表
     *
     * <AUTHOR>
     * @date 2020/7/1 9:41
     */
    public BaseOutput getProjectAccount(TreatProjectAccountForm form) {
        form.EntityID = SystemUserUtil.getSystemEntityID();
        return BaseOutputForm.success(projectAccountMapper.getProjectAccount(form));
    }

    /**
     * 功能描述：产品账户列表
     *
     * <AUTHOR>
     * @date 2020/7/1 10:28
     */
    public BaseOutput getProductAccount(TreatProductAccountForm form) {
        return BaseOutputForm.success(productAccountMapper.getProductAccount(form));
    }

    /**
     * 功能描述：储值卡账户列表
     *
     * <AUTHOR>
     * @date 2020/7/1 11:24
     */
    public BaseOutput getSavingCardAccount(TreatSavingCardAccountForm form) {
        form.EntityID = SystemUserUtil.getSystemEntityID();
        return BaseOutputForm.success(savingCardAccountMapper.getSavingCardAccount(form));
    }

    /**
     * 功能描述：储值卡消耗适用项目
     *
     * <AUTHOR>
     * @date 2020/7/2 14:43
     */
    public BaseOutput getSavingCardProjectList(TreatSavingCardAccountProjectForm form) {
        SavingCardEntity savingCardEntity = savingCardMapper.getSavingCardByID(form.SavingCardID);
        if (savingCardEntity == null) {
            return BaseOutput.failed("储值卡不存在");
        }
        //Type = 0 或 1 时，IsGoodsRange 为true 则查询全部，Type = 2 时，IsLargessGoodsRange 为true 则查询全部
        if (((form.Type.equals(1) || form.Type.equals(0)) && savingCardEntity.IsGoodsRange) || (form.Type.equals(2) && savingCardEntity.IsLargessGoodsRange)) {
            return BaseOutputForm.success(savingCardAccountMapper.getSavingCardAllProjectList(form.Name, SystemUserUtil.getSystemEntityID()));
        }
        List<SavingCardProjectOutputForm> list = savingCardAccountMapper.getSavingCardProjectList(form.SavingCardID, form.Type, form.Name, SystemUserUtil.getSystemEntityID());
        return BaseOutputForm.success(list);
    }

    public BaseOutput savingCardAccountProjectCategory(TreatSavingCardAccountProjectForm form) {
        SavingCardEntity savingCardEntity = savingCardMapper.getSavingCardByID(form.SavingCardID);
        if (savingCardEntity == null) {
            return BaseOutput.failed("储值卡不存在");
        }
        //Type = 0 或 1 时，IsGoodsRange 为true 则查询全部，Type = 2 时，IsLargessGoodsRange 为true 则查询全部
        if (((form.Type.equals(1) || form.Type.equals(0)) && savingCardEntity.IsGoodsRange) || (form.Type.equals(2) && savingCardEntity.IsLargessGoodsRange)) {
            return BaseOutputForm.success(savingCardAccountMapper.savingCardAccountAllProjectCategory(form.Name, SystemUserUtil.getSystemEntityID()));
        }
        List<SaleGoodsParentCategoryOutputForm> list = savingCardAccountMapper.savingCardAccountProjectCategory(form.SavingCardID, form.Type, form.Name, SystemUserUtil.getSystemEntityID());
        return BaseOutputForm.success(list);
    }

    public BaseOutput savingCardAccountProjectByCategory(TreatSavingCardAccountProjectForm form) {
        SavingCardEntity savingCardEntity = savingCardMapper.getSavingCardByID(form.SavingCardID);
        if (savingCardEntity == null) {
            return BaseOutput.failed("储值卡不存在");
        }

        PageHelper.startPage(form.PageNum, form.getPageSize());

        //Type = 0 或 1 时，IsGoodsRange 为true 则查询全部，Type = 2 时，IsLargessGoodsRange 为true 则查询全部
        if (((form.Type.equals(1) || form.Type.equals(0)) && savingCardEntity.IsGoodsRange) || (form.Type.equals(2) && savingCardEntity.IsLargessGoodsRange)) {
            return BasePageInfo.success(savingCardAccountMapper.getSavingCardAllProjectByCategory(form.Name, SystemUserUtil.getSystemEntityID(), form.CategoryID));
        }
        List<SavingCardProjectChildDetailOutputForm> list = savingCardAccountMapper.getSavingCardProjectByCategory(form.SavingCardID, form.Type, form.Name, SystemUserUtil.getSystemEntityID(), form.CategoryID);
        return BasePageInfo.success(list);
    }

    public BaseOutput savingCardAccountProjectCustomerDiscount(TreatSavingCardAccountProjectCustomerDiscountForm form) {
        TreatSavingCardAccountProjectCustomerDiscountOutForm outForm = savingCardAccountMapper.getSavingCardAccountProjectCustomerDiscount(form.ProjectID,form.CustomerID);
        return BaseOutputForm.success(outForm);
    }


    /**
     * 功能描述：通用次卡账户列表
     *
     * <AUTHOR>
     * @date 2020/7/2 15:50
     */
    public BaseOutput generalCardAccount(TreatGeneralCardAccountForm form) {
        form.EntityID = SystemUserUtil.getSystemEntityID();
        return BaseOutputForm.success(generalCardAccountMapper.generalCardAccount(form));
    }

    /**
     * 功能描述：通用次卡消耗适用项目
     *
     * <AUTHOR>
     * @date 2020/7/3 10:02
     */
    public BaseOutput generalCardAccountProject(GeneralCardProjectInputForm form) {
        return BaseOutputForm.success(generalCardAccountMapper.generalCardAccountProject(form.GeneralCardID, form.Name, form.GeneralCardAccountID, SystemUserUtil.getSystemEntityID()));
    }

    public BaseOutput generalCardAccountProjectCategory(GeneralCardProjectInputForm form) {
        return BaseOutputForm.success(generalCardAccountMapper.generalCardAccountProjectCategory(form.GeneralCardID, form.Name, SystemUserUtil.getSystemEntityID()));
    }

    public BaseOutput generalCardAccountProjectByCategory(GeneralCardProjectInputForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(generalCardAccountMapper.generalCardAccountProjectByCategory(form.GeneralCardID, form.Name, form.GeneralCardAccountID, SystemUserUtil.getSystemEntityID(), form.CategoryID));
    }

    public BaseOutput generalCardHistoricalData(GeneralCardProjectInputForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(generalCardAccountMapper.generalCardHistoricalData(form.GeneralCardID, form.Name, form.GeneralCardAccountID, SystemUserUtil.getSystemEntityID()));
    }

    /**
     * 功能描述：时效卡账户列表
     *
     * <AUTHOR>
     * @date 2020/7/3 17:03
     */
    public BaseOutput timeCardAccount(TreatTimeCardAccountForm form) {
        form.EntityID = SystemUserUtil.getSystemEntityID();
        return BaseOutputForm.success(timeCardAccountMapper.timeCardAccount(form));
    }

    /**
     * 功能描述：时效卡消耗适用项目
     *
     * <AUTHOR>
     * @date 2020/7/6 9:25
     */
    public BaseOutput timeCardAccountProject(TimeCardProjectInputForm form) {
        return BaseOutputForm.success(timeCardAccountMapper.timeCardAccountProject(form.TimeCardID, form.Name, SystemUserUtil.getSystemEntityID()));
    }

    public BaseOutput timeCardAccountProjectCategory(TimeCardProjectInputForm form) {
        return BaseOutputForm.success(timeCardAccountMapper.timeCardAccountProjectCategory(form.TimeCardID, form.Name, SystemUserUtil.getSystemEntityID()));
    }

    public BaseOutput timeCardAccountProjectByCategory(TimeCardProjectInputForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(timeCardAccountMapper.timeCardAccountProjectByCategory(form.TimeCardID, form.Name, SystemUserUtil.getSystemEntityID(), form.CategoryID));
    }

    /**
     * 功能描述：套餐卡账户列表
     *
     * <AUTHOR>
     * @date 2020/7/6 16:47
     */
    public BaseOutput packageCardAccount(TreatPackageCardAccountForm form) {
        return BaseOutputForm.success(packageCardAccountMapper.packageCardAccount(form));
    }

    /**
     * 功能描述：套餐卡账户明细
     *
     * <AUTHOR>
     * @date 2020/7/6 17:45
     */
    public BaseOutput packageCardAccountDetails(PackageCardAccountDetailsInputForm form) {
        String ValidDayName = packageCardAccountMapper.getPackageCardAccountValidDayName(form.PackageCardAccountID);
        return BaseOutputForm.success(packageCardAccountMapper.packageCardAccountDetails(form.PackageCardAccountID, SystemUserUtil.getSystemEntityID(), form.BillDate, ValidDayName));
    }

}
