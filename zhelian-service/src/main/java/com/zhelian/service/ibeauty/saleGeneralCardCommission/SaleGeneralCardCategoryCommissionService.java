package com.zhelian.service.ibeauty.saleGeneralCardCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.SaleGeneralCardCategoryCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SaleGeneralCardCategoryCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleGeneralCardCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.output.SaleGeneralCardCategoryCommissionOutputForm;
import com.zhelian.model.ibeauty.form.output.SaleGeneralCardCategoryOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleGeneralCardCategoryCommissionService {

    @Autowired
    SaleGeneralCardCategoryCommissionMapper saleGeneralCardCategoryCommissionMapper;


    public BaseOutput allSaleGeneralCardCategoryCommission(SaleGeneralCardCommissionSchemeAddForm form) {
        SaleGeneralCardCategoryOutputForm saleGeneralCardCategoryOutputForm = saleGeneralCardCategoryCommissionMapper.saleGeneralCardCategoryCommission(form.EntityID);
        List<SaleGeneralCardCategoryCommissionOutputForm> all = saleGeneralCardCategoryCommissionMapper.allSaleGeneralCardCategoryCommission(form.EntityID);
        saleGeneralCardCategoryOutputForm.Category = all;
        return BaseOutputForm.success(saleGeneralCardCategoryOutputForm);
    }

    @Transactional
    public BaseOutput updateSaleGeneralCardCategoryCommission(SaleGeneralCardCategoryAddForm form) {
        SaleGeneralCardCommissionSchemeEntity saleGeneralCardCommissionSchemeEntity = new SaleGeneralCardCommissionSchemeEntity();
        saleGeneralCardCommissionSchemeEntity.EntityID = form.EntityID;
        saleGeneralCardCommissionSchemeEntity.PayRate = form.PayRate;
        saleGeneralCardCommissionSchemeEntity.PayFixed = form.PayFixed;
        saleGeneralCardCommissionSchemeEntity.SavingCardRate = form.SavingCardRate;
        saleGeneralCardCommissionSchemeEntity.SavingCardFixed = form.SavingCardFixed;
        saleGeneralCardCommissionSchemeEntity.SavingCardLargessRate = form.SavingCardLargessRate;
        saleGeneralCardCommissionSchemeEntity.SavingCardLargessFixed = form.SavingCardLargessFixed;
        saleGeneralCardCommissionSchemeEntity.SpecialBenefit = form.SpecialBenefit;

        saleGeneralCardCategoryCommissionMapper.updateSaleGeneralCardCommissionScheme(saleGeneralCardCommissionSchemeEntity);
        saleGeneralCardCategoryCommissionMapper.deleteSaleGeneralCardCategoryCommission(form.EntityID);
        if (form.Category != null && form.Category.size() > 0) {
            List<SaleGeneralCardCategoryCommissionEntity> list = new ArrayList<>();
            for (SaleGeneralCardCategoryCommissionAddForm saleGeneralCardCategoryCommissionAddForm : form.Category) {
                if (saleGeneralCardCategoryCommissionAddForm.PayRate != null || saleGeneralCardCategoryCommissionAddForm.PayFixed != null ||
                        saleGeneralCardCategoryCommissionAddForm.SavingCardRate != null || saleGeneralCardCategoryCommissionAddForm.SavingCardFixed != null ||
                        saleGeneralCardCategoryCommissionAddForm.SavingCardLargessRate != null || saleGeneralCardCategoryCommissionAddForm.SavingCardLargessFixed != null ||
                        saleGeneralCardCategoryCommissionAddForm.SpecialBenefit != null) {
                    SaleGeneralCardCategoryCommissionEntity saleGeneralCardCategoryCommissionEntity = new SaleGeneralCardCategoryCommissionEntity();
                    saleGeneralCardCategoryCommissionEntity.EntityID = form.EntityID;
                    saleGeneralCardCategoryCommissionEntity.GeneralCardCategoryID = saleGeneralCardCategoryCommissionAddForm.CategoryID;
                    saleGeneralCardCategoryCommissionEntity.PayRate = saleGeneralCardCategoryCommissionAddForm.PayRate;
                    saleGeneralCardCategoryCommissionEntity.PayFixed = saleGeneralCardCategoryCommissionAddForm.PayFixed;
                    saleGeneralCardCategoryCommissionEntity.SavingCardRate = saleGeneralCardCategoryCommissionAddForm.SavingCardRate;
                    saleGeneralCardCategoryCommissionEntity.SavingCardFixed = saleGeneralCardCategoryCommissionAddForm.SavingCardFixed;
                    saleGeneralCardCategoryCommissionEntity.SavingCardLargessRate = saleGeneralCardCategoryCommissionAddForm.SavingCardLargessRate;
                    saleGeneralCardCategoryCommissionEntity.SavingCardLargessFixed = saleGeneralCardCategoryCommissionAddForm.SavingCardLargessFixed;
                    saleGeneralCardCategoryCommissionEntity.SpecialBenefit = saleGeneralCardCategoryCommissionAddForm.SpecialBenefit;
                    list.add(saleGeneralCardCategoryCommissionEntity);
                }
            }
            List<List<SaleGeneralCardCategoryCommissionEntity>> subs = Lists.partition(list, 20);
            for (List<SaleGeneralCardCategoryCommissionEntity> entityList : subs) {
                saleGeneralCardCategoryCommissionMapper.insertSaleGeneralCardCategoryCommissionEntity(entityList);
            }
        }

        return BaseOutput.success();
    }

}