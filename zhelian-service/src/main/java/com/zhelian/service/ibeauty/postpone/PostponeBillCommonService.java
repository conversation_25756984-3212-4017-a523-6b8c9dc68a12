package com.zhelian.service.ibeauty.postpone;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.PostponeBillUpdateForm;
import com.zhelian.service.weixin.WeiXinMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PostponeBillCommonService {
    @Autowired
    PostponeBillService postponeBillService;
    @Autowired
    WeiXinMessageService weiXinMessageService;

    public BaseOutput approval(PostponeBillUpdateForm form) {
        postponeBillService.approvalBill(form);
        if (form.ApprovalStatus.equals("30")) {
            weiXinMessageService.setPostponeBill(form.ID);
        }
        return BaseOutput.success();
    }
}
