package com.zhelian.service.ibeauty.saleHandler;


import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.TimeCardSaleHandlerMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.TimeCardSaleHandlerEntity;
import com.zhelian.model.ibeauty.entity.TimeCardSaleHandlerJobTypeEntity;
import com.zhelian.model.ibeauty.form.input.SaleHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerMoveForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerUpdateForm;
import com.zhelian.model.ibeauty.form.output.SaleHandlerOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * @Description:
 * @Param: No such property: code for class: Script1
 * @returns:
 * @Author: xuehao
 * @Date: 2020/3/31 11:25
 */
@Service
public class TimeCardSaleHandlerService {

    @Autowired
    TimeCardSaleHandlerMapper timeCardSaleHandlerMapper;


    public BaseOutput allTimeCardSaleHandler(SaleHandlerQueryForm form) {

        List<SaleHandlerOutputForm> list = timeCardSaleHandlerMapper.allTimeCardSaleHandler(form.Name, form.EntityID, form.Active);
        return BaseOutputForm.success(list);

    }

    @Transactional
    public BaseOutput createTimeCardSaleHandler(SaleHandlerAddForm form) {
        Integer Sequence = timeCardSaleHandlerMapper.getMaxSequence();
        TimeCardSaleHandlerEntity timeCardSaleHandlerEntity = new TimeCardSaleHandlerEntity();
        timeCardSaleHandlerEntity.Name = form.Name;
        timeCardSaleHandlerEntity.EntityID = form.EntityID;
        timeCardSaleHandlerEntity.Sequence = Sequence != null ? Sequence + 1 : 1;

        timeCardSaleHandlerMapper.createTimeCardSaleHandler(timeCardSaleHandlerEntity);

        insertSaleHandlerJobType(form.JobType, timeCardSaleHandlerEntity.ID);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateTimeCardSaleHandler(SaleHandlerUpdateForm form) {

        TimeCardSaleHandlerEntity timeCardSaleHandlerEntity = new TimeCardSaleHandlerEntity();
        timeCardSaleHandlerEntity.Name = form.Name;
        timeCardSaleHandlerEntity.ID = form.ID;
        timeCardSaleHandlerEntity.EntityID = form.EntityID;
        timeCardSaleHandlerEntity.Active = form.Active;

        timeCardSaleHandlerMapper.updateTimeCardSaleHandler(timeCardSaleHandlerEntity);

        timeCardSaleHandlerMapper.deleteSaleHandlerJobType(form.ID);

        insertSaleHandlerJobType(form.JobType, form.ID);

        return BaseOutput.success();
    }


    public void insertSaleHandlerJobType(Integer[] JobType, Integer ID) {
        List<TimeCardSaleHandlerJobTypeEntity> list = new ArrayList<>();
        for (Integer typeID : JobType) {
            TimeCardSaleHandlerJobTypeEntity timeCardSaleHandlerJobTypeEntity = new TimeCardSaleHandlerJobTypeEntity();
            timeCardSaleHandlerJobTypeEntity.TimeCardSaleHandlerID = ID;
            timeCardSaleHandlerJobTypeEntity.JobTypeID = typeID;
            list.add(timeCardSaleHandlerJobTypeEntity);
        }
        timeCardSaleHandlerMapper.insertTimeCardSaleHandlerJobType(list);
    }

    public BaseOutput moveTimeCardSaleHandler(SaleHandlerMoveForm form) {
        //重新排序
        LinkedList<Integer> linkedList = timeCardSaleHandlerMapper.getIDOrderBySequenceASC(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<TimeCardSaleHandlerEntity> list = new ArrayList<>();

        for (Integer ID : linkedList) {
            TimeCardSaleHandlerEntity timeCardSaleHandlerEntity = new TimeCardSaleHandlerEntity();
            timeCardSaleHandlerEntity.ID = ID;
            timeCardSaleHandlerEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(timeCardSaleHandlerEntity);
        }
        timeCardSaleHandlerMapper.updateSequence(list);

        return BaseOutput.success();
    }

}