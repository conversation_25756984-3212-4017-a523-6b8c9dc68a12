package com.zhelian.service.ibeauty.treatBill;

import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.ibeauty.TreatHandlerMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.output.AllTreatHandlerOutputForm;
import com.zhelian.model.ibeauty.form.output.TreatAllHandlerOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @program: zhelian
 * @description: 消耗经手人service
 * @author: wjz
 * @create: 2020-07-07 11:20
 **/
@Service
public class TreatHandlerService {
    @Autowired
    TreatHandlerMapper treatHandlerMapper;

    public BaseOutput projectHandler() {
        Integer entityID = SystemUserUtil.getSystemEntityID();
        List<AllTreatHandlerOutputForm> projectHandlerOutputForms = treatHandlerMapper.projectTreatHandler(entityID);
        return BaseOutputForm.success(projectHandlerOutputForms);
    }

    public BaseOutput productHandler() {
        Integer entityID = SystemUserUtil.getSystemEntityID();
        List<AllTreatHandlerOutputForm> productHandlerOutputForms = treatHandlerMapper.productTreatHandler(entityID);
        return BaseOutputForm.success(productHandlerOutputForms);
    }

    public BaseOutput savingCardHandler() {
        Integer entityID = SystemUserUtil.getSystemEntityID();
        List<AllTreatHandlerOutputForm> savingCardHandlerOutputForms = treatHandlerMapper.savingCardTreatHandler(entityID);
        return BaseOutputForm.success(savingCardHandlerOutputForms);
    }

    public BaseOutput allHandler(List<String> GoodTypes) {
        Integer entityID = SystemUserUtil.getSystemEntityID();
        List<TreatAllHandlerOutputForm> outputForms = treatHandlerMapper.allHandler(entityID,GoodTypes);
        return BaseOutputForm.success(outputForms);
    }
}
