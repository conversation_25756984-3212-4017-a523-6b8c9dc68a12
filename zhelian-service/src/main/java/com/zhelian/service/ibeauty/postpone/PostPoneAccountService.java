package com.zhelian.service.ibeauty.postpone;

import com.zhelian.mapper.ibeauty.GeneralCardAccountMapper;
import com.zhelian.mapper.ibeauty.PackageCardAccountMapper;
import com.zhelian.mapper.ibeauty.SavingCardAccountMapper;
import com.zhelian.mapper.ibeauty.TimeCardAccountMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.PostponeAccountQueryForm;
import com.zhelian.model.ibeauty.form.output.GeneralCardAccountOutputForm;
import com.zhelian.model.ibeauty.form.output.PackageCardAccountOutputForm;
import com.zhelian.model.ibeauty.form.output.SavingCardAccountForm;
import com.zhelian.model.ibeauty.form.output.TimeCardAccountOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class PostPoneAccountService {
    @Autowired
    private SavingCardAccountMapper savingCardAccountMapper;
    @Autowired
    private GeneralCardAccountMapper generalCardAccountMapper;
    @Autowired
    private TimeCardAccountMapper timeCardAccountMapper;
    @Autowired
    private PackageCardAccountMapper packageCardAccountMapper;

    public BaseOutput getPostPoneSavingCardAccount(PostponeAccountQueryForm form) {
        List<SavingCardAccountForm> list = savingCardAccountMapper.getPostPoneSavingCardAccount(form.CustomerID, form.Name);
        return BaseOutputForm.success(list);
    }

    public BaseOutput getPostPoneGeneralCardAccount(PostponeAccountQueryForm form) {
        List<GeneralCardAccountOutputForm> list = generalCardAccountMapper.getPostPoneGeneralCardAccount(form.CustomerID, form.Name);
        return BaseOutputForm.success(list);
    }

    public BaseOutput getPostPoneTimeCardAccount(PostponeAccountQueryForm form) {
        List<TimeCardAccountOutputForm> list = timeCardAccountMapper.getPostPoneTimeCardAccount(form.CustomerID, form.Name);
        return BaseOutputForm.success(list);
    }

    public BaseOutput getPostPonePackageCardAccount(PostponeAccountQueryForm form) {
        List<PackageCardAccountOutputForm> list = packageCardAccountMapper.getPostPonePackageCardAccount(form.CustomerID, form.Name);
        return BaseOutputForm.success(list);
    }
}