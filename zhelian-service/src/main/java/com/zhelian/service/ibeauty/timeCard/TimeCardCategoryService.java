package com.zhelian.service.ibeauty.timeCard;

import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.TimeCardCategoryMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.TimeCardCategoryEntity;
import com.zhelian.model.ibeauty.form.input.TimeCardCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.TimeCardCategoryMoveForm;
import com.zhelian.model.ibeauty.form.input.TimeCardCategoryQueryForm;
import com.zhelian.model.ibeauty.form.input.TimeCardCategoryUpdateForm;
import com.zhelian.model.ibeauty.form.output.TimeCardCategoryOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * @Description:
 * @Param:
 * @returns:
 * @Author: xuehao
 * @Date: 2020/3/18 15:27
 */
@Service
public class TimeCardCategoryService {

    @Autowired
    TimeCardCategoryMapper timeCardCategoryMapper;

    public BaseOutput timeCardCategoryList(TimeCardCategoryQueryForm form) {

        List<TimeCardCategoryOutputForm> list = timeCardCategoryMapper.timeCardCategoryList(form.Name, form.Active);

        return BaseOutputForm.success(list);
    }

    public BaseOutput createTimeCardCategory(TimeCardCategoryAddForm form) {
        Integer Sequence = timeCardCategoryMapper.getMaxSequence();
        TimeCardCategoryEntity timeCardCategoryEntity = new TimeCardCategoryEntity();
        timeCardCategoryEntity.Name = form.Name;
        timeCardCategoryEntity.Sequence = Sequence != null ? Sequence + 1 : 1;
        timeCardCategoryMapper.createTimeCardCategory(timeCardCategoryEntity);
        return BaseOutput.success();
    }

    public BaseOutput updateTimeCardCategory(TimeCardCategoryUpdateForm form) {
        if (form.Active == false) {
            Integer count = timeCardCategoryMapper.timeCardByCategoryCount(form.ID);
            if (count > 0)
                return BaseOutput.failed("该分类下有时效卡，不可以改为无效");
        }
        TimeCardCategoryEntity timeCardCategoryEntity = new TimeCardCategoryEntity();
        timeCardCategoryEntity.Name = form.Name;
        timeCardCategoryEntity.ID = form.ID;
        timeCardCategoryEntity.Active = form.Active;
        timeCardCategoryMapper.updateTimeCardCategory(timeCardCategoryEntity);

        return BaseOutput.success();
    }

    public BaseOutput move(TimeCardCategoryMoveForm form) {
        //重新排序
        LinkedList<Integer> linkedList = timeCardCategoryMapper.getASCList(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<TimeCardCategoryEntity> list = new ArrayList<>();
        for (Integer ID : linkedList) {
            TimeCardCategoryEntity timeCardCategoryEntity = new TimeCardCategoryEntity();
            timeCardCategoryEntity.ID = ID;
            timeCardCategoryEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(timeCardCategoryEntity);
        }

        timeCardCategoryMapper.updateSequence(list);
        return BaseOutput.success();
    }
}