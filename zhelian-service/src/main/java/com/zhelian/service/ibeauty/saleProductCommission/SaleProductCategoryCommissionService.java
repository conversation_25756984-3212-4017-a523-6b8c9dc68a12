package com.zhelian.service.ibeauty.saleProductCommission;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.SaleProductCategoryCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SaleProductCategoryCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleProductCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductChildCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.output.SaleProductCategoryCommissionOutputForm;
import com.zhelian.model.ibeauty.form.output.SaleProductCategoryOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import static com.zhelian.core.utils.ListUtil.listToTree;


@Service
public class SaleProductCategoryCommissionService {

    @Autowired
    SaleProductCategoryCommissionMapper saleProductCategoryCommissionMapper;


    public BaseOutput allSaleProductCategoryCommission(SaleProductCommissionSchemeAddForm form) {
        SaleProductCategoryOutputForm saleProductCategoryOutputForm = saleProductCategoryCommissionMapper.saleProductCategoryCommission(form.EntityID);
        List<SaleProductCategoryCommissionOutputForm> all = saleProductCategoryCommissionMapper.allSaleProductCategoryCommission(form.EntityID);
        JSONArray result = listToTree(JSONArray.parseArray(JSON.toJSONString(all, JSONWriter.Feature.WriteMapNullValue)), "CategoryID", "ParentID", "Child");
        saleProductCategoryOutputForm.Category = result;
        return BaseOutputForm.success(saleProductCategoryOutputForm);
    }

    @Transactional
    public BaseOutput updateSaleProductCategoryCommission(SaleProductCategoryAddForm form) {
        SaleProductCommissionSchemeEntity saleProductCommissionSchemeEntity = new SaleProductCommissionSchemeEntity();
        saleProductCommissionSchemeEntity.EntityID = form.EntityID;
        saleProductCommissionSchemeEntity.PayRate = form.PayRate;
        saleProductCommissionSchemeEntity.PayFixed = form.PayFixed;
        saleProductCommissionSchemeEntity.SavingCardRate = form.SavingCardRate;
        saleProductCommissionSchemeEntity.SavingCardFixed = form.SavingCardFixed;
        saleProductCommissionSchemeEntity.SavingCardLargessRate = form.SavingCardLargessRate;
        saleProductCommissionSchemeEntity.SavingCardLargessFixed = form.SavingCardLargessFixed;
        saleProductCommissionSchemeEntity.SpecialBenefit = form.SpecialBenefit;

        saleProductCategoryCommissionMapper.updateSaleProductCommissionScheme(saleProductCommissionSchemeEntity);

        saleProductCategoryCommissionMapper.deleteSaleProductCategoryCommissionEntity(form.EntityID);

        List<SaleProductCategoryCommissionEntity> list = new ArrayList<>();
        if (form.Category != null && form.Category.size() > 0) {
            for (SaleProductCategoryCommissionAddForm saleProductCategoryCommissionAddForm : form.Category) {
                if (saleProductCategoryCommissionAddForm.PayRate != null || saleProductCategoryCommissionAddForm.PayFixed != null ||
                        saleProductCategoryCommissionAddForm.SavingCardRate != null || saleProductCategoryCommissionAddForm.SavingCardFixed != null ||
                        saleProductCategoryCommissionAddForm.SavingCardLargessRate != null || saleProductCategoryCommissionAddForm.SavingCardLargessFixed != null ||
                        saleProductCategoryCommissionAddForm.SpecialBenefit != null) {
                    SaleProductCategoryCommissionEntity saleProductCategoryCommissionEntity = new SaleProductCategoryCommissionEntity();
                    saleProductCategoryCommissionEntity.EntityID = form.EntityID;
                    saleProductCategoryCommissionEntity.ProductCategoryID = saleProductCategoryCommissionAddForm.CategoryID;
                    saleProductCategoryCommissionEntity.PayRate = saleProductCategoryCommissionAddForm.PayRate;
                    saleProductCategoryCommissionEntity.PayFixed = saleProductCategoryCommissionAddForm.PayFixed;
                    saleProductCategoryCommissionEntity.SavingCardRate = saleProductCategoryCommissionAddForm.SavingCardRate;
                    saleProductCategoryCommissionEntity.SavingCardFixed = saleProductCategoryCommissionAddForm.SavingCardFixed;
                    saleProductCategoryCommissionEntity.SavingCardLargessRate = saleProductCategoryCommissionAddForm.SavingCardLargessRate;
                    saleProductCategoryCommissionEntity.SavingCardLargessFixed = saleProductCategoryCommissionAddForm.SavingCardLargessFixed;
                    saleProductCategoryCommissionEntity.SpecialBenefit = saleProductCategoryCommissionAddForm.SpecialBenefit;
                    list.add(saleProductCategoryCommissionEntity);
                }
                if (saleProductCategoryCommissionAddForm.Child != null && saleProductCategoryCommissionAddForm.Child.size() > 0) {

                    for (SaleProductChildCategoryCommissionAddForm saleProductChildCategoryCommissionAddForm : saleProductCategoryCommissionAddForm.Child) {
                        if (saleProductChildCategoryCommissionAddForm.PayRate != null || saleProductChildCategoryCommissionAddForm.PayFixed != null ||
                                saleProductChildCategoryCommissionAddForm.SavingCardRate != null || saleProductChildCategoryCommissionAddForm.SavingCardFixed != null ||
                                saleProductChildCategoryCommissionAddForm.SavingCardLargessRate != null || saleProductChildCategoryCommissionAddForm.SavingCardLargessFixed != null ||
                                saleProductChildCategoryCommissionAddForm.SpecialBenefit != null) {
                            SaleProductCategoryCommissionEntity saleProductCategoryCommissionEntity2 = new SaleProductCategoryCommissionEntity();
                            saleProductCategoryCommissionEntity2.EntityID = form.EntityID;
                            saleProductCategoryCommissionEntity2.ProductCategoryID = saleProductChildCategoryCommissionAddForm.CategoryID;
                            saleProductCategoryCommissionEntity2.PayRate = saleProductChildCategoryCommissionAddForm.PayRate;
                            saleProductCategoryCommissionEntity2.PayFixed = saleProductChildCategoryCommissionAddForm.PayFixed;
                            saleProductCategoryCommissionEntity2.SavingCardRate = saleProductChildCategoryCommissionAddForm.SavingCardRate;
                            saleProductCategoryCommissionEntity2.SavingCardFixed = saleProductChildCategoryCommissionAddForm.SavingCardFixed;
                            saleProductCategoryCommissionEntity2.SavingCardLargessRate = saleProductChildCategoryCommissionAddForm.SavingCardLargessRate;
                            saleProductCategoryCommissionEntity2.SavingCardLargessFixed = saleProductChildCategoryCommissionAddForm.SavingCardLargessFixed;
                            saleProductCategoryCommissionEntity2.SpecialBenefit = saleProductChildCategoryCommissionAddForm.SpecialBenefit;
                            list.add(saleProductCategoryCommissionEntity2);
                        }
                    }
                }
            }
        }
        if (list != null && list.size() > 0) {
            List<List<SaleProductCategoryCommissionEntity>> subs = Lists.partition(list, 20);
            for (List<SaleProductCategoryCommissionEntity> entityList : subs) {
                saleProductCategoryCommissionMapper.insertSaleProductCategoryCommissionEntity(entityList);
            }
        }
        return BaseOutput.success();
    }

}