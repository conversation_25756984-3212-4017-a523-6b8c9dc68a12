package com.zhelian.service.ibeauty.treatProductCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.TreatProductHandlerCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.TreatProductHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.TreatProductHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatProductHandlersCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.TreatProductHandlerCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatProductHandlerCommissionService {

    @Autowired
    TreatProductHandlerCommissionMapper treatProductHandlerCommissionMapper;

    public BaseOutput allTreatProductHandlerCommission(TreatProductHandlerCommissionQueryForm form) {
        List<TreatProductHandlerCommissionOutputForm> list = treatProductHandlerCommissionMapper.allTreatProductHandlerCommission(form.EntityID, form.ProductID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatProductHandlerCommission(TreatProductHandlersCommissionAddForm form) {

        treatProductHandlerCommissionMapper.deleteTreatProductHandler(form.EntityID, form.ProductID);
        treatProductHandlerCommissionMapper.deleteTreatProductHandlerJobType(form.EntityID, form.ProductID);

        if (form.Handler != null && form.Handler.size() > 0) {

            List<TreatProductHandlerCommissionEntity> listTreatProductHandlerCommissionEntity = new ArrayList<>();

            List<TreatProductHandlerJobTypeCommissionEntity> listTreatProductHandlerJobTypeCommissionEntity = new ArrayList<>();

            for (TreatProductHandlerCommissionAddForm treatProductHandlerAddForm : form.Handler) {
                if (treatProductHandlerAddForm.PayRate != null || treatProductHandlerAddForm.PayFixed != null ||
                        treatProductHandlerAddForm.CardRate != null || treatProductHandlerAddForm.CardFixed != null ||
                        treatProductHandlerAddForm.CardLargessRate != null || treatProductHandlerAddForm.CardLargessFixed != null ||
                        treatProductHandlerAddForm.LargessRate != null || treatProductHandlerAddForm.LargessFixed != null ||
                        treatProductHandlerAddForm.SpecialBenefit != null) {
                    TreatProductHandlerCommissionEntity treatProductHandlerCommissionEntity = new TreatProductHandlerCommissionEntity();
                    treatProductHandlerCommissionEntity.EntityID = form.EntityID;
                    treatProductHandlerCommissionEntity.ProductID = form.ProductID;
                    treatProductHandlerCommissionEntity.ProductTreatHandlerID = treatProductHandlerAddForm.TreatHandlerID;
                    treatProductHandlerCommissionEntity.PayRate = treatProductHandlerAddForm.PayRate;
                    treatProductHandlerCommissionEntity.PayFixed = treatProductHandlerAddForm.PayFixed;
                    treatProductHandlerCommissionEntity.CardRate = treatProductHandlerAddForm.CardRate;
                    treatProductHandlerCommissionEntity.CardFixed = treatProductHandlerAddForm.CardFixed;
                    treatProductHandlerCommissionEntity.CardLargessRate = treatProductHandlerAddForm.CardLargessRate;
                    treatProductHandlerCommissionEntity.CardLargessFixed = treatProductHandlerAddForm.CardLargessFixed;
                    treatProductHandlerCommissionEntity.LargessFixed = treatProductHandlerAddForm.LargessFixed;
                    treatProductHandlerCommissionEntity.LargessRate = treatProductHandlerAddForm.LargessRate;
                    treatProductHandlerCommissionEntity.SpecialBenefit = treatProductHandlerAddForm.SpecialBenefit;
                    listTreatProductHandlerCommissionEntity.add(treatProductHandlerCommissionEntity);
                }
                if (treatProductHandlerAddForm.HandlerJobTypeCommission != null && treatProductHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (TreatProductCategoryHandlerJobTypeCommissionAddForm treatProductCategoryHandlerJobTypeCommissionAddForm : treatProductHandlerAddForm.HandlerJobTypeCommission) {
                        if (treatProductCategoryHandlerJobTypeCommissionAddForm.PayRate != null || treatProductCategoryHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                treatProductCategoryHandlerJobTypeCommissionAddForm.LargessRate != null || treatProductCategoryHandlerJobTypeCommissionAddForm.LargessFixed != null ||
                                treatProductCategoryHandlerJobTypeCommissionAddForm.CardLargessRate != null || treatProductCategoryHandlerJobTypeCommissionAddForm.CardLargessFixed != null ||
                                treatProductCategoryHandlerJobTypeCommissionAddForm.LargessRate != null || treatProductCategoryHandlerJobTypeCommissionAddForm.LargessFixed != null ||
                                treatProductCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            TreatProductHandlerJobTypeCommissionEntity treatProductHandlerJobTypeCommissionEntity = new TreatProductHandlerJobTypeCommissionEntity();
                            treatProductHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            treatProductHandlerJobTypeCommissionEntity.ProductID = form.ProductID;
                            treatProductHandlerJobTypeCommissionEntity.ProductTreatHandlerID = treatProductHandlerAddForm.TreatHandlerID;
                            treatProductHandlerJobTypeCommissionEntity.JobTypeID = treatProductCategoryHandlerJobTypeCommissionAddForm.JobTypeID;
                            treatProductHandlerJobTypeCommissionEntity.PayRate = treatProductCategoryHandlerJobTypeCommissionAddForm.PayRate;
                            treatProductHandlerJobTypeCommissionEntity.PayFixed = treatProductCategoryHandlerJobTypeCommissionAddForm.PayFixed;
                            treatProductHandlerJobTypeCommissionEntity.CardRate = treatProductCategoryHandlerJobTypeCommissionAddForm.CardRate;
                            treatProductHandlerJobTypeCommissionEntity.CardFixed = treatProductCategoryHandlerJobTypeCommissionAddForm.CardFixed;
                            treatProductHandlerJobTypeCommissionEntity.CardLargessRate = treatProductCategoryHandlerJobTypeCommissionAddForm.CardLargessRate;
                            treatProductHandlerJobTypeCommissionEntity.CardLargessFixed = treatProductCategoryHandlerJobTypeCommissionAddForm.CardLargessFixed;
                            treatProductHandlerJobTypeCommissionEntity.LargessFixed = treatProductCategoryHandlerJobTypeCommissionAddForm.LargessFixed;
                            treatProductHandlerJobTypeCommissionEntity.LargessRate = treatProductCategoryHandlerJobTypeCommissionAddForm.LargessRate;
                            treatProductHandlerJobTypeCommissionEntity.SpecialBenefit = treatProductCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            listTreatProductHandlerJobTypeCommissionEntity.add(treatProductHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
            if (listTreatProductHandlerCommissionEntity != null && listTreatProductHandlerCommissionEntity.size() > 0) {
                List<List<TreatProductHandlerCommissionEntity>> subs = Lists.partition(listTreatProductHandlerCommissionEntity, 20);
                for (List<TreatProductHandlerCommissionEntity> entityList : subs) {
                    treatProductHandlerCommissionMapper.updateTreatProductHandlerCommission(entityList);
                }
            }
            if (listTreatProductHandlerJobTypeCommissionEntity != null && listTreatProductHandlerJobTypeCommissionEntity.size() > 0) {
                List<List<TreatProductHandlerJobTypeCommissionEntity>> subs = Lists.partition(listTreatProductHandlerJobTypeCommissionEntity, 20);
                for (List<TreatProductHandlerJobTypeCommissionEntity> entityList : subs) {
                    treatProductHandlerCommissionMapper.insertTreatProductHandlerJobTypeCommission(entityList);
                }
            }
        }
        return BaseOutput.success();
    }
}