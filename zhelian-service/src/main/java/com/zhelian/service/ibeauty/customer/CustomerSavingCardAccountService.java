package com.zhelian.service.ibeauty.customer;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.ibeauty.CustomerSavingCardAccountMapper;
import com.zhelian.mapper.ibeauty.SavingCardAccountMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.SavingCardAccountEntity;
import com.zhelian.model.ibeauty.form.input.AccountQueryForm;
import com.zhelian.model.ibeauty.form.output.CustomerRepaymentAccountOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class CustomerSavingCardAccountService {

    @Autowired
    private CustomerSavingCardAccountMapper customerTimeCardAccountMapper;
    @Autowired
    private SavingCardAccountMapper savingCardAccountMapper;

    public BaseOutput treatBill(AccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(customerTimeCardAccountMapper.getTreatBill(form.ID));
    }

    public BaseOutput refundBill(AccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(customerTimeCardAccountMapper.getRefundBill(form.ID));
    }

    public BaseOutput refundTreatBill(AccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(customerTimeCardAccountMapper.getRefundTreatBill(form.ID));
    }

    public BaseOutput transferBill(AccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(customerTimeCardAccountMapper.getTransferBill(form.ID));
    }

    public BaseOutput repayment(AccountQueryForm form) {
        SavingCardAccountEntity savingCardAccountByID = savingCardAccountMapper.getSavingCardAccountByID(Integer.parseInt(form.ID));

        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CustomerRepaymentAccountOutputForm> list = null;
        if (savingCardAccountByID.PackageCardAccountID == null) {
            list = customerTimeCardAccountMapper.getRepayment(form.ID);
        } else {
            list = customerTimeCardAccountMapper.getPackageCardRepayment(form.ID);
        }

        return BasePageInfo.success(list);
    }

    public BaseOutput arrearAmountProcess(AccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(customerTimeCardAccountMapper.getArrearAmountProcess(form.ID));
    }

    public BaseOutput savingCardDeduction(AccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(customerTimeCardAccountMapper.savingCardDeduction(form.ID));
    }

    public BaseOutput rechargeBill(AccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(customerTimeCardAccountMapper.rechargeBill(form.ID));
    }
}