package com.zhelian.service.ibeauty.salePackageCardCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.SalePackageCardCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SalePackageCardCommissionEntity;
import com.zhelian.model.ibeauty.form.input.SalePackageCardCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SalePackageCardCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SalePackageCardsCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.SalePackageCardCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SalePackageCardCommissionService {

    @Autowired
    SalePackageCardCommissionMapper salePackageCardCommissionMapper;


    public BaseOutput allSalePackageCardCommission(SalePackageCardCategoryHandlerCommissionQueryForm form) {
        List<SalePackageCardCommissionOutputForm> list = salePackageCardCommissionMapper.allSalePackageCardCommission(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSalePackageCardCommission(SalePackageCardsCommissionAddForm form) {

        salePackageCardCommissionMapper.deleteSalePackageCardCommissionByEntityIDAndCategory(form.EntityID, form.CategoryID);
        List list = new ArrayList<>();
        if (form.PackageCard != null && form.PackageCard.size() > 0) {
            for (SalePackageCardCommissionAddForm salePackageCardCommissionAddForm : form.PackageCard) {
                if (salePackageCardCommissionAddForm.PayRate != null || salePackageCardCommissionAddForm.PayFixed != null ||
                        salePackageCardCommissionAddForm.SavingCardRate != null || salePackageCardCommissionAddForm.SavingCardFixed != null ||
                        salePackageCardCommissionAddForm.SavingCardLargessRate != null || salePackageCardCommissionAddForm.SavingCardLargessFixed != null ||
                        salePackageCardCommissionAddForm.SpecialBenefit != null) {
                    SalePackageCardCommissionEntity salePackageCardCommissionEntity = new SalePackageCardCommissionEntity();
                    salePackageCardCommissionEntity.EntityID = form.EntityID;
                    salePackageCardCommissionEntity.PackageCardID = salePackageCardCommissionAddForm.PackageCardID;
                    salePackageCardCommissionEntity.PayFixed = salePackageCardCommissionAddForm.PayFixed;
                    salePackageCardCommissionEntity.PayRate = salePackageCardCommissionAddForm.PayRate;
                    salePackageCardCommissionEntity.SavingCardFixed = salePackageCardCommissionAddForm.SavingCardFixed;
                    salePackageCardCommissionEntity.SavingCardRate = salePackageCardCommissionAddForm.SavingCardRate;
                    salePackageCardCommissionEntity.SavingCardLargessFixed = salePackageCardCommissionAddForm.SavingCardLargessFixed;
                    salePackageCardCommissionEntity.SavingCardLargessRate = salePackageCardCommissionAddForm.SavingCardLargessRate;
                    salePackageCardCommissionEntity.SpecialBenefit = salePackageCardCommissionAddForm.SpecialBenefit;
                    list.add(salePackageCardCommissionEntity);
                }
            }
            if (list != null && list.size() > 0) {
                List<List<SalePackageCardCommissionEntity>> subs = Lists.partition(list, 20);
                for (List<SalePackageCardCommissionEntity> entityList : subs) {
                    salePackageCardCommissionMapper.insertSalePackageCardCommission(entityList);
                }
            }
        }
        return BaseOutput.success();
    }

}