package com.zhelian.service.ibeauty.projectConsumableTemplate;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.ProjectConsumableTemplateMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.*;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.ibeauty.form.output.ProjectConsumableTemplateDetailOutputForm;
import com.zhelian.model.ibeauty.form.output.ProjectConsumableTemplateOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class ProjectConsumableTemplateService {

    @Autowired
    ProjectConsumableTemplateMapper projectConsumableTemplateMapper;

    //获取耗材分类
    public BaseOutput allCategory(ProjectConsumableCategoryQueryForm form) {
        List<ProjectConsumableCategoryEntity> list = projectConsumableTemplateMapper.getProjectConsumableCategory(form);
        JSONArray result = ListUtil.listToTree(JSONArray.parseArray(JSON.toJSONString(list, JSONWriter.Feature.WriteMapNullValue)), "ID", "ParentID", "Child");
        return BaseOutputForm.success(result);
    }

    public BaseOutput listCategory(ProjectConsumableCategoryQueryForm form) {
        List<ProjectConsumableCategoryEntity> list = projectConsumableTemplateMapper.getProjectConsumableCategory(form);
        return BaseOutputForm.success(list);
    }

    //创建耗材分类
    public BaseOutput createCategory(ProjectConsumableCategoryAddForm form) {
        ProjectConsumableCategoryEntity projectConsumableCategoryEntity = new ProjectConsumableCategoryEntity();
        projectConsumableCategoryEntity.Name = form.Name;
        if (form.ParentID == null) {
            //ParentID为空时默认顶级分类
            projectConsumableCategoryEntity.ParentID = 0;
        } else {
            projectConsumableCategoryEntity.ParentID = form.ParentID;
        }

        Integer count = projectConsumableTemplateMapper.getProjectConsumableCategoryCount(projectConsumableCategoryEntity.ParentID);
        projectConsumableCategoryEntity.Sequence = count + 1;
        projectConsumableCategoryEntity.Active = true;

        projectConsumableTemplateMapper.addProjectConsumableCategory(projectConsumableCategoryEntity);
        return BaseOutput.success();
    }

    //更新耗材分类
    public BaseOutput updateCategory(ProjectConsumableCategoryUpdateForm form) {
        //检查该分类是否有关联模板数据
        if (!form.Active) {
            Integer count = projectConsumableTemplateMapper.getProjectConsumableTemplateCountByCategory(form.ID);
            if (count > 0) {
                return BaseOutput.failed("当前分类下有模板数据");
            }
        }

        if (form.ParentID == null) {
            //ParentID为空时默认顶级分类
            form.ParentID = 0;
        }
        projectConsumableTemplateMapper.updateProjectConsumableCategory(form);
        return BaseOutput.success();
    }

    //获取耗材模板
    public BaseOutput all(ProjectConsumableTemplateQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<ProjectConsumableTemplateOutputForm> list = projectConsumableTemplateMapper.getProjectConsumableTemplate(form);
        return BasePageInfo.success(list);
    }

    //获取耗材模板详细
    public BaseOutput detail(ProjectConsumableTemplateQueryForm form) {
        ProjectConsumableTemplateDetailOutputForm result = projectConsumableTemplateMapper.getProjectConsumableTemplateDetail(form.ID);
        return BaseOutputForm.success(result);
    }

    //创建耗材模板
    @Transactional
    public BaseOutput create(ProjectConsumableTemplateAddForm form) {
        ProjectConsumableTemplateEntity projectConsumableTemplateEntity = new ProjectConsumableTemplateEntity();
        projectConsumableTemplateEntity.Name = form.Name;
        projectConsumableTemplateEntity.ProjectConsumableCategoryID = form.ProjectConsumableCategoryID;
        projectConsumableTemplateEntity.Active = true;

        //add ProjectConsumableTemplate
        projectConsumableTemplateMapper.addProjectConsumableTemplate(projectConsumableTemplateEntity);

        List<ProjectConsumableTemplateProductEntity> productAddList = new ArrayList<>();
        if (form.ProductList != null && form.ProductList.size() > 0) {
            for (ProjectConsumableProductInputForm productInputForm : form.ProductList) {
                ProjectConsumableTemplateProductEntity projectConsumableTemplateProductEntity = new ProjectConsumableTemplateProductEntity();
                projectConsumableTemplateProductEntity.ProjectConsumableTemplateID = projectConsumableTemplateEntity.ID;
                projectConsumableTemplateProductEntity.ProductID = productInputForm.ProductID;
                projectConsumableTemplateProductEntity.MinimumUnitQuantity = productInputForm.MinimumUnitQuantity;
                productAddList.add(projectConsumableTemplateProductEntity);
            }
        }
        if (productAddList.size() > 0) {
            projectConsumableTemplateMapper.addProjectConsumableTemplateProduct(productAddList);
        }

        List<ProjectConsumableTemplateProjectEntity> projectAddList = new ArrayList<>();
        if (form.ProjectList != null && form.ProjectList.size() > 0) {
            for (ProjectConsumableProjectInputForm projectInputForm : form.ProjectList) {
                ProjectConsumableTemplateProjectEntity projectConsumableTemplateProjectEntity = new ProjectConsumableTemplateProjectEntity();
                projectConsumableTemplateProjectEntity.ProjectConsumableTemplateID = projectConsumableTemplateEntity.ID;
                projectConsumableTemplateProjectEntity.ProjectID = projectInputForm.ProjectID;
                projectAddList.add(projectConsumableTemplateProjectEntity);
            }
        }
        if (projectAddList.size() > 0) {
            projectConsumableTemplateMapper.addProjectConsumableTemplateProject(projectAddList);
        }

        List<ProjectConsumableTemplateProjectCategoryEntity> projectCategoryAddList = new ArrayList<>();
        if (form.ProjectCategoryList != null && form.ProjectCategoryList.size() > 0) {
            for (ProjectConsumableProjectCategoryInputForm projectCategoryInputForm : form.ProjectCategoryList) {
                ProjectConsumableTemplateProjectCategoryEntity projectConsumableTemplateProjectCategoryEntity = new ProjectConsumableTemplateProjectCategoryEntity();
                projectConsumableTemplateProjectCategoryEntity.ProjectConsumableTemplateID = projectConsumableTemplateEntity.ID;
                projectConsumableTemplateProjectCategoryEntity.ProjectCategoryID = projectCategoryInputForm.ProjectCategoryID;
                projectCategoryAddList.add(projectConsumableTemplateProjectCategoryEntity);
            }
        }
        if (projectCategoryAddList.size() > 0) {
            projectConsumableTemplateMapper.addProjectConsumableTemplateProjectCategory(projectCategoryAddList);
        }
        return BaseOutput.success();
    }

    //更新耗材模板
    @Transactional
    public BaseOutput update(ProjectConsumableTemplateUpdateForm form) {
        ProjectConsumableTemplateEntity projectConsumableTemplateEntity = new ProjectConsumableTemplateEntity();
        projectConsumableTemplateEntity.ID = form.ID;
        projectConsumableTemplateEntity.Name = form.Name;
        projectConsumableTemplateEntity.ProjectConsumableCategoryID = form.ProjectConsumableCategoryID;
        projectConsumableTemplateEntity.Active = form.Active;

        projectConsumableTemplateMapper.updateProjectConsumableTemplate(projectConsumableTemplateEntity);

        //更新使用耗材
        projectConsumableTemplateMapper.deleteProjectConsumableTemplateProduct(form.ID);
        List<ProjectConsumableTemplateProductEntity> productAddList = new ArrayList<>();
        if (form.ProductList != null && form.ProductList.size() > 0) {
            for (ProjectConsumableProductInputForm productInputForm : form.ProductList) {
                ProjectConsumableTemplateProductEntity projectConsumableTemplateProductEntity = new ProjectConsumableTemplateProductEntity();
                projectConsumableTemplateProductEntity.ProjectConsumableTemplateID = form.ID;
                projectConsumableTemplateProductEntity.ProductID = productInputForm.ProductID;
                projectConsumableTemplateProductEntity.MinimumUnitQuantity = productInputForm.MinimumUnitQuantity;
                productAddList.add(projectConsumableTemplateProductEntity);
            }
        }
        if (productAddList.size() > 0) {
            projectConsumableTemplateMapper.addProjectConsumableTemplateProduct(productAddList);
        }

        //更新适用项目
        projectConsumableTemplateMapper.deleteProjectConsumableTemplateProject(form.ID);
        List<ProjectConsumableTemplateProjectEntity> projectAddList = new ArrayList<>();
        if (form.ProjectList != null && form.ProjectList.size() > 0) {
            for (ProjectConsumableProjectInputForm projectInputForm : form.ProjectList) {
                ProjectConsumableTemplateProjectEntity projectConsumableTemplateProjectEntity = new ProjectConsumableTemplateProjectEntity();
                projectConsumableTemplateProjectEntity.ProjectConsumableTemplateID = form.ID;
                projectConsumableTemplateProjectEntity.ProjectID = projectInputForm.ProjectID;
                projectAddList.add(projectConsumableTemplateProjectEntity);
            }
        }
        if (projectAddList.size() > 0) {
            projectConsumableTemplateMapper.addProjectConsumableTemplateProject(projectAddList);
        }

        //更新适用项目分类
        projectConsumableTemplateMapper.deleteProjectConsumableTemplateProjectCategory(form.ID);
        List<ProjectConsumableTemplateProjectCategoryEntity> projectCategoryAddList = new ArrayList<>();
        if (form.ProjectCategoryList != null && form.ProjectCategoryList.size() > 0) {
            for (ProjectConsumableProjectCategoryInputForm projectCategoryInputForm : form.ProjectCategoryList) {
                ProjectConsumableTemplateProjectCategoryEntity projectConsumableTemplateProjectCategoryEntity = new ProjectConsumableTemplateProjectCategoryEntity();
                projectConsumableTemplateProjectCategoryEntity.ProjectConsumableTemplateID = form.ID;
                projectConsumableTemplateProjectCategoryEntity.ProjectCategoryID = projectCategoryInputForm.ProjectCategoryID;
                projectCategoryAddList.add(projectConsumableTemplateProjectCategoryEntity);
            }
        }
        if (projectCategoryAddList.size() > 0) {
            projectConsumableTemplateMapper.addProjectConsumableTemplateProjectCategory(projectCategoryAddList);
        }
        return BaseOutput.success();
    }

    //项目分类和项目
    public BaseOutput findCategoryAndProject() {
        return BaseOutputForm.success(ListUtil.listToTree(JSONArray.parseArray(JSON.toJSONString(projectConsumableTemplateMapper.findCategoryAndProject(), JSONWriter.Feature.WriteMapNullValue)), "ID", "ParentID", "Child"));
    }
}
