package com.zhelian.service.ibeauty.saleTimeCardCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.SaleTimeCardCategoryCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SaleTimeCardCategoryCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleTimeCardCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.output.SaleTimeCardCategoryCommissionOutputForm;
import com.zhelian.model.ibeauty.form.output.SaleTimeCardCategoryOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleTimeCardCategoryCommissionService {

    @Autowired
    SaleTimeCardCategoryCommissionMapper saleTimeCardCategoryCommissionMapper;


    public BaseOutput allSaleTimeCardCategoryCommission(SaleTimeCardCommissionSchemeAddForm form) {
        SaleTimeCardCategoryOutputForm saleTimeCardCategoryOutputForm = saleTimeCardCategoryCommissionMapper.saleTimeCardCategoryCommission(form.EntityID);
        List<SaleTimeCardCategoryCommissionOutputForm> all = saleTimeCardCategoryCommissionMapper.allSaleTimeCardCategoryCommission(form.EntityID);
        saleTimeCardCategoryOutputForm.Category = all;
        return BaseOutputForm.success(saleTimeCardCategoryOutputForm);
    }

    @Transactional
    public BaseOutput updateSaleTimeCardCategoryCommission(SaleTimeCardCategoryAddForm form) {
        SaleTimeCardCommissionSchemeEntity saleTimeCardCommissionSchemeEntity = new SaleTimeCardCommissionSchemeEntity();
        saleTimeCardCommissionSchemeEntity.EntityID = form.EntityID;
        saleTimeCardCommissionSchemeEntity.PayRate = form.PayRate;
        saleTimeCardCommissionSchemeEntity.PayFixed = form.PayFixed;
        saleTimeCardCommissionSchemeEntity.SavingCardRate = form.SavingCardRate;
        saleTimeCardCommissionSchemeEntity.SavingCardFixed = form.SavingCardFixed;
        saleTimeCardCommissionSchemeEntity.SavingCardLargessRate = form.SavingCardLargessRate;
        saleTimeCardCommissionSchemeEntity.SavingCardLargessFixed = form.SavingCardLargessFixed;
        saleTimeCardCommissionSchemeEntity.SpecialBenefit = form.SpecialBenefit;

        saleTimeCardCategoryCommissionMapper.updateSaleTimeCardCommissionScheme(saleTimeCardCommissionSchemeEntity);
        saleTimeCardCategoryCommissionMapper.deleteSaleTimeCardCategoryCommission(form.EntityID);
        if (form.Category != null && form.Category.size() > 0) {
            List<SaleTimeCardCategoryCommissionEntity> list = new ArrayList<>();
            for (SaleTimeCardCategoryCommissionAddForm saleTimeCardCategoryCommissionAddForm : form.Category) {
                if (saleTimeCardCategoryCommissionAddForm.PayRate != null || saleTimeCardCategoryCommissionAddForm.PayFixed != null ||
                        saleTimeCardCategoryCommissionAddForm.SavingCardRate != null || saleTimeCardCategoryCommissionAddForm.SavingCardFixed != null ||
                        saleTimeCardCategoryCommissionAddForm.SavingCardLargessRate != null || saleTimeCardCategoryCommissionAddForm.SavingCardLargessFixed != null ||
                        saleTimeCardCategoryCommissionAddForm.SpecialBenefit != null) {
                    SaleTimeCardCategoryCommissionEntity saleTimeCardCategoryCommissionEntity = new SaleTimeCardCategoryCommissionEntity();
                    saleTimeCardCategoryCommissionEntity.EntityID = form.EntityID;
                    saleTimeCardCategoryCommissionEntity.TimeCardCategoryID = saleTimeCardCategoryCommissionAddForm.CategoryID;
                    saleTimeCardCategoryCommissionEntity.PayRate = saleTimeCardCategoryCommissionAddForm.PayRate;
                    saleTimeCardCategoryCommissionEntity.PayFixed = saleTimeCardCategoryCommissionAddForm.PayFixed;
                    saleTimeCardCategoryCommissionEntity.SavingCardRate = saleTimeCardCategoryCommissionAddForm.SavingCardRate;
                    saleTimeCardCategoryCommissionEntity.SavingCardFixed = saleTimeCardCategoryCommissionAddForm.SavingCardFixed;
                    saleTimeCardCategoryCommissionEntity.SavingCardLargessRate = saleTimeCardCategoryCommissionAddForm.SavingCardLargessRate;
                    saleTimeCardCategoryCommissionEntity.SavingCardLargessFixed = saleTimeCardCategoryCommissionAddForm.SavingCardLargessFixed;
                    saleTimeCardCategoryCommissionEntity.SpecialBenefit = saleTimeCardCategoryCommissionAddForm.SpecialBenefit;
                    list.add(saleTimeCardCategoryCommissionEntity);
                }
            }

            if (list != null && list.size() > 0) {
                List<List<SaleTimeCardCategoryCommissionEntity>> subs = Lists.partition(list, 20);
                for (List<SaleTimeCardCategoryCommissionEntity> entityList : subs) {
                    saleTimeCardCategoryCommissionMapper.insertSaleTimeCardCategoryCommissionEntity(entityList);
                }
            }
        }

        return BaseOutput.success();
    }

}