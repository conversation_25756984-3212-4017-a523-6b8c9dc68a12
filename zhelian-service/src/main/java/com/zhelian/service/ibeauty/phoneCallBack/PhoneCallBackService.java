package com.zhelian.service.ibeauty.phoneCallBack;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.utils.DateTimeUtil;
import com.zhelian.core.utils.UploadVideoUtil;
import com.zhelian.mapper.CRM.CustomerMapper;
import com.zhelian.mapper.CRM.CustomerSourceMapper;
import com.zhelian.mapper.ibeauty.PhoneCallBackMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.CustomerEntity;
import com.zhelian.model.ibeauty.entity.PhoneCallBackCodeEntity;
import com.zhelian.model.ibeauty.entity.PhoneCallBackConfigEntity;
import com.zhelian.model.ibeauty.entity.PhoneCallBackLogEntity;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.ibeauty.form.output.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import util.PhoneCallBackUtil;

import javax.net.ssl.HttpsURLConnection;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PhoneCallBackService {

    @Autowired
    PhoneCallBackMapper phoneCallBackMapper;

    @Autowired
    CustomerMapper customerMapper;
    @Autowired
    CustomerSourceMapper customerSourceMapper;

    public BaseOutput list(PhoneCallBackQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<PhoneCallBackOutputForm> list = phoneCallBackMapper.list(form.Name, form.Phone);
        return BasePageInfo.success(list);
    }

    @Transactional
    public BaseOutput add(PhoneCallBackAddForm form) throws Exception {
        PhoneCallBackConfigEntity phoneCallBackConfigEntity = phoneCallBackMapper.getPhoneCallBackConfigEntityByEmployeeID(form.EmployeeID);
        if (phoneCallBackConfigEntity != null) {
            return BaseOutput.failed("员工已配置");
        }

        PhoneCallBackCodeEntity phoneCallBackCodeEntity = phoneCallBackMapper.getPhoneCallBackCodeEntity(form.PhoneNumber);

        if (phoneCallBackCodeEntity != null){
            PhoneCallBackConfigEntity entity = new PhoneCallBackConfigEntity();
            entity.EmployeeID = form.EmployeeID;
            entity.PhoneNumber = form.PhoneNumber;
            entity.Code = phoneCallBackCodeEntity.Code;
            phoneCallBackMapper.add(entity);
        }else {
            PhoneCallBackConfigOutForm param = new PhoneCallBackConfigOutForm();
            param.appkey = PhoneCallBackUtil.appkey;
            param.yw_code = PhoneCallBackUtil.yw_code;
            param.phone = form.PhoneNumber;
            JSONObject jsonObject = PhoneCallBackUtil.sendHttpPost(JSON.toJSONString(param), PhoneCallBackUtil.addSeatUrl);

            if (jsonObject.getString("code").equals("200")) {
                JSONObject data = jsonObject.getJSONObject("data");
                PhoneCallBackConfigEntity entity = new PhoneCallBackConfigEntity();
                entity.EmployeeID = form.EmployeeID;
                entity.PhoneNumber = form.PhoneNumber;
                entity.Code = data.getString("seat_code");
                phoneCallBackMapper.add(entity);

                PhoneCallBackCodeEntity phoneCallBackCode = new PhoneCallBackCodeEntity();
                phoneCallBackCode.PhoneNumber = form.PhoneNumber;
                phoneCallBackCode.Code = data.getString("seat_code");
                phoneCallBackMapper.addPhoneCallBackCodeEntity(phoneCallBackCode);
            } else {
                return BaseOutput.failed(jsonObject.getString("message"));
            }
        }

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput addDx(PhoneCallBackAddForm form) throws Exception {
        PhoneCallBackConfigEntity phoneCallBackConfigEntity = phoneCallBackMapper.getPhoneCallBackConfigEntityByEmployeeID(form.EmployeeID);
        if (phoneCallBackConfigEntity != null) {
            return BaseOutput.failed("员工已配置");
        }
        List<PhoneCallBackConfigEntity> list = phoneCallBackMapper.getPhoneCallBackConfigEntityByEcpId(form.EcpId);
        if (list.size() >= 3) {
            return BaseOutput.failed("该能力号配置已达上限");
        }

        PhoneCallBackConfigEntity entity = new PhoneCallBackConfigEntity();
        entity.EmployeeID = form.EmployeeID;
        entity.PhoneNumber = form.PhoneNumber;
        entity.EcpId = form.EcpId;
        phoneCallBackMapper.add(entity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput delete(PhoneCallBackDeleteForm form) {
        phoneCallBackMapper.delete(form.ID);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput callBack(PhoneCallBackForm form) {
        PhoneCallBackConfigEntity phoneCallBackConfigEntity = phoneCallBackMapper.getPhoneCallBackConfigEntityByEmployeeID(SystemUserUtil.getSystemUserID());
        if (phoneCallBackConfigEntity == null) {
            return BaseOutput.failed("员工未配置");
        }
        CustomerEntity customer = customerMapper.getCustomer(form.CustomerID);

        if (customer == null || !StringUtils.hasText(customer.PhoneNumber)) {
            return BaseOutput.failed("顾客手机号未配置");
        }

        try {
            PhoneCallBackOutForm param = new PhoneCallBackOutForm();
            param.appkey = PhoneCallBackUtil.appkey;
            param.caller = phoneCallBackConfigEntity.PhoneNumber;
            param.callee = customer.PhoneNumber;
            param.seat_code = phoneCallBackConfigEntity.Code;
            JSONObject jsonObject = PhoneCallBackUtil.sendHttpPost(JSON.toJSONString(param), PhoneCallBackUtil.Url);

            if (jsonObject.getString("code").equals("200")) {
                JSONObject data = jsonObject.getJSONObject("data");
                PhoneCallBackLogEntity entity = new PhoneCallBackLogEntity();
                entity.CallID = data.getString("callSid");
                entity.Caller = data.getString("caller");
                entity.Callee = data.getString("callee");
                entity.CustomerID = customer.ID;
                entity.EmpId = SystemUserUtil.getSystemUserID();
                phoneCallBackMapper.addPhoneCallBackLog(entity);
            } else {
                return BaseOutput.failed(jsonObject.getString("message"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return BaseOutput.success();
    }

    /**
     * 电信回拨
     * @param form form
     */
    @Transactional
    public BaseOutput callBackDx(PhoneCallBackForm form) {
        PhoneCallBackConfigEntity phoneCallBackConfigEntity = phoneCallBackMapper.getPhoneCallBackConfigEntityByEmployeeID(SystemUserUtil.getSystemUserID());
        if (phoneCallBackConfigEntity == null) {
            return BaseOutput.failed("员工未配置");
        }
        if (!StringUtils.hasText(phoneCallBackConfigEntity.EcpId)) {
            return BaseOutput.failed("顾客能力号未配置");
        }
        CustomerEntity customer = customerMapper.getCustomer(form.CustomerID);
        if (Objects.isNull(customer) || !StringUtils.hasText(customer.PhoneNumber)) {
            return BaseOutput.failed("顾客手机号未配置");
        }

        try {
            Map<String,Object> param = new HashMap<>();
            param.put("appkey", PhoneCallBackUtil.DX_APP_KEY);
            param.put("caller", phoneCallBackConfigEntity.PhoneNumber);
            param.put("callee", customer.PhoneNumber);
            param.put("ecpid", phoneCallBackConfigEntity.EcpId);
            JSONObject jsonObject = PhoneCallBackUtil.sendHttpPost(JSON.toJSONString(param), PhoneCallBackUtil.DX_URL);

            if (jsonObject.getString("code").equals("200")) {
                JSONObject data = jsonObject.getJSONObject("data");
                PhoneCallBackLogEntity entity = new PhoneCallBackLogEntity();
                entity.CallID = data.getString("callSid");
                entity.Caller = data.getString("caller");
                entity.Callee = data.getString("callee");
                entity.CustomerID = customer.ID;
                entity.EmpId = SystemUserUtil.getSystemUserID();
                phoneCallBackMapper.addPhoneCallBackLog(entity);
            } else {
                return BaseOutput.failed(jsonObject.getString("message"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput callBackAccept(PhoneCallBackAcceptForm form) throws Exception {
        PhoneCallBackLogEntity entity = phoneCallBackMapper.getPhoneCallBackLogEntity(form.callId);
        if (entity == null) {
            return BaseOutput.failed("未呼叫");
        }
        entity.Duration = form.duration;
        entity.Minutes = form.minutes;
        entity.CreateTime = DateTimeUtil.toDate(form.createTime);
        entity.StartTime = DateTimeUtil.toDate(form.startTime);
        entity.EndTime = DateTimeUtil.toDate(form.endTime);
        entity.Record = form.record;
        URL url = new URL(form.record);
        HttpsURLConnection conn = (HttpsURLConnection) url.openConnection();
        InputStream inputStream = conn.getInputStream();
        entity.VideoId = UploadVideoUtil.testUploadStream("20", entity.CallID + ".mp3", inputStream);
        entity.IsConnected = form.isConnected;
        entity.CustomParam = form.customParam;

        phoneCallBackMapper.updatePhoneCallBackLog(entity);

        return BaseOutput.success();
    }

    public BaseOutput callBackLog(PhoneCallBackLogForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<PhoneCallBackLogOutputForm> list = phoneCallBackMapper.callBackLog(form.CustomerID);
        for (PhoneCallBackLogOutputForm outputForm : list) {
            if (StringUtils.hasText(outputForm.VideoID)) {
                outputForm.Record = UploadVideoUtil.getPlayInfoAdress(outputForm.VideoID).getString("url");
            }
        }
        return BasePageInfo.success(list);
    }

    public void callBackAcceptPort() throws Exception {
        List<PhoneCallBackLogEntity> list = phoneCallBackMapper.getPhoneCallBackLogEntityAll();

        if (list != null && list.size() > 0) {

            for (PhoneCallBackLogEntity entity : list) {

                PhoneCallBackAcceptPortOutForm param = new PhoneCallBackAcceptPortOutForm();
                param.callId = entity.CallID;
                param.appkey = PhoneCallBackUtil.appkey;
                if (entity.CreateTime != null) {
                    param.date = DateTimeUtil.toyyyyMMdd(entity.CreateTime);
                }
                JSONObject jsonObject = PhoneCallBackUtil.sendHttpPost(JSON.toJSONString(param), PhoneCallBackUtil.recordUrl);

                if (jsonObject.getString("code").equals("200")) {
                    JSONObject data = jsonObject.getJSONObject("data");
                    if (data != null) {
                        entity.Duration = StringUtils.hasText(data.getString("duration")) ? data.getString("duration") : null;
                        entity.Minutes = StringUtils.hasText(data.getString("minutes")) ? data.getString("minutes") : null;
                        entity.CreateTime = StringUtils.hasText(data.getString("createTime")) ? DateTimeUtil.toDate(data.getString("createTime")) : null;
                        entity.StartTime = StringUtils.hasText(data.getString("startTime")) ? DateTimeUtil.toDate(data.getString("startTime")) : null;
                        entity.EndTime = StringUtils.hasText(data.getString("endTime")) ? DateTimeUtil.toDate(data.getString("endTime")) : null;
                        entity.Record = StringUtils.hasText(data.getString("record")) ? data.getString("record") : null;
                        if (StringUtils.hasText(data.getString("record"))) {
                            URL url = new URL(data.getString("record"));
                            URLConnection connection = url.openConnection();
                            if (connection instanceof HttpsURLConnection) {
                                HttpsURLConnection conn = (HttpsURLConnection) connection;
                                InputStream inputStream = conn.getInputStream();
                                entity.VideoId = UploadVideoUtil.testUploadStream("20", entity.CallID + ".mp3", inputStream);
                            } else if (connection instanceof HttpURLConnection) {
                                HttpURLConnection conn = (HttpURLConnection) connection;
                                InputStream inputStream = conn.getInputStream();
                                entity.VideoId = UploadVideoUtil.testUploadStream("20", entity.CallID + ".mp3", inputStream);
                            } else {
                                throw new IllegalStateException("不支持的 URL 连接类型");
                            }
                        }
                        entity.IsConnected = StringUtils.hasText(data.getString("isConnected")) ? data.getInteger("isConnected") : null;
                        entity.CustomParam = StringUtils.hasText(data.getString("customParam")) ? data.getString("customParam") : null;
                    }

                    phoneCallBackMapper.updatePhoneCallBackLog(entity);
                }
            }

        }

    }

    public void callBackDxAcceptPort() throws Exception {
        List<PhoneCallBackLogEntity> list = phoneCallBackMapper.getPhoneCallBackLogEntityAll();

        if (list != null && !list.isEmpty()) {

            for (PhoneCallBackLogEntity entity : list) {

                Map<String, Object> param = new HashMap<>();
                param.put("callSid", entity.CallID);
                param.put("appkey", PhoneCallBackUtil.DX_APP_KEY);
                if (entity.CreateTime != null) {
                    param.put("date", DateTimeUtil.toyyyyMMdd(entity.CreateTime));
                }
                JSONObject jsonObject = PhoneCallBackUtil.sendHttpPost(JSON.toJSONString(param), PhoneCallBackUtil.DX_RECORD_URL);

                if (jsonObject.getString("code").equals("200")) {
                    JSONObject data = jsonObject.getJSONObject("data");
                    if (data != null) {
                        entity.Duration = StringUtils.hasText(data.getString("duration")) ? data.getString("duration") : null;
                        entity.Minutes = StringUtils.hasText(data.getString("minutes")) ? data.getString("minutes") : null;
                        entity.CreateTime = StringUtils.hasText(data.getString("created_time")) ? DateTimeUtil.toDate(data.getString("created_time")) : null;
                        entity.StartTime = StringUtils.hasText(data.getString("start_time")) ? DateTimeUtil.toDate(data.getString("start_time")) : null;
                        entity.EndTime = StringUtils.hasText(data.getString("end_time")) ? DateTimeUtil.toDate(data.getString("end_time")) : null;
                        String filePath = data.getString("file_path");
                        entity.Record = StringUtils.hasText(filePath) ? filePath : null;
                        if (StringUtils.hasText(filePath)) {
                            URL url = new URL(filePath);
                            URLConnection connection = url.openConnection();
                            if (connection instanceof HttpURLConnection) {
                                InputStream inputStream = connection.getInputStream();
                                entity.VideoId = UploadVideoUtil.testUploadStream("20", entity.CallID + ".mp3", inputStream);
                            }  else {
                                throw new IllegalStateException("不支持的 URL 连接类型");
                            }
                        }
                        entity.IsConnected = StringUtils.hasText(data.getString("isConnected")) ? data.getInteger("isConnected") : null;
                        entity.CustomParam = StringUtils.hasText(data.getString("customParam")) ? data.getString("customParam") : null;
                        String ecpId = data.getString("ecpid");
                        entity.EcpId = StringUtils.hasText(ecpId) ? ecpId : null;
                    }

                    phoneCallBackMapper.updatePhoneCallBackLog(entity);
                }
            }

        }

    }

    public BaseOutput callBackLogReport(PhoneCallBackLogReportForm form) {
        try (Page<Object> page = PageHelper.startPage(form.PageNum, form.getPageSize())) {
            List<PhoneCallBackLogReportOutputForm> list = phoneCallBackMapper.callBackLogReport(form);
            list.forEach(p ->
                    p.setConnectTime(computePhoneCallBackLogConnectTime(p.getDuration()))
                            .setRecordUrl(UploadVideoUtil.getPlayInfoAdress(p.getVideoId()).getString("url"))
            );
            return BasePageInfo.success(list);
        }
    }

    private String computePhoneCallBackLogConnectTime(Long duration) {
        if (duration == null) {
            return null;
        }
        if (duration < 60) {
            return duration + "秒";
        } else if (duration < 3600) {
            return duration / 60 + "分" + duration % 60 + "秒";
        } else {
            return duration / 3600 + "时" + (duration % 3600) / 60 + "分" + duration % 60 + "秒";
        }
    }

    public void callBackLogReportExport(PhoneCallBackLogReportForm form, HttpServletResponse response) {
        List<PhoneCallBackLogReportOutputForm> list = phoneCallBackMapper.callBackLogReport(form);
        List<PhoneCallBackLogReportExcelForm> excelFormList = list.stream().map(p -> {
            p.setConnectTime(computePhoneCallBackLogConnectTime(p.getDuration()));
            return PhoneCallBackLogReportExcelForm.getInstance(p);
        }).collect(Collectors.toList());
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("员工通话明细", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), PhoneCallBackLogReportExcelForm.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("员工通话明细")
                    .doWrite(excelFormList);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public BaseOutput callBackLogStatisticsReport(PhoneCallBackLogStatisticsForm form) {
        try(Page<Object> page = PageHelper.startPage(form.PageNum, form.getPageSize())){
            List<PhoneCallBackLogStatisticsOutputForm> list = phoneCallBackMapper.callBackLogStatisticsReport(form);
            List<CustomerSourceOutputForm> customerSource = customerSourceMapper.getCustomerSource();
            list.forEach(statistics -> {
                statistics.setSourceMap(customerSource.stream().collect(Collectors.toMap(p -> p.ID, p -> Optional.ofNullable(
                        phoneCallBackMapper.getCustomerSourceCount(statistics.getEmployeeId(), p.ID, form.getTimeStart(), form.getTimeEnd())).orElse(0)
                        )));
            });
            return BasePageInfo.success(list);
        }
    }

    public BaseOutput callBackLogStatisticsReportTotal(PhoneCallBackLogStatisticsForm form) {
        PhoneCallBackLogStatisticsOutputForm data = Optional.ofNullable(phoneCallBackMapper.callBackLogStatisticsReportTotal(form)).orElseGet(PhoneCallBackLogStatisticsOutputForm::new);
        List<CustomerSourceOutputForm> customerSource = customerSourceMapper.getCustomerSource();
        data.setSourceMap(customerSource.stream().collect(Collectors.toMap(p -> p.ID, p -> Optional.ofNullable(
                phoneCallBackMapper.getCustomerSourceTotalCount(
                        form.getEmployeeName(), p.ID, form.getTimeStart(), form.getTimeEnd(), form.getJobIds()
                        , form.getEntityIds())).orElse(0)
        )))
                .setConnectRate(computeRate(data.getConnectCount(), data.getCallOutCount()));
        return BaseOutputForm.success(data);
    }

    private String computeRate(Number a, Number b) {
        final String zeroPercent = "0%";
        if (a == null || b == null) {
            return zeroPercent;
        }
        if (b.intValue() == 0) {
            return zeroPercent;
        }
        return BigDecimal.valueOf(a.doubleValue()).multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(b.doubleValue()), 2, RoundingMode.HALF_UP) + "%";
    }

    public void callBackLogStatisticsReportExport(PhoneCallBackLogStatisticsForm form, HttpServletResponse response) {
        List<PhoneCallBackLogStatisticsOutputForm> list = phoneCallBackMapper.callBackLogStatisticsReport(form);
        List<CustomerSourceOutputForm> customerSource = customerSourceMapper.getCustomerSource();
        list.forEach(statistics -> {
            statistics.setSourceMap(customerSource.stream().collect(Collectors.toMap(p -> p.ID, p -> Optional.ofNullable(
                    phoneCallBackMapper.getCustomerSourceCount(statistics.getEmployeeId(), p.ID, form.getTimeStart(), form.getTimeEnd())).orElse(0)
            )));
        });
        PhoneCallBackLogStatisticsOutputForm data = Optional.ofNullable(phoneCallBackMapper.callBackLogStatisticsReportTotal(form)).orElseGet(PhoneCallBackLogStatisticsOutputForm::new);
        data.setSourceMap(customerSource.stream().collect(Collectors.toMap(p -> p.ID, p -> Optional.ofNullable(
                        phoneCallBackMapper.getCustomerSourceTotalCount(
                                form.getEmployeeName(), p.ID, form.getTimeStart(), form.getTimeEnd(), form.getJobIds()
                                , form.getEntityIds())).orElse(0)
                )))
                .setConnectRate(computeRate(data.getConnectCount(), data.getCallOutCount()))
                .setEmployeeName("合计");
        list.add(data);
        writeToResponse(list, customerSource, response);
    }

    private void writeToResponse(List<PhoneCallBackLogStatisticsOutputForm> list, List<CustomerSourceOutputForm> customerSource, HttpServletResponse response) {
        List<String> header = new ArrayList<>(Arrays.asList("姓名", "职务", "通话总数", "接通数", "接通率", "总通话时长"
                , "当天名片预约数量", "实际到店人数", "签约金额（实收）", "签约金额（订单金额）", "成交数量", "体验卡客户数"
                , "体验卡实收金额"));
        customerSource = Optional.ofNullable(customerSource).orElseGet(ArrayList::new);
        List<String> dynamicField = customerSource.stream().map(p -> p.Name).filter(StrUtil::isNotBlank).collect(Collectors.toList());
        List<Integer> dynamicKey = customerSource.stream().map(p -> p.ID).filter(Objects::nonNull).collect(Collectors.toList());
        header.addAll(dynamicField);
        List<List<String>> excelHeader = header.stream().map(p -> {
            List<String> var1 = new ArrayList<>();
            var1.add(p);
            return var1;
        }).collect(Collectors.toList());
        List<List<Object>> dataList = Optional.ofNullable(list).map(coll -> coll.stream().map(p -> {
            List<Object> data = new ArrayList<>();
            data.add(p.getEmployeeName());
            data.add(p.getJobName());
            data.add(p.getCallOutCount());
            data.add(p.getConnectCount());
            data.add(p.getConnectRate());
            data.add(p.getVisitingCardCount());
            data.add(p.getArriveCount());
            data.add(p.getPayAmount());
            data.add(p.getOrderAmount());
            data.add(p.getDealCount());
            data.add(p.getProjectExperienceCardCount());
            data.add(p.getProjectExperienceCardAmount());
            if (CollUtil.isNotEmpty(p.getSourceMap())) {
                dynamicKey.forEach(key -> {
                    data.add(p.getSourceMap().get(key));
                });
            }
            return data;
        }).collect(Collectors.toList())).orElseGet(ArrayList::new);
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("员工通话记录统计分析", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream())
                    .head(excelHeader)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("员工通话记录统计分析")
                    .doWrite(dataList);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
