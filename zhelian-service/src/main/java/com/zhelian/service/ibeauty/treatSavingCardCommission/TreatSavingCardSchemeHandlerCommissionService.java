package com.zhelian.service.ibeauty.treatSavingCardCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.TreatSavingCardSchemeHandlerCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.TreatSavingCardSchemeHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.TreatSavingCardSchemeHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectSchemeHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectSchemeHandlersCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.TreatSavingCardProjectHandlerCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatSavingCardSchemeHandlerCommissionService {

    @Autowired
    TreatSavingCardSchemeHandlerCommissionMapper treatSavingCardSchemeHandlerCommissionMapper;

    public BaseOutput allTreatSavingCardSchemeHandlerCommission(TreatSavingCardProjectSchemeHandlerCommissionQueryForm form) {
        List<TreatSavingCardProjectHandlerCommissionOutputForm> list = treatSavingCardSchemeHandlerCommissionMapper.allTreatSavingCardSchemeHandlerCommission(form.EntityID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatSchemeHandlerCommission(TreatSavingCardProjectSchemeHandlersCommissionAddForm form) {

        treatSavingCardSchemeHandlerCommissionMapper.deleteTreatSavingCardSchemeHandler(form.EntityID);
        treatSavingCardSchemeHandlerCommissionMapper.deleteTreatSavingCardSchemeHandlerJobType(form.EntityID);

        if (form.Handler != null && form.Handler.size() > 0) {

            List listTreatSavingCardSchemeHandlerCommissionEntity = new ArrayList<>();

            List listTreatSavingCardSchemeHandlerJobTypeCommissionEntity = new ArrayList<>();

            for (TreatSavingCardProjectHandlerCommissionAddForm treatSavingCardProjectHandlerCommissionAddForm : form.Handler) {
                if (treatSavingCardProjectHandlerCommissionAddForm.Rate != null || treatSavingCardProjectHandlerCommissionAddForm.Fixed != null ||
                        treatSavingCardProjectHandlerCommissionAddForm.LargessRate != null || treatSavingCardProjectHandlerCommissionAddForm.LargessFixed != null ||
                        treatSavingCardProjectHandlerCommissionAddForm.SpecialBenefit != null) {
                    TreatSavingCardSchemeHandlerCommissionEntity treatSavingCardProjectSchemeHandlerCommissionEntity = new TreatSavingCardSchemeHandlerCommissionEntity();
                    treatSavingCardProjectSchemeHandlerCommissionEntity.EntityID = form.EntityID;
                    treatSavingCardProjectSchemeHandlerCommissionEntity.SavingCardProjectTreatHandlerID = treatSavingCardProjectHandlerCommissionAddForm.TreatHandlerID;
                    treatSavingCardProjectSchemeHandlerCommissionEntity.CardFixed = treatSavingCardProjectHandlerCommissionAddForm.Fixed;
                    treatSavingCardProjectSchemeHandlerCommissionEntity.CardRate = treatSavingCardProjectHandlerCommissionAddForm.Rate;
                    treatSavingCardProjectSchemeHandlerCommissionEntity.CardLargessFixed = treatSavingCardProjectHandlerCommissionAddForm.LargessFixed;
                    treatSavingCardProjectSchemeHandlerCommissionEntity.CardLargessRate = treatSavingCardProjectHandlerCommissionAddForm.LargessRate;
                    treatSavingCardProjectSchemeHandlerCommissionEntity.SpecialBenefit = treatSavingCardProjectHandlerCommissionAddForm.SpecialBenefit;
                    listTreatSavingCardSchemeHandlerCommissionEntity.add(treatSavingCardProjectSchemeHandlerCommissionEntity);
                }

                if (treatSavingCardProjectHandlerCommissionAddForm.HandlerJobTypeCommission != null && treatSavingCardProjectHandlerCommissionAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (TreatSavingCardProjectCategoryHandlerJobTypeCommissionAddForm treatSavingCardProjectCategoryHandlerJobTypeCommissionAddForm : treatSavingCardProjectHandlerCommissionAddForm.HandlerJobTypeCommission) {
                        if (treatSavingCardProjectCategoryHandlerJobTypeCommissionAddForm.Rate != null || treatSavingCardProjectCategoryHandlerJobTypeCommissionAddForm.Fixed != null ||
                                treatSavingCardProjectCategoryHandlerJobTypeCommissionAddForm.LargessRate != null || treatSavingCardProjectCategoryHandlerJobTypeCommissionAddForm.LargessFixed != null ||
                                treatSavingCardProjectCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            TreatSavingCardSchemeHandlerJobTypeCommissionEntity treatSavingCardProjectSchemeHandlerJobTypeCommissionEntity = new TreatSavingCardSchemeHandlerJobTypeCommissionEntity();
                            treatSavingCardProjectSchemeHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            treatSavingCardProjectSchemeHandlerJobTypeCommissionEntity.SavingCardProjectTreatHandlerID = treatSavingCardProjectHandlerCommissionAddForm.TreatHandlerID;
                            treatSavingCardProjectSchemeHandlerJobTypeCommissionEntity.JobTypeID = treatSavingCardProjectCategoryHandlerJobTypeCommissionAddForm.JobTypeID;
                            treatSavingCardProjectSchemeHandlerJobTypeCommissionEntity.CardFixed = treatSavingCardProjectCategoryHandlerJobTypeCommissionAddForm.Fixed;
                            treatSavingCardProjectSchemeHandlerJobTypeCommissionEntity.CardRate = treatSavingCardProjectCategoryHandlerJobTypeCommissionAddForm.Rate;
                            treatSavingCardProjectSchemeHandlerJobTypeCommissionEntity.CardLargessFixed = treatSavingCardProjectCategoryHandlerJobTypeCommissionAddForm.LargessFixed;
                            treatSavingCardProjectSchemeHandlerJobTypeCommissionEntity.CardLargessRate = treatSavingCardProjectCategoryHandlerJobTypeCommissionAddForm.LargessRate;
                            treatSavingCardProjectSchemeHandlerJobTypeCommissionEntity.SpecialBenefit = treatSavingCardProjectCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit;

                            listTreatSavingCardSchemeHandlerJobTypeCommissionEntity.add(treatSavingCardProjectSchemeHandlerJobTypeCommissionEntity);
                        }
                    }

                }
            }
            if (listTreatSavingCardSchemeHandlerCommissionEntity != null && listTreatSavingCardSchemeHandlerCommissionEntity.size() > 0) {
                List<List<TreatSavingCardSchemeHandlerCommissionEntity>> subs = Lists.partition(listTreatSavingCardSchemeHandlerCommissionEntity, 20);
                for (List<TreatSavingCardSchemeHandlerCommissionEntity> entityList : subs) {
                    treatSavingCardSchemeHandlerCommissionMapper.insertTreatSavingCardSchemeHandlerCommission(entityList);
                }
            }
            if (listTreatSavingCardSchemeHandlerJobTypeCommissionEntity != null && listTreatSavingCardSchemeHandlerJobTypeCommissionEntity.size() > 0) {
                List<List<TreatSavingCardSchemeHandlerJobTypeCommissionEntity>> subs = Lists.partition(listTreatSavingCardSchemeHandlerJobTypeCommissionEntity, 20);
                for (List<TreatSavingCardSchemeHandlerJobTypeCommissionEntity> entityList : subs) {
                    treatSavingCardSchemeHandlerCommissionMapper.insertTreatSavingCardSchemeHandlerJobTypeCommission(entityList);
                }
            }
        }
        return BaseOutput.success();
    }
}