package com.zhelian.service.ibeauty.saleTimeCardCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.SaleTimeCardCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SaleTimeCardCommissionEntity;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardsCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.SaleTimeCardCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleTimeCardCommissionService {

    @Autowired
    SaleTimeCardCommissionMapper saleTimeCardCommissionMapper;


    public BaseOutput allSaleTimeCardCommission(SaleTimeCardCategoryHandlerCommissionQueryForm form) {
        List<SaleTimeCardCommissionOutputForm> list = saleTimeCardCommissionMapper.allSaleTimeCardCommission(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleTimeCardCommission(SaleTimeCardsCommissionAddForm form) {

        saleTimeCardCommissionMapper.deleteSaleTimeCardCommissionByEntityIDAndCategory(form.EntityID, form.CategoryID);
        List list = new ArrayList<>();
        if (form.TimeCard != null && form.TimeCard.size() > 0) {
            for (SaleTimeCardCommissionAddForm saleTimeCardCommissionAddForm : form.TimeCard) {
                if (saleTimeCardCommissionAddForm.PayRate != null || saleTimeCardCommissionAddForm.PayFixed != null ||
                        saleTimeCardCommissionAddForm.SavingCardRate != null || saleTimeCardCommissionAddForm.SavingCardFixed != null ||
                        saleTimeCardCommissionAddForm.SavingCardLargessRate != null || saleTimeCardCommissionAddForm.SavingCardLargessFixed != null ||
                        saleTimeCardCommissionAddForm.SpecialBenefit != null) {
                    SaleTimeCardCommissionEntity saleTimeCardCommissionEntity = new SaleTimeCardCommissionEntity();
                    saleTimeCardCommissionEntity.EntityID = form.EntityID;
                    saleTimeCardCommissionEntity.TimeCardID = saleTimeCardCommissionAddForm.TimeCardID;
                    saleTimeCardCommissionEntity.PayFixed = saleTimeCardCommissionAddForm.PayFixed;
                    saleTimeCardCommissionEntity.PayRate = saleTimeCardCommissionAddForm.PayRate;
                    saleTimeCardCommissionEntity.SavingCardFixed = saleTimeCardCommissionAddForm.SavingCardFixed;
                    saleTimeCardCommissionEntity.SavingCardRate = saleTimeCardCommissionAddForm.SavingCardRate;
                    saleTimeCardCommissionEntity.SavingCardLargessFixed = saleTimeCardCommissionAddForm.SavingCardLargessFixed;
                    saleTimeCardCommissionEntity.SavingCardLargessRate = saleTimeCardCommissionAddForm.SavingCardLargessRate;
                    saleTimeCardCommissionEntity.SpecialBenefit = saleTimeCardCommissionAddForm.SpecialBenefit;
                    list.add(saleTimeCardCommissionEntity);
                }
            }
            if (list != null && list.size() > 0) {
                List<List<SaleTimeCardCommissionEntity>> subs = Lists.partition(list, 20);
                for (List<SaleTimeCardCommissionEntity> entityList : subs) {
                    saleTimeCardCommissionMapper.insertSaleTimeCardCommission(entityList);
                }
            }
        }
        return BaseOutput.success();
    }

}