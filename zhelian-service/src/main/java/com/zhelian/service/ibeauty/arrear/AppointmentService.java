package com.zhelian.service.ibeauty.arrear;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.utils.DateTimeUtil;
import com.zhelian.mapper.ibeauty.AppointmentMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.AppointmentConfigEntity;
import com.zhelian.model.ibeauty.entity.AppointmentScheduleEmployeeEntity;
import com.zhelian.model.ibeauty.entity.AppointmentScheduleEntity;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.ibeauty.form.output.AppointmentConfigOutputForm;
import com.zhelian.model.ibeauty.form.output.AppointmentProjectOnlyOutputForm;
import com.zhelian.model.ibeauty.form.output.SaleGoodsParentCategoryOutputForm;
import com.zhelian.model.miniprogram.form.output.MiniCategoryAndProjectOutputForm;
import com.zhelian.model.miniprogram.form.output.MiniProjectOnlyOutputForm;
import com.zhelian.model.miniprogram.form.output.MiniTopCategoryAndProjectOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import static com.zhelian.core.utils.ListUtil.listToTree;

@Service
public class AppointmentService {

    @Autowired
    private AppointmentMapper appointmentMapper;

    public BaseOutput appointmentConfig() {
        AppointmentConfigEntity appointmentConfigEntity = appointmentMapper.appointmentConfig(SystemUserUtil.getSystemEntityID());
        Integer appointmentServicerID = appointmentMapper.appointmentConfigServicer(SystemUserUtil.getSystemEntityID());
        if (appointmentConfigEntity == null) {
            appointmentConfigEntity = new AppointmentConfigEntity();
            appointmentConfigEntity.EntityID = SystemUserUtil.getSystemEntityID();
            appointmentConfigEntity.StartTime = "10:00";
            appointmentConfigEntity.EndTime = "22:00";
            appointmentConfigEntity.Period = 30;
            appointmentConfigEntity.CanChooseRestEmployee = false;
            appointmentConfigEntity.AppointmentServicerIsRequired = false;
            appointmentMapper.createAppointmentConfig(appointmentConfigEntity);
        }

        AppointmentConfigOutputForm appointmentConfigOutputForm = new AppointmentConfigOutputForm();
        appointmentConfigOutputForm.ID = appointmentConfigEntity.ID;
        appointmentConfigOutputForm.StartTime = appointmentConfigEntity.StartTime;
        appointmentConfigOutputForm.EndTime = appointmentConfigEntity.EndTime;
        appointmentConfigOutputForm.Period = appointmentConfigEntity.Period;
        appointmentConfigOutputForm.CanChooseRestEmployee = appointmentConfigEntity.CanChooseRestEmployee;
        appointmentConfigOutputForm.AppointmentServicerIsRequired = appointmentConfigEntity.AppointmentServicerIsRequired;
        if (appointmentServicerID != null) {
            appointmentConfigOutputForm.AppointmentServicerID = appointmentServicerID;
        }

        return BaseOutputForm.success(appointmentConfigOutputForm);
    }

    @Transactional
    public BaseOutput updateAppointmentConfig(AppointmentConfigUpdateForm form) {
        appointmentMapper.updateAppointmentConfig(form);
        Integer appointmentServicerID = appointmentMapper.appointmentConfigServicer(SystemUserUtil.getSystemEntityID());
        if(form.AppointmentServicerID!=null){

            if (appointmentServicerID == null) {
                appointmentMapper.createAppointmentConfigServicer(SystemUserUtil.getSystemEntityID(), form.AppointmentServicerID);
            } else {
                appointmentMapper.updateAppointmentConfigServicer(SystemUserUtil.getSystemEntityID(), form.AppointmentServicerID);
            }
        }else{
            if (appointmentServicerID != null) {
                appointmentMapper.deleteAppointmentConfigServicer(SystemUserUtil.getSystemEntityID());
            }

        }

        return BaseOutput.success();
    }

    public BaseOutput schedule() {
        return BaseOutputForm.success(appointmentMapper.appointmentSchedule(SystemUserUtil.getSystemEntityID()));
    }

    public BaseOutput createSchedule(AppointmentScheduleAddForm form) {
        AppointmentScheduleEntity appointmentScheduleEntity = new AppointmentScheduleEntity();
        appointmentScheduleEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        appointmentScheduleEntity.EntityID = SystemUserUtil.getSystemEntityID();
        appointmentScheduleEntity.Name = form.Name;
        appointmentScheduleEntity.StartTime = form.StartTime;
        appointmentScheduleEntity.EndTime = form.EndTime;
        appointmentMapper.createSchedule(appointmentScheduleEntity);
        return BaseOutput.success();
    }

    public BaseOutput updateSchedule(AppointmentScheduleUpdateForm form) {
        AppointmentScheduleEntity appointmentScheduleEntity = new AppointmentScheduleEntity();
        appointmentScheduleEntity.ID = form.ID;
        appointmentScheduleEntity.Name = form.Name;
        appointmentScheduleEntity.StartTime = form.StartTime;
        appointmentScheduleEntity.EndTime = form.EndTime;
        appointmentMapper.updateSchedule(appointmentScheduleEntity);
        return BaseOutput.success();
    }

    public BaseOutput deleteSchedule(AppointmentScheduleUpdateForm form) {
        appointmentMapper.deleteSchedule(form.ID);
        return BaseOutput.success();
    }

    public BaseOutput employeeSchedule(AppointmentEmployeeScheduleQueryForm form) {
        return BaseOutputForm.success(appointmentMapper.employeeScheduleJson(form.Month, SystemUserUtil.getSystemEntityID(), form.EmployeeID));
    }

    @Transactional
    public BaseOutput updateEmployeeSchedule(List<AppointmentEmployeeScheduleUpdateForm> formList) throws Exception {
        for (AppointmentEmployeeScheduleUpdateForm form : formList) {
            for (AppointmentScheduleForm appointmentScheduleForm : form.Schedule) {
                appointmentMapper.deleteEmployeeScheduleByScheduleDate(SystemUserUtil.getSystemEntityID(), form.AppointmentServicerID, form.EmployeeID, appointmentScheduleForm.ScheduleDate);
                AppointmentScheduleEmployeeEntity appointmentScheduleEmployeeEntity = new AppointmentScheduleEmployeeEntity();
                appointmentScheduleEmployeeEntity.EntityID = SystemUserUtil.getSystemEntityID();
                appointmentScheduleEmployeeEntity.AppointmentServicerID = form.AppointmentServicerID;
                appointmentScheduleEmployeeEntity.EmployeeID = form.EmployeeID;
                appointmentScheduleEmployeeEntity.AppointmentScheduleID = appointmentScheduleForm.AppointmentScheduleID;
                appointmentScheduleEmployeeEntity.ScheduleDate = DateTimeUtil.toDate(appointmentScheduleForm.ScheduleDate);
                appointmentScheduleEmployeeEntity.IsRest = appointmentScheduleForm.IsRest;
                appointmentScheduleEmployeeEntity.CreatedBy = SystemUserUtil.getSystemUserID();
                appointmentMapper.createEmployeeSchedule(appointmentScheduleEmployeeEntity);
            }
        }
        return BaseOutput.success();
    }

    public BaseOutput findCategoryAndProject(AppointmentBillProjectQueryForm form) {

        List<Integer> parentList = new ArrayList<>();
        List<MiniCategoryAndProjectOutputForm> list = appointmentMapper.findCategoryAndProject(SystemUserUtil.getSystemEntityID(), form.Name);
        for (MiniCategoryAndProjectOutputForm projectOnlyOutputForm : list) {
            if (!parentList.contains(projectOnlyOutputForm.ParentID))
                parentList.add(projectOnlyOutputForm.ParentID);
        }

        List<MiniTopCategoryAndProjectOutputForm> topList = null;
        JSONArray result = new JSONArray();
        if (parentList != null && parentList.size() > 0) {
            topList = appointmentMapper.getTopProjectList(parentList);
            for (MiniTopCategoryAndProjectOutputForm categoryAndProjectOutputForm : topList) {
                List<MiniCategoryAndProjectOutputForm> child = new ArrayList();
                for (MiniCategoryAndProjectOutputForm projectOnlyOutputForm : list) {
                    if (projectOnlyOutputForm.ParentID.equals(categoryAndProjectOutputForm.ID)) {
                        child.add(projectOnlyOutputForm);
                    }
                }
                categoryAndProjectOutputForm.Child = child;
            }
            result = listToTree(JSONArray.parseArray(JSON.toJSONString(topList, JSONWriter.Feature.WriteMapNullValue)), "ID", "ParentID", "Child");
        }
        return BaseOutputForm.success(result);
    }

    public BaseOutput findProject(AppointmentBillProjectQueryForm form) {
        List<MiniProjectOnlyOutputForm> list = appointmentMapper.findProject(SystemUserUtil.getSystemEntityID(), form.Name);
        return BaseOutputForm.success(list);
    }

    public BaseOutput projectCategory(AppointmentBillProjectQueryForm form) {
        List<SaleGoodsParentCategoryOutputForm> result = appointmentMapper.allAppointmemntGoodsProjectCategory(SystemUserUtil.getSystemEntityID(), form.Name);
        return BaseOutputForm.success(result);
    }

    public BaseOutput projectByCategory(AppointmentBillProjectQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<AppointmentProjectOnlyOutputForm> result = appointmentMapper.projectByCategory(SystemUserUtil.getSystemEntityID(), form.Name, form.CategoryID);
        return BasePageInfo.success(result);

    }
}