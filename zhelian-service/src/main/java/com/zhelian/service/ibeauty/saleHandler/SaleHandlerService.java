package com.zhelian.service.ibeauty.saleHandler;


import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.ibeauty.SaleHandlerMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.output.AllSaleHandlerOutputForm;
import com.zhelian.model.ibeauty.form.output.SaleAllHandlerOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class SaleHandlerService {

    @Autowired
    SaleHandlerMapper saleHandlerMapper;

    public BaseOutput productHandler() {
        Integer entityID = SystemUserUtil.getSystemEntityID();
        List<AllSaleHandlerOutputForm> productHandlerOutputForms = saleHandlerMapper.productSaleHandler(entityID);
        return BaseOutputForm.success(productHandlerOutputForms);
    }

    public BaseOutput projectHandler() {
        Integer entityID = SystemUserUtil.getSystemEntityID();
        List<AllSaleHandlerOutputForm> productHandlerOutputForms = saleHandlerMapper.projectSaleHandler(entityID);
        return BaseOutputForm.success(productHandlerOutputForms);
    }

    public BaseOutput generalCardHandler() {
        Integer entityID = SystemUserUtil.getSystemEntityID();
        List<AllSaleHandlerOutputForm> productHandlerOutputForms = saleHandlerMapper.generalCardSaleHandler(entityID);
        return BaseOutputForm.success(productHandlerOutputForms);
    }

    public BaseOutput timeCardHandler() {
        Integer entityID = SystemUserUtil.getSystemEntityID();
        List<AllSaleHandlerOutputForm> productHandlerOutputForms = saleHandlerMapper.timeCardSaleHandler(entityID);
        return BaseOutputForm.success(productHandlerOutputForms);
    }

    public BaseOutput savingCardHandler() {
        Integer entityID = SystemUserUtil.getSystemEntityID();
        List<AllSaleHandlerOutputForm> productHandlerOutputForms = saleHandlerMapper.savingCardSaleHandler(entityID);
        return BaseOutputForm.success(productHandlerOutputForms);
    }

    public BaseOutput packageCardHandler() {
        Integer entityID = SystemUserUtil.getSystemEntityID();
        List<AllSaleHandlerOutputForm> productHandlerOutputForms = saleHandlerMapper.packageCardSaleHandler(entityID);
        return BaseOutputForm.success(productHandlerOutputForms);
    }

    public BaseOutput allHandler(List<String> GoodTypes) {
        Integer entityID = SystemUserUtil.getSystemEntityID();
        List<SaleAllHandlerOutputForm> outputForms = saleHandlerMapper.allHandler(entityID,GoodTypes);
        return BaseOutputForm.success(outputForms);
    }
}