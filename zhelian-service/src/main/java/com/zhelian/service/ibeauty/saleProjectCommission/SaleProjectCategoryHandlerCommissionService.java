package com.zhelian.service.ibeauty.saleProjectCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.SalePackageCardProjectCategoryHandlerCommissionMapper;
import com.zhelian.mapper.ibeauty.SaleProjectCategoryHandlerCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SalePackageCardProjectCategoryHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SalePackageCardProjectCategoryHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleProjectCategoryHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleProjectCategoryHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.SaleProjectCategoryHandlerCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleProjectCategoryHandlerCommissionService {

    @Autowired
    SaleProjectCategoryHandlerCommissionMapper saleProjectCategoryHandlerCommissionMapper;
    @Autowired
    SalePackageCardProjectCategoryHandlerCommissionMapper salePackageCardProjectCategoryHandlerCommissionMapper;


    public BaseOutput allSaleProjectCategoryHandlerCommission(SaleProjectCategoryHandlerCommissionQueryForm form) {
        List<SaleProjectCategoryHandlerCommissionOutputForm> list = saleProjectCategoryHandlerCommissionMapper.allSaleProjectCategoryHandlerCommission(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSalePakageCardProjectCategoryHandlerCommission(SaleProjectCategoryHandlerCommissionQueryForm form) {
        List<SaleProjectCategoryHandlerCommissionOutputForm> list = salePackageCardProjectCategoryHandlerCommissionMapper.allSalePakageCardProjectCategoryHandlerCommission(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleProjectCategoryHandlerCommission(SaleProjectCategoryHandlerCommissionAddForm form) {

        saleProjectCategoryHandlerCommissionMapper.deleteSaleProjectCategoryHandler(form.EntityID, form.CategoryID);
        saleProjectCategoryHandlerCommissionMapper.deleteSaleProjectCategoryHandlerJobType(form.EntityID, form.CategoryID);

        List<SaleProjectCategoryHandlerCommissionEntity> listSaleProjectCategoryHandlerCommission = new ArrayList<>();
        List<SaleProjectCategoryHandlerJobTypeCommissionEntity> listSaleProjectCategoryHandlerJobTypeCommission = new ArrayList<>();
        if (form.ProjectHandler != null && form.ProjectHandler.size() > 0) {
            for (SaleProjectCategoryHandlerAddForm saleProjectCategoryHandlerAddForm : form.ProjectHandler) {
                if (saleProjectCategoryHandlerAddForm.PayRate != null || saleProjectCategoryHandlerAddForm.PayFixed != null ||
                        saleProjectCategoryHandlerAddForm.SavingCardRate != null || saleProjectCategoryHandlerAddForm.SavingCardFixed != null ||
                        saleProjectCategoryHandlerAddForm.SavingCardLargessRate != null || saleProjectCategoryHandlerAddForm.SavingCardLargessFixed != null ||
                        saleProjectCategoryHandlerAddForm.SpecialBenefit != null) {
                    SaleProjectCategoryHandlerCommissionEntity saleProjectCategoryHandlerCommissionEntity = new SaleProjectCategoryHandlerCommissionEntity();
                    saleProjectCategoryHandlerCommissionEntity.EntityID = form.EntityID;
                    saleProjectCategoryHandlerCommissionEntity.ProjectCategoryID = form.CategoryID;
                    saleProjectCategoryHandlerCommissionEntity.ProjectSaleHandlerID = saleProjectCategoryHandlerAddForm.SaleHandlerID;
                    saleProjectCategoryHandlerCommissionEntity.PayFixed = saleProjectCategoryHandlerAddForm.PayFixed;
                    saleProjectCategoryHandlerCommissionEntity.PayRate = saleProjectCategoryHandlerAddForm.PayRate;
                    saleProjectCategoryHandlerCommissionEntity.SavingCardFixed = saleProjectCategoryHandlerAddForm.SavingCardFixed;
                    saleProjectCategoryHandlerCommissionEntity.SavingCardRate = saleProjectCategoryHandlerAddForm.SavingCardRate;
                    saleProjectCategoryHandlerCommissionEntity.SavingCardLargessFixed = saleProjectCategoryHandlerAddForm.SavingCardLargessFixed;
                    saleProjectCategoryHandlerCommissionEntity.SavingCardLargessRate = saleProjectCategoryHandlerAddForm.SavingCardLargessRate;
                    saleProjectCategoryHandlerCommissionEntity.SpecialBenefit = saleProjectCategoryHandlerAddForm.SpecialBenefit;
                    listSaleProjectCategoryHandlerCommission.add(saleProjectCategoryHandlerCommissionEntity);
                }

                if (saleProjectCategoryHandlerAddForm.HandlerJobTypeCommission != null && saleProjectCategoryHandlerAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (SaleProjectCategoryHandlerJobTypeCommissionAddForm saleProjectCategoryHandlerJobTypeCommissionAddForm : saleProjectCategoryHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleProjectCategoryHandlerJobTypeCommissionAddForm.PayRate != null || saleProjectCategoryHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardRate != null || saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed != null ||
                                saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null || saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed != null ||
                                saleProjectCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SaleProjectCategoryHandlerJobTypeCommissionEntity saleProjectCategoryHandlerJobTypeCommissionEntity = new SaleProjectCategoryHandlerJobTypeCommissionEntity();
                            saleProjectCategoryHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            saleProjectCategoryHandlerJobTypeCommissionEntity.ProjectCategoryID = form.CategoryID;
                            saleProjectCategoryHandlerJobTypeCommissionEntity.ProjectSaleHandlerID = saleProjectCategoryHandlerAddForm.SaleHandlerID;
                            saleProjectCategoryHandlerJobTypeCommissionEntity.JobTypeID = saleProjectCategoryHandlerJobTypeCommissionAddForm.JobTypeID;
                            saleProjectCategoryHandlerJobTypeCommissionEntity.PayFixed = saleProjectCategoryHandlerJobTypeCommissionAddForm.PayFixed;
                            saleProjectCategoryHandlerJobTypeCommissionEntity.PayRate = saleProjectCategoryHandlerJobTypeCommissionAddForm.PayRate;
                            saleProjectCategoryHandlerJobTypeCommissionEntity.SavingCardFixed = saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed;
                            saleProjectCategoryHandlerJobTypeCommissionEntity.SavingCardRate = saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardRate;
                            saleProjectCategoryHandlerJobTypeCommissionEntity.SavingCardLargessFixed = saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed;
                            saleProjectCategoryHandlerJobTypeCommissionEntity.SavingCardLargessRate = saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            saleProjectCategoryHandlerJobTypeCommissionEntity.SpecialBenefit = saleProjectCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            listSaleProjectCategoryHandlerJobTypeCommission.add(saleProjectCategoryHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
        }

        salePackageCardProjectCategoryHandlerCommissionMapper.deleteSalePackageCardProjectCategoryHandler(form.EntityID, form.CategoryID);
        salePackageCardProjectCategoryHandlerCommissionMapper.deleteSalePackageCardProjectCategoryHandlerJobType(form.EntityID, form.CategoryID);

        List<SalePackageCardProjectCategoryHandlerCommissionEntity> listSalePackageCardProjectCategoryHandlerCommission = new ArrayList<>();
        List<SalePackageCardProjectCategoryHandlerJobTypeCommissionEntity> listSalePackageCardProjectCategoryHandlerJobTypeCommission = new ArrayList<>();
        if (form.ProjectHandler != null && form.ProjectHandler.size() > 0) {
            for (SaleProjectCategoryHandlerAddForm saleProjectCategoryHandlerAddForm : form.PackageCardProjectHandler) {
                if (saleProjectCategoryHandlerAddForm.PayRate != null || saleProjectCategoryHandlerAddForm.PayFixed != null ||
                        saleProjectCategoryHandlerAddForm.SavingCardRate != null || saleProjectCategoryHandlerAddForm.SavingCardFixed != null ||
                        saleProjectCategoryHandlerAddForm.SavingCardLargessRate != null || saleProjectCategoryHandlerAddForm.SavingCardLargessFixed != null ||
                        saleProjectCategoryHandlerAddForm.SpecialBenefit != null) {
                    SalePackageCardProjectCategoryHandlerCommissionEntity salePackageCardProjectCategoryHandlerCommissionEntity = new SalePackageCardProjectCategoryHandlerCommissionEntity();
                    salePackageCardProjectCategoryHandlerCommissionEntity.EntityID = form.EntityID;
                    salePackageCardProjectCategoryHandlerCommissionEntity.ProjectCategoryID = form.CategoryID;
                    salePackageCardProjectCategoryHandlerCommissionEntity.PackageCardSaleHandlerID = saleProjectCategoryHandlerAddForm.SaleHandlerID;
                    salePackageCardProjectCategoryHandlerCommissionEntity.PayFixed = saleProjectCategoryHandlerAddForm.PayFixed;
                    salePackageCardProjectCategoryHandlerCommissionEntity.PayRate = saleProjectCategoryHandlerAddForm.PayRate;
                    salePackageCardProjectCategoryHandlerCommissionEntity.SavingCardFixed = saleProjectCategoryHandlerAddForm.SavingCardFixed;
                    salePackageCardProjectCategoryHandlerCommissionEntity.SavingCardRate = saleProjectCategoryHandlerAddForm.SavingCardRate;
                    salePackageCardProjectCategoryHandlerCommissionEntity.SavingCardLargessFixed = saleProjectCategoryHandlerAddForm.SavingCardLargessFixed;
                    salePackageCardProjectCategoryHandlerCommissionEntity.SavingCardLargessRate = saleProjectCategoryHandlerAddForm.SavingCardLargessRate;
                    salePackageCardProjectCategoryHandlerCommissionEntity.SpecialBenefit = saleProjectCategoryHandlerAddForm.SpecialBenefit;
                    listSalePackageCardProjectCategoryHandlerCommission.add(salePackageCardProjectCategoryHandlerCommissionEntity);
                }

                if (saleProjectCategoryHandlerAddForm.HandlerJobTypeCommission != null && saleProjectCategoryHandlerAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (SaleProjectCategoryHandlerJobTypeCommissionAddForm saleProjectCategoryHandlerJobTypeCommissionAddForm : saleProjectCategoryHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleProjectCategoryHandlerJobTypeCommissionAddForm.PayRate != null || saleProjectCategoryHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardRate != null || saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed != null ||
                                saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null || saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed != null ||
                                saleProjectCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SalePackageCardProjectCategoryHandlerJobTypeCommissionEntity salePackageCardProjectCategoryHandlerJobTypeCommissionEntity = new SalePackageCardProjectCategoryHandlerJobTypeCommissionEntity();
                            salePackageCardProjectCategoryHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            salePackageCardProjectCategoryHandlerJobTypeCommissionEntity.ProjectCategoryID = form.CategoryID;
                            salePackageCardProjectCategoryHandlerJobTypeCommissionEntity.PackageCardSaleHandlerID = saleProjectCategoryHandlerAddForm.SaleHandlerID;
                            salePackageCardProjectCategoryHandlerJobTypeCommissionEntity.JobTypeID = saleProjectCategoryHandlerJobTypeCommissionAddForm.JobTypeID;
                            salePackageCardProjectCategoryHandlerJobTypeCommissionEntity.PayFixed = saleProjectCategoryHandlerJobTypeCommissionAddForm.PayFixed;
                            salePackageCardProjectCategoryHandlerJobTypeCommissionEntity.PayRate = saleProjectCategoryHandlerJobTypeCommissionAddForm.PayRate;
                            salePackageCardProjectCategoryHandlerJobTypeCommissionEntity.SavingCardFixed = saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed;
                            salePackageCardProjectCategoryHandlerJobTypeCommissionEntity.SavingCardRate = saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardRate;
                            salePackageCardProjectCategoryHandlerJobTypeCommissionEntity.SavingCardLargessFixed = saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed;
                            salePackageCardProjectCategoryHandlerJobTypeCommissionEntity.SavingCardLargessRate = saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            salePackageCardProjectCategoryHandlerJobTypeCommissionEntity.SpecialBenefit = saleProjectCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            listSalePackageCardProjectCategoryHandlerJobTypeCommission.add(salePackageCardProjectCategoryHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
        }
        if (listSaleProjectCategoryHandlerCommission != null && listSaleProjectCategoryHandlerCommission.size() > 0) {
            List<List<SaleProjectCategoryHandlerCommissionEntity>> subs = Lists.partition(listSaleProjectCategoryHandlerCommission, 20);
            for (List<SaleProjectCategoryHandlerCommissionEntity> entityList : subs) {
                saleProjectCategoryHandlerCommissionMapper.updateSaleProjectCategoryHandlerCommission(entityList);
            }
        }
        if (listSaleProjectCategoryHandlerJobTypeCommission != null && listSaleProjectCategoryHandlerJobTypeCommission.size() > 0) {
            List<List<SaleProjectCategoryHandlerJobTypeCommissionEntity>> subs = Lists.partition(listSaleProjectCategoryHandlerJobTypeCommission, 20);
            for (List<SaleProjectCategoryHandlerJobTypeCommissionEntity> entityList : subs) {
                saleProjectCategoryHandlerCommissionMapper.insertSaleProjectCategoryHandlerJobTypeCommission(entityList);
            }
        }
        if (listSalePackageCardProjectCategoryHandlerCommission != null && listSalePackageCardProjectCategoryHandlerCommission.size() > 0) {
            List<List<SalePackageCardProjectCategoryHandlerCommissionEntity>> subs = Lists.partition(listSalePackageCardProjectCategoryHandlerCommission, 20);
            for (List<SalePackageCardProjectCategoryHandlerCommissionEntity> entityList : subs) {
                salePackageCardProjectCategoryHandlerCommissionMapper.updateSalePackageCardProjectCategoryHandlerCommission(entityList);
            }
        }
        if (listSalePackageCardProjectCategoryHandlerJobTypeCommission != null && listSalePackageCardProjectCategoryHandlerJobTypeCommission.size() > 0) {
            List<List<SalePackageCardProjectCategoryHandlerJobTypeCommissionEntity>> subs = Lists.partition(listSalePackageCardProjectCategoryHandlerJobTypeCommission, 20);
            for (List<SalePackageCardProjectCategoryHandlerJobTypeCommissionEntity> entityList : subs) {
                salePackageCardProjectCategoryHandlerCommissionMapper.insertSalePackageCardProjectCategoryHandlerJobTypeCommission(entityList);
            }
        }

        return BaseOutput.success();
    }
}