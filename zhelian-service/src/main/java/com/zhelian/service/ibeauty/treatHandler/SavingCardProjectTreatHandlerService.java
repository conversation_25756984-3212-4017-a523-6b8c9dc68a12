package com.zhelian.service.ibeauty.treatHandler;


import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.SavingCardProjectTreatHandlerMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SavingCardProjectTreatHandlerEntity;
import com.zhelian.model.ibeauty.entity.SavingCardProjectTreatHandlerJobTypeEntity;
import com.zhelian.model.ibeauty.form.input.SaleHandlerMoveForm;
import com.zhelian.model.ibeauty.form.input.TreatHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.TreatHandlerQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatHandlerUpdateForm;
import com.zhelian.model.ibeauty.form.output.TreatHandlerOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;


@Service
public class SavingCardProjectTreatHandlerService {

    @Autowired
    SavingCardProjectTreatHandlerMapper savingCardProjectTreatHandlerMapper;


    public BaseOutput allSavingCardProjectTreatHandler(TreatHandlerQueryForm form) {

        List<TreatHandlerOutputForm> list = savingCardProjectTreatHandlerMapper.allSavingCardProjectTreatHandler(form.Name, form.EntityID, form.Active);
        return BaseOutputForm.success(list);

    }

    @Transactional
    public BaseOutput createSavingCardProjectTreatHandler(TreatHandlerAddForm form) {
        Integer Sequence = savingCardProjectTreatHandlerMapper.getMaxSequence();
        SavingCardProjectTreatHandlerEntity savingCardProjectTreatHandlerEntity = new SavingCardProjectTreatHandlerEntity();
        savingCardProjectTreatHandlerEntity.Name = form.Name;
        savingCardProjectTreatHandlerEntity.EntityID = form.EntityID;
        savingCardProjectTreatHandlerEntity.Sequence = Sequence != null ? Sequence + 1 : 1;

        savingCardProjectTreatHandlerMapper.createSavingCardProjectTreatHandler(savingCardProjectTreatHandlerEntity);

        insertHandlerJobType(form.JobType, savingCardProjectTreatHandlerEntity.ID);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateSavingCardProjectTreatHandler(TreatHandlerUpdateForm form) {

        SavingCardProjectTreatHandlerEntity savingCardProjectTreatHandlerEntity = new SavingCardProjectTreatHandlerEntity();
        savingCardProjectTreatHandlerEntity.Name = form.Name;
        savingCardProjectTreatHandlerEntity.ID = form.ID;
        savingCardProjectTreatHandlerEntity.EntityID = form.EntityID;
        savingCardProjectTreatHandlerEntity.Active = form.Active;

        savingCardProjectTreatHandlerMapper.updateSavingCardProjectTreatHandler(savingCardProjectTreatHandlerEntity);

        savingCardProjectTreatHandlerMapper.deleteTreatHandlerJobType(form.ID);

        insertHandlerJobType(form.JobType, form.ID);

        return BaseOutput.success();
    }


    public void insertHandlerJobType(Integer[] JobType, Integer ID) {
        List<SavingCardProjectTreatHandlerJobTypeEntity> list = new ArrayList<>();
        for (Integer typeID : JobType) {
            SavingCardProjectTreatHandlerJobTypeEntity savingCardProjectTreatHandlerJobTypeEntity = new SavingCardProjectTreatHandlerJobTypeEntity();
            savingCardProjectTreatHandlerJobTypeEntity.SavingCardProjectTreatHandlerID = ID;
            savingCardProjectTreatHandlerJobTypeEntity.JobTypeID = typeID;
            list.add(savingCardProjectTreatHandlerJobTypeEntity);
        }
        savingCardProjectTreatHandlerMapper.insertHandlerJobType(list);
    }

    public BaseOutput moveSavingCardProjectTreatHandler(SaleHandlerMoveForm form) {
        //重新排序
        LinkedList<Integer> linkedList = savingCardProjectTreatHandlerMapper.getIDOrderBySequenceASC(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<SavingCardProjectTreatHandlerEntity> list = new ArrayList<>();

        for (Integer ID : linkedList) {
            SavingCardProjectTreatHandlerEntity savingCardProjectTreatHandlerEntity = new SavingCardProjectTreatHandlerEntity();
            savingCardProjectTreatHandlerEntity.ID = ID;
            savingCardProjectTreatHandlerEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(savingCardProjectTreatHandlerEntity);
        }
        savingCardProjectTreatHandlerMapper.updateSequence(list);

        return BaseOutput.success();
    }

}