package com.zhelian.service.ibeauty.customer;


import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.ibeauty.CustomerProductAccountMapper;
import com.zhelian.mapper.ibeauty.ProductAccountMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.ProductAccountEntity;
import com.zhelian.model.ibeauty.form.input.AccountQueryForm;
import com.zhelian.model.ibeauty.form.output.AccountOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class CustomerProductAccountService {

    @Autowired
    private CustomerProductAccountMapper customerProductAccountMapper;
    @Autowired
    private ProductAccountMapper productAccountMapper;

    public BaseOutput treatBill(AccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(customerProductAccountMapper.getTreatBill(form.ID));
    }

    public BaseOutput refundBill(AccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(customerProductAccountMapper.getRefundBill(form.ID));
    }

    public BaseOutput refundTreatBill(AccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(customerProductAccountMapper.getRefundTreatBill(form.ID));
    }

    public BaseOutput transferBill(AccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(customerProductAccountMapper.getTransferBill(form.ID));
    }

    public BaseOutput repayment(AccountQueryForm form) {
        ProductAccountEntity productAccountEntity = productAccountMapper.getProductAccountByAccountID(Integer.parseInt(form.ID));

        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<AccountOutputForm> list = null;
        if (productAccountEntity.PackageCardAccountID == null) {
            list = customerProductAccountMapper.getRepayment(form.ID);
        } else {
            list = customerProductAccountMapper.getPackageCardRepayment(form.ID);
        }
        return BasePageInfo.success(list);
    }

    public BaseOutput arrearAmountProcess(AccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(customerProductAccountMapper.getArrearAmountProcess(form.ID));
    }
}