package com.zhelian.service.ibeauty.timeCard;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.TimeCardMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.TbTimeCardImageEntity;
import com.zhelian.model.ibeauty.entity.TimeCardEntity;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.ibeauty.form.output.AllTimeCardOutputForm;
import com.zhelian.model.ibeauty.form.output.TimeCardDetailsOutputForm;
import com.zhelian.model.ibeauty.form.output.TimeCardOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

@Service
public class TimeCardService {

    @Autowired
    TimeCardMapper mapper;

    public BaseOutput timeCardList(TimeCardQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<TimeCardOutputForm> list = mapper.timeCardList(form.Name, form.TimeCardCategoryID, form.IsAllowSell);
        return BasePageInfo.success(list);
    }

    public BaseOutput getTimeCardDetails(TimeCardQueryEntityForm form) {
        TimeCardDetailsOutputForm productDetails = mapper.getTimeCardDetails(form.ID);
        return BaseOutputForm.success(productDetails);
    }

    public BaseOutput timeCardSaleEntity(TimeCardQueryEntityForm form) {
        return BaseOutputForm.success(mapper.timeCardSaleEntity(form));
    }

    public BaseOutput timeCardConsumeEntity(TimeCardQueryEntityForm form) {
        return BaseOutputForm.success(mapper.timeCardConsumeEntity(form));
    }

    public BaseOutput timeCardModifyPriceEntity(TimeCardQueryEntityForm form) {
        return BaseOutputForm.success(mapper.timeCardModifyPriceEntity(form));
    }

    public BaseOutput timeCardProject(TimeCardQueryEntityForm form) {
        return BaseOutputForm.success(mapper.timeCardProject(form));
    }

    @Transactional
    public BaseOutput createTimeCard(TimeCardAddForm form) {
        TimeCardEntity timeCardEntity = new TimeCardEntity();
        timeCardEntity.Name = form.Name;
        timeCardEntity.Alias = form.Alias;
        timeCardEntity.TimeCardCategoryID = form.TimeCardCategoryID;
        timeCardEntity.ConsumeCycle = form.ConsumeCycle == null ? 0 : form.ConsumeCycle;
        timeCardEntity.CycleLimitAmount = form.CycleLimitAmount == null ? 0 : form.CycleLimitAmount;
        timeCardEntity.PerformanceAmount = form.PerformanceAmount;
        timeCardEntity.Price = form.Price;
        timeCardEntity.SaleStartDate = form.SaleStartDate;
        timeCardEntity.SaleEndingDate = form.SaleEndingDate;
        timeCardEntity.ValidType = form.ValidType;
        timeCardEntity.Remark = form.Remark;
        timeCardEntity.Memo = form.Memo;
        if (form.ValidType.equals(3)) {
            timeCardEntity.ValidDay = null;
        } else {
            timeCardEntity.ValidDay = form.ValidDay;
        }
        timeCardEntity.IsAllowSell = form.IsAllowSell;
        timeCardEntity.IsAllowLargess = form.IsAllowLargess;
        mapper.addTimeCard(timeCardEntity);
        addTimeCardProjectAndConsumeAndSale(form, timeCardEntity);
        if (form.ImageList.size() > 0) {
            for (int a = 0; a < form.ImageList.size(); a++) {
                mapper.insertTimeCardImageURL(timeCardEntity.ID, form.ImageList.get(a).ImageUrl, a + 1);
            }
        }
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateTimeCard(TimeCardUpdateForm form) {
        TimeCardEntity timeCardEntity = new TimeCardEntity();
        timeCardEntity.ID = form.ID;
        timeCardEntity.Name = form.Name;
        timeCardEntity.Alias = form.Alias;
        timeCardEntity.TimeCardCategoryID = form.TimeCardCategoryID;
        timeCardEntity.ConsumeCycle = form.ConsumeCycle == null ? 0 : form.ConsumeCycle;
        timeCardEntity.CycleLimitAmount = form.CycleLimitAmount == null ? 0 : form.CycleLimitAmount;
        timeCardEntity.PerformanceAmount = form.PerformanceAmount;
        timeCardEntity.Price = form.Price;
        timeCardEntity.SaleStartDate = form.SaleStartDate;
        timeCardEntity.SaleEndingDate = form.SaleEndingDate;
        timeCardEntity.ValidType = form.ValidType;
        timeCardEntity.Remark = form.Remark;
        timeCardEntity.Memo = form.Memo;
        if (form.ValidType.equals(3)) {
            timeCardEntity.ValidDay = null;
        } else {
            timeCardEntity.ValidDay = form.ValidDay;
        }
        timeCardEntity.IsAllowSell = form.IsAllowSell;
        timeCardEntity.IsAllowLargess = form.IsAllowLargess;
        mapper.updateTimeCard(timeCardEntity);
        mapper.deleteTimeCardProject(form.ID);
        mapper.deleteTimeCardConsume(form.ID);
        mapper.deleteTimeCardSale(form.ID);
        mapper.deleteTimeCardModify(form.ID);
        addTimeCardProjectAndConsumeAndSale(form, timeCardEntity);

        List<GoodsImageUrlForm> timeCardImageList = mapper.getTimeCardImageList(form.ID);
        if (timeCardImageList.size() > 0) {
            mapper.deleteTimeCardImageByID(form.ID);
        }

        if (form.ImageList.size() > 0) {
            for (int a = 0; a < form.ImageList.size(); a++) {
                mapper.insertTimeCardImageURL(form.ID, form.ImageList.get(a).ImageUrl, a + 1);
            }
        }
        return BaseOutput.success();
    }

    private void addTimeCardProjectAndConsumeAndSale(TimeCardAddForm form, TimeCardEntity timeCardEntity) {
        List saleList = new ArrayList();
        for (Integer EntityID : form.TimeCardSaleEntity) {
            TimeCardSaleEntity entity = new TimeCardSaleEntity();
            entity.EntityID = EntityID;
            entity.TimeCardID = timeCardEntity.ID;
            saleList.add(entity);
        }
        if (saleList.size() > 0)
            mapper.addTimeCardSale(saleList);
        List consumeList = new ArrayList();
        for (Integer EntityID : form.TimeCardConsumeEntity) {
            TimeCardSaleEntity entity = new TimeCardSaleEntity();
            entity.EntityID = EntityID;
            entity.TimeCardID = timeCardEntity.ID;
            consumeList.add(entity);
        }
        if (consumeList.size() > 0)
            mapper.addTimeCardConsume(consumeList);
        List modifyList = new ArrayList();
        for (Integer EntityID : form.ModifyPriceEntity) {
            TimeCardSaleEntity entity = new TimeCardSaleEntity();
            entity.EntityID = EntityID;
            entity.TimeCardID = timeCardEntity.ID;
            modifyList.add(entity);
        }
        if (modifyList.size() > 0)
            mapper.addTimeCardModifyPrice(modifyList);
        List projectList = new ArrayList();
        for (Integer ProjectID : form.TimeCardProject) {
            TimeCardProjectEntity entity = new TimeCardProjectEntity();
            entity.ProjectID = ProjectID;
            entity.TimeCardID = timeCardEntity.ID;
            projectList.add(entity);
        }


        if (projectList != null && projectList.size() > 0) {
            List<List<TimeCardProjectEntity>> subs = Lists.partition(projectList, 20);
            for (List<TimeCardProjectEntity> entityList : subs) {
                mapper.addTimeCardProject(entityList);
            }
        }
    }

    public BaseOutput allTimeCard() {
        List<AllTimeCardOutputForm> list = mapper.allTimeCard();
        return BaseOutputForm.success(list);
    }

    public BaseOutput move(GoodsMoveForm form) {

        LinkedList<Integer> linkedList = mapper.getASCList(form.MoveID, form.ID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<TbTimeCardImageEntity> list = new ArrayList<>();
        for (Integer ID : linkedList) {
            TbTimeCardImageEntity tbTimeCardImageEntity = new TbTimeCardImageEntity();
            tbTimeCardImageEntity.ID = ID;
            tbTimeCardImageEntity.TimeCardID = form.ID;
            tbTimeCardImageEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(tbTimeCardImageEntity);
        }

        mapper.updateSequence(list);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput bitchUpdateTimeCardSaleEntity(GoodsBitchUpdateForm form) {
        mapper.deleteBitchSaleEntity(form.GoodsID);
        List<TimeCardSaleEntity> list = new ArrayList<>();
        for (Integer goodsID : form.GoodsID) {
            for (Integer entityID : form.EntityID) {
                TimeCardSaleEntity saleEntity = new TimeCardSaleEntity();
                saleEntity.TimeCardID = goodsID;
                saleEntity.EntityID = entityID;
                list.add(saleEntity);
            }
        }

        if (list.size() > 0)
            mapper.addTimeCardSale(list);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput bitchUpdateTimeCardConsumeEntity(GoodsBitchUpdateForm form) {
        mapper.deleteBitchConsumeEntity(form.GoodsID);
        List<TimeCardSaleEntity> list = new ArrayList<>();
        for (Integer goodsID : form.GoodsID) {
            for (Integer entityID : form.EntityID) {
                TimeCardSaleEntity saleEntity = new TimeCardSaleEntity();
                saleEntity.TimeCardID = goodsID;
                saleEntity.EntityID = entityID;
                list.add(saleEntity);
            }
        }

        if (list.size() > 0)
            mapper.addTimeCardConsume(list);

        return BaseOutput.success();
    }
}
