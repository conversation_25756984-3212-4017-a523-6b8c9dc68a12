package com.zhelian.service.ibeauty.postpone;

import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.autoNumber.AutoNumberUtils;
import com.zhelian.core.utils.DateTimeUtil;
import com.zhelian.mapper.ibeauty.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.*;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.ibeauty.form.output.PostponeBillIDOutputForm;
import com.zhelian.model.ibeauty.form.output.PostponeBillInfoOutputForm;
import com.zhelian.model.miniprogram.form.input.PostponeQueryForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Service
public class PostponeBillService {

    @Autowired
    PostponeBillMapper postponeBillMapper;
    @Autowired
    GeneralCardAccountMapper generalCardAccountMapper;
    @Autowired
    TimeCardAccountMapper timeCardAccountMapper;
    @Autowired
    SavingCardAccountMapper savingCardAccountMapper;
    @Autowired
    PackageCardAccountMapper packageCardAccountMapper;

    @Transactional
    public BaseOutput create(PostponeBillAddForm form, String channel) throws Exception {
        String PostponeBillID;
        if (form.ID != null && !form.ID.equals("")) {
            PostponeBillID = form.ID;
            PostponeBillEntity postponeBillEntity = postponeBillMapper.getPostponeBill(form.ID);
            if (postponeBillEntity == null) {
                return BaseOutput.failed("该延期订单不存在");
            }
            postponeBillEntity.ApprovalStatus = form.ApprovalStatus;
            postponeBillEntity.PostponeRemark = form.Remark;
            postponeBillMapper.updatePostponeBill(postponeBillEntity);
            postponeBillMapper.deletePostponeGeneralCardAccount(form.ID);
            postponeBillMapper.deletePostponeTimeCardAccount(form.ID);
            postponeBillMapper.deletePostponeSavingCardAccount(form.ID);
            postponeBillMapper.deletePostponePackageCardAccount(form.ID);
        } else {
            PostponeBillEntity postponeBillEntity = new PostponeBillEntity();
            PostponeBillID = AutoNumberUtils.getIBeautyPostponeBill();
            postponeBillEntity.ID = PostponeBillID;
            postponeBillEntity.EntityID = SystemUserUtil.getSystemEntityID();
            postponeBillEntity.CustomerID = form.CustomerID;
            postponeBillEntity.ApprovalStatus = form.ApprovalStatus;
            postponeBillEntity.CreatedBy = SystemUserUtil.getSystemUserID();
            postponeBillEntity.CreatedOn = new Date();
            postponeBillEntity.PostponeRemark = form.Remark;
            postponeBillEntity.Channel = channel;
            postponeBillMapper.createPostponeBill(postponeBillEntity);
        }
        PostponeProcessEntity postponeProcessEntity = new PostponeProcessEntity();
        postponeProcessEntity.PostponeBillID = PostponeBillID;
        postponeProcessEntity.ApprovalBy = SystemUserUtil.getSystemUserID();
        postponeProcessEntity.ApprovalOn = new Date();
        postponeProcessEntity.ApprovalRemark = form.Remark;
        postponeProcessEntity.ActionType = form.ApprovalStatus;
        postponeBillMapper.createPostponeProcess(postponeProcessEntity);
        List<PostponeBillGeneralCardAccountEntity> generalCardList = new ArrayList<>();
        List<PostponeBillTimeCardAccountEntity> timeCardList = new ArrayList<>();
        List<PostponeBillSavingCardAccountEntity> savingCardList = new ArrayList<>();
        List<PostponeBillPackageCardAccountEntity> packageCardList = new ArrayList<>();
        if (form.GeneralCard != null && form.GeneralCard.size() > 0) {
            for (PostponeGeneralCardAddForm postponeGeneralCardAddForm : form.GeneralCard) {
                GeneralCardAccountEntity generalCardAccountEntity = generalCardAccountMapper.getGeneralCardAccountByAccountID(postponeGeneralCardAddForm.GeneralCardAccountID);
                PostponeBillGeneralCardAccountEntity entity = new PostponeBillGeneralCardAccountEntity();
                entity.PostponeBillID = PostponeBillID;
                entity.GeneralCardAccountID = postponeGeneralCardAddForm.GeneralCardAccountID;
                entity.GeneralCardID = generalCardAccountEntity.GeneralCardID;
                entity.ValidType = generalCardAccountEntity.ValidType;
                entity.ValidDay = generalCardAccountEntity.ValidDay;
                entity.ValidDate = generalCardAccountEntity.ValidDate;
                entity.IsEverlasting = postponeGeneralCardAddForm.IsEverlasting;
                entity.NewValidDate = postponeGeneralCardAddForm.IsEverlasting == true ? null : DateTimeUtil.toDate(postponeGeneralCardAddForm.NewValidDate);
                generalCardList.add(entity);
            }
        }
        if (form.TimeCard != null && form.TimeCard.size() > 0) {
            for (PostponeTimeCardAddForm postponeTimeCardAddForm : form.TimeCard) {
                TimeCardAccountEntity timeCardAccountEntity = timeCardAccountMapper.getTimeCardAccountByAccountID(postponeTimeCardAddForm.TimeCardAccountID);
                PostponeBillTimeCardAccountEntity entity = new PostponeBillTimeCardAccountEntity();
                entity.PostponeBillID = PostponeBillID;
                entity.TimeCardAccountID = postponeTimeCardAddForm.TimeCardAccountID;
                entity.TimeCardID = timeCardAccountEntity.TimeCardID;
                entity.ValidType = timeCardAccountEntity.ValidType;
                entity.ValidDay = timeCardAccountEntity.ValidDay;
                entity.ValidDate = timeCardAccountEntity.ValidDate;
                entity.IsEverlasting = postponeTimeCardAddForm.IsEverlasting;
                entity.NewValidDate = postponeTimeCardAddForm.IsEverlasting == true ? null : DateTimeUtil.toDate(postponeTimeCardAddForm.NewValidDate);
                timeCardList.add(entity);
            }

        }
        if (form.SavingCard != null && form.SavingCard.size() > 0) {
            for (PostponeSavingCardAddForm postponeSavingCardAddForm : form.SavingCard) {
                SavingCardAccountEntity savingCardAccountEntity = savingCardAccountMapper.getSavingCardAccountByID(postponeSavingCardAddForm.SavingCardAccountID);
                PostponeBillSavingCardAccountEntity entity = new PostponeBillSavingCardAccountEntity();
                entity.PostponeBillID = PostponeBillID;
                entity.SavingCardAccountID = postponeSavingCardAddForm.SavingCardAccountID;
                entity.SavingCardID = savingCardAccountEntity.SavingCardID;
                entity.ValidType = savingCardAccountEntity.ValidType;
                entity.ValidDay = savingCardAccountEntity.ValidDay;
                entity.ValidDate = savingCardAccountEntity.ValidDate;
                entity.IsEverlasting = postponeSavingCardAddForm.IsEverlasting;
                entity.NewValidDate = postponeSavingCardAddForm.IsEverlasting == true ? null : DateTimeUtil.toDate(postponeSavingCardAddForm.NewValidDate);
                savingCardList.add(entity);
            }
        }
        if (form.PackageCard != null && form.PackageCard.size() > 0) {
            for (PostponePackageCardAddForm postponePackageCardAddForm : form.PackageCard) {
                PackageCardAccountEntity packageCardAccountEntity = packageCardAccountMapper.getPackageCardAccount(postponePackageCardAddForm.PackageCardAccountID);
                PostponeBillPackageCardAccountEntity entity = new PostponeBillPackageCardAccountEntity();
                entity.PostponeBillID = PostponeBillID;
                entity.PackageCardAccountID = postponePackageCardAddForm.PackageCardAccountID;
                entity.PackageCardID = packageCardAccountEntity.PackageCardID;
                entity.ValidType = packageCardAccountEntity.ValidType;
                entity.ValidDay = packageCardAccountEntity.ValidDay;
                entity.ValidDate = packageCardAccountEntity.ValidDate;
                entity.IsEverlasting = postponePackageCardAddForm.IsEverlasting;
                entity.NewValidDate = postponePackageCardAddForm.IsEverlasting == true ? null : DateTimeUtil.toDate(postponePackageCardAddForm.NewValidDate);
                packageCardList.add(entity);
            }
        }

        if (generalCardList != null && generalCardList.size() > 0)
            postponeBillMapper.createPostponeBillGeneralCard(generalCardList);
        if (timeCardList != null && timeCardList.size() > 0)
            postponeBillMapper.createPostponeBillTimeCard(timeCardList);
        if (savingCardList != null && savingCardList.size() > 0)
            postponeBillMapper.createPostponeBillSavingCard(savingCardList);
        if (packageCardList != null && packageCardList.size() > 0)
            postponeBillMapper.createPostponeBillPackageCard(packageCardList);

        PostponeBillIDOutputForm postponeBillIDOutputForm = new PostponeBillIDOutputForm();
        postponeBillIDOutputForm.BillID = PostponeBillID;
        return BaseOutputForm.success(postponeBillIDOutputForm);
    }

    public BaseOutput postponeBillList(PostponeBillQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        Integer entityID = SystemUserUtil.getSystemEntityID();
        return BasePageInfo.success(postponeBillMapper.postponeBillList(entityID, form.PostponeBillID, form.Name, form.StartDate, form.EndDate, form.ApprovalStatus, true));
    }

    public BaseOutput postponeBillinfo(PostponeBillInfoQueryForm form) {
        PostponeBillInfoOutputForm postponeBillInfoOutputForm = postponeBillMapper.postponeBillInfo(form);
        postponeBillInfoOutputForm.GeneralCard = postponeBillMapper.postponeBillInfoGeneralCard(form.ID);
        postponeBillInfoOutputForm.TimeCard = postponeBillMapper.postponeBillInfoTimeCard(form.ID);
        postponeBillInfoOutputForm.SavingCard = postponeBillMapper.postponeBillInfoSavingCard(form.ID);
        postponeBillInfoOutputForm.PackageCard = postponeBillMapper.postponeBillInfoPackageCard(form.ID);
        return BaseOutputForm.success(postponeBillInfoOutputForm);
    }

    public BaseOutput postponeBillApprovalList(PostponeBillQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        Integer entityID = SystemUserUtil.getSystemEntityID();
        return BasePageInfo.success(postponeBillMapper.postponeBillList(entityID, form.PostponeBillID, form.Name, form.StartDate, form.EndDate, form.ApprovalStatus, false));
    }

    @Transactional
    public BaseOutput revoke(PostponeBillInfoQueryForm form) {
        postponeBillMapper.updatePostponeBillStatus(form.ID, "50");
        PostponeProcessEntity postponeProcessEntity = new PostponeProcessEntity();
        postponeProcessEntity.PostponeBillID = form.ID;
        postponeProcessEntity.ApprovalBy = SystemUserUtil.getSystemUserID();
        postponeProcessEntity.ApprovalOn = new Date();
        postponeProcessEntity.ActionType = "50";
        postponeBillMapper.createPostponeProcess(postponeProcessEntity);
        return BaseOutput.success();
    }


    @Transactional
    public void approvalBill(PostponeBillUpdateForm form) {
        postponeBillMapper.updatePostponeBillStatus(form.ID, form.ApprovalStatus);
        PostponeProcessEntity postponeProcessEntity = new PostponeProcessEntity();
        postponeProcessEntity.PostponeBillID = form.ID;
        postponeProcessEntity.ApprovalBy = SystemUserUtil.getSystemUserID();
        postponeProcessEntity.ApprovalOn = new Date();
        postponeProcessEntity.ActionType = form.ApprovalStatus;
        postponeProcessEntity.ApprovalRemark = form.ApprovalRemark;
        postponeBillMapper.createPostponeProcess(postponeProcessEntity);

        if (form.ApprovalStatus.equals("30")) {
            List<PostponeBillGeneralCardAccountEntity> generalCardList = postponeBillMapper.getPostponeBillGeneralCardAccount(form.ID);
            List<PostponeBillTimeCardAccountEntity> timeCardList = postponeBillMapper.getPostponeBillTimeCardAccount(form.ID);
            List<PostponeBillSavingCardAccountEntity> savingCardList = postponeBillMapper.getPostponeBillSavingCardAccount(form.ID);
            List<PostponeBillPackageCardAccountEntity> packageCardList = postponeBillMapper.getPostponeBillPackageCardAccount(form.ID);
            if (generalCardList != null && generalCardList.size() > 0) {
                for (PostponeBillGeneralCardAccountEntity entity : generalCardList) {
                    GeneralCardAccountEntity generalCardAccountEntity = new GeneralCardAccountEntity();
                    generalCardAccountEntity.ID = entity.GeneralCardAccountID;
                    generalCardAccountEntity.ValidType = entity.IsEverlasting == true ? 3 : (entity.ValidType == 3 ? 1 : entity.ValidType);
                    generalCardAccountEntity.ValidDate = entity.IsEverlasting == true ? null : entity.NewValidDate;
                    generalCardAccountEntity.ValidDay = entity.ValidDay;
                    generalCardAccountMapper.updateGeneralCardAccountValidDate(generalCardAccountEntity);
                }
            }
            if (timeCardList != null && timeCardList.size() > 0) {
                for (PostponeBillTimeCardAccountEntity entity : timeCardList) {
                    TimeCardAccountEntity timeCardAccountEntity = new TimeCardAccountEntity();
                    timeCardAccountEntity.ID = entity.TimeCardAccountID;
                    timeCardAccountEntity.ValidType = entity.IsEverlasting == true ? 3 : (entity.ValidType == 3 ? 1 : entity.ValidType);
                    timeCardAccountEntity.ValidDate = entity.IsEverlasting == true ? null : entity.NewValidDate;
                    timeCardAccountEntity.ValidDay = entity.ValidDay;
                    timeCardAccountMapper.updateTimeCardAccountValidDate(timeCardAccountEntity);
                }
            }
            if (savingCardList != null && savingCardList.size() > 0) {
                for (PostponeBillSavingCardAccountEntity entity : savingCardList) {
                    SavingCardAccountEntity savingCardAccountEntity = new SavingCardAccountEntity();
                    savingCardAccountEntity.ID = entity.SavingCardAccountID;
                    savingCardAccountEntity.ValidType = entity.IsEverlasting == true ? 3 : (entity.ValidType == 3 ? 1 : entity.ValidType);
                    savingCardAccountEntity.ValidDate = entity.IsEverlasting == true ? null : entity.NewValidDate;
                    savingCardAccountEntity.ValidDay = entity.ValidDay;
                    savingCardAccountMapper.updateSavingCardValidDate(savingCardAccountEntity);
                }
            }
            if (packageCardList != null && packageCardList.size() > 0) {
                for (PostponeBillPackageCardAccountEntity entity : packageCardList) {
                    PackageCardAccountEntity packageCardAccountEntity = new PackageCardAccountEntity();
                    packageCardAccountEntity.ID = entity.PackageCardAccountID;
                    packageCardAccountEntity.ValidType = entity.IsEverlasting == true ? 3 : (entity.ValidType == 3 ? 1 : entity.ValidType);
                    packageCardAccountEntity.ValidDate = entity.IsEverlasting == true ? null : entity.NewValidDate;
                    packageCardAccountEntity.ValidDay = entity.ValidDay;
                    packageCardAccountMapper.updatePackageCardAccountValidDate(packageCardAccountEntity);
                }
            }
        }
    }

    public BaseOutput list(PostponeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        Integer entityID = SystemUserUtil.getSystemEntityID();
        return BasePageInfo.success(postponeBillMapper.miniProgramPostponeBill(entityID, form.Name, form.StartDate, form.EndDate, form.ApprovalStatus, true));
    }

    public BaseOutput miniProgramPostponeBillApprovalList(PostponeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        Integer entityID = SystemUserUtil.getSystemEntityID();
        return BasePageInfo.success(postponeBillMapper.miniProgramPostponeBill(entityID, form.Name, form.StartDate, form.EndDate, form.ApprovalStatus, false));
    }
}