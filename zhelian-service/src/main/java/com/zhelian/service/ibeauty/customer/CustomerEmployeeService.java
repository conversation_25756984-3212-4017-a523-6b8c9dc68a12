package com.zhelian.service.ibeauty.customer;

import com.zhelian.mapper.ibeauty.CustomerEmployeeMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.entity.CustomerEmployeeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class CustomerEmployeeService {

    @Autowired
    private CustomerEmployeeMapper customerEmployeeMapper;

    //批量分配会员给员工
    public BaseOutput addCustomerEmployeeId(CustomerEmployeeEntity customerEmployeeEntity) {
        customerEmployeeMapper.addCustomerEmployeeId(customerEmployeeEntity);
        return BaseOutput.success();
    }

    //员工离职后修改其员工id的值
    public BaseOutput delEmployeeId(String EmployeeID) {
        customerEmployeeMapper.delEmloyeeId(EmployeeID);
        return BaseOutput.success();
    }


}
