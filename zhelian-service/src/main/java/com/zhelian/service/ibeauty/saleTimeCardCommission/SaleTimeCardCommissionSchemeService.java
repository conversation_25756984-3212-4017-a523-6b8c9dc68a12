package com.zhelian.service.ibeauty.saleTimeCardCommission;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.ibeauty.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.SaleTimeCardCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCommissionSchemeDeleteForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCommissionSchemeQueryForm;
import com.zhelian.model.ibeauty.form.output.SaleTimeCardCommissionSchemeOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class SaleTimeCardCommissionSchemeService {

    @Autowired
    SaleTimeCardCommissionSchemeMapper saleTimeCardCommissionSchemeMapper;
    @Autowired
    SaleTimeCardCategoryCommissionMapper saleTimeCardCategoryCommissionMapper;
    @Autowired
    SaleTimeCardCategoryHandlerCommissionMapper saleTimeCardCategoryHandlerCommissionMapper;
    @Autowired
    SaleTimeCardCommissionMapper saleTimeCardCommissionMapper;
    @Autowired
    SaleTimeCardHandlerCommissionMapper saleTimeCardHandlerCommissionMapper;

    @Autowired
    SaleTimeCardSchemeHandlerCommissionMapper saleTimeCardSchemeHandlerCommissionMapper;


    public BaseOutput saleTimeCardCommissionSchemeList(SaleTimeCardCommissionSchemeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleTimeCardCommissionSchemeOutputForm> list = saleTimeCardCommissionSchemeMapper.saleTimeCardCommissionSchemeList(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput createSaleTimeCardCommissionScheme(SaleTimeCardCommissionSchemeAddForm form) {

        if (saleTimeCardCommissionSchemeMapper.findSaleTimeCardCommissionSchemeByEntityID(form.EntityID) != null)
            return BaseOutput.failed("该组织单位已经创建提佣方案");

        SaleTimeCardCommissionSchemeEntity saleTimeCardCommissionSchemeEntity = new SaleTimeCardCommissionSchemeEntity();
        saleTimeCardCommissionSchemeEntity.EntityID = form.EntityID;

        saleTimeCardCommissionSchemeMapper.createSaleTimeCardCommissionScheme(saleTimeCardCommissionSchemeEntity);
        return BaseOutput.success();

    }

    @Transactional
    public BaseOutput deleteSaleTimeCardCommissionScheme(SaleTimeCardCommissionSchemeDeleteForm form) {
        saleTimeCardCommissionSchemeMapper.deleteSaleTimeCardCommissionScheme(form.EntityID);
        saleTimeCardCategoryCommissionMapper.deleteSaleTimeCardCategoryCommission(form.EntityID);
        saleTimeCardCategoryHandlerCommissionMapper.deleteSaleTimeCardCategoryHandlerCommission(form.EntityID);
        saleTimeCardCategoryHandlerCommissionMapper.deleteSaleTimeCardCategoryHandlerJobTypeCommission(form.EntityID);
        saleTimeCardCommissionMapper.deleteSaleTimeCardCommissionByEntityID(form.EntityID);
        saleTimeCardHandlerCommissionMapper.deleteSaleTimeCardHandlerCommission(form.EntityID);
        saleTimeCardHandlerCommissionMapper.deleteSaleTimeCardHandlerJobTypeCommission(form.EntityID);
        saleTimeCardSchemeHandlerCommissionMapper.deleteSaleTimeCardSchemeHandler(form.EntityID);
        saleTimeCardSchemeHandlerCommissionMapper.deleteSaleTimeCardSchemeHandlerJobType(form.EntityID);

        return BaseOutput.success();
    }

}