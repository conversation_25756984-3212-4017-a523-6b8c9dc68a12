package com.zhelian.service.ibeauty.padShow;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.GoodsDisplayCategoryMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.ProjectCategoryMoveForm;
import com.zhelian.model.pad.entity.GoodsDisplayCategoryEntity;
import com.zhelian.model.pad.form.input.GoodsDisplayCategoryAddForm;
import com.zhelian.model.pad.form.input.GoodsDisplayCategoryQueryForm;
import com.zhelian.model.pad.form.output.GoodsDisplayCategoryOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import static com.zhelian.core.utils.ListUtil.listToTree;

@Service
public class GoodsDisplayCategoryService {

    @Autowired
    GoodsDisplayCategoryMapper goodsDisplayCategoryMapper;


    public BaseOutput all(GoodsDisplayCategoryQueryForm form) {
        List<GoodsDisplayCategoryOutputForm> list = goodsDisplayCategoryMapper.goodsDisplayList(form.Name, form.Active);
        JSONArray result = listToTree(JSONArray.parseArray(JSON.toJSONString(list, JSONWriter.Feature.WriteMapNullValue)), "ID", "ParentID", "Child");
        return BaseOutputForm.success(result);
    }

    public BaseOutput valid() {
        List<GoodsDisplayCategoryOutputForm> list = goodsDisplayCategoryMapper.validGoodsDisplayList();
        JSONArray result = listToTree(JSONArray.parseArray(JSON.toJSONString(list, JSONWriter.Feature.WriteMapNullValue)), "ID", "ParentID", "Child");
        return BaseOutputForm.success(result);
    }

    public BaseOutput getMemo(GoodsDisplayCategoryAddForm form) {
        return BaseOutputForm.success(goodsDisplayCategoryMapper.getMemo(form.ID));
    }

    public BaseOutput createGoodsDisplayCategory(GoodsDisplayCategoryAddForm form) {
        Integer Sequence = goodsDisplayCategoryMapper.getMaxSequence();
        GoodsDisplayCategoryEntity goodsDisplayCategoryEntity = new GoodsDisplayCategoryEntity();
        if (form.ParentID != 0) {
            GoodsDisplayCategoryEntity goodsDisplayCategoryEntity1 = goodsDisplayCategoryMapper.getGoodsDisplayCategoryEntity(form.ParentID);
            if (goodsDisplayCategoryEntity1 != null && goodsDisplayCategoryEntity1.ParentID != 0) {
                return BaseOutput.failed("上级分类不属于一级分类");
            }
        }
        goodsDisplayCategoryEntity.ParentID = form.ParentID;
        goodsDisplayCategoryEntity.Name = form.Name;
        goodsDisplayCategoryEntity.Sequence = Sequence != null ? Sequence + 1 : 1;
        goodsDisplayCategoryEntity.ImageURL = form.ImageURL == null || form.ImageURL.equals("") ? null : form.ImageURL;
        goodsDisplayCategoryEntity.Memo = form.Memo;
        goodsDisplayCategoryEntity.Active = form.Active;
        goodsDisplayCategoryMapper.createGoodsDisplayCategory(goodsDisplayCategoryEntity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateGoodsDisplayCategory(GoodsDisplayCategoryAddForm form) {

        if (form.Active == false) {
            if (form.ParentID == 0) {
                Integer childCount = goodsDisplayCategoryMapper.childActiveCount(form.ID);
                if (childCount > 0)
                    return BaseOutput.failed("该分类下的子类为有效，不可以改为无效");
            } else {
                Integer count = goodsDisplayCategoryMapper.goodsDisplayByCategoryCount(form.ID);
                if (count > 0)
                    return BaseOutput.failed("该分类下有项目，不可以改为无效");
            }
        } else {
            if (form.ParentID != 0) {
                GoodsDisplayCategoryEntity goodsDisplayCategoryEntity1 = goodsDisplayCategoryMapper.getGoodsDisplayCategoryEntity(form.ParentID);
                if (goodsDisplayCategoryEntity1.Active == false)
                    return BaseOutput.failed("该分类的父集为无效，不可以改为有效");
            }

        }


        GoodsDisplayCategoryEntity goodsDisplayCategoryEntity = new GoodsDisplayCategoryEntity();
        if (form.ParentID != 0) {
            GoodsDisplayCategoryEntity goodsDisplayCategoryEntity1 = goodsDisplayCategoryMapper.getGoodsDisplayCategoryEntity(form.ParentID);
            if (goodsDisplayCategoryEntity1 != null && goodsDisplayCategoryEntity1.ParentID != 0) {
                return BaseOutput.failed("上级分类不属于一级分类");
            }
        }
        goodsDisplayCategoryEntity.ParentID = form.ParentID;
        goodsDisplayCategoryEntity.Active = form.Active;
        goodsDisplayCategoryEntity.Name = form.Name;
        goodsDisplayCategoryEntity.ID = form.ID;
        goodsDisplayCategoryEntity.ImageURL = form.ImageURL == null || form.ImageURL.equals("") ? null : form.ImageURL;
        goodsDisplayCategoryEntity.Memo = form.Memo;
        goodsDisplayCategoryMapper.updateGoodsDisplayCategory(goodsDisplayCategoryEntity);
        return BaseOutput.success();
    }

    public BaseOutput move(ProjectCategoryMoveForm form) {

        LinkedList<Integer> linkedList = goodsDisplayCategoryMapper.getASCList(form.MoveID, form.DestParentID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<GoodsDisplayCategoryEntity> list = new ArrayList<>();
        for (Integer ID : linkedList) {
            GoodsDisplayCategoryEntity goodsDisplayCategoryEntity = new GoodsDisplayCategoryEntity();
            goodsDisplayCategoryEntity.ID = ID;
            goodsDisplayCategoryEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(goodsDisplayCategoryEntity);
        }

        goodsDisplayCategoryMapper.updateSequence(list);
        return BaseOutput.success();
    }
}
