package com.zhelian.service.ibeauty.refundTreat;


import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.autoNumber.AutoNumberUtils;
import com.zhelian.core.utils.DateTimeUtil;
import com.zhelian.core.utils.enumeration.BillCode;
import com.zhelian.mapper.CRM.ChannelMapper;
import com.zhelian.mapper.CRM.CustomerGrowthMapper;
import com.zhelian.mapper.CRM.CustomerMapper;
import com.zhelian.mapper.ibeauty.*;
import com.zhelian.mapper.psi.InventoryProductInboundMapper;
import com.zhelian.mapper.psi.ProductMapper;
import com.zhelian.mapper.psi.StockMapper;
import com.zhelian.model.CRM.form.output.ChannelEmployeePerformanceRateOutputForm;
import com.zhelian.model.CRM.form.output.ChannelParentOutputForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.*;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.ibeauty.form.output.*;
import com.zhelian.model.ibeauty.service.TreatChannelEmployeePerformanceGoodsEntity;
import com.zhelian.model.ibeauty.service.TreatChannelEmployeePerformancePackageCardGoodsEntity;
import com.zhelian.model.ibeauty.service.TreatCommissionEntity;
import com.zhelian.model.ibeauty.service.TreatPerformanceEntity;
import com.zhelian.model.psi.entity.InventoryProductInboundDetailEntity;
import com.zhelian.model.psi.entity.InventoryProductInboundEntity;
import com.zhelian.model.psi.entity.StockEntity;
import com.zhelian.model.psi.form.output.ProductOutputForm;
import com.zhelian.service.ibeauty.SealingAccount.SealingAccountService;
import com.zhelian.service.ibeauty.treatBill.TreatBillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class RefundTreatBillService {

    @Autowired
    RefundTreatBillMapper refundTreatBillMapper;
    @Autowired
    ProductAccountMapper productAccountMapper;
    @Autowired
    ProjectAccountMapper projectAccountMapper;
    @Autowired
    GeneralCardAccountMapper generalCardAccountMapper;
    @Autowired
    TimeCardAccountMapper timeCardAccountMapper;
    @Autowired
    SavingCardAccountMapper savingCardAccountMapper;
    @Autowired
    PackageCardAccountMapper packageCardAccountMapper;
    @Autowired
    TreatBillMapper treatBillMapper;
    @Autowired
    TreatEntityPerformanceMapper treatEntityPerformanceMapper;
    @Autowired
    TreatEmployeePerformanceCommissionMapper treatEmployeePerformanceCommissionMapper;
    @Autowired
    InventoryProductInboundMapper inventoryProductInboundMapper;
    @Autowired
    StockMapper stockMapper;
    @Autowired
    ProductMapper productMapper;
    @Autowired
    ChannelMapper channelMapper;
    @Autowired
    TreatChannelPerformanceMapper treatChannelPerformanceMapper;
    @Autowired
    CustomerMapper customerMapper;
    @Autowired
    CustomerGrowthMapper customerGrowthMapper;
    @Autowired
    TreatBillService treatBillService;
    @Autowired
    TreatChannelDeveloperPerformanceMapper treatChannelDeveloperPerformanceMapper;
    @Autowired
    TreatChannelConsultantPerformanceMapper treatChannelConsultantPerformanceMapper;
    @Autowired
    SealingAccountService sealingAccountService;

    public BaseOutput treatList(RefundTreatBillQueryForm form) {
        List<RefundTreatBillOutputForm> refundTreatBillList = new ArrayList<>();
        List<RefundTreatBillOutputForm> refundTreatBillOutputForms = refundTreatBillMapper.getTreatBill(form.CustomerID);
        List<RefundTreatBillProductOutputForm> product = refundTreatBillMapper.getTreatBillProduct(form.CustomerID, form.Name);
        List<RefundTreatBillProjectOutputForm> project = refundTreatBillMapper.getTreatBillProject(form.CustomerID, form.Name);
        List<RefundTreatBillGeneralCardOutputForm> generalCard = refundTreatBillMapper.getTreatBillGeneralCard(form.CustomerID, form.Name);
        List<RefundTreatBillTimeCardOutputForm> timeCard = refundTreatBillMapper.getTreatBillTimeCard(form.CustomerID, form.Name);
        List<RefundTreatBillSavingCardOutputForm> savingCard = refundTreatBillMapper.getTreatBillSavingCard(form.CustomerID, form.Name);
        List<RefundTreatBillPackageCardOutputForm> packageCardList = new ArrayList<>();
        List<RefundTreatBillPackageCardOutputForm> packageCard = refundTreatBillMapper.getTreatBillPackageCard(form.CustomerID, form.Name);
        List<RefundTreatBillProductExtendOutputForm> packageCardProduct = refundTreatBillMapper.getTreatBillPackageCardProduct(form.CustomerID);
        List<RefundTreatBillProjectExtendOutputForm> packageCardProject = refundTreatBillMapper.getTreatBillPackageCardProject(form.CustomerID);
        List<RefundTreatBillGeneralCardExtendOutputForm> packageCardGeneralCard = refundTreatBillMapper.getTreatBillPackageCardGeneralCard(form.CustomerID);
        List<RefundTreatBillTimeCardExtendOutputForm> packageCardTimeCard = refundTreatBillMapper.getTreatBillPackageCardTimeCard(form.CustomerID);
        List<RefundTreatBillSavingCardExtendOutputForm> packageCardSavingCard = refundTreatBillMapper.getTreatBillPackageCardSavingCard(form.CustomerID);
        for (RefundTreatBillPackageCardOutputForm packageCardOutputForm : packageCard) {
            packageCardOutputForm.Product = packageCardProduct.stream().filter(refundTreatBillOutputForm -> refundTreatBillOutputForm.TreatBillID.equals(packageCardOutputForm.TreatBillID) && refundTreatBillOutputForm.PackageCardAccountID.equals(packageCardOutputForm.PackageCardAccountID)).collect(Collectors.toList());
            packageCardOutputForm.Project = packageCardProject.stream().filter(refundTreatBillOutputForm -> refundTreatBillOutputForm.TreatBillID.equals(packageCardOutputForm.TreatBillID) && refundTreatBillOutputForm.PackageCardAccountID.equals(packageCardOutputForm.PackageCardAccountID)).collect(Collectors.toList());
            packageCardOutputForm.GeneralCard = packageCardGeneralCard.stream().filter(refundTreatBillOutputForm -> refundTreatBillOutputForm.TreatBillID.equals(packageCardOutputForm.TreatBillID) && refundTreatBillOutputForm.PackageCardAccountID.equals(packageCardOutputForm.PackageCardAccountID)).collect(Collectors.toList());
            packageCardOutputForm.TimeCard = packageCardTimeCard.stream().filter(refundTreatBillOutputForm -> refundTreatBillOutputForm.TreatBillID.equals(packageCardOutputForm.TreatBillID) && refundTreatBillOutputForm.PackageCardAccountID.equals(packageCardOutputForm.PackageCardAccountID)).collect(Collectors.toList());
            packageCardOutputForm.SavingCard = packageCardSavingCard.stream().filter(refundTreatBillOutputForm -> refundTreatBillOutputForm.TreatBillID.equals(packageCardOutputForm.TreatBillID) && refundTreatBillOutputForm.PackageCardAccountID.equals(packageCardOutputForm.PackageCardAccountID)).collect(Collectors.toList());
            if (packageCardOutputForm.Product.size() > 0 || packageCardOutputForm.Project.size() > 0 || packageCardOutputForm.GeneralCard.size() > 0 || packageCardOutputForm.TimeCard.size() > 0 || packageCardOutputForm.SavingCard.size() > 0) {
                packageCardList.add(packageCardOutputForm);
            }

        }
        for (RefundTreatBillOutputForm refundTreatBillOutputForm : refundTreatBillOutputForms) {
            refundTreatBillOutputForm.Product = product.stream().filter(refundTreatBillProductOutputForm -> refundTreatBillProductOutputForm.TreatBillID.equals(refundTreatBillOutputForm.ID)).collect(Collectors.toList());
            refundTreatBillOutputForm.Project = project.stream().filter(refundTreatBillProductOutputForm -> refundTreatBillProductOutputForm.TreatBillID.equals(refundTreatBillOutputForm.ID)).collect(Collectors.toList());
            refundTreatBillOutputForm.GeneralCard = generalCard.stream().filter(refundTreatBillProductOutputForm -> refundTreatBillProductOutputForm.TreatBillID.equals(refundTreatBillOutputForm.ID)).collect(Collectors.toList());
            refundTreatBillOutputForm.TimeCard = timeCard.stream().filter(refundTreatBillProductOutputForm -> refundTreatBillProductOutputForm.TreatBillID.equals(refundTreatBillOutputForm.ID)).collect(Collectors.toList());
            refundTreatBillOutputForm.SavingCard = savingCard.stream().filter(refundTreatBillProductOutputForm -> refundTreatBillProductOutputForm.TreatBillID.equals(refundTreatBillOutputForm.ID)).collect(Collectors.toList());
            refundTreatBillOutputForm.PackageCard = packageCardList.stream().filter(refundTreatBillProductOutputForm -> refundTreatBillProductOutputForm.TreatBillID.equals(refundTreatBillOutputForm.ID)).collect(Collectors.toList());
            if (refundTreatBillOutputForm.Product.size() > 0 || refundTreatBillOutputForm.Project.size() > 0 || refundTreatBillOutputForm.GeneralCard.size() > 0 || refundTreatBillOutputForm.TimeCard.size() > 0 || refundTreatBillOutputForm.SavingCard.size() > 0 || refundTreatBillOutputForm.PackageCard.size() > 0) {
                refundTreatBillList.add(refundTreatBillOutputForm);
            }
        }


        return BaseOutputForm.success(refundTreatBillList);
    }

    @Transactional
    public BaseOutput create(RefundTreatBillAddForm form, String channel) throws Exception {
        Boolean replacementOrderRestriction = sealingAccountService.getReplacementOrderRestriction(form.BillDate);
        if(replacementOrderRestriction){
            return BaseOutput.failed("补单时间处于关账时间内，请选择其他时间");
        }
        RefundTreatBillEntity refundTreatBillEntity = new RefundTreatBillEntity();
        refundTreatBillEntity.ID = AutoNumberUtils.getIBeautyRefundTreatBillID();
        refundTreatBillEntity.EntityID = SystemUserUtil.getSystemEntityID();
        refundTreatBillEntity.BillDate = DateTimeUtil.toDate(form.BillDate);
        refundTreatBillEntity.CustomerID = form.CustomerID;
        refundTreatBillEntity.ApprovalStatus = "20";
        refundTreatBillEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        refundTreatBillEntity.CreatedOn = new Date();
        refundTreatBillEntity.RefundRemark = form.Remark;
        refundTreatBillEntity.Amount = form.Amount;
        refundTreatBillEntity.PayAmount = BigDecimal.ZERO;
        refundTreatBillEntity.CardDeductionAmount = BigDecimal.ZERO;
        refundTreatBillEntity.Channel = channel;
        refundTreatBillMapper.createRefundTreatBill(refundTreatBillEntity);

        RefundTreatBillProcessEntity refundTreatBillProcessEntity = new RefundTreatBillProcessEntity();
        refundTreatBillProcessEntity.RefundTreatBillID = refundTreatBillEntity.ID;
        refundTreatBillProcessEntity.ApprovalBy = SystemUserUtil.getSystemUserID();
        refundTreatBillProcessEntity.ApprovalOn = new Date();
        refundTreatBillProcessEntity.ApprovalRemark = SystemUserUtil.getSystemUserID();
        refundTreatBillProcessEntity.ActionType = "20";
        refundTreatBillMapper.createRefundTreatBillProcess(refundTreatBillProcessEntity);

        List<RefundTreatBillHandlerProductEntity> product = new ArrayList<>();
        List<RefundTreatBillHandlerProjectEntity> project = new ArrayList<>();
        List<RefundTreatBillHandlerGeneralCardEntity> generalCard = new ArrayList<>();
        List<RefundTreatBillHandlerTimeCardEntity> timeCard = new ArrayList<>();
        List<RefundTreatBillHandlerSavingCardEntity> savingCard = new ArrayList<>();
        BigDecimal payTotalAmount = BigDecimal.ZERO;
        BigDecimal cardDeductionTotalAmount = BigDecimal.ZERO;
        if (form.Product != null && form.Product.size() > 0) {
            for (RefundTreatBillProductForm refundTreatBillProductForm : form.Product) {
                TreatBillProductEntity treatBillProductEntity = treatBillMapper.getTreatBillProductEntity(refundTreatBillProductForm.TreatBillProductID);
                if (!treatBillProductEntity.IsLargess) {
                    BigDecimal payAmount = treatBillProductEntity.PayAmount.multiply(new BigDecimal(refundTreatBillProductForm.Quantity)).divide(new BigDecimal(treatBillProductEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                    BigDecimal cardDeductionAmount = refundTreatBillProductForm.TotalAmount.subtract(payAmount);
                    payTotalAmount = payTotalAmount.add(payAmount);
                    cardDeductionTotalAmount = cardDeductionTotalAmount.add(cardDeductionAmount);
                }
                RefundTreatBillProductEntity refundTreatBillProductEntity = new RefundTreatBillProductEntity();
                refundTreatBillProductEntity.RefundTreatBillID = refundTreatBillEntity.ID;
                refundTreatBillProductEntity.ProductAccountID = refundTreatBillProductForm.ProductAccountID;
                refundTreatBillProductEntity.ProductID = refundTreatBillProductForm.ProductID;
                refundTreatBillProductEntity.Quantity = refundTreatBillProductForm.Quantity;
                refundTreatBillProductEntity.Amount = refundTreatBillProductForm.Amount;
                refundTreatBillProductEntity.TotalAmount = refundTreatBillProductForm.TotalAmount;
                refundTreatBillProductEntity.TreatBillID = refundTreatBillProductForm.TreatBillID;
                refundTreatBillProductEntity.TreatBillProductID = refundTreatBillProductForm.TreatBillProductID;
                refundTreatBillMapper.createRefundTreatBillProduct(refundTreatBillProductEntity);

                for (RefundTreatBillHandlerForm refundBillHandlerForm : refundTreatBillProductForm.RefundTreatHandler) {
                    RefundTreatBillHandlerProductEntity refundTreatBillHandlerProductEntity = new RefundTreatBillHandlerProductEntity();
                    refundTreatBillHandlerProductEntity.RefundTreatBillProductID = refundTreatBillProductEntity.ID;
                    refundTreatBillHandlerProductEntity.ProductTreatHandlerID = refundBillHandlerForm.TreatHandlerID;
                    refundTreatBillHandlerProductEntity.EmployeeID = refundBillHandlerForm.EmployeeID;
                    refundTreatBillHandlerProductEntity.Scale = refundBillHandlerForm.Scale.divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP);
                    product.add(refundTreatBillHandlerProductEntity);
                }
            }
        }
        if (form.Project != null && form.Project.size() > 0) {
            for (RefundTreatBillProjectForm refundTreatBillProjectForm : form.Project) {
                TreatBillProjectEntity treatBillProjectEntity = treatBillMapper.getTreatBillProjectEntity(refundTreatBillProjectForm.TreatBillProjectID);
                if (!treatBillProjectEntity.IsLargess) {
                    BigDecimal payAmount = treatBillProjectEntity.PayAmount.multiply(new BigDecimal(refundTreatBillProjectForm.Quantity)).divide(new BigDecimal(treatBillProjectEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                    BigDecimal cardDeductionAmount = refundTreatBillProjectForm.TotalAmount.subtract(payAmount);
                    payTotalAmount = payTotalAmount.add(payAmount);
                    cardDeductionTotalAmount = cardDeductionTotalAmount.add(cardDeductionAmount);
                }
                RefundTreatBillProjectEntity refundTreatBillProjectEntity = new RefundTreatBillProjectEntity();
                refundTreatBillProjectEntity.RefundTreatBillID = refundTreatBillEntity.ID;
                refundTreatBillProjectEntity.ProjectAccountID = refundTreatBillProjectForm.ProjectAccountID;
                refundTreatBillProjectEntity.ProjectID = refundTreatBillProjectForm.ProjectID;
                refundTreatBillProjectEntity.Quantity = refundTreatBillProjectForm.Quantity;
                refundTreatBillProjectEntity.Amount = refundTreatBillProjectForm.Amount;
                refundTreatBillProjectEntity.TotalAmount = refundTreatBillProjectForm.TotalAmount;
                refundTreatBillProjectEntity.TreatBillID = refundTreatBillProjectForm.TreatBillID;
                refundTreatBillProjectEntity.TreatBillProjectID = refundTreatBillProjectForm.TreatBillProjectID;
                refundTreatBillMapper.createRefundTreatBillProject(refundTreatBillProjectEntity);

                for (RefundTreatBillHandlerForm refundBillHandlerForm : refundTreatBillProjectForm.RefundTreatHandler) {
                    RefundTreatBillHandlerProjectEntity refundTreatBillHandlerProjectEntity = new RefundTreatBillHandlerProjectEntity();
                    refundTreatBillHandlerProjectEntity.RefundTreatBillProjectID = refundTreatBillProjectEntity.ID;
                    refundTreatBillHandlerProjectEntity.ProjectTreatHandlerID = refundBillHandlerForm.TreatHandlerID;
                    refundTreatBillHandlerProjectEntity.EmployeeID = refundBillHandlerForm.EmployeeID;
                    refundTreatBillHandlerProjectEntity.Scale = refundBillHandlerForm.Scale.divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP);
                    project.add(refundTreatBillHandlerProjectEntity);
                }
            }
        }
        if (form.GeneralCard != null && form.GeneralCard.size() > 0) {
            for (RefundTreatBillGeneralCardForm refundTreatBillGeneralCardForm : form.GeneralCard) {
                for (RefundTreatBillGeneralCardProjectForm refundTreatBillGeneralCardProjectForm : refundTreatBillGeneralCardForm.Project) {
                    TreatBillGeneralCardEntity treatBillGeneralCardEntity = treatBillMapper.getTreatBillGeneralCardEntity(refundTreatBillGeneralCardProjectForm.TreatBillGeneralCardID);
                    if (!treatBillGeneralCardEntity.IsLargess) {
                        BigDecimal payAmount = treatBillGeneralCardEntity.PayAmount.multiply(new BigDecimal(refundTreatBillGeneralCardProjectForm.Quantity)).divide(new BigDecimal(treatBillGeneralCardEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                        BigDecimal cardDeductionAmount = refundTreatBillGeneralCardProjectForm.TotalAmount.subtract(payAmount);
                        payTotalAmount = payTotalAmount.add(payAmount);
                        cardDeductionTotalAmount = cardDeductionTotalAmount.add(cardDeductionAmount);
                    }
                    RefundTreatBillGeneralCardEntity refundTreatBillGeneralCardEntity = new RefundTreatBillGeneralCardEntity();
                    refundTreatBillGeneralCardEntity.RefundTreatBillID = refundTreatBillEntity.ID;
                    refundTreatBillGeneralCardEntity.GeneralCardAccountID = refundTreatBillGeneralCardForm.GeneralCardAccountID;
                    refundTreatBillGeneralCardEntity.GeneralCardID = refundTreatBillGeneralCardForm.GeneralCardID;
                    refundTreatBillGeneralCardEntity.ProjectID = refundTreatBillGeneralCardProjectForm.ProjectID;
                    refundTreatBillGeneralCardEntity.CardTreatTimes = refundTreatBillGeneralCardProjectForm.CardTreatTimes;
                    refundTreatBillGeneralCardEntity.Quantity = refundTreatBillGeneralCardProjectForm.Quantity;
                    refundTreatBillGeneralCardEntity.Amount = refundTreatBillGeneralCardProjectForm.Amount;
                    refundTreatBillGeneralCardEntity.TotalAmount = refundTreatBillGeneralCardProjectForm.TotalAmount;
                    refundTreatBillGeneralCardEntity.TreatBillID = refundTreatBillGeneralCardForm.TreatBillID;
                    refundTreatBillGeneralCardEntity.TreatBillGeneralCardID = refundTreatBillGeneralCardProjectForm.TreatBillGeneralCardID;
                    refundTreatBillMapper.createRefundTreatBillGeneralCard(refundTreatBillGeneralCardEntity);

                    for (RefundTreatBillHandlerForm refundBillHandlerForm : refundTreatBillGeneralCardProjectForm.RefundTreatHandler) {
                        RefundTreatBillHandlerGeneralCardEntity refundTreatBillHandlerGeneralCardEntity = new RefundTreatBillHandlerGeneralCardEntity();
                        refundTreatBillHandlerGeneralCardEntity.RefundTreatBillGeneralCardID = refundTreatBillGeneralCardEntity.ID;
                        refundTreatBillHandlerGeneralCardEntity.GeneralCardTreatHandlerID = refundBillHandlerForm.TreatHandlerID;
                        refundTreatBillHandlerGeneralCardEntity.EmployeeID = refundBillHandlerForm.EmployeeID;
                        refundTreatBillHandlerGeneralCardEntity.Scale = refundBillHandlerForm.Scale.divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP);
                        generalCard.add(refundTreatBillHandlerGeneralCardEntity);
                    }
                }
            }
        }
        if (form.TimeCard != null && form.TimeCard.size() > 0) {
            for (RefundTreatBillTimeCardForm refundTreatBillTimeCardForm : form.TimeCard) {
                for (RefundTreatBillTimeCardProjectForm refundTreatBillTimeCardProjectForm : refundTreatBillTimeCardForm.Project) {
                    TreatBillTimeCardEntity treatBillTimeCardEntity = treatBillMapper.getTreatBillTimeCardEntity(refundTreatBillTimeCardProjectForm.TreatBillTimeCardID);
                    if (!treatBillTimeCardEntity.IsLargess) {
                        BigDecimal payAmount = treatBillTimeCardEntity.PayAmount.multiply(new BigDecimal(refundTreatBillTimeCardProjectForm.Quantity)).divide(new BigDecimal(treatBillTimeCardEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                        BigDecimal cardDeductionAmount = refundTreatBillTimeCardProjectForm.TotalAmount.subtract(payAmount);
                        payTotalAmount = payTotalAmount.add(payAmount);
                        cardDeductionTotalAmount = cardDeductionTotalAmount.add(cardDeductionAmount);
                    }
                    RefundTreatBillTimeCardEntity refundTreatBillTimeCardEntity = new RefundTreatBillTimeCardEntity();
                    refundTreatBillTimeCardEntity.RefundTreatBillID = refundTreatBillEntity.ID;
                    refundTreatBillTimeCardEntity.TimeCardAccountID = refundTreatBillTimeCardForm.TimeCardAccountID;
                    refundTreatBillTimeCardEntity.TimeCardID = refundTreatBillTimeCardForm.TimeCardID;
                    refundTreatBillTimeCardEntity.ProjectID = refundTreatBillTimeCardProjectForm.ProjectID;
                    refundTreatBillTimeCardEntity.Quantity = refundTreatBillTimeCardProjectForm.Quantity;
                    refundTreatBillTimeCardEntity.Amount = refundTreatBillTimeCardProjectForm.Amount;
                    refundTreatBillTimeCardEntity.TotalAmount = refundTreatBillTimeCardProjectForm.TotalAmount;
                    refundTreatBillTimeCardEntity.TreatBillID = refundTreatBillTimeCardForm.TreatBillID;
                    refundTreatBillTimeCardEntity.TreatBillTimeCardID = refundTreatBillTimeCardProjectForm.TreatBillTimeCardID;
                    refundTreatBillMapper.createRefundTreatBillTimeCard(refundTreatBillTimeCardEntity);

                    for (RefundTreatBillHandlerForm refundBillHandlerForm : refundTreatBillTimeCardProjectForm.RefundTreatHandler) {
                        RefundTreatBillHandlerTimeCardEntity refundTreatBillHandlerTimeCardEntity = new RefundTreatBillHandlerTimeCardEntity();
                        refundTreatBillHandlerTimeCardEntity.RefundTreatBillTimeCardID = refundTreatBillTimeCardEntity.ID;
                        refundTreatBillHandlerTimeCardEntity.TimeCardTreatHandlerID = refundBillHandlerForm.TreatHandlerID;
                        refundTreatBillHandlerTimeCardEntity.EmployeeID = refundBillHandlerForm.EmployeeID;
                        refundTreatBillHandlerTimeCardEntity.Scale = refundBillHandlerForm.Scale.divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP);
                        timeCard.add(refundTreatBillHandlerTimeCardEntity);
                    }
                }
            }
        }
        if (form.SavingCard != null && form.SavingCard.size() > 0) {
            for (RefundTreatBillSavingCardForm refundTreatBillSavingCardForm : form.SavingCard) {
                for (RefundTreatBillSavingCardProjectForm refundTreatBillSavingCardProjectForm : refundTreatBillSavingCardForm.Project) {
                    TreatBillSavingCardEntity treatBillSavingCardEntity = treatBillMapper.getTreatBillSavingCardEntity(refundTreatBillSavingCardProjectForm.TreatBillSavingCardID);
                    cardDeductionTotalAmount = cardDeductionTotalAmount.add(refundTreatBillSavingCardProjectForm.TotalAmount);

                    RefundTreatBillSavingCardEntity refundTreatBillSavingCardEntity = new RefundTreatBillSavingCardEntity();
                    refundTreatBillSavingCardEntity.RefundTreatBillID = refundTreatBillEntity.ID;
                    refundTreatBillSavingCardEntity.SavingCardAccountID = refundTreatBillSavingCardForm.SavingCardAccountID;
                    refundTreatBillSavingCardEntity.SavingCardID = refundTreatBillSavingCardForm.SavingCardID;
                    refundTreatBillSavingCardEntity.ProjectID = refundTreatBillSavingCardProjectForm.ProjectID;
                    refundTreatBillSavingCardEntity.Quantity = refundTreatBillSavingCardProjectForm.Quantity;
                    refundTreatBillSavingCardEntity.TotalAmount = refundTreatBillSavingCardProjectForm.TotalAmount;
                    refundTreatBillSavingCardEntity.Amount = treatBillSavingCardEntity.CardTreatAmount.multiply(new BigDecimal(refundTreatBillSavingCardProjectForm.Quantity)).divide(new BigDecimal(treatBillSavingCardEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                    refundTreatBillSavingCardEntity.LargessAmount = refundTreatBillSavingCardProjectForm.TotalAmount.subtract(refundTreatBillSavingCardEntity.Amount);
                    refundTreatBillSavingCardEntity.TreatBillID = refundTreatBillSavingCardForm.TreatBillID;
                    refundTreatBillSavingCardEntity.TreatBillSavingCardID = refundTreatBillSavingCardProjectForm.TreatBillSavingCardID;
                    refundTreatBillMapper.createRefundTreatBillSavingCard(refundTreatBillSavingCardEntity);

                    for (RefundTreatBillHandlerForm refundBillHandlerForm : refundTreatBillSavingCardProjectForm.RefundTreatHandler) {
                        RefundTreatBillHandlerSavingCardEntity refundTreatBillHandlerSavingCardEntity = new RefundTreatBillHandlerSavingCardEntity();
                        refundTreatBillHandlerSavingCardEntity.RefundTreatBillSavingCardID = refundTreatBillSavingCardEntity.ID;
                        refundTreatBillHandlerSavingCardEntity.SavingCardTreatHandlerID = refundBillHandlerForm.TreatHandlerID;
                        refundTreatBillHandlerSavingCardEntity.EmployeeID = refundBillHandlerForm.EmployeeID;
                        refundTreatBillHandlerSavingCardEntity.Scale = refundBillHandlerForm.Scale.divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP);
                        savingCard.add(refundTreatBillHandlerSavingCardEntity);
                    }
                }

            }
        }
        if (form.PackageCard != null && form.PackageCard.size() > 0) {
            for (RefundTreatBillPackageCardForm refundTreatBillPackageCardForm : form.PackageCard) {
                if (refundTreatBillPackageCardForm.Product != null && refundTreatBillPackageCardForm.Product.size() > 0) {
                    for (RefundTreatBillProductForm refundTreatBillProductForm : refundTreatBillPackageCardForm.Product) {
                        TreatBillProductEntity treatBillProductEntity = treatBillMapper.getTreatBillProductEntity(refundTreatBillProductForm.TreatBillProductID);
                        if (!treatBillProductEntity.IsLargess) {
                            BigDecimal payAmount = treatBillProductEntity.PayAmount.multiply(new BigDecimal(refundTreatBillProductForm.Quantity)).divide(new BigDecimal(treatBillProductEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            BigDecimal cardDeductionAmount = refundTreatBillProductForm.TotalAmount.subtract(payAmount);
                            payTotalAmount = payTotalAmount.add(payAmount);
                            cardDeductionTotalAmount = cardDeductionTotalAmount.add(cardDeductionAmount);
                        }
                        RefundTreatBillProductEntity refundTreatBillProductEntity = new RefundTreatBillProductEntity();
                        refundTreatBillProductEntity.RefundTreatBillID = refundTreatBillEntity.ID;
                        refundTreatBillProductEntity.ProductAccountID = refundTreatBillProductForm.ProductAccountID;
                        refundTreatBillProductEntity.ProductID = refundTreatBillProductForm.ProductID;
                        refundTreatBillProductEntity.Quantity = refundTreatBillProductForm.Quantity;
                        refundTreatBillProductEntity.Amount = refundTreatBillProductForm.Amount;
                        refundTreatBillProductEntity.TotalAmount = refundTreatBillProductForm.TotalAmount;
                        refundTreatBillProductEntity.TreatBillID = refundTreatBillPackageCardForm.TreatBillID;
                        refundTreatBillProductEntity.TreatBillProductID = refundTreatBillProductForm.TreatBillProductID;
                        refundTreatBillProductEntity.PackageCardAccountID = refundTreatBillPackageCardForm.PackageCardAccountID;
                        refundTreatBillProductEntity.PackageCardID = refundTreatBillPackageCardForm.PackageCardID;
                        refundTreatBillMapper.createRefundTreatBillProduct(refundTreatBillProductEntity);

                        for (RefundTreatBillHandlerForm refundBillHandlerForm : refundTreatBillProductForm.RefundTreatHandler) {
                            RefundTreatBillHandlerProductEntity refundTreatBillHandlerProductEntity = new RefundTreatBillHandlerProductEntity();
                            refundTreatBillHandlerProductEntity.RefundTreatBillProductID = refundTreatBillProductEntity.ID;
                            refundTreatBillHandlerProductEntity.ProductTreatHandlerID = refundBillHandlerForm.TreatHandlerID;
                            refundTreatBillHandlerProductEntity.EmployeeID = refundBillHandlerForm.EmployeeID;
                            refundTreatBillHandlerProductEntity.Scale = refundBillHandlerForm.Scale.divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP);
                            product.add(refundTreatBillHandlerProductEntity);
                        }
                    }
                }
                if (refundTreatBillPackageCardForm.Project != null && refundTreatBillPackageCardForm.Project.size() > 0) {
                    for (RefundTreatBillProjectForm refundTreatBillProjectForm : refundTreatBillPackageCardForm.Project) {
                        TreatBillProjectEntity treatBillProjectEntity = treatBillMapper.getTreatBillProjectEntity(refundTreatBillProjectForm.TreatBillProjectID);
                        if (!treatBillProjectEntity.IsLargess) {
                            BigDecimal payAmount = treatBillProjectEntity.PayAmount.multiply(new BigDecimal(refundTreatBillProjectForm.Quantity)).divide(new BigDecimal(treatBillProjectEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            BigDecimal cardDeductionAmount = refundTreatBillProjectForm.TotalAmount.subtract(payAmount);
                            payTotalAmount = payTotalAmount.add(payAmount);
                            cardDeductionTotalAmount = cardDeductionTotalAmount.add(cardDeductionAmount);
                        }
                        RefundTreatBillProjectEntity refundTreatBillProjectEntity = new RefundTreatBillProjectEntity();
                        refundTreatBillProjectEntity.RefundTreatBillID = refundTreatBillEntity.ID;
                        refundTreatBillProjectEntity.ProjectAccountID = refundTreatBillProjectForm.ProjectAccountID;
                        refundTreatBillProjectEntity.ProjectID = refundTreatBillProjectForm.ProjectID;
                        refundTreatBillProjectEntity.Quantity = refundTreatBillProjectForm.Quantity;
                        refundTreatBillProjectEntity.Amount = refundTreatBillProjectForm.Amount;
                        refundTreatBillProjectEntity.TotalAmount = refundTreatBillProjectForm.TotalAmount;
                        refundTreatBillProjectEntity.TreatBillID = refundTreatBillPackageCardForm.TreatBillID;
                        refundTreatBillProjectEntity.TreatBillProjectID = refundTreatBillProjectForm.TreatBillProjectID;
                        refundTreatBillProjectEntity.PackageCardAccountID = refundTreatBillPackageCardForm.PackageCardAccountID;
                        refundTreatBillProjectEntity.PackageCardID = refundTreatBillPackageCardForm.PackageCardID;
                        refundTreatBillMapper.createRefundTreatBillProject(refundTreatBillProjectEntity);

                        for (RefundTreatBillHandlerForm refundBillHandlerForm : refundTreatBillProjectForm.RefundTreatHandler) {
                            RefundTreatBillHandlerProjectEntity refundTreatBillHandlerProjectEntity = new RefundTreatBillHandlerProjectEntity();
                            refundTreatBillHandlerProjectEntity.RefundTreatBillProjectID = refundTreatBillProjectEntity.ID;
                            refundTreatBillHandlerProjectEntity.ProjectTreatHandlerID = refundBillHandlerForm.TreatHandlerID;
                            refundTreatBillHandlerProjectEntity.EmployeeID = refundBillHandlerForm.EmployeeID;
                            refundTreatBillHandlerProjectEntity.Scale = refundBillHandlerForm.Scale.divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP);
                            project.add(refundTreatBillHandlerProjectEntity);
                        }
                    }
                }
                if (refundTreatBillPackageCardForm.GeneralCard != null && refundTreatBillPackageCardForm.GeneralCard.size() > 0) {
                    for (RefundTreatBillGeneralCardForm refundTreatBillGeneralCardForm : refundTreatBillPackageCardForm.GeneralCard) {
                        for (RefundTreatBillGeneralCardProjectForm refundTreatBillGeneralCardProjectForm : refundTreatBillGeneralCardForm.Project) {
                            TreatBillGeneralCardEntity treatBillGeneralCardEntity = treatBillMapper.getTreatBillGeneralCardEntity(refundTreatBillGeneralCardProjectForm.TreatBillGeneralCardID);
                            if (!treatBillGeneralCardEntity.IsLargess) {
                                BigDecimal payAmount = treatBillGeneralCardEntity.PayAmount.multiply(new BigDecimal(refundTreatBillGeneralCardProjectForm.Quantity)).divide(new BigDecimal(treatBillGeneralCardEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                                BigDecimal cardDeductionAmount = refundTreatBillGeneralCardProjectForm.TotalAmount.subtract(payAmount);
                                payTotalAmount = payTotalAmount.add(payAmount);
                                cardDeductionTotalAmount = cardDeductionTotalAmount.add(cardDeductionAmount);
                            }
                            RefundTreatBillGeneralCardEntity refundTreatBillGeneralCardEntity = new RefundTreatBillGeneralCardEntity();
                            refundTreatBillGeneralCardEntity.RefundTreatBillID = refundTreatBillEntity.ID;
                            refundTreatBillGeneralCardEntity.GeneralCardAccountID = refundTreatBillGeneralCardForm.GeneralCardAccountID;
                            refundTreatBillGeneralCardEntity.GeneralCardID = refundTreatBillGeneralCardForm.GeneralCardID;
                            refundTreatBillGeneralCardEntity.ProjectID = refundTreatBillGeneralCardProjectForm.ProjectID;
                            refundTreatBillGeneralCardEntity.Quantity = refundTreatBillGeneralCardProjectForm.Quantity;
                            refundTreatBillGeneralCardEntity.CardTreatTimes = refundTreatBillGeneralCardProjectForm.CardTreatTimes;
                            refundTreatBillGeneralCardEntity.Amount = refundTreatBillGeneralCardProjectForm.Amount;
                            refundTreatBillGeneralCardEntity.TotalAmount = refundTreatBillGeneralCardProjectForm.TotalAmount;
                            refundTreatBillGeneralCardEntity.TreatBillID = refundTreatBillPackageCardForm.TreatBillID;
                            refundTreatBillGeneralCardEntity.TreatBillGeneralCardID = refundTreatBillGeneralCardProjectForm.TreatBillGeneralCardID;
                            refundTreatBillGeneralCardEntity.PackageCardAccountID = refundTreatBillPackageCardForm.PackageCardAccountID;
                            refundTreatBillGeneralCardEntity.PackageCardID = refundTreatBillPackageCardForm.PackageCardID;
                            refundTreatBillMapper.createRefundTreatBillGeneralCard(refundTreatBillGeneralCardEntity);

                            for (RefundTreatBillHandlerForm refundBillHandlerForm : refundTreatBillGeneralCardProjectForm.RefundTreatHandler) {
                                RefundTreatBillHandlerGeneralCardEntity refundTreatBillHandlerGeneralCardEntity = new RefundTreatBillHandlerGeneralCardEntity();
                                refundTreatBillHandlerGeneralCardEntity.RefundTreatBillGeneralCardID = refundTreatBillGeneralCardEntity.ID;
                                refundTreatBillHandlerGeneralCardEntity.GeneralCardTreatHandlerID = refundBillHandlerForm.TreatHandlerID;
                                refundTreatBillHandlerGeneralCardEntity.EmployeeID = refundBillHandlerForm.EmployeeID;
                                refundTreatBillHandlerGeneralCardEntity.Scale = refundBillHandlerForm.Scale.divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP);
                                generalCard.add(refundTreatBillHandlerGeneralCardEntity);
                            }
                        }
                    }
                }
                if (refundTreatBillPackageCardForm.TimeCard != null && refundTreatBillPackageCardForm.TimeCard.size() > 0) {
                    for (RefundTreatBillTimeCardForm refundTreatBillTimeCardForm : refundTreatBillPackageCardForm.TimeCard) {
                        for (RefundTreatBillTimeCardProjectForm refundTreatBillTimeCardProjectForm : refundTreatBillTimeCardForm.Project) {
                            TreatBillTimeCardEntity treatBillTimeCardEntity = treatBillMapper.getTreatBillTimeCardEntity(refundTreatBillTimeCardProjectForm.TreatBillTimeCardID);
                            if (!treatBillTimeCardEntity.IsLargess) {
                                BigDecimal payAmount = treatBillTimeCardEntity.PayAmount.multiply(new BigDecimal(refundTreatBillTimeCardProjectForm.Quantity)).divide(new BigDecimal(treatBillTimeCardEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                                BigDecimal cardDeductionAmount = refundTreatBillTimeCardProjectForm.TotalAmount.subtract(payAmount);
                                payTotalAmount = payTotalAmount.add(payAmount);
                                cardDeductionTotalAmount = cardDeductionTotalAmount.add(cardDeductionAmount);
                            }
                            RefundTreatBillTimeCardEntity refundTreatBillTimeCardEntity = new RefundTreatBillTimeCardEntity();
                            refundTreatBillTimeCardEntity.RefundTreatBillID = refundTreatBillEntity.ID;
                            refundTreatBillTimeCardEntity.TimeCardAccountID = refundTreatBillTimeCardForm.TimeCardAccountID;
                            refundTreatBillTimeCardEntity.TimeCardID = refundTreatBillTimeCardForm.TimeCardID;
                            refundTreatBillTimeCardEntity.ProjectID = refundTreatBillTimeCardProjectForm.ProjectID;
                            refundTreatBillTimeCardEntity.Quantity = refundTreatBillTimeCardProjectForm.Quantity;
                            refundTreatBillTimeCardEntity.Amount = refundTreatBillTimeCardProjectForm.Amount;
                            refundTreatBillTimeCardEntity.TotalAmount = refundTreatBillTimeCardProjectForm.TotalAmount;
                            refundTreatBillTimeCardEntity.TreatBillID = refundTreatBillPackageCardForm.TreatBillID;
                            refundTreatBillTimeCardEntity.TreatBillTimeCardID = refundTreatBillTimeCardProjectForm.TreatBillTimeCardID;
                            refundTreatBillTimeCardEntity.PackageCardAccountID = refundTreatBillPackageCardForm.PackageCardAccountID;
                            refundTreatBillTimeCardEntity.PackageCardID = refundTreatBillPackageCardForm.PackageCardID;
                            refundTreatBillMapper.createRefundTreatBillTimeCard(refundTreatBillTimeCardEntity);

                            for (RefundTreatBillHandlerForm refundBillHandlerForm : refundTreatBillTimeCardProjectForm.RefundTreatHandler) {
                                RefundTreatBillHandlerTimeCardEntity refundTreatBillHandlerTimeCardEntity = new RefundTreatBillHandlerTimeCardEntity();
                                refundTreatBillHandlerTimeCardEntity.RefundTreatBillTimeCardID = refundTreatBillTimeCardEntity.ID;
                                refundTreatBillHandlerTimeCardEntity.TimeCardTreatHandlerID = refundBillHandlerForm.TreatHandlerID;
                                refundTreatBillHandlerTimeCardEntity.EmployeeID = refundBillHandlerForm.EmployeeID;
                                refundTreatBillHandlerTimeCardEntity.Scale = refundBillHandlerForm.Scale.divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP);
                                timeCard.add(refundTreatBillHandlerTimeCardEntity);
                            }
                        }
                    }
                }
                if (refundTreatBillPackageCardForm.SavingCard != null && refundTreatBillPackageCardForm.SavingCard.size() > 0) {
                    for (RefundTreatBillSavingCardForm refundTreatBillSavingCardForm : refundTreatBillPackageCardForm.SavingCard) {
                        for (RefundTreatBillSavingCardProjectForm refundTreatBillSavingCardProjectForm : refundTreatBillSavingCardForm.Project) {
                            TreatBillSavingCardEntity treatBillSavingCardEntity = treatBillMapper.getTreatBillSavingCardEntity(refundTreatBillSavingCardProjectForm.TreatBillSavingCardID);
                            cardDeductionTotalAmount = cardDeductionTotalAmount.add(refundTreatBillSavingCardProjectForm.TotalAmount);

                            RefundTreatBillSavingCardEntity refundTreatBillSavingCardEntity = new RefundTreatBillSavingCardEntity();
                            refundTreatBillSavingCardEntity.RefundTreatBillID = refundTreatBillEntity.ID;
                            refundTreatBillSavingCardEntity.SavingCardAccountID = refundTreatBillSavingCardForm.SavingCardAccountID;
                            refundTreatBillSavingCardEntity.SavingCardID = refundTreatBillSavingCardForm.SavingCardID;
                            refundTreatBillSavingCardEntity.ProjectID = refundTreatBillSavingCardProjectForm.ProjectID;
                            refundTreatBillSavingCardEntity.Quantity = refundTreatBillSavingCardProjectForm.Quantity;
                            refundTreatBillSavingCardEntity.TotalAmount = refundTreatBillSavingCardProjectForm.TotalAmount;
                            refundTreatBillSavingCardEntity.Amount = treatBillSavingCardEntity.CardTreatAmount.multiply(new BigDecimal(refundTreatBillSavingCardProjectForm.Quantity)).divide(new BigDecimal(treatBillSavingCardEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            refundTreatBillSavingCardEntity.LargessAmount = refundTreatBillSavingCardProjectForm.TotalAmount.subtract(refundTreatBillSavingCardEntity.Amount);
                            refundTreatBillSavingCardEntity.TreatBillID = refundTreatBillPackageCardForm.TreatBillID;
                            refundTreatBillSavingCardEntity.TreatBillSavingCardID = refundTreatBillSavingCardProjectForm.TreatBillSavingCardID;
                            refundTreatBillSavingCardEntity.PackageCardAccountID = refundTreatBillPackageCardForm.PackageCardAccountID;
                            refundTreatBillSavingCardEntity.PackageCardID = refundTreatBillPackageCardForm.PackageCardID;
                            refundTreatBillMapper.createRefundTreatBillSavingCard(refundTreatBillSavingCardEntity);

                            for (RefundTreatBillHandlerForm refundBillHandlerForm : refundTreatBillSavingCardProjectForm.RefundTreatHandler) {
                                RefundTreatBillHandlerSavingCardEntity refundTreatBillHandlerSavingCardEntity = new RefundTreatBillHandlerSavingCardEntity();
                                refundTreatBillHandlerSavingCardEntity.RefundTreatBillSavingCardID = refundTreatBillSavingCardEntity.ID;
                                refundTreatBillHandlerSavingCardEntity.SavingCardTreatHandlerID = refundBillHandlerForm.TreatHandlerID;
                                refundTreatBillHandlerSavingCardEntity.EmployeeID = refundBillHandlerForm.EmployeeID;
                                refundTreatBillHandlerSavingCardEntity.Scale = refundBillHandlerForm.Scale.divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP);
                                savingCard.add(refundTreatBillHandlerSavingCardEntity);
                            }
                        }
                    }
                }
            }
        }

        if (product != null && product.size() > 0)
            refundTreatBillMapper.createRefundTreatBillHandlerProduct(product);
        if (project != null && project.size() > 0)
            refundTreatBillMapper.createRefundTreatBillHandlerProject(project);
        if (generalCard != null && generalCard.size() > 0)
            refundTreatBillMapper.createRefundTreatBillHandlerGeneralCard(generalCard);
        if (timeCard != null && timeCard.size() > 0)
            refundTreatBillMapper.createRefundTreatBillHandlerTimeCard(timeCard);
        if (savingCard != null && savingCard.size() > 0)
            refundTreatBillMapper.createRefundTreatBillHandlerSavingCard(savingCard);


        refundTreatBillEntity.PayAmount = payTotalAmount;
        refundTreatBillEntity.CardDeductionAmount = cardDeductionTotalAmount;
        refundTreatBillMapper.updateRefundTreatBill(refundTreatBillEntity);

        PostponeBillIDOutputForm billIDOutputForm = new PostponeBillIDOutputForm();
        billIDOutputForm.BillID = refundTreatBillEntity.ID;
        return BaseOutputForm.success(billIDOutputForm);
    }

    public BaseOutput refundTreatBillList(RefundTreatBillListQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        Integer entityID = SystemUserUtil.getSystemEntityID();
        return BasePageInfo.success(refundTreatBillMapper.refundTreatBillList(entityID, form.ID, form.Name, form.StartDate, form.EndDate, form.ApprovalStatus, true));
    }

    public BaseOutput refundTreatBillInfo(RefundTreatBillInfoQueryForm form) {
        RefundTreatBillInfoOutputForm refundBillInfoOutputForm = refundTreatBillMapper.refundTreatBillInfo(form);
        refundBillInfoOutputForm.Product = refundTreatBillMapper.refundTreatBillInfoProduct(form.ID);
        refundBillInfoOutputForm.Project = refundTreatBillMapper.refundTreatBillInfoProject(form.ID);
        refundBillInfoOutputForm.GeneralCard = refundTreatBillMapper.refundTreatBillInfoGeneralCard(form.ID);
        refundBillInfoOutputForm.TimeCard = refundTreatBillMapper.refundTreatBillInfoTimeCard(form.ID);
        refundBillInfoOutputForm.SavingCard = refundTreatBillMapper.refundTreatBillInfoSavingCard(form.ID);
        List<RefundTreatBillInfoPackageCardOutputForm> packageCard = refundTreatBillMapper.refundTreatBillInfoPackageCard(form.ID);
        List<RefundTreatBillInfoProductExtendOutputForm> product = refundTreatBillMapper.refundTreatBillPackageCardProductInfo(form.ID);
        List<RefundTreatBillInfoProjectExtendOutputForm> project = refundTreatBillMapper.refundTreatBillPackageCardProjectInfo(form.ID);
        List<RefundTreatBillInfoGeneralCardExtendOutputForm> generalCard = refundTreatBillMapper.refundTreatBillPackageCardGeneralCardInfo(form.ID);
        List<RefundTreatBillInfoTimeCardExtendOutputForm> timeCard = refundTreatBillMapper.refundTreatBillPackageCardTimeCardInfo(form.ID);
        List<RefundTreatBillInfoSavingCardExtendOutputForm> savingCard = refundTreatBillMapper.refundTreatBillPackageCardSavingCardInfo(form.ID);
        for (RefundTreatBillInfoPackageCardOutputForm packageCardForm : packageCard) {
            packageCardForm.Product = product.stream().filter(packageCardProductListOutputForm -> packageCardProductListOutputForm.PackageCardAccountID.intValue() == packageCardForm.ID.intValue()).collect(Collectors.toList());
            packageCardForm.Project = project.stream().filter(packageCardProjectListOutputForm -> packageCardProjectListOutputForm.PackageCardAccountID.intValue() == packageCardForm.ID.intValue()).collect(Collectors.toList());
            packageCardForm.GeneralCard = generalCard.stream().filter(packageCardGeneralCardListOutputForm -> packageCardGeneralCardListOutputForm.PackageCardAccountID.intValue() == packageCardForm.ID.intValue()).collect(Collectors.toList());
            packageCardForm.TimeCard = timeCard.stream().filter(packageCardTimeCardListOutputForm -> packageCardTimeCardListOutputForm.PackageCardAccountID.intValue() == packageCardForm.ID.intValue()).collect(Collectors.toList());
            packageCardForm.SavingCard = savingCard.stream().filter(packageCardSavingCardListOutputForm -> packageCardSavingCardListOutputForm.PackageCardAccountID.intValue() == packageCardForm.ID.intValue()).collect(Collectors.toList());
        }
        refundBillInfoOutputForm.PackageCard = packageCard;
        return BaseOutputForm.success(refundBillInfoOutputForm);
    }

    public BaseOutput approvalList(RefundTreatBillListQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(refundTreatBillMapper.refundApprovalTreatBillList(SystemUserUtil.getSystemUserID(), form.EntityID, form.ID, form.Name, form.StartDate, form.EndDate, form.ApprovalStatus, false));
    }

    @Transactional
    public BaseOutput revoke(RefundTreatBillInfoQueryForm form) {
        refundTreatBillMapper.updateRefundTreatBillStatus(form.ID, "50");
        RefundTreatBillProcessEntity refundTreatBillProcessEntity = new RefundTreatBillProcessEntity();
        refundTreatBillProcessEntity.RefundTreatBillID = form.ID;
        refundTreatBillProcessEntity.ApprovalBy = SystemUserUtil.getSystemUserID();
        refundTreatBillProcessEntity.ApprovalOn = new Date();
        refundTreatBillProcessEntity.ActionType = "50";
        refundTreatBillMapper.createRefundTreatBillProcess(refundTreatBillProcessEntity);
        return BaseOutput.success();
    }


    @Transactional
    public String approvalBill(RefundTreatBillApprovalForm form, String channel) {
        RefundTreatBillEntity refundTreatBillEntity = refundTreatBillMapper.getRefundTreatBill(form.ID);
        Boolean replacementOrderRestriction = sealingAccountService.getReplacementOrderRestriction(DateTimeUtil.toDate(refundTreatBillEntity.BillDate));
        if(replacementOrderRestriction){
            throw new RuntimeException("所审批的订单时间处于关账时间内，请选择其他时间");
        }
        RefundTreatBillProcessEntity refundTreatBillProcessEntity = new RefundTreatBillProcessEntity();
        refundTreatBillProcessEntity.RefundTreatBillID = form.ID;
        refundTreatBillProcessEntity.ApprovalBy = SystemUserUtil.getSystemUserID();
        refundTreatBillProcessEntity.ApprovalOn = new Date();
        refundTreatBillProcessEntity.ActionType = form.ApprovalStatus;
        refundTreatBillProcessEntity.ApprovalRemark = form.ApprovalRemark;
        refundTreatBillMapper.createRefundTreatBillProcess(refundTreatBillProcessEntity);

        refundTreatBillMapper.updateRefundTreatBillStatus(form.ID, form.ApprovalStatus);

        TreatBillEntity treatBillEntity = new TreatBillEntity();
        if (form.ApprovalStatus.equals("30")) {

            //判断会员是否有渠道
            CustomerEntity cust = customerMapper.getCustomer(refundTreatBillEntity.CustomerID);
            Boolean isCalculateChannelPerformance = cust.ChannelID == null ? false : true;
            List<ChannelParentOutputForm> parents = new ArrayList<>();
            List<ChannelEmployeePerformanceRateOutputForm> developer = new ArrayList<>();
            List<ChannelEmployeePerformanceRateOutputForm> consultant = new ArrayList<>();
            if (isCalculateChannelPerformance) {
                parents = channelMapper.getChannelParents(cust.ChannelID);

                developer = channelMapper.channelDeveloper(cust.ChannelID);
                consultant = channelMapper.channelConsultant(cust.ChannelID);
            }
            List<RefundTreatBillProductEntity> refundTreatProduct = refundTreatBillMapper.getRefundTreatBillProduct(form.ID);
            List<RefundTreatBillProjectEntity> refundTreatProject = refundTreatBillMapper.getRefundTreatBillProject(form.ID);
            List<RefundTreatBillGeneralCardEntity> refundTreatGeneralCard = refundTreatBillMapper.getRefundTreatBillGeneralCard(form.ID);
            List<RefundTreatBillTimeCardEntity> refundTreatTimeCard = refundTreatBillMapper.getRefundTreatBillTimeCard(form.ID);
            List<RefundTreatBillSavingCardEntity> refundTreatSavingCard = refundTreatBillMapper.getRefundTreatBillSavingCard(form.ID);

            treatBillEntity = new TreatBillEntity();
            treatBillEntity.ID = refundTreatBillEntity.ID;
            treatBillEntity.EntityID = refundTreatBillEntity.EntityID;
            treatBillEntity.CustomerID = refundTreatBillEntity.CustomerID;
            treatBillEntity.BillType = BillCode.BillType_20.getID();
            treatBillEntity.BillDate = refundTreatBillEntity.BillDate;
            treatBillEntity.EmployeeID = refundTreatBillEntity.CreatedBy;
            treatBillEntity.Amount = refundTreatBillEntity.Amount;
            treatBillEntity.PayAmount = BigDecimal.ZERO;
            treatBillEntity.CardDeductionAmount = BigDecimal.ZERO;
            treatBillEntity.LargessCardDeductionAmount = BigDecimal.ZERO;
            treatBillEntity.MemberPreferentialAmount = BigDecimal.ZERO;
            treatBillEntity.PricePreferentialAmount = BigDecimal.ZERO;
            treatBillEntity.CardPreferentialAmount = BigDecimal.ZERO;
            treatBillEntity.Channel = channel;
            treatBillEntity.BillStatus = BillCode.BillStatus_20.getID();
            treatBillEntity.Remark = refundTreatBillEntity.RefundRemark;
            treatBillEntity.IsFastBill = false;
            BigDecimal BillPayAmount = BigDecimal.ZERO, BillCardDeductionAmount = BigDecimal.ZERO, BillLargessCardDeductionAmount = BigDecimal.ZERO;

            treatBillMapper.createTreatBill(treatBillEntity);
            //产品入库
            InventoryProductInboundEntity inventoryProductInboundEntity = new InventoryProductInboundEntity();
            inventoryProductInboundEntity.ID = AutoNumberUtils.getPSIInventoryProductInboundID();
            inventoryProductInboundEntity.InDate = refundTreatBillEntity.BillDate;
            inventoryProductInboundEntity.EntityID = refundTreatBillEntity.EntityID;
            inventoryProductInboundEntity.InType = "20";
            inventoryProductInboundEntity.CreatedBy = SystemUserUtil.getSystemUserID();
            inventoryProductInboundEntity.CreatedOn = new Date();

            if (refundTreatProduct != null && refundTreatProduct.size() > 0) {
                inventoryProductInboundMapper.insertInventoryProductInboundEntity(inventoryProductInboundEntity);
            }
            List<InventoryProductInboundDetailEntity> inventoryProductInboundDetailEntities = new ArrayList<>();

            //创建开发人员渠道业绩
            List<TreatChannelEmployeePerformanceGoodsEntity> treatDeveloperChannelPerformanceProduct = new ArrayList<>();
            List<TreatChannelEmployeePerformanceGoodsEntity> treatDeveloperChannelPerformanceProject = new ArrayList<>();
            List<TreatChannelEmployeePerformanceGoodsEntity> treatDeveloperChannelPerformanceGeneralCard = new ArrayList<>();
            List<TreatChannelEmployeePerformanceGoodsEntity> treatDeveloperChannelPerformanceTimeCard = new ArrayList<>();
            List<TreatChannelEmployeePerformanceGoodsEntity> treatDeveloperChannelPerformanceSavingCard = new ArrayList<>();
            //开发人员套餐卡渠道业绩
            List<TreatChannelEmployeePerformancePackageCardGoodsEntity> treatDeveloperChannelPerformancePackageCardProduct = new ArrayList<>();
            List<TreatChannelEmployeePerformancePackageCardGoodsEntity> treatDeveloperChannelPerformancePackageCardProject = new ArrayList<>();
            List<TreatChannelEmployeePerformancePackageCardGoodsEntity> treatDeveloperChannelPerformancePackageCardGeneralCard = new ArrayList<>();
            List<TreatChannelEmployeePerformancePackageCardGoodsEntity> treatDeveloperChannelPerformancePackageCardTimeCard = new ArrayList<>();
            List<TreatChannelEmployeePerformancePackageCardGoodsEntity> treatDeveloperChannelPerformancePackageCardSavingCard = new ArrayList<>();

            //创建服务人员渠道业绩
            List<TreatChannelEmployeePerformanceGoodsEntity> treatConsultantChannelPerformanceProduct = new ArrayList<>();
            List<TreatChannelEmployeePerformanceGoodsEntity> treatConsultantChannelPerformanceProject = new ArrayList<>();
            List<TreatChannelEmployeePerformanceGoodsEntity> treatConsultantChannelPerformanceGeneralCard = new ArrayList<>();
            List<TreatChannelEmployeePerformanceGoodsEntity> treatConsultantChannelPerformanceTimeCard = new ArrayList<>();
            List<TreatChannelEmployeePerformanceGoodsEntity> treatConsultantChannelPerformanceSavingCard = new ArrayList<>();
            //服务人员套餐卡渠道业绩
            List<TreatChannelEmployeePerformancePackageCardGoodsEntity> treatConsultantChannelPerformancePackageCardProduct = new ArrayList<>();
            List<TreatChannelEmployeePerformancePackageCardGoodsEntity> treatConsultantChannelPerformancePackageCardProject = new ArrayList<>();
            List<TreatChannelEmployeePerformancePackageCardGoodsEntity> treatConsultantChannelPerformancePackageCardGeneralCard = new ArrayList<>();
            List<TreatChannelEmployeePerformancePackageCardGoodsEntity> treatConsultantChannelPerformancePackageCardTimeCard = new ArrayList<>();
            List<TreatChannelEmployeePerformancePackageCardGoodsEntity> treatConsultantChannelPerformancePackageCardSavingCard = new ArrayList<>();

            if (refundTreatProduct != null && refundTreatProduct.size() > 0) {
                //渠道业绩
                List<TreatChannelPerformanceGoodEntity> treatChannelPerformanceGoodEntities = new ArrayList<>();
                List<TreatChannelPerformancePackageCardGoodEntity> treatChannelPerformancePackageCardGoodEntities = new ArrayList<>();

                for (RefundTreatBillProductEntity refundTreatBillProductEntity : refundTreatProduct) {
                    TreatBillEntity treatBill = treatBillMapper.getTreatBill(refundTreatBillProductEntity.TreatBillID);
                    ProductOutputForm productInfo = productMapper.getProductInfo(refundTreatBillProductEntity.ProductID);
                    if (treatBill.BillStatus.equals("30")) {
                        throw new RuntimeException("产品订单已经被取消，请撤销申请");
                    }
                    RefundTreatBillGoodsOutputForm refundProductCount = refundTreatBillMapper.getRefundProductCount(refundTreatBillProductEntity.ProductAccountID, refundTreatBillProductEntity.TreatBillProductID);
                    ProductAccountEntity productAccountEntity = productAccountMapper.getProductAccountByAccountID(refundTreatBillProductEntity.ProductAccountID);
                    TreatBillProductEntity oldTreatBillProductEntity = treatBillMapper.getTreatBillProductEntity(refundTreatBillProductEntity.TreatBillProductID);
                    BigDecimal PayAmount = BigDecimal.ZERO, CardDeductionAmount = BigDecimal.ZERO, LargessCardDeductionAmount = BigDecimal.ZERO, LargessAmount = BigDecimal.ZERO;
                    Integer Balance = oldTreatBillProductEntity.Quantity - refundProductCount.count;
                    if (Balance.equals(refundTreatBillProductEntity.Quantity)) {
                        if (oldTreatBillProductEntity.IsLargess) {
                            LargessAmount = refundTreatBillProductEntity.TotalAmount;
                        } else {
                            PayAmount = oldTreatBillProductEntity.PayAmount.multiply(new BigDecimal(refundProductCount.count)).divide(new BigDecimal(oldTreatBillProductEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            CardDeductionAmount = oldTreatBillProductEntity.CardDeductionAmount.multiply(new BigDecimal(refundProductCount.count)).divide(new BigDecimal(oldTreatBillProductEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            LargessCardDeductionAmount = refundProductCount.TotalAmount.subtract(PayAmount).subtract(CardDeductionAmount);

                            PayAmount = oldTreatBillProductEntity.PayAmount.subtract(PayAmount);
                            CardDeductionAmount = oldTreatBillProductEntity.CardDeductionAmount.subtract(CardDeductionAmount);
                            LargessCardDeductionAmount = oldTreatBillProductEntity.LargessCardDeductionAmount.subtract(LargessCardDeductionAmount);
                        }
                    } else {
                        if (oldTreatBillProductEntity.IsLargess) {
                            LargessAmount = refundTreatBillProductEntity.TotalAmount;
                        } else {
                            PayAmount = oldTreatBillProductEntity.PayAmount.multiply(new BigDecimal(refundTreatBillProductEntity.Quantity)).divide(new BigDecimal(oldTreatBillProductEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            CardDeductionAmount = oldTreatBillProductEntity.CardDeductionAmount.multiply(new BigDecimal(refundTreatBillProductEntity.Quantity)).divide(new BigDecimal(oldTreatBillProductEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            LargessCardDeductionAmount = refundTreatBillProductEntity.TotalAmount.subtract(PayAmount).subtract(CardDeductionAmount);
                        }
                    }
                    //更新账户
                    productAccountEntity.Balance = productAccountEntity.Balance + refundTreatBillProductEntity.Quantity;
                    productAccountEntity.PayPerformanceBalanceAmount = productAccountEntity.PayPerformanceBalanceAmount.add(PayAmount);
                    productAccountEntity.CardPerformanceBalanceAmount = productAccountEntity.CardPerformanceBalanceAmount.add(CardDeductionAmount);
                    productAccountEntity.CardLargessPerformanceBalanceAmount = productAccountEntity.CardLargessPerformanceBalanceAmount.add(LargessCardDeductionAmount);
                    productAccountEntity.LargessPerformanceBalanceAmount = productAccountEntity.LargessPerformanceBalanceAmount.add(LargessAmount);
                    productAccountMapper.updateProductAccountBalance(productAccountEntity);

                    BillPayAmount = BillPayAmount.add(PayAmount);
                    BillCardDeductionAmount = BillCardDeductionAmount.add(CardDeductionAmount);
                    BillLargessCardDeductionAmount = BillLargessCardDeductionAmount.add(LargessCardDeductionAmount);

                    TreatBillProductEntity newTreatBillProductEntity = new TreatBillProductEntity();
                    newTreatBillProductEntity.TreatBillID = treatBillEntity.ID;
                    newTreatBillProductEntity.ProductAccountID = refundTreatBillProductEntity.ProductAccountID;
                    newTreatBillProductEntity.ProductID = refundTreatBillProductEntity.ProductID;
                    newTreatBillProductEntity.Quantity = refundTreatBillProductEntity.Quantity;
                    newTreatBillProductEntity.Price = refundTreatBillProductEntity.Amount;
                    newTreatBillProductEntity.TotalAmount = refundTreatBillProductEntity.TotalAmount;
                    newTreatBillProductEntity.IsLargess = oldTreatBillProductEntity.IsLargess;
                    newTreatBillProductEntity.CardPreferentialAmount = BigDecimal.ZERO;
                    newTreatBillProductEntity.PayAmount = PayAmount;
                    newTreatBillProductEntity.CardDeductionAmount = CardDeductionAmount;
                    newTreatBillProductEntity.LargessCardDeductionAmount = LargessCardDeductionAmount;
                    if (refundTreatBillProductEntity.PackageCardAccountID != null && refundTreatBillProductEntity.PackageCardAccountID > 0) {
                        newTreatBillProductEntity.PackageCardAccountID = refundTreatBillProductEntity.PackageCardAccountID;
                        newTreatBillProductEntity.PackageCardID = refundTreatBillProductEntity.PackageCardID;
                    }
                    treatBillMapper.createTreatBillProduct(newTreatBillProductEntity);


                    //产品入库
                    StockEntity stockEntity = stockMapper.stockInfo(new StockEntity(inventoryProductInboundEntity.EntityID, refundTreatBillProductEntity.ProductID));
                    InventoryProductInboundDetailEntity inventoryProductInboundDetailEntity = new InventoryProductInboundDetailEntity();
                    inventoryProductInboundDetailEntity.InboundBillID = inventoryProductInboundEntity.ID;
                    inventoryProductInboundDetailEntity.ProductID = refundTreatBillProductEntity.ProductID;
                    inventoryProductInboundDetailEntity.UnitID = productInfo.UnitID;
                    inventoryProductInboundDetailEntity.Quantity = refundTreatBillProductEntity.Quantity;
                    inventoryProductInboundDetailEntity.MinimumUnitID = productInfo.UnitID;
                    inventoryProductInboundDetailEntity.MinimumUnitQuantity = refundTreatBillProductEntity.Quantity;
                    if (stockEntity != null) {
                        inventoryProductInboundDetailEntity.StockQuantity = stockEntity.Quantity + inventoryProductInboundDetailEntity.MinimumUnitQuantity;
                        stockEntity.Quantity = inventoryProductInboundDetailEntity.StockQuantity;
                        stockMapper.updateStockEntity(stockEntity);
                    } else {
                        inventoryProductInboundDetailEntity.StockQuantity = inventoryProductInboundDetailEntity.Quantity;
                        stockEntity = new StockEntity(inventoryProductInboundEntity.EntityID, inventoryProductInboundDetailEntity.ProductID, inventoryProductInboundDetailEntity.Quantity);
                        stockMapper.insertStockEntity(stockEntity);
                    }
                    inventoryProductInboundDetailEntities.add(inventoryProductInboundDetailEntity);

                    //门店业绩比例
                    TreatPerformanceEntity treatEntityPerformance = treatEntityPerformanceMapper.getTreatProductEntityPerformance(treatBillEntity.EntityID, newTreatBillProductEntity.ProductID);

                    //记录门店业绩
                    TreatEntityPerformancePackageCardProductEntity treatEntityPerformanceProductEntity = new TreatEntityPerformancePackageCardProductEntity();

                    treatEntityPerformanceProductEntity.EntityID = treatBillEntity.EntityID;
                    treatEntityPerformanceProductEntity.TreatBillID = treatBillEntity.ID;
                    treatEntityPerformanceProductEntity.TreatBillProductID = newTreatBillProductEntity.ID;
                    treatEntityPerformanceProductEntity.ProductID = newTreatBillProductEntity.ProductID;
                    treatEntityPerformanceProductEntity.BillDate = treatBillEntity.BillDate;
                    treatEntityPerformanceProductEntity.PayPerformance = newTreatBillProductEntity.PayAmount;
                    treatEntityPerformanceProductEntity.CardPerformance = newTreatBillProductEntity.CardDeductionAmount;
                    treatEntityPerformanceProductEntity.CardLargessPerformance = newTreatBillProductEntity.LargessCardDeductionAmount;
                    treatEntityPerformanceProductEntity.LargessPerformance = BigDecimal.ZERO;
                    if (newTreatBillProductEntity.IsLargess) {
                        treatEntityPerformanceProductEntity.LargessPerformance = refundTreatBillProductEntity.TotalAmount;
                    }
                    PackageCardAccountEntity packageCardAccountEntity = new PackageCardAccountEntity();
                    if (productAccountEntity.PackageCardAccountID != null && productAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                        packageCardAccountEntity = packageCardAccountMapper.getPackageCardAccount(productAccountEntity.PackageCardAccountID);
                        treatEntityPerformanceProductEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                    }
                    List<RefundTreatBillHandlerProductEntity> refundTreatBillHandlerProduct = refundTreatBillMapper.getRefundTreatBillHandlerProduct(refundTreatBillProductEntity.ID);
                    //记录经手人
                    if (refundTreatBillHandlerProduct != null && refundTreatBillHandlerProduct.size() > 0) {
                        for (RefundTreatBillHandlerProductEntity refundTreatBillHandlerProductEntity : refundTreatBillHandlerProduct) {
                            TreatBillHandlerProductEntity treatBillHandlerProductEntity = new TreatBillHandlerProductEntity();
                            treatBillHandlerProductEntity.TreatBillProductID = newTreatBillProductEntity.ID;
                            treatBillHandlerProductEntity.ProductTreatHandlerID = refundTreatBillHandlerProductEntity.ProductTreatHandlerID;
                            treatBillHandlerProductEntity.EmployeeID = refundTreatBillHandlerProductEntity.EmployeeID;
                            treatBillHandlerProductEntity.Scale = refundTreatBillHandlerProductEntity.Scale;
                            treatBillHandlerProductEntity.IsCalculatePassengerFlow = true;
                            treatBillMapper.createTreatBillHandlerProduct(treatBillHandlerProductEntity);
                            BigDecimal TotalPerformance = treatEntityPerformanceProductEntity.PayPerformance.add(treatEntityPerformanceProductEntity.CardPerformance).add(treatEntityPerformanceProductEntity.CardLargessPerformance).add(treatEntityPerformanceProductEntity.LargessPerformance);
                            //记录员工业绩和提佣
                            TreatPerformanceEntity treatPerformanceEntity = treatEmployeePerformanceCommissionMapper.getTreatProductPerformance(treatBillEntity.EntityID, refundTreatBillHandlerProductEntity.EmployeeID, refundTreatBillHandlerProductEntity.ProductTreatHandlerID, newTreatBillProductEntity.ProductID);
                            TreatCommissionEntity treatCommissionEntity = treatEmployeePerformanceCommissionMapper.getTreatProductCommission(treatBillEntity.EntityID, refundTreatBillHandlerProductEntity.EmployeeID, refundTreatBillHandlerProductEntity.ProductTreatHandlerID, newTreatBillProductEntity.ProductID);
                            BigDecimal performancePayRate = treatPerformanceEntity.PayRate == null ? BigDecimal.ZERO : treatPerformanceEntity.PayRate;
                            BigDecimal performanceCardRate = treatPerformanceEntity.CardRate == null ? BigDecimal.ZERO : treatPerformanceEntity.CardRate;
                            BigDecimal performanceCardLargessRate = treatPerformanceEntity.CardLargessRate == null ? BigDecimal.ZERO : treatPerformanceEntity.CardLargessRate;
                            BigDecimal performanceLargessRate = treatPerformanceEntity.LargessRate == null ? BigDecimal.ZERO : treatPerformanceEntity.LargessRate;
                            BigDecimal payRate = BigDecimal.ZERO;
                            BigDecimal cardRate = BigDecimal.ZERO;
                            BigDecimal cardLargessRate = BigDecimal.ZERO;
                            BigDecimal largessRate = BigDecimal.ZERO;
                            BigDecimal payFixed = BigDecimal.ZERO;
                            BigDecimal cardFixed = BigDecimal.ZERO;
                            BigDecimal cardLargessFixed = BigDecimal.ZERO;
                            BigDecimal largessFixed = BigDecimal.ZERO;
                            BigDecimal specialBenefit = BigDecimal.ZERO;
                            if (treatCommissionEntity != null) {
                                payRate = treatCommissionEntity.PayRate == null ? BigDecimal.ZERO : treatCommissionEntity.PayRate;
                                cardRate = treatCommissionEntity.CardRate == null ? BigDecimal.ZERO : treatCommissionEntity.CardRate;
                                cardLargessRate = treatCommissionEntity.CardLargessRate == null ? BigDecimal.ZERO : treatCommissionEntity.CardLargessRate;
                                largessRate = treatCommissionEntity.LargessRate == null ? BigDecimal.ZERO : treatCommissionEntity.LargessRate;
                                payFixed = treatCommissionEntity.PayFixed == null ? BigDecimal.ZERO : treatCommissionEntity.PayFixed;
                                cardFixed = treatCommissionEntity.CardFixed == null ? BigDecimal.ZERO : treatCommissionEntity.CardFixed;
                                cardLargessFixed = treatCommissionEntity.CardLargessFixed == null ? BigDecimal.ZERO : treatCommissionEntity.CardLargessFixed;
                                largessFixed = treatCommissionEntity.LargessFixed == null ? BigDecimal.ZERO : treatCommissionEntity.LargessFixed;
                                specialBenefit = treatCommissionEntity.SpecialBenefit == null ? BigDecimal.ZERO : treatCommissionEntity.SpecialBenefit;
                            }
                            TreatEmployeePerformanceCommissionPackageCardProductEntity treatEmployeePerformanceCommissionPackageCardProductEntity = new TreatEmployeePerformanceCommissionPackageCardProductEntity();
                            treatEmployeePerformanceCommissionPackageCardProductEntity.EntityID = treatBillEntity.EntityID;
                            treatEmployeePerformanceCommissionPackageCardProductEntity.TreatBillID = treatBillEntity.ID;
                            treatEmployeePerformanceCommissionPackageCardProductEntity.TreatBillProductID = newTreatBillProductEntity.ID;
                            treatEmployeePerformanceCommissionPackageCardProductEntity.ProductID = newTreatBillProductEntity.ProductID;
                            treatEmployeePerformanceCommissionPackageCardProductEntity.ProductTreatHandlerID = refundTreatBillHandlerProductEntity.ProductTreatHandlerID;
                            treatEmployeePerformanceCommissionPackageCardProductEntity.EmployeeID = refundTreatBillHandlerProductEntity.EmployeeID;
                            treatEmployeePerformanceCommissionPackageCardProductEntity.BillDate = treatBillEntity.BillDate;
                            if (treatEntityPerformanceProductEntity.PayPerformance.compareTo(BigDecimal.ZERO) > 0 && TotalPerformance.compareTo(BigDecimal.ZERO) > 0) {
                                treatEmployeePerformanceCommissionPackageCardProductEntity.PayPerformance = treatEntityPerformanceProductEntity.PayPerformance.multiply(performancePayRate).multiply(treatBillHandlerProductEntity.Scale).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardProductEntity.PayRateCommission = treatEmployeePerformanceCommissionPackageCardProductEntity.PayPerformance.multiply(payRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardProductEntity.PayFixedCommission = payFixed.multiply(BigDecimal.valueOf(newTreatBillProductEntity.Quantity)).multiply(treatEmployeePerformanceCommissionPackageCardProductEntity.PayPerformance).divide(TotalPerformance, 2, BigDecimal.ROUND_HALF_UP);
                            } else {
                                treatEmployeePerformanceCommissionPackageCardProductEntity.PayPerformance = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardProductEntity.PayRateCommission = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardProductEntity.PayFixedCommission = BigDecimal.ZERO;
                            }
                            if (treatEntityPerformanceProductEntity.CardPerformance.compareTo(BigDecimal.ZERO) > 0 && TotalPerformance.compareTo(BigDecimal.ZERO) > 0) {
                                treatEmployeePerformanceCommissionPackageCardProductEntity.CardPerformance = treatEntityPerformanceProductEntity.CardPerformance.multiply(performanceCardRate).multiply(treatBillHandlerProductEntity.Scale).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardProductEntity.CardRateCommission = treatEmployeePerformanceCommissionPackageCardProductEntity.CardPerformance.multiply(cardRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardProductEntity.CardFixedCommission = cardFixed.multiply(BigDecimal.valueOf(newTreatBillProductEntity.Quantity)).multiply(treatEmployeePerformanceCommissionPackageCardProductEntity.CardPerformance).divide(TotalPerformance, 2, BigDecimal.ROUND_HALF_UP);
                            } else {
                                treatEmployeePerformanceCommissionPackageCardProductEntity.CardPerformance = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardProductEntity.CardRateCommission = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardProductEntity.CardFixedCommission = BigDecimal.ZERO;
                            }
                            if (treatEntityPerformanceProductEntity.CardLargessPerformance.compareTo(BigDecimal.ZERO) > 0 && TotalPerformance.compareTo(BigDecimal.ZERO) > 0) {
                                treatEmployeePerformanceCommissionPackageCardProductEntity.CardLargessPerformance = treatEntityPerformanceProductEntity.CardLargessPerformance.multiply(performanceCardLargessRate).multiply(treatBillHandlerProductEntity.Scale).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardProductEntity.CardLargessRateCommission = treatEmployeePerformanceCommissionPackageCardProductEntity.CardLargessPerformance.multiply(cardLargessRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardProductEntity.CardLargessFixedCommission = cardLargessFixed.multiply(BigDecimal.valueOf(newTreatBillProductEntity.Quantity)).multiply(treatEmployeePerformanceCommissionPackageCardProductEntity.CardLargessPerformance).divide(TotalPerformance, 2, BigDecimal.ROUND_HALF_UP);
                            } else {
                                treatEmployeePerformanceCommissionPackageCardProductEntity.CardLargessPerformance = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardProductEntity.CardLargessRateCommission = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardProductEntity.CardLargessFixedCommission = BigDecimal.ZERO;
                            }
                            if (treatEntityPerformanceProductEntity.LargessPerformance.compareTo(BigDecimal.ZERO) > 0 && TotalPerformance.compareTo(BigDecimal.ZERO) > 0) {
                                treatEmployeePerformanceCommissionPackageCardProductEntity.LargessPerformance = treatEntityPerformanceProductEntity.LargessPerformance.multiply(performanceLargessRate).multiply(treatBillHandlerProductEntity.Scale).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardProductEntity.LargessRateCommission = treatEmployeePerformanceCommissionPackageCardProductEntity.LargessPerformance.multiply(largessRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardProductEntity.LargessFixedCommission = largessFixed.multiply(BigDecimal.valueOf(newTreatBillProductEntity.Quantity)).multiply(treatEmployeePerformanceCommissionPackageCardProductEntity.LargessPerformance).divide(TotalPerformance, 2, BigDecimal.ROUND_HALF_UP);
                            } else {
                                treatEmployeePerformanceCommissionPackageCardProductEntity.LargessPerformance = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardProductEntity.LargessRateCommission = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardProductEntity.LargessFixedCommission = BigDecimal.ZERO;
                            }
                            treatEmployeePerformanceCommissionPackageCardProductEntity.SpecialBenefitCommission = specialBenefit.multiply(BigDecimal.valueOf(newTreatBillProductEntity.Quantity)).multiply(treatBillHandlerProductEntity.Scale).setScale(2, BigDecimal.ROUND_HALF_UP);
                            ;

                            if (productAccountEntity.PackageCardAccountID != null && productAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                                treatEmployeePerformanceCommissionPackageCardProductEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                                treatEmployeePerformanceCommissionMapper.createTreatEmployeePerformanceCommissionPackageCardProduct(treatEmployeePerformanceCommissionPackageCardProductEntity);
                            } else {
                                treatEmployeePerformanceCommissionMapper.createTreatEmployeePerformanceCommissionProduct(treatEmployeePerformanceCommissionPackageCardProductEntity);
                            }
                        }
                    }

                    if (isCalculateChannelPerformance) {
                        for (ChannelParentOutputForm parent : parents) {
                            TreatChannelPerformancePackageCardGoodEntity treatChannelPerformanceGoodEntity = new TreatChannelPerformancePackageCardGoodEntity();
                            treatChannelPerformanceGoodEntity.TreatBillID = treatBillEntity.ID;
                            treatChannelPerformanceGoodEntity.EntityID = treatBillEntity.EntityID;
                            treatChannelPerformanceGoodEntity.BillDate = treatBillEntity.BillDate;
                            treatChannelPerformanceGoodEntity.TreatBillGoodsID = newTreatBillProductEntity.ID;
                            treatChannelPerformanceGoodEntity.GoodsID = newTreatBillProductEntity.ProductID;
                            treatChannelPerformanceGoodEntity.ChannelID = parent.ID;
                            treatChannelPerformanceGoodEntity.IsChannelIntroducer = false;
                            treatChannelPerformanceGoodEntity.PayPerformance = treatEntityPerformanceProductEntity.PayPerformance;
                            treatChannelPerformanceGoodEntity.CardPerformance = treatEntityPerformanceProductEntity.CardPerformance;
                            treatChannelPerformanceGoodEntity.CardLargessPerformance = treatEntityPerformanceProductEntity.CardLargessPerformance;
                            treatChannelPerformanceGoodEntity.LargessPerformance = treatEntityPerformanceProductEntity.LargessPerformance;
                            if (productAccountEntity.PackageCardAccountID != null && productAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                                treatChannelPerformanceGoodEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                                treatChannelPerformancePackageCardGoodEntities.add(treatChannelPerformanceGoodEntity);
                            } else {
                                treatChannelPerformanceGoodEntities.add(treatChannelPerformanceGoodEntity);
                            }
                            if (parent.IntroducerList.size() > 0) {
                                for (Integer integer : parent.IntroducerList) {
                                    TreatChannelPerformancePackageCardGoodEntity performanceGoodEntity = new TreatChannelPerformancePackageCardGoodEntity();
                                    performanceGoodEntity.TreatBillID = treatBillEntity.ID;
                                    performanceGoodEntity.EntityID = treatBillEntity.EntityID;
                                    performanceGoodEntity.BillDate = treatBillEntity.BillDate;
                                    performanceGoodEntity.TreatBillGoodsID = newTreatBillProductEntity.ID;
                                    performanceGoodEntity.GoodsID = newTreatBillProductEntity.ProductID;
                                    performanceGoodEntity.ChannelID = integer;
                                    performanceGoodEntity.IsChannelIntroducer = true;
                                    performanceGoodEntity.PayPerformance = treatEntityPerformanceProductEntity.PayPerformance;
                                    performanceGoodEntity.CardPerformance = treatEntityPerformanceProductEntity.CardPerformance;
                                    performanceGoodEntity.CardLargessPerformance = treatEntityPerformanceProductEntity.CardLargessPerformance;
                                    performanceGoodEntity.LargessPerformance = treatEntityPerformanceProductEntity.LargessPerformance;
                                    if (productAccountEntity.PackageCardAccountID != null && productAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                                        performanceGoodEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                                        treatChannelPerformancePackageCardGoodEntities.add(performanceGoodEntity);
                                    } else {
                                        treatChannelPerformanceGoodEntities.add(performanceGoodEntity);
                                    }
                                }

                            }
                        }
                        for (ChannelEmployeePerformanceRateOutputForm employeePerformanceRateOutputForm : developer) {
                            TreatChannelEmployeePerformancePackageCardGoodsEntity packageCardGoodsEntity = new TreatChannelEmployeePerformancePackageCardGoodsEntity();
                            packageCardGoodsEntity.TreatBillID = treatBillEntity.ID;
                            packageCardGoodsEntity.EntityID = treatBillEntity.EntityID;
                            packageCardGoodsEntity.BillDate = treatBillEntity.BillDate;
                            packageCardGoodsEntity.TreatBillGoodsID = newTreatBillProductEntity.ID;
                            packageCardGoodsEntity.GoodsID = productAccountEntity.ProductID;
                            packageCardGoodsEntity.ChannelID = cust.ChannelID;
                            packageCardGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                            packageCardGoodsEntity.PayPerformance = treatEntityPerformanceProductEntity.PayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            packageCardGoodsEntity.CardPerformance = treatEntityPerformanceProductEntity.CardPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            packageCardGoodsEntity.CardLargessPerformance = treatEntityPerformanceProductEntity.CardLargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            packageCardGoodsEntity.LargessPerformance = treatEntityPerformanceProductEntity.LargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            if (productAccountEntity.PackageCardAccountID != null && productAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                                packageCardGoodsEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                                treatDeveloperChannelPerformancePackageCardProduct.add(packageCardGoodsEntity);
                            } else {
                                treatDeveloperChannelPerformanceProduct.add(packageCardGoodsEntity);
                            }
                        }

                        for (ChannelEmployeePerformanceRateOutputForm employeePerformanceRateOutputForm : consultant) {
                            TreatChannelEmployeePerformancePackageCardGoodsEntity packageCardGoodsEntity = new TreatChannelEmployeePerformancePackageCardGoodsEntity();
                            packageCardGoodsEntity.TreatBillID = treatBillEntity.ID;
                            packageCardGoodsEntity.EntityID = treatBillEntity.EntityID;
                            packageCardGoodsEntity.BillDate = treatBillEntity.BillDate;
                            packageCardGoodsEntity.TreatBillGoodsID = newTreatBillProductEntity.ID;
                            packageCardGoodsEntity.GoodsID = productAccountEntity.ProductID;
                            packageCardGoodsEntity.ChannelID = cust.ChannelID;
                            packageCardGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                            packageCardGoodsEntity.PayPerformance = treatEntityPerformanceProductEntity.PayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            packageCardGoodsEntity.CardPerformance = treatEntityPerformanceProductEntity.CardPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            packageCardGoodsEntity.CardLargessPerformance = treatEntityPerformanceProductEntity.CardLargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            packageCardGoodsEntity.LargessPerformance = treatEntityPerformanceProductEntity.LargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            if (productAccountEntity.PackageCardAccountID != null && productAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                                packageCardGoodsEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                                treatConsultantChannelPerformancePackageCardProduct.add(packageCardGoodsEntity);
                            } else {
                                treatConsultantChannelPerformanceProduct.add(packageCardGoodsEntity);
                            }
                        }
                    }

                    treatEntityPerformanceProductEntity.PayPerformance = newTreatBillProductEntity.PayAmount.multiply(treatEntityPerformance.PayRate == null ? BigDecimal.ZERO : treatEntityPerformance.PayRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                    treatEntityPerformanceProductEntity.CardPerformance = newTreatBillProductEntity.CardDeductionAmount.multiply(treatEntityPerformance.CardRate == null ? BigDecimal.ZERO : treatEntityPerformance.CardRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                    treatEntityPerformanceProductEntity.CardLargessPerformance = newTreatBillProductEntity.LargessCardDeductionAmount.multiply(treatEntityPerformance.CardLargessRate == null ? BigDecimal.ZERO : treatEntityPerformance.CardLargessRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                    treatEntityPerformanceProductEntity.LargessPerformance = BigDecimal.ZERO;
                    if (newTreatBillProductEntity.IsLargess) {
                        treatEntityPerformanceProductEntity.LargessPerformance = refundTreatBillProductEntity.TotalAmount.multiply(treatEntityPerformance.LargessRate == null ? BigDecimal.ZERO : treatEntityPerformance.LargessRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                    }
                    treatEntityPerformanceProductEntity.Scale = new BigDecimal(100);
                    if (productAccountEntity.PackageCardAccountID != null && productAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                        treatEntityPerformanceMapper.createTreatEntityPerformancePackageCardProduct(treatEntityPerformanceProductEntity);
                    } else {
                        treatEntityPerformanceMapper.createTreatEntityPerformanceProduct(treatEntityPerformanceProductEntity);
                    }
                }

                if (treatChannelPerformanceGoodEntities.size() > 0) {
                    treatChannelPerformanceMapper.createTreatChannelPerformanceProduct(treatChannelPerformanceGoodEntities);
                }
                if (treatChannelPerformancePackageCardGoodEntities.size() > 0) {
                    treatChannelPerformanceMapper.createTreatChannelPerformancePackageCardProduct(treatChannelPerformancePackageCardGoodEntities);
                }

                //保存开发人员的渠道业绩
                if (treatDeveloperChannelPerformanceProduct.size() > 0) {
                    treatChannelDeveloperPerformanceMapper.createTreatDeveloperChannelPerformanceProduct(treatDeveloperChannelPerformanceProduct);
                }

                //保存开发人员套餐卡渠道业绩
                if (treatDeveloperChannelPerformancePackageCardProduct.size() > 0) {
                    treatChannelDeveloperPerformanceMapper.createTreatDeveloperChannelPerformancePackageCardProduct(treatDeveloperChannelPerformancePackageCardProduct);
                }


                //保存服务人员的渠道业绩
                if (treatConsultantChannelPerformanceProduct.size() > 0) {
                    treatChannelConsultantPerformanceMapper.createTreatConsultantChannelPerformanceProduct(treatConsultantChannelPerformanceProduct);
                }


                //保存服务人员套餐卡渠道业绩
                if (treatConsultantChannelPerformancePackageCardProduct.size() > 0) {
                    treatChannelConsultantPerformanceMapper.createTreatConsultantChannelPerformancePackageCardProduct(treatConsultantChannelPerformancePackageCardProduct);
                }
            }

            //产品入库
            if (inventoryProductInboundDetailEntities.size() > 0) {
                inventoryProductInboundMapper.insertInventoryProductInboundDetailEntity(inventoryProductInboundDetailEntities);
            }

            if (refundTreatProject != null && refundTreatProject.size() > 0) {
                //渠道业绩
                List<TreatChannelPerformanceGoodEntity> treatChannelPerformanceGoodEntities = new ArrayList<>();
                List<TreatChannelPerformancePackageCardGoodEntity> treatChannelPerformancePackageCardGoodEntities = new ArrayList<>();

                for (RefundTreatBillProjectEntity refundTreatBillProjectEntity : refundTreatProject) {
                    TreatBillEntity treatBill = treatBillMapper.getTreatBill(refundTreatBillProjectEntity.TreatBillID);
                    if (treatBill.BillStatus.equals("30")) {
                        throw new RuntimeException("项目订单已经被取消，请撤销申请");
                    }
                    RefundTreatBillGoodsOutputForm refundProjectCount = refundTreatBillMapper.getRefundProjectCount(refundTreatBillProjectEntity.ProjectAccountID, refundTreatBillProjectEntity.TreatBillProjectID);

                    ProjectAccountEntity projectAccountEntity = projectAccountMapper.getProjectAccountByAccountID(refundTreatBillProjectEntity.ProjectAccountID);
                    TreatBillProjectEntity oldTreatBillProjectEntity = treatBillMapper.getTreatBillProjectEntity(refundTreatBillProjectEntity.TreatBillProjectID);
                    BigDecimal PayAmount = BigDecimal.ZERO, CardDeductionAmount = BigDecimal.ZERO, LargessCardDeductionAmount = BigDecimal.ZERO, LargessAmount = BigDecimal.ZERO;
                    Integer Balance = oldTreatBillProjectEntity.Quantity - refundProjectCount.count;
                    if (Balance.equals(refundTreatBillProjectEntity.Quantity)) {
                        if (oldTreatBillProjectEntity.IsLargess) {
                            LargessAmount = refundTreatBillProjectEntity.TotalAmount;
                        } else {
                            PayAmount = oldTreatBillProjectEntity.PayAmount.multiply(new BigDecimal(refundProjectCount.count)).divide(new BigDecimal(oldTreatBillProjectEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            CardDeductionAmount = oldTreatBillProjectEntity.CardDeductionAmount.multiply(new BigDecimal(refundProjectCount.count)).divide(new BigDecimal(oldTreatBillProjectEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            LargessCardDeductionAmount = refundProjectCount.TotalAmount.subtract(PayAmount).subtract(CardDeductionAmount);

                            PayAmount = oldTreatBillProjectEntity.PayAmount.subtract(PayAmount);
                            CardDeductionAmount = oldTreatBillProjectEntity.CardDeductionAmount.subtract(CardDeductionAmount);
                            LargessCardDeductionAmount = oldTreatBillProjectEntity.LargessCardDeductionAmount.subtract(LargessCardDeductionAmount);
                        }

                    } else {
                        if (oldTreatBillProjectEntity.IsLargess) {
                            LargessAmount = refundTreatBillProjectEntity.TotalAmount;
                        } else {
                            PayAmount = oldTreatBillProjectEntity.PayAmount.multiply(new BigDecimal(refundTreatBillProjectEntity.Quantity)).divide(new BigDecimal(oldTreatBillProjectEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            CardDeductionAmount = oldTreatBillProjectEntity.CardDeductionAmount.multiply(new BigDecimal(refundTreatBillProjectEntity.Quantity)).divide(new BigDecimal(oldTreatBillProjectEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            LargessCardDeductionAmount = refundTreatBillProjectEntity.TotalAmount.subtract(PayAmount).subtract(CardDeductionAmount);
                        }
                    }
                    //更新账户
                    projectAccountEntity.Balance = projectAccountEntity.Balance + refundTreatBillProjectEntity.Quantity;
                    projectAccountEntity.PayPerformanceBalanceAmount = projectAccountEntity.PayPerformanceBalanceAmount.add(PayAmount);
                    projectAccountEntity.CardPerformanceBalanceAmount = projectAccountEntity.CardPerformanceBalanceAmount.add(CardDeductionAmount);
                    projectAccountEntity.CardLargessPerformanceBalanceAmount = projectAccountEntity.CardLargessPerformanceBalanceAmount.add(LargessCardDeductionAmount);
                    projectAccountEntity.LargessPerformanceBalanceAmount = projectAccountEntity.LargessPerformanceBalanceAmount.add(LargessAmount);
                    projectAccountMapper.updateProjectAccountBalance(projectAccountEntity);

                    BillPayAmount = BillPayAmount.add(PayAmount);
                    BillCardDeductionAmount = BillCardDeductionAmount.add(CardDeductionAmount);
                    BillLargessCardDeductionAmount = BillLargessCardDeductionAmount.add(LargessCardDeductionAmount);

                    TreatBillProjectEntity newTreatBillProjectEntity = new TreatBillProjectEntity();
                    newTreatBillProjectEntity.TreatBillID = treatBillEntity.ID;
                    newTreatBillProjectEntity.ProjectAccountID = refundTreatBillProjectEntity.ProjectAccountID;
                    newTreatBillProjectEntity.ProjectID = refundTreatBillProjectEntity.ProjectID;
                    newTreatBillProjectEntity.Quantity = refundTreatBillProjectEntity.Quantity;
                    newTreatBillProjectEntity.Price = refundTreatBillProjectEntity.Amount;
                    newTreatBillProjectEntity.TotalAmount = refundTreatBillProjectEntity.TotalAmount;
                    newTreatBillProjectEntity.IsLargess = oldTreatBillProjectEntity.IsLargess;
                    newTreatBillProjectEntity.CardPreferentialAmount = BigDecimal.ZERO;
                    newTreatBillProjectEntity.PayAmount = PayAmount;
                    newTreatBillProjectEntity.CardDeductionAmount = CardDeductionAmount;
                    newTreatBillProjectEntity.LargessCardDeductionAmount = LargessCardDeductionAmount;
                    if (refundTreatBillProjectEntity.PackageCardAccountID != null && refundTreatBillProjectEntity.PackageCardAccountID > 0) {
                        newTreatBillProjectEntity.PackageCardAccountID = refundTreatBillProjectEntity.PackageCardAccountID;
                        newTreatBillProjectEntity.PackageCardID = refundTreatBillProjectEntity.PackageCardID;
                    }
                    treatBillMapper.createTreatBillProject(newTreatBillProjectEntity);

                    TreatPerformanceEntity treatEntityPerformance = treatEntityPerformanceMapper.getTreatProjectEntityPerformance(treatBillEntity.EntityID, newTreatBillProjectEntity.ProjectID);

                    //记录门店业绩
                    TreatEntityPerformancePackageCardProjectEntity treatEntityPerformanceProjectEntity = new TreatEntityPerformancePackageCardProjectEntity();
                    treatEntityPerformanceProjectEntity.EntityID = treatBillEntity.EntityID;
                    treatEntityPerformanceProjectEntity.TreatBillID = treatBillEntity.ID;
                    treatEntityPerformanceProjectEntity.TreatBillProjectID = newTreatBillProjectEntity.ID;
                    treatEntityPerformanceProjectEntity.ProjectID = newTreatBillProjectEntity.ProjectID;
                    treatEntityPerformanceProjectEntity.BillDate = treatBillEntity.BillDate;
                    treatEntityPerformanceProjectEntity.PayPerformance = newTreatBillProjectEntity.PayAmount;
                    treatEntityPerformanceProjectEntity.CardPerformance = newTreatBillProjectEntity.CardDeductionAmount;
                    treatEntityPerformanceProjectEntity.CardLargessPerformance = newTreatBillProjectEntity.LargessCardDeductionAmount;
                    treatEntityPerformanceProjectEntity.LargessPerformance = BigDecimal.ZERO;
                    if (newTreatBillProjectEntity.IsLargess) {
                        treatEntityPerformanceProjectEntity.LargessPerformance = refundTreatBillProjectEntity.TotalAmount;
                    }

                    PackageCardAccountEntity packageCardAccountEntity = new PackageCardAccountEntity();
                    if (projectAccountEntity.PackageCardAccountID != null && projectAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                        packageCardAccountEntity = packageCardAccountMapper.getPackageCardAccount(projectAccountEntity.PackageCardAccountID);
                        treatEntityPerformanceProjectEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                    }
                    List<RefundTreatBillHandlerProjectEntity> refundTreatBillHandlerProject = refundTreatBillMapper.getRefundTreatBillHandlerProject(refundTreatBillProjectEntity.ID);
                    //记录经手人
                    if (refundTreatBillHandlerProject != null && refundTreatBillHandlerProject.size() > 0) {
                        for (RefundTreatBillHandlerProjectEntity refundTreatBillHandlerProjectEntity : refundTreatBillHandlerProject) {
                            TreatBillHandlerProjectEntity treatBillHandlerProjectEntity = new TreatBillHandlerProjectEntity();
                            treatBillHandlerProjectEntity.TreatBillProjectID = newTreatBillProjectEntity.ID;
                            treatBillHandlerProjectEntity.ProjectTreatHandlerID = refundTreatBillHandlerProjectEntity.ProjectTreatHandlerID;
                            treatBillHandlerProjectEntity.EmployeeID = refundTreatBillHandlerProjectEntity.EmployeeID;
                            treatBillHandlerProjectEntity.Scale = refundTreatBillHandlerProjectEntity.Scale;
                            treatBillHandlerProjectEntity.IsCalculatePassengerFlow = true;
                            treatBillMapper.createTreatBillHandlerProject(treatBillHandlerProjectEntity);
                            BigDecimal TotalPerformance = treatEntityPerformanceProjectEntity.PayPerformance.add(treatEntityPerformanceProjectEntity.CardPerformance).add(treatEntityPerformanceProjectEntity.CardLargessPerformance).add(treatEntityPerformanceProjectEntity.LargessPerformance);
                            //记录员工业绩和提佣
                            TreatPerformanceEntity treatPerformanceEntity = treatEmployeePerformanceCommissionMapper.getTreatProjectPerformance(treatBillEntity.EntityID, refundTreatBillHandlerProjectEntity.EmployeeID, refundTreatBillHandlerProjectEntity.ProjectTreatHandlerID, newTreatBillProjectEntity.ProjectID);
                            TreatCommissionEntity treatCommissionEntity = treatEmployeePerformanceCommissionMapper.getTreatProjectCommission(treatBillEntity.EntityID, refundTreatBillHandlerProjectEntity.EmployeeID, refundTreatBillHandlerProjectEntity.ProjectTreatHandlerID, newTreatBillProjectEntity.ProjectID);
                            TreatEmployeePerformanceCommissionPackageCardProjectEntity treatEmployeePerformanceCommissionPackageCardProjectEntity = new TreatEmployeePerformanceCommissionPackageCardProjectEntity();
                            treatEmployeePerformanceCommissionPackageCardProjectEntity.EntityID = treatBillEntity.EntityID;
                            treatEmployeePerformanceCommissionPackageCardProjectEntity.TreatBillID = treatBillEntity.ID;
                            treatEmployeePerformanceCommissionPackageCardProjectEntity.TreatBillProjectID = newTreatBillProjectEntity.ID;
                            treatEmployeePerformanceCommissionPackageCardProjectEntity.ProjectID = newTreatBillProjectEntity.ProjectID;
                            treatEmployeePerformanceCommissionPackageCardProjectEntity.ProjectTreatHandlerID = refundTreatBillHandlerProjectEntity.ProjectTreatHandlerID;
                            treatEmployeePerformanceCommissionPackageCardProjectEntity.EmployeeID = refundTreatBillHandlerProjectEntity.EmployeeID;
                            treatEmployeePerformanceCommissionPackageCardProjectEntity.BillDate = treatBillEntity.BillDate;

                            BigDecimal performancePayRate = treatPerformanceEntity.PayRate == null ? BigDecimal.ZERO : treatPerformanceEntity.PayRate;
                            BigDecimal performanceCardRate = treatPerformanceEntity.CardRate == null ? BigDecimal.ZERO : treatPerformanceEntity.CardRate;
                            BigDecimal performanceCardLargessRate = treatPerformanceEntity.CardLargessRate == null ? BigDecimal.ZERO : treatPerformanceEntity.CardLargessRate;
                            BigDecimal performanceLargessRate = treatPerformanceEntity.LargessRate == null ? BigDecimal.ZERO : treatPerformanceEntity.LargessRate;
                            BigDecimal payRate = BigDecimal.ZERO;
                            BigDecimal cardRate = BigDecimal.ZERO;
                            BigDecimal cardLargessRate = BigDecimal.ZERO;
                            BigDecimal largessRate = BigDecimal.ZERO;
                            BigDecimal payFixed = BigDecimal.ZERO;
                            BigDecimal cardFixed = BigDecimal.ZERO;
                            BigDecimal cardLargessFixed = BigDecimal.ZERO;
                            BigDecimal largessFixed = BigDecimal.ZERO;
                            BigDecimal specialBenefit = BigDecimal.ZERO;
                            if (treatCommissionEntity != null) {
                                payRate = treatCommissionEntity.PayRate == null ? BigDecimal.ZERO : treatCommissionEntity.PayRate;
                                cardRate = treatCommissionEntity.CardRate == null ? BigDecimal.ZERO : treatCommissionEntity.CardRate;
                                cardLargessRate = treatCommissionEntity.CardLargessRate == null ? BigDecimal.ZERO : treatCommissionEntity.CardLargessRate;
                                largessRate = treatCommissionEntity.LargessRate == null ? BigDecimal.ZERO : treatCommissionEntity.LargessRate;
                                payFixed = treatCommissionEntity.PayFixed == null ? BigDecimal.ZERO : treatCommissionEntity.PayFixed;
                                cardFixed = treatCommissionEntity.CardFixed == null ? BigDecimal.ZERO : treatCommissionEntity.CardFixed;
                                cardLargessFixed = treatCommissionEntity.CardLargessFixed == null ? BigDecimal.ZERO : treatCommissionEntity.CardLargessFixed;
                                largessFixed = treatCommissionEntity.LargessFixed == null ? BigDecimal.ZERO : treatCommissionEntity.LargessFixed;
                                specialBenefit = treatCommissionEntity.SpecialBenefit == null ? BigDecimal.ZERO : treatCommissionEntity.SpecialBenefit;
                            }

                            if (treatEntityPerformanceProjectEntity.PayPerformance.compareTo(new BigDecimal(0)) > 0 && TotalPerformance.compareTo(BigDecimal.ZERO) > 0) {
                                treatEmployeePerformanceCommissionPackageCardProjectEntity.PayPerformance = treatEntityPerformanceProjectEntity.PayPerformance.multiply(performancePayRate).multiply(refundTreatBillHandlerProjectEntity.Scale).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardProjectEntity.PayRateCommission = treatEmployeePerformanceCommissionPackageCardProjectEntity.PayPerformance.multiply(payRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardProjectEntity.PayFixedCommission = payFixed.multiply(BigDecimal.valueOf(newTreatBillProjectEntity.Quantity)).multiply(treatEmployeePerformanceCommissionPackageCardProjectEntity.PayPerformance).divide(TotalPerformance, 2, BigDecimal.ROUND_HALF_UP);
                            } else {
                                treatEmployeePerformanceCommissionPackageCardProjectEntity.PayPerformance = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardProjectEntity.PayRateCommission = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardProjectEntity.PayFixedCommission = BigDecimal.ZERO;
                            }
                            if (treatEntityPerformanceProjectEntity.CardPerformance.compareTo(new BigDecimal(0)) > 0 && TotalPerformance.compareTo(BigDecimal.ZERO) > 0) {
                                treatEmployeePerformanceCommissionPackageCardProjectEntity.CardPerformance = treatEntityPerformanceProjectEntity.CardPerformance.multiply(performanceCardRate).multiply(refundTreatBillHandlerProjectEntity.Scale).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardProjectEntity.CardRateCommission = treatEmployeePerformanceCommissionPackageCardProjectEntity.CardPerformance.multiply(cardRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardProjectEntity.CardFixedCommission = cardFixed.multiply(BigDecimal.valueOf(newTreatBillProjectEntity.Quantity)).multiply(treatEmployeePerformanceCommissionPackageCardProjectEntity.CardPerformance).divide(TotalPerformance, 2, BigDecimal.ROUND_HALF_UP);
                            } else {
                                treatEmployeePerformanceCommissionPackageCardProjectEntity.CardPerformance = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardProjectEntity.CardRateCommission = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardProjectEntity.CardFixedCommission = BigDecimal.ZERO;
                            }
                            if (treatEntityPerformanceProjectEntity.CardLargessPerformance.compareTo(new BigDecimal(0)) > 0 && TotalPerformance.compareTo(BigDecimal.ZERO) > 0) {
                                treatEmployeePerformanceCommissionPackageCardProjectEntity.CardLargessPerformance = treatEntityPerformanceProjectEntity.CardLargessPerformance.multiply(performanceCardLargessRate).multiply(refundTreatBillHandlerProjectEntity.Scale).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardProjectEntity.CardLargessRateCommission = treatEmployeePerformanceCommissionPackageCardProjectEntity.CardLargessPerformance.multiply(cardLargessRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardProjectEntity.CardLargessFixedCommission = cardLargessFixed.multiply(BigDecimal.valueOf(newTreatBillProjectEntity.Quantity)).multiply(treatEmployeePerformanceCommissionPackageCardProjectEntity.CardLargessPerformance).divide(TotalPerformance, 2, BigDecimal.ROUND_HALF_UP);
                            } else {
                                treatEmployeePerformanceCommissionPackageCardProjectEntity.CardLargessPerformance = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardProjectEntity.CardLargessRateCommission = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardProjectEntity.CardLargessFixedCommission = BigDecimal.ZERO;
                            }
                            if (treatEntityPerformanceProjectEntity.LargessPerformance.compareTo(new BigDecimal(0)) > 0 && TotalPerformance.compareTo(BigDecimal.ZERO) > 0) {
                                treatEmployeePerformanceCommissionPackageCardProjectEntity.LargessPerformance = treatEntityPerformanceProjectEntity.LargessPerformance.multiply(performanceLargessRate).multiply(refundTreatBillHandlerProjectEntity.Scale).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardProjectEntity.LargessRateCommission = treatEmployeePerformanceCommissionPackageCardProjectEntity.LargessPerformance.multiply(largessRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardProjectEntity.LargessFixedCommission = largessFixed.multiply(BigDecimal.valueOf(newTreatBillProjectEntity.Quantity)).multiply(treatEmployeePerformanceCommissionPackageCardProjectEntity.LargessPerformance).divide(TotalPerformance, 2, BigDecimal.ROUND_HALF_UP);
                            } else {
                                treatEmployeePerformanceCommissionPackageCardProjectEntity.LargessPerformance = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardProjectEntity.LargessRateCommission = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardProjectEntity.LargessFixedCommission = BigDecimal.ZERO;
                            }
                            treatEmployeePerformanceCommissionPackageCardProjectEntity.SpecialBenefitCommission = specialBenefit.multiply(BigDecimal.valueOf(newTreatBillProjectEntity.Quantity)).multiply(refundTreatBillHandlerProjectEntity.Scale).setScale(2, BigDecimal.ROUND_HALF_UP);
                            ;

                            if (projectAccountEntity.PackageCardAccountID != null && projectAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                                treatEmployeePerformanceCommissionPackageCardProjectEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                                treatEmployeePerformanceCommissionMapper.createTreatEmployeePerformanceCommissionPackageCardProject(treatEmployeePerformanceCommissionPackageCardProjectEntity);
                            } else {
                                treatEmployeePerformanceCommissionMapper.createTreatEmployeePerformanceCommissionProject(treatEmployeePerformanceCommissionPackageCardProjectEntity);
                            }
                        }
                    }

                    if (isCalculateChannelPerformance) {
                        for (ChannelParentOutputForm parent : parents) {
                            TreatChannelPerformancePackageCardGoodEntity treatChannelPerformanceGoodEntity = new TreatChannelPerformancePackageCardGoodEntity();
                            treatChannelPerformanceGoodEntity.TreatBillID = treatBillEntity.ID;
                            treatChannelPerformanceGoodEntity.EntityID = treatBillEntity.EntityID;
                            treatChannelPerformanceGoodEntity.BillDate = treatBillEntity.BillDate;
                            treatChannelPerformanceGoodEntity.TreatBillGoodsID = newTreatBillProjectEntity.ID;
                            treatChannelPerformanceGoodEntity.GoodsID = newTreatBillProjectEntity.ProjectID;
                            treatChannelPerformanceGoodEntity.ChannelID = parent.ID;
                            treatChannelPerformanceGoodEntity.IsChannelIntroducer = false;
                            treatChannelPerformanceGoodEntity.PayPerformance = treatEntityPerformanceProjectEntity.PayPerformance;
                            treatChannelPerformanceGoodEntity.CardPerformance = treatEntityPerformanceProjectEntity.CardPerformance;
                            treatChannelPerformanceGoodEntity.CardLargessPerformance = treatEntityPerformanceProjectEntity.CardLargessPerformance;
                            treatChannelPerformanceGoodEntity.LargessPerformance = treatEntityPerformanceProjectEntity.LargessPerformance;
                            if (projectAccountEntity.PackageCardAccountID != null && projectAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                                treatChannelPerformanceGoodEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                                treatChannelPerformancePackageCardGoodEntities.add(treatChannelPerformanceGoodEntity);
                            } else {
                                treatChannelPerformanceGoodEntities.add(treatChannelPerformanceGoodEntity);
                            }
                            if (parent.IntroducerList.size() > 0) {
                                for (Integer integer : parent.IntroducerList) {
                                    TreatChannelPerformancePackageCardGoodEntity performanceGoodEntity = new TreatChannelPerformancePackageCardGoodEntity();
                                    performanceGoodEntity.TreatBillID = treatBillEntity.ID;
                                    performanceGoodEntity.EntityID = treatBillEntity.EntityID;
                                    performanceGoodEntity.BillDate = treatBillEntity.BillDate;
                                    performanceGoodEntity.TreatBillGoodsID = newTreatBillProjectEntity.ID;
                                    performanceGoodEntity.GoodsID = newTreatBillProjectEntity.ProjectID;
                                    performanceGoodEntity.ChannelID = integer;
                                    performanceGoodEntity.IsChannelIntroducer = true;
                                    performanceGoodEntity.PayPerformance = treatEntityPerformanceProjectEntity.PayPerformance;
                                    performanceGoodEntity.CardPerformance = treatEntityPerformanceProjectEntity.CardPerformance;
                                    performanceGoodEntity.CardLargessPerformance = treatEntityPerformanceProjectEntity.CardLargessPerformance;
                                    performanceGoodEntity.LargessPerformance = treatEntityPerformanceProjectEntity.LargessPerformance;
                                    if (projectAccountEntity.PackageCardAccountID != null && projectAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                                        performanceGoodEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                                        treatChannelPerformancePackageCardGoodEntities.add(performanceGoodEntity);
                                    } else {
                                        treatChannelPerformanceGoodEntities.add(performanceGoodEntity);
                                    }
                                }

                            }
                        }

                        for (ChannelEmployeePerformanceRateOutputForm employeePerformanceRateOutputForm : developer) {
                            TreatChannelEmployeePerformancePackageCardGoodsEntity packageCardGoodsEntity = new TreatChannelEmployeePerformancePackageCardGoodsEntity();
                            packageCardGoodsEntity.TreatBillID = treatBillEntity.ID;
                            packageCardGoodsEntity.EntityID = treatBillEntity.EntityID;
                            packageCardGoodsEntity.BillDate = treatBillEntity.BillDate;
                            packageCardGoodsEntity.TreatBillGoodsID = newTreatBillProjectEntity.ID;
                            packageCardGoodsEntity.GoodsID = projectAccountEntity.ProjectID;
                            packageCardGoodsEntity.ChannelID = cust.ChannelID;
                            packageCardGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                            packageCardGoodsEntity.PayPerformance = treatEntityPerformanceProjectEntity.PayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            packageCardGoodsEntity.CardPerformance = treatEntityPerformanceProjectEntity.CardPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            packageCardGoodsEntity.CardLargessPerformance = treatEntityPerformanceProjectEntity.CardLargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            packageCardGoodsEntity.LargessPerformance = treatEntityPerformanceProjectEntity.LargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            if (projectAccountEntity.PackageCardAccountID != null && projectAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                                packageCardGoodsEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                                treatDeveloperChannelPerformancePackageCardProject.add(packageCardGoodsEntity);
                            } else {
                                treatDeveloperChannelPerformanceProject.add(packageCardGoodsEntity);
                            }
                        }

                        for (ChannelEmployeePerformanceRateOutputForm employeePerformanceRateOutputForm : consultant) {
                            TreatChannelEmployeePerformancePackageCardGoodsEntity packageCardGoodsEntity = new TreatChannelEmployeePerformancePackageCardGoodsEntity();
                            packageCardGoodsEntity.TreatBillID = treatBillEntity.ID;
                            packageCardGoodsEntity.EntityID = treatBillEntity.EntityID;
                            packageCardGoodsEntity.BillDate = treatBillEntity.BillDate;
                            packageCardGoodsEntity.TreatBillGoodsID = newTreatBillProjectEntity.ID;
                            packageCardGoodsEntity.GoodsID = projectAccountEntity.ProjectID;
                            packageCardGoodsEntity.ChannelID = cust.ChannelID;
                            packageCardGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                            packageCardGoodsEntity.PayPerformance = treatEntityPerformanceProjectEntity.PayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            packageCardGoodsEntity.CardPerformance = treatEntityPerformanceProjectEntity.CardPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            packageCardGoodsEntity.CardLargessPerformance = treatEntityPerformanceProjectEntity.CardLargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            packageCardGoodsEntity.LargessPerformance = treatEntityPerformanceProjectEntity.LargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            if (projectAccountEntity.PackageCardAccountID != null && projectAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                                packageCardGoodsEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                                treatConsultantChannelPerformancePackageCardProject.add(packageCardGoodsEntity);
                            } else {
                                treatConsultantChannelPerformanceProject.add(packageCardGoodsEntity);
                            }
                        }
                    }

                    treatEntityPerformanceProjectEntity.PayPerformance = newTreatBillProjectEntity.PayAmount.multiply(treatEntityPerformance.PayRate == null ? BigDecimal.ZERO : treatEntityPerformance.PayRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                    treatEntityPerformanceProjectEntity.CardPerformance = newTreatBillProjectEntity.CardDeductionAmount.multiply(treatEntityPerformance.CardRate == null ? BigDecimal.ZERO : treatEntityPerformance.CardRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                    treatEntityPerformanceProjectEntity.CardLargessPerformance = newTreatBillProjectEntity.LargessCardDeductionAmount.multiply(treatEntityPerformance.CardLargessRate == null ? BigDecimal.ZERO : treatEntityPerformance.CardLargessRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                    treatEntityPerformanceProjectEntity.LargessPerformance = BigDecimal.ZERO;
                    if (newTreatBillProjectEntity.IsLargess) {
                        treatEntityPerformanceProjectEntity.LargessPerformance = refundTreatBillProjectEntity.TotalAmount.multiply(treatEntityPerformance.LargessRate == null ? BigDecimal.ZERO : treatEntityPerformance.LargessRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                    }
                    treatEntityPerformanceProjectEntity.Scale = new BigDecimal(100);
                    if (projectAccountEntity.PackageCardAccountID != null && projectAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                        treatEntityPerformanceMapper.createTreatEntityPerformancePackageCardProject(treatEntityPerformanceProjectEntity);
                    } else {
                        treatEntityPerformanceMapper.createTreatEntityPerformanceProject(treatEntityPerformanceProjectEntity);
                    }

                }

                if (treatChannelPerformanceGoodEntities.size() > 0) {
                    treatChannelPerformanceMapper.createTreatChannelPerformanceProject(treatChannelPerformanceGoodEntities);
                }
                if (treatChannelPerformancePackageCardGoodEntities.size() > 0) {
                    treatChannelPerformanceMapper.createTreatChannelPerformancePackageCardProject(treatChannelPerformancePackageCardGoodEntities);
                }

                //保存开发人员的渠道业绩

                if (treatDeveloperChannelPerformanceProject.size() > 0) {
                    treatChannelDeveloperPerformanceMapper.createTreatDeveloperChannelPerformanceProject(treatDeveloperChannelPerformanceProject);
                }


                //保存开发人员套餐卡渠道业绩

                if (treatDeveloperChannelPerformancePackageCardProject.size() > 0) {
                    treatChannelDeveloperPerformanceMapper.createTreatDeveloperChannelPerformancePackageCardProject(treatDeveloperChannelPerformancePackageCardProject);
                }


                //保存服务人员的渠道业绩

                if (treatConsultantChannelPerformanceProject.size() > 0) {
                    treatChannelConsultantPerformanceMapper.createTreatConsultantChannelPerformanceProject(treatConsultantChannelPerformanceProject);
                }


                //保存服务人员套餐卡渠道业绩

                if (treatConsultantChannelPerformancePackageCardProject.size() > 0) {
                    treatChannelConsultantPerformanceMapper.createTreatConsultantChannelPerformancePackageCardProject(treatConsultantChannelPerformancePackageCardProject);
                }
            }
            if (refundTreatGeneralCard != null && refundTreatGeneralCard.size() > 0) {
                //渠道业绩
                List<TreatChannelPerformanceGoodEntity> treatChannelPerformanceGoodEntities = new ArrayList<>();
                List<TreatChannelPerformancePackageCardGoodEntity> treatChannelPerformancePackageCardGoodEntities = new ArrayList<>();

                for (RefundTreatBillGeneralCardEntity refundTreatBillGeneralCardEntity : refundTreatGeneralCard) {
                    TreatBillEntity treatBill = treatBillMapper.getTreatBill(refundTreatBillGeneralCardEntity.TreatBillID);
                    if (treatBill.BillStatus.equals("30")) {
                        throw new RuntimeException("通用次卡订单已经被取消，请撤销申请");
                    }
                    RefundTreatBillGoodsOutputForm refundGeneralCardCount = refundTreatBillMapper.getRefundGeneralCardCount(refundTreatBillGeneralCardEntity.GeneralCardAccountID, refundTreatBillGeneralCardEntity.TreatBillGeneralCardID);

                    GeneralCardAccountEntity generalCardAccountEntity = generalCardAccountMapper.getGeneralCardAccountByAccountID(refundTreatBillGeneralCardEntity.GeneralCardAccountID);
                    TreatBillGeneralCardEntity oldTreatBillGeneralCardEntity = treatBillMapper.getTreatBillGeneralCardEntity(refundTreatBillGeneralCardEntity.TreatBillGeneralCardID);
                    BigDecimal PayAmount = BigDecimal.ZERO, CardDeductionAmount = BigDecimal.ZERO, LargessCardDeductionAmount = BigDecimal.ZERO, LargessAmount = BigDecimal.ZERO;
                    Integer Balance = oldTreatBillGeneralCardEntity.Quantity - refundGeneralCardCount.count;
                    if (Balance.equals(refundTreatBillGeneralCardEntity.Quantity)) {
                        if (oldTreatBillGeneralCardEntity.IsLargess) {
                            LargessAmount = refundTreatBillGeneralCardEntity.TotalAmount;
                        } else {
                            PayAmount = oldTreatBillGeneralCardEntity.PayAmount.multiply(new BigDecimal(refundGeneralCardCount.count)).divide(new BigDecimal(oldTreatBillGeneralCardEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            CardDeductionAmount = oldTreatBillGeneralCardEntity.CardDeductionAmount.multiply(new BigDecimal(refundGeneralCardCount.count)).divide(new BigDecimal(oldTreatBillGeneralCardEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            LargessCardDeductionAmount = refundGeneralCardCount.TotalAmount.subtract(PayAmount).subtract(CardDeductionAmount);

                            PayAmount = oldTreatBillGeneralCardEntity.PayAmount.subtract(PayAmount);
                            CardDeductionAmount = oldTreatBillGeneralCardEntity.CardDeductionAmount.subtract(CardDeductionAmount);
                            LargessCardDeductionAmount = oldTreatBillGeneralCardEntity.LargessCardDeductionAmount.subtract(LargessCardDeductionAmount);
                        }

                    } else {
                        if (oldTreatBillGeneralCardEntity.IsLargess) {
                            LargessAmount = refundTreatBillGeneralCardEntity.TotalAmount;
                        } else {
                            PayAmount = oldTreatBillGeneralCardEntity.PayAmount.multiply(new BigDecimal(refundTreatBillGeneralCardEntity.Quantity)).divide(new BigDecimal(oldTreatBillGeneralCardEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            CardDeductionAmount = oldTreatBillGeneralCardEntity.CardDeductionAmount.multiply(new BigDecimal(refundTreatBillGeneralCardEntity.Quantity)).divide(new BigDecimal(oldTreatBillGeneralCardEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            LargessCardDeductionAmount = refundTreatBillGeneralCardEntity.TotalAmount.subtract(PayAmount).subtract(CardDeductionAmount);
                        }
                    }
                    //更新账户
                    generalCardAccountEntity.Balance = generalCardAccountEntity.Balance + refundTreatBillGeneralCardEntity.CardTreatTimes;
                    generalCardAccountEntity.PayPerformanceBalanceAmount = generalCardAccountEntity.PayPerformanceBalanceAmount.add(PayAmount);
                    generalCardAccountEntity.CardPerformanceBalanceAmount = generalCardAccountEntity.CardPerformanceBalanceAmount.add(CardDeductionAmount);
                    generalCardAccountEntity.CardLargessPerformanceBalanceAmount = generalCardAccountEntity.CardLargessPerformanceBalanceAmount.add(LargessCardDeductionAmount);
                    generalCardAccountEntity.LargessPerformanceBalanceAmount = generalCardAccountEntity.LargessPerformanceBalanceAmount.add(LargessAmount);
                    generalCardAccountMapper.updateGeneralCardAccountBalance(generalCardAccountEntity);

                    BillPayAmount = BillPayAmount.add(PayAmount);
                    BillCardDeductionAmount = BillCardDeductionAmount.add(CardDeductionAmount);
                    BillLargessCardDeductionAmount = BillLargessCardDeductionAmount.add(LargessCardDeductionAmount);
                    //记录明细
                    TreatBillGeneralCardEntity newTreatBillGeneralCardEntity = new TreatBillGeneralCardEntity();
                    newTreatBillGeneralCardEntity.TreatBillID = treatBillEntity.ID;
                    newTreatBillGeneralCardEntity.GeneralCardAccountID = refundTreatBillGeneralCardEntity.GeneralCardAccountID;
                    newTreatBillGeneralCardEntity.ProjectID = refundTreatBillGeneralCardEntity.ProjectID;
                    newTreatBillGeneralCardEntity.Price = refundTreatBillGeneralCardEntity.Amount;
                    newTreatBillGeneralCardEntity.Quantity = refundTreatBillGeneralCardEntity.Quantity;
                    newTreatBillGeneralCardEntity.CardTreatTimes = refundTreatBillGeneralCardEntity.CardTreatTimes;
                    newTreatBillGeneralCardEntity.TotalAmount = refundTreatBillGeneralCardEntity.TotalAmount;
                    newTreatBillGeneralCardEntity.IsLargess = generalCardAccountEntity.IsLargess;
                    newTreatBillGeneralCardEntity.CardPreferentialAmount = BigDecimal.ZERO;
                    newTreatBillGeneralCardEntity.PayAmount = PayAmount;
                    newTreatBillGeneralCardEntity.CardDeductionAmount = CardDeductionAmount;
                    newTreatBillGeneralCardEntity.LargessCardDeductionAmount = LargessCardDeductionAmount;
                    if (refundTreatBillGeneralCardEntity.PackageCardAccountID != null && refundTreatBillGeneralCardEntity.PackageCardAccountID > 0) {
                        newTreatBillGeneralCardEntity.PackageCardAccountID = refundTreatBillGeneralCardEntity.PackageCardAccountID;
                        newTreatBillGeneralCardEntity.PackageCardID = refundTreatBillGeneralCardEntity.PackageCardID;
                    }
                    treatBillMapper.createTreatBillGeneralCard(newTreatBillGeneralCardEntity);

                    TreatPerformanceEntity treatEntityPerformance = treatEntityPerformanceMapper.getTreatGeneralCardEntityPerformance(treatBillEntity.EntityID, newTreatBillGeneralCardEntity.ProjectID, generalCardAccountEntity.GeneralCardID);

                    //记录门店业绩
                    TreatEntityPerformancePackageCardGeneralCardEntity treatEntityPerformanceGeneralCardEntity = new TreatEntityPerformancePackageCardGeneralCardEntity();

                    treatEntityPerformanceGeneralCardEntity.EntityID = treatBillEntity.EntityID;
                    treatEntityPerformanceGeneralCardEntity.TreatBillID = treatBillEntity.ID;
                    treatEntityPerformanceGeneralCardEntity.TreatBillGeneralCardID = newTreatBillGeneralCardEntity.ID;
                    treatEntityPerformanceGeneralCardEntity.GeneralCardID = generalCardAccountEntity.GeneralCardID;
                    treatEntityPerformanceGeneralCardEntity.ProjectID = newTreatBillGeneralCardEntity.ProjectID;
                    treatEntityPerformanceGeneralCardEntity.BillDate = treatBillEntity.BillDate;
                    treatEntityPerformanceGeneralCardEntity.PayPerformance = newTreatBillGeneralCardEntity.PayAmount;
                    treatEntityPerformanceGeneralCardEntity.CardPerformance = newTreatBillGeneralCardEntity.CardDeductionAmount;
                    treatEntityPerformanceGeneralCardEntity.CardLargessPerformance = newTreatBillGeneralCardEntity.LargessCardDeductionAmount;
                    treatEntityPerformanceGeneralCardEntity.LargessPerformance = BigDecimal.ZERO;
                    if (newTreatBillGeneralCardEntity.IsLargess) {
                        treatEntityPerformanceGeneralCardEntity.LargessPerformance = refundTreatBillGeneralCardEntity.TotalAmount;
                    }

                    PackageCardAccountEntity packageCardAccountEntity = new PackageCardAccountEntity();
                    if (generalCardAccountEntity.PackageCardAccountID != null && generalCardAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                        packageCardAccountEntity = packageCardAccountMapper.getPackageCardAccount(generalCardAccountEntity.PackageCardAccountID);
                        treatEntityPerformanceGeneralCardEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                    }
                    List<RefundTreatBillHandlerGeneralCardEntity> refundTreatBillHandlerGeneralCard = refundTreatBillMapper.getRefundTreatBillHandlerGeneralCard(refundTreatBillGeneralCardEntity.ID);
                    //记录经手人
                    if (refundTreatBillHandlerGeneralCard != null && refundTreatBillHandlerGeneralCard.size() > 0) {
                        for (RefundTreatBillHandlerGeneralCardEntity refundTreatBillHandlerGeneralCardEntity : refundTreatBillHandlerGeneralCard) {
                            TreatBillHandlerGeneralCardEntity treatBillHandlerGeneralCardEntity = new TreatBillHandlerGeneralCardEntity();
                            treatBillHandlerGeneralCardEntity.TreatBillGeneralCardID = newTreatBillGeneralCardEntity.ID;
                            treatBillHandlerGeneralCardEntity.GeneralCardTreatHandlerID = refundTreatBillHandlerGeneralCardEntity.GeneralCardTreatHandlerID;
                            treatBillHandlerGeneralCardEntity.EmployeeID = refundTreatBillHandlerGeneralCardEntity.EmployeeID;
                            treatBillHandlerGeneralCardEntity.Scale = refundTreatBillHandlerGeneralCardEntity.Scale;
                            treatBillHandlerGeneralCardEntity.IsCalculatePassengerFlow = true;
                            treatBillMapper.createTreatBillHandlerGeneralCard(treatBillHandlerGeneralCardEntity);
                            BigDecimal TotalPerformance = treatEntityPerformanceGeneralCardEntity.PayPerformance.add(treatEntityPerformanceGeneralCardEntity.CardPerformance).add(treatEntityPerformanceGeneralCardEntity.CardLargessPerformance).add(treatEntityPerformanceGeneralCardEntity.LargessPerformance);
                            //记录员工业绩和提佣
                            TreatPerformanceEntity treatPerformanceEntity = treatEmployeePerformanceCommissionMapper.getTreatGeneralCardPerformance(treatBillEntity.EntityID, refundTreatBillHandlerGeneralCardEntity.EmployeeID, refundTreatBillHandlerGeneralCardEntity.GeneralCardTreatHandlerID, treatEntityPerformanceGeneralCardEntity.ProjectID, treatEntityPerformanceGeneralCardEntity.GeneralCardID);
                            TreatCommissionEntity treatCommissionEntity = treatEmployeePerformanceCommissionMapper.getTreatGeneralCardCommission(treatBillEntity.EntityID, refundTreatBillHandlerGeneralCardEntity.EmployeeID, refundTreatBillHandlerGeneralCardEntity.GeneralCardTreatHandlerID, treatEntityPerformanceGeneralCardEntity.ProjectID, treatEntityPerformanceGeneralCardEntity.GeneralCardID);

                            BigDecimal performancePayRate = treatPerformanceEntity.PayRate == null ? BigDecimal.ZERO : treatPerformanceEntity.PayRate;
                            BigDecimal performanceCardRate = treatPerformanceEntity.CardRate == null ? BigDecimal.ZERO : treatPerformanceEntity.CardRate;
                            BigDecimal performanceCardLargessRate = treatPerformanceEntity.CardLargessRate == null ? BigDecimal.ZERO : treatPerformanceEntity.CardLargessRate;
                            BigDecimal performanceLargessRate = treatPerformanceEntity.LargessRate == null ? BigDecimal.ZERO : treatPerformanceEntity.LargessRate;
                            BigDecimal payRate = BigDecimal.ZERO;
                            BigDecimal cardRate = BigDecimal.ZERO;
                            BigDecimal cardLargessRate = BigDecimal.ZERO;
                            BigDecimal largessRate = BigDecimal.ZERO;
                            BigDecimal payFixed = BigDecimal.ZERO;
                            BigDecimal cardFixed = BigDecimal.ZERO;
                            BigDecimal cardLargessFixed = BigDecimal.ZERO;
                            BigDecimal largessFixed = BigDecimal.ZERO;
                            BigDecimal specialBenefit = BigDecimal.ZERO;
                            if (treatCommissionEntity != null) {
                                payRate = treatCommissionEntity.PayRate == null ? BigDecimal.ZERO : treatCommissionEntity.PayRate;
                                cardRate = treatCommissionEntity.CardRate == null ? BigDecimal.ZERO : treatCommissionEntity.CardRate;
                                cardLargessRate = treatCommissionEntity.CardLargessRate == null ? BigDecimal.ZERO : treatCommissionEntity.CardLargessRate;
                                largessRate = treatCommissionEntity.LargessRate == null ? BigDecimal.ZERO : treatCommissionEntity.LargessRate;
                                payFixed = treatCommissionEntity.PayFixed == null ? BigDecimal.ZERO : treatCommissionEntity.PayFixed;
                                cardFixed = treatCommissionEntity.CardFixed == null ? BigDecimal.ZERO : treatCommissionEntity.CardFixed;
                                cardLargessFixed = treatCommissionEntity.CardLargessFixed == null ? BigDecimal.ZERO : treatCommissionEntity.CardLargessFixed;
                                largessFixed = treatCommissionEntity.LargessFixed == null ? BigDecimal.ZERO : treatCommissionEntity.LargessFixed;
                                specialBenefit = treatCommissionEntity.SpecialBenefit == null ? BigDecimal.ZERO : treatCommissionEntity.SpecialBenefit;
                            }
                            TreatEmployeePerformanceCommissionPackageCardGeneralCardEntity treatEmployeePerformanceCommissionPackageCardGeneralCardEntity = new TreatEmployeePerformanceCommissionPackageCardGeneralCardEntity();
                            treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.EntityID = treatBillEntity.EntityID;
                            treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.TreatBillID = treatBillEntity.ID;
                            treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.TreatBillGeneralCardID = newTreatBillGeneralCardEntity.ID;
                            treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.GeneralCardID = generalCardAccountEntity.GeneralCardID;
                            treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.ProjectID = newTreatBillGeneralCardEntity.ProjectID;
                            treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.GeneralCardTreatHandlerID = refundTreatBillHandlerGeneralCardEntity.GeneralCardTreatHandlerID;
                            treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.EmployeeID = refundTreatBillHandlerGeneralCardEntity.EmployeeID;
                            treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.BillDate = treatBillEntity.BillDate;
                            if (treatEntityPerformanceGeneralCardEntity.PayPerformance.compareTo(new BigDecimal(0)) > 0 && TotalPerformance.compareTo(BigDecimal.ZERO) > 0) {
                                treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.PayPerformance = treatEntityPerformanceGeneralCardEntity.PayPerformance.multiply(performancePayRate).multiply(treatBillHandlerGeneralCardEntity.Scale).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.PayRateCommission = treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.PayPerformance.multiply(payRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.PayFixedCommission = payFixed.multiply(BigDecimal.valueOf(newTreatBillGeneralCardEntity.Quantity)).multiply(treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.PayPerformance).divide(TotalPerformance, 2, BigDecimal.ROUND_HALF_UP);
                            } else {
                                treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.PayPerformance = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.PayRateCommission = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.PayFixedCommission = BigDecimal.ZERO;
                            }
                            if (treatEntityPerformanceGeneralCardEntity.CardPerformance.compareTo(new BigDecimal(0)) > 0 && TotalPerformance.compareTo(BigDecimal.ZERO) > 0) {
                                treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.CardPerformance = treatEntityPerformanceGeneralCardEntity.CardPerformance.multiply(performanceCardRate).multiply(treatBillHandlerGeneralCardEntity.Scale).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.CardRateCommission = treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.CardPerformance.multiply(cardRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.CardFixedCommission = cardFixed.multiply(BigDecimal.valueOf(newTreatBillGeneralCardEntity.Quantity)).multiply(treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.CardPerformance).divide(TotalPerformance, 2, BigDecimal.ROUND_HALF_UP);
                            } else {
                                treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.CardPerformance = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.CardRateCommission = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.CardFixedCommission = BigDecimal.ZERO;
                            }
                            if (treatEntityPerformanceGeneralCardEntity.CardLargessPerformance.compareTo(new BigDecimal(0)) > 0 && TotalPerformance.compareTo(BigDecimal.ZERO) > 0) {
                                treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.CardLargessPerformance = treatEntityPerformanceGeneralCardEntity.CardLargessPerformance.multiply(performanceCardLargessRate).multiply(treatBillHandlerGeneralCardEntity.Scale).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.CardLargessRateCommission = treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.CardLargessPerformance.multiply(cardLargessRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.CardLargessFixedCommission = cardLargessFixed.multiply(BigDecimal.valueOf(newTreatBillGeneralCardEntity.Quantity)).multiply(treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.CardLargessPerformance).divide(TotalPerformance, 2, BigDecimal.ROUND_HALF_UP);
                            } else {
                                treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.CardLargessPerformance = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.CardLargessRateCommission = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.CardLargessFixedCommission = BigDecimal.ZERO;
                            }
                            if (treatEntityPerformanceGeneralCardEntity.LargessPerformance.compareTo(new BigDecimal(0)) > 0 && TotalPerformance.compareTo(BigDecimal.ZERO) > 0) {
                                treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.LargessPerformance = treatEntityPerformanceGeneralCardEntity.LargessPerformance.multiply(performanceLargessRate).multiply(treatBillHandlerGeneralCardEntity.Scale).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.LargessRateCommission = treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.LargessPerformance.multiply(largessRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.LargessFixedCommission = largessFixed.multiply(BigDecimal.valueOf(newTreatBillGeneralCardEntity.Quantity)).multiply(treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.LargessPerformance).divide(TotalPerformance, 2, BigDecimal.ROUND_HALF_UP);
                            } else {
                                treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.LargessPerformance = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.LargessRateCommission = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.LargessFixedCommission = BigDecimal.ZERO;
                            }
                            treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.SpecialBenefitCommission = specialBenefit.multiply(BigDecimal.valueOf(newTreatBillGeneralCardEntity.Quantity)).multiply(treatBillHandlerGeneralCardEntity.Scale).setScale(2, BigDecimal.ROUND_HALF_UP);
                            ;

                            if (generalCardAccountEntity.PackageCardAccountID != null && generalCardAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                                treatEmployeePerformanceCommissionPackageCardGeneralCardEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                                treatEmployeePerformanceCommissionMapper.createTreatEmployeePerformanceCommissionPackageCardGeneralCard(treatEmployeePerformanceCommissionPackageCardGeneralCardEntity);
                            } else {
                                treatEmployeePerformanceCommissionMapper.createTreatEmployeePerformanceCommissionGeneralCard(treatEmployeePerformanceCommissionPackageCardGeneralCardEntity);
                            }
                        }
                    }

                    if (isCalculateChannelPerformance) {
                        for (ChannelParentOutputForm parent : parents) {
                            TreatChannelPerformancePackageCardGoodEntity treatChannelPerformanceGoodEntity = new TreatChannelPerformancePackageCardGoodEntity();
                            treatChannelPerformanceGoodEntity.TreatBillID = treatBillEntity.ID;
                            treatChannelPerformanceGoodEntity.EntityID = treatBillEntity.EntityID;
                            treatChannelPerformanceGoodEntity.BillDate = treatBillEntity.BillDate;
                            treatChannelPerformanceGoodEntity.TreatBillGoodsID = newTreatBillGeneralCardEntity.ID;
                            treatChannelPerformanceGoodEntity.CardID = generalCardAccountEntity.GeneralCardID;
                            treatChannelPerformanceGoodEntity.GoodsID = newTreatBillGeneralCardEntity.ProjectID;
                            treatChannelPerformanceGoodEntity.ChannelID = parent.ID;
                            treatChannelPerformanceGoodEntity.IsChannelIntroducer = false;
                            treatChannelPerformanceGoodEntity.PayPerformance = treatEntityPerformanceGeneralCardEntity.PayPerformance;
                            treatChannelPerformanceGoodEntity.CardPerformance = treatEntityPerformanceGeneralCardEntity.CardPerformance;
                            treatChannelPerformanceGoodEntity.CardLargessPerformance = treatEntityPerformanceGeneralCardEntity.CardLargessPerformance;
                            treatChannelPerformanceGoodEntity.LargessPerformance = treatEntityPerformanceGeneralCardEntity.LargessPerformance;
                            if (generalCardAccountEntity.PackageCardAccountID != null && generalCardAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                                treatChannelPerformanceGoodEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                                treatChannelPerformancePackageCardGoodEntities.add(treatChannelPerformanceGoodEntity);
                            } else {
                                treatChannelPerformanceGoodEntities.add(treatChannelPerformanceGoodEntity);
                            }
                            if (parent.IntroducerList.size() > 0) {
                                for (Integer integer : parent.IntroducerList) {
                                    TreatChannelPerformancePackageCardGoodEntity performanceGoodEntity = new TreatChannelPerformancePackageCardGoodEntity();
                                    performanceGoodEntity.TreatBillID = treatBillEntity.ID;
                                    performanceGoodEntity.EntityID = treatBillEntity.EntityID;
                                    performanceGoodEntity.BillDate = treatBillEntity.BillDate;
                                    performanceGoodEntity.TreatBillGoodsID = newTreatBillGeneralCardEntity.ID;
                                    performanceGoodEntity.CardID = generalCardAccountEntity.GeneralCardID;
                                    performanceGoodEntity.GoodsID = newTreatBillGeneralCardEntity.ProjectID;
                                    performanceGoodEntity.ChannelID = integer;
                                    performanceGoodEntity.IsChannelIntroducer = true;
                                    performanceGoodEntity.PayPerformance = treatEntityPerformanceGeneralCardEntity.PayPerformance;
                                    performanceGoodEntity.CardPerformance = treatEntityPerformanceGeneralCardEntity.CardPerformance;
                                    performanceGoodEntity.CardLargessPerformance = treatEntityPerformanceGeneralCardEntity.CardLargessPerformance;
                                    performanceGoodEntity.LargessPerformance = treatEntityPerformanceGeneralCardEntity.LargessPerformance;
                                    if (generalCardAccountEntity.PackageCardAccountID != null && generalCardAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                                        performanceGoodEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                                        treatChannelPerformancePackageCardGoodEntities.add(performanceGoodEntity);
                                    } else {
                                        treatChannelPerformanceGoodEntities.add(performanceGoodEntity);
                                    }
                                }

                            }
                        }

                        for (ChannelEmployeePerformanceRateOutputForm employeePerformanceRateOutputForm : developer) {
                            TreatChannelEmployeePerformancePackageCardGoodsEntity packageCardGoodsEntity = new TreatChannelEmployeePerformancePackageCardGoodsEntity();
                            packageCardGoodsEntity.TreatBillID = treatBillEntity.ID;
                            packageCardGoodsEntity.EntityID = treatBillEntity.EntityID;
                            packageCardGoodsEntity.BillDate = treatBillEntity.BillDate;
                            packageCardGoodsEntity.TreatBillGoodsID = newTreatBillGeneralCardEntity.ID;
                            packageCardGoodsEntity.CardID = generalCardAccountEntity.GeneralCardID;
                            packageCardGoodsEntity.GoodsID = newTreatBillGeneralCardEntity.ProjectID;
                            packageCardGoodsEntity.ChannelID = cust.ChannelID;
                            packageCardGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                            packageCardGoodsEntity.PayPerformance = treatEntityPerformanceGeneralCardEntity.PayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            packageCardGoodsEntity.CardPerformance = treatEntityPerformanceGeneralCardEntity.CardPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            packageCardGoodsEntity.CardLargessPerformance = treatEntityPerformanceGeneralCardEntity.CardLargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            packageCardGoodsEntity.LargessPerformance = treatEntityPerformanceGeneralCardEntity.LargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            if (generalCardAccountEntity.PackageCardAccountID != null && generalCardAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                                packageCardGoodsEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                                treatDeveloperChannelPerformancePackageCardGeneralCard.add(packageCardGoodsEntity);
                            } else {
                                treatDeveloperChannelPerformanceGeneralCard.add(packageCardGoodsEntity);
                            }
                        }

                        for (ChannelEmployeePerformanceRateOutputForm employeePerformanceRateOutputForm : consultant) {
                            TreatChannelEmployeePerformancePackageCardGoodsEntity packageCardGoodsEntity = new TreatChannelEmployeePerformancePackageCardGoodsEntity();
                            packageCardGoodsEntity.TreatBillID = treatBillEntity.ID;
                            packageCardGoodsEntity.EntityID = treatBillEntity.EntityID;
                            packageCardGoodsEntity.BillDate = treatBillEntity.BillDate;
                            packageCardGoodsEntity.TreatBillGoodsID = newTreatBillGeneralCardEntity.ID;
                            packageCardGoodsEntity.CardID = generalCardAccountEntity.GeneralCardID;
                            packageCardGoodsEntity.GoodsID = newTreatBillGeneralCardEntity.ProjectID;
                            packageCardGoodsEntity.ChannelID = cust.ChannelID;
                            packageCardGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                            packageCardGoodsEntity.PayPerformance = treatEntityPerformanceGeneralCardEntity.PayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            packageCardGoodsEntity.CardPerformance = treatEntityPerformanceGeneralCardEntity.CardPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            packageCardGoodsEntity.CardLargessPerformance = treatEntityPerformanceGeneralCardEntity.CardLargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            packageCardGoodsEntity.LargessPerformance = treatEntityPerformanceGeneralCardEntity.LargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            if (generalCardAccountEntity.PackageCardAccountID != null && generalCardAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                                packageCardGoodsEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                                treatConsultantChannelPerformancePackageCardGeneralCard.add(packageCardGoodsEntity);
                            } else {
                                treatConsultantChannelPerformanceGeneralCard.add(packageCardGoodsEntity);
                            }
                        }
                    }

                    treatEntityPerformanceGeneralCardEntity.PayPerformance = newTreatBillGeneralCardEntity.PayAmount.multiply(treatEntityPerformance.PayRate == null ? BigDecimal.ZERO : treatEntityPerformance.PayRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                    treatEntityPerformanceGeneralCardEntity.CardPerformance = newTreatBillGeneralCardEntity.CardDeductionAmount.multiply(treatEntityPerformance.CardRate == null ? BigDecimal.ZERO : treatEntityPerformance.CardRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                    treatEntityPerformanceGeneralCardEntity.CardLargessPerformance = newTreatBillGeneralCardEntity.LargessCardDeductionAmount.multiply(treatEntityPerformance.CardLargessRate == null ? BigDecimal.ZERO : treatEntityPerformance.CardLargessRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                    treatEntityPerformanceGeneralCardEntity.LargessPerformance = BigDecimal.ZERO;
                    if (newTreatBillGeneralCardEntity.IsLargess) {
                        treatEntityPerformanceGeneralCardEntity.LargessPerformance = refundTreatBillGeneralCardEntity.TotalAmount.multiply(treatEntityPerformance.LargessRate == null ? BigDecimal.ZERO : treatEntityPerformance.LargessRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                    }
                    treatEntityPerformanceGeneralCardEntity.Scale = new BigDecimal(100);
                    if (generalCardAccountEntity.PackageCardAccountID != null && generalCardAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                        treatEntityPerformanceMapper.createTreatEntityPerformancePackageCardGeneralCard(treatEntityPerformanceGeneralCardEntity);
                    } else {
                        treatEntityPerformanceMapper.createTreatEntityPerformanceGeneralCard(treatEntityPerformanceGeneralCardEntity);
                    }
                }

                if (treatChannelPerformanceGoodEntities.size() > 0) {
                    treatChannelPerformanceMapper.createTreatChannelPerformanceGeneralCard(treatChannelPerformanceGoodEntities);
                }
                if (treatChannelPerformancePackageCardGoodEntities.size() > 0) {
                    treatChannelPerformanceMapper.createTreatChannelPerformancePackageCardGeneralCard(treatChannelPerformancePackageCardGoodEntities);
                }

                //保存开发人员的渠道业绩

                if (treatDeveloperChannelPerformanceGeneralCard.size() > 0) {
                    treatChannelDeveloperPerformanceMapper.createTreatDeveloperChannelPerformanceGeneralCard(treatDeveloperChannelPerformanceGeneralCard);
                }


                //保存开发人员套餐卡渠道业绩

                if (treatDeveloperChannelPerformancePackageCardGeneralCard.size() > 0) {
                    treatChannelDeveloperPerformanceMapper.createTreatDeveloperChannelPerformancePackageCardGeneralCard(treatDeveloperChannelPerformancePackageCardGeneralCard);
                }


                //保存服务人员的渠道业绩

                if (treatConsultantChannelPerformanceGeneralCard.size() > 0) {
                    treatChannelConsultantPerformanceMapper.createTreatConsultantChannelPerformanceGeneralCard(treatConsultantChannelPerformanceGeneralCard);
                }


                //保存服务人员套餐卡渠道业绩

                if (treatConsultantChannelPerformancePackageCardGeneralCard.size() > 0) {
                    treatChannelConsultantPerformanceMapper.createTreatConsultantChannelPerformancePackageCardGeneralCard(treatConsultantChannelPerformancePackageCardGeneralCard);
                }
            }
            //时效卡
            if (refundTreatTimeCard != null && refundTreatTimeCard.size() > 0) {
                //渠道业绩
                List<TreatChannelPerformanceGoodEntity> treatChannelPerformanceGoodEntities = new ArrayList<>();
                List<TreatChannelPerformancePackageCardGoodEntity> treatChannelPerformancePackageCardGoodEntities = new ArrayList<>();

                for (RefundTreatBillTimeCardEntity refundTreatBillTimeCardEntity : refundTreatTimeCard) {
                    TreatBillEntity treatBill = treatBillMapper.getTreatBill(refundTreatBillTimeCardEntity.TreatBillID);
                    if (treatBill.BillStatus.equals("30")) {
                        throw new RuntimeException("时效卡订单已经被取消，请撤销申请");
                    }
                    RefundTreatBillGoodsOutputForm refundTimeCardCount = refundTreatBillMapper.getRefundTimeCardCount(refundTreatBillTimeCardEntity.TimeCardAccountID, refundTreatBillTimeCardEntity.TreatBillTimeCardID);

                    TimeCardAccountEntity timeCardAccountEntity = timeCardAccountMapper.getTimeCardAccountByAccountID(refundTreatBillTimeCardEntity.TimeCardAccountID);
                    TreatBillTimeCardEntity oldTreatBillTimeCardEntity = treatBillMapper.getTreatBillTimeCardEntity(refundTreatBillTimeCardEntity.TreatBillTimeCardID);
                    BigDecimal PayAmount = BigDecimal.ZERO, CardDeductionAmount = BigDecimal.ZERO, LargessCardDeductionAmount = BigDecimal.ZERO, LargessAmount = BigDecimal.ZERO;
                    Integer Balance = oldTreatBillTimeCardEntity.Quantity - refundTimeCardCount.count;
                    if (Balance.equals(refundTreatBillTimeCardEntity.Quantity)) {
                        if (oldTreatBillTimeCardEntity.IsLargess) {
                            LargessAmount = refundTreatBillTimeCardEntity.TotalAmount;
                        } else {
                            PayAmount = oldTreatBillTimeCardEntity.PayAmount.multiply(new BigDecimal(refundTimeCardCount.count)).divide(new BigDecimal(oldTreatBillTimeCardEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            CardDeductionAmount = oldTreatBillTimeCardEntity.CardDeductionAmount.multiply(new BigDecimal(refundTimeCardCount.count)).divide(new BigDecimal(oldTreatBillTimeCardEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            LargessCardDeductionAmount = refundTimeCardCount.TotalAmount.subtract(PayAmount).subtract(CardDeductionAmount);

                            PayAmount = oldTreatBillTimeCardEntity.PayAmount.subtract(PayAmount);
                            CardDeductionAmount = oldTreatBillTimeCardEntity.CardDeductionAmount.subtract(CardDeductionAmount);
                            LargessCardDeductionAmount = oldTreatBillTimeCardEntity.LargessCardDeductionAmount.subtract(LargessCardDeductionAmount);
                        }

                    } else {
                        if (oldTreatBillTimeCardEntity.IsLargess) {
                            LargessAmount = refundTreatBillTimeCardEntity.TotalAmount;
                        } else {
                            PayAmount = oldTreatBillTimeCardEntity.PayAmount.multiply(new BigDecimal(refundTreatBillTimeCardEntity.Quantity)).divide(new BigDecimal(oldTreatBillTimeCardEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            CardDeductionAmount = oldTreatBillTimeCardEntity.CardDeductionAmount.multiply(new BigDecimal(refundTreatBillTimeCardEntity.Quantity)).divide(new BigDecimal(oldTreatBillTimeCardEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                            LargessCardDeductionAmount = refundTreatBillTimeCardEntity.TotalAmount.subtract(PayAmount).subtract(CardDeductionAmount);
                        }
                    }

                    if (refundTreatBillTimeCardEntity.TotalAmount.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal PerformanceTimes;
                        PerformanceTimes = refundTreatBillTimeCardEntity.TotalAmount.divide(timeCardAccountEntity.TotalAmount.divide(BigDecimal.valueOf(timeCardAccountEntity.PerformanceTimes), 2, BigDecimal.ROUND_HALF_UP), 2, BigDecimal.ROUND_HALF_UP);
                        if (PerformanceTimes.compareTo(BigDecimal.valueOf(1)) < 0 && PerformanceTimes.compareTo(BigDecimal.ZERO) > 0) {
                            PerformanceTimes = BigDecimal.valueOf(1);
                        }
                        timeCardAccountEntity.PerformanceBalance = timeCardAccountEntity.PerformanceBalance + PerformanceTimes.intValue();
                    }

                    //更新账户
                    //timeCardAccountEntity.PerformanceBalance = timeCardAccountEntity.PerformanceBalance + refundTreatBillTimeCardEntity.Quantity;
                    timeCardAccountEntity.PayPerformanceBalanceAmount = timeCardAccountEntity.PayPerformanceBalanceAmount.add(PayAmount);
                    timeCardAccountEntity.CardPerformanceBalanceAmount = timeCardAccountEntity.CardPerformanceBalanceAmount.add(CardDeductionAmount);
                    timeCardAccountEntity.CardLargessPerformanceBalanceAmount = timeCardAccountEntity.CardLargessPerformanceBalanceAmount.add(LargessCardDeductionAmount);
                    timeCardAccountEntity.LargessPerformanceBalanceAmount = timeCardAccountEntity.LargessPerformanceBalanceAmount.add(LargessAmount);
                    timeCardAccountMapper.updateTimeCardAccount(timeCardAccountEntity);

                    BillPayAmount = BillPayAmount.add(PayAmount);
                    BillCardDeductionAmount = BillCardDeductionAmount.add(CardDeductionAmount);
                    BillLargessCardDeductionAmount = BillLargessCardDeductionAmount.add(LargessCardDeductionAmount);

                    //记录明细
                    TreatBillTimeCardEntity treatBillTimeCardEntity = new TreatBillTimeCardEntity();
                    treatBillTimeCardEntity.TreatBillID = treatBillEntity.ID;
                    treatBillTimeCardEntity.TimeCardAccountID = refundTreatBillTimeCardEntity.TimeCardAccountID;
                    treatBillTimeCardEntity.ProjectID = refundTreatBillTimeCardEntity.ProjectID;
                    treatBillTimeCardEntity.Quantity = refundTreatBillTimeCardEntity.Quantity;
                    treatBillTimeCardEntity.Price = refundTreatBillTimeCardEntity.Amount;
                    treatBillTimeCardEntity.TotalAmount = refundTreatBillTimeCardEntity.TotalAmount;
                    treatBillTimeCardEntity.IsLargess = timeCardAccountEntity.IsLargess;
                    treatBillTimeCardEntity.CardPreferentialAmount = BigDecimal.ZERO;
                    treatBillTimeCardEntity.PayAmount = PayAmount;
                    treatBillTimeCardEntity.CardDeductionAmount = CardDeductionAmount;
                    treatBillTimeCardEntity.LargessCardDeductionAmount = LargessCardDeductionAmount;
                    if (refundTreatBillTimeCardEntity.PackageCardAccountID != null && refundTreatBillTimeCardEntity.PackageCardAccountID > 0) {
                        treatBillTimeCardEntity.PackageCardAccountID = refundTreatBillTimeCardEntity.PackageCardAccountID;
                        treatBillTimeCardEntity.PackageCardID = refundTreatBillTimeCardEntity.PackageCardID;
                    }
                    treatBillMapper.createTreatBillTimeCard(treatBillTimeCardEntity);

                    TreatPerformanceEntity treatTimeCardEntityPerformance = treatEntityPerformanceMapper.getTreatTimeCardEntityPerformance(treatBillEntity.EntityID, refundTreatBillTimeCardEntity.ProjectID, timeCardAccountEntity.TimeCardID);
                    //记录门店业绩
                    TreatEntityPerformancePackageCardTimeCardEntity treatEntityPerformanceTimeCardEntity = new TreatEntityPerformancePackageCardTimeCardEntity();

                    treatEntityPerformanceTimeCardEntity.EntityID = treatBillEntity.EntityID;
                    treatEntityPerformanceTimeCardEntity.TreatBillID = treatBillEntity.ID;
                    treatEntityPerformanceTimeCardEntity.TreatBillTimeCardID = treatBillTimeCardEntity.ID;
                    treatEntityPerformanceTimeCardEntity.TimeCardID = timeCardAccountEntity.TimeCardID;
                    treatEntityPerformanceTimeCardEntity.ProjectID = treatBillTimeCardEntity.ProjectID;
                    treatEntityPerformanceTimeCardEntity.BillDate = treatBillEntity.BillDate;
                    treatEntityPerformanceTimeCardEntity.PayPerformance = treatBillTimeCardEntity.PayAmount;
                    treatEntityPerformanceTimeCardEntity.CardPerformance = treatBillTimeCardEntity.CardDeductionAmount;
                    treatEntityPerformanceTimeCardEntity.CardLargessPerformance = treatBillTimeCardEntity.LargessCardDeductionAmount;
                    treatEntityPerformanceTimeCardEntity.LargessPerformance = BigDecimal.ZERO;
                    if (treatBillTimeCardEntity.IsLargess) {
                        treatEntityPerformanceTimeCardEntity.LargessPerformance = refundTreatBillTimeCardEntity.TotalAmount;
                    }
                    PackageCardAccountEntity packageCardAccountEntity = new PackageCardAccountEntity();
                    if (timeCardAccountEntity.PackageCardAccountID != null && timeCardAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                        packageCardAccountEntity = packageCardAccountMapper.getPackageCardAccount(timeCardAccountEntity.PackageCardAccountID);
                        treatEntityPerformanceTimeCardEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                    }

                    List<RefundTreatBillHandlerTimeCardEntity> refundTreatBillHandlerTimeCard = refundTreatBillMapper.getRefundTreatBillHandlerTimeCard(refundTreatBillTimeCardEntity.ID);
                    //记录经手人
                    if (refundTreatBillHandlerTimeCard != null && refundTreatBillHandlerTimeCard.size() > 0) {
                        for (RefundTreatBillHandlerTimeCardEntity refundTreatBillHandlerTimeCardEntity : refundTreatBillHandlerTimeCard) {
                            TreatBillHandlerTimeCardEntity treatBillHandlerTimeCardEntity = new TreatBillHandlerTimeCardEntity();
                            treatBillHandlerTimeCardEntity.TreatBillTimeCardID = treatBillTimeCardEntity.ID;
                            treatBillHandlerTimeCardEntity.TimeCardTreatHandlerID = refundTreatBillHandlerTimeCardEntity.TimeCardTreatHandlerID;
                            treatBillHandlerTimeCardEntity.EmployeeID = refundTreatBillHandlerTimeCardEntity.EmployeeID;
                            treatBillHandlerTimeCardEntity.Scale = refundTreatBillHandlerTimeCardEntity.Scale;
                            treatBillHandlerTimeCardEntity.IsCalculatePassengerFlow = true;
                            treatBillMapper.createTreatBillHandlerTimeCard(treatBillHandlerTimeCardEntity);
                            BigDecimal TotalPerformance = treatEntityPerformanceTimeCardEntity.PayPerformance.add(treatEntityPerformanceTimeCardEntity.CardPerformance).add(treatEntityPerformanceTimeCardEntity.CardLargessPerformance).add(treatEntityPerformanceTimeCardEntity.LargessPerformance);
                            //记录员工业绩和提佣
                            TreatPerformanceEntity treatPerformanceEntity = treatEmployeePerformanceCommissionMapper.getTreatTimeCardPerformance(treatBillEntity.EntityID, refundTreatBillHandlerTimeCardEntity.EmployeeID, refundTreatBillHandlerTimeCardEntity.TimeCardTreatHandlerID, treatEntityPerformanceTimeCardEntity.ProjectID, treatEntityPerformanceTimeCardEntity.TimeCardID);
                            TreatCommissionEntity treatCommissionEntity = treatEmployeePerformanceCommissionMapper.getTreatTimeCardCommission(treatBillEntity.EntityID, refundTreatBillHandlerTimeCardEntity.EmployeeID, refundTreatBillHandlerTimeCardEntity.TimeCardTreatHandlerID, treatEntityPerformanceTimeCardEntity.ProjectID, treatEntityPerformanceTimeCardEntity.TimeCardID);

                            BigDecimal performancePayRate = treatPerformanceEntity.PayRate == null ? BigDecimal.ZERO : treatPerformanceEntity.PayRate;
                            BigDecimal performanceCardRate = treatPerformanceEntity.CardRate == null ? BigDecimal.ZERO : treatPerformanceEntity.CardRate;
                            BigDecimal performanceCardLargessRate = treatPerformanceEntity.CardLargessRate == null ? BigDecimal.ZERO : treatPerformanceEntity.CardLargessRate;
                            BigDecimal performanceLargessRate = treatPerformanceEntity.LargessRate == null ? BigDecimal.ZERO : treatPerformanceEntity.LargessRate;
                            BigDecimal payRate = BigDecimal.ZERO;
                            BigDecimal cardRate = BigDecimal.ZERO;
                            BigDecimal cardLargessRate = BigDecimal.ZERO;
                            BigDecimal largessRate = BigDecimal.ZERO;
                            BigDecimal payFixed = BigDecimal.ZERO;
                            BigDecimal cardFixed = BigDecimal.ZERO;
                            BigDecimal cardLargessFixed = BigDecimal.ZERO;
                            BigDecimal largessFixed = BigDecimal.ZERO;
                            BigDecimal specialBenefit = BigDecimal.ZERO;
                            if (treatCommissionEntity != null) {
                                payRate = treatCommissionEntity.PayRate == null ? BigDecimal.ZERO : treatCommissionEntity.PayRate;
                                cardRate = treatCommissionEntity.CardRate == null ? BigDecimal.ZERO : treatCommissionEntity.CardRate;
                                cardLargessRate = treatCommissionEntity.CardLargessRate == null ? BigDecimal.ZERO : treatCommissionEntity.CardLargessRate;
                                largessRate = treatCommissionEntity.LargessRate == null ? BigDecimal.ZERO : treatCommissionEntity.LargessRate;
                                payFixed = treatCommissionEntity.PayFixed == null ? BigDecimal.ZERO : treatCommissionEntity.PayFixed;
                                cardFixed = treatCommissionEntity.CardFixed == null ? BigDecimal.ZERO : treatCommissionEntity.CardFixed;
                                cardLargessFixed = treatCommissionEntity.CardLargessFixed == null ? BigDecimal.ZERO : treatCommissionEntity.CardLargessFixed;
                                largessFixed = treatCommissionEntity.LargessFixed == null ? BigDecimal.ZERO : treatCommissionEntity.LargessFixed;
                                specialBenefit = treatCommissionEntity.SpecialBenefit == null ? BigDecimal.ZERO : treatCommissionEntity.SpecialBenefit;
                            }
                            TreatEmployeePerformanceCommissionPackageCardTimeCardEntity treatEmployeePerformanceCommissionPackageCardTimeCardEntity = new TreatEmployeePerformanceCommissionPackageCardTimeCardEntity();
                            treatEmployeePerformanceCommissionPackageCardTimeCardEntity.EntityID = treatBillEntity.EntityID;
                            treatEmployeePerformanceCommissionPackageCardTimeCardEntity.TreatBillID = treatBillEntity.ID;
                            treatEmployeePerformanceCommissionPackageCardTimeCardEntity.TreatBillTimeCardID = treatBillTimeCardEntity.ID;
                            treatEmployeePerformanceCommissionPackageCardTimeCardEntity.TimeCardID = timeCardAccountEntity.TimeCardID;
                            treatEmployeePerformanceCommissionPackageCardTimeCardEntity.ProjectID = treatBillTimeCardEntity.ProjectID;
                            treatEmployeePerformanceCommissionPackageCardTimeCardEntity.TimeCardTreatHandlerID = refundTreatBillHandlerTimeCardEntity.TimeCardTreatHandlerID;
                            treatEmployeePerformanceCommissionPackageCardTimeCardEntity.EmployeeID = refundTreatBillHandlerTimeCardEntity.EmployeeID;
                            treatEmployeePerformanceCommissionPackageCardTimeCardEntity.BillDate = treatBillEntity.BillDate;
                            if (treatEntityPerformanceTimeCardEntity.PayPerformance.compareTo(BigDecimal.ZERO) > 0 && TotalPerformance.compareTo(BigDecimal.ZERO) > 0) {
                                treatEmployeePerformanceCommissionPackageCardTimeCardEntity.PayPerformance = treatEntityPerformanceTimeCardEntity.PayPerformance.multiply(performancePayRate).multiply(treatBillHandlerTimeCardEntity.Scale).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardTimeCardEntity.PayRateCommission = treatEmployeePerformanceCommissionPackageCardTimeCardEntity.PayPerformance.multiply(payRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardTimeCardEntity.PayFixedCommission = payFixed.multiply(BigDecimal.valueOf(treatBillTimeCardEntity.Quantity)).multiply(treatEmployeePerformanceCommissionPackageCardTimeCardEntity.PayPerformance).divide(TotalPerformance, 2, BigDecimal.ROUND_HALF_UP);
                            } else {
                                treatEmployeePerformanceCommissionPackageCardTimeCardEntity.PayPerformance = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardTimeCardEntity.PayRateCommission = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardTimeCardEntity.PayFixedCommission = BigDecimal.ZERO;
                            }
                            if (treatEntityPerformanceTimeCardEntity.CardPerformance.compareTo(BigDecimal.ZERO) > 0 && TotalPerformance.compareTo(BigDecimal.ZERO) > 0) {
                                treatEmployeePerformanceCommissionPackageCardTimeCardEntity.CardPerformance = treatEntityPerformanceTimeCardEntity.CardPerformance.multiply(performanceCardRate).multiply(treatBillHandlerTimeCardEntity.Scale).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardTimeCardEntity.CardRateCommission = treatEmployeePerformanceCommissionPackageCardTimeCardEntity.CardPerformance.multiply(cardRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardTimeCardEntity.CardFixedCommission = cardFixed.multiply(BigDecimal.valueOf(treatBillTimeCardEntity.Quantity)).multiply(treatEmployeePerformanceCommissionPackageCardTimeCardEntity.CardPerformance).divide(TotalPerformance, 2, BigDecimal.ROUND_HALF_UP);
                            } else {
                                treatEmployeePerformanceCommissionPackageCardTimeCardEntity.CardPerformance = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardTimeCardEntity.CardRateCommission = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardTimeCardEntity.CardFixedCommission = BigDecimal.ZERO;
                            }
                            if (treatEntityPerformanceTimeCardEntity.CardLargessPerformance.compareTo(BigDecimal.ZERO) > 0 && TotalPerformance.compareTo(BigDecimal.ZERO) > 0) {
                                treatEmployeePerformanceCommissionPackageCardTimeCardEntity.CardLargessPerformance = treatEntityPerformanceTimeCardEntity.CardLargessPerformance.multiply(performanceCardLargessRate).multiply(treatBillHandlerTimeCardEntity.Scale).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardTimeCardEntity.CardLargessRateCommission = treatEmployeePerformanceCommissionPackageCardTimeCardEntity.CardLargessPerformance.multiply(cardLargessRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardTimeCardEntity.CardLargessFixedCommission = cardLargessFixed.multiply(BigDecimal.valueOf(treatBillTimeCardEntity.Quantity)).multiply(treatEmployeePerformanceCommissionPackageCardTimeCardEntity.CardLargessPerformance).divide(TotalPerformance, 2, BigDecimal.ROUND_HALF_UP);
                            } else {
                                treatEmployeePerformanceCommissionPackageCardTimeCardEntity.CardLargessPerformance = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardTimeCardEntity.CardLargessRateCommission = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardTimeCardEntity.CardLargessFixedCommission = BigDecimal.ZERO;
                            }
                            if (treatEntityPerformanceTimeCardEntity.LargessPerformance.compareTo(BigDecimal.ZERO) > 0 && TotalPerformance.compareTo(BigDecimal.ZERO) > 0) {
                                treatEmployeePerformanceCommissionPackageCardTimeCardEntity.LargessPerformance = treatEntityPerformanceTimeCardEntity.LargessPerformance.multiply(performanceLargessRate).multiply(treatBillHandlerTimeCardEntity.Scale).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardTimeCardEntity.LargessRateCommission = treatEmployeePerformanceCommissionPackageCardTimeCardEntity.LargessPerformance.multiply(largessRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardTimeCardEntity.LargessFixedCommission = largessFixed.multiply(BigDecimal.valueOf(treatBillTimeCardEntity.Quantity)).multiply(treatEmployeePerformanceCommissionPackageCardTimeCardEntity.LargessPerformance).divide(TotalPerformance, 2, BigDecimal.ROUND_HALF_UP);
                            } else {
                                treatEmployeePerformanceCommissionPackageCardTimeCardEntity.LargessPerformance = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardTimeCardEntity.LargessRateCommission = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardTimeCardEntity.LargessFixedCommission = BigDecimal.ZERO;
                            }
                            treatEmployeePerformanceCommissionPackageCardTimeCardEntity.SpecialBenefitCommission = specialBenefit.multiply(BigDecimal.valueOf(treatBillTimeCardEntity.Quantity)).multiply(treatBillHandlerTimeCardEntity.Scale).setScale(2, BigDecimal.ROUND_HALF_UP);
                            ;

                            if (timeCardAccountEntity.PackageCardAccountID != null && timeCardAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                                treatEmployeePerformanceCommissionPackageCardTimeCardEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                                treatEmployeePerformanceCommissionMapper.createTreatEmployeePerformanceCommissionPackageCardTimeCard(treatEmployeePerformanceCommissionPackageCardTimeCardEntity);
                            } else {
                                treatEmployeePerformanceCommissionMapper.createTreatEmployeePerformanceCommissionTimeCard(treatEmployeePerformanceCommissionPackageCardTimeCardEntity);
                            }
                        }
                    }

                    if (isCalculateChannelPerformance) {
                        for (ChannelParentOutputForm parent : parents) {
                            TreatChannelPerformancePackageCardGoodEntity treatChannelPerformanceGoodEntity = new TreatChannelPerformancePackageCardGoodEntity();
                            treatChannelPerformanceGoodEntity.TreatBillID = treatBillEntity.ID;
                            treatChannelPerformanceGoodEntity.EntityID = treatBillEntity.EntityID;
                            treatChannelPerformanceGoodEntity.BillDate = treatBillEntity.BillDate;
                            treatChannelPerformanceGoodEntity.TreatBillGoodsID = treatBillTimeCardEntity.ID;
                            treatChannelPerformanceGoodEntity.CardID = timeCardAccountEntity.TimeCardID;
                            treatChannelPerformanceGoodEntity.GoodsID = treatBillTimeCardEntity.ProjectID;
                            treatChannelPerformanceGoodEntity.ChannelID = parent.ID;
                            treatChannelPerformanceGoodEntity.IsChannelIntroducer = false;
                            treatChannelPerformanceGoodEntity.PayPerformance = treatEntityPerformanceTimeCardEntity.PayPerformance;
                            treatChannelPerformanceGoodEntity.CardPerformance = treatEntityPerformanceTimeCardEntity.CardPerformance;
                            treatChannelPerformanceGoodEntity.CardLargessPerformance = treatEntityPerformanceTimeCardEntity.CardLargessPerformance;
                            treatChannelPerformanceGoodEntity.LargessPerformance = treatEntityPerformanceTimeCardEntity.LargessPerformance;
                            if (timeCardAccountEntity.PackageCardAccountID != null && timeCardAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                                treatChannelPerformanceGoodEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                                treatChannelPerformancePackageCardGoodEntities.add(treatChannelPerformanceGoodEntity);
                            } else {
                                treatChannelPerformanceGoodEntities.add(treatChannelPerformanceGoodEntity);
                            }
                            if (parent.IntroducerList.size() > 0) {
                                for (Integer integer : parent.IntroducerList) {
                                    TreatChannelPerformancePackageCardGoodEntity performanceGoodEntity = new TreatChannelPerformancePackageCardGoodEntity();
                                    performanceGoodEntity.TreatBillID = treatBillEntity.ID;
                                    performanceGoodEntity.EntityID = treatBillEntity.EntityID;
                                    performanceGoodEntity.BillDate = treatBillEntity.BillDate;
                                    performanceGoodEntity.TreatBillGoodsID = treatBillTimeCardEntity.ID;
                                    performanceGoodEntity.CardID = timeCardAccountEntity.TimeCardID;
                                    performanceGoodEntity.GoodsID = treatBillTimeCardEntity.ProjectID;
                                    performanceGoodEntity.ChannelID = integer;
                                    performanceGoodEntity.IsChannelIntroducer = true;
                                    performanceGoodEntity.PayPerformance = treatEntityPerformanceTimeCardEntity.PayPerformance;
                                    performanceGoodEntity.CardPerformance = treatEntityPerformanceTimeCardEntity.CardPerformance;
                                    performanceGoodEntity.CardLargessPerformance = treatEntityPerformanceTimeCardEntity.CardLargessPerformance;
                                    performanceGoodEntity.LargessPerformance = treatEntityPerformanceTimeCardEntity.LargessPerformance;
                                    if (timeCardAccountEntity.PackageCardAccountID != null && timeCardAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                                        performanceGoodEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                                        treatChannelPerformancePackageCardGoodEntities.add(performanceGoodEntity);
                                    } else {
                                        treatChannelPerformanceGoodEntities.add(performanceGoodEntity);
                                    }
                                }

                            }
                        }

                        for (ChannelEmployeePerformanceRateOutputForm employeePerformanceRateOutputForm : developer) {
                            TreatChannelEmployeePerformancePackageCardGoodsEntity packageCardGoodsEntity = new TreatChannelEmployeePerformancePackageCardGoodsEntity();
                            packageCardGoodsEntity.TreatBillID = treatBillEntity.ID;
                            packageCardGoodsEntity.EntityID = treatBillEntity.EntityID;
                            packageCardGoodsEntity.BillDate = treatBillEntity.BillDate;
                            packageCardGoodsEntity.TreatBillGoodsID = treatBillTimeCardEntity.ID;
                            packageCardGoodsEntity.CardID = timeCardAccountEntity.TimeCardID;
                            packageCardGoodsEntity.GoodsID = treatBillTimeCardEntity.ProjectID;
                            packageCardGoodsEntity.ChannelID = cust.ChannelID;
                            packageCardGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                            packageCardGoodsEntity.PayPerformance = treatEntityPerformanceTimeCardEntity.PayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            packageCardGoodsEntity.CardPerformance = treatEntityPerformanceTimeCardEntity.CardPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            packageCardGoodsEntity.CardLargessPerformance = treatEntityPerformanceTimeCardEntity.CardLargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            packageCardGoodsEntity.LargessPerformance = treatEntityPerformanceTimeCardEntity.LargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            if (timeCardAccountEntity.PackageCardAccountID != null && timeCardAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                                packageCardGoodsEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                                treatDeveloperChannelPerformancePackageCardTimeCard.add(packageCardGoodsEntity);
                            } else {
                                treatDeveloperChannelPerformanceTimeCard.add(packageCardGoodsEntity);
                            }
                        }
                        for (ChannelEmployeePerformanceRateOutputForm employeePerformanceRateOutputForm : consultant) {
                            TreatChannelEmployeePerformancePackageCardGoodsEntity packageCardGoodsEntity = new TreatChannelEmployeePerformancePackageCardGoodsEntity();
                            packageCardGoodsEntity.TreatBillID = treatBillEntity.ID;
                            packageCardGoodsEntity.EntityID = treatBillEntity.EntityID;
                            packageCardGoodsEntity.BillDate = treatBillEntity.BillDate;
                            packageCardGoodsEntity.TreatBillGoodsID = treatBillTimeCardEntity.ID;
                            packageCardGoodsEntity.CardID = timeCardAccountEntity.TimeCardID;
                            packageCardGoodsEntity.GoodsID = treatBillTimeCardEntity.ProjectID;
                            packageCardGoodsEntity.ChannelID = cust.ChannelID;
                            packageCardGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                            packageCardGoodsEntity.PayPerformance = treatEntityPerformanceTimeCardEntity.PayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            packageCardGoodsEntity.CardPerformance = treatEntityPerformanceTimeCardEntity.CardPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            packageCardGoodsEntity.CardLargessPerformance = treatEntityPerformanceTimeCardEntity.CardLargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            packageCardGoodsEntity.LargessPerformance = treatEntityPerformanceTimeCardEntity.LargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            if (timeCardAccountEntity.PackageCardAccountID != null && timeCardAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                                packageCardGoodsEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                                treatConsultantChannelPerformancePackageCardTimeCard.add(packageCardGoodsEntity);
                            } else {
                                treatConsultantChannelPerformanceTimeCard.add(packageCardGoodsEntity);
                            }
                        }
                    }

                    treatEntityPerformanceTimeCardEntity.PayPerformance = treatBillTimeCardEntity.PayAmount.multiply(treatTimeCardEntityPerformance.PayRate == null ? BigDecimal.ZERO : treatTimeCardEntityPerformance.PayRate);
                    treatEntityPerformanceTimeCardEntity.CardPerformance = treatBillTimeCardEntity.CardDeductionAmount.multiply(treatTimeCardEntityPerformance.CardRate == null ? BigDecimal.ZERO : treatTimeCardEntityPerformance.CardRate);
                    treatEntityPerformanceTimeCardEntity.CardLargessPerformance = treatBillTimeCardEntity.LargessCardDeductionAmount.multiply(treatTimeCardEntityPerformance.CardLargessRate == null ? BigDecimal.ZERO : treatTimeCardEntityPerformance.CardLargessRate);
                    treatEntityPerformanceTimeCardEntity.LargessPerformance = BigDecimal.ZERO;
                    if (treatBillTimeCardEntity.IsLargess) {
                        treatEntityPerformanceTimeCardEntity.LargessPerformance = refundTreatBillTimeCardEntity.TotalAmount.multiply(treatTimeCardEntityPerformance.LargessRate == null ? BigDecimal.ZERO : treatTimeCardEntityPerformance.LargessRate);
                    }

                    treatEntityPerformanceTimeCardEntity.Scale = new BigDecimal(100);
                    if (timeCardAccountEntity.PackageCardAccountID != null && timeCardAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                        treatEntityPerformanceMapper.createTreatEntityPerformancePackageCardTimeCard(treatEntityPerformanceTimeCardEntity);
                    } else {
                        treatEntityPerformanceMapper.createTreatEntityPerformanceTimeCard(treatEntityPerformanceTimeCardEntity);
                    }
                }

                if (treatChannelPerformanceGoodEntities.size() > 0) {
                    treatChannelPerformanceMapper.createTreatChannelPerformanceTimeCard(treatChannelPerformanceGoodEntities);
                }
                if (treatChannelPerformancePackageCardGoodEntities.size() > 0) {
                    treatChannelPerformanceMapper.createTreatChannelPerformancePackageCardTimeCard(treatChannelPerformancePackageCardGoodEntities);
                }

                //保存开发人员的渠道业绩
                if (treatDeveloperChannelPerformanceTimeCard.size() > 0) {
                    treatChannelDeveloperPerformanceMapper.createTreatDeveloperChannelPerformanceTimeCard(treatDeveloperChannelPerformanceTimeCard);
                }

                //保存开发人员套餐卡渠道业绩
                if (treatDeveloperChannelPerformancePackageCardTimeCard.size() > 0) {
                    treatChannelDeveloperPerformanceMapper.createTreatDeveloperChannelPerformancePackageCardTimeCard(treatDeveloperChannelPerformancePackageCardTimeCard);
                }

                //保存服务人员的渠道业绩
                if (treatConsultantChannelPerformanceTimeCard.size() > 0) {
                    treatChannelConsultantPerformanceMapper.createTreatConsultantChannelPerformanceTimeCard(treatConsultantChannelPerformanceTimeCard);
                }

                //保存服务人员套餐卡渠道业绩
                if (treatConsultantChannelPerformancePackageCardTimeCard.size() > 0) {
                    treatChannelConsultantPerformanceMapper.createTreatConsultantChannelPerformancePackageCardTimeCard(treatConsultantChannelPerformancePackageCardTimeCard);
                }
            }
            if (refundTreatSavingCard != null && refundTreatSavingCard.size() > 0) {
                //渠道业绩
                List<TreatChannelPerformanceGoodEntity> treatChannelPerformanceGoodEntities = new ArrayList<>();
                List<TreatChannelPerformancePackageCardGoodEntity> treatChannelPerformancePackageCardGoodEntities = new ArrayList<>();

                //储值卡
                for (RefundTreatBillSavingCardEntity refundTreatBillSavingCardEntity : refundTreatSavingCard) {
                    TreatBillEntity treatBill = treatBillMapper.getTreatBill(refundTreatBillSavingCardEntity.TreatBillID);
                    if (treatBill.BillStatus.equals("30")) {
                        throw new RuntimeException("储值卡订单已经被取消，请撤销申请");
                    }
                    SavingCardAccountEntity savingCardAccountEntity = savingCardAccountMapper.getSavingCardAccountByID(refundTreatBillSavingCardEntity.SavingCardAccountID);
                    //更新账户
                    savingCardAccountEntity.Balance = savingCardAccountEntity.Balance.add(refundTreatBillSavingCardEntity.Amount);
                    savingCardAccountEntity.LargessBalance = savingCardAccountEntity.LargessBalance.add(refundTreatBillSavingCardEntity.LargessAmount);
                    savingCardAccountMapper.updateSavingCardAccountBalance(savingCardAccountEntity);

                    BillCardDeductionAmount = BillCardDeductionAmount.add(refundTreatBillSavingCardEntity.Amount);
                    BillLargessCardDeductionAmount = BillLargessCardDeductionAmount.add(refundTreatBillSavingCardEntity.LargessAmount);

                    //记录明细
                    TreatBillSavingCardEntity treatBillSavingCardEntity = new TreatBillSavingCardEntity();
                    treatBillSavingCardEntity.TreatBillID = treatBillEntity.ID;
                    treatBillSavingCardEntity.SavingCardAccountID = refundTreatBillSavingCardEntity.SavingCardAccountID;
                    treatBillSavingCardEntity.ProjectID = refundTreatBillSavingCardEntity.ProjectID;
                    treatBillSavingCardEntity.Quantity = refundTreatBillSavingCardEntity.Quantity;
                    treatBillSavingCardEntity.Price = refundTreatBillSavingCardEntity.TotalAmount.divide(new BigDecimal(refundTreatBillSavingCardEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
                    treatBillSavingCardEntity.CardPreferentialAmount = BigDecimal.ZERO;
                    treatBillSavingCardEntity.PricePreferentialAmount = BigDecimal.ZERO;
                    treatBillSavingCardEntity.MemberPreferentialAmount = BigDecimal.ZERO;
                    treatBillSavingCardEntity.TotalAmount = refundTreatBillSavingCardEntity.TotalAmount;
                    treatBillSavingCardEntity.CardTreatAmount = refundTreatBillSavingCardEntity.Amount;
                    treatBillSavingCardEntity.LargessCardTreatAmount = refundTreatBillSavingCardEntity.LargessAmount;
                    if (refundTreatBillSavingCardEntity.PackageCardAccountID != null && refundTreatBillSavingCardEntity.PackageCardAccountID > 0) {
                        treatBillSavingCardEntity.PackageCardAccountID = refundTreatBillSavingCardEntity.PackageCardAccountID;
                        treatBillSavingCardEntity.PackageCardID = refundTreatBillSavingCardEntity.PackageCardID;
                    }
                    treatBillMapper.createTreatBillSavingCard(treatBillSavingCardEntity);

                    TreatPerformanceEntity treatSavingCardEntityPerformance = treatEntityPerformanceMapper.getTreatSavingCardEntityPerformance(treatBillEntity.EntityID, savingCardAccountEntity.SavingCardID);
                    //记录门店业绩
                    TreatEntityPerformancePackageCardSavingCardEntity treatEntityPerformanceSavingCardEntity = new TreatEntityPerformancePackageCardSavingCardEntity();
                    treatEntityPerformanceSavingCardEntity.EntityID = treatBillEntity.EntityID;
                    treatEntityPerformanceSavingCardEntity.TreatBillID = treatBillEntity.ID;
                    treatEntityPerformanceSavingCardEntity.TreatBillSavingCardID = treatBillSavingCardEntity.ID;
                    treatEntityPerformanceSavingCardEntity.SavingCardID = savingCardAccountEntity.SavingCardID;
                    treatEntityPerformanceSavingCardEntity.ProjectID = treatBillSavingCardEntity.ProjectID;
                    treatEntityPerformanceSavingCardEntity.BillDate = treatBillEntity.BillDate;
                    treatEntityPerformanceSavingCardEntity.CardPerformance = treatBillSavingCardEntity.CardTreatAmount;
                    treatEntityPerformanceSavingCardEntity.CardLargessPerformance = treatBillSavingCardEntity.LargessCardTreatAmount;
                    PackageCardAccountEntity packageCardAccountEntity = new PackageCardAccountEntity();
                    if (savingCardAccountEntity.PackageCardAccountID != null && savingCardAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                        packageCardAccountEntity = packageCardAccountMapper.getPackageCardAccount(savingCardAccountEntity.PackageCardAccountID);
                        treatEntityPerformanceSavingCardEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                    }

                    List<RefundTreatBillHandlerSavingCardEntity> refundTreatBillHandlerSavingCard = refundTreatBillMapper.getRefundTreatBillHandlerSavingCard(refundTreatBillSavingCardEntity.ID);
                    //记录经手人
                    if (refundTreatBillHandlerSavingCard != null && refundTreatBillHandlerSavingCard.size() > 0) {
                        for (RefundTreatBillHandlerSavingCardEntity refundTreatBillHandlerSavingCardEntity : refundTreatBillHandlerSavingCard) {
                            TreatBillHandlerSavingCardEntity treatBillHandlerSavingCardEntity = new TreatBillHandlerSavingCardEntity();
                            treatBillHandlerSavingCardEntity.TreatBillSavingCardID = treatBillSavingCardEntity.ID;
                            treatBillHandlerSavingCardEntity.SavingCardTreatHandlerID = refundTreatBillHandlerSavingCardEntity.SavingCardTreatHandlerID;
                            treatBillHandlerSavingCardEntity.EmployeeID = refundTreatBillHandlerSavingCardEntity.EmployeeID;
                            treatBillHandlerSavingCardEntity.Scale = refundTreatBillHandlerSavingCardEntity.Scale;
                            treatBillHandlerSavingCardEntity.IsCalculatePassengerFlow = true;
                            treatBillMapper.createTreatBillHandlerSavingCard(treatBillHandlerSavingCardEntity);
                            BigDecimal TotalPerformance = treatEntityPerformanceSavingCardEntity.CardPerformance.add(treatEntityPerformanceSavingCardEntity.CardLargessPerformance);
                            //记录员工业绩和提佣
                            TreatPerformanceEntity treatPerformanceEntity = treatEmployeePerformanceCommissionMapper.getTreatSavingCardPerformance(treatBillEntity.EntityID, refundTreatBillHandlerSavingCardEntity.EmployeeID, refundTreatBillHandlerSavingCardEntity.SavingCardTreatHandlerID, treatBillSavingCardEntity.ProjectID, savingCardAccountEntity.SavingCardID);
                            TreatCommissionEntity treatCommissionEntity = treatEmployeePerformanceCommissionMapper.getTreatSavingCardCommission(treatBillEntity.EntityID, refundTreatBillHandlerSavingCardEntity.EmployeeID, refundTreatBillHandlerSavingCardEntity.SavingCardTreatHandlerID, treatBillSavingCardEntity.ProjectID, savingCardAccountEntity.SavingCardID);

                            BigDecimal performanceCardRate = treatPerformanceEntity.CardRate == null ? BigDecimal.ZERO : treatPerformanceEntity.CardRate;
                            BigDecimal performanceCardLargessRate = treatPerformanceEntity.CardLargessRate == null ? BigDecimal.ZERO : treatPerformanceEntity.CardLargessRate;
                            BigDecimal cardRate = BigDecimal.ZERO;
                            BigDecimal cardLargessRate = BigDecimal.ZERO;
                            BigDecimal cardFixed = BigDecimal.ZERO;
                            BigDecimal cardLargessFixed = BigDecimal.ZERO;
                            BigDecimal specialBenefit = BigDecimal.ZERO;
                            if (treatCommissionEntity != null) {
                                cardRate = treatCommissionEntity.CardRate == null ? BigDecimal.ZERO : treatCommissionEntity.CardRate;
                                cardLargessRate = treatCommissionEntity.CardLargessRate == null ? BigDecimal.ZERO : treatCommissionEntity.CardLargessRate;
                                cardFixed = treatCommissionEntity.CardFixed == null ? BigDecimal.ZERO : treatCommissionEntity.CardFixed;
                                cardLargessFixed = treatCommissionEntity.CardLargessFixed == null ? BigDecimal.ZERO : treatCommissionEntity.CardLargessFixed;
                                specialBenefit = treatCommissionEntity.SpecialBenefit == null ? BigDecimal.ZERO : treatCommissionEntity.SpecialBenefit;
                            }
                            TreatEmployeePerformanceCommissionPackageCardSavingCardEntity treatEmployeePerformanceCommissionPackageCardSavingCardEntity = new TreatEmployeePerformanceCommissionPackageCardSavingCardEntity();
                            treatEmployeePerformanceCommissionPackageCardSavingCardEntity.EntityID = treatBillEntity.EntityID;
                            treatEmployeePerformanceCommissionPackageCardSavingCardEntity.TreatBillID = treatBillEntity.ID;
                            treatEmployeePerformanceCommissionPackageCardSavingCardEntity.TreatBillSavingCardID = treatBillSavingCardEntity.ID;
                            treatEmployeePerformanceCommissionPackageCardSavingCardEntity.SavingCardID = treatEntityPerformanceSavingCardEntity.SavingCardID;
                            treatEmployeePerformanceCommissionPackageCardSavingCardEntity.ProjectID = treatBillSavingCardEntity.ProjectID;
                            treatEmployeePerformanceCommissionPackageCardSavingCardEntity.SavingCardTreatHandlerID = refundTreatBillHandlerSavingCardEntity.SavingCardTreatHandlerID;
                            treatEmployeePerformanceCommissionPackageCardSavingCardEntity.EmployeeID = refundTreatBillHandlerSavingCardEntity.EmployeeID;
                            treatEmployeePerformanceCommissionPackageCardSavingCardEntity.BillDate = treatBillEntity.BillDate;
                            if (treatEntityPerformanceSavingCardEntity.CardPerformance.compareTo(new BigDecimal(0)) > 0 && TotalPerformance.compareTo(BigDecimal.ZERO) > 0) {
                                treatEmployeePerformanceCommissionPackageCardSavingCardEntity.CardPerformance = treatEntityPerformanceSavingCardEntity.CardPerformance.multiply(performanceCardRate).multiply(treatBillHandlerSavingCardEntity.Scale).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardSavingCardEntity.CardRateCommission = treatEmployeePerformanceCommissionPackageCardSavingCardEntity.CardPerformance.multiply(cardRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardSavingCardEntity.CardFixedCommission = cardFixed.multiply(BigDecimal.valueOf(treatBillSavingCardEntity.Quantity)).multiply(treatEmployeePerformanceCommissionPackageCardSavingCardEntity.CardPerformance).divide(TotalPerformance, 2, BigDecimal.ROUND_HALF_UP);
                            } else {
                                treatEmployeePerformanceCommissionPackageCardSavingCardEntity.CardPerformance = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardSavingCardEntity.CardRateCommission = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardSavingCardEntity.CardFixedCommission = BigDecimal.ZERO;
                            }
                            if (treatEntityPerformanceSavingCardEntity.CardLargessPerformance.compareTo(new BigDecimal(0)) > 0 && TotalPerformance.compareTo(BigDecimal.ZERO) > 0) {
                                treatEmployeePerformanceCommissionPackageCardSavingCardEntity.CardLargessPerformance = treatEntityPerformanceSavingCardEntity.CardLargessPerformance.multiply(performanceCardLargessRate).multiply(treatBillHandlerSavingCardEntity.Scale).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardSavingCardEntity.CardLargessRateCommission = treatEmployeePerformanceCommissionPackageCardSavingCardEntity.CardLargessPerformance.multiply(cardLargessRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                                treatEmployeePerformanceCommissionPackageCardSavingCardEntity.CardLargessFixedCommission = cardLargessFixed.multiply(BigDecimal.valueOf(treatBillSavingCardEntity.Quantity)).multiply(treatEmployeePerformanceCommissionPackageCardSavingCardEntity.CardLargessPerformance).divide(TotalPerformance, 2, BigDecimal.ROUND_HALF_UP);
                            } else {
                                treatEmployeePerformanceCommissionPackageCardSavingCardEntity.CardLargessPerformance = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardSavingCardEntity.CardLargessRateCommission = BigDecimal.ZERO;
                                treatEmployeePerformanceCommissionPackageCardSavingCardEntity.CardLargessFixedCommission = BigDecimal.ZERO;
                            }
                            treatEmployeePerformanceCommissionPackageCardSavingCardEntity.SpecialBenefitCommission = specialBenefit.multiply(BigDecimal.valueOf(treatBillSavingCardEntity.Quantity)).multiply(treatBillHandlerSavingCardEntity.Scale).setScale(2, BigDecimal.ROUND_HALF_UP);
                            ;

                            if (savingCardAccountEntity.PackageCardAccountID != null && savingCardAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                                treatEmployeePerformanceCommissionPackageCardSavingCardEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                                treatEmployeePerformanceCommissionMapper.createTreatEmployeePerformanceCommissionPackageCardSavingCard(treatEmployeePerformanceCommissionPackageCardSavingCardEntity);
                            } else {
                                treatEmployeePerformanceCommissionMapper.createTreatEmployeePerformanceCommissionSavingCard(treatEmployeePerformanceCommissionPackageCardSavingCardEntity);
                            }
                        }
                    }

                    if (isCalculateChannelPerformance) {
                        for (ChannelParentOutputForm parent : parents) {
                            TreatChannelPerformancePackageCardGoodEntity treatChannelPerformanceGoodEntity = new TreatChannelPerformancePackageCardGoodEntity();
                            treatChannelPerformanceGoodEntity.TreatBillID = treatBillEntity.ID;
                            treatChannelPerformanceGoodEntity.EntityID = treatBillEntity.EntityID;
                            treatChannelPerformanceGoodEntity.BillDate = treatBillEntity.BillDate;
                            treatChannelPerformanceGoodEntity.TreatBillGoodsID = treatBillSavingCardEntity.ID;
                            treatChannelPerformanceGoodEntity.CardID = savingCardAccountEntity.SavingCardID;
                            treatChannelPerformanceGoodEntity.GoodsID = treatBillSavingCardEntity.ProjectID;
                            treatChannelPerformanceGoodEntity.ChannelID = parent.ID;
                            treatChannelPerformanceGoodEntity.IsChannelIntroducer = false;
                            treatChannelPerformanceGoodEntity.CardPerformance = treatEntityPerformanceSavingCardEntity.CardPerformance;
                            treatChannelPerformanceGoodEntity.CardLargessPerformance = treatEntityPerformanceSavingCardEntity.CardLargessPerformance;
                            if (savingCardAccountEntity.PackageCardAccountID != null && savingCardAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                                treatChannelPerformanceGoodEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                                treatChannelPerformancePackageCardGoodEntities.add(treatChannelPerformanceGoodEntity);
                            } else {
                                treatChannelPerformanceGoodEntities.add(treatChannelPerformanceGoodEntity);
                            }
                            if (parent.IntroducerList.size() > 0) {
                                for (Integer integer : parent.IntroducerList) {
                                    TreatChannelPerformancePackageCardGoodEntity performanceGoodEntity = new TreatChannelPerformancePackageCardGoodEntity();
                                    performanceGoodEntity.TreatBillID = treatBillEntity.ID;
                                    performanceGoodEntity.EntityID = treatBillEntity.EntityID;
                                    performanceGoodEntity.BillDate = treatBillEntity.BillDate;
                                    performanceGoodEntity.TreatBillGoodsID = treatBillSavingCardEntity.ID;
                                    performanceGoodEntity.CardID = savingCardAccountEntity.SavingCardID;
                                    performanceGoodEntity.GoodsID = treatBillSavingCardEntity.ProjectID;
                                    performanceGoodEntity.ChannelID = integer;
                                    performanceGoodEntity.IsChannelIntroducer = true;
                                    performanceGoodEntity.CardPerformance = treatEntityPerformanceSavingCardEntity.CardPerformance;
                                    performanceGoodEntity.CardLargessPerformance = treatEntityPerformanceSavingCardEntity.CardLargessPerformance;
                                    if (savingCardAccountEntity.PackageCardAccountID != null && savingCardAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                                        performanceGoodEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                                        treatChannelPerformancePackageCardGoodEntities.add(performanceGoodEntity);
                                    } else {
                                        treatChannelPerformanceGoodEntities.add(performanceGoodEntity);
                                    }
                                }

                            }
                        }

                        for (ChannelEmployeePerformanceRateOutputForm employeePerformanceRateOutputForm : developer) {
                            TreatChannelEmployeePerformancePackageCardGoodsEntity packageCardGoodsEntity = new TreatChannelEmployeePerformancePackageCardGoodsEntity();
                            packageCardGoodsEntity.TreatBillID = treatBillEntity.ID;
                            packageCardGoodsEntity.EntityID = treatBillEntity.EntityID;
                            packageCardGoodsEntity.BillDate = treatBillEntity.BillDate;
                            packageCardGoodsEntity.TreatBillGoodsID = treatBillSavingCardEntity.ID;
                            packageCardGoodsEntity.CardID = savingCardAccountEntity.SavingCardID;
                            packageCardGoodsEntity.GoodsID = treatBillSavingCardEntity.ProjectID;
                            packageCardGoodsEntity.ChannelID = cust.ChannelID;
                            packageCardGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                            packageCardGoodsEntity.CardPerformance = treatEntityPerformanceSavingCardEntity.CardPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            packageCardGoodsEntity.CardLargessPerformance = treatEntityPerformanceSavingCardEntity.CardLargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            if (savingCardAccountEntity.PackageCardAccountID != null && savingCardAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                                packageCardGoodsEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                                treatDeveloperChannelPerformancePackageCardSavingCard.add(packageCardGoodsEntity);
                            } else {
                                treatDeveloperChannelPerformanceSavingCard.add(packageCardGoodsEntity);
                            }
                        }

                        for (ChannelEmployeePerformanceRateOutputForm employeePerformanceRateOutputForm : consultant) {
                            TreatChannelEmployeePerformancePackageCardGoodsEntity packageCardGoodsEntity = new TreatChannelEmployeePerformancePackageCardGoodsEntity();
                            packageCardGoodsEntity.TreatBillID = treatBillEntity.ID;
                            packageCardGoodsEntity.EntityID = treatBillEntity.EntityID;
                            packageCardGoodsEntity.BillDate = treatBillEntity.BillDate;
                            packageCardGoodsEntity.TreatBillGoodsID = treatBillSavingCardEntity.ID;
                            packageCardGoodsEntity.CardID = savingCardAccountEntity.SavingCardID;
                            packageCardGoodsEntity.GoodsID = treatBillSavingCardEntity.ProjectID;
                            packageCardGoodsEntity.ChannelID = cust.ChannelID;
                            packageCardGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                            packageCardGoodsEntity.CardPerformance = treatEntityPerformanceSavingCardEntity.CardPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            packageCardGoodsEntity.CardLargessPerformance = treatEntityPerformanceSavingCardEntity.CardLargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            if (savingCardAccountEntity.PackageCardAccountID != null && savingCardAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                                packageCardGoodsEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                                treatConsultantChannelPerformancePackageCardSavingCard.add(packageCardGoodsEntity);
                            } else {
                                treatConsultantChannelPerformanceSavingCard.add(packageCardGoodsEntity);
                            }
                        }
                    }

                    treatEntityPerformanceSavingCardEntity.CardPerformance = treatBillSavingCardEntity.CardTreatAmount.multiply(treatSavingCardEntityPerformance.CardRate == null ? BigDecimal.ZERO : treatSavingCardEntityPerformance.CardRate);
                    treatEntityPerformanceSavingCardEntity.CardLargessPerformance = treatBillSavingCardEntity.LargessCardTreatAmount.multiply(treatSavingCardEntityPerformance.CardLargessRate == null ? BigDecimal.ZERO : treatSavingCardEntityPerformance.CardLargessRate);
                    treatEntityPerformanceSavingCardEntity.Scale = new BigDecimal(100);
                    if (savingCardAccountEntity.PackageCardAccountID != null && savingCardAccountEntity.PackageCardAccountID > 0) { //判断是否消耗套餐卡
                        packageCardAccountEntity = packageCardAccountMapper.getPackageCardAccount(savingCardAccountEntity.PackageCardAccountID);
                        treatEntityPerformanceSavingCardEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                        treatEntityPerformanceMapper.createTreatEntityPerformancePackageCardSavingCard(treatEntityPerformanceSavingCardEntity);
                    } else {
                        treatEntityPerformanceMapper.createTreatEntityPerformanceSavingCard(treatEntityPerformanceSavingCardEntity);
                    }

                }

                if (treatChannelPerformanceGoodEntities.size() > 0) {
                    treatChannelPerformanceMapper.createTreatChannelPerformanceSavingCard(treatChannelPerformanceGoodEntities);
                }
                if (treatChannelPerformancePackageCardGoodEntities.size() > 0) {
                    treatChannelPerformanceMapper.createTreatChannelPerformancePackageCardSavingCard(treatChannelPerformancePackageCardGoodEntities);
                }

                //保存开发人员的渠道业绩

                if (treatDeveloperChannelPerformanceSavingCard.size() > 0) {
                    treatChannelDeveloperPerformanceMapper.createTreatDeveloperChannelPerformanceSavingCard(treatDeveloperChannelPerformanceSavingCard);
                }

                //保存开发人员套餐卡渠道业绩

                if (treatDeveloperChannelPerformancePackageCardSavingCard.size() > 0) {
                    treatChannelDeveloperPerformanceMapper.createTreatDeveloperChannelPerformancePackageCardSavingCard(treatDeveloperChannelPerformancePackageCardSavingCard);
                }

                //保存服务人员的渠道业绩

                if (treatConsultantChannelPerformanceSavingCard.size() > 0) {
                    treatChannelConsultantPerformanceMapper.createTreatConsultantChannelPerformanceSavingCard(treatConsultantChannelPerformanceSavingCard);
                }

                //保存服务人员套餐卡渠道业绩

                if (treatConsultantChannelPerformancePackageCardSavingCard.size() > 0) {
                    treatChannelConsultantPerformanceMapper.createTreatConsultantChannelPerformancePackageCardSavingCard(treatConsultantChannelPerformancePackageCardSavingCard);
                }
            }
            treatBillEntity.PayAmount = BillPayAmount;
            treatBillEntity.CardDeductionAmount = BillCardDeductionAmount;
            treatBillEntity.LargessCardDeductionAmount = BillLargessCardDeductionAmount;
            treatBillMapper.updateTreatBill(treatBillEntity);
            refundTreatBillMapper.updateRefundTreatBillStatus(form.ID, form.ApprovalStatus);

            //是否计算成长值
            Boolean isCalculateCustomerGrowth = customerGrowthMapper.getCustomerGrowthSetting();
            CustomerEntity customerEntity = customerMapper.getCustomer(treatBillEntity.CustomerID);
            //升降级
            if (isCalculateCustomerGrowth && treatBillEntity.CustomerID != null && customerEntity.IsMember) {
                List<CustomerGrowthEntity> customerGrowthEntities = treatBillService.calculateCustomerGrowth("RefundTreat", -1, treatBillEntity.ID, treatBillEntity.CustomerID);
                if (customerGrowthEntities.size() > 0)
                    customerGrowthMapper.createCustomerGrowth(customerGrowthEntities);
            }
            treatBillService.send(treatBillEntity.ID);

            return treatBillEntity.ID;
        }
        return "";
    }
}



