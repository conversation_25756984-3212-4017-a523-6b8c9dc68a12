package com.zhelian.service.ibeauty.treatProductCommission;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.TreatProductCategoryCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.TreatProductCategoryCommissionEntity;
import com.zhelian.model.ibeauty.entity.TreatProductCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatProductChildCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.TreatProductCategoryCommissionOutputForm;
import com.zhelian.model.ibeauty.form.output.TreatProductCategoryOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import static com.zhelian.core.utils.ListUtil.listToTree;


@Service
public class TreatProductCategoryCommissionService {

    @Autowired
    TreatProductCategoryCommissionMapper treatProductCategoryCommissionMapper;


    public BaseOutput allTreatProductCategoryCommission(TreatProductCategoryCommissionQueryForm form) {
        TreatProductCategoryOutputForm treatProductCategoryOutputForm = treatProductCategoryCommissionMapper.treatProductCategoryCommission(form.EntityID);
        List<TreatProductCategoryCommissionOutputForm> all = treatProductCategoryCommissionMapper.allTreatProductCategoryCommission(form.EntityID);
        JSONArray result = listToTree(JSONArray.parseArray(JSON.toJSONString(all, JSONWriter.Feature.WriteMapNullValue)), "CategoryID", "ParentID", "Child");
        treatProductCategoryOutputForm.Category = result;
        return BaseOutputForm.success(treatProductCategoryOutputForm);
    }

    @Transactional
    public BaseOutput updateTreatProductCategoryCommission(TreatProductCategoryAddForm form) {
        TreatProductCommissionSchemeEntity treatProductCommissionSchemeEntity = new TreatProductCommissionSchemeEntity();
        treatProductCommissionSchemeEntity.EntityID = form.EntityID;
        treatProductCommissionSchemeEntity.PayRate = form.PayRate;
        treatProductCommissionSchemeEntity.PayFixed = form.PayFixed;
        treatProductCommissionSchemeEntity.CardRate = form.CardRate;
        treatProductCommissionSchemeEntity.CardFixed = form.CardFixed;
        treatProductCommissionSchemeEntity.CardLargessRate = form.CardLargessRate;
        treatProductCommissionSchemeEntity.CardLargessFixed = form.CardLargessFixed;
        treatProductCommissionSchemeEntity.LargessRate = form.LargessRate;
        treatProductCommissionSchemeEntity.LargessFixed = form.LargessFixed;
        treatProductCommissionSchemeEntity.SpecialBenefit = form.SpecialBenefit;

        treatProductCategoryCommissionMapper.updateTreatProductCommissionScheme(treatProductCommissionSchemeEntity);

        treatProductCategoryCommissionMapper.deleteTreatProductCategoryCommissionEntity(form.EntityID);

        List<TreatProductCategoryCommissionEntity> list = new ArrayList<>();
        if (form.Category != null && form.Category.size() > 0) {
            for (TreatProductCategoryCommissionAddForm treatProductCategoryCommissionAddForm : form.Category) {
                if (treatProductCategoryCommissionAddForm.PayRate != null || treatProductCategoryCommissionAddForm.PayFixed != null ||
                        treatProductCategoryCommissionAddForm.CardRate != null || treatProductCategoryCommissionAddForm.CardFixed != null ||
                        treatProductCategoryCommissionAddForm.CardLargessRate != null || treatProductCategoryCommissionAddForm.CardLargessFixed != null ||
                        treatProductCategoryCommissionAddForm.LargessRate != null || treatProductCategoryCommissionAddForm.LargessFixed != null ||
                        treatProductCategoryCommissionAddForm.SpecialBenefit != null) {
                    TreatProductCategoryCommissionEntity treatProductCategoryCommissionEntity = new TreatProductCategoryCommissionEntity();
                    treatProductCategoryCommissionEntity.EntityID = form.EntityID;
                    treatProductCategoryCommissionEntity.ProductCategoryID = treatProductCategoryCommissionAddForm.CategoryID;
                    treatProductCategoryCommissionEntity.PayRate = treatProductCategoryCommissionAddForm.PayRate;
                    treatProductCategoryCommissionEntity.PayFixed = treatProductCategoryCommissionAddForm.PayFixed;
                    treatProductCategoryCommissionEntity.CardRate = treatProductCategoryCommissionAddForm.CardRate;
                    treatProductCategoryCommissionEntity.CardFixed = treatProductCategoryCommissionAddForm.CardFixed;
                    treatProductCategoryCommissionEntity.CardLargessRate = treatProductCategoryCommissionAddForm.CardLargessRate;
                    treatProductCategoryCommissionEntity.CardLargessFixed = treatProductCategoryCommissionAddForm.CardLargessFixed;
                    treatProductCategoryCommissionEntity.LargessRate = treatProductCategoryCommissionAddForm.LargessRate;
                    treatProductCategoryCommissionEntity.LargessFixed = treatProductCategoryCommissionAddForm.LargessFixed;
                    treatProductCategoryCommissionEntity.SpecialBenefit = treatProductCategoryCommissionAddForm.SpecialBenefit;
                    list.add(treatProductCategoryCommissionEntity);
                }

                for (TreatProductChildCategoryCommissionAddForm treatProductChildCategoryCommissionAddForm : treatProductCategoryCommissionAddForm.Child) {
                    if (treatProductChildCategoryCommissionAddForm.PayRate != null || treatProductChildCategoryCommissionAddForm.PayFixed != null ||
                            treatProductChildCategoryCommissionAddForm.CardRate != null || treatProductChildCategoryCommissionAddForm.CardFixed != null ||
                            treatProductChildCategoryCommissionAddForm.CardLargessRate != null || treatProductChildCategoryCommissionAddForm.CardLargessFixed != null ||
                            treatProductChildCategoryCommissionAddForm.LargessRate != null || treatProductChildCategoryCommissionAddForm.LargessFixed != null ||
                            treatProductChildCategoryCommissionAddForm.SpecialBenefit != null) {
                        TreatProductCategoryCommissionEntity treatProductCategoryCommissionEntity2 = new TreatProductCategoryCommissionEntity();
                        treatProductCategoryCommissionEntity2.EntityID = form.EntityID;
                        treatProductCategoryCommissionEntity2.ProductCategoryID = treatProductChildCategoryCommissionAddForm.CategoryID;
                        treatProductCategoryCommissionEntity2.PayRate = treatProductChildCategoryCommissionAddForm.PayRate;
                        treatProductCategoryCommissionEntity2.PayFixed = treatProductChildCategoryCommissionAddForm.PayFixed;
                        treatProductCategoryCommissionEntity2.CardRate = treatProductChildCategoryCommissionAddForm.CardRate;
                        treatProductCategoryCommissionEntity2.CardFixed = treatProductChildCategoryCommissionAddForm.CardFixed;
                        treatProductCategoryCommissionEntity2.CardLargessRate = treatProductChildCategoryCommissionAddForm.CardLargessRate;
                        treatProductCategoryCommissionEntity2.CardLargessFixed = treatProductChildCategoryCommissionAddForm.CardLargessFixed;
                        treatProductCategoryCommissionEntity2.LargessRate = treatProductChildCategoryCommissionAddForm.LargessRate;
                        treatProductCategoryCommissionEntity2.LargessFixed = treatProductChildCategoryCommissionAddForm.LargessFixed;
                        treatProductCategoryCommissionEntity2.SpecialBenefit = treatProductChildCategoryCommissionAddForm.SpecialBenefit;
                        list.add(treatProductCategoryCommissionEntity2);
                    }
                }
            }
        }
        if (list != null && list.size() > 0) {
            List<List<TreatProductCategoryCommissionEntity>> subs = Lists.partition(list, 20);
            for (List<TreatProductCategoryCommissionEntity> entityList : subs) {
                treatProductCategoryCommissionMapper.insertTreatProductCategoryCommissionEntity(entityList);
            }
        }
        return BaseOutput.success();
    }

}