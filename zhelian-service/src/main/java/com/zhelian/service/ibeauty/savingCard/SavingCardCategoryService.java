package com.zhelian.service.ibeauty.savingCard;

import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.SavingCardCategoryMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SavingCardCategoryEntity;
import com.zhelian.model.ibeauty.form.input.SavingCardCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.SavingCardCategoryMoveForm;
import com.zhelian.model.ibeauty.form.input.SavingCardCategoryQueryForm;
import com.zhelian.model.ibeauty.form.input.SavingCardCategoryUpdateForm;
import com.zhelian.model.ibeauty.form.output.SavingCardCategoryOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * @Description:
 * @Param:
 * @returns:
 * @Author: xuehao
 * @Date: 2020/3/19 10:44
 */
@Service
public class SavingCardCategoryService {

    @Autowired
    SavingCardCategoryMapper savingCardCategoryMapper;

    public BaseOutput savingCardCategoryList(SavingCardCategoryQueryForm form) {

        List<SavingCardCategoryOutputForm> list = savingCardCategoryMapper.savingCardCategoryList(form.Name, form.Active);

        return BaseOutputForm.success(list);
    }


    public BaseOutput createSavingCardCategory(SavingCardCategoryAddForm form) {

        Integer Sequence = savingCardCategoryMapper.getMaxSequence();
        SavingCardCategoryEntity savingCardCategoryEntity = new SavingCardCategoryEntity();
        savingCardCategoryEntity.Name = form.Name;
        savingCardCategoryEntity.Sequence = Sequence != null ? Sequence + 1 : 1;

        savingCardCategoryMapper.createSavingCardCategory(savingCardCategoryEntity);

        return BaseOutput.success();
    }

    public BaseOutput updateSavingCardCategory(SavingCardCategoryUpdateForm form) {
        if (form.Active == false) {
            Integer count = savingCardCategoryMapper.savingCardByCategoryCount(form.ID);
            if (count > 0)
                return BaseOutput.failed("该分类下有储值卡，不可以改为无效");
        }
        SavingCardCategoryEntity savingCardCategoryEntity = new SavingCardCategoryEntity();
        savingCardCategoryEntity.ID = form.ID;
        savingCardCategoryEntity.Name = form.Name;
        savingCardCategoryEntity.Active = form.Active;

        savingCardCategoryMapper.updateSavingCardCategory(savingCardCategoryEntity);

        return BaseOutput.success();
    }

    public BaseOutput move(SavingCardCategoryMoveForm form) {

        //重新排序
        LinkedList<Integer> linkedList = savingCardCategoryMapper.getASCList(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<SavingCardCategoryEntity> list = new ArrayList<>();
        for (Integer ID : linkedList) {
            SavingCardCategoryEntity savingCardCategoryEntity = new SavingCardCategoryEntity();
            savingCardCategoryEntity.ID = ID;
            savingCardCategoryEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(savingCardCategoryEntity);
        }

        savingCardCategoryMapper.updateSequence(list);
        return BaseOutput.success();
    }

}