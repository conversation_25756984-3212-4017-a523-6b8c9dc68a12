package com.zhelian.service.ibeauty.saleGeneralCardCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.SaleGeneralCardCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SaleGeneralCardCommissionEntity;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardsCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.SaleGeneralCardCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleGeneralCardCommissionService {

    @Autowired
    SaleGeneralCardCommissionMapper saleGeneralCardCommissionMapper;


    public BaseOutput allSaleGeneralCardCommission(SaleGeneralCardCategoryHandlerCommissionQueryForm form) {
        List<SaleGeneralCardCommissionOutputForm> list = saleGeneralCardCommissionMapper.allSaleGeneralCardCommission(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleGeneralCardCommission(SaleGeneralCardsCommissionAddForm form) {

        saleGeneralCardCommissionMapper.deleteSaleGeneralCardCommissionByEntityIDAndCategory(form.EntityID, form.CategoryID);
        List<SaleGeneralCardCommissionEntity> list = new ArrayList<>();
        if (form.GeneralCard != null && form.GeneralCard.size() > 0) {
            for (SaleGeneralCardCommissionAddForm saleGeneralCardCommissionAddForm : form.GeneralCard) {
                if (saleGeneralCardCommissionAddForm.PayRate != null || saleGeneralCardCommissionAddForm.PayFixed != null ||
                        saleGeneralCardCommissionAddForm.SavingCardRate != null || saleGeneralCardCommissionAddForm.SavingCardFixed != null ||
                        saleGeneralCardCommissionAddForm.SavingCardLargessRate != null || saleGeneralCardCommissionAddForm.SavingCardLargessFixed != null ||
                        saleGeneralCardCommissionAddForm.SpecialBenefit != null) {
                    SaleGeneralCardCommissionEntity saleGeneralCardCommissionEntity = new SaleGeneralCardCommissionEntity();
                    saleGeneralCardCommissionEntity.EntityID = form.EntityID;
                    saleGeneralCardCommissionEntity.GeneralCardID = saleGeneralCardCommissionAddForm.GeneralCardID;
                    saleGeneralCardCommissionEntity.PayFixed = saleGeneralCardCommissionAddForm.PayFixed;
                    saleGeneralCardCommissionEntity.PayRate = saleGeneralCardCommissionAddForm.PayRate;
                    saleGeneralCardCommissionEntity.SavingCardFixed = saleGeneralCardCommissionAddForm.SavingCardFixed;
                    saleGeneralCardCommissionEntity.SavingCardRate = saleGeneralCardCommissionAddForm.SavingCardRate;
                    saleGeneralCardCommissionEntity.SavingCardLargessFixed = saleGeneralCardCommissionAddForm.SavingCardLargessFixed;
                    saleGeneralCardCommissionEntity.SavingCardLargessRate = saleGeneralCardCommissionAddForm.SavingCardLargessRate;
                    saleGeneralCardCommissionEntity.SpecialBenefit = saleGeneralCardCommissionAddForm.SpecialBenefit;
                    list.add(saleGeneralCardCommissionEntity);
                }
            }

            if (list != null && list.size() > 0) {
                List<List<SaleGeneralCardCommissionEntity>> subs = Lists.partition(list, 20);
                for (List<SaleGeneralCardCommissionEntity> entityList : subs) {
                    saleGeneralCardCommissionMapper.insertSaleGeneralCardCommission(entityList);
                }
            }
        }
        return BaseOutput.success();
    }

}