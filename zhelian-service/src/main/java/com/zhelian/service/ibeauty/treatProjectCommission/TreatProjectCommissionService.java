package com.zhelian.service.ibeauty.treatProjectCommission;


import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.TreatProjectCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.TreatProjectCommissionEntity;
import com.zhelian.model.ibeauty.form.input.TreatProjectCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectsCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.TreatProjectCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatProjectCommissionService {

    @Autowired
    TreatProjectCommissionMapper treatProjectCommissionMapper;

    public BaseOutput allTreatProjectCommission(TreatProjectCommissionQueryForm form) {
        List<TreatProjectCommissionOutputForm> list = treatProjectCommissionMapper.allTreatProjectCommission(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatProjectCommission(TreatProjectsCommissionAddForm form) {

        treatProjectCommissionMapper.deleteTreatProjectCommissionByEntityAndCategory(form.EntityID, form.CategoryID);
        List<TreatProjectCommissionEntity> list = new ArrayList<>();
        if (form.Project != null && form.Project.size() > 0) {
            for (TreatProjectCommissionAddForm treatProjectCommissionAddForm : form.Project) {
                if (treatProjectCommissionAddForm.PayRate != null || treatProjectCommissionAddForm.PayFixed != null ||
                        treatProjectCommissionAddForm.CardRate != null || treatProjectCommissionAddForm.CardFixed != null ||
                        treatProjectCommissionAddForm.CardLargessRate != null || treatProjectCommissionAddForm.CardLargessFixed != null ||
                        treatProjectCommissionAddForm.LargessRate != null || treatProjectCommissionAddForm.LargessFixed != null ||
                        treatProjectCommissionAddForm.SpecialBenefit != null) {
                    TreatProjectCommissionEntity treatProjectCommissionEntity = new TreatProjectCommissionEntity();
                    treatProjectCommissionEntity.EntityID = form.EntityID;
                    treatProjectCommissionEntity.ProjectID = treatProjectCommissionAddForm.ProjectID;
                    treatProjectCommissionEntity.PayRate = treatProjectCommissionAddForm.PayRate;
                    treatProjectCommissionEntity.PayFixed = treatProjectCommissionAddForm.PayFixed;
                    treatProjectCommissionEntity.CardRate = treatProjectCommissionAddForm.CardRate;
                    treatProjectCommissionEntity.CardFixed = treatProjectCommissionAddForm.CardFixed;
                    treatProjectCommissionEntity.CardLargessRate = treatProjectCommissionAddForm.CardLargessRate;
                    treatProjectCommissionEntity.CardLargessFixed = treatProjectCommissionAddForm.CardLargessFixed;
                    treatProjectCommissionEntity.LargessFixed = treatProjectCommissionAddForm.LargessFixed;
                    treatProjectCommissionEntity.LargessRate = treatProjectCommissionAddForm.LargessRate;
                    treatProjectCommissionEntity.SpecialBenefit = treatProjectCommissionAddForm.SpecialBenefit;
                    list.add(treatProjectCommissionEntity);
                }
            }

            if (list.size() > 0) {
                List<List<TreatProjectCommissionEntity>> subs = Lists.partition(list, 20);
                for (List<TreatProjectCommissionEntity> list1 : subs) {
                    treatProjectCommissionMapper.insertTreatProjectCommission(list1);
                }
            }
        }
        return BaseOutput.success();
    }

}