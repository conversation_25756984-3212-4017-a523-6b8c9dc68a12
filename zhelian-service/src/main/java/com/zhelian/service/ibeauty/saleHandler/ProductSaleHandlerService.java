package com.zhelian.service.ibeauty.saleHandler;


import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.ProductSaleHandlerMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.ProductSaleHandlerEntity;
import com.zhelian.model.ibeauty.entity.ProductSaleHandlerJobTypeEntity;
import com.zhelian.model.ibeauty.form.input.SaleHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerMoveForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerUpdateForm;
import com.zhelian.model.ibeauty.form.output.SaleHandlerOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;


/**
 * @Description:
 * @Param: No such property: code for class: Script1
 * @returns:
 * @Author: xuehao
 * @Date: 2020/3/30 14:46
 */
@Service
public class ProductSaleHandlerService {

    @Autowired
    ProductSaleHandlerMapper productSaleHandlerMapper;


    public BaseOutput allProductSaleHandler(SaleHandlerQueryForm form) {

        List<SaleHandlerOutputForm> list = productSaleHandlerMapper.allProductSaleHandler(form.Name, form.EntityID, form.Active);
        return BaseOutputForm.success(list);

    }

    @Transactional
    public BaseOutput createProductSaleHandler(SaleHandlerAddForm form) {
        Integer Sequence = productSaleHandlerMapper.getMaxSequence();
        ProductSaleHandlerEntity productSaleHandlerEntity = new ProductSaleHandlerEntity();
        productSaleHandlerEntity.Name = form.Name;
        productSaleHandlerEntity.EntityID = form.EntityID;
        productSaleHandlerEntity.Sequence = Sequence != null ? Sequence + 1 : 1;

        productSaleHandlerMapper.createProductSaleHandler(productSaleHandlerEntity);

        insertSaleHandlerJobType(form.JobType, productSaleHandlerEntity.ID);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateProductSaleHandler(SaleHandlerUpdateForm form) {

        ProductSaleHandlerEntity productSaleHandlerEntity = new ProductSaleHandlerEntity();
        productSaleHandlerEntity.Name = form.Name;
        productSaleHandlerEntity.ID = form.ID;
        productSaleHandlerEntity.EntityID = form.EntityID;
        productSaleHandlerEntity.Active = form.Active;

        productSaleHandlerMapper.updateProductSaleHandler(productSaleHandlerEntity);

        productSaleHandlerMapper.deleteSaleHandlerJobType(form.ID);

        insertSaleHandlerJobType(form.JobType, form.ID);

        return BaseOutput.success();
    }


    public void insertSaleHandlerJobType(Integer[] JobType, Integer ID) {
        List<ProductSaleHandlerJobTypeEntity> list = new ArrayList<>();
        for (Integer typeID : JobType) {
            ProductSaleHandlerJobTypeEntity productSaleHandlerJobTypeEntity = new ProductSaleHandlerJobTypeEntity();
            productSaleHandlerJobTypeEntity.ProductSaleHandlerID = ID;
            productSaleHandlerJobTypeEntity.JobTypeID = typeID;
            list.add(productSaleHandlerJobTypeEntity);
        }
        productSaleHandlerMapper.insertProductSaleHandlerJobType(list);
    }

    public BaseOutput moveProductSaleHandler(SaleHandlerMoveForm form) {
        //重新排序
        LinkedList<Integer> linkedList = productSaleHandlerMapper.getIDOrderBySequenceASC(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<ProductSaleHandlerEntity> list = new ArrayList<>();

        for (Integer ID : linkedList) {
            ProductSaleHandlerEntity productSaleHandlerEntity = new ProductSaleHandlerEntity();
            productSaleHandlerEntity.ID = ID;
            productSaleHandlerEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(productSaleHandlerEntity);
        }
        productSaleHandlerMapper.updateSequence(list);

        return BaseOutput.success();
    }

}