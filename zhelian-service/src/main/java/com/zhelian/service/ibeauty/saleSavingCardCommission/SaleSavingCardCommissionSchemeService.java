package com.zhelian.service.ibeauty.saleSavingCardCommission;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.ibeauty.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.SaleSavingCardCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardCommissionSchemeDeleteForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardCommissionSchemeQueryForm;
import com.zhelian.model.ibeauty.form.output.SaleSavingCardCommissionSchemeOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class SaleSavingCardCommissionSchemeService {

    @Autowired
    SaleSavingCardCommissionSchemeMapper saleSavingCardCommissionSchemeMapper;
    @Autowired
    SaleSavingCardCategoryCommissionMapper saleSavingCardCategoryCommissionMapper;
    @Autowired
    SaleSavingCardCategoryHandlerCommissionMapper saleSavingCardCategoryHandlerCommissionMapper;
    @Autowired
    SaleSavingCardCommissionMapper saleSavingCardCommissionMapper;
    @Autowired
    SaleSavingCardHandlerCommissionMapper saleSavingCardHandlerCommissionMapper;
    @Autowired
    SaleSavingCardSchemeHandlerCommissionMapper saleSavingCardSchemeHandlerCommissionMapper;

    public BaseOutput saleSavingCardCommissionSchemeList(SaleSavingCardCommissionSchemeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleSavingCardCommissionSchemeOutputForm> list = saleSavingCardCommissionSchemeMapper.saleSavingCardCommissionSchemeList(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput createSaleSavingCardCommissionScheme(SaleSavingCardCommissionSchemeAddForm form) {
        Integer count = saleSavingCardCommissionSchemeMapper.getSaleSavingCardCommissionScheme(form.EntityID);
        if (count > 0)
            return BaseOutput.failed("组织单位已经存在");
        SaleSavingCardCommissionSchemeEntity saleSavingCardCommissionSchemeEntity = new SaleSavingCardCommissionSchemeEntity();
        saleSavingCardCommissionSchemeEntity.EntityID = form.EntityID;
        saleSavingCardCommissionSchemeMapper.createSaleSavingCardCommissionScheme(saleSavingCardCommissionSchemeEntity);
        return BaseOutput.success();

    }

    public BaseOutput deleteSaleSavingCardCommissionScheme(SaleSavingCardCommissionSchemeDeleteForm form) {
        saleSavingCardCommissionSchemeMapper.deleteSaleSavingCardCommissionScheme(form.EntityID);
        saleSavingCardCategoryCommissionMapper.deleteSaleSavingCardCategoryCommission(form.EntityID);
        saleSavingCardCategoryHandlerCommissionMapper.deleteSaleSavingCardCategoryHandlerCommissionByEntity(form.EntityID);
        saleSavingCardCategoryHandlerCommissionMapper.deleteSaleSavingCardCategoryHandlerJobTypeCommissionByEntity(form.EntityID);
        saleSavingCardCommissionMapper.deleteSaleSavingCardCommissionByEntity(form.EntityID);
        saleSavingCardHandlerCommissionMapper.deleteSaleSavingCardHandlerCommissionByEntity(form.EntityID);
        saleSavingCardHandlerCommissionMapper.deleteSaleSavingCardHandlerJobTypeCommissionByEntity(form.EntityID);
        saleSavingCardSchemeHandlerCommissionMapper.deleteSaleSavingCardSchemeHandler(form.EntityID);
        saleSavingCardSchemeHandlerCommissionMapper.deleteSaleSavingCardSchemeHandlerJobType(form.EntityID);
        return BaseOutput.success();
    }
}