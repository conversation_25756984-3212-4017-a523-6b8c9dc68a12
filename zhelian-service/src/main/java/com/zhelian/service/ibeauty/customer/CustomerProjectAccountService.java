package com.zhelian.service.ibeauty.customer;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.ibeauty.CustomerProjectAccountMapper;
import com.zhelian.mapper.ibeauty.ProjectAccountMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.ProjectAccountEntity;
import com.zhelian.model.ibeauty.form.input.AccountQueryForm;
import com.zhelian.model.ibeauty.form.output.AccountOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class CustomerProjectAccountService {

    @Autowired
    private CustomerProjectAccountMapper customerProjectAccountMapper;
    @Autowired
    private ProjectAccountMapper projectAccountMapper;


    public BaseOutput treatBill(AccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(customerProjectAccountMapper.getTreatBill(form.ID));
    }

    public BaseOutput refundBill(AccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(customerProjectAccountMapper.getRefundBill(form.ID));
    }

    public BaseOutput refundTreatBill(AccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(customerProjectAccountMapper.getRefundTreatBill(form.ID));
    }

    public BaseOutput transferBill(AccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(customerProjectAccountMapper.getTransferBill(form.ID));
    }

    public BaseOutput repayment(AccountQueryForm form) {
        ProjectAccountEntity projectAccountByAccountID = projectAccountMapper.getProjectAccountByAccountID(Integer.parseInt(form.ID));

        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<AccountOutputForm> list = null;
        if (projectAccountByAccountID.PackageCardAccountID == null) {
            list = customerProjectAccountMapper.getRepayment(form.ID);
        } else {
            list = customerProjectAccountMapper.getPackageCardRepayment(form.ID);
        }
        return BasePageInfo.success(list);
    }

    public BaseOutput arrearAmountProcess(AccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(customerProjectAccountMapper.getArrearAmountProcess(form.ID));
    }
}