package com.zhelian.service.ibeauty.saleProductCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.SalePackageCardProductCategoryHandlerCommissionMapper;
import com.zhelian.mapper.ibeauty.SaleProductCategoryHandlerCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SalePackageCardProductCategoryHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SalePackageCardProductCategoryHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleProductCategoryHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleProductCategoryHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.SaleProductCategoryHandlerCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleProductCategoryHandlerCommissionService {

    @Autowired
    SaleProductCategoryHandlerCommissionMapper saleProductCategoryHandlerCommissionMapper;
    @Autowired
    SalePackageCardProductCategoryHandlerCommissionMapper salePackageCardProductCategoryHandlerCommissionMapper;


    public BaseOutput allSaleProductCategoryHandlerCommission(SaleProductCategoryHandlerCommissionQueryForm form) {
        List<SaleProductCategoryHandlerCommissionOutputForm> list = saleProductCategoryHandlerCommissionMapper.allSaleProductCategoryHandlerCommission(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSalePackageCardProductCategoryHandlerCommission(SaleProductCategoryHandlerCommissionQueryForm form) {
        List<SaleProductCategoryHandlerCommissionOutputForm> list = salePackageCardProductCategoryHandlerCommissionMapper.allSalePackageCardProductCategoryHandlerCommission(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleProductCategoryHandlerCommission(SaleProductCategoryHandlerCommissionAddForm form) {


        saleProductCategoryHandlerCommissionMapper.deleteSaleProductCategoryHandler(form.EntityID, form.CategoryID);
        saleProductCategoryHandlerCommissionMapper.deleteSaleProductCategoryHandlerJobType(form.EntityID, form.CategoryID);
        List<SaleProductCategoryHandlerCommissionEntity> listSaleProductCategoryHandlerCommission = new ArrayList<>();
        List<SaleProductCategoryHandlerJobTypeCommissionEntity> listSaleProductCategoryHandlerJobTypeCommission = new ArrayList<>();
        if (form.ProductHandler != null && form.ProductHandler.size() > 0) {
            for (SaleProductCategoryHandlerAddForm saleProductCategoryHandlerAddForm : form.ProductHandler) {
                if (saleProductCategoryHandlerAddForm.PayRate != null || saleProductCategoryHandlerAddForm.PayFixed != null ||
                        saleProductCategoryHandlerAddForm.SavingCardRate != null || saleProductCategoryHandlerAddForm.SavingCardFixed != null ||
                        saleProductCategoryHandlerAddForm.SavingCardLargessRate != null || saleProductCategoryHandlerAddForm.SavingCardLargessFixed != null ||
                        saleProductCategoryHandlerAddForm.SpecialBenefit != null) {
                    SaleProductCategoryHandlerCommissionEntity saleProductCategoryHandlerCommissionEntity = new SaleProductCategoryHandlerCommissionEntity();
                    saleProductCategoryHandlerCommissionEntity.EntityID = form.EntityID;
                    saleProductCategoryHandlerCommissionEntity.ProductCategoryID = form.CategoryID;
                    saleProductCategoryHandlerCommissionEntity.ProductSaleHandlerID = saleProductCategoryHandlerAddForm.SaleHandlerID;
                    saleProductCategoryHandlerCommissionEntity.PayFixed = saleProductCategoryHandlerAddForm.PayFixed;
                    saleProductCategoryHandlerCommissionEntity.PayRate = saleProductCategoryHandlerAddForm.PayRate;
                    saleProductCategoryHandlerCommissionEntity.SavingCardFixed = saleProductCategoryHandlerAddForm.SavingCardFixed;
                    saleProductCategoryHandlerCommissionEntity.SavingCardRate = saleProductCategoryHandlerAddForm.SavingCardRate;
                    saleProductCategoryHandlerCommissionEntity.SavingCardLargessFixed = saleProductCategoryHandlerAddForm.SavingCardLargessFixed;
                    saleProductCategoryHandlerCommissionEntity.SavingCardLargessRate = saleProductCategoryHandlerAddForm.SavingCardLargessRate;
                    saleProductCategoryHandlerCommissionEntity.SpecialBenefit = saleProductCategoryHandlerAddForm.SpecialBenefit;
                    listSaleProductCategoryHandlerCommission.add(saleProductCategoryHandlerCommissionEntity);
                }

                if (saleProductCategoryHandlerAddForm.HandlerJobTypeCommission != null && saleProductCategoryHandlerAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (SaleProductCategoryHandlerJobTypeCommissionAddForm saleProductCategoryHandlerJobTypeCommissionAddForm : saleProductCategoryHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleProductCategoryHandlerJobTypeCommissionAddForm.PayRate != null || saleProductCategoryHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardRate != null || saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed != null ||
                                saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null || saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed != null ||
                                saleProductCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SaleProductCategoryHandlerJobTypeCommissionEntity saleProductCategoryHandlerJobTypeCommissionEntity = new SaleProductCategoryHandlerJobTypeCommissionEntity();
                            saleProductCategoryHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            saleProductCategoryHandlerJobTypeCommissionEntity.ProductCategoryID = form.CategoryID;
                            saleProductCategoryHandlerJobTypeCommissionEntity.ProductSaleHandlerID = saleProductCategoryHandlerAddForm.SaleHandlerID;
                            saleProductCategoryHandlerJobTypeCommissionEntity.JobTypeID = saleProductCategoryHandlerJobTypeCommissionAddForm.JobTypeID;
                            saleProductCategoryHandlerJobTypeCommissionEntity.PayFixed = saleProductCategoryHandlerJobTypeCommissionAddForm.PayFixed;
                            saleProductCategoryHandlerJobTypeCommissionEntity.PayRate = saleProductCategoryHandlerJobTypeCommissionAddForm.PayRate;
                            saleProductCategoryHandlerJobTypeCommissionEntity.SavingCardFixed = saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed;
                            saleProductCategoryHandlerJobTypeCommissionEntity.SavingCardRate = saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardRate;
                            saleProductCategoryHandlerJobTypeCommissionEntity.SavingCardLargessFixed = saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed;
                            saleProductCategoryHandlerJobTypeCommissionEntity.SavingCardLargessRate = saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            saleProductCategoryHandlerJobTypeCommissionEntity.SpecialBenefit = saleProductCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            listSaleProductCategoryHandlerJobTypeCommission.add(saleProductCategoryHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
        }


        salePackageCardProductCategoryHandlerCommissionMapper.deleteSalePackageCardProductCategoryHandler(form.EntityID, form.CategoryID);
        salePackageCardProductCategoryHandlerCommissionMapper.deleteSalePackageCardProductCategoryHandlerJobType(form.EntityID, form.CategoryID);
        List<SalePackageCardProductCategoryHandlerCommissionEntity> listSalePackageCardProductCategoryHandlerCommission = new ArrayList<>();
        List<SalePackageCardProductCategoryHandlerJobTypeCommissionEntity> listSalePackageCardProductCategoryHandlerJobTypeCommission = new ArrayList<>();
        if (form.PackageCardProductHandler != null && form.PackageCardProductHandler.size() > 0) {
            for (SaleProductCategoryHandlerAddForm saleProductCategoryHandlerAddForm : form.PackageCardProductHandler) {
                if (saleProductCategoryHandlerAddForm.PayRate != null || saleProductCategoryHandlerAddForm.PayFixed != null ||
                        saleProductCategoryHandlerAddForm.SavingCardRate != null || saleProductCategoryHandlerAddForm.SavingCardFixed != null ||
                        saleProductCategoryHandlerAddForm.SavingCardLargessRate != null || saleProductCategoryHandlerAddForm.SavingCardLargessFixed != null ||
                        saleProductCategoryHandlerAddForm.SpecialBenefit != null) {
                    SalePackageCardProductCategoryHandlerCommissionEntity salePackageCardProductCategoryHandlerCommissionEntity = new SalePackageCardProductCategoryHandlerCommissionEntity();
                    salePackageCardProductCategoryHandlerCommissionEntity.EntityID = form.EntityID;
                    salePackageCardProductCategoryHandlerCommissionEntity.ProductCategoryID = form.CategoryID;
                    salePackageCardProductCategoryHandlerCommissionEntity.PackageCardSaleHandlerID = saleProductCategoryHandlerAddForm.SaleHandlerID;
                    salePackageCardProductCategoryHandlerCommissionEntity.PayFixed = saleProductCategoryHandlerAddForm.PayFixed;
                    salePackageCardProductCategoryHandlerCommissionEntity.PayRate = saleProductCategoryHandlerAddForm.PayRate;
                    salePackageCardProductCategoryHandlerCommissionEntity.SavingCardFixed = saleProductCategoryHandlerAddForm.SavingCardFixed;
                    salePackageCardProductCategoryHandlerCommissionEntity.SavingCardRate = saleProductCategoryHandlerAddForm.SavingCardRate;
                    salePackageCardProductCategoryHandlerCommissionEntity.SavingCardLargessFixed = saleProductCategoryHandlerAddForm.SavingCardLargessFixed;
                    salePackageCardProductCategoryHandlerCommissionEntity.SavingCardLargessRate = saleProductCategoryHandlerAddForm.SavingCardLargessRate;
                    salePackageCardProductCategoryHandlerCommissionEntity.SpecialBenefit = saleProductCategoryHandlerAddForm.SpecialBenefit;
                    listSalePackageCardProductCategoryHandlerCommission.add(salePackageCardProductCategoryHandlerCommissionEntity);
                }

                if (saleProductCategoryHandlerAddForm.HandlerJobTypeCommission != null && saleProductCategoryHandlerAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (SaleProductCategoryHandlerJobTypeCommissionAddForm saleProductCategoryHandlerJobTypeCommissionAddForm : saleProductCategoryHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleProductCategoryHandlerJobTypeCommissionAddForm.PayRate != null || saleProductCategoryHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardRate != null || saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed != null ||
                                saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null || saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed != null ||
                                saleProductCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SalePackageCardProductCategoryHandlerJobTypeCommissionEntity salePackageCardProductCategoryHandlerJobTypeCommissionEntity = new SalePackageCardProductCategoryHandlerJobTypeCommissionEntity();
                            salePackageCardProductCategoryHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            salePackageCardProductCategoryHandlerJobTypeCommissionEntity.ProductCategoryID = form.CategoryID;
                            salePackageCardProductCategoryHandlerJobTypeCommissionEntity.PackageCardSaleHandlerID = saleProductCategoryHandlerAddForm.SaleHandlerID;
                            salePackageCardProductCategoryHandlerJobTypeCommissionEntity.JobTypeID = saleProductCategoryHandlerJobTypeCommissionAddForm.JobTypeID;
                            salePackageCardProductCategoryHandlerJobTypeCommissionEntity.PayFixed = saleProductCategoryHandlerJobTypeCommissionAddForm.PayFixed;
                            salePackageCardProductCategoryHandlerJobTypeCommissionEntity.PayRate = saleProductCategoryHandlerJobTypeCommissionAddForm.PayRate;
                            salePackageCardProductCategoryHandlerJobTypeCommissionEntity.SavingCardFixed = saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed;
                            salePackageCardProductCategoryHandlerJobTypeCommissionEntity.SavingCardRate = saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardRate;
                            salePackageCardProductCategoryHandlerJobTypeCommissionEntity.SavingCardLargessFixed = saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed;
                            salePackageCardProductCategoryHandlerJobTypeCommissionEntity.SavingCardLargessRate = saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            salePackageCardProductCategoryHandlerJobTypeCommissionEntity.SpecialBenefit = saleProductCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            listSalePackageCardProductCategoryHandlerJobTypeCommission.add(salePackageCardProductCategoryHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
        }
        if (listSaleProductCategoryHandlerCommission != null && listSaleProductCategoryHandlerCommission.size() > 0) {
            List<List<SaleProductCategoryHandlerCommissionEntity>> subs = Lists.partition(listSaleProductCategoryHandlerCommission, 20);
            for (List<SaleProductCategoryHandlerCommissionEntity> entityList : subs) {
                saleProductCategoryHandlerCommissionMapper.updateSaleProductCategoryHandlerCommission(entityList);
            }
        }
        if (listSaleProductCategoryHandlerJobTypeCommission != null && listSaleProductCategoryHandlerJobTypeCommission.size() > 0) {
            List<List<SaleProductCategoryHandlerJobTypeCommissionEntity>> subs = Lists.partition(listSaleProductCategoryHandlerJobTypeCommission, 20);
            for (List<SaleProductCategoryHandlerJobTypeCommissionEntity> entityList : subs) {
                saleProductCategoryHandlerCommissionMapper.insertSaleProductCategoryHandlerJobTypeCommission(entityList);
            }
        }
        if (listSalePackageCardProductCategoryHandlerCommission != null && listSalePackageCardProductCategoryHandlerCommission.size() > 0) {
            List<List<SalePackageCardProductCategoryHandlerCommissionEntity>> subs = Lists.partition(listSalePackageCardProductCategoryHandlerCommission, 20);
            for (List<SalePackageCardProductCategoryHandlerCommissionEntity> entityList : subs) {
                salePackageCardProductCategoryHandlerCommissionMapper.updateSalePackageCardProductCategoryHandlerCommission(entityList);
            }
        }
        if (listSalePackageCardProductCategoryHandlerJobTypeCommission != null && listSalePackageCardProductCategoryHandlerJobTypeCommission.size() > 0) {
            List<List<SalePackageCardProductCategoryHandlerJobTypeCommissionEntity>> subs = Lists.partition(listSalePackageCardProductCategoryHandlerJobTypeCommission, 20);
            for (List<SalePackageCardProductCategoryHandlerJobTypeCommissionEntity> entityList : subs) {
                salePackageCardProductCategoryHandlerCommissionMapper.insertSalePackageCardProductCategoryHandlerJobTypeCommission(entityList);
            }
        }
        return BaseOutput.success();
    }


}