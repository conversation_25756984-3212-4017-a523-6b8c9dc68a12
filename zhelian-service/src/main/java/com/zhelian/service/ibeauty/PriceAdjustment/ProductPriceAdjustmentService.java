package com.zhelian.service.ibeauty.PriceAdjustment;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.ibeauty.ProductPriceAdjustmentMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.ProductPriceAdjustmentEntity;
import com.zhelian.model.ibeauty.form.input.ProductPriceAdjustmentAddForm;
import com.zhelian.model.ibeauty.form.input.ProductPriceAdjustmentDeleteForm;
import com.zhelian.model.ibeauty.form.input.ProductPriceAdjustmentQueryForm;
import com.zhelian.model.ibeauty.form.output.ProductPriceAdjustmentOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description:
 * @Param:
 * @returns:
 * @Author: 86158
 * @Date: 2020/3/21 9:34
 */
@Service
public class ProductPriceAdjustmentService {

    @Autowired
    ProductPriceAdjustmentMapper productPriceAdjustmentMapper;

    public BaseOutput productPriceAdjustmentList(ProductPriceAdjustmentQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<ProductPriceAdjustmentOutputForm> list = productPriceAdjustmentMapper.productPriceAdjustmentList(form.ProductName, form.EntityName);
        return BasePageInfo.success(list);
    }

    public BaseOutput createProductPriceAdjustment(ProductPriceAdjustmentAddForm form) {

        Integer count = productPriceAdjustmentMapper.productPriceAdjustmentCount(form.EntityID, form.ProductID);
        if (count > 0)
            return BaseOutput.failed("产品在该组织单位已经设置调整价格");

        ProductPriceAdjustmentEntity productPriceAdjustmentEntity = new ProductPriceAdjustmentEntity();
        productPriceAdjustmentEntity.EntityID = form.EntityID;
        productPriceAdjustmentEntity.Price = form.Price;
        productPriceAdjustmentEntity.ProductID = form.ProductID;
        productPriceAdjustmentMapper.insertProductPriceAdjustmentEntity(productPriceAdjustmentEntity);
        return BaseOutputForm.success();
    }


    public BaseOutput updateProductPriceAdjustmentEntity(ProductPriceAdjustmentAddForm form) {

        ProductPriceAdjustmentEntity productPriceAdjustmentEntity = new ProductPriceAdjustmentEntity();
        productPriceAdjustmentEntity.EntityID = form.EntityID;
        productPriceAdjustmentEntity.Price = form.Price;
        productPriceAdjustmentEntity.ProductID = form.ProductID;
        productPriceAdjustmentMapper.updateProductPriceAdjustmentEntity(productPriceAdjustmentEntity);

        return BaseOutput.success();
    }

    public BaseOutput deleteProductPriceAdjustmentEntity(ProductPriceAdjustmentDeleteForm form) {

        productPriceAdjustmentMapper.deleteProductPriceAdjustmentEntity(form.EntityID, form.ProductID);
        return BaseOutput.success();
    }
}