/**
 * fileName: PayMethodService.java
 * author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * date: 2020/3/4 4:17 PM
 * copyright(c) 2019-2020 xxx公司
 */
package com.zhelian.service.ibeauty.paymethod;

import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.PayMethodMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.PayMethodEntity;
import com.zhelian.model.ibeauty.entity.PayMethodEntityEntity;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.ibeauty.form.output.PayMethodOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * @className: PayMethodService
 * @description:
 * @version: V1.0
 * @author: <PERSON><PERSON>ian<PERSON><PERSON>
 * @date: 2020/3/4 4:17 PM
 */
@Service
public class PayMethodService {

    @Autowired
    PayMethodMapper payMethodMapper;

    public BaseOutput all(PayMethodQueryForm form) {

        List<PayMethodOutputForm> list = payMethodMapper.payMethodList(form.Name, form.Active);

        return BaseOutputForm.success(list);
    }


    public BaseOutput create(PayMethodAddForm form) {

        Integer Sequence = payMethodMapper.getMaxSequence();
        PayMethodEntity payMethodEntity = new PayMethodEntity();
        payMethodEntity.Name = form.Name;
        payMethodEntity.Sequence = Sequence != null ? Sequence + 1 : 1;

        payMethodMapper.createPayMethod(payMethodEntity);
        if (form.PayMethodEntity != null && form.PayMethodEntity.length > 0) {
            List<PayMethodEntityEntity> list = new ArrayList<>();
            for (Integer ID : form.PayMethodEntity) {
                PayMethodEntityEntity payMethodEntityEntity = new PayMethodEntityEntity();
                payMethodEntityEntity.EntityID = ID;
                payMethodEntityEntity.PayMethodID = payMethodEntity.ID;
                list.add(payMethodEntityEntity);
            }
            payMethodMapper.updatePayMethodEntity(list);
        }
        return BaseOutput.success();
    }

    public BaseOutput update(PayMethodUpdateForm form) {

        PayMethodEntity payMethodEntity = new PayMethodEntity();
        payMethodEntity.ID = form.ID;
        payMethodEntity.Name = form.Name;
        payMethodEntity.Active = form.Active;

        payMethodMapper.updatePayMethod(payMethodEntity);
        payMethodMapper.deletePayMethodEntity(form.ID);
        if (form.PayMethodEntity != null && form.PayMethodEntity.length > 0) {
            List<PayMethodEntityEntity> list = new ArrayList<>();
            for (Integer ID : form.PayMethodEntity) {
                PayMethodEntityEntity payMethodEntityEntity = new PayMethodEntityEntity();
                payMethodEntityEntity.EntityID = ID;
                payMethodEntityEntity.PayMethodID = form.ID;
                list.add(payMethodEntityEntity);
            }
            payMethodMapper.updatePayMethodEntity(list);
        }
        return BaseOutput.success();
    }

    public BaseOutput move(PayMethodMoveForm form) {

        //重新排序
        LinkedList<Integer> linkedList = payMethodMapper.getIDOrderBySequenceASC(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<PayMethodEntity> list = new ArrayList<>();

        for (Integer ID : linkedList) {
            PayMethodEntity payMethodEntity = new PayMethodEntity();
            payMethodEntity.ID = ID;
            payMethodEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(payMethodEntity);
        }
        payMethodMapper.updateSequence(list);
        return BaseOutput.success();
    }

    public BaseOutput paymethodEntity(ProjectSaleEntityQueryForm form) {
        return BaseOutputForm.success(payMethodMapper.paymethodEntity(form.ID));
    }
}