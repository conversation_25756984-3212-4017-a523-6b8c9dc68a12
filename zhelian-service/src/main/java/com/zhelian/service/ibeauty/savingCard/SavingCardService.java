package com.zhelian.service.ibeauty.savingCard;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.SavingCardMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.*;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.ibeauty.form.output.SavingCardAllGoodsOutputForm;
import com.zhelian.model.ibeauty.form.output.SavingCardDetailsOutputForm;
import com.zhelian.model.ibeauty.form.output.SavingCardOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import static java.math.BigDecimal.ROUND_HALF_UP;

@Service
public class SavingCardService {

    @Autowired
    SavingCardMapper savingCardMapper;

    public BaseOutput savingCardList(SavingCardQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SavingCardOutputForm> list = savingCardMapper.savingCardList(form.Name, form.SavingCardCategoryID, form.IsAllowSell);
        return BasePageInfo.success(list);
    }

    public BaseOutput getSavingCardDetails(SavingCardQueryEntityForm form) {
        SavingCardDetailsOutputForm productDetails = savingCardMapper.getSavingCardDetails(form.ID);
        return BaseOutputForm.success(productDetails);
    }

    public BaseOutput savingCardSaleEntity(SavingCardQueryEntityForm form) {
        return BaseOutputForm.success(savingCardMapper.savingCardSaleEntity(form));
    }

    public BaseOutput savingCardConsumeEntity(SavingCardQueryEntityForm form) {
        return BaseOutputForm.success(savingCardMapper.savingCardConsumeEntity(form));
    }

    @Transactional
    public BaseOutput create(SavingCardAddForm form) {
        SavingCardEntity savingCardEntity = new SavingCardEntity();
        savingCardEntity.Name = form.Name;
        savingCardEntity.Alias = form.Alias;
        savingCardEntity.SavingCardCategoryID = form.SavingCardCategoryID;
        savingCardEntity.Price = form.Price.setScale(2, ROUND_HALF_UP);
        if (form.LargessPrice == null)
            form.LargessPrice = BigDecimal.ZERO;
        savingCardEntity.LargessPrice = form.LargessPrice.setScale(2, ROUND_HALF_UP);
        savingCardEntity.SaleStartDate = form.SaleStartDate;
        savingCardEntity.SaleEndingDate = form.SaleEndingDate;
        savingCardEntity.ValidType = form.ValidType;
        if (form.ValidType.equals(3)) {
            savingCardEntity.ValidDay = null;
        } else {
            savingCardEntity.ValidDay = form.ValidDay;
        }

        savingCardEntity.IsAllowSell = form.IsAllowSell;
        savingCardEntity.IsSettingLargess = form.IsSettingLargess;
        savingCardEntity.IsAllowSaleDeduct = form.IsAllowSaleDeduct;
        savingCardEntity.IsAllowConsumeDeduct = form.IsAllowConsumeDeduct;
        savingCardEntity.IsGoodsRange = form.IsGoodsRange;
        savingCardEntity.IsLargessGoodsRange = form.IsLargessGoodsRange;
        savingCardEntity.IsUseRechargeRules = form.IsUseRechargeRules;
        savingCardEntity.IsModifyPrice = form.IsModifyPrice;
        savingCardEntity.IsModifyLargessPrice = form.IsModifyLargessPrice;
        savingCardEntity.Remark = form.Remark;
        savingCardEntity.Memo = form.Memo;

        savingCardMapper.addSavingCard(savingCardEntity);
        addSavingCardProjectAndConsumeAndSale(form, savingCardEntity.ID);
        addSavingCardSavingCardRechargeRules(form, savingCardEntity.ID);
        if (form.ImageList.size() > 0) {
            for (int a = 0; a < form.ImageList.size(); a++) {
                savingCardMapper.insertSavingCardImageURL(savingCardEntity.ID, form.ImageList.get(a).ImageUrl, a + 1);
            }
        }
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput update(SavingCardUpdateForm form) {
        SavingCardEntity savingCardEntity = new SavingCardEntity();
        savingCardEntity.ID = form.ID;
        savingCardEntity.Name = form.Name;
        savingCardEntity.Alias = form.Alias;
        savingCardEntity.SavingCardCategoryID = form.SavingCardCategoryID;
        savingCardEntity.Price = form.Price;
        savingCardEntity.LargessPrice = form.LargessPrice;
        savingCardEntity.SaleStartDate = form.SaleStartDate;
        savingCardEntity.SaleEndingDate = form.SaleEndingDate;
        savingCardEntity.ValidType = form.ValidType;
        if (form.ValidType.equals(3)) {
            savingCardEntity.ValidDay = null;
        } else {
            savingCardEntity.ValidDay = form.ValidDay;
        }
        savingCardEntity.IsAllowSell = form.IsAllowSell;
        savingCardEntity.IsSettingLargess = form.IsSettingLargess;
        savingCardEntity.IsAllowSaleDeduct = form.IsAllowSaleDeduct;
        savingCardEntity.IsAllowConsumeDeduct = form.IsAllowConsumeDeduct;
        savingCardEntity.IsGoodsRange = form.IsGoodsRange;
        savingCardEntity.IsLargessGoodsRange = form.IsLargessGoodsRange;
        savingCardEntity.IsUseRechargeRules = form.IsUseRechargeRules;
        savingCardEntity.IsModifyPrice = form.IsModifyPrice;
        savingCardEntity.IsModifyLargessPrice = form.IsModifyLargessPrice;
        savingCardEntity.Remark = form.Remark;
        savingCardEntity.Memo = form.Memo;
        savingCardMapper.updateSavingCard(savingCardEntity);
        savingCardMapper.deleteSavingCardConsumeEntity(form.ID);
        savingCardMapper.deleteSavingCardSaleEntity(form.ID);
        savingCardMapper.deleteSavingCardRechargeRules(form.ID);

        savingCardMapper.deleteSavingCardProduct(form.ID);
        savingCardMapper.deleteSavingCardProductCategory(form.ID);
        savingCardMapper.deleteSavingCardProject(form.ID);
        savingCardMapper.deleteSavingCardProjectCategory(form.ID);
        savingCardMapper.deleteSavingCardGeneralCard(form.ID);
        savingCardMapper.deleteSavingCardGeneralCardCategory(form.ID);
        savingCardMapper.deleteSavingCardPackageCard(form.ID);
        savingCardMapper.deleteSavingCardPackageCardCategory(form.ID);
        savingCardMapper.deleteSavingCardTimeCard(form.ID);
        savingCardMapper.deleteSavingCardTimeCardCategory(form.ID);
        addSavingCardProjectAndConsumeAndSale(form, form.ID);
        addSavingCardSavingCardRechargeRules(form, form.ID);

        List<GoodsImageUrlForm> savingCardImageList = savingCardMapper.getSavingCardImageList(form.ID);
        if (savingCardImageList.size() > 0) {

            savingCardMapper.deleteSavingCardImageByID(form.ID);
        }
        if (form.ImageList.size() > 0) {
            for (int a = 0; a < form.ImageList.size(); a++) {
                savingCardMapper.insertSavingCardImageURL(form.ID, form.ImageList.get(a).ImageUrl, a + 1);
            }
        }
        return BaseOutput.success();
    }

    public static void AssemblyForm(SavingCardProductEntity savingCardProductEntity, SavingCardGoodsAddForm form, Integer ID, boolean IsLargess) {
        savingCardProductEntity.SavingCardID = ID;
        savingCardProductEntity.ProductID = form.ID;
        savingCardProductEntity.IsLargess = IsLargess;
        savingCardProductEntity.PriceType = form.PriceType;
        if (form.PriceType.equals(1))
            form.DiscountPrice = form.DiscountPrice.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
        savingCardProductEntity.DiscountPrice = form.DiscountPrice.setScale(2, ROUND_HALF_UP);
    }

    public static void AssemblyCategoryForm(SavingCardProductCategoryEntity savingCardProductCategoryEntity, SavingCardGoodsCategoryAddForm form, Integer ID, boolean IsLargess) {
        savingCardProductCategoryEntity.SavingCardID = ID;
        savingCardProductCategoryEntity.ProductCategoryID = form.CategoryID;
        savingCardProductCategoryEntity.Discount = form.Discount.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
        savingCardProductCategoryEntity.IsLargess = IsLargess;
    }

    private void addSavingCardProjectAndConsumeAndSale(SavingCardAddForm form, Integer ID) {
        List saleList = new ArrayList();
        for (Integer EntityID : form.SavingCardSaleEntity) {
            SavingCardSaleEntity entity = new SavingCardSaleEntity();
            entity.EntityID = EntityID;
            entity.SavingCardID = ID;
            saleList.add(entity);
        }
        if (saleList.size() > 0)
            savingCardMapper.addSavingCardSaleEntity(saleList);
        List consumeList = new ArrayList();
        for (Integer EntityID : form.SavingCardConsumeEntity) {
            SavingCardSaleEntity entity = new SavingCardSaleEntity();
            entity.EntityID = EntityID;
            entity.SavingCardID = ID;
            consumeList.add(entity);
        }
        if (consumeList.size() > 0)
            savingCardMapper.addSavingCardConsumeEntity(consumeList);

        List product = new ArrayList();
        List project = new ArrayList();
        List generalCard = new ArrayList();
        List timeCard = new ArrayList();
        List packageCard = new ArrayList();
        List productCategory = new ArrayList();
        List projectCategory = new ArrayList();
        List generalCardCategory = new ArrayList();
        List timeCardCategory = new ArrayList();
        List packageCardCategory = new ArrayList();

        if (!form.IsGoodsRange) {
            for (SavingCardGoodsAddForm form1 : form.GoodsRange.Product) {
                SavingCardProductEntity savingCardProductEntity = new SavingCardProductEntity();
                AssemblyForm(savingCardProductEntity, form1, ID, false);
                product.add(savingCardProductEntity);
            }
            for (SavingCardGoodsAddForm form1 : form.GoodsRange.Project) {
                SavingCardProductEntity savingCardProductEntity = new SavingCardProductEntity();
                AssemblyForm(savingCardProductEntity, form1, ID, false);
                project.add(savingCardProductEntity);
            }
            for (SavingCardGoodsAddForm form1 : form.GoodsRange.GeneralCard) {
                SavingCardProductEntity savingCardProductEntity = new SavingCardProductEntity();
                AssemblyForm(savingCardProductEntity, form1, ID, false);
                generalCard.add(savingCardProductEntity);
            }
            for (SavingCardGoodsAddForm form1 : form.GoodsRange.TimeCard) {
                SavingCardProductEntity savingCardProductEntity = new SavingCardProductEntity();
                AssemblyForm(savingCardProductEntity, form1, ID, false);
                timeCard.add(savingCardProductEntity);
            }
            for (SavingCardGoodsAddForm form1 : form.GoodsRange.PackageCard) {
                SavingCardProductEntity savingCardProductEntity = new SavingCardProductEntity();
                AssemblyForm(savingCardProductEntity, form1, ID, false);
                packageCard.add(savingCardProductEntity);
            }
            for (SavingCardGoodsCategoryAddForm form1 : form.GoodsRange.ProductCategory) {
                SavingCardProductCategoryEntity savingCardProductCategoryEntity = new SavingCardProductCategoryEntity();
                AssemblyCategoryForm(savingCardProductCategoryEntity, form1, ID, false);
                productCategory.add(savingCardProductCategoryEntity);
            }
            for (SavingCardGoodsCategoryAddForm form1 : form.GoodsRange.ProjectCategory) {
                SavingCardProductCategoryEntity savingCardProductCategoryEntity = new SavingCardProductCategoryEntity();
                AssemblyCategoryForm(savingCardProductCategoryEntity, form1, ID, false);
                projectCategory.add(savingCardProductCategoryEntity);
            }
            for (SavingCardGoodsCategoryAddForm form1 : form.GoodsRange.GeneralCardCategory) {
                SavingCardProductCategoryEntity savingCardProductCategoryEntity = new SavingCardProductCategoryEntity();
                AssemblyCategoryForm(savingCardProductCategoryEntity, form1, ID, false);
                generalCardCategory.add(savingCardProductCategoryEntity);
            }
            for (SavingCardGoodsCategoryAddForm form1 : form.GoodsRange.TimeCardCategory) {
                SavingCardProductCategoryEntity savingCardProductCategoryEntity = new SavingCardProductCategoryEntity();
                AssemblyCategoryForm(savingCardProductCategoryEntity, form1, ID, false);
                timeCardCategory.add(savingCardProductCategoryEntity);
            }
            for (SavingCardGoodsCategoryAddForm form1 : form.GoodsRange.PackageCardCategory) {
                SavingCardProductCategoryEntity savingCardProductCategoryEntity = new SavingCardProductCategoryEntity();
                AssemblyCategoryForm(savingCardProductCategoryEntity, form1, ID, false);
                packageCardCategory.add(savingCardProductCategoryEntity);
            }
        }
        if (!form.IsLargessGoodsRange) {
            for (SavingCardGoodsAddForm form1 : form.GoodsRangeLargess.Product) {
                SavingCardProductEntity savingCardProductEntity = new SavingCardProductEntity();
                AssemblyForm(savingCardProductEntity, form1, ID, true);
                product.add(savingCardProductEntity);
            }
            for (SavingCardGoodsAddForm form1 : form.GoodsRangeLargess.Project) {
                SavingCardProductEntity savingCardProductEntity = new SavingCardProductEntity();
                AssemblyForm(savingCardProductEntity, form1, ID, true);
                project.add(savingCardProductEntity);
            }
            for (SavingCardGoodsAddForm form1 : form.GoodsRangeLargess.GeneralCard) {
                SavingCardProductEntity savingCardProductEntity = new SavingCardProductEntity();
                AssemblyForm(savingCardProductEntity, form1, ID, true);
                generalCard.add(savingCardProductEntity);
            }
            for (SavingCardGoodsAddForm form1 : form.GoodsRangeLargess.TimeCard) {
                SavingCardProductEntity savingCardProductEntity = new SavingCardProductEntity();
                AssemblyForm(savingCardProductEntity, form1, ID, true);
                timeCard.add(savingCardProductEntity);
            }
            for (SavingCardGoodsAddForm form1 : form.GoodsRangeLargess.PackageCard) {
                SavingCardProductEntity savingCardProductEntity = new SavingCardProductEntity();
                AssemblyForm(savingCardProductEntity, form1, ID, true);
                packageCard.add(savingCardProductEntity);
            }
            for (SavingCardGoodsCategoryAddForm form1 : form.GoodsRangeLargess.ProductCategory) {
                SavingCardProductCategoryEntity savingCardProductCategoryEntity = new SavingCardProductCategoryEntity();
                AssemblyCategoryForm(savingCardProductCategoryEntity, form1, ID, true);
                productCategory.add(savingCardProductCategoryEntity);
            }
            for (SavingCardGoodsCategoryAddForm form1 : form.GoodsRangeLargess.ProjectCategory) {
                SavingCardProductCategoryEntity savingCardProductCategoryEntity = new SavingCardProductCategoryEntity();
                AssemblyCategoryForm(savingCardProductCategoryEntity, form1, ID, true);
                projectCategory.add(savingCardProductCategoryEntity);
            }
            for (SavingCardGoodsCategoryAddForm form1 : form.GoodsRangeLargess.GeneralCardCategory) {
                SavingCardProductCategoryEntity savingCardProductCategoryEntity = new SavingCardProductCategoryEntity();
                AssemblyCategoryForm(savingCardProductCategoryEntity, form1, ID, true);
                generalCardCategory.add(savingCardProductCategoryEntity);
            }
            for (SavingCardGoodsCategoryAddForm form1 : form.GoodsRangeLargess.TimeCardCategory) {
                SavingCardProductCategoryEntity savingCardProductCategoryEntity = new SavingCardProductCategoryEntity();
                AssemblyCategoryForm(savingCardProductCategoryEntity, form1, ID, true);
                timeCardCategory.add(savingCardProductCategoryEntity);
            }

            for (SavingCardGoodsCategoryAddForm form1 : form.GoodsRangeLargess.PackageCardCategory) {
                SavingCardProductCategoryEntity savingCardProductCategoryEntity = new SavingCardProductCategoryEntity();
                AssemblyCategoryForm(savingCardProductCategoryEntity, form1, ID, true);
                packageCardCategory.add(savingCardProductCategoryEntity);
            }
        }


        if (product != null && product.size() > 0) {
            List<List<SavingCardProductEntity>> subs = Lists.partition(product, 20);
            for (List<SavingCardProductEntity> entityList : subs) {
                savingCardMapper.addSavingCardProduct(entityList);
            }
        }

        if (project != null && project.size() > 0) {
            List<List<SavingCardProductEntity>> subs = Lists.partition(project, 20);
            for (List<SavingCardProductEntity> entityList : subs) {
                savingCardMapper.addSavingCardProject(entityList);
            }
        }

        if (generalCard != null && generalCard.size() > 0) {
            List<List<SavingCardProductEntity>> subs = Lists.partition(generalCard, 20);
            for (List<SavingCardProductEntity> entityList : subs) {
                savingCardMapper.addSavingCardGeneralCard(entityList);
            }
        }

        if (timeCard != null && timeCard.size() > 0) {
            List<List<SavingCardProductEntity>> subs = Lists.partition(timeCard, 20);
            for (List<SavingCardProductEntity> entityList : subs) {
                savingCardMapper.addSavingCardTimeCard(entityList);
            }
        }

        if (packageCard != null && packageCard.size() > 0) {
            List<List<SavingCardProductEntity>> subs = Lists.partition(packageCard, 20);
            for (List<SavingCardProductEntity> entityList : subs) {
                savingCardMapper.addSavingCardPackageCard(entityList);
            }
        }

        if (productCategory != null && productCategory.size() > 0) {
            List<List<SavingCardProductCategoryEntity>> subs = Lists.partition(productCategory, 20);
            for (List<SavingCardProductCategoryEntity> entityList : subs) {
                savingCardMapper.addSavingCardProductCategory(entityList);
            }
        }

        if (projectCategory != null && projectCategory.size() > 0) {
            List<List<SavingCardProductCategoryEntity>> subs = Lists.partition(projectCategory, 20);
            for (List<SavingCardProductCategoryEntity> entityList : subs) {
                savingCardMapper.addSavingCardProjectCategory(entityList);
            }
        }

        if (generalCardCategory != null && generalCardCategory.size() > 0) {
            List<List<SavingCardProductCategoryEntity>> subs = Lists.partition(generalCardCategory, 20);
            for (List<SavingCardProductCategoryEntity> entityList : subs) {
                savingCardMapper.addSavingCardGeneralCardCategory(entityList);
            }
        }

        if (timeCardCategory != null && timeCardCategory.size() > 0) {
            List<List<SavingCardProductCategoryEntity>> subs = Lists.partition(timeCardCategory, 20);
            for (List<SavingCardProductCategoryEntity> entityList : subs) {
                savingCardMapper.addSavingCardTimeCardCategory(entityList);
            }
        }

        if (packageCardCategory != null && packageCardCategory.size() > 0) {
            List<List<SavingCardProductCategoryEntity>> subs = Lists.partition(packageCardCategory, 20);
            for (List<SavingCardProductCategoryEntity> entityList : subs) {
                savingCardMapper.addSavingCardPackageCardCategory(entityList);
            }
        }

    }

    private void addSavingCardSavingCardRechargeRules(SavingCardAddForm form, Integer ID) {
        List<SavingCardRechargeRulesEntity> SavingCardRechargeRulesList = new ArrayList<>();
        for (SavingCardRechargeRulesAddForm savingCardRechargeRule : form.SavingCardRechargeRules) {
            SavingCardRechargeRulesEntity savingCardRechargeRulesEntity = new SavingCardRechargeRulesEntity();
            savingCardRechargeRulesEntity.SavingCardID = ID;
            savingCardRechargeRulesEntity.Price = savingCardRechargeRule.Price;
            savingCardRechargeRulesEntity.LargessPrice = savingCardRechargeRule.LargessPrice;
            SavingCardRechargeRulesList.add(savingCardRechargeRulesEntity);
        }

        if (SavingCardRechargeRulesList.size() > 0)
            savingCardMapper.createSavingCardRechargeRules(SavingCardRechargeRulesList);
    }

    public BaseOutput product() {
        return BaseOutputForm.success(ListUtil.listToTree(JSONArray.parseArray(JSON.toJSONString(savingCardMapper.product(), JSONWriter.Feature.WriteMapNullValue)), "ID", "ParentID", "Child"));
    }

    public BaseOutput generalCard() {
        return BaseOutputForm.success(savingCardMapper.generalCard());
    }

    public BaseOutput timeCard() {
        return BaseOutputForm.success(savingCardMapper.timeCard());
    }

    public BaseOutput packageCard() {
        return BaseOutputForm.success(savingCardMapper.packageCard());
    }

    public BaseOutput project() {
        return BaseOutputForm.success(ListUtil.listToTree(JSONArray.parseArray(JSON.toJSONString(savingCardMapper.project(), JSONWriter.Feature.WriteMapNullValue)), "ID", "ParentID", "Child"));
    }

    public BaseOutput savingCardGoods(SavingCardGoodsQueryEntityForm form) {
        SavingCardAllGoodsOutputForm savingCardAllGoodsOutputForm = new SavingCardAllGoodsOutputForm();
        savingCardAllGoodsOutputForm.Product = savingCardMapper.savingCardProduct(form.ID, form.IsLargess);
        savingCardAllGoodsOutputForm.Project = savingCardMapper.savingCardProject(form.ID, form.IsLargess);
        savingCardAllGoodsOutputForm.GeneralCard = savingCardMapper.savingCardGeneralCard(form.ID, form.IsLargess);
        savingCardAllGoodsOutputForm.TimeCard = savingCardMapper.savingCardTimeCard(form.ID, form.IsLargess);
        savingCardAllGoodsOutputForm.PackageCard = savingCardMapper.savingCardPackageCard(form.ID, form.IsLargess);
        savingCardAllGoodsOutputForm.ProductCategory = savingCardMapper.savingCardProductCategory(form.ID, form.IsLargess);
        savingCardAllGoodsOutputForm.ProjectCategory = savingCardMapper.savingCardProjectCategory(form.ID, form.IsLargess);
        savingCardAllGoodsOutputForm.GeneralCardCategory = savingCardMapper.savingCardGeneralCardCategory(form.ID, form.IsLargess);
        savingCardAllGoodsOutputForm.TimeCardCategory = savingCardMapper.savingCardTimeCardCategory(form.ID, form.IsLargess);
        savingCardAllGoodsOutputForm.PackageCardCategory = savingCardMapper.savingCardPackageCardCategory(form.ID, form.IsLargess);
        return BaseOutputForm.success(savingCardAllGoodsOutputForm);
    }

    public BaseOutput savingCardRechargeRules(SavingCardQueryEntityForm form) {
        return BaseOutputForm.success(savingCardMapper.savingCardRechargeRules(form.ID));
    }

    public BaseOutput move(GoodsMoveForm form) {

        LinkedList<Integer> linkedList = savingCardMapper.getASCList(form.MoveID, form.ID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<TbSavingCardImageEntity> list = new ArrayList<>();
        for (Integer ID : linkedList) {
            TbSavingCardImageEntity savingCardImageEntity = new TbSavingCardImageEntity();
            savingCardImageEntity.ID = ID;
            savingCardImageEntity.SavingCardID = form.ID;
            savingCardImageEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(savingCardImageEntity);
        }

        savingCardMapper.updateSequence(list);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput bitchUpdateSavingCardSaleEntity(GoodsBitchUpdateForm form) {
        savingCardMapper.deleteBitchSaleEntity(form.GoodsID);
        List<SavingCardSaleEntity> list = new ArrayList<>();
        for (Integer goodsID : form.GoodsID) {
            for (Integer entityID : form.EntityID) {
                SavingCardSaleEntity saleEntity = new SavingCardSaleEntity();
                saleEntity.SavingCardID = goodsID;
                saleEntity.EntityID = entityID;
                list.add(saleEntity);
            }
        }

        if (list.size() > 0)
            savingCardMapper.addSavingCardSaleEntity(list);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput bitchUpdateSavingCardConsumeEntity(GoodsBitchUpdateForm form) {
        savingCardMapper.deleteBitchConsumeEntity(form.GoodsID);
        List<SavingCardSaleEntity> list = new ArrayList<>();
        for (Integer goodsID : form.GoodsID) {
            for (Integer entityID : form.EntityID) {
                SavingCardSaleEntity saleEntity = new SavingCardSaleEntity();
                saleEntity.SavingCardID = goodsID;
                saleEntity.EntityID = entityID;
                list.add(saleEntity);
            }
        }

        if (list.size() > 0)
            savingCardMapper.addSavingCardConsumeEntity(list);

        return BaseOutput.success();
    }
}
