package com.zhelian.service.ibeauty.saleGeneralCardCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.SaleGeneralCardHandlerCommissionMapper;
import com.zhelian.mapper.ibeauty.SalePackageCardGeneralCardHandlerCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SaleGeneralCardHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleGeneralCardHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.entity.SalePackageCardGeneralCardHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SalePackageCardGeneralCardHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardHandlersCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.SaleGeneralCardHandlerCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class SaleGeneralCardHandlerCommissionService {

    @Autowired
    SaleGeneralCardHandlerCommissionMapper saleGeneralCardHandlerCommissionMapper;

    @Autowired
    SalePackageCardGeneralCardHandlerCommissionMapper salePackageCardGeneralCardHandlerCommissionMapper;


    public BaseOutput allSaleGeneralCardHandlerCommission(SaleGeneralCardHandlerCommissionQueryForm form) {
        List<SaleGeneralCardHandlerCommissionOutputForm> list = saleGeneralCardHandlerCommissionMapper.allSaleGeneralCardHandlerCommission(form.EntityID, form.GeneralCardID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSalePackageCardGeneralCardHandlerCommission(SaleGeneralCardHandlerCommissionQueryForm form) {
        List<SaleGeneralCardHandlerCommissionOutputForm> list = salePackageCardGeneralCardHandlerCommissionMapper.allSalePackageCardGeneralCardHandlerCommission(form.EntityID, form.GeneralCardID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleGeneralCardHandlerCommission(SaleGeneralCardHandlersCommissionAddForm form) {

        saleGeneralCardHandlerCommissionMapper.deleteSaleGeneralCardHandler(form.EntityID, form.GeneralCardID);
        saleGeneralCardHandlerCommissionMapper.deleteSaleGeneralCardHandlerJobType(form.EntityID, form.GeneralCardID);
        List<SaleGeneralCardHandlerCommissionEntity> saleGeneralCardHandlerCommissionList = new ArrayList<>();
        List<SaleGeneralCardHandlerJobTypeCommissionEntity> saleGeneralCardHandlerJobTypeCommissionList = new ArrayList<>();
        if (form.GeneralCardHandler != null && form.GeneralCardHandler.size() > 0) {
            for (SaleGeneralCardHandlerCommissionAddForm saleGeneralCardHandlerAddForm : form.GeneralCardHandler) {
                if (saleGeneralCardHandlerAddForm.PayRate != null || saleGeneralCardHandlerAddForm.PayFixed != null ||
                        saleGeneralCardHandlerAddForm.SavingCardRate != null || saleGeneralCardHandlerAddForm.SavingCardFixed != null ||
                        saleGeneralCardHandlerAddForm.SavingCardLargessRate != null || saleGeneralCardHandlerAddForm.SavingCardLargessFixed != null ||
                        saleGeneralCardHandlerAddForm.SpecialBenefit != null) {
                    SaleGeneralCardHandlerCommissionEntity saleGeneralCardHandlerCommissionEntity = new SaleGeneralCardHandlerCommissionEntity();
                    saleGeneralCardHandlerCommissionEntity.EntityID = form.EntityID;
                    saleGeneralCardHandlerCommissionEntity.GeneralCardID = form.GeneralCardID;
                    saleGeneralCardHandlerCommissionEntity.GeneralCardSaleHandlerID = saleGeneralCardHandlerAddForm.SaleHandlerID;
                    saleGeneralCardHandlerCommissionEntity.PayFixed = saleGeneralCardHandlerAddForm.PayFixed;
                    saleGeneralCardHandlerCommissionEntity.PayRate = saleGeneralCardHandlerAddForm.PayRate;
                    saleGeneralCardHandlerCommissionEntity.SavingCardFixed = saleGeneralCardHandlerAddForm.SavingCardFixed;
                    saleGeneralCardHandlerCommissionEntity.SavingCardRate = saleGeneralCardHandlerAddForm.SavingCardRate;
                    saleGeneralCardHandlerCommissionEntity.SavingCardLargessFixed = saleGeneralCardHandlerAddForm.SavingCardLargessFixed;
                    saleGeneralCardHandlerCommissionEntity.SavingCardLargessRate = saleGeneralCardHandlerAddForm.SavingCardLargessRate;
                    saleGeneralCardHandlerCommissionEntity.SpecialBenefit = saleGeneralCardHandlerAddForm.SpecialBenefit;
                    saleGeneralCardHandlerCommissionList.add(saleGeneralCardHandlerCommissionEntity);
                }
                if (saleGeneralCardHandlerAddForm.HandlerJobTypeCommission != null && saleGeneralCardHandlerAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (SaleGeneralCardCategoryHandlerJobTypeCommissionAddForm saleGeneralCardCategoryHandlerJobTypeCommissionAddForm : saleGeneralCardHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.PayRate != null || saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardRate != null || saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed != null ||
                                saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null || saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed != null ||
                                saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SaleGeneralCardHandlerJobTypeCommissionEntity saleGeneralCardHandlerJobTypeCommissionEntity = new SaleGeneralCardHandlerJobTypeCommissionEntity();
                            saleGeneralCardHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            saleGeneralCardHandlerJobTypeCommissionEntity.GeneralCardID = form.GeneralCardID;
                            saleGeneralCardHandlerJobTypeCommissionEntity.GeneralCardSaleHandlerID = saleGeneralCardHandlerAddForm.SaleHandlerID;
                            saleGeneralCardHandlerJobTypeCommissionEntity.JobTypeID = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.JobTypeID;
                            saleGeneralCardHandlerJobTypeCommissionEntity.PayFixed = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.PayFixed;
                            saleGeneralCardHandlerJobTypeCommissionEntity.PayRate = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.PayRate;
                            saleGeneralCardHandlerJobTypeCommissionEntity.SavingCardFixed = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed;
                            saleGeneralCardHandlerJobTypeCommissionEntity.SavingCardRate = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardRate;
                            saleGeneralCardHandlerJobTypeCommissionEntity.SavingCardLargessFixed = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed;
                            saleGeneralCardHandlerJobTypeCommissionEntity.SavingCardLargessRate = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            saleGeneralCardHandlerJobTypeCommissionEntity.SpecialBenefit = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            saleGeneralCardHandlerJobTypeCommissionList.add(saleGeneralCardHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
        }

        salePackageCardGeneralCardHandlerCommissionMapper.deleteSalePackageCardGeneralCardHandler(form.EntityID, form.GeneralCardID);
        salePackageCardGeneralCardHandlerCommissionMapper.deleteSalePackageCardGeneralCardHandlerJobType(form.EntityID, form.GeneralCardID);
        List<SalePackageCardGeneralCardHandlerCommissionEntity> salePackageCardGeneralCardHandlerCommissionList = new ArrayList<>();
        List<SalePackageCardGeneralCardHandlerJobTypeCommissionEntity> salePackageCardGeneralCardHandlerJobTypeCommissionList = new ArrayList<>();
        if (form.PackageCardGeneralCardHandler != null && form.PackageCardGeneralCardHandler.size() > 0) {
            for (SaleGeneralCardHandlerCommissionAddForm saleGeneralCardHandlerAddForm : form.PackageCardGeneralCardHandler) {
                if (saleGeneralCardHandlerAddForm.PayRate != null || saleGeneralCardHandlerAddForm.PayFixed != null ||
                        saleGeneralCardHandlerAddForm.SavingCardRate != null || saleGeneralCardHandlerAddForm.SavingCardFixed != null ||
                        saleGeneralCardHandlerAddForm.SavingCardLargessRate != null || saleGeneralCardHandlerAddForm.SavingCardLargessFixed != null ||
                        saleGeneralCardHandlerAddForm.SpecialBenefit != null) {
                    SalePackageCardGeneralCardHandlerCommissionEntity salePackageCardGeneralCardHandlerCommissionEntity = new SalePackageCardGeneralCardHandlerCommissionEntity();
                    salePackageCardGeneralCardHandlerCommissionEntity.EntityID = form.EntityID;
                    salePackageCardGeneralCardHandlerCommissionEntity.GeneralCardID = form.GeneralCardID;
                    salePackageCardGeneralCardHandlerCommissionEntity.PackageCardSaleHandlerID = saleGeneralCardHandlerAddForm.SaleHandlerID;
                    salePackageCardGeneralCardHandlerCommissionEntity.PayFixed = saleGeneralCardHandlerAddForm.PayFixed;
                    salePackageCardGeneralCardHandlerCommissionEntity.PayRate = saleGeneralCardHandlerAddForm.PayRate;
                    salePackageCardGeneralCardHandlerCommissionEntity.SavingCardFixed = saleGeneralCardHandlerAddForm.SavingCardFixed;
                    salePackageCardGeneralCardHandlerCommissionEntity.SavingCardRate = saleGeneralCardHandlerAddForm.SavingCardRate;
                    salePackageCardGeneralCardHandlerCommissionEntity.SavingCardLargessFixed = saleGeneralCardHandlerAddForm.SavingCardLargessFixed;
                    salePackageCardGeneralCardHandlerCommissionEntity.SavingCardLargessRate = saleGeneralCardHandlerAddForm.SavingCardLargessRate;
                    salePackageCardGeneralCardHandlerCommissionEntity.SpecialBenefit = saleGeneralCardHandlerAddForm.SpecialBenefit;
                    salePackageCardGeneralCardHandlerCommissionList.add(salePackageCardGeneralCardHandlerCommissionEntity);
                }
                if (saleGeneralCardHandlerAddForm.HandlerJobTypeCommission != null && saleGeneralCardHandlerAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (SaleGeneralCardCategoryHandlerJobTypeCommissionAddForm saleGeneralCardCategoryHandlerJobTypeCommissionAddForm : saleGeneralCardHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.PayRate != null || saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardRate != null || saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed != null ||
                                saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null || saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed != null ||
                                saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SalePackageCardGeneralCardHandlerJobTypeCommissionEntity salePackageCardGeneralCardHandlerJobTypeCommissionEntity = new SalePackageCardGeneralCardHandlerJobTypeCommissionEntity();
                            salePackageCardGeneralCardHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            salePackageCardGeneralCardHandlerJobTypeCommissionEntity.GeneralCardID = form.GeneralCardID;
                            salePackageCardGeneralCardHandlerJobTypeCommissionEntity.PackageCardSaleHandlerID = saleGeneralCardHandlerAddForm.SaleHandlerID;
                            salePackageCardGeneralCardHandlerJobTypeCommissionEntity.JobTypeID = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.JobTypeID;
                            salePackageCardGeneralCardHandlerJobTypeCommissionEntity.PayFixed = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.PayFixed;
                            salePackageCardGeneralCardHandlerJobTypeCommissionEntity.PayRate = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.PayRate;
                            salePackageCardGeneralCardHandlerJobTypeCommissionEntity.SavingCardFixed = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed;
                            salePackageCardGeneralCardHandlerJobTypeCommissionEntity.SavingCardRate = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardRate;
                            salePackageCardGeneralCardHandlerJobTypeCommissionEntity.SavingCardLargessFixed = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed;
                            salePackageCardGeneralCardHandlerJobTypeCommissionEntity.SavingCardLargessRate = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            salePackageCardGeneralCardHandlerJobTypeCommissionEntity.SpecialBenefit = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            salePackageCardGeneralCardHandlerJobTypeCommissionList.add(salePackageCardGeneralCardHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
        }
        if (saleGeneralCardHandlerCommissionList != null && saleGeneralCardHandlerCommissionList.size() > 0) {
            List<List<SaleGeneralCardHandlerCommissionEntity>> subs = Lists.partition(saleGeneralCardHandlerCommissionList, 20);
            for (List<SaleGeneralCardHandlerCommissionEntity> entityList : subs) {
                saleGeneralCardHandlerCommissionMapper.insertSaleGeneralCardHandlerCommission(entityList);
            }
        }
        if (saleGeneralCardHandlerJobTypeCommissionList != null && saleGeneralCardHandlerJobTypeCommissionList.size() > 0) {
            List<List<SaleGeneralCardHandlerJobTypeCommissionEntity>> subs = Lists.partition(saleGeneralCardHandlerJobTypeCommissionList, 20);
            for (List<SaleGeneralCardHandlerJobTypeCommissionEntity> entityList : subs) {
                saleGeneralCardHandlerCommissionMapper.insertSaleGeneralCardHandlerJobTypeCommission(entityList);
            }
        }
        if (salePackageCardGeneralCardHandlerCommissionList != null && salePackageCardGeneralCardHandlerCommissionList.size() > 0) {
            List<List<SalePackageCardGeneralCardHandlerCommissionEntity>> subs = Lists.partition(salePackageCardGeneralCardHandlerCommissionList, 20);
            for (List<SalePackageCardGeneralCardHandlerCommissionEntity> entityList : subs) {
                salePackageCardGeneralCardHandlerCommissionMapper.insertSalePackageCardGeneralCardHandlerCommission(entityList);
            }
        }

        if (salePackageCardGeneralCardHandlerJobTypeCommissionList != null && salePackageCardGeneralCardHandlerJobTypeCommissionList.size() > 0) {
            List<List<SalePackageCardGeneralCardHandlerJobTypeCommissionEntity>> subs = Lists.partition(salePackageCardGeneralCardHandlerJobTypeCommissionList, 20);
            for (List<SalePackageCardGeneralCardHandlerJobTypeCommissionEntity> entityList : subs) {
                salePackageCardGeneralCardHandlerCommissionMapper.insertSalePackageCardGeneralCardHandlerJobTypeCommission(entityList);
            }
        }
        return BaseOutput.success();
    }
}