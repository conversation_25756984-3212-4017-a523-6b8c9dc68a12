package com.zhelian.service.ibeauty.refund;


import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.autoNumber.AutoNumberUtils;
import com.zhelian.core.utils.DateTimeUtil;
import com.zhelian.core.utils.enumeration.AccountChannel;
import com.zhelian.core.utils.enumeration.BillCode;
import com.zhelian.mapper.CRM.ChannelMapper;
import com.zhelian.mapper.CRM.CustomerGrowthMapper;
import com.zhelian.mapper.CRM.CustomerMapper;
import com.zhelian.mapper.ibeauty.*;
import com.zhelian.model.CRM.form.output.ChannelEmployeePerformanceRateOutputForm;
import com.zhelian.model.CRM.form.output.ChannelParentOutputForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.*;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.ibeauty.form.output.*;
import com.zhelian.model.ibeauty.service.*;
import com.zhelian.service.CRM.customer.CustomerService;
import com.zhelian.service.ibeauty.SealingAccount.SealingAccountService;
import com.zhelian.service.ibeauty.saleBill.SaleBillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class RefundSaleBillService {

    @Autowired
    RefundBillMapper refundBillMapper;
    @Autowired
    RefundBillHandlerMapper refundBillHandlerMapper;
    @Autowired
    SaleBillHandlerMapper saleBillHandlerMapper;
    @Autowired
    ProductAccountMapper productAccountMapper;
    @Autowired
    ProjectAccountMapper projectAccountMapper;
    @Autowired
    GeneralCardAccountMapper generalCardAccountMapper;
    @Autowired
    TimeCardAccountMapper timeCardAccountMapper;
    @Autowired
    SavingCardAccountMapper savingCardAccountMapper;
    @Autowired
    PackageCardAccountMapper packageCardAccountMapper;
    @Autowired
    SaleBillPayMapper saleBillPayMapper;
    @Autowired
    CustomerService customerService;
    @Autowired
    SaleBillMapper saleBillMapper;
    @Autowired
    SaleEntityPerformanceMapper saleEntityPerformanceMapper;
    @Autowired
    SaleEmployeePerformanceCommissionMapper saleEmployeePerformanceCommissionMapper;
    @Autowired
    CustomerMapper customerMapper;
    @Autowired
    ChannelMapper channelMapper;

    @Autowired
    SaleBillService saleBillService;

    @Autowired
    CustomerGrowthMapper customerGrowthMapper;

    @Autowired
    SaleChannelPerformanceMapper saleChannelPerformanceMapper;
    @Autowired
    SaleChannelDeveloperPerformanceMapper saleChannelDeveloperPerformanceMapper;

    @Autowired
    SaleChannelConsultantPerformanceMapper saleChannelConsultantPerformanceMapper;
    @Autowired
    SealingAccountService sealingAccountService;

    public BaseOutput refundBillPay(RefundBillPayQueryForm form) {

        BigDecimal totalSavingCardDeductionAmount = BigDecimal.ZERO;
        BigDecimal totalSavingCardDeductionLargessAmount = BigDecimal.ZERO;
        BigDecimal totalPayAmount = BigDecimal.ZERO;
        List<RefundBillPayGoodsForm> refundBillPayGoodsForms = new ArrayList<>();
        List<RefundBillPaySavingCardGoodsForm> refundBillPaySavingCardGoodsForms = new ArrayList<>();
        if (form.Product != null && form.Product.size() > 0) {
            for (RefundBillPayProductForm refundBillPayProductForm : form.Product) {
                ProductAccountEntity productAccountEntity = productAccountMapper.getProductAccountByAccountID(refundBillPayProductForm.ProductAccountID);

                BigDecimal BalanceAmount = productAccountEntity.PayPerformanceBalanceAmount.add(productAccountEntity.CardPerformanceBalanceAmount).add(productAccountEntity.CardLargessPerformanceBalanceAmount);
                if (!productAccountEntity.IsLargess) {
                    if (refundBillPayProductForm.TotalAmount.compareTo(BalanceAmount) > 0) {
                        throw new RuntimeException("产品账户没有足够的数量，请修改数量");
                    }

                    BigDecimal payAmount;
                    BigDecimal savingCardDeductionAmount;
                    BigDecimal savingCardDeductionLargessAmount;
                    if (refundBillPayProductForm.TotalAmount.compareTo(BalanceAmount) < 0) {
                        RefundRateForm refundRateForm = getProductAmount(productAccountEntity, refundBillPayProductForm.TotalAmount);
                        payAmount = refundRateForm.payAmount;
                        savingCardDeductionAmount = refundRateForm.savingCardDeductionAmount;
                        savingCardDeductionLargessAmount = refundRateForm.savingCardDeductionLargessAmount;
                        totalPayAmount = totalPayAmount.add(payAmount);
                        totalSavingCardDeductionAmount = totalSavingCardDeductionAmount.add(savingCardDeductionAmount);
                        totalSavingCardDeductionLargessAmount = totalSavingCardDeductionLargessAmount.add(savingCardDeductionLargessAmount);
                    } else {
                        payAmount = productAccountEntity.PayPerformanceBalanceAmount;
                        savingCardDeductionAmount = productAccountEntity.CardPerformanceBalanceAmount;
                        savingCardDeductionLargessAmount = productAccountEntity.CardLargessPerformanceBalanceAmount;
                        totalPayAmount = totalPayAmount.add(payAmount);
                        totalSavingCardDeductionAmount = totalSavingCardDeductionAmount.add(savingCardDeductionAmount);
                        totalSavingCardDeductionLargessAmount = totalSavingCardDeductionLargessAmount.add(savingCardDeductionLargessAmount);
                    }
                    List<RefundBillPayGoodsForm> saleBillPayProduct = saleBillPayMapper.getSaleBillPayProduct(productAccountEntity.SaleBillProductID,productAccountEntity.ID);
                    BigDecimal money = BigDecimal.ZERO;
                    for (int i = 0; i < saleBillPayProduct.size(); i++) {
                        RefundBillPayGoodsForm newRefundBillPayGoodsForm = new RefundBillPayGoodsForm();
                        newRefundBillPayGoodsForm.PayMethodID = saleBillPayProduct.get(i).PayMethodID;
                        newRefundBillPayGoodsForm.Name = saleBillPayProduct.get(i).Name;
                        if (i == saleBillPayProduct.size() - 1) {
                            newRefundBillPayGoodsForm.Amount = payAmount.subtract(money);
                        } else {
                            newRefundBillPayGoodsForm.Amount = payAmount.multiply(saleBillPayProduct.get(i).Amount).divide(productAccountEntity.PayAmount, 2, BigDecimal.ROUND_HALF_UP);
                            money = money.add(newRefundBillPayGoodsForm.Amount);
                        }
                        refundBillPayGoodsForms.add(newRefundBillPayGoodsForm);
                    }

                    List<RefundBillPaySavingCardGoodsForm> saleBillPaySavingCardProduct = new ArrayList<>();
                    if (productAccountEntity.Channel.equals(AccountChannel.AccountChannel_20.getID())){
                        saleBillPaySavingCardProduct = saleBillPayMapper.getSaleBillPaySavingCardProductTransfer(productAccountEntity.ID);
                    }else {
                        saleBillPaySavingCardProduct = saleBillPayMapper.getSaleBillPaySavingCardProduct(productAccountEntity.SaleBillProductID,productAccountEntity.ID);
                    }
                    BigDecimal amount = BigDecimal.ZERO;
                    BigDecimal largessAmount = BigDecimal.ZERO;
                    for (int i = 0; i < saleBillPaySavingCardProduct.size(); i++) {
                        RefundBillPaySavingCardGoodsForm newRefundBillPaySavingCardGoodsForm = new RefundBillPaySavingCardGoodsForm();
                        newRefundBillPaySavingCardGoodsForm.SavingCardAccountID = saleBillPaySavingCardProduct.get(i).SavingCardAccountID;
                        newRefundBillPaySavingCardGoodsForm.Name = saleBillPaySavingCardProduct.get(i).Name;
                        if (i == saleBillPaySavingCardProduct.size() - 1) {
                            newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.subtract(amount);
                            newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.subtract(largessAmount);
                        } else {
                            newRefundBillPaySavingCardGoodsForm.Amount = BigDecimal.ZERO;
                            newRefundBillPaySavingCardGoodsForm.LargessAmount = BigDecimal.ZERO;
                            if (productAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
                                newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.multiply(saleBillPaySavingCardProduct.get(i).Amount).divide(productAccountEntity.SavingCardDeductionAmount, 2, BigDecimal.ROUND_HALF_UP);
                                amount = amount.add(newRefundBillPaySavingCardGoodsForm.Amount);
                            }
                            if (productAccountEntity.SavingCardDeductionLargessAmount.compareTo(BigDecimal.ZERO) > 0) {
                                newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.multiply(saleBillPaySavingCardProduct.get(i).LargessAmount).divide(productAccountEntity.SavingCardDeductionLargessAmount, 2, BigDecimal.ROUND_HALF_UP);
                                largessAmount = largessAmount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);
                            }
                        }
                        newRefundBillPaySavingCardGoodsForm.TotalAmount = newRefundBillPaySavingCardGoodsForm.Amount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);
                        refundBillPaySavingCardGoodsForms.add(newRefundBillPaySavingCardGoodsForm);
                    }

                }
            }
        }
        if (form.Project != null && form.Project.size() > 0) {
            for (RefundBillPayProjectForm refundBillPayProjectForm : form.Project) {
                ProjectAccountEntity projectAccountEntity = projectAccountMapper.getProjectAccountByAccountID(refundBillPayProjectForm.ProjectAccountID);
                BigDecimal BalanceAmount = projectAccountEntity.PayPerformanceBalanceAmount.add(projectAccountEntity.CardPerformanceBalanceAmount).add(projectAccountEntity.CardLargessPerformanceBalanceAmount);
                if (!projectAccountEntity.IsLargess) {
                    if (refundBillPayProjectForm.TotalAmount.compareTo(BalanceAmount) > 0) {
                        throw new RuntimeException("项目账户没有足够的数量，请修改数量");
                    }
                    BigDecimal payAmount;
                    BigDecimal savingCardDeductionAmount;
                    BigDecimal savingCardDeductionLargessAmount;
                    if (refundBillPayProjectForm.TotalAmount.compareTo(BalanceAmount) < 0) {
                        RefundRateForm refundRateForm = getProjectAmount(projectAccountEntity, refundBillPayProjectForm.TotalAmount);
                        payAmount = refundRateForm.payAmount;
                        savingCardDeductionAmount = refundRateForm.savingCardDeductionAmount;
                        savingCardDeductionLargessAmount = refundRateForm.savingCardDeductionLargessAmount;
                        totalPayAmount = totalPayAmount.add(payAmount);
                        totalSavingCardDeductionAmount = totalSavingCardDeductionAmount.add(savingCardDeductionAmount);
                        totalSavingCardDeductionLargessAmount = totalSavingCardDeductionLargessAmount.add(savingCardDeductionLargessAmount);
                    } else {
                        payAmount = projectAccountEntity.PayPerformanceBalanceAmount;
                        savingCardDeductionAmount = projectAccountEntity.CardPerformanceBalanceAmount;
                        savingCardDeductionLargessAmount = projectAccountEntity.CardLargessPerformanceBalanceAmount;
                        totalPayAmount = totalPayAmount.add(payAmount);
                        totalSavingCardDeductionAmount = totalSavingCardDeductionAmount.add(savingCardDeductionAmount);
                        totalSavingCardDeductionLargessAmount = totalSavingCardDeductionLargessAmount.add(savingCardDeductionLargessAmount);
                    }
                    List<RefundBillPayGoodsForm> saleBillPayProject = saleBillPayMapper.getSaleBillPayProject(projectAccountEntity.SaleBillProjectID,projectAccountEntity.ID);
                    BigDecimal money = BigDecimal.ZERO;
                    for (int i = 0; i < saleBillPayProject.size(); i++) {

                        RefundBillPayGoodsForm newRefundBillPayGoodsForm = new RefundBillPayGoodsForm();
                        newRefundBillPayGoodsForm.PayMethodID = saleBillPayProject.get(i).PayMethodID;
                        newRefundBillPayGoodsForm.Name = saleBillPayProject.get(i).Name;
                        if (i == saleBillPayProject.size() - 1) {
                            newRefundBillPayGoodsForm.Amount = payAmount.subtract(money);
                        } else {
                            newRefundBillPayGoodsForm.Amount = payAmount.multiply(saleBillPayProject.get(i).Amount).divide(projectAccountEntity.PayAmount, 2, BigDecimal.ROUND_HALF_UP);
                            money = money.add(newRefundBillPayGoodsForm.Amount);
                        }
                        refundBillPayGoodsForms.add(newRefundBillPayGoodsForm);
                    }

                    List<RefundBillPaySavingCardGoodsForm> saleBillPaySavingCardProject = saleBillPayMapper.getSaleBillPaySavingCardProject(projectAccountEntity.SaleBillProjectID,projectAccountEntity.ID);
                    BigDecimal amount = BigDecimal.ZERO;
                    BigDecimal largessAmount = BigDecimal.ZERO;
                    for (int i = 0; i < saleBillPaySavingCardProject.size(); i++) {
                        RefundBillPaySavingCardGoodsForm newRefundBillPaySavingCardGoodsForm = new RefundBillPaySavingCardGoodsForm();
                        newRefundBillPaySavingCardGoodsForm.SavingCardAccountID = saleBillPaySavingCardProject.get(i).SavingCardAccountID;
                        newRefundBillPaySavingCardGoodsForm.Name = saleBillPaySavingCardProject.get(i).Name;
                        if (i == saleBillPaySavingCardProject.size() - 1) {
                            newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.subtract(amount);
                            newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.subtract(largessAmount);
                        } else {
                            newRefundBillPaySavingCardGoodsForm.Amount = BigDecimal.ZERO;
                            newRefundBillPaySavingCardGoodsForm.LargessAmount = BigDecimal.ZERO;
                            if (projectAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
                                newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.multiply(saleBillPaySavingCardProject.get(i).Amount).divide(projectAccountEntity.SavingCardDeductionAmount, 2, BigDecimal.ROUND_HALF_UP);
                                amount = amount.add(newRefundBillPaySavingCardGoodsForm.Amount);
                            }
                            if (projectAccountEntity.SavingCardDeductionLargessAmount.compareTo(BigDecimal.ZERO) > 0) {
                                newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.multiply(saleBillPaySavingCardProject.get(i).LargessAmount).divide(projectAccountEntity.SavingCardDeductionLargessAmount, 2, BigDecimal.ROUND_HALF_UP);
                                largessAmount = largessAmount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);
                            }

                        }
                        newRefundBillPaySavingCardGoodsForm.TotalAmount = newRefundBillPaySavingCardGoodsForm.Amount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);
                        refundBillPaySavingCardGoodsForms.add(newRefundBillPaySavingCardGoodsForm);
                    }
                }
            }
        }
        if (form.GeneralCard != null && form.GeneralCard.size() > 0) {
            for (RefundBillPayGeneralCardForm refundBillPayGeneralCardForm : form.GeneralCard) {
                GeneralCardAccountEntity generalCardAccountEntity = generalCardAccountMapper.getGeneralCardAccountByAccountID(refundBillPayGeneralCardForm.GeneralCardAccountID);
                BigDecimal BalanceAmount = generalCardAccountEntity.PayPerformanceBalanceAmount.add(generalCardAccountEntity.CardPerformanceBalanceAmount).add(generalCardAccountEntity.CardLargessPerformanceBalanceAmount);
                if (!generalCardAccountEntity.IsLargess) {
                    if (refundBillPayGeneralCardForm.TotalAmount.compareTo(BalanceAmount) > 0) {
                        throw new RuntimeException("通用次卡账户没有足够的数量，请修改数量");
                    }
                    BigDecimal payAmount;
                    BigDecimal savingCardDeductionAmount;
                    BigDecimal savingCardDeductionLargessAmount;
                    if (refundBillPayGeneralCardForm.TotalAmount.compareTo(BalanceAmount) < 0) {
                        RefundRateForm refundRateForm = getGeneralCardAmount(generalCardAccountEntity, refundBillPayGeneralCardForm.TotalAmount);
                        payAmount = refundRateForm.payAmount;
                        savingCardDeductionAmount = refundRateForm.savingCardDeductionAmount;
                        savingCardDeductionLargessAmount = refundRateForm.savingCardDeductionLargessAmount;
                    } else {
                        payAmount = generalCardAccountEntity.PayPerformanceBalanceAmount;
                        savingCardDeductionAmount = generalCardAccountEntity.CardPerformanceBalanceAmount;
                        savingCardDeductionLargessAmount = generalCardAccountEntity.CardLargessPerformanceBalanceAmount;
                    }
                    totalPayAmount = totalPayAmount.add(payAmount);
                    totalSavingCardDeductionAmount = totalSavingCardDeductionAmount.add(savingCardDeductionAmount);
                    totalSavingCardDeductionLargessAmount = totalSavingCardDeductionLargessAmount.add(savingCardDeductionLargessAmount);
                    List<RefundBillPayGoodsForm> saleBillPayGeneralCard = saleBillPayMapper.getSaleBillPayGeneralCard(generalCardAccountEntity.SaleBillGeneralCardID,generalCardAccountEntity.ID);
                    BigDecimal money = BigDecimal.ZERO;
                    for (int i = 0; i < saleBillPayGeneralCard.size(); i++) {
                        RefundBillPayGoodsForm newRefundBillPayGoodsForm = new RefundBillPayGoodsForm();
                        newRefundBillPayGoodsForm.PayMethodID = saleBillPayGeneralCard.get(i).PayMethodID;
                        newRefundBillPayGoodsForm.Name = saleBillPayGeneralCard.get(i).Name;
                        if (i == saleBillPayGeneralCard.size() - 1) {
                            newRefundBillPayGoodsForm.Amount = payAmount.subtract(money);
                        } else {
                            newRefundBillPayGoodsForm.Amount = payAmount.multiply(saleBillPayGeneralCard.get(i).Amount).divide(generalCardAccountEntity.PayAmount, 2, BigDecimal.ROUND_HALF_UP);
                            money = money.add(newRefundBillPayGoodsForm.Amount);
                        }
                        refundBillPayGoodsForms.add(newRefundBillPayGoodsForm);
                    }
                    List<RefundBillPaySavingCardGoodsForm> saleBillPaySavingCardGeneralCard = saleBillPayMapper.getSaleBillPaySavingCardGeneralCard(generalCardAccountEntity.SaleBillGeneralCardID,generalCardAccountEntity.ID);
                    BigDecimal amount = BigDecimal.ZERO;
                    BigDecimal largessAmount = BigDecimal.ZERO;
                    for (int i = 0; i < saleBillPaySavingCardGeneralCard.size(); i++) {
                        RefundBillPaySavingCardGoodsForm newRefundBillPaySavingCardGoodsForm = new RefundBillPaySavingCardGoodsForm();
                        newRefundBillPaySavingCardGoodsForm.SavingCardAccountID = saleBillPaySavingCardGeneralCard.get(i).SavingCardAccountID;
                        newRefundBillPaySavingCardGoodsForm.Name = saleBillPaySavingCardGeneralCard.get(i).Name;
                        if (i == saleBillPaySavingCardGeneralCard.size() - 1) {
                            newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.subtract(amount);
                            newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.subtract(largessAmount);
                        } else {
                            newRefundBillPaySavingCardGoodsForm.Amount = BigDecimal.ZERO;
                            newRefundBillPaySavingCardGoodsForm.LargessAmount = BigDecimal.ZERO;
                            if (generalCardAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0 && saleBillPaySavingCardGeneralCard.get(i).Amount.compareTo(BigDecimal.ZERO) > 0) {
                                newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.multiply(saleBillPaySavingCardGeneralCard.get(i).Amount).divide(generalCardAccountEntity.SavingCardDeductionAmount.multiply(new BigDecimal(saleBillPaySavingCardGeneralCard.get(i).Quantity)), 2, BigDecimal.ROUND_HALF_UP);
                                amount = amount.add(newRefundBillPaySavingCardGoodsForm.Amount);
                            }
                            if (generalCardAccountEntity.SavingCardDeductionLargessAmount.compareTo(BigDecimal.ZERO) > 0 && saleBillPaySavingCardGeneralCard.get(i).LargessAmount.compareTo(BigDecimal.ZERO) > 0) {
                                newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.multiply(saleBillPaySavingCardGeneralCard.get(i).LargessAmount).divide(generalCardAccountEntity.SavingCardDeductionLargessAmount.multiply(new BigDecimal(saleBillPaySavingCardGeneralCard.get(i).Quantity)), 2, BigDecimal.ROUND_HALF_UP);
                                largessAmount = largessAmount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);
                            }
                        }
                        newRefundBillPaySavingCardGoodsForm.TotalAmount = newRefundBillPaySavingCardGoodsForm.Amount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);
                        refundBillPaySavingCardGoodsForms.add(newRefundBillPaySavingCardGoodsForm);
                    }
                }
            }
        }
        if (form.TimeCard != null && form.TimeCard.size() > 0) {
            for (RefundBillPayTimeCardForm refundBillPayTimeCardForm : form.TimeCard) {
                TimeCardAccountEntity timeCardAccountEntity = timeCardAccountMapper.getTimeCardAccountByAccountID(refundBillPayTimeCardForm.TimeCardAccountID);
                BigDecimal BalanceAmount = timeCardAccountEntity.PayPerformanceBalanceAmount.add(timeCardAccountEntity.CardPerformanceBalanceAmount).add(timeCardAccountEntity.CardLargessPerformanceBalanceAmount);
                if (!timeCardAccountEntity.IsLargess) {
                    if (refundBillPayTimeCardForm.TotalAmount.compareTo(BalanceAmount) > 0) {
                        throw new RuntimeException("时效卡账户没有足够的数量，请修改数量");
                    }
                    BigDecimal payAmount;
                    BigDecimal savingCardDeductionAmount;
                    BigDecimal savingCardDeductionLargessAmount;
                    if (refundBillPayTimeCardForm.TotalAmount.compareTo(BalanceAmount) < 0) {
                        RefundRateForm refundRateForm = getTimeCardAmount(timeCardAccountEntity, refundBillPayTimeCardForm.TotalAmount);
                        payAmount = refundRateForm.payAmount;
                        savingCardDeductionAmount = refundRateForm.savingCardDeductionAmount;
                        savingCardDeductionLargessAmount = refundRateForm.savingCardDeductionLargessAmount;
                        totalPayAmount = totalPayAmount.add(payAmount);
                        totalSavingCardDeductionAmount = totalSavingCardDeductionAmount.add(savingCardDeductionAmount);
                        totalSavingCardDeductionLargessAmount = totalSavingCardDeductionLargessAmount.add(savingCardDeductionLargessAmount);
                    } else {
                        payAmount = timeCardAccountEntity.PayPerformanceBalanceAmount;
                        savingCardDeductionAmount = timeCardAccountEntity.CardPerformanceBalanceAmount;
                        savingCardDeductionLargessAmount = timeCardAccountEntity.CardLargessPerformanceBalanceAmount;
                        totalPayAmount = totalPayAmount.add(payAmount);
                        totalSavingCardDeductionAmount = totalSavingCardDeductionAmount.add(savingCardDeductionAmount);
                        totalSavingCardDeductionLargessAmount = totalSavingCardDeductionLargessAmount.add(savingCardDeductionLargessAmount);
                    }
                    List<RefundBillPayGoodsForm> saleBillPayTimeCard = saleBillPayMapper.getSaleBillPayTimeCard(timeCardAccountEntity.SaleBillTimeCardID,timeCardAccountEntity.ID);
                    BigDecimal money = BigDecimal.ZERO;
                    for (int i = 0; i < saleBillPayTimeCard.size(); i++) {
                        RefundBillPayGoodsForm newRefundBillPayGoodsForm = new RefundBillPayGoodsForm();
                        newRefundBillPayGoodsForm.PayMethodID = saleBillPayTimeCard.get(i).PayMethodID;
                        newRefundBillPayGoodsForm.Name = saleBillPayTimeCard.get(i).Name;
                        if (i == saleBillPayTimeCard.size() - 1) {
                            newRefundBillPayGoodsForm.Amount = payAmount.subtract(money);
                        } else {
                            newRefundBillPayGoodsForm.Amount = payAmount.multiply(saleBillPayTimeCard.get(i).Amount).divide(timeCardAccountEntity.PayAmount, 2, BigDecimal.ROUND_HALF_UP);
                            money = money.add(newRefundBillPayGoodsForm.Amount);
                        }
                        refundBillPayGoodsForms.add(newRefundBillPayGoodsForm);
                    }

                    List<RefundBillPaySavingCardGoodsForm> saleBillPaySavingCardTimeCard = saleBillPayMapper.getSaleBillPaySavingCardTimeCard(timeCardAccountEntity.SaleBillTimeCardID,timeCardAccountEntity.ID);
                    BigDecimal amount = BigDecimal.ZERO;
                    BigDecimal largessAmount = BigDecimal.ZERO;
                    for (int i = 0; i < saleBillPaySavingCardTimeCard.size(); i++) {
                        RefundBillPaySavingCardGoodsForm newRefundBillPaySavingCardGoodsForm = new RefundBillPaySavingCardGoodsForm();
                        newRefundBillPaySavingCardGoodsForm.SavingCardAccountID = saleBillPaySavingCardTimeCard.get(i).SavingCardAccountID;
                        newRefundBillPaySavingCardGoodsForm.Name = saleBillPaySavingCardTimeCard.get(i).Name;
                        if (i == saleBillPaySavingCardTimeCard.size() - 1) {
                            newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.subtract(amount);
                            newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.subtract(largessAmount);
                        } else {
                            newRefundBillPaySavingCardGoodsForm.Amount = BigDecimal.ZERO;
                            newRefundBillPaySavingCardGoodsForm.LargessAmount = BigDecimal.ZERO;
                            if (timeCardAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0 && saleBillPaySavingCardTimeCard.get(i).Amount.compareTo(BigDecimal.ZERO) > 0) {
                                newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.multiply(saleBillPaySavingCardTimeCard.get(i).Amount).divide(timeCardAccountEntity.SavingCardDeductionAmount.multiply(new BigDecimal(saleBillPaySavingCardTimeCard.get(i).Quantity)), 2, BigDecimal.ROUND_HALF_UP);
                                amount = amount.add(newRefundBillPaySavingCardGoodsForm.Amount);
                            }
                            if (timeCardAccountEntity.SavingCardDeductionLargessAmount.compareTo(BigDecimal.ZERO) > 0 && saleBillPaySavingCardTimeCard.get(i).LargessAmount.compareTo(BigDecimal.ZERO) > 0) {
                                newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.multiply(saleBillPaySavingCardTimeCard.get(i).LargessAmount).divide(timeCardAccountEntity.SavingCardDeductionLargessAmount.multiply(new BigDecimal(saleBillPaySavingCardTimeCard.get(i).Quantity)), 2, BigDecimal.ROUND_HALF_UP);
                                largessAmount = largessAmount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);
                            }
                        }
                        newRefundBillPaySavingCardGoodsForm.TotalAmount = newRefundBillPaySavingCardGoodsForm.Amount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);
                        refundBillPaySavingCardGoodsForms.add(newRefundBillPaySavingCardGoodsForm);
                    }
                }
            }
        }
        if (form.SavingCard != null && form.SavingCard.size() > 0) {
            for (RefundBillPaySavingCardForm refundBillPaySavingCardForm : form.SavingCard) {
                SavingCardAccountEntity savingCardAccountEntity = savingCardAccountMapper.getSavingCardAccountByID(refundBillPaySavingCardForm.SavingCardAccountID);
                if (refundBillPaySavingCardForm.Amount.compareTo((savingCardAccountEntity.Balance.subtract(savingCardAccountEntity.ArrearAmount))) > 0 || refundBillPaySavingCardForm.LargessAmount.compareTo(savingCardAccountEntity.LargessAmount) > 0) {
                    throw new RuntimeException("储值卡账户余额不足，请修改金额");
                }
                BigDecimal payAmount = refundBillPaySavingCardForm.Amount;

                totalPayAmount = totalPayAmount.add(payAmount);


                List<RefundBillPayGoodsForm> saleBillPaySavingCard = saleBillPayMapper.getSaleBillPaySavingCard(savingCardAccountEntity.SaleBillSavingCardID,savingCardAccountEntity.ID);
                BigDecimal money = BigDecimal.ZERO;
                for (int i = 0; i < saleBillPaySavingCard.size(); i++) {
                    RefundBillPayGoodsForm newRefundBillPayGoodsForm = new RefundBillPayGoodsForm();
                    newRefundBillPayGoodsForm.PayMethodID = saleBillPaySavingCard.get(i).PayMethodID;
                    newRefundBillPayGoodsForm.Name = saleBillPaySavingCard.get(i).Name;
                    if (i == saleBillPaySavingCard.size() - 1) {
                        newRefundBillPayGoodsForm.Amount = payAmount.subtract(money);
                    } else {
                        newRefundBillPayGoodsForm.Amount = payAmount.multiply(saleBillPaySavingCard.get(i).Amount).divide(savingCardAccountEntity.Amount, 2, BigDecimal.ROUND_HALF_UP);
                        money = money.add(newRefundBillPayGoodsForm.Amount);
                    }
                    refundBillPayGoodsForms.add(newRefundBillPayGoodsForm);
                }
            }
        }

        if (form.PackageCard != null && form.PackageCard.size() > 0) {
            for (RefundBillPayPackageCardForm refundBillPayPackageCardForm : form.PackageCard) {
                if (refundBillPayPackageCardForm.Product != null && refundBillPayPackageCardForm.Product.size() > 0) {
                    for (RefundBillPayProductForm refundBillPayProductForm : refundBillPayPackageCardForm.Product) {
                        ProductAccountEntity productAccountEntity = productAccountMapper.getProductAccountByAccountID(refundBillPayProductForm.ProductAccountID);

                        BigDecimal BalanceAmount = productAccountEntity.PayPerformanceBalanceAmount.add(productAccountEntity.CardPerformanceBalanceAmount).add(productAccountEntity.CardLargessPerformanceBalanceAmount);
                        if (!productAccountEntity.IsLargess) {
                            if (refundBillPayProductForm.TotalAmount.compareTo(BalanceAmount) > 0) {
                                throw new RuntimeException("产品账户没有足够的数量，请修改数量");
                            }

                            BigDecimal payAmount;
                            BigDecimal savingCardDeductionAmount;
                            BigDecimal savingCardDeductionLargessAmount;
                            if (refundBillPayProductForm.TotalAmount.compareTo(BalanceAmount) < 0) {
                                RefundRateForm refundRateForm = getProductAmount(productAccountEntity, refundBillPayProductForm.TotalAmount);
                                payAmount = refundRateForm.payAmount;
                                savingCardDeductionAmount = refundRateForm.savingCardDeductionAmount;
                                savingCardDeductionLargessAmount = refundRateForm.savingCardDeductionLargessAmount;
                            } else {
                                payAmount = productAccountEntity.PayPerformanceBalanceAmount;
                                savingCardDeductionAmount = productAccountEntity.CardPerformanceBalanceAmount;
                                savingCardDeductionLargessAmount = productAccountEntity.CardLargessPerformanceBalanceAmount;
                            }
                            totalPayAmount = totalPayAmount.add(payAmount);
                            totalSavingCardDeductionAmount = totalSavingCardDeductionAmount.add(savingCardDeductionAmount);
                            totalSavingCardDeductionLargessAmount = totalSavingCardDeductionLargessAmount.add(savingCardDeductionLargessAmount);
                            List<RefundBillPayGoodsForm> saleBillPayProduct = saleBillPayMapper.getSaleBillPayPackageCardProduct(productAccountEntity.SaleBillPackageCardProductID,productAccountEntity.ID);
                            BigDecimal money = BigDecimal.ZERO;
                            for (int i = 0; i < saleBillPayProduct.size(); i++) {
                                RefundBillPayGoodsForm newRefundBillPayGoodsForm = new RefundBillPayGoodsForm();
                                newRefundBillPayGoodsForm.PayMethodID = saleBillPayProduct.get(i).PayMethodID;
                                newRefundBillPayGoodsForm.Name = saleBillPayProduct.get(i).Name;
                                if (i == saleBillPayProduct.size() - 1) {
                                    newRefundBillPayGoodsForm.Amount = payAmount.subtract(money);
                                } else {
                                    newRefundBillPayGoodsForm.Amount = payAmount.multiply(saleBillPayProduct.get(i).Amount).divide(productAccountEntity.PayAmount, 2, BigDecimal.ROUND_HALF_UP);
                                    money = money.add(newRefundBillPayGoodsForm.Amount);
                                }
                                refundBillPayGoodsForms.add(newRefundBillPayGoodsForm);
                            }

                            List<RefundBillPaySavingCardGoodsForm> saleBillPaySavingCardProduct = saleBillPayMapper.getSaleBillPayPackageCardSavingCardProduct(productAccountEntity.SaleBillPackageCardProductID,productAccountEntity.ID);
                            BigDecimal amount = BigDecimal.ZERO;
                            BigDecimal largessAmount = BigDecimal.ZERO;
                            for (int i = 0; i < saleBillPaySavingCardProduct.size(); i++) {
                                RefundBillPaySavingCardGoodsForm newRefundBillPaySavingCardGoodsForm = new RefundBillPaySavingCardGoodsForm();
                                newRefundBillPaySavingCardGoodsForm.SavingCardAccountID = saleBillPaySavingCardProduct.get(i).SavingCardAccountID;
                                newRefundBillPaySavingCardGoodsForm.Name = saleBillPaySavingCardProduct.get(i).Name;
                                if (i == saleBillPayProduct.size() - 1) {
                                    newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.subtract(amount);
                                    newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.subtract(largessAmount);
                                } else {
                                    newRefundBillPaySavingCardGoodsForm.Amount = BigDecimal.ZERO;
                                    newRefundBillPaySavingCardGoodsForm.LargessAmount = BigDecimal.ZERO;
                                    if (productAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
                                        newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.multiply(saleBillPaySavingCardProduct.get(i).Amount).divide(productAccountEntity.SavingCardDeductionAmount.multiply(new BigDecimal(saleBillPaySavingCardProduct.get(i).Quantity)), 2, BigDecimal.ROUND_HALF_UP);
                                        amount = amount.add(newRefundBillPaySavingCardGoodsForm.Amount);
                                    }
                                    if (productAccountEntity.SavingCardDeductionLargessAmount.compareTo(BigDecimal.ZERO) > 0) {
                                        newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.multiply(saleBillPaySavingCardProduct.get(i).LargessAmount).divide(productAccountEntity.SavingCardDeductionLargessAmount.multiply(new BigDecimal(saleBillPaySavingCardProduct.get(i).Quantity)), 2, BigDecimal.ROUND_HALF_UP);
                                        largessAmount = largessAmount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);
                                    }
                                }
                                newRefundBillPaySavingCardGoodsForm.TotalAmount = newRefundBillPaySavingCardGoodsForm.Amount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);
                                refundBillPaySavingCardGoodsForms.add(newRefundBillPaySavingCardGoodsForm);
                            }

                        }
                    }
                }

                if (refundBillPayPackageCardForm.Project != null && refundBillPayPackageCardForm.Project.size() > 0) {
                    for (RefundBillPayProjectForm refundBillPayProjectForm : refundBillPayPackageCardForm.Project) {
                        ProjectAccountEntity projectAccountEntity = projectAccountMapper.getProjectAccountByAccountID(refundBillPayProjectForm.ProjectAccountID);
                        BigDecimal BalanceAmount = projectAccountEntity.PayPerformanceBalanceAmount.add(projectAccountEntity.CardPerformanceBalanceAmount).add(projectAccountEntity.CardLargessPerformanceBalanceAmount);
                        if (!projectAccountEntity.IsLargess) {
                            if (refundBillPayProjectForm.TotalAmount.compareTo(BalanceAmount) > 0) {
                                throw new RuntimeException("项目账户没有足够的数量，请修改数量");
                            }
                            BigDecimal payAmount;
                            BigDecimal savingCardDeductionAmount;
                            BigDecimal savingCardDeductionLargessAmount;
                            if (refundBillPayProjectForm.TotalAmount.compareTo(BalanceAmount) < 0) {
                                RefundRateForm refundRateForm = getProjectAmount(projectAccountEntity, refundBillPayProjectForm.TotalAmount);
                                payAmount = refundRateForm.payAmount;
                                savingCardDeductionAmount = refundRateForm.savingCardDeductionAmount;
                                savingCardDeductionLargessAmount = refundRateForm.savingCardDeductionLargessAmount;
                            } else {
                                payAmount = projectAccountEntity.PayPerformanceBalanceAmount;
                                savingCardDeductionAmount = projectAccountEntity.CardPerformanceBalanceAmount;
                                savingCardDeductionLargessAmount = projectAccountEntity.CardLargessPerformanceBalanceAmount;
                            }
                            totalPayAmount = totalPayAmount.add(payAmount);
                            totalSavingCardDeductionAmount = totalSavingCardDeductionAmount.add(savingCardDeductionAmount);
                            totalSavingCardDeductionLargessAmount = totalSavingCardDeductionLargessAmount.add(savingCardDeductionLargessAmount);
                            List<RefundBillPayGoodsForm> saleBillPayProject = saleBillPayMapper.getSaleBillPayPackageCardProject(projectAccountEntity.SaleBillPackageCardProjectID,projectAccountEntity.ID);
                            BigDecimal money = BigDecimal.ZERO;
                            for (int i = 0; i < saleBillPayProject.size(); i++) {
                                RefundBillPayGoodsForm newRefundBillPayGoodsForm = new RefundBillPayGoodsForm();
                                newRefundBillPayGoodsForm.PayMethodID = saleBillPayProject.get(i).PayMethodID;
                                newRefundBillPayGoodsForm.Name = saleBillPayProject.get(i).Name;
                                if (i == saleBillPayProject.size() - 1) {
                                    newRefundBillPayGoodsForm.Amount = payAmount.subtract(money);
                                } else {
                                    newRefundBillPayGoodsForm.Amount = payAmount.multiply(saleBillPayProject.get(i).Amount).divide(projectAccountEntity.PayAmount, 2, BigDecimal.ROUND_HALF_UP);
                                    money = money.add(newRefundBillPayGoodsForm.Amount);
                                }
                                refundBillPayGoodsForms.add(newRefundBillPayGoodsForm);
                            }

                            List<RefundBillPaySavingCardGoodsForm> saleBillPaySavingCardProject = saleBillPayMapper.getSaleBillPayPackageCardSavingCardProject(projectAccountEntity.SaleBillPackageCardProjectID,projectAccountEntity.ID);
                            BigDecimal amount = BigDecimal.ZERO;
                            BigDecimal largessAmount = BigDecimal.ZERO;
                            for (int i = 0; i < saleBillPaySavingCardProject.size(); i++) {
                                RefundBillPaySavingCardGoodsForm newRefundBillPaySavingCardGoodsForm = new RefundBillPaySavingCardGoodsForm();
                                newRefundBillPaySavingCardGoodsForm.SavingCardAccountID = saleBillPaySavingCardProject.get(i).SavingCardAccountID;
                                newRefundBillPaySavingCardGoodsForm.Name = saleBillPaySavingCardProject.get(i).Name;
                                if (i == saleBillPaySavingCardProject.size() - 1) {
                                    newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.subtract(amount);
                                    newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.subtract(largessAmount);
                                } else {
                                    newRefundBillPaySavingCardGoodsForm.Amount = BigDecimal.ZERO;
                                    newRefundBillPaySavingCardGoodsForm.LargessAmount = BigDecimal.ZERO;
                                    if (projectAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
                                        newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.multiply(saleBillPaySavingCardProject.get(i).Amount).divide(projectAccountEntity.SavingCardDeductionAmount.multiply(new BigDecimal(saleBillPaySavingCardProject.get(i).Quantity)), 2, BigDecimal.ROUND_HALF_UP);
                                        amount = amount.add(newRefundBillPaySavingCardGoodsForm.Amount);
                                    }
                                    if (projectAccountEntity.SavingCardDeductionLargessAmount.compareTo(BigDecimal.ZERO) > 0) {
                                        newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.multiply(saleBillPaySavingCardProject.get(i).LargessAmount).divide(projectAccountEntity.SavingCardDeductionLargessAmount.multiply(new BigDecimal(saleBillPaySavingCardProject.get(i).Quantity)), 2, BigDecimal.ROUND_HALF_UP);
                                        largessAmount = largessAmount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);
                                    }
                                }
                                newRefundBillPaySavingCardGoodsForm.TotalAmount = newRefundBillPaySavingCardGoodsForm.Amount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);
                                refundBillPaySavingCardGoodsForms.add(newRefundBillPaySavingCardGoodsForm);
                            }
                        }
                    }
                }
                if (refundBillPayPackageCardForm.GeneralCard != null && refundBillPayPackageCardForm.GeneralCard.size() > 0) {
                    for (RefundBillPayGeneralCardForm refundBillPayGeneralCardForm : refundBillPayPackageCardForm.GeneralCard) {
                        GeneralCardAccountEntity generalCardAccountEntity = generalCardAccountMapper.getGeneralCardAccountByAccountID(refundBillPayGeneralCardForm.GeneralCardAccountID);
                        BigDecimal BalanceAmount = generalCardAccountEntity.PayPerformanceBalanceAmount.add(generalCardAccountEntity.CardPerformanceBalanceAmount).add(generalCardAccountEntity.CardLargessPerformanceBalanceAmount);
                        if (!generalCardAccountEntity.IsLargess) {
                            if (refundBillPayGeneralCardForm.TotalAmount.compareTo(BalanceAmount) > 0) {
                                throw new RuntimeException("通用次卡账户没有足够的数量，请修改数量");
                            }
                            BigDecimal payAmount;
                            BigDecimal savingCardDeductionAmount;
                            BigDecimal savingCardDeductionLargessAmount;
                            if (refundBillPayGeneralCardForm.TotalAmount.compareTo(BalanceAmount) < 0) {
                                RefundRateForm refundRateForm = getGeneralCardAmount(generalCardAccountEntity, refundBillPayGeneralCardForm.TotalAmount);
                                payAmount = refundRateForm.payAmount;
                                savingCardDeductionAmount = refundRateForm.savingCardDeductionAmount;
                                savingCardDeductionLargessAmount = refundRateForm.savingCardDeductionLargessAmount;
                                totalPayAmount = totalPayAmount.add(payAmount);
                                totalSavingCardDeductionAmount = totalSavingCardDeductionAmount.add(savingCardDeductionAmount);
                                totalSavingCardDeductionLargessAmount = totalSavingCardDeductionLargessAmount.add(savingCardDeductionLargessAmount);
                            } else {
                                payAmount = generalCardAccountEntity.PayPerformanceBalanceAmount;
                                savingCardDeductionAmount = generalCardAccountEntity.CardPerformanceBalanceAmount;
                                savingCardDeductionLargessAmount = generalCardAccountEntity.CardLargessPerformanceBalanceAmount;
                                totalPayAmount = totalPayAmount.add(payAmount);
                                totalSavingCardDeductionAmount = totalSavingCardDeductionAmount.add(savingCardDeductionAmount);
                                totalSavingCardDeductionLargessAmount = totalSavingCardDeductionLargessAmount.add(savingCardDeductionLargessAmount);
                            }
                            List<RefundBillPayGoodsForm> saleBillPayGeneralCard = saleBillPayMapper.getSaleBillPayPackageCardGeneralCard(generalCardAccountEntity.SaleBillPackageCardGeneralCardID,generalCardAccountEntity.ID);
                            BigDecimal money = BigDecimal.ZERO;
                            for (int i = 0; i < saleBillPayGeneralCard.size(); i++) {
                                RefundBillPayGoodsForm newRefundBillPayGoodsForm = new RefundBillPayGoodsForm();
                                newRefundBillPayGoodsForm.PayMethodID = saleBillPayGeneralCard.get(i).PayMethodID;
                                newRefundBillPayGoodsForm.Name = saleBillPayGeneralCard.get(i).Name;
                                if (i == saleBillPayGeneralCard.size() - 1) {
                                    newRefundBillPayGoodsForm.Amount = payAmount.subtract(money);
                                } else {
                                    newRefundBillPayGoodsForm.Amount = payAmount.multiply(saleBillPayGeneralCard.get(i).Amount).divide(generalCardAccountEntity.PayAmount, 2, BigDecimal.ROUND_HALF_UP);
                                    money = money.add(newRefundBillPayGoodsForm.Amount);
                                }
                                refundBillPayGoodsForms.add(newRefundBillPayGoodsForm);
                            }
                            List<RefundBillPaySavingCardGoodsForm> saleBillPaySavingCardGeneralCard = saleBillPayMapper.getSaleBillPayPackageCardSavingCardGeneralCard(generalCardAccountEntity.SaleBillPackageCardGeneralCardID,generalCardAccountEntity.ID);
                            BigDecimal amount = BigDecimal.ZERO;
                            BigDecimal largessAmount = BigDecimal.ZERO;
                            for (int i = 0; i < saleBillPaySavingCardGeneralCard.size(); i++) {
                                RefundBillPaySavingCardGoodsForm newRefundBillPaySavingCardGoodsForm = new RefundBillPaySavingCardGoodsForm();
                                newRefundBillPaySavingCardGoodsForm.SavingCardAccountID = saleBillPaySavingCardGeneralCard.get(i).SavingCardAccountID;
                                newRefundBillPaySavingCardGoodsForm.Name = saleBillPaySavingCardGeneralCard.get(i).Name;
                                if (i == saleBillPaySavingCardGeneralCard.size() - 1) {
                                    newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.subtract(amount);
                                    newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.subtract(largessAmount);
                                } else {
                                    newRefundBillPaySavingCardGoodsForm.Amount = BigDecimal.ZERO;
                                    newRefundBillPaySavingCardGoodsForm.LargessAmount = BigDecimal.ZERO;
                                    if (generalCardAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0 && saleBillPaySavingCardGeneralCard.get(i).Amount.compareTo(BigDecimal.ZERO) > 0) {
                                        newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.multiply(saleBillPaySavingCardGeneralCard.get(i).Amount).divide(generalCardAccountEntity.SavingCardDeductionAmount.multiply(new BigDecimal(saleBillPaySavingCardGeneralCard.get(i).Quantity)), 2, BigDecimal.ROUND_HALF_UP);
                                        amount = amount.add(newRefundBillPaySavingCardGoodsForm.Amount);
                                    }
                                    if (generalCardAccountEntity.SavingCardDeductionLargessAmount.compareTo(BigDecimal.ZERO) > 0 && saleBillPaySavingCardGeneralCard.get(i).LargessAmount.compareTo(BigDecimal.ZERO) > 0) {
                                        newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.multiply(saleBillPaySavingCardGeneralCard.get(i).LargessAmount).divide(generalCardAccountEntity.SavingCardDeductionLargessAmount.multiply(new BigDecimal(saleBillPaySavingCardGeneralCard.get(i).Quantity)), 2, BigDecimal.ROUND_HALF_UP);
                                        largessAmount = largessAmount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);
                                    }
                                }
                                newRefundBillPaySavingCardGoodsForm.TotalAmount = newRefundBillPaySavingCardGoodsForm.Amount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);
                                refundBillPaySavingCardGoodsForms.add(newRefundBillPaySavingCardGoodsForm);
                            }
                        }
                    }
                }
                if (refundBillPayPackageCardForm.TimeCard != null && refundBillPayPackageCardForm.TimeCard.size() > 0) {
                    for (RefundBillPayTimeCardForm refundBillPayTimeCardForm : refundBillPayPackageCardForm.TimeCard) {
                        TimeCardAccountEntity timeCardAccountEntity = timeCardAccountMapper.getTimeCardAccountByAccountID(refundBillPayTimeCardForm.TimeCardAccountID);
                        BigDecimal BalanceAmount = timeCardAccountEntity.PayPerformanceBalanceAmount.add(timeCardAccountEntity.CardPerformanceBalanceAmount).add(timeCardAccountEntity.CardLargessPerformanceBalanceAmount);
                        if (timeCardAccountEntity.IsLargess == false) {
                            if (refundBillPayTimeCardForm.TotalAmount.compareTo(BalanceAmount) > 0) {
                                throw new RuntimeException("时效卡账户没有足够的数量，请修改数量");
                            }
                            BigDecimal payAmount;
                            BigDecimal savingCardDeductionAmount;
                            BigDecimal savingCardDeductionLargessAmount;
                            if (refundBillPayTimeCardForm.TotalAmount.compareTo(BalanceAmount) < 0) {
                                RefundRateForm refundRateForm = getTimeCardAmount(timeCardAccountEntity, refundBillPayTimeCardForm.TotalAmount);
                                payAmount = refundRateForm.payAmount;
                                savingCardDeductionAmount = refundRateForm.savingCardDeductionAmount;
                                savingCardDeductionLargessAmount = refundRateForm.savingCardDeductionLargessAmount;
                            } else {
                                payAmount = timeCardAccountEntity.PayPerformanceBalanceAmount;
                                savingCardDeductionAmount = timeCardAccountEntity.CardPerformanceBalanceAmount;
                                savingCardDeductionLargessAmount = timeCardAccountEntity.CardLargessPerformanceBalanceAmount;
                            }
                            totalPayAmount = totalPayAmount.add(payAmount);
                            totalSavingCardDeductionAmount = totalSavingCardDeductionAmount.add(savingCardDeductionAmount);
                            totalSavingCardDeductionLargessAmount = totalSavingCardDeductionLargessAmount.add(savingCardDeductionLargessAmount);
                            List<RefundBillPayGoodsForm> saleBillPayTimeCard = saleBillPayMapper.getSaleBillPayPackageCardTimeCard(timeCardAccountEntity.SaleBillPackageCardTimeCardID,timeCardAccountEntity.ID);
                            BigDecimal money = BigDecimal.ZERO;
                            for (int i = 0; i < saleBillPayTimeCard.size(); i++) {
                                RefundBillPayGoodsForm newRefundBillPayGoodsForm = new RefundBillPayGoodsForm();
                                newRefundBillPayGoodsForm.PayMethodID = saleBillPayTimeCard.get(i).PayMethodID;
                                newRefundBillPayGoodsForm.Name = saleBillPayTimeCard.get(i).Name;
                                if (i == saleBillPayTimeCard.size() - 1) {
                                    newRefundBillPayGoodsForm.Amount = payAmount.subtract(money);
                                } else {
                                    newRefundBillPayGoodsForm.Amount = payAmount.multiply(saleBillPayTimeCard.get(i).Amount).divide(timeCardAccountEntity.PayAmount, 2, BigDecimal.ROUND_HALF_UP);
                                    money = money.add(newRefundBillPayGoodsForm.Amount);
                                }
                                refundBillPayGoodsForms.add(newRefundBillPayGoodsForm);
                            }

                            List<RefundBillPaySavingCardGoodsForm> saleBillPaySavingCardTimeCard = saleBillPayMapper.getSaleBillPayPackageCardSavingCardTimeCard(timeCardAccountEntity.SaleBillPackageCardTimeCardID,timeCardAccountEntity.ID);
                            BigDecimal amount = BigDecimal.ZERO;
                            BigDecimal largessAmount = BigDecimal.ZERO;
                            for (int i = 0; i < saleBillPaySavingCardTimeCard.size(); i++) {
                                RefundBillPaySavingCardGoodsForm newRefundBillPaySavingCardGoodsForm = new RefundBillPaySavingCardGoodsForm();
                                newRefundBillPaySavingCardGoodsForm.SavingCardAccountID = saleBillPaySavingCardTimeCard.get(i).SavingCardAccountID;
                                newRefundBillPaySavingCardGoodsForm.Name = saleBillPaySavingCardTimeCard.get(i).Name;
                                if (i == saleBillPaySavingCardTimeCard.size() - 1) {
                                    newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.subtract(amount);
                                    newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.subtract(largessAmount);
                                } else {
                                    newRefundBillPaySavingCardGoodsForm.Amount = BigDecimal.ZERO;
                                    newRefundBillPaySavingCardGoodsForm.LargessAmount = BigDecimal.ZERO;
                                    if (timeCardAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0 && saleBillPaySavingCardTimeCard.get(i).Amount.compareTo(BigDecimal.ZERO) > 0) {
                                        newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.multiply(saleBillPaySavingCardTimeCard.get(i).Amount).divide(timeCardAccountEntity.SavingCardDeductionAmount.multiply(new BigDecimal(saleBillPaySavingCardTimeCard.get(i).Quantity)), 2, BigDecimal.ROUND_HALF_UP);
                                        amount = amount.add(newRefundBillPaySavingCardGoodsForm.Amount);
                                    }
                                    if (timeCardAccountEntity.SavingCardDeductionLargessAmount.compareTo(BigDecimal.ZERO) > 0 && saleBillPaySavingCardTimeCard.get(i).LargessAmount.compareTo(BigDecimal.ZERO) > 0) {
                                        newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.multiply(saleBillPaySavingCardTimeCard.get(i).LargessAmount).divide(timeCardAccountEntity.SavingCardDeductionLargessAmount.multiply(new BigDecimal(saleBillPaySavingCardTimeCard.get(i).Quantity)), 2, BigDecimal.ROUND_HALF_UP);
                                        largessAmount = largessAmount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);
                                    }
                                }
                                newRefundBillPaySavingCardGoodsForm.TotalAmount = newRefundBillPaySavingCardGoodsForm.Amount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);
                                refundBillPaySavingCardGoodsForms.add(newRefundBillPaySavingCardGoodsForm);
                            }
                        }
                    }
                }
                if (refundBillPayPackageCardForm.SavingCard != null && refundBillPayPackageCardForm.SavingCard.size() > 0) {
                    for (RefundBillPaySavingCardForm refundBillPaySavingCardForm : refundBillPayPackageCardForm.SavingCard) {
                        SavingCardAccountEntity savingCardAccountEntity = savingCardAccountMapper.getSavingCardAccountByID(refundBillPaySavingCardForm.SavingCardAccountID);
                        if (refundBillPaySavingCardForm.Amount.compareTo(savingCardAccountEntity.Balance) > 0 || refundBillPaySavingCardForm.LargessAmount.compareTo(savingCardAccountEntity.LargessAmount) > 0) {
                            throw new RuntimeException("储值卡账户余额不足，请修改金额");
                        }
                        BigDecimal payAmount = refundBillPaySavingCardForm.Amount;

                        totalPayAmount = totalPayAmount.add(payAmount);


                        List<RefundBillPayGoodsForm> saleBillPaySavingCard = saleBillPayMapper.getSaleBillPayPackageCardSavingCard(savingCardAccountEntity.SaleBillPackageCardSavingCardID,savingCardAccountEntity.ID);
                        BigDecimal money = BigDecimal.ZERO;
                        for (int i = 0; i < saleBillPaySavingCard.size(); i++) {
                            RefundBillPayGoodsForm newRefundBillPayGoodsForm = new RefundBillPayGoodsForm();
                            newRefundBillPayGoodsForm.PayMethodID = saleBillPaySavingCard.get(i).PayMethodID;
                            newRefundBillPayGoodsForm.Name = saleBillPaySavingCard.get(i).Name;
                            if (i == saleBillPaySavingCard.size() - 1) {
                                newRefundBillPayGoodsForm.Amount = payAmount.subtract(money);
                            } else {
                                newRefundBillPayGoodsForm.Amount = payAmount.multiply(saleBillPaySavingCard.get(i).Amount).divide(savingCardAccountEntity.Amount, 2, BigDecimal.ROUND_HALF_UP);
                                money = money.add(newRefundBillPayGoodsForm.Amount);
                            }
                            refundBillPayGoodsForms.add(newRefundBillPayGoodsForm);
                        }
                    }
                }
            }
        }


        List<RefundBillPayGoodsForm> newRefundBillPayGoodsForms = new ArrayList<>();
        refundBillPayGoodsForms.stream().collect(Collectors.groupingBy(i -> i.PayMethodID)).forEach((key, list) -> {
            RefundBillPayGoodsForm refundBillPayGoodsForm = new RefundBillPayGoodsForm();
            refundBillPayGoodsForm.PayMethodID = key;
            refundBillPayGoodsForm.Name = list.stream().map(i -> i.Name).collect(Collectors.toList()).get(0);
            refundBillPayGoodsForm.Amount = list.stream().map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
            newRefundBillPayGoodsForms.add(refundBillPayGoodsForm);
        });
        List<RefundBillPaySavingCardGoodsForm> newRefundBillPaySavingCardGoodsForm = new ArrayList<>();
        refundBillPaySavingCardGoodsForms.stream().collect(Collectors.groupingBy(i -> i.SavingCardAccountID)).forEach((key, list) -> {
            RefundBillPaySavingCardGoodsForm refundBillPaySavingCardGoodsForm = new RefundBillPaySavingCardGoodsForm();
            refundBillPaySavingCardGoodsForm.SavingCardAccountID = key;
            refundBillPaySavingCardGoodsForm.Name = list.stream().map(i -> i.Name).collect(Collectors.toList()).get(0);
            refundBillPaySavingCardGoodsForm.Amount = list.stream().map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
            refundBillPaySavingCardGoodsForm.LargessAmount = list.stream().map(i -> i.LargessAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
            refundBillPaySavingCardGoodsForm.TotalAmount = refundBillPaySavingCardGoodsForm.Amount.add(refundBillPaySavingCardGoodsForm.LargessAmount);
            newRefundBillPaySavingCardGoodsForm.add(refundBillPaySavingCardGoodsForm);
        });

        RefundBillPayOutputForm refundBillPayOutputForm = new RefundBillPayOutputForm();
        refundBillPayOutputForm.PayAmount = totalPayAmount;
        refundBillPayOutputForm.CardDeductionAmount = totalSavingCardDeductionAmount;
        refundBillPayOutputForm.CardDeductionLargessAmount = totalSavingCardDeductionLargessAmount;
        refundBillPayOutputForm.Amount = refundBillPayOutputForm.PayAmount.add(refundBillPayOutputForm.CardDeductionAmount);
        refundBillPayOutputForm.TotalAmount = refundBillPayOutputForm.Amount.add(refundBillPayOutputForm.CardDeductionLargessAmount);
        refundBillPayOutputForm.PayMethod = newRefundBillPayGoodsForms;
        refundBillPayOutputForm.SavingCardDeduction = newRefundBillPaySavingCardGoodsForm;

        return BaseOutputForm.success(refundBillPayOutputForm);
    }

    @Transactional
    public BaseOutput create(RefundBillAddForm form, String channel) throws Exception {
        Boolean replacementOrderRestriction = sealingAccountService.getReplacementOrderRestriction(form.BillDate);
        if(replacementOrderRestriction){
            throw new RuntimeException("补单时间处于关账时间内，请选择其他时间");
        }
        RefundSaleBillEntity refundBillEntity = new RefundSaleBillEntity();
        refundBillEntity.ID = AutoNumberUtils.getIBeautyRefundSaleBillID();
        refundBillEntity.BillDate = DateTimeUtil.toDate(form.BillDate);
        refundBillEntity.EntityID = SystemUserUtil.getSystemEntityID();
        refundBillEntity.CustomerID = form.CustomerID;
        refundBillEntity.ApprovalStatus = "20";
        refundBillEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        refundBillEntity.CreatedOn = new Date();
        refundBillEntity.RefundRemark = form.Remark;
        refundBillEntity.PayAmount = form.PayAmount;
        if (form.PricePreferentialAmount == null || form.PricePreferentialAmount.equals(""))
            form.PricePreferentialAmount = BigDecimal.ZERO;

        refundBillEntity.PricePreferentialAmount = form.PricePreferentialAmount;
        refundBillEntity.TotalAmount = form.TotalAmount;
        refundBillEntity.CardDeductionAmount = form.CardDeductionAmount;
        refundBillEntity.CardDeductionLargessAmount = form.CardDeductionLargessAmount;
        refundBillEntity.Channel = channel;
        refundBillMapper.createRefundBill(refundBillEntity);

        RefundBillProcessEntity refundTreatBillProcessEntity = new RefundBillProcessEntity();
        refundTreatBillProcessEntity.RefundBillID = refundBillEntity.ID;
        refundTreatBillProcessEntity.ApprovalBy = SystemUserUtil.getSystemUserID();
        refundTreatBillProcessEntity.ApprovalOn = new Date();
        refundTreatBillProcessEntity.ApprovalRemark = SystemUserUtil.getSystemUserID();
        refundTreatBillProcessEntity.ActionType = "20";
        refundBillMapper.createRefundBillProcess(refundTreatBillProcessEntity);

        List<RefundBillHandlerProductEntity> product = new ArrayList<>();
        List<RefundBillHandlerProjectEntity> project = new ArrayList<>();
        List<RefundBillHandlerGeneralCardEntity> generalCard = new ArrayList<>();
        List<RefundBillHandlerTimeCardEntity> timeCard = new ArrayList<>();
        List<RefundBillHandlerSavingCardEntity> savingCard = new ArrayList<>();
        List<RefundBillHandlerPackageCardEntity> packageCard = new ArrayList<>();
        List<RefundBillPayEntity> payMethod = new ArrayList<>();
        List<RefundBillPaySavingCardDeductionEntity> savingCardDeduction = new ArrayList<>();
        if (form.Product != null && form.Product.size() > 0) {
            for (RefundBillProductForm refundBillProductForm : form.Product) {
                ProductAccountEntity productAccountEntity = productAccountMapper.getProductAccountByAccountID(refundBillProductForm.ProductAccountID);
                RefundBillProductEntity refundBillProductEntity = new RefundBillProductEntity();
                refundBillProductEntity.RefundBillID = refundBillEntity.ID;
                refundBillProductEntity.ProductAccountID = refundBillProductForm.ProductAccountID;
                refundBillProductEntity.ProductID = productAccountEntity.ProductID;
                refundBillProductEntity.Price = refundBillProductForm.Price;
                refundBillProductEntity.Quantity = refundBillProductForm.Quantity;
                if (refundBillProductForm.PricePreferentialAmount == null || refundBillProductForm.PricePreferentialAmount.equals(""))
                    refundBillProductForm.PricePreferentialAmount = BigDecimal.ZERO;

                refundBillProductEntity.PricePreferentialAmount = refundBillProductForm.PricePreferentialAmount;
                refundBillProductEntity.TotalAmount = refundBillProductForm.TotalAmount;
                refundBillMapper.createRefundBillProduct(refundBillProductEntity);

                for (RefundBillHandlerForm refundBillHandlerForm : refundBillProductForm.RefundHandler) {
                    RefundBillHandlerProductEntity refundBillHandlerProductEntity = new RefundBillHandlerProductEntity();
                    refundBillHandlerProductEntity.RefundBillProductID = refundBillProductEntity.ID;
                    refundBillHandlerProductEntity.ProductSaleHandlerID = refundBillHandlerForm.HandlerID;
                    refundBillHandlerProductEntity.EmployeeID = refundBillHandlerForm.EmployeeID;
                    refundBillHandlerProductEntity.Scale = refundBillHandlerForm.Scale.divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP);
                    product.add(refundBillHandlerProductEntity);
                }
            }
        }
        if (form.Project != null && form.Project.size() > 0) {
            for (RefundBillProjectForm refundBillProjectForm : form.Project) {
                ProjectAccountEntity projectAccountEntity = projectAccountMapper.getProjectAccountByAccountID(refundBillProjectForm.ProjectAccountID);
                RefundBillProjectEntity refundBillProjectEntity = new RefundBillProjectEntity();
                refundBillProjectEntity.RefundBillID = refundBillEntity.ID;
                refundBillProjectEntity.ProjectAccountID = refundBillProjectForm.ProjectAccountID;
                refundBillProjectEntity.ProjectID = projectAccountEntity.ProjectID;
                refundBillProjectEntity.Price = refundBillProjectForm.Price;
                refundBillProjectEntity.Quantity = refundBillProjectForm.Quantity;
                if (refundBillProjectForm.PricePreferentialAmount == null || refundBillProjectForm.PricePreferentialAmount.equals(""))
                    refundBillProjectForm.PricePreferentialAmount = BigDecimal.ZERO;

                refundBillProjectEntity.PricePreferentialAmount = refundBillProjectForm.PricePreferentialAmount;
                refundBillProjectEntity.TotalAmount = refundBillProjectForm.TotalAmount;
                refundBillMapper.createRefundBillProject(refundBillProjectEntity);

                for (RefundBillHandlerForm refundBillHandlerForm : refundBillProjectForm.RefundHandler) {
                    RefundBillHandlerProjectEntity refundBillHandlerProjectEntity = new RefundBillHandlerProjectEntity();
                    refundBillHandlerProjectEntity.RefundBillProjectID = refundBillProjectEntity.ID;
                    refundBillHandlerProjectEntity.ProjectSaleHandlerID = refundBillHandlerForm.HandlerID;
                    refundBillHandlerProjectEntity.EmployeeID = refundBillHandlerForm.EmployeeID;
                    refundBillHandlerProjectEntity.Scale = refundBillHandlerForm.Scale.divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP);
                    project.add(refundBillHandlerProjectEntity);
                }
            }
        }
        if (form.GeneralCard != null && form.GeneralCard.size() > 0) {
            for (RefundBillGeneralCardForm refundBillGeneralCardForm : form.GeneralCard) {
                GeneralCardAccountEntity generalCardAccountEntity = generalCardAccountMapper.getGeneralCardAccountByAccountID(refundBillGeneralCardForm.GeneralCardAccountID);
                RefundBillGeneralCardEntity refundBillGeneralCardEntity = new RefundBillGeneralCardEntity();
                refundBillGeneralCardEntity.RefundBillID = refundBillEntity.ID;
                refundBillGeneralCardEntity.GeneralCardAccountID = refundBillGeneralCardForm.GeneralCardAccountID;
                refundBillGeneralCardEntity.GeneralCardID = generalCardAccountEntity.GeneralCardID;
                refundBillGeneralCardEntity.Price = refundBillGeneralCardForm.Price;
                refundBillGeneralCardEntity.Quantity = refundBillGeneralCardForm.Quantity;
                if (refundBillGeneralCardForm.PricePreferentialAmount == null || refundBillGeneralCardForm.PricePreferentialAmount.equals(""))
                    refundBillGeneralCardForm.PricePreferentialAmount = BigDecimal.ZERO;

                refundBillGeneralCardEntity.PricePreferentialAmount = refundBillGeneralCardForm.PricePreferentialAmount;
                refundBillGeneralCardEntity.TotalAmount = refundBillGeneralCardForm.TotalAmount;
                refundBillMapper.createRefundBillGeneralCard(refundBillGeneralCardEntity);

                for (RefundBillHandlerForm refundBillHandlerForm : refundBillGeneralCardForm.RefundHandler) {
                    RefundBillHandlerGeneralCardEntity refundBillHandlerGeneralCardEntity = new RefundBillHandlerGeneralCardEntity();
                    refundBillHandlerGeneralCardEntity.RefundBillGeneralCardID = refundBillGeneralCardEntity.ID;
                    refundBillHandlerGeneralCardEntity.GeneralCardSaleHandlerID = refundBillHandlerForm.HandlerID;
                    refundBillHandlerGeneralCardEntity.EmployeeID = refundBillHandlerForm.EmployeeID;
                    refundBillHandlerGeneralCardEntity.Scale = refundBillHandlerForm.Scale.divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP);
                    generalCard.add(refundBillHandlerGeneralCardEntity);
                }
            }
        }
        if (form.TimeCard != null && form.TimeCard.size() > 0) {
            for (RefundBillTimeCardForm refundBillTimeCardForm : form.TimeCard) {
                TimeCardAccountEntity timeCardAccountEntity = timeCardAccountMapper.getTimeCardAccountByAccountID(refundBillTimeCardForm.TimeCardAccountID);
                RefundBillTimeCardEntity refundBillTimeCardEntity = new RefundBillTimeCardEntity();
                refundBillTimeCardEntity.RefundBillID = refundBillEntity.ID;
                refundBillTimeCardEntity.TimeCardAccountID = refundBillTimeCardForm.TimeCardAccountID;
                refundBillTimeCardEntity.TimeCardID = timeCardAccountEntity.TimeCardID;
                refundBillTimeCardEntity.Price = refundBillTimeCardForm.Price;
                if (refundBillTimeCardForm.PricePreferentialAmount == null || refundBillTimeCardForm.PricePreferentialAmount.equals(""))
                    refundBillTimeCardForm.PricePreferentialAmount = BigDecimal.ZERO;

                refundBillTimeCardEntity.PricePreferentialAmount = refundBillTimeCardForm.PricePreferentialAmount;
                refundBillTimeCardEntity.TotalAmount = refundBillTimeCardForm.TotalAmount;
                refundBillTimeCardEntity.ValidType = timeCardAccountEntity.ValidType;
                refundBillTimeCardEntity.ValidDay = timeCardAccountEntity.ValidDay;
                refundBillTimeCardEntity.ValidDate = timeCardAccountEntity.ValidDate;
                refundBillMapper.createRefundBillTimeCard(refundBillTimeCardEntity);

                for (RefundBillHandlerForm refundBillHandlerForm : refundBillTimeCardForm.RefundHandler) {
                    RefundBillHandlerTimeCardEntity refundBillHandlerTimeCardEntity = new RefundBillHandlerTimeCardEntity();
                    refundBillHandlerTimeCardEntity.RefundBillTimeCardID = refundBillTimeCardEntity.ID;
                    refundBillHandlerTimeCardEntity.TimeCardSaleHandlerID = refundBillHandlerForm.HandlerID;
                    refundBillHandlerTimeCardEntity.EmployeeID = refundBillHandlerForm.EmployeeID;
                    refundBillHandlerTimeCardEntity.Scale = refundBillHandlerForm.Scale.divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP);
                    timeCard.add(refundBillHandlerTimeCardEntity);
                }
            }
        }
        if (form.SavingCard != null && form.SavingCard.size() > 0) {
            for (RefundBillSavingCardForm refundBillSavingCardForm : form.SavingCard) {
                SavingCardAccountEntity savingCardAccountEntity = savingCardAccountMapper.getSavingCardAccountByID(refundBillSavingCardForm.SavingCardAccountID);
                RefundBillSavingCardEntity refundBillSavingCardEntity = new RefundBillSavingCardEntity();
                refundBillSavingCardEntity.RefundBillID = refundBillEntity.ID;
                refundBillSavingCardEntity.SavingCardAccountID = refundBillSavingCardForm.SavingCardAccountID;
                refundBillSavingCardEntity.SavingCardID = savingCardAccountEntity.SavingCardID;
                refundBillSavingCardEntity.Amount = refundBillSavingCardForm.Amount;
                refundBillSavingCardEntity.LargessAmount = refundBillSavingCardForm.LargessAmount;
                refundBillMapper.createRefundBillSavingCard(refundBillSavingCardEntity);

                for (RefundBillHandlerForm refundBillHandlerForm : refundBillSavingCardForm.RefundHandler) {
                    RefundBillHandlerSavingCardEntity refundBillHandlerSavingCardEntity = new RefundBillHandlerSavingCardEntity();
                    refundBillHandlerSavingCardEntity.RefundBillSavingCardID = refundBillSavingCardEntity.ID;
                    refundBillHandlerSavingCardEntity.SavingCardSaleHandlerID = refundBillHandlerForm.HandlerID;
                    refundBillHandlerSavingCardEntity.EmployeeID = refundBillHandlerForm.EmployeeID;
                    refundBillHandlerSavingCardEntity.Scale = refundBillHandlerForm.Scale.divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP);
                    savingCard.add(refundBillHandlerSavingCardEntity);
                }
            }
        }
        if (form.PackageCard != null && form.PackageCard.size() > 0) {
            for (RefundBillPackageCardForm refundBillPackageCardForm : form.PackageCard) {
                PackageCardAccountEntity packageCardAccountEntity = packageCardAccountMapper.getPackageCardAccount(refundBillPackageCardForm.PackageCardAccountID);
                RefundBillPackageCardEntity refundBillPackageCardEntity = new RefundBillPackageCardEntity();
                refundBillPackageCardEntity.RefundBillID = refundBillEntity.ID;
                refundBillPackageCardEntity.PackageCardAccountID = refundBillPackageCardForm.PackageCardAccountID;
                refundBillPackageCardEntity.PackageCardID = packageCardAccountEntity.PackageCardID;
                if (refundBillPackageCardForm.PricePreferentialAmount == null || refundBillPackageCardForm.PricePreferentialAmount.equals(""))
                    refundBillPackageCardForm.PricePreferentialAmount = BigDecimal.ZERO;

                refundBillPackageCardEntity.PricePreferentialAmount = refundBillPackageCardForm.PricePreferentialAmount;
                refundBillPackageCardEntity.Amount = refundBillPackageCardForm.Amount;
                refundBillPackageCardEntity.LargessAmount = refundBillPackageCardForm.LargessAmount;
                refundBillMapper.createRefundBillPackageCard(refundBillPackageCardEntity);

                for (RefundBillHandlerForm refundBillHandlerForm : refundBillPackageCardForm.RefundHandler) {
                    RefundBillHandlerPackageCardEntity refundBillHandlerPackageCardEntity = new RefundBillHandlerPackageCardEntity();
                    refundBillHandlerPackageCardEntity.RefundBillPackageCardID = refundBillPackageCardEntity.ID;
                    refundBillHandlerPackageCardEntity.PackageCardSaleHandlerID = refundBillHandlerForm.HandlerID;
                    refundBillHandlerPackageCardEntity.EmployeeID = refundBillHandlerForm.EmployeeID;
                    refundBillHandlerPackageCardEntity.Scale = refundBillHandlerForm.Scale.divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP);
                    packageCard.add(refundBillHandlerPackageCardEntity);
                }

                if (refundBillPackageCardForm.Product != null && refundBillPackageCardForm.Product.size() > 0) {
                    for (RefundBillProductForm refundBillProductForm : refundBillPackageCardForm.Product) {
                        ProductAccountEntity productAccountEntity = productAccountMapper.getProductAccountByAccountID(refundBillProductForm.ProductAccountID);
                        RefundBillProductEntity refundBillProductEntity = new RefundBillProductEntity();
                        refundBillProductEntity.RefundBillID = refundBillEntity.ID;
                        refundBillProductEntity.ProductAccountID = refundBillProductForm.ProductAccountID;
                        refundBillProductEntity.ProductID = productAccountEntity.ProductID;
                        refundBillProductEntity.Price = refundBillProductForm.Price;
                        refundBillProductEntity.Quantity = refundBillProductForm.Quantity;
                        if (refundBillProductForm.PricePreferentialAmount == null || refundBillProductForm.PricePreferentialAmount.equals(""))
                            refundBillProductForm.PricePreferentialAmount = BigDecimal.ZERO;

                        refundBillProductEntity.PricePreferentialAmount = refundBillProductForm.PricePreferentialAmount;
                        refundBillProductEntity.TotalAmount = refundBillProductForm.TotalAmount;
                        refundBillProductEntity.RefundBillPackageCardID = refundBillPackageCardEntity.ID;
                        refundBillMapper.createRefundBillProduct(refundBillProductEntity);
                    }
                }
                if (refundBillPackageCardForm.Project != null && refundBillPackageCardForm.Project.size() > 0) {
                    for (RefundBillProjectForm refundBillProjectForm : refundBillPackageCardForm.Project) {
                        ProjectAccountEntity projectAccountEntity = projectAccountMapper.getProjectAccountByAccountID(refundBillProjectForm.ProjectAccountID);
                        RefundBillProjectEntity refundBillProjectEntity = new RefundBillProjectEntity();
                        refundBillProjectEntity.RefundBillID = refundBillEntity.ID;
                        refundBillProjectEntity.ProjectAccountID = refundBillProjectForm.ProjectAccountID;
                        refundBillProjectEntity.ProjectID = projectAccountEntity.ProjectID;
                        refundBillProjectEntity.Price = refundBillProjectForm.Price;
                        refundBillProjectEntity.Quantity = refundBillProjectForm.Quantity;
                        if (refundBillProjectForm.PricePreferentialAmount == null || refundBillProjectForm.PricePreferentialAmount.equals(""))
                            refundBillProjectForm.PricePreferentialAmount = BigDecimal.ZERO;

                        refundBillProjectEntity.PricePreferentialAmount = refundBillProjectForm.PricePreferentialAmount;
                        refundBillProjectEntity.TotalAmount = refundBillProjectForm.TotalAmount;
                        refundBillProjectEntity.RefundBillPackageCardID = refundBillPackageCardEntity.ID;
                        refundBillMapper.createRefundBillProject(refundBillProjectEntity);
                    }
                }
                if (refundBillPackageCardForm.GeneralCard != null && refundBillPackageCardForm.GeneralCard.size() > 0) {
                    for (RefundBillGeneralCardForm refundBillGeneralCardForm : refundBillPackageCardForm.GeneralCard) {
                        GeneralCardAccountEntity generalCardAccountEntity = generalCardAccountMapper.getGeneralCardAccountByAccountID(refundBillGeneralCardForm.GeneralCardAccountID);
                        RefundBillGeneralCardEntity refundBillGeneralCardEntity = new RefundBillGeneralCardEntity();
                        refundBillGeneralCardEntity.RefundBillID = refundBillEntity.ID;
                        refundBillGeneralCardEntity.GeneralCardAccountID = refundBillGeneralCardForm.GeneralCardAccountID;
                        refundBillGeneralCardEntity.GeneralCardID = generalCardAccountEntity.GeneralCardID;
                        refundBillGeneralCardEntity.Price = refundBillGeneralCardForm.Price;
                        refundBillGeneralCardEntity.Quantity = refundBillGeneralCardForm.Quantity;
                        if (refundBillGeneralCardForm.PricePreferentialAmount == null || refundBillGeneralCardForm.PricePreferentialAmount.equals(""))
                            refundBillGeneralCardForm.PricePreferentialAmount = BigDecimal.ZERO;

                        refundBillGeneralCardEntity.PricePreferentialAmount = refundBillGeneralCardForm.PricePreferentialAmount;
                        refundBillGeneralCardEntity.TotalAmount = refundBillGeneralCardForm.TotalAmount;
                        refundBillGeneralCardEntity.RefundBillPackageCardID = refundBillPackageCardEntity.ID;
                        refundBillMapper.createRefundBillGeneralCard(refundBillGeneralCardEntity);
                    }
                }
                if (refundBillPackageCardForm.TimeCard != null && refundBillPackageCardForm.TimeCard.size() > 0) {
                    for (RefundBillTimeCardForm refundBillTimeCardForm : refundBillPackageCardForm.TimeCard) {
                        TimeCardAccountEntity timeCardAccountEntity = timeCardAccountMapper.getTimeCardAccountByAccountID(refundBillTimeCardForm.TimeCardAccountID);
                        RefundBillTimeCardEntity refundBillTimeCardEntity = new RefundBillTimeCardEntity();
                        refundBillTimeCardEntity.RefundBillID = refundBillEntity.ID;
                        refundBillTimeCardEntity.TimeCardAccountID = refundBillTimeCardForm.TimeCardAccountID;
                        refundBillTimeCardEntity.TimeCardID = timeCardAccountEntity.TimeCardID;
                        refundBillTimeCardEntity.Price = refundBillTimeCardForm.Price;
                        if (refundBillTimeCardForm.PricePreferentialAmount == null || refundBillTimeCardForm.PricePreferentialAmount.equals(""))
                            refundBillTimeCardForm.PricePreferentialAmount = BigDecimal.ZERO;

                        refundBillTimeCardEntity.PricePreferentialAmount = refundBillTimeCardForm.PricePreferentialAmount;
                        refundBillTimeCardEntity.TotalAmount = refundBillTimeCardForm.TotalAmount;
                        refundBillTimeCardEntity.RefundBillPackageCardID = refundBillPackageCardEntity.ID;
                        refundBillTimeCardEntity.ValidType = timeCardAccountEntity.ValidType;
                        refundBillTimeCardEntity.ValidDay = timeCardAccountEntity.ValidDay;
                        refundBillTimeCardEntity.ValidDate = timeCardAccountEntity.ValidDate;
                        refundBillMapper.createRefundBillTimeCard(refundBillTimeCardEntity);
                    }
                }
                if (refundBillPackageCardForm.SavingCard != null && refundBillPackageCardForm.SavingCard.size() > 0) {
                    for (RefundBillSavingCardForm refundBillSavingCardForm : refundBillPackageCardForm.SavingCard) {
                        SavingCardAccountEntity savingCardAccountEntity = savingCardAccountMapper.getSavingCardAccountByID(refundBillSavingCardForm.SavingCardAccountID);
                        RefundBillSavingCardEntity refundBillSavingCardEntity = new RefundBillSavingCardEntity();
                        refundBillSavingCardEntity.RefundBillID = refundBillEntity.ID;
                        refundBillSavingCardEntity.SavingCardAccountID = refundBillSavingCardForm.SavingCardAccountID;
                        refundBillSavingCardEntity.SavingCardID = savingCardAccountEntity.SavingCardID;
                        refundBillSavingCardEntity.Amount = refundBillSavingCardForm.Amount;
                        refundBillSavingCardEntity.LargessAmount = refundBillSavingCardForm.LargessAmount;
                        refundBillSavingCardEntity.RefundBillPackageCardID = refundBillPackageCardEntity.ID;
                        refundBillMapper.createRefundBillSavingCard(refundBillSavingCardEntity);
                    }
                }
            }
        }
        if (form.PayMethod != null && form.PayMethod.size() > 0) {
            for (RefundBillPayMethodForm refundBillPayMethodForm : form.PayMethod) {
                RefundBillPayEntity refundBillPayEntity = new RefundBillPayEntity();
                refundBillPayEntity.RefundBillID = refundBillEntity.ID;
                refundBillPayEntity.PayMethodID = refundBillPayMethodForm.PayMethodID;
                refundBillPayEntity.Amount = refundBillPayMethodForm.Amount.setScale(2, BigDecimal.ROUND_HALF_UP);
                payMethod.add(refundBillPayEntity);
            }
        }
        if (form.SavingCardDeduction != null && form.SavingCardDeduction.size() > 0) {
            for (RefundBillSavingCardDeductionForm refundBillSavingCardDeductionForm : form.SavingCardDeduction) {
                RefundBillPaySavingCardDeductionEntity refundBillPaySavingCardDeductionEntity = new RefundBillPaySavingCardDeductionEntity();
                refundBillPaySavingCardDeductionEntity.RefundBillID = refundBillEntity.ID;
                refundBillPaySavingCardDeductionEntity.SavingCardAccountID = refundBillSavingCardDeductionForm.SavingCardAccountID;
                refundBillPaySavingCardDeductionEntity.Amount = refundBillSavingCardDeductionForm.DeductionAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                refundBillPaySavingCardDeductionEntity.LargessAmount = refundBillSavingCardDeductionForm.LargessDeductionAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                refundBillPaySavingCardDeductionEntity.TotalAmount = refundBillSavingCardDeductionForm.DeductionAmount.add(refundBillSavingCardDeductionForm.LargessDeductionAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
                savingCardDeduction.add(refundBillPaySavingCardDeductionEntity);
            }
        }


        if (product.size() > 0)
            refundBillHandlerMapper.createRefundBillHandlerProduct(product);
        if (project.size() > 0)
            refundBillHandlerMapper.createRefundBillHandlerProject(project);
        if (generalCard.size() > 0)
            refundBillHandlerMapper.createRefundBillHandlerGeneralCard(generalCard);
        if (timeCard.size() > 0)
            refundBillHandlerMapper.createRefundBillHandlerTimeCard(timeCard);
        if (savingCard.size() > 0)
            refundBillHandlerMapper.createRefundBillHandlerSavingCard(savingCard);
        if (packageCard.size() > 0)
            refundBillHandlerMapper.createRefundBillHandlerPackageCard(packageCard);

        if (payMethod.size() > 0)
            refundBillMapper.createRefundBillPay(payMethod);
        if (savingCardDeduction.size() > 0)
            refundBillMapper.createRefundBillPaySavingCardDeduction(savingCardDeduction);


        TranserBillIDOutputForm transerBillIDOutputForm = new TranserBillIDOutputForm();
        transerBillIDOutputForm.BillID = refundBillEntity.ID;
        return BaseOutputForm.success(transerBillIDOutputForm);
    }

    public BaseOutput refundBillList(RefundBillQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        Integer entityID = SystemUserUtil.getSystemEntityID();
        return BasePageInfo.success(refundBillMapper.refundBillList(entityID, form.ID, form.Name, form.StartDate, form.EndDate, form.ApprovalStatus, true));
    }

    public BaseOutput refundBillInfo(RefundBillInfoQueryForm form) {

        RefundBillInfoOutputForm refundBillInfoOutputForm = refundBillMapper.refundBillInfo(form);
        refundBillInfoOutputForm.PayMethod = refundBillMapper.refundBillInfoPayMethod(form.ID);
        refundBillInfoOutputForm.SavingCardDeduction = refundBillMapper.refundBillInfoSavingCardDeduction(form.ID);
        refundBillInfoOutputForm.Product = refundBillMapper.refundBillInfoProduct(form.ID);
        refundBillInfoOutputForm.Project = refundBillMapper.refundBillInfoProject(form.ID);
        refundBillInfoOutputForm.GeneralCard = refundBillMapper.refundBillInfoGeneralCard(form.ID);
        refundBillInfoOutputForm.TimeCard = refundBillMapper.refundBillInfoTimeCard(form.ID);
        refundBillInfoOutputForm.SavingCard = refundBillMapper.refundBillInfoSavingCard(form.ID);
        List<RefundBillInfoPackageCardOutputForm> packageCard = refundBillMapper.refundBillInfoPackageCard(form.ID);
        List<RefundBillInfoProductOutputForm> product = refundBillMapper.refundBillPackageCardProductInfo(form.ID);
        List<RefundBillInfoProjectOutputForm> project = refundBillMapper.refundBillPackageCardProjectInfo(form.ID);
        List<RefundBillInfoGeneralCardOutputForm> generalCard = refundBillMapper.refundBillPackageCardGeneralCardInfo(form.ID);
        List<RefundBillInfoTimeCardOutputForm> timeCard = refundBillMapper.refundBillPackageCardTimeCardInfo(form.ID);
        List<RefundBillInfoSavingCardOutputForm> savingCard = refundBillMapper.refundBillPackageCardSavingCardInfo(form.ID);
        for (RefundBillInfoPackageCardOutputForm packageCardForm : packageCard) {
            packageCardForm.Product = product.stream().filter(packageCardProductListOutputForm -> packageCardProductListOutputForm.RefundBillPackageCardID.intValue() == packageCardForm.ID.intValue()).collect(Collectors.toList());
            packageCardForm.Project = project.stream().filter(packageCardProjectListOutputForm -> packageCardProjectListOutputForm.RefundBillPackageCardID.intValue() == packageCardForm.ID.intValue()).collect(Collectors.toList());
            packageCardForm.GeneralCard = generalCard.stream().filter(packageCardGeneralCardListOutputForm -> packageCardGeneralCardListOutputForm.RefundBillPackageCardID.intValue() == packageCardForm.ID.intValue()).collect(Collectors.toList());
            packageCardForm.TimeCard = timeCard.stream().filter(packageCardTimeCardListOutputForm -> packageCardTimeCardListOutputForm.RefundBillPackageCardID.intValue() == packageCardForm.ID.intValue()).collect(Collectors.toList());
            packageCardForm.SavingCard = savingCard.stream().filter(packageCardSavingCardListOutputForm -> packageCardSavingCardListOutputForm.RefundBillPackageCardID.intValue() == packageCardForm.ID.intValue()).collect(Collectors.toList());
        }
        refundBillInfoOutputForm.PackageCard = packageCard;
        return BaseOutputForm.success(refundBillInfoOutputForm);
    }

    public BaseOutput approvalList(RefundBillQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(refundBillMapper.refundApprovalBillList(SystemUserUtil.getSystemUserID(), form.EntityID, form.RefundBillID, form.Name, form.StartDate, form.EndDate, form.ApprovalStatus, false));
    }

    @Transactional
    public BaseOutput revoke(RefundBillInfoQueryForm form) {
        refundBillMapper.updateRefundBillStatus(form.ID, "50");
        RefundBillProcessEntity refundBillProcessEntity = new RefundBillProcessEntity();
        refundBillProcessEntity.RefundBillID = form.ID;
        refundBillProcessEntity.ApprovalBy = SystemUserUtil.getSystemUserID();
        refundBillProcessEntity.ApprovalOn = new Date();
        refundBillProcessEntity.ActionType = "50";
        refundBillMapper.createRefundBillProcess(refundBillProcessEntity);
        return BaseOutput.success();
    }


    @Transactional
    public String approvalSaleBill(RefundBillApprovalAddForm form, String channel) {
        RefundSaleBillEntity refundBillEntity = refundBillMapper.getRefundBill(form.ID);
        Boolean replacementOrderRestriction = sealingAccountService.getReplacementOrderRestriction(DateTimeUtil.toDate(refundBillEntity.BillDate));
        if(replacementOrderRestriction){
            throw new RuntimeException("所审批的订单时间处于关账时间内，请选择其他时间");
        }
        refundBillMapper.updateRefundBillStatus(form.ID, form.ApprovalStatus);

        RefundBillProcessEntity refundBillProcessEntity = new RefundBillProcessEntity();
        refundBillProcessEntity.RefundBillID = form.ID;
        refundBillProcessEntity.ApprovalBy = SystemUserUtil.getSystemUserID();
        refundBillProcessEntity.ApprovalOn = new Date();
        refundBillProcessEntity.ActionType = form.ApprovalStatus;
        refundBillProcessEntity.ApprovalRemark = form.ApprovalRemark;
        refundBillMapper.createRefundBillProcess(refundBillProcessEntity);
        SaleBillEntity saleBillEntity = new SaleBillEntity();
        if (form.ApprovalStatus.equals("30")) {
            //1:判断会员是否和申请门店关联，没有的话进行关联
            Integer count = customerMapper.getCustomerEntity(refundBillEntity.CustomerID);
            Boolean isBelongEntity = count == 0 ? true : false;
            customerService.insertCustomerEntity(refundBillEntity.CustomerID, refundBillEntity.EntityID, isBelongEntity, isBelongEntity);

            //判断会员是否有渠道
            CustomerEntity cust = customerMapper.getCustomer(refundBillEntity.CustomerID);
            Boolean isCalculateChannelPerformance = cust.ChannelID == null ? false : true;
            List<ChannelParentOutputForm> parents = new ArrayList<>();
            List<ChannelEmployeePerformanceRateOutputForm> developer = new ArrayList<>();
            List<ChannelEmployeePerformanceRateOutputForm> consultant = new ArrayList<>();
            if (isCalculateChannelPerformance) {
                parents = channelMapper.getChannelParents(cust.ChannelID);
                developer = channelMapper.channelDeveloper(cust.ChannelID);
                consultant = channelMapper.channelConsultant(cust.ChannelID);
            }

            //2.创建主订单
            saleBillEntity = new SaleBillEntity();
            saleBillEntity.ID = form.ID;
            saleBillEntity.EntityID = refundBillEntity.EntityID;
            saleBillEntity.CustomerID = refundBillEntity.CustomerID;
            saleBillEntity.BillDate = refundBillEntity.BillDate;
            saleBillEntity.BillType = BillCode.BillType_20.getID();
            saleBillEntity.BillStatus = BillCode.BillStatus_20.getID();
            saleBillEntity.EmployeeID = refundBillEntity.CreatedBy;
            saleBillEntity.Amount = refundBillEntity.PayAmount.add(refundBillEntity.CardDeductionAmount).add(refundBillEntity.CardDeductionLargessAmount);
            saleBillEntity.PayAmount = refundBillEntity.PayAmount;
            saleBillEntity.TotalAmount = refundBillEntity.TotalAmount;
            saleBillEntity.ArrearAmount = BigDecimal.ZERO;
            saleBillEntity.CardDeductionAmount = refundBillEntity.CardDeductionAmount.add(refundBillEntity.CardDeductionLargessAmount);
            saleBillEntity.PricePreferentialAmount = refundBillEntity.PricePreferentialAmount;
            saleBillEntity.MemberPreferentialAmount = BigDecimal.ZERO;
            saleBillEntity.CardPreferentialAmount = BigDecimal.ZERO;
            saleBillEntity.Channel = channel;
            saleBillEntity.Remark = refundBillEntity.RefundRemark;
            saleBillEntity.IsFastBill = false;
            saleBillMapper.createSaleBill(saleBillEntity);

            List<RefundBillPayEntity> payMethod = refundBillMapper.getRefundBillPayMethod(form.ID);
            List<RefundBillPaySavingCardDeductionEntity> savingCardDeduction = refundBillMapper.getRefundBillSavingCardDeduction(form.ID);
            List<RefundBillProductEntity> refundProduct = refundBillMapper.getRefundBillProduct(form.ID);
            List<RefundBillProjectEntity> refundProject = refundBillMapper.getRefundBillProject(form.ID);
            List<RefundBillGeneralCardEntity> refundGeneralCard = refundBillMapper.getRefundBillGeneralCard(form.ID);
            List<RefundBillTimeCardEntity> refundTimeCard = refundBillMapper.getRefundBillTimeCard(form.ID);
            List<RefundBillSavingCardEntity> refundSavingCard = refundBillMapper.getRefundBillSavingCard(form.ID);
            List<RefundBillPackageCardEntity> refundPackageCard = refundBillMapper.getRefundBillPackageCard(form.ID);

            //添加现金支付方式
            List<SaleBillPayEntity> saleBillPayEntities = new ArrayList<>();
            for (RefundBillPayEntity refundBillPayEntity : payMethod) {
                if (refundBillPayEntity.Amount.compareTo(BigDecimal.ZERO) > 0) {
                    SaleBillPayEntity saleBillPayEntity = new SaleBillPayEntity();
                    saleBillPayEntity.PayMethodID = refundBillPayEntity.PayMethodID;
                    saleBillPayEntity.Amount = refundBillPayEntity.Amount.setScale(2, BigDecimal.ROUND_HALF_UP);
                    saleBillPayEntity.SaleBillID = saleBillEntity.ID;
                    saleBillPayMapper.createSaleBillPay(saleBillPayEntity);
                    saleBillPayEntities.add(saleBillPayEntity);
                }
            }
            //订单抵扣明细
            List<SaleBillPaySavingCardDeductionEntity> saleBillPaySavingCardDeductionEntities = new ArrayList<>();
            for (RefundBillPaySavingCardDeductionEntity refundBillPaySavingCardDeductionEntity : savingCardDeduction) {
                SaleBillPaySavingCardDeductionEntity saleBillPaySavingCardDeductionEntity = new SaleBillPaySavingCardDeductionEntity();
                saleBillPaySavingCardDeductionEntity.Amount = refundBillPaySavingCardDeductionEntity.Amount;
                saleBillPaySavingCardDeductionEntity.LargessAmount = refundBillPaySavingCardDeductionEntity.LargessAmount;
                saleBillPaySavingCardDeductionEntity.TotalAmount = refundBillPaySavingCardDeductionEntity.TotalAmount;
                saleBillPaySavingCardDeductionEntity.PreferentialAmount = BigDecimal.ZERO;
                saleBillPaySavingCardDeductionEntity.LargessPreferentialAmount = BigDecimal.ZERO;
                saleBillPaySavingCardDeductionEntity.SaleBillID = saleBillEntity.ID;
                saleBillPaySavingCardDeductionEntity.SavingCardAccountID = refundBillPaySavingCardDeductionEntity.SavingCardAccountID;
                saleBillPaySavingCardDeductionEntities.add(saleBillPaySavingCardDeductionEntity);
            }
            if (saleBillPaySavingCardDeductionEntities != null && saleBillPaySavingCardDeductionEntities.size() > 0)
                saleBillPayMapper.createSaleBillPaySavingCardDeduction(saleBillPaySavingCardDeductionEntities);

            List<GoodAssignAmountEntity> goodsAssignAmountEntities = new ArrayList<>();//现金
            List<GoodAssignAmountEntity> savingCardAssignAmountEntities = new ArrayList<>();//储值卡现金支付集合

            //保存储值卡抵扣
            List<SaleBillPaySavingCardDeductionGoodsEntity> productSavingCardDeduction = new ArrayList<>();
            List<SaleBillPaySavingCardDeductionGoodsEntity> projectSavingCardDeduction = new ArrayList<>();
            List<SaleBillPaySavingCardDeductionGoodsEntity> generalCardSavingCardDeduction = new ArrayList<>();
            List<SaleBillPaySavingCardDeductionGoodsEntity> timeCardSavingCardDeduction = new ArrayList<>();
            List<SaleBillPaySavingCardDeductionGoodsExtendEntity> packageCardSavingCardDeduction = new ArrayList<>();
            //保存套餐卡明细储值卡抵扣
            List<SaleBillPaySavingCardDeductionGoodsExtendEntity> packageCardSavingCardDeductionProduct = new ArrayList<>();
            List<SaleBillPaySavingCardDeductionGoodsExtendEntity> packageCardSavingCardDeductionProject = new ArrayList<>();
            List<SaleBillPaySavingCardDeductionGoodsExtendEntity> packageCardSavingCardDeductionGeneralCard = new ArrayList<>();
            List<SaleBillPaySavingCardDeductionGoodsExtendEntity> packageCardSavingCardDeductionTimeCard = new ArrayList<>();

            List<SaleEntityPerformanceGoodsEntity> saleEntityPerformanceGoodsEntities = new ArrayList<>();//非套餐卡门店业绩
            List<SaleEntityPerformancePackageCardGoodsEntity> saleEntityPerformancePackageCardGoodsEntities = new ArrayList<>();//套餐卡门店业绩


            if (refundProduct != null && refundProduct.size() > 0) {
                for (RefundBillProductEntity refundBillProductEntity : refundProduct) {
                    ProductAccountEntity productAccountEntity = productAccountMapper.getProductAccountByAccountID(refundBillProductEntity.ProductAccountID);
                    if (!productAccountEntity.Active) {
                        throw new RuntimeException("购买当前产品的销售订单已被取消，请撤销或驳回当前退款单据");
                    }
                    if (productAccountEntity.Balance.intValue() < refundBillProductEntity.Quantity.intValue()) {
                        throw new RuntimeException("产品剩余次数不足");
                    }
                    SaleBillProductEntity saleBillProductEntity = new SaleBillProductEntity();
                    saleBillProductEntity.BillID = saleBillEntity.ID;
                    saleBillProductEntity.ProductID = refundBillProductEntity.ProductID;
                    saleBillProductEntity.Price = refundBillProductEntity.Price;
                    saleBillProductEntity.Quantity = refundBillProductEntity.Quantity;
                    saleBillProductEntity.TotalAmount = refundBillProductEntity.TotalAmount;
                    saleBillProductEntity.ArrearAmount = BigDecimal.ZERO;
                    saleBillProductEntity.IsLargess = productAccountEntity.IsLargess;
                    saleBillProductEntity.PricePreferentialAmount = refundBillProductEntity.PricePreferentialAmount;
                    saleBillProductEntity.MemberPreferentialAmount = BigDecimal.ZERO;
                    saleBillProductEntity.CardPreferentialAmount = BigDecimal.ZERO;
                    saleBillProductEntity.ProductAccountID = refundBillProductEntity.ProductAccountID;
                    saleBillMapper.createSaleBillProduct(saleBillProductEntity);

                    List<RefundBillHandlerProductEntity> refundBillHandlerProduct = refundBillHandlerMapper.getRefundBillHandlerProduct(refundBillProductEntity.ID);
                    //添加经手人
                    List<SaleBillHandlerProductEntity> saleBillHandlerProductEntities = new ArrayList<>();
                    for (RefundBillHandlerProductEntity handlerProductEntity : refundBillHandlerProduct) {
                        SaleBillHandlerProductEntity entity = new SaleBillHandlerProductEntity();
                        entity.SaleBillProductID = saleBillProductEntity.ID;
                        entity.ProductSaleHandlerID = handlerProductEntity.ProductSaleHandlerID;
                        entity.EmployeeID = handlerProductEntity.EmployeeID;
                        entity.Scale = handlerProductEntity.Scale;
                        entity.IsCalculatePassengerFlow = true;
                        saleBillHandlerProductEntities.add(entity);
                    }
                    if (saleBillHandlerProductEntities.size() > 0) {
                        saleBillHandlerMapper.createSaleBillHandlerProduct(saleBillHandlerProductEntities);
                    }

                    RefundRateForm refundRateForm = new RefundRateForm();
                    BigDecimal productPricePreferentialAmountTotal = refundBillMapper.getProductPricePreferentialAmountTotal(productAccountEntity.ID);//优惠金额合计
                    if (refundBillProductEntity.PricePreferentialAmount.compareTo(BigDecimal.ZERO) == 0 && productPricePreferentialAmountTotal.compareTo(BigDecimal.ZERO) == 0) {
                        if (productAccountEntity.Balance.equals(refundBillProductEntity.Quantity)) {
                            refundRateForm.payAmount = productAccountEntity.PayPerformanceBalanceAmount;
                            refundRateForm.savingCardDeductionAmount = productAccountEntity.CardPerformanceBalanceAmount;
                            refundRateForm.savingCardDeductionLargessAmount = productAccountEntity.CardLargessPerformanceBalanceAmount;
                        } else {
                            refundRateForm = getProductAmount(productAccountEntity, refundBillProductEntity.TotalAmount);
                        }
                    } else {
                        refundRateForm = getProductAmount(productAccountEntity, refundBillProductEntity.TotalAmount);
                    }

                    BigDecimal payAmount = refundBillProductEntity.TotalAmount.subtract(refundRateForm.savingCardDeductionAmount).subtract(refundRateForm.savingCardDeductionLargessAmount);
                    if (payAmount.compareTo(BigDecimal.ZERO) > 0) {
                        GoodAssignAmountEntity entity = new GoodAssignAmountEntity();
                        entity.SaleBillGoodsID = saleBillProductEntity.ID;
                        entity.GoodsID = saleBillProductEntity.ProductID;
                        entity.Type = 1;
                        entity.UnassignedAmount = payAmount;//现金金额 去掉卡抵扣
                        goodsAssignAmountEntities.add(entity);
                    }

                    BigDecimal savingCardDeductionAmount = refundRateForm.savingCardDeductionAmount;
                    BigDecimal savingCardDeductionLargessAmount = refundRateForm.savingCardDeductionLargessAmount;
                    List<RefundBillPaySavingCardGoodsForm> saleBillPaySavingCardProduct = saleBillPayMapper.getSaleBillPaySavingCardProduct(productAccountEntity.SaleBillProductID,productAccountEntity.ID);
                    BigDecimal amount = BigDecimal.ZERO;
                    BigDecimal largessAmount = BigDecimal.ZERO;
                    for (int i = 0; i < saleBillPaySavingCardProduct.size(); i++) {
                        RefundBillPaySavingCardGoodsForm newRefundBillPaySavingCardGoodsForm = new RefundBillPaySavingCardGoodsForm();
                        newRefundBillPaySavingCardGoodsForm.SavingCardAccountID = saleBillPaySavingCardProduct.get(i).SavingCardAccountID;
                        newRefundBillPaySavingCardGoodsForm.Name = saleBillPaySavingCardProduct.get(i).Name;
                        if (i == saleBillPaySavingCardProduct.size() - 1) {
                            newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.subtract(amount);
                            newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.subtract(largessAmount);
                        } else {
                            newRefundBillPaySavingCardGoodsForm.Amount = BigDecimal.ZERO;
                            newRefundBillPaySavingCardGoodsForm.LargessAmount = BigDecimal.ZERO;
                            if (productAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
                                newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.multiply(saleBillPaySavingCardProduct.get(i).Amount).divide(productAccountEntity.SavingCardDeductionAmount, 2, BigDecimal.ROUND_HALF_UP);
                                amount = amount.add(newRefundBillPaySavingCardGoodsForm.Amount);
                            }
                            if (productAccountEntity.SavingCardDeductionLargessAmount.compareTo(BigDecimal.ZERO) > 0) {
                                newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.multiply(saleBillPaySavingCardProduct.get(i).LargessAmount).divide(productAccountEntity.SavingCardDeductionLargessAmount, 2, BigDecimal.ROUND_HALF_UP);
                                largessAmount = largessAmount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);
                            }
                        }
                        newRefundBillPaySavingCardGoodsForm.TotalAmount = newRefundBillPaySavingCardGoodsForm.Amount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);

                        SaleBillPaySavingCardDeductionGoodsEntity saleBillPaySavingCardDeductionGoodsEntity = new SaleBillPaySavingCardDeductionGoodsEntity();
                        saleBillPaySavingCardDeductionGoodsEntity.SaleBillGoodsID = saleBillProductEntity.ID;
                        saleBillPaySavingCardDeductionGoodsEntity.SavingCardAccountID = newRefundBillPaySavingCardGoodsForm.SavingCardAccountID;
                        saleBillPaySavingCardDeductionGoodsEntity.TotalAmount = newRefundBillPaySavingCardGoodsForm.TotalAmount;
                        saleBillPaySavingCardDeductionGoodsEntity.Amount = newRefundBillPaySavingCardGoodsForm.Amount;
                        saleBillPaySavingCardDeductionGoodsEntity.LargessAmount = newRefundBillPaySavingCardGoodsForm.LargessAmount;
                        saleBillPaySavingCardDeductionGoodsEntity.PreferentialAmount = BigDecimal.ZERO;
                        saleBillPaySavingCardDeductionGoodsEntity.LargessPreferentialAmount = BigDecimal.ZERO;
                        productSavingCardDeduction.add(saleBillPaySavingCardDeductionGoodsEntity);

                    }

                    SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity = new SaleEntityPerformanceGoodsEntity();
                    saleEntityPerformanceGoodsEntity.Type = 1;
                    saleEntityPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;//订单编号
                    saleEntityPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;//门店ID
                    saleEntityPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;//开单时间
                    saleEntityPerformanceGoodsEntity.SaleBillGoodsID = saleBillProductEntity.ID;//商品明细ID
                    saleEntityPerformanceGoodsEntity.GoodsID = saleBillProductEntity.ProductID;//商品ID
                    saleEntityPerformanceGoodsEntity.Scale = new BigDecimal(100);
                    List<SaleBillHandlerForm> saleBillHandlerFormList = new ArrayList<>();
                    for (RefundBillHandlerProductEntity refundBillHandlerProductEntity : refundBillHandlerProduct) {
                        SaleBillHandlerForm saleBillHandlerForm = new SaleBillHandlerForm();
                        saleBillHandlerForm.EmployeeID = refundBillHandlerProductEntity.EmployeeID;
                        saleBillHandlerForm.SaleHandlerID = refundBillHandlerProductEntity.ProductSaleHandlerID;
                        saleBillHandlerForm.Scale = refundBillHandlerProductEntity.Scale.multiply(new BigDecimal(100).setScale(2, BigDecimal.ROUND_HALF_UP));
                        saleBillHandlerFormList.add(saleBillHandlerForm);
                    }
                    saleEntityPerformanceGoodsEntity.SaleBillHandlerEntityList = saleBillHandlerFormList;
                    saleEntityPerformanceGoodsEntity.Quantity = saleBillProductEntity.Quantity;
                    saleEntityPerformanceGoodsEntity.AccountQuantity = productAccountEntity.Quantity;
                    saleEntityPerformanceGoodsEntity.Price = saleBillProductEntity.Price;
                    saleEntityPerformanceGoodsEntity.TotalAmount = saleBillProductEntity.TotalAmount;
                    saleEntityPerformanceGoodsEntity.AccountTotalAmount = productAccountEntity.TotalAmount;
                    saleEntityPerformanceGoodsEntity.ArrearAmount = saleBillProductEntity.ArrearAmount;
                    saleEntityPerformanceGoodsEntity.IsLargess = saleBillProductEntity.IsLargess;
                    saleEntityPerformanceGoodsEntities.add(saleEntityPerformanceGoodsEntity);
                    //更新账户

                    if (refundBillProductEntity.PricePreferentialAmount.compareTo(BigDecimal.ZERO) == 1) {
                        refundRateForm = getProductAmount(productAccountEntity, refundBillProductEntity.TotalAmount.add(refundBillProductEntity.PricePreferentialAmount));
                    }
                    productAccountEntity.Balance = productAccountEntity.Balance - refundBillProductEntity.Quantity;
                    productAccountEntity.PayPerformanceBalanceAmount = productAccountEntity.PayPerformanceBalanceAmount.subtract(refundRateForm.payAmount);
                    productAccountEntity.CardPerformanceBalanceAmount = productAccountEntity.CardPerformanceBalanceAmount.subtract(refundRateForm.savingCardDeductionAmount);
                    productAccountEntity.CardLargessPerformanceBalanceAmount = productAccountEntity.CardLargessPerformanceBalanceAmount.subtract(refundRateForm.savingCardDeductionLargessAmount);
                    productAccountEntity.LargessPerformanceBalanceAmount = productAccountEntity.LargessPerformanceBalanceAmount.subtract(productAccountEntity.IsLargess ? refundBillProductEntity.Price.multiply(new BigDecimal(refundBillProductEntity.Quantity)) : BigDecimal.ZERO);
                    if (productAccountEntity.Balance.equals(0) && productAccountEntity.ArrearAmount.compareTo(BigDecimal.ZERO) > 0) {
                        productAccountEntity.ArrearAmount = BigDecimal.ZERO;
                    }
                    productAccountMapper.updateProductAccountBalance(productAccountEntity);
                }
            }
            if (refundProject != null && refundProject.size() > 0) {
                for (RefundBillProjectEntity refundBillProjectEntity : refundProject) {
                    ProjectAccountEntity projectAccountEntity = projectAccountMapper.getProjectAccountByAccountID(refundBillProjectEntity.ProjectAccountID);
                    SaleBillProjectEntity saleBillProjectEntity = new SaleBillProjectEntity();
                    if (!projectAccountEntity.Active) {
                        throw new RuntimeException("购买当前项目的销售订单已被取消，请撤销或驳回当前退款单据");
                    }
                    if (projectAccountEntity.Balance.intValue() < refundBillProjectEntity.Quantity.intValue()) {
                        throw new RuntimeException("项目剩余次数不足");
                    }
                    saleBillProjectEntity.BillID = saleBillEntity.ID;
                    saleBillProjectEntity.ProjectID = refundBillProjectEntity.ProjectID;
                    saleBillProjectEntity.Price = refundBillProjectEntity.Price;
                    saleBillProjectEntity.Quantity = refundBillProjectEntity.Quantity;
                    saleBillProjectEntity.TotalAmount = refundBillProjectEntity.TotalAmount;
                    saleBillProjectEntity.ArrearAmount = BigDecimal.ZERO;
                    saleBillProjectEntity.IsLargess = projectAccountEntity.IsLargess;
                    saleBillProjectEntity.PricePreferentialAmount = refundBillProjectEntity.PricePreferentialAmount;
                    saleBillProjectEntity.MemberPreferentialAmount = BigDecimal.ZERO;
                    saleBillProjectEntity.CardPreferentialAmount = BigDecimal.ZERO;
                    saleBillProjectEntity.ProjectAccountID = refundBillProjectEntity.ProjectAccountID;
                    saleBillMapper.createSaleBillProject(saleBillProjectEntity);

                    List<RefundBillHandlerProjectEntity> refundBillHandlerProject = refundBillHandlerMapper.getRefundBillHandlerProject(refundBillProjectEntity.ID);
                    //添加经手人
                    List<SaleBillHandlerProjectEntity> saleBillHandlerProjectEntities = new ArrayList<>();
                    for (RefundBillHandlerProjectEntity handlerProductEntity : refundBillHandlerProject) {
                        SaleBillHandlerProjectEntity entity = new SaleBillHandlerProjectEntity();
                        entity.SaleBillProjectID = saleBillProjectEntity.ID;
                        entity.ProjectSaleHandlerID = handlerProductEntity.ProjectSaleHandlerID;
                        entity.EmployeeID = handlerProductEntity.EmployeeID;
                        entity.Scale = handlerProductEntity.Scale;
                        entity.IsCalculatePassengerFlow = true;
                        saleBillHandlerProjectEntities.add(entity);
                    }
                    if (saleBillHandlerProjectEntities.size() > 0) {
                        saleBillHandlerMapper.createSaleBillHandlerProject(saleBillHandlerProjectEntities);
                    }

                    RefundRateForm refundRateForm = new RefundRateForm();

                    BigDecimal projectPricePreferentialAmountTotal = refundBillMapper.getProjectPricePreferentialAmountTotal(projectAccountEntity.ID);//优惠金额合计
                    if (refundBillProjectEntity.PricePreferentialAmount.compareTo(BigDecimal.ZERO) == 0 && projectPricePreferentialAmountTotal.compareTo(BigDecimal.ZERO) == 0) {
                        if (projectAccountEntity.Balance.equals(refundBillProjectEntity.Quantity)) {
                            refundRateForm.payAmount = projectAccountEntity.PayPerformanceBalanceAmount;
                            refundRateForm.savingCardDeductionAmount = projectAccountEntity.CardPerformanceBalanceAmount;
                            refundRateForm.savingCardDeductionLargessAmount = projectAccountEntity.CardLargessPerformanceBalanceAmount;
                        } else {
                            refundRateForm = getProjectAmount(projectAccountEntity, refundBillProjectEntity.TotalAmount);
                        }
                    } else {
                        refundRateForm = getProjectAmount(projectAccountEntity, refundBillProjectEntity.TotalAmount);
                    }

                    BigDecimal payAmount = refundBillProjectEntity.TotalAmount.subtract(refundRateForm.savingCardDeductionAmount).subtract(refundRateForm.savingCardDeductionLargessAmount);
                    if (payAmount.compareTo(BigDecimal.ZERO) > 0) {
                        GoodAssignAmountEntity entity = new GoodAssignAmountEntity();
                        entity.SaleBillGoodsID = saleBillProjectEntity.ID;
                        entity.GoodsID = saleBillProjectEntity.ProjectID;
                        entity.Type = 2;
                        entity.UnassignedAmount = payAmount;
                        goodsAssignAmountEntities.add(entity);
                    }

                    BigDecimal savingCardDeductionAmount = refundRateForm.savingCardDeductionAmount;
                    BigDecimal savingCardDeductionLargessAmount = refundRateForm.savingCardDeductionLargessAmount;
                    List<RefundBillPaySavingCardGoodsForm> saleBillPaySavingCardProject = saleBillPayMapper.getSaleBillPaySavingCardProject(projectAccountEntity.SaleBillProjectID,projectAccountEntity.ID);
                    BigDecimal amount = BigDecimal.ZERO;
                    BigDecimal largessAmount = BigDecimal.ZERO;
                    for (int i = 0; i < saleBillPaySavingCardProject.size(); i++) {
                        RefundBillPaySavingCardGoodsForm newRefundBillPaySavingCardGoodsForm = new RefundBillPaySavingCardGoodsForm();
                        newRefundBillPaySavingCardGoodsForm.SavingCardAccountID = saleBillPaySavingCardProject.get(i).SavingCardAccountID;
                        newRefundBillPaySavingCardGoodsForm.Name = saleBillPaySavingCardProject.get(i).Name;
                        if (i == saleBillPaySavingCardProject.size() - 1) {
                            newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.subtract(amount);
                            newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.subtract(largessAmount);
                        } else {
                            newRefundBillPaySavingCardGoodsForm.Amount = BigDecimal.ZERO;
                            newRefundBillPaySavingCardGoodsForm.LargessAmount = BigDecimal.ZERO;
                            if (projectAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
                                newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.multiply(saleBillPaySavingCardProject.get(i).Amount).divide(projectAccountEntity.SavingCardDeductionAmount, 2, BigDecimal.ROUND_HALF_UP);
                                amount = amount.add(newRefundBillPaySavingCardGoodsForm.Amount);
                            }
                            if (projectAccountEntity.SavingCardDeductionLargessAmount.compareTo(BigDecimal.ZERO) > 0) {
                                newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.multiply(saleBillPaySavingCardProject.get(i).LargessAmount).divide(projectAccountEntity.SavingCardDeductionLargessAmount, 2, BigDecimal.ROUND_HALF_UP);
                                largessAmount = largessAmount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);
                            }
                        }
                        newRefundBillPaySavingCardGoodsForm.TotalAmount = newRefundBillPaySavingCardGoodsForm.Amount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);

                        SaleBillPaySavingCardDeductionGoodsEntity saleBillPaySavingCardDeductionGoodsEntity = new SaleBillPaySavingCardDeductionGoodsEntity();
                        saleBillPaySavingCardDeductionGoodsEntity.SaleBillGoodsID = saleBillProjectEntity.ID;
                        saleBillPaySavingCardDeductionGoodsEntity.SavingCardAccountID = newRefundBillPaySavingCardGoodsForm.SavingCardAccountID;
                        saleBillPaySavingCardDeductionGoodsEntity.TotalAmount = newRefundBillPaySavingCardGoodsForm.TotalAmount;
                        saleBillPaySavingCardDeductionGoodsEntity.Amount = newRefundBillPaySavingCardGoodsForm.Amount;
                        saleBillPaySavingCardDeductionGoodsEntity.LargessAmount = newRefundBillPaySavingCardGoodsForm.LargessAmount;
                        saleBillPaySavingCardDeductionGoodsEntity.PreferentialAmount = BigDecimal.ZERO;
                        saleBillPaySavingCardDeductionGoodsEntity.LargessPreferentialAmount = BigDecimal.ZERO;
                        projectSavingCardDeduction.add(saleBillPaySavingCardDeductionGoodsEntity);
                    }


                    SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity = new SaleEntityPerformanceGoodsEntity();
                    saleEntityPerformanceGoodsEntity.Type = 2;
                    saleEntityPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;//订单编号
                    saleEntityPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;//门店ID
                    saleEntityPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;//开单时间
                    saleEntityPerformanceGoodsEntity.SaleBillGoodsID = saleBillProjectEntity.ID;//商品明细ID
                    saleEntityPerformanceGoodsEntity.GoodsID = saleBillProjectEntity.ProjectID;//商品ID
                    saleEntityPerformanceGoodsEntity.Scale = new BigDecimal(100);
                    List<SaleBillHandlerForm> saleBillHandlerFormList = new ArrayList<>();
                    for (RefundBillHandlerProjectEntity refundBillHandlerProjectEntity : refundBillHandlerProject) {
                        SaleBillHandlerForm saleBillHandlerForm = new SaleBillHandlerForm();
                        saleBillHandlerForm.EmployeeID = refundBillHandlerProjectEntity.EmployeeID;
                        saleBillHandlerForm.SaleHandlerID = refundBillHandlerProjectEntity.ProjectSaleHandlerID;
                        saleBillHandlerForm.Scale = refundBillHandlerProjectEntity.Scale.multiply(new BigDecimal(100).setScale(2, BigDecimal.ROUND_HALF_UP));
                        saleBillHandlerFormList.add(saleBillHandlerForm);
                    }
                    saleEntityPerformanceGoodsEntity.SaleBillHandlerEntityList = saleBillHandlerFormList;
                    saleEntityPerformanceGoodsEntity.Quantity = saleBillProjectEntity.Quantity;
                    saleEntityPerformanceGoodsEntity.AccountQuantity = projectAccountEntity.Quantity;
                    saleEntityPerformanceGoodsEntity.Price = saleBillProjectEntity.Price;
                    saleEntityPerformanceGoodsEntity.TotalAmount = saleBillProjectEntity.TotalAmount;
                    saleEntityPerformanceGoodsEntity.AccountTotalAmount = projectAccountEntity.TotalAmount;
                    saleEntityPerformanceGoodsEntity.ArrearAmount = saleBillProjectEntity.ArrearAmount;
                    saleEntityPerformanceGoodsEntity.IsLargess = saleBillProjectEntity.IsLargess;
                    saleEntityPerformanceGoodsEntities.add(saleEntityPerformanceGoodsEntity);

                    if (refundBillProjectEntity.PricePreferentialAmount.compareTo(BigDecimal.ZERO) == 1) {
                        refundRateForm = getProjectAmount(projectAccountEntity, refundBillProjectEntity.TotalAmount.add(refundBillProjectEntity.PricePreferentialAmount));
                    }
                    projectAccountEntity.Balance = projectAccountEntity.Balance - refundBillProjectEntity.Quantity;
                    projectAccountEntity.PayPerformanceBalanceAmount = projectAccountEntity.PayPerformanceBalanceAmount.subtract(refundRateForm.payAmount);
                    projectAccountEntity.CardPerformanceBalanceAmount = projectAccountEntity.CardPerformanceBalanceAmount.subtract(refundRateForm.savingCardDeductionAmount);
                    projectAccountEntity.CardLargessPerformanceBalanceAmount = projectAccountEntity.CardLargessPerformanceBalanceAmount.subtract(refundRateForm.savingCardDeductionLargessAmount);
                    projectAccountEntity.LargessPerformanceBalanceAmount = projectAccountEntity.LargessPerformanceBalanceAmount.subtract(projectAccountEntity.IsLargess ? refundBillProjectEntity.Price.multiply(new BigDecimal(refundBillProjectEntity.Quantity)) : BigDecimal.ZERO);
                    if (projectAccountEntity.Balance.equals(0) && projectAccountEntity.ArrearAmount.compareTo(BigDecimal.ZERO) > 0) {
                        projectAccountEntity.ArrearAmount = BigDecimal.ZERO;
                    }
                    projectAccountMapper.updateProjectAccountBalance(projectAccountEntity);
                }
            }
            if (refundGeneralCard != null && refundGeneralCard.size() > 0) {
                for (RefundBillGeneralCardEntity refundBillGeneralCardEntity : refundGeneralCard) {
                    GeneralCardAccountEntity generalCardAccountEntity = generalCardAccountMapper.getGeneralCardAccountByAccountID(refundBillGeneralCardEntity.GeneralCardAccountID);
                    if (!generalCardAccountEntity.Active) {
                        throw new RuntimeException("购买当前通用次卡的销售订单已被取消，请撤销或驳回当前退款单据");
                    }
                    if (generalCardAccountEntity.Balance.intValue() < refundBillGeneralCardEntity.Quantity.intValue()) {
                        throw new RuntimeException("通用次卡剩余次数不足");
                    }
                    SaleBillGeneralCardEntity saleBillGeneralCardEntity = new SaleBillGeneralCardEntity();
                    saleBillGeneralCardEntity.BillID = saleBillEntity.ID;
                    saleBillGeneralCardEntity.GeneralCardID = refundBillGeneralCardEntity.GeneralCardID;
                    saleBillGeneralCardEntity.Price = refundBillGeneralCardEntity.Price;
                    saleBillGeneralCardEntity.CardTimes = refundBillGeneralCardEntity.Quantity;
                    saleBillGeneralCardEntity.Quantity = 1;
                    saleBillGeneralCardEntity.TotalAmount = refundBillGeneralCardEntity.TotalAmount;
                    saleBillGeneralCardEntity.ArrearAmount = BigDecimal.ZERO;
                    saleBillGeneralCardEntity.IsLargess = generalCardAccountEntity.IsLargess;
                    saleBillGeneralCardEntity.PricePreferentialAmount = refundBillGeneralCardEntity.PricePreferentialAmount;
                    saleBillGeneralCardEntity.MemberPreferentialAmount = BigDecimal.ZERO;
                    saleBillGeneralCardEntity.CardPreferentialAmount = BigDecimal.ZERO;
                    saleBillGeneralCardEntity.GeneralCardAccountID = refundBillGeneralCardEntity.GeneralCardAccountID;
                    saleBillMapper.createSaleBillGeneralCard(saleBillGeneralCardEntity);

                    List<RefundBillHandlerGeneralCardEntity> refundBillHandlerGeneralCard = refundBillHandlerMapper.getRefundBillHandlerGeneralCard(refundBillGeneralCardEntity.ID);
                    //添加经手人
                    List<SaleBillHandlerGeneralCardEntity> saleBillHandlerGeneralCardEntities = new ArrayList<>();
                    for (RefundBillHandlerGeneralCardEntity handlerProductEntity : refundBillHandlerGeneralCard) {
                        SaleBillHandlerGeneralCardEntity entity = new SaleBillHandlerGeneralCardEntity();
                        entity.SaleBillGeneralCardID = saleBillGeneralCardEntity.ID;
                        entity.GeneralCardSaleHandlerID = handlerProductEntity.GeneralCardSaleHandlerID;
                        entity.EmployeeID = handlerProductEntity.EmployeeID;
                        entity.Scale = handlerProductEntity.Scale;
                        entity.IsCalculatePassengerFlow = true;
                        saleBillHandlerGeneralCardEntities.add(entity);
                    }
                    if (saleBillHandlerGeneralCardEntities.size() > 0) {
                        saleBillHandlerMapper.createSaleBillHandlerGeneralCard(saleBillHandlerGeneralCardEntities);
                    }

                    //更新账户
                    RefundRateForm refundRateForm = new RefundRateForm();
                    BigDecimal generalCardPricePreferentialAmountTotal = refundBillMapper.getGeneralCardPricePreferentialAmountTotal(generalCardAccountEntity.ID);//优惠金额合计
                    if (refundBillGeneralCardEntity.PricePreferentialAmount.compareTo(BigDecimal.ZERO) == 0 && generalCardPricePreferentialAmountTotal.compareTo(BigDecimal.ZERO) == 0) {
                        if (generalCardAccountEntity.Balance.equals(refundBillGeneralCardEntity.Quantity)) {
                            refundRateForm.payAmount = generalCardAccountEntity.PayPerformanceBalanceAmount;
                            refundRateForm.savingCardDeductionAmount = generalCardAccountEntity.CardPerformanceBalanceAmount;
                            refundRateForm.savingCardDeductionLargessAmount = generalCardAccountEntity.CardLargessPerformanceBalanceAmount;
                        } else {
                            refundRateForm = getGeneralCardAmount(generalCardAccountEntity, refundBillGeneralCardEntity.TotalAmount);
                        }
                    } else {
                        refundRateForm = getGeneralCardAmount(generalCardAccountEntity, refundBillGeneralCardEntity.TotalAmount);
                    }

                    BigDecimal payAmount = refundBillGeneralCardEntity.TotalAmount.subtract(refundRateForm.savingCardDeductionAmount).subtract(refundRateForm.savingCardDeductionLargessAmount);
                    if (payAmount.compareTo(BigDecimal.ZERO) > 0) {
                        GoodAssignAmountEntity entity = new GoodAssignAmountEntity();
                        entity.SaleBillGoodsID = saleBillGeneralCardEntity.ID;
                        entity.GoodsID = saleBillGeneralCardEntity.GeneralCardID;
                        entity.Type = 3;
                        entity.UnassignedAmount = payAmount;
                        goodsAssignAmountEntities.add(entity);
                    }

                    BigDecimal savingCardDeductionAmount = refundRateForm.savingCardDeductionAmount;
                    BigDecimal savingCardDeductionLargessAmount = refundRateForm.savingCardDeductionLargessAmount;
                    List<RefundBillPaySavingCardGoodsForm> saleBillPaySavingCardGeneralCard = saleBillPayMapper.getSaleBillPaySavingCardGeneralCard(generalCardAccountEntity.SaleBillGeneralCardID,generalCardAccountEntity.ID);
                    BigDecimal amount = BigDecimal.ZERO;
                    BigDecimal largessAmount = BigDecimal.ZERO;
                    for (int i = 0; i < saleBillPaySavingCardGeneralCard.size(); i++) {
                        RefundBillPaySavingCardGoodsForm newRefundBillPaySavingCardGoodsForm = new RefundBillPaySavingCardGoodsForm();
                        newRefundBillPaySavingCardGoodsForm.SavingCardAccountID = saleBillPaySavingCardGeneralCard.get(i).SavingCardAccountID;
                        newRefundBillPaySavingCardGoodsForm.Name = saleBillPaySavingCardGeneralCard.get(i).Name;
                        if (i == saleBillPaySavingCardGeneralCard.size() - 1) {
                            newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.subtract(amount);
                            newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.subtract(largessAmount);
                        } else {
                            newRefundBillPaySavingCardGoodsForm.Amount = BigDecimal.ZERO;
                            newRefundBillPaySavingCardGoodsForm.LargessAmount = BigDecimal.ZERO;
                            if (generalCardAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0 && saleBillPaySavingCardGeneralCard.get(i).Amount.compareTo(BigDecimal.ZERO) > 0) {
                                newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.multiply(saleBillPaySavingCardGeneralCard.get(i).Amount).divide(generalCardAccountEntity.SavingCardDeductionAmount.multiply(new BigDecimal(saleBillPaySavingCardGeneralCard.get(i).Quantity)), 2, BigDecimal.ROUND_HALF_UP);
                                amount = amount.add(newRefundBillPaySavingCardGoodsForm.Amount);
                            }
                            if (generalCardAccountEntity.SavingCardDeductionLargessAmount.compareTo(BigDecimal.ZERO) > 0 && saleBillPaySavingCardGeneralCard.get(i).LargessAmount.compareTo(BigDecimal.ZERO) > 0) {
                                newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.multiply(saleBillPaySavingCardGeneralCard.get(i).LargessAmount).divide(generalCardAccountEntity.SavingCardDeductionLargessAmount.multiply(new BigDecimal(saleBillPaySavingCardGeneralCard.get(i).Quantity)), 2, BigDecimal.ROUND_HALF_UP);
                                largessAmount = largessAmount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);
                            }
                        }
                        newRefundBillPaySavingCardGoodsForm.TotalAmount = newRefundBillPaySavingCardGoodsForm.Amount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);

                        SaleBillPaySavingCardDeductionGoodsEntity saleBillPaySavingCardDeductionGoodsEntity = new SaleBillPaySavingCardDeductionGoodsEntity();
                        saleBillPaySavingCardDeductionGoodsEntity.SaleBillGoodsID = saleBillGeneralCardEntity.ID;
                        saleBillPaySavingCardDeductionGoodsEntity.SavingCardAccountID = newRefundBillPaySavingCardGoodsForm.SavingCardAccountID;
                        saleBillPaySavingCardDeductionGoodsEntity.TotalAmount = newRefundBillPaySavingCardGoodsForm.TotalAmount;
                        saleBillPaySavingCardDeductionGoodsEntity.Amount = newRefundBillPaySavingCardGoodsForm.Amount;
                        saleBillPaySavingCardDeductionGoodsEntity.LargessAmount = newRefundBillPaySavingCardGoodsForm.LargessAmount;
                        saleBillPaySavingCardDeductionGoodsEntity.PreferentialAmount = BigDecimal.ZERO;
                        saleBillPaySavingCardDeductionGoodsEntity.LargessPreferentialAmount = BigDecimal.ZERO;
                        generalCardSavingCardDeduction.add(saleBillPaySavingCardDeductionGoodsEntity);
                    }


                    SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity = new SaleEntityPerformanceGoodsEntity();
                    saleEntityPerformanceGoodsEntity.Type = 3;
                    saleEntityPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;//订单编号
                    saleEntityPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;//门店ID
                    saleEntityPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;//开单时间
                    saleEntityPerformanceGoodsEntity.SaleBillGoodsID = saleBillGeneralCardEntity.ID;//商品明细ID
                    saleEntityPerformanceGoodsEntity.GoodsID = saleBillGeneralCardEntity.GeneralCardID;//商品ID
                    saleEntityPerformanceGoodsEntity.Scale = new BigDecimal(100);
                    List<SaleBillHandlerForm> saleBillHandlerFormList = new ArrayList<>();
                    for (RefundBillHandlerGeneralCardEntity refundBillHandlerGeneralCardEntity : refundBillHandlerGeneralCard) {
                        SaleBillHandlerForm saleBillHandlerForm = new SaleBillHandlerForm();
                        saleBillHandlerForm.EmployeeID = refundBillHandlerGeneralCardEntity.EmployeeID;
                        saleBillHandlerForm.SaleHandlerID = refundBillHandlerGeneralCardEntity.GeneralCardSaleHandlerID;
                        saleBillHandlerForm.Scale = refundBillHandlerGeneralCardEntity.Scale.multiply(new BigDecimal(100).setScale(2, BigDecimal.ROUND_HALF_UP));
                        saleBillHandlerFormList.add(saleBillHandlerForm);
                    }
                    saleEntityPerformanceGoodsEntity.SaleBillHandlerEntityList = saleBillHandlerFormList;
                    saleEntityPerformanceGoodsEntity.Quantity = saleBillGeneralCardEntity.Quantity;
                    saleEntityPerformanceGoodsEntity.AccountQuantity = 1;
                    saleEntityPerformanceGoodsEntity.Price = saleBillGeneralCardEntity.Price;
                    saleEntityPerformanceGoodsEntity.TotalAmount = saleBillGeneralCardEntity.TotalAmount;
                    saleEntityPerformanceGoodsEntity.AccountTotalAmount = generalCardAccountEntity.TotalAmount;
                    saleEntityPerformanceGoodsEntity.ArrearAmount = saleBillGeneralCardEntity.ArrearAmount;
                    saleEntityPerformanceGoodsEntity.IsLargess = saleBillGeneralCardEntity.IsLargess;
                    saleEntityPerformanceGoodsEntities.add(saleEntityPerformanceGoodsEntity);

                    if (refundBillGeneralCardEntity.PricePreferentialAmount.compareTo(BigDecimal.ZERO) == 1) {
                        refundRateForm = getGeneralCardAmount(generalCardAccountEntity, refundBillGeneralCardEntity.TotalAmount.add(refundBillGeneralCardEntity.PricePreferentialAmount));
                    }
                    generalCardAccountEntity.Balance = generalCardAccountEntity.Balance - refundBillGeneralCardEntity.Quantity;
                    generalCardAccountEntity.PayPerformanceBalanceAmount = generalCardAccountEntity.PayPerformanceBalanceAmount.subtract(refundRateForm.payAmount);
                    generalCardAccountEntity.CardPerformanceBalanceAmount = generalCardAccountEntity.CardPerformanceBalanceAmount.subtract(refundRateForm.savingCardDeductionAmount);
                    generalCardAccountEntity.CardLargessPerformanceBalanceAmount = generalCardAccountEntity.CardLargessPerformanceBalanceAmount.subtract(refundRateForm.savingCardDeductionLargessAmount);
                    generalCardAccountEntity.LargessPerformanceBalanceAmount = generalCardAccountEntity.LargessPerformanceBalanceAmount.subtract(generalCardAccountEntity.IsLargess ? generalCardAccountEntity.TotalAmount.multiply(new BigDecimal(refundBillGeneralCardEntity.Quantity)).divide(new BigDecimal(generalCardAccountEntity.CardTimes), 2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);
                    if (generalCardAccountEntity.Balance.equals(0) && generalCardAccountEntity.ArrearAmount.compareTo(BigDecimal.ZERO) > 0) {
                        generalCardAccountEntity.ArrearAmount = BigDecimal.ZERO;
                    }
                    generalCardAccountMapper.updateGeneralCardAccountBalance(generalCardAccountEntity);
                }
            }
            if (refundTimeCard != null && refundTimeCard.size() > 0) {
                for (RefundBillTimeCardEntity refundBillTimeCardEntity : refundTimeCard) {
                    TimeCardAccountEntity timeCardAccountEntity = timeCardAccountMapper.getTimeCardAccountByAccountID(refundBillTimeCardEntity.TimeCardAccountID);
                    if (!timeCardAccountEntity.Active) {
                        throw new RuntimeException("购买当前时效卡的销售订单已被取消，请撤销或驳回当前退款单据");
                    }
                    SaleBillTimeCardEntity saleBillTimeCardEntity = new SaleBillTimeCardEntity();
                    saleBillTimeCardEntity.BillID = saleBillEntity.ID;
                    saleBillTimeCardEntity.TimeCardID = refundBillTimeCardEntity.TimeCardID;
                    saleBillTimeCardEntity.Price = refundBillTimeCardEntity.Price;
                    saleBillTimeCardEntity.Quantity = 1;
                    saleBillTimeCardEntity.TotalAmount = refundBillTimeCardEntity.TotalAmount;
                    saleBillTimeCardEntity.ArrearAmount = BigDecimal.ZERO;
                    saleBillTimeCardEntity.IsLargess = timeCardAccountEntity.IsLargess;
                    saleBillTimeCardEntity.PricePreferentialAmount = refundBillTimeCardEntity.PricePreferentialAmount;
                    saleBillTimeCardEntity.MemberPreferentialAmount = BigDecimal.ZERO;
                    saleBillTimeCardEntity.CardPreferentialAmount = BigDecimal.ZERO;
                    saleBillTimeCardEntity.TimeCardAccountID = refundBillTimeCardEntity.TimeCardAccountID;
                    saleBillMapper.createSaleBillTimeCard(saleBillTimeCardEntity);

                    List<RefundBillHandlerTimeCardEntity> refundBillHandlerTimeCard = refundBillHandlerMapper.getRefundBillHandlerTimeCard(refundBillTimeCardEntity.ID);
                    //添加经手人
                    List<SaleBillHandlerTimeCardEntity> saleBillHandlerTimeCardEntities = new ArrayList<>();
                    for (RefundBillHandlerTimeCardEntity handlerTimeCardEntity : refundBillHandlerTimeCard) {
                        SaleBillHandlerTimeCardEntity entity = new SaleBillHandlerTimeCardEntity();
                        entity.SaleBillTimeCardID = saleBillTimeCardEntity.ID;
                        entity.TimeCardSaleHandlerID = handlerTimeCardEntity.TimeCardSaleHandlerID;
                        entity.EmployeeID = handlerTimeCardEntity.EmployeeID;
                        entity.Scale = handlerTimeCardEntity.Scale;
                        entity.IsCalculatePassengerFlow = true;
                        saleBillHandlerTimeCardEntities.add(entity);
                    }
                    if (saleBillHandlerTimeCardEntities.size() > 0) {
                        saleBillHandlerMapper.createSaleBillHandlerTimeCard(saleBillHandlerTimeCardEntities);
                    }
                    RefundRateForm refundRateForm = new RefundRateForm();
                    if (refundBillTimeCardEntity.PricePreferentialAmount.compareTo(BigDecimal.ZERO) == 0) {
                        refundRateForm.payAmount = timeCardAccountEntity.PayPerformanceBalanceAmount;
                        refundRateForm.savingCardDeductionAmount = timeCardAccountEntity.CardPerformanceBalanceAmount;
                        refundRateForm.savingCardDeductionLargessAmount = timeCardAccountEntity.CardLargessPerformanceBalanceAmount;
                    } else {
                        refundRateForm = getTimeCardAmount(timeCardAccountEntity, refundBillTimeCardEntity.TotalAmount);
                    }
                    BigDecimal payAmount = refundBillTimeCardEntity.TotalAmount.subtract(refundRateForm.savingCardDeductionAmount).subtract(refundRateForm.savingCardDeductionLargessAmount);
                    if (payAmount.compareTo(BigDecimal.ZERO) > 0) {
                        GoodAssignAmountEntity entity = new GoodAssignAmountEntity();
                        entity.SaleBillGoodsID = saleBillTimeCardEntity.ID;
                        entity.GoodsID = saleBillTimeCardEntity.TimeCardID;
                        entity.Type = 4;
                        entity.UnassignedAmount = payAmount;
                        goodsAssignAmountEntities.add(entity);
                    }

                    BigDecimal savingCardDeductionAmount = refundRateForm.savingCardDeductionAmount;
                    BigDecimal savingCardDeductionLargessAmount = refundRateForm.savingCardDeductionLargessAmount;
                    List<RefundBillPaySavingCardGoodsForm> saleBillPaySavingCardTimeCard = saleBillPayMapper.getSaleBillPaySavingCardTimeCard(timeCardAccountEntity.SaleBillTimeCardID,timeCardAccountEntity.ID);
                    BigDecimal amount = BigDecimal.ZERO;
                    BigDecimal largessAmount = BigDecimal.ZERO;
                    for (int i = 0; i < saleBillPaySavingCardTimeCard.size(); i++) {
                        RefundBillPaySavingCardGoodsForm newRefundBillPaySavingCardGoodsForm = new RefundBillPaySavingCardGoodsForm();
                        newRefundBillPaySavingCardGoodsForm.SavingCardAccountID = saleBillPaySavingCardTimeCard.get(i).SavingCardAccountID;
                        newRefundBillPaySavingCardGoodsForm.Name = saleBillPaySavingCardTimeCard.get(i).Name;
                        if (i == saleBillPaySavingCardTimeCard.size() - 1) {
                            newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.subtract(amount);
                            newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.subtract(largessAmount);
                        } else {
                            newRefundBillPaySavingCardGoodsForm.Amount = BigDecimal.ZERO;
                            newRefundBillPaySavingCardGoodsForm.LargessAmount = BigDecimal.ZERO;
                            if (timeCardAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0 && saleBillPaySavingCardTimeCard.get(i).Amount.compareTo(BigDecimal.ZERO) > 0) {
                                newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.multiply(saleBillPaySavingCardTimeCard.get(i).Amount).divide(timeCardAccountEntity.SavingCardDeductionAmount.multiply(new BigDecimal(saleBillPaySavingCardTimeCard.get(i).Quantity)), 2, BigDecimal.ROUND_HALF_UP);
                                amount = amount.add(newRefundBillPaySavingCardGoodsForm.Amount);
                            }
                            if (timeCardAccountEntity.SavingCardDeductionLargessAmount.compareTo(BigDecimal.ZERO) > 0 && saleBillPaySavingCardTimeCard.get(i).LargessAmount.compareTo(BigDecimal.ZERO) > 0) {
                                newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.multiply(saleBillPaySavingCardTimeCard.get(i).LargessAmount).divide(timeCardAccountEntity.SavingCardDeductionLargessAmount.multiply(new BigDecimal(saleBillPaySavingCardTimeCard.get(i).Quantity)), 2, BigDecimal.ROUND_HALF_UP);
                                largessAmount = largessAmount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);
                            }
                        }
                        newRefundBillPaySavingCardGoodsForm.TotalAmount = newRefundBillPaySavingCardGoodsForm.Amount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);

                        SaleBillPaySavingCardDeductionGoodsEntity saleBillPaySavingCardDeductionGoodsEntity = new SaleBillPaySavingCardDeductionGoodsEntity();
                        saleBillPaySavingCardDeductionGoodsEntity.SaleBillGoodsID = saleBillTimeCardEntity.ID;
                        saleBillPaySavingCardDeductionGoodsEntity.SavingCardAccountID = newRefundBillPaySavingCardGoodsForm.SavingCardAccountID;
                        saleBillPaySavingCardDeductionGoodsEntity.TotalAmount = newRefundBillPaySavingCardGoodsForm.TotalAmount;
                        saleBillPaySavingCardDeductionGoodsEntity.Amount = newRefundBillPaySavingCardGoodsForm.Amount;
                        saleBillPaySavingCardDeductionGoodsEntity.LargessAmount = newRefundBillPaySavingCardGoodsForm.LargessAmount;
                        saleBillPaySavingCardDeductionGoodsEntity.PreferentialAmount = BigDecimal.ZERO;
                        saleBillPaySavingCardDeductionGoodsEntity.LargessPreferentialAmount = BigDecimal.ZERO;
                        timeCardSavingCardDeduction.add(saleBillPaySavingCardDeductionGoodsEntity);
                    }
                    SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity = new SaleEntityPerformanceGoodsEntity();
                    saleEntityPerformanceGoodsEntity.Type = 4;
                    saleEntityPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;//订单编号
                    saleEntityPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;//门店ID
                    saleEntityPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;//开单时间
                    saleEntityPerformanceGoodsEntity.SaleBillGoodsID = saleBillTimeCardEntity.ID;//商品明细ID
                    saleEntityPerformanceGoodsEntity.GoodsID = saleBillTimeCardEntity.TimeCardID;//商品ID
                    saleEntityPerformanceGoodsEntity.Scale = new BigDecimal(100);
                    List<SaleBillHandlerForm> saleBillHandlerFormList = new ArrayList<>();
                    for (RefundBillHandlerTimeCardEntity refundBillHandlerTimeCardEntity : refundBillHandlerTimeCard) {
                        SaleBillHandlerForm saleBillHandlerForm = new SaleBillHandlerForm();
                        saleBillHandlerForm.EmployeeID = refundBillHandlerTimeCardEntity.EmployeeID;
                        saleBillHandlerForm.SaleHandlerID = refundBillHandlerTimeCardEntity.TimeCardSaleHandlerID;
                        saleBillHandlerForm.Scale = refundBillHandlerTimeCardEntity.Scale.multiply(new BigDecimal(100).setScale(2, BigDecimal.ROUND_HALF_UP));
                        saleBillHandlerFormList.add(saleBillHandlerForm);
                    }
                    saleEntityPerformanceGoodsEntity.SaleBillHandlerEntityList = saleBillHandlerFormList;
                    saleEntityPerformanceGoodsEntity.Quantity = saleBillTimeCardEntity.Quantity;
                    saleEntityPerformanceGoodsEntity.AccountQuantity = 1;
                    saleEntityPerformanceGoodsEntity.Price = saleBillTimeCardEntity.Price;
                    saleEntityPerformanceGoodsEntity.TotalAmount = saleBillTimeCardEntity.TotalAmount;
                    saleEntityPerformanceGoodsEntity.AccountTotalAmount = timeCardAccountEntity.TotalAmount;
                    saleEntityPerformanceGoodsEntity.ArrearAmount = saleBillTimeCardEntity.ArrearAmount;
                    saleEntityPerformanceGoodsEntity.IsLargess = saleBillTimeCardEntity.IsLargess;
                    saleEntityPerformanceGoodsEntities.add(saleEntityPerformanceGoodsEntity);
                    //更新账户

                    if (refundBillTimeCardEntity.PricePreferentialAmount.compareTo(BigDecimal.ZERO) == 1) {
                        refundRateForm = getTimeCardAmount(timeCardAccountEntity, refundBillTimeCardEntity.TotalAmount.add(refundBillTimeCardEntity.PricePreferentialAmount));
                    }
                    timeCardAccountEntity.Active = false;
                    timeCardAccountEntity.PayPerformanceBalanceAmount = timeCardAccountEntity.PayPerformanceBalanceAmount.subtract(refundRateForm.payAmount);
                    timeCardAccountEntity.CardPerformanceBalanceAmount = timeCardAccountEntity.CardPerformanceBalanceAmount.subtract(refundRateForm.savingCardDeductionAmount);
                    timeCardAccountEntity.CardLargessPerformanceBalanceAmount = timeCardAccountEntity.CardLargessPerformanceBalanceAmount.subtract(refundRateForm.savingCardDeductionLargessAmount);
                    timeCardAccountEntity.LargessPerformanceBalanceAmount = timeCardAccountEntity.LargessPerformanceBalanceAmount.subtract(timeCardAccountEntity.IsLargess ? refundBillTimeCardEntity.Price : BigDecimal.ZERO);
                    timeCardAccountMapper.updateTimeCardAccount(timeCardAccountEntity);
                }
            }

            if (refundSavingCard != null && refundSavingCard.size() > 0) {
                for (RefundBillSavingCardEntity refundBillSavingCardEntity : refundSavingCard) {
                    SavingCardAccountExtendEntity savingCardAccountEntity = savingCardAccountMapper.getSavingCardAccountByID(refundBillSavingCardEntity.SavingCardAccountID);
                    if (!savingCardAccountEntity.Active) {
                        throw new RuntimeException("购买当前储值卡的销售订单已被取消，请撤销或驳回当前退款单据");
                    }
                    SaleBillSavingCardEntity saleBillSavingCardEntity = new SaleBillSavingCardEntity();
                    saleBillSavingCardEntity.BillID = saleBillEntity.ID;
                    saleBillSavingCardEntity.SavingCardID = refundBillSavingCardEntity.SavingCardID;
                    saleBillSavingCardEntity.Quantity = 1;
                    saleBillSavingCardEntity.Amount = refundBillSavingCardEntity.Amount;
                    saleBillSavingCardEntity.LargessAmount = refundBillSavingCardEntity.LargessAmount;
                    saleBillSavingCardEntity.TotalAmount = refundBillSavingCardEntity.Amount;
                    saleBillSavingCardEntity.ArrearAmount = BigDecimal.ZERO;
                    saleBillSavingCardEntity.SavingCardAccountID = refundBillSavingCardEntity.SavingCardAccountID;
                    saleBillMapper.createSaleBillSavingCard(saleBillSavingCardEntity);

                    List<RefundBillHandlerSavingCardEntity> refundBillHandlerSavingCard = refundBillHandlerMapper.getRefundBillHandlerSavingCard(refundBillSavingCardEntity.ID);
                    //添加经手人
                    List<SaleBillHandlerSavingCardEntity> saleBillHandlerSavingCardEntities = new ArrayList<>();
                    for (RefundBillHandlerSavingCardEntity handlerSavingCardEntity : refundBillHandlerSavingCard) {
                        SaleBillHandlerSavingCardEntity entity = new SaleBillHandlerSavingCardEntity();
                        entity.SaleBillSavingCardID = saleBillSavingCardEntity.ID;
                        entity.SavingCardSaleHandlerID = handlerSavingCardEntity.SavingCardSaleHandlerID;
                        entity.EmployeeID = handlerSavingCardEntity.EmployeeID;
                        entity.Scale = handlerSavingCardEntity.Scale;
                        entity.IsCalculatePassengerFlow = true;
                        saleBillHandlerSavingCardEntities.add(entity);
                    }
                    if (saleBillHandlerSavingCardEntities.size() > 0) {
                        saleBillHandlerMapper.createSaleBillHandlerSavingCard(saleBillHandlerSavingCardEntities);
                    }

                    if (refundBillSavingCardEntity.Amount.compareTo(BigDecimal.ZERO) > 0) {
                        GoodAssignAmountEntity entity = new GoodAssignAmountEntity();
                        entity.SaleBillGoodsID = saleBillSavingCardEntity.ID;
                        entity.GoodsID = saleBillSavingCardEntity.SavingCardID;
                        entity.Type = 5;
                        entity.UnassignedAmount = refundBillSavingCardEntity.Amount;
                        goodsAssignAmountEntities.add(entity);
                    }
                    SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity = new SaleEntityPerformanceGoodsEntity();
                    saleEntityPerformanceGoodsEntity.Type = 5;
                    saleEntityPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;//订单编号
                    saleEntityPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;//门店ID
                    saleEntityPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;//开单时间
                    saleEntityPerformanceGoodsEntity.SaleBillGoodsID = saleBillSavingCardEntity.ID;//商品明细ID
                    saleEntityPerformanceGoodsEntity.GoodsID = saleBillSavingCardEntity.SavingCardID;//商品ID
                    List<SaleBillHandlerForm> saleBillHandlerFormList = new ArrayList<>();
                    for (RefundBillHandlerSavingCardEntity refundBillHandlerSavingCardEntity : refundBillHandlerSavingCard) {
                        SaleBillHandlerForm saleBillHandlerForm = new SaleBillHandlerForm();
                        saleBillHandlerForm.EmployeeID = refundBillHandlerSavingCardEntity.EmployeeID;
                        saleBillHandlerForm.SaleHandlerID = refundBillHandlerSavingCardEntity.SavingCardSaleHandlerID;
                        saleBillHandlerForm.Scale = refundBillHandlerSavingCardEntity.Scale.multiply(new BigDecimal(100).setScale(2, BigDecimal.ROUND_HALF_UP));
                        saleBillHandlerFormList.add(saleBillHandlerForm);
                    }
                    saleEntityPerformanceGoodsEntity.SaleBillHandlerEntityList = saleBillHandlerFormList;
                    saleEntityPerformanceGoodsEntity.Quantity = saleBillSavingCardEntity.Quantity;
                    saleEntityPerformanceGoodsEntity.TotalAmount = saleBillSavingCardEntity.TotalAmount;
                    saleEntityPerformanceGoodsEntity.ArrearAmount = saleBillSavingCardEntity.ArrearAmount;
                    saleEntityPerformanceGoodsEntity.AccountTotalAmount = savingCardAccountEntity.Amount;
                    saleEntityPerformanceGoodsEntity.Scale = new BigDecimal(100);
                    saleEntityPerformanceGoodsEntities.add(saleEntityPerformanceGoodsEntity);
                    //更新账户
                    savingCardAccountEntity.Balance = savingCardAccountEntity.Balance.subtract(refundBillSavingCardEntity.Amount);
                    savingCardAccountEntity.LargessBalance = savingCardAccountEntity.LargessBalance.subtract(refundBillSavingCardEntity.LargessAmount);

                    if (refundBillSavingCardEntity.Amount.compareTo(savingCardAccountEntity.AllowRefundBalance) == 0 && refundBillSavingCardEntity.LargessAmount.compareTo(savingCardAccountEntity.AllowRefundLargessBalance) == 0 && savingCardAccountEntity.ArrearAmount.compareTo(BigDecimal.ZERO) > 0) {
                        savingCardAccountEntity.Balance = BigDecimal.ZERO;
                        savingCardAccountEntity.LargessBalance = BigDecimal.ZERO;
                        savingCardAccountEntity.ArrearAmount = BigDecimal.ZERO;
                    }
                    savingCardAccountMapper.updateSavingCardAccountBalance(savingCardAccountEntity);
                }
            }
            //套餐卡明细、经手人创建，并计抵扣
            List<PackageCardGoodAssignAmountEntity> packageCardGoodsAll = new ArrayList<>();//套餐卡明细商品金额分配列表

            for (RefundBillPackageCardEntity refundBillPackageCardEntity : refundPackageCard) {
                PackageCardAccountEntity packageCardAccountEntity = packageCardAccountMapper.getPackageCardAccount(refundBillPackageCardEntity.PackageCardAccountID);
                if (!packageCardAccountEntity.Active) {
                    throw new RuntimeException("购买当前套餐卡的销售订单已被取消，请撤销或驳回当前退款单据");
                }
                SaleBillPackageCardEntity saleBillPackageCardEntity = new SaleBillPackageCardEntity();
                saleBillPackageCardEntity.BillID = saleBillEntity.ID;
                saleBillPackageCardEntity.PackageCardID = refundBillPackageCardEntity.PackageCardID;
                saleBillPackageCardEntity.Price = refundBillPackageCardEntity.Amount;
                saleBillPackageCardEntity.Quantity = 1;
                saleBillPackageCardEntity.TotalAmount = refundBillPackageCardEntity.Amount;
                saleBillPackageCardEntity.ArrearAmount = BigDecimal.ZERO;
                saleBillPackageCardEntity.IsLargess = packageCardAccountEntity.IsLargess;
                saleBillPackageCardEntity.PricePreferentialAmount = refundBillPackageCardEntity.PricePreferentialAmount;
                saleBillPackageCardEntity.MemberPreferentialAmount = BigDecimal.ZERO;
                saleBillPackageCardEntity.CardPreferentialAmount = BigDecimal.ZERO;
                saleBillPackageCardEntity.PackageCardAccountID = packageCardAccountEntity.ID;
                saleBillMapper.createSaleBillPackageCard(saleBillPackageCardEntity);

                List<RefundBillHandlerPackageCardEntity> refundBillHandlerPackageCard = refundBillHandlerMapper.getRefundBillHandlerPackageCard(refundBillPackageCardEntity.ID);
                //添加经手人
                List<SaleBillHandlerPackageCardEntity> saleBillHandlerPackageCardEntities = new ArrayList<>();
                for (RefundBillHandlerPackageCardEntity handlerPackageCardEntity : refundBillHandlerPackageCard) {
                    SaleBillHandlerPackageCardEntity entity = new SaleBillHandlerPackageCardEntity();
                    entity.SaleBillPackageCardID = saleBillPackageCardEntity.ID;
                    entity.PackageCardSaleHandlerID = handlerPackageCardEntity.PackageCardSaleHandlerID;
                    entity.EmployeeID = handlerPackageCardEntity.EmployeeID;
                    entity.Scale = handlerPackageCardEntity.Scale;
                    entity.IsCalculatePassengerFlow = true;
                    saleBillHandlerPackageCardEntities.add(entity);
                }
                if (saleBillHandlerPackageCardEntities.size() > 0) {
                    saleBillHandlerMapper.createSaleBillHandlerPackageCard(saleBillHandlerPackageCardEntities);
                }
                List<Integer> PackageCardAccountID = new ArrayList<>();
                PackageCardAccountID.add(packageCardAccountEntity.ID);

                List<RefundBillProductEntity> refundPackageCardProduct = refundBillMapper.getRefundBillProductByPackageCardID(form.ID, refundBillPackageCardEntity.ID);
                List<RefundBillProjectEntity> refundPackageCardProject = refundBillMapper.getRefundBillProjectByPackageCardID(form.ID, refundBillPackageCardEntity.ID);
                List<RefundBillGeneralCardEntity> refundPackageCardGeneralCard = refundBillMapper.getRefundBillGeneralCardByPackageCardID(form.ID, refundBillPackageCardEntity.ID);
                List<RefundBillTimeCardEntity> refundPackageCardTimeCard = refundBillMapper.getRefundBillTimeCardByPackageCardID(form.ID, refundBillPackageCardEntity.ID);
                List<RefundBillSavingCardEntity> refundPackageCardSavingCard = refundBillMapper.getRefundBillSavingCardByPackageCardID(form.ID, refundBillPackageCardEntity.ID);

                BigDecimal packagePayAmount = BigDecimal.ZERO;
                //套餐卡明细创建
                //套餐卡产品明细
                PackageCardGoodAssignAmountEntity packageCardGoodAssignAmountEntity = new PackageCardGoodAssignAmountEntity();
                List<GoodAssignAmountEntity> packageCardGoodAssignAmountEntities = new ArrayList<>();//套餐卡现金
                List<GoodAssignAmountEntity> packageCardSavingCardAssignAmountEntities = new ArrayList<>();//套餐卡储值卡未分配集合

                //套餐卡明细创建
                //套餐卡产品
                for (RefundBillProductEntity refundBillProductEntity : refundPackageCardProduct) {
                    ProductAccountEntity productAccountEntity = productAccountMapper.getProductAccountByAccountID(refundBillProductEntity.ProductAccountID);
                    if (!productAccountEntity.Active) {
                        throw new RuntimeException("购买当前产品的销售订单已被取消，请撤销或驳回当前退款单据");
                    }
                    if (productAccountEntity.Balance.intValue() < refundBillProductEntity.Quantity.intValue()) {
                        throw new RuntimeException("产品剩余次数不足");
                    }
                    SaleBillPackageCardProductEntity saleBillPackageCardProductEntity = new SaleBillPackageCardProductEntity();
                    saleBillPackageCardProductEntity.SaleBillPackageCardID = saleBillPackageCardEntity.ID;
                    saleBillPackageCardProductEntity.ProductID = refundBillProductEntity.ProductID;
                    saleBillPackageCardProductEntity.Quantity = refundBillProductEntity.Quantity;
                    saleBillPackageCardProductEntity.PackageQuantity = 1;
                    saleBillPackageCardProductEntity.PackagePrice = refundBillProductEntity.Price;
                    saleBillPackageCardProductEntity.PackageTotalPrice = refundBillProductEntity.Price.multiply(new BigDecimal(refundBillProductEntity.Quantity)).setScale(2, BigDecimal.ROUND_HALF_UP);
                    saleBillPackageCardProductEntity.TotalAmount = refundBillProductEntity.TotalAmount;
                    saleBillPackageCardProductEntity.ArrearAmount = BigDecimal.ZERO;
                    saleBillPackageCardProductEntity.PricePreferentialAmount = refundBillProductEntity.PricePreferentialAmount;
                    saleBillPackageCardProductEntity.MemberPreferentialAmount = BigDecimal.ZERO;
                    saleBillPackageCardProductEntity.CardPreferentialAmount = BigDecimal.ZERO;
                    saleBillPackageCardProductEntity.IsLargess = productAccountEntity.IsLargess;
                    saleBillPackageCardProductEntity.ProductAccountID = refundBillProductEntity.ProductAccountID;
                    saleBillMapper.createSaleBillPackageCardProduct(saleBillPackageCardProductEntity);

                    RefundRateForm refundRateForm = new RefundRateForm();
                    BigDecimal productPricePreferentialAmountTotal = refundBillMapper.getProductPricePreferentialAmountTotal(productAccountEntity.ID);//优惠金额合计
                    if (refundBillProductEntity.PricePreferentialAmount.compareTo(BigDecimal.ZERO) == 0 && productPricePreferentialAmountTotal.compareTo(BigDecimal.ZERO) == 0) {
                        if (productAccountEntity.Balance.equals(refundBillProductEntity.Quantity)) {
                            refundRateForm.payAmount = productAccountEntity.PayPerformanceBalanceAmount;
                            refundRateForm.savingCardDeductionAmount = productAccountEntity.CardPerformanceBalanceAmount;
                            refundRateForm.savingCardDeductionLargessAmount = productAccountEntity.CardLargessPerformanceBalanceAmount;
                        } else {
                            refundRateForm = getProductAmount(productAccountEntity, refundBillProductEntity.TotalAmount);
                        }
                    } else {
                        refundRateForm = getProductAmount(productAccountEntity, refundBillProductEntity.TotalAmount);
                    }

                    BigDecimal payAmount = refundBillProductEntity.TotalAmount.subtract(refundRateForm.savingCardDeductionAmount).subtract(refundRateForm.savingCardDeductionLargessAmount);
                    packagePayAmount = packagePayAmount.add(payAmount);
                    if (payAmount.compareTo(BigDecimal.ZERO) > 0) {
                        GoodAssignAmountEntity goodAssignAmountEntity = new GoodAssignAmountEntity();
                        goodAssignAmountEntity.Type = 1;
                        goodAssignAmountEntity.SaleBillGoodsID = saleBillPackageCardProductEntity.ID;
                        goodAssignAmountEntity.GoodsID = refundBillProductEntity.ProductID;
                        goodAssignAmountEntity.UnassignedAmount = payAmount;
                        goodAssignAmountEntity.UnassignedCardPreferentialAmount = BigDecimal.ZERO;
                        goodAssignAmountEntity.UnassignedCardDeductionAmount = BigDecimal.ZERO;
                        packageCardGoodAssignAmountEntities.add(goodAssignAmountEntity);
                    }

                    BigDecimal savingCardDeductionAmount = refundRateForm.savingCardDeductionAmount;
                    BigDecimal savingCardDeductionLargessAmount = refundRateForm.savingCardDeductionLargessAmount;
                    List<RefundBillPaySavingCardGoodsForm> saleBillPaySavingCardProduct = saleBillPayMapper.getSaleBillPayPackageCardSavingCardProduct(productAccountEntity.SaleBillPackageCardProductID,productAccountEntity.ID);
                    BigDecimal amount = BigDecimal.ZERO;
                    BigDecimal largessAmount = BigDecimal.ZERO;
                    for (int i = 0; i < saleBillPaySavingCardProduct.size(); i++) {
                        RefundBillPaySavingCardGoodsForm newRefundBillPaySavingCardGoodsForm = new RefundBillPaySavingCardGoodsForm();
                        newRefundBillPaySavingCardGoodsForm.SavingCardAccountID = saleBillPaySavingCardProduct.get(i).SavingCardAccountID;
                        newRefundBillPaySavingCardGoodsForm.Name = saleBillPaySavingCardProduct.get(i).Name;
                        if (i == saleBillPaySavingCardProduct.size() - 1) {
                            newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.subtract(amount);
                            newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.subtract(largessAmount);
                        } else {
                            newRefundBillPaySavingCardGoodsForm.Amount = BigDecimal.ZERO;
                            newRefundBillPaySavingCardGoodsForm.LargessAmount = BigDecimal.ZERO;
                            if (productAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
                                newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.multiply(saleBillPaySavingCardProduct.get(i).Amount).divide(productAccountEntity.SavingCardDeductionAmount.multiply(new BigDecimal(saleBillPaySavingCardProduct.get(i).Quantity)), 2, BigDecimal.ROUND_HALF_UP);
                                amount = amount.add(newRefundBillPaySavingCardGoodsForm.Amount);
                            }
                            if (productAccountEntity.SavingCardDeductionLargessAmount.compareTo(BigDecimal.ZERO) > 0) {
                                newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.multiply(saleBillPaySavingCardProduct.get(i).LargessAmount).divide(productAccountEntity.SavingCardDeductionLargessAmount.multiply(new BigDecimal(saleBillPaySavingCardProduct.get(i).Quantity)), 2, BigDecimal.ROUND_HALF_UP);
                                largessAmount = largessAmount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);
                            }
                        }
                        newRefundBillPaySavingCardGoodsForm.TotalAmount = newRefundBillPaySavingCardGoodsForm.Amount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);

                        SaleBillPaySavingCardDeductionGoodsExtendEntity saleBillPaySavingCardDeductionGoodsEntity = new SaleBillPaySavingCardDeductionGoodsExtendEntity();
                        saleBillPaySavingCardDeductionGoodsEntity.SaleBillPackageCardGoodsID = saleBillPackageCardEntity.ID;
                        saleBillPaySavingCardDeductionGoodsEntity.SaleBillGoodsID = saleBillPackageCardProductEntity.ID;
                        saleBillPaySavingCardDeductionGoodsEntity.SavingCardAccountID = newRefundBillPaySavingCardGoodsForm.SavingCardAccountID;
                        saleBillPaySavingCardDeductionGoodsEntity.TotalAmount = newRefundBillPaySavingCardGoodsForm.TotalAmount;
                        saleBillPaySavingCardDeductionGoodsEntity.Amount = newRefundBillPaySavingCardGoodsForm.Amount;
                        saleBillPaySavingCardDeductionGoodsEntity.LargessAmount = newRefundBillPaySavingCardGoodsForm.LargessAmount;
                        saleBillPaySavingCardDeductionGoodsEntity.PreferentialAmount = BigDecimal.ZERO;
                        saleBillPaySavingCardDeductionGoodsEntity.LargessPreferentialAmount = BigDecimal.ZERO;
                        packageCardSavingCardDeductionProduct.add(saleBillPaySavingCardDeductionGoodsEntity);

                    }


                    SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity = new SaleEntityPerformancePackageCardGoodsEntity();
                    saleEntityPerformancePackageCardGoodsEntity.Type = 1;
                    saleEntityPerformancePackageCardGoodsEntity.SaleBillID = saleBillEntity.ID;//订单编号
                    saleEntityPerformancePackageCardGoodsEntity.EntityID = saleBillEntity.EntityID;//门店ID
                    saleEntityPerformancePackageCardGoodsEntity.BillDate = saleBillEntity.BillDate;//开单时间
                    saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID = saleBillPackageCardEntity.ID;
                    saleEntityPerformancePackageCardGoodsEntity.PackageCardID = saleBillPackageCardEntity.PackageCardID;
                    saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID = saleBillPackageCardProductEntity.ID;//商品明细ID
                    saleEntityPerformancePackageCardGoodsEntity.GoodsID = saleBillPackageCardProductEntity.ProductID;//商品ID
                    saleEntityPerformancePackageCardGoodsEntity.Scale = new BigDecimal(100);
                    List<SaleBillHandlerForm> saleBillHandlerFormList = new ArrayList<>();
                    for (RefundBillHandlerPackageCardEntity handlerPackageCardEntity : refundBillHandlerPackageCard) {
                        SaleBillHandlerForm saleBillHandlerForm = new SaleBillHandlerForm();
                        saleBillHandlerForm.EmployeeID = handlerPackageCardEntity.EmployeeID;
                        saleBillHandlerForm.SaleHandlerID = handlerPackageCardEntity.PackageCardSaleHandlerID;
                        saleBillHandlerForm.Scale = handlerPackageCardEntity.Scale.multiply(new BigDecimal(100).setScale(2, BigDecimal.ROUND_HALF_UP));
                        saleBillHandlerFormList.add(saleBillHandlerForm);
                    }
                    saleEntityPerformancePackageCardGoodsEntity.SaleBillHandlerEntityList = saleBillHandlerFormList;
                    saleEntityPerformancePackageCardGoodsEntity.PackageCardAccountID = PackageCardAccountID;
                    saleEntityPerformancePackageCardGoodsEntity.Quantity = saleBillPackageCardProductEntity.Quantity;
                    saleEntityPerformancePackageCardGoodsEntity.PackageCardQuantity = saleBillPackageCardProductEntity.PackageQuantity;
                    saleEntityPerformancePackageCardGoodsEntity.Price = saleBillPackageCardProductEntity.PackagePrice;
                    saleEntityPerformancePackageCardGoodsEntity.TotalAmount = saleBillPackageCardProductEntity.TotalAmount;
                    saleEntityPerformancePackageCardGoodsEntity.AccountTotalAmount = productAccountEntity.TotalAmount;
                    saleEntityPerformancePackageCardGoodsEntity.AccountQuantity = productAccountEntity.Quantity;
                    saleEntityPerformancePackageCardGoodsEntity.ArrearAmount = saleBillPackageCardProductEntity.ArrearAmount;
                    saleEntityPerformancePackageCardGoodsEntity.IsLargess = saleBillPackageCardProductEntity.IsLargess;
                    if (!saleBillPackageCardProductEntity.IsLargess) {
                        saleEntityPerformancePackageCardGoodsEntities.add(saleEntityPerformancePackageCardGoodsEntity);
                    }

                    //更新账户
                    if (refundBillProductEntity.PricePreferentialAmount.compareTo(BigDecimal.ZERO) == 1) {
                        refundRateForm = getProductAmount(productAccountEntity, refundBillProductEntity.TotalAmount.add(refundBillProductEntity.PricePreferentialAmount));
                    }
                    productAccountEntity.Balance = productAccountEntity.Balance - refundBillProductEntity.Quantity;
                    productAccountEntity.PayPerformanceBalanceAmount = productAccountEntity.PayPerformanceBalanceAmount.subtract(refundRateForm.payAmount);
                    productAccountEntity.CardPerformanceBalanceAmount = productAccountEntity.CardPerformanceBalanceAmount.subtract(refundRateForm.savingCardDeductionAmount);
                    productAccountEntity.CardLargessPerformanceBalanceAmount = productAccountEntity.CardLargessPerformanceBalanceAmount.subtract(refundRateForm.savingCardDeductionLargessAmount);
                    productAccountEntity.LargessPerformanceBalanceAmount = productAccountEntity.LargessPerformanceBalanceAmount.subtract(productAccountEntity.IsLargess ? refundBillProductEntity.Price.multiply(new BigDecimal(refundBillProductEntity.Quantity)) : BigDecimal.ZERO);
                    if (productAccountEntity.Balance.equals(0) && productAccountEntity.ArrearAmount.compareTo(BigDecimal.ZERO) > 0) {
                        productAccountEntity.ArrearAmount = BigDecimal.ZERO;
                    }
                    productAccountMapper.updateProductAccountBalance(productAccountEntity);
                }
                //添加套餐卡下的项目
                for (RefundBillProjectEntity refundBillProjectEntity : refundPackageCardProject) {
                    ProjectAccountEntity projectAccountEntity = projectAccountMapper.getProjectAccountByAccountID(refundBillProjectEntity.ProjectAccountID);
                    if (!projectAccountEntity.Active) {
                        throw new RuntimeException("购买当前项目的销售订单已被取消，请撤销或驳回当前退款单据");
                    }
                    if (projectAccountEntity.Balance.intValue() < refundBillProjectEntity.Quantity.intValue()) {
                        throw new RuntimeException("项目剩余次数不足");
                    }
                    SaleBillPackageCardProjectEntity saleBillPackageCardProjectEntity = new SaleBillPackageCardProjectEntity();
                    saleBillPackageCardProjectEntity.SaleBillPackageCardID = saleBillPackageCardEntity.ID;
                    saleBillPackageCardProjectEntity.ProjectID = refundBillProjectEntity.ProjectID;
                    saleBillPackageCardProjectEntity.Quantity = refundBillProjectEntity.Quantity;
                    saleBillPackageCardProjectEntity.PackageQuantity = 1;
                    saleBillPackageCardProjectEntity.PackagePrice = refundBillProjectEntity.Price;
                    saleBillPackageCardProjectEntity.PackageTotalPrice = refundBillProjectEntity.Price.multiply(new BigDecimal(refundBillProjectEntity.Quantity)).setScale(2, BigDecimal.ROUND_HALF_UP);
                    saleBillPackageCardProjectEntity.TotalAmount = refundBillProjectEntity.TotalAmount;
                    saleBillPackageCardProjectEntity.ArrearAmount = BigDecimal.ZERO;
                    saleBillPackageCardProjectEntity.PricePreferentialAmount = refundBillProjectEntity.PricePreferentialAmount;
                    saleBillPackageCardProjectEntity.MemberPreferentialAmount = BigDecimal.ZERO;
                    saleBillPackageCardProjectEntity.CardPreferentialAmount = BigDecimal.ZERO;
                    saleBillPackageCardProjectEntity.IsLargess = projectAccountEntity.IsLargess;
                    saleBillPackageCardProjectEntity.ProjectAccountID = refundBillProjectEntity.ProjectAccountID;
                    saleBillMapper.createSaleBillPackageCardProject(saleBillPackageCardProjectEntity);


                    RefundRateForm refundRateForm = new RefundRateForm();
                    BigDecimal projectPricePreferentialAmountTotal = refundBillMapper.getProjectPricePreferentialAmountTotal(projectAccountEntity.ID);//优惠金额合计
                    if (refundBillProjectEntity.PricePreferentialAmount.compareTo(BigDecimal.ZERO) == 0 && projectPricePreferentialAmountTotal.compareTo(BigDecimal.ZERO) == 0) {
                        if (projectAccountEntity.Balance.equals(refundBillProjectEntity.Quantity)) {
                            refundRateForm.payAmount = projectAccountEntity.PayPerformanceBalanceAmount;
                            refundRateForm.savingCardDeductionAmount = projectAccountEntity.CardPerformanceBalanceAmount;
                            refundRateForm.savingCardDeductionLargessAmount = projectAccountEntity.CardLargessPerformanceBalanceAmount;
                        } else {
                            refundRateForm = getProjectAmount(projectAccountEntity, refundBillProjectEntity.TotalAmount);
                        }
                    } else {
                        refundRateForm = getProjectAmount(projectAccountEntity, refundBillProjectEntity.TotalAmount);
                    }

                    BigDecimal payAmount = refundBillProjectEntity.TotalAmount.subtract(refundRateForm.savingCardDeductionAmount).subtract(refundRateForm.savingCardDeductionLargessAmount);
                    packagePayAmount = packagePayAmount.add(payAmount);
                    if (payAmount.compareTo(BigDecimal.ZERO) > 0) {
                        GoodAssignAmountEntity goodAssignAmountEntity = new GoodAssignAmountEntity();
                        goodAssignAmountEntity.Type = 2;
                        goodAssignAmountEntity.SaleBillGoodsID = saleBillPackageCardProjectEntity.ID;
                        goodAssignAmountEntity.GoodsID = refundBillProjectEntity.ID;
                        goodAssignAmountEntity.UnassignedAmount = payAmount;
                        goodAssignAmountEntity.UnassignedCardPreferentialAmount = BigDecimal.ZERO;
                        goodAssignAmountEntity.UnassignedCardDeductionAmount = BigDecimal.ZERO;
                        packageCardGoodAssignAmountEntities.add(goodAssignAmountEntity);
                    }

                    BigDecimal savingCardDeductionAmount = refundRateForm.savingCardDeductionAmount;
                    BigDecimal savingCardDeductionLargessAmount = refundRateForm.savingCardDeductionLargessAmount;
                    List<RefundBillPaySavingCardGoodsForm> saleBillPaySavingCardProject = saleBillPayMapper.getSaleBillPayPackageCardSavingCardProject(projectAccountEntity.SaleBillPackageCardProjectID,projectAccountEntity.ID);
                    BigDecimal amount = BigDecimal.ZERO;
                    BigDecimal largessAmount = BigDecimal.ZERO;
                    for (int i = 0; i < saleBillPaySavingCardProject.size(); i++) {
                        RefundBillPaySavingCardGoodsForm newRefundBillPaySavingCardGoodsForm = new RefundBillPaySavingCardGoodsForm();
                        newRefundBillPaySavingCardGoodsForm.SavingCardAccountID = saleBillPaySavingCardProject.get(i).SavingCardAccountID;
                        newRefundBillPaySavingCardGoodsForm.Name = saleBillPaySavingCardProject.get(i).Name;
                        if (i == saleBillPaySavingCardProject.size() - 1) {
                            newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.subtract(amount);
                            newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.subtract(largessAmount);
                        } else {
                            newRefundBillPaySavingCardGoodsForm.Amount = BigDecimal.ZERO;
                            newRefundBillPaySavingCardGoodsForm.LargessAmount = BigDecimal.ZERO;
                            if (projectAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
                                newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.multiply(saleBillPaySavingCardProject.get(i).Amount).divide(projectAccountEntity.SavingCardDeductionAmount.multiply(new BigDecimal(saleBillPaySavingCardProject.get(i).Quantity)), 2, BigDecimal.ROUND_HALF_UP);
                                amount = amount.add(newRefundBillPaySavingCardGoodsForm.Amount);
                            }
                            if (projectAccountEntity.SavingCardDeductionLargessAmount.compareTo(BigDecimal.ZERO) > 0) {
                                newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.multiply(saleBillPaySavingCardProject.get(i).LargessAmount).divide(projectAccountEntity.SavingCardDeductionLargessAmount.multiply(new BigDecimal(saleBillPaySavingCardProject.get(i).Quantity)), 2, BigDecimal.ROUND_HALF_UP);
                                largessAmount = largessAmount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);
                            }
                        }
                        newRefundBillPaySavingCardGoodsForm.TotalAmount = newRefundBillPaySavingCardGoodsForm.Amount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);

                        SaleBillPaySavingCardDeductionGoodsExtendEntity saleBillPaySavingCardDeductionGoodsEntity = new SaleBillPaySavingCardDeductionGoodsExtendEntity();
                        saleBillPaySavingCardDeductionGoodsEntity.SaleBillPackageCardGoodsID = saleBillPackageCardEntity.ID;
                        saleBillPaySavingCardDeductionGoodsEntity.SaleBillGoodsID = saleBillPackageCardProjectEntity.ID;
                        saleBillPaySavingCardDeductionGoodsEntity.SavingCardAccountID = newRefundBillPaySavingCardGoodsForm.SavingCardAccountID;
                        saleBillPaySavingCardDeductionGoodsEntity.TotalAmount = newRefundBillPaySavingCardGoodsForm.TotalAmount;
                        saleBillPaySavingCardDeductionGoodsEntity.Amount = newRefundBillPaySavingCardGoodsForm.Amount;
                        saleBillPaySavingCardDeductionGoodsEntity.LargessAmount = newRefundBillPaySavingCardGoodsForm.LargessAmount;
                        saleBillPaySavingCardDeductionGoodsEntity.PreferentialAmount = BigDecimal.ZERO;
                        saleBillPaySavingCardDeductionGoodsEntity.LargessPreferentialAmount = BigDecimal.ZERO;
                        packageCardSavingCardDeductionProject.add(saleBillPaySavingCardDeductionGoodsEntity);
                    }


                    SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity = new SaleEntityPerformancePackageCardGoodsEntity();
                    saleEntityPerformancePackageCardGoodsEntity.Type = 2;
                    saleEntityPerformancePackageCardGoodsEntity.SaleBillID = saleBillEntity.ID;//订单编号
                    saleEntityPerformancePackageCardGoodsEntity.EntityID = saleBillEntity.EntityID;//门店ID
                    saleEntityPerformancePackageCardGoodsEntity.BillDate = saleBillEntity.BillDate;//开单时间
                    saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID = saleBillPackageCardEntity.ID;
                    saleEntityPerformancePackageCardGoodsEntity.PackageCardID = saleBillPackageCardEntity.PackageCardID;
                    saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID = saleBillPackageCardProjectEntity.ID;//商品明细ID
                    saleEntityPerformancePackageCardGoodsEntity.GoodsID = saleBillPackageCardProjectEntity.ProjectID;//商品ID
                    saleEntityPerformancePackageCardGoodsEntity.Scale = new BigDecimal(100);
                    List<SaleBillHandlerForm> saleBillHandlerFormList = new ArrayList<>();
                    for (RefundBillHandlerPackageCardEntity handlerPackageCardEntity : refundBillHandlerPackageCard) {
                        SaleBillHandlerForm saleBillHandlerForm = new SaleBillHandlerForm();
                        saleBillHandlerForm.EmployeeID = handlerPackageCardEntity.EmployeeID;
                        saleBillHandlerForm.SaleHandlerID = handlerPackageCardEntity.PackageCardSaleHandlerID;
                        saleBillHandlerForm.Scale = handlerPackageCardEntity.Scale.multiply(new BigDecimal(100).setScale(2, BigDecimal.ROUND_HALF_UP));
                        saleBillHandlerFormList.add(saleBillHandlerForm);
                    }
                    saleEntityPerformancePackageCardGoodsEntity.SaleBillHandlerEntityList = saleBillHandlerFormList;
                    saleEntityPerformancePackageCardGoodsEntity.PackageCardAccountID = PackageCardAccountID;
                    saleEntityPerformancePackageCardGoodsEntity.Quantity = saleBillPackageCardProjectEntity.Quantity;
                    saleEntityPerformancePackageCardGoodsEntity.PackageCardQuantity = saleBillPackageCardProjectEntity.PackageQuantity;
                    saleEntityPerformancePackageCardGoodsEntity.Price = saleBillPackageCardProjectEntity.PackagePrice;
                    saleEntityPerformancePackageCardGoodsEntity.TotalAmount = saleBillPackageCardProjectEntity.TotalAmount;
                    saleEntityPerformancePackageCardGoodsEntity.AccountTotalAmount = projectAccountEntity.TotalAmount;
                    saleEntityPerformancePackageCardGoodsEntity.AccountQuantity = projectAccountEntity.Quantity;
                    saleEntityPerformancePackageCardGoodsEntity.ArrearAmount = saleBillPackageCardProjectEntity.ArrearAmount;
                    saleEntityPerformancePackageCardGoodsEntity.IsLargess = saleBillPackageCardProjectEntity.IsLargess;
                    if (!saleBillPackageCardProjectEntity.IsLargess) {
                        saleEntityPerformancePackageCardGoodsEntities.add(saleEntityPerformancePackageCardGoodsEntity);
                    }

                    //更新账户
                    if (refundBillProjectEntity.PricePreferentialAmount.compareTo(BigDecimal.ZERO) == 1) {
                        refundRateForm = getProjectAmount(projectAccountEntity, refundBillProjectEntity.TotalAmount.add(refundBillProjectEntity.PricePreferentialAmount));
                    }
                    projectAccountEntity.Balance = projectAccountEntity.Balance - refundBillProjectEntity.Quantity;
                    projectAccountEntity.PayPerformanceBalanceAmount = projectAccountEntity.PayPerformanceBalanceAmount.subtract(refundRateForm.payAmount);
                    projectAccountEntity.CardPerformanceBalanceAmount = projectAccountEntity.CardPerformanceBalanceAmount.subtract(refundRateForm.savingCardDeductionAmount);
                    projectAccountEntity.CardLargessPerformanceBalanceAmount = projectAccountEntity.CardLargessPerformanceBalanceAmount.subtract(refundRateForm.savingCardDeductionLargessAmount);
                    projectAccountEntity.LargessPerformanceBalanceAmount = projectAccountEntity.LargessPerformanceBalanceAmount.subtract(projectAccountEntity.IsLargess ? refundBillProjectEntity.Price.multiply(new BigDecimal(refundBillProjectEntity.Quantity)) : BigDecimal.ZERO);
                    if (projectAccountEntity.Balance.equals(0) && projectAccountEntity.ArrearAmount.compareTo(BigDecimal.ZERO) > 0) {
                        projectAccountEntity.ArrearAmount = BigDecimal.ZERO;
                    }
                    projectAccountMapper.updateProjectAccountBalance(projectAccountEntity);

                }

                //添加套餐卡下的通用次卡
                for (RefundBillGeneralCardEntity refundBillGeneralCardEntity : refundPackageCardGeneralCard) {
                    GeneralCardAccountEntity generalCardAccountEntity = generalCardAccountMapper.getGeneralCardAccountByAccountID(refundBillGeneralCardEntity.GeneralCardAccountID);
                    if (!generalCardAccountEntity.Active) {
                        throw new RuntimeException("购买当前通用次卡的销售订单已被取消，请撤销或驳回当前退款单据");
                    }
                    if (generalCardAccountEntity.Balance.intValue() < refundBillGeneralCardEntity.Quantity.intValue()) {
                        throw new RuntimeException("通用次卡剩余次数不足");
                    }
                    SaleBillPackageCardGeneralCardEntity saleBillPackageCardGeneralCardEntity = new SaleBillPackageCardGeneralCardEntity();
                    saleBillPackageCardGeneralCardEntity.SaleBillPackageCardID = saleBillPackageCardEntity.ID;
                    saleBillPackageCardGeneralCardEntity.GeneralCardID = refundBillGeneralCardEntity.GeneralCardID;
                    saleBillPackageCardGeneralCardEntity.CardTimes = refundBillGeneralCardEntity.Quantity;
                    saleBillPackageCardGeneralCardEntity.Quantity = 1;
                    saleBillPackageCardGeneralCardEntity.PackageQuantity = 1;
                    saleBillPackageCardGeneralCardEntity.PackagePrice = refundBillGeneralCardEntity.Price;
                    saleBillPackageCardGeneralCardEntity.PackageTotalPrice = refundBillGeneralCardEntity.Price.multiply(new BigDecimal(refundBillGeneralCardEntity.Quantity)).setScale(2, BigDecimal.ROUND_HALF_UP);
                    saleBillPackageCardGeneralCardEntity.TotalAmount = refundBillGeneralCardEntity.TotalAmount;
                    saleBillPackageCardGeneralCardEntity.ArrearAmount = BigDecimal.ZERO;
                    saleBillPackageCardGeneralCardEntity.PricePreferentialAmount = refundBillGeneralCardEntity.PricePreferentialAmount;
                    saleBillPackageCardGeneralCardEntity.MemberPreferentialAmount = BigDecimal.ZERO;
                    saleBillPackageCardGeneralCardEntity.CardPreferentialAmount = BigDecimal.ZERO;
                    saleBillPackageCardGeneralCardEntity.IsLargess = generalCardAccountEntity.IsLargess;
                    saleBillPackageCardGeneralCardEntity.GeneralCardAccountID = refundBillGeneralCardEntity.GeneralCardAccountID;
                    saleBillMapper.createSaleBillPackageCardGeneralCard(saleBillPackageCardGeneralCardEntity);

                    RefundRateForm refundRateForm = new RefundRateForm();
                    BigDecimal generalCardPricePreferentialAmountTotal = refundBillMapper.getGeneralCardPricePreferentialAmountTotal(generalCardAccountEntity.ID);//优惠金额合计
                    if (refundBillGeneralCardEntity.PricePreferentialAmount.compareTo(BigDecimal.ZERO) == 0 && generalCardPricePreferentialAmountTotal.compareTo(BigDecimal.ZERO) == 0) {
                        if (generalCardAccountEntity.Balance.equals(refundBillGeneralCardEntity.Quantity)) {
                            refundRateForm.payAmount = generalCardAccountEntity.PayPerformanceBalanceAmount;
                            refundRateForm.savingCardDeductionAmount = generalCardAccountEntity.CardPerformanceBalanceAmount;
                            refundRateForm.savingCardDeductionLargessAmount = generalCardAccountEntity.CardLargessPerformanceBalanceAmount;
                        } else {
                            refundRateForm = getGeneralCardAmount(generalCardAccountEntity, refundBillGeneralCardEntity.TotalAmount);
                        }
                    } else {
                        refundRateForm = getGeneralCardAmount(generalCardAccountEntity, refundBillGeneralCardEntity.TotalAmount);
                    }

                    BigDecimal payAmount = refundBillGeneralCardEntity.TotalAmount.subtract(refundRateForm.savingCardDeductionAmount).subtract(refundRateForm.savingCardDeductionLargessAmount);
                    packagePayAmount = packagePayAmount.add(payAmount);
                    if (payAmount.compareTo(BigDecimal.ZERO) > 0) {
                        GoodAssignAmountEntity goodAssignAmountEntity = new GoodAssignAmountEntity();
                        goodAssignAmountEntity.Type = 3;
                        goodAssignAmountEntity.SaleBillGoodsID = saleBillPackageCardGeneralCardEntity.ID;
                        goodAssignAmountEntity.GoodsID = refundBillGeneralCardEntity.GeneralCardID;
                        goodAssignAmountEntity.UnassignedAmount = payAmount;
                        goodAssignAmountEntity.UnassignedCardPreferentialAmount = BigDecimal.ZERO;
                        goodAssignAmountEntity.UnassignedCardDeductionAmount = BigDecimal.ZERO;
                        packageCardGoodAssignAmountEntities.add(goodAssignAmountEntity);
                    }

                    BigDecimal savingCardDeductionAmount = refundRateForm.savingCardDeductionAmount;
                    BigDecimal savingCardDeductionLargessAmount = refundRateForm.savingCardDeductionLargessAmount;
                    List<RefundBillPaySavingCardGoodsForm> saleBillPaySavingCardGeneralCard = saleBillPayMapper.getSaleBillPayPackageCardSavingCardGeneralCard(generalCardAccountEntity.SaleBillPackageCardGeneralCardID,generalCardAccountEntity.ID);
                    BigDecimal amount = BigDecimal.ZERO;
                    BigDecimal largessAmount = BigDecimal.ZERO;
                    for (int i = 0; i < saleBillPaySavingCardGeneralCard.size(); i++) {
                        RefundBillPaySavingCardGoodsForm newRefundBillPaySavingCardGoodsForm = new RefundBillPaySavingCardGoodsForm();
                        newRefundBillPaySavingCardGoodsForm.SavingCardAccountID = saleBillPaySavingCardGeneralCard.get(i).SavingCardAccountID;
                        newRefundBillPaySavingCardGoodsForm.Name = saleBillPaySavingCardGeneralCard.get(i).Name;
                        if (i == saleBillPaySavingCardGeneralCard.size() - 1) {
                            newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.subtract(amount);
                            newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.subtract(largessAmount);
                        } else {
                            newRefundBillPaySavingCardGoodsForm.Amount = BigDecimal.ZERO;
                            newRefundBillPaySavingCardGoodsForm.LargessAmount = BigDecimal.ZERO;
                            if (generalCardAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0 && saleBillPaySavingCardGeneralCard.get(i).Amount.compareTo(BigDecimal.ZERO) > 0) {
                                newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.multiply(saleBillPaySavingCardGeneralCard.get(i).Amount).divide(generalCardAccountEntity.SavingCardDeductionAmount.multiply(new BigDecimal(saleBillPaySavingCardGeneralCard.get(i).Quantity)), 2, BigDecimal.ROUND_HALF_UP);
                                amount = amount.add(newRefundBillPaySavingCardGoodsForm.Amount);
                            }
                            if (generalCardAccountEntity.SavingCardDeductionLargessAmount.compareTo(BigDecimal.ZERO) > 0 && saleBillPaySavingCardGeneralCard.get(i).LargessAmount.compareTo(BigDecimal.ZERO) > 0) {
                                newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.multiply(saleBillPaySavingCardGeneralCard.get(i).LargessAmount).divide(generalCardAccountEntity.SavingCardDeductionLargessAmount.multiply(new BigDecimal(saleBillPaySavingCardGeneralCard.get(i).Quantity)), 2, BigDecimal.ROUND_HALF_UP);
                                largessAmount = largessAmount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);
                            }
                        }
                        newRefundBillPaySavingCardGoodsForm.TotalAmount = newRefundBillPaySavingCardGoodsForm.Amount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);

                        SaleBillPaySavingCardDeductionGoodsExtendEntity saleBillPaySavingCardDeductionGoodsEntity = new SaleBillPaySavingCardDeductionGoodsExtendEntity();
                        saleBillPaySavingCardDeductionGoodsEntity.SaleBillPackageCardGoodsID = saleBillPackageCardEntity.ID;
                        saleBillPaySavingCardDeductionGoodsEntity.SaleBillGoodsID = saleBillPackageCardGeneralCardEntity.ID;
                        saleBillPaySavingCardDeductionGoodsEntity.SavingCardAccountID = newRefundBillPaySavingCardGoodsForm.SavingCardAccountID;
                        saleBillPaySavingCardDeductionGoodsEntity.TotalAmount = newRefundBillPaySavingCardGoodsForm.TotalAmount;
                        saleBillPaySavingCardDeductionGoodsEntity.Amount = newRefundBillPaySavingCardGoodsForm.Amount;
                        saleBillPaySavingCardDeductionGoodsEntity.LargessAmount = newRefundBillPaySavingCardGoodsForm.LargessAmount;
                        saleBillPaySavingCardDeductionGoodsEntity.PreferentialAmount = BigDecimal.ZERO;
                        saleBillPaySavingCardDeductionGoodsEntity.LargessPreferentialAmount = BigDecimal.ZERO;
                        packageCardSavingCardDeductionGeneralCard.add(saleBillPaySavingCardDeductionGoodsEntity);
                    }

                    SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity = new SaleEntityPerformancePackageCardGoodsEntity();
                    saleEntityPerformancePackageCardGoodsEntity.Type = 3;
                    saleEntityPerformancePackageCardGoodsEntity.SaleBillID = saleBillEntity.ID;//订单编号
                    saleEntityPerformancePackageCardGoodsEntity.EntityID = saleBillEntity.EntityID;//门店ID
                    saleEntityPerformancePackageCardGoodsEntity.BillDate = saleBillEntity.BillDate;//开单时间
                    saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID = saleBillPackageCardEntity.ID;
                    saleEntityPerformancePackageCardGoodsEntity.PackageCardID = saleBillPackageCardEntity.PackageCardID;
                    saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID = saleBillPackageCardGeneralCardEntity.ID;//商品明细ID
                    saleEntityPerformancePackageCardGoodsEntity.GoodsID = saleBillPackageCardGeneralCardEntity.GeneralCardID;//商品ID
                    saleEntityPerformancePackageCardGoodsEntity.Scale = new BigDecimal(100);
                    List<SaleBillHandlerForm> saleBillHandlerFormList = new ArrayList<>();
                    for (RefundBillHandlerPackageCardEntity handlerPackageCardEntity : refundBillHandlerPackageCard) {
                        SaleBillHandlerForm saleBillHandlerForm = new SaleBillHandlerForm();
                        saleBillHandlerForm.EmployeeID = handlerPackageCardEntity.EmployeeID;
                        saleBillHandlerForm.SaleHandlerID = handlerPackageCardEntity.PackageCardSaleHandlerID;
                        saleBillHandlerForm.Scale = handlerPackageCardEntity.Scale.multiply(new BigDecimal(100).setScale(2, BigDecimal.ROUND_HALF_UP));
                        saleBillHandlerFormList.add(saleBillHandlerForm);
                    }
                    saleEntityPerformancePackageCardGoodsEntity.SaleBillHandlerEntityList = saleBillHandlerFormList;
                    saleEntityPerformancePackageCardGoodsEntity.PackageCardAccountID = PackageCardAccountID;
                    saleEntityPerformancePackageCardGoodsEntity.Quantity = saleBillPackageCardGeneralCardEntity.Quantity;
                    saleEntityPerformancePackageCardGoodsEntity.PackageCardQuantity = saleBillPackageCardGeneralCardEntity.PackageQuantity;
                    saleEntityPerformancePackageCardGoodsEntity.Price = saleBillPackageCardGeneralCardEntity.PackagePrice;
                    saleEntityPerformancePackageCardGoodsEntity.TotalAmount = saleBillPackageCardGeneralCardEntity.TotalAmount;
                    saleEntityPerformancePackageCardGoodsEntity.AccountTotalAmount = generalCardAccountEntity.TotalAmount;
                    saleEntityPerformancePackageCardGoodsEntity.AccountQuantity = 1;
                    saleEntityPerformancePackageCardGoodsEntity.ArrearAmount = saleBillPackageCardGeneralCardEntity.ArrearAmount;
                    saleEntityPerformancePackageCardGoodsEntity.IsLargess = saleBillPackageCardGeneralCardEntity.IsLargess;
                    if (!saleBillPackageCardGeneralCardEntity.IsLargess) {
                        saleEntityPerformancePackageCardGoodsEntities.add(saleEntityPerformancePackageCardGoodsEntity);
                    }
                    //更新账户
                    if (refundBillGeneralCardEntity.PricePreferentialAmount.compareTo(BigDecimal.ZERO) == 1) {
                        refundRateForm = getGeneralCardAmount(generalCardAccountEntity, refundBillGeneralCardEntity.TotalAmount.add(refundBillGeneralCardEntity.PricePreferentialAmount));
                    }
                    generalCardAccountEntity.Balance = generalCardAccountEntity.Balance - refundBillGeneralCardEntity.Quantity;
                    generalCardAccountEntity.PayPerformanceBalanceAmount = generalCardAccountEntity.PayPerformanceBalanceAmount.subtract(refundRateForm.payAmount);
                    generalCardAccountEntity.CardPerformanceBalanceAmount = generalCardAccountEntity.CardPerformanceBalanceAmount.subtract(refundRateForm.savingCardDeductionAmount);
                    generalCardAccountEntity.CardLargessPerformanceBalanceAmount = generalCardAccountEntity.CardLargessPerformanceBalanceAmount.subtract(refundRateForm.savingCardDeductionLargessAmount);
                    generalCardAccountEntity.LargessPerformanceBalanceAmount = generalCardAccountEntity.LargessPerformanceBalanceAmount.subtract(generalCardAccountEntity.IsLargess ? generalCardAccountEntity.TotalAmount.multiply(new BigDecimal(refundBillGeneralCardEntity.Quantity)).divide(new BigDecimal(generalCardAccountEntity.CardTimes), 2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);
                    if (generalCardAccountEntity.Balance.equals(0) && generalCardAccountEntity.ArrearAmount.compareTo(BigDecimal.ZERO) > 0) {
                        generalCardAccountEntity.ArrearAmount = BigDecimal.ZERO;
                    }
                    generalCardAccountMapper.updateGeneralCardAccountBalance(generalCardAccountEntity);
                }

                //添加套餐卡下的时效卡
                for (RefundBillTimeCardEntity refundBillTimeCardEntity : refundPackageCardTimeCard) {
                    TimeCardAccountEntity timeCardAccountEntity = timeCardAccountMapper.getTimeCardAccountByAccountID(refundBillTimeCardEntity.TimeCardAccountID);
                    if (!timeCardAccountEntity.Active) {
                        throw new RuntimeException("购买当前时效卡的销售订单已被取消，请撤销或驳回当前退款单据");
                    }
                    SaleBillPackageCardTimeCardEntity saleBillPackageCardTimeCardEntity = new SaleBillPackageCardTimeCardEntity();
                    saleBillPackageCardTimeCardEntity.SaleBillPackageCardID = saleBillPackageCardEntity.ID;
                    saleBillPackageCardTimeCardEntity.TimeCardID = refundBillTimeCardEntity.TimeCardID;
                    saleBillPackageCardTimeCardEntity.Quantity = 1;
                    saleBillPackageCardTimeCardEntity.PackageQuantity = 1;
                    saleBillPackageCardTimeCardEntity.PackagePrice = refundBillTimeCardEntity.Price;
                    saleBillPackageCardTimeCardEntity.PackageTotalPrice = refundBillTimeCardEntity.Price;
                    saleBillPackageCardTimeCardEntity.TotalAmount = refundBillTimeCardEntity.TotalAmount;
                    saleBillPackageCardTimeCardEntity.ArrearAmount = BigDecimal.ZERO;
                    saleBillPackageCardTimeCardEntity.PricePreferentialAmount = refundBillTimeCardEntity.PricePreferentialAmount;
                    saleBillPackageCardTimeCardEntity.MemberPreferentialAmount = BigDecimal.ZERO;
                    saleBillPackageCardTimeCardEntity.CardPreferentialAmount = BigDecimal.ZERO;
                    saleBillPackageCardTimeCardEntity.IsLargess = timeCardAccountEntity.IsLargess;
                    saleBillPackageCardTimeCardEntity.TimeCardAccountID = refundBillTimeCardEntity.TimeCardAccountID;
                    saleBillMapper.createSaleBillPackageCardTimeCard(saleBillPackageCardTimeCardEntity);

                    RefundRateForm refundRateForm = new RefundRateForm();
                    if (refundBillTimeCardEntity.PricePreferentialAmount.compareTo(BigDecimal.ZERO) == 0) {
                        refundRateForm.payAmount = timeCardAccountEntity.PayPerformanceBalanceAmount;
                        refundRateForm.savingCardDeductionAmount = timeCardAccountEntity.CardPerformanceBalanceAmount;
                        refundRateForm.savingCardDeductionLargessAmount = timeCardAccountEntity.CardLargessPerformanceBalanceAmount;
                    } else {
                        refundRateForm = getTimeCardAmount(timeCardAccountEntity, refundBillTimeCardEntity.TotalAmount);
                    }
                    BigDecimal payAmount = refundBillTimeCardEntity.TotalAmount.subtract(refundRateForm.savingCardDeductionAmount).subtract(refundRateForm.savingCardDeductionLargessAmount);
                    packagePayAmount = packagePayAmount.add(payAmount);
                    if (payAmount.compareTo(BigDecimal.ZERO) > 0) {
                        GoodAssignAmountEntity goodAssignAmountEntity = new GoodAssignAmountEntity();
                        goodAssignAmountEntity.Type = 4;
                        goodAssignAmountEntity.SaleBillGoodsID = saleBillPackageCardTimeCardEntity.ID;
                        goodAssignAmountEntity.GoodsID = refundBillTimeCardEntity.TimeCardID;
                        goodAssignAmountEntity.UnassignedAmount = payAmount;
                        goodAssignAmountEntity.UnassignedCardPreferentialAmount = BigDecimal.ZERO;
                        goodAssignAmountEntity.UnassignedCardDeductionAmount = BigDecimal.ZERO;
                        packageCardGoodAssignAmountEntities.add(goodAssignAmountEntity);
                    }

                    BigDecimal savingCardDeductionAmount = refundRateForm.savingCardDeductionAmount;
                    BigDecimal savingCardDeductionLargessAmount = refundRateForm.savingCardDeductionLargessAmount;
                    List<RefundBillPaySavingCardGoodsForm> saleBillPaySavingCardTimeCard = saleBillPayMapper.getSaleBillPayPackageCardSavingCardTimeCard(timeCardAccountEntity.SaleBillPackageCardTimeCardID,timeCardAccountEntity.ID);
                    BigDecimal amount = BigDecimal.ZERO;
                    BigDecimal largessAmount = BigDecimal.ZERO;
                    for (int i = 0; i < saleBillPaySavingCardTimeCard.size(); i++) {
                        RefundBillPaySavingCardGoodsForm newRefundBillPaySavingCardGoodsForm = new RefundBillPaySavingCardGoodsForm();
                        newRefundBillPaySavingCardGoodsForm.SavingCardAccountID = saleBillPaySavingCardTimeCard.get(i).SavingCardAccountID;
                        newRefundBillPaySavingCardGoodsForm.Name = saleBillPaySavingCardTimeCard.get(i).Name;
                        if (i == saleBillPaySavingCardTimeCard.size() - 1) {
                            newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.subtract(amount);
                            newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.subtract(largessAmount);
                        } else {
                            newRefundBillPaySavingCardGoodsForm.Amount = BigDecimal.ZERO;
                            newRefundBillPaySavingCardGoodsForm.LargessAmount = BigDecimal.ZERO;
                            if (timeCardAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0 && saleBillPaySavingCardTimeCard.get(i).Amount.compareTo(BigDecimal.ZERO) > 0) {
                                newRefundBillPaySavingCardGoodsForm.Amount = savingCardDeductionAmount.multiply(saleBillPaySavingCardTimeCard.get(i).Amount).divide(timeCardAccountEntity.SavingCardDeductionAmount.multiply(new BigDecimal(saleBillPaySavingCardTimeCard.get(i).Quantity)), 2, BigDecimal.ROUND_HALF_UP);
                                amount = amount.add(newRefundBillPaySavingCardGoodsForm.Amount);
                            }
                            if (timeCardAccountEntity.SavingCardDeductionLargessAmount.compareTo(BigDecimal.ZERO) > 0 && saleBillPaySavingCardTimeCard.get(i).LargessAmount.compareTo(BigDecimal.ZERO) > 0) {
                                newRefundBillPaySavingCardGoodsForm.LargessAmount = savingCardDeductionLargessAmount.multiply(saleBillPaySavingCardTimeCard.get(i).LargessAmount).divide(timeCardAccountEntity.SavingCardDeductionLargessAmount.multiply(new BigDecimal(saleBillPaySavingCardTimeCard.get(i).Quantity)), 2, BigDecimal.ROUND_HALF_UP);
                                largessAmount = largessAmount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);
                            }
                        }
                        newRefundBillPaySavingCardGoodsForm.TotalAmount = newRefundBillPaySavingCardGoodsForm.Amount.add(newRefundBillPaySavingCardGoodsForm.LargessAmount);

                        SaleBillPaySavingCardDeductionGoodsExtendEntity saleBillPaySavingCardDeductionGoodsEntity = new SaleBillPaySavingCardDeductionGoodsExtendEntity();
                        saleBillPaySavingCardDeductionGoodsEntity.SaleBillPackageCardGoodsID = saleBillPackageCardEntity.ID;
                        saleBillPaySavingCardDeductionGoodsEntity.SaleBillGoodsID = saleBillPackageCardTimeCardEntity.ID;
                        saleBillPaySavingCardDeductionGoodsEntity.SavingCardAccountID = newRefundBillPaySavingCardGoodsForm.SavingCardAccountID;
                        saleBillPaySavingCardDeductionGoodsEntity.TotalAmount = newRefundBillPaySavingCardGoodsForm.TotalAmount;
                        saleBillPaySavingCardDeductionGoodsEntity.Amount = newRefundBillPaySavingCardGoodsForm.Amount;
                        saleBillPaySavingCardDeductionGoodsEntity.LargessAmount = newRefundBillPaySavingCardGoodsForm.LargessAmount;
                        saleBillPaySavingCardDeductionGoodsEntity.PreferentialAmount = BigDecimal.ZERO;
                        saleBillPaySavingCardDeductionGoodsEntity.LargessPreferentialAmount = BigDecimal.ZERO;
                        packageCardSavingCardDeductionTimeCard.add(saleBillPaySavingCardDeductionGoodsEntity);
                    }


                    SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity = new SaleEntityPerformancePackageCardGoodsEntity();
                    saleEntityPerformancePackageCardGoodsEntity.Type = 4;
                    saleEntityPerformancePackageCardGoodsEntity.SaleBillID = saleBillEntity.ID;//订单编号
                    saleEntityPerformancePackageCardGoodsEntity.EntityID = saleBillEntity.EntityID;//门店ID
                    saleEntityPerformancePackageCardGoodsEntity.BillDate = saleBillEntity.BillDate;//开单时间
                    saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID = saleBillPackageCardEntity.ID;
                    saleEntityPerformancePackageCardGoodsEntity.PackageCardID = saleBillPackageCardEntity.PackageCardID;
                    saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID = saleBillPackageCardTimeCardEntity.ID;//商品明细ID
                    saleEntityPerformancePackageCardGoodsEntity.GoodsID = saleBillPackageCardTimeCardEntity.TimeCardID;//商品ID
                    saleEntityPerformancePackageCardGoodsEntity.Scale = new BigDecimal(100);
                    List<SaleBillHandlerForm> saleBillHandlerFormList = new ArrayList<>();
                    for (RefundBillHandlerPackageCardEntity handlerPackageCardEntity : refundBillHandlerPackageCard) {
                        SaleBillHandlerForm saleBillHandlerForm = new SaleBillHandlerForm();
                        saleBillHandlerForm.EmployeeID = handlerPackageCardEntity.EmployeeID;
                        saleBillHandlerForm.SaleHandlerID = handlerPackageCardEntity.PackageCardSaleHandlerID;
                        saleBillHandlerForm.Scale = handlerPackageCardEntity.Scale.multiply(new BigDecimal(100).setScale(2, BigDecimal.ROUND_HALF_UP));
                        saleBillHandlerFormList.add(saleBillHandlerForm);
                    }
                    saleEntityPerformancePackageCardGoodsEntity.SaleBillHandlerEntityList = saleBillHandlerFormList;
                    saleEntityPerformancePackageCardGoodsEntity.PackageCardAccountID = PackageCardAccountID;
                    saleEntityPerformancePackageCardGoodsEntity.Quantity = saleBillPackageCardTimeCardEntity.Quantity;
                    saleEntityPerformancePackageCardGoodsEntity.PackageCardQuantity = saleBillPackageCardTimeCardEntity.PackageQuantity;
                    saleEntityPerformancePackageCardGoodsEntity.Price = saleBillPackageCardTimeCardEntity.PackagePrice;
                    saleEntityPerformancePackageCardGoodsEntity.TotalAmount = saleBillPackageCardTimeCardEntity.TotalAmount;
                    saleEntityPerformancePackageCardGoodsEntity.AccountTotalAmount = timeCardAccountEntity.TotalAmount;
                    saleEntityPerformancePackageCardGoodsEntity.AccountQuantity = 1;
                    saleEntityPerformancePackageCardGoodsEntity.ArrearAmount = saleBillPackageCardTimeCardEntity.ArrearAmount;
                    saleEntityPerformancePackageCardGoodsEntity.IsLargess = saleBillPackageCardTimeCardEntity.IsLargess;
                    if (!saleBillPackageCardTimeCardEntity.IsLargess) {
                        saleEntityPerformancePackageCardGoodsEntities.add(saleEntityPerformancePackageCardGoodsEntity);
                    }
                    //更新账户
                    if (refundBillTimeCardEntity.PricePreferentialAmount.compareTo(BigDecimal.ZERO) == 1) {
                        refundRateForm = getTimeCardAmount(timeCardAccountEntity, refundBillTimeCardEntity.TotalAmount.add(refundBillTimeCardEntity.PricePreferentialAmount));
                    }
                    timeCardAccountEntity.Active = false;
                    timeCardAccountEntity.PayPerformanceBalanceAmount = timeCardAccountEntity.PayPerformanceBalanceAmount.subtract(refundRateForm.payAmount);
                    timeCardAccountEntity.CardPerformanceBalanceAmount = timeCardAccountEntity.CardPerformanceBalanceAmount.subtract(refundRateForm.savingCardDeductionAmount);
                    timeCardAccountEntity.CardLargessPerformanceBalanceAmount = timeCardAccountEntity.CardLargessPerformanceBalanceAmount.subtract(refundRateForm.savingCardDeductionLargessAmount);
                    timeCardAccountEntity.LargessPerformanceBalanceAmount = timeCardAccountEntity.LargessPerformanceBalanceAmount.subtract(timeCardAccountEntity.IsLargess ? refundBillTimeCardEntity.Price : BigDecimal.ZERO);
                    timeCardAccountMapper.updateTimeCardAccount(timeCardAccountEntity);
                }

                //添加套餐卡下的储值卡
                for (RefundBillSavingCardEntity refundBillSavingCardEntity : refundPackageCardSavingCard) {
                    SavingCardAccountExtendEntity savingCardAccountEntity = savingCardAccountMapper.getSavingCardAccountByID(refundBillSavingCardEntity.SavingCardAccountID);
                    if (!savingCardAccountEntity.Active) {
                        throw new RuntimeException("购买当前储值卡的销售订单已被取消，请撤销或驳回当前退款单据");
                    }
                    SaleBillPackageCardSavingCardEntity saleBillPackageCardSavingCardEntity = new SaleBillPackageCardSavingCardEntity();
                    saleBillPackageCardSavingCardEntity.SaleBillPackageCardID = saleBillPackageCardEntity.ID;
                    saleBillPackageCardSavingCardEntity.SavingCardID = refundBillSavingCardEntity.SavingCardID;
                    saleBillPackageCardSavingCardEntity.Quantity = 1;
                    saleBillPackageCardSavingCardEntity.PackageQuantity = 1;
                    saleBillPackageCardSavingCardEntity.Amount = refundBillSavingCardEntity.Amount;
                    saleBillPackageCardSavingCardEntity.LargessAmount = refundBillSavingCardEntity.LargessAmount;
                    saleBillPackageCardSavingCardEntity.ArrearAmount = BigDecimal.ZERO;
                    saleBillPackageCardSavingCardEntity.SavingCardAccountID = refundBillSavingCardEntity.SavingCardAccountID;
                    if (savingCardAccountEntity.Amount.compareTo(BigDecimal.ZERO) > 0) {
                        saleBillPackageCardSavingCardEntity.TotalPrice = refundBillSavingCardEntity.Amount;
                        saleBillPackageCardSavingCardEntity.IsLargess = false;
                    } else {
                        saleBillPackageCardSavingCardEntity.TotalPrice = refundBillSavingCardEntity.LargessAmount;
                        saleBillPackageCardSavingCardEntity.IsLargess = true;
                    }
                    saleBillPackageCardSavingCardEntity.TotalAmount = saleBillPackageCardSavingCardEntity.IsLargess ? BigDecimal.ZERO : refundBillSavingCardEntity.Amount;
                    saleBillMapper.createSaleBillPackageCardSavingCard(saleBillPackageCardSavingCardEntity);

                    //未分配明细
                    packagePayAmount = packagePayAmount.add(refundBillSavingCardEntity.Amount);
                    GoodAssignAmountEntity goodAssignAmountEntity = new GoodAssignAmountEntity();
                    goodAssignAmountEntity.Type = 5;
                    goodAssignAmountEntity.SaleBillGoodsID = saleBillPackageCardSavingCardEntity.ID;
                    goodAssignAmountEntity.GoodsID = refundBillSavingCardEntity.SavingCardID;
                    goodAssignAmountEntity.UnassignedAmount = refundBillSavingCardEntity.Amount;
                    packageCardSavingCardAssignAmountEntities.add(goodAssignAmountEntity);

                    SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity = new SaleEntityPerformancePackageCardGoodsEntity();
                    saleEntityPerformancePackageCardGoodsEntity.Type = 5;
                    saleEntityPerformancePackageCardGoodsEntity.SaleBillID = saleBillEntity.ID;//订单编号
                    saleEntityPerformancePackageCardGoodsEntity.EntityID = saleBillEntity.EntityID;//门店ID
                    saleEntityPerformancePackageCardGoodsEntity.BillDate = saleBillEntity.BillDate;//开单时间
                    saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID = saleBillPackageCardEntity.ID;
                    saleEntityPerformancePackageCardGoodsEntity.PackageCardID = saleBillPackageCardEntity.PackageCardID;
                    saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID = saleBillPackageCardSavingCardEntity.ID;//商品明细ID
                    saleEntityPerformancePackageCardGoodsEntity.GoodsID = saleBillPackageCardSavingCardEntity.SavingCardID;//商品ID
                    List<SaleBillHandlerForm> saleBillHandlerFormList = new ArrayList<>();
                    for (RefundBillHandlerPackageCardEntity handlerPackageCardEntity : refundBillHandlerPackageCard) {
                        SaleBillHandlerForm saleBillHandlerForm = new SaleBillHandlerForm();
                        saleBillHandlerForm.EmployeeID = handlerPackageCardEntity.EmployeeID;
                        saleBillHandlerForm.SaleHandlerID = handlerPackageCardEntity.PackageCardSaleHandlerID;
                        saleBillHandlerForm.Scale = handlerPackageCardEntity.Scale.multiply(new BigDecimal(100).setScale(2, BigDecimal.ROUND_HALF_UP));
                        saleBillHandlerFormList.add(saleBillHandlerForm);
                    }
                    saleEntityPerformancePackageCardGoodsEntity.SaleBillHandlerEntityList = saleBillHandlerFormList;
                    saleEntityPerformancePackageCardGoodsEntity.PackageCardAccountID = PackageCardAccountID;
                    saleEntityPerformancePackageCardGoodsEntity.Quantity = saleBillPackageCardSavingCardEntity.Quantity;
                    saleEntityPerformancePackageCardGoodsEntity.PackageCardQuantity = saleBillPackageCardSavingCardEntity.PackageQuantity;
                    saleEntityPerformancePackageCardGoodsEntity.Price = saleBillPackageCardSavingCardEntity.Amount;
                    saleEntityPerformancePackageCardGoodsEntity.TotalAmount = saleBillPackageCardSavingCardEntity.TotalPrice;
                    saleEntityPerformancePackageCardGoodsEntity.ArrearAmount = saleBillPackageCardSavingCardEntity.ArrearAmount;
                    saleEntityPerformancePackageCardGoodsEntity.LargessAmount = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
                    saleEntityPerformancePackageCardGoodsEntity.IsLargess = saleBillPackageCardSavingCardEntity.IsLargess;
                    saleEntityPerformancePackageCardGoodsEntity.Scale = new BigDecimal(100);
                    if (!saleBillPackageCardSavingCardEntity.IsLargess) {
                        saleEntityPerformancePackageCardGoodsEntities.add(saleEntityPerformancePackageCardGoodsEntity);
                    }
                    //更新账户
                    savingCardAccountEntity.Balance = savingCardAccountEntity.Balance.subtract(refundBillSavingCardEntity.Amount);
                    savingCardAccountEntity.LargessBalance = savingCardAccountEntity.LargessBalance.subtract(refundBillSavingCardEntity.LargessAmount);
                    if (refundBillSavingCardEntity.Amount.compareTo(savingCardAccountEntity.AllowRefundBalance) == 0 && refundBillSavingCardEntity.LargessAmount.compareTo(savingCardAccountEntity.AllowRefundLargessBalance) == 0 && savingCardAccountEntity.ArrearAmount.compareTo(BigDecimal.ZERO) > 0) {
                        savingCardAccountEntity.Balance = BigDecimal.ZERO;
                        savingCardAccountEntity.LargessBalance = BigDecimal.ZERO;
                        savingCardAccountEntity.ArrearAmount = BigDecimal.ZERO;
                    }
                    savingCardAccountMapper.updateSavingCardAccountBalance(savingCardAccountEntity);
                }
                //未分配套餐卡明细放入套餐信息中，最终分配套餐卡下的明细支付
                packageCardGoodAssignAmountEntity.SaleBillPackageCardID = saleBillPackageCardEntity.ID;
                packageCardGoodAssignAmountEntity.PackageCardID = saleBillPackageCardEntity.PackageCardID;
                packageCardGoodAssignAmountEntity.packageCardGoodAssignAmountEntities = packageCardGoodAssignAmountEntities;
                packageCardGoodAssignAmountEntity.packageCardSavingCardAssignAmountEntities = packageCardSavingCardAssignAmountEntities;
                packageCardGoodsAll.add(packageCardGoodAssignAmountEntity);

                if (refundBillPackageCardEntity.Amount.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal packageSavingCardAmount = BigDecimal.ZERO;//套餐卡储值卡金额
                    if (refundPackageCardSavingCard.size() > 0) {
                        packageSavingCardAmount = refundPackageCardSavingCard.stream().map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
                    }
                    if (packageSavingCardAmount.compareTo(BigDecimal.ZERO) > 0) {
                        GoodAssignAmountEntity entity = new GoodAssignAmountEntity();
                        entity.SaleBillGoodsID = saleBillPackageCardEntity.ID;
                        entity.GoodsID = saleBillPackageCardEntity.PackageCardID;
                        entity.Type = 6;
                        entity.UnassignedAmount = packageSavingCardAmount;
                        savingCardAssignAmountEntities.add(entity);
                    }
                    if (refundBillPackageCardEntity.Amount.compareTo(packageSavingCardAmount) > 0) {
                        GoodAssignAmountEntity entity = new GoodAssignAmountEntity();
                        entity.SaleBillGoodsID = saleBillPackageCardEntity.ID;
                        entity.GoodsID = saleBillPackageCardEntity.PackageCardID;
                        entity.Type = 6;
                        entity.UnassignedAmount = packagePayAmount.subtract(packageSavingCardAmount);
                        goodsAssignAmountEntities.add(entity);
                    }
                }
            }
            packageCardSavingCardDeduction.addAll(packageCardSavingCardDeductionProduct);
            packageCardSavingCardDeduction.addAll(packageCardSavingCardDeductionProject);
            packageCardSavingCardDeduction.addAll(packageCardSavingCardDeductionGeneralCard);
            packageCardSavingCardDeduction.addAll(packageCardSavingCardDeductionTimeCard);

            List<SaleBillPaySavingCardDeductionGoodsEntity> saleBillSavingCardDeductionFormsGroupByType = new ArrayList<>();
            packageCardSavingCardDeduction.stream().collect(Collectors.groupingBy(i -> new HashMap() {
                {
                    put("SaleBillPackageCardGoodsID", i.SaleBillPackageCardGoodsID);
                    put("SavingCardAccountID", i.SavingCardAccountID);
                }
            })).forEach((key, list) -> {
                SaleBillPaySavingCardDeductionGoodsEntity saleBillPaySavingCardDeductionGoodsEntity = new SaleBillPaySavingCardDeductionGoodsEntity();
                saleBillPaySavingCardDeductionGoodsEntity.SaleBillGoodsID = Integer.parseInt(key.get("SaleBillPackageCardGoodsID").toString());
                saleBillPaySavingCardDeductionGoodsEntity.SavingCardAccountID = Integer.parseInt(key.get("SavingCardAccountID").toString());
                saleBillPaySavingCardDeductionGoodsEntity.Amount = list.stream().map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add);
                saleBillPaySavingCardDeductionGoodsEntity.LargessAmount = list.stream().map(i -> i.LargessAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                saleBillPaySavingCardDeductionGoodsEntity.TotalAmount = list.stream().map(i -> i.TotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                saleBillPaySavingCardDeductionGoodsEntity.PreferentialAmount = BigDecimal.ZERO;
                saleBillPaySavingCardDeductionGoodsEntity.LargessPreferentialAmount = BigDecimal.ZERO;
                saleBillSavingCardDeductionFormsGroupByType.add(saleBillPaySavingCardDeductionGoodsEntity);
            });
            List<SaleBillPaySavingCardDeductionGoodsEntity> packageCardSavingCardDeductionNewProduct = packageCardSavingCardDeductionProduct.stream().collect(Collectors.toList());
            List<SaleBillPaySavingCardDeductionGoodsEntity> packageCardSavingCardDeductionNewProject = packageCardSavingCardDeductionProject.stream().collect(Collectors.toList());
            List<SaleBillPaySavingCardDeductionGoodsEntity> packageCardSavingCardDeductionNewGeneralCard = packageCardSavingCardDeductionGeneralCard.stream().collect(Collectors.toList());
            List<SaleBillPaySavingCardDeductionGoodsEntity> packageCardSavingCardDeductionNewTimeCard = packageCardSavingCardDeductionTimeCard.stream().collect(Collectors.toList());


            if (productSavingCardDeduction.size() > 0)
                saleBillPayMapper.createSaleBillPaySavingCardDeductionProduct(productSavingCardDeduction);

            if (projectSavingCardDeduction.size() > 0)
                saleBillPayMapper.createSaleBillPaySavingCardDeductionProject(projectSavingCardDeduction);

            if (generalCardSavingCardDeduction.size() > 0)
                saleBillPayMapper.createSaleBillPaySavingCardDeductionGeneralCard(generalCardSavingCardDeduction);

            if (timeCardSavingCardDeduction.size() > 0)
                saleBillPayMapper.createSaleBillPaySavingCardDeductionTimeCard(timeCardSavingCardDeduction);

            if (saleBillSavingCardDeductionFormsGroupByType.size() > 0)
                saleBillPayMapper.createSaleBillPaySavingCardDeductionPackageCard(saleBillSavingCardDeductionFormsGroupByType);

            if (packageCardSavingCardDeductionProduct.size() > 0)
                saleBillPayMapper.createSaleBillPaySavingCardDeductionPackageCardProduct(packageCardSavingCardDeductionNewProduct);

            if (packageCardSavingCardDeductionProject.size() > 0)
                saleBillPayMapper.createSaleBillPaySavingCardDeductionPackageCardProject(packageCardSavingCardDeductionNewProject);

            if (packageCardSavingCardDeductionGeneralCard.size() > 0)
                saleBillPayMapper.createSaleBillPaySavingCardDeductionPackageCardGeneralCard(packageCardSavingCardDeductionNewGeneralCard);

            if (packageCardSavingCardDeductionTimeCard.size() > 0)
                saleBillPayMapper.createSaleBillPaySavingCardDeductionPackageCardTimeCard(packageCardSavingCardDeductionNewTimeCard);


            //更新储值卡账户
            for (RefundBillPaySavingCardDeductionEntity refundBillPaySavingCardDeductionEntity : savingCardDeduction) {
                SavingCardAccountExtendEntity savingCardAccountByID = savingCardAccountMapper.getSavingCardAccountByID(refundBillPaySavingCardDeductionEntity.SavingCardAccountID);
                savingCardAccountByID.Balance = savingCardAccountByID.Balance.add(refundBillPaySavingCardDeductionEntity.Amount);
                savingCardAccountByID.LargessBalance = savingCardAccountByID.LargessBalance.add(refundBillPaySavingCardDeductionEntity.LargessAmount);
                savingCardAccountMapper.updateSavingCardAccountBalance(savingCardAccountByID);
            }

            //分配商品现金支付明细
            List<SaleBillPayGoodsEntity> packageCard = new ArrayList<>();
            List<SaleBillPayGoodsEntity> product = new ArrayList<>();
            List<SaleBillPayGoodsEntity> project = new ArrayList<>();
            List<SaleBillPayGoodsEntity> generalCard = new ArrayList<>();
            List<SaleBillPayGoodsEntity> timeCard = new ArrayList<>();
            List<SaleBillPayGoodsEntity> savingCard = new ArrayList<>();
            if (saleBillPayEntities.size() > 0) {
                //合并现金抵扣（非储值卡现金+储值卡现金）
                goodsAssignAmountEntities.addAll(savingCardAssignAmountEntities);
                List<GoodAssignAmountEntity> goodAssignCashAmountEntities = new ArrayList<>();

                goodsAssignAmountEntities.stream().filter(i -> i.UnassignedAmount.compareTo(BigDecimal.ZERO) > 0).collect(Collectors.groupingBy(i -> new HashMap() {{
                    put("GoodsID", i.SaleBillGoodsID);
                    put("Type", i.Type);
                }})).forEach((key, list) -> {
                    GoodAssignAmountEntity goodAssignCashAmountEntity = new GoodAssignAmountEntity();
                    goodAssignCashAmountEntity.SaleBillGoodsID = Integer.parseInt(key.get("GoodsID").toString());
                    goodAssignCashAmountEntity.Type = Integer.parseInt(key.get("Type").toString());
                    goodAssignCashAmountEntity.UnassignedAmount = list.stream().map(i -> i.UnassignedAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
                    goodAssignCashAmountEntities.add(goodAssignCashAmountEntity);
                });

                List<SaleBillPayGoodsEntity> saleBillPayGoodsEntities = new ArrayList<>();
                for (SaleBillPayEntity saleBillPayEntity : saleBillPayEntities) {
                    BigDecimal goodsAssignTotalCashAmount = goodAssignCashAmountEntities.stream().map(i -> i.UnassignedAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (!payMethod.get(payMethod.size() - 1).equals(saleBillPayEntity)) {//不是最后一个支付方式
                        for (GoodAssignAmountEntity goodAssignCashAmountEntity : goodAssignCashAmountEntities) {
                            SaleBillPayGoodsEntity saleBillPayGoodsEntity = new SaleBillPayGoodsEntity();
                            if (!goodAssignCashAmountEntities.get(goodAssignCashAmountEntities.size() - 1).equals(goodAssignCashAmountEntity)) {//不是最后一个商品
                                saleBillPayGoodsEntity.SaleBillPayID = saleBillPayEntity.ID;
                                saleBillPayGoodsEntity.Type = goodAssignCashAmountEntity.Type;
                                saleBillPayGoodsEntity.SaleBillGoodsID = goodAssignCashAmountEntity.SaleBillGoodsID;
                                saleBillPayGoodsEntity.PayMethodID = saleBillPayEntity.PayMethodID;
                                saleBillPayGoodsEntity.Amount = saleBillPayEntity.Amount.multiply(goodAssignCashAmountEntity.UnassignedAmount).divide(goodsAssignTotalCashAmount, 2, BigDecimal.ROUND_HALF_UP);
                            } else {//最后一个商品
                                saleBillPayGoodsEntity.SaleBillPayID = saleBillPayEntity.ID;
                                saleBillPayGoodsEntity.Type = goodAssignCashAmountEntity.Type;
                                saleBillPayGoodsEntity.SaleBillGoodsID = goodAssignCashAmountEntity.SaleBillGoodsID;
                                saleBillPayGoodsEntity.PayMethodID = saleBillPayEntity.PayMethodID;
                                saleBillPayGoodsEntity.Amount = saleBillPayEntity.Amount;
                            }
                            saleBillPayGoodsEntities.add(saleBillPayGoodsEntity);
                            saleBillPayEntity.Amount = saleBillPayEntity.Amount.subtract(saleBillPayGoodsEntity.Amount);
                            goodsAssignTotalCashAmount = goodsAssignTotalCashAmount.subtract(goodAssignCashAmountEntity.UnassignedAmount);
                            goodAssignCashAmountEntity.UnassignedAmount = goodAssignCashAmountEntity.UnassignedAmount.subtract(saleBillPayGoodsEntity.Amount);
                        }
                    } else {//最后一个支付方式
                        for (GoodAssignAmountEntity goodAssignCashAmountEntity : goodAssignCashAmountEntities) {
                            SaleBillPayGoodsEntity saleBillPayGoodsEntity = new SaleBillPayGoodsEntity();
                            saleBillPayGoodsEntity.SaleBillPayID = saleBillPayEntity.ID;
                            saleBillPayGoodsEntity.Type = goodAssignCashAmountEntity.Type;
                            saleBillPayGoodsEntity.SaleBillGoodsID = goodAssignCashAmountEntity.SaleBillGoodsID;
                            saleBillPayGoodsEntity.PayMethodID = saleBillPayEntity.PayMethodID;
                            saleBillPayGoodsEntity.Amount = goodAssignCashAmountEntity.UnassignedAmount;
                            saleBillPayGoodsEntities.add(saleBillPayGoodsEntity);
                        }
                    }
                }

                //插入商品现金支付明细
                if (saleBillPayGoodsEntities.size() > 0) {

                    for (SaleBillPayGoodsEntity payGoodsEntity : saleBillPayGoodsEntities) {

                        switch (payGoodsEntity.Type) {
                            case 1:
                                product.add(payGoodsEntity);
                                break;
                            case 2:
                                project.add(payGoodsEntity);
                                break;
                            case 3:
                                generalCard.add(payGoodsEntity);
                                break;
                            case 4:
                                timeCard.add(payGoodsEntity);
                                break;
                            case 5:
                                savingCard.add(payGoodsEntity);
                                break;
                            case 6:
                                packageCard.add(payGoodsEntity);
                                break;
                        }
                    }
                    if (product != null && product.size() > 0) {
                        saleBillPayMapper.createSaleBillPayProduct(product);
                    }
                    if (project != null && project.size() > 0) {
                        saleBillPayMapper.createSaleBillPayProject(project);
                    }
                    if (generalCard != null && generalCard.size() > 0) {
                        saleBillPayMapper.createSaleBillPayGeneralCard(generalCard);
                    }
                    if (timeCard != null && timeCard.size() > 0) {
                        saleBillPayMapper.createSaleBillPayTimeCard(timeCard);
                    }
                    if (packageCard != null && packageCard.size() > 0) {
                        saleBillPayMapper.createSaleBillPayPackageCard(packageCard);
                    }
                    if (savingCard != null && savingCard.size() > 0) {
                        saleBillPayMapper.createSaleBillPaySavingCard(savingCard);
                    }
                }
            }
            //存入套餐卡的支付明细
            packageCardGoodsAll.forEach(i -> {
                i.packageCardCashAmount = packageCard.stream().filter(paySavingCardDeductionGoodsEntity -> paySavingCardDeductionGoodsEntity.SaleBillGoodsID.equals(i.SaleBillPackageCardID)).collect(Collectors.toList());
            });
            List<SaleBillPayGoodsEntity> packageCardPayGoodsList = new ArrayList<>();
            for (PackageCardGoodAssignAmountEntity packageCardGoodAssignAmountEntity : packageCardGoodsAll) {
                //分配套餐下的现金支付
                packageCardGoodAssignAmountEntity.packageCardGoodAssignAmountEntities.addAll(packageCardGoodAssignAmountEntity.packageCardSavingCardAssignAmountEntities);
                for (SaleBillPayGoodsEntity saleBillPayGoodsEntity : packageCardGoodAssignAmountEntity.packageCardCashAmount) {
                    BigDecimal totalAssignAmount = packageCardGoodAssignAmountEntity.packageCardGoodAssignAmountEntities.stream().map(i -> i.UnassignedAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    for (GoodAssignAmountEntity goodAssignAmountEntity : packageCardGoodAssignAmountEntity.packageCardGoodAssignAmountEntities) {
                        SaleBillPayGoodsEntity saleBillPayPackageCardGoodsEntity = new SaleBillPayGoodsEntity();
                        saleBillPayPackageCardGoodsEntity.SaleBillGoodsID = goodAssignAmountEntity.SaleBillGoodsID;
                        saleBillPayPackageCardGoodsEntity.SaleBillPayID = saleBillPayGoodsEntity.SaleBillPayID;
                        saleBillPayPackageCardGoodsEntity.PayMethodID = saleBillPayGoodsEntity.PayMethodID;
                        saleBillPayPackageCardGoodsEntity.Type = goodAssignAmountEntity.Type;

                        if (!goodAssignAmountEntity.equals(packageCardGoodAssignAmountEntity.packageCardGoodAssignAmountEntities.get(packageCardGoodAssignAmountEntity.packageCardGoodAssignAmountEntities.size() - 1))) {//不是最后一个商品
                            saleBillPayPackageCardGoodsEntity.Amount = goodAssignAmountEntity.UnassignedAmount.multiply(saleBillPayGoodsEntity.Amount).divide(totalAssignAmount, 2, BigDecimal.ROUND_HALF_UP).setScale(2, BigDecimal.ROUND_HALF_UP);
                        } else {//最后一个商品
                            saleBillPayPackageCardGoodsEntity.Amount = saleBillPayGoodsEntity.Amount.setScale(2, BigDecimal.ROUND_HALF_UP);
                        }
                        packageCardPayGoodsList.add(saleBillPayPackageCardGoodsEntity);
                        totalAssignAmount = totalAssignAmount.subtract(goodAssignAmountEntity.UnassignedAmount);
                        goodAssignAmountEntity.UnassignedAmount = goodAssignAmountEntity.UnassignedAmount.subtract(saleBillPayPackageCardGoodsEntity.Amount);
                        saleBillPayGoodsEntity.Amount = saleBillPayGoodsEntity.Amount.subtract(saleBillPayPackageCardGoodsEntity.Amount);
                    }
                }
            }

            //保存套餐卡明细现金抵扣
            List<SaleBillPayGoodsEntity> packageCardCashProduct = new ArrayList<>();
            List<SaleBillPayGoodsEntity> packageCardCashProject = new ArrayList<>();
            List<SaleBillPayGoodsEntity> packageCardCashGeneralCard = new ArrayList<>();
            List<SaleBillPayGoodsEntity> packageCardCashTimeCard = new ArrayList<>();
            List<SaleBillPayGoodsEntity> packageCardCashSavingCard = new ArrayList<>();

            if (packageCardPayGoodsList.size() > 0) {
                for (SaleBillPayGoodsEntity saleBillPayGoodsEntity : packageCardPayGoodsList) {

                    switch (saleBillPayGoodsEntity.Type) {
                        case 1:
                            packageCardCashProduct.add(saleBillPayGoodsEntity);
                            break;
                        case 2:
                            packageCardCashProject.add(saleBillPayGoodsEntity);
                            break;
                        case 3:
                            packageCardCashGeneralCard.add(saleBillPayGoodsEntity);
                            break;
                        case 4:
                            packageCardCashTimeCard.add(saleBillPayGoodsEntity);
                            break;
                        case 5:
                            packageCardCashSavingCard.add(saleBillPayGoodsEntity);
                            break;
                    }
                }

                if (packageCardCashProduct != null && packageCardCashProduct.size() > 0) {
                    saleBillPayMapper.createSaleBillPayPackageCardProduct(packageCardCashProduct);
                }
                if (packageCardCashProject != null && packageCardCashProject.size() > 0) {
                    saleBillPayMapper.createSaleBillPayPackageCardProject(packageCardCashProject);
                }
                if (packageCardCashGeneralCard != null && packageCardCashGeneralCard.size() > 0) {
                    saleBillPayMapper.createSaleBillPayPackageCardGeneralCard(packageCardCashGeneralCard);
                }
                if (packageCardCashTimeCard != null && packageCardCashTimeCard.size() > 0) {
                    saleBillPayMapper.createSaleBillPayPackageCardTimeCard(packageCardCashTimeCard);
                }
                if (packageCardCashSavingCard != null && packageCardCashSavingCard.size() > 0) {
                    saleBillPayMapper.createSaleBillPayPackageCardSavingCard(packageCardCashSavingCard);
                }
            }
            //创建门店业绩
            List<SaleEntityPerformanceGoodsEntity> saleEntityPerformanceProduct = new ArrayList<>();
            List<SaleEntityPerformanceGoodsEntity> saleEntityPerformanceProject = new ArrayList<>();
            List<SaleEntityPerformanceGoodsEntity> saleEntityPerformanceGeneralCard = new ArrayList<>();
            List<SaleEntityPerformanceGoodsEntity> saleEntityPerformanceTimeCard = new ArrayList<>();
            List<SaleEntityPerformanceGoodsEntity> saleEntityPerformanceSavingCard = new ArrayList<>();

            for (SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity : saleEntityPerformanceGoodsEntities) {
                //类型，1：产品 2：项目 3：次卡 4：时效卡 5：储值卡
                switch (saleEntityPerformanceGoodsEntity.Type) {
                    case 1:
                        SalePerformanceEntity productEntityPerformance = saleEntityPerformanceMapper.getSaleProductPerformance(saleEntityPerformanceGoodsEntity.EntityID, saleEntityPerformanceGoodsEntity.GoodsID);
                        saleEntityPerformanceGoodsEntity.OriginPayPerformance = product.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformanceGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformanceGoodsEntity.OriginSavingCardPerformance = productSavingCardDeduction.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformanceGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformanceGoodsEntity.OriginSavingCardLargessPerformance = productSavingCardDeduction.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformanceGoodsEntity.SaleBillGoodsID)).map(i -> i.LargessAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);

                        saleEntityPerformanceGoodsEntity.PayPerformance = product.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformanceGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(productEntityPerformance.PayRate == null ? BigDecimal.ZERO : productEntityPerformance.PayRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformanceGoodsEntity.SavingCardPerformance = productSavingCardDeduction.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformanceGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(productEntityPerformance.SavingCardRate == null ? BigDecimal.ZERO : productEntityPerformance.SavingCardRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformanceGoodsEntity.SavingCardLargessPerformance = productSavingCardDeduction.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformanceGoodsEntity.SaleBillGoodsID)).map(i -> i.LargessAmount).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(productEntityPerformance.SavingCardLargessRate == null ? BigDecimal.ZERO : productEntityPerformance.SavingCardLargessRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        if (!saleEntityPerformanceGoodsEntity.IsLargess) {
                            saleEntityPerformanceProduct.add(saleEntityPerformanceGoodsEntity);
                        }
                        break;
                    case 2:
                        SalePerformanceEntity projectEntityPerformance = saleEntityPerformanceMapper.getSaleProjectPerformance(saleEntityPerformanceGoodsEntity.EntityID, saleEntityPerformanceGoodsEntity.GoodsID);
                        saleEntityPerformanceGoodsEntity.OriginPayPerformance = project.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformanceGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformanceGoodsEntity.OriginSavingCardPerformance = projectSavingCardDeduction.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformanceGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformanceGoodsEntity.OriginSavingCardLargessPerformance = projectSavingCardDeduction.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformanceGoodsEntity.SaleBillGoodsID)).map(i -> i.LargessAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);

                        saleEntityPerformanceGoodsEntity.PayPerformance = project.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformanceGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(projectEntityPerformance.PayRate == null ? BigDecimal.ZERO : projectEntityPerformance.PayRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformanceGoodsEntity.SavingCardPerformance = projectSavingCardDeduction.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformanceGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(projectEntityPerformance.SavingCardRate == null ? BigDecimal.ZERO : projectEntityPerformance.SavingCardRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformanceGoodsEntity.SavingCardLargessPerformance = projectSavingCardDeduction.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformanceGoodsEntity.SaleBillGoodsID)).map(i -> i.LargessAmount).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(projectEntityPerformance.SavingCardLargessRate == null ? BigDecimal.ZERO : projectEntityPerformance.SavingCardLargessRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        if (!saleEntityPerformanceGoodsEntity.IsLargess) {
                            saleEntityPerformanceProject.add(saleEntityPerformanceGoodsEntity);
                        }
                        break;
                    case 3:
                        SalePerformanceEntity generalCardEntityPerformance = saleEntityPerformanceMapper.getSaleGeneralCardPerformance(saleEntityPerformanceGoodsEntity.EntityID, saleEntityPerformanceGoodsEntity.GoodsID);
                        saleEntityPerformanceGoodsEntity.OriginPayPerformance = generalCard.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformanceGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformanceGoodsEntity.OriginSavingCardPerformance = generalCardSavingCardDeduction.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformanceGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformanceGoodsEntity.OriginSavingCardLargessPerformance = generalCardSavingCardDeduction.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformanceGoodsEntity.SaleBillGoodsID)).map(i -> i.LargessAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);

                        saleEntityPerformanceGoodsEntity.PayPerformance = generalCard.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformanceGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(generalCardEntityPerformance.PayRate == null ? BigDecimal.ZERO : generalCardEntityPerformance.PayRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformanceGoodsEntity.SavingCardPerformance = generalCardSavingCardDeduction.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformanceGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(generalCardEntityPerformance.SavingCardRate == null ? BigDecimal.ZERO : generalCardEntityPerformance.SavingCardRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformanceGoodsEntity.SavingCardLargessPerformance = generalCardSavingCardDeduction.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformanceGoodsEntity.SaleBillGoodsID)).map(i -> i.LargessAmount).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(generalCardEntityPerformance.SavingCardLargessRate == null ? BigDecimal.ZERO : generalCardEntityPerformance.SavingCardLargessRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        if (!saleEntityPerformanceGoodsEntity.IsLargess) {
                            saleEntityPerformanceGeneralCard.add(saleEntityPerformanceGoodsEntity);
                        }
                        break;
                    case 4:
                        SalePerformanceEntity timeCardEntityPerformance = saleEntityPerformanceMapper.getSaleTimeCardPerformance(saleEntityPerformanceGoodsEntity.EntityID, saleEntityPerformanceGoodsEntity.GoodsID);
                        saleEntityPerformanceGoodsEntity.OriginPayPerformance = timeCard.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformanceGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformanceGoodsEntity.OriginSavingCardPerformance = timeCardSavingCardDeduction.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformanceGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformanceGoodsEntity.OriginSavingCardLargessPerformance = timeCardSavingCardDeduction.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformanceGoodsEntity.SaleBillGoodsID)).map(i -> i.LargessAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);

                        saleEntityPerformanceGoodsEntity.PayPerformance = timeCard.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformanceGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(timeCardEntityPerformance.PayRate == null ? BigDecimal.ZERO : timeCardEntityPerformance.PayRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformanceGoodsEntity.SavingCardPerformance = timeCardSavingCardDeduction.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformanceGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(timeCardEntityPerformance.SavingCardRate == null ? BigDecimal.ZERO : timeCardEntityPerformance.SavingCardRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformanceGoodsEntity.SavingCardLargessPerformance = timeCardSavingCardDeduction.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformanceGoodsEntity.SaleBillGoodsID)).map(i -> i.LargessAmount).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(timeCardEntityPerformance.SavingCardLargessRate == null ? BigDecimal.ZERO : timeCardEntityPerformance.SavingCardLargessRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        if (!saleEntityPerformanceGoodsEntity.IsLargess) {
                            saleEntityPerformanceTimeCard.add(saleEntityPerformanceGoodsEntity);
                        }
                        break;
                    case 5:
                        SalePerformanceEntity savingCardEntityPerformance = saleEntityPerformanceMapper.getSaleSavingCardPerformance(saleEntityPerformanceGoodsEntity.EntityID, saleEntityPerformanceGoodsEntity.GoodsID);
                        saleEntityPerformanceGoodsEntity.OriginPayPerformance = savingCard.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformanceGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformanceGoodsEntity.PayPerformance = savingCard.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformanceGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(savingCardEntityPerformance.PayRate == null ? BigDecimal.ZERO : savingCardEntityPerformance.PayRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        if (saleEntityPerformanceGoodsEntity.PayPerformance.compareTo(BigDecimal.ZERO) != 0) {
                            saleEntityPerformanceSavingCard.add(saleEntityPerformanceGoodsEntity);
                        }
                        break;
                }
            }
            //保存门店业绩
            if (saleEntityPerformanceProduct.size() > 0) {
                saleEntityPerformanceMapper.createSaleEntityPerformanceProduct(saleEntityPerformanceProduct);
            }
            if (saleEntityPerformanceProject.size() > 0) {
                saleEntityPerformanceMapper.createSaleEntityPerformanceProject(saleEntityPerformanceProject);
            }
            if (saleEntityPerformanceGeneralCard.size() > 0) {
                saleEntityPerformanceMapper.createSaleEntityPerformanceGeneralCard(saleEntityPerformanceGeneralCard);
            }
            if (saleEntityPerformanceTimeCard.size() > 0) {
                saleEntityPerformanceMapper.createSaleEntityPerformanceTimeCard(saleEntityPerformanceTimeCard);
            }
            if (saleEntityPerformanceSavingCard.size() > 0) {
                saleEntityPerformanceMapper.createSaleEntityPerformanceSavingCard(saleEntityPerformanceSavingCard);
            }

            //门店套餐卡明细业绩
            List<SaleEntityPerformancePackageCardGoodsEntity> saleEntityPerformancePackageCardProduct = new ArrayList<>();
            List<SaleEntityPerformancePackageCardGoodsEntity> saleEntityPerformancePackageCardProject = new ArrayList<>();
            List<SaleEntityPerformancePackageCardGoodsEntity> saleEntityPerformancePackageCardGeneralCard = new ArrayList<>();
            List<SaleEntityPerformancePackageCardGoodsEntity> saleEntityPerformancePackageCardTimeCard = new ArrayList<>();
            List<SaleEntityPerformancePackageCardGoodsEntity> saleEntityPerformancePackageCardSavingCard = new ArrayList<>();

            for (SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity : saleEntityPerformancePackageCardGoodsEntities) {
                switch (saleEntityPerformancePackageCardGoodsEntity.Type) {
                    case 1:
                        SalePerformanceEntity productEntityPerformance = saleEntityPerformanceMapper.getSaleProductPerformance(saleEntityPerformancePackageCardGoodsEntity.EntityID, saleEntityPerformancePackageCardGoodsEntity.GoodsID);
                        saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance = packageCardCashProduct.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardPerformance = packageCardSavingCardDeductionProduct.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardLargessPerformance = packageCardSavingCardDeductionProduct.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID)).map(i -> i.LargessAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);

                        saleEntityPerformancePackageCardGoodsEntity.PayPerformance = packageCardCashProduct.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(productEntityPerformance.PayRate == null ? BigDecimal.ZERO : productEntityPerformance.PayRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformancePackageCardGoodsEntity.SavingCardPerformance = packageCardSavingCardDeductionProduct.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(productEntityPerformance.SavingCardRate == null ? BigDecimal.ZERO : productEntityPerformance.SavingCardRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformancePackageCardGoodsEntity.SavingCardLargessPerformance = packageCardSavingCardDeductionProduct.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID)).map(i -> i.LargessAmount).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(productEntityPerformance.SavingCardLargessRate == null ? BigDecimal.ZERO : productEntityPerformance.SavingCardLargessRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformancePackageCardProduct.add(saleEntityPerformancePackageCardGoodsEntity);
                        break;
                    case 2:
                        SalePerformanceEntity projectEntityPerformance = saleEntityPerformanceMapper.getSaleProjectPerformance(saleEntityPerformancePackageCardGoodsEntity.EntityID, saleEntityPerformancePackageCardGoodsEntity.GoodsID);
                        saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance = packageCardCashProject.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardPerformance = packageCardSavingCardDeductionProject.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardLargessPerformance = packageCardSavingCardDeductionProject.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID)).map(i -> i.LargessAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);

                        saleEntityPerformancePackageCardGoodsEntity.PayPerformance = packageCardCashProject.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(projectEntityPerformance.PayRate == null ? BigDecimal.ZERO : projectEntityPerformance.PayRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformancePackageCardGoodsEntity.SavingCardPerformance = packageCardSavingCardDeductionProject.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(projectEntityPerformance.SavingCardRate == null ? BigDecimal.ZERO : projectEntityPerformance.SavingCardRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformancePackageCardGoodsEntity.SavingCardLargessPerformance = packageCardSavingCardDeductionProject.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID)).map(i -> i.LargessAmount).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(projectEntityPerformance.SavingCardLargessRate == null ? BigDecimal.ZERO : projectEntityPerformance.SavingCardLargessRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformancePackageCardProject.add(saleEntityPerformancePackageCardGoodsEntity);
                        break;
                    case 3:
                        SalePerformanceEntity generalCardEntityPerformance = saleEntityPerformanceMapper.getSaleGeneralCardPerformance(saleEntityPerformancePackageCardGoodsEntity.EntityID, saleEntityPerformancePackageCardGoodsEntity.GoodsID);
                        saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance = packageCardCashGeneralCard.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardPerformance = packageCardSavingCardDeductionGeneralCard.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardLargessPerformance = packageCardSavingCardDeductionGeneralCard.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID)).map(i -> i.LargessAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);

                        saleEntityPerformancePackageCardGoodsEntity.PayPerformance = packageCardCashGeneralCard.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(generalCardEntityPerformance.PayRate == null ? BigDecimal.ZERO : generalCardEntityPerformance.PayRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformancePackageCardGoodsEntity.SavingCardPerformance = packageCardSavingCardDeductionGeneralCard.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(generalCardEntityPerformance.SavingCardRate == null ? BigDecimal.ZERO : generalCardEntityPerformance.SavingCardRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformancePackageCardGoodsEntity.SavingCardLargessPerformance = packageCardSavingCardDeductionGeneralCard.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID)).map(i -> i.LargessAmount).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(generalCardEntityPerformance.SavingCardLargessRate == null ? BigDecimal.ZERO : generalCardEntityPerformance.SavingCardLargessRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformancePackageCardGeneralCard.add(saleEntityPerformancePackageCardGoodsEntity);
                        break;
                    case 4:
                        SalePerformanceEntity timeCardEntityPerformance = saleEntityPerformanceMapper.getSaleTimeCardPerformance(saleEntityPerformancePackageCardGoodsEntity.EntityID, saleEntityPerformancePackageCardGoodsEntity.GoodsID);
                        saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance = packageCardCashTimeCard.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardPerformance = packageCardSavingCardDeductionTimeCard.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardLargessPerformance = packageCardSavingCardDeductionTimeCard.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID)).map(i -> i.LargessAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);

                        saleEntityPerformancePackageCardGoodsEntity.PayPerformance = packageCardCashTimeCard.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(timeCardEntityPerformance.PayRate == null ? BigDecimal.ZERO : timeCardEntityPerformance.PayRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformancePackageCardGoodsEntity.SavingCardPerformance = packageCardSavingCardDeductionTimeCard.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(timeCardEntityPerformance.SavingCardRate == null ? BigDecimal.ZERO : timeCardEntityPerformance.SavingCardRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformancePackageCardGoodsEntity.SavingCardLargessPerformance = packageCardSavingCardDeductionTimeCard.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID)).map(i -> i.LargessAmount).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(timeCardEntityPerformance.SavingCardLargessRate == null ? BigDecimal.ZERO : timeCardEntityPerformance.SavingCardLargessRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformancePackageCardTimeCard.add(saleEntityPerformancePackageCardGoodsEntity);
                        break;
                    case 5:
                        SalePerformanceEntity savingCardEntityPerformance = saleEntityPerformanceMapper.getSaleSavingCardPerformance(saleEntityPerformancePackageCardGoodsEntity.EntityID, saleEntityPerformancePackageCardGoodsEntity.GoodsID);
                        saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance = packageCardCashSavingCard.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);

                        saleEntityPerformancePackageCardGoodsEntity.PayPerformance = packageCardCashSavingCard.stream().filter(i -> i.SaleBillGoodsID.equals(saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID)).map(i -> i.Amount).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(savingCardEntityPerformance.PayRate == null ? BigDecimal.ZERO : savingCardEntityPerformance.PayRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleEntityPerformancePackageCardSavingCard.add(saleEntityPerformancePackageCardGoodsEntity);
                        break;
                }
            }

            //保存门店业绩
            if (saleEntityPerformancePackageCardProduct.size() > 0) {
                saleEntityPerformanceMapper.createSaleEntityPerformancePackageCardProduct(saleEntityPerformancePackageCardProduct);
            }
            if (saleEntityPerformancePackageCardProject.size() > 0) {
                saleEntityPerformanceMapper.createSaleEntityPerformancePackageCardProject(saleEntityPerformancePackageCardProject);
            }
            if (saleEntityPerformancePackageCardGeneralCard.size() > 0) {
                saleEntityPerformanceMapper.createSaleEntityPerformancePackageCardGeneralCard(saleEntityPerformancePackageCardGeneralCard);
            }
            if (saleEntityPerformancePackageCardTimeCard.size() > 0) {
                saleEntityPerformanceMapper.createSaleEntityPerformancePackageCardTimeCard(saleEntityPerformancePackageCardTimeCard);
            }
            if (saleEntityPerformancePackageCardSavingCard.size() > 0) {
                saleEntityPerformanceMapper.createSaleEntityPerformancePackageCardSavingCard(saleEntityPerformancePackageCardSavingCard);
            }

            //创建渠道业绩
            List<SaleChannelPerformanceGoodsEntity> saleChannelPerformanceProduct = new ArrayList<>();
            List<SaleChannelPerformanceGoodsEntity> saleChannelPerformanceProject = new ArrayList<>();
            List<SaleChannelPerformanceGoodsEntity> saleChannelPerformanceGeneralCard = new ArrayList<>();
            List<SaleChannelPerformanceGoodsEntity> saleChannelPerformanceTimeCard = new ArrayList<>();
            List<SaleChannelPerformanceGoodsEntity> saleChannelPerformanceSavingCard = new ArrayList<>();
            //门店套餐卡渠道业绩
            List<SaleChannelPerformancePackageCardGoodsEntity> saleChannelPerformancePackageCardProduct = new ArrayList<>();
            List<SaleChannelPerformancePackageCardGoodsEntity> saleChannelPerformancePackageCardProject = new ArrayList<>();
            List<SaleChannelPerformancePackageCardGoodsEntity> saleChannelPerformancePackageCardGeneralCard = new ArrayList<>();
            List<SaleChannelPerformancePackageCardGoodsEntity> saleChannelPerformancePackageCardTimeCard = new ArrayList<>();
            List<SaleChannelPerformancePackageCardGoodsEntity> saleChannelPerformancePackageCardSavingCard = new ArrayList<>();

            //创建开发人员渠道业绩
            List<SaleChannelEmployeePerformanceGoodsEntity> saleDeveloperChannelPerformanceProduct = new ArrayList<>();
            List<SaleChannelEmployeePerformanceGoodsEntity> saleDeveloperChannelPerformanceProject = new ArrayList<>();
            List<SaleChannelEmployeePerformanceGoodsEntity> saleDeveloperChannelPerformanceGeneralCard = new ArrayList<>();
            List<SaleChannelEmployeePerformanceGoodsEntity> saleDeveloperChannelPerformanceTimeCard = new ArrayList<>();
            List<SaleChannelEmployeePerformanceGoodsEntity> saleDeveloperChannelPerformanceSavingCard = new ArrayList<>();
            //开发人员套餐卡渠道业绩
            List<SaleChannelEmployeePerformancePackageCardGoodsEntity> saleDeveloperChannelPerformancePackageCardProduct = new ArrayList<>();
            List<SaleChannelEmployeePerformancePackageCardGoodsEntity> saleDeveloperChannelPerformancePackageCardProject = new ArrayList<>();
            List<SaleChannelEmployeePerformancePackageCardGoodsEntity> saleDeveloperChannelPerformancePackageCardGeneralCard = new ArrayList<>();
            List<SaleChannelEmployeePerformancePackageCardGoodsEntity> saleDeveloperChannelPerformancePackageCardTimeCard = new ArrayList<>();
            List<SaleChannelEmployeePerformancePackageCardGoodsEntity> saleDeveloperChannelPerformancePackageCardSavingCard = new ArrayList<>();

            //创建服务人员渠道业绩
            List<SaleChannelEmployeePerformanceGoodsEntity> saleConsultantChannelPerformanceProduct = new ArrayList<>();
            List<SaleChannelEmployeePerformanceGoodsEntity> saleConsultantChannelPerformanceProject = new ArrayList<>();
            List<SaleChannelEmployeePerformanceGoodsEntity> saleConsultantChannelPerformanceGeneralCard = new ArrayList<>();
            List<SaleChannelEmployeePerformanceGoodsEntity> saleConsultantChannelPerformanceTimeCard = new ArrayList<>();
            List<SaleChannelEmployeePerformanceGoodsEntity> saleConsultantChannelPerformanceSavingCard = new ArrayList<>();
            //服务人员套餐卡渠道业绩
            List<SaleChannelEmployeePerformancePackageCardGoodsEntity> saleConsultantChannelPerformancePackageCardProduct = new ArrayList<>();
            List<SaleChannelEmployeePerformancePackageCardGoodsEntity> saleConsultantChannelPerformancePackageCardProject = new ArrayList<>();
            List<SaleChannelEmployeePerformancePackageCardGoodsEntity> saleConsultantChannelPerformancePackageCardGeneralCard = new ArrayList<>();
            List<SaleChannelEmployeePerformancePackageCardGoodsEntity> saleConsultantChannelPerformancePackageCardTimeCard = new ArrayList<>();
            List<SaleChannelEmployeePerformancePackageCardGoodsEntity> saleConsultantChannelPerformancePackageCardSavingCard = new ArrayList<>();
            if (isCalculateChannelPerformance) {
                for (ChannelParentOutputForm parent : parents) {
                    for (SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity : saleEntityPerformanceProduct) {
                        SaleChannelPerformanceGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelPerformanceGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformanceGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformanceGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformanceGoodsEntity.OriginPayPerformance;
                        saleChannelPerformanceGoodsEntity.SavingCardPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardPerformance;
                        saleChannelPerformanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardLargessPerformance;
                        saleChannelPerformanceGoodsEntity.ChannelID = parent.ID;
                        saleChannelPerformanceGoodsEntity.IsChannelIntroducer = false;
                        saleChannelPerformanceProduct.add(saleChannelPerformanceGoodsEntity);
                        if (parent.IntroducerList.size() > 0) {
                            for (Integer integer : parent.IntroducerList) {
                                SaleChannelPerformanceGoodsEntity performanceGoodsEntity = new SaleChannelPerformanceGoodsEntity();
                                performanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                                performanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                                performanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                                performanceGoodsEntity.SaleBillGoodsID = saleEntityPerformanceGoodsEntity.SaleBillGoodsID;
                                performanceGoodsEntity.GoodsID = saleEntityPerformanceGoodsEntity.GoodsID;
                                performanceGoodsEntity.PayPerformance = saleEntityPerformanceGoodsEntity.OriginPayPerformance;
                                performanceGoodsEntity.SavingCardPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardPerformance;
                                performanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardLargessPerformance;
                                performanceGoodsEntity.ChannelID = integer;
                                performanceGoodsEntity.IsChannelIntroducer = true;
                                saleChannelPerformanceProduct.add(performanceGoodsEntity);
                            }

                        }
                    }
                    for (SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity : saleEntityPerformanceProject) {
                        SaleChannelPerformanceGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelPerformanceGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformanceGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformanceGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformanceGoodsEntity.OriginPayPerformance;
                        saleChannelPerformanceGoodsEntity.SavingCardPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardPerformance;
                        saleChannelPerformanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardLargessPerformance;
                        saleChannelPerformanceGoodsEntity.ChannelID = parent.ID;
                        saleChannelPerformanceGoodsEntity.IsChannelIntroducer = false;
                        saleChannelPerformanceProject.add(saleChannelPerformanceGoodsEntity);
                        if (parent.IntroducerList.size() > 0) {
                            for (Integer integer : parent.IntroducerList) {
                                SaleChannelPerformanceGoodsEntity performanceGoodsEntity = new SaleChannelPerformanceGoodsEntity();
                                performanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                                performanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                                performanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                                performanceGoodsEntity.SaleBillGoodsID = saleEntityPerformanceGoodsEntity.SaleBillGoodsID;
                                performanceGoodsEntity.GoodsID = saleEntityPerformanceGoodsEntity.GoodsID;
                                performanceGoodsEntity.PayPerformance = saleEntityPerformanceGoodsEntity.OriginPayPerformance;
                                performanceGoodsEntity.SavingCardPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardPerformance;
                                performanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardLargessPerformance;
                                performanceGoodsEntity.ChannelID = integer;
                                performanceGoodsEntity.IsChannelIntroducer = true;
                                saleChannelPerformanceProject.add(performanceGoodsEntity);
                            }

                        }
                    }
                    for (SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity : saleEntityPerformanceGeneralCard) {
                        SaleChannelPerformanceGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelPerformanceGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformanceGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformanceGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformanceGoodsEntity.OriginPayPerformance;
                        saleChannelPerformanceGoodsEntity.SavingCardPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardPerformance;
                        saleChannelPerformanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardLargessPerformance;
                        saleChannelPerformanceGoodsEntity.ChannelID = parent.ID;
                        saleChannelPerformanceGoodsEntity.IsChannelIntroducer = false;
                        saleChannelPerformanceGeneralCard.add(saleChannelPerformanceGoodsEntity);
                        if (parent.IntroducerList.size() > 0) {
                            for (Integer integer : parent.IntroducerList) {
                                SaleChannelPerformanceGoodsEntity performanceGoodsEntity = new SaleChannelPerformanceGoodsEntity();
                                performanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                                performanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                                performanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                                performanceGoodsEntity.SaleBillGoodsID = saleEntityPerformanceGoodsEntity.SaleBillGoodsID;
                                performanceGoodsEntity.GoodsID = saleEntityPerformanceGoodsEntity.GoodsID;
                                performanceGoodsEntity.PayPerformance = saleEntityPerformanceGoodsEntity.OriginPayPerformance;
                                performanceGoodsEntity.SavingCardPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardPerformance;
                                performanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardLargessPerformance;
                                performanceGoodsEntity.ChannelID = integer;
                                performanceGoodsEntity.IsChannelIntroducer = true;
                                saleChannelPerformanceGeneralCard.add(performanceGoodsEntity);
                            }

                        }
                    }
                    for (SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity : saleEntityPerformanceTimeCard) {
                        SaleChannelPerformanceGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelPerformanceGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformanceGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformanceGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformanceGoodsEntity.OriginPayPerformance;
                        saleChannelPerformanceGoodsEntity.SavingCardPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardPerformance;
                        saleChannelPerformanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardLargessPerformance;
                        saleChannelPerformanceGoodsEntity.ChannelID = parent.ID;
                        saleChannelPerformanceGoodsEntity.IsChannelIntroducer = false;
                        saleChannelPerformanceTimeCard.add(saleChannelPerformanceGoodsEntity);
                        if (parent.IntroducerList.size() > 0) {
                            for (Integer integer : parent.IntroducerList) {
                                SaleChannelPerformanceGoodsEntity performanceGoodsEntity = new SaleChannelPerformanceGoodsEntity();
                                performanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                                performanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                                performanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                                performanceGoodsEntity.SaleBillGoodsID = saleEntityPerformanceGoodsEntity.SaleBillGoodsID;
                                performanceGoodsEntity.GoodsID = saleEntityPerformanceGoodsEntity.GoodsID;
                                performanceGoodsEntity.PayPerformance = saleEntityPerformanceGoodsEntity.OriginPayPerformance;
                                performanceGoodsEntity.SavingCardPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardPerformance;
                                performanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardLargessPerformance;
                                performanceGoodsEntity.ChannelID = integer;
                                performanceGoodsEntity.IsChannelIntroducer = true;
                                saleChannelPerformanceTimeCard.add(performanceGoodsEntity);
                            }

                        }
                    }
                    for (SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity : saleEntityPerformanceSavingCard) {
                        SaleChannelPerformanceGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelPerformanceGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformanceGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformanceGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformanceGoodsEntity.OriginPayPerformance;
                        saleChannelPerformanceGoodsEntity.ChannelID = parent.ID;
                        saleChannelPerformanceGoodsEntity.IsChannelIntroducer = false;
                        saleChannelPerformanceSavingCard.add(saleChannelPerformanceGoodsEntity);
                        if (parent.IntroducerList.size() > 0) {
                            for (Integer integer : parent.IntroducerList) {
                                SaleChannelPerformanceGoodsEntity performanceGoodsEntity = new SaleChannelPerformanceGoodsEntity();
                                performanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                                performanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                                performanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                                performanceGoodsEntity.SaleBillGoodsID = saleEntityPerformanceGoodsEntity.SaleBillGoodsID;
                                performanceGoodsEntity.GoodsID = saleEntityPerformanceGoodsEntity.GoodsID;
                                performanceGoodsEntity.PayPerformance = saleEntityPerformanceGoodsEntity.OriginPayPerformance;
                                performanceGoodsEntity.ChannelID = integer;
                                performanceGoodsEntity.IsChannelIntroducer = true;
                                saleChannelPerformanceSavingCard.add(performanceGoodsEntity);
                            }

                        }
                    }


                    for (SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity : saleEntityPerformancePackageCardProduct) {
                        SaleChannelPerformancePackageCardGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelPerformancePackageCardGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillPackageCardID = saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID;
                        saleChannelPerformanceGoodsEntity.PackageCardID = saleEntityPerformancePackageCardGoodsEntity.PackageCardID;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformancePackageCardGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance;
                        saleChannelPerformanceGoodsEntity.SavingCardPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardPerformance;
                        saleChannelPerformanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardLargessPerformance;
                        saleChannelPerformanceGoodsEntity.ChannelID = parent.ID;
                        saleChannelPerformanceGoodsEntity.IsChannelIntroducer = false;
                        saleChannelPerformancePackageCardProduct.add(saleChannelPerformanceGoodsEntity);
                        if (parent.IntroducerList.size() > 0) {
                            for (Integer integer : parent.IntroducerList) {
                                SaleChannelPerformancePackageCardGoodsEntity performanceGoodsEntity = new SaleChannelPerformancePackageCardGoodsEntity();
                                performanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                                performanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                                performanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                                performanceGoodsEntity.SaleBillPackageCardID = saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID;
                                performanceGoodsEntity.PackageCardID = saleEntityPerformancePackageCardGoodsEntity.PackageCardID;
                                performanceGoodsEntity.SaleBillGoodsID = saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID;
                                performanceGoodsEntity.GoodsID = saleEntityPerformancePackageCardGoodsEntity.GoodsID;
                                performanceGoodsEntity.PayPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance;
                                performanceGoodsEntity.SavingCardPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardPerformance;
                                performanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardLargessPerformance;
                                performanceGoodsEntity.ChannelID = integer;
                                performanceGoodsEntity.IsChannelIntroducer = true;
                                saleChannelPerformancePackageCardProduct.add(performanceGoodsEntity);
                            }

                        }
                    }
                    for (SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity : saleEntityPerformancePackageCardProject) {
                        SaleChannelPerformancePackageCardGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelPerformancePackageCardGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillPackageCardID = saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID;
                        saleChannelPerformanceGoodsEntity.PackageCardID = saleEntityPerformancePackageCardGoodsEntity.PackageCardID;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformancePackageCardGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance;
                        saleChannelPerformanceGoodsEntity.SavingCardPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardPerformance;
                        saleChannelPerformanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardLargessPerformance;
                        saleChannelPerformanceGoodsEntity.ChannelID = parent.ID;
                        saleChannelPerformanceGoodsEntity.IsChannelIntroducer = false;
                        saleChannelPerformancePackageCardProject.add(saleChannelPerformanceGoodsEntity);
                        if (parent.IntroducerList.size() > 0) {
                            for (Integer integer : parent.IntroducerList) {
                                SaleChannelPerformancePackageCardGoodsEntity performanceGoodsEntity = new SaleChannelPerformancePackageCardGoodsEntity();
                                performanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                                performanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                                performanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                                performanceGoodsEntity.SaleBillPackageCardID = saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID;
                                performanceGoodsEntity.PackageCardID = saleEntityPerformancePackageCardGoodsEntity.PackageCardID;
                                performanceGoodsEntity.SaleBillGoodsID = saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID;
                                performanceGoodsEntity.GoodsID = saleEntityPerformancePackageCardGoodsEntity.GoodsID;
                                performanceGoodsEntity.PayPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance;
                                performanceGoodsEntity.SavingCardPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardPerformance;
                                performanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardLargessPerformance;
                                performanceGoodsEntity.ChannelID = integer;
                                performanceGoodsEntity.IsChannelIntroducer = true;
                                saleChannelPerformancePackageCardProject.add(performanceGoodsEntity);
                            }

                        }
                    }
                    for (SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity : saleEntityPerformancePackageCardGeneralCard) {
                        SaleChannelPerformancePackageCardGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelPerformancePackageCardGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillPackageCardID = saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID;
                        saleChannelPerformanceGoodsEntity.PackageCardID = saleEntityPerformancePackageCardGoodsEntity.PackageCardID;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformancePackageCardGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance;
                        saleChannelPerformanceGoodsEntity.SavingCardPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardPerformance;
                        saleChannelPerformanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardLargessPerformance;
                        saleChannelPerformanceGoodsEntity.ChannelID = parent.ID;
                        saleChannelPerformanceGoodsEntity.IsChannelIntroducer = false;
                        saleChannelPerformancePackageCardGeneralCard.add(saleChannelPerformanceGoodsEntity);
                        if (parent.IntroducerList.size() > 0) {
                            for (Integer integer : parent.IntroducerList) {
                                SaleChannelPerformancePackageCardGoodsEntity performanceGoodsEntity = new SaleChannelPerformancePackageCardGoodsEntity();
                                performanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                                performanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                                performanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                                performanceGoodsEntity.SaleBillPackageCardID = saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID;
                                performanceGoodsEntity.PackageCardID = saleEntityPerformancePackageCardGoodsEntity.PackageCardID;
                                performanceGoodsEntity.SaleBillGoodsID = saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID;
                                performanceGoodsEntity.GoodsID = saleEntityPerformancePackageCardGoodsEntity.GoodsID;
                                performanceGoodsEntity.PayPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance;
                                performanceGoodsEntity.SavingCardPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardPerformance;
                                performanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardLargessPerformance;
                                performanceGoodsEntity.ChannelID = integer;
                                performanceGoodsEntity.IsChannelIntroducer = true;
                                saleChannelPerformancePackageCardGeneralCard.add(performanceGoodsEntity);
                            }

                        }
                    }
                    for (SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity : saleEntityPerformancePackageCardTimeCard) {
                        SaleChannelPerformancePackageCardGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelPerformancePackageCardGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillPackageCardID = saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID;
                        saleChannelPerformanceGoodsEntity.PackageCardID = saleEntityPerformancePackageCardGoodsEntity.PackageCardID;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformancePackageCardGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance;
                        saleChannelPerformanceGoodsEntity.SavingCardPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardPerformance;
                        saleChannelPerformanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardLargessPerformance;
                        saleChannelPerformanceGoodsEntity.ChannelID = parent.ID;
                        saleChannelPerformanceGoodsEntity.IsChannelIntroducer = false;
                        saleChannelPerformancePackageCardTimeCard.add(saleChannelPerformanceGoodsEntity);
                        if (parent.IntroducerList.size() > 0) {
                            for (Integer integer : parent.IntroducerList) {
                                SaleChannelPerformancePackageCardGoodsEntity performanceGoodsEntity = new SaleChannelPerformancePackageCardGoodsEntity();
                                performanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                                performanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                                performanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                                performanceGoodsEntity.SaleBillPackageCardID = saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID;
                                performanceGoodsEntity.PackageCardID = saleEntityPerformancePackageCardGoodsEntity.PackageCardID;
                                performanceGoodsEntity.SaleBillGoodsID = saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID;
                                performanceGoodsEntity.GoodsID = saleEntityPerformancePackageCardGoodsEntity.GoodsID;
                                performanceGoodsEntity.PayPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance;
                                performanceGoodsEntity.SavingCardPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardPerformance;
                                performanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardLargessPerformance;
                                performanceGoodsEntity.ChannelID = integer;
                                performanceGoodsEntity.IsChannelIntroducer = true;
                                saleChannelPerformancePackageCardTimeCard.add(performanceGoodsEntity);
                            }

                        }
                    }
                    for (SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity : saleEntityPerformancePackageCardSavingCard) {
                        SaleChannelPerformancePackageCardGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelPerformancePackageCardGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillPackageCardID = saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID;
                        saleChannelPerformanceGoodsEntity.PackageCardID = saleEntityPerformancePackageCardGoodsEntity.PackageCardID;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformancePackageCardGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance;
                        saleChannelPerformanceGoodsEntity.ChannelID = parent.ID;
                        saleChannelPerformanceGoodsEntity.IsChannelIntroducer = false;
                        saleChannelPerformancePackageCardSavingCard.add(saleChannelPerformanceGoodsEntity);
                        if (parent.IntroducerList.size() > 0) {
                            for (Integer integer : parent.IntroducerList) {
                                SaleChannelPerformancePackageCardGoodsEntity performanceGoodsEntity = new SaleChannelPerformancePackageCardGoodsEntity();
                                performanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                                performanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                                performanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                                performanceGoodsEntity.SaleBillPackageCardID = saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID;
                                performanceGoodsEntity.PackageCardID = saleEntityPerformancePackageCardGoodsEntity.PackageCardID;
                                performanceGoodsEntity.SaleBillGoodsID = saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID;
                                performanceGoodsEntity.GoodsID = saleEntityPerformancePackageCardGoodsEntity.GoodsID;
                                performanceGoodsEntity.PayPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance;
                                performanceGoodsEntity.ChannelID = integer;
                                performanceGoodsEntity.IsChannelIntroducer = true;
                                saleChannelPerformancePackageCardSavingCard.add(performanceGoodsEntity);
                            }

                        }
                    }
                }
                for (ChannelEmployeePerformanceRateOutputForm employeePerformanceRateOutputForm : developer) {
                    for (SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity : saleEntityPerformanceProduct) {
                        SaleChannelEmployeePerformanceGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelEmployeePerformanceGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformanceGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformanceGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformanceGoodsEntity.OriginPayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardLargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.ChannelID = cust.ChannelID;
                        saleDeveloperChannelPerformanceProduct.add(saleChannelPerformanceGoodsEntity);

                    }
                    for (SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity : saleEntityPerformanceProject) {
                        SaleChannelEmployeePerformanceGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelEmployeePerformanceGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformanceGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformanceGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformanceGoodsEntity.OriginPayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardLargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.ChannelID = cust.ChannelID;
                        saleDeveloperChannelPerformanceProject.add(saleChannelPerformanceGoodsEntity);

                    }
                    for (SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity : saleEntityPerformanceGeneralCard) {
                        SaleChannelEmployeePerformanceGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelEmployeePerformanceGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformanceGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformanceGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformanceGoodsEntity.OriginPayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardLargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.ChannelID = cust.ChannelID;
                        saleDeveloperChannelPerformanceGeneralCard.add(saleChannelPerformanceGoodsEntity);
                    }
                    for (SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity : saleEntityPerformanceTimeCard) {
                        SaleChannelEmployeePerformanceGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelEmployeePerformanceGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformanceGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformanceGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformanceGoodsEntity.OriginPayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardLargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.ChannelID = cust.ChannelID;
                        saleDeveloperChannelPerformanceTimeCard.add(saleChannelPerformanceGoodsEntity);
                    }
                    for (SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity : saleEntityPerformanceSavingCard) {
                        SaleChannelEmployeePerformanceGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelEmployeePerformanceGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformanceGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformanceGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformanceGoodsEntity.OriginPayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.ChannelID = cust.ChannelID;
                        saleDeveloperChannelPerformanceSavingCard.add(saleChannelPerformanceGoodsEntity);
                    }

                    for (SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity : saleEntityPerformancePackageCardProduct) {
                        SaleChannelEmployeePerformancePackageCardGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelEmployeePerformancePackageCardGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillPackageCardID = saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID;
                        saleChannelPerformanceGoodsEntity.PackageCardID = saleEntityPerformancePackageCardGoodsEntity.PackageCardID;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformancePackageCardGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardLargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.ChannelID = cust.ChannelID;
                        saleChannelPerformanceGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                        saleDeveloperChannelPerformancePackageCardProduct.add(saleChannelPerformanceGoodsEntity);
                    }
                    for (SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity : saleEntityPerformancePackageCardProject) {
                        SaleChannelEmployeePerformancePackageCardGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelEmployeePerformancePackageCardGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillPackageCardID = saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID;
                        saleChannelPerformanceGoodsEntity.PackageCardID = saleEntityPerformancePackageCardGoodsEntity.PackageCardID;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformancePackageCardGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardLargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.ChannelID = cust.ChannelID;
                        saleChannelPerformanceGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                        saleDeveloperChannelPerformancePackageCardProject.add(saleChannelPerformanceGoodsEntity);
                    }
                    for (SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity : saleEntityPerformancePackageCardGeneralCard) {
                        SaleChannelEmployeePerformancePackageCardGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelEmployeePerformancePackageCardGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillPackageCardID = saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID;
                        saleChannelPerformanceGoodsEntity.PackageCardID = saleEntityPerformancePackageCardGoodsEntity.PackageCardID;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformancePackageCardGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardLargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.ChannelID = cust.ChannelID;
                        saleChannelPerformanceGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                        saleDeveloperChannelPerformancePackageCardGeneralCard.add(saleChannelPerformanceGoodsEntity);
                    }
                    for (SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity : saleEntityPerformancePackageCardTimeCard) {
                        SaleChannelEmployeePerformancePackageCardGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelEmployeePerformancePackageCardGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillPackageCardID = saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID;
                        saleChannelPerformanceGoodsEntity.PackageCardID = saleEntityPerformancePackageCardGoodsEntity.PackageCardID;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformancePackageCardGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardLargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.ChannelID = cust.ChannelID;
                        saleChannelPerformanceGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                        saleDeveloperChannelPerformancePackageCardTimeCard.add(saleChannelPerformanceGoodsEntity);
                    }
                    for (SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity : saleEntityPerformancePackageCardSavingCard) {
                        SaleChannelEmployeePerformancePackageCardGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelEmployeePerformancePackageCardGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillPackageCardID = saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID;
                        saleChannelPerformanceGoodsEntity.PackageCardID = saleEntityPerformancePackageCardGoodsEntity.PackageCardID;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformancePackageCardGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.ChannelID = cust.ChannelID;
                        saleChannelPerformanceGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                        saleDeveloperChannelPerformancePackageCardSavingCard.add(saleChannelPerformanceGoodsEntity);
                    }
                }

                for (ChannelEmployeePerformanceRateOutputForm employeePerformanceRateOutputForm : consultant) {
                    for (SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity : saleEntityPerformanceProduct) {
                        SaleChannelEmployeePerformanceGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelEmployeePerformanceGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformanceGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformanceGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformanceGoodsEntity.OriginPayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardLargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.ChannelID = cust.ChannelID;
                        saleConsultantChannelPerformanceProduct.add(saleChannelPerformanceGoodsEntity);

                    }
                    for (SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity : saleEntityPerformanceProject) {
                        SaleChannelEmployeePerformanceGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelEmployeePerformanceGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformanceGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformanceGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformanceGoodsEntity.OriginPayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardLargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.ChannelID = cust.ChannelID;
                        saleConsultantChannelPerformanceProject.add(saleChannelPerformanceGoodsEntity);

                    }
                    for (SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity : saleEntityPerformanceGeneralCard) {
                        SaleChannelEmployeePerformanceGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelEmployeePerformanceGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformanceGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformanceGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformanceGoodsEntity.OriginPayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardLargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.ChannelID = cust.ChannelID;
                        saleConsultantChannelPerformanceGeneralCard.add(saleChannelPerformanceGoodsEntity);
                    }
                    for (SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity : saleEntityPerformanceTimeCard) {
                        SaleChannelEmployeePerformanceGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelEmployeePerformanceGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformanceGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformanceGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformanceGoodsEntity.OriginPayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardLargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.ChannelID = cust.ChannelID;
                        saleConsultantChannelPerformanceTimeCard.add(saleChannelPerformanceGoodsEntity);
                    }
                    for (SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity : saleEntityPerformanceSavingCard) {
                        SaleChannelEmployeePerformanceGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelEmployeePerformanceGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformanceGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformanceGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformanceGoodsEntity.OriginPayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.ChannelID = cust.ChannelID;
                        saleConsultantChannelPerformanceSavingCard.add(saleChannelPerformanceGoodsEntity);
                    }

                    for (SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity : saleEntityPerformancePackageCardProduct) {
                        SaleChannelEmployeePerformancePackageCardGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelEmployeePerformancePackageCardGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillPackageCardID = saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID;
                        saleChannelPerformanceGoodsEntity.PackageCardID = saleEntityPerformancePackageCardGoodsEntity.PackageCardID;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformancePackageCardGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardLargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.ChannelID = cust.ChannelID;
                        saleChannelPerformanceGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                        saleConsultantChannelPerformancePackageCardProduct.add(saleChannelPerformanceGoodsEntity);
                    }
                    for (SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity : saleEntityPerformancePackageCardProject) {
                        SaleChannelEmployeePerformancePackageCardGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelEmployeePerformancePackageCardGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillPackageCardID = saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID;
                        saleChannelPerformanceGoodsEntity.PackageCardID = saleEntityPerformancePackageCardGoodsEntity.PackageCardID;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformancePackageCardGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardLargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.ChannelID = cust.ChannelID;
                        saleChannelPerformanceGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                        saleConsultantChannelPerformancePackageCardProject.add(saleChannelPerformanceGoodsEntity);
                    }
                    for (SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity : saleEntityPerformancePackageCardGeneralCard) {
                        SaleChannelEmployeePerformancePackageCardGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelEmployeePerformancePackageCardGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillPackageCardID = saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID;
                        saleChannelPerformanceGoodsEntity.PackageCardID = saleEntityPerformancePackageCardGoodsEntity.PackageCardID;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformancePackageCardGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardLargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.ChannelID = cust.ChannelID;
                        saleChannelPerformanceGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                        saleConsultantChannelPerformancePackageCardGeneralCard.add(saleChannelPerformanceGoodsEntity);
                    }
                    for (SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity : saleEntityPerformancePackageCardTimeCard) {
                        SaleChannelEmployeePerformancePackageCardGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelEmployeePerformancePackageCardGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillPackageCardID = saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID;
                        saleChannelPerformanceGoodsEntity.PackageCardID = saleEntityPerformancePackageCardGoodsEntity.PackageCardID;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformancePackageCardGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.SavingCardLargessPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardLargessPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.ChannelID = cust.ChannelID;
                        saleChannelPerformanceGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                        saleConsultantChannelPerformancePackageCardTimeCard.add(saleChannelPerformanceGoodsEntity);
                    }
                    for (SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity : saleEntityPerformancePackageCardSavingCard) {
                        SaleChannelEmployeePerformancePackageCardGoodsEntity saleChannelPerformanceGoodsEntity = new SaleChannelEmployeePerformancePackageCardGoodsEntity();
                        saleChannelPerformanceGoodsEntity.SaleBillID = saleBillEntity.ID;
                        saleChannelPerformanceGoodsEntity.EntityID = saleBillEntity.EntityID;
                        saleChannelPerformanceGoodsEntity.BillDate = saleBillEntity.BillDate;
                        saleChannelPerformanceGoodsEntity.SaleBillPackageCardID = saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID;
                        saleChannelPerformanceGoodsEntity.PackageCardID = saleEntityPerformancePackageCardGoodsEntity.PackageCardID;
                        saleChannelPerformanceGoodsEntity.SaleBillGoodsID = saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID;
                        saleChannelPerformanceGoodsEntity.GoodsID = saleEntityPerformancePackageCardGoodsEntity.GoodsID;
                        saleChannelPerformanceGoodsEntity.PayPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance.multiply(employeePerformanceRateOutputForm.Rate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        saleChannelPerformanceGoodsEntity.ChannelID = cust.ChannelID;
                        saleChannelPerformanceGoodsEntity.EmployeeID = employeePerformanceRateOutputForm.EmployeeID;
                        saleConsultantChannelPerformancePackageCardSavingCard.add(saleChannelPerformanceGoodsEntity);
                    }
                }

            }

            //保存渠道业绩
            if (saleChannelPerformanceProduct.size() > 0) {
                saleChannelPerformanceMapper.createSaleChannelPerformanceProduct(saleChannelPerformanceProduct);
            }
            if (saleChannelPerformanceProject.size() > 0) {
                saleChannelPerformanceMapper.createSaleChannelPerformanceProject(saleChannelPerformanceProject);
            }
            if (saleChannelPerformanceGeneralCard.size() > 0) {
                saleChannelPerformanceMapper.createSaleChannelPerformanceGeneralCard(saleChannelPerformanceGeneralCard);
            }
            if (saleChannelPerformanceTimeCard.size() > 0) {
                saleChannelPerformanceMapper.createSaleChannelPerformanceTimeCard(saleChannelPerformanceTimeCard);
            }
            if (saleChannelPerformanceSavingCard.size() > 0) {
                saleChannelPerformanceMapper.createSaleChannelPerformanceSavingCard(saleChannelPerformanceSavingCard);
            }

            //保存套餐卡渠道业绩
            if (saleChannelPerformancePackageCardProduct.size() > 0) {
                saleChannelPerformanceMapper.createSaleChannelPerformancePackageCardProduct(saleChannelPerformancePackageCardProduct);
            }
            if (saleChannelPerformancePackageCardProject.size() > 0) {
                saleChannelPerformanceMapper.createSaleChannelPerformancePackageCardProject(saleChannelPerformancePackageCardProject);
            }
            if (saleChannelPerformancePackageCardGeneralCard.size() > 0) {
                saleChannelPerformanceMapper.createSaleChannelPerformancePackageCardGeneralCard(saleChannelPerformancePackageCardGeneralCard);
            }
            if (saleChannelPerformancePackageCardTimeCard.size() > 0) {
                saleChannelPerformanceMapper.createSaleChannelPerformancePackageCardTimeCard(saleChannelPerformancePackageCardTimeCard);
            }
            if (saleChannelPerformancePackageCardSavingCard.size() > 0) {
                saleChannelPerformanceMapper.createSaleChannelPerformancePackageCardSavingCard(saleChannelPerformancePackageCardSavingCard);
            }

            //保存开发人员的渠道业绩
            if (saleDeveloperChannelPerformanceProduct.size() > 0) {
                saleChannelDeveloperPerformanceMapper.createSaleDeveloperChannelPerformanceProduct(saleDeveloperChannelPerformanceProduct);
            }
            if (saleDeveloperChannelPerformanceProject.size() > 0) {
                saleChannelDeveloperPerformanceMapper.createSaleDeveloperChannelPerformanceProject(saleDeveloperChannelPerformanceProject);
            }
            if (saleDeveloperChannelPerformanceGeneralCard.size() > 0) {
                saleChannelDeveloperPerformanceMapper.createSaleDeveloperChannelPerformanceGeneralCard(saleDeveloperChannelPerformanceGeneralCard);
            }
            if (saleDeveloperChannelPerformanceTimeCard.size() > 0) {
                saleChannelDeveloperPerformanceMapper.createSaleDeveloperChannelPerformanceTimeCard(saleDeveloperChannelPerformanceTimeCard);
            }
            if (saleDeveloperChannelPerformanceSavingCard.size() > 0) {
                saleChannelDeveloperPerformanceMapper.createSaleDeveloperChannelPerformanceSavingCard(saleDeveloperChannelPerformanceSavingCard);
            }

            //保存开发人员套餐卡渠道业绩
            if (saleDeveloperChannelPerformancePackageCardProduct.size() > 0) {
                saleChannelDeveloperPerformanceMapper.createSaleDeveloperChannelPerformancePackageCardProduct(saleDeveloperChannelPerformancePackageCardProduct);
            }
            if (saleDeveloperChannelPerformancePackageCardProject.size() > 0) {
                saleChannelDeveloperPerformanceMapper.createSaleDeveloperChannelPerformancePackageCardProject(saleDeveloperChannelPerformancePackageCardProject);
            }
            if (saleDeveloperChannelPerformancePackageCardGeneralCard.size() > 0) {
                saleChannelDeveloperPerformanceMapper.createSaleDeveloperChannelPerformancePackageCardGeneralCard(saleDeveloperChannelPerformancePackageCardGeneralCard);
            }
            if (saleDeveloperChannelPerformancePackageCardTimeCard.size() > 0) {
                saleChannelDeveloperPerformanceMapper.createSaleDeveloperChannelPerformancePackageCardTimeCard(saleDeveloperChannelPerformancePackageCardTimeCard);
            }
            if (saleDeveloperChannelPerformancePackageCardSavingCard.size() > 0) {
                saleChannelDeveloperPerformanceMapper.createSaleDeveloperChannelPerformancePackageCardSavingCard(saleDeveloperChannelPerformancePackageCardSavingCard);
            }

            //保存服务人员的渠道业绩
            if (saleConsultantChannelPerformanceProduct.size() > 0) {
                saleChannelConsultantPerformanceMapper.createSaleConsultantChannelPerformanceProduct(saleConsultantChannelPerformanceProduct);
            }
            if (saleConsultantChannelPerformanceProject.size() > 0) {
                saleChannelConsultantPerformanceMapper.createSaleConsultantChannelPerformanceProject(saleConsultantChannelPerformanceProject);
            }
            if (saleConsultantChannelPerformanceGeneralCard.size() > 0) {
                saleChannelConsultantPerformanceMapper.createSaleConsultantChannelPerformanceGeneralCard(saleConsultantChannelPerformanceGeneralCard);
            }
            if (saleConsultantChannelPerformanceTimeCard.size() > 0) {
                saleChannelConsultantPerformanceMapper.createSaleConsultantChannelPerformanceTimeCard(saleConsultantChannelPerformanceTimeCard);
            }
            if (saleConsultantChannelPerformanceSavingCard.size() > 0) {
                saleChannelConsultantPerformanceMapper.createSaleConsultantChannelPerformanceSavingCard(saleConsultantChannelPerformanceSavingCard);
            }

            //保存服务人员套餐卡渠道业绩
            if (saleConsultantChannelPerformancePackageCardProduct.size() > 0) {
                saleChannelConsultantPerformanceMapper.createSaleConsultantChannelPerformancePackageCardProduct(saleConsultantChannelPerformancePackageCardProduct);
            }
            if (saleConsultantChannelPerformancePackageCardProject.size() > 0) {
                saleChannelConsultantPerformanceMapper.createSaleConsultantChannelPerformancePackageCardProject(saleConsultantChannelPerformancePackageCardProject);
            }
            if (saleConsultantChannelPerformancePackageCardGeneralCard.size() > 0) {
                saleChannelConsultantPerformanceMapper.createSaleConsultantChannelPerformancePackageCardGeneralCard(saleConsultantChannelPerformancePackageCardGeneralCard);
            }
            if (saleConsultantChannelPerformancePackageCardTimeCard.size() > 0) {
                saleChannelConsultantPerformanceMapper.createSaleConsultantChannelPerformancePackageCardTimeCard(saleConsultantChannelPerformancePackageCardTimeCard);
            }
            if (saleConsultantChannelPerformancePackageCardSavingCard.size() > 0) {
                saleChannelConsultantPerformanceMapper.createSaleConsultantChannelPerformancePackageCardSavingCard(saleConsultantChannelPerformancePackageCardSavingCard);
            }
            //计算员工业绩和佣金
            //计算员工业绩和提佣（非套餐卡）
            //产品-员工业绩和提佣
            List<SaleEmployeePerformanceCommissionGoodsEntity> employeePerformanceProductCommission = new ArrayList<>();
            for (SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity : saleEntityPerformanceProduct) {
                if (saleEntityPerformanceGoodsEntity.SaleBillHandlerEntityList != null) {
                    for (SaleBillHandlerForm saleBillHandlerForm : saleEntityPerformanceGoodsEntity.SaleBillHandlerEntityList) {
                        SaleCommissionEntity saleCommissionEntity = saleEmployeePerformanceCommissionMapper.getSaleProductCommission(saleEntityPerformanceGoodsEntity.EntityID, saleBillHandlerForm.EmployeeID, saleBillHandlerForm.SaleHandlerID, saleEntityPerformanceGoodsEntity.GoodsID);
                        SalePerformanceEntity salePerformanceEntity = saleEmployeePerformanceCommissionMapper.getSaleProductPerformance(saleEntityPerformanceGoodsEntity.EntityID, saleBillHandlerForm.EmployeeID, saleBillHandlerForm.SaleHandlerID, saleEntityPerformanceGoodsEntity.GoodsID);
                        employeePerformanceProductCommission.add(saleEmployeePerformanceCommission(saleEntityPerformanceGoodsEntity, saleBillHandlerForm, saleCommissionEntity, salePerformanceEntity));
                    }
                }
            }
            if (employeePerformanceProductCommission.size() > 0) {
                saleEmployeePerformanceCommissionMapper.createSaleEmployeePerformanceCommissionProduct(employeePerformanceProductCommission);
            }
            //项目-员工业绩和提佣
            List<SaleEmployeePerformanceCommissionGoodsEntity> employeePerformanceProjectCommission = new ArrayList<>();
            for (SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity : saleEntityPerformanceProject) {
                if (saleEntityPerformanceGoodsEntity.SaleBillHandlerEntityList != null) {
                    for (SaleBillHandlerForm saleBillHandlerForm : saleEntityPerformanceGoodsEntity.SaleBillHandlerEntityList) {
                        SaleCommissionEntity saleCommissionEntity = saleEmployeePerformanceCommissionMapper.getSaleProjectCommission(saleEntityPerformanceGoodsEntity.EntityID, saleBillHandlerForm.EmployeeID, saleBillHandlerForm.SaleHandlerID, saleEntityPerformanceGoodsEntity.GoodsID);
                        SalePerformanceEntity salePerformanceEntity = saleEmployeePerformanceCommissionMapper.getSaleProjectPerformance(saleEntityPerformanceGoodsEntity.EntityID, saleBillHandlerForm.EmployeeID, saleBillHandlerForm.SaleHandlerID, saleEntityPerformanceGoodsEntity.GoodsID);
                        employeePerformanceProjectCommission.add(saleEmployeePerformanceCommission(saleEntityPerformanceGoodsEntity, saleBillHandlerForm, saleCommissionEntity, salePerformanceEntity));
                    }
                }
            }
            if (employeePerformanceProjectCommission.size() > 0) {
                saleEmployeePerformanceCommissionMapper.createSaleEmployeePerformanceCommissionProject(employeePerformanceProjectCommission);
            }
            //通用次卡-员工业绩和提佣
            List<SaleEmployeePerformanceCommissionGoodsEntity> employeePerformanceGeneralCardCommission = new ArrayList<>();
            for (SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity : saleEntityPerformanceGeneralCard) {
                if (saleEntityPerformanceGoodsEntity.SaleBillHandlerEntityList != null) {
                    for (SaleBillHandlerForm saleBillHandlerForm : saleEntityPerformanceGoodsEntity.SaleBillHandlerEntityList) {
                        SaleCommissionEntity saleCommissionEntity = saleEmployeePerformanceCommissionMapper.getSaleGeneralCardCommission(saleEntityPerformanceGoodsEntity.EntityID, saleBillHandlerForm.EmployeeID, saleBillHandlerForm.SaleHandlerID, saleEntityPerformanceGoodsEntity.GoodsID);
                        SalePerformanceEntity salePerformanceEntity = saleEmployeePerformanceCommissionMapper.getSaleGeneralCardPerformance(saleEntityPerformanceGoodsEntity.EntityID, saleBillHandlerForm.EmployeeID, saleBillHandlerForm.SaleHandlerID, saleEntityPerformanceGoodsEntity.GoodsID);
                        employeePerformanceGeneralCardCommission.add(saleEmployeePerformanceCommission(saleEntityPerformanceGoodsEntity, saleBillHandlerForm, saleCommissionEntity, salePerformanceEntity));
                    }
                }
            }
            if (employeePerformanceGeneralCardCommission.size() > 0) {
                saleEmployeePerformanceCommissionMapper.createSaleEmployeePerformanceCommissionGeneralCard(employeePerformanceGeneralCardCommission);
            }
            //时效卡-员工业绩和提佣
            List<SaleEmployeePerformanceCommissionGoodsEntity> employeePerformanceTimeCardCommission = new ArrayList<>();
            for (SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity : saleEntityPerformanceTimeCard) {
                if (saleEntityPerformanceGoodsEntity.SaleBillHandlerEntityList != null) {
                    for (SaleBillHandlerForm saleBillHandlerForm : saleEntityPerformanceGoodsEntity.SaleBillHandlerEntityList) {
                        SaleCommissionEntity saleCommissionEntity = saleEmployeePerformanceCommissionMapper.getSaleTimeCardCommission(saleEntityPerformanceGoodsEntity.EntityID, saleBillHandlerForm.EmployeeID, saleBillHandlerForm.SaleHandlerID, saleEntityPerformanceGoodsEntity.GoodsID);
                        SalePerformanceEntity salePerformanceEntity = saleEmployeePerformanceCommissionMapper.getSaleTimeCardPerformance(saleEntityPerformanceGoodsEntity.EntityID, saleBillHandlerForm.EmployeeID, saleBillHandlerForm.SaleHandlerID, saleEntityPerformanceGoodsEntity.GoodsID);
                        employeePerformanceTimeCardCommission.add(saleEmployeePerformanceCommission(saleEntityPerformanceGoodsEntity, saleBillHandlerForm, saleCommissionEntity, salePerformanceEntity));
                    }
                }
            }
            if (employeePerformanceTimeCardCommission.size() > 0) {
                saleEmployeePerformanceCommissionMapper.createSaleEmployeePerformanceCommissionTimeCard(employeePerformanceTimeCardCommission);
            }
            //储值卡-员工业绩和提佣
            List<SaleEmployeePerformanceCommissionGoodsEntity> employeePerformanceSavingCardCommission = new ArrayList<>();
            for (SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity : saleEntityPerformanceSavingCard) {
                if (saleEntityPerformanceGoodsEntity.SaleBillHandlerEntityList != null) {
                    for (SaleBillHandlerForm saleBillHandlerForm : saleEntityPerformanceGoodsEntity.SaleBillHandlerEntityList) {
                        SaleCommissionEntity saleCommissionEntity = saleEmployeePerformanceCommissionMapper.getSaleSavingCardCommission(saleEntityPerformanceGoodsEntity.EntityID, saleBillHandlerForm.EmployeeID, saleBillHandlerForm.SaleHandlerID, saleEntityPerformanceGoodsEntity.GoodsID);
                        SalePerformanceEntity salePerformanceEntity = saleEmployeePerformanceCommissionMapper.getSaleSavingCardPerformance(saleEntityPerformanceGoodsEntity.EntityID, saleBillHandlerForm.EmployeeID, saleBillHandlerForm.SaleHandlerID, saleEntityPerformanceGoodsEntity.GoodsID);
                        SaleEmployeePerformanceCommissionGoodsEntity saleEmployeePerformanceCommissionGoodsEntity = new SaleEmployeePerformanceCommissionGoodsEntity();
                        saleEmployeePerformanceCommissionGoodsEntity.SaleBillID = saleEntityPerformanceGoodsEntity.SaleBillID;//订单编号
                        saleEmployeePerformanceCommissionGoodsEntity.EntityID = saleEntityPerformanceGoodsEntity.EntityID;//门店ID
                        saleEmployeePerformanceCommissionGoodsEntity.BillDate = saleEntityPerformanceGoodsEntity.BillDate;//开单时间
                        saleEmployeePerformanceCommissionGoodsEntity.SaleBillGoodsID = saleEntityPerformanceGoodsEntity.SaleBillGoodsID;//商品明细ID
                        saleEmployeePerformanceCommissionGoodsEntity.GoodsID = saleEntityPerformanceGoodsEntity.GoodsID;//商品ID
                        saleEmployeePerformanceCommissionGoodsEntity.EmployeeID = saleBillHandlerForm.EmployeeID;
                        saleEmployeePerformanceCommissionGoodsEntity.SaleHandlerID = saleBillHandlerForm.SaleHandlerID;

                        BigDecimal performancePayRate = salePerformanceEntity.PayRate == null ? BigDecimal.ZERO : salePerformanceEntity.PayRate;
                        BigDecimal payRate = BigDecimal.ZERO;
                        BigDecimal payFixed = BigDecimal.ZERO;
                        BigDecimal specialBenefit = BigDecimal.ZERO;
                        if (saleCommissionEntity != null) {
                            payRate = saleCommissionEntity.PayRate == null ? BigDecimal.ZERO : saleCommissionEntity.PayRate;
                            payFixed = saleCommissionEntity.PayFixed == null ? BigDecimal.ZERO : saleCommissionEntity.PayFixed;
                            specialBenefit = saleCommissionEntity.SpecialBenefit == null ? BigDecimal.ZERO : saleCommissionEntity.SpecialBenefit;
                        }
                        //现金
                        if (saleEntityPerformanceGoodsEntity.OriginPayPerformance.compareTo(BigDecimal.ZERO) == 0) {
                            saleEmployeePerformanceCommissionGoodsEntity.PayPerformance = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
                            saleEmployeePerformanceCommissionGoodsEntity.PayRateCommission = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
                            saleEmployeePerformanceCommissionGoodsEntity.PayFixedCommission = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
                        } else {
                            saleEmployeePerformanceCommissionGoodsEntity.PayPerformance = saleEntityPerformanceGoodsEntity.OriginPayPerformance.multiply(performancePayRate).multiply(saleBillHandlerForm.Scale).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);
                            saleEmployeePerformanceCommissionGoodsEntity.PayRateCommission = saleEmployeePerformanceCommissionGoodsEntity.PayPerformance.multiply(payRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            saleEmployeePerformanceCommissionGoodsEntity.PayFixedCommission = payFixed.multiply(BigDecimal.valueOf(saleEntityPerformanceGoodsEntity.Quantity)).multiply(performancePayRate).multiply(saleEntityPerformanceGoodsEntity.OriginPayPerformance).multiply(saleBillHandlerForm.Scale).divide(saleEntityPerformanceGoodsEntity.TotalAmount.multiply(new BigDecimal(100)), 2, BigDecimal.ROUND_HALF_UP);
                        }
                        //特殊补贴
                        saleEmployeePerformanceCommissionGoodsEntity.SpecialBenefitCommission = saleEntityPerformanceGoodsEntity.TotalAmount.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP) : specialBenefit.multiply(saleBillHandlerForm.Scale.divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP)).multiply(BigDecimal.valueOf(saleEntityPerformanceGoodsEntity.Quantity)).multiply(saleEntityPerformanceGoodsEntity.OriginPayPerformance).divide(saleEntityPerformanceGoodsEntity.TotalAmount, 2, BigDecimal.ROUND_HALF_UP);

                        employeePerformanceSavingCardCommission.add(saleEmployeePerformanceCommissionGoodsEntity);
                    }
                }
            }
            if (employeePerformanceSavingCardCommission.size() > 0) {
                saleEmployeePerformanceCommissionMapper.createSaleEmployeePerformanceCommissionSavingCard(employeePerformanceSavingCardCommission);
            }
            //计算员工业绩和提佣（套餐卡）
            //产品-员工业绩和提佣
            List<SaleEmployeePerformanceCommissionPackageCardGoodsEntity> employeePerformancePackageCardProductCommission = new ArrayList<>();
            for (SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity : saleEntityPerformancePackageCardProduct) {
                if (saleEntityPerformancePackageCardGoodsEntity.SaleBillHandlerEntityList != null) {
                    for (SaleBillHandlerForm saleBillHandlerForm : saleEntityPerformancePackageCardGoodsEntity.SaleBillHandlerEntityList) {
                        SaleCommissionEntity saleCommissionEntity = saleEmployeePerformanceCommissionMapper.getSalePackageCardProductCommission(saleEntityPerformancePackageCardGoodsEntity.EntityID, saleBillHandlerForm.EmployeeID, saleBillHandlerForm.SaleHandlerID, saleEntityPerformancePackageCardGoodsEntity.GoodsID);
                        SalePerformanceEntity salePerformanceEntity = saleEmployeePerformanceCommissionMapper.getSalePackageCardProductPerformance(saleEntityPerformancePackageCardGoodsEntity.EntityID, saleBillHandlerForm.EmployeeID, saleBillHandlerForm.SaleHandlerID, saleEntityPerformancePackageCardGoodsEntity.GoodsID);
                        employeePerformancePackageCardProductCommission.add(saleEmployeePerformanceCommissionPackageCard(saleEntityPerformancePackageCardGoodsEntity, saleBillHandlerForm, saleCommissionEntity, salePerformanceEntity));
                    }
                }
            }
            if (employeePerformancePackageCardProductCommission.size() > 0) {
                saleEmployeePerformanceCommissionMapper.createSaleEmployeePerformanceCommissionPackageCardProduct(employeePerformancePackageCardProductCommission);
            }
            //项目-员工业绩和提佣
            List<SaleEmployeePerformanceCommissionPackageCardGoodsEntity> employeePerformancePackageCardProjectCommission = new ArrayList<>();
            for (SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity : saleEntityPerformancePackageCardProject) {
                if (saleEntityPerformancePackageCardGoodsEntity.SaleBillHandlerEntityList != null) {
                    for (SaleBillHandlerForm saleBillHandlerForm : saleEntityPerformancePackageCardGoodsEntity.SaleBillHandlerEntityList) {
                        SaleCommissionEntity saleCommissionEntity = saleEmployeePerformanceCommissionMapper.getSalePackageCardProjectCommission(saleEntityPerformancePackageCardGoodsEntity.EntityID, saleBillHandlerForm.EmployeeID, saleBillHandlerForm.SaleHandlerID, saleEntityPerformancePackageCardGoodsEntity.GoodsID);
                        SalePerformanceEntity salePerformanceEntity = saleEmployeePerformanceCommissionMapper.getSalePackageCardProjectPerformance(saleEntityPerformancePackageCardGoodsEntity.EntityID, saleBillHandlerForm.EmployeeID, saleBillHandlerForm.SaleHandlerID, saleEntityPerformancePackageCardGoodsEntity.GoodsID);
                        employeePerformancePackageCardProjectCommission.add(saleEmployeePerformanceCommissionPackageCard(saleEntityPerformancePackageCardGoodsEntity, saleBillHandlerForm, saleCommissionEntity, salePerformanceEntity));
                    }
                }
            }
            if (employeePerformancePackageCardProjectCommission.size() > 0) {
                saleEmployeePerformanceCommissionMapper.createSaleEmployeePerformanceCommissionPackageCardProject(employeePerformancePackageCardProjectCommission);
            }
            //通用次卡-员工业绩和提佣
            List<SaleEmployeePerformanceCommissionPackageCardGoodsEntity> employeePerformancePackageCardGeneralCardCommission = new ArrayList<>();
            for (SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity : saleEntityPerformancePackageCardGeneralCard) {
                if (saleEntityPerformancePackageCardGoodsEntity.SaleBillHandlerEntityList != null) {
                    for (SaleBillHandlerForm saleBillHandlerForm : saleEntityPerformancePackageCardGoodsEntity.SaleBillHandlerEntityList) {
                        SaleCommissionEntity saleCommissionEntity = saleEmployeePerformanceCommissionMapper.getSalePackageCardGeneralCardCommission(saleEntityPerformancePackageCardGoodsEntity.EntityID, saleBillHandlerForm.EmployeeID, saleBillHandlerForm.SaleHandlerID, saleEntityPerformancePackageCardGoodsEntity.GoodsID);
                        SalePerformanceEntity salePerformanceEntity = saleEmployeePerformanceCommissionMapper.getSalePackageCardGeneralCardPerformance(saleEntityPerformancePackageCardGoodsEntity.EntityID, saleBillHandlerForm.EmployeeID, saleBillHandlerForm.SaleHandlerID, saleEntityPerformancePackageCardGoodsEntity.GoodsID);
                        employeePerformancePackageCardGeneralCardCommission.add(saleEmployeePerformanceCommissionPackageCard(saleEntityPerformancePackageCardGoodsEntity, saleBillHandlerForm, saleCommissionEntity, salePerformanceEntity));
                    }
                }
            }
            if (employeePerformancePackageCardGeneralCardCommission.size() > 0) {
                saleEmployeePerformanceCommissionMapper.createSaleEmployeePerformanceCommissionPackageCardGeneralCard(employeePerformancePackageCardGeneralCardCommission);
            }
            //时效卡-员工业绩和提佣
            List<SaleEmployeePerformanceCommissionPackageCardGoodsEntity> employeePerformancePackageCardTimeCardCommission = new ArrayList<>();
            for (SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity : saleEntityPerformancePackageCardTimeCard) {
                if (saleEntityPerformancePackageCardGoodsEntity.SaleBillHandlerEntityList != null) {
                    for (SaleBillHandlerForm saleBillHandlerForm : saleEntityPerformancePackageCardGoodsEntity.SaleBillHandlerEntityList) {
                        SaleCommissionEntity saleCommissionEntity = saleEmployeePerformanceCommissionMapper.getSalePackageCardTimeCardCommission(saleEntityPerformancePackageCardGoodsEntity.EntityID, saleBillHandlerForm.EmployeeID, saleBillHandlerForm.SaleHandlerID, saleEntityPerformancePackageCardGoodsEntity.GoodsID);
                        SalePerformanceEntity salePerformanceEntity = saleEmployeePerformanceCommissionMapper.getSalePackageCardTimeCardPerformance(saleEntityPerformancePackageCardGoodsEntity.EntityID, saleBillHandlerForm.EmployeeID, saleBillHandlerForm.SaleHandlerID, saleEntityPerformancePackageCardGoodsEntity.GoodsID);
                        employeePerformancePackageCardTimeCardCommission.add(saleEmployeePerformanceCommissionPackageCard(saleEntityPerformancePackageCardGoodsEntity, saleBillHandlerForm, saleCommissionEntity, salePerformanceEntity));
                    }
                }
            }
            if (employeePerformancePackageCardTimeCardCommission.size() > 0) {
                saleEmployeePerformanceCommissionMapper.createSaleEmployeePerformanceCommissionPackageCardTimeCard(employeePerformancePackageCardTimeCardCommission);
            }
            //储值卡-员工业绩和提佣
            List<SaleEmployeePerformanceCommissionPackageCardGoodsEntity> employeePerformancePackageCardSavingCardCommission = new ArrayList<>();
            for (SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity : saleEntityPerformancePackageCardSavingCard) {
                if (saleEntityPerformancePackageCardGoodsEntity.SaleBillHandlerEntityList != null) {
                    for (SaleBillHandlerForm saleBillHandlerForm : saleEntityPerformancePackageCardGoodsEntity.SaleBillHandlerEntityList) {
                        SaleCommissionEntity saleCommissionEntity = saleEmployeePerformanceCommissionMapper.getSalePackageCardSavingCardCommission(saleEntityPerformancePackageCardGoodsEntity.EntityID, saleBillHandlerForm.EmployeeID, saleBillHandlerForm.SaleHandlerID, saleEntityPerformancePackageCardGoodsEntity.GoodsID);
                        SalePerformanceEntity salePerformanceEntity = saleEmployeePerformanceCommissionMapper.getSalePackageCardSavingCardPerformance(saleEntityPerformancePackageCardGoodsEntity.EntityID, saleBillHandlerForm.EmployeeID, saleBillHandlerForm.SaleHandlerID, saleEntityPerformancePackageCardGoodsEntity.GoodsID);
                        SaleEmployeePerformanceCommissionPackageCardGoodsEntity saleEmployeePerformanceCommissionPackageCardGoodsEntity = new SaleEmployeePerformanceCommissionPackageCardGoodsEntity();
                        saleEmployeePerformanceCommissionPackageCardGoodsEntity.SaleBillID = saleEntityPerformancePackageCardGoodsEntity.SaleBillID;//订单编号
                        saleEmployeePerformanceCommissionPackageCardGoodsEntity.EntityID = saleEntityPerformancePackageCardGoodsEntity.EntityID;//门店ID
                        saleEmployeePerformanceCommissionPackageCardGoodsEntity.BillDate = saleEntityPerformancePackageCardGoodsEntity.BillDate;//开单时间
                        saleEmployeePerformanceCommissionPackageCardGoodsEntity.SaleBillGoodsID = saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID;//商品明细ID
                        saleEmployeePerformanceCommissionPackageCardGoodsEntity.SaleBillPackageCardID = saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID;
                        saleEmployeePerformanceCommissionPackageCardGoodsEntity.PackageCardID = saleEntityPerformancePackageCardGoodsEntity.PackageCardID;
                        saleEmployeePerformanceCommissionPackageCardGoodsEntity.GoodsID = saleEntityPerformancePackageCardGoodsEntity.GoodsID;//商品ID
                        saleEmployeePerformanceCommissionPackageCardGoodsEntity.EmployeeID = saleBillHandlerForm.EmployeeID;
                        saleEmployeePerformanceCommissionPackageCardGoodsEntity.SaleHandlerID = saleBillHandlerForm.SaleHandlerID;

                        BigDecimal performancePayRate = salePerformanceEntity.PayRate == null ? BigDecimal.ZERO : salePerformanceEntity.PayRate;
                        BigDecimal payRate = BigDecimal.ZERO;
                        BigDecimal payFixed = BigDecimal.ZERO;
                        BigDecimal specialBenefit = BigDecimal.ZERO;
                        if (saleCommissionEntity != null) {
                            payRate = saleCommissionEntity.PayRate == null ? BigDecimal.ZERO : saleCommissionEntity.PayRate;
                            payFixed = saleCommissionEntity.PayFixed == null ? BigDecimal.ZERO : saleCommissionEntity.PayFixed;
                            specialBenefit = saleCommissionEntity.SpecialBenefit == null ? BigDecimal.ZERO : saleCommissionEntity.SpecialBenefit;
                        }
                        //现金
                        if (saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance.compareTo(BigDecimal.ZERO) == 0) {
                            saleEmployeePerformanceCommissionPackageCardGoodsEntity.PayPerformance = BigDecimal.ZERO;
                            saleEmployeePerformanceCommissionPackageCardGoodsEntity.PayRateCommission = BigDecimal.ZERO;
                            saleEmployeePerformanceCommissionPackageCardGoodsEntity.PayFixedCommission = BigDecimal.ZERO;
                        } else {
                            saleEmployeePerformanceCommissionPackageCardGoodsEntity.PayPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance.multiply(performancePayRate).multiply(saleBillHandlerForm.Scale).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);
                            saleEmployeePerformanceCommissionPackageCardGoodsEntity.PayRateCommission = saleEmployeePerformanceCommissionPackageCardGoodsEntity.PayPerformance.multiply(payRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                            saleEmployeePerformanceCommissionPackageCardGoodsEntity.PayFixedCommission = payFixed.multiply(BigDecimal.valueOf(saleEntityPerformancePackageCardGoodsEntity.Quantity * saleEntityPerformancePackageCardGoodsEntity.PackageCardQuantity)).multiply(saleEmployeePerformanceCommissionPackageCardGoodsEntity.PayPerformance).divide(saleEntityPerformancePackageCardGoodsEntity.TotalAmount, 2, BigDecimal.ROUND_HALF_UP);
                        }
                        //特殊补贴
                        saleEmployeePerformanceCommissionPackageCardGoodsEntity.SpecialBenefitCommission = saleEntityPerformancePackageCardGoodsEntity.TotalAmount.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : specialBenefit.multiply(saleBillHandlerForm.Scale.divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP)).multiply(BigDecimal.valueOf(saleEntityPerformancePackageCardGoodsEntity.Quantity * saleEntityPerformancePackageCardGoodsEntity.PackageCardQuantity)).multiply(saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance).divide(saleEntityPerformancePackageCardGoodsEntity.TotalAmount, 2, BigDecimal.ROUND_HALF_UP);
                        ;

                        employeePerformancePackageCardSavingCardCommission.add(saleEmployeePerformanceCommissionPackageCardGoodsEntity);
                    }
                }
            }
            if (employeePerformancePackageCardSavingCardCommission.size() > 0) {
                saleEmployeePerformanceCommissionMapper.createSaleEmployeePerformanceCommissionPackageCardSavingCard(employeePerformancePackageCardSavingCardCommission);
            }

            //是否计算成长值
            Boolean isCalculateCustomerGrowth = customerGrowthMapper.getCustomerGrowthSetting();
            CustomerEntity customerEntity = customerMapper.getCustomer(saleBillEntity.CustomerID);
            //升降级
            if (isCalculateCustomerGrowth && saleBillEntity.CustomerID != null && customerEntity.IsMember) {
                List<CustomerGrowthEntity> customerGrowthEntities = saleBillService.calculateCustomerGrowth("RefundSale", -1, saleBillEntity.ID, saleBillEntity.CustomerID);
                if (customerGrowthEntities.size() > 0)
                    customerGrowthMapper.createCustomerGrowth(customerGrowthEntities);
            }

            saleBillService.send(saleBillEntity.ID);
            return saleBillEntity.ID;
        }
        return "";
    }

    private SaleEmployeePerformanceCommissionGoodsEntity saleEmployeePerformanceCommission(SaleEntityPerformanceGoodsEntity saleEntityPerformanceGoodsEntity, SaleBillHandlerForm saleBillHandlerForm, SaleCommissionEntity saleCommissionEntity, SalePerformanceEntity salePerformanceEntity) {
        SaleEmployeePerformanceCommissionGoodsEntity saleEmployeePerformanceCommissionGoodsEntity = new SaleEmployeePerformanceCommissionGoodsEntity();
        saleEmployeePerformanceCommissionGoodsEntity.SaleBillID = saleEntityPerformanceGoodsEntity.SaleBillID;//订单编号
        saleEmployeePerformanceCommissionGoodsEntity.EntityID = saleEntityPerformanceGoodsEntity.EntityID;//门店ID
        saleEmployeePerformanceCommissionGoodsEntity.BillDate = saleEntityPerformanceGoodsEntity.BillDate;//开单时间
        saleEmployeePerformanceCommissionGoodsEntity.SaleBillGoodsID = saleEntityPerformanceGoodsEntity.SaleBillGoodsID;//商品明细ID
        saleEmployeePerformanceCommissionGoodsEntity.GoodsID = saleEntityPerformanceGoodsEntity.GoodsID;//商品ID
        saleEmployeePerformanceCommissionGoodsEntity.EmployeeID = saleBillHandlerForm.EmployeeID;
        saleEmployeePerformanceCommissionGoodsEntity.SaleHandlerID = saleBillHandlerForm.SaleHandlerID;

        BigDecimal performancePayRate = salePerformanceEntity.PayRate == null ? BigDecimal.ZERO : salePerformanceEntity.PayRate;
        BigDecimal performanceSavingCardRate = salePerformanceEntity.SavingCardRate == null ? BigDecimal.ZERO : salePerformanceEntity.SavingCardRate;
        BigDecimal performanceSavingCardLargessRate = salePerformanceEntity.SavingCardLargessRate == null ? BigDecimal.ZERO : salePerformanceEntity.SavingCardLargessRate;
        BigDecimal payRate = BigDecimal.ZERO;
        BigDecimal savingCardRate = BigDecimal.ZERO;
        BigDecimal savingCardLargessRate = BigDecimal.ZERO;
        BigDecimal payFixed = BigDecimal.ZERO;
        BigDecimal savingCardFixed = BigDecimal.ZERO;
        BigDecimal savingCardLargessFixed = BigDecimal.ZERO;
        BigDecimal specialBenefit = BigDecimal.ZERO;
        if (saleCommissionEntity != null) {
            payRate = saleCommissionEntity.PayRate == null ? BigDecimal.ZERO : saleCommissionEntity.PayRate;
            savingCardRate = saleCommissionEntity.SavingCardRate == null ? BigDecimal.ZERO : saleCommissionEntity.SavingCardRate;
            savingCardLargessRate = saleCommissionEntity.SavingCardLargessRate == null ? BigDecimal.ZERO : saleCommissionEntity.SavingCardLargessRate;
            payFixed = saleCommissionEntity.PayFixed == null ? BigDecimal.ZERO : saleCommissionEntity.PayFixed;
            savingCardFixed = saleCommissionEntity.SavingCardFixed == null ? BigDecimal.ZERO : saleCommissionEntity.SavingCardFixed;
            savingCardLargessFixed = saleCommissionEntity.SavingCardLargessFixed == null ? BigDecimal.ZERO : saleCommissionEntity.SavingCardLargessFixed;
            specialBenefit = saleCommissionEntity.SpecialBenefit == null ? BigDecimal.ZERO : saleCommissionEntity.SpecialBenefit;
        }
        //现金
        if (saleEntityPerformanceGoodsEntity.OriginPayPerformance.compareTo(BigDecimal.ZERO) == 0) {
            saleEmployeePerformanceCommissionGoodsEntity.PayPerformance = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
            saleEmployeePerformanceCommissionGoodsEntity.PayRateCommission = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
            saleEmployeePerformanceCommissionGoodsEntity.PayFixedCommission = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
        } else {
            saleEmployeePerformanceCommissionGoodsEntity.PayPerformance = saleEntityPerformanceGoodsEntity.OriginPayPerformance.multiply(performancePayRate).multiply(saleBillHandlerForm.Scale).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);
            saleEmployeePerformanceCommissionGoodsEntity.PayRateCommission = saleEmployeePerformanceCommissionGoodsEntity.PayPerformance.multiply(payRate).setScale(2, BigDecimal.ROUND_HALF_UP);
            saleEmployeePerformanceCommissionGoodsEntity.PayFixedCommission = payFixed.multiply(BigDecimal.valueOf(saleEntityPerformanceGoodsEntity.Quantity)).multiply(saleEmployeePerformanceCommissionGoodsEntity.PayPerformance).divide(saleEntityPerformanceGoodsEntity.TotalAmount, 2, BigDecimal.ROUND_HALF_UP);
        }
        //储值卡（非赠送）
        if (saleEntityPerformanceGoodsEntity.OriginSavingCardPerformance.compareTo(BigDecimal.ZERO) == 0) {
            saleEmployeePerformanceCommissionGoodsEntity.SavingCardPerformance = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
            saleEmployeePerformanceCommissionGoodsEntity.SavingCardRateCommission = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
            saleEmployeePerformanceCommissionGoodsEntity.SavingCardFixedCommission = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
        } else {
            saleEmployeePerformanceCommissionGoodsEntity.SavingCardPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardPerformance.multiply(performanceSavingCardRate).multiply(saleBillHandlerForm.Scale).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);
            saleEmployeePerformanceCommissionGoodsEntity.SavingCardRateCommission = saleEmployeePerformanceCommissionGoodsEntity.SavingCardPerformance.multiply(savingCardRate).setScale(2, BigDecimal.ROUND_HALF_UP);
            saleEmployeePerformanceCommissionGoodsEntity.SavingCardFixedCommission = savingCardFixed.multiply(BigDecimal.valueOf(saleEntityPerformanceGoodsEntity.Quantity)).multiply(saleEmployeePerformanceCommissionGoodsEntity.SavingCardPerformance).divide(saleEntityPerformanceGoodsEntity.TotalAmount, 2, BigDecimal.ROUND_HALF_UP);
        }
        //储值卡（赠送）
        if (saleEntityPerformanceGoodsEntity.OriginSavingCardLargessPerformance.compareTo(BigDecimal.ZERO) == 0) {
            saleEmployeePerformanceCommissionGoodsEntity.SavingCardLargessPerformance = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
            saleEmployeePerformanceCommissionGoodsEntity.SavingCardLargessRateCommission = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
            saleEmployeePerformanceCommissionGoodsEntity.SavingCardLargessFixedCommission = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
        } else {
            saleEmployeePerformanceCommissionGoodsEntity.SavingCardLargessPerformance = saleEntityPerformanceGoodsEntity.OriginSavingCardLargessPerformance.multiply(performanceSavingCardLargessRate).multiply(saleBillHandlerForm.Scale).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).setScale(2, BigDecimal.ROUND_HALF_UP);
            saleEmployeePerformanceCommissionGoodsEntity.SavingCardLargessRateCommission = saleEmployeePerformanceCommissionGoodsEntity.SavingCardLargessPerformance.multiply(savingCardLargessRate).setScale(2, BigDecimal.ROUND_HALF_UP);
            saleEmployeePerformanceCommissionGoodsEntity.SavingCardLargessFixedCommission = savingCardLargessFixed.multiply(BigDecimal.valueOf(saleEntityPerformanceGoodsEntity.Quantity)).multiply(saleEmployeePerformanceCommissionGoodsEntity.SavingCardLargessPerformance).divide(saleEntityPerformanceGoodsEntity.TotalAmount, 2, BigDecimal.ROUND_HALF_UP);
        }
        //特殊补贴
        saleEmployeePerformanceCommissionGoodsEntity.SpecialBenefitCommission = saleEntityPerformanceGoodsEntity.TotalAmount.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP) : specialBenefit.multiply(saleBillHandlerForm.Scale.divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP)).multiply(BigDecimal.valueOf(saleEntityPerformanceGoodsEntity.Quantity)).multiply(saleEntityPerformanceGoodsEntity.OriginPayPerformance.add(saleEntityPerformanceGoodsEntity.OriginSavingCardPerformance).add(saleEntityPerformanceGoodsEntity.OriginSavingCardLargessPerformance)).divide(saleEntityPerformanceGoodsEntity.TotalAmount, 2, BigDecimal.ROUND_HALF_UP);
        return saleEmployeePerformanceCommissionGoodsEntity;
    }

    private SaleEmployeePerformanceCommissionPackageCardGoodsEntity saleEmployeePerformanceCommissionPackageCard(SaleEntityPerformancePackageCardGoodsEntity saleEntityPerformancePackageCardGoodsEntity, SaleBillHandlerForm saleBillHandlerForm, SaleCommissionEntity saleCommissionEntity, SalePerformanceEntity salePerformanceEntity) {
        SaleEmployeePerformanceCommissionPackageCardGoodsEntity saleEmployeePerformanceCommissionPackageCardGoodsEntity = new SaleEmployeePerformanceCommissionPackageCardGoodsEntity();
        saleEmployeePerformanceCommissionPackageCardGoodsEntity.SaleBillID = saleEntityPerformancePackageCardGoodsEntity.SaleBillID;//订单编号
        saleEmployeePerformanceCommissionPackageCardGoodsEntity.EntityID = saleEntityPerformancePackageCardGoodsEntity.EntityID;//门店ID
        saleEmployeePerformanceCommissionPackageCardGoodsEntity.BillDate = saleEntityPerformancePackageCardGoodsEntity.BillDate;//开单时间
        saleEmployeePerformanceCommissionPackageCardGoodsEntity.SaleBillGoodsID = saleEntityPerformancePackageCardGoodsEntity.SaleBillGoodsID;//商品明细ID
        saleEmployeePerformanceCommissionPackageCardGoodsEntity.SaleBillPackageCardID = saleEntityPerformancePackageCardGoodsEntity.SaleBillPackageCardID;
        saleEmployeePerformanceCommissionPackageCardGoodsEntity.PackageCardID = saleEntityPerformancePackageCardGoodsEntity.PackageCardID;
        saleEmployeePerformanceCommissionPackageCardGoodsEntity.GoodsID = saleEntityPerformancePackageCardGoodsEntity.GoodsID;//商品ID
        saleEmployeePerformanceCommissionPackageCardGoodsEntity.EmployeeID = saleBillHandlerForm.EmployeeID;
        saleEmployeePerformanceCommissionPackageCardGoodsEntity.SaleHandlerID = saleBillHandlerForm.SaleHandlerID;

        BigDecimal performancePayRate = salePerformanceEntity.PayRate == null ? BigDecimal.ZERO : salePerformanceEntity.PayRate;
        BigDecimal performanceSavingCardRate = salePerformanceEntity.SavingCardRate == null ? BigDecimal.ZERO : salePerformanceEntity.SavingCardRate;
        BigDecimal performanceSavingCardLargessRate = salePerformanceEntity.SavingCardLargessRate == null ? BigDecimal.ZERO : salePerformanceEntity.SavingCardLargessRate;
        BigDecimal payRate = BigDecimal.ZERO;
        BigDecimal savingCardRate = BigDecimal.ZERO;
        BigDecimal savingCardLargessRate = BigDecimal.ZERO;
        BigDecimal payFixed = BigDecimal.ZERO;
        BigDecimal savingCardFixed = BigDecimal.ZERO;
        BigDecimal savingCardLargessFixed = BigDecimal.ZERO;
        BigDecimal specialBenefit = BigDecimal.ZERO;
        if (saleCommissionEntity != null) {
            payRate = saleCommissionEntity.PayRate == null ? BigDecimal.ZERO : saleCommissionEntity.PayRate;
            savingCardRate = saleCommissionEntity.SavingCardRate == null ? BigDecimal.ZERO : saleCommissionEntity.SavingCardRate;
            savingCardLargessRate = saleCommissionEntity.SavingCardLargessRate == null ? BigDecimal.ZERO : saleCommissionEntity.SavingCardLargessRate;
            payFixed = saleCommissionEntity.PayFixed == null ? BigDecimal.ZERO : saleCommissionEntity.PayFixed;
            savingCardFixed = saleCommissionEntity.SavingCardFixed == null ? BigDecimal.ZERO : saleCommissionEntity.SavingCardFixed;
            savingCardLargessFixed = saleCommissionEntity.SavingCardLargessFixed == null ? BigDecimal.ZERO : saleCommissionEntity.SavingCardLargessFixed;
            specialBenefit = saleCommissionEntity.SpecialBenefit == null ? BigDecimal.ZERO : saleCommissionEntity.SpecialBenefit;
        }
        //现金
        if (saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance.compareTo(BigDecimal.ZERO) == 0) {
            saleEmployeePerformanceCommissionPackageCardGoodsEntity.PayPerformance = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
            saleEmployeePerformanceCommissionPackageCardGoodsEntity.PayRateCommission = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
            saleEmployeePerformanceCommissionPackageCardGoodsEntity.PayFixedCommission = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
        } else {
            saleEmployeePerformanceCommissionPackageCardGoodsEntity.PayPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance.multiply(performancePayRate).multiply(saleBillHandlerForm.Scale).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);
            saleEmployeePerformanceCommissionPackageCardGoodsEntity.PayRateCommission = saleEmployeePerformanceCommissionPackageCardGoodsEntity.PayPerformance.multiply(payRate).setScale(2, BigDecimal.ROUND_HALF_UP);
            saleEmployeePerformanceCommissionPackageCardGoodsEntity.PayFixedCommission = payFixed.multiply(BigDecimal.valueOf(saleEntityPerformancePackageCardGoodsEntity.Quantity * saleEntityPerformancePackageCardGoodsEntity.PackageCardQuantity)).multiply(saleEmployeePerformanceCommissionPackageCardGoodsEntity.PayPerformance).divide(saleEntityPerformancePackageCardGoodsEntity.TotalAmount, 2, BigDecimal.ROUND_HALF_UP);
        }
        //储值卡（非赠送）
        if (saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardPerformance.compareTo(BigDecimal.ZERO) == 0) {
            saleEmployeePerformanceCommissionPackageCardGoodsEntity.SavingCardPerformance = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
            saleEmployeePerformanceCommissionPackageCardGoodsEntity.SavingCardRateCommission = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
            saleEmployeePerformanceCommissionPackageCardGoodsEntity.SavingCardFixedCommission = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
        } else {
            saleEmployeePerformanceCommissionPackageCardGoodsEntity.SavingCardPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardPerformance.multiply(performanceSavingCardRate).multiply(saleBillHandlerForm.Scale).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);
            saleEmployeePerformanceCommissionPackageCardGoodsEntity.SavingCardRateCommission = saleEmployeePerformanceCommissionPackageCardGoodsEntity.SavingCardPerformance.multiply(savingCardRate).setScale(2, BigDecimal.ROUND_HALF_UP);
            saleEmployeePerformanceCommissionPackageCardGoodsEntity.SavingCardFixedCommission = savingCardFixed.multiply(BigDecimal.valueOf(saleEntityPerformancePackageCardGoodsEntity.Quantity * saleEntityPerformancePackageCardGoodsEntity.PackageCardQuantity)).multiply(saleEmployeePerformanceCommissionPackageCardGoodsEntity.SavingCardPerformance).divide(saleEntityPerformancePackageCardGoodsEntity.TotalAmount, 2, BigDecimal.ROUND_HALF_UP);
        }
        //储值卡（赠送）
        if (saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardLargessPerformance.compareTo(BigDecimal.ZERO) == 0) {
            saleEmployeePerformanceCommissionPackageCardGoodsEntity.SavingCardLargessPerformance = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
            saleEmployeePerformanceCommissionPackageCardGoodsEntity.SavingCardLargessRateCommission = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
            saleEmployeePerformanceCommissionPackageCardGoodsEntity.SavingCardLargessFixedCommission = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
        } else {
            saleEmployeePerformanceCommissionPackageCardGoodsEntity.SavingCardLargessPerformance = saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardLargessPerformance.multiply(performanceSavingCardLargessRate).multiply(saleBillHandlerForm.Scale).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);
            saleEmployeePerformanceCommissionPackageCardGoodsEntity.SavingCardLargessRateCommission = saleEmployeePerformanceCommissionPackageCardGoodsEntity.SavingCardLargessPerformance.multiply(savingCardLargessRate).setScale(2, BigDecimal.ROUND_HALF_UP);
            saleEmployeePerformanceCommissionPackageCardGoodsEntity.SavingCardLargessFixedCommission = savingCardLargessFixed.multiply(BigDecimal.valueOf(saleEntityPerformancePackageCardGoodsEntity.Quantity * saleEntityPerformancePackageCardGoodsEntity.PackageCardQuantity)).multiply(saleEmployeePerformanceCommissionPackageCardGoodsEntity.SavingCardLargessPerformance).divide(saleEntityPerformancePackageCardGoodsEntity.TotalAmount, 2, BigDecimal.ROUND_HALF_UP);
        }
        //特殊补贴
        saleEmployeePerformanceCommissionPackageCardGoodsEntity.SpecialBenefitCommission = saleEntityPerformancePackageCardGoodsEntity.TotalAmount.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP) : specialBenefit.multiply(saleBillHandlerForm.Scale.divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP)).multiply(BigDecimal.valueOf(saleEntityPerformancePackageCardGoodsEntity.Quantity * saleEntityPerformancePackageCardGoodsEntity.PackageCardQuantity)).multiply(saleEntityPerformancePackageCardGoodsEntity.OriginPayPerformance.add(saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardPerformance).add(saleEntityPerformancePackageCardGoodsEntity.OriginSavingCardLargessPerformance)).divide(saleEntityPerformancePackageCardGoodsEntity.TotalAmount, 2, BigDecimal.ROUND_HALF_UP);
        return saleEmployeePerformanceCommissionPackageCardGoodsEntity;
    }

    public RefundRateForm getProductAmount(ProductAccountEntity productAccountEntity, BigDecimal amount) {
        RefundRateForm refundRateForm = new RefundRateForm();
        BigDecimal payAmount = BigDecimal.ZERO;
        BigDecimal savingCardDeductionAmount = BigDecimal.ZERO;
        BigDecimal savingCardDeductionLargessAmount = BigDecimal.ZERO;
        BigDecimal totalAmount = productAccountEntity.TotalAmount.subtract(productAccountEntity.ArrearAmount);//综合
        if (!productAccountEntity.IsLargess) {
            if (productAccountEntity.SavingCardDeductionLargessAmount.compareTo(BigDecimal.ZERO) == 1) {
                savingCardDeductionLargessAmount = productAccountEntity.SavingCardDeductionLargessAmount.multiply(amount).divide(totalAmount, 2, BigDecimal.ROUND_HALF_UP);
                if (productAccountEntity.PayAmount.compareTo(BigDecimal.ZERO) > 0 && productAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
                    payAmount = productAccountEntity.PayAmount.multiply(amount).divide(totalAmount, 2, BigDecimal.ROUND_HALF_UP);
                    savingCardDeductionAmount = amount.subtract(payAmount).subtract(savingCardDeductionLargessAmount);
                }
                if (productAccountEntity.PayAmount.compareTo(BigDecimal.ZERO) > 0 && productAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) == 0) {
                    payAmount = amount.subtract(savingCardDeductionLargessAmount);
                }
                if (productAccountEntity.PayAmount.compareTo(BigDecimal.ZERO) == 0 && productAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
                    savingCardDeductionAmount = amount.subtract(savingCardDeductionLargessAmount);
                }
            } else {
                if (productAccountEntity.PayAmount.compareTo(BigDecimal.ZERO) > 0 && productAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
                    payAmount = productAccountEntity.PayAmount.multiply(amount).divide(totalAmount, 2, BigDecimal.ROUND_HALF_UP);
                    savingCardDeductionAmount = amount.subtract(payAmount);
                }
                if (productAccountEntity.PayAmount.compareTo(BigDecimal.ZERO) > 0 && productAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) == 0) {
                    payAmount = amount;
                }
                if (productAccountEntity.PayAmount.compareTo(BigDecimal.ZERO) == 0 && productAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
                    savingCardDeductionAmount = amount;
                }
            }
        }
        refundRateForm.savingCardDeductionAmount = savingCardDeductionAmount;
        refundRateForm.payAmount = payAmount;
        refundRateForm.savingCardDeductionLargessAmount = savingCardDeductionLargessAmount;
        return refundRateForm;
    }

    public RefundRateForm getProjectAmount(ProjectAccountEntity projectAccountEntity, BigDecimal amount) {
        RefundRateForm refundRateForm = new RefundRateForm();
        BigDecimal payAmount = BigDecimal.ZERO;
        BigDecimal savingCardDeductionAmount = BigDecimal.ZERO;
        BigDecimal savingCardDeductionLargessAmount = BigDecimal.ZERO;
        BigDecimal totalAmount = projectAccountEntity.TotalAmount.subtract(projectAccountEntity.ArrearAmount);//综合
        if (!projectAccountEntity.IsLargess) {
            if (projectAccountEntity.SavingCardDeductionLargessAmount.compareTo(BigDecimal.ZERO) == 1) {
                savingCardDeductionLargessAmount = projectAccountEntity.SavingCardDeductionLargessAmount.multiply(amount).divide(totalAmount, 2, BigDecimal.ROUND_HALF_UP);
                if (projectAccountEntity.PayAmount.compareTo(BigDecimal.ZERO) > 0 && projectAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
                    payAmount = projectAccountEntity.PayAmount.multiply(amount).divide(totalAmount, 2, BigDecimal.ROUND_HALF_UP);
                    savingCardDeductionAmount = amount.subtract(payAmount).subtract(savingCardDeductionLargessAmount);
                }
                if (projectAccountEntity.PayAmount.compareTo(BigDecimal.ZERO) > 0 && projectAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) == 0) {
                    payAmount = amount.subtract(savingCardDeductionLargessAmount);
                }
                if (projectAccountEntity.PayAmount.compareTo(BigDecimal.ZERO) == 0 && projectAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
                    savingCardDeductionAmount = amount.subtract(savingCardDeductionLargessAmount);
                }
            } else {
                if (projectAccountEntity.PayAmount.compareTo(BigDecimal.ZERO) > 0 && projectAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
                    payAmount = projectAccountEntity.PayAmount.multiply(amount).divide(totalAmount, 2, BigDecimal.ROUND_HALF_UP);
                    savingCardDeductionAmount = amount.subtract(payAmount);
                }
                if (projectAccountEntity.PayAmount.compareTo(BigDecimal.ZERO) > 0 && projectAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) == 0) {
                    payAmount = amount;
                }
                if (projectAccountEntity.PayAmount.compareTo(BigDecimal.ZERO) == 0 && projectAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
                    savingCardDeductionAmount = amount;
                }
            }
        }
        refundRateForm.savingCardDeductionAmount = savingCardDeductionAmount;
        refundRateForm.payAmount = payAmount;
        refundRateForm.savingCardDeductionLargessAmount = savingCardDeductionLargessAmount;
        return refundRateForm;
    }

    public RefundRateForm getGeneralCardAmount(GeneralCardAccountEntity generalCardAccountEntity, BigDecimal amount) {
        RefundRateForm refundRateForm = new RefundRateForm();
        BigDecimal payAmount = BigDecimal.ZERO;
        BigDecimal savingCardDeductionAmount = BigDecimal.ZERO;
        BigDecimal savingCardDeductionLargessAmount = BigDecimal.ZERO;
        BigDecimal totalAmount = generalCardAccountEntity.TotalAmount.subtract(generalCardAccountEntity.ArrearAmount);//综合
        if (!generalCardAccountEntity.IsLargess) {
            if (generalCardAccountEntity.SavingCardDeductionLargessAmount.compareTo(BigDecimal.ZERO) == 1) {
                savingCardDeductionLargessAmount = generalCardAccountEntity.SavingCardDeductionLargessAmount.multiply(amount).divide(totalAmount, 2, BigDecimal.ROUND_HALF_UP);
                if (generalCardAccountEntity.PayAmount.compareTo(BigDecimal.ZERO) > 0 && generalCardAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
                    payAmount = generalCardAccountEntity.PayAmount.multiply(amount).divide(totalAmount, 2, BigDecimal.ROUND_HALF_UP);
                    savingCardDeductionAmount = amount.subtract(payAmount).subtract(savingCardDeductionLargessAmount);
                }
                if (generalCardAccountEntity.PayAmount.compareTo(BigDecimal.ZERO) > 0 && generalCardAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) == 0) {
                    payAmount = amount.subtract(savingCardDeductionLargessAmount);
                }
                if (generalCardAccountEntity.PayAmount.compareTo(BigDecimal.ZERO) == 0 && generalCardAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
                    savingCardDeductionAmount = amount.subtract(savingCardDeductionLargessAmount);
                }
            } else {
                if (generalCardAccountEntity.PayAmount.compareTo(BigDecimal.ZERO) > 0 && generalCardAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
                    payAmount = generalCardAccountEntity.PayAmount.multiply(amount).divide(totalAmount, 2, BigDecimal.ROUND_HALF_UP);
                    savingCardDeductionAmount = amount.subtract(payAmount);
                }
                if (generalCardAccountEntity.PayAmount.compareTo(BigDecimal.ZERO) > 0 && generalCardAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) == 0) {
                    payAmount = amount;
                }
                if (generalCardAccountEntity.PayAmount.compareTo(BigDecimal.ZERO) == 0 && generalCardAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
                    savingCardDeductionAmount = amount;
                }
            }
        }
        refundRateForm.savingCardDeductionAmount = savingCardDeductionAmount;
        refundRateForm.payAmount = payAmount;
        refundRateForm.savingCardDeductionLargessAmount = savingCardDeductionLargessAmount;
        return refundRateForm;
    }

    public RefundRateForm getTimeCardAmount(TimeCardAccountEntity timeCardAccountEntity, BigDecimal amount) {
        RefundRateForm refundRateForm = new RefundRateForm();
        BigDecimal payAmount = BigDecimal.ZERO;
        BigDecimal savingCardDeductionAmount = BigDecimal.ZERO;
        BigDecimal savingCardDeductionLargessAmount = BigDecimal.ZERO;
        BigDecimal totalAmount = timeCardAccountEntity.TotalAmount.subtract(timeCardAccountEntity.ArrearAmount);//综合
        if (!timeCardAccountEntity.IsLargess) {
            if (timeCardAccountEntity.SavingCardDeductionLargessAmount.compareTo(BigDecimal.ZERO) == 1) {
                savingCardDeductionLargessAmount = timeCardAccountEntity.SavingCardDeductionLargessAmount.multiply(amount).divide(totalAmount, 2, BigDecimal.ROUND_HALF_UP);
                if (timeCardAccountEntity.PayAmount.compareTo(BigDecimal.ZERO) > 0 && timeCardAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
                    payAmount = timeCardAccountEntity.PayAmount.multiply(amount).divide(totalAmount, 2, BigDecimal.ROUND_HALF_UP);
                    savingCardDeductionAmount = amount.subtract(payAmount).subtract(savingCardDeductionLargessAmount);
                }
                if (timeCardAccountEntity.PayAmount.compareTo(BigDecimal.ZERO) > 0 && timeCardAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) == 0) {
                    payAmount = amount.subtract(savingCardDeductionLargessAmount);
                }
                if (timeCardAccountEntity.PayAmount.compareTo(BigDecimal.ZERO) == 0 && timeCardAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
                    savingCardDeductionAmount = amount.subtract(savingCardDeductionLargessAmount);
                }
            } else {
                if (timeCardAccountEntity.PayAmount.compareTo(BigDecimal.ZERO) > 0 && timeCardAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
                    payAmount = timeCardAccountEntity.PayAmount.multiply(amount).divide(totalAmount, 2, BigDecimal.ROUND_HALF_UP);
                    savingCardDeductionAmount = amount.subtract(payAmount);
                }
                if (timeCardAccountEntity.PayAmount.compareTo(BigDecimal.ZERO) > 0 && timeCardAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) == 0) {
                    payAmount = amount;
                }
                if (timeCardAccountEntity.PayAmount.compareTo(BigDecimal.ZERO) == 0 && timeCardAccountEntity.SavingCardDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
                    savingCardDeductionAmount = amount;
                }
            }
        }
        refundRateForm.savingCardDeductionAmount = savingCardDeductionAmount;
        refundRateForm.payAmount = payAmount;
        refundRateForm.savingCardDeductionLargessAmount = savingCardDeductionLargessAmount;
        return refundRateForm;
    }


    public BaseOutput updatePayMethod(SaleBillUpdatePayMethodUpdateForm form) {
        refundBillMapper.updatePayMethod(form.ID, form.PayMethodID);
        return BaseOutput.success();
    }
}



