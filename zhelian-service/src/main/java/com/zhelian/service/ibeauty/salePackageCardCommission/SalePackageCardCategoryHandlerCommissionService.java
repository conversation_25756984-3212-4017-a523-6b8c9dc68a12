package com.zhelian.service.ibeauty.salePackageCardCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.SalePackageCardCategoryHandlerCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SalePackageCardCategoryHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SalePackageCardCategoryHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.input.SalePackageCardCategoryHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SalePackageCardCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SalePackageCardCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SalePackageCardCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.SalePackageCardCategoryHandlerCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SalePackageCardCategoryHandlerCommissionService {

    @Autowired
    SalePackageCardCategoryHandlerCommissionMapper salePackageCardCategoryHandlerCommissionMapper;


    public BaseOutput allSalePackageCardCategoryHandlerCommission(SalePackageCardCategoryHandlerCommissionQueryForm form) {
        List<SalePackageCardCategoryHandlerCommissionOutputForm> list = salePackageCardCategoryHandlerCommissionMapper.allSalePackageCardCategoryHandlerCommission(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSalePackageCardCategoryHandlerCommission(SalePackageCardCategoryHandlerCommissionAddForm form) {
        salePackageCardCategoryHandlerCommissionMapper.deleteSalePackageCardCategoryHandler(form.EntityID, form.CategoryID);
        salePackageCardCategoryHandlerCommissionMapper.deleteSalePackageCardCategoryHandlerJobType(form.EntityID, form.CategoryID);

        if (form.Handler != null && form.Handler.size() > 0) {
            List<SalePackageCardCategoryHandlerCommissionEntity> list = new ArrayList<>();
            List<SalePackageCardCategoryHandlerJobTypeCommissionEntity> handlerJobTypeCommissionList = new ArrayList<>();
            for (SalePackageCardCategoryHandlerAddForm salePackageCardCategoryHandlerAddForm : form.Handler) {
                if (salePackageCardCategoryHandlerAddForm.PayRate != null || salePackageCardCategoryHandlerAddForm.PayFixed != null ||
                        salePackageCardCategoryHandlerAddForm.SavingCardRate != null || salePackageCardCategoryHandlerAddForm.SavingCardFixed != null ||
                        salePackageCardCategoryHandlerAddForm.SavingCardLargessRate != null || salePackageCardCategoryHandlerAddForm.SavingCardLargessFixed != null ||
                        salePackageCardCategoryHandlerAddForm.SpecialBenefit != null) {
                    SalePackageCardCategoryHandlerCommissionEntity salePackageCardCategoryHandlerCommissionEntity = new SalePackageCardCategoryHandlerCommissionEntity();
                    salePackageCardCategoryHandlerCommissionEntity.EntityID = form.EntityID;
                    salePackageCardCategoryHandlerCommissionEntity.PackageCardCategoryID = form.CategoryID;
                    salePackageCardCategoryHandlerCommissionEntity.PackageCardSaleHandlerID = salePackageCardCategoryHandlerAddForm.SaleHandlerID;
                    salePackageCardCategoryHandlerCommissionEntity.PayFixed = salePackageCardCategoryHandlerAddForm.PayFixed;
                    salePackageCardCategoryHandlerCommissionEntity.PayRate = salePackageCardCategoryHandlerAddForm.PayRate;
                    salePackageCardCategoryHandlerCommissionEntity.SavingCardFixed = salePackageCardCategoryHandlerAddForm.SavingCardFixed;
                    salePackageCardCategoryHandlerCommissionEntity.SavingCardRate = salePackageCardCategoryHandlerAddForm.SavingCardRate;
                    salePackageCardCategoryHandlerCommissionEntity.SavingCardLargessFixed = salePackageCardCategoryHandlerAddForm.SavingCardLargessFixed;
                    salePackageCardCategoryHandlerCommissionEntity.SavingCardLargessRate = salePackageCardCategoryHandlerAddForm.SavingCardLargessRate;
                    salePackageCardCategoryHandlerCommissionEntity.SpecialBenefit = salePackageCardCategoryHandlerAddForm.SpecialBenefit;
                    list.add(salePackageCardCategoryHandlerCommissionEntity);
                }
                if (salePackageCardCategoryHandlerAddForm.HandlerJobTypeCommission != null && salePackageCardCategoryHandlerAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (SalePackageCardCategoryHandlerJobTypeCommissionAddForm salePackageCardCategoryHandlerJobTypeCommissionAddForm : salePackageCardCategoryHandlerAddForm.HandlerJobTypeCommission) {
                        if (salePackageCardCategoryHandlerJobTypeCommissionAddForm.PayRate != null || salePackageCardCategoryHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                salePackageCardCategoryHandlerJobTypeCommissionAddForm.SavingCardRate != null || salePackageCardCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed != null ||
                                salePackageCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null || salePackageCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed != null ||
                                salePackageCardCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SalePackageCardCategoryHandlerJobTypeCommissionEntity salePackageCardCategoryHandlerJobTypeCommissionEntity = new SalePackageCardCategoryHandlerJobTypeCommissionEntity();
                            salePackageCardCategoryHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            salePackageCardCategoryHandlerJobTypeCommissionEntity.PackageCardCategoryID = form.CategoryID;
                            salePackageCardCategoryHandlerJobTypeCommissionEntity.PackageCardSaleHandlerID = salePackageCardCategoryHandlerAddForm.SaleHandlerID;
                            salePackageCardCategoryHandlerJobTypeCommissionEntity.JobTypeID = salePackageCardCategoryHandlerJobTypeCommissionAddForm.JobTypeID;
                            salePackageCardCategoryHandlerJobTypeCommissionEntity.PayFixed = salePackageCardCategoryHandlerJobTypeCommissionAddForm.PayFixed;
                            salePackageCardCategoryHandlerJobTypeCommissionEntity.PayRate = salePackageCardCategoryHandlerJobTypeCommissionAddForm.PayRate;
                            salePackageCardCategoryHandlerJobTypeCommissionEntity.SavingCardFixed = salePackageCardCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed;
                            salePackageCardCategoryHandlerJobTypeCommissionEntity.SavingCardRate = salePackageCardCategoryHandlerJobTypeCommissionAddForm.SavingCardRate;
                            salePackageCardCategoryHandlerJobTypeCommissionEntity.SavingCardLargessFixed = salePackageCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed;
                            salePackageCardCategoryHandlerJobTypeCommissionEntity.SavingCardLargessRate = salePackageCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            salePackageCardCategoryHandlerJobTypeCommissionEntity.SpecialBenefit = salePackageCardCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            handlerJobTypeCommissionList.add(salePackageCardCategoryHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }

            if (list != null && list.size() > 0) {
                List<List<SalePackageCardCategoryHandlerCommissionEntity>> subs = Lists.partition(list, 20);
                for (List<SalePackageCardCategoryHandlerCommissionEntity> entityList : subs) {
                    salePackageCardCategoryHandlerCommissionMapper.insertSalePackageCardCategoryHandlerCommission(entityList);
                }
            }
            if (handlerJobTypeCommissionList != null && handlerJobTypeCommissionList.size() > 0) {
                List<List<SalePackageCardCategoryHandlerJobTypeCommissionEntity>> subs = Lists.partition(handlerJobTypeCommissionList, 20);
                for (List<SalePackageCardCategoryHandlerJobTypeCommissionEntity> entityList : subs) {
                    salePackageCardCategoryHandlerCommissionMapper.insertSalePackageCardCategoryHandlerJobTypeCommission(entityList);
                }
            }
        }
        return BaseOutput.success();
    }
}