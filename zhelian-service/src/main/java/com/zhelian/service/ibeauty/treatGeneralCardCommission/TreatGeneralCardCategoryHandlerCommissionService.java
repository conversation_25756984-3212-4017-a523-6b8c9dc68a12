package com.zhelian.service.ibeauty.treatGeneralCardCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.TreatGeneralCardCategoryHandlerCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.TreatGeneralCardCategoryHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.TreatGeneralCardCategoryHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatGeneralCardCategoryHandlerAddForm;
import com.zhelian.model.ibeauty.form.output.TreatCardCategoryHandlerCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatGeneralCardCategoryHandlerCommissionService {

    @Autowired
    TreatGeneralCardCategoryHandlerCommissionMapper treatGeneralCardCategoryHandlerCommissionMapper;


    public BaseOutput allTreatGeneralCardCategoryHandlerCommission(TreatCardCategoryHandlerCommissionQueryForm form) {
        List<TreatCardCategoryHandlerCommissionOutputForm> list = treatGeneralCardCategoryHandlerCommissionMapper.allTreatGeneralCardCategoryHandlerCommission(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatGeneralCardCategoryHandlerCommission(TreatCardCategoryHandlerCommissionAddForm form) {
        treatGeneralCardCategoryHandlerCommissionMapper.deleteTreatGeneralCardCategoryHandler(form.EntityID, form.CategoryID);
        treatGeneralCardCategoryHandlerCommissionMapper.deleteTreatGeneralCardCategoryHandlerJobType(form.EntityID, form.CategoryID);
        List<TreatGeneralCardCategoryHandlerCommissionEntity> list = new ArrayList<>();
        List<TreatGeneralCardCategoryHandlerJobTypeCommissionEntity> handlerJobTypeCommissionList = new ArrayList<>();
        if (form.CardHandler != null && form.CardHandler.size() > 0) {
            for (TreatGeneralCardCategoryHandlerAddForm treatGeneralCardCategoryHandlerAddForm : form.CardHandler) {
                if (treatGeneralCardCategoryHandlerAddForm.PayRate != null || treatGeneralCardCategoryHandlerAddForm.PayFixed != null ||
                        treatGeneralCardCategoryHandlerAddForm.CardRate != null || treatGeneralCardCategoryHandlerAddForm.CardFixed != null ||
                        treatGeneralCardCategoryHandlerAddForm.CardLargessRate != null || treatGeneralCardCategoryHandlerAddForm.CardLargessFixed != null ||
                        treatGeneralCardCategoryHandlerAddForm.LargessRate != null || treatGeneralCardCategoryHandlerAddForm.LargessFixed != null ||
                        treatGeneralCardCategoryHandlerAddForm.SpecialBenefit != null) {
                    TreatGeneralCardCategoryHandlerCommissionEntity treatGeneralCardCategoryHandlerCommissionEntity = new TreatGeneralCardCategoryHandlerCommissionEntity();
                    treatGeneralCardCategoryHandlerCommissionEntity.EntityID = form.EntityID;
                    treatGeneralCardCategoryHandlerCommissionEntity.GeneralCardCategoryID = form.CategoryID;
                    treatGeneralCardCategoryHandlerCommissionEntity.GeneralCardTreatHandlerID = treatGeneralCardCategoryHandlerAddForm.TreatHandlerID;
                    treatGeneralCardCategoryHandlerCommissionEntity.PayRate = treatGeneralCardCategoryHandlerAddForm.PayRate;
                    treatGeneralCardCategoryHandlerCommissionEntity.PayFixed = treatGeneralCardCategoryHandlerAddForm.PayFixed;
                    treatGeneralCardCategoryHandlerCommissionEntity.CardRate = treatGeneralCardCategoryHandlerAddForm.CardRate;
                    treatGeneralCardCategoryHandlerCommissionEntity.CardFixed = treatGeneralCardCategoryHandlerAddForm.CardFixed;
                    treatGeneralCardCategoryHandlerCommissionEntity.CardLargessRate = treatGeneralCardCategoryHandlerAddForm.CardLargessRate;
                    treatGeneralCardCategoryHandlerCommissionEntity.CardLargessFixed = treatGeneralCardCategoryHandlerAddForm.CardLargessFixed;
                    treatGeneralCardCategoryHandlerCommissionEntity.LargessRate = treatGeneralCardCategoryHandlerAddForm.LargessRate;
                    treatGeneralCardCategoryHandlerCommissionEntity.LargessFixed = treatGeneralCardCategoryHandlerAddForm.LargessFixed;
                    treatGeneralCardCategoryHandlerCommissionEntity.SpecialBenefit = treatGeneralCardCategoryHandlerAddForm.SpecialBenefit;
                    list.add(treatGeneralCardCategoryHandlerCommissionEntity);
                }
                if (treatGeneralCardCategoryHandlerAddForm.HandlerJobTypeCommission != null && treatGeneralCardCategoryHandlerAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (TreatCardCategoryHandlerJobTypeCommissionAddForm treatCardCategoryHandlerJobTypeCommissionAddForm : treatGeneralCardCategoryHandlerAddForm.HandlerJobTypeCommission) {
                        if (treatCardCategoryHandlerJobTypeCommissionAddForm.PayRate != null || treatCardCategoryHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                treatCardCategoryHandlerJobTypeCommissionAddForm.CardRate != null || treatCardCategoryHandlerJobTypeCommissionAddForm.CardFixed != null ||
                                treatCardCategoryHandlerJobTypeCommissionAddForm.CardLargessRate != null || treatCardCategoryHandlerJobTypeCommissionAddForm.CardLargessFixed != null ||
                                treatCardCategoryHandlerJobTypeCommissionAddForm.LargessFixed != null || treatCardCategoryHandlerJobTypeCommissionAddForm.LargessRate != null ||
                                treatCardCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            TreatGeneralCardCategoryHandlerJobTypeCommissionEntity commissionEntity = new TreatGeneralCardCategoryHandlerJobTypeCommissionEntity();
                            commissionEntity.EntityID = form.EntityID;
                            commissionEntity.GeneralCardCategoryID = form.CategoryID;
                            commissionEntity.GeneralCardTreatHandlerID = treatGeneralCardCategoryHandlerAddForm.TreatHandlerID;
                            commissionEntity.JobTypeID = treatCardCategoryHandlerJobTypeCommissionAddForm.JobTypeID;
                            commissionEntity.PayRate = treatCardCategoryHandlerJobTypeCommissionAddForm.PayRate;
                            commissionEntity.PayFixed = treatCardCategoryHandlerJobTypeCommissionAddForm.PayFixed;
                            commissionEntity.CardRate = treatCardCategoryHandlerJobTypeCommissionAddForm.CardRate;
                            commissionEntity.CardFixed = treatCardCategoryHandlerJobTypeCommissionAddForm.CardFixed;
                            commissionEntity.CardLargessRate = treatCardCategoryHandlerJobTypeCommissionAddForm.CardLargessRate;
                            commissionEntity.CardLargessFixed = treatCardCategoryHandlerJobTypeCommissionAddForm.CardLargessFixed;
                            commissionEntity.LargessRate = treatCardCategoryHandlerJobTypeCommissionAddForm.LargessRate;
                            commissionEntity.LargessFixed = treatCardCategoryHandlerJobTypeCommissionAddForm.LargessFixed;
                            commissionEntity.SpecialBenefit = treatCardCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            handlerJobTypeCommissionList.add(commissionEntity);
                        }
                    }
                }
            }
        }
        if (list != null && list.size() > 0) {
            List<List<TreatGeneralCardCategoryHandlerCommissionEntity>> subs = Lists.partition(list, 20);
            for (List<TreatGeneralCardCategoryHandlerCommissionEntity> entityList : subs) {
                treatGeneralCardCategoryHandlerCommissionMapper.insertTreatGeneralCardCategoryHandlerCommission(entityList);
            }
        }

        if (handlerJobTypeCommissionList != null && handlerJobTypeCommissionList.size() > 0) {
            List<List<TreatGeneralCardCategoryHandlerJobTypeCommissionEntity>> subs = Lists.partition(handlerJobTypeCommissionList, 20);
            for (List<TreatGeneralCardCategoryHandlerJobTypeCommissionEntity> entityList : subs) {
                treatGeneralCardCategoryHandlerCommissionMapper.insertTreatGeneralCardCategoryHandlerJobTypeCommission(entityList);
            }
        }
        return BaseOutput.success();
    }
}