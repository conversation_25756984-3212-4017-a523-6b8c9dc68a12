package com.zhelian.service.ibeauty.arrear;

import com.zhelian.core.utils.enumeration.WeiXinOfficialAccountMessage;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.ArrearAddForm;
import com.zhelian.model.ibeauty.form.output.ArrearBillIDOutputForm;
import com.zhelian.service.ibeauty.SealingAccount.SealingAccountService;
import com.zhelian.service.weixin.WeiXinMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ArrearCommonService {
    @Autowired
    ArrearService arrearService;
    @Autowired
    WeiXinMessageService weiXinMessageService;
    @Autowired
    SealingAccountService sealingAccountService;

    public BaseOutput create(ArrearAddForm form, String channel) throws Exception {
        Boolean replacementOrderRestriction = sealingAccountService.getReplacementOrderRestriction(form.BillDate);
        if(replacementOrderRestriction){
            return BaseOutput.failed("补单时间处于关账时间内，请选择其他时间");
        }
        String bill = arrearService.createBill(form, channel);
        weiXinMessageService.setSaleBillWxMessage(bill, WeiXinOfficialAccountMessage.ArrearsNotice);

        ArrearBillIDOutputForm arrearBillIDOutputForm = new ArrearBillIDOutputForm();
        arrearBillIDOutputForm.BillID = bill;
        return BaseOutputForm.success(arrearBillIDOutputForm);
    }
}
