package com.zhelian.service.ibeauty.saleSavingCardCommission;


import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.SaleSavingCardCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SaleSavingCardCommissionEntity;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardsCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.SaleSavingCardCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleSavingCardCommissionService {

    @Autowired
    SaleSavingCardCommissionMapper saleSavingCardCommissionMapper;

    public BaseOutput allSaleSavingCardCommission(SaleSavingCardCategoryHandlerCommissionQueryForm form) {
        List<SaleSavingCardCommissionOutputForm> list = saleSavingCardCommissionMapper.allSaleSavingCardCommission(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleSavingCardCommission(SaleSavingCardsCommissionAddForm form) {

        saleSavingCardCommissionMapper.deleteSaleSavingCardCommissionByEntityAndCategory(form.EntityID, form.CategoryID);
        List<SaleSavingCardCommissionEntity> list = new ArrayList<>();
        if (form.SavingCard != null && form.SavingCard.size() > 0) {
            for (SaleSavingCardCommissionAddForm saleSavingCardCommissionAddForm : form.SavingCard) {
                if (saleSavingCardCommissionAddForm.PayFixed != null || saleSavingCardCommissionAddForm.PayRate != null ||
                        saleSavingCardCommissionAddForm.SpecialBenefit != null) {
                    SaleSavingCardCommissionEntity saleSavingCardCommissionEntity = new SaleSavingCardCommissionEntity();
                    saleSavingCardCommissionEntity.EntityID = form.EntityID;
                    saleSavingCardCommissionEntity.SavingCardID = saleSavingCardCommissionAddForm.SavingCardID;
                    saleSavingCardCommissionEntity.PayFixed = saleSavingCardCommissionAddForm.PayFixed;
                    saleSavingCardCommissionEntity.PayRate = saleSavingCardCommissionAddForm.PayRate;
                    saleSavingCardCommissionEntity.SpecialBenefit = saleSavingCardCommissionAddForm.SpecialBenefit;
                    list.add(saleSavingCardCommissionEntity);
                }
            }
            List<List<SaleSavingCardCommissionEntity>> subs = Lists.partition(list, 20);
            for (List<SaleSavingCardCommissionEntity> entityList : subs) {
                saleSavingCardCommissionMapper.insertSaleSavingCardCommission(entityList);
            }
        }
        return BaseOutput.success();
    }

}