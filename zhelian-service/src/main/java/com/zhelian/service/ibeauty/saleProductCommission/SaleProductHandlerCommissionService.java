package com.zhelian.service.ibeauty.saleProductCommission;

import com.zhelian.mapper.ibeauty.SalePackageCardProductHandlerCommissionMapper;
import com.zhelian.mapper.ibeauty.SaleProductHandlerCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SalePackageCardProductHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SalePackageCardProductHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleProductHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleProductHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProductHandlersCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.SaleProductHandlerCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleProductHandlerCommissionService {

    @Autowired
    SaleProductHandlerCommissionMapper saleProductHandlerCommissionMapper;
    @Autowired
    SalePackageCardProductHandlerCommissionMapper salePackageCardProductHandlerCommissionMapper;

    public BaseOutput allSaleProductHandlerCommission(SaleProductHandlerCommissionQueryForm form) {
        List<SaleProductHandlerCommissionOutputForm> list = saleProductHandlerCommissionMapper.allSaleProductHandlerCommission(form.EntityID, form.ProductID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSalePackageCardProductHandlerCommission(SaleProductHandlerCommissionQueryForm form) {
        List<SaleProductHandlerCommissionOutputForm> list = salePackageCardProductHandlerCommissionMapper.allSalePackageCardProductHandlerCommission(form.EntityID, form.ProductID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleProductHandlerCommission(SaleProductHandlersCommissionAddForm form) {

        saleProductHandlerCommissionMapper.deleteSaleProductHandler(form.EntityID, form.ProductID);
        saleProductHandlerCommissionMapper.deleteSaleProductHandlerJobType(form.EntityID, form.ProductID);
        List<SaleProductHandlerCommissionEntity> listSaleProductHandlerCommissionEntity = new ArrayList<>();
        List<SaleProductHandlerJobTypeCommissionEntity> listSaleProductHandlerJobTypeCommissionEntity = new ArrayList<>();
        if (form.ProductHandler != null && form.ProductHandler.size() > 0) {
            for (SaleProductHandlerCommissionAddForm saleProductHandlerAddForm : form.ProductHandler) {
                if (saleProductHandlerAddForm.PayRate != null || saleProductHandlerAddForm.PayFixed != null ||
                        saleProductHandlerAddForm.SavingCardRate != null || saleProductHandlerAddForm.SavingCardFixed != null ||
                        saleProductHandlerAddForm.SavingCardLargessRate != null || saleProductHandlerAddForm.SavingCardLargessFixed != null ||
                        saleProductHandlerAddForm.SpecialBenefit != null) {
                    SaleProductHandlerCommissionEntity saleProductHandlerCommissionEntity = new SaleProductHandlerCommissionEntity();
                    saleProductHandlerCommissionEntity.EntityID = form.EntityID;
                    saleProductHandlerCommissionEntity.ProductID = form.ProductID;
                    saleProductHandlerCommissionEntity.ProductSaleHandlerID = saleProductHandlerAddForm.SaleHandlerID;
                    saleProductHandlerCommissionEntity.PayFixed = saleProductHandlerAddForm.PayFixed;
                    saleProductHandlerCommissionEntity.PayRate = saleProductHandlerAddForm.PayRate;
                    saleProductHandlerCommissionEntity.SavingCardFixed = saleProductHandlerAddForm.SavingCardFixed;
                    saleProductHandlerCommissionEntity.SavingCardRate = saleProductHandlerAddForm.SavingCardRate;
                    saleProductHandlerCommissionEntity.SavingCardLargessFixed = saleProductHandlerAddForm.SavingCardLargessFixed;
                    saleProductHandlerCommissionEntity.SavingCardLargessRate = saleProductHandlerAddForm.SavingCardLargessRate;
                    saleProductHandlerCommissionEntity.SpecialBenefit = saleProductHandlerAddForm.SpecialBenefit;
                    listSaleProductHandlerCommissionEntity.add(saleProductHandlerCommissionEntity);
                }

                if (saleProductHandlerAddForm.HandlerJobTypeCommission != null && saleProductHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleProductCategoryHandlerJobTypeCommissionAddForm saleProductCategoryHandlerJobTypeCommissionAddForm : saleProductHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleProductCategoryHandlerJobTypeCommissionAddForm.PayRate != null || saleProductCategoryHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardRate != null || saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed != null ||
                                saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null || saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed != null ||
                                saleProductCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SaleProductHandlerJobTypeCommissionEntity saleProductHandlerJobTypeCommissionEntity = new SaleProductHandlerJobTypeCommissionEntity();
                            saleProductHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            saleProductHandlerJobTypeCommissionEntity.ProductID = form.ProductID;
                            saleProductHandlerJobTypeCommissionEntity.ProductSaleHandlerID = saleProductHandlerAddForm.SaleHandlerID;
                            saleProductHandlerJobTypeCommissionEntity.JobTypeID = saleProductCategoryHandlerJobTypeCommissionAddForm.JobTypeID;
                            saleProductHandlerJobTypeCommissionEntity.PayFixed = saleProductCategoryHandlerJobTypeCommissionAddForm.PayFixed;
                            saleProductHandlerJobTypeCommissionEntity.PayRate = saleProductCategoryHandlerJobTypeCommissionAddForm.PayRate;
                            saleProductHandlerJobTypeCommissionEntity.SavingCardFixed = saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed;
                            saleProductHandlerJobTypeCommissionEntity.SavingCardRate = saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardRate;
                            saleProductHandlerJobTypeCommissionEntity.SavingCardLargessFixed = saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed;
                            saleProductHandlerJobTypeCommissionEntity.SavingCardLargessRate = saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            saleProductHandlerJobTypeCommissionEntity.SpecialBenefit = saleProductCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            listSaleProductHandlerJobTypeCommissionEntity.add(saleProductHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
        }

        salePackageCardProductHandlerCommissionMapper.deleteSalePackageCardProductHandler(form.EntityID, form.ProductID);
        salePackageCardProductHandlerCommissionMapper.deleteSalePackageCardProductHandlerJobType(form.EntityID, form.ProductID);
        List<SalePackageCardProductHandlerCommissionEntity> listSalePackageCardProductHandlerCommissionEntity = new ArrayList<>();
        List<SalePackageCardProductHandlerJobTypeCommissionEntity> listSalePackageCardProductHandlerJobTypeCommissionEntity = new ArrayList<>();
        if (form.PackageCardProductHandler != null && form.PackageCardProductHandler.size() > 0) {
            for (SaleProductHandlerCommissionAddForm saleProductHandlerAddForm : form.PackageCardProductHandler) {
                if (saleProductHandlerAddForm.PayRate != null || saleProductHandlerAddForm.PayFixed != null ||
                        saleProductHandlerAddForm.SavingCardRate != null || saleProductHandlerAddForm.SavingCardFixed != null ||
                        saleProductHandlerAddForm.SavingCardLargessRate != null || saleProductHandlerAddForm.SavingCardLargessFixed != null ||
                        saleProductHandlerAddForm.SpecialBenefit != null) {
                    SalePackageCardProductHandlerCommissionEntity salePackageCardProductHandlerCommissionEntity = new SalePackageCardProductHandlerCommissionEntity();
                    salePackageCardProductHandlerCommissionEntity.EntityID = form.EntityID;
                    salePackageCardProductHandlerCommissionEntity.ProductID = form.ProductID;
                    salePackageCardProductHandlerCommissionEntity.PackageCardSaleHandlerID = saleProductHandlerAddForm.SaleHandlerID;
                    salePackageCardProductHandlerCommissionEntity.PayFixed = saleProductHandlerAddForm.PayFixed;
                    salePackageCardProductHandlerCommissionEntity.PayRate = saleProductHandlerAddForm.PayRate;
                    salePackageCardProductHandlerCommissionEntity.SavingCardFixed = saleProductHandlerAddForm.SavingCardFixed;
                    salePackageCardProductHandlerCommissionEntity.SavingCardRate = saleProductHandlerAddForm.SavingCardRate;
                    salePackageCardProductHandlerCommissionEntity.SavingCardLargessFixed = saleProductHandlerAddForm.SavingCardLargessFixed;
                    salePackageCardProductHandlerCommissionEntity.SavingCardLargessRate = saleProductHandlerAddForm.SavingCardLargessRate;
                    salePackageCardProductHandlerCommissionEntity.SpecialBenefit = saleProductHandlerAddForm.SpecialBenefit;
                    listSalePackageCardProductHandlerCommissionEntity.add(salePackageCardProductHandlerCommissionEntity);
                }

                if (saleProductHandlerAddForm.HandlerJobTypeCommission != null && saleProductHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleProductCategoryHandlerJobTypeCommissionAddForm saleProductCategoryHandlerJobTypeCommissionAddForm : saleProductHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleProductCategoryHandlerJobTypeCommissionAddForm.PayRate != null || saleProductCategoryHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardRate != null || saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed != null ||
                                saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null || saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed != null ||
                                saleProductCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SalePackageCardProductHandlerJobTypeCommissionEntity salePackageCardProductHandlerJobTypeCommissionEntity = new SalePackageCardProductHandlerJobTypeCommissionEntity();
                            salePackageCardProductHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            salePackageCardProductHandlerJobTypeCommissionEntity.ProductID = form.ProductID;
                            salePackageCardProductHandlerJobTypeCommissionEntity.PackageCardSaleHandlerID = saleProductHandlerAddForm.SaleHandlerID;
                            salePackageCardProductHandlerJobTypeCommissionEntity.JobTypeID = saleProductCategoryHandlerJobTypeCommissionAddForm.JobTypeID;
                            salePackageCardProductHandlerJobTypeCommissionEntity.PayFixed = saleProductCategoryHandlerJobTypeCommissionAddForm.PayFixed;
                            salePackageCardProductHandlerJobTypeCommissionEntity.PayRate = saleProductCategoryHandlerJobTypeCommissionAddForm.PayRate;
                            salePackageCardProductHandlerJobTypeCommissionEntity.SavingCardFixed = saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed;
                            salePackageCardProductHandlerJobTypeCommissionEntity.SavingCardRate = saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardRate;
                            salePackageCardProductHandlerJobTypeCommissionEntity.SavingCardLargessFixed = saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed;
                            salePackageCardProductHandlerJobTypeCommissionEntity.SavingCardLargessRate = saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            salePackageCardProductHandlerJobTypeCommissionEntity.SpecialBenefit = saleProductCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            listSalePackageCardProductHandlerJobTypeCommissionEntity.add(salePackageCardProductHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
        }

        if (listSaleProductHandlerCommissionEntity.size() > 0)
            saleProductHandlerCommissionMapper.updateSaleProductHandlerCommission(listSaleProductHandlerCommissionEntity);

        if (listSaleProductHandlerJobTypeCommissionEntity.size() > 0)
            saleProductHandlerCommissionMapper.insertSaleProductHandlerJobTypeCommission(listSaleProductHandlerJobTypeCommissionEntity);

        if (listSalePackageCardProductHandlerCommissionEntity.size() > 0)
            salePackageCardProductHandlerCommissionMapper.updateSalePackageCardProductHandlerCommission(listSalePackageCardProductHandlerCommissionEntity);

        if (listSalePackageCardProductHandlerJobTypeCommissionEntity.size() > 0)
            salePackageCardProductHandlerCommissionMapper.insertSalePackageCardProductHandlerJobTypeCommission(listSalePackageCardProductHandlerJobTypeCommissionEntity);
        return BaseOutput.success();
    }
}