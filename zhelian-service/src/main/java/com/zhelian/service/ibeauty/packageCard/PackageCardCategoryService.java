package com.zhelian.service.ibeauty.packageCard;

import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.PackageCardCategoryMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.PackageCardCategoryEntity;
import com.zhelian.model.ibeauty.form.input.PackageCardCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.PackageCardCategoryMoveForm;
import com.zhelian.model.ibeauty.form.input.PackageCardCategoryQueryForm;
import com.zhelian.model.ibeauty.form.input.PackageCardCategoryUpdateForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

@Service
public class PackageCardCategoryService {

    @Autowired
    PackageCardCategoryMapper mapper;

    public BaseOutput packageCardCategoryAll(PackageCardCategoryQueryForm form) {
        return BaseOutputForm.success(mapper.packageCardCategoryAll(form.Name, form.Active));
    }

    public BaseOutput createPackageCardCategory(PackageCardCategoryAddForm form) {
        Integer Sequence = mapper.getMaxSequence();
        PackageCardCategoryEntity entity = new PackageCardCategoryEntity();
        entity.Name = form.Name;
        entity.Sequence = Sequence != null ? Sequence + 1 : 1;
        mapper.createPackageCardCategory(entity);
        return BaseOutput.success();
    }

    public BaseOutput updatePackageCardCategory(PackageCardCategoryUpdateForm form) {
        if (!form.Active) {
            if (mapper.packageCardByCategoryCount(form.ID) > 0) {
                return BaseOutput.failed("该分类下有套餐卡，不可以改为无效");
            }
        }
        PackageCardCategoryEntity entity = new PackageCardCategoryEntity();
        entity.Name = form.Name;
        entity.ID = form.ID;
        entity.Active = form.Active;
        mapper.updatePackageCardCategory(entity);
        return BaseOutput.success();
    }

    public BaseOutput move(PackageCardCategoryMoveForm form) {
        LinkedList<Integer> linkedList = mapper.packageCardCategoryAscList(form.MoveID);
        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<PackageCardCategoryEntity> list = new ArrayList<>();
        for (Integer ID : linkedList) {
            PackageCardCategoryEntity entity = new PackageCardCategoryEntity();
            entity.ID = ID;
            entity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(entity);
        }
        mapper.updateSequence(list);
        return BaseOutput.success();
    }
}
