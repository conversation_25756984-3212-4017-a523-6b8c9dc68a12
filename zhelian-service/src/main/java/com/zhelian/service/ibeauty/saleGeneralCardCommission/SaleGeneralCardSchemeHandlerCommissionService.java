package com.zhelian.service.ibeauty.saleGeneralCardCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.SaleGeneralCardSchemeHandlerCommissionMapper;
import com.zhelian.mapper.ibeauty.SalePackageCardGeneralCardSchemeHandlerCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SaleGeneralCardSchemeHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleGeneralCardSchemeHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.entity.SalePackageCardGeneralCardSchemeHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SalePackageCardGeneralCardSchemeHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardSchemeHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardSchemeHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardSchemeHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardsSchemeHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.SaleGeneralCardSchemeHandlerCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleGeneralCardSchemeHandlerCommissionService {

    @Autowired
    SaleGeneralCardSchemeHandlerCommissionMapper saleGeneralCardSchemeHandlerCommissionMapper;
    @Autowired
    SalePackageCardGeneralCardSchemeHandlerCommissionMapper salePackageCardGeneralCardSchemeHandlerCommissionMapper;

    public BaseOutput allSaleGeneralCardSchemeHandlerCommission(SaleGeneralCardSchemeHandlerCommissionQueryForm form) {
        List<SaleGeneralCardSchemeHandlerCommissionOutputForm> list = saleGeneralCardSchemeHandlerCommissionMapper.allSaleGeneralCardSchemeHandlerCommission(form.EntityID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSalePackageCardGeneralCardSchemeHandlerCommission(SaleGeneralCardSchemeHandlerCommissionQueryForm form) {
        List<SaleGeneralCardSchemeHandlerCommissionOutputForm> list = salePackageCardGeneralCardSchemeHandlerCommissionMapper.allSalePackageCardGeneralCardSchemeHandlerCommission(form.EntityID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleGeneralCardSchemeHandlerCommission(SaleGeneralCardsSchemeHandlerCommissionAddForm form) {

        saleGeneralCardSchemeHandlerCommissionMapper.deleteSaleGeneralCardSchemeHandler(form.EntityID);
        saleGeneralCardSchemeHandlerCommissionMapper.deleteSaleGeneralCardSchemeHandlerJobType(form.EntityID);
        List<SaleGeneralCardSchemeHandlerCommissionEntity> listSaleGeneralCardSchemeHandlerCommissionEntity = new ArrayList<>();
        List<SaleGeneralCardSchemeHandlerJobTypeCommissionEntity> listSaleGeneralCardSchemeHandlerJobTypeCommissionEntity = new ArrayList<>();
        if (form.GeneralCardHandler != null && form.GeneralCardHandler.size() > 0) {
            for (SaleGeneralCardSchemeHandlerCommissionAddForm saleGeneralCardSchemeHandlerAddForm : form.GeneralCardHandler) {
                if (saleGeneralCardSchemeHandlerAddForm.PayRate != null || saleGeneralCardSchemeHandlerAddForm.PayFixed != null ||
                        saleGeneralCardSchemeHandlerAddForm.SavingCardRate != null || saleGeneralCardSchemeHandlerAddForm.SavingCardFixed != null ||
                        saleGeneralCardSchemeHandlerAddForm.SavingCardLargessRate != null || saleGeneralCardSchemeHandlerAddForm.SavingCardLargessFixed != null ||
                        saleGeneralCardSchemeHandlerAddForm.SpecialBenefit != null) {
                    SaleGeneralCardSchemeHandlerCommissionEntity saleGeneralCardSchemeHandlerCommissionEntity = new SaleGeneralCardSchemeHandlerCommissionEntity();
                    saleGeneralCardSchemeHandlerCommissionEntity.EntityID = form.EntityID;
                    saleGeneralCardSchemeHandlerCommissionEntity.GeneralCardSaleHandlerID = saleGeneralCardSchemeHandlerAddForm.SaleHandlerID;
                    saleGeneralCardSchemeHandlerCommissionEntity.PayFixed = saleGeneralCardSchemeHandlerAddForm.PayFixed;
                    saleGeneralCardSchemeHandlerCommissionEntity.PayRate = saleGeneralCardSchemeHandlerAddForm.PayRate;
                    saleGeneralCardSchemeHandlerCommissionEntity.SavingCardFixed = saleGeneralCardSchemeHandlerAddForm.SavingCardFixed;
                    saleGeneralCardSchemeHandlerCommissionEntity.SavingCardRate = saleGeneralCardSchemeHandlerAddForm.SavingCardRate;
                    saleGeneralCardSchemeHandlerCommissionEntity.SavingCardLargessFixed = saleGeneralCardSchemeHandlerAddForm.SavingCardLargessFixed;
                    saleGeneralCardSchemeHandlerCommissionEntity.SavingCardLargessRate = saleGeneralCardSchemeHandlerAddForm.SavingCardLargessRate;
                    saleGeneralCardSchemeHandlerCommissionEntity.SpecialBenefit = saleGeneralCardSchemeHandlerAddForm.SpecialBenefit;
                    listSaleGeneralCardSchemeHandlerCommissionEntity.add(saleGeneralCardSchemeHandlerCommissionEntity);
                }

                if (saleGeneralCardSchemeHandlerAddForm.HandlerJobTypeCommission != null && saleGeneralCardSchemeHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleGeneralCardSchemeHandlerJobTypeCommissionAddForm saleGeneralCardSchemeHandlerJobTypeCommissionAddForm : saleGeneralCardSchemeHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.PayRate != null || saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.SavingCardRate != null || saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.SavingCardFixed != null ||
                                saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null || saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessFixed != null ||
                                saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SaleGeneralCardSchemeHandlerJobTypeCommissionEntity saleGeneralCardSchemeHandlerJobTypeCommissionEntity = new SaleGeneralCardSchemeHandlerJobTypeCommissionEntity();
                            saleGeneralCardSchemeHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            saleGeneralCardSchemeHandlerJobTypeCommissionEntity.GeneralCardSaleHandlerID = saleGeneralCardSchemeHandlerAddForm.SaleHandlerID;
                            saleGeneralCardSchemeHandlerJobTypeCommissionEntity.JobTypeID = saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.JobTypeID;
                            saleGeneralCardSchemeHandlerJobTypeCommissionEntity.PayFixed = saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.PayFixed;
                            saleGeneralCardSchemeHandlerJobTypeCommissionEntity.PayRate = saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.PayRate;
                            saleGeneralCardSchemeHandlerJobTypeCommissionEntity.SavingCardFixed = saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.SavingCardFixed;
                            saleGeneralCardSchemeHandlerJobTypeCommissionEntity.SavingCardRate = saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.SavingCardRate;
                            saleGeneralCardSchemeHandlerJobTypeCommissionEntity.SavingCardLargessFixed = saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessFixed;
                            saleGeneralCardSchemeHandlerJobTypeCommissionEntity.SavingCardLargessRate = saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            saleGeneralCardSchemeHandlerJobTypeCommissionEntity.SpecialBenefit = saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            listSaleGeneralCardSchemeHandlerJobTypeCommissionEntity.add(saleGeneralCardSchemeHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
        }

        salePackageCardGeneralCardSchemeHandlerCommissionMapper.deleteSalePackageCardGeneralCardSchemeHandler(form.EntityID);
        salePackageCardGeneralCardSchemeHandlerCommissionMapper.deleteSalePackageCardGeneralCardSchemeHandlerJobType(form.EntityID);
        List<SalePackageCardGeneralCardSchemeHandlerCommissionEntity> listSalePackageCardGeneralCardSchemeHandlerCommissionEntity = new ArrayList<>();
        List<SalePackageCardGeneralCardSchemeHandlerJobTypeCommissionEntity> listSalePackageCardGeneralCardSchemeHandlerJobTypeCommissionEntity = new ArrayList<>();
        if (form.PackageCardGeneralCardHandler != null && form.PackageCardGeneralCardHandler.size() > 0) {
            for (SaleGeneralCardSchemeHandlerCommissionAddForm saleGeneralCardSchemeHandlerAddForm : form.PackageCardGeneralCardHandler) {
                if (saleGeneralCardSchemeHandlerAddForm.PayRate != null || saleGeneralCardSchemeHandlerAddForm.PayFixed != null ||
                        saleGeneralCardSchemeHandlerAddForm.SavingCardRate != null || saleGeneralCardSchemeHandlerAddForm.SavingCardFixed != null ||
                        saleGeneralCardSchemeHandlerAddForm.SavingCardLargessRate != null || saleGeneralCardSchemeHandlerAddForm.SavingCardLargessFixed != null ||
                        saleGeneralCardSchemeHandlerAddForm.SpecialBenefit != null) {
                    SalePackageCardGeneralCardSchemeHandlerCommissionEntity salePackageCardGeneralCardSchemeHandlerCommissionEntity = new SalePackageCardGeneralCardSchemeHandlerCommissionEntity();
                    salePackageCardGeneralCardSchemeHandlerCommissionEntity.EntityID = form.EntityID;
                    salePackageCardGeneralCardSchemeHandlerCommissionEntity.PackageCardSaleHandlerID = saleGeneralCardSchemeHandlerAddForm.SaleHandlerID;
                    salePackageCardGeneralCardSchemeHandlerCommissionEntity.PayFixed = saleGeneralCardSchemeHandlerAddForm.PayFixed;
                    salePackageCardGeneralCardSchemeHandlerCommissionEntity.PayRate = saleGeneralCardSchemeHandlerAddForm.PayRate;
                    salePackageCardGeneralCardSchemeHandlerCommissionEntity.SavingCardFixed = saleGeneralCardSchemeHandlerAddForm.SavingCardFixed;
                    salePackageCardGeneralCardSchemeHandlerCommissionEntity.SavingCardRate = saleGeneralCardSchemeHandlerAddForm.SavingCardRate;
                    salePackageCardGeneralCardSchemeHandlerCommissionEntity.SavingCardLargessFixed = saleGeneralCardSchemeHandlerAddForm.SavingCardLargessFixed;
                    salePackageCardGeneralCardSchemeHandlerCommissionEntity.SavingCardLargessRate = saleGeneralCardSchemeHandlerAddForm.SavingCardLargessRate;
                    salePackageCardGeneralCardSchemeHandlerCommissionEntity.SpecialBenefit = saleGeneralCardSchemeHandlerAddForm.SpecialBenefit;
                    listSalePackageCardGeneralCardSchemeHandlerCommissionEntity.add(salePackageCardGeneralCardSchemeHandlerCommissionEntity);
                }

                if (saleGeneralCardSchemeHandlerAddForm.HandlerJobTypeCommission != null && saleGeneralCardSchemeHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleGeneralCardSchemeHandlerJobTypeCommissionAddForm saleGeneralCardSchemeHandlerJobTypeCommissionAddForm : saleGeneralCardSchemeHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.PayRate != null || saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.SavingCardRate != null || saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.SavingCardFixed != null ||
                                saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null || saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessFixed != null ||
                                saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SalePackageCardGeneralCardSchemeHandlerJobTypeCommissionEntity salePackageCardGeneralCardSchemeHandlerJobTypeCommissionEntity = new SalePackageCardGeneralCardSchemeHandlerJobTypeCommissionEntity();
                            salePackageCardGeneralCardSchemeHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            salePackageCardGeneralCardSchemeHandlerJobTypeCommissionEntity.PackageCardSaleHandlerID = saleGeneralCardSchemeHandlerAddForm.SaleHandlerID;
                            salePackageCardGeneralCardSchemeHandlerJobTypeCommissionEntity.JobTypeID = saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.JobTypeID;
                            salePackageCardGeneralCardSchemeHandlerJobTypeCommissionEntity.PayFixed = saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.PayFixed;
                            salePackageCardGeneralCardSchemeHandlerJobTypeCommissionEntity.PayRate = saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.PayRate;
                            salePackageCardGeneralCardSchemeHandlerJobTypeCommissionEntity.SavingCardFixed = saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.SavingCardFixed;
                            salePackageCardGeneralCardSchemeHandlerJobTypeCommissionEntity.SavingCardRate = saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.SavingCardRate;
                            salePackageCardGeneralCardSchemeHandlerJobTypeCommissionEntity.SavingCardLargessFixed = saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessFixed;
                            salePackageCardGeneralCardSchemeHandlerJobTypeCommissionEntity.SavingCardLargessRate = saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            salePackageCardGeneralCardSchemeHandlerJobTypeCommissionEntity.SpecialBenefit = saleGeneralCardSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            listSalePackageCardGeneralCardSchemeHandlerJobTypeCommissionEntity.add(salePackageCardGeneralCardSchemeHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
        }

        if (listSaleGeneralCardSchemeHandlerCommissionEntity != null && listSaleGeneralCardSchemeHandlerCommissionEntity.size() > 0) {
            List<List<SaleGeneralCardSchemeHandlerCommissionEntity>> subs = Lists.partition(listSaleGeneralCardSchemeHandlerCommissionEntity, 20);
            for (List<SaleGeneralCardSchemeHandlerCommissionEntity> entityList : subs) {
                saleGeneralCardSchemeHandlerCommissionMapper.updateSaleGeneralCardSchemeHandlerCommission(entityList);
            }
        }
        if (listSaleGeneralCardSchemeHandlerJobTypeCommissionEntity != null && listSaleGeneralCardSchemeHandlerJobTypeCommissionEntity.size() > 0) {
            List<List<SaleGeneralCardSchemeHandlerJobTypeCommissionEntity>> subs = Lists.partition(listSaleGeneralCardSchemeHandlerJobTypeCommissionEntity, 20);
            for (List<SaleGeneralCardSchemeHandlerJobTypeCommissionEntity> entityList : subs) {
                saleGeneralCardSchemeHandlerCommissionMapper.insertSaleGeneralCardSchemeHandlerJobTypeCommission(entityList);
            }
        }
        if (listSalePackageCardGeneralCardSchemeHandlerCommissionEntity != null && listSalePackageCardGeneralCardSchemeHandlerCommissionEntity.size() > 0) {
            List<List<SalePackageCardGeneralCardSchemeHandlerCommissionEntity>> subs = Lists.partition(listSalePackageCardGeneralCardSchemeHandlerCommissionEntity, 20);
            for (List<SalePackageCardGeneralCardSchemeHandlerCommissionEntity> entityList : subs) {
                salePackageCardGeneralCardSchemeHandlerCommissionMapper.updateSalePackageCardGeneralCardSchemeHandlerCommission(entityList);
            }
        }
        if (listSalePackageCardGeneralCardSchemeHandlerJobTypeCommissionEntity != null && listSalePackageCardGeneralCardSchemeHandlerJobTypeCommissionEntity.size() > 0) {
            List<List<SalePackageCardGeneralCardSchemeHandlerJobTypeCommissionEntity>> subs = Lists.partition(listSalePackageCardGeneralCardSchemeHandlerJobTypeCommissionEntity, 20);
            for (List<SalePackageCardGeneralCardSchemeHandlerJobTypeCommissionEntity> entityList : subs) {
                salePackageCardGeneralCardSchemeHandlerCommissionMapper.insertSalePackageCardGeneralCardSchemeHandlerJobTypeCommission(entityList);
            }
        }

        return BaseOutput.success();
    }
}