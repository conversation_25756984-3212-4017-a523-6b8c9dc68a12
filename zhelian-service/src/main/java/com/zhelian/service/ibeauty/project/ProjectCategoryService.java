package com.zhelian.service.ibeauty.project;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.ProjectCategoryMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.ProjectCategoryEntity;
import com.zhelian.model.ibeauty.form.input.ProjectCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.ProjectCategoryMoveForm;
import com.zhelian.model.ibeauty.form.input.ProjectCategoryQueryForm;
import com.zhelian.model.ibeauty.form.input.ProjectCategoryUpdateForm;
import com.zhelian.model.ibeauty.form.output.ProjectCategoryOutputForm;
import com.zhelian.model.ibeauty.form.output.TopProjectCategoryOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import static com.zhelian.core.utils.ListUtil.listToTree;

/**
 * @className: ProjectCategoryService
 * @description:
 * @version: V1.0
 * @author: xuehao
 * @date: 2020/3/17 2:14 PM
 */
@Service
public class ProjectCategoryService {

    @Autowired
    ProjectCategoryMapper projectCategoryMapper;

    public BaseOutput projectCategoryList(ProjectCategoryQueryForm form) {

        List<ProjectCategoryOutputForm> list = projectCategoryMapper.projectCategoryList(form.Name, form.Active);
        JSONArray result = listToTree(JSONArray.parseArray(JSON.toJSONString(list, JSONWriter.Feature.WriteMapNullValue)), "ID", "ParentID", "Child");
        return BaseOutputForm.success(result);
    }

    public BaseOutput getValidProjectCategoryValid() {
        List<ProjectCategoryOutputForm> list = projectCategoryMapper.getValidProjectCategoryValid();
        JSONArray result = listToTree(JSONArray.parseArray(JSON.toJSONString(list, JSONWriter.Feature.WriteMapNullValue)), "ID", "ParentID", "Child");
        return BaseOutputForm.success(result);
    }

    public BaseOutput getTopProjectList(ProjectCategoryQueryForm form) {
        List<TopProjectCategoryOutputForm> list = projectCategoryMapper.getTopProjectList(form.Active);
        return BaseOutputForm.success(list);
    }


    public BaseOutput createProjectCategory(ProjectCategoryAddForm form) {
        Integer Sequence = projectCategoryMapper.getMaxSequence();
        ProjectCategoryEntity projectCategoryEntity = new ProjectCategoryEntity();
        if (form.ParentID != 0) {
            ProjectCategoryEntity projectCategoryEntity1 = projectCategoryMapper.getProjectCategoryEntity(form.ParentID);
            if (projectCategoryEntity1 != null && projectCategoryEntity1.ParentID != 0) {
                return BaseOutput.failed("上级分类不属于一级分类");
            }
        }
        projectCategoryEntity.ParentID = form.ParentID;
        projectCategoryEntity.Name = form.Name;
        projectCategoryEntity.Sequence = Sequence != null ? Sequence + 1 : 1;
        projectCategoryMapper.createProjectCategory(projectCategoryEntity);

        return BaseOutput.success();

    }

    public BaseOutput updateProjectCategory(ProjectCategoryUpdateForm form) {

        if (form.Active == false) {
            if (form.ParentID == 0) {
                Integer childCount = projectCategoryMapper.childActiveCount(form.ID);
                if (childCount > 0)
                    return BaseOutput.failed("该分类下的子类为有效，不可以改为无效");
            } else {
                Integer count = projectCategoryMapper.projectByCategoryCount(form.ID);
                if (count > 0)
                    return BaseOutput.failed("该分类下有项目，不可以改为无效");
            }
        } else {
            if (form.ParentID != 0) {
                ProjectCategoryEntity projectCategoryEntity1 = projectCategoryMapper.getProjectCategoryEntity(form.ParentID);
                if (projectCategoryEntity1.Active == false)
                    return BaseOutput.failed("该分类的父集为无效，不可以改为有效");
            }

        }

        ProjectCategoryEntity projectCategoryEntity = new ProjectCategoryEntity();
        if (form.ParentID != 0) {
            ProjectCategoryEntity projectCategoryEntity1 = projectCategoryMapper.getProjectCategoryEntity(form.ParentID);
            if (projectCategoryEntity1 != null && projectCategoryEntity1.ParentID != 0) {
                return BaseOutput.failed("上级分类不属于一级分类");
            }
        }
        projectCategoryEntity.ParentID = form.ParentID;
        projectCategoryEntity.Active = form.Active;
        projectCategoryEntity.Name = form.Name;
        projectCategoryEntity.ID = form.ID;
        projectCategoryMapper.updateProjectCategory(projectCategoryEntity);
        return BaseOutput.success();
    }

    public BaseOutput move(ProjectCategoryMoveForm form) {

        LinkedList<Integer> linkedList = projectCategoryMapper.getASCList(form.MoveID, form.DestParentID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<ProjectCategoryEntity> list = new ArrayList<>();
        for (Integer ID : linkedList) {
            ProjectCategoryEntity projectCategoryEntity = new ProjectCategoryEntity();
            projectCategoryEntity.ID = ID;
            projectCategoryEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(projectCategoryEntity);
        }

        projectCategoryMapper.updateSequence(list);
        return BaseOutput.success();
    }

}