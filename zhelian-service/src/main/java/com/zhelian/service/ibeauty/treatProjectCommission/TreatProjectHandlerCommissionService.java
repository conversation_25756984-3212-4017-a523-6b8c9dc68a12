package com.zhelian.service.ibeauty.treatProjectCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.TreatProjectHandlerCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.TreatProjectHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.TreatProjectHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.input.TreatProjectCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectHandlersCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.TreatProjectHandlerCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatProjectHandlerCommissionService {

    @Autowired
    TreatProjectHandlerCommissionMapper treatProjectHandlerCommissionMapper;

    public BaseOutput allTreatProjectHandlerCommission(TreatProjectHandlerCommissionQueryForm form) {
        List<TreatProjectHandlerCommissionOutputForm> list = treatProjectHandlerCommissionMapper.allTreatProjectHandlerCommission(form.EntityID, form.ProjectID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatProjectHandlerCommission(TreatProjectHandlersCommissionAddForm form) {

        treatProjectHandlerCommissionMapper.deleteTreatProjectHandler(form.EntityID, form.ProjectID);
        treatProjectHandlerCommissionMapper.deleteTreatProjectHandlerJobType(form.EntityID, form.ProjectID);

        if (form.Handler != null && form.Handler.size() > 0) {

            List<TreatProjectHandlerCommissionEntity> listTreatProjectHandlerCommissionEntity = new ArrayList<>();

            List<TreatProjectHandlerJobTypeCommissionEntity> listTreatProjectHandlerJobTypeCommissionEntity = new ArrayList<>();

            for (TreatProjectHandlerCommissionAddForm treatProjectHandlerAddForm : form.Handler) {
                if (treatProjectHandlerAddForm.PayRate != null || treatProjectHandlerAddForm.PayFixed != null ||
                        treatProjectHandlerAddForm.CardRate != null || treatProjectHandlerAddForm.CardFixed != null ||
                        treatProjectHandlerAddForm.CardLargessRate != null || treatProjectHandlerAddForm.CardLargessFixed != null ||
                        treatProjectHandlerAddForm.LargessRate != null || treatProjectHandlerAddForm.LargessFixed != null ||
                        treatProjectHandlerAddForm.SpecialBenefit != null) {
                    TreatProjectHandlerCommissionEntity treatProjectHandlerCommissionEntity = new TreatProjectHandlerCommissionEntity();
                    treatProjectHandlerCommissionEntity.EntityID = form.EntityID;
                    treatProjectHandlerCommissionEntity.ProjectID = form.ProjectID;
                    treatProjectHandlerCommissionEntity.ProjectTreatHandlerID = treatProjectHandlerAddForm.TreatHandlerID;
                    treatProjectHandlerCommissionEntity.PayRate = treatProjectHandlerAddForm.PayRate;
                    treatProjectHandlerCommissionEntity.PayFixed = treatProjectHandlerAddForm.PayFixed;
                    treatProjectHandlerCommissionEntity.CardRate = treatProjectHandlerAddForm.CardRate;
                    treatProjectHandlerCommissionEntity.CardFixed = treatProjectHandlerAddForm.CardFixed;
                    treatProjectHandlerCommissionEntity.CardLargessRate = treatProjectHandlerAddForm.CardLargessRate;
                    treatProjectHandlerCommissionEntity.CardLargessFixed = treatProjectHandlerAddForm.CardLargessFixed;
                    treatProjectHandlerCommissionEntity.LargessFixed = treatProjectHandlerAddForm.LargessFixed;
                    treatProjectHandlerCommissionEntity.LargessRate = treatProjectHandlerAddForm.LargessRate;
                    treatProjectHandlerCommissionEntity.SpecialBenefit = treatProjectHandlerAddForm.SpecialBenefit;
                    listTreatProjectHandlerCommissionEntity.add(treatProjectHandlerCommissionEntity);
                }
                if (treatProjectHandlerAddForm.HandlerJobTypeCommission != null && treatProjectHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (TreatProjectCategoryHandlerJobTypeCommissionAddForm treatProjectCategoryHandlerJobTypeCommissionAddForm : treatProjectHandlerAddForm.HandlerJobTypeCommission) {
                        if (treatProjectCategoryHandlerJobTypeCommissionAddForm.PayRate != null || treatProjectCategoryHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                treatProjectCategoryHandlerJobTypeCommissionAddForm.CardRate != null || treatProjectCategoryHandlerJobTypeCommissionAddForm.CardFixed != null ||
                                treatProjectCategoryHandlerJobTypeCommissionAddForm.CardLargessRate != null || treatProjectCategoryHandlerJobTypeCommissionAddForm.CardLargessFixed != null ||
                                treatProjectCategoryHandlerJobTypeCommissionAddForm.LargessRate != null || treatProjectCategoryHandlerJobTypeCommissionAddForm.LargessFixed != null ||
                                treatProjectCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            TreatProjectHandlerJobTypeCommissionEntity TreatProjectHandlerJobTypeCommissionEntity = new TreatProjectHandlerJobTypeCommissionEntity();
                            TreatProjectHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            TreatProjectHandlerJobTypeCommissionEntity.ProjectID = form.ProjectID;
                            TreatProjectHandlerJobTypeCommissionEntity.ProjectTreatHandlerID = treatProjectHandlerAddForm.TreatHandlerID;
                            TreatProjectHandlerJobTypeCommissionEntity.JobTypeID = treatProjectCategoryHandlerJobTypeCommissionAddForm.JobTypeID;
                            TreatProjectHandlerJobTypeCommissionEntity.PayRate = treatProjectCategoryHandlerJobTypeCommissionAddForm.PayRate;
                            TreatProjectHandlerJobTypeCommissionEntity.PayFixed = treatProjectCategoryHandlerJobTypeCommissionAddForm.PayFixed;
                            TreatProjectHandlerJobTypeCommissionEntity.CardRate = treatProjectCategoryHandlerJobTypeCommissionAddForm.CardRate;
                            TreatProjectHandlerJobTypeCommissionEntity.CardFixed = treatProjectCategoryHandlerJobTypeCommissionAddForm.CardFixed;
                            TreatProjectHandlerJobTypeCommissionEntity.CardLargessRate = treatProjectCategoryHandlerJobTypeCommissionAddForm.CardLargessRate;
                            TreatProjectHandlerJobTypeCommissionEntity.CardLargessFixed = treatProjectCategoryHandlerJobTypeCommissionAddForm.CardLargessFixed;
                            TreatProjectHandlerJobTypeCommissionEntity.LargessFixed = treatProjectCategoryHandlerJobTypeCommissionAddForm.LargessFixed;
                            TreatProjectHandlerJobTypeCommissionEntity.LargessRate = treatProjectCategoryHandlerJobTypeCommissionAddForm.LargessRate;
                            TreatProjectHandlerJobTypeCommissionEntity.SpecialBenefit = treatProjectCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            listTreatProjectHandlerJobTypeCommissionEntity.add(TreatProjectHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
            if (listTreatProjectHandlerCommissionEntity != null && listTreatProjectHandlerCommissionEntity.size() > 0) {
                List<List<TreatProjectHandlerCommissionEntity>> subs = Lists.partition(listTreatProjectHandlerCommissionEntity, 20);
                for (List<TreatProjectHandlerCommissionEntity> entityList : subs) {
                    treatProjectHandlerCommissionMapper.updateTreatProjectHandlerCommission(entityList);
                }
            }
            if (listTreatProjectHandlerJobTypeCommissionEntity != null && listTreatProjectHandlerJobTypeCommissionEntity.size() > 0) {
                List<List<TreatProjectHandlerJobTypeCommissionEntity>> subs = Lists.partition(listTreatProjectHandlerJobTypeCommissionEntity, 20);
                for (List<TreatProjectHandlerJobTypeCommissionEntity> entityList : subs) {
                    treatProjectHandlerCommissionMapper.insertTreatProjectHandlerJobTypeCommission(entityList);
                }
            }
        }
        return BaseOutput.success();
    }
}