package com.zhelian.service.ibeauty.customer;

import com.zhelian.mapper.ibeauty.CustomerEntityGroupMapper;
import com.zhelian.mapper.ibeauty.CustomerGroupConditionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.CustomerEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * @Description: 获取当前门店的会员分群信息
 * @author: zhanglongyang
 * @date: 2021/05/19
 * @time: 14:46
 */
@Service
@Transactional
public class CustomerEntityGroupService {

    @Autowired
    CustomerEntityGroupMapper customerEntityGroupMapper;
    @Autowired
    CustomerGroupConditionMapper customerGroupConditionMapper;

    //查询该门店下的会员分群信息
    public BaseOutput getCustomerEntityGroup(Map map) {
        List<Map<String, Object>> result = customerEntityGroupMapper.getCustomerEntityGroup(map);
        return BasePageInfo.success(result);
    }

    //获取当前门店下的会员数
    public BaseOutput getAllCustomer(Integer ID) {
        List<CustomerEntity> cList = customerEntityGroupMapper.getAllCustomer(ID);
        return BaseOutputForm.success(cList);
    }

    //获取所有的分群规则
    public BaseOutput getRule() {
        List<Map<String, Object>> cgcList = customerEntityGroupMapper.getRule();


        return BaseOutputForm.success(cgcList);
    }


}
