package com.zhelian.service.ibeauty.treatProductCommission;


import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.TreatProductCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.TreatProductCommissionEntity;
import com.zhelian.model.ibeauty.form.input.TreatProductCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatProductsCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.TreatProductCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatProductCommissionService {

    @Autowired
    TreatProductCommissionMapper treatProductCommissionMapper;

    public BaseOutput allTreatProductCommission(TreatProductCommissionQueryForm form) {
        List<TreatProductCommissionOutputForm> list = treatProductCommissionMapper.allTreatProductCommission(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatProductCommission(TreatProductsCommissionAddForm form) {

        treatProductCommissionMapper.deleteTreatProductCommissionByEntityAndCategory(form.EntityID, form.CategoryID);
        List<TreatProductCommissionEntity> list = new ArrayList<>();
        if (form.Product != null && form.Product.size() > 0) {
            for (TreatProductCommissionAddForm treatProductCommissionAddForm : form.Product) {
                if (treatProductCommissionAddForm.PayRate != null || treatProductCommissionAddForm.PayFixed != null ||
                        treatProductCommissionAddForm.CardRate != null || treatProductCommissionAddForm.CardFixed != null ||
                        treatProductCommissionAddForm.CardLargessRate != null || treatProductCommissionAddForm.CardLargessFixed != null ||
                        treatProductCommissionAddForm.LargessRate != null || treatProductCommissionAddForm.LargessFixed != null ||
                        treatProductCommissionAddForm.SpecialBenefit != null) {
                    TreatProductCommissionEntity treatProductCommissionEntity = new TreatProductCommissionEntity();
                    treatProductCommissionEntity.EntityID = form.EntityID;
                    treatProductCommissionEntity.ProductID = treatProductCommissionAddForm.ProductID;
                    treatProductCommissionEntity.PayRate = treatProductCommissionAddForm.PayRate;
                    treatProductCommissionEntity.PayFixed = treatProductCommissionAddForm.PayFixed;
                    treatProductCommissionEntity.CardRate = treatProductCommissionAddForm.CardRate;
                    treatProductCommissionEntity.CardFixed = treatProductCommissionAddForm.CardFixed;
                    treatProductCommissionEntity.CardLargessRate = treatProductCommissionAddForm.CardLargessRate;
                    treatProductCommissionEntity.CardLargessFixed = treatProductCommissionAddForm.CardLargessFixed;
                    treatProductCommissionEntity.LargessFixed = treatProductCommissionAddForm.LargessFixed;
                    treatProductCommissionEntity.LargessRate = treatProductCommissionAddForm.LargessRate;
                    treatProductCommissionEntity.SpecialBenefit = treatProductCommissionAddForm.SpecialBenefit;
                    list.add(treatProductCommissionEntity);
                }
            }
            if (list != null && list.size() > 0) {
                List<List<TreatProductCommissionEntity>> subs = Lists.partition(list, 20);
                for (List<TreatProductCommissionEntity> entityList : subs) {
                    treatProductCommissionMapper.insertTreatProductCommission(entityList);
                }
            }
        }
        return BaseOutput.success();
    }

}