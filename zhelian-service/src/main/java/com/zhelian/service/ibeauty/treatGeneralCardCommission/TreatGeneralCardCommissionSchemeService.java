package com.zhelian.service.ibeauty.treatGeneralCardCommission;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.ibeauty.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.TreatGeneralCardProjectCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.input.TreatProjectCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCommissionSchemeDeleteForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCommissionSchemeQueryForm;
import com.zhelian.model.ibeauty.form.output.TreatProjectCommissionSchemeOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Service
public class TreatGeneralCardCommissionSchemeService {

    @Autowired
    TreatGeneralCardCommissionSchemeMapper treatGeneralCardCommissionSchemeMapper;
    @Autowired
    TreatGeneralCardCategoryCommissionMapper treatGeneralCardCategoryCommissionMapper;
    @Autowired
    TreatGeneralCardCategoryHandlerCommissionMapper treatGeneralCardCategoryHandlerCommissionMapper;
    @Autowired
    TreatGeneralCardCommissionMapper treatGeneralCardCommissionMapper;
    @Autowired
    TreatGeneralCardHandlerCommissionMapper treatGeneralCardHandlerCommissionMapper;
    @Autowired
    TreatGeneralCardProjectCommissionMapper treatGeneralCardProjectCommissionMapper;
    @Autowired
    TreatGeneralCardProjectHandlerCommissionMapper treatGeneralCardProjectHandlerCommissionMapper;
    @Autowired
    TreatGeneralCardSchemeHandlerCommissionMapper treatGeneralCardSchemeHandlerCommissionMapper;


    public BaseOutput treatGeneralCardCommissionSchemeList(TreatProjectCommissionSchemeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<TreatProjectCommissionSchemeOutputForm> list = treatGeneralCardCommissionSchemeMapper.treatGeneralCardCommissionSchemeList(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput createTreatGeneralCardCommissionScheme(TreatProjectCommissionSchemeAddForm form) {
        Integer count = treatGeneralCardCommissionSchemeMapper.getTreatGeneralCardCommissionScheme(form.EntityID);
        if (count > 0)
            return BaseOutput.failed("组织单位已经存在");
        TreatGeneralCardProjectCommissionSchemeEntity treatGeneralCardProjectCommissionSchemeEntity = new TreatGeneralCardProjectCommissionSchemeEntity();
        treatGeneralCardProjectCommissionSchemeEntity.EntityID = form.EntityID;
        treatGeneralCardCommissionSchemeMapper.createTreatGeneralCardCommissionScheme(treatGeneralCardProjectCommissionSchemeEntity);
        return BaseOutput.success();

    }

    @Transactional
    public BaseOutput deleteTreatGeneralCardCommissionScheme(TreatProjectCommissionSchemeDeleteForm form) {
        treatGeneralCardCommissionSchemeMapper.deleteTreatGeneralCardCommissionScheme(form.EntityID);
        treatGeneralCardCategoryCommissionMapper.deleteTreatGeneralCardCategoryCommission(form.EntityID);
        treatGeneralCardCategoryHandlerCommissionMapper.deleteTreatGeneralCardCategoryHandlerByEntityID(form.EntityID);
        treatGeneralCardCategoryHandlerCommissionMapper.deleteTreatGeneralCardCategoryHandlerJobTypeByEntityID(form.EntityID);
        treatGeneralCardCommissionMapper.deleteTreatGeneralCardCommissionByEntityID(form.EntityID);
        treatGeneralCardHandlerCommissionMapper.deleteTreatGeneralCardHandlerCommissionByEntityID(form.EntityID);
        treatGeneralCardHandlerCommissionMapper.deleteTreatGeneralCardHandlerJobTypeCommissionByEntityID(form.EntityID);
        treatGeneralCardProjectCommissionMapper.deleteTreatGeneralCardProjectCommissionByEntityID(form.EntityID);
        treatGeneralCardProjectHandlerCommissionMapper.deleteTreatGeneralCardProjectHandlerByEntityID(form.EntityID);
        treatGeneralCardProjectHandlerCommissionMapper.deleteTreatGeneralCardProjectHandlerJobTypeByEntityID(form.EntityID);
        treatGeneralCardSchemeHandlerCommissionMapper.deleteTreatGeneralCardSchemeHandler(form.EntityID);
        treatGeneralCardSchemeHandlerCommissionMapper.deleteTreatGeneralCardSchemeHandlerJobType(form.EntityID);
        return BaseOutput.success();
    }
}