package com.zhelian.service.ibeauty.saleProductCommission;

import com.zhelian.mapper.ibeauty.SalePackageCardProductSchemeHandlerCommissionMapper;
import com.zhelian.mapper.ibeauty.SaleProductSchemeHandlerCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SalePackageCardProductSchemeHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SalePackageCardProductSchemeHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleProductSchemeHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleProductSchemeHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.input.SaleProductSchemeHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductSchemeHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProductSchemeHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductsSchemeHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.SaleProductSchemeHandlerCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleProductSchemeHandlerCommissionService {

    @Autowired
    SaleProductSchemeHandlerCommissionMapper saleProductSchemeHandlerCommissionMapper;
    @Autowired
    SalePackageCardProductSchemeHandlerCommissionMapper salePackageCardProductSchemeHandlerCommissionMapper;

    public BaseOutput allSaleProductSchemeHandlerCommission(SaleProductSchemeHandlerCommissionQueryForm form) {
        List<SaleProductSchemeHandlerCommissionOutputForm> list = saleProductSchemeHandlerCommissionMapper.allSaleProductSchemeHandlerCommission(form.EntityID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSalePackageCardProductSchemeHandlerCommission(SaleProductSchemeHandlerCommissionQueryForm form) {
        List<SaleProductSchemeHandlerCommissionOutputForm> list = salePackageCardProductSchemeHandlerCommissionMapper.allSalePackageCardProductSchemeHandlerCommission(form.EntityID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleProductSchemeHandlerCommission(SaleProductsSchemeHandlerCommissionAddForm form) {

        saleProductSchemeHandlerCommissionMapper.deleteSaleProductSchemeHandler(form.EntityID);
        saleProductSchemeHandlerCommissionMapper.deleteSaleProductSchemeHandlerJobType(form.EntityID);
        if (form.ProductHandler != null && form.ProductHandler.size() > 0) {
            List<SaleProductSchemeHandlerCommissionEntity> listSaleProductSchemeHandlerCommissionEntity = new ArrayList<>();
            List<SaleProductSchemeHandlerJobTypeCommissionEntity> listSaleProductSchemeHandlerJobTypeCommissionEntity = new ArrayList<>();
            for (SaleProductSchemeHandlerCommissionAddForm saleProductSchemeHandlerAddForm : form.ProductHandler) {
                if (saleProductSchemeHandlerAddForm.PayRate != null || saleProductSchemeHandlerAddForm.PayFixed != null ||
                        saleProductSchemeHandlerAddForm.SavingCardRate != null || saleProductSchemeHandlerAddForm.SavingCardFixed != null ||
                        saleProductSchemeHandlerAddForm.SavingCardLargessRate != null || saleProductSchemeHandlerAddForm.SavingCardLargessFixed != null ||
                        saleProductSchemeHandlerAddForm.SpecialBenefit != null) {
                    SaleProductSchemeHandlerCommissionEntity saleProductSchemeHandlerCommissionEntity = new SaleProductSchemeHandlerCommissionEntity();
                    saleProductSchemeHandlerCommissionEntity.EntityID = form.EntityID;
                    saleProductSchemeHandlerCommissionEntity.ProductSaleHandlerID = saleProductSchemeHandlerAddForm.SaleHandlerID;
                    saleProductSchemeHandlerCommissionEntity.PayFixed = saleProductSchemeHandlerAddForm.PayFixed;
                    saleProductSchemeHandlerCommissionEntity.PayRate = saleProductSchemeHandlerAddForm.PayRate;
                    saleProductSchemeHandlerCommissionEntity.SavingCardFixed = saleProductSchemeHandlerAddForm.SavingCardFixed;
                    saleProductSchemeHandlerCommissionEntity.SavingCardRate = saleProductSchemeHandlerAddForm.SavingCardRate;
                    saleProductSchemeHandlerCommissionEntity.SavingCardLargessFixed = saleProductSchemeHandlerAddForm.SavingCardLargessFixed;
                    saleProductSchemeHandlerCommissionEntity.SavingCardLargessRate = saleProductSchemeHandlerAddForm.SavingCardLargessRate;
                    saleProductSchemeHandlerCommissionEntity.SpecialBenefit = saleProductSchemeHandlerAddForm.SpecialBenefit;
                    listSaleProductSchemeHandlerCommissionEntity.add(saleProductSchemeHandlerCommissionEntity);
                }
                if (saleProductSchemeHandlerAddForm.HandlerJobTypeCommission != null && saleProductSchemeHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleProductSchemeHandlerJobTypeCommissionAddForm saleProductSchemeHandlerJobTypeCommissionAddForm : saleProductSchemeHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleProductSchemeHandlerJobTypeCommissionAddForm.PayRate != null || saleProductSchemeHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                saleProductSchemeHandlerJobTypeCommissionAddForm.SavingCardRate != null || saleProductSchemeHandlerJobTypeCommissionAddForm.SavingCardFixed != null ||
                                saleProductSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null || saleProductSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessFixed != null ||
                                saleProductSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SaleProductSchemeHandlerJobTypeCommissionEntity saleProductSchemeHandlerJobTypeCommissionEntity = new SaleProductSchemeHandlerJobTypeCommissionEntity();
                            saleProductSchemeHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            saleProductSchemeHandlerJobTypeCommissionEntity.ProductSaleHandlerID = saleProductSchemeHandlerAddForm.SaleHandlerID;
                            saleProductSchemeHandlerJobTypeCommissionEntity.JobTypeID = saleProductSchemeHandlerJobTypeCommissionAddForm.JobTypeID;
                            saleProductSchemeHandlerJobTypeCommissionEntity.PayFixed = saleProductSchemeHandlerJobTypeCommissionAddForm.PayFixed;
                            saleProductSchemeHandlerJobTypeCommissionEntity.PayRate = saleProductSchemeHandlerJobTypeCommissionAddForm.PayRate;
                            saleProductSchemeHandlerJobTypeCommissionEntity.SavingCardFixed = saleProductSchemeHandlerJobTypeCommissionAddForm.SavingCardFixed;
                            saleProductSchemeHandlerJobTypeCommissionEntity.SavingCardRate = saleProductSchemeHandlerJobTypeCommissionAddForm.SavingCardRate;
                            saleProductSchemeHandlerJobTypeCommissionEntity.SavingCardLargessFixed = saleProductSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessFixed;
                            saleProductSchemeHandlerJobTypeCommissionEntity.SavingCardLargessRate = saleProductSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            saleProductSchemeHandlerJobTypeCommissionEntity.SpecialBenefit = saleProductSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            listSaleProductSchemeHandlerJobTypeCommissionEntity.add(saleProductSchemeHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
            if (listSaleProductSchemeHandlerCommissionEntity.size() > 0)
                saleProductSchemeHandlerCommissionMapper.insertSaleProductSchemeHandlerCommission(listSaleProductSchemeHandlerCommissionEntity);
            if (listSaleProductSchemeHandlerJobTypeCommissionEntity.size() > 0)
                saleProductSchemeHandlerCommissionMapper.insertSaleProductSchemeHandlerJobTypeCommission(listSaleProductSchemeHandlerJobTypeCommissionEntity);
        }

        salePackageCardProductSchemeHandlerCommissionMapper.deleteSalePackageCardProductSchemeHandler(form.EntityID);
        salePackageCardProductSchemeHandlerCommissionMapper.deleteSalePackageCardProductSchemeHandlerJobType(form.EntityID);
        if (form.PackageCardProductHandler != null && form.PackageCardProductHandler.size() > 0) {
            List<SalePackageCardProductSchemeHandlerCommissionEntity> listSalePackageCardProductSchemeHandlerCommissionEntity = new ArrayList<>();
            List<SalePackageCardProductSchemeHandlerJobTypeCommissionEntity> listSalePackageCardProductSchemeHandlerJobTypeCommissionEntity = new ArrayList<>();
            for (SaleProductSchemeHandlerCommissionAddForm saleProductSchemeHandlerAddForm : form.PackageCardProductHandler) {
                if (saleProductSchemeHandlerAddForm.PayRate != null || saleProductSchemeHandlerAddForm.PayFixed != null ||
                        saleProductSchemeHandlerAddForm.SavingCardRate != null || saleProductSchemeHandlerAddForm.SavingCardFixed != null ||
                        saleProductSchemeHandlerAddForm.SavingCardLargessRate != null || saleProductSchemeHandlerAddForm.SavingCardLargessFixed != null ||
                        saleProductSchemeHandlerAddForm.SpecialBenefit != null) {
                    SalePackageCardProductSchemeHandlerCommissionEntity saleProductSchemeHandlerCommissionEntity = new SalePackageCardProductSchemeHandlerCommissionEntity();
                    saleProductSchemeHandlerCommissionEntity.EntityID = form.EntityID;
                    saleProductSchemeHandlerCommissionEntity.PackageCardSaleHandlerID = saleProductSchemeHandlerAddForm.SaleHandlerID;
                    saleProductSchemeHandlerCommissionEntity.PayFixed = saleProductSchemeHandlerAddForm.PayFixed;
                    saleProductSchemeHandlerCommissionEntity.PayRate = saleProductSchemeHandlerAddForm.PayRate;
                    saleProductSchemeHandlerCommissionEntity.SavingCardFixed = saleProductSchemeHandlerAddForm.SavingCardFixed;
                    saleProductSchemeHandlerCommissionEntity.SavingCardRate = saleProductSchemeHandlerAddForm.SavingCardRate;
                    saleProductSchemeHandlerCommissionEntity.SavingCardLargessFixed = saleProductSchemeHandlerAddForm.SavingCardLargessFixed;
                    saleProductSchemeHandlerCommissionEntity.SavingCardLargessRate = saleProductSchemeHandlerAddForm.SavingCardLargessRate;
                    saleProductSchemeHandlerCommissionEntity.SpecialBenefit = saleProductSchemeHandlerAddForm.SpecialBenefit;
                    listSalePackageCardProductSchemeHandlerCommissionEntity.add(saleProductSchemeHandlerCommissionEntity);
                }
                if (saleProductSchemeHandlerAddForm.HandlerJobTypeCommission != null && saleProductSchemeHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleProductSchemeHandlerJobTypeCommissionAddForm saleProductSchemeHandlerJobTypeCommissionAddForm : saleProductSchemeHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleProductSchemeHandlerJobTypeCommissionAddForm.PayRate != null || saleProductSchemeHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                saleProductSchemeHandlerJobTypeCommissionAddForm.SavingCardRate != null || saleProductSchemeHandlerJobTypeCommissionAddForm.SavingCardFixed != null ||
                                saleProductSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null || saleProductSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessFixed != null ||
                                saleProductSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SalePackageCardProductSchemeHandlerJobTypeCommissionEntity saleProductSchemeHandlerJobTypeCommissionEntity = new SalePackageCardProductSchemeHandlerJobTypeCommissionEntity();
                            saleProductSchemeHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            saleProductSchemeHandlerJobTypeCommissionEntity.PackageCardSaleHandlerID = saleProductSchemeHandlerAddForm.SaleHandlerID;
                            saleProductSchemeHandlerJobTypeCommissionEntity.JobTypeID = saleProductSchemeHandlerJobTypeCommissionAddForm.JobTypeID;
                            saleProductSchemeHandlerJobTypeCommissionEntity.PayFixed = saleProductSchemeHandlerJobTypeCommissionAddForm.PayFixed;
                            saleProductSchemeHandlerJobTypeCommissionEntity.PayRate = saleProductSchemeHandlerJobTypeCommissionAddForm.PayRate;
                            saleProductSchemeHandlerJobTypeCommissionEntity.SavingCardFixed = saleProductSchemeHandlerJobTypeCommissionAddForm.SavingCardFixed;
                            saleProductSchemeHandlerJobTypeCommissionEntity.SavingCardRate = saleProductSchemeHandlerJobTypeCommissionAddForm.SavingCardRate;
                            saleProductSchemeHandlerJobTypeCommissionEntity.SavingCardLargessFixed = saleProductSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessFixed;
                            saleProductSchemeHandlerJobTypeCommissionEntity.SavingCardLargessRate = saleProductSchemeHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            saleProductSchemeHandlerJobTypeCommissionEntity.SpecialBenefit = saleProductSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            listSalePackageCardProductSchemeHandlerJobTypeCommissionEntity.add(saleProductSchemeHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
            if (listSalePackageCardProductSchemeHandlerCommissionEntity.size() > 0)
                salePackageCardProductSchemeHandlerCommissionMapper.insertSalePackageCardProductSchemeHandlerCommission(listSalePackageCardProductSchemeHandlerCommissionEntity);
            if (listSalePackageCardProductSchemeHandlerJobTypeCommissionEntity.size() > 0)
                salePackageCardProductSchemeHandlerCommissionMapper.insertSalePackageCardProductSchemeHandlerJobTypeCommission(listSalePackageCardProductSchemeHandlerJobTypeCommissionEntity);
        }
        return BaseOutput.success();
    }
}