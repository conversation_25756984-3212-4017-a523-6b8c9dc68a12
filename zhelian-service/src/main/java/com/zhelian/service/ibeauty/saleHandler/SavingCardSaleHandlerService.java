package com.zhelian.service.ibeauty.saleHandler;


import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.SavingCardSaleHandlerMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SavingCardSaleHandlerEntity;
import com.zhelian.model.ibeauty.entity.SavingCardSaleHandlerJobTypeEntity;
import com.zhelian.model.ibeauty.form.input.SaleHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerMoveForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerUpdateForm;
import com.zhelian.model.ibeauty.form.output.SaleHandlerOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * @Description:
 * @Param: No such property: code for class: Script1
 * @returns:
 * @Author: xuehao
 * @Date: 2020/3/31 9:01
 */
@Service
public class SavingCardSaleHandlerService {

    @Autowired
    SavingCardSaleHandlerMapper savingCardSaleHandlerMapper;


    public BaseOutput allSavingCardSaleHandler(SaleHandlerQueryForm form) {

        List<SaleHandlerOutputForm> list = savingCardSaleHandlerMapper.allSavingCardSaleHandler(form.Name, form.EntityID, form.Active);
        return BaseOutputForm.success(list);

    }

    @Transactional
    public BaseOutput createSavingCardSaleHandler(SaleHandlerAddForm form) {
        Integer Sequence = savingCardSaleHandlerMapper.getMaxSequence();
        SavingCardSaleHandlerEntity savingCardSaleHandlerEntity = new SavingCardSaleHandlerEntity();
        savingCardSaleHandlerEntity.Name = form.Name;
        savingCardSaleHandlerEntity.EntityID = form.EntityID;
        savingCardSaleHandlerEntity.Sequence = Sequence != null ? Sequence + 1 : 1;

        savingCardSaleHandlerMapper.createSavingCardSaleHandler(savingCardSaleHandlerEntity);

        insertSaleHandlerJobType(form.JobType, savingCardSaleHandlerEntity.ID);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateSavingCardSaleHandler(SaleHandlerUpdateForm form) {

        SavingCardSaleHandlerEntity savingCardSaleHandlerEntity = new SavingCardSaleHandlerEntity();
        savingCardSaleHandlerEntity.Name = form.Name;
        savingCardSaleHandlerEntity.ID = form.ID;
        savingCardSaleHandlerEntity.EntityID = form.EntityID;
        savingCardSaleHandlerEntity.Active = form.Active;

        savingCardSaleHandlerMapper.updateSavingCardSaleHandler(savingCardSaleHandlerEntity);

        savingCardSaleHandlerMapper.deleteSaleHandlerJobType(form.ID);

        insertSaleHandlerJobType(form.JobType, form.ID);

        return BaseOutput.success();
    }


    public void insertSaleHandlerJobType(Integer[] JobType, Integer ID) {
        List<SavingCardSaleHandlerJobTypeEntity> list = new ArrayList<>();
        for (Integer typeID : JobType) {
            SavingCardSaleHandlerJobTypeEntity savingCardSaleHandlerJobTypeEntity = new SavingCardSaleHandlerJobTypeEntity();
            savingCardSaleHandlerJobTypeEntity.SavingCardSaleHandlerID = ID;
            savingCardSaleHandlerJobTypeEntity.JobTypeID = typeID;
            list.add(savingCardSaleHandlerJobTypeEntity);
        }
        savingCardSaleHandlerMapper.insertSavingCardSaleHandlerJobType(list);
    }

    public BaseOutput moveSavingCardSaleHandler(SaleHandlerMoveForm form) {
        //重新排序
        LinkedList<Integer> linkedList = savingCardSaleHandlerMapper.getIDOrderBySequenceASC(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<SavingCardSaleHandlerEntity> list = new ArrayList<>();

        for (Integer ID : linkedList) {
            SavingCardSaleHandlerEntity savingCardSaleHandlerEntity = new SavingCardSaleHandlerEntity();
            savingCardSaleHandlerEntity.ID = ID;
            savingCardSaleHandlerEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(savingCardSaleHandlerEntity);
        }
        savingCardSaleHandlerMapper.updateSequence(list);

        return BaseOutput.success();
    }

}