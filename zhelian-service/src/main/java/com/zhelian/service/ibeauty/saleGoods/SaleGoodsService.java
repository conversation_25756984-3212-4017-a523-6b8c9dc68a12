package com.zhelian.service.ibeauty.saleGoods;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.ibeauty.SaleGoodsMapper;
import com.zhelian.mapper.ibeauty.SavingCardMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.SavingCardEntity;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.ibeauty.form.output.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static com.zhelian.core.utils.ListUtil.listToTree;


@Service
public class SaleGoodsService {

    @Autowired
    SaleGoodsMapper saleGoodsMapper;
    @Autowired
    SavingCardMapper savingCardMapper;


    public BaseOutput allSaleGoods(SaleGoodsQueryForm form) {
        Integer entityID = SystemUserUtil.getSystemEntityID();
        AllSaleGoodsOutputForm allSaleGoodOutputForm = new AllSaleGoodsOutputForm();
        allSaleGoodOutputForm.Product = saleGoodsMapper.allSaleGoodProduct(form.Name, entityID);
        allSaleGoodOutputForm.Project = saleGoodsMapper.allSaleGoodProject(form.Name, entityID);
        allSaleGoodOutputForm.GeneralCard = saleGoodsMapper.allSaleGoodGeneralCard(form.Name, entityID, form.BillDate);
        allSaleGoodOutputForm.TimeCard = saleGoodsMapper.allSaleGoodTimeCard(form.Name, entityID, form.BillDate);
        allSaleGoodOutputForm.SavingCard = saleGoodsMapper.allSaleGoodSavingCard(form.Name, entityID, form.BillDate);
        List<SaleGoodAllPackageCardOutputForm> packageCardList = saleGoodsMapper.allSaleGoodPackageCard(form.Name, entityID, form.BillDate);
        packageDetail(packageCardList, form.Name, entityID, form.BillDate);
        allSaleGoodOutputForm.PackageCard = packageCardList;
        return BaseOutputForm.success(allSaleGoodOutputForm);
    }

    public BaseOutput allFastSaleGoods(FastSaleGoodsQueryForm form) {
        Integer entityID = SystemUserUtil.getSystemEntityID();
        AllFastSaleGoodsOutputForm allSaleGoodOutputForm = new AllFastSaleGoodsOutputForm();
        allSaleGoodOutputForm.Product = saleGoodsMapper.allSaleGoodProduct(form.Name, entityID);
        allSaleGoodOutputForm.Project = saleGoodsMapper.allSaleGoodProject(form.Name, entityID);
        return BaseOutputForm.success(allSaleGoodOutputForm);
    }

    public void packageDetail(List<SaleGoodAllPackageCardOutputForm> packageCardList, String Name, Integer entityID, String BillDate) {

        List<SaleGoodsPackageCardDetailOutputForm> productList = saleGoodsMapper.saleGoodsPackageCardProduct(Name, entityID, BillDate);
        List<SaleGoodsPackageCardDetailOutputForm> projectList = saleGoodsMapper.saleGoodsPackageCardProject(Name, entityID, BillDate);
        List<SaleGoodsPackageCardDetailOutputForm> generalCardList = saleGoodsMapper.saleGoodsPackageCardGeneralCard(Name, entityID, BillDate);
        List<SaleGoodsPackageCardDetailOutputForm> timeCardList = saleGoodsMapper.saleGoodsPackageCardTimeCard(Name, entityID, BillDate);
        List<SaleGoodsPackageCardDetailOutputForm> savingCardList = saleGoodsMapper.saleGoodsPackageCardSavingCard(Name, entityID, BillDate);

        for (SaleGoodAllPackageCardOutputForm saleGoodAllPackageCardOutputForm : packageCardList) {

            List<SaleGoodsPackageCardDetailOutputForm> product = new ArrayList<>();
            List<SaleGoodsPackageCardDetailOutputForm> project = new ArrayList<>();
            List<SaleGoodsPackageCardDetailOutputForm> generalCard = new ArrayList<>();
            List<SaleGoodsPackageCardDetailOutputForm> timeCard = new ArrayList<>();
            List<SaleGoodsPackageCardDetailOutputForm> savingCard = new ArrayList<>();

            List<SaleGoodsPackageCardDetailOutputForm> largessProduct = new ArrayList<>();
            List<SaleGoodsPackageCardDetailOutputForm> largessProject = new ArrayList<>();
            List<SaleGoodsPackageCardDetailOutputForm> largessGeneralCard = new ArrayList<>();
            List<SaleGoodsPackageCardDetailOutputForm> largessTimeCard = new ArrayList<>();
            List<SaleGoodsPackageCardDetailOutputForm> largessSavingCard = new ArrayList<>();

            for (SaleGoodsPackageCardDetailOutputForm SaleGoodsPackageCardDetailOutputForm : projectList) {
                if (SaleGoodsPackageCardDetailOutputForm.PackageCardID.equals(saleGoodAllPackageCardOutputForm.ID)) {
                    if (SaleGoodsPackageCardDetailOutputForm.IsLargess) {
                        largessProject.add(SaleGoodsPackageCardDetailOutputForm);
                    } else {
                        project.add(SaleGoodsPackageCardDetailOutputForm);
                    }
                }
            }
            for (SaleGoodsPackageCardDetailOutputForm SaleGoodsPackageCardDetailOutputForm : productList) {
                if (SaleGoodsPackageCardDetailOutputForm.PackageCardID.equals(saleGoodAllPackageCardOutputForm.ID)) {
                    if (SaleGoodsPackageCardDetailOutputForm.IsLargess) {
                        largessProduct.add(SaleGoodsPackageCardDetailOutputForm);
                    } else {
                        product.add(SaleGoodsPackageCardDetailOutputForm);
                    }
                }
            }
            for (SaleGoodsPackageCardDetailOutputForm SaleGoodsPackageCardDetailOutputForm : generalCardList) {
                if (SaleGoodsPackageCardDetailOutputForm.PackageCardID.equals(saleGoodAllPackageCardOutputForm.ID)) {
                    if (SaleGoodsPackageCardDetailOutputForm.IsLargess) {
                        largessGeneralCard.add(SaleGoodsPackageCardDetailOutputForm);
                    } else {
                        generalCard.add(SaleGoodsPackageCardDetailOutputForm);
                    }
                }
            }
            for (SaleGoodsPackageCardDetailOutputForm SaleGoodsPackageCardDetailOutputForm : timeCardList) {
                if (SaleGoodsPackageCardDetailOutputForm.PackageCardID.equals(saleGoodAllPackageCardOutputForm.ID)) {
                    if (SaleGoodsPackageCardDetailOutputForm.IsLargess) {
                        largessTimeCard.add(SaleGoodsPackageCardDetailOutputForm);
                    } else {
                        timeCard.add(SaleGoodsPackageCardDetailOutputForm);
                    }
                }
            }
            BigDecimal savingCardTotalPrice = BigDecimal.ZERO;
            for (SaleGoodsPackageCardDetailOutputForm SaleGoodsPackageCardDetailOutputForm : savingCardList) {
                if (SaleGoodsPackageCardDetailOutputForm.PackageCardID.equals(saleGoodAllPackageCardOutputForm.ID)) {
                    if (SaleGoodsPackageCardDetailOutputForm.IsLargess) {
                        largessSavingCard.add(SaleGoodsPackageCardDetailOutputForm);
                    } else {
                        savingCardTotalPrice = savingCardTotalPrice.add(SaleGoodsPackageCardDetailOutputForm.TotalPrice);
                        savingCard.add(SaleGoodsPackageCardDetailOutputForm);
                    }
                }
            }

            PackageCardGoodsDetailOutputForm packageCardGoodsDetailOutputForm = new PackageCardGoodsDetailOutputForm();
            PackageCardGoodsDetailOutputForm packageCardLargessGoodsDetailOutputForm = new PackageCardGoodsDetailOutputForm();

            packageCardGoodsDetailOutputForm.Product = product;
            packageCardGoodsDetailOutputForm.Project = project;
            packageCardGoodsDetailOutputForm.GeneralCard = generalCard;
            packageCardGoodsDetailOutputForm.TimeCard = timeCard;
            packageCardGoodsDetailOutputForm.SavingCard = savingCard;
            saleGoodAllPackageCardOutputForm.PackageCardGoods = packageCardGoodsDetailOutputForm;

            packageCardLargessGoodsDetailOutputForm.Product = largessProduct;
            packageCardLargessGoodsDetailOutputForm.Project = largessProject;
            packageCardLargessGoodsDetailOutputForm.GeneralCard = largessGeneralCard;
            packageCardLargessGoodsDetailOutputForm.TimeCard = largessTimeCard;
            packageCardLargessGoodsDetailOutputForm.SavingCard = largessSavingCard;
            saleGoodAllPackageCardOutputForm.PackageCardLargessGoods = packageCardLargessGoodsDetailOutputForm;

            saleGoodAllPackageCardOutputForm.DeductPrice = saleGoodAllPackageCardOutputForm.Price.subtract(savingCardTotalPrice);
        }
    }

    public BaseOutput projectCustomerDiscount(SaleProjectCustomerDiscountQueryForm form) {
        SaleProjectCustomerDiscountOutForm outForm = saleGoodsMapper.getProjectCustomerDiscount(form.ProjectID,form.CustomerID);
        return BaseOutputForm.success(outForm);
    }

    public BaseOutput allSaleGoodsProject(SaleGoodsQueryForm form) {
        Integer entityID = SystemUserUtil.getSystemEntityID();
        List<Integer> parentList = new ArrayList<>();
        List<SaleGoodsProjectCategoryChildOutputForm> list = saleGoodsMapper.saleGoodsProject(form.Name, entityID);
        for (SaleGoodsProjectCategoryChildOutputForm saleGoodsProjectCategoryChildOutputForm : list) {
            if (!parentList.contains(saleGoodsProjectCategoryChildOutputForm.ParentID))
                parentList.add(saleGoodsProjectCategoryChildOutputForm.ParentID);
        }
        List<SaleGoodsProjectCategoryOutputForm> topList = null;
        JSONArray result = new JSONArray();
        if (parentList != null && parentList.size() > 0) {
            topList = saleGoodsMapper.getTopProjectList(parentList);
            for (SaleGoodsProjectCategoryOutputForm saleGoodsProjectCategoryOutputForm : topList) {
                List<SaleGoodsProjectCategoryChildOutputForm> child = new ArrayList();
                for (SaleGoodsProjectCategoryChildOutputForm saleGoodsProjectCategoryChildOutputForm : list) {
                    if (saleGoodsProjectCategoryChildOutputForm.ParentID.equals(saleGoodsProjectCategoryOutputForm.ID)) {
                        child.add(saleGoodsProjectCategoryChildOutputForm);
                    }
                }
                saleGoodsProjectCategoryOutputForm.Child = child;
            }
            result = listToTree(JSONArray.parseArray(JSON.toJSONString(topList, JSONWriter.Feature.WriteMapNullValue)), "ID", "ParentID", "Child");
        }
        return BaseOutputForm.success(result);

    }

    public BaseOutput allSaleGoodsProjectCategory(SaleGoodsQueryForm form) {
        List<SaleGoodsParentCategoryOutputForm> result = saleGoodsMapper.allSaleGoodsProjectCategory(SystemUserUtil.getSystemEntityID(), form.Name);
        return BaseOutputForm.success(result);

    }

    public BaseOutput projectByCategory(SaleGoodsQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleGoodAllProjectOutputForm> result = saleGoodsMapper.projectByCategory(SystemUserUtil.getSystemEntityID(), form.Name, form.CategoryID);
        return BasePageInfo.success(result);

    }

    public BaseOutput productCustomerDiscount(SaleProductCustomerDiscountQueryForm form) {
        SaleProductCustomerDiscountOutForm outForm = saleGoodsMapper.getProductCustomerDiscount(form.ProductID,form.CustomerID);
        return BaseOutputForm.success(outForm);
    }


    public BaseOutput saleGoodsProduct(SaleGoodsQueryForm form) {
        Integer entityID = SystemUserUtil.getSystemEntityID();
        List<Integer> parentList = new ArrayList<>();
        List<SaleGoodsProductCategoryChildOutputForm> list = saleGoodsMapper.saleGoodsProduct(form.Name, entityID);
        for (SaleGoodsProductCategoryChildOutputForm saleGoodsProductCategoryChildOutputForm : list) {
            if (!parentList.contains(saleGoodsProductCategoryChildOutputForm.ParentID))
                parentList.add(saleGoodsProductCategoryChildOutputForm.ParentID);
        }
        List<SaleGoodsProductCategoryOutputForm> topList = null;
        JSONArray result = new JSONArray();
        if (parentList != null && parentList.size() > 0) {
            topList = saleGoodsMapper.getTopProductList(parentList);
            for (SaleGoodsProductCategoryOutputForm saleGoodsProductCategoryOutputForm : topList) {
                List<SaleGoodsProductCategoryChildOutputForm> child = new ArrayList();
                for (SaleGoodsProductCategoryChildOutputForm saleGoodsProductCategoryChildOutputForm : list) {
                    if (saleGoodsProductCategoryChildOutputForm.ParentID.equals(saleGoodsProductCategoryOutputForm.ID)) {
                        child.add(saleGoodsProductCategoryChildOutputForm);
                    }
                }
                saleGoodsProductCategoryOutputForm.Child = child;
            }
            result = listToTree(JSONArray.parseArray(JSON.toJSONString(topList, JSONWriter.Feature.WriteMapNullValue)), "ID", "ParentID", "Child");
        }
        return BaseOutputForm.success(result);
    }

    public BaseOutput saleGoodsProductCategory(SaleGoodsQueryForm form) {
        List<SaleGoodsParentCategoryOutputForm> result = saleGoodsMapper.saleGoodsProductCategory(SystemUserUtil.getSystemEntityID(), form.Name);
        return BaseOutputForm.success(result);
    }

    public BaseOutput saleGoodsProductByCategory(SaleGoodsQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleGoodAllProductOutputForm> result = saleGoodsMapper.saleGoodsProductByCategory(SystemUserUtil.getSystemEntityID(), form.Name, form.CategoryID);
        return BasePageInfo.success(result);
    }

    public BaseOutput generalCardCustomerDiscount(SaleGeneralCardCustomerDiscountQueryForm form) {
        SaleGeneralCardCustomerDiscountOutForm outForm = saleGoodsMapper.getGeneralCardCustomerDiscount(form.GeneralCardID,form.CustomerID);
        return BaseOutputForm.success(outForm);
    }

    public BaseOutput saleGoodsGeneralCard(SaleGoodsQueryForm form) {
        Integer entityID = SystemUserUtil.getSystemEntityID();
        List<SaleGoodsGeneralCardCategoryOutputForm> list = saleGoodsMapper.saleGoodsGeneralCard(form.Name, entityID, form.BillDate);
        return BaseOutputForm.success(list);
    }

    public BaseOutput saleGoodsGeneralCardCategory(SaleGoodsQueryForm form) {
        List<SaleGoodsCategoryChildOutputForm> list = saleGoodsMapper.saleGoodsGeneralCardCategory(form.Name, SystemUserUtil.getSystemEntityID(), form.BillDate);
        return BaseOutputForm.success(list);
    }

    public BaseOutput saleGoodsGeneralCardByCategory(SaleGoodsQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleGoodGeneralCardOutputForm> list = saleGoodsMapper.saleGoodsGeneralCardByCategory(form.Name, SystemUserUtil.getSystemEntityID(), form.BillDate, form.CategoryID);
        return BasePageInfo.success(list);
    }

    public BaseOutput timeCardCustomerDiscount(SaleTimeCardCustomerDiscountQueryForm form) {
        SaleTimeCardCustomerDiscountOutForm outForm = saleGoodsMapper.getTimeCardCustomerDiscount(form.TimeCardID,form.CustomerID);
        return BaseOutputForm.success(outForm);
    }


    public BaseOutput saleGoodsTimeCard(SaleGoodsQueryForm form) {
        Integer entityID = SystemUserUtil.getSystemEntityID();
        List<SaleGoodsTimeCardCategoryOutputForm> list = saleGoodsMapper.saleGoodsTimeCard(form.Name, entityID, form.BillDate);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSaleGoodsTimeCardCategory(SaleGoodsQueryForm form) {
        List<SaleGoodsCategoryChildOutputForm> result = saleGoodsMapper.allSaleGoodsTimeCardCategory(SystemUserUtil.getSystemEntityID(), form.Name, form.BillDate);
        return BaseOutputForm.success(result);

    }

    public BaseOutput timeCardByCategory(SaleGoodsQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleGoodTimeCardOutputForm> result = saleGoodsMapper.timeCardByCategory(SystemUserUtil.getSystemEntityID(), form.Name, form.CategoryID, form.BillDate);
        return BasePageInfo.success(result);

    }

    public BaseOutput saleGoodsSavingCard(SaleGoodsQueryForm form) {
        Integer entityID = SystemUserUtil.getSystemEntityID();
        List<SaleGoodsSavingCardCategoryOutputForm> list = saleGoodsMapper.saleGoodsSavingCard(form.Name, entityID, form.BillDate);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSaleGoodsSavingCardCategory(SaleGoodsQueryForm form) {
        List<SaleGoodsCategoryChildOutputForm> result = saleGoodsMapper.allSaleGoodsSavingCardCategory(SystemUserUtil.getSystemEntityID(), form.Name, form.BillDate);
        return BaseOutputForm.success(result);

    }

    public BaseOutput savingCardByCategory(SaleGoodsQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleGoodSavingCardOutputForm> result = saleGoodsMapper.savingCardByCategory(SystemUserUtil.getSystemEntityID(), form.Name, form.CategoryID, form.BillDate);
        return BasePageInfo.success(result);

    }

    public BaseOutput packageCardCustomerDiscount(SalePackageCardCustomerDiscountQueryForm form) {
        SalePackageCardCustomerDiscountOutForm outForm = saleGoodsMapper.getPackageCardCustomerDiscount(form.PackageCardID,form.CustomerID);
        return BaseOutputForm.success(outForm);
    }

    public BaseOutput saleGoodsPackageCard(SaleGoodsQueryForm form) {
        Integer entityID = SystemUserUtil.getSystemEntityID();
        List<SaleGoodsPackageCardCategoryOutputForm> list = saleGoodsMapper.saleGoodsPackageCardCategory(form.Name, entityID, form.BillDate);
        packageDetailList(list, form.Name, entityID, form.BillDate);
        return BaseOutputForm.success(list);
    }

    public BaseOutput saleGoodsPackageCardCategory(SaleGoodsQueryForm form) {
        List<SaleGoodsCategoryChildOutputForm> list = saleGoodsMapper.saleGoodsPackageCardOnlyCategory(form.Name, SystemUserUtil.getSystemEntityID(), form.BillDate);
        return BaseOutputForm.success(list);
    }

    public BaseOutput saleGoodsPackageCardByCategory(SaleGoodsQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleGoodPackageCardOutputForm> list = saleGoodsMapper.saleGoodsPackageCardByCategory(form.Name, SystemUserUtil.getSystemEntityID(), form.BillDate, form.CategoryID);
        if (list != null && list.size() > 0) {
            SaleGoodsPackageCardCategoryOutputForm saleGoodsPackageCardCategoryOutputForm = new SaleGoodsPackageCardCategoryOutputForm();
            saleGoodsPackageCardCategoryOutputForm.PackageCard = list;
            List<SaleGoodsPackageCardCategoryOutputForm> tmpList = new ArrayList<>();
            tmpList.add(saleGoodsPackageCardCategoryOutputForm);
            packageDetailList(tmpList, form.Name, SystemUserUtil.getSystemEntityID(), form.BillDate);
            list = tmpList.get(0).PackageCard;
        }
        return BasePageInfo.success(list);
    }

    public void packageDetailList(List<SaleGoodsPackageCardCategoryOutputForm> list, String Name, Integer entityID, String BillDate) {
        List<SaleGoodsPackageCardDetailOutputForm> productList = saleGoodsMapper.saleGoodsPackageCardProduct(Name, entityID, BillDate);
        List<SaleGoodsPackageCardDetailOutputForm> projectList = saleGoodsMapper.saleGoodsPackageCardProject(Name, entityID, BillDate);
        List<SaleGoodsPackageCardDetailOutputForm> generalCardList = saleGoodsMapper.saleGoodsPackageCardGeneralCard(Name, entityID, BillDate);
        List<SaleGoodsPackageCardDetailOutputForm> timeCardList = saleGoodsMapper.saleGoodsPackageCardTimeCard(Name, entityID, BillDate);
        List<SaleGoodsPackageCardDetailOutputForm> savingCardList = saleGoodsMapper.saleGoodsPackageCardSavingCard(Name, entityID, BillDate);


        for (SaleGoodsPackageCardCategoryOutputForm saleGoodsPackageCardCategoryOutputForm : list) {
            for (SaleGoodPackageCardOutputForm saleGoodAllPackageCardOutputForm : saleGoodsPackageCardCategoryOutputForm.PackageCard) {
                List<SaleGoodsPackageCardDetailOutputForm> product = new ArrayList<>();
                List<SaleGoodsPackageCardDetailOutputForm> project = new ArrayList<>();
                List<SaleGoodsPackageCardDetailOutputForm> generalCard = new ArrayList<>();
                List<SaleGoodsPackageCardDetailOutputForm> timeCard = new ArrayList<>();
                List<SaleGoodsPackageCardDetailOutputForm> savingCard = new ArrayList<>();

                List<SaleGoodsPackageCardDetailOutputForm> largessProduct = new ArrayList<>();
                List<SaleGoodsPackageCardDetailOutputForm> largessProject = new ArrayList<>();
                List<SaleGoodsPackageCardDetailOutputForm> largessGeneralCard = new ArrayList<>();
                List<SaleGoodsPackageCardDetailOutputForm> largessTimeCard = new ArrayList<>();
                List<SaleGoodsPackageCardDetailOutputForm> largessSavingCard = new ArrayList<>();

                for (SaleGoodsPackageCardDetailOutputForm SaleGoodsPackageCardDetailOutputForm : projectList) {
                    if (SaleGoodsPackageCardDetailOutputForm.PackageCardID.equals(saleGoodAllPackageCardOutputForm.ID)) {
                        if (SaleGoodsPackageCardDetailOutputForm.IsLargess) {
                            largessProject.add(SaleGoodsPackageCardDetailOutputForm);
                        } else {
                            project.add(SaleGoodsPackageCardDetailOutputForm);
                        }
                    }
                }
                for (SaleGoodsPackageCardDetailOutputForm SaleGoodsPackageCardDetailOutputForm : productList) {
                    if (SaleGoodsPackageCardDetailOutputForm.PackageCardID.equals(saleGoodAllPackageCardOutputForm.ID)) {
                        if (SaleGoodsPackageCardDetailOutputForm.IsLargess) {
                            largessProduct.add(SaleGoodsPackageCardDetailOutputForm);
                        } else {
                            product.add(SaleGoodsPackageCardDetailOutputForm);
                        }
                    }
                }
                for (SaleGoodsPackageCardDetailOutputForm SaleGoodsPackageCardDetailOutputForm : generalCardList) {
                    if (SaleGoodsPackageCardDetailOutputForm.PackageCardID.equals(saleGoodAllPackageCardOutputForm.ID)) {
                        if (SaleGoodsPackageCardDetailOutputForm.IsLargess) {
                            largessGeneralCard.add(SaleGoodsPackageCardDetailOutputForm);
                        } else {
                            generalCard.add(SaleGoodsPackageCardDetailOutputForm);
                        }
                    }
                }
                for (SaleGoodsPackageCardDetailOutputForm SaleGoodsPackageCardDetailOutputForm : timeCardList) {
                    if (SaleGoodsPackageCardDetailOutputForm.PackageCardID.equals(saleGoodAllPackageCardOutputForm.ID)) {
                        if (SaleGoodsPackageCardDetailOutputForm.IsLargess) {
                            largessTimeCard.add(SaleGoodsPackageCardDetailOutputForm);
                        } else {
                            timeCard.add(SaleGoodsPackageCardDetailOutputForm);
                        }
                    }
                }
                BigDecimal savingCardTotalPrice = BigDecimal.ZERO;
                for (SaleGoodsPackageCardDetailOutputForm SaleGoodsPackageCardDetailOutputForm : savingCardList) {

                    if (SaleGoodsPackageCardDetailOutputForm.PackageCardID.equals(saleGoodAllPackageCardOutputForm.ID)) {
                        if (SaleGoodsPackageCardDetailOutputForm.IsLargess) {
                            largessSavingCard.add(SaleGoodsPackageCardDetailOutputForm);
                        } else {
                            savingCardTotalPrice = savingCardTotalPrice.add(SaleGoodsPackageCardDetailOutputForm.TotalPrice);
                            savingCard.add(SaleGoodsPackageCardDetailOutputForm);
                        }
                    }
                }

                PackageCardGoodsDetailOutputForm packageCardGoodsDetailOutputForm = new PackageCardGoodsDetailOutputForm();
                PackageCardGoodsDetailOutputForm packageCardLargessGoodsDetailOutputForm = new PackageCardGoodsDetailOutputForm();

                packageCardGoodsDetailOutputForm.Product = product;
                packageCardGoodsDetailOutputForm.Project = project;
                packageCardGoodsDetailOutputForm.GeneralCard = generalCard;
                packageCardGoodsDetailOutputForm.TimeCard = timeCard;
                packageCardGoodsDetailOutputForm.SavingCard = savingCard;
                saleGoodAllPackageCardOutputForm.PackageCardGoods = packageCardGoodsDetailOutputForm;

                packageCardLargessGoodsDetailOutputForm.Product = largessProduct;
                packageCardLargessGoodsDetailOutputForm.Project = largessProject;
                packageCardLargessGoodsDetailOutputForm.GeneralCard = largessGeneralCard;
                packageCardLargessGoodsDetailOutputForm.TimeCard = largessTimeCard;
                packageCardLargessGoodsDetailOutputForm.SavingCard = largessSavingCard;
                saleGoodAllPackageCardOutputForm.PackageCardLargessGoods = packageCardLargessGoodsDetailOutputForm;

                saleGoodAllPackageCardOutputForm.DeductPrice = saleGoodAllPackageCardOutputForm.Price.subtract(savingCardTotalPrice);
            }
        }
    }

    public BaseOutput goodsType(SaleGoodsQueryForm form) {
        return BaseOutputForm.success(saleGoodsMapper.getGoodsType(SystemUserUtil.getSystemEntityID(), form.Name, form.BillDate));

    }

    public BaseOutput fastGoodsType(FastSaleGoodsQueryForm form) {
        return BaseOutputForm.success(saleGoodsMapper.getFastGoodsType(SystemUserUtil.getSystemEntityID(), form.Name));

    }

    public BaseOutput goods(SaleGoodsQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        if (form.GoodsType.equals("20")) {
            List<SaleGoodAllProjectOutputForm> result = saleGoodsMapper.projectByCategory(SystemUserUtil.getSystemEntityID(), form.Name, null);
            return BasePageInfo.success(result);
        } else if (form.GoodsType.equals("40")) {
            List<SaleGoodTimeCardOutputForm> result = saleGoodsMapper.timeCardByCategory(SystemUserUtil.getSystemEntityID(), form.Name, null, form.BillDate);
            return BasePageInfo.success(result);
        } else if (form.GoodsType.equals("50")) {
            List<SaleGoodSavingCardOutputForm> result = saleGoodsMapper.savingCardByCategory(SystemUserUtil.getSystemEntityID(), form.Name, null, form.BillDate);
            return BasePageInfo.success(result);
        } else if (form.GoodsType.equals("10")) {
            List<SaleGoodAllProductOutputForm> result = saleGoodsMapper.saleGoodsProductByCategory(SystemUserUtil.getSystemEntityID(), form.Name, null);
            return BasePageInfo.success(result);
        } else if (form.GoodsType.equals("30")) {
            List<SaleGoodGeneralCardOutputForm> result = saleGoodsMapper.saleGoodsGeneralCardByCategory(form.Name, SystemUserUtil.getSystemEntityID(), form.BillDate, null);
            return BasePageInfo.success(result);
        } else if (form.GoodsType.equals("60")) {
            List<SaleGoodPackageCardOutputForm> list = saleGoodsMapper.saleGoodsPackageCardByCategory(form.Name, SystemUserUtil.getSystemEntityID(), form.BillDate, null);
            if (list != null && list.size() > 0) {
                SaleGoodsPackageCardCategoryOutputForm saleGoodsPackageCardCategoryOutputForm = new SaleGoodsPackageCardCategoryOutputForm();
                saleGoodsPackageCardCategoryOutputForm.PackageCard = list;
                List<SaleGoodsPackageCardCategoryOutputForm> tmpList = new ArrayList<>();
                tmpList.add(saleGoodsPackageCardCategoryOutputForm);
                packageDetailList(tmpList, form.Name, SystemUserUtil.getSystemEntityID(), form.BillDate);
                list = tmpList.get(0).PackageCard;
            }
            return BasePageInfo.success(list);
        }
        return null;
    }

    public BaseOutput generalCardProjectCategory(SalePreviewGoodsQueryForm form) {
        List<SaleGoodsParentCategoryOutputForm> list = saleGoodsMapper.generalCardProjectCategory(form.Name, form.GoodsID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput generalCardProjectByCategory(SalePreviewGoodsQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleGoodCardProjectOutputForm> list = saleGoodsMapper.generalCardProjectByCategory(form.Name, form.GoodsID, form.CategoryID);
        return BasePageInfo.success(list);
    }

    public BaseOutput timeCardProjectCategory(SalePreviewGoodsQueryForm form) {
        List<SaleGoodsParentCategoryOutputForm> list = saleGoodsMapper.timeCardProjectCategory(form.Name, form.GoodsID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput timeCardProjectByCategory(SalePreviewGoodsQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleGoodCardProjectOutputForm> list = saleGoodsMapper.timeCardProjectByCategory(form.Name, form.GoodsID, form.CategoryID);
        return BasePageInfo.success(list);
    }

    public BaseOutput savingCardProjectCategory(SalePreviewGoodsQueryForm form) {
        SavingCardEntity savingCardEntity = savingCardMapper.getSavingCardByID(form.GoodsID);
        if (savingCardEntity == null) {
            return BaseOutput.failed("储值卡不存在");
        }
        //IsGoodsRange 为true 则查询全部，
        if (savingCardEntity.IsGoodsRange) {
            return BaseOutputForm.success(saleGoodsMapper.savingCardAllProjectCategory(form.Name));
        }
        List<SaleGoodsParentCategoryOutputForm> list = saleGoodsMapper.savingCardProjectCategory(form.Name, form.GoodsID, false);
        return BaseOutputForm.success(list);
    }


    public BaseOutput savingCardProjectByCategory(SalePreviewGoodsQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        SavingCardEntity savingCardEntity = savingCardMapper.getSavingCardByID(form.GoodsID);
        if (savingCardEntity == null) {
            return BaseOutput.failed("储值卡不存在");
        }
        //IsGoodsRange 为true 则查询全部，
        if (savingCardEntity.IsGoodsRange) {
            return BasePageInfo.success(saleGoodsMapper.savingCardAllProjectByCategory(form.Name, form.CategoryID));
        }
        List<SaleGoodCardProjectOutputForm> list = saleGoodsMapper.savingCardProjectByCategory(form.Name, form.GoodsID, form.CategoryID, false);
        return BasePageInfo.success(list);
    }

    public BaseOutput savingCardLargessProjectCategory(SalePreviewGoodsQueryForm form) {
        SavingCardEntity savingCardEntity = savingCardMapper.getSavingCardByID(form.GoodsID);
        if (savingCardEntity == null) {
            return BaseOutput.failed("储值卡不存在");
        }
        //IsGoodsRange 为true 则查询全部，
        if (savingCardEntity.IsLargessGoodsRange) {
            return BaseOutputForm.success(saleGoodsMapper.savingCardAllProjectCategory(form.Name));
        }
        List<SaleGoodsParentCategoryOutputForm> list = saleGoodsMapper.savingCardProjectCategory(form.Name, form.GoodsID, true);
        return BaseOutputForm.success(list);
    }


    public BaseOutput savingCardLargessProjectByCategory(SalePreviewGoodsQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        SavingCardEntity savingCardEntity = savingCardMapper.getSavingCardByID(form.GoodsID);
        if (savingCardEntity == null) {
            return BaseOutput.failed("储值卡不存在");
        }
        //IsGoodsRange 为true 则查询全部，
        if (savingCardEntity.IsLargessGoodsRange) {
            return BasePageInfo.success(saleGoodsMapper.savingCardAllProjectByCategory(form.Name, form.CategoryID));
        }
        List<SaleGoodCardProjectOutputForm> list = saleGoodsMapper.savingCardProjectByCategory(form.Name, form.GoodsID, form.CategoryID, true);
        return BasePageInfo.success(list);
    }


    public BaseOutput packageCardGoodsByCategory(SalePreviewGoodsQueryForm form) {
        SalePackageCardGoodsCategoryOutputForm salePackageCardGoodsCategoryOutputForm = new SalePackageCardGoodsCategoryOutputForm();
        salePackageCardGoodsCategoryOutputForm.Product = saleGoodsMapper.packageCardProductCategory(form.Name, form.GoodsID, false);
        salePackageCardGoodsCategoryOutputForm.Project = saleGoodsMapper.packageCardProjectCategory(form.Name, form.GoodsID, false);
        salePackageCardGoodsCategoryOutputForm.GeneralCard = saleGoodsMapper.packageCardGeneralCardCategory(form.Name, form.GoodsID, false);
        salePackageCardGoodsCategoryOutputForm.TimeCard = saleGoodsMapper.packageCardTimeCardCategory(form.Name, form.GoodsID, false);
        salePackageCardGoodsCategoryOutputForm.SavingCard = saleGoodsMapper.packageCardSavingCardCategory(form.Name, form.GoodsID, false);
        return BaseOutputForm.success(salePackageCardGoodsCategoryOutputForm);
    }

    public BaseOutput packageCardProductByCategory(SalePreviewGoodsQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleGoodCardProjectOutputForm> list = saleGoodsMapper.packageCardProductByCategory(form.Name, form.GoodsID, form.CategoryID, false);
        return BasePageInfo.success(list);
    }

    public BaseOutput packageCardProjectByCategory(SalePreviewGoodsQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleGoodCardProjectOutputForm> list = saleGoodsMapper.packageCardProjectByCategory(form.Name, form.GoodsID, form.CategoryID, false);
        return BasePageInfo.success(list);
    }

    public BaseOutput packageCardGeneralCardByCategory(SalePreviewGoodsQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleGoodCardProjectOutputForm> list = saleGoodsMapper.packageCardGeneralCardByCategory(form.Name, form.GoodsID, form.CategoryID, false);
        return BasePageInfo.success(list);
    }

    public BaseOutput packageCardTimeCardByCategory(SalePreviewGoodsQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleGoodCardProjectOutputForm> list = saleGoodsMapper.packageCardTimeCardByCategory(form.Name, form.GoodsID, form.CategoryID, false);
        return BasePageInfo.success(list);
    }

    public BaseOutput packageCardSavingCardByCategory(SalePreviewGoodsQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleGoodCardProjectOutputForm> list = saleGoodsMapper.packageCardSavingCardByCategory(form.Name, form.GoodsID, form.CategoryID, false);
        return BasePageInfo.success(list);
    }


    public BaseOutput packageCardLargessGoodsByCategory(SalePreviewGoodsQueryForm form) {
        SalePackageCardGoodsCategoryOutputForm salePackageCardGoodsCategoryOutputForm = new SalePackageCardGoodsCategoryOutputForm();
        salePackageCardGoodsCategoryOutputForm.Product = saleGoodsMapper.packageCardProductCategory(form.Name, form.GoodsID, false);
        salePackageCardGoodsCategoryOutputForm.Project = saleGoodsMapper.packageCardProjectCategory(form.Name, form.GoodsID, false);
        salePackageCardGoodsCategoryOutputForm.GeneralCard = saleGoodsMapper.packageCardGeneralCardCategory(form.Name, form.GoodsID, false);
        salePackageCardGoodsCategoryOutputForm.TimeCard = saleGoodsMapper.packageCardTimeCardCategory(form.Name, form.GoodsID, false);
        salePackageCardGoodsCategoryOutputForm.SavingCard = saleGoodsMapper.packageCardSavingCardCategory(form.Name, form.GoodsID, false);
        return BaseOutputForm.success(salePackageCardGoodsCategoryOutputForm);
    }

    public BaseOutput packageCardLargessProductByCategory(SalePreviewGoodsQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleGoodCardProjectOutputForm> list = saleGoodsMapper.packageCardProductByCategory(form.Name, form.GoodsID, form.CategoryID, true);
        return BasePageInfo.success(list);
    }

    public BaseOutput packageCardLargessProjectByCategory(SalePreviewGoodsQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleGoodCardProjectOutputForm> list = saleGoodsMapper.packageCardProjectByCategory(form.Name, form.GoodsID, form.CategoryID, true);
        return BasePageInfo.success(list);
    }

    public BaseOutput packageCardLargessGeneralCardByCategory(SalePreviewGoodsQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleGoodCardProjectOutputForm> list = saleGoodsMapper.packageCardGeneralCardByCategory(form.Name, form.GoodsID, form.CategoryID, true);
        return BasePageInfo.success(list);
    }

    public BaseOutput packageCardLargessTimeCardByCategory(SalePreviewGoodsQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleGoodCardProjectOutputForm> list = saleGoodsMapper.packageCardTimeCardByCategory(form.Name, form.GoodsID, form.CategoryID, true);
        return BasePageInfo.success(list);
    }

    public BaseOutput packageCardLargessSavingCardByCategory(SalePreviewGoodsQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleGoodCardProjectOutputForm> list = saleGoodsMapper.packageCardSavingCardByCategory(form.Name, form.GoodsID, form.CategoryID, true);
        return BasePageInfo.success(list);
    }
}