package com.zhelian.service.ibeauty.treatTimeCardCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.TreatTimeCardCategoryCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.TreatTimeCardCategoryCommissionEntity;
import com.zhelian.model.ibeauty.entity.TreatTimeCardProjectCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCategoryCommissionQueryForm;
import com.zhelian.model.ibeauty.form.output.TreatGeneralCardCategoryCommissionOutputForm;
import com.zhelian.model.ibeauty.form.output.TreatGeneralCardCategoryOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatTimeCardCategoryCommissionService {

    @Autowired
    TreatTimeCardCategoryCommissionMapper treatTimeCardCategoryCommissionMapper;


    public BaseOutput alltreatTimeCardProjectCategoryCommission(TreatSavingCardProjectCategoryCommissionQueryForm form) {
        TreatGeneralCardCategoryOutputForm saleGeneralCardCategoryOutputForm = treatTimeCardCategoryCommissionMapper.treatTimeCardProjectCategoryCommission(form.EntityID);
        List<TreatGeneralCardCategoryCommissionOutputForm> all = treatTimeCardCategoryCommissionMapper.allSaleTimeCardCategoryCommission(form.EntityID);
        saleGeneralCardCategoryOutputForm.Category = all;
        return BaseOutputForm.success(saleGeneralCardCategoryOutputForm);
    }

    @Transactional
    public BaseOutput updateTreatTimeCardProjectCategoryCommission(TreatCardCategoryAddForm form) {
        TreatTimeCardProjectCommissionSchemeEntity treatTimeCardProjectCommissionSchemeEntity = new TreatTimeCardProjectCommissionSchemeEntity();
        treatTimeCardProjectCommissionSchemeEntity.EntityID = form.EntityID;
        treatTimeCardProjectCommissionSchemeEntity.PayRate = form.PayRate;
        treatTimeCardProjectCommissionSchemeEntity.PayFixed = form.PayFixed;
        treatTimeCardProjectCommissionSchemeEntity.CardRate = form.CardRate;
        treatTimeCardProjectCommissionSchemeEntity.CardFixed = form.CardFixed;
        treatTimeCardProjectCommissionSchemeEntity.CardLargessRate = form.CardLargessRate;
        treatTimeCardProjectCommissionSchemeEntity.CardLargessFixed = form.CardLargessFixed;
        treatTimeCardProjectCommissionSchemeEntity.LargessRate = form.LargessRate;
        treatTimeCardProjectCommissionSchemeEntity.LargessFixed = form.LargessFixed;
        treatTimeCardProjectCommissionSchemeEntity.SpecialBenefit = form.SpecialBenefit;

        treatTimeCardCategoryCommissionMapper.updateTreatTimeCardCommissionScheme(treatTimeCardProjectCommissionSchemeEntity);
        treatTimeCardCategoryCommissionMapper.deleteTreatTimeCardCategoryCommission(form.EntityID);

        if (form.Category != null && form.Category.size() > 0) {
            List<TreatTimeCardCategoryCommissionEntity> list = new ArrayList<>();
            for (TreatCardCategoryCommissionAddForm treatCardCategoryCommissionAddForm : form.Category) {
                if (treatCardCategoryCommissionAddForm.PayRate != null || treatCardCategoryCommissionAddForm.PayFixed != null ||
                        treatCardCategoryCommissionAddForm.CardRate != null || treatCardCategoryCommissionAddForm.CardFixed != null ||
                        treatCardCategoryCommissionAddForm.CardLargessRate != null || treatCardCategoryCommissionAddForm.CardLargessFixed != null ||
                        treatCardCategoryCommissionAddForm.LargessRate != null || treatCardCategoryCommissionAddForm.LargessFixed != null ||
                        treatCardCategoryCommissionAddForm.SpecialBenefit != null) {
                    TreatTimeCardCategoryCommissionEntity treatTimeCardCategoryCommissionEntity = new TreatTimeCardCategoryCommissionEntity();
                    treatTimeCardCategoryCommissionEntity.EntityID = form.EntityID;
                    treatTimeCardCategoryCommissionEntity.TimeCardCategoryID = treatCardCategoryCommissionAddForm.CategoryID;
                    treatTimeCardCategoryCommissionEntity.PayRate = treatCardCategoryCommissionAddForm.PayRate;
                    treatTimeCardCategoryCommissionEntity.PayFixed = treatCardCategoryCommissionAddForm.PayFixed;
                    treatTimeCardCategoryCommissionEntity.CardRate = treatCardCategoryCommissionAddForm.CardRate;
                    treatTimeCardCategoryCommissionEntity.CardFixed = treatCardCategoryCommissionAddForm.CardFixed;
                    treatTimeCardCategoryCommissionEntity.CardLargessRate = treatCardCategoryCommissionAddForm.CardLargessRate;
                    treatTimeCardCategoryCommissionEntity.CardLargessFixed = treatCardCategoryCommissionAddForm.CardLargessFixed;
                    treatTimeCardCategoryCommissionEntity.LargessRate = treatCardCategoryCommissionAddForm.LargessRate;
                    treatTimeCardCategoryCommissionEntity.LargessFixed = treatCardCategoryCommissionAddForm.LargessFixed;
                    treatTimeCardCategoryCommissionEntity.SpecialBenefit = treatCardCategoryCommissionAddForm.SpecialBenefit;
                    list.add(treatTimeCardCategoryCommissionEntity);
                }
            }

            if (list != null && list.size() > 0) {
                List<List<TreatTimeCardCategoryCommissionEntity>> subs = Lists.partition(list, 20);
                for (List<TreatTimeCardCategoryCommissionEntity> entityList : subs) {
                    treatTimeCardCategoryCommissionMapper.insertTreatTimeCardCategoryCommissionEntity(entityList);
                }
            }
        }

        return BaseOutput.success();
    }
}