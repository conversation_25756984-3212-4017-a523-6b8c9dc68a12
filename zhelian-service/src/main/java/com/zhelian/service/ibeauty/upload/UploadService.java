package com.zhelian.service.ibeauty.upload;

import com.zhelian.core.utils.OSSClientUtil;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.AttachmentAddForm;
import com.zhelian.model.ibeauty.form.output.AttachmentOutputForm;
import com.zhelian.model.miniprogram.form.output.FileOutputForm;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.text.SimpleDateFormat;
import java.util.Date;


@Service
public class UploadService {

    public BaseOutput addAttachment(AttachmentAddForm form) {
        AttachmentOutputForm attachmentOutputForm = new AttachmentOutputForm();
        attachmentOutputForm.AttachmentURL = OSSClientUtil.putOSSBase64Img(form.AttachmentURL);
        return BaseOutputForm.success(attachmentOutputForm);
    }


    public BaseOutput uploadFile(MultipartFile multipartFile) throws Exception {
        Date d = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String newFileName = sdf.format(d);
        String filename = multipartFile.getOriginalFilename();
        String title = filename.substring(0, filename.indexOf("."));
        String fileSuffix = filename.substring(filename.lastIndexOf("."));
        String suffix = filename.substring(filename.lastIndexOf(".") + 1);
        byte[] bytes = multipartFile.getBytes();
        FileOutputForm form = new FileOutputForm();
        form.ID = OSSClientUtil.putOSSObject(title + newFileName + fileSuffix, bytes, suffix);
        form.Name = title + newFileName;
        return BaseOutputForm.success(form);
    }

}
