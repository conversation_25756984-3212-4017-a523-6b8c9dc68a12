package com.zhelian.service.ibeauty.saleGeneralCardCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.SaleGeneralCardCategoryHandlerCommissionMapper;
import com.zhelian.mapper.ibeauty.SalePackageCardGeneralCardCategoryHandlerCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SaleGeneralCardCategoryHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleGeneralCardCategoryHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.entity.SalePackageCardGeneralCardCategoryHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SalePackageCardGeneralCardCategoryHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardCategoryHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.SaleGeneralCardCategoryHandlerCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleGeneralCardCategoryHandlerCommissionService {

    @Autowired
    SaleGeneralCardCategoryHandlerCommissionMapper saleGeneralCardCategoryHandlerCommissionMapper;
    @Autowired
    SalePackageCardGeneralCardCategoryHandlerCommissionMapper salePackageCardGeneralCardCategoryHandlerCommissionMapper;


    public BaseOutput allSaleGeneralCardCategoryHandlerCommission(SaleGeneralCardCategoryHandlerCommissionQueryForm form) {
        List<SaleGeneralCardCategoryHandlerCommissionOutputForm> list = saleGeneralCardCategoryHandlerCommissionMapper.allSaleGeneralCardCategoryHandlerCommission(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSalePackageCardGeneralCardCategoryHandlerCommission(SaleGeneralCardCategoryHandlerCommissionQueryForm form) {
        List<SaleGeneralCardCategoryHandlerCommissionOutputForm> list = salePackageCardGeneralCardCategoryHandlerCommissionMapper.allSalePackageCardGeneralCardCategoryHandlerCommission(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleGeneralCardCategoryHandlerCommission(SaleGeneralCardCategoryHandlerCommissionAddForm form) {
        saleGeneralCardCategoryHandlerCommissionMapper.deleteSaleGeneralCardCategoryHandler(form.EntityID, form.CategoryID);
        saleGeneralCardCategoryHandlerCommissionMapper.deleteSaleGeneralCardCategoryHandlerJobType(form.EntityID, form.CategoryID);
        List<SaleGeneralCardCategoryHandlerCommissionEntity> list = new ArrayList<>();
        List<SaleGeneralCardCategoryHandlerJobTypeCommissionEntity> handlerJobTypeCommissionList = new ArrayList<>();
        if (form.GeneralCardHandler != null && form.GeneralCardHandler.size() > 0) {
            for (SaleGeneralCardCategoryHandlerAddForm saleGeneralCardCategoryHandlerAddForm : form.GeneralCardHandler) {
                if (saleGeneralCardCategoryHandlerAddForm.PayRate != null || saleGeneralCardCategoryHandlerAddForm.PayFixed != null ||
                        saleGeneralCardCategoryHandlerAddForm.SavingCardRate != null || saleGeneralCardCategoryHandlerAddForm.SavingCardFixed != null ||
                        saleGeneralCardCategoryHandlerAddForm.SavingCardLargessRate != null || saleGeneralCardCategoryHandlerAddForm.SavingCardLargessFixed != null ||
                        saleGeneralCardCategoryHandlerAddForm.SpecialBenefit != null) {
                    SaleGeneralCardCategoryHandlerCommissionEntity saleGeneralCardCategoryHandlerCommissionEntity = new SaleGeneralCardCategoryHandlerCommissionEntity();
                    saleGeneralCardCategoryHandlerCommissionEntity.EntityID = form.EntityID;
                    saleGeneralCardCategoryHandlerCommissionEntity.GeneralCardCategoryID = form.CategoryID;
                    saleGeneralCardCategoryHandlerCommissionEntity.GeneralCardSaleHandlerID = saleGeneralCardCategoryHandlerAddForm.SaleHandlerID;
                    saleGeneralCardCategoryHandlerCommissionEntity.PayFixed = saleGeneralCardCategoryHandlerAddForm.PayFixed;
                    saleGeneralCardCategoryHandlerCommissionEntity.PayRate = saleGeneralCardCategoryHandlerAddForm.PayRate;
                    saleGeneralCardCategoryHandlerCommissionEntity.SavingCardFixed = saleGeneralCardCategoryHandlerAddForm.SavingCardFixed;
                    saleGeneralCardCategoryHandlerCommissionEntity.SavingCardRate = saleGeneralCardCategoryHandlerAddForm.SavingCardRate;
                    saleGeneralCardCategoryHandlerCommissionEntity.SavingCardLargessFixed = saleGeneralCardCategoryHandlerAddForm.SavingCardLargessFixed;
                    saleGeneralCardCategoryHandlerCommissionEntity.SavingCardLargessRate = saleGeneralCardCategoryHandlerAddForm.SavingCardLargessRate;
                    saleGeneralCardCategoryHandlerCommissionEntity.SpecialBenefit = saleGeneralCardCategoryHandlerAddForm.SpecialBenefit;
                    list.add(saleGeneralCardCategoryHandlerCommissionEntity);
                }
                if (saleGeneralCardCategoryHandlerAddForm.HandlerJobTypeCommission != null && saleGeneralCardCategoryHandlerAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (SaleGeneralCardCategoryHandlerJobTypeCommissionAddForm saleGeneralCardCategoryHandlerJobTypeCommissionAddForm : saleGeneralCardCategoryHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.PayRate != null || saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardRate != null || saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed != null ||
                                saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null || saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed != null ||
                                saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SaleGeneralCardCategoryHandlerJobTypeCommissionEntity saleGeneralCardCategoryHandlerJobTypeCommissionEntity = new SaleGeneralCardCategoryHandlerJobTypeCommissionEntity();
                            saleGeneralCardCategoryHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            saleGeneralCardCategoryHandlerJobTypeCommissionEntity.GeneralCardCategoryID = form.CategoryID;
                            saleGeneralCardCategoryHandlerJobTypeCommissionEntity.GeneralCardSaleHandlerID = saleGeneralCardCategoryHandlerAddForm.SaleHandlerID;
                            saleGeneralCardCategoryHandlerJobTypeCommissionEntity.JobTypeID = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.JobTypeID;
                            saleGeneralCardCategoryHandlerJobTypeCommissionEntity.PayFixed = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.PayFixed;
                            saleGeneralCardCategoryHandlerJobTypeCommissionEntity.PayRate = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.PayRate;
                            saleGeneralCardCategoryHandlerJobTypeCommissionEntity.SavingCardFixed = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed;
                            saleGeneralCardCategoryHandlerJobTypeCommissionEntity.SavingCardRate = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardRate;
                            saleGeneralCardCategoryHandlerJobTypeCommissionEntity.SavingCardLargessFixed = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed;
                            saleGeneralCardCategoryHandlerJobTypeCommissionEntity.SavingCardLargessRate = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            saleGeneralCardCategoryHandlerJobTypeCommissionEntity.SpecialBenefit = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            handlerJobTypeCommissionList.add(saleGeneralCardCategoryHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
        }

        salePackageCardGeneralCardCategoryHandlerCommissionMapper.deleteSalePackageCardGeneralCardCategoryHandler(form.EntityID, form.CategoryID);
        salePackageCardGeneralCardCategoryHandlerCommissionMapper.deleteSalePackageCardGeneralCardCategoryHandlerJobType(form.EntityID, form.CategoryID);
        List<SalePackageCardGeneralCardCategoryHandlerCommissionEntity> packageList = new ArrayList<>();
        List<SalePackageCardGeneralCardCategoryHandlerJobTypeCommissionEntity> packageCardHandlerJobTypeCommissionList = new ArrayList<>();
        if (form.PackageCardGeneralCardHandler != null && form.PackageCardGeneralCardHandler.size() > 0) {
            for (SaleGeneralCardCategoryHandlerAddForm saleGeneralCardCategoryHandlerAddForm : form.PackageCardGeneralCardHandler) {
                if (saleGeneralCardCategoryHandlerAddForm.PayRate != null || saleGeneralCardCategoryHandlerAddForm.PayFixed != null ||
                        saleGeneralCardCategoryHandlerAddForm.SavingCardRate != null || saleGeneralCardCategoryHandlerAddForm.SavingCardFixed != null ||
                        saleGeneralCardCategoryHandlerAddForm.SavingCardLargessRate != null || saleGeneralCardCategoryHandlerAddForm.SavingCardLargessFixed != null ||
                        saleGeneralCardCategoryHandlerAddForm.SpecialBenefit != null) {
                    SalePackageCardGeneralCardCategoryHandlerCommissionEntity salePackageCardGeneralCardCategoryHandlerCommissionEntity = new SalePackageCardGeneralCardCategoryHandlerCommissionEntity();
                    salePackageCardGeneralCardCategoryHandlerCommissionEntity.EntityID = form.EntityID;
                    salePackageCardGeneralCardCategoryHandlerCommissionEntity.GeneralCardCategoryID = form.CategoryID;
                    salePackageCardGeneralCardCategoryHandlerCommissionEntity.PackageCardSaleHandlerID = saleGeneralCardCategoryHandlerAddForm.SaleHandlerID;
                    salePackageCardGeneralCardCategoryHandlerCommissionEntity.PayFixed = saleGeneralCardCategoryHandlerAddForm.PayFixed;
                    salePackageCardGeneralCardCategoryHandlerCommissionEntity.PayRate = saleGeneralCardCategoryHandlerAddForm.PayRate;
                    salePackageCardGeneralCardCategoryHandlerCommissionEntity.SavingCardFixed = saleGeneralCardCategoryHandlerAddForm.SavingCardFixed;
                    salePackageCardGeneralCardCategoryHandlerCommissionEntity.SavingCardRate = saleGeneralCardCategoryHandlerAddForm.SavingCardRate;
                    salePackageCardGeneralCardCategoryHandlerCommissionEntity.SavingCardLargessFixed = saleGeneralCardCategoryHandlerAddForm.SavingCardLargessFixed;
                    salePackageCardGeneralCardCategoryHandlerCommissionEntity.SavingCardLargessRate = saleGeneralCardCategoryHandlerAddForm.SavingCardLargessRate;
                    salePackageCardGeneralCardCategoryHandlerCommissionEntity.SpecialBenefit = saleGeneralCardCategoryHandlerAddForm.SpecialBenefit;
                    packageList.add(salePackageCardGeneralCardCategoryHandlerCommissionEntity);
                }
                if (saleGeneralCardCategoryHandlerAddForm.HandlerJobTypeCommission != null && saleGeneralCardCategoryHandlerAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (SaleGeneralCardCategoryHandlerJobTypeCommissionAddForm saleGeneralCardCategoryHandlerJobTypeCommissionAddForm : saleGeneralCardCategoryHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.PayRate != null || saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardRate != null || saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed != null ||
                                saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null || saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed != null ||
                                saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SalePackageCardGeneralCardCategoryHandlerJobTypeCommissionEntity salePackageCardGeneralCardCategoryHandlerJobTypeCommissionEntity = new SalePackageCardGeneralCardCategoryHandlerJobTypeCommissionEntity();
                            salePackageCardGeneralCardCategoryHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            salePackageCardGeneralCardCategoryHandlerJobTypeCommissionEntity.GeneralCardCategoryID = form.CategoryID;
                            salePackageCardGeneralCardCategoryHandlerJobTypeCommissionEntity.PackageCardSaleHandlerID = saleGeneralCardCategoryHandlerAddForm.SaleHandlerID;
                            salePackageCardGeneralCardCategoryHandlerJobTypeCommissionEntity.JobTypeID = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.JobTypeID;
                            salePackageCardGeneralCardCategoryHandlerJobTypeCommissionEntity.PayFixed = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.PayFixed;
                            salePackageCardGeneralCardCategoryHandlerJobTypeCommissionEntity.PayRate = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.PayRate;
                            salePackageCardGeneralCardCategoryHandlerJobTypeCommissionEntity.SavingCardFixed = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed;
                            salePackageCardGeneralCardCategoryHandlerJobTypeCommissionEntity.SavingCardRate = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardRate;
                            salePackageCardGeneralCardCategoryHandlerJobTypeCommissionEntity.SavingCardLargessFixed = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed;
                            salePackageCardGeneralCardCategoryHandlerJobTypeCommissionEntity.SavingCardLargessRate = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            salePackageCardGeneralCardCategoryHandlerJobTypeCommissionEntity.SpecialBenefit = saleGeneralCardCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            packageCardHandlerJobTypeCommissionList.add(salePackageCardGeneralCardCategoryHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
        }

        if (list != null && list.size() > 0) {
            List<List<SaleGeneralCardCategoryHandlerCommissionEntity>> subs = Lists.partition(list, 20);
            for (List<SaleGeneralCardCategoryHandlerCommissionEntity> entityList : subs) {
                saleGeneralCardCategoryHandlerCommissionMapper.insertSaleGeneralCardCategoryHandlerCommission(entityList);
            }
        }

        if (handlerJobTypeCommissionList != null && handlerJobTypeCommissionList.size() > 0) {
            List<List<SaleGeneralCardCategoryHandlerJobTypeCommissionEntity>> subs = Lists.partition(handlerJobTypeCommissionList, 20);
            for (List<SaleGeneralCardCategoryHandlerJobTypeCommissionEntity> entityList : subs) {
                saleGeneralCardCategoryHandlerCommissionMapper.insertSaleGeneralCardCategoryHandlerJobTypeCommission(entityList);
            }
        }
        if (packageList != null && packageList.size() > 0) {
            List<List<SalePackageCardGeneralCardCategoryHandlerCommissionEntity>> subs = Lists.partition(packageList, 20);
            for (List<SalePackageCardGeneralCardCategoryHandlerCommissionEntity> entityList : subs) {
                salePackageCardGeneralCardCategoryHandlerCommissionMapper.insertSalePackageCardGeneralCardCategoryHandlerCommission(entityList);
            }
        }
        if (packageCardHandlerJobTypeCommissionList != null && packageCardHandlerJobTypeCommissionList.size() > 0) {
            List<List<SalePackageCardGeneralCardCategoryHandlerJobTypeCommissionEntity>> subs = Lists.partition(packageCardHandlerJobTypeCommissionList, 20);
            for (List<SalePackageCardGeneralCardCategoryHandlerJobTypeCommissionEntity> entityList : subs) {
                salePackageCardGeneralCardCategoryHandlerCommissionMapper.insertSalePackageCardGeneralCardCategoryHandlerJobTypeCommission(entityList);
            }
        }

        return BaseOutput.success();
    }
}