package com.zhelian.service.ibeauty.treatProjectCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.TreatProjectCategoryHandlerCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.TreatProjectCategoryHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.TreatProjectCategoryHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.input.TreatProjectCategoryHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.TreatProjectCategoryHandlerCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatProjectCategoryHandlerCommissionService {

    @Autowired
    TreatProjectCategoryHandlerCommissionMapper treatProjectCategoryHandlerCommissionMapper;


    public BaseOutput allTreatProjectCategoryHandlerCommission(TreatProjectCategoryHandlerCommissionQueryForm form) {
        List<TreatProjectCategoryHandlerCommissionOutputForm> list = treatProjectCategoryHandlerCommissionMapper.allTreatProjectCategoryHandlerCommission(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatProjectCategoryHandlerCommission(TreatProjectCategoryHandlerCommissionAddForm form) {


        treatProjectCategoryHandlerCommissionMapper.deleteTreatProjectCategoryHandler(form.EntityID, form.CategoryID);
        treatProjectCategoryHandlerCommissionMapper.deleteTreatProjectCategoryHandlerJobType(form.EntityID, form.CategoryID);

        if (form.Handler != null && form.Handler.size() > 0) {

            List<TreatProjectCategoryHandlerCommissionEntity> listTreatProjectCategoryHandlerCommission = new ArrayList<>();

            List<TreatProjectCategoryHandlerJobTypeCommissionEntity> listTreatProjectCategoryHandlerJobTypeCommission = new ArrayList<>();


            for (TreatProjectCategoryHandlerAddForm treatProjectCategoryHandlerAddForm : form.Handler) {
                if (treatProjectCategoryHandlerAddForm.PayRate != null || treatProjectCategoryHandlerAddForm.PayFixed != null ||
                        treatProjectCategoryHandlerAddForm.CardRate != null || treatProjectCategoryHandlerAddForm.CardFixed != null ||
                        treatProjectCategoryHandlerAddForm.CardLargessRate != null || treatProjectCategoryHandlerAddForm.CardLargessFixed != null ||
                        treatProjectCategoryHandlerAddForm.LargessRate != null || treatProjectCategoryHandlerAddForm.LargessFixed != null ||
                        treatProjectCategoryHandlerAddForm.SpecialBenefit != null) {
                    TreatProjectCategoryHandlerCommissionEntity treatProjectCategoryHandlerCommissionEntity = new TreatProjectCategoryHandlerCommissionEntity();
                    treatProjectCategoryHandlerCommissionEntity.EntityID = form.EntityID;
                    treatProjectCategoryHandlerCommissionEntity.ProjectCategoryID = form.CategoryID;
                    treatProjectCategoryHandlerCommissionEntity.ProjectTreatHandlerID = treatProjectCategoryHandlerAddForm.TreatHandlerID;
                    treatProjectCategoryHandlerCommissionEntity.PayRate = treatProjectCategoryHandlerAddForm.PayRate;
                    treatProjectCategoryHandlerCommissionEntity.PayFixed = treatProjectCategoryHandlerAddForm.PayFixed;
                    treatProjectCategoryHandlerCommissionEntity.CardRate = treatProjectCategoryHandlerAddForm.CardRate;
                    treatProjectCategoryHandlerCommissionEntity.CardFixed = treatProjectCategoryHandlerAddForm.CardFixed;
                    treatProjectCategoryHandlerCommissionEntity.CardLargessRate = treatProjectCategoryHandlerAddForm.CardLargessRate;
                    treatProjectCategoryHandlerCommissionEntity.CardLargessFixed = treatProjectCategoryHandlerAddForm.CardLargessFixed;
                    treatProjectCategoryHandlerCommissionEntity.LargessFixed = treatProjectCategoryHandlerAddForm.LargessFixed;
                    treatProjectCategoryHandlerCommissionEntity.LargessRate = treatProjectCategoryHandlerAddForm.LargessRate;
                    treatProjectCategoryHandlerCommissionEntity.SpecialBenefit = treatProjectCategoryHandlerAddForm.SpecialBenefit;
                    listTreatProjectCategoryHandlerCommission.add(treatProjectCategoryHandlerCommissionEntity);
                }

                if (treatProjectCategoryHandlerAddForm.HandlerJobTypeCommission != null && treatProjectCategoryHandlerAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (TreatProjectCategoryHandlerJobTypeCommissionAddForm treatProjectCategoryHandlerJobTypeCommissionAddForm : treatProjectCategoryHandlerAddForm.HandlerJobTypeCommission) {
                        if (treatProjectCategoryHandlerJobTypeCommissionAddForm.PayRate != null || treatProjectCategoryHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                treatProjectCategoryHandlerJobTypeCommissionAddForm.CardRate != null || treatProjectCategoryHandlerJobTypeCommissionAddForm.CardFixed != null ||
                                treatProjectCategoryHandlerJobTypeCommissionAddForm.CardLargessRate != null || treatProjectCategoryHandlerJobTypeCommissionAddForm.CardLargessFixed != null ||
                                treatProjectCategoryHandlerJobTypeCommissionAddForm.LargessRate != null || treatProjectCategoryHandlerJobTypeCommissionAddForm.LargessFixed != null ||
                                treatProjectCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            TreatProjectCategoryHandlerJobTypeCommissionEntity treatProjectCategoryHandlerJobTypeCommissionEntity = new TreatProjectCategoryHandlerJobTypeCommissionEntity();
                            treatProjectCategoryHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            treatProjectCategoryHandlerJobTypeCommissionEntity.ProjectCategoryID = form.CategoryID;
                            treatProjectCategoryHandlerJobTypeCommissionEntity.ProjectTreatHandlerID = treatProjectCategoryHandlerAddForm.TreatHandlerID;
                            treatProjectCategoryHandlerJobTypeCommissionEntity.JobTypeID = treatProjectCategoryHandlerJobTypeCommissionAddForm.JobTypeID;
                            treatProjectCategoryHandlerJobTypeCommissionEntity.PayRate = treatProjectCategoryHandlerJobTypeCommissionAddForm.PayRate;
                            treatProjectCategoryHandlerJobTypeCommissionEntity.PayFixed = treatProjectCategoryHandlerJobTypeCommissionAddForm.PayFixed;
                            treatProjectCategoryHandlerJobTypeCommissionEntity.CardRate = treatProjectCategoryHandlerJobTypeCommissionAddForm.CardRate;
                            treatProjectCategoryHandlerJobTypeCommissionEntity.CardFixed = treatProjectCategoryHandlerJobTypeCommissionAddForm.CardFixed;
                            treatProjectCategoryHandlerJobTypeCommissionEntity.CardLargessRate = treatProjectCategoryHandlerJobTypeCommissionAddForm.CardLargessRate;
                            treatProjectCategoryHandlerJobTypeCommissionEntity.CardLargessFixed = treatProjectCategoryHandlerJobTypeCommissionAddForm.CardLargessFixed;
                            treatProjectCategoryHandlerJobTypeCommissionEntity.LargessFixed = treatProjectCategoryHandlerJobTypeCommissionAddForm.LargessFixed;
                            treatProjectCategoryHandlerJobTypeCommissionEntity.LargessRate = treatProjectCategoryHandlerJobTypeCommissionAddForm.LargessRate;
                            treatProjectCategoryHandlerJobTypeCommissionEntity.SpecialBenefit = treatProjectCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            listTreatProjectCategoryHandlerJobTypeCommission.add(treatProjectCategoryHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
            if (listTreatProjectCategoryHandlerCommission != null && listTreatProjectCategoryHandlerCommission.size() > 0) {
                List<List<TreatProjectCategoryHandlerCommissionEntity>> subs = Lists.partition(listTreatProjectCategoryHandlerCommission, 20);
                for (List<TreatProjectCategoryHandlerCommissionEntity> entityList : subs) {
                    treatProjectCategoryHandlerCommissionMapper.updateTreatProjectCategoryHandlerCommission(entityList);
                }
            }
            if (listTreatProjectCategoryHandlerJobTypeCommission != null && listTreatProjectCategoryHandlerJobTypeCommission.size() > 0) {
                List<List<TreatProjectCategoryHandlerJobTypeCommissionEntity>> subs = Lists.partition(listTreatProjectCategoryHandlerJobTypeCommission, 20);
                for (List<TreatProjectCategoryHandlerJobTypeCommissionEntity> entityList : subs) {
                    treatProjectCategoryHandlerCommissionMapper.insertTreatProjectCategoryHandlerJobTypeCommission(entityList);
                }
            }
        }
        return BaseOutput.success();
    }
}