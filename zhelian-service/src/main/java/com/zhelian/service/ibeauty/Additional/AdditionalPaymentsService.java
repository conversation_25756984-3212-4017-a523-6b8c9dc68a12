package com.zhelian.service.ibeauty.Additional;

import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.AdditionalPaymentsMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.AdditionalPaymentsEntity;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingOrIncomeOrPaymentsCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingOrIncomeOrPaymentsCategoryQueryForm;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingOrIncomeOrPaymentsCategoryUpdateForm;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingOrIncomeOrPaymentsMoveForm;
import com.zhelian.model.ibeauty.form.output.AdditionalSpendingOrIncomeOrPaymentsCategoryOutputForm;
import com.zhelian.model.ibeauty.form.output.SpendingCategoryOrIncomeOrPaymentsOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * @className: AdditionalPaymentsService
 * @description:
 * @version: V1.0
 * @author: zhoutianxiang
 * @date: 2020/4/25 4:17 PM
 */
@Service
public class AdditionalPaymentsService {

    @Autowired
    AdditionalPaymentsMapper additionalPaymentsMapper;

    public BaseOutput all(AdditionalSpendingOrIncomeOrPaymentsCategoryQueryForm form) {

        List<SpendingCategoryOrIncomeOrPaymentsOutputForm> list = additionalPaymentsMapper.additionalPaymentsList(form.Name, form.Active);

        return BaseOutputForm.success(list);
    }


    public BaseOutput create(AdditionalSpendingOrIncomeOrPaymentsCategoryAddForm form) {

        Integer Sequence = additionalPaymentsMapper.getMaxSequence();
        AdditionalPaymentsEntity additionalPaymentsEntity = new AdditionalPaymentsEntity();
        additionalPaymentsEntity.Name = form.Name;
        additionalPaymentsEntity.Sequence = Sequence != null ? Sequence + 1 : 1;

        additionalPaymentsMapper.createAdditionalPayments(additionalPaymentsEntity);

        return BaseOutput.success();
    }

    public BaseOutput update(AdditionalSpendingOrIncomeOrPaymentsCategoryUpdateForm form) {

        AdditionalPaymentsEntity additionalPaymentsEntity = new AdditionalPaymentsEntity();
        additionalPaymentsEntity.ID = form.ID;
        additionalPaymentsEntity.Name = form.Name;
        additionalPaymentsEntity.Active = form.Active;

        additionalPaymentsMapper.updateAdditionalPayments(additionalPaymentsEntity);

        return BaseOutput.success();
    }

    public BaseOutput move(AdditionalSpendingOrIncomeOrPaymentsMoveForm form) {

        //重新排序
        LinkedList<Integer> linkedList = additionalPaymentsMapper.getIDOrderBySequenceASC(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<AdditionalPaymentsEntity> list = new ArrayList<>();

        for (Integer ID : linkedList) {
            AdditionalPaymentsEntity additionalPaymentsEntity = new AdditionalPaymentsEntity();
            additionalPaymentsEntity.ID = ID;
            additionalPaymentsEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(additionalPaymentsEntity);
        }
        additionalPaymentsMapper.updateSequence(list);
        return BaseOutput.success();
    }

    public BaseOutput getAdditionalPayments() {
        List<AdditionalSpendingOrIncomeOrPaymentsCategoryOutputForm> list = additionalPaymentsMapper.getAdditionalPayments();
        return BaseOutputForm.success(list);
    }
}