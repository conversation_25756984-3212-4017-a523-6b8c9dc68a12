package com.zhelian.service.ibeauty.treatGeneralCardCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.TreatGeneralCardCategoryCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.TreatGeneralCardCategoryCommissionEntity;
import com.zhelian.model.ibeauty.entity.TreatGeneralCardProjectCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCategoryCommissionQueryForm;
import com.zhelian.model.ibeauty.form.output.TreatGeneralCardCategoryCommissionOutputForm;
import com.zhelian.model.ibeauty.form.output.TreatGeneralCardCategoryOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatGeneralCardCategoryCommissionService {

    @Autowired
    TreatGeneralCardCategoryCommissionMapper treatGeneralCardCategoryCommissionMapper;


    public BaseOutput alltreatGeneralCardProjectCategoryCommission(TreatSavingCardProjectCategoryCommissionQueryForm form) {
        TreatGeneralCardCategoryOutputForm saleGeneralCardCategoryOutputForm = treatGeneralCardCategoryCommissionMapper.treatGeneralCardProjectCategoryCommission(form.EntityID);
        List<TreatGeneralCardCategoryCommissionOutputForm> all = treatGeneralCardCategoryCommissionMapper.allSaleGeneralCardCategoryCommission(form.EntityID);
        saleGeneralCardCategoryOutputForm.Category = all;
        return BaseOutputForm.success(saleGeneralCardCategoryOutputForm);
    }

    @Transactional
    public BaseOutput updateTreatGeneralCardProjectCategoryCommission(TreatCardCategoryAddForm form) {
        TreatGeneralCardProjectCommissionSchemeEntity treatGeneralCardProjectCommissionSchemeEntity = new TreatGeneralCardProjectCommissionSchemeEntity();
        treatGeneralCardProjectCommissionSchemeEntity.EntityID = form.EntityID;
        treatGeneralCardProjectCommissionSchemeEntity.PayRate = form.PayRate;
        treatGeneralCardProjectCommissionSchemeEntity.PayFixed = form.PayFixed;
        treatGeneralCardProjectCommissionSchemeEntity.CardRate = form.CardRate;
        treatGeneralCardProjectCommissionSchemeEntity.CardFixed = form.CardFixed;
        treatGeneralCardProjectCommissionSchemeEntity.CardLargessRate = form.CardLargessRate;
        treatGeneralCardProjectCommissionSchemeEntity.CardLargessFixed = form.CardLargessFixed;
        treatGeneralCardProjectCommissionSchemeEntity.LargessRate = form.LargessRate;
        treatGeneralCardProjectCommissionSchemeEntity.LargessFixed = form.LargessFixed;
        treatGeneralCardProjectCommissionSchemeEntity.SpecialBenefit = form.SpecialBenefit;

        treatGeneralCardCategoryCommissionMapper.updateTreatGeneralCardCommissionScheme(treatGeneralCardProjectCommissionSchemeEntity);
        treatGeneralCardCategoryCommissionMapper.deleteTreatGeneralCardCategoryCommission(form.EntityID);

        if (form.Category != null && form.Category.size() > 0) {
            List<TreatGeneralCardCategoryCommissionEntity> list = new ArrayList<>();
            for (TreatCardCategoryCommissionAddForm treatCardCategoryCommissionAddForm : form.Category) {
                if (treatCardCategoryCommissionAddForm.PayRate != null || treatCardCategoryCommissionAddForm.PayFixed != null ||
                        treatCardCategoryCommissionAddForm.CardRate != null || treatCardCategoryCommissionAddForm.CardFixed != null ||
                        treatCardCategoryCommissionAddForm.CardLargessRate != null || treatCardCategoryCommissionAddForm.CardLargessFixed != null ||
                        treatCardCategoryCommissionAddForm.LargessRate != null || treatCardCategoryCommissionAddForm.LargessFixed != null ||
                        treatCardCategoryCommissionAddForm.SpecialBenefit != null) {
                    TreatGeneralCardCategoryCommissionEntity treatGeneralCardCategoryCommissionEntity = new TreatGeneralCardCategoryCommissionEntity();
                    treatGeneralCardCategoryCommissionEntity.EntityID = form.EntityID;
                    treatGeneralCardCategoryCommissionEntity.GeneralCardCategoryID = treatCardCategoryCommissionAddForm.CategoryID;
                    treatGeneralCardCategoryCommissionEntity.PayRate = treatCardCategoryCommissionAddForm.PayRate;
                    treatGeneralCardCategoryCommissionEntity.PayFixed = treatCardCategoryCommissionAddForm.PayFixed;
                    treatGeneralCardCategoryCommissionEntity.CardRate = treatCardCategoryCommissionAddForm.CardRate;
                    treatGeneralCardCategoryCommissionEntity.CardFixed = treatCardCategoryCommissionAddForm.CardFixed;
                    treatGeneralCardCategoryCommissionEntity.CardLargessRate = treatCardCategoryCommissionAddForm.CardLargessRate;
                    treatGeneralCardCategoryCommissionEntity.CardLargessFixed = treatCardCategoryCommissionAddForm.CardLargessFixed;
                    treatGeneralCardCategoryCommissionEntity.LargessRate = treatCardCategoryCommissionAddForm.LargessRate;
                    treatGeneralCardCategoryCommissionEntity.LargessFixed = treatCardCategoryCommissionAddForm.LargessFixed;
                    treatGeneralCardCategoryCommissionEntity.SpecialBenefit = treatCardCategoryCommissionAddForm.SpecialBenefit;
                    list.add(treatGeneralCardCategoryCommissionEntity);
                }
            }
            List<List<TreatGeneralCardCategoryCommissionEntity>> subs = Lists.partition(list, 20);
            for (List<TreatGeneralCardCategoryCommissionEntity> entityList : subs) {
                treatGeneralCardCategoryCommissionMapper.insertTreatGeneralCardCategoryCommissionEntity(entityList);
            }
        }

        return BaseOutput.success();
    }
}