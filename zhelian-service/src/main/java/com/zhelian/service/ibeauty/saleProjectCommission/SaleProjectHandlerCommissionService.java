package com.zhelian.service.ibeauty.saleProjectCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.SalePackageCardProjectHandlerCommissionMapper;
import com.zhelian.mapper.ibeauty.SaleProjectHandlerCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SalePackageCardProjectHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SalePackageCardProjectHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleProjectHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleProjectHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectHandlersCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.SaleProjectHandlerCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleProjectHandlerCommissionService {

    @Autowired
    SaleProjectHandlerCommissionMapper saleProjectHandlerCommissionMapper;
    @Autowired
    SalePackageCardProjectHandlerCommissionMapper salePackageCardProjectHandlerCommissionMapper;

    public BaseOutput allSaleProjectHandlerCommission(SaleProjectHandlerCommissionQueryForm form) {
        List<SaleProjectHandlerCommissionOutputForm> list = saleProjectHandlerCommissionMapper.allSaleProjectHandlerCommission(form.EntityID, form.ProjectID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSalePackageCardProjectHandlerCommission(SaleProjectHandlerCommissionQueryForm form) {
        List<SaleProjectHandlerCommissionOutputForm> list = salePackageCardProjectHandlerCommissionMapper.allSalePackageCardProjectHandlerCommission(form.EntityID, form.ProjectID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleProjectHandlerCommission(SaleProjectHandlersCommissionAddForm form) {

        saleProjectHandlerCommissionMapper.deleteSaleProjectHandler(form.EntityID, form.ProjectID);
        saleProjectHandlerCommissionMapper.deleteSaleProjectHandlerJobType(form.EntityID, form.ProjectID);
        List<SaleProjectHandlerCommissionEntity> listSaleProjectHandlerCommissionEntity = new ArrayList<>();
        List<SaleProjectHandlerJobTypeCommissionEntity> listSaleProjectHandlerJobTypeCommissionEntity = new ArrayList<>();

        if (form.ProjectHandler != null && form.ProjectHandler.size() > 0) {
            for (SaleProjectHandlerCommissionAddForm saleProjectHandlerAddForm : form.ProjectHandler) {
                if (saleProjectHandlerAddForm.PayRate != null || saleProjectHandlerAddForm.PayFixed != null ||
                        saleProjectHandlerAddForm.SavingCardRate != null || saleProjectHandlerAddForm.SavingCardFixed != null ||
                        saleProjectHandlerAddForm.SavingCardLargessRate != null || saleProjectHandlerAddForm.SavingCardLargessFixed != null ||
                        saleProjectHandlerAddForm.SpecialBenefit != null) {
                    SaleProjectHandlerCommissionEntity saleProjectHandlerCommissionEntity = new SaleProjectHandlerCommissionEntity();
                    saleProjectHandlerCommissionEntity.EntityID = form.EntityID;
                    saleProjectHandlerCommissionEntity.ProjectID = form.ProjectID;
                    saleProjectHandlerCommissionEntity.ProjectSaleHandlerID = saleProjectHandlerAddForm.SaleHandlerID;
                    saleProjectHandlerCommissionEntity.PayFixed = saleProjectHandlerAddForm.PayFixed;
                    saleProjectHandlerCommissionEntity.PayRate = saleProjectHandlerAddForm.PayRate;
                    saleProjectHandlerCommissionEntity.SavingCardFixed = saleProjectHandlerAddForm.SavingCardFixed;
                    saleProjectHandlerCommissionEntity.SavingCardRate = saleProjectHandlerAddForm.SavingCardRate;
                    saleProjectHandlerCommissionEntity.SavingCardLargessFixed = saleProjectHandlerAddForm.SavingCardLargessFixed;
                    saleProjectHandlerCommissionEntity.SavingCardLargessRate = saleProjectHandlerAddForm.SavingCardLargessRate;
                    saleProjectHandlerCommissionEntity.SpecialBenefit = saleProjectHandlerAddForm.SpecialBenefit;
                    listSaleProjectHandlerCommissionEntity.add(saleProjectHandlerCommissionEntity);
                }

                if (saleProjectHandlerAddForm.HandlerJobTypeCommission != null && saleProjectHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleProjectCategoryHandlerJobTypeCommissionAddForm saleProjectCategoryHandlerJobTypeCommissionAddForm : saleProjectHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleProjectCategoryHandlerJobTypeCommissionAddForm.PayRate != null || saleProjectCategoryHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardRate != null || saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed != null ||
                                saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null || saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed != null ||
                                saleProjectCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SaleProjectHandlerJobTypeCommissionEntity saleProjectHandlerJobTypeCommissionEntity = new SaleProjectHandlerJobTypeCommissionEntity();
                            saleProjectHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            saleProjectHandlerJobTypeCommissionEntity.ProjectID = form.ProjectID;
                            saleProjectHandlerJobTypeCommissionEntity.ProjectSaleHandlerID = saleProjectHandlerAddForm.SaleHandlerID;
                            saleProjectHandlerJobTypeCommissionEntity.JobTypeID = saleProjectCategoryHandlerJobTypeCommissionAddForm.JobTypeID;
                            saleProjectHandlerJobTypeCommissionEntity.PayFixed = saleProjectCategoryHandlerJobTypeCommissionAddForm.PayFixed;
                            saleProjectHandlerJobTypeCommissionEntity.PayRate = saleProjectCategoryHandlerJobTypeCommissionAddForm.PayRate;
                            saleProjectHandlerJobTypeCommissionEntity.SavingCardFixed = saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed;
                            saleProjectHandlerJobTypeCommissionEntity.SavingCardRate = saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardRate;
                            saleProjectHandlerJobTypeCommissionEntity.SavingCardLargessFixed = saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed;
                            saleProjectHandlerJobTypeCommissionEntity.SavingCardLargessRate = saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            saleProjectHandlerJobTypeCommissionEntity.SpecialBenefit = saleProjectCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            listSaleProjectHandlerJobTypeCommissionEntity.add(saleProjectHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
        }

        salePackageCardProjectHandlerCommissionMapper.deleteSalePackageCardProjectHandler(form.EntityID, form.ProjectID);
        salePackageCardProjectHandlerCommissionMapper.deleteSalePackageCardProjectHandlerJobType(form.EntityID, form.ProjectID);
        List<SalePackageCardProjectHandlerCommissionEntity> listSalePackageCardProjectHandlerCommissionEntity = new ArrayList<>();
        List<SalePackageCardProjectHandlerJobTypeCommissionEntity> listSalePackageCardPackageCardProjectHandlerJobTypeCommissionEntity = new ArrayList<>();

        if (form.PackageCardProjectHandler != null && form.PackageCardProjectHandler.size() > 0) {
            for (SaleProjectHandlerCommissionAddForm saleProjectHandlerAddForm : form.PackageCardProjectHandler) {
                if (saleProjectHandlerAddForm.PayRate != null || saleProjectHandlerAddForm.PayFixed != null ||
                        saleProjectHandlerAddForm.SavingCardRate != null || saleProjectHandlerAddForm.SavingCardFixed != null ||
                        saleProjectHandlerAddForm.SavingCardLargessRate != null || saleProjectHandlerAddForm.SavingCardLargessFixed != null ||
                        saleProjectHandlerAddForm.SpecialBenefit != null) {
                    SalePackageCardProjectHandlerCommissionEntity salePackageCardProjectHandlerCommissionEntity = new SalePackageCardProjectHandlerCommissionEntity();
                    salePackageCardProjectHandlerCommissionEntity.EntityID = form.EntityID;
                    salePackageCardProjectHandlerCommissionEntity.ProjectID = form.ProjectID;
                    salePackageCardProjectHandlerCommissionEntity.PackageCardSaleHandlerID = saleProjectHandlerAddForm.SaleHandlerID;
                    salePackageCardProjectHandlerCommissionEntity.PayFixed = saleProjectHandlerAddForm.PayFixed;
                    salePackageCardProjectHandlerCommissionEntity.PayRate = saleProjectHandlerAddForm.PayRate;
                    salePackageCardProjectHandlerCommissionEntity.SavingCardFixed = saleProjectHandlerAddForm.SavingCardFixed;
                    salePackageCardProjectHandlerCommissionEntity.SavingCardRate = saleProjectHandlerAddForm.SavingCardRate;
                    salePackageCardProjectHandlerCommissionEntity.SavingCardLargessFixed = saleProjectHandlerAddForm.SavingCardLargessFixed;
                    salePackageCardProjectHandlerCommissionEntity.SavingCardLargessRate = saleProjectHandlerAddForm.SavingCardLargessRate;
                    salePackageCardProjectHandlerCommissionEntity.SpecialBenefit = saleProjectHandlerAddForm.SpecialBenefit;
                    listSalePackageCardProjectHandlerCommissionEntity.add(salePackageCardProjectHandlerCommissionEntity);
                }

                if (saleProjectHandlerAddForm.HandlerJobTypeCommission != null && saleProjectHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleProjectCategoryHandlerJobTypeCommissionAddForm saleProjectCategoryHandlerJobTypeCommissionAddForm : saleProjectHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleProjectCategoryHandlerJobTypeCommissionAddForm.PayRate != null || saleProjectCategoryHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardRate != null || saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed != null ||
                                saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null || saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed != null ||
                                saleProjectCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SalePackageCardProjectHandlerJobTypeCommissionEntity salePackageCardProjectHandlerJobTypeCommissionEntity = new SalePackageCardProjectHandlerJobTypeCommissionEntity();
                            salePackageCardProjectHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            salePackageCardProjectHandlerJobTypeCommissionEntity.ProjectID = form.ProjectID;
                            salePackageCardProjectHandlerJobTypeCommissionEntity.PackageCardSaleHandlerID = saleProjectHandlerAddForm.SaleHandlerID;
                            salePackageCardProjectHandlerJobTypeCommissionEntity.JobTypeID = saleProjectCategoryHandlerJobTypeCommissionAddForm.JobTypeID;
                            salePackageCardProjectHandlerJobTypeCommissionEntity.PayFixed = saleProjectCategoryHandlerJobTypeCommissionAddForm.PayFixed;
                            salePackageCardProjectHandlerJobTypeCommissionEntity.PayRate = saleProjectCategoryHandlerJobTypeCommissionAddForm.PayRate;
                            salePackageCardProjectHandlerJobTypeCommissionEntity.SavingCardFixed = saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed;
                            salePackageCardProjectHandlerJobTypeCommissionEntity.SavingCardRate = saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardRate;
                            salePackageCardProjectHandlerJobTypeCommissionEntity.SavingCardLargessFixed = saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed;
                            salePackageCardProjectHandlerJobTypeCommissionEntity.SavingCardLargessRate = saleProjectCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            salePackageCardProjectHandlerJobTypeCommissionEntity.SpecialBenefit = saleProjectCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            listSalePackageCardPackageCardProjectHandlerJobTypeCommissionEntity.add(salePackageCardProjectHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
        }
        if (listSaleProjectHandlerCommissionEntity != null && listSaleProjectHandlerCommissionEntity.size() > 0) {
            List<List<SaleProjectHandlerCommissionEntity>> subs = Lists.partition(listSaleProjectHandlerCommissionEntity, 20);
            for (List<SaleProjectHandlerCommissionEntity> entityList : subs) {
                saleProjectHandlerCommissionMapper.updateSaleProjectHandlerCommission(entityList);
            }
        }
        if (listSaleProjectHandlerJobTypeCommissionEntity != null && listSaleProjectHandlerJobTypeCommissionEntity.size() > 0) {
            List<List<SaleProjectHandlerJobTypeCommissionEntity>> subs = Lists.partition(listSaleProjectHandlerJobTypeCommissionEntity, 20);
            for (List<SaleProjectHandlerJobTypeCommissionEntity> entityList : subs) {
                saleProjectHandlerCommissionMapper.insertSaleProjectHandlerJobTypeCommission(entityList);
            }
        }
        if (listSalePackageCardProjectHandlerCommissionEntity != null && listSalePackageCardProjectHandlerCommissionEntity.size() > 0) {
            List<List<SalePackageCardProjectHandlerCommissionEntity>> subs = Lists.partition(listSalePackageCardProjectHandlerCommissionEntity, 20);
            for (List<SalePackageCardProjectHandlerCommissionEntity> entityList : subs) {
                salePackageCardProjectHandlerCommissionMapper.updateSalePackageCardProjectHandlerCommission(entityList);
            }
        }
        if (listSalePackageCardPackageCardProjectHandlerJobTypeCommissionEntity != null && listSalePackageCardPackageCardProjectHandlerJobTypeCommissionEntity.size() > 0) {
            List<List<SalePackageCardProjectHandlerJobTypeCommissionEntity>> subs = Lists.partition(listSalePackageCardPackageCardProjectHandlerJobTypeCommissionEntity, 20);
            for (List<SalePackageCardProjectHandlerJobTypeCommissionEntity> entityList : subs) {
                salePackageCardProjectHandlerCommissionMapper.insertSalePackageCardProjectHandlerJobTypeCommission(entityList);
            }
        }
        return BaseOutput.success();
    }
}