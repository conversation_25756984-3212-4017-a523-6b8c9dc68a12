package com.zhelian.service.ibeauty.saleBill;

import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.print.PrintUtils;
import com.zhelian.core.print.entity.Print;
import com.zhelian.core.print.entity.PrintDocument;
import com.zhelian.mapper.ibeauty.ReceiptConfigMapper;
import com.zhelian.mapper.ibeauty.RefundBillMapper;
import com.zhelian.mapper.ibeauty.SaleBillMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.ReceiptConfigEntity;
import com.zhelian.model.ibeauty.form.input.SaleBillInfoQueryForm;
import com.zhelian.model.ibeauty.form.output.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class SaleBillPrintService {

    @Autowired
    SaleBillMapper saleBillMapper;
    @Autowired
    RefundBillMapper refundBillMapper;

    @Autowired
    ReceiptConfigMapper receiptConfigMapper;

    public BaseOutput getSaleBillPrintContent(SaleBillInfoQueryForm form) {

        int documentID = 1;

        List<PrintDocument> printDocuments = new ArrayList<PrintDocument>();
        //门店名称
        printDocuments.add(PrintUtils.getTitleDocument(80, documentID++, " 哲联美业珠江店"));

        printDocuments.add(PrintUtils.getCuttingLineDocument(80, documentID++, "订单信息"));

        printDocuments.add(PrintUtils.getBillInfoDocument(80, documentID++, "订单编号", "XSD20210402000026"));

        printDocuments.add(PrintUtils.getBillInfoDocument(80, documentID++, "下单时间", "2018-03-12 10:17:07"));

        printDocuments.add(PrintUtils.getBillInfoDocument(80, documentID++, "收银员", "颜常青"));

        printDocuments.add(PrintUtils.getCuttingLineDocument(80, documentID++, "消费明细"));

        printDocuments.add(PrintUtils.getBillInfoDocument(80, documentID++, "1 美容次卡", "￥ 200.00"));

        printDocuments.add(PrintUtils.getBillInfoDocument(80, documentID++, "数量", "1"));

        printDocuments.add(PrintUtils.getBillInfoDocument(80, documentID++, "小计", "￥ 200.00"));

        printDocuments.add(PrintUtils.getCuttingLineDocument(80, documentID++, ""));

        printDocuments.add(PrintUtils.getBillInfoDocument(80, documentID++, "合计", "￥ 200.00"));

        printDocuments.add(PrintUtils.getBillInfoDocument(80, documentID++, "付款:现金", "￥ 100.00"));

        printDocuments.add(PrintUtils.getBillInfoDocument(80, documentID++, "付款:支付宝", "￥ 100.00"));

        printDocuments.add(PrintUtils.getCuttingLineDocument(80, documentID++, ""));

        printDocuments.add(PrintUtils.getWriteDocument(80, documentID++, "扫码收藏店铺，随时可以预约"));

        printDocuments.add(PrintUtils.getWriteDocument(80, documentID++, "谢谢光临，欢迎再来！"));

        printDocuments.add(PrintUtils.getCuttingLineDocument(80, documentID++, ""));

        printDocuments.add(PrintUtils.getCuttingLineDocument(80, documentID++, ""));
        return BaseOutputForm.success(printDocuments);
    }

    public BaseOutput getSaleBill(SaleBillInfoQueryForm form) {
        SaleBillInfoOutputForm bill = saleBillMapper.saleBillInfo(form.SaleBillID);
        ReceiptConfigEntity receiptConfig = saleBillMapper.getReceiptConfig(SystemUserUtil.getSystemEntityID());
        if (receiptConfig == null) {
            receiptConfig = new ReceiptConfigEntity();
            receiptConfig.EntityID = SystemUserUtil.getSystemEntityID();
            receiptConfig.PrintWidth = 58;
            receiptConfig.PrintQuantity = 1;
            receiptConfig.NameEncrypt = true;
            receiptConfig.MobileEncrypt = true;
            receiptConfig.SaleGoodsOriginPrice = true;
            receiptConfig.SalePromotions = true;
            receiptConfig.SaleRefundBuyQuantity = true;
            receiptConfig.SaleRefundBuyAmount = true;
            receiptConfig.SaleRefundableAmount = true;
            receiptConfig.SaleArrearBuyQuantity = true;
            receiptConfig.SaleArrearBuyAmount = true;
            receiptConfig.TreatGoodsOriginPrice = true;
            receiptConfig.TreatPromotions = true;
            receiptConfig.WriteTextFirst = "谢谢光临，欢迎再来！";
            receiptConfig.WriteTextSecond = "";
            receiptConfig.EntityAddress = true;
            receiptConfig.SaleArrearBalanceAmount = true;
        }

        bill.SaleBillPay = saleBillMapper.getSaleBillPay(form.SaleBillID);
        bill.SaleBillPaySavingCardDeduction = saleBillMapper.getSaleBillPaySavingCardDeduction(form.SaleBillID);
        bill.RepayBill = saleBillMapper.getRepayBill(form.SaleBillID);
        bill.Product = saleBillMapper.saleBillProductInfo(form.SaleBillID);
        bill.Project = saleBillMapper.saleBillProjectInfo(form.SaleBillID);
        bill.GeneralCard = saleBillMapper.saleBillGeneralCardInfo(form.SaleBillID);
        bill.TimeCard = saleBillMapper.saleBillTimeCardInfo(form.SaleBillID);
        bill.SavingCard = saleBillMapper.saleBillSavingCardInfo(form.SaleBillID);
        bill.PackageCard = saleBillMapper.saleBillPackageCardInfo(form.SaleBillID);

        Print print = new Print();
        print.copies = receiptConfig.PrintQuantity;
        int documentID = 1;

        List<PrintDocument> printDocuments = new ArrayList<PrintDocument>();
        //门店名称
        printDocuments.add(PrintUtils.getTitleDocument(receiptConfig.PrintWidth, documentID++, bill.EntityName));

        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, "订单信息"));

        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "订单编号", form.SaleBillID));

        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "下单时间", bill.BillDate));

        if (bill.Name.equals("散客")) {
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "会员姓名", bill.Name));
        } else {
            String newName = "";
            if (bill.Name.length() > 1) {
                newName = bill.Name.substring(0, 1) + bill.Name.substring(1, bill.Name.length()).replaceAll(".{1}", "*");
            } else {
                newName = bill.Name;
            }
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "会员姓名", receiptConfig.NameEncrypt ? newName : bill.Name));
        }
        if (bill.PhoneNumber != null) {
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "会员手机号", receiptConfig.MobileEncrypt ? bill.PhoneNumber.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2") : bill.PhoneNumber));
        } else {
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "会员手机号", ""));

        }


        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "开单人", bill.EmployeeName));
        if (receiptConfig.EntityAddress) {
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "门店地址", bill.AddressDetail));
        }
        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, "消费明细"));


        if (bill.Project != null && bill.Project.size() > 0) {
            for (int i = 0; i < bill.Project.size(); i++) {
                String flag = bill.Project.get(i).IsLargess ? "【赠】" : "";
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, i + 1 + " " + bill.Project.get(i).ProjectName + flag, receiptConfig.SaleGoodsOriginPrice == false ? "" : "￥ " + bill.Project.get(i).Price.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "数量", bill.Project.get(i).Quantity.toString()));

                if (receiptConfig.SalePromotions && bill.Project.get(i).PricePreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "手动改价", "￥ " + bill.Project.get(i).PricePreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                    if (receiptConfig.SalePromotions && bill.Project.get(i).CardPreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡优惠", "￥ " + bill.Project.get(i).CardPreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                    }
                } else {
                    if (receiptConfig.SalePromotions && bill.Project.get(i).CardPreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡优惠", "￥ " + bill.Project.get(i).CardPreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                    }
                }
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + bill.Project.get(i).TotalAmount.toString()));
   /*             List<SaleBillHandlerOutputForm> handlers=bill.Project.get(i).SaleBillHandler;
                if(receiptConfig.Handler){
                    if(handlers!=null && handlers.size()>0){
                        for (int j = 0; j < handlers.size(); j++) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).SaleHandlerName, ""));
                            List<SaleBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                            for (int k = 0; k < employee.size(); k++) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                            }
                        }
                    }
                }*/
            }
        }
        if (bill.SavingCard != null && bill.SavingCard.size() > 0) {
            for (int i = 0; i < bill.SavingCard.size(); i++) {
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, bill.Project.size() + i + 1 + " " + bill.SavingCard.get(i).SavingCardName, receiptConfig.SaleGoodsOriginPrice == false ? "" : "￥ " + bill.SavingCard.get(i).Amount.toString()));
                if (bill.SavingCard.get(i).LargessAmount.compareTo(BigDecimal.ZERO) == 1) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "充值赠送", "￥ " + bill.SavingCard.get(i).LargessAmount.toString()));
                }
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "数量", bill.SavingCard.get(i).Quantity.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + bill.SavingCard.get(i).TotalAmount.toString()));
/*                List<SaleBillHandlerOutputForm> handlers=bill.SavingCard.get(i).SaleBillHandler;
                if(receiptConfig.Handler){
                if(handlers!=null && handlers.size()>0){
                    for (int j = 0; j < handlers.size(); j++) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).SaleHandlerName, ""));
                        List<SaleBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                        for (int k = 0; k < employee.size(); k++) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                        }
                    }
                }
                }*/
            }
        }
        if (bill.TimeCard != null && bill.TimeCard.size() > 0) {
            for (int i = 0; i < bill.TimeCard.size(); i++) {
                String flag = bill.TimeCard.get(i).IsLargess ? "【赠】" : "";
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, bill.Project.size() + bill.SavingCard.size() + i + 1 + " " + bill.TimeCard.get(i).TimeCardName + flag, receiptConfig.SaleGoodsOriginPrice == false ? "" : "￥ " + bill.TimeCard.get(i).Price.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "数量", bill.TimeCard.get(i).Quantity.toString()));
                if (receiptConfig.SalePromotions && bill.TimeCard.get(i).PricePreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "手动改价", "￥ " + bill.TimeCard.get(i).PricePreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                    if (receiptConfig.SalePromotions && bill.TimeCard.get(i).CardPreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡优惠", "￥ " + bill.TimeCard.get(i).CardPreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + bill.TimeCard.get(i).TotalAmount.toString()));
                    } else {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + bill.TimeCard.get(i).TotalAmount.toString()));
                    }
                } else {
                    if (receiptConfig.SalePromotions && bill.TimeCard.get(i).CardPreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡优惠", "￥ " + bill.TimeCard.get(i).CardPreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + bill.TimeCard.get(i).TotalAmount.toString()));
                    } else {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + bill.TimeCard.get(i).TotalAmount.toString()));

                    }
                }
/*                List<SaleBillHandlerOutputForm> handlers=bill.TimeCard.get(i).SaleBillHandler;
                if(receiptConfig.Handler){
                if(handlers!=null && handlers.size()>0){
                    for (int j = 0; j < handlers.size(); j++) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).SaleHandlerName, ""));
                        List<SaleBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                        for (int k = 0; k < employee.size(); k++) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                        }
                    }
                }
                }*/
            }
        }
        if (bill.GeneralCard != null && bill.GeneralCard.size() > 0) {
            for (int i = 0; i < bill.GeneralCard.size(); i++) {
                String flag = bill.GeneralCard.get(i).IsLargess ? "【赠】" : "";
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, bill.Project.size() + bill.SavingCard.size() + bill.TimeCard.size() + i + 1 + " " + bill.GeneralCard.get(i).GeneralCardName + flag, receiptConfig.SaleGoodsOriginPrice == false ? "" : "￥ " + bill.GeneralCard.get(i).Price.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "数量", bill.GeneralCard.get(i).Quantity.toString()));
                if (receiptConfig.SalePromotions && bill.GeneralCard.get(i).PricePreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "手动改价", "￥ " + bill.GeneralCard.get(i).PricePreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                    if (receiptConfig.SalePromotions && bill.GeneralCard.get(i).CardPreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡优惠", "￥ " + bill.GeneralCard.get(i).CardPreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + bill.GeneralCard.get(i).TotalAmount.toString()));
                    } else {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + bill.GeneralCard.get(i).TotalAmount.toString()));
                    }
                } else {
                    if (receiptConfig.SalePromotions && bill.GeneralCard.get(i).CardPreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡优惠", "￥ " + bill.GeneralCard.get(i).CardPreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + bill.GeneralCard.get(i).TotalAmount.toString()));
                    } else {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + bill.GeneralCard.get(i).TotalAmount.toString()));
                    }
                }
/*                List<SaleBillHandlerOutputForm> handlers=bill.GeneralCard.get(i).SaleBillHandler;
                if(receiptConfig.Handler){
                if(handlers!=null && handlers.size()>0){
                    for (int j = 0; j < handlers.size(); j++) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).SaleHandlerName, ""));
                        List<SaleBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                        for (int k = 0; k < employee.size(); k++) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                        }
                    }
                }
                }*/
            }
        }
        if (bill.Product != null && bill.Product.size() > 0) {
            for (int i = 0; i < bill.Product.size(); i++) {
                String flag = bill.Product.get(i).IsLargess ? "【赠】" : "";
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, bill.Project.size() + bill.SavingCard.size() + bill.TimeCard.size() + bill.GeneralCard.size() + i + 1 + " " + bill.Product.get(i).ProductName + flag, receiptConfig.SaleGoodsOriginPrice == false ? "" : "￥ " + bill.Product.get(i).Price.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "数量", bill.Product.get(i).Quantity.toString()));
                if (receiptConfig.SalePromotions && bill.Product.get(i).PricePreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "手动改价", "￥ " + bill.Product.get(i).PricePreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                    if (bill.Product.get(i).CardPreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡优惠", "￥ " + bill.Product.get(i).CardPreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + bill.Product.get(i).TotalAmount.toString()));
                    } else {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + bill.Product.get(i).TotalAmount.toString()));
                    }
                } else {
                    if (receiptConfig.SalePromotions && bill.Product.get(i).CardPreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡优惠", "￥ " + bill.Product.get(i).CardPreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + bill.Product.get(i).TotalAmount.toString()));
                    } else {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + bill.Product.get(i).TotalAmount.toString()));
                    }
                }
 /*               List<SaleBillHandlerOutputForm> handlers=bill.Product.get(i).SaleBillHandler;
                if(receiptConfig.Handler){
                if(handlers!=null && handlers.size()>0){
                    for (int j = 0; j < handlers.size(); j++) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).SaleHandlerName, ""));
                        List<SaleBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                        for (int k = 0; k < employee.size(); k++) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                        }
                    }
                }
                }*/
            }
        }
        if (bill.PackageCard != null && bill.PackageCard.size() > 0) {

            for (int i = 0; i < bill.PackageCard.size(); i++) {
                String flag = bill.PackageCard.get(i).IsLargess ? "【赠】" : "";
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, bill.Project.size() + bill.SavingCard.size() + bill.TimeCard.size() + bill.GeneralCard.size() + bill.Product.size() + i + 1 + " " + bill.PackageCard.get(i).PackageCardName + flag, receiptConfig.SaleGoodsOriginPrice == false ? "" : "￥ " + bill.PackageCard.get(i).Price.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "数量", bill.PackageCard.get(i).Quantity.toString()));
                if (receiptConfig.SalePromotions && bill.PackageCard.get(i).PricePreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "手动改价", "￥ " + bill.PackageCard.get(i).PricePreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                    if (receiptConfig.SalePromotions && bill.PackageCard.get(i).CardPreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡优惠", "￥ " + bill.PackageCard.get(i).CardPreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + bill.PackageCard.get(i).TotalAmount.toString()));
                    } else {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + bill.PackageCard.get(i).TotalAmount.toString()));
                    }
                } else {
                    if (receiptConfig.SalePromotions && bill.PackageCard.get(i).CardPreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡优惠", "￥ " + bill.PackageCard.get(i).CardPreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + bill.PackageCard.get(i).TotalAmount.toString()));
                    } else {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + bill.PackageCard.get(i).TotalAmount.toString()));
                    }
                }
/*                List<SaleBillHandlerOutputForm> handlers=bill.PackageCard.get(i).SaleBillHandler;
                if(receiptConfig.Handler){
                if(handlers!=null && handlers.size()>0){
                    for (int j = 0; j < handlers.size(); j++) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).SaleHandlerName, ""));
                        List<SaleBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                        for (int k = 0; k < employee.size(); k++) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                        }
                    }
                }
                }*/
            }
        }
        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "合计", "￥ " + bill.Amount.toString()));
        if (bill.SaleBillPay != null && bill.SaleBillPay.size() > 0) {
            for (int i = 0; i < bill.SaleBillPay.size(); i++) {
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "付款:" + bill.SaleBillPay.get(i).Name, "￥ " + bill.SaleBillPay.get(i).Amount.toString()));
            }
        }
        if (bill.SaleBillPaySavingCardDeduction != null && bill.SaleBillPaySavingCardDeduction.size() > 0) {
            for (int i = 0; i < bill.SaleBillPaySavingCardDeduction.size(); i++) {
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡抵扣:" + bill.SaleBillPaySavingCardDeduction.get(i).Name, "￥ " + bill.SaleBillPaySavingCardDeduction.get(i).TotalAmount.toString()));
            }
        }
        if (bill.ArrearAmount.compareTo(BigDecimal.ZERO) == 1) {
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "欠款", "￥ " + bill.ArrearAmount.toString()));

        }

        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, receiptConfig.WriteTextFirst));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, receiptConfig.WriteTextSecond));

        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, ""));


        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "客户签字", ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, ""));

        print.printDocuments = printDocuments;
        return BaseOutputForm.success(print);
    }

    public BaseOutput getRefundSaleBill(SaleBillInfoQueryForm form) {
        SaleBillInfoOutputForm bill = saleBillMapper.saleBillInfo(form.SaleBillID);
        ReceiptConfigEntity receiptConfig = saleBillMapper.getReceiptConfig(SystemUserUtil.getSystemEntityID());
        if (receiptConfig == null) {
            receiptConfig = new ReceiptConfigEntity();
            receiptConfig.EntityID = SystemUserUtil.getSystemEntityID();
            receiptConfig.PrintWidth = 58;
            receiptConfig.PrintQuantity = 1;
            receiptConfig.NameEncrypt = true;
            receiptConfig.MobileEncrypt = true;
            receiptConfig.SaleGoodsOriginPrice = true;
            receiptConfig.SalePromotions = true;
            receiptConfig.SaleRefundBuyQuantity = true;
            receiptConfig.SaleRefundBuyAmount = true;
            receiptConfig.SaleRefundableAmount = true;
            receiptConfig.SaleArrearBuyQuantity = true;
            receiptConfig.SaleArrearBuyAmount = true;
            receiptConfig.TreatGoodsOriginPrice = true;
            receiptConfig.TreatPromotions = true;
            receiptConfig.WriteTextFirst = "谢谢光临，欢迎再来！";
            receiptConfig.WriteTextSecond = "";
            receiptConfig.EntityAddress = true;
            receiptConfig.SaleArrearBalanceAmount = true;
        }

        bill.SaleBillPay = saleBillMapper.getSaleBillPay(form.SaleBillID);
        bill.SaleBillPaySavingCardDeduction = saleBillMapper.getSaleBillPaySavingCardDeduction(form.SaleBillID);
        bill.RepayBill = saleBillMapper.getRepayBill(form.SaleBillID);
        bill.Product = saleBillMapper.saleBillProductInfo(form.SaleBillID);
        bill.Project = saleBillMapper.saleBillProjectInfo(form.SaleBillID);
        bill.GeneralCard = saleBillMapper.saleBillGeneralCardInfo(form.SaleBillID);
        bill.TimeCard = saleBillMapper.saleBillTimeCardInfo(form.SaleBillID);
        bill.SavingCard = saleBillMapper.saleBillSavingCardInfo(form.SaleBillID);
        bill.PackageCard = saleBillMapper.saleBillPackageCardInfo(form.SaleBillID);

        List<RefundBillInfoPackageCardOutputForm> packageCard = refundBillMapper.refundBillInfoPackageCard(form.SaleBillID);
        List<RefundBillInfoProductOutputForm> product = refundBillMapper.refundBillPackageCardProductInfo(form.SaleBillID);
        List<RefundBillInfoProjectOutputForm> project = refundBillMapper.refundBillPackageCardProjectInfo(form.SaleBillID);
        List<RefundBillInfoGeneralCardOutputForm> generalCard = refundBillMapper.refundBillPackageCardGeneralCardInfo(form.SaleBillID);
        List<RefundBillInfoTimeCardOutputForm> timeCard = refundBillMapper.refundBillPackageCardTimeCardInfo(form.SaleBillID);
        List<RefundBillInfoSavingCardOutputForm> savingCard = refundBillMapper.refundBillPackageCardSavingCardInfo(form.SaleBillID);
        for (RefundBillInfoPackageCardOutputForm packageCardForm : packageCard) {
            packageCardForm.Product = product.stream().filter(packageCardProductListOutputForm -> packageCardProductListOutputForm.RefundBillPackageCardID.intValue() == packageCardForm.ID.intValue()).collect(Collectors.toList());
            packageCardForm.Project = project.stream().filter(packageCardProjectListOutputForm -> packageCardProjectListOutputForm.RefundBillPackageCardID.intValue() == packageCardForm.ID.intValue()).collect(Collectors.toList());
            packageCardForm.GeneralCard = generalCard.stream().filter(packageCardGeneralCardListOutputForm -> packageCardGeneralCardListOutputForm.RefundBillPackageCardID.intValue() == packageCardForm.ID.intValue()).collect(Collectors.toList());
            packageCardForm.TimeCard = timeCard.stream().filter(packageCardTimeCardListOutputForm -> packageCardTimeCardListOutputForm.RefundBillPackageCardID.intValue() == packageCardForm.ID.intValue()).collect(Collectors.toList());
            packageCardForm.SavingCard = savingCard.stream().filter(packageCardSavingCardListOutputForm -> packageCardSavingCardListOutputForm.RefundBillPackageCardID.intValue() == packageCardForm.ID.intValue()).collect(Collectors.toList());
        }

        Print print = new Print();
        print.copies = receiptConfig.PrintQuantity;
        int documentID = 1;

        List<PrintDocument> printDocuments = new ArrayList<PrintDocument>();
        //门店名称
        printDocuments.add(PrintUtils.getTitleDocument(receiptConfig.PrintWidth, documentID++, bill.EntityName));

        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, "退款信息"));

        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "订单编号", form.SaleBillID));

        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "下单时间", bill.BillDate));

        if (bill.Name.equals("散客")) {
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "会员姓名", bill.Name));
        } else {
            String newName = "";
            if (bill.Name.length() > 1) {
                newName = bill.Name.substring(0, 1) + bill.Name.substring(1, bill.Name.length()).replaceAll(".{1}", "*");
            } else {
                newName = bill.Name;
            }
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "会员姓名", receiptConfig.NameEncrypt ? newName : bill.Name));
        }
        if (bill.PhoneNumber != null) {
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "会员手机号", receiptConfig.MobileEncrypt ? bill.PhoneNumber.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2") : bill.PhoneNumber));
        } else {
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "会员手机号", ""));

        }


        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "开单人", bill.EmployeeName));
        if (receiptConfig.EntityAddress) {
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "门店地址", bill.AddressDetail));
        }
        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, "退款明细"));


        if (bill.Project != null && bill.Project.size() > 0) {
            for (int i = 0; i < bill.Project.size(); i++) {
                String flag = bill.Project.get(i).IsLargess ? "【赠】" : "";
                BigDecimal refundAmount = bill.Project.get(i).TotalAmount.add(bill.Project.get(i).PreferentialTotalAmount);//可退金额
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, i + 1 + " " + bill.Project.get(i).ProjectName + flag, ""));
                if (receiptConfig.SaleRefundBuyQuantity) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买数量", bill.Project.get(i).AccountQuantity.toString()));
                    if (receiptConfig.SaleRefundBuyAmount && bill.Project.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + bill.Project.get(i).AccountTotalAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款数量", bill.Project.get(i).Quantity.toString()));
                        if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        } else {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        }
                    } else {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款数量", bill.Project.get(i).Quantity.toString()));
                        if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        } else {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        }
                    }
                } else {
                    if (receiptConfig.SaleRefundBuyAmount && bill.Project.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + bill.Project.get(i).AccountTotalAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款数量", bill.Project.get(i).Quantity.toString()));
                        if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        } else {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        }
                    } else {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款数量", bill.Project.get(i).Quantity.toString()));
                        if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        } else {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        }
                    }
                }
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款金额", "￥ " + bill.Project.get(i).TotalAmount.toString()));
 /*               List<SaleBillHandlerOutputForm> handlers=bill.Project.get(i).SaleBillHandler;
                if(receiptConfig.Handler) {
                if(handlers!=null && handlers.size()>0){
                    for (int j = 0; j < handlers.size(); j++) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).SaleHandlerName, ""));
                        List<SaleBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                        for (int k = 0; k < employee.size(); k++) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                        }
                    }
                }
                }*/
            }

        }
        if (bill.SavingCard != null && bill.SavingCard.size() > 0) {
            for (int i = 0; i < bill.SavingCard.size(); i++) {
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, bill.Project.size() + i + 1 + " " + bill.SavingCard.get(i).SavingCardName, ""));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "充值金额", "￥ " + bill.SavingCard.get(i).AccountTotalAmount.toString()));
                if (bill.SavingCard.get(i).AccountLargessAmount.compareTo(BigDecimal.ZERO) == 1) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "充值赠送", "￥ " + bill.SavingCard.get(i).AccountLargessAmount.toString()));
                }
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款金额", "￥ " + bill.SavingCard.get(i).Amount.toString()));
                if (bill.SavingCard.get(i).LargessAmount.compareTo(BigDecimal.ZERO) == 1) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款赠额", "￥ " + bill.SavingCard.get(i).LargessAmount.toString()));
                }
/*                List<SaleBillHandlerOutputForm> handlers=bill.SavingCard.get(i).SaleBillHandler;
                if(receiptConfig.Handler) {
                if(handlers!=null && handlers.size()>0){
                    for (int j = 0; j < handlers.size(); j++) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).SaleHandlerName, ""));
                        List<SaleBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                        for (int k = 0; k < employee.size(); k++) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                        }
                    }
                }
                }*/
            }
        }
        if (bill.TimeCard != null && bill.TimeCard.size() > 0) {
            for (int i = 0; i < bill.TimeCard.size(); i++) {
                String flag = bill.TimeCard.get(i).IsLargess ? "【赠】" : "";
                BigDecimal refundAmount = bill.TimeCard.get(i).TotalAmount.add(bill.TimeCard.get(i).PreferentialTotalAmount);//可退金额
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, bill.Project.size() + bill.SavingCard.size() + i + 1 + " " + bill.TimeCard.get(i).TimeCardName + flag, ""));
                if (receiptConfig.SaleRefundBuyQuantity) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买数量", "1"));
                    if (receiptConfig.SaleRefundBuyAmount && bill.TimeCard.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + bill.TimeCard.get(i).AccountTotalAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款数量", bill.TimeCard.get(i).Quantity.toString()));
                        if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        } else {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        }
                    } else {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款数量", bill.TimeCard.get(i).Quantity.toString()));
                        if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        } else {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        }
                    }
                } else {
                    if (receiptConfig.SaleRefundBuyAmount && bill.TimeCard.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + bill.TimeCard.get(i).AccountTotalAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款数量", bill.TimeCard.get(i).Quantity.toString()));
                        if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        } else {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        }
                    } else {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款数量", bill.TimeCard.get(i).Quantity.toString()));
                        if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        } else {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        }
                    }
                }
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款金额", "￥ " + bill.TimeCard.get(i).TotalAmount.toString()));

/*                List<SaleBillHandlerOutputForm> handlers=bill.TimeCard.get(i).SaleBillHandler;
                if(receiptConfig.Handler) {
                if(handlers!=null && handlers.size()>0){
                    for (int j = 0; j < handlers.size(); j++) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).SaleHandlerName, ""));
                        List<SaleBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                        for (int k = 0; k < employee.size(); k++) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                        }
                    }
                }
                }*/
            }

        }
        if (bill.GeneralCard != null && bill.GeneralCard.size() > 0) {
            for (int i = 0; i < bill.GeneralCard.size(); i++) {
                String flag = bill.GeneralCard.get(i).IsLargess ? "【赠】" : "";
                BigDecimal refundAmount = bill.GeneralCard.get(i).TotalAmount.add(bill.GeneralCard.get(i).PreferentialTotalAmount);//可退金额
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, bill.Project.size() + bill.SavingCard.size() + bill.TimeCard.size() + i + 1 + " " + bill.GeneralCard.get(i).GeneralCardName + flag, ""));
                if (receiptConfig.SaleRefundBuyQuantity) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买次数", bill.GeneralCard.get(i).AccountCardTimes.toString()));
                    if (receiptConfig.SaleRefundBuyAmount && bill.GeneralCard.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + bill.GeneralCard.get(i).AccountTotalAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退卡次数", bill.GeneralCard.get(i).CardTimes.toString()));
                        if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        } else {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        }
                    } else {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退卡次数", bill.GeneralCard.get(i).CardTimes.toString()));
                        if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        } else {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        }
                    }
                } else {
                    if (receiptConfig.SaleRefundBuyAmount && bill.GeneralCard.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + bill.GeneralCard.get(i).AccountTotalAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退卡次数", bill.GeneralCard.get(i).CardTimes.toString()));
                        if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        } else {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        }
                    } else {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退卡次数", bill.GeneralCard.get(i).CardTimes.toString()));
                        if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        } else {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        }
                    }
                }
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款金额", "￥ " + bill.GeneralCard.get(i).TotalAmount.toString()));

/*                List<SaleBillHandlerOutputForm> handlers=bill.GeneralCard.get(i).SaleBillHandler;
                if(handlers!=null && handlers.size()>0){
                    for (int j = 0; j < handlers.size(); j++) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).SaleHandlerName, ""));
                        List<SaleBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                        for (int k = 0; k < employee.size(); k++) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                        }
                    }
                }*/
            }

        }
        if (bill.Product != null && bill.Product.size() > 0) {
            for (int i = 0; i < bill.Product.size(); i++) {
                String flag = bill.Product.get(i).IsLargess ? "【赠】" : "";
                BigDecimal refundAmount = bill.Product.get(i).TotalAmount.add(bill.Product.get(i).PreferentialTotalAmount);//可退金额
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, bill.Project.size() + bill.SavingCard.size() + bill.TimeCard.size() + bill.GeneralCard.size() + i + 1 + " " + bill.Product.get(i).ProductName + flag, ""));
                if (receiptConfig.SaleRefundBuyQuantity) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买数量", bill.Product.get(i).AccountQuantity.toString()));
                    if (receiptConfig.SaleRefundBuyAmount && bill.Product.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + bill.Product.get(i).AccountTotalAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款数量", bill.Product.get(i).Quantity.toString()));
                        if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        } else {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        }
                    } else {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款数量", bill.Product.get(i).Quantity.toString()));
                        if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        } else {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        }
                    }
                } else {
                    if (receiptConfig.SaleRefundBuyAmount && bill.Product.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + bill.Product.get(i).AccountTotalAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款数量", bill.Product.get(i).Quantity.toString()));
                        if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        } else {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        }
                    } else {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款数量", bill.Product.get(i).Quantity.toString()));
                        if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        } else {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                        }
                    }
                }
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款金额", "￥ " + bill.Product.get(i).TotalAmount.toString()));
      /*          List<SaleBillHandlerOutputForm> handlers=bill.Product.get(i).SaleBillHandler;
                if(receiptConfig.Handler) {
                if(handlers!=null && handlers.size()>0){
                    for (int j = 0; j < handlers.size(); j++) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).SaleHandlerName, ""));
                        List<SaleBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                        for (int k = 0; k < employee.size(); k++) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                        }
                    }
                }
                }*/
            }

        }

        if (packageCard != null && packageCard.size() > 0) {
            int lenrth = bill.Project.size() + bill.SavingCard.size() + bill.TimeCard.size() + bill.GeneralCard.size() + bill.Product.size();
            for (RefundBillInfoPackageCardOutputForm refundBillInfoPackageCardOutputForm : packageCard) {
                if (refundBillInfoPackageCardOutputForm.Project != null && refundBillInfoPackageCardOutputForm.Project.size() > 0) {
                    for (int i = 0; i < refundBillInfoPackageCardOutputForm.Project.size(); i++) {
                        String flag = refundBillInfoPackageCardOutputForm.Project.get(i).IsLargess ? "【赠】" : "";
                        BigDecimal refundAmount = refundBillInfoPackageCardOutputForm.Project.get(i).TotalAmount.add(refundBillInfoPackageCardOutputForm.Project.get(i).PricePreferentialAmount);//可退金额
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, lenrth + i + 1 + " " + refundBillInfoPackageCardOutputForm.Project.get(i).Name + flag, ""));
                        if (receiptConfig.SaleRefundBuyQuantity) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买数量", refundBillInfoPackageCardOutputForm.Project.get(i).AccountQuantity.toString()));
                            if (receiptConfig.SaleRefundBuyAmount && refundBillInfoPackageCardOutputForm.Project.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + refundBillInfoPackageCardOutputForm.Project.get(i).AccountTotalAmount.toString()));
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款数量", refundBillInfoPackageCardOutputForm.Project.get(i).Quantity.toString()));
                                if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                } else {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                }
                            } else {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款数量", refundBillInfoPackageCardOutputForm.Project.get(i).Quantity.toString()));
                                if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                } else {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                }
                            }
                        } else {
                            if (receiptConfig.SaleRefundBuyAmount && refundBillInfoPackageCardOutputForm.Project.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + refundBillInfoPackageCardOutputForm.Project.get(i).AccountTotalAmount.toString()));
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款数量", refundBillInfoPackageCardOutputForm.Project.get(i).Quantity.toString()));
                                if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                } else {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                }
                            } else {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款数量", refundBillInfoPackageCardOutputForm.Project.get(i).Quantity.toString()));
                                if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                } else {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                }
                            }
                        }
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款金额", "￥ " + refundBillInfoPackageCardOutputForm.Project.get(i).TotalAmount.toString()));
     /*                   List<RefundBillIHandlerOutputForm> handlers=refundBillInfoPackageCardOutputForm.Project.get(i).RefundBillHandler;
                        if(handlers!=null && handlers.size()>0){
                            for (int j = 0; j < handlers.size(); j++) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).RefundHandlerName, ""));
                                List<RefundBillIEmployeeOutputForm> employee=handlers.get(j).Employee;
                                for (int k = 0; k < employee.size(); k++) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                                }
                            }
                        }*/
                    }

                }
                if (refundBillInfoPackageCardOutputForm.SavingCard != null && refundBillInfoPackageCardOutputForm.SavingCard.size() > 0) {
                    for (int i = 0; i < refundBillInfoPackageCardOutputForm.SavingCard.size(); i++) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, lenrth + refundBillInfoPackageCardOutputForm.Project.size() + i + 1 + " " + refundBillInfoPackageCardOutputForm.SavingCard.get(i).Name, ""));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "充值金额", "￥ " + refundBillInfoPackageCardOutputForm.SavingCard.get(i).AccountTotalAmount.toString()));
                        if (refundBillInfoPackageCardOutputForm.SavingCard.get(i).AccountLargessAmount.compareTo(BigDecimal.ZERO) == 1) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "充值赠送", "￥ " + refundBillInfoPackageCardOutputForm.SavingCard.get(i).AccountLargessAmount.toString()));
                        }
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款金额", "￥ " + refundBillInfoPackageCardOutputForm.SavingCard.get(i).Amount.toString()));
                        if (refundBillInfoPackageCardOutputForm.SavingCard.get(i).LargessAmount.compareTo(BigDecimal.ZERO) == 1) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款赠额", "￥ " + refundBillInfoPackageCardOutputForm.SavingCard.get(i).LargessAmount.toString()));
                        }
         /*               List<RefundBillIHandlerOutputForm> handlers=refundBillInfoPackageCardOutputForm.SavingCard.get(i).RefundBillHandler;
                        if(receiptConfig.Handler) {
                        if(handlers!=null && handlers.size()>0){
                            for (int j = 0; j < handlers.size(); j++) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).RefundHandlerName, ""));
                                List<RefundBillIEmployeeOutputForm> employee=handlers.get(j).Employee;
                                for (int k = 0; k < employee.size(); k++) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                                }
                            }
                        }
                        }*/
                    }
                }
                if (refundBillInfoPackageCardOutputForm.TimeCard != null && refundBillInfoPackageCardOutputForm.TimeCard.size() > 0) {
                    for (int i = 0; i < refundBillInfoPackageCardOutputForm.TimeCard.size(); i++) {
                        String flag = refundBillInfoPackageCardOutputForm.TimeCard.get(i).IsLargess ? "【赠】" : "";
                        BigDecimal refundAmount = refundBillInfoPackageCardOutputForm.TimeCard.get(i).TotalAmount.add(refundBillInfoPackageCardOutputForm.TimeCard.get(i).PricePreferentialAmount);//可退金额
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, lenrth + refundBillInfoPackageCardOutputForm.Project.size() + refundBillInfoPackageCardOutputForm.SavingCard.size() + i + 1 + " " + refundBillInfoPackageCardOutputForm.TimeCard.get(i).Name + flag, ""));
                        if (receiptConfig.SaleRefundBuyQuantity) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买数量", "1"));
                            if (receiptConfig.SaleRefundBuyAmount && refundBillInfoPackageCardOutputForm.TimeCard.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + refundBillInfoPackageCardOutputForm.TimeCard.get(i).AccountTotalAmount.toString()));
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款数量", "1"));
                                if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                } else {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                }
                            } else {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款数量", "1"));
                                if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                } else {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                }
                            }
                        } else {
                            if (receiptConfig.SaleRefundBuyAmount && refundBillInfoPackageCardOutputForm.TimeCard.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + refundBillInfoPackageCardOutputForm.TimeCard.get(i).AccountTotalAmount.toString()));
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款数量", "1"));
                                if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                } else {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                }
                            } else {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款数量", "1"));
                                if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                } else {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                }
                            }
                        }
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款金额", "￥ " + refundBillInfoPackageCardOutputForm.TimeCard.get(i).TotalAmount.toString()));
       /*                 List<RefundBillIHandlerOutputForm> handlers=refundBillInfoPackageCardOutputForm.TimeCard.get(i).RefundBillHandler;
                        if(receiptConfig.Handler) {
                        if(handlers!=null && handlers.size()>0){
                            for (int j = 0; j < handlers.size(); j++) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).RefundHandlerName, ""));
                                List<RefundBillIEmployeeOutputForm> employee=handlers.get(j).Employee;
                                for (int k = 0; k < employee.size(); k++) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                                }
                            }
                        }
                        }*/
                    }

                }
                if (refundBillInfoPackageCardOutputForm.GeneralCard != null && refundBillInfoPackageCardOutputForm.GeneralCard.size() > 0) {
                    for (int i = 0; i < refundBillInfoPackageCardOutputForm.GeneralCard.size(); i++) {
                        String flag = refundBillInfoPackageCardOutputForm.GeneralCard.get(i).IsLargess ? "【赠】" : "";
                        BigDecimal refundAmount = refundBillInfoPackageCardOutputForm.GeneralCard.get(i).TotalAmount.add(refundBillInfoPackageCardOutputForm.GeneralCard.get(i).PricePreferentialAmount);//可退金额
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, lenrth + refundBillInfoPackageCardOutputForm.Project.size() + refundBillInfoPackageCardOutputForm.SavingCard.size() + refundBillInfoPackageCardOutputForm.TimeCard.size() + i + 1 + " " + refundBillInfoPackageCardOutputForm.GeneralCard.get(i).Name + flag, ""));
                        if (receiptConfig.SaleRefundBuyQuantity) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买次数", refundBillInfoPackageCardOutputForm.GeneralCard.get(i).AccountCardTimes.toString()));
                            if (receiptConfig.SaleRefundBuyAmount && refundBillInfoPackageCardOutputForm.GeneralCard.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + refundBillInfoPackageCardOutputForm.GeneralCard.get(i).AccountTotalAmount.toString()));
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退卡次数", refundBillInfoPackageCardOutputForm.GeneralCard.get(i).Quantity.toString()));
                                if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                } else {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                }
                            } else {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退卡次数", refundBillInfoPackageCardOutputForm.GeneralCard.get(i).Quantity.toString()));
                                if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                } else {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                }
                            }
                        } else {
                            if (receiptConfig.SaleRefundBuyAmount && refundBillInfoPackageCardOutputForm.GeneralCard.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + refundBillInfoPackageCardOutputForm.GeneralCard.get(i).AccountTotalAmount.toString()));
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退卡次数", refundBillInfoPackageCardOutputForm.GeneralCard.get(i).Quantity.toString()));
                                if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                } else {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                }
                            } else {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退卡次数", refundBillInfoPackageCardOutputForm.GeneralCard.get(i).Quantity.toString()));
                                if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                } else {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                }
                            }
                        }
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款金额", "￥ " + refundBillInfoPackageCardOutputForm.GeneralCard.get(i).TotalAmount.toString()));
       /*                 List<RefundBillIHandlerOutputForm> handlers=refundBillInfoPackageCardOutputForm.GeneralCard.get(i).RefundBillHandler;
                        if(receiptConfig.Handler) {
                        if(handlers!=null && handlers.size()>0){
                            for (int j = 0; j < handlers.size(); j++) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).RefundHandlerName, ""));
                                List<RefundBillIEmployeeOutputForm> employee=handlers.get(j).Employee;
                                for (int k = 0; k < employee.size(); k++) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                                }
                            }
                        }
                        }*/
                    }

                }
                if (refundBillInfoPackageCardOutputForm.Product != null && refundBillInfoPackageCardOutputForm.Product.size() > 0) {
                    for (int i = 0; i < refundBillInfoPackageCardOutputForm.Product.size(); i++) {
                        String flag = refundBillInfoPackageCardOutputForm.Product.get(i).IsLargess ? "【赠】" : "";
                        BigDecimal refundAmount = refundBillInfoPackageCardOutputForm.Product.get(i).TotalAmount.add(refundBillInfoPackageCardOutputForm.Product.get(i).PricePreferentialAmount);//可退金额
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, lenrth + refundBillInfoPackageCardOutputForm.Project.size() + refundBillInfoPackageCardOutputForm.SavingCard.size() + refundBillInfoPackageCardOutputForm.TimeCard.size() + refundBillInfoPackageCardOutputForm.GeneralCard.size() + i + 1 + " " + refundBillInfoPackageCardOutputForm.Product.get(i).Name + flag, ""));
                        if (receiptConfig.SaleRefundBuyQuantity) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买数量", refundBillInfoPackageCardOutputForm.Product.get(i).AccountQuantity.toString()));
                            if (receiptConfig.SaleRefundBuyAmount && refundBillInfoPackageCardOutputForm.Product.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + refundBillInfoPackageCardOutputForm.Product.get(i).AccountTotalAmount.toString()));
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款数量", refundBillInfoPackageCardOutputForm.Product.get(i).Quantity.toString()));
                                if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                } else {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                }
                            } else {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款数量", refundBillInfoPackageCardOutputForm.Product.get(i).Quantity.toString()));
                                if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                } else {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                }
                            }
                        } else {
                            if (receiptConfig.SaleRefundBuyAmount && refundBillInfoPackageCardOutputForm.Product.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + refundBillInfoPackageCardOutputForm.Product.get(i).AccountTotalAmount.toString()));
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款数量", refundBillInfoPackageCardOutputForm.Product.get(i).Quantity.toString()));
                                if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                } else {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                }
                            } else {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款数量", refundBillInfoPackageCardOutputForm.Product.get(i).Quantity.toString()));
                                if (receiptConfig.SaleRefundableAmount && refundAmount.compareTo(BigDecimal.ZERO) != 0) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                } else {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "可退金额", "￥ " + refundAmount.toString()));
                                }
                            }
                        }
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退款金额", "￥ " + refundBillInfoPackageCardOutputForm.Product.get(i).TotalAmount.toString()));
       /*                 List<RefundBillIHandlerOutputForm> handlers=refundBillInfoPackageCardOutputForm.Product.get(i).RefundBillHandler;
                        if(receiptConfig.Handler) {
                        if(handlers!=null && handlers.size()>0){
                            for (int j = 0; j < handlers.size(); j++) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).RefundHandlerName, ""));
                                List<RefundBillIEmployeeOutputForm> employee=handlers.get(j).Employee;
                                for (int k = 0; k < employee.size(); k++) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                                }
                            }
                        }
                        }*/
                    }

                }
            }

        }
        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "合计", "￥ " + bill.Amount.toString()));
        if (bill.SaleBillPay != null && bill.SaleBillPay.size() > 0) {
            for (int i = 0; i < bill.SaleBillPay.size(); i++) {
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "付款:" + bill.SaleBillPay.get(i).Name, "￥ " + bill.SaleBillPay.get(i).Amount.toString()));
            }
        }
        if (bill.SaleBillPaySavingCardDeduction != null && bill.SaleBillPaySavingCardDeduction.size() > 0) {
            for (int i = 0; i < bill.SaleBillPaySavingCardDeduction.size(); i++) {
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡抵扣:" + bill.SaleBillPaySavingCardDeduction.get(i).Name, "￥ " + bill.SaleBillPaySavingCardDeduction.get(i).TotalAmount.toString()));
            }
        }
        if (bill.ArrearAmount.compareTo(BigDecimal.ZERO) == 1) {
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "欠款", "￥ " + bill.ArrearAmount.toString()));

        }

        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, receiptConfig.WriteTextFirst));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, receiptConfig.WriteTextSecond));

        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, ""));

        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "客户签字", ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, ""));

        print.printDocuments = printDocuments;
        return BaseOutputForm.success(print);
    }

    public BaseOutput getArrearSaleBill(SaleBillInfoQueryForm form) {
        SaleBillInfoOutputForm bill = saleBillMapper.saleBillInfo(form.SaleBillID);
        ReceiptConfigEntity receiptConfig = saleBillMapper.getReceiptConfig(SystemUserUtil.getSystemEntityID());
        if (receiptConfig == null) {
            receiptConfig = new ReceiptConfigEntity();
            receiptConfig.EntityID = SystemUserUtil.getSystemEntityID();
            receiptConfig.PrintWidth = 58;
            receiptConfig.PrintQuantity = 1;
            receiptConfig.NameEncrypt = true;
            receiptConfig.MobileEncrypt = true;
            receiptConfig.SaleGoodsOriginPrice = true;
            receiptConfig.SalePromotions = true;
            receiptConfig.SaleRefundBuyQuantity = true;
            receiptConfig.SaleRefundBuyAmount = true;
            receiptConfig.SaleRefundableAmount = true;
            receiptConfig.SaleArrearBuyQuantity = true;
            receiptConfig.SaleArrearBuyAmount = true;
            receiptConfig.TreatGoodsOriginPrice = true;
            receiptConfig.TreatPromotions = true;
            receiptConfig.WriteTextFirst = "谢谢光临，欢迎再来！";
            receiptConfig.WriteTextSecond = "";
            receiptConfig.EntityAddress = true;
            receiptConfig.SaleArrearBalanceAmount = true;
        }

        bill.SaleBillPay = saleBillMapper.getSaleBillPay(form.SaleBillID);
        bill.SaleBillPaySavingCardDeduction = saleBillMapper.getSaleBillPaySavingCardDeduction(form.SaleBillID);
        bill.RepayBill = saleBillMapper.getRepayBill(form.SaleBillID);
        bill.Product = saleBillMapper.saleBillProductInfo(form.SaleBillID);
        bill.Project = saleBillMapper.saleBillProjectInfo(form.SaleBillID);
        bill.GeneralCard = saleBillMapper.saleBillGeneralCardInfo(form.SaleBillID);
        bill.TimeCard = saleBillMapper.saleBillTimeCardInfo(form.SaleBillID);
        bill.SavingCard = saleBillMapper.saleBillSavingCardInfo(form.SaleBillID);

        List<SaleBillPackageCardInfoOutputForm> packageCardList = saleBillMapper.saleBillPackageCardInfo(form.SaleBillID);
        List<SaleBillPackageCardProductOutputForm> packageCardProductList = saleBillMapper.saleBillPackageCardProduct(form.SaleBillID);
        List<SaleBillPackageCardProjectOutputForm> packageCardProjectList = saleBillMapper.saleBillPackageCardProject(form.SaleBillID);
        List<SaleBillPackageCardGeneralCardOutputForm> packageCardGeneralCardList = saleBillMapper.saleBillPackageCardGeneralCard(form.SaleBillID);
        List<SaleBillPackageCardTimeCardOutputForm> packageCardTimeCardList = saleBillMapper.saleBillPackageCardTimeCard(form.SaleBillID);
        List<SaleBillPackageCardSavingCardOutputForm> packageCardSavingCardList = saleBillMapper.saleBillPackageCardSavingCard(form.SaleBillID);

        for (SaleBillPackageCardInfoOutputForm saleBillPackageCardInfoOutputForm : packageCardList) {
            saleBillPackageCardInfoOutputForm.PackageCardGoods = new SaleBillPackageCardGoodsInfoOutputForm();
            saleBillPackageCardInfoOutputForm.PackageCardLargessGoods = new SaleBillPackageCardGoodsInfoOutputForm();
            saleBillPackageCardInfoOutputForm.PackageCardGoods.PackageCardProduct = packageCardProductList.stream().filter(saleBillPackageCardProductOutputForm ->
                            saleBillPackageCardProductOutputForm.IsLargess == false && saleBillPackageCardProductOutputForm.SaleBillPackageCardID.intValue() == saleBillPackageCardInfoOutputForm.SaleBillPackageCardID.intValue())
                    .collect(Collectors.toList());
            saleBillPackageCardInfoOutputForm.PackageCardLargessGoods.PackageCardProduct = packageCardProductList.stream().filter(saleBillPackageCardProductOutputForm ->
                            saleBillPackageCardProductOutputForm.IsLargess == true && saleBillPackageCardProductOutputForm.SaleBillPackageCardID.intValue() == saleBillPackageCardInfoOutputForm.SaleBillPackageCardID.intValue())
                    .collect(Collectors.toList());
            saleBillPackageCardInfoOutputForm.PackageCardGoods.PackageCardProject = packageCardProjectList.stream().filter(saleBillPackageCardProjectOutputForm ->
                            saleBillPackageCardProjectOutputForm.IsLargess == false && saleBillPackageCardProjectOutputForm.SaleBillPackageCardID.intValue() == saleBillPackageCardInfoOutputForm.SaleBillPackageCardID.intValue())
                    .collect(Collectors.toList());
            saleBillPackageCardInfoOutputForm.PackageCardLargessGoods.PackageCardProject = packageCardProjectList.stream().filter(saleBillPackageCardProjectOutputForm ->
                            saleBillPackageCardProjectOutputForm.IsLargess == true && saleBillPackageCardProjectOutputForm.SaleBillPackageCardID.intValue() == saleBillPackageCardInfoOutputForm.SaleBillPackageCardID.intValue())
                    .collect(Collectors.toList());
            saleBillPackageCardInfoOutputForm.PackageCardGoods.PackageCardGeneralCard = packageCardGeneralCardList.stream().filter(saleBillPackageCardGeneralOutputForm ->
                            saleBillPackageCardGeneralOutputForm.IsLargess == false && saleBillPackageCardGeneralOutputForm.SaleBillPackageCardID.intValue() == saleBillPackageCardInfoOutputForm.SaleBillPackageCardID.intValue())
                    .collect(Collectors.toList());
            saleBillPackageCardInfoOutputForm.PackageCardLargessGoods.PackageCardGeneralCard = packageCardGeneralCardList.stream().filter(saleBillPackageCardGeneralOutputForm ->
                            saleBillPackageCardGeneralOutputForm.IsLargess == true && saleBillPackageCardGeneralOutputForm.SaleBillPackageCardID.intValue() == saleBillPackageCardInfoOutputForm.SaleBillPackageCardID.intValue())
                    .collect(Collectors.toList());
            saleBillPackageCardInfoOutputForm.PackageCardGoods.PackageCardTimeCard = packageCardTimeCardList.stream().filter(saleBillPackageCardTimeCardOutputForm ->
                            saleBillPackageCardTimeCardOutputForm.IsLargess == false && saleBillPackageCardTimeCardOutputForm.SaleBillPackageCardID.intValue() == saleBillPackageCardInfoOutputForm.SaleBillPackageCardID.intValue())
                    .collect(Collectors.toList());
            saleBillPackageCardInfoOutputForm.PackageCardLargessGoods.PackageCardTimeCard = packageCardTimeCardList.stream().filter(saleBillPackageCardTimeCardOutputForm ->
                            saleBillPackageCardTimeCardOutputForm.IsLargess == true && saleBillPackageCardTimeCardOutputForm.SaleBillPackageCardID.intValue() == saleBillPackageCardInfoOutputForm.SaleBillPackageCardID.intValue())
                    .collect(Collectors.toList());
            saleBillPackageCardInfoOutputForm.PackageCardGoods.PackageCardSavingCard = packageCardSavingCardList.stream().filter(saleBillPackageCardSavingCardOutputForm ->
                            saleBillPackageCardSavingCardOutputForm.IsLargess == false && saleBillPackageCardSavingCardOutputForm.SaleBillPackageCardID.intValue() == saleBillPackageCardInfoOutputForm.SaleBillPackageCardID.intValue())
                    .collect(Collectors.toList());
            saleBillPackageCardInfoOutputForm.PackageCardLargessGoods.PackageCardSavingCard = packageCardSavingCardList.stream().filter(saleBillPackageCardSavingCardOutputForm ->
                            saleBillPackageCardSavingCardOutputForm.IsLargess == true && saleBillPackageCardSavingCardOutputForm.SaleBillPackageCardID.intValue() == saleBillPackageCardInfoOutputForm.SaleBillPackageCardID.intValue())
                    .collect(Collectors.toList());
        }
        Print print = new Print();
        print.copies = receiptConfig.PrintQuantity;
        int documentID = 1;

        List<PrintDocument> printDocuments = new ArrayList<PrintDocument>();
        //门店名称
        printDocuments.add(PrintUtils.getTitleDocument(receiptConfig.PrintWidth, documentID++, bill.EntityName));

        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, "补尾款信息"));

        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "订单编号", form.SaleBillID));

        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "下单时间", bill.BillDate));

        if (bill.Name.equals("散客")) {
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "会员姓名", bill.Name));
        } else {
            String newName = "";
            if (bill.Name.length() > 1) {
                newName = bill.Name.substring(0, 1) + bill.Name.substring(1, bill.Name.length()).replaceAll(".{1}", "*");
            } else {
                newName = bill.Name;
            }
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "会员姓名", receiptConfig.NameEncrypt ? newName : bill.Name));
        }
        if (bill.PhoneNumber != null) {
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "会员手机号", receiptConfig.MobileEncrypt ? bill.PhoneNumber.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2") : bill.PhoneNumber));
        } else {
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "会员手机号", ""));

        }


        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "开单人", bill.EmployeeName));
        if (receiptConfig.EntityAddress) {
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "门店地址", bill.AddressDetail));
        }
        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, "补尾款明细"));


        if (bill.Project != null && bill.Project.size() > 0) {
            for (int i = 0; i < bill.Project.size(); i++) {
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, i + 1 + " " + bill.Project.get(i).ProjectName, ""));
                if (receiptConfig.SaleArrearBuyQuantity) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "数量", bill.Project.get(i).AccountQuantity.toString()));
                    if (receiptConfig.SaleArrearBuyAmount && bill.Project.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + bill.Project.get(i).AccountTotalAmount.toString()));
                    }
                } else {
                    if (receiptConfig.SaleArrearBuyAmount && bill.Project.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + bill.Project.get(i).AccountTotalAmount.toString()));
                    }
                }
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "补欠款金额", "￥ " + bill.Project.get(i).TotalAmount.toString()));
                if (receiptConfig.SaleArrearBalanceAmount) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "剩余欠款金额", "￥ " + bill.Project.get(i).BalanceArrearAmount.toString()));
                }
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + bill.Project.get(i).TotalAmount.toString()));
/*                List<SaleBillHandlerOutputForm> handlers=bill.Product.get(i).SaleBillHandler;
                if(receiptConfig.Handler) {
                if(handlers!=null && handlers.size()>0){
                    for (int j = 0; j < handlers.size(); j++) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).SaleHandlerName, ""));
                        List<SaleBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                        for (int k = 0; k < employee.size(); k++) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                        }
                    }
                }
                }*/

            }
        }
        if (bill.SavingCard != null && bill.SavingCard.size() > 0) {
            for (int i = 0; i < bill.SavingCard.size(); i++) {
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, bill.Project.size() + i + 1 + " " + bill.SavingCard.get(i).SavingCardName, ""));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "充值金额", "￥ " + bill.SavingCard.get(i).AccountTotalAmount.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "补欠款金额", "￥ " + bill.SavingCard.get(i).TotalAmount.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "剩余欠款金额", "￥ " + bill.SavingCard.get(i).BalanceArrearAmount.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + bill.SavingCard.get(i).TotalAmount.toString()));
/*                List<SaleBillHandlerOutputForm> handlers=bill.SavingCard.get(i).SaleBillHandler;
                if(receiptConfig.Handler) {
                if(handlers!=null && handlers.size()>0){
                    for (int j = 0; j < handlers.size(); j++) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).SaleHandlerName, ""));
                        List<SaleBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                        for (int k = 0; k < employee.size(); k++) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                        }
                    }
                }
                }*/
            }
        }
        if (bill.TimeCard != null && bill.TimeCard.size() > 0) {
            for (int i = 0; i < bill.TimeCard.size(); i++) {
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, bill.Project.size() + bill.SavingCard.size() + i + 1 + " " + bill.TimeCard.get(i).TimeCardName, ""));
                if (receiptConfig.SaleArrearBuyAmount && bill.TimeCard.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + bill.TimeCard.get(i).AccountTotalAmount.toString()));
                }

                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "补欠款金额", "￥ " + bill.TimeCard.get(i).TotalAmount.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "剩余欠款金额", "￥ " + bill.TimeCard.get(i).BalanceArrearAmount.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + bill.TimeCard.get(i).TotalAmount.toString()));
/*                List<SaleBillHandlerOutputForm> handlers=bill.TimeCard.get(i).SaleBillHandler;
                if(receiptConfig.Handler) {
                if(handlers!=null && handlers.size()>0){
                    for (int j = 0; j < handlers.size(); j++) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).SaleHandlerName, ""));
                        List<SaleBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                        for (int k = 0; k < employee.size(); k++) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                        }
                    }
                }
                }*/
            }
        }
        if (bill.GeneralCard != null && bill.GeneralCard.size() > 0) {
            for (int i = 0; i < bill.GeneralCard.size(); i++) {
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, bill.Project.size() + bill.SavingCard.size() + bill.TimeCard.size() + i + 1 + " " + bill.GeneralCard.get(i).GeneralCardName, ""));
                if (receiptConfig.SaleArrearBuyAmount && bill.GeneralCard.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + bill.GeneralCard.get(i).AccountTotalAmount.toString()));
                }

                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "补欠款金额", "￥ " + bill.GeneralCard.get(i).TotalAmount.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "剩余欠款金额", "￥ " + bill.GeneralCard.get(i).BalanceArrearAmount.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + bill.GeneralCard.get(i).TotalAmount.toString()));
/*                List<SaleBillHandlerOutputForm> handlers=bill.GeneralCard.get(i).SaleBillHandler;
                if(receiptConfig.Handler) {
                if(handlers!=null && handlers.size()>0){
                    for (int j = 0; j < handlers.size(); j++) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).SaleHandlerName, ""));
                        List<SaleBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                        for (int k = 0; k < employee.size(); k++) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                        }
                    }
                }
                }*/
            }
        }
        if (bill.Product != null && bill.Product.size() > 0) {
            for (int i = 0; i < bill.Product.size(); i++) {
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, bill.Project.size() + bill.SavingCard.size() + bill.TimeCard.size() + bill.GeneralCard.size() + i + 1 + " " + bill.Product.get(i).ProductName, ""));
                if (receiptConfig.SaleArrearBuyQuantity) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "数量", bill.Product.get(i).AccountQuantity.toString()));
                    if (receiptConfig.SaleArrearBuyAmount && bill.Product.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + bill.Product.get(i).AccountTotalAmount.toString()));
                    }
                } else {
                    if (receiptConfig.SaleArrearBuyAmount && bill.Product.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + bill.Product.get(i).AccountTotalAmount.toString()));
                    }
                }
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "补欠款金额", "￥ " + bill.Product.get(i).TotalAmount.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "剩余欠款金额", "￥ " + bill.Product.get(i).BalanceArrearAmount.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + bill.Product.get(i).TotalAmount.toString()));
/*                List<SaleBillHandlerOutputForm> handlers=bill.Product.get(i).SaleBillHandler;
                if(receiptConfig.Handler) {
                if(handlers!=null && handlers.size()>0){
                    for (int j = 0; j < handlers.size(); j++) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).SaleHandlerName, ""));
                        List<SaleBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                        for (int k = 0; k < employee.size(); k++) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                        }
                    }
                }
                }*/
            }
        }
        int length = bill.Project.size() + bill.SavingCard.size() + bill.TimeCard.size() + bill.GeneralCard.size() + bill.Product.size();
        if (packageCardList != null && packageCardList.size() > 0) {
            for (SaleBillPackageCardInfoOutputForm packageCardInfoOutputForm : packageCardList) {
                if (packageCardInfoOutputForm.PackageCardGoods.PackageCardProject != null && packageCardInfoOutputForm.PackageCardGoods.PackageCardProject.size() > 0) {
                    for (int i = 0; i < packageCardInfoOutputForm.PackageCardGoods.PackageCardProject.size(); i++) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, length + i + 1 + " " + packageCardInfoOutputForm.PackageCardGoods.PackageCardProject.get(i).Name, ""));
                        if (receiptConfig.SaleArrearBuyQuantity) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "数量", packageCardInfoOutputForm.PackageCardGoods.PackageCardProject.get(i).AccountQuantity.toString()));
                            if (receiptConfig.SaleArrearBuyAmount && packageCardInfoOutputForm.PackageCardGoods.PackageCardProject.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + packageCardInfoOutputForm.PackageCardGoods.PackageCardProject.get(i).AccountTotalAmount.toString()));
                            }
                        } else {
                            if (receiptConfig.SaleArrearBuyAmount && packageCardInfoOutputForm.PackageCardGoods.PackageCardProject.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + packageCardInfoOutputForm.PackageCardGoods.PackageCardProject.get(i).AccountTotalAmount.toString()));
                            }
                        }
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "补欠款金额", "￥ " + packageCardInfoOutputForm.PackageCardGoods.PackageCardProject.get(i).TotalAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "剩余欠款金额", "￥ " + packageCardInfoOutputForm.PackageCardGoods.PackageCardProject.get(i).BalanceArrearAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + packageCardInfoOutputForm.PackageCardGoods.PackageCardProject.get(i).TotalAmount.toString()));
/*                        List<SaleBillHandlerOutputForm> handlers=packageCardList.get(i).SaleBillHandler;
                        if(receiptConfig.Handler) {
                        if(handlers!=null && handlers.size()>0){
                            for (int j = 0; j < handlers.size(); j++) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).SaleHandlerName, ""));
                                List<SaleBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                                for (int k = 0; k < employee.size(); k++) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                                }
                            }
                        }
                        }*/
                    }
                }
                if (packageCardInfoOutputForm.PackageCardGoods.PackageCardSavingCard != null && packageCardInfoOutputForm.PackageCardGoods.PackageCardSavingCard.size() > 0) {
                    for (int i = 0; i < packageCardInfoOutputForm.PackageCardGoods.PackageCardSavingCard.size(); i++) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, length + packageCardInfoOutputForm.PackageCardGoods.PackageCardProject.size() + i + 1 + " " + packageCardInfoOutputForm.PackageCardGoods.PackageCardSavingCard.get(i).Name, ""));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "充值金额", "￥ " + packageCardInfoOutputForm.PackageCardGoods.PackageCardSavingCard.get(i).AccountTotalAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "补欠款金额", "￥ " + packageCardInfoOutputForm.PackageCardGoods.PackageCardSavingCard.get(i).TotalAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "剩余欠款金额", "￥ " + packageCardInfoOutputForm.PackageCardGoods.PackageCardSavingCard.get(i).BalanceArrearAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + packageCardInfoOutputForm.PackageCardGoods.PackageCardSavingCard.get(i).TotalAmount.toString()));
/*                        List<SaleBillHandlerOutputForm> handlers=packageCardList.get(i).SaleBillHandler;
                        if(receiptConfig.Handler) {
                        if(handlers!=null && handlers.size()>0){
                            for (int j = 0; j < handlers.size(); j++) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).SaleHandlerName, ""));
                                List<SaleBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                                for (int k = 0; k < employee.size(); k++) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                                }
                            }
                        }
                        }*/
                    }
                }
                if (packageCardInfoOutputForm.PackageCardGoods.PackageCardTimeCard != null && packageCardInfoOutputForm.PackageCardGoods.PackageCardTimeCard.size() > 0) {
                    for (int i = 0; i < packageCardInfoOutputForm.PackageCardGoods.PackageCardTimeCard.size(); i++) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, length + packageCardInfoOutputForm.PackageCardGoods.PackageCardProject.size() + packageCardInfoOutputForm.PackageCardGoods.PackageCardSavingCard.size() + i + 1 + " " + packageCardInfoOutputForm.PackageCardGoods.PackageCardTimeCard.get(i).Name, ""));
                        if (receiptConfig.SaleArrearBuyAmount && packageCardInfoOutputForm.PackageCardGoods.PackageCardTimeCard.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + packageCardInfoOutputForm.PackageCardGoods.PackageCardTimeCard.get(i).AccountTotalAmount.toString()));
                        }

                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "补欠款金额", "￥ " + packageCardInfoOutputForm.PackageCardGoods.PackageCardTimeCard.get(i).TotalAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "剩余欠款金额", "￥ " + packageCardInfoOutputForm.PackageCardGoods.PackageCardTimeCard.get(i).BalanceArrearAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + packageCardInfoOutputForm.PackageCardGoods.PackageCardTimeCard.get(i).TotalAmount.toString()));
/*                        List<SaleBillHandlerOutputForm> handlers=packageCardList.get(i).SaleBillHandler;
                        if(receiptConfig.Handler) {
                        if(handlers!=null && handlers.size()>0){
                            for (int j = 0; j < handlers.size(); j++) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).SaleHandlerName, ""));
                                List<SaleBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                                for (int k = 0; k < employee.size(); k++) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                                }
                            }
                        }
                        }*/
                    }
                }
                if (packageCardInfoOutputForm.PackageCardGoods.PackageCardGeneralCard != null && packageCardInfoOutputForm.PackageCardGoods.PackageCardGeneralCard.size() > 0) {
                    for (int i = 0; i < packageCardInfoOutputForm.PackageCardGoods.PackageCardGeneralCard.size(); i++) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, length + packageCardInfoOutputForm.PackageCardGoods.PackageCardProject.size() + packageCardInfoOutputForm.PackageCardGoods.PackageCardSavingCard.size() + packageCardInfoOutputForm.PackageCardGoods.PackageCardTimeCard.size() + i + 1 + " " + packageCardInfoOutputForm.PackageCardGoods.PackageCardGeneralCard.get(i).Name, ""));
                        if (receiptConfig.SaleArrearBuyAmount && packageCardInfoOutputForm.PackageCardGoods.PackageCardGeneralCard.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + packageCardInfoOutputForm.PackageCardGoods.PackageCardGeneralCard.get(i).AccountTotalAmount.toString()));
                        }

                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "补欠款金额", "￥ " + packageCardInfoOutputForm.PackageCardGoods.PackageCardGeneralCard.get(i).TotalAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "剩余欠款金额", "￥ " + packageCardInfoOutputForm.PackageCardGoods.PackageCardGeneralCard.get(i).BalanceArrearAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + packageCardInfoOutputForm.PackageCardGoods.PackageCardGeneralCard.get(i).TotalAmount.toString()));
/*                        List<SaleBillHandlerOutputForm> handlers=packageCardList.get(i).SaleBillHandler;
                        if(receiptConfig.Handler) {
                        if(handlers!=null && handlers.size()>0){
                            for (int j = 0; j < handlers.size(); j++) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).SaleHandlerName, ""));
                                List<SaleBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                                for (int k = 0; k < employee.size(); k++) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                                }
                            }
                        }
                        }*/
                    }
                }
                if (packageCardInfoOutputForm.PackageCardGoods.PackageCardProduct != null && packageCardInfoOutputForm.PackageCardGoods.PackageCardProduct.size() > 0) {
                    for (int i = 0; i < packageCardInfoOutputForm.PackageCardGoods.PackageCardProduct.size(); i++) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, length + packageCardInfoOutputForm.PackageCardGoods.PackageCardProject.size() + packageCardInfoOutputForm.PackageCardGoods.PackageCardSavingCard.size() + packageCardInfoOutputForm.PackageCardGoods.PackageCardTimeCard.size() + packageCardInfoOutputForm.PackageCardGoods.PackageCardGeneralCard.size() + i + 1 + " " + packageCardInfoOutputForm.PackageCardGoods.PackageCardProduct.get(i).Name, ""));
                        if (receiptConfig.SaleArrearBuyQuantity) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "数量", packageCardInfoOutputForm.PackageCardGoods.PackageCardProduct.get(i).AccountQuantity.toString()));
                            if (receiptConfig.SaleArrearBuyAmount && packageCardInfoOutputForm.PackageCardGoods.PackageCardProduct.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + packageCardInfoOutputForm.PackageCardGoods.PackageCardProduct.get(i).AccountTotalAmount.toString()));
                            }
                        } else {
                            if (receiptConfig.SaleArrearBuyAmount && packageCardInfoOutputForm.PackageCardGoods.PackageCardProduct.get(i).AccountTotalAmount.compareTo(BigDecimal.ZERO) != 0) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "购买金额", "￥ " + packageCardInfoOutputForm.PackageCardGoods.PackageCardProduct.get(i).AccountTotalAmount.toString()));
                            }
                        }
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "补欠款金额", "￥ " + packageCardInfoOutputForm.PackageCardGoods.PackageCardProduct.get(i).TotalAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "剩余欠款金额", "￥ " + packageCardInfoOutputForm.PackageCardGoods.PackageCardProduct.get(i).BalanceArrearAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + packageCardInfoOutputForm.PackageCardGoods.PackageCardProduct.get(i).TotalAmount.toString()));
 /*                       List<SaleBillHandlerOutputForm> handlers=packageCardList.get(i).SaleBillHandler;
                        if(receiptConfig.Handler) {
                        if(handlers!=null && handlers.size()>0){
                            for (int j = 0; j < handlers.size(); j++) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).SaleHandlerName, ""));
                                List<SaleBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                                for (int k = 0; k < employee.size(); k++) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                                }
                            }
                        }
                        }*/
                    }
                }
            }

        }
        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "合计", "￥ " + bill.Amount.toString()));
        if (bill.SaleBillPay != null && bill.SaleBillPay.size() > 0) {
            for (int i = 0; i < bill.SaleBillPay.size(); i++) {
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "付款:" + bill.SaleBillPay.get(i).Name, "￥ " + bill.SaleBillPay.get(i).Amount.toString()));
            }
        }
        if (bill.SaleBillPaySavingCardDeduction != null && bill.SaleBillPaySavingCardDeduction.size() > 0) {
            for (int i = 0; i < bill.SaleBillPaySavingCardDeduction.size(); i++) {
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡抵扣:" + bill.SaleBillPaySavingCardDeduction.get(i).Name, "￥ " + bill.SaleBillPaySavingCardDeduction.get(i).TotalAmount.toString()));
            }
        }
        if (bill.ArrearAmount.compareTo(BigDecimal.ZERO) == 1) {
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "欠款", "￥ " + bill.ArrearAmount.toString()));

        }

        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, receiptConfig.WriteTextFirst));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, receiptConfig.WriteTextSecond));

        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, ""));


        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "客户签字", ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, ""));
        print.printDocuments = printDocuments;
        return BaseOutputForm.success(print);
    }
}
