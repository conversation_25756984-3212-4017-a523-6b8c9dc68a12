package com.zhelian.service.ibeauty.appointment;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.AppointmentProjectMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.form.input.AppointmentProjectAddForm;
import com.zhelian.model.ibeauty.form.input.AppointmentProjectQueryForm;
import com.zhelian.model.ibeauty.form.output.AppointmentProjectOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class AppointmentProjectService {

    @Autowired
    AppointmentProjectMapper appointmentProjectMapper;

    public BaseOutput all(AppointmentProjectQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<AppointmentProjectOutputForm> list = appointmentProjectMapper.all(form);
        return BasePageInfo.success(list);
    }

    @Transactional
    public BaseOutput config(AppointmentProjectAddForm form) {
        appointmentProjectMapper.delete();

        appointmentProjectMapper.add(form.ProjectIDList);
        return BaseOutput.success();
    }

    public BaseOutput findCategoryAndProject() {
        return BaseOutputForm.success(ListUtil.listToTree(JSONArray.parseArray(JSON.toJSONString(appointmentProjectMapper.findCategoryAndProject(), JSONWriter.Feature.WriteMapNullValue)), "ID", "ParentID", "Child"));
    }
}
