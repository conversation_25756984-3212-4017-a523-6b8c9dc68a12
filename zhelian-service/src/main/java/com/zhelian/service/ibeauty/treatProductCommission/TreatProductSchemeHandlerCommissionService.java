package com.zhelian.service.ibeauty.treatProductCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.TreatProductSchemeHandlerCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.TreatProductSchemeHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.TreatProductSchemeHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductSchemeHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatProductSchemeHandlersCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.TreatProductHandlerCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatProductSchemeHandlerCommissionService {

    @Autowired
    TreatProductSchemeHandlerCommissionMapper treatProductSchemeHandlerCommissionMapper;

    public BaseOutput allTreatProductSchemeHandlerCommission(TreatProductSchemeHandlerCommissionQueryForm form) {
        List<TreatProductHandlerCommissionOutputForm> list = treatProductSchemeHandlerCommissionMapper.allTreatProductSchemeHandlerCommission(form.EntityID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatProductSchemeHandlerCommission(TreatProductSchemeHandlersCommissionAddForm form) {

        treatProductSchemeHandlerCommissionMapper.deleteTreatProductSchemeHandler(form.EntityID);
        treatProductSchemeHandlerCommissionMapper.deleteTreatProductSchemeHandlerJobType(form.EntityID);

        if (form.Handler != null && form.Handler.size() > 0) {

            List listTreatProductSchemeHandlerCommissionEntity = new ArrayList<>();

            List listTreatProductSchemeHandlerJobTypeCommissionEntity = new ArrayList<>();

            for (TreatProductHandlerCommissionAddForm treatProductSchemeHandlerAddForm : form.Handler) {
                if (treatProductSchemeHandlerAddForm.PayRate != null || treatProductSchemeHandlerAddForm.PayFixed != null ||
                        treatProductSchemeHandlerAddForm.CardRate != null || treatProductSchemeHandlerAddForm.CardFixed != null ||
                        treatProductSchemeHandlerAddForm.CardLargessRate != null || treatProductSchemeHandlerAddForm.CardLargessFixed != null ||
                        treatProductSchemeHandlerAddForm.LargessRate != null || treatProductSchemeHandlerAddForm.LargessFixed != null ||
                        treatProductSchemeHandlerAddForm.SpecialBenefit != null) {
                    TreatProductSchemeHandlerCommissionEntity treatProductSchemeHandlerCommissionEntity = new TreatProductSchemeHandlerCommissionEntity();
                    treatProductSchemeHandlerCommissionEntity.EntityID = form.EntityID;
                    treatProductSchemeHandlerCommissionEntity.ProductTreatHandlerID = treatProductSchemeHandlerAddForm.TreatHandlerID;
                    treatProductSchemeHandlerCommissionEntity.PayRate = treatProductSchemeHandlerAddForm.PayRate;
                    treatProductSchemeHandlerCommissionEntity.PayFixed = treatProductSchemeHandlerAddForm.PayFixed;
                    treatProductSchemeHandlerCommissionEntity.CardRate = treatProductSchemeHandlerAddForm.CardRate;
                    treatProductSchemeHandlerCommissionEntity.CardFixed = treatProductSchemeHandlerAddForm.CardFixed;
                    treatProductSchemeHandlerCommissionEntity.CardLargessRate = treatProductSchemeHandlerAddForm.CardLargessRate;
                    treatProductSchemeHandlerCommissionEntity.CardLargessFixed = treatProductSchemeHandlerAddForm.CardLargessFixed;
                    treatProductSchemeHandlerCommissionEntity.LargessFixed = treatProductSchemeHandlerAddForm.LargessFixed;
                    treatProductSchemeHandlerCommissionEntity.LargessRate = treatProductSchemeHandlerAddForm.LargessRate;
                    treatProductSchemeHandlerCommissionEntity.SpecialBenefit = treatProductSchemeHandlerAddForm.SpecialBenefit;
                    listTreatProductSchemeHandlerCommissionEntity.add(treatProductSchemeHandlerCommissionEntity);
                }

                if (treatProductSchemeHandlerAddForm.HandlerJobTypeCommission != null && treatProductSchemeHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (TreatProductCategoryHandlerJobTypeCommissionAddForm treatProductSchemeHandlerJobTypeCommissionAddForm : treatProductSchemeHandlerAddForm.HandlerJobTypeCommission) {
                        if (treatProductSchemeHandlerJobTypeCommissionAddForm.PayRate != null || treatProductSchemeHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                treatProductSchemeHandlerJobTypeCommissionAddForm.CardRate != null || treatProductSchemeHandlerJobTypeCommissionAddForm.CardFixed != null ||
                                treatProductSchemeHandlerJobTypeCommissionAddForm.CardLargessRate != null || treatProductSchemeHandlerJobTypeCommissionAddForm.CardLargessFixed != null ||
                                treatProductSchemeHandlerJobTypeCommissionAddForm.LargessRate != null || treatProductSchemeHandlerJobTypeCommissionAddForm.LargessFixed != null ||
                                treatProductSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            TreatProductSchemeHandlerJobTypeCommissionEntity treatProductSchemeHandlerJobTypeCommissionEntity = new TreatProductSchemeHandlerJobTypeCommissionEntity();
                            treatProductSchemeHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            treatProductSchemeHandlerJobTypeCommissionEntity.ProductTreatHandlerID = treatProductSchemeHandlerAddForm.TreatHandlerID;
                            treatProductSchemeHandlerJobTypeCommissionEntity.JobTypeID = treatProductSchemeHandlerJobTypeCommissionAddForm.JobTypeID;
                            treatProductSchemeHandlerJobTypeCommissionEntity.PayRate = treatProductSchemeHandlerJobTypeCommissionAddForm.PayRate;
                            treatProductSchemeHandlerJobTypeCommissionEntity.PayFixed = treatProductSchemeHandlerJobTypeCommissionAddForm.PayFixed;
                            treatProductSchemeHandlerJobTypeCommissionEntity.CardRate = treatProductSchemeHandlerJobTypeCommissionAddForm.CardRate;
                            treatProductSchemeHandlerJobTypeCommissionEntity.CardFixed = treatProductSchemeHandlerJobTypeCommissionAddForm.CardFixed;
                            treatProductSchemeHandlerJobTypeCommissionEntity.CardLargessRate = treatProductSchemeHandlerJobTypeCommissionAddForm.CardLargessRate;
                            treatProductSchemeHandlerJobTypeCommissionEntity.CardLargessFixed = treatProductSchemeHandlerJobTypeCommissionAddForm.CardLargessFixed;
                            treatProductSchemeHandlerJobTypeCommissionEntity.LargessFixed = treatProductSchemeHandlerJobTypeCommissionAddForm.LargessFixed;
                            treatProductSchemeHandlerJobTypeCommissionEntity.LargessRate = treatProductSchemeHandlerJobTypeCommissionAddForm.LargessRate;
                            treatProductSchemeHandlerJobTypeCommissionEntity.SpecialBenefit = treatProductSchemeHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            listTreatProductSchemeHandlerJobTypeCommissionEntity.add(treatProductSchemeHandlerJobTypeCommissionEntity);
                        }
                    }

                }
            }
            if (listTreatProductSchemeHandlerCommissionEntity != null && listTreatProductSchemeHandlerCommissionEntity.size() > 0) {
                List<List<TreatProductSchemeHandlerCommissionEntity>> subs = Lists.partition(listTreatProductSchemeHandlerCommissionEntity, 20);
                for (List<TreatProductSchemeHandlerCommissionEntity> entityList : subs) {
                    treatProductSchemeHandlerCommissionMapper.updateTreatProductSchemeHandlerCommission(entityList);
                }
            }
            if (listTreatProductSchemeHandlerJobTypeCommissionEntity != null && listTreatProductSchemeHandlerJobTypeCommissionEntity.size() > 0) {
                List<List<TreatProductSchemeHandlerJobTypeCommissionEntity>> subs = Lists.partition(listTreatProductSchemeHandlerJobTypeCommissionEntity, 20);
                for (List<TreatProductSchemeHandlerJobTypeCommissionEntity> entityList : subs) {
                    treatProductSchemeHandlerCommissionMapper.insertTreatProductSchemeHandlerJobTypeCommission(entityList);
                }
            }
        }
        return BaseOutput.success();
    }
}