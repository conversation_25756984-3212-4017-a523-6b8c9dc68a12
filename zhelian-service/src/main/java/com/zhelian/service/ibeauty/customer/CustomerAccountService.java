package com.zhelian.service.ibeauty.customer;

import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.customer.SystemCustomerUtil;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.CRM.CustomerMapper;
import com.zhelian.mapper.ibeauty.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.*;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.ibeauty.form.output.*;
import com.zhelian.model.micromall.form.input.CardEntityForm;
import com.zhelian.model.micromall.form.input.CardProjectForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

@Service
public class CustomerAccountService {

    @Autowired
    private CustomerAccountMapper customerAccountMapper;
    @Autowired
    private ProjectAccountMapper projectAccountMapper;
    @Autowired
    private ProductAccountMapper productAccountMapper;
    @Autowired
    private SavingCardAccountMapper savingCardAccountMapper;
    @Autowired
    private GeneralCardAccountMapper generalCardAccountMapper;
    @Autowired
    private TimeCardAccountMapper timeCardAccountMapper;
    @Autowired
    private PackageCardAccountMapper packageCardAccountMapper;
    @Autowired
    private CustomerMapper customerMapper;


    public BaseOutput productAccount(CustomerAccountQueryForm form) {
        CustomerProductAccountOverviewOutputForm customerProductAccountOverviewOutputForm = new CustomerProductAccountOverviewOutputForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        customerProductAccountOverviewOutputForm.product = BasePageInfo.success(productAccountMapper.customerProductAccount(form.CustomerID, form.Active, form.Name));
        customerProductAccountOverviewOutputForm.customerProductAccountSumOutputForm = productAccountMapper.customerProductAccountSum(form.CustomerID, form.Active, form.Name);
        return BaseOutputForm.success(customerProductAccountOverviewOutputForm);
    }

    public BaseOutput miniprogramProductAccount(CustomerAccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(productAccountMapper.customerMiniprogramProductAccount(form.CustomerID, form.Active, form.Name));
    }

    public BaseOutput projectAccount(CustomerAccountQueryForm form) {
        CustomerProjectAccountOverviewOutputForm customerProjectAccountOverviewOutputForm = new CustomerProjectAccountOverviewOutputForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        customerProjectAccountOverviewOutputForm.project = BasePageInfo.success(projectAccountMapper.customerProjectAccount(form.CustomerID, form.Active, form.Name));
        customerProjectAccountOverviewOutputForm.customerProjectAccountSumOutputForm = projectAccountMapper.customerProjectAccountSum(form.CustomerID, form.Active, form.Name);
        return BaseOutputForm.success(customerProjectAccountOverviewOutputForm);
    }

    public BaseOutput miniprogramProjectAccount(CustomerAccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(projectAccountMapper.customerMiniprogramProjectAccount(form.CustomerID, form.Active, form.Name));
    }

    public BaseOutput generalCardAccount(CustomerAccountQueryForm form) {
        CustomerGeneralCardAccountOverviewOutputForm customerGeneralCardAccountOverviewOutputForm = new CustomerGeneralCardAccountOverviewOutputForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        customerGeneralCardAccountOverviewOutputForm.generalCard = BasePageInfo.success(generalCardAccountMapper.customerGeneralCardAccount(form.CustomerID, form.Active, form.Name));
        customerGeneralCardAccountOverviewOutputForm.customerGeneralCardAccountSumOutputForm = generalCardAccountMapper.customerGeneralCardAccountSum(form.CustomerID, form.Active, form.Name);
        return BaseOutputForm.success(customerGeneralCardAccountOverviewOutputForm);

    }

    public BaseOutput miniprogramGeneralCardAccount(CustomerAccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(generalCardAccountMapper.customerMiniprogramGeneralCardAccount(form.CustomerID, form.Active, form.Name));
    }

    public BaseOutput timeCardAccount(CustomerAccountQueryForm form) {
        CustomerTimeCardAccountOverviewOutputForm customerTimeCardAccountOverviewOutputForm = new CustomerTimeCardAccountOverviewOutputForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        customerTimeCardAccountOverviewOutputForm.timeCard = BasePageInfo.success(timeCardAccountMapper.customerTimeCardAccount(form.CustomerID, form.Active, form.Name));
        customerTimeCardAccountOverviewOutputForm.customerTimeCardAccountSumOutputForm = timeCardAccountMapper.customerTimeCardAccountSum(form.CustomerID, form.Active, form.Name);
        return BaseOutputForm.success(customerTimeCardAccountOverviewOutputForm);

    }

    public BaseOutput miniprogramTimeCardAccount(CustomerAccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(timeCardAccountMapper.customerMiniprogramTimeCardAccount(form.CustomerID, form.Active, form.Name));
    }

    public BaseOutput savingCardAccount(CustomerAccountQueryForm form) {
        CustomerSavingCardAccountOverviewOutputForm customerSavingCardAccountOverviewOutputForm = new CustomerSavingCardAccountOverviewOutputForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        customerSavingCardAccountOverviewOutputForm.savingCard = BasePageInfo.success(savingCardAccountMapper.customerSavingCardAccount(form.CustomerID, form.Active, form.Name));
        customerSavingCardAccountOverviewOutputForm.customerSavingCardAccountSumOutputForm = savingCardAccountMapper.customerSavingCardAccountSum(form.CustomerID, form.Active, form.Name);
        return BaseOutputForm.success(customerSavingCardAccountOverviewOutputForm);
    }

    public BaseOutput miniprogramSavingCardAccount(CustomerAccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(savingCardAccountMapper.customerMiniprogramSavingCardAccount(form.CustomerID, form.Active, form.Name));
    }

    public BaseOutput packageCardAccount(CustomerAccountQueryForm form) {
        CustomerPackageCardAccountOverviewOutputForm customerPackageCardAccountOverviewOutputForm = new CustomerPackageCardAccountOverviewOutputForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        customerPackageCardAccountOverviewOutputForm.packageCard = BasePageInfo.success(packageCardAccountMapper.customerPackageCardAccount(form.CustomerID, form.Active, form.Name, null));
        customerPackageCardAccountOverviewOutputForm.customerPackageCardAccountSumOutputForm = packageCardAccountMapper.customerPackageCardAccountSum(form.CustomerID, form.Active, form.Name, null);
        return BaseOutputForm.success(customerPackageCardAccountOverviewOutputForm);

    }

    @Transactional
    public BaseOutput createProjectAccountArrearAmountProcess(CustomerArrearAmountProcessAddForm form) {
        ProjectAccountEntity projectAccountEntity = projectAccountMapper.getProjectAccountByAccountID(form.ProjectAccountID);
        if (form.Amount.compareTo(projectAccountEntity.ArrearAmount) > 0)
            return BaseOutput.failed("平账金额大于该账户欠款金额，请重新填写金额");

        if (form.Quantity > projectAccountEntity.Balance)
            return BaseOutput.failed("平账数量大于该账户剩余账户，请重新填写数量");

        ProjectAccountArrearAmountProcessEntity projectAccountArrearAmountProcessEntity = new ProjectAccountArrearAmountProcessEntity();
        projectAccountArrearAmountProcessEntity.ProjectAccountID = form.ProjectAccountID;
        projectAccountArrearAmountProcessEntity.EntityID = SystemUserUtil.getSystemEntityID();
        projectAccountArrearAmountProcessEntity.Amount = form.Amount;
        projectAccountArrearAmountProcessEntity.Quantity = form.Quantity;
        projectAccountArrearAmountProcessEntity.BeforeArrearAmount = projectAccountEntity.ArrearAmount;
        projectAccountArrearAmountProcessEntity.AfterArrearAmount = projectAccountEntity.ArrearAmount.subtract(form.Amount);
        projectAccountArrearAmountProcessEntity.BeforeQuantity = projectAccountEntity.Balance;
        projectAccountArrearAmountProcessEntity.AfterQuantity = projectAccountEntity.Balance - form.Quantity;
        projectAccountArrearAmountProcessEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        customerAccountMapper.createProjectAccountArrearAmountProcess(projectAccountArrearAmountProcessEntity);

        projectAccountEntity.ArrearAmount = projectAccountArrearAmountProcessEntity.AfterArrearAmount;
        projectAccountEntity.Balance = projectAccountArrearAmountProcessEntity.AfterQuantity;
        if (projectAccountArrearAmountProcessEntity.AfterQuantity == 0) {
            projectAccountEntity.CardPerformanceBalanceAmount = BigDecimal.ZERO;
            projectAccountEntity.CardLargessPerformanceBalanceAmount = BigDecimal.ZERO;
            projectAccountEntity.PayPerformanceBalanceAmount = BigDecimal.ZERO;
        }
        if (projectAccountArrearAmountProcessEntity.AfterQuantity > 0) {
            projectAccountEntity.CardPerformanceBalanceAmount = projectAccountEntity.IsLargess == true ? BigDecimal.ZERO : projectAccountEntity.SavingCardDeductionAmount.multiply(new BigDecimal(form.Quantity)).divide(new BigDecimal(projectAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
            projectAccountEntity.CardLargessPerformanceBalanceAmount = projectAccountEntity.IsLargess == true ? BigDecimal.ZERO : projectAccountEntity.SavingCardDeductionLargessAmount.multiply(new BigDecimal(form.Quantity)).divide(new BigDecimal(projectAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
            projectAccountEntity.PayPerformanceBalanceAmount = projectAccountEntity.IsLargess == true ? BigDecimal.ZERO : projectAccountEntity.PayPerformanceBalanceAmount.multiply(new BigDecimal(form.Quantity)).divide(new BigDecimal(projectAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
        }
        projectAccountMapper.updateCustomerProjectAccount(projectAccountEntity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput createProductAccountArrearAmountProcess(CustomerProductArrearAmountProcessAddForm form) {
        ProductAccountEntity productAccountEntity = productAccountMapper.getProductAccountByAccountID(form.ProductAccountID);
        if (form.Amount.compareTo(productAccountEntity.ArrearAmount) > 0)
            return BaseOutput.failed("平账金额大于该账户欠款金额，请重新填写金额");

        if (form.Quantity > productAccountEntity.Balance)
            return BaseOutput.failed("平账数量大于该账户剩余账户，请重新填写数量");

        ProductAccountArrearAmountProcessEntity productAccountArrearAmountProcessEntity = new ProductAccountArrearAmountProcessEntity();
        productAccountArrearAmountProcessEntity.ProductAccountID = form.ProductAccountID;
        productAccountArrearAmountProcessEntity.EntityID = SystemUserUtil.getSystemEntityID();
        productAccountArrearAmountProcessEntity.Amount = form.Amount;
        productAccountArrearAmountProcessEntity.Quantity = form.Quantity;
        productAccountArrearAmountProcessEntity.BeforeArrearAmount = productAccountEntity.ArrearAmount;
        productAccountArrearAmountProcessEntity.AfterArrearAmount = productAccountEntity.ArrearAmount.subtract(form.Amount);
        productAccountArrearAmountProcessEntity.BeforeQuantity = productAccountEntity.Balance;
        productAccountArrearAmountProcessEntity.AfterQuantity = productAccountEntity.Balance - form.Quantity;
        productAccountArrearAmountProcessEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        customerAccountMapper.createProductAccountArrearAmountProcess(productAccountArrearAmountProcessEntity);

        productAccountEntity.ArrearAmount = productAccountArrearAmountProcessEntity.AfterArrearAmount;
        productAccountEntity.Balance = productAccountArrearAmountProcessEntity.AfterQuantity;
        if (productAccountArrearAmountProcessEntity.AfterQuantity == 0) {
            productAccountEntity.CardPerformanceBalanceAmount = BigDecimal.ZERO;
            productAccountEntity.CardLargessPerformanceBalanceAmount = BigDecimal.ZERO;
            productAccountEntity.PayPerformanceBalanceAmount = BigDecimal.ZERO;
        }
        if (productAccountArrearAmountProcessEntity.AfterQuantity > 0) {
            productAccountEntity.CardPerformanceBalanceAmount = productAccountEntity.IsLargess == true ? BigDecimal.ZERO : productAccountEntity.SavingCardDeductionAmount.multiply(new BigDecimal(form.Quantity)).divide(new BigDecimal(productAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
            productAccountEntity.CardLargessPerformanceBalanceAmount = productAccountEntity.IsLargess == true ? BigDecimal.ZERO : productAccountEntity.SavingCardDeductionLargessAmount.multiply(new BigDecimal(form.Quantity)).divide(new BigDecimal(productAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
            productAccountEntity.PayPerformanceBalanceAmount = productAccountEntity.IsLargess == true ? BigDecimal.ZERO : productAccountEntity.PayPerformanceBalanceAmount.multiply(new BigDecimal(form.Quantity)).divide(new BigDecimal(productAccountEntity.Quantity), 2, BigDecimal.ROUND_HALF_UP);
        }
        productAccountMapper.updateCustomerProductAccount(productAccountEntity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput createGeneralCardAccountArrearAmountProcess(CustomerGeneralCardArrearAmountProcessAddForm form) {
        GeneralCardAccountEntity generalCardAccountEntity = generalCardAccountMapper.getGeneralCardAccountByAccountID(form.GeneralCardAccountID);
        if (form.Amount.compareTo(generalCardAccountEntity.ArrearAmount) > 0)
            return BaseOutput.failed("平账金额大于该账户欠款金额，请重新填写金额");

        if (form.Quantity > generalCardAccountEntity.Balance)
            return BaseOutput.failed("平账数量大于该账户剩余账户，请重新填写数量");

        GeneralCardAccountArrearAmountProcessEntity generalCardAccountArrearAmountProcessEntity = new GeneralCardAccountArrearAmountProcessEntity();
        generalCardAccountArrearAmountProcessEntity.GeneralCardAccountID = form.GeneralCardAccountID;
        generalCardAccountArrearAmountProcessEntity.EntityID = SystemUserUtil.getSystemEntityID();
        generalCardAccountArrearAmountProcessEntity.Amount = form.Amount;
        generalCardAccountArrearAmountProcessEntity.Quantity = form.Quantity;
        generalCardAccountArrearAmountProcessEntity.BeforeArrearAmount = generalCardAccountEntity.ArrearAmount;
        generalCardAccountArrearAmountProcessEntity.AfterArrearAmount = generalCardAccountEntity.ArrearAmount.subtract(form.Amount);
        generalCardAccountArrearAmountProcessEntity.BeforeQuantity = generalCardAccountEntity.Balance;
        generalCardAccountArrearAmountProcessEntity.AfterQuantity = generalCardAccountEntity.Balance - form.Quantity;
        generalCardAccountArrearAmountProcessEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        customerAccountMapper.createGeneralCardAccountArrearAmountProcess(generalCardAccountArrearAmountProcessEntity);

        generalCardAccountEntity.ArrearAmount = generalCardAccountArrearAmountProcessEntity.AfterArrearAmount;
        generalCardAccountEntity.Balance = generalCardAccountArrearAmountProcessEntity.AfterQuantity;
        if (generalCardAccountArrearAmountProcessEntity.AfterQuantity == 0) {
            generalCardAccountEntity.CardPerformanceBalanceAmount = BigDecimal.ZERO;
            generalCardAccountEntity.CardLargessPerformanceBalanceAmount = BigDecimal.ZERO;
            generalCardAccountEntity.PayPerformanceBalanceAmount = BigDecimal.ZERO;
        }
        if (generalCardAccountArrearAmountProcessEntity.AfterQuantity > 0) {
            generalCardAccountEntity.CardPerformanceBalanceAmount = generalCardAccountEntity.IsLargess == true ? BigDecimal.ZERO : generalCardAccountEntity.SavingCardDeductionAmount.multiply(new BigDecimal(form.Quantity)).divide(new BigDecimal(generalCardAccountEntity.CardTimes), 2, BigDecimal.ROUND_HALF_UP);
            generalCardAccountEntity.CardLargessPerformanceBalanceAmount = generalCardAccountEntity.IsLargess == true ? BigDecimal.ZERO : generalCardAccountEntity.SavingCardDeductionLargessAmount.multiply(new BigDecimal(form.Quantity)).divide(new BigDecimal(generalCardAccountEntity.CardTimes), 2, BigDecimal.ROUND_HALF_UP);
            generalCardAccountEntity.PayPerformanceBalanceAmount = generalCardAccountEntity.IsLargess == true ? BigDecimal.ZERO : generalCardAccountEntity.PayPerformanceBalanceAmount.multiply(new BigDecimal(form.Quantity)).divide(new BigDecimal(generalCardAccountEntity.CardTimes), 2, BigDecimal.ROUND_HALF_UP);
        }
        generalCardAccountMapper.updateCustomerGeneralCardAccount(generalCardAccountEntity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput createTimeCardAccountArrearAmountProcess(CustomerTimeCardArrearAmountProcessAddForm form) {
        TimeCardAccountEntity timeCardAccountEntity = timeCardAccountMapper.getTimeCardAccountByAccountID(form.TimeCardAccountID);
        if (form.Amount.compareTo(timeCardAccountEntity.ArrearAmount) > 0)
            return BaseOutput.failed("平账金额大于该账户欠款金额，请重新填写金额");


        TimeCardAccountArrearAmountProcessEntity timeCardAccountArrearAmountProcessEntity = new TimeCardAccountArrearAmountProcessEntity();
        timeCardAccountArrearAmountProcessEntity.TimeCardAccountID = form.TimeCardAccountID;
        timeCardAccountArrearAmountProcessEntity.EntityID = SystemUserUtil.getSystemEntityID();
        timeCardAccountArrearAmountProcessEntity.Amount = form.Amount;
        timeCardAccountArrearAmountProcessEntity.BeforeArrearAmount = timeCardAccountEntity.ArrearAmount;
        timeCardAccountArrearAmountProcessEntity.AfterArrearAmount = timeCardAccountEntity.ArrearAmount.subtract(form.Amount);
        timeCardAccountArrearAmountProcessEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        customerAccountMapper.createTimeCardAccountArrearAmountProcess(timeCardAccountArrearAmountProcessEntity);

        timeCardAccountEntity.ArrearAmount = timeCardAccountArrearAmountProcessEntity.AfterArrearAmount;
        timeCardAccountMapper.updateArrearTimeCardAccount(timeCardAccountEntity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput createSavingCardAccountArrearAmountProcess(CustomerSavingCardArrearAmountProcessAddForm form) {
        SavingCardAccountEntity savingCardAccountEntity = savingCardAccountMapper.getSavingCardAccountByID(form.SavingCardAccountID);
        if (form.Amount.compareTo(savingCardAccountEntity.ArrearAmount) > 0)
            return BaseOutput.failed("平账金额大于该账户欠款金额，请重新填写金额");

        SavingCardAccountArrearAmountProcessEntity savingCardAccountArrearAmountProcessEntity = new SavingCardAccountArrearAmountProcessEntity();
        savingCardAccountArrearAmountProcessEntity.SavingCardAccountID = form.SavingCardAccountID;
        savingCardAccountArrearAmountProcessEntity.EntityID = SystemUserUtil.getSystemEntityID();
        savingCardAccountArrearAmountProcessEntity.Amount = form.Amount;
        savingCardAccountArrearAmountProcessEntity.BeforeArrearAmount = savingCardAccountEntity.ArrearAmount;
        savingCardAccountArrearAmountProcessEntity.AfterArrearAmount = savingCardAccountEntity.ArrearAmount.subtract(form.Amount);
        savingCardAccountArrearAmountProcessEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        customerAccountMapper.createSavingCardAccountArrearAmountProcess(savingCardAccountArrearAmountProcessEntity);

        savingCardAccountEntity.ArrearAmount = savingCardAccountArrearAmountProcessEntity.AfterArrearAmount;
        savingCardAccountMapper.updateArrearSavingCardAccount(savingCardAccountEntity);
        return BaseOutput.success();
    }

    public BaseOutput packageCardAccountDetails(CustomerPackageCardAccountQueryForm form) {
        CustomerPackageCardAccountOutputForm customerPackageCardAccountOutputForm = packageCardAccountMapper.customerPackageCardAccount(null, null, null, form.PackageCardAccountID).get(0);
        String ValidDayName = customerPackageCardAccountOutputForm.ValidDayName;
        String ChannelName = customerPackageCardAccountOutputForm.ChannelName;
        String EntityName = customerPackageCardAccountOutputForm.EntityName;

        CustomerPackageCardAccountDetailsOutputForm packageCardAccountDetailsOutputForm = new CustomerPackageCardAccountDetailsOutputForm();
        packageCardAccountDetailsOutputForm.Product = packageCardAccountMapper.custPackageCardProductAccount(form.PackageCardAccountID);
        if (packageCardAccountDetailsOutputForm.Product != null)
            packageCardAccountDetailsOutputForm.Product.stream().forEach(i -> {
                i.ValidDayName = ValidDayName;
                i.ChannelName = ChannelName;
                i.EntityName = EntityName;
            });
        packageCardAccountDetailsOutputForm.Project = packageCardAccountMapper.custPackageCardProjectAccount(form.PackageCardAccountID);
        if (packageCardAccountDetailsOutputForm.Project != null)
            packageCardAccountDetailsOutputForm.Project.stream().forEach(i -> {
                i.ValidDayName = ValidDayName;
                i.ChannelName = ChannelName;
                i.EntityName = EntityName;
            });
        packageCardAccountDetailsOutputForm.GeneralCard = packageCardAccountMapper.custPackageCardGeneralCardAccount(form.PackageCardAccountID);
        if (packageCardAccountDetailsOutputForm.GeneralCard != null)
            packageCardAccountDetailsOutputForm.GeneralCard.stream().forEach(i -> {
                i.ValidDayName = ValidDayName;
                i.ChannelName = ChannelName;
                i.EntityName = EntityName;
            });
        packageCardAccountDetailsOutputForm.TimeCard = packageCardAccountMapper.custPackageCardTimeCardAccount(form.PackageCardAccountID);
        if (packageCardAccountDetailsOutputForm.TimeCard != null)
            packageCardAccountDetailsOutputForm.TimeCard.stream().forEach(i -> {
                i.ValidDayName = ValidDayName;
                i.ChannelName = ChannelName;
                i.EntityName = EntityName;
            });
        packageCardAccountDetailsOutputForm.SavingCard = packageCardAccountMapper.custPackageCardSavingCardAccount(form.PackageCardAccountID);
        if (packageCardAccountDetailsOutputForm.SavingCard != null)
            packageCardAccountDetailsOutputForm.SavingCard.stream().forEach(i -> {
                i.ValidDayName = ValidDayName;
                i.ChannelName = ChannelName;
                i.EntityName = EntityName;
            });
        return BaseOutputForm.success(packageCardAccountDetailsOutputForm);
    }


    public BaseOutput micromallProductAccount(CustomerAccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(productAccountMapper.customerMicromallProductAccount(SystemCustomerUtil.getSystemCustomerID(), form.Active, form.Name));
    }

    public BaseOutput micromallProjectAccount(CustomerAccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(projectAccountMapper.customerMicromallProjectAccount(SystemCustomerUtil.getSystemCustomerID(), form.Active, form.Name));
    }

    public BaseOutput micromallProjectAccountEntity(CardEntityForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(projectAccountMapper.customerMicromallProjectAccountEntity(form.ID, form.Name, form.CityCode, form.Longitude, form.Latitude));
    }

    public BaseOutput micromallGeneralCardAccount(CustomerAccountQueryForm form) {

        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(generalCardAccountMapper.customerMicromallGeneralCardAccount(SystemCustomerUtil.getSystemCustomerID(), form.Active, form.Name));
    }

    public BaseOutput generalCardProject(CardProjectForm form) {
        return BaseOutputForm.success(generalCardAccountMapper.generalCardProject(form.ID, form.Name));
    }

    public BaseOutput generalCardAccountEntity(CardEntityForm form) {
        return BaseOutputForm.success(generalCardAccountMapper.customerMicromallGeneralCardAccountEntity(form.ID, form.Name, form.CityCode, form.Longitude, form.Latitude));
    }

    public BaseOutput micromallTimeCardAccount(CustomerAccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(timeCardAccountMapper.customerMicromallTimeCardAccount(SystemCustomerUtil.getSystemCustomerID(), form.Active, form.Name));
    }

    public BaseOutput timeCardAccountProject(CardProjectForm form) {
        return BaseOutputForm.success(timeCardAccountMapper.customerMicromallTimeCardAccountProject(form.ID, form.Name));
    }

    public BaseOutput timeCardAccountEntity(CardEntityForm form) {
        return BaseOutputForm.success(timeCardAccountMapper.customerMicromallTimeCardAccountEntity(form.ID, form.Name, form.CityCode, form.Longitude, form.Latitude));
    }

    public BaseOutput micromallSavingCardAccount(CustomerAccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(savingCardAccountMapper.customerMicromallSavingCardAccount(SystemCustomerUtil.getSystemCustomerID(), form.Active, form.Name));
    }

    public BaseOutput savingCardAccountEntity(CardEntityForm form) {
        return BaseOutputForm.success(savingCardAccountMapper.customerMicromallSavingCardAccountEntity(form.ID, form.Name, form.CityCode, form.Longitude, form.Latitude));
    }

    public BaseOutput micromallPackageCardAccount(CustomerAccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(packageCardAccountMapper.micromallPackageCardAccount(SystemCustomerUtil.getSystemCustomerID(), form.Active, form.Name));
    }

    public BaseOutput all() {
        CustomerEntity customer = customerMapper.getCustomer(SystemCustomerUtil.getSystemCustomerID());
        return BaseOutputForm.success(customer);
    }

    public BaseOutput rechargeSavingCardAccount(CustomerAccountQueryForm form) {
        List<SavingCardAccountForm> list = savingCardAccountMapper.rechargeSavingCardAccount(form.CustomerID, form.Name);
        return BaseOutputForm.success(list);
    }
}