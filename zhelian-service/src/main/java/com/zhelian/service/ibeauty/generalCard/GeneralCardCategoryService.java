package com.zhelian.service.ibeauty.generalCard;

import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.GeneralCardCategoryMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.GeneralCardCategoryEntity;
import com.zhelian.model.ibeauty.form.input.GeneralCardCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.GeneralCardCategoryMoveForm;
import com.zhelian.model.ibeauty.form.input.GeneralCardCategoryQueryForm;
import com.zhelian.model.ibeauty.form.input.GeneralCardCategoryUpdateForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

@Service
public class GeneralCardCategoryService {

    @Autowired
    GeneralCardCategoryMapper mapper;

    public BaseOutput generalCardCategoryAll(GeneralCardCategoryQueryForm form) {
        return BaseOutputForm.success(mapper.generalCardCategoryAll(form.Name, form.Active));
    }

    public BaseOutput createGeneralCardCategory(GeneralCardCategoryAddForm form) {
        Integer Sequence = mapper.getGeneralCardCategoryMaxSequence();
        GeneralCardCategoryEntity entity = new GeneralCardCategoryEntity();
        entity.Name = form.Name;
        entity.Sequence = Sequence != null ? Sequence + 1 : 1;
        mapper.createGeneralCardCategory(entity);
        return BaseOutput.success();
    }

    public BaseOutput updateGeneralCardCategory(GeneralCardCategoryUpdateForm form) {

        if (form.Active == false) {
            Integer count = mapper.generalCardByCategoryCount(form.ID);
            if (count > 0)
                return BaseOutput.failed("该分类下有通用次卡，不可以改为无效");
        }

        GeneralCardCategoryEntity entity = new GeneralCardCategoryEntity();
        entity.Name = form.Name;
        entity.ID = form.ID;
        entity.Active = form.Active;
        mapper.updateGeneralCardCategory(entity);
        return BaseOutput.success();
    }

    public BaseOutput moveGeneralCardCategorySequence(GeneralCardCategoryMoveForm form) {
        LinkedList<Integer> linkedList = mapper.generalCardCategoryAscList(form.MoveID);
        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);
        List<GeneralCardCategoryEntity> list = new ArrayList<>();
        for (Integer ID : linkedList) {
            GeneralCardCategoryEntity entity = new GeneralCardCategoryEntity();
            entity.ID = ID;
            entity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(entity);
        }
        mapper.updateGeneralCardCategorySequence(list);
        return BaseOutput.success();
    }
}
