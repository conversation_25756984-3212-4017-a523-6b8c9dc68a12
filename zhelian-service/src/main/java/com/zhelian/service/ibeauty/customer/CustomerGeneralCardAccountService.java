package com.zhelian.service.ibeauty.customer;


import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.ibeauty.CustomerGeneralCardAccountMapper;
import com.zhelian.mapper.ibeauty.GeneralCardAccountMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.GeneralCardAccountEntity;
import com.zhelian.model.ibeauty.form.input.AccountQueryForm;
import com.zhelian.model.ibeauty.form.output.CustomerRepaymentAccountOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class CustomerGeneralCardAccountService {

    @Autowired
    private CustomerGeneralCardAccountMapper customerGeneralCardAccountMapper;
    @Autowired
    private GeneralCardAccountMapper generalCardAccountMapper;

    public BaseOutput treatBill(AccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(customerGeneralCardAccountMapper.getTreatBill(form.ID));
    }

    public BaseOutput refundBill(AccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(customerGeneralCardAccountMapper.getRefundBill(form.ID));
    }

    public BaseOutput refundTreatBill(AccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(customerGeneralCardAccountMapper.getRefundTreatBill(form.ID));
    }

    public BaseOutput transferBill(AccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(customerGeneralCardAccountMapper.getTransferBill(form.ID));
    }

    public BaseOutput repayment(AccountQueryForm form) {
        GeneralCardAccountEntity productAccountEntity = generalCardAccountMapper.getGeneralCardAccountByAccountID(Integer.parseInt(form.ID));

        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CustomerRepaymentAccountOutputForm> list = null;
        if (productAccountEntity.PackageCardAccountID == null) {
            list = customerGeneralCardAccountMapper.getRepayment(form.ID);
        } else {
            list = customerGeneralCardAccountMapper.getPackageCardRepayment(form.ID);
        }
        return BasePageInfo.success(list);
    }

    public BaseOutput arrearAmountProcess(AccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(customerGeneralCardAccountMapper.getArrearAmountProcess(form.ID));
    }
}