package com.zhelian.service.ibeauty.saleCustomer;


import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.ibeauty.SaleCustomerMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.SaleCustomerQueryForm;
import com.zhelian.model.ibeauty.form.output.CustomerShortInfoOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class SaleCustomerService {

    @Autowired
    SaleCustomerMapper saleCustomerMapper;

    public BaseOutput saleCustomer(SaleCustomerQueryForm form) {

        List<CustomerShortInfoOutputForm> list = saleCustomerMapper.saleCustomer(form.Name, SystemUserUtil.getSystemEntityID());
        return BaseOutputForm.success(list);
    }

    public BaseOutput saleAllCustomer(SaleCustomerQueryForm form) {

        List<CustomerShortInfoOutputForm> list = saleCustomerMapper.saleAllCustomer(form.Name);
        return BaseOutputForm.success(list);
    }

}