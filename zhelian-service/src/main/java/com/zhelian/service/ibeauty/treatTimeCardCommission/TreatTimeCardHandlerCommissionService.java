package com.zhelian.service.ibeauty.treatTimeCardCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.TreatTimeCardHandlerCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.TreatTimeCardHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.TreatTimeCardHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatCardHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardHandlersCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.TreatCardHandlerCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class TreatTimeCardHandlerCommissionService {

    @Autowired
    TreatTimeCardHandlerCommissionMapper treatTimeCardHandlerCommissionMapper;


    public BaseOutput allTreatTimeCardHandlerCommission(TreatCardCommissionQueryForm form) {
        List<TreatCardHandlerCommissionOutputForm> list = treatTimeCardHandlerCommissionMapper.allTreatTimeCardHandlerCommission(form.EntityID, form.CardID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatTimeCardHandlerCommission(TreatCardHandlersCommissionAddForm form) {

        treatTimeCardHandlerCommissionMapper.deleteTreatTimeCardHandler(form.EntityID, form.CardID);
        treatTimeCardHandlerCommissionMapper.deleteTreatTimeCardHandlerJobType(form.EntityID, form.CardID);
        List<TreatTimeCardHandlerCommissionEntity> treatTimeCardHandlerCommissionList = new ArrayList<>();
        List<TreatTimeCardHandlerJobTypeCommissionEntity> treatTimeCardHandlerJobTypeCommissionList = new ArrayList<>();
        if (form.CardHandler != null && form.CardHandler.size() > 0) {
            for (TreatCardHandlerCommissionAddForm treatCardHandlerCommissionAddForm : form.CardHandler) {
                if (treatCardHandlerCommissionAddForm.PayRate != null || treatCardHandlerCommissionAddForm.PayFixed != null ||
                        treatCardHandlerCommissionAddForm.CardRate != null || treatCardHandlerCommissionAddForm.CardFixed != null ||
                        treatCardHandlerCommissionAddForm.CardLargessRate != null || treatCardHandlerCommissionAddForm.CardLargessFixed != null ||
                        treatCardHandlerCommissionAddForm.LargessRate != null || treatCardHandlerCommissionAddForm.LargessFixed != null ||
                        treatCardHandlerCommissionAddForm.SpecialBenefit != null) {
                    TreatTimeCardHandlerCommissionEntity treatTimeCardHandlerCommissionEntity = new TreatTimeCardHandlerCommissionEntity();
                    treatTimeCardHandlerCommissionEntity.EntityID = form.EntityID;
                    treatTimeCardHandlerCommissionEntity.TimeCardID = form.CardID;
                    treatTimeCardHandlerCommissionEntity.TimeCardProjectTreatHandlerID = treatCardHandlerCommissionAddForm.TreatHandlerID;
                    treatTimeCardHandlerCommissionEntity.PayRate = treatCardHandlerCommissionAddForm.PayRate;
                    treatTimeCardHandlerCommissionEntity.PayFixed = treatCardHandlerCommissionAddForm.PayFixed;
                    treatTimeCardHandlerCommissionEntity.CardRate = treatCardHandlerCommissionAddForm.CardRate;
                    treatTimeCardHandlerCommissionEntity.CardFixed = treatCardHandlerCommissionAddForm.CardFixed;
                    treatTimeCardHandlerCommissionEntity.CardLargessRate = treatCardHandlerCommissionAddForm.CardLargessRate;
                    treatTimeCardHandlerCommissionEntity.CardLargessFixed = treatCardHandlerCommissionAddForm.CardLargessFixed;
                    treatTimeCardHandlerCommissionEntity.LargessFixed = treatCardHandlerCommissionAddForm.LargessFixed;
                    treatTimeCardHandlerCommissionEntity.LargessRate = treatCardHandlerCommissionAddForm.LargessRate;
                    treatTimeCardHandlerCommissionEntity.SpecialBenefit = treatCardHandlerCommissionAddForm.SpecialBenefit;
                    treatTimeCardHandlerCommissionList.add(treatTimeCardHandlerCommissionEntity);
                }
                if (treatCardHandlerCommissionAddForm.HandlerJobTypeCommission != null && treatCardHandlerCommissionAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (TreatCardCategoryHandlerJobTypeCommissionAddForm typeCommissionAddForm : treatCardHandlerCommissionAddForm.HandlerJobTypeCommission) {
                        if (typeCommissionAddForm.PayRate != null || typeCommissionAddForm.PayFixed != null ||
                                typeCommissionAddForm.CardRate != null || typeCommissionAddForm.CardFixed != null ||
                                typeCommissionAddForm.CardLargessRate != null || typeCommissionAddForm.CardLargessFixed != null ||
                                typeCommissionAddForm.LargessRate != null || typeCommissionAddForm.LargessFixed != null ||
                                typeCommissionAddForm.SpecialBenefit != null) {
                            TreatTimeCardHandlerJobTypeCommissionEntity commissionEntity = new TreatTimeCardHandlerJobTypeCommissionEntity();
                            commissionEntity.EntityID = form.EntityID;
                            commissionEntity.TimeCardID = form.CardID;
                            commissionEntity.TimeCardProjectTreatHandlerID = treatCardHandlerCommissionAddForm.TreatHandlerID;
                            commissionEntity.JobTypeID = typeCommissionAddForm.JobTypeID;
                            commissionEntity.PayRate = typeCommissionAddForm.PayRate;
                            commissionEntity.PayFixed = typeCommissionAddForm.PayFixed;
                            commissionEntity.CardRate = typeCommissionAddForm.CardRate;
                            commissionEntity.CardFixed = typeCommissionAddForm.CardFixed;
                            commissionEntity.CardLargessRate = typeCommissionAddForm.CardLargessRate;
                            commissionEntity.CardLargessFixed = typeCommissionAddForm.CardLargessFixed;
                            commissionEntity.LargessFixed = typeCommissionAddForm.LargessFixed;
                            commissionEntity.LargessRate = typeCommissionAddForm.LargessRate;
                            commissionEntity.SpecialBenefit = typeCommissionAddForm.SpecialBenefit;
                            treatTimeCardHandlerJobTypeCommissionList.add(commissionEntity);
                        }
                    }
                }
            }
        }
        if (treatTimeCardHandlerCommissionList != null && treatTimeCardHandlerCommissionList.size() > 0) {
            List<List<TreatTimeCardHandlerCommissionEntity>> subs = Lists.partition(treatTimeCardHandlerCommissionList, 20);
            for (List<TreatTimeCardHandlerCommissionEntity> entityList : subs) {
                treatTimeCardHandlerCommissionMapper.insertTreatTimeCardHandlerCommission(entityList);
            }
        }
        if (treatTimeCardHandlerJobTypeCommissionList != null && treatTimeCardHandlerJobTypeCommissionList.size() > 0) {
            List<List<TreatTimeCardHandlerJobTypeCommissionEntity>> subs = Lists.partition(treatTimeCardHandlerJobTypeCommissionList, 20);
            for (List<TreatTimeCardHandlerJobTypeCommissionEntity> entityList : subs) {
                treatTimeCardHandlerCommissionMapper.insertTreatTimeCardHandlerJobTypeCommission(entityList);
            }
        }
        return BaseOutput.success();
    }
}