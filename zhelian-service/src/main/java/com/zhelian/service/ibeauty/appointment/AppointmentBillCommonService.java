package com.zhelian.service.ibeauty.appointment;

import com.zhelian.core.utils.DateTimeUtil;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.entity.AppointmentBillEntity;
import com.zhelian.model.ibeauty.form.input.AppointmentBillAddForm;
import com.zhelian.model.ibeauty.form.input.AppointmentBillUpdateForm;
import com.zhelian.model.ibeauty.form.input.AppointmentBillUpdateStatusForm;
import com.zhelian.model.miniprogram.form.input.MiniAppointmentBillAddExtendForm;
import com.zhelian.model.miniprogram.form.input.MiniAppointmentBillAddForm;
import com.zhelian.model.miniprogram.form.input.MiniAppointmentBillUpdateForm;
import com.zhelian.service.weixin.WeiXinMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class AppointmentBillCommonService {
    @Autowired
    private AppointmentBillService appointmentBillService;

    @Autowired
    private WeiXinMessageService weiXinMessageService;


    public BaseOutput create(AppointmentBillAddForm form) throws Exception {
        BaseOutput baseOutput = appointmentBillService.createAppointmentBil(form);
        if (baseOutput.StateCode.equals(200)) {
            weiXinMessageService.setAppointmentBillWxMessage(baseOutput.Message);
        }
        return baseOutput;
    }

    public BaseOutput update(AppointmentBillUpdateForm form) throws Exception {
        AppointmentBillEntity appointmentBillEntity = appointmentBillService.getAppointmentBill(form.ID);
        Date originalAppointmentDate = appointmentBillEntity.AppointmentDate;
        BaseOutput baseOutput = appointmentBillService.updateAppointmentBill(form);
        if (baseOutput.StateCode.equals(200)) {
            if (!originalAppointmentDate.equals(DateTimeUtil.toDate(form.AppointmentDate))){
                weiXinMessageService.setUpdateAppointmentBillWxMessage(form.ID,originalAppointmentDate);
            }
        }
        return baseOutput;
    }

    public BaseOutput updateStatus(AppointmentBillUpdateStatusForm form) {
        BaseOutput baseOutput = appointmentBillService.updateStatus(form);
        if (baseOutput.StateCode.equals(200)) {
            if (form.Status.equals("30")) {
                weiXinMessageService.setCancelAppointmentBillWxMessage(form.ID);
            }
        }
        return baseOutput;
    }

    public BaseOutput miniCreate(MiniAppointmentBillAddForm form) throws Exception {
        BaseOutput baseOutput = appointmentBillService.miniCreateBill(form);
        if (baseOutput.StateCode.equals(200)) {
            weiXinMessageService.setAppointmentBillWxMessage(baseOutput.Message);
        }
        return baseOutput;
    }


    public BaseOutput newCustomerCreate(MiniAppointmentBillAddExtendForm form) throws Exception {

        BaseOutput baseOutput = appointmentBillService.newCustomerCreateBill(form);
        if (baseOutput.StateCode.equals(200)) {
            weiXinMessageService.setAppointmentBillWxMessage(baseOutput.Message);
        }
        return baseOutput;
    }


    public BaseOutput miniUpdate(MiniAppointmentBillUpdateForm form) throws Exception {
        AppointmentBillEntity appointmentBillEntity = appointmentBillService.getAppointmentBill(form.ID);
        Date originalAppointmentDate = appointmentBillEntity.AppointmentDate;
        BaseOutput baseOutput = appointmentBillService.miniUpdateBill(form);
        if (baseOutput.StateCode.equals(200)) {
            if (!originalAppointmentDate.equals(DateTimeUtil.toDate(form.AppointmentDate))){
                weiXinMessageService.setUpdateAppointmentBillWxMessage(form.ID,originalAppointmentDate);
            }
        }
        return baseOutput;
    }
}
