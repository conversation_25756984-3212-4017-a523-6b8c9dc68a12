package com.zhelian.service.ibeauty.treatProductCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.TreatProductCategoryHandlerCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.TreatProductCategoryHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.TreatProductCategoryHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.TreatProductCategoryHandlerCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatProductCategoryHandlerCommissionService {

    @Autowired
    TreatProductCategoryHandlerCommissionMapper treatProductCategoryHandlerCommissionMapper;


    public BaseOutput allTreatProductCategoryHandlerCommission(TreatProductCategoryHandlerCommissionQueryForm form) {
        List<TreatProductCategoryHandlerCommissionOutputForm> list = treatProductCategoryHandlerCommissionMapper.allTreatProductCategoryHandlerCommission(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatProductCategoryHandlerCommission(TreatProductCategoryHandlerCommissionAddForm form) {


        treatProductCategoryHandlerCommissionMapper.deleteTreatProductCategoryHandler(form.EntityID, form.CategoryID);
        treatProductCategoryHandlerCommissionMapper.deleteTreatProductCategoryHandlerJobType(form.EntityID, form.CategoryID);

        if (form.Handler != null && form.Handler.size() > 0) {

            List<TreatProductCategoryHandlerCommissionEntity> listTreatProductCategoryHandlerCommission = new ArrayList<>();

            List<TreatProductCategoryHandlerJobTypeCommissionEntity> listTreatProductCategoryHandlerJobTypeCommission = new ArrayList<>();


            for (TreatProductCategoryHandlerAddForm treatProductCategoryHandlerAddForm : form.Handler) {
                if (treatProductCategoryHandlerAddForm.PayRate != null || treatProductCategoryHandlerAddForm.PayFixed != null ||
                        treatProductCategoryHandlerAddForm.CardRate != null || treatProductCategoryHandlerAddForm.CardFixed != null ||
                        treatProductCategoryHandlerAddForm.CardLargessRate != null || treatProductCategoryHandlerAddForm.CardLargessFixed != null ||
                        treatProductCategoryHandlerAddForm.LargessRate != null || treatProductCategoryHandlerAddForm.LargessFixed != null ||
                        treatProductCategoryHandlerAddForm.SpecialBenefit != null) {
                    TreatProductCategoryHandlerCommissionEntity treatProductCategoryHandlerCommissionEntity = new TreatProductCategoryHandlerCommissionEntity();
                    treatProductCategoryHandlerCommissionEntity.EntityID = form.EntityID;
                    treatProductCategoryHandlerCommissionEntity.ProductCategoryID = form.CategoryID;
                    treatProductCategoryHandlerCommissionEntity.ProductTreatHandlerID = treatProductCategoryHandlerAddForm.TreatHandlerID;
                    treatProductCategoryHandlerCommissionEntity.PayRate = treatProductCategoryHandlerAddForm.PayRate;
                    treatProductCategoryHandlerCommissionEntity.PayFixed = treatProductCategoryHandlerAddForm.PayFixed;
                    treatProductCategoryHandlerCommissionEntity.CardRate = treatProductCategoryHandlerAddForm.CardRate;
                    treatProductCategoryHandlerCommissionEntity.CardFixed = treatProductCategoryHandlerAddForm.CardFixed;
                    treatProductCategoryHandlerCommissionEntity.CardLargessRate = treatProductCategoryHandlerAddForm.CardLargessRate;
                    treatProductCategoryHandlerCommissionEntity.CardLargessFixed = treatProductCategoryHandlerAddForm.CardLargessFixed;
                    treatProductCategoryHandlerCommissionEntity.LargessFixed = treatProductCategoryHandlerAddForm.LargessFixed;
                    treatProductCategoryHandlerCommissionEntity.LargessRate = treatProductCategoryHandlerAddForm.LargessRate;
                    treatProductCategoryHandlerCommissionEntity.SpecialBenefit = treatProductCategoryHandlerAddForm.SpecialBenefit;
                    listTreatProductCategoryHandlerCommission.add(treatProductCategoryHandlerCommissionEntity);
                }

                if (treatProductCategoryHandlerAddForm.HandlerJobTypeCommission != null && treatProductCategoryHandlerAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (TreatProductCategoryHandlerJobTypeCommissionAddForm treatProductCategoryHandlerJobTypeCommissionAddForm : treatProductCategoryHandlerAddForm.HandlerJobTypeCommission) {
                        if (treatProductCategoryHandlerJobTypeCommissionAddForm.PayRate != null || treatProductCategoryHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                treatProductCategoryHandlerJobTypeCommissionAddForm.CardRate != null || treatProductCategoryHandlerJobTypeCommissionAddForm.CardFixed != null ||
                                treatProductCategoryHandlerJobTypeCommissionAddForm.CardLargessRate != null || treatProductCategoryHandlerJobTypeCommissionAddForm.CardLargessFixed != null ||
                                treatProductCategoryHandlerJobTypeCommissionAddForm.LargessRate != null || treatProductCategoryHandlerJobTypeCommissionAddForm.LargessFixed != null ||
                                treatProductCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            TreatProductCategoryHandlerJobTypeCommissionEntity treatProductCategoryHandlerJobTypeCommissionEntity = new TreatProductCategoryHandlerJobTypeCommissionEntity();
                            treatProductCategoryHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            treatProductCategoryHandlerJobTypeCommissionEntity.ProductCategoryID = form.CategoryID;
                            treatProductCategoryHandlerJobTypeCommissionEntity.ProductTreatHandlerID = treatProductCategoryHandlerAddForm.TreatHandlerID;
                            treatProductCategoryHandlerJobTypeCommissionEntity.JobTypeID = treatProductCategoryHandlerJobTypeCommissionAddForm.JobTypeID;
                            treatProductCategoryHandlerJobTypeCommissionEntity.PayRate = treatProductCategoryHandlerJobTypeCommissionAddForm.PayRate;
                            treatProductCategoryHandlerJobTypeCommissionEntity.PayFixed = treatProductCategoryHandlerJobTypeCommissionAddForm.PayFixed;
                            treatProductCategoryHandlerJobTypeCommissionEntity.CardRate = treatProductCategoryHandlerJobTypeCommissionAddForm.CardRate;
                            treatProductCategoryHandlerJobTypeCommissionEntity.CardFixed = treatProductCategoryHandlerJobTypeCommissionAddForm.CardFixed;
                            treatProductCategoryHandlerJobTypeCommissionEntity.CardLargessRate = treatProductCategoryHandlerJobTypeCommissionAddForm.CardLargessRate;
                            treatProductCategoryHandlerJobTypeCommissionEntity.CardLargessFixed = treatProductCategoryHandlerJobTypeCommissionAddForm.CardLargessFixed;
                            treatProductCategoryHandlerJobTypeCommissionEntity.LargessFixed = treatProductCategoryHandlerJobTypeCommissionAddForm.LargessFixed;
                            treatProductCategoryHandlerJobTypeCommissionEntity.LargessRate = treatProductCategoryHandlerJobTypeCommissionAddForm.LargessRate;
                            treatProductCategoryHandlerJobTypeCommissionEntity.SpecialBenefit = treatProductCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            listTreatProductCategoryHandlerJobTypeCommission.add(treatProductCategoryHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
            if (listTreatProductCategoryHandlerCommission != null && listTreatProductCategoryHandlerCommission.size() > 0) {
                List<List<TreatProductCategoryHandlerCommissionEntity>> subs = Lists.partition(listTreatProductCategoryHandlerCommission, 20);
                for (List<TreatProductCategoryHandlerCommissionEntity> entityList : subs) {
                    treatProductCategoryHandlerCommissionMapper.updateTreatProductCategoryHandlerCommission(entityList);
                }
            }

            if (listTreatProductCategoryHandlerJobTypeCommission != null && listTreatProductCategoryHandlerJobTypeCommission.size() > 0) {
                List<List<TreatProductCategoryHandlerJobTypeCommissionEntity>> subs = Lists.partition(listTreatProductCategoryHandlerJobTypeCommission, 20);
                for (List<TreatProductCategoryHandlerJobTypeCommissionEntity> entityList : subs) {
                    treatProductCategoryHandlerCommissionMapper.insertTreatProductCategoryHandlerJobTypeCommission(entityList);
                }
            }
        }
        return BaseOutput.success();
    }
}