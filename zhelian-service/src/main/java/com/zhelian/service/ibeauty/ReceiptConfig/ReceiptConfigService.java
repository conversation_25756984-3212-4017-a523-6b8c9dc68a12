package com.zhelian.service.ibeauty.ReceiptConfig;

import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.ibeauty.ReceiptConfigMapper;
import com.zhelian.mapper.ibeauty.SaleBillMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.ReceiptConfigEntity;
import com.zhelian.model.ibeauty.form.input.ReceiptConfigAddForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReceiptConfigService {

    @Autowired
    ReceiptConfigMapper receiptConfigMapper;

    @Autowired
    SaleBillMapper saleBillMapper;

    public BaseOutput receiptConfigAll() {
        ReceiptConfigEntity receiptConfig = receiptConfigMapper.receiptConfigAll(SystemUserUtil.getSystemEntityID());
        if (receiptConfig == null) {
            receiptConfig = new ReceiptConfigEntity();
            receiptConfig.EntityID = SystemUserUtil.getSystemEntityID();
            receiptConfig.PrintWidth = 58;
            receiptConfig.PrintQuantity = 1;
            receiptConfig.NameEncrypt = true;
            receiptConfig.MobileEncrypt = true;
            receiptConfig.SaleGoodsOriginPrice = true;
            receiptConfig.SalePromotions = true;
            receiptConfig.SaleRefundBuyQuantity = true;
            receiptConfig.SaleRefundBuyAmount = true;
            receiptConfig.SaleRefundableAmount = true;
            receiptConfig.SaleArrearBuyQuantity = true;
            receiptConfig.SaleArrearBuyAmount = true;
            receiptConfig.TreatGoodsOriginPrice = true;
            receiptConfig.TreatPromotions = true;
            receiptConfig.WriteTextFirst = "谢谢光临，欢迎再来！";
            receiptConfig.WriteTextSecond = "";
            receiptConfig.EntityAddress = true;
            receiptConfig.SaleArrearBalanceAmount = true;
        }
        return BaseOutputForm.success(receiptConfig);
    }


    public BaseOutput createReceiptConfig(ReceiptConfigAddForm form) {
        ReceiptConfigEntity receiptConfigEntity = receiptConfigMapper.receiptConfigAll(SystemUserUtil.getSystemEntityID());
        if (receiptConfigEntity != null) {
            receiptConfigEntity.PrintWidth = form.PrintWidth;
            receiptConfigEntity.PrintQuantity = form.PrintQuantity;
            receiptConfigEntity.NameEncrypt = form.NameEncrypt;
            receiptConfigEntity.MobileEncrypt = form.MobileEncrypt;
            receiptConfigEntity.SaleGoodsOriginPrice = form.SaleGoodsOriginPrice;
            receiptConfigEntity.SalePromotions = form.SalePromotions;
            receiptConfigEntity.SaleRefundBuyQuantity = form.SaleRefundBuyQuantity;
            receiptConfigEntity.SaleRefundBuyAmount = form.SaleRefundBuyAmount;
            receiptConfigEntity.SaleRefundableAmount = form.SaleRefundableAmount;
            receiptConfigEntity.SaleArrearBuyQuantity = form.SaleArrearBuyQuantity;
            receiptConfigEntity.SaleArrearBuyAmount = form.SaleArrearBuyAmount;
            receiptConfigEntity.TreatGoodsOriginPrice = form.TreatGoodsOriginPrice;
            receiptConfigEntity.TreatPromotions = form.TreatPromotions;
            receiptConfigEntity.TreatAmount = form.TreatAmount;
            receiptConfigEntity.WriteTextFirst = form.WriteTextFirst;
            receiptConfigEntity.WriteTextSecond = form.WriteTextSecond;
            receiptConfigEntity.EntityAddress = form.EntityAddress;
            receiptConfigEntity.SaleArrearBalanceAmount = form.SaleArrearBalanceAmount;
            receiptConfigEntity.SaleTotalAmount = form.SaleTotalAmount;
            receiptConfigMapper.updateReceiptConfig(receiptConfigEntity);
        } else {
            receiptConfigEntity = new ReceiptConfigEntity();
            receiptConfigEntity.EntityID = SystemUserUtil.getSystemEntityID();
            receiptConfigEntity.PrintWidth = form.PrintWidth;
            receiptConfigEntity.PrintQuantity = form.PrintQuantity;
            receiptConfigEntity.NameEncrypt = form.NameEncrypt;
            receiptConfigEntity.MobileEncrypt = form.MobileEncrypt;
            receiptConfigEntity.SaleGoodsOriginPrice = form.SaleGoodsOriginPrice;
            receiptConfigEntity.SalePromotions = form.SalePromotions;
            receiptConfigEntity.SaleRefundBuyQuantity = form.SaleRefundBuyQuantity;
            receiptConfigEntity.SaleRefundBuyAmount = form.SaleRefundBuyAmount;
            receiptConfigEntity.SaleRefundableAmount = form.SaleRefundableAmount;
            receiptConfigEntity.SaleArrearBuyQuantity = form.SaleArrearBuyQuantity;
            receiptConfigEntity.SaleArrearBuyAmount = form.SaleArrearBuyAmount;
            receiptConfigEntity.TreatGoodsOriginPrice = form.TreatGoodsOriginPrice;
            receiptConfigEntity.TreatPromotions = form.TreatPromotions;
            receiptConfigEntity.TreatAmount = form.TreatAmount;
            receiptConfigEntity.WriteTextFirst = form.WriteTextFirst;
            receiptConfigEntity.WriteTextSecond = form.WriteTextSecond;
            receiptConfigEntity.EntityAddress = form.EntityAddress;
            receiptConfigEntity.SaleArrearBalanceAmount = form.SaleArrearBalanceAmount;
            receiptConfigEntity.SaleTotalAmount = form.SaleTotalAmount;
            receiptConfigMapper.createReceiptConfig(receiptConfigEntity);
        }
        return BaseOutput.success();
    }

    public BaseOutput receiptConfigBill() {
        ReceiptConfigEntity receiptConfig = saleBillMapper.getReceiptConfig(SystemUserUtil.getSystemEntityID());
        if (receiptConfig == null) {
            receiptConfig = new ReceiptConfigEntity();
            receiptConfig.EntityID = SystemUserUtil.getSystemEntityID();
            receiptConfig.PrintWidth = 58;
            receiptConfig.PrintQuantity = 1;
            receiptConfig.NameEncrypt = true;
            receiptConfig.MobileEncrypt = true;
            receiptConfig.SaleGoodsOriginPrice = true;
            receiptConfig.SalePromotions = true;
            receiptConfig.SaleRefundBuyQuantity = true;
            receiptConfig.SaleRefundBuyAmount = true;
            receiptConfig.SaleRefundableAmount = true;
            receiptConfig.SaleArrearBuyQuantity = true;
            receiptConfig.SaleArrearBuyAmount = true;
            receiptConfig.TreatGoodsOriginPrice = true;
            receiptConfig.TreatPromotions = true;
            receiptConfig.TreatAmount = true;
            receiptConfig.WriteTextFirst = "谢谢光临，欢迎再来！";
            receiptConfig.WriteTextSecond = "";
            receiptConfig.EntityAddress = true;
            receiptConfig.SaleArrearBalanceAmount = true;
            receiptConfig.SaleTotalAmount = true;
        }
        return BaseOutputForm.success(receiptConfig);
    }
}