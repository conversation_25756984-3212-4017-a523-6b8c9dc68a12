package com.zhelian.service.ibeauty.saleHandler;


import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.PackageCardSaleHandlerMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.PackageCardSaleHandlerEntity;
import com.zhelian.model.ibeauty.entity.PackageCardSaleHandlerJobTypeEntity;
import com.zhelian.model.ibeauty.form.input.SaleHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerMoveForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerUpdateForm;
import com.zhelian.model.ibeauty.form.output.SaleHandlerOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * @Description:
 * @Param: No such property: code for class: Script1
 * @returns:
 * @Author: xuehao
 * @Date: 2020/3/31 11:25
 */
@Service
public class PackageCardSaleHandlerService {

    @Autowired
    PackageCardSaleHandlerMapper packageCardSaleHandlerMapper;


    public BaseOutput allPackageCardSaleHandler(SaleHandlerQueryForm form) {

        List<SaleHandlerOutputForm> list = packageCardSaleHandlerMapper.allPackageCardSaleHandler(form.Name, form.EntityID, form.Active);
        return BaseOutputForm.success(list);

    }

    @Transactional
    public BaseOutput createPackageCardSaleHandler(SaleHandlerAddForm form) {
        Integer Sequence = packageCardSaleHandlerMapper.getMaxSequence();
        PackageCardSaleHandlerEntity packageCardSaleHandlerEntity = new PackageCardSaleHandlerEntity();
        packageCardSaleHandlerEntity.Name = form.Name;
        packageCardSaleHandlerEntity.EntityID = form.EntityID;
        packageCardSaleHandlerEntity.Sequence = Sequence != null ? Sequence + 1 : 1;

        packageCardSaleHandlerMapper.createPackageCardSaleHandler(packageCardSaleHandlerEntity);

        insertSaleHandlerJobType(form.JobType, packageCardSaleHandlerEntity.ID);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updatePackageCardSaleHandler(SaleHandlerUpdateForm form) {

        PackageCardSaleHandlerEntity packageCardSaleHandlerEntity = new PackageCardSaleHandlerEntity();
        packageCardSaleHandlerEntity.Name = form.Name;
        packageCardSaleHandlerEntity.ID = form.ID;
        packageCardSaleHandlerEntity.EntityID = form.EntityID;
        packageCardSaleHandlerEntity.Active = form.Active;

        packageCardSaleHandlerMapper.updatePackageCardSaleHandler(packageCardSaleHandlerEntity);

        packageCardSaleHandlerMapper.deleteSaleHandlerJobType(form.ID);

        insertSaleHandlerJobType(form.JobType, form.ID);

        return BaseOutput.success();
    }


    public void insertSaleHandlerJobType(Integer[] JobType, Integer ID) {
        List<PackageCardSaleHandlerJobTypeEntity> list = new ArrayList<>();
        for (Integer typeID : JobType) {
            PackageCardSaleHandlerJobTypeEntity packageCardSaleHandlerJobTypeEntity = new PackageCardSaleHandlerJobTypeEntity();
            packageCardSaleHandlerJobTypeEntity.PackageCardSaleHandlerID = ID;
            packageCardSaleHandlerJobTypeEntity.JobTypeID = typeID;
            list.add(packageCardSaleHandlerJobTypeEntity);
        }
        packageCardSaleHandlerMapper.insertPackageCardSaleHandlerJobType(list);
    }

    public BaseOutput movePackageCardSaleHandler(SaleHandlerMoveForm form) {
        //重新排序
        LinkedList<Integer> linkedList = packageCardSaleHandlerMapper.getIDOrderBySequenceASC(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<PackageCardSaleHandlerEntity> list = new ArrayList<>();

        for (Integer ID : linkedList) {
            PackageCardSaleHandlerEntity packageCardSaleHandlerEntity = new PackageCardSaleHandlerEntity();
            packageCardSaleHandlerEntity.ID = ID;
            packageCardSaleHandlerEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(packageCardSaleHandlerEntity);
        }
        packageCardSaleHandlerMapper.updateSequence(list);

        return BaseOutput.success();
    }

}