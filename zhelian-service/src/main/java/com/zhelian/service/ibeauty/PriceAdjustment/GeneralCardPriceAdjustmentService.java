package com.zhelian.service.ibeauty.PriceAdjustment;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.ibeauty.GeneralCardPriceAdjustmentMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.GeneralCardPriceAdjustmentEntity;
import com.zhelian.model.ibeauty.form.input.GeneralCardPriceAdjustmentAddForm;
import com.zhelian.model.ibeauty.form.input.GeneralCardPriceAdjustmentDeleteForm;
import com.zhelian.model.ibeauty.form.input.GeneralCardPriceAdjustmentQueryForm;
import com.zhelian.model.ibeauty.form.output.GeneralCardPriceAdjustmentOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class GeneralCardPriceAdjustmentService {

    @Autowired
    GeneralCardPriceAdjustmentMapper generalCardPriceAdjustmentMapper;

    public BaseOutput generalCardPriceAdjustmentList(GeneralCardPriceAdjustmentQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<GeneralCardPriceAdjustmentOutputForm> list = generalCardPriceAdjustmentMapper.generalCardPriceAdjustmentList(form.GeneralCardName, form.EntityName);
        return BasePageInfo.success(list);
    }

    public BaseOutput createGeneralCardPriceAdjustment(GeneralCardPriceAdjustmentAddForm form) {

        Integer count = generalCardPriceAdjustmentMapper.generalCardPriceAdjustmentCount(form.EntityID, form.GeneralCardID);
        if (count > 0)
            return BaseOutput.failed("通用次卡在该组织单位已经设置调整价格");

        GeneralCardPriceAdjustmentEntity generalCardPriceAdjustmentEntity = new GeneralCardPriceAdjustmentEntity();
        generalCardPriceAdjustmentEntity.EntityID = form.EntityID;
        generalCardPriceAdjustmentEntity.Price = form.Price;
        generalCardPriceAdjustmentEntity.GeneralCardID = form.GeneralCardID;
        generalCardPriceAdjustmentMapper.insertGeneralCardPriceAdjustmentEntity(generalCardPriceAdjustmentEntity);
        return BaseOutputForm.success();
    }


    public BaseOutput updateGeneralCardPriceAdjustmentEntity(GeneralCardPriceAdjustmentAddForm form) {

        GeneralCardPriceAdjustmentEntity generalCardPriceAdjustmentEntity = new GeneralCardPriceAdjustmentEntity();
        generalCardPriceAdjustmentEntity.EntityID = form.EntityID;
        generalCardPriceAdjustmentEntity.Price = form.Price;
        generalCardPriceAdjustmentEntity.GeneralCardID = form.GeneralCardID;
        generalCardPriceAdjustmentMapper.updateGeneralCardPriceAdjustmentEntity(generalCardPriceAdjustmentEntity);

        return BaseOutput.success();
    }

    public BaseOutput deleteGeneralCardPriceAdjustmentEntity(GeneralCardPriceAdjustmentDeleteForm form) {

        generalCardPriceAdjustmentMapper.deleteGeneralCardPriceAdjustmentEntity(form.EntityID, form.GeneralCardID);
        return BaseOutput.success();
    }
}