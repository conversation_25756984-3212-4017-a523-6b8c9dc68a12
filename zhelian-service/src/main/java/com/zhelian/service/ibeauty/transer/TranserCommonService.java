package com.zhelian.service.ibeauty.transer;

import com.zhelian.core.utils.enumeration.WeiXinOfficialAccountMessage;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.TranserAddForm;
import com.zhelian.model.ibeauty.form.input.TranserCancelForm;
import com.zhelian.model.ibeauty.form.output.TranserBillIDOutputForm;
import com.zhelian.service.weixin.WeiXinMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TranserCommonService {
    @Autowired
    private TranserService transerService;
    @Autowired
    WeiXinMessageService weiXinMessageService;

    public BaseOutput create(TranserAddForm form, String channel) throws Exception {

        String bill = transerService.createBill(form, channel);
        weiXinMessageService.setTransBillWxMessage(bill, WeiXinOfficialAccountMessage.TransferOutCustomerNotice, WeiXinOfficialAccountMessage.TransferInCustomerNotice);
        TranserBillIDOutputForm transerBillIDOutputForm = new TranserBillIDOutputForm();
        transerBillIDOutputForm.BillID = bill;
        return BaseOutputForm.success(transerBillIDOutputForm);
    }

    public BaseOutput cancel(TranserCancelForm form) {
        transerService.cancelBill(form);
        weiXinMessageService.setCancelTransBillWxMessage(form.ID, WeiXinOfficialAccountMessage.TransferCancelOutCustomerNotice, WeiXinOfficialAccountMessage.TransferCancelInCustomerNotice);
        return BaseOutput.success();
    }
}
