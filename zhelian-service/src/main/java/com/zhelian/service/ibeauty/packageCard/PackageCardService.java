package com.zhelian.service.ibeauty.packageCard;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.PackageCardMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.*;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.ibeauty.form.output.GoodsCategoryOutputForm;
import com.zhelian.model.ibeauty.form.output.PackageCardDetailsOutputForm;
import com.zhelian.model.ibeauty.form.output.PackageCardGoodsOutputForm;
import com.zhelian.model.ibeauty.form.output.PackageCardOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;


/**
 * @Description:
 * @Param: No such property: code for class: Script1
 * @returns:
 * @Author: xuehao
 * @Date: 2020/3/27 9:04
 */
@Service
public class PackageCardService {

    @Autowired
    PackageCardMapper packageCardMapper;


    public BaseOutput packageCardList(PackageCardQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<PackageCardOutputForm> list = packageCardMapper.packageCardList(form.Name, form.PackageCardCategoryID, form.IsAllowSell);
        return BasePageInfo.success(list);
    }

    public BaseOutput getPackageCardDetails(PackageCardGoodsQueryForm form) {
        PackageCardDetailsOutputForm productDetails = packageCardMapper.getPackageCardDetails(form.ID);
        return BaseOutputForm.success(productDetails);
    }


    public BaseOutput savingCard() {
        List<GoodsCategoryOutputForm> list = packageCardMapper.savingCard();
        return BaseOutputForm.success(list);
    }

    public BaseOutput packageCardGoods(PackageCardGoodsQueryForm form) {
        PackageCardGoodsOutputForm packageCardGoodsOutputForm = new PackageCardGoodsOutputForm();
        packageCardGoodsOutputForm.Product = packageCardMapper.packageCardProduct(form.ID, form.IsLargess);
        packageCardGoodsOutputForm.Project = packageCardMapper.packageCardProject(form.ID, form.IsLargess);
        packageCardGoodsOutputForm.GeneralCard = packageCardMapper.packageCardGeneralCard(form.ID, form.IsLargess);
        packageCardGoodsOutputForm.TimeCard = packageCardMapper.packageCardTimeCard(form.ID, form.IsLargess);
        packageCardGoodsOutputForm.SavingCard = packageCardMapper.packageCardSavingCard(form.ID, form.IsLargess);
        return BaseOutputForm.success(packageCardGoodsOutputForm);
    }

    @Transactional
    public BaseOutput create(PackageCardAddForm form) {

        PackageCardEntity packageCardEntity = new PackageCardEntity();
        packageCardEntity.Name = form.Name;
        packageCardEntity.Alias = form.Alias;
        packageCardEntity.PackageCardCategoryID = form.PackageCardCategoryID;
        packageCardEntity.SaleStartDate = form.SaleStartDate;
        packageCardEntity.SaleEndingDate = form.SaleEndingDate;
        packageCardEntity.ValidType = form.ValidType;
        packageCardEntity.Remark = form.Remark;
        packageCardEntity.Memo = form.Memo;
        if (form.ValidType.equals(3)) {
            packageCardEntity.ValidDay = null;
        } else {

            packageCardEntity.ValidDay = form.ValidDay;
        }
        packageCardEntity.IsAllowSell = form.IsAllowSell;
        packageCardEntity.IsAllowLargess = form.IsAllowLargess;
        packageCardEntity.Price = getTotal(form.Goods);

        packageCardMapper.create(packageCardEntity);

        if (form.PackageCardSaleEntity != null && form.PackageCardSaleEntity.length > 0) {
            List<PackageCardSaleEntity> list = new ArrayList<>();
            for (Integer ID : form.PackageCardSaleEntity) {
                PackageCardSaleEntity packageCardSaleEntity = new PackageCardSaleEntity();
                packageCardSaleEntity.EntityID = ID;
                packageCardSaleEntity.PackageCardID = packageCardEntity.ID;
                list.add(packageCardSaleEntity);
            }
            packageCardMapper.insertSaleEntity(list);
        }
        if (form.ModifyPriceEntity != null && form.ModifyPriceEntity.length > 0) {
            List<PackageCardSaleEntity> list = new ArrayList<>();
            for (Integer ID : form.ModifyPriceEntity) {
                PackageCardSaleEntity packageCardSaleEntity = new PackageCardSaleEntity();
                packageCardSaleEntity.EntityID = ID;
                packageCardSaleEntity.PackageCardID = packageCardEntity.ID;
                list.add(packageCardSaleEntity);
            }
            packageCardMapper.insertModifyPriceEntity(list);
        }
        updatePackageCard(form.Goods, form.GoodsLargess, packageCardEntity.ID);
        if (form.ImageList.size() > 0) {
            for (int a = 0; a < form.ImageList.size(); a++) {
                packageCardMapper.insertPackageCardImageURL(packageCardEntity.ID, form.ImageList.get(a).ImageUrl, a + 1);
            }
        }
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput update(PackageCardUpdateForm form) {

        PackageCardEntity packageCardEntity = new PackageCardEntity();
        packageCardEntity.ID = form.ID;
        packageCardEntity.Name = form.Name;
        packageCardEntity.Alias = form.Alias;
        packageCardEntity.PackageCardCategoryID = form.PackageCardCategoryID;
        packageCardEntity.SaleStartDate = form.SaleStartDate;
        packageCardEntity.SaleEndingDate = form.SaleEndingDate;
        packageCardEntity.ValidType = form.ValidType;
        packageCardEntity.Remark = form.Remark;
        packageCardEntity.Memo = form.Memo;
        if (form.ValidType.equals(3)) {
            packageCardEntity.ValidDay = null;
        } else {

            packageCardEntity.ValidDay = form.ValidDay;
        }
        packageCardEntity.IsAllowSell = form.IsAllowSell;
        packageCardEntity.IsAllowLargess = form.IsAllowLargess;
        packageCardEntity.Price = getTotal(form.Goods);

        packageCardMapper.update(packageCardEntity);


        packageCardMapper.deletePackageCardSaleEntity(form.ID);
        packageCardMapper.deletePackageCardModifyPriceEntity(form.ID);
        if (form.PackageCardSaleEntity != null && form.PackageCardSaleEntity.length > 0) {
            List<PackageCardSaleEntity> list = new ArrayList<>();
            for (Integer ID : form.PackageCardSaleEntity) {
                PackageCardSaleEntity packageCardSaleEntity = new PackageCardSaleEntity();
                packageCardSaleEntity.EntityID = ID;
                packageCardSaleEntity.PackageCardID = packageCardEntity.ID;
                list.add(packageCardSaleEntity);
            }
            packageCardMapper.insertSaleEntity(list);
        }


        if (form.ModifyPriceEntity != null && form.ModifyPriceEntity.length > 0) {
            List<PackageCardSaleEntity> list = new ArrayList<>();
            for (Integer ID : form.ModifyPriceEntity) {
                PackageCardSaleEntity packageCardSaleEntity = new PackageCardSaleEntity();
                packageCardSaleEntity.EntityID = ID;
                packageCardSaleEntity.PackageCardID = packageCardEntity.ID;
                list.add(packageCardSaleEntity);
            }
            packageCardMapper.insertModifyPriceEntity(list);
        }

        packageCardMapper.deletePackageCardProductEntity(form.ID);
        packageCardMapper.deletePackageCardProjectEntity(form.ID);
        packageCardMapper.deletePackageCardTimeCardEntity(form.ID);
        packageCardMapper.deletePackageCardGeneralCardEntity(form.ID);
        packageCardMapper.deletePackageCardSavingCardEntity(form.ID);

        List<GoodsImageUrlForm> packageCardImageList = packageCardMapper.getPackageCardImageList(form.ID);
        if (packageCardImageList.size() > 0) {
            packageCardMapper.deletePackageCardImageByID(form.ID);
        }
        if (form.ImageList.size() > 0) {
            for (int a = 0; a < form.ImageList.size(); a++) {
                packageCardMapper.insertPackageCardImageURL(form.ID, form.ImageList.get(a).ImageUrl, a + 1);
            }
        }

        updatePackageCard(form.Goods, form.GoodsLargess, form.ID);

        return BaseOutput.success();
    }

    public void updatePackageCard(PackageCardGoodsAddForm Goods, PackageCardGoodsAddForm GoodsLargess, Integer ID) {
        List<PackageCardProductEntity> listProduct = new ArrayList<>();
        if (Goods.Product != null && Goods.Product.size() > 0) {
            for (PackageCardGoodAddForm packageCardGoodAddForm : Goods.Product) {
                PackageCardProductEntity packageCardProductEntity = new PackageCardProductEntity();
                packageCardProductEntity.ProductID = packageCardGoodAddForm.ID;
                packageCardProductEntity.PackageCardID = ID;
                packageCardProductEntity.Price = packageCardGoodAddForm.Price;
                packageCardProductEntity.Amount = packageCardGoodAddForm.Amount.intValue();
                packageCardProductEntity.TotalPrice = packageCardGoodAddForm.TotalPrice;
                packageCardProductEntity.IsLargess = false;
                listProduct.add(packageCardProductEntity);
            }
        }
        if (GoodsLargess.Product != null && GoodsLargess.Product.size() > 0) {
            for (PackageCardGoodAddForm packageCardGoodAddForm : GoodsLargess.Product) {
                PackageCardProductEntity packageCardProductEntity = new PackageCardProductEntity();
                packageCardProductEntity.ProductID = packageCardGoodAddForm.ID;
                packageCardProductEntity.PackageCardID = ID;
                packageCardProductEntity.Price = packageCardGoodAddForm.Price;
                packageCardProductEntity.Amount = packageCardGoodAddForm.Amount.intValue();
                packageCardProductEntity.TotalPrice = packageCardGoodAddForm.TotalPrice;
                packageCardProductEntity.IsLargess = true;
                listProduct.add(packageCardProductEntity);
            }
        }
        if (listProduct != null && listProduct.size() > 0) {
            List<List<PackageCardProductEntity>> subs = Lists.partition(listProduct, 20);
            for (List<PackageCardProductEntity> entityList : subs) {
                packageCardMapper.insertPackageCardProductEntity(entityList);
            }
        }

        List<PackageCardProjectEntity> listProject = new ArrayList<>();
        if (Goods.Project != null && Goods.Project.size() > 0) {
            for (PackageCardGoodAddForm packageCardGoodAddForm : Goods.Project) {
                PackageCardProjectEntity packageCardProjectEntity = new PackageCardProjectEntity();
                packageCardProjectEntity.ProjectID = packageCardGoodAddForm.ID;
                packageCardProjectEntity.PackageCardID = ID;
                packageCardProjectEntity.Price = packageCardGoodAddForm.Price;
                packageCardProjectEntity.Amount = packageCardGoodAddForm.Amount.intValue();
                packageCardProjectEntity.TotalPrice = packageCardGoodAddForm.TotalPrice;
                packageCardProjectEntity.IsLargess = false;
                listProject.add(packageCardProjectEntity);
            }
        }
        if (GoodsLargess.Project != null && GoodsLargess.Project.size() > 0) {
            for (PackageCardGoodAddForm packageCardGoodAddForm : GoodsLargess.Project) {
                PackageCardProjectEntity packageCardProjectEntity = new PackageCardProjectEntity();
                packageCardProjectEntity.ProjectID = packageCardGoodAddForm.ID;
                packageCardProjectEntity.PackageCardID = ID;
                packageCardProjectEntity.Price = packageCardGoodAddForm.Price;
                packageCardProjectEntity.Amount = packageCardGoodAddForm.Amount.intValue();
                packageCardProjectEntity.TotalPrice = packageCardGoodAddForm.TotalPrice;
                packageCardProjectEntity.IsLargess = true;
                listProject.add(packageCardProjectEntity);
            }
        }

        if (listProject != null && listProject.size() > 0) {
            List<List<PackageCardProjectEntity>> subs = Lists.partition(listProject, 20);
            for (List<PackageCardProjectEntity> entityList : subs) {
                packageCardMapper.insertPackageCardProjectEntity(entityList);
            }
        }


        List<PackageCardTimeCardEntity> listTimeCard = new ArrayList<>();
        if (Goods.TimeCard != null && Goods.TimeCard.size() > 0) {
            for (PackageCardGoodAddForm packageCardGoodAddForm : Goods.TimeCard) {
                PackageCardTimeCardEntity packageCardTimeCardEntity = new PackageCardTimeCardEntity();
                packageCardTimeCardEntity.TimeCardID = packageCardGoodAddForm.ID;
                packageCardTimeCardEntity.PackageCardID = ID;
                packageCardTimeCardEntity.Price = packageCardGoodAddForm.Price;
                packageCardTimeCardEntity.Amount = packageCardGoodAddForm.Amount.intValue();
                packageCardTimeCardEntity.TotalPrice = packageCardGoodAddForm.TotalPrice;
                packageCardTimeCardEntity.IsLargess = false;
                listTimeCard.add(packageCardTimeCardEntity);
            }
        }
        if (GoodsLargess.TimeCard != null && GoodsLargess.TimeCard.size() > 0) {
            for (PackageCardGoodAddForm packageCardGoodAddForm : GoodsLargess.TimeCard) {
                PackageCardTimeCardEntity packageCardTimeCardEntity = new PackageCardTimeCardEntity();
                packageCardTimeCardEntity.TimeCardID = packageCardGoodAddForm.ID;
                packageCardTimeCardEntity.PackageCardID = ID;
                packageCardTimeCardEntity.Price = packageCardGoodAddForm.Price;
                packageCardTimeCardEntity.Amount = packageCardGoodAddForm.Amount.intValue();
                packageCardTimeCardEntity.TotalPrice = packageCardGoodAddForm.TotalPrice;
                packageCardTimeCardEntity.IsLargess = true;
                listTimeCard.add(packageCardTimeCardEntity);
            }

        }

        if (listTimeCard != null && listTimeCard.size() > 0) {
            List<List<PackageCardTimeCardEntity>> subs = Lists.partition(listTimeCard, 20);
            for (List<PackageCardTimeCardEntity> entityList : subs) {
                packageCardMapper.insertPackageCardTimeCardEntity(entityList);
            }
        }

        List<PackageCardGeneralCardEntity> listGeneralCard = new ArrayList<>();
        if (Goods.GeneralCard != null && Goods.GeneralCard.size() > 0) {
            for (PackageCardGoodAddForm packageCardGoodAddForm : Goods.GeneralCard) {
                PackageCardGeneralCardEntity packageCardGeneralCardEntity = new PackageCardGeneralCardEntity();
                packageCardGeneralCardEntity.GeneralCardID = packageCardGoodAddForm.ID;
                packageCardGeneralCardEntity.PackageCardID = ID;
                packageCardGeneralCardEntity.Price = packageCardGoodAddForm.Price;
                packageCardGeneralCardEntity.Amount = packageCardGoodAddForm.Amount.intValue();
                packageCardGeneralCardEntity.TotalPrice = packageCardGoodAddForm.TotalPrice;
                packageCardGeneralCardEntity.IsLargess = false;
                listGeneralCard.add(packageCardGeneralCardEntity);
            }
        }
        if (GoodsLargess.GeneralCard != null && GoodsLargess.GeneralCard.size() > 0) {
            for (PackageCardGoodAddForm packageCardGoodAddForm : GoodsLargess.GeneralCard) {
                PackageCardGeneralCardEntity packageCardGeneralCardEntity = new PackageCardGeneralCardEntity();
                packageCardGeneralCardEntity.GeneralCardID = packageCardGoodAddForm.ID;
                packageCardGeneralCardEntity.PackageCardID = ID;
                packageCardGeneralCardEntity.Price = packageCardGoodAddForm.Price;
                packageCardGeneralCardEntity.Amount = packageCardGoodAddForm.Amount.intValue();
                packageCardGeneralCardEntity.TotalPrice = packageCardGoodAddForm.TotalPrice;
                packageCardGeneralCardEntity.IsLargess = true;
                listGeneralCard.add(packageCardGeneralCardEntity);
            }
        }
        if (listGeneralCard != null && listGeneralCard.size() > 0) {
            List<List<PackageCardGeneralCardEntity>> subs = Lists.partition(listGeneralCard, 20);
            for (List<PackageCardGeneralCardEntity> entityList : subs) {
                packageCardMapper.insertPackageCardGeneralCardEntity(entityList);
            }
        }


        List<PackageSavingCardEntity> listSavingCard = new ArrayList<>();
        if (Goods.SavingCard != null && Goods.SavingCard.size() > 0) {
            for (PackageCardGoodAddForm packageCardGoodAddForm : Goods.SavingCard) {
                PackageSavingCardEntity packageCardSavingCardEntity = new PackageSavingCardEntity();
                packageCardSavingCardEntity.SavingCardID = packageCardGoodAddForm.ID;
                packageCardSavingCardEntity.PackageCardID = ID;
                packageCardSavingCardEntity.Price = packageCardGoodAddForm.Price;
                packageCardSavingCardEntity.Amount = packageCardGoodAddForm.Amount.intValue();
                packageCardSavingCardEntity.TotalPrice = packageCardGoodAddForm.TotalPrice;
                packageCardSavingCardEntity.IsLargess = false;
                listSavingCard.add(packageCardSavingCardEntity);
            }
        }
        if (GoodsLargess.SavingCard != null && GoodsLargess.SavingCard.size() > 0) {
            for (PackageCardGoodAddForm packageCardGoodAddForm : GoodsLargess.SavingCard) {
                PackageSavingCardEntity packageCardSavingCardEntity = new PackageSavingCardEntity();
                packageCardSavingCardEntity.SavingCardID = packageCardGoodAddForm.ID;
                packageCardSavingCardEntity.PackageCardID = ID;
                packageCardSavingCardEntity.Price = packageCardGoodAddForm.Price;
                packageCardSavingCardEntity.Amount = 1;
                packageCardSavingCardEntity.TotalPrice = packageCardGoodAddForm.TotalPrice;
                packageCardSavingCardEntity.IsLargess = true;
                listSavingCard.add(packageCardSavingCardEntity);
            }
        }

        if (listSavingCard != null && listSavingCard.size() > 0) {
            List<List<PackageSavingCardEntity>> subs = Lists.partition(listSavingCard, 20);
            for (List<PackageSavingCardEntity> entityList : subs) {
                packageCardMapper.insertPackageSavingCardEntity(entityList);
            }
        }

    }


    public BaseOutput packageCardSaleEntity(PackageCardSaleEntityQueryForm form) {
        return BaseOutputForm.success(packageCardMapper.packageCardSaleEntity(form.ID));
    }

    public BaseOutput packageCardModifyPriceEntity(PackageCardSaleEntityQueryForm form) {
        return BaseOutputForm.success(packageCardMapper.packageCardModifyPriceEntity(form.ID));
    }

    public BigDecimal getTotal(PackageCardGoodsAddForm form) {
        BigDecimal total = BigDecimal.ZERO;

        List<PackageCardGoodAddForm> list = new ArrayList<>();
        if (form.Product != null && form.Product.size() > 0)
            list.addAll(form.Product);
        if (form.Project != null && form.Project.size() > 0)
            list.addAll(form.Project);
        if (form.GeneralCard != null && form.GeneralCard.size() > 0)
            list.addAll(form.GeneralCard);
        if (form.TimeCard != null && form.TimeCard.size() > 0)
            list.addAll(form.TimeCard);
        if (form.SavingCard != null && form.SavingCard.size() > 0)
            list.addAll(form.SavingCard);
        for (PackageCardGoodAddForm packageCardGoodAddForm : list) {
            if (packageCardGoodAddForm.TotalPrice != null)
                total = total.add(packageCardGoodAddForm.TotalPrice);
        }
        return total;
    }

    public BaseOutput move(GoodsMoveForm form) {

        LinkedList<Integer> linkedList = packageCardMapper.getASCList(form.MoveID, form.ID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<TbPackageCardImageEntity> list = new ArrayList<>();
        for (Integer ID : linkedList) {
            TbPackageCardImageEntity packageCardImageEntity = new TbPackageCardImageEntity();
            packageCardImageEntity.ID = ID;
            packageCardImageEntity.PackageCardID = form.ID;
            packageCardImageEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(packageCardImageEntity);
        }

        packageCardMapper.updateSequence(list);
        return BaseOutput.success();
    }

    public BaseOutput allPackageCard() {
        List<PackageCardOutputForm> list = packageCardMapper.allPackageCard();
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput bitchUpdatePackageCardSaleEntity(GoodsBitchUpdateForm form) {
        packageCardMapper.deleteBitchSaleEntity(form.GoodsID);
        List<PackageCardSaleEntity> list = new ArrayList<>();
        for (Integer goodsID : form.GoodsID) {
            for (Integer entityID : form.EntityID) {
                PackageCardSaleEntity packageCardSaleEntity = new PackageCardSaleEntity();
                packageCardSaleEntity.PackageCardID = goodsID;
                packageCardSaleEntity.EntityID = entityID;
                list.add(packageCardSaleEntity);
            }
        }

        if (list.size() > 0)
            packageCardMapper.insertSaleEntity(list);

        return BaseOutput.success();
    }


}