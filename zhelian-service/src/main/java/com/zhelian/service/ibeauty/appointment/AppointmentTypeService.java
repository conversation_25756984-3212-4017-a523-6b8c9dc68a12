package com.zhelian.service.ibeauty.appointment;

import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.AppointmentTypeMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.AppointmentTypeEntity;
import com.zhelian.model.ibeauty.form.input.AppointmentTypeAddForm;
import com.zhelian.model.ibeauty.form.input.AppointmentTypeMoveForm;
import com.zhelian.model.ibeauty.form.input.AppointmentTypeQueryForm;
import com.zhelian.model.ibeauty.form.output.AppointmentTypeOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

@Service
public class AppointmentTypeService {

    @Autowired
    AppointmentTypeMapper appointmentTypeMapper;

    public BaseOutput all(AppointmentTypeQueryForm form) {
        List<AppointmentTypeOutputForm> list = appointmentTypeMapper.all(form);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput create(AppointmentTypeAddForm form) {
        Integer count = appointmentTypeMapper.getAppointmentTypeCount();

        AppointmentTypeEntity appointmentTypeEntity = new AppointmentTypeEntity();
        appointmentTypeEntity.Name = form.Name;
        appointmentTypeEntity.Sequence = count + 1;
        appointmentTypeEntity.Active = true;
        appointmentTypeMapper.create(appointmentTypeEntity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput update(AppointmentTypeAddForm form) {
        AppointmentTypeEntity appointmentTypeEntity = new AppointmentTypeEntity();
        appointmentTypeEntity.ID = form.ID;
        appointmentTypeEntity.Name = form.Name;
        appointmentTypeEntity.Active = form.Active;
        appointmentTypeMapper.update(appointmentTypeEntity);
        return BaseOutput.success();
    }

    public BaseOutput move(AppointmentTypeMoveForm form) {
        LinkedList<Integer> linkedList = appointmentTypeMapper.getASCList(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<AppointmentTypeEntity> list = new ArrayList<>();
        for (Integer ID : linkedList) {
            AppointmentTypeEntity appointmentTypeEntity = new AppointmentTypeEntity();
            appointmentTypeEntity.ID = ID;
            appointmentTypeEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(appointmentTypeEntity);
        }
        appointmentTypeMapper.updateSequence(list);
        return BaseOutput.success();
    }
}
