package com.zhelian.service.ibeauty.treatHandler;


import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.ProjectTreatHandlerMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.ProjectTreatHandlerEntity;
import com.zhelian.model.ibeauty.entity.ProjectTreatHandlerJobTypeEntity;
import com.zhelian.model.ibeauty.form.input.SaleHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerMoveForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerUpdateForm;
import com.zhelian.model.ibeauty.form.output.SaleHandlerOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * @Description:
 * @Param: No such property: code for class: Script1
 * @returns:
 * @Author: xuehao
 * @Date: 2020/3/31 13:43
 */
@Service
public class ProjectTreatHandlerService {

    @Autowired
    ProjectTreatHandlerMapper projectTreatHandlerMapper;


    public BaseOutput allProjectTreatHandler(SaleHandlerQueryForm form) {

        List<SaleHandlerOutputForm> list = projectTreatHandlerMapper.allProjectTreatHandler(form.Name, form.EntityID, form.Active);
        return BaseOutputForm.success(list);

    }

    @Transactional
    public BaseOutput createProjectTreatHandler(SaleHandlerAddForm form) {
        Integer Sequence = projectTreatHandlerMapper.getMaxSequence();
        ProjectTreatHandlerEntity projectTreatHandlerEntity = new ProjectTreatHandlerEntity();
        projectTreatHandlerEntity.Name = form.Name;
        projectTreatHandlerEntity.EntityID = form.EntityID;
        projectTreatHandlerEntity.Sequence = Sequence != null ? Sequence + 1 : 1;

        projectTreatHandlerMapper.createProjectTreatHandler(projectTreatHandlerEntity);

        insertHandlerJobType(form.JobType, projectTreatHandlerEntity.ID);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateProjectTreatHandler(SaleHandlerUpdateForm form) {

        ProjectTreatHandlerEntity projectTreatHandlerEntity = new ProjectTreatHandlerEntity();
        projectTreatHandlerEntity.Name = form.Name;
        projectTreatHandlerEntity.ID = form.ID;
        projectTreatHandlerEntity.EntityID = form.EntityID;
        projectTreatHandlerEntity.Active = form.Active;

        projectTreatHandlerMapper.updateProjectTreatHandler(projectTreatHandlerEntity);

        projectTreatHandlerMapper.deleteTreatHandlerJobType(form.ID);

        insertHandlerJobType(form.JobType, form.ID);

        return BaseOutput.success();
    }


    public void insertHandlerJobType(Integer[] JobType, Integer ID) {
        List<ProjectTreatHandlerJobTypeEntity> list = new ArrayList<>();
        for (Integer typeID : JobType) {
            ProjectTreatHandlerJobTypeEntity projectTreatHandlerJobTypeEntity = new ProjectTreatHandlerJobTypeEntity();
            projectTreatHandlerJobTypeEntity.ProjectTreatHandlerID = ID;
            projectTreatHandlerJobTypeEntity.JobTypeID = typeID;
            list.add(projectTreatHandlerJobTypeEntity);
        }
        projectTreatHandlerMapper.insertHandlerJobType(list);
    }

    public BaseOutput moveProjectTreatHandler(SaleHandlerMoveForm form) {
        //重新排序
        LinkedList<Integer> linkedList = projectTreatHandlerMapper.getIDOrderBySequenceASC(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<ProjectTreatHandlerEntity> list = new ArrayList<>();

        for (Integer ID : linkedList) {
            ProjectTreatHandlerEntity projectTreatHandlerEntity = new ProjectTreatHandlerEntity();
            projectTreatHandlerEntity.ID = ID;
            projectTreatHandlerEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(projectTreatHandlerEntity);
        }
        projectTreatHandlerMapper.updateSequence(list);

        return BaseOutput.success();
    }

}