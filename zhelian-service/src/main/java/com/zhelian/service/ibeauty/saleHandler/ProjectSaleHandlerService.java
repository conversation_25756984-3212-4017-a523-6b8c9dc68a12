package com.zhelian.service.ibeauty.saleHandler;


import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.ProjectSaleHandlerMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.ProjectSaleHandlerEntity;
import com.zhelian.model.ibeauty.entity.ProjectSaleHandlerJobTypeEntity;
import com.zhelian.model.ibeauty.form.input.SaleHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerMoveForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerUpdateForm;
import com.zhelian.model.ibeauty.form.output.SaleHandlerOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * @Description:
 * @Param: No such property: code for class: Script1
 * @returns:
 * @Author: xuehao
 * @Date: 2020/3/30 16:22
 */
@Service
public class ProjectSaleHandlerService {

    @Autowired
    ProjectSaleHandlerMapper projectSaleHandlerMapper;


    public BaseOutput allProjectSaleHandler(SaleHandlerQueryForm form) {

        List<SaleHandlerOutputForm> list = projectSaleHandlerMapper.allProjectSaleHandler(form.Name, form.EntityID, form.Active);
        return BaseOutputForm.success(list);

    }

    @Transactional
    public BaseOutput createProjectSaleHandler(SaleHandlerAddForm form) {
        Integer Sequence = projectSaleHandlerMapper.getMaxSequence();
        ProjectSaleHandlerEntity projectSaleHandlerEntity = new ProjectSaleHandlerEntity();
        projectSaleHandlerEntity.Name = form.Name;
        projectSaleHandlerEntity.EntityID = form.EntityID;
        projectSaleHandlerEntity.Sequence = Sequence != null ? Sequence + 1 : 1;

        projectSaleHandlerMapper.createProjectSaleHandler(projectSaleHandlerEntity);

        insertSaleHandlerJobType(form.JobType, projectSaleHandlerEntity.ID);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateProjectSaleHandler(SaleHandlerUpdateForm form) {

        ProjectSaleHandlerEntity projectSaleHandlerEntity = new ProjectSaleHandlerEntity();
        projectSaleHandlerEntity.Name = form.Name;
        projectSaleHandlerEntity.ID = form.ID;
        projectSaleHandlerEntity.EntityID = form.EntityID;
        projectSaleHandlerEntity.Active = form.Active;

        projectSaleHandlerMapper.updateProjectSaleHandler(projectSaleHandlerEntity);

        projectSaleHandlerMapper.deleteSaleHandlerJobType(form.ID);

        insertSaleHandlerJobType(form.JobType, form.ID);

        return BaseOutput.success();
    }


    public void insertSaleHandlerJobType(Integer[] JobType, Integer ID) {
        List<ProjectSaleHandlerJobTypeEntity> list = new ArrayList<>();
        for (Integer typeID : JobType) {
            ProjectSaleHandlerJobTypeEntity projectSaleHandlerJobTypeEntity = new ProjectSaleHandlerJobTypeEntity();
            projectSaleHandlerJobTypeEntity.ProjectSaleHandlerID = ID;
            projectSaleHandlerJobTypeEntity.JobTypeID = typeID;
            list.add(projectSaleHandlerJobTypeEntity);
        }
        projectSaleHandlerMapper.insertProjectSaleHandlerJobType(list);
    }

    public BaseOutput moveProjectSaleHandler(SaleHandlerMoveForm form) {
        //重新排序
        LinkedList<Integer> linkedList = projectSaleHandlerMapper.getIDOrderBySequenceASC(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<ProjectSaleHandlerEntity> list = new ArrayList<>();

        for (Integer ID : linkedList) {
            ProjectSaleHandlerEntity projectSaleHandlerEntity = new ProjectSaleHandlerEntity();
            projectSaleHandlerEntity.ID = ID;
            projectSaleHandlerEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(projectSaleHandlerEntity);
        }
        projectSaleHandlerMapper.updateSequence(list);

        return BaseOutput.success();
    }

}