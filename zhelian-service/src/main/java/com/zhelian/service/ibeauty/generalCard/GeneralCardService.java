package com.zhelian.service.ibeauty.generalCard;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.GeneralCardMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.GeneralCardEntity;
import com.zhelian.model.ibeauty.entity.GeneralCardProjectEntity;
import com.zhelian.model.ibeauty.entity.GeneralCardSaleEntity;
import com.zhelian.model.ibeauty.entity.TbGeneralCardImageEntity;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.ibeauty.form.output.AllGeneralCardOutputForm;
import com.zhelian.model.ibeauty.form.output.GeneralCardDetailsOutputForm;
import com.zhelian.model.ibeauty.form.output.GeneralCardOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

@Service
public class GeneralCardService {

    @Autowired
    GeneralCardMapper generalCardMapper;

    public BaseOutput generalCardList(GeneralCardQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<GeneralCardOutputForm> list = generalCardMapper.generalCardList(form.Name, form.GeneralCardCategoryID, form.IsAllowSell);
        return BasePageInfo.success(list);
    }

    public BaseOutput getGeneralCardDetails(GeneralCardQueryEntityForm form) {
        GeneralCardDetailsOutputForm productDetails = generalCardMapper.getGeneralCardDetails(form.ID);
        return BaseOutputForm.success(productDetails);
    }


    public BaseOutput generalCardSaleEntity(GeneralCardQueryEntityForm form) {
        return BaseOutputForm.success(generalCardMapper.generalCardSaleEntity(form));
    }

    public BaseOutput generalCardConsumeEntity(GeneralCardQueryEntityForm form) {
        return BaseOutputForm.success(generalCardMapper.generalCardConsumeEntity(form));
    }

    public BaseOutput generalCardModifyPriceEntity(GeneralCardQueryEntityForm form) {
        return BaseOutputForm.success(generalCardMapper.generalCardModifyPriceEntity(form));
    }

    public BaseOutput generalCardProject(GeneralCardQueryEntityForm form) {
        return BaseOutputForm.success(generalCardMapper.generalCardProject(form));
    }

    @Transactional
    public BaseOutput create(GeneralCardAddForm form) {
        GeneralCardEntity generalCardEntity = new GeneralCardEntity();
        generalCardEntity.Name = form.Name;
        generalCardEntity.Alias = form.Alias;
        generalCardEntity.GeneralCardCategoryID = form.GeneralCardCategoryID;
        generalCardEntity.Price = form.Price;
        generalCardEntity.Amount = form.Amount;
        generalCardEntity.SaleStartDate = form.SaleStartDate;
        generalCardEntity.SaleEndingDate = form.SaleEndingDate;
        generalCardEntity.ValidType = form.ValidType;
        generalCardEntity.Remark = form.Remark;
        generalCardEntity.TreatProjectNumber = form.TreatProjectNumber;
        generalCardEntity.Memo = form.Memo;
        if (generalCardEntity.ValidType == 3) {
            form.ValidDay = null;
        }
        if (form.ValidDay != null) {
            generalCardEntity.ValidDay = form.ValidDay;
        }
        generalCardEntity.IsAllowSell = form.IsAllowSell;
        generalCardEntity.IsAllowLargess = form.IsAllowLargess;
        generalCardMapper.addGeneralCard(generalCardEntity);
        addGeneralCardProjectAndConsumeAndSale(form, generalCardEntity);
        if (form.ImageList.size() > 0) {
            for (int a = 0; a < form.ImageList.size(); a++) {
                generalCardMapper.insertGeneralCardImageURL(generalCardEntity.ID, form.ImageList.get(a).ImageUrl, a + 1);
            }
        }
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput update(GeneralCardUpdateForm form) {
        GeneralCardEntity generalCardEntity = new GeneralCardEntity();
        generalCardEntity.ID = form.ID;
        generalCardEntity.Name = form.Name;
        generalCardEntity.Alias = form.Alias;
        generalCardEntity.GeneralCardCategoryID = form.GeneralCardCategoryID;
        generalCardEntity.Price = form.Price;
        generalCardEntity.Amount = form.Amount;
        generalCardEntity.SaleStartDate = form.SaleStartDate;
        generalCardEntity.SaleEndingDate = form.SaleEndingDate;
        generalCardEntity.ValidType = form.ValidType;
        generalCardEntity.Remark = form.Remark;
        generalCardEntity.TreatProjectNumber = form.TreatProjectNumber;
        generalCardEntity.Memo = form.Memo;
        if (generalCardEntity.ValidType == 3) {
            form.ValidDay = null;
        }
        if (form.ValidDay != null) {
            generalCardEntity.ValidDay = form.ValidDay;
        }
        generalCardEntity.IsAllowSell = form.IsAllowSell;
        generalCardEntity.IsAllowLargess = form.IsAllowLargess;
        generalCardMapper.updateGeneralCard(generalCardEntity);
        generalCardMapper.deleteGeneralCardProject(form.ID);
        generalCardMapper.deleteGeneralCardConsume(form.ID);
        generalCardMapper.deleteGeneralCardSale(form.ID);
        generalCardMapper.deleteGeneralCardModify(form.ID);
        addGeneralCardProjectAndConsumeAndSale(form, generalCardEntity);

        List<GoodsImageUrlForm> generalCardImageList = generalCardMapper.getGeneralCardImageList(form.ID);
        if (generalCardImageList.size() > 0) {
            generalCardMapper.deleteGeneralCardImageByID(form.ID);
        }

        if (form.ImageList.size() > 0) {
            for (int a = 0; a < form.ImageList.size(); a++) {
                generalCardMapper.insertGeneralCardImageURL(form.ID, form.ImageList.get(a).ImageUrl, a + 1);
            }
        }
        return BaseOutput.success();
    }

    public BaseOutput findCategoryAndProject() {
        return BaseOutputForm.success(ListUtil.listToTree(JSONArray.parseArray(JSON.toJSONString(generalCardMapper.findCategoryAndProject(), JSONWriter.Feature.WriteMapNullValue)), "ID", "ParentID", "Child"));
    }

    private void addGeneralCardProjectAndConsumeAndSale(GeneralCardAddForm form, GeneralCardEntity generalCardEntity) {
        List<GeneralCardSaleEntity> saleList = new ArrayList<>();
        for (Integer EntityID : form.GeneralCardSaleEntity) {
            GeneralCardSaleEntity entity = new GeneralCardSaleEntity();
            entity.EntityID = EntityID;
            entity.GeneralCardID = generalCardEntity.ID;
            saleList.add(entity);
        }
        if (saleList.size() > 0)
            generalCardMapper.addGeneralCardSale(saleList);
        List<GeneralCardSaleEntity> consumeList = new ArrayList<>();
        for (Integer EntityID : form.GeneralCardConsumeEntity) {
            GeneralCardSaleEntity entity = new GeneralCardSaleEntity();
            entity.EntityID = EntityID;
            entity.GeneralCardID = generalCardEntity.ID;
            consumeList.add(entity);
        }
        if (consumeList.size() > 0)
            generalCardMapper.addGeneralCardConsume(consumeList);
        List<GeneralCardSaleEntity> modifyList = new ArrayList<>();
        for (Integer EntityID : form.ModifyPriceEntity) {
            GeneralCardSaleEntity entity = new GeneralCardSaleEntity();
            entity.EntityID = EntityID;
            entity.GeneralCardID = generalCardEntity.ID;
            modifyList.add(entity);
        }
        if (modifyList.size() > 0)
            generalCardMapper.addGeneralCardModifyPrice(modifyList);
        List<GeneralCardProjectEntity> projectList = new ArrayList<>();
        for (GeneralCardProjectEntity generalCardProjectEntity : form.GeneralCardProject) {
            GeneralCardProjectEntity entity = new GeneralCardProjectEntity();
            entity.ProjectID = generalCardProjectEntity.ProjectID;
            entity.ConsumeAmount = generalCardProjectEntity.ConsumeAmount;
            entity.LimitConsumeTimes = generalCardProjectEntity.LimitConsumeTimes;
            entity.GeneralCardID = generalCardEntity.ID;
            projectList.add(entity);
        }

        if (projectList != null && projectList.size() > 0) {
            List<List<GeneralCardProjectEntity>> subs = Lists.partition(projectList, 20);
            for (List<GeneralCardProjectEntity> entityList : subs) {
                generalCardMapper.addGeneralCardProject(entityList);
            }
        }

    }

    public BaseOutput allGeneralCard() {
        List<AllGeneralCardOutputForm> list = generalCardMapper.allGeneralCard();
        return BaseOutputForm.success(list);
    }

    public BaseOutput move(GoodsMoveForm form) {

        LinkedList<Integer> linkedList = generalCardMapper.getASCList(form.MoveID, form.ID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<TbGeneralCardImageEntity> list = new ArrayList<>();
        for (Integer ID : linkedList) {
            TbGeneralCardImageEntity generalCardImageEntity = new TbGeneralCardImageEntity();
            generalCardImageEntity.ID = ID;
            generalCardImageEntity.GeneralCardID = form.ID;
            generalCardImageEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(generalCardImageEntity);
        }

        generalCardMapper.updateSequence(list);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput bitchUpdateGeneralCardSaleEntity(GoodsBitchUpdateForm form) {
        generalCardMapper.deleteBitchSaleEntity(form.GoodsID);
        List<GeneralCardSaleEntity> list = new ArrayList<>();
        for (Integer goodsID : form.GoodsID) {
            for (Integer entityID : form.EntityID) {
                GeneralCardSaleEntity saleEntity = new GeneralCardSaleEntity();
                saleEntity.GeneralCardID = goodsID;
                saleEntity.EntityID = entityID;
                list.add(saleEntity);
            }
        }

        if (list.size() > 0)
            generalCardMapper.addGeneralCardSale(list);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput bitchUpdateGeneralCardConsumeEntity(GoodsBitchUpdateForm form) {
        generalCardMapper.deleteBitchConsumeEntity(form.GoodsID);
        List<GeneralCardSaleEntity> list = new ArrayList<>();
        for (Integer goodsID : form.GoodsID) {
            for (Integer entityID : form.EntityID) {
                GeneralCardSaleEntity saleEntity = new GeneralCardSaleEntity();
                saleEntity.GeneralCardID = goodsID;
                saleEntity.EntityID = entityID;
                list.add(saleEntity);
            }
        }

        if (list.size() > 0)
            generalCardMapper.addGeneralCardConsume(list);

        return BaseOutput.success();
    }
}
