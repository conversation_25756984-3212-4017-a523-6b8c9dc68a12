package com.zhelian.service.ibeauty.saleTimeCardCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.SalePackageCardTimeCardHandlerCommissionMapper;
import com.zhelian.mapper.ibeauty.SaleTimeCardHandlerCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SalePackageCardTimeCardHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SalePackageCardTimeCardHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleTimeCardHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleTimeCardHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardHandlersCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.SaleTimeCardHandlerCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class SaleTimeCardHandlerCommissionService {

    @Autowired
    SaleTimeCardHandlerCommissionMapper saleTimeCardHandlerCommissionMapper;

    @Autowired
    SalePackageCardTimeCardHandlerCommissionMapper salePackageCardTimeCardHandlerCommissionMapper;


    public BaseOutput allSaleTimeCardHandlerCommission(SaleTimeCardHandlerCommissionQueryForm form) {
        List<SaleTimeCardHandlerCommissionOutputForm> list = saleTimeCardHandlerCommissionMapper.allSaleTimeCardHandlerCommission(form.EntityID, form.TimeCardID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSalePackageCardTimeCardHandlerCommission(SaleTimeCardHandlerCommissionQueryForm form) {
        List<SaleTimeCardHandlerCommissionOutputForm> list = salePackageCardTimeCardHandlerCommissionMapper.allSalePackageCardTimeCardHandlerCommission(form.EntityID, form.TimeCardID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleTimeCardHandlerCommission(SaleTimeCardHandlersCommissionAddForm form) {

        saleTimeCardHandlerCommissionMapper.deleteSaleTimeCardHandler(form.EntityID, form.TimeCardID);
        saleTimeCardHandlerCommissionMapper.deleteSaleTimeCardHandlerJobType(form.EntityID, form.TimeCardID);
        List saleTimeCardHandlerCommissionList = new ArrayList();
        List saleTimeCardHandlerJobTypeCommissionList = new ArrayList<>();
        if (form.TimeCardHandler != null && form.TimeCardHandler.size() > 0) {
            for (SaleTimeCardHandlerCommissionAddForm saleTimeCardHandlerAddForm : form.TimeCardHandler) {
                if (saleTimeCardHandlerAddForm.PayRate != null || saleTimeCardHandlerAddForm.PayFixed != null ||
                        saleTimeCardHandlerAddForm.SavingCardRate != null || saleTimeCardHandlerAddForm.SavingCardFixed != null ||
                        saleTimeCardHandlerAddForm.SavingCardLargessRate != null || saleTimeCardHandlerAddForm.SavingCardLargessFixed != null ||
                        saleTimeCardHandlerAddForm.SpecialBenefit != null) {
                    SaleTimeCardHandlerCommissionEntity saleTimeCardHandlerCommissionEntity = new SaleTimeCardHandlerCommissionEntity();
                    saleTimeCardHandlerCommissionEntity.EntityID = form.EntityID;
                    saleTimeCardHandlerCommissionEntity.TimeCardID = form.TimeCardID;
                    saleTimeCardHandlerCommissionEntity.TimeCardSaleHandlerID = saleTimeCardHandlerAddForm.SaleHandlerID;
                    saleTimeCardHandlerCommissionEntity.PayFixed = saleTimeCardHandlerAddForm.PayFixed;
                    saleTimeCardHandlerCommissionEntity.PayRate = saleTimeCardHandlerAddForm.PayRate;
                    saleTimeCardHandlerCommissionEntity.SavingCardFixed = saleTimeCardHandlerAddForm.SavingCardFixed;
                    saleTimeCardHandlerCommissionEntity.SavingCardRate = saleTimeCardHandlerAddForm.SavingCardRate;
                    saleTimeCardHandlerCommissionEntity.SavingCardLargessFixed = saleTimeCardHandlerAddForm.SavingCardLargessFixed;
                    saleTimeCardHandlerCommissionEntity.SavingCardLargessRate = saleTimeCardHandlerAddForm.SavingCardLargessRate;
                    saleTimeCardHandlerCommissionEntity.SpecialBenefit = saleTimeCardHandlerAddForm.SpecialBenefit;
                    saleTimeCardHandlerCommissionList.add(saleTimeCardHandlerCommissionEntity);
                }

                if (saleTimeCardHandlerAddForm.HandlerJobTypeCommission != null && saleTimeCardHandlerAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (SaleTimeCardCategoryHandlerJobTypeCommissionAddForm saleTimeCardCategoryHandlerJobTypeCommissionAddForm : saleTimeCardHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleTimeCardCategoryHandlerJobTypeCommissionAddForm.PayRate != null || saleTimeCardCategoryHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardRate != null || saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed != null ||
                                saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null || saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed != null ||
                                saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SaleTimeCardHandlerJobTypeCommissionEntity saleTimeCardHandlerJobTypeCommissionEntity = new SaleTimeCardHandlerJobTypeCommissionEntity();
                            saleTimeCardHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            saleTimeCardHandlerJobTypeCommissionEntity.TimeCardID = form.TimeCardID;
                            saleTimeCardHandlerJobTypeCommissionEntity.TimeCardSaleHandlerID = saleTimeCardHandlerAddForm.SaleHandlerID;
                            saleTimeCardHandlerJobTypeCommissionEntity.JobTypeID = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.JobTypeID;
                            saleTimeCardHandlerJobTypeCommissionEntity.PayFixed = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.PayFixed;
                            saleTimeCardHandlerJobTypeCommissionEntity.PayRate = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.PayRate;
                            saleTimeCardHandlerJobTypeCommissionEntity.SavingCardFixed = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed;
                            saleTimeCardHandlerJobTypeCommissionEntity.SavingCardRate = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardRate;
                            saleTimeCardHandlerJobTypeCommissionEntity.SavingCardLargessFixed = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed;
                            saleTimeCardHandlerJobTypeCommissionEntity.SavingCardLargessRate = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            saleTimeCardHandlerJobTypeCommissionEntity.SpecialBenefit = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            saleTimeCardHandlerJobTypeCommissionList.add(saleTimeCardHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
        }

        salePackageCardTimeCardHandlerCommissionMapper.deleteSalePackageCardTimeCardHandler(form.EntityID, form.TimeCardID);
        salePackageCardTimeCardHandlerCommissionMapper.deleteSalePackageCardTimeCardHandlerJobType(form.EntityID, form.TimeCardID);
        List salePackageCardTimeCardHandlerCommissionList = new ArrayList();
        List salePackageCardTimeCardHandlerJobTypeCommissionList = new ArrayList<>();
        if (form.PackageCardTimeCardHandler != null && form.PackageCardTimeCardHandler.size() > 0) {
            for (SaleTimeCardHandlerCommissionAddForm saleTimeCardHandlerAddForm : form.PackageCardTimeCardHandler) {
                if (saleTimeCardHandlerAddForm.PayRate != null || saleTimeCardHandlerAddForm.PayFixed != null ||
                        saleTimeCardHandlerAddForm.SavingCardRate != null || saleTimeCardHandlerAddForm.SavingCardFixed != null ||
                        saleTimeCardHandlerAddForm.SavingCardLargessRate != null || saleTimeCardHandlerAddForm.SavingCardLargessFixed != null ||
                        saleTimeCardHandlerAddForm.SpecialBenefit != null) {
                    SalePackageCardTimeCardHandlerCommissionEntity salePackageCardTimeCardHandlerCommissionEntity = new SalePackageCardTimeCardHandlerCommissionEntity();
                    salePackageCardTimeCardHandlerCommissionEntity.EntityID = form.EntityID;
                    salePackageCardTimeCardHandlerCommissionEntity.TimeCardID = form.TimeCardID;
                    salePackageCardTimeCardHandlerCommissionEntity.PackageCardSaleHandlerID = saleTimeCardHandlerAddForm.SaleHandlerID;
                    salePackageCardTimeCardHandlerCommissionEntity.PayFixed = saleTimeCardHandlerAddForm.PayFixed;
                    salePackageCardTimeCardHandlerCommissionEntity.PayRate = saleTimeCardHandlerAddForm.PayRate;
                    salePackageCardTimeCardHandlerCommissionEntity.SavingCardFixed = saleTimeCardHandlerAddForm.SavingCardFixed;
                    salePackageCardTimeCardHandlerCommissionEntity.SavingCardRate = saleTimeCardHandlerAddForm.SavingCardRate;
                    salePackageCardTimeCardHandlerCommissionEntity.SavingCardLargessFixed = saleTimeCardHandlerAddForm.SavingCardLargessFixed;
                    salePackageCardTimeCardHandlerCommissionEntity.SavingCardLargessRate = saleTimeCardHandlerAddForm.SavingCardLargessRate;
                    salePackageCardTimeCardHandlerCommissionEntity.SpecialBenefit = saleTimeCardHandlerAddForm.SpecialBenefit;
                    salePackageCardTimeCardHandlerCommissionList.add(salePackageCardTimeCardHandlerCommissionEntity);
                }

                if (saleTimeCardHandlerAddForm.HandlerJobTypeCommission != null && saleTimeCardHandlerAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (SaleTimeCardCategoryHandlerJobTypeCommissionAddForm saleTimeCardCategoryHandlerJobTypeCommissionAddForm : saleTimeCardHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleTimeCardCategoryHandlerJobTypeCommissionAddForm.PayRate != null || saleTimeCardCategoryHandlerJobTypeCommissionAddForm.PayFixed != null ||
                                saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardRate != null || saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed != null ||
                                saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null || saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed != null ||
                                saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit != null) {
                            SalePackageCardTimeCardHandlerJobTypeCommissionEntity salePackageCardTimeCardHandlerJobTypeCommissionEntity = new SalePackageCardTimeCardHandlerJobTypeCommissionEntity();
                            salePackageCardTimeCardHandlerJobTypeCommissionEntity.EntityID = form.EntityID;
                            salePackageCardTimeCardHandlerJobTypeCommissionEntity.TimeCardID = form.TimeCardID;
                            salePackageCardTimeCardHandlerJobTypeCommissionEntity.PackageCardSaleHandlerID = saleTimeCardHandlerAddForm.SaleHandlerID;
                            salePackageCardTimeCardHandlerJobTypeCommissionEntity.JobTypeID = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.JobTypeID;
                            salePackageCardTimeCardHandlerJobTypeCommissionEntity.PayFixed = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.PayFixed;
                            salePackageCardTimeCardHandlerJobTypeCommissionEntity.PayRate = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.PayRate;
                            salePackageCardTimeCardHandlerJobTypeCommissionEntity.SavingCardFixed = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardFixed;
                            salePackageCardTimeCardHandlerJobTypeCommissionEntity.SavingCardRate = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardRate;
                            salePackageCardTimeCardHandlerJobTypeCommissionEntity.SavingCardLargessFixed = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessFixed;
                            salePackageCardTimeCardHandlerJobTypeCommissionEntity.SavingCardLargessRate = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            salePackageCardTimeCardHandlerJobTypeCommissionEntity.SpecialBenefit = saleTimeCardCategoryHandlerJobTypeCommissionAddForm.SpecialBenefit;
                            salePackageCardTimeCardHandlerJobTypeCommissionList.add(salePackageCardTimeCardHandlerJobTypeCommissionEntity);
                        }
                    }
                }
            }
        }
        if (saleTimeCardHandlerCommissionList != null && saleTimeCardHandlerCommissionList.size() > 0) {
            List<List<SaleTimeCardHandlerCommissionEntity>> subs = Lists.partition(saleTimeCardHandlerCommissionList, 20);
            for (List<SaleTimeCardHandlerCommissionEntity> entityList : subs) {
                saleTimeCardHandlerCommissionMapper.insertSaleTimeCardHandlerCommission(entityList);
            }
        }
        if (saleTimeCardHandlerJobTypeCommissionList != null && saleTimeCardHandlerJobTypeCommissionList.size() > 0) {
            List<List<SaleTimeCardHandlerJobTypeCommissionEntity>> subs = Lists.partition(saleTimeCardHandlerJobTypeCommissionList, 20);
            for (List<SaleTimeCardHandlerJobTypeCommissionEntity> entityList : subs) {
                saleTimeCardHandlerCommissionMapper.insertSaleTimeCardHandlerJobTypeCommission(entityList);
            }
        }
        if (salePackageCardTimeCardHandlerCommissionList != null && salePackageCardTimeCardHandlerCommissionList.size() > 0) {
            List<List<SalePackageCardTimeCardHandlerCommissionEntity>> subs = Lists.partition(salePackageCardTimeCardHandlerCommissionList, 20);
            for (List<SalePackageCardTimeCardHandlerCommissionEntity> entityList : subs) {
                salePackageCardTimeCardHandlerCommissionMapper.insertSalePackageCardTimeCardHandlerCommission(entityList);
            }
        }
        if (salePackageCardTimeCardHandlerJobTypeCommissionList != null && salePackageCardTimeCardHandlerJobTypeCommissionList.size() > 0) {
            List<List<SalePackageCardTimeCardHandlerJobTypeCommissionEntity>> subs = Lists.partition(salePackageCardTimeCardHandlerJobTypeCommissionList, 20);
            for (List<SalePackageCardTimeCardHandlerJobTypeCommissionEntity> entityList : subs) {
                salePackageCardTimeCardHandlerCommissionMapper.insertSalePackageCardTimeCardHandlerJobTypeCommission(entityList);
            }
        }
        return BaseOutput.success();
    }
}