package com.zhelian.service.ibeauty.treatGeneralCardCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.TreatGeneralCardCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.TreatGeneralCardCommissionEntity;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardsCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.TreatCardCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatGeneralCardCommissionService {

    @Autowired
    TreatGeneralCardCommissionMapper treatGeneralCardCommissionMapper;


    public BaseOutput allTreatGeneralCardCommission(TreatCardCategoryHandlerCommissionQueryForm form) {
        List<TreatCardCommissionOutputForm> list = treatGeneralCardCommissionMapper.allTreatGeneralCardCommission(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatGeneralCardCommission(TreatCardsCommissionAddForm form) {

        treatGeneralCardCommissionMapper.deleteTreatGeneralCardCommissionByEntityIDAndCategory(form.EntityID, form.CategoryID);
        List<TreatGeneralCardCommissionEntity> list = new ArrayList<>();
        if (form.Card != null && form.Card.size() > 0) {
            for (TreatCardCommissionAddForm treatCardCommissionAddForm : form.Card) {
                if (treatCardCommissionAddForm.PayRate != null || treatCardCommissionAddForm.PayFixed != null ||
                        treatCardCommissionAddForm.CardRate != null || treatCardCommissionAddForm.CardFixed != null ||
                        treatCardCommissionAddForm.CardLargessRate != null || treatCardCommissionAddForm.CardLargessFixed != null ||
                        treatCardCommissionAddForm.LargessRate != null || treatCardCommissionAddForm.LargessFixed != null ||
                        treatCardCommissionAddForm.SpecialBenefit != null) {
                    TreatGeneralCardCommissionEntity treatGeneralCardCommissionEntity = new TreatGeneralCardCommissionEntity();
                    treatGeneralCardCommissionEntity.EntityID = form.EntityID;
                    treatGeneralCardCommissionEntity.GeneralCardID = treatCardCommissionAddForm.CardID;
                    treatGeneralCardCommissionEntity.PayRate = treatCardCommissionAddForm.PayRate;
                    treatGeneralCardCommissionEntity.PayFixed = treatCardCommissionAddForm.PayFixed;
                    treatGeneralCardCommissionEntity.CardRate = treatCardCommissionAddForm.CardRate;
                    treatGeneralCardCommissionEntity.CardFixed = treatCardCommissionAddForm.CardFixed;
                    treatGeneralCardCommissionEntity.CardLargessRate = treatCardCommissionAddForm.CardLargessRate;
                    treatGeneralCardCommissionEntity.CardLargessFixed = treatCardCommissionAddForm.CardLargessFixed;
                    treatGeneralCardCommissionEntity.LargessFixed = treatCardCommissionAddForm.LargessFixed;
                    treatGeneralCardCommissionEntity.LargessRate = treatCardCommissionAddForm.LargessRate;
                    treatGeneralCardCommissionEntity.SpecialBenefit = treatCardCommissionAddForm.SpecialBenefit;
                    list.add(treatGeneralCardCommissionEntity);
                }
            }
            if (list != null && list.size() > 0) {
                List<List<TreatGeneralCardCommissionEntity>> subs = Lists.partition(list, 20);
                for (List<TreatGeneralCardCommissionEntity> entityList : subs) {
                    treatGeneralCardCommissionMapper.insertTreatGeneralCardCommission(entityList);
                }
            }
        }
        return BaseOutput.success();
    }

}