package com.zhelian.service.ibeauty.saleProductCommission;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.ibeauty.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.SaleProductCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionSchemeDeleteForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionSchemeQueryForm;
import com.zhelian.model.ibeauty.form.output.SaleProductCommissionSchemeOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class SaleProductCommissionSchemeService {

    @Autowired
    SaleProductCommissionSchemeMapper saleProductCommissionSchemeMapper;
    @Autowired
    SaleProductCategoryCommissionMapper saleProductCategoryCommissionMapper;
    @Autowired
    SaleProductCategoryHandlerCommissionMapper saleProductCategoryHandlerCommissionMapper;
    @Autowired
    SaleProductCommissionMapper saleProductCommissionMapper;
    @Autowired
    SaleProductHandlerCommissionMapper saleProductHandlerCommissionMapper;
    @Autowired
    SaleProductSchemeHandlerCommissionMapper saleProductSchemeHandlerCommissionMapper;

    public BaseOutput saleProductCommissionSchemeList(SaleProductCommissionSchemeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleProductCommissionSchemeOutputForm> list = saleProductCommissionSchemeMapper.saleProductCommissionSchemeList(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput createSaleProductCommissionScheme(SaleProductCommissionSchemeAddForm form) {
        Integer count = saleProductCommissionSchemeMapper.getSaleProductCommissionScheme(form.EntityID);
        if (count > 0)
            return BaseOutput.failed("组织单位已经存在");

        SaleProductCommissionSchemeEntity saleProductCommissionSchemeEntity = new SaleProductCommissionSchemeEntity();
        saleProductCommissionSchemeEntity.EntityID = form.EntityID;

        saleProductCommissionSchemeMapper.createSaleProductCommissionScheme(saleProductCommissionSchemeEntity);
        return BaseOutput.success();

    }

    public BaseOutput deleteSaleProductCommissionScheme(SaleProductCommissionSchemeDeleteForm form) {
        saleProductCommissionSchemeMapper.deleteSaleProductCommissionScheme(form.EntityID);
        saleProductCategoryCommissionMapper.deleteSaleProductCategoryCommissionEntity(form.EntityID);
        saleProductCategoryHandlerCommissionMapper.deleteSaleProductCategoryHandlerCommissionByEntityID(form.EntityID);
        saleProductCategoryHandlerCommissionMapper.deleteSaleProductCategoryHandlerJobTypeCommissionByEntityID(form.EntityID);
        saleProductCommissionMapper.deleteSaleProductCommissionByEntityID(form.EntityID);
        saleProductHandlerCommissionMapper.deleteSaleProductHandlerCommissionByEntityID(form.EntityID);
        saleProductHandlerCommissionMapper.deleteSaleProductHandlerJobTypeCommissionByEntityID(form.EntityID);
        saleProductSchemeHandlerCommissionMapper.deleteSaleProductSchemeHandler(form.EntityID);
        saleProductSchemeHandlerCommissionMapper.deleteSaleProductSchemeHandlerJobType(form.EntityID);
        return BaseOutput.success();
    }
}