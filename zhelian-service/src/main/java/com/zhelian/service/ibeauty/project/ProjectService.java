package com.zhelian.service.ibeauty.project;

import com.github.pagehelper.PageHelper;
import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.ProjectCategoryMapper;
import com.zhelian.mapper.ibeauty.ProjectMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.TbProjectConsumeEntity;
import com.zhelian.model.ibeauty.entity.TbProjectEntity;
import com.zhelian.model.ibeauty.entity.TbProjectImageEntity;
import com.zhelian.model.ibeauty.entity.TbProjectSaleEntity;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.ibeauty.form.output.AllProjectOutputForm;
import com.zhelian.model.ibeauty.form.output.ProjectDetailsOutputForm;
import com.zhelian.model.ibeauty.form.output.ProjectOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;


/**
 * @Description:
 * @Param:
 * @returns:
 * @Author: xuhao
 * @Date: 2020/3/21 14:33
 */
@Service
public class ProjectService {

    @Autowired
    ProjectMapper projectMapper;
    @Autowired
    ProjectCategoryMapper projectCategoryMapper;

    public BaseOutput projectList(ProjectQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<ProjectOutputForm> list = projectMapper.projectList(form.Name, form.ProjectCategoryID, form.IsAllowSell, form.ProjectBrandID);
        return BasePageInfo.success(list);
    }

    public BaseOutput getProjectDetails(ProjectSaleEntityQueryForm form) {
        ProjectDetailsOutputForm productDetails = projectMapper.getProjectDetails(form.ID);
        return BaseOutputForm.success(productDetails);
    }


    public BaseOutput projectSaleEntity(ProjectSaleEntityQueryForm form) {
        return BaseOutputForm.success(projectMapper.projectSaleEntity(form.ID));
    }

    public BaseOutput projectConsumeEntity(ProjectConsumeEntityQueryForm form) {
        return BaseOutputForm.success(projectMapper.projectConsumeEntity(form.ID));
    }

    public BaseOutput projectModifyPriceEntity(ProjectConsumeEntityQueryForm form) {
        return BaseOutputForm.success(projectMapper.projectModifyPriceEntity(form.ID));
    }

    @Transactional
    public BaseOutput createProject(ProjectAddForm form) {

        TbProjectEntity tbProjectEntity = new TbProjectEntity();
        tbProjectEntity.Name = form.Name;
        tbProjectEntity.Alias = form.Alias;
        tbProjectEntity.ProjectCategoryID = form.ProjectCategoryID;
        tbProjectEntity.TreatTime = form.TreatTime;
        tbProjectEntity.Price = form.Price;
        tbProjectEntity.IsAllowSell = form.IsAllowSell;
        tbProjectEntity.IsAllowConsume = form.IsAllowConsume;
        tbProjectEntity.IsAllowLargess = form.IsAllowLargess;
        tbProjectEntity.ProjectBrandID = form.ProjectBrandID;
        tbProjectEntity.Remark = form.Remark;
        tbProjectEntity.OriginalText = form.OriginalText;
        tbProjectEntity.Memo = form.Memo;

        projectMapper.createProject(tbProjectEntity);

        updateProject(form.ProjectSaleEntity, form.ProjectConsumeEntity, form.ModifyPriceEntity, tbProjectEntity.ID);
        if (form.ImageList.size() > 0) {
            for (int a = 0; a < form.ImageList.size(); a++) {
                projectMapper.insertProjectImageURL(tbProjectEntity.ID, form.ImageList.get(a).ImageUrl, a + 1);
            }
        }

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateProject(ProjectUpdateForm form) {
        TbProjectEntity tbProjectEntity = new TbProjectEntity();
        tbProjectEntity.ID = form.ID;
        tbProjectEntity.Name = form.Name;
        tbProjectEntity.Alias = form.Alias;
        tbProjectEntity.Price = form.Price;
        tbProjectEntity.TreatTime = form.TreatTime;
        tbProjectEntity.ProjectCategoryID = form.ProjectCategoryID;
        tbProjectEntity.IsAllowSell = form.IsAllowSell;
        tbProjectEntity.IsAllowConsume = form.IsAllowConsume;
        tbProjectEntity.IsAllowLargess = form.IsAllowLargess;
        tbProjectEntity.ProjectBrandID = form.ProjectBrandID;
        tbProjectEntity.Remark = form.Remark;
        tbProjectEntity.OriginalText = form.OriginalText;
        tbProjectEntity.Memo = form.Memo;

        projectMapper.updateProject(tbProjectEntity);


        projectMapper.deleteSaleEntity(form.ID);
        projectMapper.deleteConsumeEntity(form.ID);
        projectMapper.deleteModfyPriceEntity(form.ID);

        updateProject(form.ProjectSaleEntity, form.ProjectConsumeEntity, form.ModifyPriceEntity, form.ID);

        List<GoodsImageUrlForm> projectImageList = projectMapper.getProjectImageList(form.ID);
        if (projectImageList.size() > 0) {
            projectMapper.deleteProjectImageByID(form.ID);
        }

        if (form.ImageList.size() > 0) {
            for (int a = 0; a < form.ImageList.size(); a++) {
                projectMapper.insertProjectImageURL(form.ID, form.ImageList.get(a).ImageUrl, a + 1);
            }
        }

        return BaseOutput.success();
    }

    public void updateProject(int[] ProjectSaleEntity, int[] ProjectConsumeEntity, int[] ModifyPriceEntity, Integer projectID) {
        if (ProjectSaleEntity != null && ProjectSaleEntity.length > 0) {
            List<TbProjectSaleEntity> list = new ArrayList<>();
            for (Integer ID : ProjectSaleEntity) {
                TbProjectSaleEntity tbProjectSaleEntity = new TbProjectSaleEntity();
                tbProjectSaleEntity.EntityID = ID;
                tbProjectSaleEntity.ProjectID = projectID;
                list.add(tbProjectSaleEntity);
            }
            projectMapper.updateSaleEntity(list);
        }
        if (ProjectConsumeEntity != null && ProjectConsumeEntity.length > 0) {
            List<TbProjectConsumeEntity> list = new ArrayList<>();
            for (Integer ID : ProjectConsumeEntity) {
                TbProjectConsumeEntity tbProjectConsumeEntity = new TbProjectConsumeEntity();
                tbProjectConsumeEntity.EntityID = ID;
                tbProjectConsumeEntity.ProjectID = projectID;
                list.add(tbProjectConsumeEntity);
            }
            projectMapper.updateConsumeEntity(list);
        }
        if (ModifyPriceEntity != null && ModifyPriceEntity.length > 0) {
            List<TbProjectSaleEntity> list = new ArrayList<>();
            for (Integer ID : ModifyPriceEntity) {
                TbProjectSaleEntity tbProjectModifyPriceEntity = new TbProjectSaleEntity();
                tbProjectModifyPriceEntity.EntityID = ID;
                tbProjectModifyPriceEntity.ProjectID = projectID;
                list.add(tbProjectModifyPriceEntity);
            }
            projectMapper.updateModifyPriceEntity(list);
        }
    }

    public BaseOutput allProject() {
        List<AllProjectOutputForm> list = projectMapper.allProject();
        return BaseOutputForm.success(list);
    }

    public BaseOutput move(GoodsMoveForm form) {

        LinkedList<Integer> linkedList = projectMapper.getASCList(form.MoveID, form.ID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<TbProjectImageEntity> list = new ArrayList<>();
        for (Integer ID : linkedList) {
            TbProjectImageEntity projectImageEntity = new TbProjectImageEntity();
            projectImageEntity.ID = ID;
            projectImageEntity.ProjectID = form.ID;
            projectImageEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(projectImageEntity);
        }

        projectMapper.updateSequence(list);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput bitchUpdateProjectSaleEntity(GoodsBitchUpdateForm form) {
        projectMapper.deleteBitchSaleEntity(form.GoodsID);
        List<TbProjectSaleEntity> list = new ArrayList<>();
        for (Integer goodsID : form.GoodsID) {
            for (Integer entityID : form.EntityID) {
                TbProjectSaleEntity saleEntity = new TbProjectSaleEntity();
                saleEntity.ProjectID = goodsID;
                saleEntity.EntityID = entityID;
                list.add(saleEntity);
            }
        }

        if (list.size() > 0)
            projectMapper.updateSaleEntity(list);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput bitchUpdateProjectConsumeEntity(GoodsBitchUpdateForm form) {
        projectMapper.deleteBitchConsumeEntity(form.GoodsID);
        List<TbProjectConsumeEntity> list = new ArrayList<>();
        for (Integer goodsID : form.GoodsID) {
            for (Integer entityID : form.EntityID) {
                TbProjectConsumeEntity saleEntity = new TbProjectConsumeEntity();
                saleEntity.ProjectID = goodsID;
                saleEntity.EntityID = entityID;
                list.add(saleEntity);
            }
        }

        if (list.size() > 0)
            projectMapper.updateConsumeEntity(list);

        return BaseOutput.success();
    }
}