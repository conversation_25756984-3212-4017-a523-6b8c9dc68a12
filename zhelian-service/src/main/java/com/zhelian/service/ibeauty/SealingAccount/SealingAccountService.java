package com.zhelian.service.ibeauty.SealingAccount;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.SealingAccountMapper;
import com.zhelian.mapper.khs.EmployeeMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.SealingAccountEntity;
import com.zhelian.model.ibeauty.entity.SealingAccountEntityEntity;
import com.zhelian.model.ibeauty.entity.SealingAccountJobTypeEntity;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.ibeauty.form.output.SealingAccountOutputForm;
import com.zhelian.model.ibeauty.form.output.SealingAccountPermissionEntityOutputForm;
import com.zhelian.model.ibeauty.form.output.SealingAccountrRestrictionOutputForm;
import com.zhelian.model.khs.entity.EmployeeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

@Service
public class SealingAccountService {

    @Autowired
    SealingAccountMapper sealingAccountMapper;
    @Autowired
    EmployeeMapper employeeMapper ;

    public BaseOutput sealingAccountList(SealingAccountQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SealingAccountOutputForm> list = sealingAccountMapper.sealingAccountList(form.EntityID,form.Name);
        return BasePageInfo.success(list);
    }
    @Transactional
    public BaseOutput createSealingAccount(SealingAccountAddForm form) {
        if(form.StartAccountPeriod>form.EndAccountPeriod){
            return BaseOutput.failed("开始时间不能大于结束时间");
        }
        if(form.StartAccountPeriod==form.EndAccountPeriod&& form.StartAccountDate>form.EndAccountDate){
            return BaseOutput.failed("开始时间不能大于结束时间");
        }

        if(form.Entity!=null && form.Entity.length>0){
            List<Integer> sealingAccountEntity=sealingAccountMapper.getSealingAccountEntity();
            List<Integer> entity = Arrays.asList(form.Entity);
            if(sealingAccountEntity!=null && sealingAccountEntity.size()>0){
                List<Integer> collect = entity.stream().filter(i -> sealingAccountEntity.contains(i)).collect(Collectors.toList());
                if(collect.size()>0){
                    return BaseOutput.failed("所选的门店已经设置过关账，请重新选择门店");
                }
            }
        }

        SealingAccountEntity sealingAccountEntity = new SealingAccountEntity();
        sealingAccountEntity.Name = form.Name;
        sealingAccountEntity.StartAccountPeriod = form.StartAccountPeriod;
        sealingAccountEntity.StartAccountDate = form.StartAccountDate;
        sealingAccountEntity.EndAccountPeriod = form.EndAccountPeriod;
        sealingAccountEntity.EndAccountDate = form.EndAccountDate;
        sealingAccountEntity.CloseAccountDate = form.CloseAccountDate;
        sealingAccountEntity.ReplacementOrder = form.ReplacementOrder;
        sealingAccountEntity.CancelOrder = form.CancelOrder;
        sealingAccountEntity.ModifyBillDate = form.ModifyBillDate;
        sealingAccountEntity.ModifyBillPayMethod = form.ModifyBillPayMethod;
        sealingAccountEntity.ModifyEntityPerformance = form.ModifyEntityPerformance;
        sealingAccountEntity.ModifyEmployeePerformanceCommission = form.ModifyEmployeePerformanceCommission;
        sealingAccountEntity.IncomeAndSpending = form.IncomeAndSpending;
        sealingAccountMapper.createSealingAccount(sealingAccountEntity);

        List<SealingAccountEntityEntity> entityEntities=new ArrayList<>();
        List<SealingAccountJobTypeEntity> jobTypeEntities=new ArrayList<>();
        if(form.Entity!=null && form.Entity.length>0){
            for (Integer integer : form.Entity) {
                SealingAccountEntityEntity sealingAccountEntityEntity=new SealingAccountEntityEntity();
                sealingAccountEntityEntity.SealingAccountID=sealingAccountEntity.ID;
                sealingAccountEntityEntity.EntityID=integer;
                entityEntities.add(sealingAccountEntityEntity);
            }
        }
        if(form.JobType!=null && form.JobType.length>0){
            for (Integer integer : form.JobType) {
                SealingAccountJobTypeEntity sealingAccountJobTypeEntity=new SealingAccountJobTypeEntity();
                sealingAccountJobTypeEntity.SealingAccountID=sealingAccountEntity.ID;
                sealingAccountJobTypeEntity.JobTypeID=integer;
                jobTypeEntities.add(sealingAccountJobTypeEntity);
            }
        }

        if(entityEntities.size()>0)
            sealingAccountMapper.createSealingAccountEntityEntity(entityEntities);

        if(jobTypeEntities.size()>0)
            sealingAccountMapper.createSealingAccountJobTypeEntity(jobTypeEntities);

        return BaseOutput.success();
    }
    @Transactional
    public BaseOutput updateSealingAccount(SealingAccountUpdateForm form) {
        if(form.StartAccountPeriod>form.EndAccountPeriod){
            return BaseOutput.failed("开始时间不能大于结束时间");
        }
        if(form.StartAccountPeriod==form.EndAccountPeriod&& form.StartAccountDate>form.EndAccountDate){
            return BaseOutput.failed("开始时间不能大于结束时间");
        }
        if(form.Entity!=null && form.Entity.length>0){
            List<Integer> sealingAccountEntity=sealingAccountMapper.getSealingAccountEntityRemoveSelf(form.ID);
            List<Integer> entity = Arrays.asList(form.Entity);
            if(sealingAccountEntity!=null && sealingAccountEntity.size()>0){
                List<Integer> collect = entity.stream().filter(i -> sealingAccountEntity.contains(i)).collect(Collectors.toList());
                if(collect.size()>0){
                    return BaseOutput.failed("所选的门店已经设置过关账，请重新选择门店");
                }
            }
        }

        SealingAccountEntity sealingAccountEntity = new SealingAccountEntity();
        sealingAccountEntity.ID = form.ID;
        sealingAccountEntity.Name = form.Name;
        sealingAccountEntity.StartAccountPeriod = form.StartAccountPeriod;
        sealingAccountEntity.StartAccountDate = form.StartAccountDate;
        sealingAccountEntity.EndAccountPeriod = form.EndAccountPeriod;
        sealingAccountEntity.EndAccountDate = form.EndAccountDate;
        sealingAccountEntity.CloseAccountDate = form.CloseAccountDate;
        sealingAccountEntity.ReplacementOrder = form.ReplacementOrder;
        sealingAccountEntity.CancelOrder = form.CancelOrder;
        sealingAccountEntity.ModifyBillDate = form.ModifyBillDate;
        sealingAccountEntity.ModifyBillPayMethod = form.ModifyBillPayMethod;
        sealingAccountEntity.ModifyEntityPerformance = form.ModifyEntityPerformance;
        sealingAccountEntity.ModifyEmployeePerformanceCommission = form.ModifyEmployeePerformanceCommission;
        sealingAccountEntity.IncomeAndSpending = form.IncomeAndSpending;
        sealingAccountMapper.updateSealingAccount(sealingAccountEntity);
        sealingAccountMapper.deleteSealingAccountEntity(form.ID);
        sealingAccountMapper.deleteSealingAccountJobTypeEntity(form.ID);


        List<SealingAccountEntityEntity> entityEntities=new ArrayList<>();
        List<SealingAccountJobTypeEntity> jobTypeEntities=new ArrayList<>();
        if(form.Entity!=null && form.Entity.length>0){
            for (Integer integer : form.Entity) {
                SealingAccountEntityEntity sealingAccountEntityEntity=new SealingAccountEntityEntity();
                sealingAccountEntityEntity.SealingAccountID=sealingAccountEntity.ID;
                sealingAccountEntityEntity.EntityID=integer;
                entityEntities.add(sealingAccountEntityEntity);
            }
        }
        if(form.JobType!=null && form.JobType.length>0){
            for (Integer integer : form.JobType) {
                SealingAccountJobTypeEntity sealingAccountJobTypeEntity=new SealingAccountJobTypeEntity();
                sealingAccountJobTypeEntity.SealingAccountID=sealingAccountEntity.ID;
                sealingAccountJobTypeEntity.JobTypeID=integer;
                jobTypeEntities.add(sealingAccountJobTypeEntity);
            }
        }

        if(entityEntities.size()>0)
            sealingAccountMapper.createSealingAccountEntityEntity(entityEntities);

        if(jobTypeEntities.size()>0)
            sealingAccountMapper.createSealingAccountJobTypeEntity(jobTypeEntities);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput delete(SealingAccountUpdateForm form) {
        sealingAccountMapper.deleteSealingAccount(form.ID);
        sealingAccountMapper.deleteSealingAccountEntity(form.ID);
        sealingAccountMapper.deleteSealingAccountJobTypeEntity(form.ID);
        return BaseOutput.success();
    }

    public BaseOutput getEmployeeEntityPermission() {
            List<SealingAccountPermissionEntityOutputForm> list = sealingAccountMapper.entityPermission();
            JSONArray result = ListUtil.listToTree(JSONArray.parseArray(JSON.toJSONString(list, JSONWriter.Feature.WriteMapNullValue)), "ID", "ParentID", "Child");
            return BaseOutputForm.success(result);
        }

    public BaseOutput getReplacementOrderRestriction() {
        return BaseOutputForm.success(getReplacementOrder());
    }
    public Boolean getReplacementOrderRestriction(String bllDate) {
        SealingAccountrRestrictionOutputForm replacementOrder = getReplacementOrder();
        if(replacementOrder.IsHaveRestriction){
            LocalDate time = LocalDate.parse(bllDate.substring(0,10));
            LocalDate beginTime = LocalDate.parse(replacementOrder.Deadline);
            if (time.isBefore(beginTime)){
                return true;
            }
            return false;
        }
        return false;
    }
    public SealingAccountrRestrictionOutputForm getReplacementOrder(){
        SealingAccountrRestrictionOutputForm sealingAccountrRestrictionOutputForm=new SealingAccountrRestrictionOutputForm();
        sealingAccountrRestrictionOutputForm.IsHaveRestriction=false;
        List<SealingAccountOutputForm> list = sealingAccountMapper.sealingAccountList(SystemUserUtil.getSystemEntityID(),null);
        if(list!=null && list.size()>0){
            LocalDate now = LocalDate.now();
            EmployeeEntity mapperEmployee = employeeMapper.getEmployee(SystemUserUtil.getSystemUserID());
            List<Integer> jobs = list.get(0).JobType.stream().map(i -> i.JobTypeID).collect(Collectors.toList());
            List<Integer> collect = jobs.stream().filter(i -> i.equals(mapperEmployee.JobID)).collect(Collectors.toList());
            if(list.get(0).ReplacementOrder  &&(collect==null || collect.size()==0)){
                if(now.getDayOfMonth()>=list.get(0).CloseAccountDate.intValue()){//开启关账
                    if(list.get(0).EndAccountPeriod==0){
                        int days=now.minusMonths(1).lengthOfMonth()<list.get(0).EndAccountDate.intValue() ? now.minusMonths(1).lengthOfMonth(): list.get(0).EndAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()).plusDays(days).toString();
                    }else{
                        int days=now.lengthOfMonth()<list.get(0).EndAccountDate.intValue() ? now.lengthOfMonth(): list.get(0).EndAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.with(TemporalAdjusters.firstDayOfMonth()).plusDays(days).toString();
                    }
                }else{
                    if(list.get(0).StartAccountPeriod==0){
                        int days=now.minusMonths(1).lengthOfMonth()<list.get(0).StartAccountDate.intValue() ? now.minusMonths(1).lengthOfMonth(): list.get(0).StartAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()).plusDays(days-1).toString();
                    }else{
                        int days=now.lengthOfMonth()<list.get(0).StartAccountDate.intValue() ? now.lengthOfMonth(): list.get(0).StartAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.with(TemporalAdjusters.firstDayOfMonth()).plusDays(days-1).toString();
                    }
                }
                sealingAccountrRestrictionOutputForm.IsHaveRestriction=true;
            }
        }
        return sealingAccountrRestrictionOutputForm;
    }


    public BaseOutput getCancelOrderRestriction() {
        return BaseOutputForm.success(getCancelOrder());
    }
    public Boolean getCancelOrderRestriction(String bllDate) {
        SealingAccountrRestrictionOutputForm replacementOrder = getCancelOrder();
        if(replacementOrder.IsHaveRestriction){
            LocalDate time = LocalDate.parse(bllDate.substring(0,10));
            LocalDate beginTime = LocalDate.parse(replacementOrder.Deadline);
            if (time.isBefore(beginTime)){
                return true;
            }
            return false;
        }
        return false;
    }
    public SealingAccountrRestrictionOutputForm getCancelOrder(){
        SealingAccountrRestrictionOutputForm sealingAccountrRestrictionOutputForm=new SealingAccountrRestrictionOutputForm();
        sealingAccountrRestrictionOutputForm.IsHaveRestriction=false;
        List<SealingAccountOutputForm> list = sealingAccountMapper.sealingAccountList(SystemUserUtil.getSystemEntityID(),null);
        if(list!=null && list.size()>0){
            LocalDate now = LocalDate.now();
            EmployeeEntity mapperEmployee = employeeMapper.getEmployee(SystemUserUtil.getSystemUserID());
            List<Integer> jobs = list.get(0).JobType.stream().map(i -> i.JobTypeID).collect(Collectors.toList());
            List<Integer> collect = jobs.stream().filter(i -> i.equals(mapperEmployee.JobID)).collect(Collectors.toList());
            if(list.get(0).CancelOrder  &&(collect==null || collect.size()==0)){
                if(now.getDayOfMonth()>=list.get(0).CloseAccountDate.intValue()){//开启关账
                    if(list.get(0).EndAccountPeriod==0){
                        int days=now.minusMonths(1).lengthOfMonth()<list.get(0).EndAccountDate.intValue() ? now.minusMonths(1).lengthOfMonth(): list.get(0).EndAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()).plusDays(days).toString();
                    }else{
                        int days=now.lengthOfMonth()<list.get(0).EndAccountDate.intValue() ? now.lengthOfMonth(): list.get(0).EndAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.with(TemporalAdjusters.firstDayOfMonth()).plusDays(days).toString();
                    }
                }else{
                    if(list.get(0).StartAccountPeriod==0){
                        int days=now.minusMonths(1).lengthOfMonth()<list.get(0).StartAccountDate.intValue() ? now.minusMonths(1).lengthOfMonth(): list.get(0).StartAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()).plusDays(days-1).toString();
                    }else{
                        int days=now.lengthOfMonth()<list.get(0).StartAccountDate.intValue() ? now.lengthOfMonth(): list.get(0).StartAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.with(TemporalAdjusters.firstDayOfMonth()).plusDays(days-1).toString();
                    }
                }
                sealingAccountrRestrictionOutputForm.IsHaveRestriction=true;
            }
        }
        return sealingAccountrRestrictionOutputForm;
    }

    public BaseOutput getModifyBillDateRestriction() {
        return BaseOutputForm.success(getModifyBillDate());
    }
    public Boolean getModifyBillDateRestriction(String bllDate) {
        SealingAccountrRestrictionOutputForm replacementOrder = getModifyBillDate();
        if(replacementOrder.IsHaveRestriction){
            LocalDate time = LocalDate.parse(bllDate.substring(0,10));
            LocalDate beginTime = LocalDate.parse(replacementOrder.Deadline);

            if (time.isBefore(beginTime)){
                return true;
            }
            return false;
        }
        return false;
    }
    public SealingAccountrRestrictionOutputForm getModifyBillDate(){
        SealingAccountrRestrictionOutputForm sealingAccountrRestrictionOutputForm=new SealingAccountrRestrictionOutputForm();
        sealingAccountrRestrictionOutputForm.IsHaveRestriction=false;
        List<SealingAccountOutputForm> list = sealingAccountMapper.sealingAccountList(SystemUserUtil.getSystemEntityID(),null);
        if(list!=null && list.size()>0){
            LocalDate now = LocalDate.now();
            EmployeeEntity mapperEmployee = employeeMapper.getEmployee(SystemUserUtil.getSystemUserID());
            List<Integer> jobs = list.get(0).JobType.stream().map(i -> i.JobTypeID).collect(Collectors.toList());
            List<Integer> collect = jobs.stream().filter(i -> i.equals(mapperEmployee.JobID)).collect(Collectors.toList());
            if(list.get(0).ModifyBillDate  &&(collect==null || collect.size()==0)){
                if(now.getDayOfMonth()>=list.get(0).CloseAccountDate.intValue()){//开启关账
                    if(list.get(0).EndAccountPeriod==0){
                        int days=now.minusMonths(1).lengthOfMonth()<list.get(0).EndAccountDate.intValue() ? now.minusMonths(1).lengthOfMonth(): list.get(0).EndAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()).plusDays(days).toString();
                    }else{
                        int days=now.lengthOfMonth()<list.get(0).EndAccountDate.intValue() ? now.lengthOfMonth(): list.get(0).EndAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.with(TemporalAdjusters.firstDayOfMonth()).plusDays(days).toString();
                    }
                }else{
                    if(list.get(0).StartAccountPeriod==0){
                        int days=now.minusMonths(1).lengthOfMonth()<list.get(0).StartAccountDate.intValue() ? now.minusMonths(1).lengthOfMonth(): list.get(0).StartAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()).plusDays(days-1).toString();
                    }else{
                        int days=now.lengthOfMonth()<list.get(0).StartAccountDate.intValue() ? now.lengthOfMonth(): list.get(0).StartAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.with(TemporalAdjusters.firstDayOfMonth()).plusDays(days-1).toString();
                    }
                }
                sealingAccountrRestrictionOutputForm.IsHaveRestriction=true;
            }
        }
        return sealingAccountrRestrictionOutputForm;
    }


    public BaseOutput getModifyBillPayMethodRestriction() {

        return BaseOutputForm.success(getModifyBillPayMethod());
    }
    public Boolean getModifyBillPayMethodRestriction(String bllDate) {
        SealingAccountrRestrictionOutputForm replacementOrder = getModifyBillPayMethod();
        if(replacementOrder.IsHaveRestriction){
            LocalDate time = LocalDate.parse(bllDate.substring(0,10));
            LocalDate beginTime = LocalDate.parse(replacementOrder.Deadline);

            if (time.isBefore(beginTime)){
                return true;
            }
            return false;
        }
        return false;
    }
    public SealingAccountrRestrictionOutputForm getModifyBillPayMethod(){
        SealingAccountrRestrictionOutputForm sealingAccountrRestrictionOutputForm=new SealingAccountrRestrictionOutputForm();
        sealingAccountrRestrictionOutputForm.IsHaveRestriction=false;
        List<SealingAccountOutputForm> list = sealingAccountMapper.sealingAccountList(SystemUserUtil.getSystemEntityID(),null);
        if(list!=null && list.size()>0){
            LocalDate now = LocalDate.now();
            EmployeeEntity mapperEmployee = employeeMapper.getEmployee(SystemUserUtil.getSystemUserID());
            List<Integer> jobs = list.get(0).JobType.stream().map(i -> i.JobTypeID).collect(Collectors.toList());
            List<Integer> collect = jobs.stream().filter(i -> i.equals(mapperEmployee.JobID)).collect(Collectors.toList());
            if(list.get(0).ModifyBillPayMethod  &&(collect==null || collect.size()==0)){
                if(now.getDayOfMonth()>=list.get(0).CloseAccountDate.intValue()){//开启关账
                    if(list.get(0).EndAccountPeriod==0){
                        int days=now.minusMonths(1).lengthOfMonth()<list.get(0).EndAccountDate.intValue() ? now.minusMonths(1).lengthOfMonth(): list.get(0).EndAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()).plusDays(days).toString();
                    }else{
                        int days=now.lengthOfMonth()<list.get(0).EndAccountDate.intValue() ? now.lengthOfMonth(): list.get(0).EndAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.with(TemporalAdjusters.firstDayOfMonth()).plusDays(days).toString();
                    }
                }else{
                    if(list.get(0).StartAccountPeriod==0){
                        int days=now.minusMonths(1).lengthOfMonth()<list.get(0).StartAccountDate.intValue() ? now.minusMonths(1).lengthOfMonth(): list.get(0).StartAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()).plusDays(days-1).toString();
                    }else{
                        int days=now.lengthOfMonth()<list.get(0).StartAccountDate.intValue() ? now.lengthOfMonth(): list.get(0).StartAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.with(TemporalAdjusters.firstDayOfMonth()).plusDays(days-1).toString();
                    }
                }
                sealingAccountrRestrictionOutputForm.IsHaveRestriction=true;
            }
        }
        return sealingAccountrRestrictionOutputForm;
    }
    public BaseOutput getModifyEntityPerformanceRestriction() {
        return BaseOutputForm.success(getModifyEntityPerformance());
    }
    public Boolean getModifyEntityPerformanceRestriction(String bllDate) {
        SealingAccountrRestrictionOutputForm replacementOrder = getModifyEntityPerformance();
        if(replacementOrder.IsHaveRestriction){
            LocalDate time = LocalDate.parse(bllDate.substring(0,10));
            LocalDate beginTime = LocalDate.parse(replacementOrder.Deadline);

            if (time.isBefore(beginTime)){
                return true;
            }
            return false;
        }
        return false;
    }
    public SealingAccountrRestrictionOutputForm getModifyEntityPerformance(){
        SealingAccountrRestrictionOutputForm sealingAccountrRestrictionOutputForm=new SealingAccountrRestrictionOutputForm();
        sealingAccountrRestrictionOutputForm.IsHaveRestriction=false;
        List<SealingAccountOutputForm> list = sealingAccountMapper.sealingAccountList(SystemUserUtil.getSystemEntityID(),null);
        if(list!=null && list.size()>0){
            LocalDate now = LocalDate.now();
            EmployeeEntity mapperEmployee = employeeMapper.getEmployee(SystemUserUtil.getSystemUserID());
            List<Integer> jobs = list.get(0).JobType.stream().map(i -> i.JobTypeID).collect(Collectors.toList());
            List<Integer> collect = jobs.stream().filter(i -> i.equals(mapperEmployee.JobID)).collect(Collectors.toList());
            if(list.get(0).ModifyEntityPerformance  &&(collect==null || collect.size()==0)){
                if(now.getDayOfMonth()>=list.get(0).CloseAccountDate.intValue()){//开启关账
                    if(list.get(0).EndAccountPeriod==0){
                        int days=now.minusMonths(1).lengthOfMonth()<list.get(0).EndAccountDate.intValue() ? now.minusMonths(1).lengthOfMonth(): list.get(0).EndAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()).plusDays(days).toString();
                    }else{
                        int days=now.lengthOfMonth()<list.get(0).EndAccountDate.intValue() ? now.lengthOfMonth(): list.get(0).EndAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.with(TemporalAdjusters.firstDayOfMonth()).plusDays(days).toString();
                    }
                }else{
                    if(list.get(0).StartAccountPeriod==0){
                        int days=now.minusMonths(1).lengthOfMonth()<list.get(0).StartAccountDate.intValue() ? now.minusMonths(1).lengthOfMonth(): list.get(0).StartAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()).plusDays(days-1).toString();
                    }else{
                        int days=now.lengthOfMonth()<list.get(0).StartAccountDate.intValue() ? now.lengthOfMonth(): list.get(0).StartAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.with(TemporalAdjusters.firstDayOfMonth()).plusDays(days-1).toString();
                    }
                }
                sealingAccountrRestrictionOutputForm.IsHaveRestriction=true;
            }
        }
        return sealingAccountrRestrictionOutputForm;
    }
    public BaseOutput getModifyEmployeePerformanceCommissionRestriction() {
        return BaseOutputForm.success(getModifyEmployeePerformanceCommission());
    }
    public Boolean getModifyEmployeePerformanceCommissionRestriction(String bllDate) {
        SealingAccountrRestrictionOutputForm replacementOrder = getModifyEmployeePerformanceCommission();
        if(replacementOrder.IsHaveRestriction){
            LocalDate time = LocalDate.parse(bllDate.substring(0,10));
            LocalDate beginTime = LocalDate.parse(replacementOrder.Deadline);

            if (time.isBefore(beginTime)){
                return true;
            }
            return false;
        }
        return false;
    }
    public SealingAccountrRestrictionOutputForm getModifyEmployeePerformanceCommission(){
        SealingAccountrRestrictionOutputForm sealingAccountrRestrictionOutputForm=new SealingAccountrRestrictionOutputForm();
        sealingAccountrRestrictionOutputForm.IsHaveRestriction=false;
        List<SealingAccountOutputForm> list = sealingAccountMapper.sealingAccountList(SystemUserUtil.getSystemEntityID(),null);
        if(list!=null && list.size()>0){
            LocalDate now = LocalDate.now();
            EmployeeEntity mapperEmployee = employeeMapper.getEmployee(SystemUserUtil.getSystemUserID());
            List<Integer> jobs = list.get(0).JobType.stream().map(i -> i.JobTypeID).collect(Collectors.toList());
            List<Integer> collect = jobs.stream().filter(i -> i.equals(mapperEmployee.JobID)).collect(Collectors.toList());
            if(list.get(0).ModifyEmployeePerformanceCommission  &&(collect==null || collect.size()==0)){
                if(now.getDayOfMonth()>=list.get(0).CloseAccountDate.intValue()){//开启关账
                    if(list.get(0).EndAccountPeriod==0){
                        int days=now.minusMonths(1).lengthOfMonth()<list.get(0).EndAccountDate.intValue() ? now.minusMonths(1).lengthOfMonth(): list.get(0).EndAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()).plusDays(days).toString();
                    }else{
                        int days=now.lengthOfMonth()<list.get(0).EndAccountDate.intValue() ? now.lengthOfMonth(): list.get(0).EndAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.with(TemporalAdjusters.firstDayOfMonth()).plusDays(days).toString();
                    }
                }else{
                    if(list.get(0).StartAccountPeriod==0){
                        int days=now.minusMonths(1).lengthOfMonth()<list.get(0).StartAccountDate.intValue() ? now.minusMonths(1).lengthOfMonth(): list.get(0).StartAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()).plusDays(days-1).toString();
                    }else{
                        int days=now.lengthOfMonth()<list.get(0).StartAccountDate.intValue() ? now.lengthOfMonth(): list.get(0).StartAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.with(TemporalAdjusters.firstDayOfMonth()).plusDays(days-1).toString();
                    }
                }
                sealingAccountrRestrictionOutputForm.IsHaveRestriction=true;
            }
        }
        return sealingAccountrRestrictionOutputForm;
    }
    public BaseOutput getModifyIncomeAndSpendingRestriction() {
        return BaseOutputForm.success(getModifyIncomeAndSpending());
    }
    public Boolean getModifyIncomeAndSpendingRestriction(String bllDate) {
        SealingAccountrRestrictionOutputForm replacementOrder = getModifyIncomeAndSpending();
        if(replacementOrder.IsHaveRestriction){
            LocalDate time = LocalDate.parse(bllDate.substring(0,10));
            LocalDate beginTime = LocalDate.parse(replacementOrder.Deadline);
            if (time.isBefore(beginTime)){
                return true;
            }
            return false;
        }
        return false;
    }
    public SealingAccountrRestrictionOutputForm getModifyIncomeAndSpending(){
        SealingAccountrRestrictionOutputForm sealingAccountrRestrictionOutputForm=new SealingAccountrRestrictionOutputForm();
        sealingAccountrRestrictionOutputForm.IsHaveRestriction=false;
        List<SealingAccountOutputForm> list = sealingAccountMapper.sealingAccountList(SystemUserUtil.getSystemEntityID(),null);
        if(list!=null && list.size()>0){
            LocalDate now = LocalDate.now();
            EmployeeEntity mapperEmployee = employeeMapper.getEmployee(SystemUserUtil.getSystemUserID());
            List<Integer> jobs = list.get(0).JobType.stream().map(i -> i.JobTypeID).collect(Collectors.toList());
            List<Integer> collect = jobs.stream().filter(i -> i.equals(mapperEmployee.JobID)).collect(Collectors.toList());
            if(list.get(0).IncomeAndSpending  &&(collect==null || collect.size()==0)){
                if(now.getDayOfMonth()>=list.get(0).CloseAccountDate.intValue()){//开启关账
                    if(list.get(0).EndAccountPeriod==0){
                        int days=now.minusMonths(1).lengthOfMonth()<list.get(0).EndAccountDate.intValue() ? now.minusMonths(1).lengthOfMonth(): list.get(0).EndAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()).plusDays(days).toString();
                    }else{
                        int days=now.lengthOfMonth()<list.get(0).EndAccountDate.intValue() ? now.lengthOfMonth(): list.get(0).EndAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.with(TemporalAdjusters.firstDayOfMonth()).plusDays(days).toString();
                    }
                }else{
                    if(list.get(0).StartAccountPeriod==0){
                        int days=now.minusMonths(1).lengthOfMonth()<list.get(0).StartAccountDate.intValue() ? now.minusMonths(1).lengthOfMonth(): list.get(0).StartAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()).plusDays(days-1).toString();
                    }else{
                        int days=now.lengthOfMonth()<list.get(0).StartAccountDate.intValue() ? now.lengthOfMonth(): list.get(0).StartAccountDate.intValue();
                        sealingAccountrRestrictionOutputForm.Deadline = now.with(TemporalAdjusters.firstDayOfMonth()).plusDays(days-1).toString();
                    }
                }
                sealingAccountrRestrictionOutputForm.IsHaveRestriction=true;
            }
        }
        return sealingAccountrRestrictionOutputForm;
    }
}