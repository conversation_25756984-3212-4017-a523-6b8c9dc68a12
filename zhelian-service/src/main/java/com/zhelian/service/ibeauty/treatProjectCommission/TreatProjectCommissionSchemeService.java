package com.zhelian.service.ibeauty.treatProjectCommission;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.ibeauty.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.TreatProjectCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.input.TreatProjectCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCommissionSchemeDeleteForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCommissionSchemeQueryForm;
import com.zhelian.model.ibeauty.form.output.TreatProjectCommissionSchemeOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class TreatProjectCommissionSchemeService {

    @Autowired
    TreatProjectCommissionSchemeMapper treatProjectCommissionSchemeMapper;
    @Autowired
    TreatProjectCategoryCommissionMapper treatProjectCategoryCommissionMapper;
    @Autowired
    TreatProjectCategoryHandlerCommissionMapper treatProjectCategoryHandlerCommissionMapper;
    @Autowired
    TreatProjectCommissionMapper treatProjectCommissionMapper;
    @Autowired
    TreatProjectHandlerCommissionMapper treatProjectHandlerCommissionMapper;
    @Autowired
    TreatProjectSchemeHandlerCommissionMapper treatProjectSchemeHandlerCommissionMapper;

    public BaseOutput treatProjectCommissionSchemeList(TreatProjectCommissionSchemeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<TreatProjectCommissionSchemeOutputForm> list = treatProjectCommissionSchemeMapper.treatProjectCommissionSchemeList(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput createTreatProjectCommissionScheme(TreatProjectCommissionSchemeAddForm form) {
        Integer count = treatProjectCommissionSchemeMapper.getTreatProjectCommissionScheme(form.EntityID);
        if (count > 0)
            return BaseOutput.failed("组织单位已经存在");
        TreatProjectCommissionSchemeEntity treatProjectCommissionSchemeEntity = new TreatProjectCommissionSchemeEntity();
        treatProjectCommissionSchemeEntity.EntityID = form.EntityID;
        treatProjectCommissionSchemeMapper.createTreatProjectCommissionScheme(treatProjectCommissionSchemeEntity);
        return BaseOutput.success();

    }

    public BaseOutput deleteTreatProjectCommissionScheme(TreatProjectCommissionSchemeDeleteForm form) {
        treatProjectCommissionSchemeMapper.deleteTreatProjectCommissionScheme(form.EntityID);
        treatProjectCategoryCommissionMapper.deleteTreatProjectCategoryCommissionEntity(form.EntityID);
        treatProjectCategoryHandlerCommissionMapper.deleteTreatProjectCategoryHandlerCommissionByEntity(form.EntityID);
        treatProjectCategoryHandlerCommissionMapper.deleteTreatProjectCategoryHandlerJobTypeCommissionByEntity(form.EntityID);
        treatProjectCommissionMapper.deleteTreatProjectCommissionByEntity(form.EntityID);
        treatProjectHandlerCommissionMapper.deleteTreatProjectHandlerCommissionByEntity(form.EntityID);
        treatProjectHandlerCommissionMapper.deleteTreatProjectHandlerJobTypeCommissionByEntity(form.EntityID);
        treatProjectSchemeHandlerCommissionMapper.deleteTreatProjectSchemeHandler(form.EntityID);
        treatProjectSchemeHandlerCommissionMapper.deleteTreatProjectSchemeHandlerJobType(form.EntityID);
        return BaseOutput.success();
    }
}