package com.zhelian.service.ibeauty.transer;

import com.zhelian.mapper.ibeauty.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.TranserAccountQueryForm;
import com.zhelian.model.ibeauty.form.input.TranserPackageCardAccountQueryForm;
import com.zhelian.model.ibeauty.form.output.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class TranserAccountService {
    @Autowired
    private ProjectAccountMapper projectAccountMapper;
    @Autowired
    private ProductAccountMapper productAccountMapper;
    @Autowired
    private SavingCardAccountMapper savingCardAccountMapper;
    @Autowired
    private GeneralCardAccountMapper generalCardAccountMapper;
    @Autowired
    private TimeCardAccountMapper timeCardAccountMapper;
    @Autowired
    private PackageCardAccountMapper packageCardAccountMapper;

    public BaseOutput getTranserProductAccount(TranserAccountQueryForm form) {
        List<ProductAccountOutputForm> list = productAccountMapper.getTranserProductAccount(form.CustomerID, form.Name);
        return BaseOutputForm.success(list);
    }

    public BaseOutput getTranserProjectAccount(TranserAccountQueryForm form) {
        List<ProjectAccountOutputForm> list = projectAccountMapper.getTranserProjectAccount(form.CustomerID, form.Name);
        return BaseOutputForm.success(list);
    }

    public BaseOutput getTranserSavingCardAccount(TranserAccountQueryForm form) {
        List<SavingCardAccountForm> list = savingCardAccountMapper.getTranserSavingCardAccount(form.CustomerID, form.Name);
        return BaseOutputForm.success(list);
    }

    public BaseOutput getTranserGeneralCardAccount(TranserAccountQueryForm form) {
        List<GeneralCardAccountOutputForm> list = generalCardAccountMapper.getTranserGeneralCardAccount(form.CustomerID, form.Name);
        return BaseOutputForm.success(list);
    }

    public BaseOutput getTranserTimeCardAccount(TranserAccountQueryForm form) {
        List<TimeCardAccountOutputForm> list = timeCardAccountMapper.getTranserTimeCardAccount(form.CustomerID, form.Name);
        return BaseOutputForm.success(list);
    }

    public BaseOutput getTranserPackageCardAccount(TranserAccountQueryForm form) {
        List<PackageCardAccountOutputForm> list = packageCardAccountMapper.getTranserPackageCardAccount(form.CustomerID, form.Name);
        return BaseOutputForm.success(list);
    }

    public BaseOutput packageCardAccountDetails(TranserPackageCardAccountQueryForm form) {
        PackageCardAccountDetailsOutputForm packageCardAccountDetailsOutputForm = new PackageCardAccountDetailsOutputForm();
        String ValidDayName = packageCardAccountMapper.getPackageCardAccountValidDayName(form.PackageCardAccountID);
        packageCardAccountDetailsOutputForm.Product = packageCardAccountMapper.transerPackageCardProductAccount(form.PackageCardAccountID);
        if (packageCardAccountDetailsOutputForm.Product != null)
            packageCardAccountDetailsOutputForm.Product.stream().forEach(i -> i.ValidDayName = ValidDayName);
        packageCardAccountDetailsOutputForm.Project = packageCardAccountMapper.transerPackageCardProjectAccount(form.PackageCardAccountID);
        if (packageCardAccountDetailsOutputForm.Project != null)
            packageCardAccountDetailsOutputForm.Project.stream().forEach(i -> i.ValidDayName = ValidDayName);
        packageCardAccountDetailsOutputForm.GeneralCard = packageCardAccountMapper.transerPackageCardGeneralCardAccount(form.PackageCardAccountID);
        if (packageCardAccountDetailsOutputForm.GeneralCard != null)
            packageCardAccountDetailsOutputForm.GeneralCard.stream().forEach(i -> i.ValidDayName = ValidDayName);
        packageCardAccountDetailsOutputForm.TimeCard = packageCardAccountMapper.transerPackageCardTimeCardAccount(form.PackageCardAccountID);
        if (packageCardAccountDetailsOutputForm.TimeCard != null)
            packageCardAccountDetailsOutputForm.TimeCard.stream().forEach(i -> i.ValidDayName = ValidDayName);
        packageCardAccountDetailsOutputForm.SavingCard = packageCardAccountMapper.transerPackageCardSavingCardAccount(form.PackageCardAccountID);
        if (packageCardAccountDetailsOutputForm.SavingCard != null)
            packageCardAccountDetailsOutputForm.SavingCard.stream().forEach(i -> i.ValidDayName = ValidDayName);
        return BaseOutputForm.success(packageCardAccountDetailsOutputForm);
    }
}