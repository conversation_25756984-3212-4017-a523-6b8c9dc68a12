package com.zhelian.service.ibeauty.treatBill;

import com.zhelian.core.utils.enumeration.WeiXinOfficialAccountMessage;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatBillAddForm;
import com.zhelian.model.ibeauty.form.input.TreatBillInfoQueryForm;
import com.zhelian.service.ibeauty.SealingAccount.SealingAccountService;
import com.zhelian.service.weixin.WeiXinMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TreatBillCommonService {
    @Autowired
    TreatBillService treatBillService;

    @Autowired
    WeiXinMessageService weiXinMessageService;
    @Autowired
    SealingAccountService sealingAccountService;


    public BaseOutput create(TreatBillAddForm treatBillAddForm, Boolean IsFastBil, String channel) throws Exception {
        Boolean replacementOrderRestriction = sealingAccountService.getReplacementOrderRestriction(treatBillAddForm.BillDate);
        if(replacementOrderRestriction){
            return BaseOutput.failed("补单时间处于关账时间内，请选择其他时间");
        }
        String treatBillID = treatBillService.createTreatBill(treatBillAddForm, IsFastBil, channel);

        weiXinMessageService.setTreatBillWxMessage(treatBillID, WeiXinOfficialAccountMessage.TreatNotice);
        return BaseOutput.success(treatBillID);


    }

    public BaseOutput cancelTreatBill(TreatBillInfoQueryForm form) {
        treatBillService.cancel(form);

        weiXinMessageService.setCancelTreatBillWxMessage(form.ID, WeiXinOfficialAccountMessage.TreatCancelNotice);

        return BaseOutput.success();
    }
}
