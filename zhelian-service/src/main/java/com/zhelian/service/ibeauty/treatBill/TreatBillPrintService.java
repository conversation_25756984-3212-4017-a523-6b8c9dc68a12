package com.zhelian.service.ibeauty.treatBill;

import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.print.PrintUtils;
import com.zhelian.core.print.entity.Print;
import com.zhelian.core.print.entity.PrintDocument;
import com.zhelian.mapper.ibeauty.SaleBillMapper;
import com.zhelian.mapper.ibeauty.TreatBillMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.ReceiptConfigEntity;
import com.zhelian.model.ibeauty.form.input.TreatBillInfoQueryForm;
import com.zhelian.model.ibeauty.form.output.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class TreatBillPrintService {

    @Autowired
    TreatBillMapper treatBillMapper;

    @Autowired
    SaleBillMapper saleBillMapper;


    public BaseOutput getTreatBill(TreatBillInfoQueryForm form) {
        TreatBillInfoOutputForm bill = treatBillMapper.treatBillInfo(form.ID);
        ReceiptConfigEntity receiptConfig = saleBillMapper.getReceiptConfig(SystemUserUtil.getSystemEntityID());
        if (receiptConfig == null) {
            receiptConfig = new ReceiptConfigEntity();
            receiptConfig.EntityID = SystemUserUtil.getSystemEntityID();
            receiptConfig.PrintWidth = 58;
            receiptConfig.PrintQuantity = 1;
            receiptConfig.NameEncrypt = true;
            receiptConfig.MobileEncrypt = true;
            receiptConfig.SaleGoodsOriginPrice = true;
            receiptConfig.SalePromotions = true;
            receiptConfig.SaleRefundBuyQuantity = true;
            receiptConfig.SaleRefundBuyAmount = true;
            receiptConfig.SaleRefundableAmount = true;
            receiptConfig.SaleArrearBuyQuantity = true;
            receiptConfig.SaleArrearBuyAmount = true;
            receiptConfig.TreatGoodsOriginPrice = true;
            receiptConfig.TreatPromotions = true;
            receiptConfig.TreatAmount = true;
            receiptConfig.WriteTextFirst = "谢谢光临，欢迎再来！";
            receiptConfig.WriteTextSecond = "";
            receiptConfig.EntityAddress = true;
            receiptConfig.SaleArrearBalanceAmount = true;
        }

        bill.Product = treatBillMapper.treatBillProductInfo(form.ID);
        bill.Project = treatBillMapper.treatBillProjectInfo(form.ID);
        bill.GeneralCard = treatBillMapper.treatBillGeneralCardInfo(form.ID);
        bill.TimeCard = treatBillMapper.treatBillTimeCardInfo(form.ID);
        bill.SavingCard = treatBillMapper.treatBillSavingCardInfo(form.ID);
        List<TreatBillPackageCardInfoOutputForm> packageCard = treatBillMapper.treatBillPackageCardInfo(form.ID);
        List<TreatBillProductInfoExtendOutputForm> product = treatBillMapper.treatBillPackageCardProductInfo(form.ID);
        List<TreatBillProjectInfoExtendOutputForm> project = treatBillMapper.treatBillPackageCardProjectInfo(form.ID);
        List<TreatBillGeneralCardInfoExtendOutputForm> generalCard = treatBillMapper.treatBillPackageCardGeneralCardInfo(form.ID);
        List<TreatBillTimeCardInfoExtendOutputForm> timeCard = treatBillMapper.treatBillPackageCardTimeCardInfo(form.ID);
        List<TreatBillSavingCardInfoExtendOutputForm> savingCard = treatBillMapper.treatBillPackageCardSavingCardInfo(form.ID);
        for (TreatBillPackageCardInfoOutputForm packageCardForm : packageCard) {
            packageCardForm.Product = product.stream().filter(packageCardProductListOutputForm -> packageCardProductListOutputForm.PackageCardAccountID.intValue() == packageCardForm.PackageCardAccountID.intValue()).collect(Collectors.toList());
            packageCardForm.Project = project.stream().filter(packageCardProjectListOutputForm -> packageCardProjectListOutputForm.PackageCardAccountID.intValue() == packageCardForm.PackageCardAccountID.intValue()).collect(Collectors.toList());
            packageCardForm.GeneralCard = generalCard.stream().filter(packageCardGeneralCardListOutputForm -> packageCardGeneralCardListOutputForm.PackageCardAccountID.intValue() == packageCardForm.PackageCardAccountID.intValue()).collect(Collectors.toList());
            packageCardForm.TimeCard = timeCard.stream().filter(packageCardTimeCardListOutputForm -> packageCardTimeCardListOutputForm.PackageCardAccountID.intValue() == packageCardForm.PackageCardAccountID.intValue()).collect(Collectors.toList());
            packageCardForm.SavingCard = savingCard.stream().filter(packageCardSavingCardListOutputForm -> packageCardSavingCardListOutputForm.PackageCardAccountID.intValue() == packageCardForm.PackageCardAccountID.intValue()).collect(Collectors.toList());
        }


        Print print = new Print();
        print.copies = receiptConfig.PrintQuantity;
        int documentID = 1;

        List<PrintDocument> printDocuments = new ArrayList<PrintDocument>();
        //门店名称
        printDocuments.add(PrintUtils.getTitleDocument(receiptConfig.PrintWidth, documentID++, bill.EntityName));

        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, "订单信息"));

        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "订单编号", form.ID));

        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "下单时间", bill.BillDate));

        if (bill.Name.equals("散客")) {
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "会员姓名", bill.Name));
        } else {
            String newName = "";
            if (bill.Name.length() > 1) {
                newName = bill.Name.substring(0, 1) + bill.Name.substring(1, bill.Name.length()).replaceAll(".{1}", "*");
            } else {
                newName = bill.Name;
            }
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "会员姓名", receiptConfig.NameEncrypt ? newName : bill.Name));
        }
        if (bill.PhoneNumber != null) {
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "会员手机号", receiptConfig.MobileEncrypt ? bill.PhoneNumber.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2") : bill.PhoneNumber));
        } else {
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "会员手机号", ""));

        }


        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "录单人", bill.EmployeeName));
        if (receiptConfig.EntityAddress) {
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "门店地址", bill.AddressDetail));
        }
        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, "消耗明细"));


        if (bill.Project != null && bill.Project.size() > 0) {
            for (int i = 0; i < bill.Project.size(); i++) {
                String flag = bill.Project.get(i).IsLargess ? "【赠】" : "";
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, i + 1 + " " + bill.Project.get(i).ProjectName + flag, receiptConfig.TreatGoodsOriginPrice == false ? "" : "￥ " + bill.Project.get(i).Price.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "数量", bill.Project.get(i).Quantity.toString()));
                if (receiptConfig.TreatPromotions && bill.Project.get(i).CardPreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "优惠金额", "￥ " + bill.Project.get(i).CardPreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                }
                if (receiptConfig.TreatAmount) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + bill.Project.get(i).TotalAmount.toString()));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "现金金额", "￥ " + bill.Project.get(i).PayAmount.toString()));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡抵扣金额", "￥ " + bill.Project.get(i).CardDeductionAmount.toString()));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "赠送卡抵扣金额金额", "￥ " + bill.Project.get(i).LargessCardDeductionAmount.toString()));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "赠送金额", "￥ " + bill.Project.get(i).LargessAmount.toString()));
                }
 /*               List<TreatBillHandlerOutputForm> handlers=bill.Project.get(i).TreatBillHandler;
                if(receiptConfig.Handler) {
                    if(handlers!=null && handlers.size()>0){
                        for (int j = 0; j < handlers.size(); j++) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).TreatHandlerName, ""));
                            List<TreatBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                            for (int k = 0; k < employee.size(); k++) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                            }
                        }
                    }
                }*/

            }

        }
        int savingCardProjectLength = 0;
        if (bill.SavingCard != null && bill.SavingCard.size() > 0) {
            for (TreatBillSavingCardInfoOutputForm savingCardInfoOutputForm : bill.SavingCard) {

                for (int i = 0; i < savingCardInfoOutputForm.Project.size(); i++) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, savingCardProjectLength + bill.Project.size() + i + 1 + " " + savingCardInfoOutputForm.Project.get(i).ProjectName, receiptConfig.TreatGoodsOriginPrice == false ? "" : "￥ " + savingCardInfoOutputForm.Project.get(i).Price.toString()));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "数量", savingCardInfoOutputForm.Project.get(i).Quantity.toString()));
                    if (receiptConfig.TreatPromotions && savingCardInfoOutputForm.Project.get(i).PricePreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "手动改价", "￥ " + savingCardInfoOutputForm.Project.get(i).PricePreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                        if (receiptConfig.TreatPromotions && savingCardInfoOutputForm.Project.get(i).CardPreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡优惠", "￥ " + savingCardInfoOutputForm.Project.get(i).CardPreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                        }
                    } else {
                        if (receiptConfig.TreatPromotions && savingCardInfoOutputForm.Project.get(i).CardPreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡优惠", "￥ " + savingCardInfoOutputForm.Project.get(i).CardPreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                        }
                    }
                    if (receiptConfig.TreatAmount) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + savingCardInfoOutputForm.Project.get(i).TotalAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡抵扣金额", "￥ " + savingCardInfoOutputForm.Project.get(i).CardDeductionAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, " 赠送卡抵扣金额金额", "￥ " + savingCardInfoOutputForm.Project.get(i).LargessCardDeductionAmount.toString()));
                    }
  /*                  List<TreatBillHandlerOutputForm> handlers=savingCardInfoOutputForm.Project.get(i).TreatBillHandler;
                    if(receiptConfig.Handler) {
                        if(handlers!=null && handlers.size()>0){
                            for (int j = 0; j < handlers.size(); j++) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).TreatHandlerName, ""));
                                List<TreatBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                                for (int k = 0; k < employee.size(); k++) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                                }
                            }
                        }
                    }*/
                }
                savingCardProjectLength = savingCardProjectLength + savingCardInfoOutputForm.Project.size();
            }
        }
        int timeCardProjectLength = 0;
        if (bill.TimeCard != null && bill.TimeCard.size() > 0) {
            for (TreatBillTimeCardInfoOutputForm timeCardInfoOutputForm : bill.TimeCard) {
                String flag = timeCardInfoOutputForm.IsLargess ? "【赠】" : "";
                for (int i = 0; i < timeCardInfoOutputForm.Project.size(); i++) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, timeCardProjectLength + bill.Project.size() + savingCardProjectLength + i + 1 + " " + timeCardInfoOutputForm.Project.get(i).ProjectName + flag, receiptConfig.TreatGoodsOriginPrice == false ? "" : "￥ " + timeCardInfoOutputForm.Project.get(i).Price.toString()));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "数量", timeCardInfoOutputForm.Project.get(i).Quantity.toString()));
                    if (receiptConfig.TreatPromotions && timeCardInfoOutputForm.Project.get(i).CardPreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡优惠", "￥ " + timeCardInfoOutputForm.Project.get(i).CardPreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                    }
                    if (receiptConfig.TreatAmount) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + timeCardInfoOutputForm.Project.get(i).TotalAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "现金金额", "￥ " + timeCardInfoOutputForm.Project.get(i).PayAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡抵扣金额", "￥ " + timeCardInfoOutputForm.Project.get(i).CardDeductionAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "赠送卡抵扣金额金额", "￥ " + timeCardInfoOutputForm.Project.get(i).LargessCardDeductionAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "赠送金额", "￥ " + timeCardInfoOutputForm.Project.get(i).LargessAmount.toString()));
                    }
   /*                 List<TreatBillHandlerOutputForm> handlers=timeCardInfoOutputForm.Project.get(i).TreatBillHandler;
                    if(receiptConfig.Handler) {
                        if(handlers!=null && handlers.size()>0){
                            for (int j = 0; j < handlers.size(); j++) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).TreatHandlerName, ""));
                                List<TreatBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                                for (int k = 0; k < employee.size(); k++) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                                }
                            }
                        }
                    }*/
                }
                timeCardProjectLength = timeCardProjectLength + timeCardInfoOutputForm.Project.size();
            }
        }
        int generalCardProjectLength = 0;
        if (bill.GeneralCard != null && bill.GeneralCard.size() > 0) {
            for (TreatBillGeneralCardInfoOutputForm generalCardInfoOutputForm : bill.GeneralCard) {
                String flag = generalCardInfoOutputForm.IsLargess ? "【赠】" : "";
                for (int i = 0; i < generalCardInfoOutputForm.Project.size(); i++) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, generalCardProjectLength + bill.Project.size() + savingCardProjectLength + timeCardProjectLength + i + 1 + " " + generalCardInfoOutputForm.Project.get(i).ProjectName + flag, receiptConfig.TreatGoodsOriginPrice == false ? "" : "￥ " + generalCardInfoOutputForm.Project.get(i).Price.toString()));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "数量", generalCardInfoOutputForm.Project.get(i).Quantity.toString()));
                    if (receiptConfig.TreatPromotions && generalCardInfoOutputForm.Project.get(i).CardPreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡优惠", "￥ " + generalCardInfoOutputForm.Project.get(i).CardPreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                    }
                    if (receiptConfig.TreatAmount) {
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + generalCardInfoOutputForm.Project.get(i).TotalAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "现金金额", "￥ " + generalCardInfoOutputForm.Project.get(i).PayAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡抵扣金额", "￥ " + generalCardInfoOutputForm.Project.get(i).CardDeductionAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "赠送卡抵扣金额金额", "￥ " + generalCardInfoOutputForm.Project.get(i).LargessCardDeductionAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "赠送金额", "￥ " + generalCardInfoOutputForm.Project.get(i).LargessAmount.toString()));
                    }
/*                    List<TreatBillHandlerOutputForm> handlers=generalCardInfoOutputForm.Project.get(i).TreatBillHandler;
                    if(receiptConfig.Handler) {
                        if(handlers!=null && handlers.size()>0){
                            for (int j = 0; j < handlers.size(); j++) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).TreatHandlerName, ""));
                                List<TreatBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                                for (int k = 0; k < employee.size(); k++) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                                }
                            }
                        }
                    }*/
                }
                generalCardProjectLength = generalCardProjectLength + generalCardInfoOutputForm.Project.size();
            }
        }
        if (bill.Product != null && bill.Product.size() > 0) {
            for (int i = 0; i < bill.Product.size(); i++) {
                String flag = bill.Product.get(i).IsLargess ? "【赠】" : "";
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, bill.Project.size() + savingCardProjectLength + timeCardProjectLength + generalCardProjectLength + i + 1 + " " + bill.Product.get(i).ProductName + flag, receiptConfig.TreatGoodsOriginPrice == false ? "" : "￥ " + bill.Product.get(i).Price.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "数量", bill.Product.get(i).Quantity.toString()));
                if (receiptConfig.TreatPromotions && bill.Product.get(i).CardPreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "优惠金额", "￥ " + bill.Product.get(i).CardPreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                }
                if (receiptConfig.TreatAmount) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + bill.Product.get(i).TotalAmount.toString()));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "现金金额", "￥ " + bill.Product.get(i).PayAmount.toString()));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡抵扣金额", "￥ " + bill.Product.get(i).CardDeductionAmount.toString()));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "赠送卡抵扣金额金额", "￥ " + bill.Product.get(i).LargessCardDeductionAmount.toString()));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "赠送金额", "￥ " + bill.Product.get(i).LargessAmount.toString()));
                }
/*                List<TreatBillHandlerOutputForm> handlers=bill.Product.get(i).TreatBillHandler;
                if(receiptConfig.Handler) {
                    if(handlers!=null && handlers.size()>0){
                        for (int j = 0; j < handlers.size(); j++) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).TreatHandlerName, ""));
                            List<TreatBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                            for (int k = 0; k < employee.size(); k++) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                            }
                        }
                    }
                }*/
            }

        }
        int length = bill.Project.size() + savingCardProjectLength + timeCardProjectLength + generalCardProjectLength + bill.Product.size();
        if (packageCard != null && packageCard.size() > 0) {
            for (TreatBillPackageCardInfoOutputForm packageCardInfoOutputForm : packageCard) {
                if (packageCardInfoOutputForm.Project != null && packageCardInfoOutputForm.Project.size() > 0) {
                    for (int i = 0; i < packageCardInfoOutputForm.Project.size(); i++) {
                        String flag = packageCardInfoOutputForm.Project.get(i).IsLargess ? "【赠】" : "";
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, length + i + 1 + " " + packageCardInfoOutputForm.Project.get(i).ProjectName + flag, receiptConfig.TreatGoodsOriginPrice == false ? "" : "￥ " + packageCardInfoOutputForm.Project.get(i).Price.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "数量", packageCardInfoOutputForm.Project.get(i).Quantity.toString()));
                        if (receiptConfig.TreatPromotions && packageCardInfoOutputForm.Project.get(i).CardPreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "优惠金额", "￥ " + packageCardInfoOutputForm.Project.get(i).CardPreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                        }
                        if (receiptConfig.TreatAmount) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + packageCardInfoOutputForm.Project.get(i).TotalAmount.toString()));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "现金金额", "￥ " + packageCardInfoOutputForm.Project.get(i).PayAmount.toString()));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡抵扣金额", "￥ " + packageCardInfoOutputForm.Project.get(i).CardDeductionAmount.toString()));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "赠送卡抵扣金额金额", "￥ " + packageCardInfoOutputForm.Project.get(i).LargessCardDeductionAmount.toString()));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "赠送金额", "￥ " + packageCardInfoOutputForm.Project.get(i).LargessAmount.toString()));
                        }
   /*                 List<TreatBillHandlerOutputForm> handlers=packageCardInfoOutputForm.Project.get(i).TreatBillHandler;
                    if(receiptConfig.Handler) {
                        if(handlers!=null && handlers.size()>0){
                            for (int j = 0; j < handlers.size(); j++) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).TreatHandlerName, ""));
                                List<TreatBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                                for (int k = 0; k < employee.size(); k++) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                                }
                            }
                        }
                    }*/
                    }

                }
                int packageSavingCardProjectLength = 0;
                if (packageCardInfoOutputForm.SavingCard != null && packageCardInfoOutputForm.SavingCard.size() > 0) {
                    for (TreatBillSavingCardInfoOutputForm savingCardInfoOutputForm : packageCardInfoOutputForm.SavingCard) {

                        for (int i = 0; i < savingCardInfoOutputForm.Project.size(); i++) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, packageSavingCardProjectLength + length + packageCardInfoOutputForm.Project.size() + i + 1 + " " + savingCardInfoOutputForm.Project.get(i).ProjectName, receiptConfig.TreatGoodsOriginPrice == false ? "" : "￥ " + savingCardInfoOutputForm.Project.get(i).Price.toString()));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "数量", savingCardInfoOutputForm.Project.get(i).Quantity.toString()));
                            if (receiptConfig.TreatPromotions && savingCardInfoOutputForm.Project.get(i).PricePreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "手动改价", "￥ " + savingCardInfoOutputForm.Project.get(i).PricePreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                                if (receiptConfig.TreatPromotions && savingCardInfoOutputForm.Project.get(i).CardPreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡优惠", "￥ " + savingCardInfoOutputForm.Project.get(i).CardPreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                                }
                            } else {
                                if (receiptConfig.TreatPromotions && savingCardInfoOutputForm.Project.get(i).CardPreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡优惠", "￥ " + savingCardInfoOutputForm.Project.get(i).CardPreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                                }
                            }
                            if (receiptConfig.TreatAmount) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + savingCardInfoOutputForm.Project.get(i).TotalAmount.toString()));
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡抵扣金额", "￥ " + savingCardInfoOutputForm.Project.get(i).CardDeductionAmount.toString()));
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "赠送卡抵扣金额金额", "￥ " + savingCardInfoOutputForm.Project.get(i).LargessCardDeductionAmount.toString()));
                            }
                            List<TreatBillHandlerOutputForm> handlers = savingCardInfoOutputForm.Project.get(i).TreatBillHandler;
   /*                     if(receiptConfig.Handler) {
                            if(handlers!=null && handlers.size()>0){
                                for (int j = 0; j < handlers.size(); j++) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).TreatHandlerName, ""));
                                    List<TreatBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                                    for (int k = 0; k < employee.size(); k++) {
                                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                                    }
                                }
                            }
                        }*/
                        }
                        packageSavingCardProjectLength = packageSavingCardProjectLength + savingCardInfoOutputForm.Project.size();
                    }
                }
                int packageTimeCardProjectLength = 0;
                if (packageCardInfoOutputForm.TimeCard != null && packageCardInfoOutputForm.TimeCard.size() > 0) {
                    for (TreatBillTimeCardInfoOutputForm timeCardInfoOutputForm : packageCardInfoOutputForm.TimeCard) {
                        String flag = timeCardInfoOutputForm.IsLargess ? "【赠】" : "";
                        for (int i = 0; i < timeCardInfoOutputForm.Project.size(); i++) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, packageTimeCardProjectLength + length + packageCardInfoOutputForm.Project.size() + packageSavingCardProjectLength + i + 1 + " " + timeCardInfoOutputForm.Project.get(i).ProjectName + flag, receiptConfig.TreatGoodsOriginPrice == false ? "" : "￥ " + timeCardInfoOutputForm.Project.get(i).Price.toString()));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "数量", timeCardInfoOutputForm.Project.get(i).Quantity.toString()));
                            if (receiptConfig.TreatPromotions && timeCardInfoOutputForm.Project.get(i).CardPreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡优惠", "￥ " + timeCardInfoOutputForm.Project.get(i).CardPreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                            }
                            if (receiptConfig.TreatAmount) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + timeCardInfoOutputForm.Project.get(i).TotalAmount.toString()));
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "现金金额", "￥ " + timeCardInfoOutputForm.Project.get(i).PayAmount.toString()));
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡抵扣金额", "￥ " + timeCardInfoOutputForm.Project.get(i).CardDeductionAmount.toString()));
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "赠送卡抵扣金额金额", "￥ " + timeCardInfoOutputForm.Project.get(i).LargessCardDeductionAmount.toString()));
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "赠送金额", "￥ " + timeCardInfoOutputForm.Project.get(i).LargessAmount.toString()));
                            }
        /*                List<TreatBillHandlerOutputForm> handlers=timeCardInfoOutputForm.Project.get(i).TreatBillHandler;
                        if(receiptConfig.Handler) {
                            if(handlers!=null && handlers.size()>0){
                                for (int j = 0; j < handlers.size(); j++) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).TreatHandlerName, ""));
                                    List<TreatBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                                    for (int k = 0; k < employee.size(); k++) {
                                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                                    }
                                }
                            }
                        }*/
                        }
                        packageTimeCardProjectLength = packageTimeCardProjectLength + timeCardInfoOutputForm.Project.size();
                    }
                }
                int packageGeneralCardProjectLength = 0;
                if (packageCardInfoOutputForm.GeneralCard != null && packageCardInfoOutputForm.GeneralCard.size() > 0) {
                    for (TreatBillGeneralCardInfoOutputForm generalCardInfoOutputForm : packageCardInfoOutputForm.GeneralCard) {
                        String flag = generalCardInfoOutputForm.IsLargess ? "【赠】" : "";
                        for (int i = 0; i < generalCardInfoOutputForm.Project.size(); i++) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, packageGeneralCardProjectLength + length + packageCardInfoOutputForm.Project.size() + packageSavingCardProjectLength + packageTimeCardProjectLength + i + 1 + " " + generalCardInfoOutputForm.Project.get(i).ProjectName + flag, receiptConfig.TreatGoodsOriginPrice == false ? "" : "￥ " + generalCardInfoOutputForm.Project.get(i).Price.toString()));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "数量", generalCardInfoOutputForm.Project.get(i).Quantity.toString()));
                            if (receiptConfig.TreatPromotions && generalCardInfoOutputForm.Project.get(i).CardPreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡优惠", "￥ " + generalCardInfoOutputForm.Project.get(i).CardPreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                            }
                            if (receiptConfig.TreatAmount) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + generalCardInfoOutputForm.Project.get(i).TotalAmount.toString()));
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "现金金额", "￥ " + generalCardInfoOutputForm.Project.get(i).PayAmount.toString()));
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡抵扣金额", "￥ " + generalCardInfoOutputForm.Project.get(i).CardDeductionAmount.toString()));
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "赠送卡抵扣金额金额", "￥ " + generalCardInfoOutputForm.Project.get(i).LargessCardDeductionAmount.toString()));
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "赠送金额", "￥ " + generalCardInfoOutputForm.Project.get(i).LargessAmount.toString()));
                            }
         /*               List<TreatBillHandlerOutputForm> handlers=generalCardInfoOutputForm.Project.get(i).TreatBillHandler;
                        if(receiptConfig.Handler) {
                            if(handlers!=null && handlers.size()>0){
                                for (int j = 0; j < handlers.size(); j++) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).TreatHandlerName, ""));
                                    List<TreatBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                                    for (int k = 0; k < employee.size(); k++) {
                                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                                    }
                                }
                            }
                        }*/
                        }
                        packageGeneralCardProjectLength = packageGeneralCardProjectLength + generalCardInfoOutputForm.Project.size();
                    }
                }
                if (packageCardInfoOutputForm.Product != null && packageCardInfoOutputForm.Product.size() > 0) {
                    for (int i = 0; i < packageCardInfoOutputForm.Product.size(); i++) {
                        String flag = packageCardInfoOutputForm.Product.get(i).IsLargess ? "【赠】" : "";
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, length + packageCardInfoOutputForm.Project.size() + packageSavingCardProjectLength + packageTimeCardProjectLength + packageGeneralCardProjectLength + i + 1 + " " + packageCardInfoOutputForm.Product.get(i).ProductName + flag, receiptConfig.TreatGoodsOriginPrice == false ? "" : "￥ " + packageCardInfoOutputForm.Product.get(i).Price.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "数量", packageCardInfoOutputForm.Product.get(i).Quantity.toString()));
                        if (receiptConfig.TreatPromotions && packageCardInfoOutputForm.Product.get(i).CardPreferentialAmount.compareTo(BigDecimal.ZERO) != 0) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "优惠金额", "￥ " + packageCardInfoOutputForm.Product.get(i).CardPreferentialAmount.multiply(new BigDecimal(-1).setScale(2)).toString()));
                        }
                        if (receiptConfig.TreatAmount) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "小计", "￥ " + packageCardInfoOutputForm.Product.get(i).TotalAmount.toString()));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "现金金额", "￥ " + packageCardInfoOutputForm.Product.get(i).PayAmount.toString()));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡抵扣金额", "￥ " + packageCardInfoOutputForm.Product.get(i).CardDeductionAmount.toString()));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "赠送卡抵扣金额金额", "￥ " + packageCardInfoOutputForm.Product.get(i).LargessCardDeductionAmount.toString()));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "赠送金额", "￥ " + packageCardInfoOutputForm.Product.get(i).LargessAmount.toString()));
                        }
      /*              List<TreatBillHandlerOutputForm> handlers=packageCardInfoOutputForm.Product.get(i).TreatBillHandler;
                    if(receiptConfig.Handler) {
                        if(handlers!=null && handlers.size()>0){
                            for (int j = 0; j < handlers.size(); j++) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).TreatHandlerName, ""));
                                List<TreatBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                                for (int k = 0; k < employee.size(); k++) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                                }
                            }
                        }
                    }*/
                    }

                }
            }

        }

        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, ""));
        if (receiptConfig.TreatAmount) {
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "合计", "￥ " + bill.Amount.toString()));
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "现金金额", "￥ " + bill.PayAmount.toString()));
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡抵扣金额", "￥ " + bill.CardDeductionAmount.toString()));
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "赠送卡抵扣金额金额", "￥ " + bill.LargessCardDeductionAmount.toString()));
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "赠送金额", "￥ " + bill.LargessAmount.toString()));
        }
        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, receiptConfig.WriteTextFirst));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, receiptConfig.WriteTextSecond));

        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, ""));


        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "客户签字", ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, ""));
        print.printDocuments = printDocuments;
        return BaseOutputForm.success(print);
    }

    public BaseOutput getRefundTreatBill(TreatBillInfoQueryForm form) {
        TreatBillInfoOutputForm bill = treatBillMapper.treatBillInfo(form.ID);
        ReceiptConfigEntity receiptConfig = saleBillMapper.getReceiptConfig(SystemUserUtil.getSystemEntityID());
        if (receiptConfig == null) {
            receiptConfig = new ReceiptConfigEntity();
            receiptConfig.EntityID = SystemUserUtil.getSystemEntityID();
            receiptConfig.PrintWidth = 58;
            receiptConfig.PrintQuantity = 1;
            receiptConfig.NameEncrypt = true;
            receiptConfig.MobileEncrypt = true;
            receiptConfig.SaleGoodsOriginPrice = true;
            receiptConfig.SalePromotions = true;
            receiptConfig.SaleRefundBuyQuantity = true;
            receiptConfig.SaleRefundBuyAmount = true;
            receiptConfig.SaleRefundableAmount = true;
            receiptConfig.SaleArrearBuyQuantity = true;
            receiptConfig.SaleArrearBuyAmount = true;
            receiptConfig.TreatGoodsOriginPrice = true;
            receiptConfig.TreatPromotions = true;
            receiptConfig.WriteTextFirst = "谢谢光临，欢迎再来！";
            receiptConfig.WriteTextSecond = "";
            receiptConfig.EntityAddress = true;
            receiptConfig.SaleArrearBalanceAmount = true;
        }

        bill.Product = treatBillMapper.treatBillProductInfo(form.ID);
        bill.Project = treatBillMapper.treatBillProjectInfo(form.ID);
        bill.GeneralCard = treatBillMapper.treatBillGeneralCardInfo(form.ID);
        bill.TimeCard = treatBillMapper.treatBillTimeCardInfo(form.ID);
        bill.SavingCard = treatBillMapper.treatBillSavingCardInfo(form.ID);
        List<TreatBillPackageCardInfoOutputForm> packageCard = treatBillMapper.treatBillPackageCardInfo(form.ID);
        List<TreatBillProductInfoExtendOutputForm> product = treatBillMapper.treatBillPackageCardProductInfo(form.ID);
        List<TreatBillProjectInfoExtendOutputForm> project = treatBillMapper.treatBillPackageCardProjectInfo(form.ID);
        List<TreatBillGeneralCardInfoExtendOutputForm> generalCard = treatBillMapper.treatBillPackageCardGeneralCardInfo(form.ID);
        List<TreatBillTimeCardInfoExtendOutputForm> timeCard = treatBillMapper.treatBillPackageCardTimeCardInfo(form.ID);
        List<TreatBillSavingCardInfoExtendOutputForm> savingCard = treatBillMapper.treatBillPackageCardSavingCardInfo(form.ID);
        for (TreatBillPackageCardInfoOutputForm packageCardForm : packageCard) {
            packageCardForm.Product = product.stream().filter(packageCardProductListOutputForm -> packageCardProductListOutputForm.PackageCardAccountID.intValue() == packageCardForm.PackageCardAccountID.intValue()).collect(Collectors.toList());
            packageCardForm.Project = project.stream().filter(packageCardProjectListOutputForm -> packageCardProjectListOutputForm.PackageCardAccountID.intValue() == packageCardForm.PackageCardAccountID.intValue()).collect(Collectors.toList());
            packageCardForm.GeneralCard = generalCard.stream().filter(packageCardGeneralCardListOutputForm -> packageCardGeneralCardListOutputForm.PackageCardAccountID.intValue() == packageCardForm.PackageCardAccountID.intValue()).collect(Collectors.toList());
            packageCardForm.TimeCard = timeCard.stream().filter(packageCardTimeCardListOutputForm -> packageCardTimeCardListOutputForm.PackageCardAccountID.intValue() == packageCardForm.PackageCardAccountID.intValue()).collect(Collectors.toList());
            packageCardForm.SavingCard = savingCard.stream().filter(packageCardSavingCardListOutputForm -> packageCardSavingCardListOutputForm.PackageCardAccountID.intValue() == packageCardForm.PackageCardAccountID.intValue()).collect(Collectors.toList());
        }

        Print print = new Print();
        print.copies = receiptConfig.PrintQuantity;
        int documentID = 1;

        List<PrintDocument> printDocuments = new ArrayList<PrintDocument>();
        //门店名称
        printDocuments.add(PrintUtils.getTitleDocument(receiptConfig.PrintWidth, documentID++, bill.EntityName));

        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, "退消耗信息"));

        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "订单编号", form.ID));

        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "下单时间", bill.BillDate));

        if (bill.Name.equals("散客")) {
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "会员姓名", bill.Name));
        } else {
            String newName = "";
            if (bill.Name.length() > 1) {
                newName = bill.Name.substring(0, 1) + bill.Name.substring(1, bill.Name.length()).replaceAll(".{1}", "*");
            } else {
                newName = bill.Name;
            }
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "会员姓名", receiptConfig.NameEncrypt ? newName : bill.Name));
        }
        if (bill.PhoneNumber != null) {
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "会员手机号", receiptConfig.MobileEncrypt ? bill.PhoneNumber.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2") : bill.PhoneNumber));
        } else {
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "会员手机号", ""));

        }


        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "录单人", bill.EmployeeName));
        if (receiptConfig.EntityAddress) {
            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "门店地址", bill.AddressDetail));
        }
        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, "退消耗明细"));


        if (bill.Project != null && bill.Project.size() > 0) {
            for (int i = 0; i < bill.Project.size(); i++) {
                String flag = bill.Project.get(i).IsLargess ? "【赠】" : "";
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, i + 1 + " " + bill.Project.get(i).ProjectName + flag, ""));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗数量", bill.Project.get(i).Quantity.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗金额", "￥ " + bill.Project.get(i).TotalAmount.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗现金金额", "￥ " + bill.Project.get(i).PayAmount.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗卡抵扣金额", "￥ " + bill.Project.get(i).CardDeductionAmount.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗赠送卡抵扣金额金额", "￥ " + bill.Project.get(i).LargessCardDeductionAmount.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗赠送金额", "￥ " + bill.Project.get(i).LargessAmount.toString()));
     /*           List<TreatBillHandlerOutputForm> handlers=bill.Project.get(i).TreatBillHandler;
                if(receiptConfig.Handler) {
                    if(handlers!=null && handlers.size()>0){
                        for (int j = 0; j < handlers.size(); j++) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).TreatHandlerName, ""));
                            List<TreatBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                            for (int k = 0; k < employee.size(); k++) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                            }
                        }
                    }
                }*/
            }

        }
        int savingCardProjectLength = 0;
        if (bill.SavingCard != null && bill.SavingCard.size() > 0) {
            for (TreatBillSavingCardInfoOutputForm savingCardInfoOutputForm : bill.SavingCard) {

                for (int i = 0; i < savingCardInfoOutputForm.Project.size(); i++) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, savingCardProjectLength + bill.Project.size() + i + 1 + " " + savingCardInfoOutputForm.Project.get(i).ProjectName, ""));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗数量", savingCardInfoOutputForm.Project.get(i).Quantity.toString()));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗金额", "￥ " + savingCardInfoOutputForm.Project.get(i).TotalAmount.toString()));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗卡抵扣金额", "￥ " + savingCardInfoOutputForm.Project.get(i).CardDeductionAmount.toString()));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗赠送卡抵扣金额金额", "￥ " + savingCardInfoOutputForm.Project.get(i).LargessCardDeductionAmount.toString()));
/*                    List<TreatBillHandlerOutputForm> handlers=savingCardInfoOutputForm.Project.get(i).TreatBillHandler;
                    if(receiptConfig.Handler) {
                        if(handlers!=null && handlers.size()>0){
                            for (int j = 0; j < handlers.size(); j++) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).TreatHandlerName, ""));
                                List<TreatBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                                for (int k = 0; k < employee.size(); k++) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                                }
                            }
                        }
                    }*/
                }
                savingCardProjectLength = savingCardProjectLength + savingCardInfoOutputForm.Project.size();
            }
        }
        int timeCardProjectLength = 0;
        if (bill.TimeCard != null && bill.TimeCard.size() > 0) {
            for (TreatBillTimeCardInfoOutputForm timeCardInfoOutputForm : bill.TimeCard) {
                String flag = timeCardInfoOutputForm.IsLargess ? "【赠】" : "";
                for (int i = 0; i < timeCardInfoOutputForm.Project.size(); i++) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, timeCardProjectLength + bill.Project.size() + savingCardProjectLength + i + 1 + " " + timeCardInfoOutputForm.Project.get(i).ProjectName + flag, ""));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗数量", timeCardInfoOutputForm.Project.get(i).Quantity.toString()));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗金额", "￥ " + timeCardInfoOutputForm.Project.get(i).TotalAmount.toString()));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗现金金额", "￥ " + timeCardInfoOutputForm.Project.get(i).PayAmount.toString()));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗卡抵扣金额", "￥ " + timeCardInfoOutputForm.Project.get(i).CardDeductionAmount.toString()));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗赠送卡抵扣金额金额", "￥ " + timeCardInfoOutputForm.Project.get(i).LargessCardDeductionAmount.toString()));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗赠送金额", "￥ " + timeCardInfoOutputForm.Project.get(i).LargessAmount.toString()));
      /*             List<TreatBillHandlerOutputForm> handlers=timeCardInfoOutputForm.Project.get(i).TreatBillHandler;
                    if(receiptConfig.Handler) {
                        if(handlers!=null && handlers.size()>0){
                            for (int j = 0; j < handlers.size(); j++) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).TreatHandlerName, ""));
                                List<TreatBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                                for (int k = 0; k < employee.size(); k++) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                                }
                            }
                        }
                    }*/
                }
                timeCardProjectLength = timeCardProjectLength + timeCardInfoOutputForm.Project.size();
            }
        }
        int generalCardProjectLength = 0;
        if (bill.GeneralCard != null && bill.GeneralCard.size() > 0) {
            for (TreatBillGeneralCardInfoOutputForm generalCardInfoOutputForm : bill.GeneralCard) {
                String flag = generalCardInfoOutputForm.IsLargess ? "【赠】" : "";
                for (int i = 0; i < generalCardInfoOutputForm.Project.size(); i++) {
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, generalCardProjectLength + bill.Project.size() + savingCardProjectLength + timeCardProjectLength + i + 1 + " " + generalCardInfoOutputForm.Project.get(i).ProjectName + flag, ""));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗数量", generalCardInfoOutputForm.Project.get(i).Quantity.toString()));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗金额", "￥ " + generalCardInfoOutputForm.Project.get(i).TotalAmount.toString()));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗现金金额", "￥ " + generalCardInfoOutputForm.Project.get(i).PayAmount.toString()));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗卡抵扣金额", "￥ " + generalCardInfoOutputForm.Project.get(i).CardDeductionAmount.toString()));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗赠送卡抵扣金额金额", "￥ " + generalCardInfoOutputForm.Project.get(i).LargessCardDeductionAmount.toString()));
                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗赠送金额", "￥ " + generalCardInfoOutputForm.Project.get(i).LargessAmount.toString()));
      /*              List<TreatBillHandlerOutputForm> handlers=generalCardInfoOutputForm.Project.get(i).TreatBillHandler;
                    if(receiptConfig.Handler) {
                        if(handlers!=null && handlers.size()>0){
                            for (int j = 0; j < handlers.size(); j++) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).TreatHandlerName, ""));
                                List<TreatBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                                for (int k = 0; k < employee.size(); k++) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                                }
                            }
                        }
                    }*/
                }
                generalCardProjectLength = generalCardProjectLength + generalCardInfoOutputForm.Project.size();
            }
        }
        if (bill.Product != null && bill.Product.size() > 0) {
            for (int i = 0; i < bill.Product.size(); i++) {
                String flag = bill.Product.get(i).IsLargess ? "【赠】" : "";
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, bill.Project.size() + savingCardProjectLength + timeCardProjectLength + generalCardProjectLength + i + 1 + " " + bill.Product.get(i).ProductName + flag, ""));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗数量", bill.Product.get(i).Quantity.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗金额", "￥ " + bill.Product.get(i).TotalAmount.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗现金金额", "￥ " + bill.Product.get(i).PayAmount.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗卡抵扣金额", "￥ " + bill.Product.get(i).CardDeductionAmount.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗赠送卡抵扣金额金额", "￥ " + bill.Product.get(i).LargessCardDeductionAmount.toString()));
                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗赠送金额", "￥ " + bill.Product.get(i).LargessAmount.toString()));
                /*            List<TreatBillHandlerOutputForm> handlers=bill.Product.get(i).TreatBillHandler;
                if(receiptConfig.Handler) {
                    if(handlers!=null && handlers.size()>0){
                        for (int j = 0; j < handlers.size(); j++) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).TreatHandlerName, ""));
                            List<TreatBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                            for (int k = 0; k < employee.size(); k++) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                            }
                        }
                    }
                }*/
            }

        }
        int length = bill.Project.size() + savingCardProjectLength + timeCardProjectLength + generalCardProjectLength + bill.Product.size();
        if (packageCard != null && packageCard.size() > 0) {
            for (TreatBillPackageCardInfoOutputForm packageCardInfoOutputForm : packageCard) {
                if (packageCardInfoOutputForm.Project != null && packageCardInfoOutputForm.Project.size() > 0) {
                    for (int i = 0; i < packageCardInfoOutputForm.Project.size(); i++) {
                        String flag = packageCardInfoOutputForm.Project.get(i).IsLargess ? "【赠】" : "";
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, length + i + 1 + " " + packageCardInfoOutputForm.Project.get(i).ProjectName + flag, ""));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗数量", packageCardInfoOutputForm.Project.get(i).Quantity.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗金额", "￥ " + packageCardInfoOutputForm.Project.get(i).TotalAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗现金金额", "￥ " + packageCardInfoOutputForm.Project.get(i).PayAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗卡抵扣金额", "￥ " + packageCardInfoOutputForm.Project.get(i).CardDeductionAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗赠送卡抵扣金额金额", "￥ " + packageCardInfoOutputForm.Project.get(i).LargessCardDeductionAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗赠送金额", "￥ " + packageCardInfoOutputForm.Project.get(i).LargessAmount.toString()));
                        List<TreatBillHandlerOutputForm> handlers = packageCardInfoOutputForm.Project.get(i).TreatBillHandler;
     /*               if(receiptConfig.Handler) {
                        if(handlers!=null && handlers.size()>0){
                            for (int j = 0; j < handlers.size(); j++) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).TreatHandlerName, ""));
                                List<TreatBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                                for (int k = 0; k < employee.size(); k++) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                                }
                            }
                        }
                    }*/
                    }

                }
                int packageSavingCardProjectLength = 0;
                if (packageCardInfoOutputForm.SavingCard != null && packageCardInfoOutputForm.SavingCard.size() > 0) {
                    for (TreatBillSavingCardInfoOutputForm savingCardInfoOutputForm : packageCardInfoOutputForm.SavingCard) {

                        for (int i = 0; i < savingCardInfoOutputForm.Project.size(); i++) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, packageSavingCardProjectLength + length + packageCardInfoOutputForm.Project.size() + i + 1 + " " + savingCardInfoOutputForm.Project.get(i).ProjectName, ""));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗数量", savingCardInfoOutputForm.Project.get(i).Quantity.toString()));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗金额", "￥ " + savingCardInfoOutputForm.Project.get(i).TotalAmount.toString()));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗卡抵扣金额", "￥ " + savingCardInfoOutputForm.Project.get(i).CardDeductionAmount.toString()));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗赠送卡抵扣金额金额", "￥ " + savingCardInfoOutputForm.Project.get(i).LargessCardDeductionAmount.toString()));
                            List<TreatBillHandlerOutputForm> handlers = savingCardInfoOutputForm.Project.get(i).TreatBillHandler;
           /*             if(receiptConfig.Handler) {
                            if(handlers!=null && handlers.size()>0){
                                for (int j = 0; j < handlers.size(); j++) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).TreatHandlerName, ""));
                                    List<TreatBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                                    for (int k = 0; k < employee.size(); k++) {
                                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                                    }
                                }
                            }
                        }*/
                        }
                        packageSavingCardProjectLength = packageSavingCardProjectLength + savingCardInfoOutputForm.Project.size();
                    }
                }
                int packageTimeCardProjectLength = 0;
                if (packageCardInfoOutputForm.TimeCard != null && packageCardInfoOutputForm.TimeCard.size() > 0) {
                    for (TreatBillTimeCardInfoOutputForm timeCardInfoOutputForm : packageCardInfoOutputForm.TimeCard) {
                        String flag = timeCardInfoOutputForm.IsLargess ? "【赠】" : "";
                        for (int i = 0; i < timeCardInfoOutputForm.Project.size(); i++) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, packageTimeCardProjectLength + length + packageCardInfoOutputForm.Project.size() + packageSavingCardProjectLength + i + 1 + " " + timeCardInfoOutputForm.Project.get(i).ProjectName + flag, ""));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗数量", timeCardInfoOutputForm.Project.get(i).Quantity.toString()));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗金额", "￥ " + timeCardInfoOutputForm.Project.get(i).TotalAmount.toString()));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗现金金额", "￥ " + timeCardInfoOutputForm.Project.get(i).PayAmount.toString()));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗卡抵扣金额", "￥ " + timeCardInfoOutputForm.Project.get(i).CardDeductionAmount.toString()));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗赠送卡抵扣金额金额", "￥ " + timeCardInfoOutputForm.Project.get(i).LargessCardDeductionAmount.toString()));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗赠送金额", "￥ " + timeCardInfoOutputForm.Project.get(i).LargessAmount.toString()));
          /*              List<TreatBillHandlerOutputForm> handlers=timeCardInfoOutputForm.Project.get(i).TreatBillHandler;
                        if(receiptConfig.Handler) {
                            if(handlers!=null && handlers.size()>0){
                                for (int j = 0; j < handlers.size(); j++) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).TreatHandlerName, ""));
                                    List<TreatBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                                    for (int k = 0; k < employee.size(); k++) {
                                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                                    }
                                }
                            }
                        }*/
                        }
                        packageTimeCardProjectLength = packageTimeCardProjectLength + timeCardInfoOutputForm.Project.size();
                    }
                }
                int packageGeneralCardProjectLength = 0;
                if (packageCardInfoOutputForm.GeneralCard != null && packageCardInfoOutputForm.GeneralCard.size() > 0) {
                    for (TreatBillGeneralCardInfoOutputForm generalCardInfoOutputForm : packageCardInfoOutputForm.GeneralCard) {
                        String flag = generalCardInfoOutputForm.IsLargess ? "【赠】" : "";
                        for (int i = 0; i < generalCardInfoOutputForm.Project.size(); i++) {
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, packageGeneralCardProjectLength + length + packageCardInfoOutputForm.Project.size() + packageSavingCardProjectLength + packageTimeCardProjectLength + i + 1 + " " + generalCardInfoOutputForm.Project.get(i).ProjectName + flag, ""));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗数量", generalCardInfoOutputForm.Project.get(i).Quantity.toString()));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗金额", "￥ " + generalCardInfoOutputForm.Project.get(i).TotalAmount.toString()));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗现金金额", "￥ " + generalCardInfoOutputForm.Project.get(i).PayAmount.toString()));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗卡抵扣金额", "￥ " + generalCardInfoOutputForm.Project.get(i).CardDeductionAmount.toString()));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗赠送卡抵扣金额金额", "￥ " + generalCardInfoOutputForm.Project.get(i).LargessCardDeductionAmount.toString()));
                            printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗赠送金额", "￥ " + generalCardInfoOutputForm.Project.get(i).LargessAmount.toString()));
         /*                List<TreatBillHandlerOutputForm> handlers=generalCardInfoOutputForm.Project.get(i).TreatBillHandler;
                        if(receiptConfig.Handler) {
                            if(handlers!=null && handlers.size()>0){
                                for (int j = 0; j < handlers.size(); j++) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).TreatHandlerName, ""));
                                    List<TreatBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                                    for (int k = 0; k < employee.size(); k++) {
                                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                                    }
                                }
                            }
                        }*/
                        }
                        packageGeneralCardProjectLength = packageGeneralCardProjectLength + generalCardInfoOutputForm.Project.size();
                    }
                }
                if (packageCardInfoOutputForm.Product != null && packageCardInfoOutputForm.Product.size() > 0) {
                    for (int i = 0; i < packageCardInfoOutputForm.Product.size(); i++) {
                        String flag = packageCardInfoOutputForm.Product.get(i).IsLargess ? "【赠】" : "";
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, length + packageCardInfoOutputForm.Project.size() + packageSavingCardProjectLength + packageTimeCardProjectLength + packageGeneralCardProjectLength + i + 1 + " " + packageCardInfoOutputForm.Product.get(i).ProductName + flag, ""));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗数量", packageCardInfoOutputForm.Product.get(i).Quantity.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗金额", "￥ " + packageCardInfoOutputForm.Product.get(i).TotalAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗现金金额", "￥ " + packageCardInfoOutputForm.Product.get(i).PayAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗卡抵扣金额", "￥ " + packageCardInfoOutputForm.Product.get(i).CardDeductionAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗赠卡抵扣金额", "￥ " + packageCardInfoOutputForm.Product.get(i).LargessCardDeductionAmount.toString()));
                        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "退消耗赠送金额", "￥ " + packageCardInfoOutputForm.Product.get(i).LargessAmount.toString()));
    /*                List<TreatBillHandlerOutputForm> handlers=packageCardInfoOutputForm.Product.get(i).TreatBillHandler;
                    if(receiptConfig.Handler) {
                        if(handlers!=null && handlers.size()>0){
                            for (int j = 0; j < handlers.size(); j++) {
                                printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, handlers.get(j).TreatHandlerName, ""));
                                List<TreatBillHandlerEmployeeOutputForm> employee=handlers.get(j).Employee;
                                for (int k = 0; k < employee.size(); k++) {
                                    printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, employee.get(k).EmployeeName, employee.get(k).Scale.toString()+"%"));
                                }
                            }
                        }
                    }*/
                    }
                }
            }
        }

        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "合计", "￥ " + bill.Amount.toString()));
        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "现金金额", "￥ " + bill.PayAmount.toString()));
        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "卡抵扣金额", "￥ " + bill.CardDeductionAmount.toString()));
        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "赠卡抵扣金额", "￥ " + bill.LargessCardDeductionAmount.toString()));
        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "赠送金额", "￥ " + bill.LargessAmount.toString()));

        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, receiptConfig.WriteTextFirst));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, receiptConfig.WriteTextSecond));

        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getCuttingLineDocument(receiptConfig.PrintWidth, documentID++, ""));

        printDocuments.add(PrintUtils.getBillInfoDocument(receiptConfig.PrintWidth, documentID++, "客户签字", ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, ""));
        printDocuments.add(PrintUtils.getWriteDocument(receiptConfig.PrintWidth, documentID++, ""));
        print.printDocuments = printDocuments;
        return BaseOutputForm.success(print);
    }

}
