package com.zhelian.service.ibeauty.saleProductCommission;


import com.zhelian.mapper.ibeauty.SaleProductCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SaleProductCommissionEntity;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductsCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.SaleProductCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleProductCommissionService {

    @Autowired
    SaleProductCommissionMapper saleProductCommissionMapper;

    public BaseOutput allSaleProductCommission(SaleProductCategoryHandlerCommissionQueryForm form) {
        List<SaleProductCommissionOutputForm> list = saleProductCommissionMapper.allSaleProductCommission(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleProductCommission(SaleProductsCommissionAddForm form) {

        saleProductCommissionMapper.deleteSaleProductCommissionByEntityIDAndCategory(form.EntityID, form.CategoryID);
        List<SaleProductCommissionEntity> list = new ArrayList<>();
        if (form.Product != null && form.Product.size() > 0) {
            for (SaleProductCommissionAddForm saleProductCommissionAddForm : form.Product) {
                if (saleProductCommissionAddForm.PayRate != null || saleProductCommissionAddForm.PayFixed != null ||
                        saleProductCommissionAddForm.SavingCardRate != null || saleProductCommissionAddForm.SavingCardFixed != null ||
                        saleProductCommissionAddForm.SavingCardLargessRate != null || saleProductCommissionAddForm.SavingCardLargessFixed != null ||
                        saleProductCommissionAddForm.SpecialBenefit != null) {
                    SaleProductCommissionEntity saleProductCommissionEntity = new SaleProductCommissionEntity();
                    saleProductCommissionEntity.EntityID = form.EntityID;
                    saleProductCommissionEntity.ProductID = saleProductCommissionAddForm.ProductID;
                    saleProductCommissionEntity.PayFixed = saleProductCommissionAddForm.PayFixed;
                    saleProductCommissionEntity.PayRate = saleProductCommissionAddForm.PayRate;
                    saleProductCommissionEntity.SavingCardFixed = saleProductCommissionAddForm.SavingCardFixed;
                    saleProductCommissionEntity.SavingCardRate = saleProductCommissionAddForm.SavingCardRate;
                    saleProductCommissionEntity.SavingCardLargessFixed = saleProductCommissionAddForm.SavingCardLargessFixed;
                    saleProductCommissionEntity.SavingCardLargessRate = saleProductCommissionAddForm.SavingCardLargessRate;
                    saleProductCommissionEntity.SpecialBenefit = saleProductCommissionAddForm.SpecialBenefit;
                    list.add(saleProductCommissionEntity);
                }
            }
            saleProductCommissionMapper.insertSaleProductCommission(list);
        }
        return BaseOutput.success();
    }

}