package com.zhelian.service.ibeauty.treatProductCommission;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.ibeauty.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.TreatProductCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.input.TreatProductCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCommissionSchemeDeleteForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCommissionSchemeQueryForm;
import com.zhelian.model.ibeauty.form.output.TreatProductCommissionSchemeOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class TreatProductCommissionSchemeService {

    @Autowired
    TreatProductCommissionSchemeMapper treatProductCommissionSchemeMapper;
    @Autowired
    TreatProductCategoryCommissionMapper treatProductCategoryCommissionMapper;
    @Autowired
    TreatProductCategoryHandlerCommissionMapper treatProductCategoryHandlerCommissionMapper;
    @Autowired
    TreatProductCommissionMapper treatProductCommissionMapper;
    @Autowired
    TreatProductHandlerCommissionMapper treatProductHandlerCommissionMapper;
    @Autowired
    TreatProductSchemeHandlerCommissionMapper treatProductSchemeHandlerCommissionMapper;

    public BaseOutput treatProductCommissionSchemeList(TreatProductCommissionSchemeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<TreatProductCommissionSchemeOutputForm> list = treatProductCommissionSchemeMapper.treatProductCommissionSchemeList(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput createTreatProductCommissionScheme(TreatProductCommissionSchemeAddForm form) {
        Integer count = treatProductCommissionSchemeMapper.getTreatProductCommissionScheme(form.EntityID);
        if (count > 0)
            return BaseOutput.failed("组织单位已经存在");
        TreatProductCommissionSchemeEntity treatProductCommissionSchemeEntity = new TreatProductCommissionSchemeEntity();
        treatProductCommissionSchemeEntity.EntityID = form.EntityID;
        treatProductCommissionSchemeMapper.createTreatProductCommissionScheme(treatProductCommissionSchemeEntity);
        return BaseOutput.success();

    }

    public BaseOutput deleteTreatProductCommissionScheme(TreatProductCommissionSchemeDeleteForm form) {
        treatProductCommissionSchemeMapper.deleteTreatProductCommissionScheme(form.EntityID);
        treatProductCategoryCommissionMapper.deleteTreatProductCategoryCommissionEntity(form.EntityID);
        treatProductCategoryHandlerCommissionMapper.deleteTreatProductCategoryHandlerCommissionByEntity(form.EntityID);
        treatProductCategoryHandlerCommissionMapper.deleteTreatProductCategoryHandlerJobTypeCommissionByEntity(form.EntityID);
        treatProductCommissionMapper.deleteTreatProductCommissionByEntity(form.EntityID);
        treatProductHandlerCommissionMapper.deleteTreatProductHandlerCommissionByEntity(form.EntityID);
        treatProductHandlerCommissionMapper.deleteTreatProductHandlerJobTypeCommissionByEntity(form.EntityID);
        treatProductSchemeHandlerCommissionMapper.deleteTreatProductSchemeHandler(form.EntityID);
        treatProductSchemeHandlerCommissionMapper.deleteTreatProductSchemeHandlerJobType(form.EntityID);
        return BaseOutput.success();
    }
}