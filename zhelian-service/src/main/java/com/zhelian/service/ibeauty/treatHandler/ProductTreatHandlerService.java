package com.zhelian.service.ibeauty.treatHandler;


import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.ProductTreatHandlerMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.ProductTreatHandlerEntity;
import com.zhelian.model.ibeauty.entity.ProductTreatHandlerJobTypeEntity;
import com.zhelian.model.ibeauty.form.input.SaleHandlerMoveForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerUpdateForm;
import com.zhelian.model.ibeauty.form.input.TreatHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.TreatHandlerQueryForm;
import com.zhelian.model.ibeauty.form.output.TreatHandlerOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;


@Service
public class ProductTreatHandlerService {

    @Autowired
    ProductTreatHandlerMapper productTreatHandlerMapper;


    public BaseOutput allProductTreatHandler(TreatHandlerQueryForm form) {

        List<TreatHandlerOutputForm> list = productTreatHandlerMapper.allProductTreatHandler(form.Name, form.EntityID, form.Active);
        return BaseOutputForm.success(list);

    }

    @Transactional
    public BaseOutput createProductTreatHandler(TreatHandlerAddForm form) {
        Integer Sequence = productTreatHandlerMapper.getMaxSequence();
        ProductTreatHandlerEntity productTreatHandlerEntity = new ProductTreatHandlerEntity();
        productTreatHandlerEntity.Name = form.Name;
        productTreatHandlerEntity.EntityID = form.EntityID;
        productTreatHandlerEntity.Sequence = Sequence != null ? Sequence + 1 : 1;

        productTreatHandlerMapper.createProductTreatHandler(productTreatHandlerEntity);

        insertHandlerJobType(form.JobType, productTreatHandlerEntity.ID);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateProductTreatHandler(SaleHandlerUpdateForm form) {

        ProductTreatHandlerEntity productTreatHandlerEntity = new ProductTreatHandlerEntity();
        productTreatHandlerEntity.Name = form.Name;
        productTreatHandlerEntity.ID = form.ID;
        productTreatHandlerEntity.EntityID = form.EntityID;
        productTreatHandlerEntity.Active = form.Active;

        productTreatHandlerMapper.updateProductTreatHandler(productTreatHandlerEntity);

        productTreatHandlerMapper.deleteTreatHandlerJobType(form.ID);

        insertHandlerJobType(form.JobType, form.ID);

        return BaseOutput.success();
    }


    public void insertHandlerJobType(Integer[] JobType, Integer ID) {
        List<ProductTreatHandlerJobTypeEntity> list = new ArrayList<>();
        for (Integer typeID : JobType) {
            ProductTreatHandlerJobTypeEntity productTreatHandlerJobTypeEntity = new ProductTreatHandlerJobTypeEntity();
            productTreatHandlerJobTypeEntity.ProductTreatHandlerID = ID;
            productTreatHandlerJobTypeEntity.JobTypeID = typeID;
            list.add(productTreatHandlerJobTypeEntity);
        }
        productTreatHandlerMapper.insertHandlerJobType(list);
    }

    public BaseOutput moveProductTreatHandler(SaleHandlerMoveForm form) {
        //重新排序
        LinkedList<Integer> linkedList = productTreatHandlerMapper.getIDOrderBySequenceASC(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<ProductTreatHandlerEntity> list = new ArrayList<>();

        for (Integer ID : linkedList) {
            ProductTreatHandlerEntity productTreatHandlerEntity = new ProductTreatHandlerEntity();
            productTreatHandlerEntity.ID = ID;
            productTreatHandlerEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(productTreatHandlerEntity);
        }
        productTreatHandlerMapper.updateSequence(list);

        return BaseOutput.success();
    }

}