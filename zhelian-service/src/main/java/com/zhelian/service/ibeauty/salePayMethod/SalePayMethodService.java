package com.zhelian.service.ibeauty.salePayMethod;


import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.ibeauty.SalePayMethodMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.output.SalePayMethodOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class SalePayMethodService {

    @Autowired
    SalePayMethodMapper salePayMethodMapper;

    public BaseOutput allSalePayMethod() {

        List<SalePayMethodOutputForm> list = salePayMethodMapper.allSalePayMethod(SystemUserUtil.getSystemEntityID());
        return BaseOutputForm.success(list);
    }

}