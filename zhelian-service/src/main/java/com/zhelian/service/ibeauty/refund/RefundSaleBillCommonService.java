package com.zhelian.service.ibeauty.refund;

import com.zhelian.core.utils.enumeration.WeiXinOfficialAccountMessage;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.RefundBillApprovalAddForm;
import com.zhelian.service.weixin.WeiXinMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
public class RefundSaleBillCommonService {

    @Autowired
    RefundSaleBillService refundSaleBillService;

    @Autowired
    WeiXinMessageService weiXinMessageService;

    public BaseOutput approval(RefundBillApprovalAddForm form, String channel) {
        String id = refundSaleBillService.approvalSaleBill(form, channel);
        if (StringUtils.hasText(id)) {
            weiXinMessageService.setRefundSaleBillWxMessage(id, WeiXinOfficialAccountMessage.RefundNotice);
        }
        return BaseOutput.success();
    }
}
