package com.zhelian.service.ibeauty.customer;


import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.utils.OSSClientUtil;
import com.zhelian.core.utils.UploadVideoUtil;
import com.zhelian.mapper.ibeauty.AppointmentBillMapper;
import com.zhelian.mapper.ibeauty.CustomerBillMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.ibeauty.form.output.*;
import com.zhelian.model.miniprogram.form.input.CustomerNursingLogDeleteForm;
import com.zhelian.model.miniprogram.form.input.CustomerNursingLogQueryForm;
import com.zhelian.model.miniprogram.form.input.MiniCustomerAppointmentBillQueryForm;
import com.zhelian.model.miniprogram.form.output.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.List;

@Service
public class CustomerBillService {

    @Autowired
    private CustomerBillMapper customerBillMapper;
    @Autowired
    private AppointmentBillMapper appointmentBillMapper;

    public BaseOutput saleBill(CustomerSaleBillQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleBillListOutputForm> list = customerBillMapper.saleBill(form.CustomerID, form.SaleBillID, form.StartDate, form.EndDate, form.BillStatus, form.BillType);
        return BasePageInfo.success(list);
    }

    public BaseOutput saleDetailBill(CustomerSaleDetailBillQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleDetailBillListOutputForm> list = customerBillMapper.saleDetailBill(form.CustomerID, form.BillStatus, form.BillType,form.BillID);
        return BasePageInfo.success(list);
    }

    public BaseOutput salePayDetail(CustomerSalePayDetailQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SalePayDetailListOutputForm> list = customerBillMapper.salePayDetail(form.CustomerID,form.BillType,form.BillID);
        return BasePageInfo.success(list);
    }

    public BaseOutput treatBill(CustomerTreatBillQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<TreatBillOutputForm> list = customerBillMapper.treatBill(form.CustomerID, form.BillID, form.StartDate, form.EndDate, form.BillStatus, form.BillType);
        return BasePageInfo.success(list);
    }

    public BaseOutput treatDetailBill(CustomerTreatDetailBillQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<TreatDetailBillOutputForm> list = customerBillMapper.treatDetailBill(form.CustomerID, form.BillStatus, form.BillType,form.BillID);
        return BasePageInfo.success(list);
    }

    public BaseOutput refundBill(CustomerRefundBillQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CustomerRefundBillOutputForm> list = customerBillMapper.refundBill(form.CustomerID, form.ID, form.StartDate, form.EndDate, form.ApprovalStatus);
        return BasePageInfo.success(list);
    }

    public BaseOutput refundTreatBill(CustomerRefundBillQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<RefundTreatBillListOutputForm> list = customerBillMapper.refundTreatBill(form.CustomerID, form.ID, form.StartDate, form.EndDate, form.ApprovalStatus);
        return BasePageInfo.success(list);
    }

    public BaseOutput transer(CustomerTranserBillQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<TranserBillOutputForm> list = customerBillMapper.transer(form.CustomerID, form.TranserBillID, form.StartDate, form.EndDate);
        return BasePageInfo.success(list);
    }

    public BaseOutput postponeBill(CustomerPostponeBillQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<PostponeBillOutputForm> list = customerBillMapper.postponeBill(form.CustomerID, form.PostponeBillID, form.StartDate, form.EndDate, form.ApprovalStatus);
        return BasePageInfo.success(list);
    }

    public BaseOutput appointmentBill(CustomerAppointmentBillQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<AppointmentBillOutputForm> list = customerBillMapper.appointmentBill(form.CustomerID, form.StartDate, form.EndDate, form.Status, form.AppointmentTypeID, form.ServicerID, form.ServicerEmployeeID, form.EmployeeName);
        return BasePageInfo.success(list);
    }

    public BaseOutput nursingLog(CustomerNursingLogQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<NursingLogOutputForm> list = customerBillMapper.nursingLogList(form.CustomerID, form.Name, form.StartDate, form.EndDate);
        for (NursingLogOutputForm nursingLogOutputForm : list) {
            List<NursingLogAudioOutputForm> newAudio = new ArrayList<>();
            for (NursingLogAudioOutputForm nursingLogAudioOutputForm : nursingLogOutputForm.Audio) {
                if (nursingLogAudioOutputForm.AudioURL != null && !nursingLogAudioOutputForm.AudioURL.equals("")) {
                    JSONObject jsonObject = UploadVideoUtil.getPlayInfoAdress(nursingLogAudioOutputForm.AudioURL);
                    if (jsonObject != null && jsonObject.size() > 0) {
                        String duration = jsonObject.getString("duration");
                        if (StringUtils.hasText(duration)) {
                            nursingLogAudioOutputForm.Duration = duration.substring(0, duration.indexOf("."));
                            nursingLogAudioOutputForm.AudioURL = UploadVideoUtil.getPlayInfoAdress(nursingLogAudioOutputForm.AudioURL).getString("url");
                            newAudio.add(nursingLogAudioOutputForm);
                        }
                    }
                }
            }
            nursingLogOutputForm.Audio = newAudio;
            for (NursingLogMediaOutputForm nursingLogMediaOutputForm : nursingLogOutputForm.Media) {
                if (nursingLogMediaOutputForm.MediaType == 20) {
                    nursingLogMediaOutputForm.ThumbnailUrl = UploadVideoUtil.getPlayInfoAdress(nursingLogMediaOutputForm.MediaURL).getString("coverUrl");
                    nursingLogMediaOutputForm.MediaURL = UploadVideoUtil.getPlayInfoAdress(nursingLogMediaOutputForm.MediaURL).getString("url");

                }
            }
            for (NursingLogCommentOutputForm nursingLogCommentOutputForm : nursingLogOutputForm.Comment) {
                if (nursingLogCommentOutputForm.CommentType == 20) {
                    String duration = UploadVideoUtil.getPlayInfoAdress(nursingLogCommentOutputForm.AudioURL).getString("duration");
                    if (StringUtils.hasText(duration)) {
                        nursingLogCommentOutputForm.Duration = duration.substring(0, duration.indexOf("."));
                        nursingLogCommentOutputForm.AudioURL = UploadVideoUtil.getPlayInfoAdress(nursingLogCommentOutputForm.AudioURL).getString("url");

                    }
                }

            }
        }
        return BasePageInfo.success(list);
    }

    public BaseOutput customerAppointmentBill(MiniCustomerAppointmentBillQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<MiniAppointmentBillOutputForm> appointmentBillInfoList = appointmentBillMapper.customerAppointmentBill(form.CustomerID, form.Status, form.StartDate, form.EndDate, form.AppointmentTypeID, form.ServicerID, form.ServicerEmployeeID, form.EmployeeName);
        for (MiniAppointmentBillOutputForm miniAppointmentBillOutputForm : appointmentBillInfoList) {
            List<MiniAppointmentBillAppointmentAudioOutputForm> newAudio = new ArrayList<>();
            if (miniAppointmentBillOutputForm.Audio.size() > 0) {
                for (MiniAppointmentBillAppointmentAudioOutputForm miniAppointmentBillAppointmentAudioOutputForm : miniAppointmentBillOutputForm.Audio) {
                    if (miniAppointmentBillAppointmentAudioOutputForm.AudioID != null && !miniAppointmentBillAppointmentAudioOutputForm.AudioID.equals("")) {
                        JSONObject jsonObject = UploadVideoUtil.getPlayInfoAdress(miniAppointmentBillAppointmentAudioOutputForm.AudioID);
                        if (jsonObject != null && jsonObject.size() > 0) {
                            String duration = jsonObject.getString("duration");
                            if (StringUtils.hasText(duration)) {
                                miniAppointmentBillAppointmentAudioOutputForm.Duration = duration.substring(0, duration.indexOf("."));
                                miniAppointmentBillAppointmentAudioOutputForm.AudioURL = UploadVideoUtil.getPlayInfoAdress(miniAppointmentBillAppointmentAudioOutputForm.AudioID).getString("url");
                                newAudio.add(miniAppointmentBillAppointmentAudioOutputForm);
                            }

                        }
                    }
                }
            }
            miniAppointmentBillOutputForm.Audio = newAudio;
        }
        return BasePageInfo.success(appointmentBillInfoList);
    }

    @Transactional
    public BaseOutput deleteNursingLog(CustomerNursingLogDeleteForm form) throws Exception {

        List<NursingLogAudioOutputForm> audio = customerBillMapper.getNursingLogAudio(form.ID);
        for (NursingLogAudioOutputForm nursingLogAudioOutputForm : audio) {
            UploadVideoUtil.deleteVideo(nursingLogAudioOutputForm.AudioURL);
        }

        List<NursingLogMediaOutputForm> media = customerBillMapper.getNursingLogMedia(form.ID);
        for (NursingLogMediaOutputForm nursingLogMediaOutputForm : media) {
            if (nursingLogMediaOutputForm.MediaType == 20) {
                UploadVideoUtil.deleteVideo(nursingLogMediaOutputForm.MediaURL);
            } else {
                String url = nursingLogMediaOutputForm.MediaURL;
                String[] split = url.split("/");
                OSSClientUtil.deleteObject(URLDecoder.decode(split[split.length - 1], "UTF-8"));
            }
        }

        List<NursingLogCommentOutputForm> comment  = customerBillMapper.getNursingLogComment(form.ID);
        for (NursingLogCommentOutputForm nursingLogCommentOutputForm : comment) {
            if (nursingLogCommentOutputForm.CommentType == 20) {
               UploadVideoUtil.deleteVideo(nursingLogCommentOutputForm.AudioURL);
            }
        }


        customerBillMapper.deleteNursingLog(form.ID);
        customerBillMapper.deleteNursingLogAudio(form.ID);
        customerBillMapper.deleteNursingLogMedia(form.ID);
        customerBillMapper.deleteNursingLogComment(form.ID);
        customerBillMapper.deleteNursingLogTag(form.ID);
        customerBillMapper.deleteNursingLogThumbsUp(form.ID);
        return BaseOutput.success();
    }
}