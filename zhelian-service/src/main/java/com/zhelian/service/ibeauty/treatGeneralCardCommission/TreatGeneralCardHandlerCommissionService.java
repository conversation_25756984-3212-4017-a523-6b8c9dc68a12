package com.zhelian.service.ibeauty.treatGeneralCardCommission;

import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.TreatGeneralCardHandlerCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.TreatGeneralCardHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.TreatGeneralCardHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatCardHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardHandlersCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.TreatCardHandlerCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class TreatGeneralCardHandlerCommissionService {

    @Autowired
    TreatGeneralCardHandlerCommissionMapper treatGeneralCardHandlerCommissionMapper;


    public BaseOutput allTreatGeneralCardHandlerCommission(TreatCardCommissionQueryForm form) {
        List<TreatCardHandlerCommissionOutputForm> list = treatGeneralCardHandlerCommissionMapper.allTreatGeneralCardHandlerCommission(form.EntityID, form.CardID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatGeneralCardHandlerCommission(TreatCardHandlersCommissionAddForm form) {

        treatGeneralCardHandlerCommissionMapper.deleteTreatGeneralCardHandler(form.EntityID, form.CardID);
        treatGeneralCardHandlerCommissionMapper.deleteTreatGeneralCardHandlerJobType(form.EntityID, form.CardID);
        List<TreatGeneralCardHandlerCommissionEntity> treatGeneralCardHandlerCommissionList = new ArrayList<>();
        List<TreatGeneralCardHandlerJobTypeCommissionEntity> treatGeneralCardHandlerJobTypeCommissionList = new ArrayList<>();
        if (form.CardHandler != null && form.CardHandler.size() > 0) {
            for (TreatCardHandlerCommissionAddForm treatCardHandlerCommissionAddForm : form.CardHandler) {
                if (treatCardHandlerCommissionAddForm.PayRate != null || treatCardHandlerCommissionAddForm.PayFixed != null ||
                        treatCardHandlerCommissionAddForm.CardRate != null || treatCardHandlerCommissionAddForm.CardFixed != null ||
                        treatCardHandlerCommissionAddForm.CardLargessRate != null || treatCardHandlerCommissionAddForm.CardLargessFixed != null ||
                        treatCardHandlerCommissionAddForm.LargessRate != null || treatCardHandlerCommissionAddForm.LargessFixed != null ||
                        treatCardHandlerCommissionAddForm.SpecialBenefit != null) {
                    TreatGeneralCardHandlerCommissionEntity treatGeneralCardHandlerCommissionEntity = new TreatGeneralCardHandlerCommissionEntity();
                    treatGeneralCardHandlerCommissionEntity.EntityID = form.EntityID;
                    treatGeneralCardHandlerCommissionEntity.GeneralCardID = form.CardID;
                    treatGeneralCardHandlerCommissionEntity.GeneralCardProjectTreatHandlerID = treatCardHandlerCommissionAddForm.TreatHandlerID;
                    treatGeneralCardHandlerCommissionEntity.PayRate = treatCardHandlerCommissionAddForm.PayRate;
                    treatGeneralCardHandlerCommissionEntity.PayFixed = treatCardHandlerCommissionAddForm.PayFixed;
                    treatGeneralCardHandlerCommissionEntity.CardRate = treatCardHandlerCommissionAddForm.CardRate;
                    treatGeneralCardHandlerCommissionEntity.CardFixed = treatCardHandlerCommissionAddForm.CardFixed;
                    treatGeneralCardHandlerCommissionEntity.CardLargessRate = treatCardHandlerCommissionAddForm.CardLargessRate;
                    treatGeneralCardHandlerCommissionEntity.CardLargessFixed = treatCardHandlerCommissionAddForm.CardLargessFixed;
                    treatGeneralCardHandlerCommissionEntity.LargessFixed = treatCardHandlerCommissionAddForm.LargessFixed;
                    treatGeneralCardHandlerCommissionEntity.LargessRate = treatCardHandlerCommissionAddForm.LargessRate;
                    treatGeneralCardHandlerCommissionEntity.SpecialBenefit = treatCardHandlerCommissionAddForm.SpecialBenefit;
                    treatGeneralCardHandlerCommissionList.add(treatGeneralCardHandlerCommissionEntity);
                }
                if (treatCardHandlerCommissionAddForm.HandlerJobTypeCommission != null && treatCardHandlerCommissionAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (TreatCardCategoryHandlerJobTypeCommissionAddForm typeCommissionAddForm : treatCardHandlerCommissionAddForm.HandlerJobTypeCommission) {
                        if (typeCommissionAddForm.PayRate != null || typeCommissionAddForm.PayFixed != null ||
                                typeCommissionAddForm.CardRate != null || typeCommissionAddForm.CardFixed != null ||
                                typeCommissionAddForm.CardLargessRate != null || typeCommissionAddForm.CardLargessFixed != null ||
                                typeCommissionAddForm.LargessRate != null || typeCommissionAddForm.LargessFixed != null ||
                                typeCommissionAddForm.SpecialBenefit != null) {
                            TreatGeneralCardHandlerJobTypeCommissionEntity commissionEntity = new TreatGeneralCardHandlerJobTypeCommissionEntity();
                            commissionEntity.EntityID = form.EntityID;
                            commissionEntity.GeneralCardID = form.CardID;
                            commissionEntity.GeneralCardProjectTreatHandlerID = treatCardHandlerCommissionAddForm.TreatHandlerID;
                            commissionEntity.JobTypeID = typeCommissionAddForm.JobTypeID;
                            commissionEntity.PayRate = typeCommissionAddForm.PayRate;
                            commissionEntity.PayFixed = typeCommissionAddForm.PayFixed;
                            commissionEntity.CardRate = typeCommissionAddForm.CardRate;
                            commissionEntity.CardFixed = typeCommissionAddForm.CardFixed;
                            commissionEntity.CardLargessRate = typeCommissionAddForm.CardLargessRate;
                            commissionEntity.CardLargessFixed = typeCommissionAddForm.CardLargessFixed;
                            commissionEntity.LargessFixed = typeCommissionAddForm.LargessFixed;
                            commissionEntity.LargessRate = typeCommissionAddForm.LargessRate;
                            commissionEntity.SpecialBenefit = typeCommissionAddForm.SpecialBenefit;
                            treatGeneralCardHandlerJobTypeCommissionList.add(commissionEntity);
                        }
                    }
                }
            }
        }
        if (treatGeneralCardHandlerCommissionList != null && treatGeneralCardHandlerCommissionList.size() > 0) {
            List<List<TreatGeneralCardHandlerCommissionEntity>> subs = Lists.partition(treatGeneralCardHandlerCommissionList, 20);
            for (List<TreatGeneralCardHandlerCommissionEntity> entityList : subs) {
                treatGeneralCardHandlerCommissionMapper.insertTreatGeneralCardHandlerCommission(entityList);
            }
        }
        if (treatGeneralCardHandlerJobTypeCommissionList != null && treatGeneralCardHandlerJobTypeCommissionList.size() > 0) {
            List<List<TreatGeneralCardHandlerJobTypeCommissionEntity>> subs = Lists.partition(treatGeneralCardHandlerJobTypeCommissionList, 20);
            for (List<TreatGeneralCardHandlerJobTypeCommissionEntity> entityList : subs) {
                treatGeneralCardHandlerCommissionMapper.insertTreatGeneralCardHandlerJobTypeCommission(entityList);
            }
        }
        return BaseOutput.success();
    }
}