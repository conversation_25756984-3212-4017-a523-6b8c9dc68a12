package com.zhelian.service.ibeauty.treatProjectCommission;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.TreatProjectCategoryCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.TreatProjectCategoryCommissionEntity;
import com.zhelian.model.ibeauty.entity.TreatProjectCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.input.TreatProjectCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCategoryCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectChildCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.TreatProjectCategoryOutputForm;
import com.zhelian.model.ibeauty.form.output.TreatProjectChildCategoryCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import static com.zhelian.core.utils.ListUtil.listToTree;


@Service
public class TreatProjectCategoryCommissionService {

    @Autowired
    TreatProjectCategoryCommissionMapper treatProjectCategoryCommissionMapper;


    public BaseOutput allTreatProjectCategoryCommission(TreatProjectCategoryCommissionQueryForm form) {
        TreatProjectCategoryOutputForm treatProjectCategoryOutputForm = treatProjectCategoryCommissionMapper.treatProjectCategoryCommission(form.EntityID);
        List<TreatProjectChildCategoryCommissionOutputForm> all = treatProjectCategoryCommissionMapper.allTreatProjectCategoryCommission(form.EntityID);
        JSONArray result = listToTree(JSONArray.parseArray(JSON.toJSONString(all, JSONWriter.Feature.WriteMapNullValue)), "CategoryID", "ParentID", "Child");
        treatProjectCategoryOutputForm.Category = result;
        return BaseOutputForm.success(treatProjectCategoryOutputForm);
    }

    @Transactional
    public BaseOutput updateTreatProjectCategoryCommission(TreatProjectCategoryAddForm form) {
        TreatProjectCommissionSchemeEntity treatProjectCommissionSchemeEntity = new TreatProjectCommissionSchemeEntity();
        treatProjectCommissionSchemeEntity.EntityID = form.EntityID;
        treatProjectCommissionSchemeEntity.PayRate = form.PayRate;
        treatProjectCommissionSchemeEntity.PayFixed = form.PayFixed;
        treatProjectCommissionSchemeEntity.CardRate = form.CardRate;
        treatProjectCommissionSchemeEntity.CardFixed = form.CardFixed;
        treatProjectCommissionSchemeEntity.CardLargessRate = form.CardLargessRate;
        treatProjectCommissionSchemeEntity.CardLargessFixed = form.CardLargessFixed;
        treatProjectCommissionSchemeEntity.LargessRate = form.LargessRate;
        treatProjectCommissionSchemeEntity.LargessFixed = form.LargessFixed;
        treatProjectCommissionSchemeEntity.SpecialBenefit = form.SpecialBenefit;

        treatProjectCategoryCommissionMapper.updateTreatProjectCommissionScheme(treatProjectCommissionSchemeEntity);

        treatProjectCategoryCommissionMapper.deleteTreatProjectCategoryCommissionEntity(form.EntityID);


        List<TreatProjectCategoryCommissionEntity> list = new ArrayList<>();
        if (form.Category != null && form.Category.size() > 0) {

            for (TreatProjectCategoryCommissionAddForm treatProjectCategoryCommissionAddForm : form.Category) {
                if (treatProjectCategoryCommissionAddForm.PayRate != null || treatProjectCategoryCommissionAddForm.PayFixed != null ||
                        treatProjectCategoryCommissionAddForm.CardRate != null || treatProjectCategoryCommissionAddForm.CardFixed != null ||
                        treatProjectCategoryCommissionAddForm.CardLargessRate != null || treatProjectCategoryCommissionAddForm.CardLargessFixed != null ||
                        treatProjectCategoryCommissionAddForm.LargessRate != null || treatProjectCategoryCommissionAddForm.LargessFixed != null ||
                        treatProjectCategoryCommissionAddForm.SpecialBenefit != null) {
                    TreatProjectCategoryCommissionEntity treatProjectCategoryCommissionEntity = new TreatProjectCategoryCommissionEntity();
                    treatProjectCategoryCommissionEntity.EntityID = form.EntityID;
                    treatProjectCategoryCommissionEntity.ProjectCategoryID = treatProjectCategoryCommissionAddForm.CategoryID;
                    treatProjectCategoryCommissionEntity.PayRate = treatProjectCategoryCommissionAddForm.PayRate;
                    treatProjectCategoryCommissionEntity.PayFixed = treatProjectCategoryCommissionAddForm.PayFixed;
                    treatProjectCategoryCommissionEntity.CardRate = treatProjectCategoryCommissionAddForm.CardRate;
                    treatProjectCategoryCommissionEntity.CardFixed = treatProjectCategoryCommissionAddForm.CardFixed;
                    treatProjectCategoryCommissionEntity.CardLargessRate = treatProjectCategoryCommissionAddForm.CardLargessRate;
                    treatProjectCategoryCommissionEntity.CardLargessFixed = treatProjectCategoryCommissionAddForm.CardLargessFixed;
                    treatProjectCategoryCommissionEntity.LargessRate = treatProjectCategoryCommissionAddForm.LargessRate;
                    treatProjectCategoryCommissionEntity.LargessFixed = treatProjectCategoryCommissionAddForm.LargessFixed;
                    treatProjectCategoryCommissionEntity.SpecialBenefit = treatProjectCategoryCommissionAddForm.SpecialBenefit;
                    list.add(treatProjectCategoryCommissionEntity);
                }

                for (TreatProjectChildCategoryCommissionAddForm treatProjectChildCategoryCommissionAddForm : treatProjectCategoryCommissionAddForm.Child) {
                    if (treatProjectChildCategoryCommissionAddForm.PayRate != null || treatProjectChildCategoryCommissionAddForm.PayFixed != null ||
                            treatProjectChildCategoryCommissionAddForm.CardRate != null || treatProjectChildCategoryCommissionAddForm.CardFixed != null ||
                            treatProjectChildCategoryCommissionAddForm.CardLargessRate != null || treatProjectChildCategoryCommissionAddForm.CardLargessFixed != null ||
                            treatProjectChildCategoryCommissionAddForm.LargessRate != null || treatProjectChildCategoryCommissionAddForm.LargessFixed != null ||
                            treatProjectChildCategoryCommissionAddForm.SpecialBenefit != null) {
                        TreatProjectCategoryCommissionEntity treatProjectCategoryCommissionEntity2 = new TreatProjectCategoryCommissionEntity();
                        treatProjectCategoryCommissionEntity2.EntityID = form.EntityID;
                        treatProjectCategoryCommissionEntity2.ProjectCategoryID = treatProjectChildCategoryCommissionAddForm.CategoryID;
                        treatProjectCategoryCommissionEntity2.PayRate = treatProjectChildCategoryCommissionAddForm.PayRate;
                        treatProjectCategoryCommissionEntity2.PayFixed = treatProjectChildCategoryCommissionAddForm.PayFixed;
                        treatProjectCategoryCommissionEntity2.CardRate = treatProjectChildCategoryCommissionAddForm.CardRate;
                        treatProjectCategoryCommissionEntity2.CardFixed = treatProjectChildCategoryCommissionAddForm.CardFixed;
                        treatProjectCategoryCommissionEntity2.CardLargessRate = treatProjectChildCategoryCommissionAddForm.CardLargessRate;
                        treatProjectCategoryCommissionEntity2.CardLargessFixed = treatProjectChildCategoryCommissionAddForm.CardLargessFixed;
                        treatProjectCategoryCommissionEntity2.LargessRate = treatProjectChildCategoryCommissionAddForm.LargessRate;
                        treatProjectCategoryCommissionEntity2.LargessFixed = treatProjectChildCategoryCommissionAddForm.LargessFixed;
                        treatProjectCategoryCommissionEntity2.SpecialBenefit = treatProjectChildCategoryCommissionAddForm.SpecialBenefit;
                        list.add(treatProjectCategoryCommissionEntity2);
                    }
                }
            }
        }
        if (list != null && list.size() > 0) {
            List<List<TreatProjectCategoryCommissionEntity>> subs = Lists.partition(list, 20);
            for (List<TreatProjectCategoryCommissionEntity> entityList : subs) {
                treatProjectCategoryCommissionMapper.insertTreatProjectCategoryCommissionEntity(entityList);
            }
        }
        return BaseOutput.success();
    }

}