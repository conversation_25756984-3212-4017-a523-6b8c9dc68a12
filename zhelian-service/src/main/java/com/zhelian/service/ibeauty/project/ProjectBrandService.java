package com.zhelian.service.ibeauty.project;

import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.ibeauty.ProjectBrandMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.ProjectBrandEntity;
import com.zhelian.model.ibeauty.form.input.ProjectBrandAddForm;
import com.zhelian.model.ibeauty.form.input.ProjectBrandMoveForm;
import com.zhelian.model.ibeauty.form.input.ProjectBrandQueryForm;
import com.zhelian.model.ibeauty.form.input.ProjectBrandUpdateForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

@Service
public class ProjectBrandService {

    @Autowired
    ProjectBrandMapper projectBrandMapper;

    public BaseOutput projectBrandList(ProjectBrandQueryForm form) {
        return BaseOutputForm.success(projectBrandMapper.projectBrandList(form.Name, form.Active));
    }


    public BaseOutput createProjectBrand(ProjectBrandAddForm form) {
        Integer Sequence = projectBrandMapper.getMaxSequence();
        ProjectBrandEntity projectBrandEntity = new ProjectBrandEntity();
        projectBrandEntity.Name = form.Name;
        projectBrandEntity.Sequence = Sequence != null ? Sequence + 1 : 1;
        projectBrandMapper.createProjectBrand(projectBrandEntity);
        return BaseOutput.success();

    }

    public BaseOutput updateProjectBrand(ProjectBrandUpdateForm form) {
        if (form.Active == false) {
            Integer count = projectBrandMapper.projectByBrandCount(form.ID);
            if (count > 0)
                return BaseOutput.failed("该品牌下有项目，不可以改为无效");
        }
        ProjectBrandEntity projectBrandEntity = new ProjectBrandEntity();
        projectBrandEntity.Active = form.Active;
        projectBrandEntity.Name = form.Name;
        projectBrandEntity.ID = form.ID;
        projectBrandMapper.updateProjectBrand(projectBrandEntity);
        return BaseOutput.success();
    }

    public BaseOutput move(ProjectBrandMoveForm form) {

        LinkedList<Integer> linkedList = projectBrandMapper.getASCList(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<ProjectBrandEntity> list = new ArrayList<>();
        for (Integer ID : linkedList) {
            ProjectBrandEntity projectBrandEntity = new ProjectBrandEntity();
            projectBrandEntity.ID = ID;
            projectBrandEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(projectBrandEntity);
        }
        projectBrandMapper.updateSequence(list);
        return BaseOutput.success();
    }

}