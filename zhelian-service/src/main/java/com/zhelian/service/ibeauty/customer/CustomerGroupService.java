package com.zhelian.service.ibeauty.customer;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.ibeauty.CustomerGroupMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.CustomerGroupEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class CustomerGroupService {

    @Autowired
    private CustomerGroupMapper groupMapper;

    //新增会员分群信息
    public BaseOutput addCustomerGroup(CustomerGroupEntity group) {
        groupMapper.addCustomerGroup(group);
        return BaseOutput.success();
    }

    //编辑会员基本信息
    public BaseOutput updateCustomerGroup(CustomerGroupEntity group) {
        groupMapper.updateCustomerGroup(group);
        return BaseOutput.success();
    }

    //删除会员基本信息
    public BaseOutput delCustomerGroup(String groupName) {
        groupMapper.delCustomerGroup(groupName);
        return BaseOutput.success();
    }

    //会员分群信息查询
    public BaseOutput getGroupList(CustomerGroupEntity form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(groupMapper.getGroupList(form.groupName));
    }

}
