package com.zhelian.service.ibeauty.saleProjectCommission;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.ibeauty.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.SaleProjectCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionSchemeDeleteForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionSchemeQueryForm;
import com.zhelian.model.ibeauty.form.output.SaleProductCommissionSchemeOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class SaleProjectCommissionSchemeService {

    @Autowired
    SaleProjectCommissionSchemeMapper saleProjectCommissionSchemeMapper;
    @Autowired
    SaleProjectCategoryCommissionMapper saleProjectCategoryCommissionMapper;
    @Autowired
    SaleProjectCategoryHandlerCommissionMapper saleProjectCategoryHandlerCommissionMapper;
    @Autowired
    SaleProjectCommissionMapper saleProjectCommissionMapper;
    @Autowired
    SaleProjectHandlerCommissionMapper saleProjectHandlerCommissionMapper;
    @Autowired
    SaleProjectSchemeHandlerCommissionMapper saleProjectSchemeHandlerCommissionMapper;

    public BaseOutput saleProjectCommissionSchemeList(SaleProductCommissionSchemeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleProductCommissionSchemeOutputForm> list = saleProjectCommissionSchemeMapper.saleProjectCommissionSchemeList(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput createSaleProjectCommissionScheme(SaleProductCommissionSchemeAddForm form) {
        Integer count = saleProjectCommissionSchemeMapper.getSaleProjectCommissionScheme(form.EntityID);
        if (count > 0)
            return BaseOutput.failed("组织单位已经存在");
        SaleProjectCommissionSchemeEntity saleProjectCommissionSchemeEntity = new SaleProjectCommissionSchemeEntity();
        saleProjectCommissionSchemeEntity.EntityID = form.EntityID;
        saleProjectCommissionSchemeMapper.createSaleProjectCommissionScheme(saleProjectCommissionSchemeEntity);
        return BaseOutput.success();

    }

    public BaseOutput deleteSaleProjectCommissionScheme(SaleProductCommissionSchemeDeleteForm form) {
        saleProjectCommissionSchemeMapper.deleteSaleProjectCommissionScheme(form.EntityID);
        saleProjectCategoryCommissionMapper.deleteSaleProjectCategoryCommissionEntity(form.EntityID);
        saleProjectCategoryHandlerCommissionMapper.deleteSaleProjectCategoryHandlerCommissionByEntity(form.EntityID);
        saleProjectCategoryHandlerCommissionMapper.deleteSaleProjectCategoryHandlerJobTypeCommissionByEntity(form.EntityID);
        saleProjectCommissionMapper.deleteSaleProjectCommissionByEntity(form.EntityID);
        saleProjectHandlerCommissionMapper.deleteSaleProjectHandlerCommissionByEntity(form.EntityID);
        saleProjectHandlerCommissionMapper.deleteSaleProjectHandlerJobTypeCommissionByEntity(form.EntityID);
        saleProjectSchemeHandlerCommissionMapper.deleteSaleProjectSchemeHandler(form.EntityID);
        saleProjectSchemeHandlerCommissionMapper.deleteSaleProjectSchemeHandlerJobType(form.EntityID);
        return BaseOutput.success();
    }


}