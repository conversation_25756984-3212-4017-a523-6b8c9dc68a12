package com.zhelian.service.ibeauty.saleProjectCommission;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.google.common.collect.Lists;
import com.zhelian.mapper.ibeauty.SaleProjectCategoryCommissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.SaleProjectCategoryCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleProjectCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectChildCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.SaleProjectCategoryOutputForm;
import com.zhelian.model.ibeauty.form.output.SaleProjectChildCategoryCommissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import static com.zhelian.core.utils.ListUtil.listToTree;


@Service
public class SaleProjectCategoryCommissionService {

    @Autowired
    SaleProjectCategoryCommissionMapper saleProjectCategoryCommissionMapper;


    public BaseOutput allSaleProjectCategoryCommission(SaleProjectCategoryCommissionQueryForm form) {
        SaleProjectCategoryOutputForm saleProjectCategoryOutputForm = saleProjectCategoryCommissionMapper.saleProjectCategoryCommission(form.EntityID);
        List<SaleProjectChildCategoryCommissionOutputForm> all = saleProjectCategoryCommissionMapper.allSaleProjectCategoryCommission(form.EntityID);
        JSONArray result = listToTree(JSONArray.parseArray(JSON.toJSONString(all, JSONWriter.Feature.WriteMapNullValue)), "CategoryID", "ParentID", "Child");
        saleProjectCategoryOutputForm.Category = result;
        return BaseOutputForm.success(saleProjectCategoryOutputForm);
    }

    @Transactional
    public BaseOutput updateSaleProjectCategoryCommission(SaleProjectCategoryAddForm form) {
        SaleProjectCommissionSchemeEntity saleProjectCommissionSchemeEntity = new SaleProjectCommissionSchemeEntity();
        saleProjectCommissionSchemeEntity.EntityID = form.EntityID;
        saleProjectCommissionSchemeEntity.PayRate = form.PayRate;
        saleProjectCommissionSchemeEntity.PayFixed = form.PayFixed;
        saleProjectCommissionSchemeEntity.SavingCardRate = form.SavingCardRate;
        saleProjectCommissionSchemeEntity.SavingCardFixed = form.SavingCardFixed;
        saleProjectCommissionSchemeEntity.SavingCardLargessRate = form.SavingCardLargessRate;
        saleProjectCommissionSchemeEntity.SavingCardLargessFixed = form.SavingCardLargessFixed;
        saleProjectCommissionSchemeEntity.SpecialBenefit = form.SpecialBenefit;

        saleProjectCategoryCommissionMapper.updateSaleProjectCommissionScheme(saleProjectCommissionSchemeEntity);

        saleProjectCategoryCommissionMapper.deleteSaleProjectCategoryCommissionEntity(form.EntityID);


        List<SaleProjectCategoryCommissionEntity> list = new ArrayList<>();

        if (form.Category != null && form.Category.size() > 0) {
            for (SaleProjectCategoryCommissionAddForm saleProjectCategoryCommissionAddForm : form.Category) {
                if (saleProjectCategoryCommissionAddForm.PayRate != null || saleProjectCategoryCommissionAddForm.PayFixed != null ||
                        saleProjectCategoryCommissionAddForm.SavingCardRate != null || saleProjectCategoryCommissionAddForm.SavingCardFixed != null ||
                        saleProjectCategoryCommissionAddForm.SavingCardLargessRate != null || saleProjectCategoryCommissionAddForm.SavingCardLargessFixed != null ||
                        saleProjectCategoryCommissionAddForm.SpecialBenefit != null) {
                    SaleProjectCategoryCommissionEntity saleProjectCategoryCommissionEntity = new SaleProjectCategoryCommissionEntity();
                    saleProjectCategoryCommissionEntity.EntityID = form.EntityID;
                    saleProjectCategoryCommissionEntity.ProjectCategoryID = saleProjectCategoryCommissionAddForm.CategoryID;
                    saleProjectCategoryCommissionEntity.PayRate = saleProjectCategoryCommissionAddForm.PayRate;
                    saleProjectCategoryCommissionEntity.PayFixed = saleProjectCategoryCommissionAddForm.PayFixed;
                    saleProjectCategoryCommissionEntity.SavingCardRate = saleProjectCategoryCommissionAddForm.SavingCardRate;
                    saleProjectCategoryCommissionEntity.SavingCardFixed = saleProjectCategoryCommissionAddForm.SavingCardFixed;
                    saleProjectCategoryCommissionEntity.SavingCardLargessRate = saleProjectCategoryCommissionAddForm.SavingCardLargessRate;
                    saleProjectCategoryCommissionEntity.SavingCardLargessFixed = saleProjectCategoryCommissionAddForm.SavingCardLargessFixed;
                    saleProjectCategoryCommissionEntity.SpecialBenefit = saleProjectCategoryCommissionAddForm.SpecialBenefit;
                    list.add(saleProjectCategoryCommissionEntity);
                }
                if (saleProjectCategoryCommissionAddForm.Child != null && saleProjectCategoryCommissionAddForm.Child.size() > 0) {

                    for (SaleProjectChildCategoryCommissionAddForm saleProjectChildCategoryCommissionAddForm : saleProjectCategoryCommissionAddForm.Child) {
                        if (saleProjectChildCategoryCommissionAddForm.PayRate != null || saleProjectChildCategoryCommissionAddForm.PayFixed != null ||
                                saleProjectChildCategoryCommissionAddForm.SavingCardRate != null || saleProjectChildCategoryCommissionAddForm.SavingCardFixed != null ||
                                saleProjectChildCategoryCommissionAddForm.SavingCardLargessRate != null || saleProjectChildCategoryCommissionAddForm.SavingCardLargessFixed != null ||
                                saleProjectChildCategoryCommissionAddForm.SpecialBenefit != null) {
                            SaleProjectCategoryCommissionEntity saleProjectCategoryCommissionEntity2 = new SaleProjectCategoryCommissionEntity();
                            saleProjectCategoryCommissionEntity2.EntityID = form.EntityID;
                            saleProjectCategoryCommissionEntity2.ProjectCategoryID = saleProjectChildCategoryCommissionAddForm.CategoryID;
                            saleProjectCategoryCommissionEntity2.PayRate = saleProjectChildCategoryCommissionAddForm.PayRate;
                            saleProjectCategoryCommissionEntity2.PayFixed = saleProjectChildCategoryCommissionAddForm.PayFixed;
                            saleProjectCategoryCommissionEntity2.SavingCardRate = saleProjectChildCategoryCommissionAddForm.SavingCardRate;
                            saleProjectCategoryCommissionEntity2.SavingCardFixed = saleProjectChildCategoryCommissionAddForm.SavingCardFixed;
                            saleProjectCategoryCommissionEntity2.SavingCardLargessRate = saleProjectChildCategoryCommissionAddForm.SavingCardLargessRate;
                            saleProjectCategoryCommissionEntity2.SavingCardLargessFixed = saleProjectChildCategoryCommissionAddForm.SavingCardLargessFixed;
                            saleProjectCategoryCommissionEntity2.SpecialBenefit = saleProjectChildCategoryCommissionAddForm.SpecialBenefit;
                            list.add(saleProjectCategoryCommissionEntity2);
                        }
                    }
                }
            }
        }
        if (list != null && list.size() > 0) {
            List<List<SaleProjectCategoryCommissionEntity>> subs = Lists.partition(list, 20);
            for (List<SaleProjectCategoryCommissionEntity> entityList : subs) {
                saleProjectCategoryCommissionMapper.insertSaleProjectCategoryCommissionEntity(entityList);
            }
        }

        return BaseOutput.success();
    }

}