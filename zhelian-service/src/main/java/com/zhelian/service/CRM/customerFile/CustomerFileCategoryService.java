package com.zhelian.service.CRM.customerFile;

import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.CRM.CustomerFileCategoryMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.CustomerFileCategoryEntity;
import com.zhelian.model.ibeauty.form.input.CustomerFileCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.CustomerFileCategoryMoveForm;
import com.zhelian.model.ibeauty.form.input.CustomerFileCategoryQueryForm;
import com.zhelian.model.ibeauty.form.input.CustomerFileCategoryUpdateForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

@Service
public class CustomerFileCategoryService {

    @Autowired
    CustomerFileCategoryMapper customerFileCategoryMapper;

    public BaseOutput allCustomerFileCategory(CustomerFileCategoryQueryForm form) {
        List<CustomerFileCategoryEntity> list = customerFileCategoryMapper.allCustomerFileCategory(form.Name, form.Active);
        return BaseOutputForm.success(list);
    }


    public BaseOutput createCustomerFileCategory(CustomerFileCategoryAddForm form) {
        Integer Sequence = customerFileCategoryMapper.getMaxSequence();
        CustomerFileCategoryEntity customerFileCategoryEntity = new CustomerFileCategoryEntity();
        customerFileCategoryEntity.Name = form.Name;
        customerFileCategoryEntity.Sequence = Sequence != null ? Sequence + 1 : 1;

        customerFileCategoryMapper.createCustomerFileCategory(customerFileCategoryEntity);

        return BaseOutput.success();
    }

    public BaseOutput updateCustomerFileCategory(CustomerFileCategoryUpdateForm form) {

        CustomerFileCategoryEntity customerFileCategoryEntity = new CustomerFileCategoryEntity();
        customerFileCategoryEntity.ID = form.ID;
        customerFileCategoryEntity.Name = form.Name;
        customerFileCategoryEntity.Active = form.Active;

        customerFileCategoryMapper.updateCustomerFileCategory(customerFileCategoryEntity);
        return BaseOutput.success();
    }

    public BaseOutput moveCustomerFileCategory(CustomerFileCategoryMoveForm form) {

        //重新排序
        LinkedList<Integer> linkedList = customerFileCategoryMapper.getIDOrderBySequenceASC(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<CustomerFileCategoryEntity> list = new ArrayList<>();

        for (Integer ID : linkedList) {
            CustomerFileCategoryEntity customerFileCategoryEntity = new CustomerFileCategoryEntity();
            customerFileCategoryEntity.ID = ID;
            customerFileCategoryEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(customerFileCategoryEntity);
        }
        customerFileCategoryMapper.updateSequence(list);
        return BaseOutput.success();
    }

}