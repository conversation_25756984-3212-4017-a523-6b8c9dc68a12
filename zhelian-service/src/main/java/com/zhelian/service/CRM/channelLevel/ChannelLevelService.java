package com.zhelian.service.CRM.channelLevel;

import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.CRM.ChannelLevelMapper;
import com.zhelian.model.CRM.entity.ChannelLevelEntity;
import com.zhelian.model.CRM.form.input.ChannelTypeAddForm;
import com.zhelian.model.CRM.form.input.ChannelTypeMoveForm;
import com.zhelian.model.CRM.form.input.ChannelTypeQueryForm;
import com.zhelian.model.CRM.form.input.ChannelTypeUpdateForm;
import com.zhelian.model.CRM.form.output.ChannelTypeOutputForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;


@Service
public class ChannelLevelService {

    @Autowired
    ChannelLevelMapper channelLevelMapper;


    public BaseOutput create(ChannelTypeAddForm form) {
        Integer Sequence = channelLevelMapper.getMaxSequence();
        ChannelLevelEntity entity = new ChannelLevelEntity();
        entity.Name = form.Name;
        entity.Sequence = Sequence != null ? Sequence + 1 : 1;
        channelLevelMapper.create(entity);
        return BaseOutput.success();
    }

    public BaseOutput updateChannelLevel(ChannelTypeUpdateForm form) {
        if (form.Active == false) {
            Integer count = channelLevelMapper.channelByChannelLevelCount(form.ID);
            if (count > 0)
                return BaseOutput.failed("该等级下存在渠道正在使用，不可以改为无效");
        }
        ChannelLevelEntity entity = new ChannelLevelEntity();
        entity.ID = form.ID;
        entity.Name = form.Name;
        entity.Active = form.Active;
        channelLevelMapper.updateChannelLevel(entity);
        return BaseOutput.success();
    }

    public BaseOutput list(ChannelTypeQueryForm form) {
        return BaseOutputForm.success(channelLevelMapper.getPageList(form));
    }

    public BaseOutput move(ChannelTypeMoveForm form) {
        //重新排序
        LinkedList<Integer> linkedList = channelLevelMapper.getIDOrderBySequenceASC(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<ChannelLevelEntity> list = new ArrayList<>();

        for (Integer ID : linkedList) {
            ChannelLevelEntity entity = new ChannelLevelEntity();
            entity.ID = ID;
            entity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(entity);
        }
        channelLevelMapper.updateSequence(list);
        return BaseOutput.success();
    }

    public BaseOutput all(ChannelTypeQueryForm form) {
        List<ChannelTypeOutputForm> pageList = channelLevelMapper.all(form);
        return BaseOutputForm.success(pageList);
    }

}
