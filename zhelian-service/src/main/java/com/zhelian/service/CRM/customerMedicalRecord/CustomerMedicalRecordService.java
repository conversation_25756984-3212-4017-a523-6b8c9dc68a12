package com.zhelian.service.CRM.customerMedicalRecord;

import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.autoNumber.AutoNumberUtils;
import com.zhelian.mapper.CRM.CustomerMapper;
import com.zhelian.mapper.CRM.CustomerMedicalRecordMapper;
import com.zhelian.mapper.khs.MedicalRecordMapper;
import com.zhelian.model.CRM.entity.CustomerMedicalRecordDetailEntity;
import com.zhelian.model.CRM.entity.CustomerMedicalRecordEntity;
import com.zhelian.model.CRM.form.input.*;
import com.zhelian.model.CRM.form.output.CustomerMedicalRecordDetailAllOutputForm;
import com.zhelian.model.CRM.form.output.CustomerMedicalRecordEmployeeOutputForm;
import com.zhelian.model.CRM.form.output.CustomerMedicalRecordOutputForm;
import com.zhelian.model.CRM.form.output.CustomerMedicalRecordTemplateAllOutputForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.CustomerEntity;
import com.zhelian.model.khs.entity.DepartmentEntity;
import com.zhelian.model.khs.entity.MedicalRecordCategoryEntity;
import com.zhelian.model.khs.entity.MedicalRecordTemplateEntity;
import com.zhelian.model.khs.form.output.MedicalRecordTemplateOutForm;
import com.zhelian.model.miniprogram.form.output.EmployeeOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class CustomerMedicalRecordService {

    @Autowired
    CustomerMedicalRecordMapper customerMedicalRecordMapper;

    @Autowired
    MedicalRecordMapper medicalRecordMapper;

    @Autowired
    CustomerMapper customerMapper;

    public BaseOutput doctor() {
        List<CustomerMedicalRecordEmployeeOutputForm> list=customerMedicalRecordMapper.getDoctor(SystemUserUtil.getSystemEntityID());
        return  BaseOutputForm.success(list);
    }

    public BaseOutput department() {
        List<DepartmentEntity> list = customerMedicalRecordMapper.getDepartment(SystemUserUtil.getSystemEntityID());
        return BaseOutputForm.success(list);
    }

    public BaseOutput medicalRecordCategory() {
        List<MedicalRecordCategoryEntity> list = customerMedicalRecordMapper.getMedicalRecordCategory();
        return BaseOutputForm.success(list);
    }

    public BaseOutput medicalRecordTemplate(CustomerMedicalRecordTemplateQueryForm form) {
        List<CustomerMedicalRecordTemplateAllOutputForm> list = customerMedicalRecordMapper.getMedicalRecordTemplate(form.CategoryID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput medicalRecordTemplateDetail(CustomerMedicalRecordTemplateDetailQueryForm form) {
        MedicalRecordTemplateEntity entity = customerMedicalRecordMapper.getMedicalRecordTemplateDetail(form.ID);
        return BaseOutputForm.success(entity);
    }

    public BaseOutput list(CustomerMedicalRecordQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CustomerMedicalRecordOutputForm> list = customerMedicalRecordMapper.customerMedicalRecordList(form.CustomerID);
        return BasePageInfo.success(list);
    }

    @Transactional
    public BaseOutput create(CustomerMedicalRecordAddForm form) {

        CustomerEntity customer = customerMapper.getCustomer(form.CustomerID);
        List<String> errors = new ArrayList<>();

        if (!StringUtils.hasText(customer.Gender)) {
            errors.add("顾客性别");
        }

        if (customer.Birthday == null) {
            errors.add("顾客生日");
        }

        int age = 0;
        if (customer.Birthday != null) {
            LocalDate localDate1 = customer.Birthday.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate localDate2 = LocalDate.now();
            Period between = Period.between(localDate1, localDate2);
            age = between.getYears();
        }

        CustomerMedicalRecordEntity customerMedicalRecordEntity = new CustomerMedicalRecordEntity();
        customerMedicalRecordEntity.ID = AutoNumberUtils.getCRMCustomerMedicalRecordID();
        customerMedicalRecordEntity.CustomerID = form.CustomerID;
        customerMedicalRecordEntity.Gender = customer.Gender;
        customerMedicalRecordEntity.Age = age;
        customerMedicalRecordEntity.EntityID = SystemUserUtil.getSystemEntityID();
        customerMedicalRecordEntity.DepartmentID = form.DepartmentID;
        customerMedicalRecordEntity.EmployeeID = form.EmployeeID;
        customerMedicalRecordEntity.TreatmentDate = Date.from(LocalDate.parse(form.TreatmentDate).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        customerMedicalRecordEntity.Remark = form.Remark;
        customerMedicalRecordEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        customerMedicalRecordEntity.CreatedOn = Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant());

        customerMedicalRecordMapper.createCustomerMedicalRecordEntity(customerMedicalRecordEntity);
        CustomerMedicalRecordOutputForm outputForm = customerMedicalRecordMapper.customerMedicalRecordByID(customerMedicalRecordEntity.ID);
        return BaseOutputForm.success(outputForm);
    }

    @Transactional
    public BaseOutput update(CustomerMedicalRecordUpdateForm form) {
        CustomerMedicalRecordEntity customerMedicalRecordEntity=customerMedicalRecordMapper.getCustomerMedicalRecordEntity(form.ID);
        customerMedicalRecordEntity.DepartmentID=form.DepartmentID;
        customerMedicalRecordEntity.EmployeeID=form.EmployeeID;
        customerMedicalRecordEntity.TreatmentDate=Date.from(LocalDate.parse(form.TreatmentDate).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        customerMedicalRecordEntity.Remark=form.Remark;
        customerMedicalRecordMapper.updateCustomerMedicalRecordEntity(customerMedicalRecordEntity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput delete(CustomerMedicalRecordDeleteForm form) {
        customerMedicalRecordMapper.deleteCustomerMedicalRecord(form.ID);
        customerMedicalRecordMapper.deleteCustomerMedicalRecordDetail(form.ID);
        return BaseOutput.success();
    }

    public BaseOutput detail(CustomerMedicalRecordDetailQueryForm form) {
        CustomerMedicalRecordDetailAllOutputForm customerMedicalRecordDetailOutputForm=customerMedicalRecordMapper.getCustomerMedicalRecordDetail(form.ID,form.CategoryID);
        return BaseOutputForm.success(customerMedicalRecordDetailOutputForm);
    }

    @Transactional
    public BaseOutput createMedicalTemplate(CustomerMedicalTemplateAddForm form) {

        List<CustomerMedicalRecordDetailEntity> list=new ArrayList<>();
        for (CustomerMedicalTemplateAddDetailForm customerMedicalTemplateAddDetailForm : form.Template) {
            MedicalRecordTemplateOutForm template = medicalRecordMapper.getTemplate(customerMedicalTemplateAddDetailForm.MedicalRecordTemplateID);
            CustomerMedicalRecordDetailEntity entity=new CustomerMedicalRecordDetailEntity();
            entity.CustomerMedicalRecordID=form.CustomerMedicalRecordID;
            entity.MedicalRecordCategoryID=template.CategoryID;
            entity.MedicalRecordTemplateName=template.Name;
            entity.MedicalRecordContent=template.Content;
            entity.MedicalRecordDate="";
            entity.MedicalRecordPrintJSON=template.PrintJSON;
            entity.CreatedBy= SystemUserUtil.getSystemUserID();
            entity.CreatedOn= Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant());
            list.add(entity);
        }

        customerMedicalRecordMapper.insertCustomerMedicalRecordDetail(list);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateMedicalTemplate(CustomerMedicalTemplateUpdateForm form) {
        CustomerMedicalRecordDetailEntity customerMedicalRecordDetailEntity=customerMedicalRecordMapper.getCustomerMedicalRecordDetailEntity(form.ID);
        customerMedicalRecordDetailEntity.MedicalRecordContent=form.MedicalRecordContent;
        customerMedicalRecordDetailEntity.MedicalRecordDate=form.MedicalRecordDate;
        customerMedicalRecordDetailEntity.MedicalRecordPrintJSON=form.TemplatePrintJSON;
        customerMedicalRecordMapper.updateCustomerMedicalRecordDetailEntity(customerMedicalRecordDetailEntity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput deleteMedicalTemplate(CustomerMedicalTemplateDeleteForm form) {
        customerMedicalRecordMapper.deleteCustomerMedicalRecordDetailEntity(form.ID);
        return BaseOutput.success();
    }
}
