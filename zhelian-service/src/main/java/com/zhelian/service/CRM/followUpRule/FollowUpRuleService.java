package com.zhelian.service.CRM.followUpRule;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.utils.DateTimeUtil;
import com.zhelian.mapper.CRM.CallbackMapper;
import com.zhelian.mapper.CRM.CustomerMapper;
import com.zhelian.mapper.CRM.FollowUpMapper;
import com.zhelian.mapper.CRM.FollowUpRuleMapper;
import com.zhelian.model.CRM.entity.FollowUpRecordEntity;
import com.zhelian.model.CRM.entity.FollowUpRuleEntity;
import com.zhelian.model.CRM.entity.FollowUpRuleEntityEntity;
import com.zhelian.model.CRM.entity.FollowUpRuleFilterConditionEntity;
import com.zhelian.model.CRM.form.input.*;
import com.zhelian.model.CRM.form.output.FollowUpRuleAllConditionOutForm;
import com.zhelian.model.CRM.form.output.FollowUpRuleDetailOutputExtendForm;
import com.zhelian.model.CRM.form.output.FollowUpRuleDetailOutputForm;
import com.zhelian.model.CRM.form.output.FollowUpRuleOutForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;


@Service
public class FollowUpRuleService {
    @Autowired
    FollowUpRuleMapper followUpRuleMapper;

    @Autowired
    CustomerMapper customerMapper;

    @Autowired
    CallbackMapper callbackMapper;
    @Autowired
    FollowUpMapper followUpMapper;

    public BaseOutput all(FollowUpRuleQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<FollowUpRuleOutForm> list = followUpRuleMapper.followUpRuleList(form.Name, form.Active);
        return BasePageInfo.success(list);
    }

    @Transactional
    public BaseOutput create(FollowUpRuleAddForm form) {
        FollowUpRuleEntity followUpRuleEntity = new FollowUpRuleEntity();
        followUpRuleEntity.Name = form.Name;
        followUpRuleEntity.FollowUpMethodID = form.FollowUpMethodID;
        followUpRuleEntity.ServicerID = form.ServicerID;
        followUpRuleEntity.EmployeeID = form.EmployeeID;
        followUpRuleEntity.FollowUpContent = form.FollowUpContent;
        followUpRuleEntity.Active = form.Active;
        followUpRuleEntity.ScheduleType = form.ScheduleType;
        followUpRuleEntity.ScheduleInterval = form.ScheduleInterval;
        followUpRuleEntity.ScheduleBeginDate = form.ScheduleBeginDate;
        followUpRuleEntity.ScheduleEndDate = form.ScheduleEndDate;
        followUpRuleMapper.createFollowUpRule(followUpRuleEntity);

        List<FollowUpRuleEntityEntity> entityList = new ArrayList<>();
        if (form.Entity.length > 0) {
            for (Integer integer : form.Entity) {
                FollowUpRuleEntityEntity followUpRuleEntityEntity = new FollowUpRuleEntityEntity();
                followUpRuleEntityEntity.EntityID = integer;
                followUpRuleEntityEntity.FollowUpRuleID = followUpRuleEntity.ID;
                entityList.add(followUpRuleEntityEntity);
            }
            followUpRuleMapper.createFollowUpRuleEntity(entityList);
        }

        List<FollowUpRuleFilterConditionEntity> conditionEntities = new ArrayList<>();
        if (form.Condition != null && form.Condition.size() > 0) {
            form.Condition.forEach(i -> {
                FollowUpRuleFilterConditionEntity followUpRuleFilterConditionEntity = new FollowUpRuleFilterConditionEntity();
                followUpRuleFilterConditionEntity.FollowUpRuleID = followUpRuleEntity.ID;
                followUpRuleFilterConditionEntity.FollowUpConditionID = i.Code;
                followUpRuleFilterConditionEntity.Value = i.Value;
                conditionEntities.add(followUpRuleFilterConditionEntity);
            });

            followUpRuleMapper.createFollowUpRuleCondition(conditionEntities);
        }

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput update(FollowUpRuleUpdateForm form) {
        FollowUpRuleEntity followUpRuleEntity = followUpRuleMapper.getCreateFollowUpRule(form.ID);
        followUpRuleEntity.Name = form.Name;
        followUpRuleEntity.FollowUpMethodID = form.FollowUpMethodID;
        followUpRuleEntity.ServicerID = form.ServicerID;
        followUpRuleEntity.EmployeeID = form.EmployeeID;
        followUpRuleEntity.FollowUpContent = form.FollowUpContent;
        followUpRuleEntity.Active = form.Active;
        followUpRuleEntity.ScheduleType = form.ScheduleType;
        followUpRuleEntity.ScheduleInterval = form.ScheduleInterval;
        followUpRuleEntity.ScheduleBeginDate = form.ScheduleBeginDate;
        followUpRuleEntity.ScheduleEndDate = form.ScheduleEndDate;
        followUpRuleMapper.updateFollowUpRule(followUpRuleEntity);


        followUpRuleMapper.deleteFollowUpRuleEntity(form.ID);
        followUpRuleMapper.deleteFollowUpRuleCondition(form.ID);

        List<FollowUpRuleEntityEntity> entityList = new ArrayList<>();
        if (form.Entity.length > 0) {
            for (Integer integer : form.Entity) {
                FollowUpRuleEntityEntity followUpRuleEntityEntity = new FollowUpRuleEntityEntity();
                followUpRuleEntityEntity.EntityID = integer;
                followUpRuleEntityEntity.FollowUpRuleID = followUpRuleEntity.ID;
                entityList.add(followUpRuleEntityEntity);
            }
            followUpRuleMapper.createFollowUpRuleEntity(entityList);
        }

        List<FollowUpRuleFilterConditionEntity> conditionEntities = new ArrayList<>();
        if (form.Condition != null && form.Condition.size() > 0) {
            form.Condition.forEach(i -> {
                FollowUpRuleFilterConditionEntity followUpRuleFilterConditionEntity = new FollowUpRuleFilterConditionEntity();
                followUpRuleFilterConditionEntity.FollowUpRuleID = followUpRuleEntity.ID;
                followUpRuleFilterConditionEntity.FollowUpConditionID = i.Code;
                followUpRuleFilterConditionEntity.Value = i.Value;
                conditionEntities.add(followUpRuleFilterConditionEntity);
            });

            followUpRuleMapper.createFollowUpRuleCondition(conditionEntities);
        }

        return BaseOutput.success();
    }


    public BaseOutput condition() {
        List<FollowUpRuleAllConditionOutForm> list = followUpRuleMapper.getCondition();
        return BaseOutputForm.success(list);
    }

    public BaseOutput detail(FollowUpRuleDetailQueryForm form) {
        FollowUpRuleDetailOutputForm followUpRuleDetailOutputForm = followUpRuleMapper.detail(form.ID);
        followUpRuleDetailOutputForm.Entity = followUpRuleMapper.getEntityDetail(form.ID);
        followUpRuleDetailOutputForm.Condition = followUpRuleMapper.getConditionDetail(form.ID);
        return BaseOutputForm.success(followUpRuleDetailOutputForm);
    }


    @Transactional
    public BaseOutput createFollowUp() {
        List<FollowUpRuleDetailOutputExtendForm> followUpRuleEntities = followUpRuleMapper.allFollowUpRule();
        for (FollowUpRuleDetailOutputExtendForm followUpRuleEntity : followUpRuleEntities) {
            FollowUpConditionQueryForm form = new FollowUpConditionQueryForm();
            form.Entity = followUpRuleEntity.Entity;
            if (form.Entity != null && form.Entity.size() > 0) {
                followUpRuleEntity.Condition.forEach(i -> {

                    if (i.Code.equals("Age")) {
                        JSONObject js = JSON.parseObject(i.Value, JSONObject.class);
                        form.AgeStart = js.getInteger("startValue");
                        form.AgeEnd = js.getInteger("endValue");
                        form.AgeStartDate = customerMapper.getLunarTime(DateTimeUtil.toyyyyMMdd2(DateTimeUtil.addYear(new Date(), form.AgeEnd * -1)));
                        form.AgeEndDate = customerMapper.getLunarTime(DateTimeUtil.toyyyyMMdd2(DateTimeUtil.addYear(new Date(), form.AgeStart * -1)));
                    } else if (i.Code.equals("Birthday")) {
                        JSONObject js = JSON.parseObject(i.Value, JSONObject.class);
                        form.BirthDayStartDate = js.getString("startValue");
                        form.BirthDayEndDate = js.getString("endValue");
                        form.LunarBirthDayStartDate = customerMapper.getLunarTime(DateTimeUtil.getCurrentYear() + "-" + form.BirthDayStartDate);
                        form.LunarBirthDayEndDate = customerMapper.getLunarTime(DateTimeUtil.getCurrentYear() + "-" + form.BirthDayEndDate);
                    } else if (i.Code.equals("RecentBirthday")) {
                        form.ExtendBirthDayStartDate = LocalDate.now().format(DateTimeFormatter.ofPattern("MM-dd"));
                        form.ExtendBirthDayEndDate = LocalDate.now().plusDays(Integer.parseInt(i.Value)).format(DateTimeFormatter.ofPattern("MM-dd"));
                        form.ExtendLunarBirthDayStartDate = customerMapper.getLunarTime(DateTimeUtil.getCurrentYear() + "-" + form.ExtendBirthDayStartDate);
                        form.ExtendLunarBirthDayEndDate = customerMapper.getLunarTime(DateTimeUtil.getCurrentYear() + "-" + form.ExtendBirthDayEndDate);
                    } else if (i.Code.equals("Card_Balance_Amount")) {
                        JSONObject js = JSON.parseObject(i.Value, JSONObject.class);
                        form.CardBalanceAmountStart = js.getBigDecimal("startValue");
                        form.CardBalanceAmountEnd = js.getBigDecimal("endValue");
                    } else if (i.Code.equals("Card_Balance_Times")) {
                        JSONObject js = JSON.parseObject(i.Value, JSONObject.class);
                        form.CardBalanceTimesStart = js.getInteger("startValue");
                        form.CardBalanceTimesEnd = js.getInteger("endValue");
                    } else if (i.Code.equals("Card_Expired_Date")) {
                        JSONObject js = JSON.parseObject(i.Value, JSONObject.class);
                        form.CardExpiredDateStartDate = js.getString("startValue");
                        form.CardExpiredDateEndDate = js.getString("endValue");
                    } else if (i.Code.equals("Card_Expired_Day")) {
                        JSONObject js = JSON.parseObject(i.Value, JSONObject.class);
                        form.CardExpiredDayStart = js.getInteger("startValue");
                        form.CardExpiredDayEnd = js.getInteger("endValue");
                    } else if (i.Code.equals("CreatedOn_Date")) {
                        JSONObject js = JSON.parseObject(i.Value, JSONObject.class);
                        form.CreatedOnDateStartDate = js.getString("startValue");
                        form.CreatedOnDateEndDate = js.getString("endValue");
                    } else if (i.Code.equals("CreatedOn_Day")) {
                        JSONObject js = JSON.parseObject(i.Value, JSONObject.class);
                        form.CreatedOnDayStart = js.getInteger("startValue");
                        form.CreatedOnDayEnd = js.getInteger("endValue");
                    } else if (i.Code.equals("Customer_Level")) {
                        List<Integer> list = JSON.parseArray(i.Value, Integer.class);
                        form.CustomerLevel = list.toArray(new Integer[list.size()]);
                    } else if (i.Code.equals("Customer_Source")) {
                        List<Integer> list = JSON.parseArray(i.Value, Integer.class);
                        form.CustomerSource = list.toArray(new Integer[list.size()]);
                    } else if (i.Code.equals("Customer_Tag")) {
                        List<Integer> list = JSON.parseArray(i.Value, Integer.class);
                        form.CustomerTag = list.toArray(new Integer[list.size()]);
                    } else if (i.Code.equals("Gender")) {
                        form.Gender = i.Value;
                    } else if (i.Code.equals("Sale_Amount")) {
                        JSONObject js = JSON.parseObject(i.Value, JSONObject.class);
                        form.SaleAmountStart = js.getBigDecimal("startValue");
                        form.SaleAmountEnd = js.getBigDecimal("endValue");
                    } else if (i.Code.equals("Sale_Average")) {
                        JSONObject js = JSON.parseObject(i.Value, JSONObject.class);
                        form.SaleAverageStart = js.getBigDecimal("startValue");
                        form.SaleAverageEnd = js.getBigDecimal("endValue");
                    } else if (i.Code.equals("Sale_Count")) {
                        JSONObject js = JSON.parseObject(i.Value, JSONObject.class);
                        form.SaleCountStart = js.getInteger("startValue");
                        form.SaleCountEnd = js.getInteger("endValue");
                    } else if (i.Code.equals("Sale_No_Recent")) {
                        form.SaleNoRecent = Integer.parseInt(i.Value);
                    } else if (i.Code.equals("Sale_Recent")) {
                        form.SaleRecent = Integer.parseInt(i.Value);
                    } else if (i.Code.equals("Treat_Amount")) {
                        JSONObject js = JSON.parseObject(i.Value, JSONObject.class);
                        form.TreatAmountStart = js.getBigDecimal("startValue");
                        form.TreatAmountEnd = js.getBigDecimal("endValue");
                    } else if (i.Code.equals("Treat_Average")) {
                        JSONObject js = JSON.parseObject(i.Value, JSONObject.class);
                        form.TreatAverageStart = js.getBigDecimal("startValue");
                        form.TreatAverageEnd = js.getBigDecimal("endValue");
                    } else if (i.Code.equals("Treat_Count")) {
                        JSONObject js = JSON.parseObject(i.Value, JSONObject.class);
                        form.TreatCountStart = js.getInteger("startValue");
                        form.TreatCountEnd = js.getInteger("endValue");
                    } else if (i.Code.equals("Treat_No_Recent")) {
                        form.TreatNoRecent = Integer.parseInt(i.Value);
                    } else if (i.Code.equals("Treat_Recent")) {
                        form.TreatRecent = Integer.parseInt(i.Value);
                    }
                });

                //获取条件符合的顾客
                List<Integer> customerList = followUpRuleMapper.getCustomerByFollowUpCondition(form);
                customerList.forEach(i -> {
                    String employeeID = "";
                    if (followUpRuleEntity.ServicerID != null) {
                        List<String> servicerList = callbackMapper.getCustomerServicer(i);
                        if (servicerList.size() > 0) {
                            List<String> serverEmployee = callbackMapper.getCustomerServicerEmployee(followUpRuleEntity.ServicerID, i);
                            if (serverEmployee.size() > 0) {
                                employeeID = serverEmployee.get(new Random().nextInt(serverEmployee.size()));
                            }
                        } else {
                            employeeID = followUpRuleEntity.EmployeeID;
                        }
                    } else {
                        employeeID = followUpRuleEntity.EmployeeID;
                    }
                    if (StringUtils.hasText(employeeID)) {
                        FollowUpRecordEntity followUpRecordEntity = new FollowUpRecordEntity();
                        followUpRecordEntity.CustomerID = i;
                        followUpRecordEntity.FollowUpMethodID = followUpRuleEntity.FollowUpMethodID;
                        followUpRecordEntity.FollowUpContent = "";
                        followUpRecordEntity.IsFollowUp = false;
                        try {
                            if (followUpRuleEntity.ScheduleType.equals("10")) {
                                followUpRecordEntity.PlannedOn = DateTimeUtil.toDate(followUpRuleEntity.ScheduleBeginDate);
                            } else {
                                followUpRecordEntity.PlannedOn = new Date();
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        followUpRecordEntity.PlannedRemark = followUpRuleEntity.FollowUpContent;
                        followUpRecordEntity.FollowUpBy = employeeID;
                        followUpRecordEntity.CreatedOn = new Date();
                        followUpRecordEntity.CreatedBy = employeeID;
                        followUpMapper.createFollowUpRecord(followUpRecordEntity);
                    }
                });

                Date date = null;
                try {
                    if (followUpRuleEntity.ScheduleType.equals("10")) {
                        date = DateTimeUtil.toDate(followUpRuleEntity.ScheduleBeginDate);
                    } else {
                        date = DateTimeUtil.addDay(DateTimeUtil.toDate(new Date()), followUpRuleEntity.ScheduleInterval);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                followUpRuleMapper.updateFollowUpRuleLastExecutionDate(followUpRuleEntity.ID, date);
            }
        }
        return BaseOutput.success();

    }
}