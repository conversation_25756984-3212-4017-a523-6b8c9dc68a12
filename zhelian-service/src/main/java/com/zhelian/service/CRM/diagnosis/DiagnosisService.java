package com.zhelian.service.CRM.diagnosis;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.utils.DateTimeUtil;
import com.zhelian.core.utils.YingXiaoYunUtil;
import com.zhelian.mapper.CRM.CustomerMapper;
import com.zhelian.mapper.CRM.DiagnosisMapper;
import com.zhelian.mapper.CRM.FollowUpMapper;
import com.zhelian.mapper.CRM.ReceptionMapper;
import com.zhelian.mapper.khs.EmployeeMapper;
import com.zhelian.mapper.yingxiaoyun.YingXiaoYunAppointmentBillMapper;
import com.zhelian.mapper.yingxiaoyun.YingXiaoYunTenantMapper;
import com.zhelian.model.CRM.entity.DiagnosisRecordAttachmentEntity;
import com.zhelian.model.CRM.entity.DiagnosisRecordEntity;
import com.zhelian.model.CRM.entity.FollowUpRecordEntity;
import com.zhelian.model.CRM.form.input.*;
import com.zhelian.model.CRM.form.output.DiagnosisExcelOutputForm;
import com.zhelian.model.CRM.form.output.DiagnosisOutputForm;
import com.zhelian.model.CRM.form.output.FollowUpRecordExcelOutputForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.CustomerEntity;
import com.zhelian.model.ibeauty.entity.CustomerServicerEntity;
import com.zhelian.model.khs.entity.EmployeeEntity;
import com.zhelian.model.yingxiaoyun.entity.YingXiaoYunCustomerEntity;
import com.zhelian.model.yingxiaoyun.entity.YingXiaoYunTenantEntity;
import com.zhelian.model.yingxiaoyun.form.output.YXYAttachmentOutPutForm;
import com.zhelian.model.yingxiaoyun.form.output.YXYDiagnosisListOutPutForm;
import com.zhelian.model.yingxiaoyun.form.output.YXYDiagnosisOutPutForm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Service
public class DiagnosisService {

    @Autowired
    DiagnosisMapper diagnosisMapper;
    @Autowired
    YingXiaoYunTenantMapper yingXiaoYunTenantMapper;
    @Autowired
    CustomerMapper customerMapper;
    @Autowired
    ReceptionMapper receptionMapper;
    @Autowired
    EmployeeMapper employeeMapper;
    @Autowired
    FollowUpMapper followUpMapper;
    @Autowired
    YingXiaoYunAppointmentBillMapper yingXiaoYunAppointmentBillMapper;

    private static final Logger logger = LoggerFactory.getLogger(DiagnosisService.class);

    public BaseOutput list(DiagnosisQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<DiagnosisOutputForm> list = diagnosisMapper.diagnosisList(SystemUserUtil.getSystemUserID(), form.Name, form.StartDate, form.EndDate, form.IsDiagnosis);
        return BasePageInfo.success(list);
    }

    public BaseOutput all(DiagnosisQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<DiagnosisOutputForm> list = diagnosisMapper.allDiagnosisList(SystemUserUtil.getSystemUserID(), form.Name, form.StartDate, form.EndDate, form.IsDiagnosis, SystemUserUtil.getSystemEntityID(),form.CustomerLevelID,form.ChannelName);
        return BasePageInfo.success(list);
    }

    @Transactional
    public BaseOutput diagnosis(DiagnosisAddForm form) throws Exception {
        DiagnosisRecordEntity diagnosisRecordEntity = diagnosisMapper.getDiagnosisRecordEntity(form.DiagnosisID);
        diagnosisRecordEntity.DiagnosisOn = new Date();
        diagnosisRecordEntity.DiagnosisContent = form.DiagnosisContent;
        diagnosisRecordEntity.IsDiagnosis = true;
        receptionMapper.updateDiagnosisRecord(diagnosisRecordEntity);

        List<DiagnosisRecordAttachmentEntity> list = new ArrayList<>();
        if (form.Attachment != null && form.Attachment.size() > 0) {
            for (FollowUpRecordAttachmentAddForm followUpRecordAttachmentAddForm : form.Attachment) {
                DiagnosisRecordAttachmentEntity diagnosisRecordAttachmentEntity = new DiagnosisRecordAttachmentEntity();
                diagnosisRecordAttachmentEntity.DiagnosisRecordID = form.DiagnosisID;
                diagnosisRecordAttachmentEntity.AttachmentURL = followUpRecordAttachmentAddForm.AttachmentURL;
                diagnosisRecordAttachmentEntity.AttachmentType = followUpRecordAttachmentAddForm.AttachmentType;
                list.add(diagnosisRecordAttachmentEntity);
            }

            diagnosisMapper.createDiagnosisRecordAttachmentEntity(list);
        }

        if (form.IsNextFollowUp) {
            FollowUpRecordEntity nextFollowUpRecordEntity = new FollowUpRecordEntity();
            nextFollowUpRecordEntity.CustomerID = diagnosisRecordEntity.CustomerID;
            nextFollowUpRecordEntity.FollowUpContent = "";
            nextFollowUpRecordEntity.IsFollowUp = false;
            nextFollowUpRecordEntity.PlannedOn = DateTimeUtil.toDate(form.PlannedOn);
            nextFollowUpRecordEntity.PlannedRemark = form.PlannedRemark;
            nextFollowUpRecordEntity.FollowUpBy = SystemUserUtil.getSystemUserID();
            nextFollowUpRecordEntity.CreatedOn = new Date();
            nextFollowUpRecordEntity.CreatedBy = SystemUserUtil.getSystemUserID();
            followUpMapper.createFollowUpRecord(nextFollowUpRecordEntity);
        }


        send(diagnosisRecordEntity, list);
        return BaseOutput.success();
    }


    public BaseOutput transferDiagnosisAddForm(TransferDiagnosisAddForm form) {
        DiagnosisRecordEntity recordEntity = diagnosisMapper.getDiagnosisRecordEntity(form.DiagnosisID);

        DiagnosisRecordEntity diagnosisRecordEntity = new DiagnosisRecordEntity();
        diagnosisRecordEntity.AppointmentBillID = recordEntity.AppointmentBillID;
        diagnosisRecordEntity.CustomerID = recordEntity.CustomerID;
        diagnosisRecordEntity.Guidance = form.Guidance;
        diagnosisRecordEntity.DiagnosisContent = "";
        diagnosisRecordEntity.IsDiagnosis = false;
        diagnosisRecordEntity.DiagnosisBy = form.DiagnosisBy;
        diagnosisRecordEntity.CreatedOn = new Date();
        diagnosisRecordEntity.CreatedBy = SystemUserUtil.getSystemUserID();

        receptionMapper.diagnosisRecord(diagnosisRecordEntity);

        if (form.IsServicer) {
            if (form.Status.equals("20")) {
                customerMapper.deleteServicerByCustomerID(recordEntity.CustomerID, form.ServicerID);
                CustomerServicerEntity entity = new CustomerServicerEntity();
                entity.CustomerID = recordEntity.CustomerID;
                entity.ServicerID = form.ServicerID;
                entity.EmployeeID = form.DiagnosisBy;
                customerMapper.insertCustomerServicer(entity);
            } else if (form.Status.equals("30")) {

                Integer count = customerMapper.getCustomerServicer(recordEntity.CustomerID, form.ServicerID, form.DiagnosisBy);
                if (count == 0) {
                    CustomerServicerEntity entity = new CustomerServicerEntity();
                    entity.CustomerID = recordEntity.CustomerID;
                    entity.ServicerID = form.ServicerID;
                    entity.EmployeeID = form.DiagnosisBy;
                    customerMapper.insertCustomerServicer(entity);
                }
            }
        }

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput update(DiagnosisAddForm form) throws Exception {
        DiagnosisRecordEntity diagnosisRecordEntity = diagnosisMapper.getDiagnosisRecordEntity(form.DiagnosisID);
        diagnosisRecordEntity.DiagnosisContent = form.DiagnosisContent;
        receptionMapper.updateDiagnosisRecord(diagnosisRecordEntity);

        List<DiagnosisRecordAttachmentEntity> followUpAttachment = receptionMapper.getDiagnosisRecordAttachment(form.DiagnosisID);
        if (followUpAttachment.size() > 0) {

            receptionMapper.deleteDiagnosisRecordAttachment(form.DiagnosisID);
        }

        List<DiagnosisRecordAttachmentEntity> list = new ArrayList<>();
        if (form.Attachment != null && form.Attachment.size() > 0) {
            for (FollowUpRecordAttachmentAddForm followUpRecordAttachmentAddForm : form.Attachment) {
                DiagnosisRecordAttachmentEntity diagnosisRecordAttachmentEntity = new DiagnosisRecordAttachmentEntity();
                diagnosisRecordAttachmentEntity.DiagnosisRecordID = form.DiagnosisID;
                diagnosisRecordAttachmentEntity.AttachmentURL = followUpRecordAttachmentAddForm.AttachmentURL;
                diagnosisRecordAttachmentEntity.AttachmentType = followUpRecordAttachmentAddForm.AttachmentType;
                list.add(diagnosisRecordAttachmentEntity);
            }

            diagnosisMapper.createDiagnosisRecordAttachmentEntity(list);
        }
        send(diagnosisRecordEntity, list);
        return BaseOutput.success();
    }

    public void send(DiagnosisRecordEntity diagnosisRecordEntity, List<DiagnosisRecordAttachmentEntity> list) {
        try {
            YXYDiagnosisOutPutForm yxyInputForm = new YXYDiagnosisOutPutForm();
            YingXiaoYunTenantEntity yingXiaoYunTenant = yingXiaoYunTenantMapper.getYingXiaoYunTenantEntity();
            if (yingXiaoYunTenant != null && yingXiaoYunTenant.Active) {
                CustomerEntity customer = customerMapper.getCustomer(diagnosisRecordEntity.CustomerID);
                EmployeeEntity employee = employeeMapper.getEmployee(SystemUserUtil.getSystemUserID());
                YingXiaoYunCustomerEntity yingXiaoYunCustomer = yingXiaoYunAppointmentBillMapper.getYingXiaoYunCustomer(customer.PhoneNumber);
                if (yingXiaoYunCustomer != null) {
                    List<YXYDiagnosisListOutPutForm> diagnosisList = new ArrayList<>();
                    List<YXYAttachmentOutPutForm> attachmentOutPutFormList = new ArrayList<>();
                    yxyInputForm.EnterpriseCode = SystemUserUtil.getSystemEnterpriseCode();
                    yxyInputForm.TenantCode = yingXiaoYunTenant.TenantID;
                    yxyInputForm.PhoneNumber = customer.PhoneNumber;
                    YXYDiagnosisListOutPutForm yxyDiagnosisListOutPutForm = new YXYDiagnosisListOutPutForm();
                    yxyDiagnosisListOutPutForm.DiagnosisRecordID = diagnosisRecordEntity.ID;
                    yxyDiagnosisListOutPutForm.DiagnosisContent = diagnosisRecordEntity.DiagnosisContent;
                    yxyDiagnosisListOutPutForm.DiagnosisOn = DateTimeUtil.toDate(diagnosisRecordEntity.DiagnosisOn);
                    yxyDiagnosisListOutPutForm.DiagnosisBy = employee.Name;
                    yxyDiagnosisListOutPutForm.CreatedOn = DateTimeUtil.toDate(diagnosisRecordEntity.CreatedOn);
                    if (list != null && list.size() > 0) {
                        for (DiagnosisRecordAttachmentEntity attachmentEntity : list) {
                            YXYAttachmentOutPutForm attachmentOutPutForm = new YXYAttachmentOutPutForm();
                            attachmentOutPutForm.AttachmentURL = attachmentEntity.AttachmentURL;
                            attachmentOutPutFormList.add(attachmentOutPutForm);
                        }
                    }
                    yxyDiagnosisListOutPutForm.Attachment = attachmentOutPutFormList;
                    diagnosisList.add(yxyDiagnosisListOutPutForm);
                    yxyInputForm.DiagnosisList = diagnosisList;

                    JSONObject param = new JSONObject();
                    param.put("code", "ZL_CreateOrUpdateDiagnosisRecord");
                    param.put("content", JSON.toJSONString(yxyInputForm));
                    JSONObject param2 = new JSONObject();
                    param2.put("request", param);
                    YingXiaoYunUtil.sendHttpPost(param2);
                }
            }
        } catch (Exception e) {
            logger.error("接诊数据传输失败！", e);
            e.printStackTrace();

        }
    }

    public BaseOutput excelDisPlayPhone(DiagnosisQueryForm form, HttpServletResponse response) {
        List<DiagnosisExcelOutputForm> list = diagnosisMapper.excelDisPlayPhone(SystemUserUtil.getSystemUserID(), form.Name, form.StartDate, form.EndDate, form.IsDiagnosis, SystemUserUtil.getSystemEntityID(),form.CustomerLevelID,form.ChannelName);
        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("接诊报表", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), DiagnosisExcelOutputForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("接诊报表").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    public BaseOutput excelNoDisPlayPhone(DiagnosisQueryForm form, HttpServletResponse response) {
        List<DiagnosisExcelOutputForm> list = diagnosisMapper.excelNoDisPlayPhone(SystemUserUtil.getSystemUserID(), form.Name, form.StartDate, form.EndDate, form.IsDiagnosis, SystemUserUtil.getSystemEntityID(),form.CustomerLevelID,form.ChannelName);
        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("接诊报表", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), DiagnosisExcelOutputForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("接诊报表").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }
}