package com.zhelian.service.CRM.consultation;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.CRM.ConsultationMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.CRM.entity.ConsultationEntity;
import com.zhelian.model.CRM.form.input.BatchDeleteForm;
import com.zhelian.model.CRM.form.input.ConsultationForm;
import com.zhelian.model.CRM.form.input.ConsultationQueryForm;
import com.zhelian.model.CRM.form.output.ConsultationOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ConsultationService {

    @Autowired
    private ConsultationMapper consultationMapper;

    @Autowired
    private ObjectMapper objectMapper;

    @Transactional
    public BaseOutputForm<ConsultationOutputForm> createConsultation(ConsultationForm form) {
        if (form == null) {
            return BaseOutputForm.failure("咨询信息不能为空");
        }
        
        if (form.getName() == null || form.getName().trim().isEmpty()) {
            return BaseOutputForm.failure("姓名不能为空");
        }
        
        if (form.getPhoneNumber() == null || form.getPhoneNumber().trim().isEmpty()) {
            return BaseOutputForm.failure("手机号码不能为空");
        }
        
        if (form.getGender() == null || form.getGender().trim().isEmpty()) {
            return BaseOutputForm.failure("性别不能为空");
        }
        
        if (form.getSpotRemoval() == null || form.getSpotRemoval().isEmpty()) {
            return BaseOutputForm.failure("祛斑经历不能为空");
        }
        
        if (form.getCosmetic() == null || form.getCosmetic().isEmpty()) {
            return BaseOutputForm.failure("美容经历不能为空");
        }
        
        if (form.getSkinCare() == null || form.getSkinCare().isEmpty()) {
            return BaseOutputForm.failure("家居护肤品不能为空");
        }
        
        if (form.getStainsDuration() == null || form.getStainsDuration().trim().isEmpty()) {
            return BaseOutputForm.failure("色斑出现时长不能为空");
        }
        
        if (form.getDemand() == null || form.getDemand().isEmpty()) {
            return BaseOutputForm.failure("改善诉求不能为空");
        }

        if (form.getEntityId() == null) {
            return BaseOutputForm.failure("所属门店不能为空");
        }

        if (form.getEmployeeId() == null || form.getEmployeeId().trim().isEmpty()) {
            return BaseOutputForm.failure("所属员工不能为空");
        }

        ConsultationEntity consultation = new ConsultationEntity();
        consultation.setName(form.getName());
        consultation.setPhoneNumber(form.getPhoneNumber());
        consultation.setGender(form.getGender());
        consultation.setAge(form.getAge());
        consultation.setIsMarried(form.getIsMarried());
        consultation.setProvinceCode(form.getProvinceCode());
        consultation.setCityCode(form.getCityCode());
        consultation.setAreaCode(form.getAreaCode());
        consultation.setAddress(form.getAddress());
        
        try {
            consultation.setSpotRemoval(form.getSpotRemoval() != null ? objectMapper.writeValueAsString(form.getSpotRemoval()) : null);
            consultation.setCosmetic(form.getCosmetic() != null ? objectMapper.writeValueAsString(form.getCosmetic()) : null);
            consultation.setSkinCare(form.getSkinCare() != null ? objectMapper.writeValueAsString(form.getSkinCare()) : null);
            consultation.setDemand(form.getDemand() != null ? objectMapper.writeValueAsString(form.getDemand()) : null);
        } catch (JsonProcessingException e) {
            return BaseOutputForm.failure("JSON转换失败");
        }
        
        consultation.setBrand(form.getBrand());
        consultation.setStainsDuration(form.getStainsDuration());
        consultation.setEntityId(form.getEntityId());
        consultation.setEmployeeId(form.getEmployeeId());
        consultation.setCreatedOn(new Date());

        int result = consultationMapper.insert(consultation);
        
        if (result > 0) {
            ConsultationEntity createdEntity = consultationMapper.selectByPrimaryKey(consultation.getId());
            return BaseOutputForm.success(convertToOutputForm(createdEntity), "创建成功");
        } else {
            return BaseOutputForm.failure("创建失败");
        }
    }

    @Transactional
    public BaseOutput batchDeleteConsultations(BatchDeleteForm form) {
        if (form == null || form.getIds() == null || form.getIds().isEmpty()) {
            return BaseOutput.failed("ID列表不能为空");
        }
        
        int result = consultationMapper.batchDeleteByIds(form.getIds());
        
        if (result > 0) {
            return BaseOutput.success("删除成功");
        } else {
            return BaseOutput.failed("删除失败");
        }
    }

    @Transactional
    public BaseOutputForm<ConsultationOutputForm> updateConsultation(ConsultationForm form) {
        if (form == null) {
            return BaseOutputForm.failure("咨询信息不能为空");
        }
        
        if (form.getId() == null) {
            return BaseOutputForm.failure("ID不能为空");
        }
        
        if (form.getName() == null || form.getName().trim().isEmpty()) {
            return BaseOutputForm.failure("姓名不能为空");
        }
        
        if (form.getPhoneNumber() == null || form.getPhoneNumber().trim().isEmpty()) {
            return BaseOutputForm.failure("手机号码不能为空");
        }
        
        if (form.getGender() == null || form.getGender().trim().isEmpty()) {
            return BaseOutputForm.failure("性别不能为空");
        }
        
        if (form.getSpotRemoval() == null || form.getSpotRemoval().isEmpty()) {
            return BaseOutputForm.failure("祛斑经历不能为空");
        }
        
        if (form.getCosmetic() == null || form.getCosmetic().isEmpty()) {
            return BaseOutputForm.failure("美容经历不能为空");
        }
        
        if (form.getSkinCare() == null || form.getSkinCare().isEmpty()) {
            return BaseOutputForm.failure("家居护肤品不能为空");
        }
        
        if (form.getStainsDuration() == null || form.getStainsDuration().trim().isEmpty()) {
            return BaseOutputForm.failure("色斑出现时长不能为空");
        }
        
        if (form.getDemand() == null || form.getDemand().isEmpty()) {
            return BaseOutputForm.failure("改善诉求不能为空");
        }

        ConsultationEntity consultation = new ConsultationEntity();
        consultation.setId(form.getId());
        consultation.setName(form.getName());
        consultation.setPhoneNumber(form.getPhoneNumber());
        consultation.setGender(form.getGender());
        consultation.setAge(form.getAge());
        consultation.setIsMarried(form.getIsMarried());
        consultation.setProvinceCode(form.getProvinceCode());
        consultation.setCityCode(form.getCityCode());
        consultation.setAreaCode(form.getAreaCode());
        consultation.setAddress(form.getAddress());
        
        try {
            consultation.setSpotRemoval(form.getSpotRemoval() != null ? objectMapper.writeValueAsString(form.getSpotRemoval()) : null);
            consultation.setCosmetic(form.getCosmetic() != null ? objectMapper.writeValueAsString(form.getCosmetic()) : null);
            consultation.setSkinCare(form.getSkinCare() != null ? objectMapper.writeValueAsString(form.getSkinCare()) : null);
            consultation.setDemand(form.getDemand() != null ? objectMapper.writeValueAsString(form.getDemand()) : null);
        } catch (JsonProcessingException e) {
            return BaseOutputForm.failure("JSON转换失败");
        }
        
        consultation.setBrand(form.getBrand());
        consultation.setStainsDuration(form.getStainsDuration());

        int result = consultationMapper.updateByPrimaryKey(consultation);
        
        if (result > 0) {
            ConsultationEntity updatedEntity = consultationMapper.selectByPrimaryKey(consultation.getId());
            return BaseOutputForm.success(convertToOutputForm(updatedEntity), "更新成功");
        } else {
            return BaseOutputForm.failure("更新失败");
        }
    }

    public BaseOutputForm<ConsultationOutputForm> getConsultationById(Integer id) {
        if (id == null) {
            return BaseOutputForm.failure("ID不能为空");
        }
        
        ConsultationEntity consultation = consultationMapper.selectByPrimaryKey(id);
        
        if (consultation != null) {
            return BaseOutputForm.success(convertToOutputForm(consultation), "查询成功");
        } else {
            return BaseOutputForm.failure("未找到该咨询信息");
        }
    }

    public BasePageInfo<ConsultationOutputForm> queryConsultations(ConsultationQueryForm form) {
        if (form == null) {
            return BasePageInfo.success(null, "查询条件不能为空");
        }

        if (form.getEntityId() == null) {
            return BasePageInfo.success(null, "实体ID不能为空");
        }

        if (form.getEmployeeId() == null || form.getEmployeeId().trim().isEmpty()) {
            return BasePageInfo.success(null, "员工ID不能为空");
        }
        
        Integer pageNum = form.getPageNum() != null ? form.getPageNum() : 1;
        Integer pageSize = form.getPageSize() != null ? form.getPageSize() : 10;
        
        PageHelper.startPage(pageNum, pageSize);
        List<ConsultationEntity> consultations = consultationMapper.selectByConditions(
            form.getName(),
            form.getPhoneNumber(),
            form.getGender(),
            form.getStartTime(),
            form.getEndTime(),
            form.getEntityId(),
            form.getEmployeeId()
        );
        
        List<ConsultationOutputForm> outputForms = convertToOutputForms(consultations);
        return BasePageInfo.success(outputForms, "查询成功");
    }

    private ConsultationOutputForm convertToOutputForm(ConsultationEntity entity) {
        if (entity == null) {
            return null;
        }
        
        ConsultationOutputForm form = new ConsultationOutputForm();
        form.setId(entity.getId());
        form.setName(entity.getName());
        form.setPhoneNumber(entity.getPhoneNumber());
        form.setGender(entity.getGender());
        form.setAge(entity.getAge());
        form.setIsMarried(entity.getIsMarried());
        form.setProvinceCode(entity.getProvinceCode());
        form.setCityCode(entity.getCityCode());
        form.setAreaCode(entity.getAreaCode());
        form.setAddress(entity.getAddress());
        form.setBrand(entity.getBrand());
        form.setStainsDuration(entity.getStainsDuration());
        form.setEntityId(entity.getEntityId());
        form.setEmployeeId(entity.getEmployeeId());
        form.setCreatedOn(entity.getCreatedOn());
        
        try {
            if (entity.getSpotRemoval() != null && !entity.getSpotRemoval().trim().isEmpty()) {
                form.setSpotRemoval(objectMapper.readValue(entity.getSpotRemoval(), new TypeReference<List<String>>() {}));
            }
            if (entity.getCosmetic() != null && !entity.getCosmetic().trim().isEmpty()) {
                form.setCosmetic(objectMapper.readValue(entity.getCosmetic(), new TypeReference<List<String>>() {}));
            }
            if (entity.getSkinCare() != null && !entity.getSkinCare().trim().isEmpty()) {
                form.setSkinCare(objectMapper.readValue(entity.getSkinCare(), new TypeReference<List<String>>() {}));
            }
            if (entity.getDemand() != null && !entity.getDemand().trim().isEmpty()) {
                form.setDemand(objectMapper.readValue(entity.getDemand(), new TypeReference<List<String>>() {}));
            }
        } catch (Exception e) {
            // 静默处理JSON转换错误
        }
        
        return form;
    }

    private List<ConsultationOutputForm> convertToOutputForms(List<ConsultationEntity> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(this::convertToOutputForm)
                .collect(Collectors.toList());
    }
}