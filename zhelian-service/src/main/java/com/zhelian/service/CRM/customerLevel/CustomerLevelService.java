package com.zhelian.service.CRM.customerLevel;

import com.google.common.collect.Lists;
import com.zhelian.core.utils.DateTimeUtil;
import com.zhelian.mapper.CRM.CustomerGrowthMapper;
import com.zhelian.mapper.CRM.CustomerLevelMapper;
import com.zhelian.mapper.CRM.CustomerMapper;
import com.zhelian.mapper.CRM.CustomerReduceConfigMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.CustomerLevelEntity;
import com.zhelian.model.ibeauty.entity.CustomerLevelSettingEntity;
import com.zhelian.model.ibeauty.entity.CustomerLevelValidityEntity;
import com.zhelian.model.ibeauty.entity.CustomerReduceConfigEntity;
import com.zhelian.model.ibeauty.form.input.CustomerLevelAddForm;
import com.zhelian.model.ibeauty.form.input.CustomerLevelQueryForm;
import com.zhelian.model.ibeauty.form.input.CustomerLevelUpdateForm;
import com.zhelian.model.ibeauty.form.output.CustomerLevelOutputForm;
import com.zhelian.model.ibeauty.form.output.CustomerReduceConfigOutputForm;
import com.zhelian.model.ibeauty.form.output.MemberCustomerOutputForm;
import com.zhelian.service.CRM.customerReduceConfig.CustomerReduceConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class CustomerLevelService {

    @Autowired
    CustomerLevelMapper customerLevelMapper;

    @Autowired
    CustomerGrowthMapper customerGrowthMapper;
    @Autowired
    CustomerReduceConfigMapper customerReduceConfigMapper;
    @Autowired
    CustomerReduceConfigService customerReduceConfigService;
    @Autowired
    CustomerMapper customerMapper;

    public BaseOutput allCustomerLevel(CustomerLevelQueryForm form) {
        List<CustomerLevelOutputForm> list = customerLevelMapper.allCustomerLevel();
        return BaseOutputForm.success(list);
    }

    //获取所有用户等级
    public BaseOutput getAllLevel(CustomerLevelEntity form) {
        List<CustomerLevelEntity> list = customerLevelMapper.getAllLevel(form);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput createCustomerLevel(CustomerLevelAddForm form) {
        CustomerLevelEntity customerLevelEntity = new CustomerLevelEntity();
        customerLevelEntity.Name = form.Name;
        customerLevelEntity.LevelValue = form.LevelValue;
        customerLevelEntity.ReduceLevelValue = form.ReduceLevelValue;

        customerLevelMapper.createCustomerLevel(customerLevelEntity);

        customerLevelMapper.updateCustomerLevelSetting(true);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateCustomerLevel(CustomerLevelUpdateForm form) {
        CustomerLevelEntity customerLevelEntity = customerLevelMapper.getCustomerLevel(form.ID);

        if (!customerLevelEntity.LevelValue.equals(form.LevelValue) || (customerLevelEntity.ReduceLevelValue == null && form.ReduceLevelValue != null) || (customerLevelEntity.ReduceLevelValue != null && form.ReduceLevelValue == null) || (customerLevelEntity.ReduceLevelValue != null && form.ReduceLevelValue != null && (!customerLevelEntity.ReduceLevelValue.equals(form.ReduceLevelValue)))) {
            customerLevelMapper.updateCustomerLevelSetting(true);
        }

        customerLevelEntity.Name = form.Name;
        customerLevelEntity.LevelValue = form.LevelValue;
        customerLevelEntity.ReduceLevelValue = form.ReduceLevelValue;

        customerLevelMapper.updateCustomerLevel(customerLevelEntity);


        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput delete(CustomerLevelUpdateForm form) {
        customerLevelMapper.deleteCustomerLevel(form.ID);

        customerLevelMapper.updateCustomerLevelSetting(true);
        return BaseOutput.success();
    }

    public BaseOutput status() {
        CustomerLevelSettingEntity customerLevelSettingEntity = customerGrowthMapper.status();
        return BaseOutputForm.success(customerLevelSettingEntity.IsUpdateCustomerLevel);
    }


    @Transactional
    public BaseOutput updateAllCustomerLevel() {
        CustomerLevelSettingEntity customerLevelSettingEntity = customerGrowthMapper.status();
        List<CustomerLevelOutputForm> customerLevelOutputForms = customerLevelMapper.allCustomerLevel();
        if (customerLevelSettingEntity.IsUpdateCustomerLevel && customerLevelOutputForms.size() > 0) {
            //顾客有效期
            List<CustomerLevelValidityEntity> newCustomerValidDayList = new ArrayList<>();
            //要更新的顾客
            List<MemberCustomerOutputForm> newList = new ArrayList<>();
            //升降级规则
            CustomerReduceConfigOutputForm customerReduceConfig = customerReduceConfigMapper.getCustomerReduceConfig();
            List<MemberCustomerOutputForm> list = customerMapper.getMemberCustomer();
            //成长值最低的等级
            Integer level = customerLevelOutputForms.get(0).ID;
            for (MemberCustomerOutputForm memberCustomerOutputForm : list) {
                if (customerReduceConfig.IsReduce == false) { //暂不降级
                    //获取顾客全部成长值
                    BigDecimal customerGrowth = customerGrowthMapper.getCustomerGrowth(memberCustomerOutputForm.ID) == null ? BigDecimal.ZERO : customerGrowthMapper.getCustomerGrowth(memberCustomerOutputForm.ID);
                    //获取成长值所对应的等级
                    Integer customerLevelByGrowthTotal = customerGrowthMapper.getCustomerLevelByGrowthTotal(customerGrowth);
                    customerLevelByGrowthTotal = customerLevelByGrowthTotal == null ? level : customerLevelByGrowthTotal;
                    if (memberCustomerOutputForm.LevelID != null) {//当前顾客等级未被删除
                        if (customerGrowth.compareTo(memberCustomerOutputForm.LevelValue) < 0) {
                            if (memberCustomerOutputForm.ReduceLevelValue != null) {//保级
                                if (customerGrowth.compareTo(memberCustomerOutputForm.ReduceLevelValue) < 0) {
                                    memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                                }
                            } else {
                                memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                            }
                        } else {
                            memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                        }
                    } else {
                        memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                    }

                } else {
                    CustomerLevelValidityEntity customerLevelValidityEntity = new CustomerLevelValidityEntity();
                    if (customerReduceConfig.ReduceRule.equals("10")) { //降级规则（10：有效期模式、20：定期更新模式）
                        //获取升级时间到有效期的成长值加上当前订单成长值
                        BigDecimal customerGrowth = customerGrowthMapper.getCustomerLevelByValidityOn(memberCustomerOutputForm.ID, DateTimeUtil.toyyyyMMdd2(DateTimeUtil.addDay(new Date(), customerReduceConfig.VaildModeDay * -1)), DateTimeUtil.toyyyyMMdd2(DateTimeUtil.addDay(new Date(), 1)));
                        customerGrowth = customerGrowth == null ? BigDecimal.ZERO : customerGrowth;
                        Integer customerLevelByGrowthTotal = customerGrowthMapper.getCustomerLevelByGrowthTotal(customerGrowth);
                        //获取成长值对应的等级
                        customerLevelByGrowthTotal = customerLevelByGrowthTotal == null ? level : customerLevelByGrowthTotal;

                        if (customerReduceConfig.ReduceMode.equals("10")) {//降级模式（10：若达不到客户所在等级的成长值要求，会员等级降低一级、20：对客户重新定级）
                            CustomerLevelEntity downOneLevelByID = customerLevelMapper.getDownOneLevelByID(memberCustomerOutputForm.CustomerLevelID);
                            if (memberCustomerOutputForm.LevelID != null) {
                                if (customerGrowth.compareTo(memberCustomerOutputForm.LevelValue) < 0) {
                                    if (memberCustomerOutputForm.ReduceLevelValue != null) {
                                        if (customerGrowth.compareTo(memberCustomerOutputForm.ReduceLevelValue) < 0) {
                                            memberCustomerOutputForm.CustomerLevelID = downOneLevelByID == null ? level : downOneLevelByID.ID;
                                        }
                                    } else {
                                        memberCustomerOutputForm.CustomerLevelID = downOneLevelByID == null ? level : downOneLevelByID.ID;
                                    }
                                } else {
                                    memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                                }
                            } else {
                                memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                            }

                        } else {
                            if (memberCustomerOutputForm.LevelID != null) {
                                if (customerGrowth.compareTo(memberCustomerOutputForm.LevelValue) < 0) {
                                    if (memberCustomerOutputForm.ReduceLevelValue != null) {
                                        if (customerGrowth.compareTo(memberCustomerOutputForm.ReduceLevelValue) < 0) {
                                            memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                                        }
                                    } else {
                                        memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                                    }
                                } else {
                                    memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                                }
                            } else {
                                memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                            }
                        }
                        customerLevelValidityEntity.CustomerID = memberCustomerOutputForm.ID;
                        customerLevelValidityEntity.ModifyOn = new Date();
                        customerLevelValidityEntity.ValidityOn = DateTimeUtil.addDay(new Date(), customerReduceConfig.VaildModeDay);

                    } else {
                        BigDecimal customerGrowth = customerGrowthMapper.getCustomerLevelByValidity(memberCustomerOutputForm.ID, DateTimeUtil.toDate(DateTimeUtil.addMonth(new Date(), customerReduceConfig.RegularModeMonth * -1)), DateTimeUtil.toyyyyMMdd2(DateTimeUtil.addDay(new Date(), 1)));
                        customerGrowth = customerGrowth == null ? BigDecimal.ZERO : customerGrowth;
                        Integer customerLevelByGrowthTotal = customerGrowthMapper.getCustomerLevelByGrowthTotal(customerGrowth);
                        customerLevelByGrowthTotal = customerLevelByGrowthTotal == null ? level : customerLevelByGrowthTotal;
                        if (customerReduceConfig.ReduceMode.equals("10")) {
                            CustomerLevelEntity downOneLevelByID = customerLevelMapper.getDownOneLevelByID(memberCustomerOutputForm.CustomerLevelID);
                            if (memberCustomerOutputForm.LevelID != null) {
                                if (customerGrowth.compareTo(memberCustomerOutputForm.LevelValue) < 0) {
                                    if (memberCustomerOutputForm.ReduceLevelValue != null) {
                                        if (customerGrowth.compareTo(memberCustomerOutputForm.ReduceLevelValue) < 0) {
                                            memberCustomerOutputForm.CustomerLevelID = downOneLevelByID == null ? level : downOneLevelByID.ID;
                                        }
                                    } else {
                                        memberCustomerOutputForm.CustomerLevelID = downOneLevelByID == null ? level : downOneLevelByID.ID;
                                    }
                                } else {
                                    memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                                }
                            } else {
                                memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                            }

                        } else {
                            if (memberCustomerOutputForm.LevelID != null) {
                                if (customerGrowth.compareTo(memberCustomerOutputForm.LevelValue) < 0) {
                                    if (memberCustomerOutputForm.ReduceLevelValue != null) {
                                        if (customerGrowth.compareTo(memberCustomerOutputForm.ReduceLevelValue) < 0) {
                                            memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                                        }
                                    } else {
                                        memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                                    }
                                } else {
                                    memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                                }
                            } else {
                                memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                            }
                        }

                        customerLevelValidityEntity.CustomerID = memberCustomerOutputForm.ID;
                        customerLevelValidityEntity.ModifyOn = new Date();
                        customerLevelValidityEntity.ValidityOn = DateTimeUtil.addDay(customerReduceConfig.RegularModeUpdateDate, -1);
                    }
                    newCustomerValidDayList.add(customerLevelValidityEntity);
                }
                if (memberCustomerOutputForm.IsLockMemberLevel == false) {
                    newList.add(memberCustomerOutputForm);
                }
            }
            customerLevelMapper.deleteAllCustomerLevelValidity();

            List<List<CustomerLevelValidityEntity>> subs = Lists.partition(newCustomerValidDayList, 200);
            for (List<CustomerLevelValidityEntity> entityList : subs) {
                customerLevelMapper.insertCustomerValid(entityList);
            }
            Map<Integer, List<Integer>> collect = newList.stream().collect(Collectors.groupingBy(i -> i.CustomerLevelID, Collectors.mapping(i -> i.ID, Collectors.toList())));

            Set<Map.Entry<Integer, List<Integer>>> entries = collect.entrySet();
            for (Map.Entry<Integer, List<Integer>> entry : entries) {
                List<List<Integer>> subss = Lists.partition(entry.getValue(), 200);
                for (List<Integer> entityList : subss) {
                    customerMapper.updateCustomerByLevel(entry.getKey(), entityList);
                }
            }
            customerLevelMapper.updateCustomerLevelSetting(false);
        }
        return BaseOutput.success();
    }


    //有效期模式
    public void updateCustomerLevelByValidityMode() {
        CustomerReduceConfigEntity customerReduceConfigEntity = customerReduceConfigMapper.getCustomerReduceConfigEntity();
        List<CustomerLevelOutputForm> customerLevelOutputForms = customerLevelMapper.allCustomerLevel();
        if (customerReduceConfigEntity.IsReduce == true && customerReduceConfigEntity.ReduceRule.equals("10") && customerLevelOutputForms.size() > 0) {

            //要更新的顾客
            List<MemberCustomerOutputForm> newList = new ArrayList<>();

            List<MemberCustomerOutputForm> list = customerMapper.getMemberCustomer();
            //成长值最低的等级
            Integer level = customerLevelOutputForms.get(0).ID;
            for (MemberCustomerOutputForm memberCustomerOutputForm : list) {
                long timeIntervalDay = DateTimeUtil.getTimeIntervalDay(memberCustomerOutputForm.ModifyOn, new Date());
                if (timeIntervalDay == customerReduceConfigEntity.VaildModeDay.longValue()) {
                    BigDecimal customerGrowthTotal = customerGrowthMapper.getCustomerLevelByValidityOn(memberCustomerOutputForm.ID, DateTimeUtil.toyyyyMMdd2(DateTimeUtil.addDay(new Date(), customerReduceConfigEntity.VaildModeDay * -1)), DateTimeUtil.toyyyyMMdd2(DateTimeUtil.addDay(new Date(), 1)));
                    customerGrowthTotal = customerGrowthTotal == null ? BigDecimal.ZERO : customerGrowthTotal;
                    Integer customerLevelByGrowthTotal = customerGrowthMapper.getCustomerLevelByGrowthTotal(customerGrowthTotal);
                    customerLevelByGrowthTotal = customerLevelByGrowthTotal == null ? level : customerLevelByGrowthTotal;

                    if (customerReduceConfigEntity.ReduceMode.equals("10")) {
                        CustomerLevelEntity downOneLevelByID = customerLevelMapper.getDownOneLevelByID(memberCustomerOutputForm.CustomerLevelID);
                        if (memberCustomerOutputForm.LevelID != null) {
                            if (customerGrowthTotal.compareTo(memberCustomerOutputForm.LevelValue) < 0) {
                                if (memberCustomerOutputForm.ReduceLevelValue != null) {
                                    if (customerGrowthTotal.compareTo(memberCustomerOutputForm.ReduceLevelValue) < 0) {
                                        memberCustomerOutputForm.CustomerLevelID = downOneLevelByID == null ? level : downOneLevelByID.ID;
                                    }
                                } else {
                                    memberCustomerOutputForm.CustomerLevelID = downOneLevelByID == null ? level : downOneLevelByID.ID;
                                }
                            } else {
                                memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                            }
                        } else {
                            memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                        }

                    } else {
                        if (memberCustomerOutputForm.LevelID != null) {
                            if (customerGrowthTotal.compareTo(memberCustomerOutputForm.LevelValue) < 0) {
                                if (memberCustomerOutputForm.ReduceLevelValue != null) {
                                    if (customerGrowthTotal.compareTo(memberCustomerOutputForm.ReduceLevelValue) < 0) {
                                        memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                                    }
                                } else {
                                    memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                                }
                            } else {
                                memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                            }
                        } else {
                            memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                        }
                    }

                    CustomerLevelValidityEntity customerLevelValidityEntity = customerLevelMapper.getCustomerLevelValidity(memberCustomerOutputForm.ID);
                    if (customerLevelValidityEntity != null) {
                        customerLevelValidityEntity.CustomerID = memberCustomerOutputForm.ID;
                        customerLevelValidityEntity.ModifyOn = new Date();
                        customerLevelValidityEntity.ValidityOn = DateTimeUtil.addDay(new Date(), customerReduceConfigEntity.VaildModeDay);
                        customerLevelMapper.updateCustomerLevelValidity(customerLevelValidityEntity);
                    } else {
                        customerLevelValidityEntity = new CustomerLevelValidityEntity();
                        customerLevelValidityEntity.CustomerID = memberCustomerOutputForm.ID;
                        customerLevelValidityEntity.ModifyOn = new Date();
                        customerLevelValidityEntity.ValidityOn = DateTimeUtil.addDay(new Date(), customerReduceConfigEntity.VaildModeDay);
                        customerLevelMapper.insertCustomerLevelValidity(customerLevelValidityEntity);
                    }

                    if (memberCustomerOutputForm.IsLockMemberLevel == false) {
                        newList.add(memberCustomerOutputForm);
                    }
                }
            }

            Map<Integer, List<Integer>> collect = newList.stream().collect(Collectors.groupingBy(i -> i.CustomerLevelID, Collectors.mapping(i -> i.ID, Collectors.toList())));

            Set<Map.Entry<Integer, List<Integer>>> entries = collect.entrySet();
            for (Map.Entry<Integer, List<Integer>> entry : entries) {
                List<List<Integer>> subss = Lists.partition(entry.getValue(), 200);
                for (List<Integer> entityList : subss) {
                    customerMapper.updateCustomerByLevel(entry.getKey(), entityList);
                }
            }
        }
    }


    //定时模式
    public void updateCustomerLevelByTimingMode() {
        CustomerReduceConfigEntity customerReduceConfigEntity = customerReduceConfigMapper.getCustomerReduceConfigEntity();
        List<CustomerLevelOutputForm> customerLevelOutputForms = customerLevelMapper.allCustomerLevel();
        if (customerReduceConfigEntity.IsReduce == true && customerReduceConfigEntity.ReduceRule.equals("20") && customerLevelOutputForms.size() > 0) {
            List<MemberCustomerOutputForm> list = customerMapper.getMemberCustomer();

            //要更新的顾客
            List<MemberCustomerOutputForm> newList = new ArrayList<>();
            //有效期
            List<CustomerLevelValidityEntity> newCustomerValidDayList = new ArrayList<>();

            //成长值最低的等级
            Integer level = customerLevelOutputForms.get(0).ID;
            if (DateTimeUtil.toyyyyMMdd(customerReduceConfigEntity.RegularModeUpdateDate).equals(DateTimeUtil.toyyyyMMdd(new Date()))) {
                //下次更新时间
                if (customerReduceConfigEntity.RegularModeType.equals("10")) {
                    customerReduceConfigEntity.RegularModeUpdateDate = customerReduceConfigService.addMonth(1);
                } else if (customerReduceConfigEntity.RegularModeType.equals("20")) {
                    customerReduceConfigEntity.RegularModeUpdateDate = customerReduceConfigService.addMonth(3);
                } else if (customerReduceConfigEntity.RegularModeType.equals("30")) {
                    customerReduceConfigEntity.RegularModeUpdateDate = customerReduceConfigService.addMonth(6);
                } else if (customerReduceConfigEntity.RegularModeType.equals("40")) {
                    customerReduceConfigEntity.RegularModeUpdateDate = customerReduceConfigService.addMonth(12);
                }
                for (MemberCustomerOutputForm memberCustomerOutputForm : list) {
                    //获取近几个月的成长值
                    BigDecimal customerGrowthTotal = customerGrowthMapper.getCustomerLevelByValidity(memberCustomerOutputForm.ID, DateTimeUtil.toDate(DateTimeUtil.addMonth(new Date(), customerReduceConfigEntity.RegularModeMonth * -1)), DateTimeUtil.toyyyyMMdd2(DateTimeUtil.addDay(new Date(), 1)));
                    customerGrowthTotal = customerGrowthTotal == null ? BigDecimal.ZERO : customerGrowthTotal;
                    Integer customerLevelByGrowthTotal = customerGrowthMapper.getCustomerLevelByGrowthTotal(customerGrowthTotal);
                    customerLevelByGrowthTotal = customerLevelByGrowthTotal == null ? level : customerLevelByGrowthTotal;

                    if (customerReduceConfigEntity.ReduceMode.equals("10")) {
                        CustomerLevelEntity downOneLevelByID = customerLevelMapper.getDownOneLevelByID(memberCustomerOutputForm.CustomerLevelID);
                        if (memberCustomerOutputForm.LevelID != null) {
                            if (customerGrowthTotal.compareTo(memberCustomerOutputForm.LevelValue) < 0) {
                                if (memberCustomerOutputForm.ReduceLevelValue != null) {
                                    if (customerGrowthTotal.compareTo(memberCustomerOutputForm.ReduceLevelValue) < 0) {
                                        memberCustomerOutputForm.CustomerLevelID = downOneLevelByID == null ? level : downOneLevelByID.ID;
                                    }
                                } else {
                                    memberCustomerOutputForm.CustomerLevelID = downOneLevelByID == null ? level : downOneLevelByID.ID;
                                }
                            } else {
                                memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                            }
                        } else {
                            memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                        }
                    } else {
                        if (memberCustomerOutputForm.LevelID != null) {
                            if (customerGrowthTotal.compareTo(memberCustomerOutputForm.LevelValue) < 0) {
                                if (memberCustomerOutputForm.ReduceLevelValue != null) {
                                    if (customerGrowthTotal.compareTo(memberCustomerOutputForm.ReduceLevelValue) < 0) {
                                        memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                                    }
                                } else {
                                    memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                                }
                            } else {
                                memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                            }
                        } else {
                            memberCustomerOutputForm.CustomerLevelID = customerLevelByGrowthTotal;
                        }
                    }

                    CustomerLevelValidityEntity customerLevelValidityEntity = new CustomerLevelValidityEntity();
                    customerLevelValidityEntity.CustomerID = memberCustomerOutputForm.ID;
                    customerLevelValidityEntity.ModifyOn = new Date();
                    customerLevelValidityEntity.ValidityOn = DateTimeUtil.addDay(customerReduceConfigEntity.RegularModeUpdateDate, -1);
                    newCustomerValidDayList.add(customerLevelValidityEntity);


                    if (memberCustomerOutputForm.IsLockMemberLevel == false) {
                        newList.add(memberCustomerOutputForm);
                    }
                }

                customerLevelMapper.deleteAllCustomerLevelValidity();

                List<List<CustomerLevelValidityEntity>> subs = Lists.partition(newCustomerValidDayList, 200);
                for (List<CustomerLevelValidityEntity> entityList : subs) {
                    customerLevelMapper.insertCustomerValid(entityList);
                }
                Map<Integer, List<Integer>> collect = newList.stream().collect(Collectors.groupingBy(i -> i.CustomerLevelID, Collectors.mapping(i -> i.ID, Collectors.toList())));

                Set<Map.Entry<Integer, List<Integer>>> entries = collect.entrySet();
                for (Map.Entry<Integer, List<Integer>> entry : entries) {
                    List<List<Integer>> subss = Lists.partition(entry.getValue(), 200);
                    for (List<Integer> entityList : subss) {
                        customerMapper.updateCustomerByLevel(entry.getKey(), entityList);
                    }
                }


                customerReduceConfigMapper.updateCustomerReduceConfig(customerReduceConfigEntity);
            }
        }
    }
}