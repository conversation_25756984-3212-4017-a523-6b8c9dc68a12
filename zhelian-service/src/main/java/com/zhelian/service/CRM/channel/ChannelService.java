package com.zhelian.service.CRM.channel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.utils.DateTimeUtil;
import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.CRM.ChannelLevelMapper;
import com.zhelian.mapper.CRM.ChannelMapper;
import com.zhelian.mapper.CRM.ChannelTypeMapper;
import com.zhelian.mapper.CRM.CustomerMapper;
import com.zhelian.model.CRM.entity.*;
import com.zhelian.model.CRM.form.input.*;
import com.zhelian.model.CRM.form.output.ChannelInfoOutputForm;
import com.zhelian.model.CRM.form.output.ChannelOutputExcelForm;
import com.zhelian.model.CRM.form.output.ChannelOutputForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.form.input.ChannelBatchUpdateForm;
import com.zhelian.model.ibeauty.form.input.ChannelTransferConsultantForm;
import com.zhelian.model.ibeauty.form.input.ChannelTransferDeveloperForm;
import com.zhelian.model.ibeauty.form.input.CustomerQueryForm;
import com.zhelian.model.ibeauty.form.output.CustomerOutputExcelForm;
import com.zhelian.model.ibeauty.form.output.CustomerOutputForm;
import com.zhelian.model.miniprogram.form.input.ChannelDetailQueryForm;
import com.zhelian.model.miniprogram.form.input.MiniChannelQueryForm;
import com.zhelian.model.miniprogram.form.input.MiniCustomerQueryForm;
import com.zhelian.model.miniprogram.form.output.ChannelDataOutputForm;
import com.zhelian.model.miniprogram.form.output.EmployeeOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Service
public class ChannelService {

    @Autowired
    ChannelMapper channelMapper;
    @Autowired
    CustomerMapper customerMapper;

    @Autowired
    ChannelLevelMapper channelLevelMapper;
    @Autowired
    ChannelTypeMapper channelTypeMapper;

    @Transactional
    public BaseOutput create(ChannelAddForm form) throws Exception {
        ChannelLevelEntity channelLevel = channelLevelMapper.getChannelLevel(form.ChannelLevelID);
        if (channelLevel != null && channelLevel.Active == false) {
            return BaseOutput.failed("当前渠道等级为无效状态，请重新选择");
        }
        ChannelTypeEntity channelType = channelTypeMapper.getChannelType(form.ChannelTypeID);
        if (channelType.Active == false) {
            return BaseOutput.failed("当前渠道类型为无效状态，请重新选择");
        }
        Integer Sequence = channelMapper.channelSequenceByParentID(form.ParentID);
        ChannelEntity entity = new ChannelEntity();
        entity.Name = form.Name;
        entity.ChannelTypeID = form.ChannelTypeID;
        entity.ChannelLevelID = form.ChannelLevelID;
        entity.ApprovalStatus = "20";
        if (form.ParentID != null) {
            entity.ParentID = form.ParentID;
        } else {
            entity.ParentID = 0;
        }
        entity.ContactPersonName = form.ContactPersonName;
        entity.ContactPersonMobile = form.ContactPersonMobile;
        entity.ContactPersonIDNumber = form.ContactPersonIDNumber;
        entity.ProvinceCode = form.ProvinceCode;
        entity.CityCode = form.CityCode;
        entity.AreaCode = form.AreaCode;
        entity.AddressDetail = form.AddressDetail;
        entity.Active = form.Active;
        entity.Longitude = form.Longitude;
        entity.Latitude = form.Latitude;
        entity.Remark = form.Remark;
        entity.Sequence = Sequence != null ? Sequence + 1 : 1;
        entity.CreatedBy = SystemUserUtil.getSystemUserID();
        entity.CreatedOn = new Date();
        channelMapper.create(entity);

        List<IntroducerEntity> list = new ArrayList<>();
        List<ChannelCompanyEntity> company = new ArrayList<>();
        List<ChannelContractAttachmentEntity> contractAttachment = new ArrayList<>();
        List<ChannelDeveloperEntity> developer = new ArrayList<>();
        List<ChannelConsultantEntity> consultant = new ArrayList<>();

        for (IntroducerAddForm introducerAddForm : form.IntroducerList) {
            IntroducerEntity introducerEntity = new IntroducerEntity();
            introducerEntity.ChannelID = entity.ID;
            introducerEntity.IntroducerID = introducerAddForm.IntroducerID;
            list.add(introducerEntity);
        }

        if (form.Company != null) {
            for (ChannelCompanyAddForm channelCompanyAddForm : form.Company) {
                ChannelCompanyEntity channelCompanyEntity = new ChannelCompanyEntity();
                channelCompanyEntity.ChannelID = entity.ID;
                channelCompanyEntity.Name = channelCompanyAddForm.Name;
                channelCompanyEntity.LegalPerson = channelCompanyAddForm.LegalPerson;
                channelCompanyEntity.PhoneNumber = channelCompanyAddForm.PhoneNumber;
                channelCompanyEntity.BankBranchName = channelCompanyAddForm.BankBranchName;
                channelCompanyEntity.BankName = channelCompanyAddForm.BankName;
                channelCompanyEntity.BankAccount = channelCompanyAddForm.BankAccount;
                channelCompanyEntity.Amount = channelCompanyAddForm.Amount;
                channelCompanyEntity.Remark = channelCompanyAddForm.Remark;
                company.add(channelCompanyEntity);
            }
        }

        if (form.Contract != null) {
            for (ChannelContractAddForm channelContractAddForm : form.Contract) {
                ChannelContractEntity channelContractEntity = new ChannelContractEntity();
                channelContractEntity.ChannelID = entity.ID;
                channelContractEntity.Code = channelContractAddForm.Code;
                channelContractEntity.BeginDate = DateTimeUtil.toDate(channelContractAddForm.BeginDate);
                channelContractEntity.EndDate = DateTimeUtil.toDate(channelContractAddForm.EndDate);
                channelMapper.createChannelContract(channelContractEntity);

                if (channelContractAddForm.Attachment != null) {
                    for (ChannelContractAttachmentAddForm channelContractAttachmentAddForm : channelContractAddForm.Attachment) {
                        ChannelContractAttachmentEntity channelContractAttachmentEntity = new ChannelContractAttachmentEntity();
                        channelContractAttachmentEntity.ChannelContractID = channelContractEntity.ID;
                        channelContractAttachmentEntity.AttachmentType = channelContractAttachmentAddForm.AttachmentType;
                        channelContractAttachmentEntity.AttachmentURL = channelContractAttachmentAddForm.AttachmentURL;
                        contractAttachment.add(channelContractAttachmentEntity);
                    }
                }
            }
        }

        if (form.DeveloperList != null) {
            for (ChannelDeveloperAddForm channelDeveloperAddForm : form.DeveloperList) {
                ChannelDeveloperEntity channelDeveloperEntity = new ChannelDeveloperEntity();
                channelDeveloperEntity.ChannelID = entity.ID;
                channelDeveloperEntity.EmployeeID = channelDeveloperAddForm.EmployeeID;
                channelDeveloperEntity.PerformanceRate = channelDeveloperAddForm.PerformanceRate;
                developer.add(channelDeveloperEntity);
            }
        }
        if (form.ConsultantList != null) {
            for (ChannelConsultantAddForm channelConsultantAddForm : form.ConsultantList) {
                ChannelConsultantEntity channelConsultantEntity = new ChannelConsultantEntity();
                channelConsultantEntity.ChannelID = entity.ID;
                channelConsultantEntity.EmployeeID = channelConsultantAddForm.EmployeeID;
                channelConsultantEntity.PerformanceRate = channelConsultantAddForm.PerformanceRate;
                consultant.add(channelConsultantEntity);
            }
        }

        if (list.size() > 0) channelMapper.insertIntroducer(list);

        if (company.size() > 0) channelMapper.insertChannelCompany(company);

        if (contractAttachment.size() > 0) channelMapper.insertChannelContractAttachment(contractAttachment);

        if (developer.size() > 0) channelMapper.insertChannelDeveloper(developer);

        if (consultant.size() > 0) channelMapper.insertChannelConsultant(consultant);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateChannel(ChannelUpdateForm form) throws Exception {
        ChannelLevelEntity channelLevel = channelLevelMapper.getChannelLevel(form.ChannelLevelID);
        if (channelLevel != null && channelLevel.Active == false) {
            return BaseOutput.failed("当前渠道等级为无效状态，请重新选择");
        }
        ChannelTypeEntity channelType = channelTypeMapper.getChannelType(form.ChannelTypeID);
        if (channelType.Active == false) {
            return BaseOutput.failed("当前渠道类型为无效状态，请重新选择");
        }

        if (form.ID.equals(form.ParentID)) {
            return BaseOutput.failed("上级渠道不能与当前渠道相同，请重新选择");
        }

        if (form.Active == false) {
            List<ChannelEntity> list = channelMapper.channerlListByParentID(form.ID);
            if (list.size() > 0) return BaseOutput.failed("下级包含有效渠道");
        }

        ChannelEntity entity = new ChannelEntity();
        entity.ID = form.ID;
        entity.Name = form.Name;
        entity.ChannelTypeID = form.ChannelTypeID;
        entity.ChannelLevelID = form.ChannelLevelID;
        entity.ApprovalStatus = form.ApprovalStatus;
        entity.Latitude = form.Latitude;
        entity.Longitude = form.Longitude;
        if (form.ParentID != null) {
            entity.ParentID = form.ParentID;
        } else {
            entity.ParentID = 0;
        }
        entity.ContactPersonName = form.ContactPersonName;
        entity.ContactPersonMobile = form.ContactPersonMobile;
        entity.ContactPersonIDNumber = form.ContactPersonIDNumber;
        entity.ProvinceCode = form.ProvinceCode;
        entity.CityCode = form.CityCode;
        entity.AreaCode = form.AreaCode;
        entity.AddressDetail = form.AddressDetail;
        entity.Active = form.Active;
        entity.Remark = form.Remark;
        channelMapper.updateChannel(entity);
        channelMapper.deleteIntroducerByID(entity.ID);
        channelMapper.deleteCompanyByID(entity.ID);
        channelMapper.deleteContractAttachmentByID(entity.ID);
        channelMapper.deleteContractByID(entity.ID);
        channelMapper.deleteDeveloperByID(entity.ID);
        channelMapper.deleteConsultantByID(entity.ID);

        List<IntroducerEntity> list = new ArrayList<>();
        List<ChannelCompanyEntity> company = new ArrayList<>();
        List<ChannelContractAttachmentEntity> contractAttachment = new ArrayList<>();
        List<ChannelDeveloperEntity> developer = new ArrayList<>();
        List<ChannelConsultantEntity> consultant = new ArrayList<>();


        for (IntroducerAddForm introducerAddForm : form.IntroducerList) {
            IntroducerEntity introducerEntity = new IntroducerEntity();
            introducerEntity.ChannelID = entity.ID;
            introducerEntity.IntroducerID = introducerAddForm.IntroducerID;
            list.add(introducerEntity);
        }

        if (form.Company != null) {
            for (ChannelCompanyAddForm channelCompanyAddForm : form.Company) {
                ChannelCompanyEntity channelCompanyEntity = new ChannelCompanyEntity();
                channelCompanyEntity.ChannelID = entity.ID;
                channelCompanyEntity.Name = channelCompanyAddForm.Name;
                channelCompanyEntity.LegalPerson = channelCompanyAddForm.LegalPerson;
                channelCompanyEntity.PhoneNumber = channelCompanyAddForm.PhoneNumber;
                channelCompanyEntity.BankBranchName = channelCompanyAddForm.BankBranchName;
                channelCompanyEntity.BankName = channelCompanyAddForm.BankName;
                channelCompanyEntity.BankAccount = channelCompanyAddForm.BankAccount;
                channelCompanyEntity.Amount = channelCompanyAddForm.Amount;
                channelCompanyEntity.Remark = channelCompanyAddForm.Remark;
                company.add(channelCompanyEntity);
            }
        }

        if (form.Contract != null) {
            for (ChannelContractAddForm channelContractAddForm : form.Contract) {
                ChannelContractEntity channelContractEntity = new ChannelContractEntity();
                channelContractEntity.ChannelID = entity.ID;
                channelContractEntity.Code = channelContractAddForm.Code;
                channelContractEntity.BeginDate = DateTimeUtil.toDate(channelContractAddForm.BeginDate);
                channelContractEntity.EndDate = DateTimeUtil.toDate(channelContractAddForm.EndDate);
                channelMapper.createChannelContract(channelContractEntity);

                if (channelContractAddForm.Attachment != null) {
                    for (ChannelContractAttachmentAddForm channelContractAttachmentAddForm : channelContractAddForm.Attachment) {
                        ChannelContractAttachmentEntity channelContractAttachmentEntity = new ChannelContractAttachmentEntity();
                        channelContractAttachmentEntity.ChannelContractID = channelContractEntity.ID;
                        channelContractAttachmentEntity.AttachmentType = channelContractAttachmentAddForm.AttachmentType;
                        channelContractAttachmentEntity.AttachmentURL = channelContractAttachmentAddForm.AttachmentURL;
                        contractAttachment.add(channelContractAttachmentEntity);
                    }
                }
            }
        }

        if (form.DeveloperList != null) {
            for (ChannelDeveloperAddForm channelDeveloperAddForm : form.DeveloperList) {
                ChannelDeveloperEntity channelDeveloperEntity = new ChannelDeveloperEntity();
                channelDeveloperEntity.ChannelID = entity.ID;
                channelDeveloperEntity.EmployeeID = channelDeveloperAddForm.EmployeeID;
                channelDeveloperEntity.PerformanceRate = channelDeveloperAddForm.PerformanceRate;
                developer.add(channelDeveloperEntity);
            }
        }
        if (form.ConsultantList != null) {
            for (ChannelConsultantAddForm channelConsultantAddForm : form.ConsultantList) {
                ChannelConsultantEntity channelConsultantEntity = new ChannelConsultantEntity();
                channelConsultantEntity.ChannelID = entity.ID;
                channelConsultantEntity.EmployeeID = channelConsultantAddForm.EmployeeID;
                channelConsultantEntity.PerformanceRate = channelConsultantAddForm.PerformanceRate;
                consultant.add(channelConsultantEntity);
            }
        }

        if (list.size() > 0) channelMapper.insertIntroducer(list);

        if (company.size() > 0) channelMapper.insertChannelCompany(company);

        if (contractAttachment.size() > 0) channelMapper.insertChannelContractAttachment(contractAttachment);

        if (developer.size() > 0) channelMapper.insertChannelDeveloper(developer);

        if (consultant.size() > 0) channelMapper.insertChannelConsultant(consultant);

        return BaseOutput.success();
    }

    public BaseOutput list(ChannelQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(channelMapper.getPageList(form));
    }

    public BaseOutput excel(ChannelQueryForm form, HttpServletResponse response) {
        List<ChannelOutputExcelForm> list = channelMapper.excel(form);
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("渠道信息", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), ChannelOutputExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("渠道信息").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    @Transactional
    public BaseOutput move(ChannelMoveForm form) {

        ChannelEntity entity = channelMapper.channelInfo(form.MoveChannelID);
        if (entity == null) return BaseOutput.failed("渠道不存在");

        entity.ParentID = form.DestParentID;

        //重新排序，正序查出列表
        List<ChannelEntity> list = channelMapper.channelListByParentID(form.DestParentID, form.MoveChannelID, null);

        if (list == null) {
            list = new ArrayList<>();
            list.add(entity);
        } else {
            //前面有单位的话，顶替前面单位，没有单位的话则插入最后一个，最终根据下标重新排序
            if (form.BeforeChannelID != null) {
                ChannelEntity beforeEntityEntity = new ChannelEntity();

                for (ChannelEntity entity1 : list) {
                    if (entity1.ID.equals(form.BeforeChannelID)) {
                        beforeEntityEntity = entity1;
                    }
                }
                if (beforeEntityEntity == null) return BaseOutput.failed("前面单位不存在");

                list.add(list.indexOf(beforeEntityEntity), entity);

            } else {
                //插入最后一位
                list.add(entity);
            }
        }

        for (ChannelEntity entity1 : list) {
            entity1.Sequence = list.indexOf(entity1) + 1;
        }

        channelMapper.updateChannelSequence(list);

        return BaseOutput.success();
    }

    public BaseOutput channelList(ChannelQueryForm form) {
        List<ChannelEntity> list = channelMapper.channelList(form);
        JSONArray result = ListUtil.listToTree(JSONArray.parseArray(JSON.toJSONString(list, JSONWriter.Feature.WriteMapNullValue)), "ID", "ParentID", "Child");
        return BaseOutputForm.success(result);
    }


    public BaseOutput updateChannelparent(ChannelUpdateForm form) {
        if (form.ChannelList.size() > 0) {
            channelMapper.updateChannelParent(form.ChannelList);
        }
        if (form.EmployeeList.size() > 0) {
            channelMapper.updateChannelEmployee(form.EmployeeList);
        }
        return BaseOutput.success();
    }

    public BaseOutput empList() {
        List<EmployeeOutputForm> list = channelMapper.empList();
        return BaseOutputForm.success(list);
    }

    public BaseOutput all(ChannelQueryForm form) {
        List<ChannelInfoOutputForm> list = channelMapper.all(form.Name,form.Active);
        return BaseOutputForm.success(list);
    }

    public BaseOutput customerInfo(ChannelQueryForm form) {
        List<ChannelInfoOutputForm> list = channelMapper.customerInfo(form.Name,form.CustomerID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput miniSelfList(MiniChannelQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<ChannelDataOutputForm> list = channelMapper.miniSelfList(SystemUserUtil.getSystemUserID(), form.Name, form.ApprovalStatus, form.ChannelTypeID, form.ChannelLevelID, form.ParentID, form.DeveloperID, form.ConsultantID);
        return BasePageInfo.success(list);
    }

    public BaseOutput miniAllList(MiniChannelQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<ChannelDataOutputForm> list = channelMapper.miniAllList(SystemUserUtil.getSystemUserID(), form.Name, form.ApprovalStatus, form.EmployeeID, form.EntityID, form.IsShow == true ? true : null, form.ChannelTypeID, form.ChannelLevelID, form.ParentID, form.DeveloperID, form.ConsultantID);
        return BasePageInfo.success(list);
    }

    public BaseOutput approvalList(ChannelApprovalQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(channelMapper.approvalList(form.ChannelTypeID, form.ChannelLevelID, form.EmployeeID, form.ParentID, form.Name, form.ApprovalStatus, form.DeveloperID, form.ConsultantID));
    }

    public BaseOutput approvalDetail(ChannelDetailQueryForm form) {
        return BaseOutputForm.success(channelMapper.approvalDetail(form.ID));
    }

    @Transactional
    public BaseOutput approval(ChannelApprovalAddForm form) {

        ChannelEntity channelEntity = channelMapper.getChannelEntity(form.ID);
        channelEntity.ApprovalStatus = form.ApprovalStatus;
        channelMapper.updateChannel(channelEntity);


        ChannelApprovalProcessEntity channelApprovalProcessEntity = new ChannelApprovalProcessEntity();
        channelApprovalProcessEntity.ChannelID = form.ID;
        channelApprovalProcessEntity.ApprovalBy = SystemUserUtil.getSystemUserID();
        channelApprovalProcessEntity.ApprovalOn = new Date();
        channelApprovalProcessEntity.ActionType = form.ApprovalStatus;
        channelApprovalProcessEntity.ApprovalRemark = form.Remark;
        channelMapper.createChannelApprovalProcess(channelApprovalProcessEntity);
        return BaseOutput.success();
    }

    public BaseOutput detail(ChannelDetailQueryForm form) {
        return BaseOutputForm.success(channelMapper.detail(form.ID));
    }

    @Transactional
    public BaseOutput createMiniChannel(ChannelAddForm form) throws Exception {
        ChannelLevelEntity channelLevel = channelLevelMapper.getChannelLevel(form.ChannelLevelID);
        if (channelLevel != null && channelLevel.Active == false) {
            return BaseOutput.failed("当前渠道等级为无效状态，请重新选择");
        }
        ChannelTypeEntity channelType = channelTypeMapper.getChannelType(form.ChannelTypeID);
        if (channelType.Active == false) {
            return BaseOutput.failed("当前渠道类型为无效状态，请重新选择");
        }
        Integer Sequence = channelMapper.channelSequenceByParentID(form.ParentID);
        ChannelEntity entity = new ChannelEntity();
        entity.Name = form.Name;
        entity.ChannelTypeID = form.ChannelTypeID;
        entity.ChannelLevelID = form.ChannelLevelID;
        entity.ApprovalStatus = "10";
        entity.CreatedBy = SystemUserUtil.getSystemUserID();
        if (form.ParentID != null) {
            entity.ParentID = form.ParentID;
        } else {
            entity.ParentID = 0;
        }
        entity.ContactPersonName = form.ContactPersonName;
        entity.ContactPersonMobile = form.ContactPersonMobile;
        entity.ContactPersonIDNumber = form.ContactPersonIDNumber;
        entity.ProvinceCode = form.ProvinceCode;
        entity.CityCode = form.CityCode;
        entity.AreaCode = form.AreaCode;
        entity.AddressDetail = form.AddressDetail;
        entity.Active = form.Active;
        entity.Longitude = form.Longitude;
        entity.Latitude = form.Latitude;
        entity.Remark = form.Remark;
        entity.Sequence = Sequence != null ? Sequence + 1 : 1;
        entity.CreatedOn = new Date();
        channelMapper.create(entity);

        List<IntroducerEntity> list = new ArrayList<>();
        List<ChannelCompanyEntity> company = new ArrayList<>();
        List<ChannelContractAttachmentEntity> contractAttachment = new ArrayList<>();
        List<ChannelDeveloperEntity> developer = new ArrayList<>();
        List<ChannelConsultantEntity> consultant = new ArrayList<>();

        for (IntroducerAddForm introducerAddForm : form.IntroducerList) {
            IntroducerEntity introducerEntity = new IntroducerEntity();
            introducerEntity.ChannelID = entity.ID;
            introducerEntity.IntroducerID = introducerAddForm.IntroducerID;
            list.add(introducerEntity);
        }

        if (form.Company != null) {
            for (ChannelCompanyAddForm channelCompanyAddForm : form.Company) {
                ChannelCompanyEntity channelCompanyEntity = new ChannelCompanyEntity();
                channelCompanyEntity.ChannelID = entity.ID;
                channelCompanyEntity.Name = channelCompanyAddForm.Name;
                channelCompanyEntity.LegalPerson = channelCompanyAddForm.LegalPerson;
                channelCompanyEntity.PhoneNumber = channelCompanyAddForm.PhoneNumber;
                channelCompanyEntity.BankBranchName = channelCompanyAddForm.BankBranchName;
                channelCompanyEntity.BankName = channelCompanyAddForm.BankName;
                channelCompanyEntity.BankAccount = channelCompanyAddForm.BankAccount;
                channelCompanyEntity.Amount = channelCompanyAddForm.Amount;
                channelCompanyEntity.Remark = channelCompanyAddForm.Remark;
                company.add(channelCompanyEntity);
            }
        }

        if (form.Contract != null) {
            for (ChannelContractAddForm channelContractAddForm : form.Contract) {
                ChannelContractEntity channelContractEntity = new ChannelContractEntity();
                channelContractEntity.ChannelID = entity.ID;
                channelContractEntity.Code = channelContractAddForm.Code;
                channelContractEntity.BeginDate = DateTimeUtil.toDate(channelContractAddForm.BeginDate);
                channelContractEntity.EndDate = DateTimeUtil.toDate(channelContractAddForm.EndDate);
                channelMapper.createChannelContract(channelContractEntity);

                if (channelContractAddForm.Attachment != null) {
                    for (ChannelContractAttachmentAddForm channelContractAttachmentAddForm : channelContractAddForm.Attachment) {
                        ChannelContractAttachmentEntity channelContractAttachmentEntity = new ChannelContractAttachmentEntity();
                        channelContractAttachmentEntity.ChannelContractID = channelContractEntity.ID;
                        channelContractAttachmentEntity.AttachmentType = channelContractAttachmentAddForm.AttachmentType;
                        channelContractAttachmentEntity.AttachmentURL = channelContractAttachmentAddForm.AttachmentURL;
                        contractAttachment.add(channelContractAttachmentEntity);
                    }
                }
            }
        }

        if (form.DeveloperList != null) {
            for (ChannelDeveloperAddForm channelDeveloperAddForm : form.DeveloperList) {
                ChannelDeveloperEntity channelDeveloperEntity = new ChannelDeveloperEntity();
                channelDeveloperEntity.ChannelID = entity.ID;
                channelDeveloperEntity.EmployeeID = channelDeveloperAddForm.EmployeeID;
                channelDeveloperEntity.PerformanceRate = channelDeveloperAddForm.PerformanceRate;
                developer.add(channelDeveloperEntity);
            }
        }
        if (form.ConsultantList != null) {
            for (ChannelConsultantAddForm channelConsultantAddForm : form.ConsultantList) {
                ChannelConsultantEntity channelConsultantEntity = new ChannelConsultantEntity();
                channelConsultantEntity.ChannelID = entity.ID;
                channelConsultantEntity.EmployeeID = channelConsultantAddForm.EmployeeID;
                channelConsultantEntity.PerformanceRate = channelConsultantAddForm.PerformanceRate;
                consultant.add(channelConsultantEntity);
            }
        }


        if (list.size() > 0) channelMapper.insertIntroducer(list);

        if (company.size() > 0) channelMapper.insertChannelCompany(company);

        if (contractAttachment.size() > 0) channelMapper.insertChannelContractAttachment(contractAttachment);

        if (developer.size() > 0) channelMapper.insertChannelDeveloper(developer);

        if (consultant.size() > 0) channelMapper.insertChannelConsultant(consultant);

        ChannelApprovalProcessEntity channelApprovalProcessEntity = new ChannelApprovalProcessEntity();
        channelApprovalProcessEntity.ChannelID = entity.ID;
        channelApprovalProcessEntity.ApprovalBy = SystemUserUtil.getSystemUserID();
        channelApprovalProcessEntity.ApprovalOn = new Date();
        channelApprovalProcessEntity.ActionType = "10";
        channelMapper.createChannelApprovalProcess(channelApprovalProcessEntity);

        return BaseOutput.success();
    }

    public BaseOutput data(ChannelQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<ChannelInfoOutputForm> list = channelMapper.all(form.Name,form.Active);
        return BasePageInfo.success(list);
    }

    public BaseOutput customer(MiniCustomerQueryForm form) {
        String lunarStartDate = customerMapper.getLunarTime(form.StartDate);
        String lunarEndDate = customerMapper.getLunarTime(form.EndDate);
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CustomerOutputForm> list = customerMapper.channelCustomerList(SystemUserUtil.getSystemUserID(), form.Name, form.CustomerLevelID, form.CustomerSourceID, form.StartDate, form.EndDate, lunarStartDate, lunarEndDate, form.ServicerID, form.ServicerEmployeeID, form.ToShopStartDate, form.ToShopEndDate, form.StartTime, form.EndTime, form.ChannelID);
        return BasePageInfo.success(list);
    }

    public BaseOutput infoPage(ChannelQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<ChannelInfoOutputForm> list = channelMapper.all(form.Name,form.Active);
        return BasePageInfo.success(list);
    }

    public BaseOutput currentChannel(ChannelQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<ChannelInfoOutputForm> list = channelMapper.currentChannel(SystemUserUtil.getSystemUserID(), form.Name, false,form.Active);
        return BasePageInfo.success(list);
    }

    public BaseOutput visitChannel(ChannelQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<ChannelInfoOutputForm> list = channelMapper.currentChannel(SystemUserUtil.getSystemUserID(), form.Name, true,form.Active);
        return BasePageInfo.success(list);
    }

    @Transactional
    public BaseOutput batchUpdateConsultant(ChannelBatchUpdateForm form) {
        channelMapper.deleteBatchConsultant(form.ChannelID);
        List<ChannelConsultantEntity> list = new ArrayList<>();
        for (Integer integer : form.ChannelID) {
            for (ChannelDeveloperAddForm channelDeveloperAddForm : form.Employee) {
                ChannelConsultantEntity channelConsultantEntity = new ChannelConsultantEntity();
                channelConsultantEntity.ChannelID = integer;
                channelConsultantEntity.EmployeeID = channelDeveloperAddForm.EmployeeID;
                channelConsultantEntity.PerformanceRate = channelDeveloperAddForm.PerformanceRate;
                list.add(channelConsultantEntity);
            }
        }

        if (list.size() > 0)
            channelMapper.insertChannelConsultant(list);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput batchUpdateDeveloper(ChannelBatchUpdateForm form) {
        channelMapper.deleteBatchDeveloper(form.ChannelID);
        List<ChannelDeveloperEntity> list = new ArrayList<>();
        for (Integer integer : form.ChannelID) {
            for (ChannelDeveloperAddForm channelDeveloperAddForm : form.Employee) {
                ChannelDeveloperEntity channelDeveloperEntity = new ChannelDeveloperEntity();
                channelDeveloperEntity.ChannelID = integer;
                channelDeveloperEntity.EmployeeID = channelDeveloperAddForm.EmployeeID;
                channelDeveloperEntity.PerformanceRate = channelDeveloperAddForm.PerformanceRate;
                list.add(channelDeveloperEntity);
            }
        }

        if (list.size() > 0)
            channelMapper.insertChannelDeveloper(list);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput transferDeveloper(ChannelTransferDeveloperForm form) {

        List<ChannelDeveloperEntity> inDeveloper = new ArrayList<>();

        List<ChannelDeveloperEntity> outChannelDeveloperEntityList = channelMapper.getChannelDeveloperByEmployeeID(form.OutEmployeeID);
        if (outChannelDeveloperEntityList != null) {
            for (ChannelDeveloperEntity channelDeveloperEntity : outChannelDeveloperEntityList) {
                ChannelDeveloperEntity inChannelDeveloperEntity = new ChannelDeveloperEntity();
                inChannelDeveloperEntity.ChannelID = channelDeveloperEntity.ChannelID;
                inChannelDeveloperEntity.EmployeeID = form.InEmployeeID;
                inChannelDeveloperEntity.PerformanceRate = channelDeveloperEntity.PerformanceRate;
                inDeveloper.add(inChannelDeveloperEntity);
                channelMapper.deleteChannelDeveloperByEmployeeIDAndChannelID(form.InEmployeeID,channelDeveloperEntity.ChannelID);
            }
        }

        channelMapper.deleteChannelDeveloperByEmployeeID(form.OutEmployeeID);

        if (inDeveloper.size() > 0) channelMapper.insertTransferChannelDeveloper(inDeveloper);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput transferConsultant(ChannelTransferConsultantForm form) {

        List<ChannelConsultantEntity> inConsultant = new ArrayList<>();

        List<ChannelConsultantEntity> outChannelConsultantEntityList = channelMapper.getChannelConsultantByEmployeeID(form.OutEmployeeID);
        if (outChannelConsultantEntityList != null) {
            for (ChannelConsultantEntity channelConsultantEntity : outChannelConsultantEntityList) {
                ChannelConsultantEntity inChannelConsultantEntity = new ChannelConsultantEntity();
                inChannelConsultantEntity.ChannelID = channelConsultantEntity.ChannelID;
                inChannelConsultantEntity.EmployeeID = form.InEmployeeID;
                inChannelConsultantEntity.PerformanceRate = channelConsultantEntity.PerformanceRate;
                inConsultant.add(inChannelConsultantEntity);
                channelMapper.deleteChannelConsultantByEmployeeIDAndChannelID(form.InEmployeeID,channelConsultantEntity.ChannelID);
            }
        }

        channelMapper.deleteChannelConsultantByEmployeeID(form.OutEmployeeID);

        if (inConsultant.size() > 0) channelMapper.insertTransferChannelConsultant(inConsultant);

        return BaseOutput.success();
    }
}
