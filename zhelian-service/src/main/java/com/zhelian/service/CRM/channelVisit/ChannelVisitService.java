package com.zhelian.service.CRM.channelVisit;

import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.autoNumber.AutoNumberUtils;
import com.zhelian.core.utils.DateTimeUtil;
import com.zhelian.core.utils.enumeration.BillChannel;
import com.zhelian.mapper.CRM.ChannelVisitMapper;
import com.zhelian.mapper.ibeauty.AppointmentBillMapper;
import com.zhelian.mapper.ibeauty.AppointmentMapper;
import com.zhelian.model.CRM.entity.*;
import com.zhelian.model.CRM.form.output.ChannelVisitChannelOutputForm;
import com.zhelian.model.CRM.form.output.ChannelVisitDetailOutputForm;
import com.zhelian.model.CRM.form.output.ChannelVisitOutputForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.AppointmentBillEntity;
import com.zhelian.model.ibeauty.entity.AppointmentBillProcessEntity;
import com.zhelian.model.miniprogram.form.input.*;
import com.zhelian.model.miniprogram.form.output.ChannelVisitAppointmentOutputForm;
import com.zhelian.model.miniprogram.form.output.ChannelVisitReportOutForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Service
public class ChannelVisitService {

    @Autowired
    ChannelVisitMapper channelVisitMapper;
    @Autowired
    AppointmentMapper appointmentMapper;
    @Autowired
    AppointmentBillMapper appointmentBillMapper;


    public BaseOutput list(ChannelVisitQueryForm form) {

        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<ChannelVisitOutputForm> list = channelVisitMapper.channelVisitList(SystemUserUtil.getSystemUserID(), form.Name, form.VisitType, form.Assistor, form.VisitStatus, form.StartDate, form.EndDate);
        return BasePageInfo.success(list);
    }

    public BaseOutput subordinatesList(ChannelVisitQueryForm form) {

        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<ChannelVisitOutputForm> list = channelVisitMapper.subordinatesList(SystemUserUtil.getSystemUserID(), form.Name, form.VisitType, form.Assistor, form.VisitStatus, form.StartDate, form.EndDate);
        return BasePageInfo.success(list);
    }


    @Transactional
    public BaseOutput create(ChannelVisitAddForm form) throws Exception {
        for (Integer integer : form.Channel) {
            ChannelVisitEntity channelVisitEntity = new ChannelVisitEntity();
            channelVisitEntity.ChannelID = integer;
            channelVisitEntity.VisitType = form.VisitType;
            channelVisitEntity.VisitStatus = "10";
            channelVisitEntity.VisitBy = form.VisitBy;
            channelVisitEntity.IsPlan = true;
            channelVisitEntity.PlannedOn = DateTimeUtil.toDate(form.PlannedOn);
            channelVisitEntity.PlannedRemark = form.PlannedRemark;
            channelVisitEntity.SignInRemark = "";
            channelVisitEntity.VisitRecord = "";
            channelVisitEntity.SignOutRemark = "";
            channelVisitMapper.createChannelVisit(channelVisitEntity);

            List<ChannelVisitAssistorEntity> list = new ArrayList<>();
            if (form.Assistor != null && form.Assistor.length > 0) {
                for (String s : form.Assistor) {
                    ChannelVisitAssistorEntity channelVisitAssistorEntity = new ChannelVisitAssistorEntity();
                    channelVisitAssistorEntity.ChannelVisitID = channelVisitEntity.ID;
                    channelVisitAssistorEntity.AssistBy = s;
                    list.add(channelVisitAssistorEntity);
                }
            }
            if (list.size() > 0) channelVisitMapper.createChannelVisitAssistor(list);
        }


        return BaseOutput.success();
    }


    @Transactional
    public BaseOutput createTemporary(ChannelVisitTemporaryAddForm form) throws Exception {
        ChannelVisitEntity channelVisitEntity = new ChannelVisitEntity();
        channelVisitEntity.ChannelID = form.ChannelID;
        channelVisitEntity.VisitType = "20";
        channelVisitEntity.VisitStatus = "30";
        channelVisitEntity.VisitBy = SystemUserUtil.getSystemUserID();
        channelVisitEntity.PlannedOn = DateTimeUtil.toDate(form.PlannedOn);
        channelVisitEntity.PlannedRemark = "";
        channelVisitEntity.VisitRecord = form.Remark;
        channelVisitEntity.SignInRemark = "";
        channelVisitEntity.SignOutRemark = "";
        channelVisitEntity.IsPlan = false;
        channelVisitMapper.createChannelVisit(channelVisitEntity);

        List<ChannelVisitAssistorEntity> list = new ArrayList<>();
        List<ChannelVisitRecordAttachmentEntity> attachment = new ArrayList<>();
        if (form.Assistor != null && form.Assistor.length > 0) {
            for (String s : form.Assistor) {
                ChannelVisitAssistorEntity channelVisitAssistorEntity = new ChannelVisitAssistorEntity();
                channelVisitAssistorEntity.ChannelVisitID = channelVisitEntity.ID;
                channelVisitAssistorEntity.AssistBy = s;
                list.add(channelVisitAssistorEntity);
            }
        }

        if (form.Attachment != null) {
            for (ChannelVisitAttachmentAddForm channelVisitAttachmentAddForm : form.Attachment) {
                ChannelVisitRecordAttachmentEntity channelVisitRecordAttachmentEntity = new ChannelVisitRecordAttachmentEntity();
                channelVisitRecordAttachmentEntity.ChannelVisitID = channelVisitEntity.ID;
                channelVisitRecordAttachmentEntity.AttachmentType = channelVisitAttachmentAddForm.AttachmentType;
                channelVisitRecordAttachmentEntity.AttachmentURL = channelVisitAttachmentAddForm.AttachmentURL;
                attachment.add(channelVisitRecordAttachmentEntity);
            }
        }

        if (list.size() > 0) channelVisitMapper.createChannelVisitAssistor(list);

        if (attachment.size() > 0) channelVisitMapper.createChannelVisitRecordAttachment(attachment);

        return BaseOutput.success();

    }

    @Transactional
    public BaseOutput createTemporaryOffline(ChannelVisitTemporaryOfflineAddForm form) throws Exception {
        ChannelVisitEntity channelVisitEntity = new ChannelVisitEntity();
        channelVisitEntity.ChannelID = form.ChannelID;
        channelVisitEntity.VisitType = "10";
        channelVisitEntity.VisitStatus = "20";
        channelVisitEntity.VisitBy = SystemUserUtil.getSystemUserID();
        channelVisitEntity.PlannedOn = DateTimeUtil.toDate(form.PlannedOn);
        channelVisitEntity.PlannedRemark = "";
        channelVisitEntity.VisitRecord = "";
        channelVisitEntity.SignInRemark = form.SignRemark;
        channelVisitEntity.SignOutRemark = "";
        channelVisitEntity.SignInOn = DateTimeUtil.toDate(form.SignOn);
        channelVisitEntity.SignInAddress = form.SignAddress;
        channelVisitEntity.SignInLongitude = form.SignLongitude;
        channelVisitEntity.SignInLatitude = form.SignLatitude;
        channelVisitEntity.IsPlan = false;
        channelVisitMapper.createChannelVisit(channelVisitEntity);

        List<ChannelVisitAssistorEntity> list = new ArrayList<>();
        List<ChannelVisitSignInAttachmentEntity> attachment = new ArrayList<>();
        if (form.Assistor != null && form.Assistor.length > 0) {
            for (String s : form.Assistor) {
                ChannelVisitAssistorEntity channelVisitAssistorEntity = new ChannelVisitAssistorEntity();
                channelVisitAssistorEntity.ChannelVisitID = channelVisitEntity.ID;
                channelVisitAssistorEntity.AssistBy = s;
                list.add(channelVisitAssistorEntity);
            }
        }

        if (form.Attachment != null) {
            for (ChannelVisitAttachmentAddForm channelVisitAttachmentAddForm : form.Attachment) {
                ChannelVisitSignInAttachmentEntity attachmentEntity = new ChannelVisitSignInAttachmentEntity();
                attachmentEntity.ChannelVisitID = channelVisitEntity.ID;
                attachmentEntity.AttachmentType = channelVisitAttachmentAddForm.AttachmentType;
                attachmentEntity.AttachmentURL = channelVisitAttachmentAddForm.AttachmentURL;
                attachment.add(attachmentEntity);
            }
        }

        if (list.size() > 0) channelVisitMapper.createChannelVisitAssistor(list);

        if (attachment.size() > 0) channelVisitMapper.createChannelVisitSignInAttachment(attachment);

        return BaseOutput.success();
    }

    public BaseOutput cancel(ChannelVisitCnannelAddForm form) {
        ChannelVisitEntity channelVisit = channelVisitMapper.getChannelVisit(form.ID);
        channelVisit.VisitStatus = "40";
        channelVisitMapper.updateChannelVisit(channelVisit);
        return BaseOutput.success();

    }

    public BaseOutput channel(ChannelVisitQueryForm form) {
        List<ChannelVisitChannelOutputForm> list = channelVisitMapper.getChannelVisitChannel(form.Name);
        return BaseOutputForm.success(list);

    }

    public BaseOutput detail(ChannelVisitCnannelAddForm form) {
        ChannelVisitDetailOutputForm channelVisitDetailOutputForm = channelVisitMapper.channelVisitDetail(form.ID);
        channelVisitDetailOutputForm.RecentVisitDate = channelVisitMapper.getRecentVisitDate(channelVisitDetailOutputForm.ChannelID);

        return BaseOutputForm.success(channelVisitDetailOutputForm);
    }

    @Transactional
    public BaseOutput SignIn(ChannelVisitSignAddForm form) throws Exception {
        ChannelVisitEntity channelVisit = channelVisitMapper.getChannelVisit(form.ID);
        channelVisit.SignInOn = DateTimeUtil.toDate(form.SignOn);
        channelVisit.VisitStatus = "20";
        channelVisit.SignInAddress = form.SignAddress;
        channelVisit.SignInLongitude = form.SignLongitude;
        channelVisit.SignInLatitude = form.SignLatitude;
        channelVisit.SignInRemark = form.SignRemark;

        channelVisitMapper.updateChannelVisit(channelVisit);

        List<ChannelVisitSignInAttachmentEntity> attachment = new ArrayList<>();
        if (form.Attachment != null) {
            for (ChannelVisitAttachmentAddForm channelVisitAttachmentAddForm : form.Attachment) {
                ChannelVisitSignInAttachmentEntity attachmentEntity = new ChannelVisitSignInAttachmentEntity();
                attachmentEntity.ChannelVisitID = form.ID;
                attachmentEntity.AttachmentType = channelVisitAttachmentAddForm.AttachmentType;
                attachmentEntity.AttachmentURL = channelVisitAttachmentAddForm.AttachmentURL;
                attachment.add(attachmentEntity);
            }
        }
        if (attachment.size() > 0) channelVisitMapper.createChannelVisitSignInAttachment(attachment);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput SignOut(ChannelVisitSignAddForm form) throws Exception {
        ChannelVisitEntity channelVisit = channelVisitMapper.getChannelVisit(form.ID);
        channelVisit.SignOutOn = DateTimeUtil.toDate(form.SignOn);
        channelVisit.VisitStatus = "30";
        channelVisit.SignOutAddress = form.SignAddress;
        channelVisit.SignOutLongitude = form.SignLongitude;
        channelVisit.SignOutLatitude = form.SignLatitude;
        channelVisit.SignOutRemark = form.SignRemark;

        channelVisitMapper.updateChannelVisit(channelVisit);

        List<ChannelVisitSignOutAttachmentEntity> attachment = new ArrayList<>();
        if (form.Attachment != null) {
            for (ChannelVisitAttachmentAddForm channelVisitAttachmentAddForm : form.Attachment) {
                ChannelVisitSignOutAttachmentEntity attachmentEntity = new ChannelVisitSignOutAttachmentEntity();
                attachmentEntity.ChannelVisitID = form.ID;
                attachmentEntity.AttachmentType = channelVisitAttachmentAddForm.AttachmentType;
                attachmentEntity.AttachmentURL = channelVisitAttachmentAddForm.AttachmentURL;
                attachment.add(attachmentEntity);
            }
        }
        if (attachment.size() > 0) channelVisitMapper.createChannelVisitSignOutAttachment(attachment);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput createVisitLog(ChannelVisitLogAddForm form) {
        ChannelVisitEntity channelVisit = channelVisitMapper.getChannelVisit(form.ID);
        if (channelVisit.VisitType.equals("20")) {
            channelVisit.VisitStatus = "30";
        }
        channelVisit.VisitRecord = form.Remark;
        channelVisitMapper.updateChannelVisit(channelVisit);

        List<ChannelVisitRecordAttachmentEntity> attachment = new ArrayList<>();
        if (form.Attachment != null) {
            for (ChannelVisitAttachmentAddForm channelVisitAttachmentAddForm : form.Attachment) {
                ChannelVisitRecordAttachmentEntity attachmentEntity = new ChannelVisitRecordAttachmentEntity();
                attachmentEntity.ChannelVisitID = form.ID;
                attachmentEntity.AttachmentType = channelVisitAttachmentAddForm.AttachmentType;
                attachmentEntity.AttachmentURL = channelVisitAttachmentAddForm.AttachmentURL;
                attachment.add(attachmentEntity);
            }
        }
        if (attachment.size() > 0) channelVisitMapper.createChannelVisitRecordAttachment(attachment);
        return BaseOutput.success();
    }

    public BaseOutput report(ChannelVisitReportQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<ChannelVisitReportOutForm> list = channelVisitMapper.report(SystemUserUtil.getSystemUserID(), form.StartDate, form.EndDate, form.Name);

        return BasePageInfo.success(list);
    }

    @Transactional
    public BaseOutput ceateAppointment(ChannelVisitAppointmentAddForm form) throws Exception {

        AppointmentBillEntity appointmentBillEntity = new AppointmentBillEntity();
        appointmentBillEntity.ID = AutoNumberUtils.getIBeautyAppointmentBill();
        appointmentBillEntity.EntityID = form.EntityID;
        appointmentBillEntity.CustomerID = form.CustomerID;
        appointmentBillEntity.Status = "10";
        appointmentBillEntity.AppointmentDate = DateTimeUtil.toDate(form.AppointmentDate);
        appointmentBillEntity.Period = 60;
        appointmentBillEntity.Remark = form.Remark;
        appointmentBillEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        appointmentBillEntity.Channel = BillChannel.BillChannelMiniprogram.getID();
        appointmentBillEntity.CreatedOn = new Date();
        appointmentBillMapper.createAppointmentBill(appointmentBillEntity);


        AppointmentBillProcessEntity appointmentBillProcessEntity = new AppointmentBillProcessEntity();
        appointmentBillProcessEntity.AppointmentBillID = appointmentBillEntity.ID;
        appointmentBillProcessEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        appointmentBillProcessEntity.CreatedOn = new Date();
        appointmentBillProcessEntity.ActionType = "10";
        appointmentBillMapper.createAppointmentBillProcess(appointmentBillProcessEntity);

        return BaseOutputForm.success(appointmentBillEntity.ID);
    }

    @Transactional
    public BaseOutput updateAppointment(ChannelVisitAppointmentUpdateForm form) throws Exception {

        AppointmentBillEntity appointmentBillEntity = appointmentBillMapper.getAppointmentBill(form.ID);
        appointmentBillEntity.EntityID = form.EntityID;
        appointmentBillEntity.AppointmentDate = DateTimeUtil.toDate(form.AppointmentDate);
        appointmentBillEntity.Remark = form.Remark;
        appointmentBillMapper.updateAppointmentBill(appointmentBillEntity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput cancelAppointment(ChannelVisitAppointmentUpdateForm form) {

        AppointmentBillEntity appointmentBillEntity = appointmentBillMapper.getAppointmentBill(form.ID);
        appointmentBillEntity.Status = "30";
        appointmentBillMapper.updateAppointmentBill(appointmentBillEntity);
        AppointmentBillProcessEntity appointmentBillProcessEntity = new AppointmentBillProcessEntity();
        appointmentBillProcessEntity.AppointmentBillID = appointmentBillEntity.ID;
        appointmentBillProcessEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        appointmentBillProcessEntity.CreatedOn = new Date();
        appointmentBillProcessEntity.ActionType = "30";
        appointmentBillMapper.createAppointmentBillProcess(appointmentBillProcessEntity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput appointmentInfo(ChannelVisitAppointmentUpdateForm form) {
        return BaseOutputForm.success(appointmentBillMapper.appointmentBillInfo(form.ID));
    }

    public BaseOutput appointmentList(ChannelVisitAppointmentQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<ChannelVisitAppointmentOutputForm> list = appointmentBillMapper.getChannelAppointmentBill(SystemUserUtil.getSystemUserID(), form.Name, form.Status, form.StartDate, form.EndDate, form.EntityID,form.ChannelID,form.ReceptionEmployeeID);
        return BasePageInfo.success(list);
    }
}
