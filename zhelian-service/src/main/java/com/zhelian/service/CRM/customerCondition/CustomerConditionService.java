package com.zhelian.service.CRM.customerCondition;

import com.zhelian.mapper.CRM.CustomerConditionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.CustomerConditionEntity;
import com.zhelian.model.ibeauty.entity.CustomerConditionGoodsEntity;
import com.zhelian.model.ibeauty.entity.CustomerConditionGoodsTypeEntity;
import com.zhelian.model.ibeauty.form.input.CustomerConditionAddForm;
import com.zhelian.model.ibeauty.form.input.CustomerConditionGoodsAddForm;
import com.zhelian.model.ibeauty.form.output.CustomerConditionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
@Transactional
public class CustomerConditionService {

    @Autowired
    CustomerConditionMapper customerConditionMapper;

    public BaseOutput all() {
        CustomerConditionOutputForm customerCondition = customerConditionMapper.allCustomerCondition();
        if (customerCondition != null) {
            customerCondition.GoodsType = customerConditionMapper.allCustomerConditionGoodsType();
            customerCondition.Goods = customerConditionMapper.allCustomerConditionGoods();
        }
        return BaseOutputForm.success(customerCondition);
    }


    @Transactional
    public BaseOutput create(CustomerConditionAddForm form) {
        if (form.Type.equals("40") && form.Amount == null) {
            return BaseOutput.failed("请填写金额");
        }
        if (form.Type.equals("50") && form.Amount == null) {
            return BaseOutput.failed("请填写金额");
        }
        if (form.Type.equals("60") && form.Amount == null) {
            return BaseOutput.failed("请填写金额");
        }
        if (form.Type.equals("80")) {
            if (form.Goods == null || form.Goods.size() == 0) {
                return BaseOutput.failed("请选择指定销售商品");
            }
        }
        if (form.Type.equals("70") && form.GoodsType.length == 0) {
            return BaseOutput.failed("请选择指定商品类型");

        }
        CustomerConditionEntity customerConditionEntity = customerConditionMapper.getCustomerCondition();
        List<CustomerConditionGoodsTypeEntity> goodsType = new ArrayList<>();
        List<CustomerConditionGoodsEntity> goods = new ArrayList<>();
        if (customerConditionEntity != null) {
            customerConditionEntity.Type = form.Type;
            customerConditionEntity.Amount = form.Amount;
            customerConditionMapper.updateCustomerCondition(customerConditionEntity);
            customerConditionMapper.deleteCustomerConditionGoodsTypeEntity();
            customerConditionMapper.deleteCustomerConditionGoodsEntity();

        } else {
            customerConditionEntity = new CustomerConditionEntity();
            customerConditionEntity.Type = form.Type;
            customerConditionEntity.Amount = form.Amount;
            customerConditionMapper.createCustomerCondition(customerConditionEntity);
        }

        if (form.GoodsType.length > 0) {
            for (String s : form.GoodsType) {
                CustomerConditionGoodsTypeEntity customerConditionGoodsTypeEntity = new CustomerConditionGoodsTypeEntity();
                customerConditionGoodsTypeEntity.GoodsType = s;
                goodsType.add(customerConditionGoodsTypeEntity);
            }
            customerConditionMapper.createCustomerConditionGoodsType(goodsType);
        }
        if (form.Goods != null && form.Goods.size() > 0) {
            for (CustomerConditionGoodsAddForm conditionGoodsAddForm : form.Goods) {
                CustomerConditionGoodsEntity customerConditionGoodsEntity = new CustomerConditionGoodsEntity();
                customerConditionGoodsEntity.GoodsType = conditionGoodsAddForm.GoodsType;
                customerConditionGoodsEntity.GoodsID = conditionGoodsAddForm.GoodsID;
                customerConditionGoodsEntity.CustomerLevelID = conditionGoodsAddForm.CustomerLevelID;
                goods.add(customerConditionGoodsEntity);
            }
            customerConditionMapper.createCustomerConditionGoods(goods);
        }

        return BaseOutput.success();
    }


}