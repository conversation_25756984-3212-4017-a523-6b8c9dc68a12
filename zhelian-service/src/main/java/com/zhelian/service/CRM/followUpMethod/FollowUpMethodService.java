package com.zhelian.service.CRM.followUpMethod;

import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.CRM.FollowUpMethodMapper;
import com.zhelian.model.CRM.entity.FollowUpMethodEntity;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.PayMethodAddForm;
import com.zhelian.model.ibeauty.form.input.PayMethodMoveForm;
import com.zhelian.model.ibeauty.form.input.PayMethodQueryForm;
import com.zhelian.model.ibeauty.form.input.PayMethodUpdateForm;
import com.zhelian.model.ibeauty.form.output.PayMethodOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;


@Service
public class FollowUpMethodService {
    @Autowired
    FollowUpMethodMapper followUpMethodMapper;

    public BaseOutput all(PayMethodQueryForm form) {
        List<PayMethodOutputForm> list = followUpMethodMapper.payMethodList(form.Name, form.Active);
        return BaseOutputForm.success(list);
    }

    public BaseOutput create(PayMethodAddForm form) {
        Integer Sequence = followUpMethodMapper.getMaxSequence();
        FollowUpMethodEntity payMethodEntity = new FollowUpMethodEntity();
        payMethodEntity.Name = form.Name;
        payMethodEntity.Sequence = Sequence != null ? Sequence + 1 : 1;
        followUpMethodMapper.createPayMethod(payMethodEntity);
        return BaseOutput.success();
    }

    public BaseOutput update(PayMethodUpdateForm form) {
        FollowUpMethodEntity payMethodEntity = new FollowUpMethodEntity();
        payMethodEntity.ID = form.ID;
        payMethodEntity.Name = form.Name;
        payMethodEntity.Active = form.Active;
        followUpMethodMapper.updatePayMethod(payMethodEntity);

        return BaseOutput.success();
    }

    public BaseOutput move(PayMethodMoveForm form) {
        //重新排序
        LinkedList<Integer> linkedList = followUpMethodMapper.getIDOrderBySequenceASC(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<FollowUpMethodEntity> list = new ArrayList<>();

        for (Integer ID : linkedList) {
            FollowUpMethodEntity payMethodEntity = new FollowUpMethodEntity();
            payMethodEntity.ID = ID;
            payMethodEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(payMethodEntity);
        }
        followUpMethodMapper.updateSequence(list);
        return BaseOutput.success();
    }

}