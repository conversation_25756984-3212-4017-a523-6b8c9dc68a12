package com.zhelian.service.CRM.customerGrowth;

import com.zhelian.mapper.CRM.CustomerGrowthMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.CustomerLevelSettingEntity;
import com.zhelian.model.ibeauty.form.input.CustomerGrowthStatusUpdateForm;
import com.zhelian.model.ibeauty.form.input.CustomerGrowthUpdateForm;
import com.zhelian.model.ibeauty.form.output.CustomerGrowthOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional
public class CustomerGrowthService {

    @Autowired
    CustomerGrowthMapper customerGrowthMapper;

    public BaseOutput all() {
        List<CustomerGrowthOutputForm> list = customerGrowthMapper.allCustomerGrowth();
        return BaseOutputForm.success(list);
    }


    public BaseOutput updateCustomerGrowth(CustomerGrowthUpdateForm form) {
        customerGrowthMapper.updateCustomerGrowth(form.Detail);
        return BaseOutput.success();
    }


    public BaseOutput status() {
        CustomerLevelSettingEntity customerGrowth = customerGrowthMapper.status();
        Boolean isGrowth = customerGrowth == null ? false : customerGrowth.IsGrowth;
        return BaseOutputForm.success(isGrowth);
    }

    public BaseOutput updateStatus(CustomerGrowthStatusUpdateForm form) {
        customerGrowthMapper.updateStatus(form.Status);
        return BaseOutput.success();
    }
}