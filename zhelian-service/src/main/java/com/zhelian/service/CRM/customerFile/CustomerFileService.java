package com.zhelian.service.CRM.customerFile;

import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.utils.OSSClientUtil;
import com.zhelian.core.utils.UploadVideoUtil;
import com.zhelian.mapper.CRM.CustomerFileMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.CustomerFileAttachmentEntity;
import com.zhelian.model.ibeauty.entity.CustomerFileEntity;
import com.zhelian.model.ibeauty.form.input.CustomerFileAddForm;
import com.zhelian.model.ibeauty.form.input.CustomerFileAttachmentAddForm;
import com.zhelian.model.miniprogram.form.output.VideoOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class CustomerFileService {

    @Autowired
    CustomerFileMapper customerFileMapper;


    @Transactional
    public BaseOutput createCustomerFile(CustomerFileAddForm form) throws IOException {
        CustomerFileEntity customerFileEntity = new CustomerFileEntity();
        customerFileEntity.CustomerID = form.CustomerID;
        customerFileEntity.CustomerFileCategoryID = form.CustomerFileCategoryID;
        customerFileEntity.Remark = form.Remark;
        customerFileEntity.CreatedOn = new Timestamp(System.currentTimeMillis());
        customerFileEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        customerFileMapper.createCustomerFile(customerFileEntity);

        List<CustomerFileAttachmentEntity> list = new ArrayList<>();
        if (form.Attachment != null && form.Attachment.size() > 0) {
            for (CustomerFileAttachmentAddForm customerFileAttachmentAddForm : form.Attachment) {
                CustomerFileAttachmentEntity customerFileAttachmentEntity = new CustomerFileAttachmentEntity();
                customerFileAttachmentEntity.Name = customerFileAttachmentAddForm.Name;
                customerFileAttachmentEntity.MimeType = customerFileAttachmentAddForm.MimeType;
                customerFileAttachmentEntity.CustomerFileID = customerFileEntity.ID;
                customerFileAttachmentEntity.AttachmentURL = customerFileAttachmentAddForm.AttachmentURL;
                customerFileAttachmentEntity.AttachmentType = customerFileAttachmentAddForm.AttachmentType;

                list.add(customerFileAttachmentEntity);
            }

        }
        if (list.size() > 0) {
            customerFileMapper.createCustomerFileAttachment(list);
        }


        return BaseOutput.success();
    }

    public BaseOutput uploadVideoUtil(MultipartFile multipartFile) throws Exception {
        String filename = multipartFile.getOriginalFilename();
        String title = filename.substring(0, filename.indexOf("."));
        byte[] bytes = multipartFile.getBytes();
        InputStream inputStream = new BufferedInputStream(new ByteArrayInputStream(bytes));
        VideoOutputForm videoOutputForm = new VideoOutputForm();
        videoOutputForm.VideoId = UploadVideoUtil.testUploadStream(title, filename, inputStream);
        videoOutputForm.VideoName = title;
        return BaseOutputForm.success(videoOutputForm);
    }

    public BaseOutput uploadFile(MultipartFile multipartFile) throws Exception {
        Date d = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String newFileName = sdf.format(d);
        String filename = multipartFile.getOriginalFilename();
        String title = filename.substring(0, filename.indexOf("."));
        String fileSuffix = filename.substring(filename.lastIndexOf("."));
        String suffix = filename.substring(filename.lastIndexOf(".") + 1);
        byte[] bytes = multipartFile.getBytes();
        VideoOutputForm videoOutputForm = new VideoOutputForm();
        videoOutputForm.VideoId = OSSClientUtil.putOSSObject(title + newFileName + fileSuffix, bytes, suffix);
        videoOutputForm.VideoName = title + newFileName;
        return BaseOutputForm.success(videoOutputForm);
    }

}