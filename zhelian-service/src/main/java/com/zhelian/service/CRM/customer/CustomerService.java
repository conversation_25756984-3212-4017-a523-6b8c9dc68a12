package com.zhelian.service.CRM.customer;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.utils.DateTimeUtil;
import com.zhelian.core.utils.OSSClientUtil;
import com.zhelian.core.utils.UploadVideoUtil;
import com.zhelian.core.utils.YingXiaoYunUtil;
import com.zhelian.mapper.CRM.*;
import com.zhelian.mapper.yingxiaoyun.YingXiaoYunAppointmentBillMapper;
import com.zhelian.mapper.yingxiaoyun.YingXiaoYunTenantMapper;
import com.zhelian.model.CRM.form.output.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.*;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.ibeauty.form.output.*;
import com.zhelian.model.khs.entity.EntityEntity;
import com.zhelian.model.miniprogram.form.input.*;
import com.zhelian.model.miniprogram.form.output.CustomerBirthdayOutputForm;
import com.zhelian.model.yingxiaoyun.entity.YingXiaoYunCustomerEntity;
import com.zhelian.model.yingxiaoyun.entity.YingXiaoYunTenantEntity;
import com.zhelian.model.yingxiaoyun.form.output.YXYCustomerRecordOutPutForm;
import com.zhelian.model.yingxiaoyun.form.output.YXYCustomerRecordPageOutPutForm;
import com.zhelian.model.yingxiaoyun.form.output.YXYRecordOutPutForm;
import com.zhelian.service.utils.AgeCalculator;
import com.zhelian.service.miniprogram.nursingLog.NursingLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Service
public class CustomerService {

    @Autowired
    CustomerMapper customerMapper;
    @Autowired
    NursingLogService nursingLogService;
    @Autowired
    TagLibraryMapper tagLibraryMapper;

    @Autowired
    CustomerLevelMapper customerLevelMapper;

    @Autowired
    CustomerReduceConfigMapper customerReduceConfigMapper;
    @Autowired
    CustomerGrowthMapper customerGrowthMapper;
    @Autowired
    YingXiaoYunTenantMapper yingXiaoYunTenantMapper;

    @Autowired
    YingXiaoYunAppointmentBillMapper yingXiaoYunAppointmentBillMapper;
    @Autowired
    CustomerFileApplicationSceneMapper customerFileApplicationSceneMapper;

    public BaseOutput consultant(String name) {
        List<ConsultantOutputForm> list = customerMapper.consultant(SystemUserUtil.getSystemEntityID(), name);
        return BaseOutputForm.success(list);
    }

    public BaseOutput customerList(CustomerQueryForm form) {
        String lunarStartDate = customerMapper.getLunarTime(form.StartDate);
        String lunarEndDate = customerMapper.getLunarTime(form.EndDate);
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CustomerOutputForm> list = customerMapper.customerList(null, SystemUserUtil.getSystemEntityID(), form.Name, form.EmployeeID, form.CustomerLevelID, form.CustomerSourceID, form.StartDate, form.EndDate, lunarStartDate, lunarEndDate, form.ServicerID, form.ServicerEmployeeID, form.ToShopStartDate, form.ToShopEndDate, form.MemberOnStartDate, form.MemberOnEndDate,form.IntroducerName,form.ChannelName,form.CreatedOnStartDate,form.CreatedOnEndDate);

        return BasePageInfo.success(list);
    }

    public BaseOutput allCustomer(CustomerQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<ConsultantOutputForm> list = customerMapper.allCustomer(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput customerInfo(Integer ID) {
        CustomerInfoOutputForm customerInfoOutputForm = customerMapper.customerInfo(ID);
        if (customerInfoOutputForm == null)
            return BaseOutput.failed("会员不存在");
        return BaseOutputForm.success(customerInfoOutputForm);
    }

    @Transactional
    public BaseOutput createCustomer(CustomerAddForm form) throws Exception {
        String message = check(form.PhoneNumber, form.Gender, form.Birthday, form.CustomerSourceID, form.ChannelID, form.Code, form.IdentityCard, form.Job, form.ProvinceCode, form.CityCode, form.AreaCode, form.Address, form.Remark);
        if (!message.equals("")) {
            return BaseOutput.failed(message);
        }

        if (form.PhoneNumber != null && !form.PhoneNumber.equals("")) {
            Integer count = customerMapper.customerCount(form.PhoneNumber);
            if (count > 0)
                return BaseOutput.failed("手机号已存在，不用新建客户");
        }

        if (form.IdentityCard != null && !form.IdentityCard.equals("")) {
            Integer count = customerMapper.customerCountByIdentityCard(form.IdentityCard);
            if (count > 0)
                return BaseOutput.failed("身份证号已存在");
        }

        if (form.Code != null && !form.Code.equals("")) {
            Integer codeCount = customerMapper.customerCodeCount(form.Code);
            if (codeCount > 0)
                return BaseOutput.failed("顾客编号已存在");
        }

        CustomerEntity customerEntity = new CustomerEntity();
        customerEntity.Name = form.Name;
        customerEntity.PhoneNumber = form.PhoneNumber;
        customerEntity.Gender = form.Gender;
        customerEntity.CustomerLevelID = form.IsMember == false  ? null : form.CustomerLevelID;
        customerEntity.CustomerSourceID = form.CustomerSourceID;
        customerEntity.Code = form.Code;
        customerEntity.Introducer = form.Introducer;
        customerEntity.Birthday = form.Birthday == null || form.Birthday.equals("") ? null : DateTimeUtil.toDate(form.Birthday);
        customerEntity.CreatedOn = new Date();
        customerEntity.BirthdayType = form.BirthdayType;
        customerEntity.ProvinceCode = form.ProvinceCode;
        customerEntity.CityCode = form.CityCode;
        customerEntity.AreaCode = form.AreaCode;
        customerEntity.Address = form.Address;
        customerEntity.Job = form.Job;
        customerEntity.IdentityCard = form.IdentityCard;
        customerEntity.Remark = form.Remark;
        customerEntity.ChannelID = form.ChannelID;
        customerEntity.IsMember = form.IsMember;
        customerEntity.IsLockMemberLevel = form.IsLockMemberLevel;
        customerEntity.MemberOn = form.IsMember == false  ? null : new Date();
        customerMapper.insertCustomer(customerEntity);

        if (form.IsMember) {
            CustomerLevelEntity customerLevel = customerLevelMapper.getCustomerLevel(form.CustomerLevelID);
            if (customerLevel !=null && new BigDecimal(customerLevel.LevelValue).compareTo(BigDecimal.ZERO) > 0) {
                CustomerGrowthEntity customerGrowthEntity = new CustomerGrowthEntity();
                customerGrowthEntity.CustomerID = customerEntity.ID;
                customerGrowthEntity.CustomerGrowthTypeID = "AdjustGrowth";
                customerGrowthEntity.GrowthValue = new BigDecimal(customerLevel.LevelValue);
                customerGrowthEntity.CreatedOn = new Date();
                customerGrowthEntity.Remark = form.Remark;
                customerGrowthMapper.insertCustomerGrowth(customerGrowthEntity);
            }

            CustomerReduceConfigOutputForm customerReduceConfig = customerReduceConfigMapper.getCustomerReduceConfig();
            if (customerReduceConfig.IsReduce) {
                CustomerLevelValidityEntity customerLevelValidityEntity = new CustomerLevelValidityEntity();
                customerLevelValidityEntity.CustomerID = customerEntity.ID;
                customerLevelValidityEntity.ModifyOn = new Date();
                if (customerReduceConfig.ReduceRule.equals("10")) {
                    customerLevelValidityEntity.ValidityOn = DateTimeUtil.addDay(new Date(), customerReduceConfig.VaildModeDay);

                } else {
                    customerLevelValidityEntity.ValidityOn = DateTimeUtil.addDay(customerReduceConfig.RegularModeUpdateDate, -1);
                }
                customerLevelMapper.insertCustomerLevelValidity(customerLevelValidityEntity);
            }
        }

        if (form.ServicerList != null && form.ServicerList.size() > 0) {
            for (ServicerEmployeeAddForm ServicerList : form.ServicerList) {
                CustomerServicerEntity entity = new CustomerServicerEntity();
                entity.CustomerID = customerEntity.ID;
                entity.ServicerID = ServicerList.ServicerID;
                entity.EmployeeID = ServicerList.EmployeeID;
                customerMapper.insertCustomerServicer(entity);
            }
        }

        insertCustomerEntity(customerEntity.ID, SystemUserUtil.getSystemEntityID(), true, true);

        CustomerShortInfoOutputForm customerShortInfoOutputForm = new CustomerShortInfoOutputForm();
        customerShortInfoOutputForm.ID = customerEntity.ID;
        customerShortInfoOutputForm.Name = customerEntity.Name;
        customerShortInfoOutputForm.PhoneNumber = customerEntity.PhoneNumber;
        return BaseOutputForm.success(customerShortInfoOutputForm);
    }

    @Transactional
    public BaseOutput createMiniCustomer(CustomerAddExtendForm form) throws Exception {
        String message = check(form.PhoneNumber, form.Gender, form.Birthday, form.CustomerSourceID, form.ChannelID, form.Code, form.IdentityCard, form.Job, form.ProvinceCode, form.CityCode, form.AreaCode, form.Address, form.Remark);
        if (!message.equals("")) {
            return BaseOutput.failed(message);
        }
        if (form.PhoneNumber != null && !form.PhoneNumber.equals("")) {
            Integer count = customerMapper.customerCount(form.PhoneNumber);
            if (count > 0)
                return BaseOutput.failed("手机号已存在，不用新建客户");
        }

        if (form.IdentityCard != null && !form.IdentityCard.equals("")) {
            Integer count = customerMapper.customerCountByIdentityCard(form.IdentityCard);
            if (count > 0)
                return BaseOutput.failed("身份证号已存在");
        }


        if (form.Code != null && !form.Code.equals("")) {
            Integer codeCount = customerMapper.customerCodeCount(form.Code);
            if (codeCount > 0)
                return BaseOutput.failed("顾客编号已存在");
        }

        CustomerEntity customerEntity = new CustomerEntity();
        customerEntity.Name = form.Name;
        customerEntity.PhoneNumber = form.PhoneNumber;
        customerEntity.Gender = form.Gender;
        customerEntity.CustomerLevelID = form.IsMember == false  ? null : form.CustomerLevelID;
        customerEntity.CustomerSourceID = form.CustomerSourceID;
        customerEntity.Code = form.Code;
        customerEntity.Introducer = form.Introducer;
        customerEntity.Birthday = form.Birthday == null || form.Birthday.equals("") ? null : DateTimeUtil.toDate(form.Birthday);
        customerEntity.CreatedOn = new Date();
        customerEntity.BirthdayType = form.BirthdayType;
        customerEntity.ProvinceCode = form.ProvinceCode;
        customerEntity.CityCode = form.CityCode;
        customerEntity.AreaCode = form.AreaCode;
        customerEntity.Address = form.Address;
        customerEntity.Job = form.Job;
        customerEntity.IdentityCard = form.IdentityCard;
        customerEntity.Remark = form.Remark;
        customerEntity.ChannelID = form.ChannelID;
        customerEntity.IsMember = form.IsMember;
        customerEntity.IsLockMemberLevel = form.IsLockMemberLevel;
        customerEntity.MemberOn = form.IsMember == false  ? null : new Date();
        customerMapper.insertCustomer(customerEntity);

        if (form.IsMember) {
            CustomerLevelEntity customerLevel = customerLevelMapper.getCustomerLevel(form.CustomerLevelID);
            if (customerLevel != null && new BigDecimal(customerLevel.LevelValue).compareTo(BigDecimal.ZERO) > 0) {
                CustomerGrowthEntity customerGrowthEntity = new CustomerGrowthEntity();
                customerGrowthEntity.CustomerID = customerEntity.ID;
                customerGrowthEntity.CustomerGrowthTypeID = "AdjustGrowth";
                customerGrowthEntity.GrowthValue = new BigDecimal(customerLevel.LevelValue);
                customerGrowthEntity.CreatedOn = new Date();
                customerGrowthEntity.Remark = form.Remark;
                customerGrowthMapper.insertCustomerGrowth(customerGrowthEntity);
            }
            CustomerReduceConfigOutputForm customerReduceConfig = customerReduceConfigMapper.getCustomerReduceConfig();
            if (customerReduceConfig.IsReduce == true) {
                CustomerLevelValidityEntity customerLevelValidityEntity = new CustomerLevelValidityEntity();
                customerLevelValidityEntity.CustomerID = customerEntity.ID;
                customerLevelValidityEntity.ModifyOn = new Date();
                if (customerReduceConfig.ReduceRule.equals("10")) {
                    customerLevelValidityEntity.ValidityOn = DateTimeUtil.addDay(new Date(), customerReduceConfig.VaildModeDay);

                } else {
                    customerLevelValidityEntity.ValidityOn = DateTimeUtil.addDay(customerReduceConfig.RegularModeUpdateDate, -1);
                }
                customerLevelMapper.insertCustomerLevelValidity(customerLevelValidityEntity);
            }
        }


        if (form.ServicerList != null && form.ServicerList.size() > 0) {
            for (ServicerEmployeeAddForm ServicerList : form.ServicerList) {
                CustomerServicerEntity entity = new CustomerServicerEntity();
                entity.CustomerID = customerEntity.ID;
                entity.ServicerID = ServicerList.ServicerID;
                entity.EmployeeID = ServicerList.EmployeeID;
                customerMapper.insertCustomerServicer(entity);
            }
        }
        insertCustomerEntity(customerEntity.ID, SystemUserUtil.getSystemEntityID(), true, true);

        List<CustomerBasicFileInfoEntity> list = new ArrayList<>();
        if (form.detail != null && form.detail.size() > 0) {
            for (CustomerBasicFileDetailForm customerBasicFileDetailForm : form.detail) {
                CustomerBasicFileInfoEntity customerBasicFileInfoEntity = new CustomerBasicFileInfoEntity();
                customerBasicFileInfoEntity.CustomerID = customerEntity.ID;
                customerBasicFileInfoEntity.CustomerBasicFileID = customerBasicFileDetailForm.CustomerBasicFileID;
                customerBasicFileInfoEntity.Value = customerBasicFileDetailForm.Value;
                list.add(customerBasicFileInfoEntity);
            }

        }
        if (list.size() > 0) {
            customerMapper.createBasicFile(list);
        }

        CustomerShortInfoOutputForm customerShortInfoOutputForm = new CustomerShortInfoOutputForm();
        customerShortInfoOutputForm.ID = customerEntity.ID;
        customerShortInfoOutputForm.Name = customerEntity.Name;
        customerShortInfoOutputForm.PhoneNumber = customerEntity.PhoneNumber;
        return BaseOutputForm.success(customerShortInfoOutputForm);
    }

    public void insertCustomerEntity(Integer CustomerID, Integer EntityID, Boolean IsBelongEntity, Boolean IsCreateEntity) {
        if (CustomerID != null) {
            Integer ID = customerMapper.getCustomerEntityID(CustomerID, EntityID);
            if (ID == null) {
                CustomerEntityEntity customerEntityEntity = new CustomerEntityEntity();
                customerEntityEntity.CustomerID = CustomerID;
                customerEntityEntity.EntityID = EntityID;
                customerEntityEntity.IsBelongEntity = IsBelongEntity;
                customerEntityEntity.IsCreateEntity = IsCreateEntity;
                customerMapper.insertCustomerEntity(customerEntityEntity);

            }
        }
    }

    @Transactional
    public BaseOutput updateCustomer(CustomerUpdateForm form) throws Exception {
        String message = check(form.PhoneNumber, form.Gender, form.Birthday, form.CustomerSourceID, form.ChannelID, form.Code, form.IdentityCard, form.Job, form.ProvinceCode, form.CityCode, form.AreaCode, form.Address, form.Remark);
        if (!message.equals("")) {
            return BaseOutput.failed(message);
        }
        Integer entityID = SystemUserUtil.getSystemEntityID();
        CustomerEntity customerEntity = customerMapper.getCustomer(form.ID);
        if (customerEntity == null)
            return BaseOutput.failed("顾客不存在");

        if (form.PhoneNumber != null && !form.PhoneNumber.equals("")) {
            Integer count = customerMapper.exceptCustomerCount(form.PhoneNumber, form.ID);
            if (count > 0)
                return BaseOutput.failed("手机号重复");
        }
        if (form.IdentityCard != null && !form.IdentityCard.equals("")) {
            Integer count = customerMapper.exceptCustomerCountByIdentityCard(form.IdentityCard, form.ID);
            if (count > 0)
                return BaseOutput.failed("身份证号已存在");
        }
        if (form.Code != null && !form.Code.equals("")) {
            Integer codeCount = customerMapper.exceptCustomerCodeCount(form.Code, form.ID);
            if (codeCount > 0)
                return BaseOutput.failed("顾客编号已存在");
        }

        customerEntity.ID = form.ID;
        customerEntity.Name = form.Name;
        customerEntity.PhoneNumber = form.PhoneNumber;
        customerEntity.Gender = form.Gender;
        customerEntity.CustomerSourceID = form.CustomerSourceID;
        customerEntity.Code = form.Code;
        customerEntity.Introducer = form.Introducer;
        customerEntity.Birthday = form.Birthday == null || form.Birthday.equals("") ? null : DateTimeUtil.toDate(form.Birthday);
        customerEntity.BirthdayType = form.BirthdayType;
        customerEntity.ProvinceCode = form.ProvinceCode;
        customerEntity.CityCode = form.CityCode;
        customerEntity.AreaCode = form.AreaCode;
        customerEntity.Address = form.Address;
        customerEntity.Job = form.Job;
        customerEntity.IdentityCard = form.IdentityCard;
        customerEntity.Remark = form.Remark;
        customerEntity.ChannelID = form.ChannelID;
        customerMapper.updateCustomer(customerEntity);

        Integer CustomerEntityID = customerMapper.getCustomerEntityID(form.ID, entityID);
//        customerMapper.deleteCustomerEmployee(CustomerEntityID);
        customerMapper.deleteCustomerServicer(customerEntity.ID);
        if (form.ServicerList != null && form.ServicerList.size() > 0) {
            for (ServicerEmployeeAddForm ServicerList : form.ServicerList) {
                CustomerServicerEntity entity = new CustomerServicerEntity();
                entity.CustomerID = customerEntity.ID;
                entity.ServicerID = ServicerList.ServicerID;
                entity.EmployeeID = ServicerList.EmployeeID;
                customerMapper.insertCustomerServicer(entity);
            }
        }

//        if (form.EmployeeID != null) {
//            for (String employeeID : form.EmployeeID) {
//                CustomerEmployeeEntity customerEmployeeEntity = new CustomerEmployeeEntity();
//                customerEmployeeEntity.CustomerEntityID = CustomerEntityID;
//                customerEmployeeEntity.EmployeeID = employeeID;
//                customerMapper.insertCustomerEmployee(customerEmployeeEntity);
//            }
//        }

        List<CustomerOutputForm> list = customerMapper.customerList(form.ID, entityID, null, null, null, null, null, null, null, null, null, null, null, null, null, null,null,null,null,null);
        CustomerOutputForm customerOutputForm = null;
        if (list != null && list.size() > 0) {
            customerOutputForm = list.get(0);
        }
        return BaseOutputForm.success(customerOutputForm);
    }

    @Transactional
    public BaseOutput updateMiniCustomer(CustomerUpdateExtendForm form) throws Exception {
        String message = check(form.PhoneNumber, form.Gender, form.Birthday, form.CustomerSourceID, form.ChannelID, form.Code, form.IdentityCard, form.Job, form.ProvinceCode, form.CityCode, form.AreaCode, form.Address, form.Remark);
        if (!message.equals("")) {
            return BaseOutput.failed(message);
        }
        Integer entityID = SystemUserUtil.getSystemEntityID();
        CustomerEntity customerEntity = customerMapper.getCustomer(form.ID);
        if (customerEntity == null)
            return BaseOutput.failed("顾客不存在");

        if (form.PhoneNumber != null && !form.PhoneNumber.equals("")) {
            Integer count = customerMapper.exceptCustomerCount(form.PhoneNumber, form.ID);
            if (count > 0)
                return BaseOutput.failed("手机号重复");
        }
        if (form.IdentityCard != null && !form.IdentityCard.equals("")) {
            Integer count = customerMapper.exceptCustomerCountByIdentityCard(form.IdentityCard, form.ID);
            if (count > 0)
                return BaseOutput.failed("身份证号已存在");
        }
        if (form.Code != null && !form.Code.equals("")) {
            Integer codeCount = customerMapper.exceptCustomerCodeCount(form.Code, form.ID);
            if (codeCount > 0)
                return BaseOutput.failed("顾客编号已存在");
        }


        customerEntity.ID = form.ID;
        customerEntity.Name = form.Name;
        customerEntity.PhoneNumber = form.PhoneNumber;
        customerEntity.Gender = form.Gender;
        customerEntity.CustomerSourceID = form.CustomerSourceID;
        customerEntity.Code = form.Code;
        customerEntity.Introducer = form.Introducer;
        customerEntity.Birthday = form.Birthday == null || form.Birthday.equals("") ? null : DateTimeUtil.toDate(form.Birthday);
        customerEntity.BirthdayType = form.BirthdayType;
        customerEntity.ProvinceCode = form.ProvinceCode;
        customerEntity.CityCode = form.CityCode;
        customerEntity.AreaCode = form.AreaCode;
        customerEntity.Address = form.Address;
        customerEntity.Job = form.Job;
        customerEntity.IdentityCard = form.IdentityCard;
        customerEntity.Remark = form.Remark;
        customerEntity.ChannelID = form.ChannelID;
        customerMapper.updateCustomer(customerEntity);

        Integer CustomerEntityID = customerMapper.getCustomerEntityID(form.ID, entityID);
        customerMapper.deleteCustomerServicer(customerEntity.ID);

        if (form.ServicerList != null && form.ServicerList.size() > 0) {
            for (ServicerEmployeeAddForm ServicerList : form.ServicerList) {
                CustomerServicerEntity entity = new CustomerServicerEntity();
                entity.CustomerID = customerEntity.ID;
                entity.ServicerID = ServicerList.ServicerID;
                entity.EmployeeID = ServicerList.EmployeeID;
                customerMapper.insertCustomerServicer(entity);
            }
        }

        customerMapper.deleteCustomerBasicFile(form.ID);

        List<CustomerBasicFileInfoEntity> infoList = new ArrayList<>();
        if (form.detail != null && form.detail.size() > 0) {
            for (CustomerBasicFileDetailForm customerBasicFileDetailForm : form.detail) {
                CustomerBasicFileInfoEntity customerBasicFileInfoEntity = new CustomerBasicFileInfoEntity();
                customerBasicFileInfoEntity.CustomerID = form.ID;
                customerBasicFileInfoEntity.CustomerBasicFileID = customerBasicFileDetailForm.CustomerBasicFileID;
                customerBasicFileInfoEntity.Value = customerBasicFileDetailForm.Value;
                infoList.add(customerBasicFileInfoEntity);
            }

        }
        if (infoList.size() > 0) {
            customerMapper.createBasicFile(infoList);
        }

        List<CustomerOutputForm> list = customerMapper.customerList(form.ID, entityID, null, null, null, null, null, null, null, null, null, null, null, null, null, null,null,null,null,null);
        CustomerOutputForm customerOutputForm = null;
        if (list != null && list.size() > 0) {
            customerOutputForm = list.get(0);
        }
        return BaseOutputForm.success(customerOutputForm);
    }

    public BaseOutput getCustomerTagLibrary(CustTagLibraryQueryForm form) {
        List<TagLibraryOutputForm> list = customerMapper.getCustomerTagLibrary(form.ID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput updateCustomerTagLibrary(CustTagLibraryUpdateForm form) {

        customerMapper.deleteCustomerTagLibrary(form.ID);
        List<CustomerTagEntity> list = new ArrayList<>();
        if (form.TagLibrary != null && form.TagLibrary.length > 0) {
            for (Integer tagLibrary : form.TagLibrary) {
                CustomerTagEntity customerTagEntity = new CustomerTagEntity();
                customerTagEntity.CustomerID = form.ID;
                customerTagEntity.CustomerTagID = tagLibrary;
                list.add(customerTagEntity);
            }
            customerMapper.insertCustomerTagEntity(list);
        }
        return BaseOutput.success();
    }

    public BaseOutput getCustomerDetailSelfList(MiniCustomerQueryForm form) {
        String lunarStartDate = customerMapper.getLunarTime(form.StartDate);
        String lunarEndDate = customerMapper.getLunarTime(form.EndDate);
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CustomerOutputForm> list = customerMapper.miniCustomerList(SystemUserUtil.getSystemEntityID(), form.Name, form.CustomerLevelID, form.CustomerSourceID, form.StartDate, form.EndDate, lunarStartDate, lunarEndDate, form.ServicerID, SystemUserUtil.getSystemUserID(), form.ToShopStartDate, form.ToShopEndDate, form.StartTime, form.EndTime, form.MemberOnStartDate, form.MemberOnEndDate,form.ChannelName);
        return BasePageInfo.success(list);
    }

    public BaseOutput getCustomerDetailEntityList(MiniCustomerQueryForm form) {
        String lunarStartDate = customerMapper.getLunarTime(form.StartDate);
        String lunarEndDate = customerMapper.getLunarTime(form.EndDate);
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CustomerOutputForm> list = customerMapper.miniCustomerList(SystemUserUtil.getSystemEntityID(), form.Name, form.CustomerLevelID, form.CustomerSourceID, form.StartDate, form.EndDate, lunarStartDate, lunarEndDate, form.ServicerID, form.ServicerEmployeeID, form.ToShopStartDate, form.ToShopEndDate, form.StartTime, form.EndTime, form.MemberOnStartDate, form.MemberOnEndDate,form.ChannelName);
        return BasePageInfo.success(list);
    }

    public BaseOutput recommendProject(RecommendProjectQueryForm form) {
        return BaseOutputForm.success(customerMapper.getRecommendProject(SystemUserUtil.getSystemEntityID(), form.KeyWord, form.CustomerID));
    }

    public BaseOutput addRecommendProject(RecommendProjectAddForm form) {
        CustomerProjectEntity customerProjectEntity = new CustomerProjectEntity();
        customerProjectEntity.CustomerID = form.CustomerID;
        customerProjectEntity.EntityID = SystemUserUtil.getSystemEntityID();
        customerProjectEntity.ProjectID = form.ProjectID;
        customerMapper.insertCustomerProject(customerProjectEntity);
        return BaseOutput.success();
    }

    public BaseOutput deleteRecommendProject(RecommendProjectAddForm form) {
        customerMapper.deleteRecommendProject(SystemUserUtil.getSystemEntityID(), form.CustomerID, form.ProjectID);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput addCustomerTag(CustomerTagAddForm form) {
        List<TagLibraryEntity> list = tagLibraryMapper.allTagLibraryByName(form.Tag);
        CustomerTagEntity customerTagEntity = new CustomerTagEntity();
        customerTagEntity.CustomerID = form.CustomerID;
        if (list == null || list.size() == 0) {
            TagLibraryEntity tagLibraryEntity = new TagLibraryEntity();
            tagLibraryEntity.Name = form.Tag;
            tagLibraryMapper.createTagLibrary(tagLibraryEntity);

            customerTagEntity.CustomerTagID = tagLibraryEntity.ID;
        } else {
            customerTagEntity.CustomerTagID = list.get(0).ID;
        }
        customerMapper.insertCustomerTagByID(customerTagEntity);
        return BaseOutput.success();
    }

    public BaseOutput recommendProjectConsumeTime(RecommendProjectConsumeTimeQueryForm form) {
        return BaseOutputForm.success(customerMapper.recommendProjectConsumeTime(SystemUserUtil.getSystemEntityID(), form.CustomerID, form.Name));
    }

    public BaseOutput getCustomerDetail(CustomerDetailQueryForm form) {
        CustomerOutputForm customerDetail = customerMapper.getCustomerDetail(SystemUserUtil.getSystemEntityID(), form.CustomerID);
        CustomerReduceConfigOutputForm customerReduceConfig = customerReduceConfigMapper.getCustomerReduceConfig();
        customerDetail.IsShowGrowthValue = customerGrowthMapper.getCustomerGrowthSetting();
        if (customerDetail.CustomerLevelID != null) {
            if (customerReduceConfig.IsReduce == false) {
                customerDetail.GrowthValue = customerGrowthMapper.getCustomerGrowth(form.CustomerID);
            } else {
                CustomerLevelValidityEntity customerLevelValidityEntity = customerLevelMapper.getCustomerLevelValidity(form.CustomerID);
                if (customerLevelValidityEntity != null) {
                    if (customerReduceConfig.ReduceRule.equals("10")) {
                        BigDecimal customerGrowthTotal = customerGrowthMapper.getCustomerLevelByValidityOn(form.CustomerID, DateTimeUtil.toDate(customerLevelValidityEntity.ModifyOn), DateTimeUtil.toyyyyMMdd2(DateTimeUtil.addDay(customerLevelValidityEntity.ValidityOn, 1)));
                        customerDetail.GrowthValue = customerGrowthTotal == null ? BigDecimal.ZERO : customerGrowthTotal;
                    } else {

                        BigDecimal customerGrowthTotal = customerGrowthMapper.getCustomerLevelByValidity(form.CustomerID, DateTimeUtil.toDate(DateTimeUtil.addMonth(new Date(), customerReduceConfig.RegularModeMonth * -1)), DateTimeUtil.toyyyyMMdd2(DateTimeUtil.addDay(new Date(), 1)));
                        customerDetail.GrowthValue = customerGrowthTotal == null ? BigDecimal.ZERO : customerGrowthTotal;
                    }
                    customerDetail.CustomerLevelValidityOn = DateTimeUtil.toyyyyMMdd2(customerLevelValidityEntity.ValidityOn);
                } else {
                    customerDetail.GrowthValue = customerGrowthMapper.getCustomerGrowth(form.CustomerID);
                }
            }
        }
        customerDetail.ServicerEmployee = customerMapper.getCustomerServicerEmployee(form.CustomerID);
        return BaseOutputForm.success(customerDetail);
    }

    public BaseOutput getBirthdayList(CustomerBirthdayQueryForm form) {
        String lunarStartTime = customerMapper.getLunarTime(form.StartTime);
        String lunarEndTime = customerMapper.getLunarTime(form.EndTime);
        List<CustomerBirthdayOutputForm> list = customerMapper.getBirthdayList(SystemUserUtil.getSystemEntityID(), form.StartTime, form.EndTime, lunarStartTime, lunarEndTime);
        return BasePageInfo.success(list);
    }

    public BaseOutput uploadImage(CustomerImageAddForm form) {
        String url = OSSClientUtil.putOSSBase64Img(form.CustomerImage);
        CustomerEntity customer = customerMapper.getCustomer(form.CustomerID);
        customer.Avatar = url;
        customerMapper.updateCustomer(customer);
        return BaseOutput.success(url);
    }

    public BaseOutput accountInfo(CustomerImageAddForm form) {
        CustomerAccountInfoOutputForm customerAccountInfoOutputForm = customerMapper.getAccountInfo(form.CustomerID);
        customerAccountInfoOutputForm.SalePrice = customerAccountInfoOutputForm.SaleQuantity.compareTo(BigDecimal.ZERO) != 0 ? customerAccountInfoOutputForm.SaleAmount.divide(customerAccountInfoOutputForm.SaleQuantity, 2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO;
        customerAccountInfoOutputForm.ConsumePrice = customerAccountInfoOutputForm.ConsumeProjectQuantity.compareTo(BigDecimal.ZERO) != 0 ? customerAccountInfoOutputForm.ConsumeProjectAmount.divide(customerAccountInfoOutputForm.ConsumeProjectQuantity, 2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO;
        customerAccountInfoOutputForm.LastSaleBillDate = customerMapper.getLastSaleBillDate(form.CustomerID);
        customerAccountInfoOutputForm.LastConsumeProjectBillDate = customerMapper.getLastConsumeProjectBillDate(form.CustomerID);
        return BaseOutputForm.success(customerAccountInfoOutputForm);
    }

    public BaseOutput getDetailByCode(CustomerCodeQueryForm form) {
        CustomerShortInfoOutputForm customerShortInfoOutputForm = customerMapper.getDetailByCode(form.Code);
        return BaseOutputForm.success(customerShortInfoOutputForm);
    }

    public BaseOutput judgeCode(CustomerCodeQueryForm form) {
        Integer codeCount = customerMapper.customerCodeCount(form.Code);
        if (codeCount > 0)
            return BaseOutput.failed("顾客编号已存在");

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput createBasicFile(CustomerBasicFileInfoForm form) {

        customerMapper.deleteCustomerBasicFile(form.CustomerID);

        List<CustomerBasicFileInfoEntity> list = new ArrayList<>();
        if (form.detail != null && form.detail.size() > 0) {
            for (CustomerBasicFileDetailForm customerBasicFileDetailForm : form.detail) {
                CustomerBasicFileInfoEntity customerBasicFileInfoEntity = new CustomerBasicFileInfoEntity();
                customerBasicFileInfoEntity.CustomerID = form.CustomerID;
                customerBasicFileInfoEntity.CustomerBasicFileID = customerBasicFileDetailForm.CustomerBasicFileID;
                customerBasicFileInfoEntity.Value = customerBasicFileDetailForm.Value;
                list.add(customerBasicFileInfoEntity);
            }

        }
        if (list.size() > 0) {
            customerMapper.createBasicFile(list);
        }

        return BaseOutput.success();
    }

    public BaseOutput getBasicFile(CustomerBasicFileInfoForm form) {
        List<CustomerBasicFileOutputForm> list = customerMapper.getBasicFile(form.CustomerID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput getFile(CustomerDetailQueryForm form) {
        List<CustomerFileOutputForm> list = customerMapper.getFile(form.CustomerID, form.CategoryID);
        for (CustomerFileOutputForm customerFileOutputForm : list) {
            for (CustomerFileDetailOutputForm customerFileDetailOutputForm : customerFileOutputForm.Attachment) {
                if (customerFileDetailOutputForm.AttachmentType == 20) {
                    customerFileDetailOutputForm.AttachmentURL = UploadVideoUtil.getPlayInfoAdress(customerFileDetailOutputForm.AttachmentURL).getString("url");
                }
            }
        }
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput deleteFile(CustomerFileDeleteForm form) throws Exception {
        CustomerFileAttachmentEntity customerFileAttachmentEntity = customerMapper.getAttachment(form.ID);
        Integer count = customerMapper.getCustomerFileCount(form.ID);
        if (count == 0) {
            customerMapper.deleteFile(customerFileAttachmentEntity.CustomerFileID);
        }
        customerMapper.deleteFileAttachment(form.ID);
        if (customerFileAttachmentEntity.AttachmentType == 20) {
            UploadVideoUtil.deleteVideo(customerFileAttachmentEntity.AttachmentURL);
        } else {
            String url = customerFileAttachmentEntity.AttachmentURL;
            String[] split = url.split("/");
            OSSClientUtil.deleteObject(URLDecoder.decode(split[split.length - 1], "UTF-8"));
        }

        return BaseOutput.success();
    }

    public BaseOutput getFile(CustomerFileDownloadQueryForm form, HttpServletResponse response) {
        CustomerFileAttachmentEntity customerFileAttachmentEntity = customerMapper.getAttachment(form.AttachmentID);
        try {
            String[] split = customerFileAttachmentEntity.AttachmentURL.split("/");
            OSSClientUtil.getContentFile(URLDecoder.decode(split[split.length - 1], "UTF-8"), response, customerFileAttachmentEntity.MimeType);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return BaseOutput.success();
    }

    public BaseOutput followUpLog(CustomerDetailQueryForm form) {
        List<CustomerFollowUpOutputForm> list = customerMapper.getFollowUpLog(form.CustomerID);

        return BaseOutputForm.success(list);
    }

    public BaseOutput diagnosisRecord(CustomerDetailQueryForm form) {
        List<CustomerDiagnosisRecordTopOutputForm> list = customerMapper.getDiagnosisRecord(form.CustomerID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput project(CustomerProjectQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CustomerProjectOutputForm> list = new ArrayList<>();
        if (form.Type == 10) {
            list = customerMapper.allProject(form.Name);
        } else if (form.Type == 20) {
            list = customerMapper.saleProject(form.CustomerID, form.Name);
        } else {
            list = customerMapper.treatProject(form.CustomerID, form.Name);
        }

        return BasePageInfo.success(list);
    }

    public BaseOutput photoCompare(CustomerPhotoCompareQueryForm form) {
        List<CustomerPhotoCompareOutputForm> list = customerMapper.getPhotoCompare(form.CustomerID, form.Name);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput createPhotoCompare(CustomerPhotoCompareAddForm form) {
        PhotoCompareEntity photoCompareEntity = new PhotoCompareEntity();
        photoCompareEntity.ProjectID = form.ProjectID;
        photoCompareEntity.CustomerID = form.CustomerID;
        photoCompareEntity.CreatedOn = new Date();
        photoCompareEntity.CreatedBy = SystemUserUtil.getSystemUserID();

        customerMapper.createPhotoCompare(photoCompareEntity);

        List<PhotoCompareAttachmentEntity> list = new ArrayList<>();

        if (form.Attachment != null && form.Attachment.size() > 0) {
            form.Attachment.forEach((i) -> {
                        PhotoCompareAttachmentEntity photoCompareAttachmentEntity = new PhotoCompareAttachmentEntity();
                        photoCompareAttachmentEntity.PhotoCompareID = photoCompareEntity.ID;
                        photoCompareAttachmentEntity.PhotoType = i.PhotoType;
                        photoCompareAttachmentEntity.AttachmentURL = i.AttachmentURL;
                        photoCompareAttachmentEntity.CreatedOn = new Date();
                        list.add(photoCompareAttachmentEntity);
                    }
            );
        }

        customerMapper.createPhotoCompareAttachemnt(list);
        return BaseOutput.success();
    }


    public BaseOutput addToPhoto(CustomerAddToPhotoAddForm form) {
        PhotoCompareAttachmentEntity photoCompareAttachmentEntity = new PhotoCompareAttachmentEntity();
        photoCompareAttachmentEntity.PhotoCompareID = form.PhotoCompareID;
        photoCompareAttachmentEntity.PhotoType = form.PhotoType;
        photoCompareAttachmentEntity.AttachmentURL = form.AttachmentURL;
        photoCompareAttachmentEntity.CreatedOn = new Date();
        customerMapper.addToPhoto(photoCompareAttachmentEntity);
        return BaseOutputForm.success(photoCompareAttachmentEntity.CreatedOn);
    }

    @Transactional
    public BaseOutput deletePhoto(CustomerPhotoCompareDeleteForm form) {
        PhotoCompareAttachmentEntity photoCompareAttachmentEntity = customerMapper.getPhotoCompareAttachment(form.ID);
        String url = photoCompareAttachmentEntity.AttachmentURL;
        String[] split = url.split("/");
        try {
            OSSClientUtil.deleteObject(URLDecoder.decode(split[split.length - 1], "UTF-8"));
            customerMapper.deletePhotoCompareAttachmentEntity(form.ID);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return BaseOutput.success();
    }


    @Transactional
    public BaseOutput saveLevel(CustomerLevelSetAddForm form) {
        CustomerEntity customer = customerMapper.getCustomer(form.CustomerID);
        CustomerReduceConfigOutputForm customerReduceConfig = customerReduceConfigMapper.getCustomerReduceConfig();
        if (customer.CustomerLevelID == null) {
            BigDecimal customerGrowth = customerGrowthMapper.getCustomerGrowth(form.CustomerID);
            if (customerGrowth.compareTo(BigDecimal.ZERO) != 0) {
                CustomerGrowthEntity customerGrowthEntity = new CustomerGrowthEntity();
                customerGrowthEntity.CustomerID = form.CustomerID;
                customerGrowthEntity.CustomerGrowthTypeID = "AdjustGrowth";
                customerGrowthEntity.GrowthValue = customerGrowth.multiply(new BigDecimal(-1));
                customerGrowthEntity.CreatedOn = new Date();
                customerGrowthMapper.insertCustomerGrowth(customerGrowthEntity);
            }
            customer.CustomerLevelID = form.CustomerLevelID;
            customer.IsMember = true;
            customer.IsLockMemberLevel = form.IsLockMemberLevel;
            customer.MemberOn = new Date();
            customerMapper.updateCustomer(customer);

            CustomerLevelValidityEntity customerLevelValidityEntity = new CustomerLevelValidityEntity();
            customerLevelValidityEntity.CustomerID = form.CustomerID;
            customerLevelValidityEntity.ModifyOn = new Date();
            if (customerReduceConfig.IsReduce) {
                if (customerReduceConfig.ReduceRule.equals("10")) {
                    customerLevelValidityEntity.ValidityOn = DateTimeUtil.addDay(new Date(), customerReduceConfig.VaildModeDay);
                } else {
                    customerLevelValidityEntity.ValidityOn = DateTimeUtil.addDay(customerReduceConfig.RegularModeUpdateDate, -1);
                }
                customerLevelMapper.insertCustomerLevelValidity(customerLevelValidityEntity);
            }


        } else {
            if (!form.CustomerLevelID.equals(customer.CustomerLevelID)) {
                customer.CustomerLevelID = form.CustomerLevelID;
                customer.IsMember = true;
                customer.IsLockMemberLevel = form.IsLockMemberLevel;
                customerMapper.updateCustomer(customer);

                if (customerReduceConfig.IsReduce) {
                    CustomerLevelValidityEntity customerLevelValidityEntity = customerLevelMapper.getCustomerLevelValidity(customer.ID);
                    customerLevelValidityEntity.CustomerID = form.CustomerID;
                    customerLevelValidityEntity.ModifyOn = new Date();
                    if (customerReduceConfig.ReduceRule.equals("10")) {
                        customerLevelValidityEntity.ValidityOn = DateTimeUtil.addDay(new Date(), customerReduceConfig.VaildModeDay);
                    } else {
                        customerLevelValidityEntity.ValidityOn = DateTimeUtil.addDay(customerReduceConfig.RegularModeUpdateDate, -1);
                    }
                    customerLevelMapper.updateCustomerLevelValidity(customerLevelValidityEntity);
                }

            } else {
                customer.IsLockMemberLevel = form.IsLockMemberLevel;
                customerMapper.updateCustomer(customer);
            }
        }
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput deleteLevel(CustomerLevelSetAddForm form) {
        CustomerEntity customer = customerMapper.getCustomer(form.CustomerID);
        customer.IsMember = false;
        customer.CustomerLevelID = null;
        customer.MemberOn = null;
        customerMapper.updateCustomer(customer);
        customerLevelMapper.deleteCustomerLevelValidity(customer.ID);

        BigDecimal customerGrowth = customerGrowthMapper.getCustomerGrowth(customer.ID);
        if (customerGrowth.compareTo(BigDecimal.ZERO) != 0) {
            CustomerGrowthEntity customerGrowthEntity = new CustomerGrowthEntity();
            customerGrowthEntity.CustomerID = form.CustomerID;
            customerGrowthEntity.CustomerGrowthTypeID = "AdjustGrowth";
            customerGrowthEntity.GrowthValue = customerGrowth.multiply(new BigDecimal(-1));
            customerGrowthEntity.CreatedOn = new Date();
            customerGrowthMapper.insertCustomerGrowth(customerGrowthEntity);
        }

        return BaseOutput.success();
    }

    public BaseOutput growthValue(CustomerLevelSetAddForm form) {
        List<CustomerGrowthValueOutputForm> list = customerMapper.getGrowthValue(form.CustomerID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput addGrowthValue(CustomerGrowthValueAddForm form) {
        CustomerEntity customerEntity = customerMapper.getCustomer(form.CustomerID);
        CustomerReduceConfigEntity customerReduceConfigEntity = customerReduceConfigMapper.getCustomerReduceConfigEntity();
        if (customerEntity.IsLockMemberLevel == false) {
            CustomerLevelEntity customerLevelEntity = customerLevelMapper.getUpOneLevelByID(customerEntity.CustomerLevelID);
            if (customerReduceConfigEntity.IsReduce == false) {
                BigDecimal customerGrowth = customerGrowthMapper.getCustomerGrowth(form.CustomerID) == null ? BigDecimal.ZERO : customerGrowthMapper.getCustomerGrowth(form.CustomerID);
                customerGrowth = customerGrowth.add(form.GrowthValue);
                if (customerLevelEntity != null && customerGrowth.compareTo(new BigDecimal(customerLevelEntity.LevelValue)) >= 0) {
                    Integer customerLevel = customerGrowthMapper.getCustomerLevelByGrowthTotal(customerGrowth);
                    customerEntity.CustomerLevelID = customerLevel;
                    customerMapper.updateCustomer(customerEntity);
                }

            } else {
                if (customerReduceConfigEntity.ReduceRule.equals("10")) {
                    CustomerLevelValidityEntity customerLevelValidityEntity = customerLevelMapper.getCustomerLevelValidity(customerEntity.ID);
                    if (customerLevelValidityEntity != null) {
                        BigDecimal customerGrowthTotal = form.GrowthValue.add(customerGrowthMapper.getCustomerLevelByValidityOn(customerEntity.ID, DateTimeUtil.toDate(customerLevelValidityEntity.ModifyOn), DateTimeUtil.toyyyyMMdd2(DateTimeUtil.addDay(customerLevelValidityEntity.ValidityOn, 1))));

                        if (customerLevelEntity != null && customerGrowthTotal.compareTo(new BigDecimal(customerLevelEntity.LevelValue)) >= 0) {
                            //判断会员等级
                            CustomerLevelEntity entity = customerGrowthMapper.getCustomerLevelEntityByGrowthTotal(customerGrowthTotal);
                            customerEntity.CustomerLevelID = entity.ID;
                            customerMapper.updateCustomer(customerEntity);

                            customerLevelValidityEntity.CustomerID = customerEntity.ID;
                            customerLevelValidityEntity.ModifyOn = new Date();
                            customerLevelValidityEntity.ValidityOn = DateTimeUtil.addDay(new Date(), customerReduceConfigEntity.VaildModeDay);
                            customerLevelMapper.updateCustomerLevelValidity(customerLevelValidityEntity);

                        }
                    } else {
                        if (customerLevelEntity != null && form.GrowthValue.compareTo(new BigDecimal(customerLevelEntity.LevelValue)) >= 0) {
                            //判断会员等级
                            CustomerLevelEntity entity = customerGrowthMapper.getCustomerLevelEntityByGrowthTotal(form.GrowthValue);
                            customerEntity.CustomerLevelID = entity.ID;
                            customerMapper.updateCustomer(customerEntity);

                            customerLevelValidityEntity.CustomerID = customerEntity.ID;
                            customerLevelValidityEntity.ModifyOn = new Date();
                            customerLevelValidityEntity.ValidityOn = DateTimeUtil.addDay(new Date(), customerReduceConfigEntity.VaildModeDay);
                            customerLevelMapper.updateCustomerLevelValidity(customerLevelValidityEntity);
                        }
                    }
                }
            }
        }

        CustomerGrowthEntity customerGrowthEntity = new CustomerGrowthEntity();
        customerGrowthEntity.CustomerID = form.CustomerID;
        customerGrowthEntity.CustomerGrowthTypeID = "AdjustGrowth";
        customerGrowthEntity.GrowthValue = form.GrowthValue;
        customerGrowthEntity.CreatedOn = new Date();
        customerGrowthEntity.Remark = form.Remark;
        customerGrowthMapper.insertCustomerGrowth(customerGrowthEntity);

        return BaseOutput.success();
    }


    public BaseOutput record(CustomerRecordSessionQueryForm form) {
        YXYCustomerRecordOutPutForm bean = new YXYCustomerRecordOutPutForm();
        YXYCustomerRecordPageOutPutForm yxyCustomerRecordPageOutPutForm = new YXYCustomerRecordPageOutPutForm();
        CustomerEntity customer = customerMapper.getCustomer(form.CustomerID);
        YingXiaoYunTenantEntity yingXiaoYunTenant = yingXiaoYunTenantMapper.getYingXiaoYunTenantEntity();
        if (yingXiaoYunTenant != null && yingXiaoYunTenant.Active) {
            YXYRecordOutPutForm yxyInputForm = new YXYRecordOutPutForm();

            YingXiaoYunCustomerEntity yingXiaoYunCustomer = yingXiaoYunAppointmentBillMapper.getYingXiaoYunCustomer(customer.PhoneNumber);
            if (yingXiaoYunCustomer != null) {
                yxyInputForm.EnterpriseCode = SystemUserUtil.getSystemEnterpriseCode();
                yxyInputForm.TenantCode = yingXiaoYunTenant.TenantID;
                yxyInputForm.AccountPhone = customer.PhoneNumber;
                yxyInputForm.BeginTime = form.BeginTime;
                yxyInputForm.EndTime = form.EndTime;
                yxyInputForm.Type = form.Type;
                yxyInputForm.PageIndex = form.PageIndex;
                yxyInputForm.PageSize = form.PageSize;

                try {
                    JSONObject param = new JSONObject();
                    param.put("code", "ZL_GetChatRecordList");
                    param.put("content", JSON.toJSONString(yxyInputForm));
                    JSONObject param2 = new JSONObject();
                    param2.put("request", param);

                    String responseContent = YingXiaoYunUtil.sendHttpPostGetValue(param2);

                    bean = JSONObject.parseObject(responseContent, YXYCustomerRecordOutPutForm.class);

                    yxyCustomerRecordPageOutPutForm = new YXYCustomerRecordPageOutPutForm();
                    yxyCustomerRecordPageOutPutForm.PageSize = form.PageSize;
                    yxyCustomerRecordPageOutPutForm.Total = bean.Data.TotalCount;
                    yxyCustomerRecordPageOutPutForm.List = bean.Data.RecordList;

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return BaseOutputForm.success(yxyCustomerRecordPageOutPutForm);
    }

    public BaseOutput label(CustomerRecordSessionQueryForm form) {

        return BaseOutputForm.success(customerMapper.getYingXioaYunLabel(form.CustomerID));
    }

    public String check(String PhoneNumber, String Gender, String Birthday, Integer CustomerSourceID, Integer ChannelID, String Code,
                        String IdentityCard, String Job, String ProvinceCode, String CityCode, String AreaCode, String Address, String Remark) {
        CustomerFileApplicationSceneOutputForm phoneNumberScene = customerFileApplicationSceneMapper.getCustomerFileApplicationScene("PhoneNumber");
        if (phoneNumberScene != null && phoneNumberScene.IsRequired && !StringUtils.hasText(PhoneNumber)) {
            return phoneNumberScene.Name + "不能为空";
        }
        CustomerFileApplicationSceneOutputForm genderScene = customerFileApplicationSceneMapper.getCustomerFileApplicationScene("Gender");
        if (genderScene != null && genderScene.IsRequired && !StringUtils.hasText(Gender)) {
            return genderScene.Name + "不能为空";
        }
        CustomerFileApplicationSceneOutputForm birthdayScene = customerFileApplicationSceneMapper.getCustomerFileApplicationScene("Birthday");
        if (birthdayScene != null && birthdayScene.IsRequired && !StringUtils.hasText(Birthday)) {
            return birthdayScene.Name + "不能为空";
        }
        CustomerFileApplicationSceneOutputForm customerSourceScene = customerFileApplicationSceneMapper.getCustomerFileApplicationScene("CustomerSource");
        if (customerSourceScene != null && customerSourceScene.IsRequired && CustomerSourceID == null) {
            return customerSourceScene.Name + "不能为空";
        }
        CustomerFileApplicationSceneOutputForm channelScene = customerFileApplicationSceneMapper.getCustomerFileApplicationScene("Channel");
        if (channelScene != null && channelScene.IsRequired && ChannelID == null) {
            return channelScene.Name + "不能为空";
        }
        CustomerFileApplicationSceneOutputForm codeScene = customerFileApplicationSceneMapper.getCustomerFileApplicationScene("Code");
        if (codeScene != null && codeScene.IsRequired && !StringUtils.hasText(Code)) {
            return codeScene.Name + "不能为空";
        }
        CustomerFileApplicationSceneOutputForm identityCardScene = customerFileApplicationSceneMapper.getCustomerFileApplicationScene("IdentityCard");
        if (identityCardScene != null && identityCardScene.IsRequired && !StringUtils.hasText(IdentityCard)) {
            return identityCardScene.Name + "不能为空";
        }
        CustomerFileApplicationSceneOutputForm jobScene = customerFileApplicationSceneMapper.getCustomerFileApplicationScene("Job");
        if (jobScene != null && jobScene.IsRequired && !StringUtils.hasText(Job)) {
            return jobScene.Name + "不能为空";
        }
        CustomerFileApplicationSceneOutputForm provinceCityAreaScene = customerFileApplicationSceneMapper.getCustomerFileApplicationScene("ProvinceCityArea");
        if (provinceCityAreaScene != null && provinceCityAreaScene.IsRequired && (!StringUtils.hasText(ProvinceCode) || !StringUtils.hasText(CityCode) || !StringUtils.hasText(AreaCode))) {
            return provinceCityAreaScene.Name + "不能为空";
        }
        CustomerFileApplicationSceneOutputForm addressScene = customerFileApplicationSceneMapper.getCustomerFileApplicationScene("Address");
        if (addressScene != null && addressScene.IsRequired && !StringUtils.hasText(Address)) {
            return addressScene.Name + "不能为空";
        }
        CustomerFileApplicationSceneOutputForm remarkScene = customerFileApplicationSceneMapper.getCustomerFileApplicationScene("Remark");
        if (remarkScene != null && remarkScene.IsRequired && !StringUtils.hasText(Remark)) {
            return remarkScene.Name + "不能为空";
        }

        return "";
    }

    @Transactional
    public BaseOutput batchUpdateServicer(CustomerBatchUpdateForm form) {
        customerMapper.deleteBatchCustomerServicer(form.CustomerID);
        List<CustomerServicerEntity> list = new ArrayList<>();
        for (Integer integer : form.CustomerID) {
            for (ServicerEmployeeAddForm servicerEmployeeAddForm : form.ServicerList) {
                CustomerServicerEntity customerServicerEntity = new CustomerServicerEntity();
                customerServicerEntity.CustomerID = integer;
                customerServicerEntity.ServicerID = servicerEmployeeAddForm.ServicerID;
                customerServicerEntity.EmployeeID = servicerEmployeeAddForm.EmployeeID;
                list.add(customerServicerEntity);
            }
        }

        if (list.size() > 0)
            customerMapper.insertCustomerServicerList(list);

        return BaseOutput.success();
    }

    public BaseOutput excelDisPlayPhone(CustomerQueryForm form, HttpServletResponse response) {
        String lunarStartDate = customerMapper.getLunarTime(form.StartDate);
        String lunarEndDate = customerMapper.getLunarTime(form.EndDate);
        List<CustomerOutputExcelForm> list = customerMapper.excelDisPlayPhone(null, SystemUserUtil.getSystemEntityID(), form.Name, form.EmployeeID, form.CustomerLevelID, form.CustomerSourceID, form.StartDate, form.EndDate, lunarStartDate, lunarEndDate, form.ServicerID, form.ServicerEmployeeID, form.ToShopStartDate, form.ToShopEndDate, form.MemberOnStartDate, form.MemberOnEndDate,form.IntroducerName,form.ChannelName);

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("客户列表", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), CustomerOutputExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("客户列表").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    public BaseOutput excelNoDisPlayPhone(CustomerQueryForm form, HttpServletResponse response) {
        String lunarStartDate = customerMapper.getLunarTime(form.StartDate);
        String lunarEndDate = customerMapper.getLunarTime(form.EndDate);
        List<CustomerOutputExcelForm> list = customerMapper.excelNoDisPlayPhone(null, SystemUserUtil.getSystemEntityID(), form.Name, form.EmployeeID, form.CustomerLevelID, form.CustomerSourceID, form.StartDate, form.EndDate, lunarStartDate, lunarEndDate, form.ServicerID, form.ServicerEmployeeID, form.ToShopStartDate, form.ToShopEndDate, form.MemberOnStartDate, form.MemberOnEndDate,form.IntroducerName,form.ChannelName);

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("客户列表", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), CustomerOutputExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("客户列表").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    public BaseOutput customerBelongEntity(CustomerBasicFileInfoForm form) {
        List<EntityEntity> list = customerMapper.getCustomerBelongEntityList(form.CustomerID, SystemUserUtil.getSystemUserID());
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateCustomerBelongEntity(CustomerBelongEntityUpdateForm form) {
        CustomerEntityEntity customerBelongEntity = customerMapper.getBelongEntity(form.CustomerID);
        if (customerBelongEntity != null) {
            customerBelongEntity.IsBelongEntity = false;
            customerMapper.updateCustomerEntity(customerBelongEntity);
        }

        customerMapper.updateCustomerEntityEntity(form.CustomerID, form.EntityID);

        return BaseOutput.success();
    }

    /**
     * 客户档案列表 - 美际小肤设备专用
     * 根据关键词搜索客户档案，支持姓名匹配
     * 返回格式：{"code": 0, "message": "success", "data": [...]}
     *
     * @param form 查询参数
     * @return 客户档案列表，符合美际小肤设备响应格式
     */
    public BeautyDeviceResponse<List<CustomerProfileDTO>> customerProfileList(CustomerProfileListQueryForm form) {
        Integer entityID = Integer.valueOf(form.store_id);

        List<CustomerOutputForm> customers;
        
        // Use existing customerList method with name filtering
        customers = customerMapper.customerList(
            null,           // ID
            entityID,       // EntityID
            form.keyword,   // Name (will search name, phone, code)
            null,           // EmployeeID
            null,           // CustomerLevelID
            null,           // CustomerSourceID
            null,           // StartDate
            null,           // EndDate
            null,           // LunarStartDate
            null,           // LunarEndDate
            null,           // ServicerID
            null,           // ServicerEmployeeID
            null,           // ToShopStartDate
            null,           // ToShopEndDate
            null,           // MemberOnStartDate
            null,           // MemberOnEndDate
            null,           // IntroducerName
            null,           // ChannelName
            null,           // CreatedOnStartDate
            null            // CreatedOnEndDate
        );
        
        List<CustomerProfileDTO> result = new ArrayList<>();
        for (CustomerOutputForm customer : customers) {
            CustomerProfileDTO dto = new CustomerProfileDTO();
            dto.setId(String.valueOf(customer.ID));
            dto.setName(customer.Name);
            dto.setUnique(customer.PhoneNumber);
            if (customer.Birthday != null && !customer.Birthday.isEmpty()) {
                dto.setAge(AgeCalculator.calculateAge(customer.Birthday));
                dto.setBirthday(customer.Birthday);
            }
            
            // Parse created_on date string
            Long createdTime = null;
            if (customer.CreatedOn != null && !customer.CreatedOn.isEmpty()) {
                try {
                    createdTime = DateTimeUtil.convertToDate(customer.CreatedOn).getTime() / 1000;
                } catch (Exception e) {
                    // If parsing fails, use current time
                    createdTime = System.currentTimeMillis() / 1000;
                }
            }
            dto.setCreated_time(createdTime);
            
            // Convert gender string to integer
            Integer genderInt = 0; // default: 保密
            if ("男".equals(customer.Gender)) {
                genderInt = 1;
            } else if ("女".equals(customer.Gender)) {
                genderInt = 2;
            }
            dto.setGender(genderInt);
            
            dto.setSource(customer.CustomerSourceName);
            result.add(dto);
        }
        
        return BeautyDeviceResponse.success(result);
    }
    
    /**
     * 皮肤报告接收 - 美际小肤设备专用
     * 接收并保存皮肤报告数据
     *
     * @param form 皮肤报告数据
     * @return 接收结果
     */
    @Transactional
    public BeautyDeviceResponse skinReportReceive(CustomerSkinReportReceiveForm form) {
        try {
            // 转换并保存皮肤报告数据
            CustomerSkinReportEntity entity = new CustomerSkinReportEntity();
            
            // 基本信息转换
            entity.CustomerID = Integer.valueOf(form.customer_id);
            entity.ReportID = form.report_id;
            entity.ChannelID = Integer.valueOf(form.channel_id);
            entity.StoreID = Integer.valueOf(form.store_id);
            entity.ScannerID = form.scanner_id;
            entity.Edition = form.edition != null ? form.edition : 1; // 默认为P1设备
            entity.CreatedTime = form.created_time;
            entity.Icon = form.icon;
            entity.ViewUrl = form.view_url;
            entity.ShareUrl = form.share_url;
            entity.CreatedOn = new Date();
            
            // 将复杂对象转换为JSON字符串存储
            if (form.resources != null) {
                entity.Resources = JSON.toJSONString(form.resources);
            }
            
            if (form.contents != null) {
                entity.Contents = JSON.toJSONString(form.contents);
            }
            
            if (form.measured_results != null) {
                entity.MeasuredResults = JSON.toJSONString(form.measured_results);
            }
            
            // 保存到数据库
            customerMapper.insertCustomerSkinReport(entity);
            
            return BeautyDeviceResponse.success();
        } catch (Exception e) {
            e.printStackTrace();
            return BeautyDeviceResponse.failed("皮肤报告接收失败: " + e.getMessage());
        }
    }
}