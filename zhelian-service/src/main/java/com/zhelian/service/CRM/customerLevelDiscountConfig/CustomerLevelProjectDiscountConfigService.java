package com.zhelian.service.CRM.customerLevelDiscountConfig;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.CRM.CustomerLevelProjectDiscountConfigMapper;
import com.zhelian.model.CRM.entity.*;
import com.zhelian.model.CRM.form.input.*;
import com.zhelian.model.CRM.form.output.CustomerLevelDiscountPriceProjectAllOutputForm;
import com.zhelian.model.CRM.form.output.CustomerLevelDiscountPriceProjectConfigAllOutputForm;
import com.zhelian.model.CRM.form.output.CustomerLevelProjectCategoryDiscountConfigOutputForm;
import com.zhelian.model.CRM.form.output.CustomerLevelProjectCategoryDiscountOutputForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static java.math.BigDecimal.ROUND_HALF_UP;

@Service
public class CustomerLevelProjectDiscountConfigService {

    @Autowired
    CustomerLevelProjectDiscountConfigMapper customerLevelProjectDiscountConfigMapper;

    public BaseOutput customerLevelDiscountAll() {
        List<CustomerLevelProjectCategoryDiscountOutputForm> list = customerLevelProjectDiscountConfigMapper.customerLevelDiscountAll();
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput createCustomerLevelDiscountConfig(List<CustomerLevelProjectDiscountConfigAddForm> form) {

        customerLevelProjectDiscountConfigMapper.deleteCustomerLevelDiscountConfig();
        List<CustomerPriceProjectSchemeEntity> list = new ArrayList<>();
        for (CustomerLevelProjectDiscountConfigAddForm customerLevelProjectDiscountConfigAddForm : form) {
            CustomerPriceProjectSchemeEntity customerPriceProjectSchemeEntity = new CustomerPriceProjectSchemeEntity();
            customerPriceProjectSchemeEntity.CustomerLevelID = customerLevelProjectDiscountConfigAddForm.CustomerLevelID;
            if (customerLevelProjectDiscountConfigAddForm.DiscountPrice != null){
                customerPriceProjectSchemeEntity.DiscountPrice = customerLevelProjectDiscountConfigAddForm.DiscountPrice.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
            }
            list.add(customerPriceProjectSchemeEntity);
        }
        customerLevelProjectDiscountConfigMapper.createCustomerLevelDiscountConfig(list);
        return BaseOutput.success();
    }


    public BaseOutput customerLevelCategoryDiscountAll(CustomerLevelProjectCategoryDiscountConfigQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CustomerLevelProjectCategoryDiscountConfigOutputForm> list = customerLevelProjectDiscountConfigMapper.customerLevelCategoryDiscountAll(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput category() {
        return BaseOutputForm.success(ListUtil.listToTree(JSONArray.parseArray(JSON.toJSONString(customerLevelProjectDiscountConfigMapper.category(), JSONWriter.Feature.WriteMapNullValue)), "ID", "ParentID", "Child"));
    }


    public BaseOutput selectedCategory() {
        List<Integer> list = customerLevelProjectDiscountConfigMapper.selectedCategory();
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput createCustomerLevelCategoryDiscountConfig(List<CustomerLevelProjectCategoryDiscountConfigAddForm> form) {

        List<CustomerPriceProjectCategoryEntity> list = new ArrayList<>();
        for (CustomerLevelProjectCategoryDiscountConfigAddForm customerLevelProjectCategoryDiscountConfigAddForm : form) {
            CustomerPriceProjectCategoryEntity customerPriceProjectCategoryEntity = new CustomerPriceProjectCategoryEntity();
            customerPriceProjectCategoryEntity.CustomerLevelID = customerLevelProjectCategoryDiscountConfigAddForm.CustomerLevelID;
            customerPriceProjectCategoryEntity.ProjectCategoryID = customerLevelProjectCategoryDiscountConfigAddForm.ProjectCategoryID;
            list.add(customerPriceProjectCategoryEntity);
        }
        customerLevelProjectDiscountConfigMapper.createCustomerLevelCategoryDiscountConfig(list);
        return BaseOutput.success();
    }


    @Transactional
    public BaseOutput updateCustomerLevelCategoryDiscountConfig(CustomerLevelProjectCategoryDiscountConfigUpdateForm form) {

        customerLevelProjectDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByProjectCategoryID(form.ProjectCategoryID);

        List<CustomerPriceProjectCategoryEntity> list = new ArrayList<>();
        for (CustomerLevelProjectCategoryDiscountUpdateForm customerLevelProjectCategoryUpdateForm : form.CustomerLevelDiscountPriceList) {
            CustomerPriceProjectCategoryEntity customerPriceProjectCategoryEntity = new CustomerPriceProjectCategoryEntity();
            customerPriceProjectCategoryEntity.CustomerLevelID = customerLevelProjectCategoryUpdateForm.CustomerLevelID;
            customerPriceProjectCategoryEntity.ProjectCategoryID = form.ProjectCategoryID;
            if (customerLevelProjectCategoryUpdateForm.DiscountPrice != null){
                customerPriceProjectCategoryEntity.DiscountPrice = customerLevelProjectCategoryUpdateForm.DiscountPrice.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
            }
            list.add(customerPriceProjectCategoryEntity);
        }
        customerLevelProjectDiscountConfigMapper.createCustomerLevelCategoryDiscountConfig(list);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput batchUpdateCustomerLevelCategoryDiscountConfig(List<CustomerLevelProjectCategoryDiscountConfigUpdateForm> forms) {


        forms.forEach(form -> {
            customerLevelProjectDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByProjectCategoryID(form.ProjectCategoryID);

            List<CustomerPriceProjectCategoryEntity> list = new ArrayList<>();
            for (CustomerLevelProjectCategoryDiscountUpdateForm customerLevelProjectCategoryUpdateForm : form.CustomerLevelDiscountPriceList) {
                CustomerPriceProjectCategoryEntity customerPriceProjectCategoryEntity = new CustomerPriceProjectCategoryEntity();
                customerPriceProjectCategoryEntity.CustomerLevelID = customerLevelProjectCategoryUpdateForm.CustomerLevelID;
                customerPriceProjectCategoryEntity.ProjectCategoryID = form.ProjectCategoryID;
                if (customerLevelProjectCategoryUpdateForm.DiscountPrice != null){
                    customerPriceProjectCategoryEntity.DiscountPrice = customerLevelProjectCategoryUpdateForm.DiscountPrice.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
                }
                list.add(customerPriceProjectCategoryEntity);
            }
            customerLevelProjectDiscountConfigMapper.createCustomerLevelCategoryDiscountConfig(list);
        });

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput deleteCustomerLevelCategoryDiscountConfig(CustomerLevelProjectCategoryDiscountConfigDeleteForm form) {

        customerLevelProjectDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByProjectCategoryID(form.ProjectCategoryID);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput batchDeleteCustomerLevelCategoryDiscountConfig(List<CustomerLevelProjectCategoryDiscountConfigDeleteForm> forms) {

        forms.forEach(form -> {
            customerLevelProjectDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByProjectCategoryID(form.ProjectCategoryID);
        });
        return BaseOutput.success();
    }

    public BaseOutput project(CustomerLevelProjectDiscountPriceQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CustomerLevelDiscountPriceProjectAllOutputForm> list = customerLevelProjectDiscountConfigMapper.project(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput selectedProject() {
        List<Integer> list = customerLevelProjectDiscountConfigMapper.selectedProject();
        return BaseOutputForm.success(list);
    }

    public BaseOutput customerLevelProjectDiscountPriceAll(CustomerLevelProjectDiscountPriceConfigQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CustomerLevelDiscountPriceProjectConfigAllOutputForm> list = customerLevelProjectDiscountConfigMapper.customerLevelProjectDiscountPriceAll(form.Name);
        return BasePageInfo.success(list);
    }

    @Transactional
    public BaseOutput createCustomerLevelProjectDiscountPriceConfig(List<CustomerLevelProjectDiscountPriceConfigAddForm> form) {

        List<CustomerPriceProjectEntity> list = new ArrayList<>();
        for (CustomerLevelProjectDiscountPriceConfigAddForm customerLevelProjectDiscountPriceConfigAddForm : form) {
            CustomerPriceProjectEntity customerPriceProjectEntity = new CustomerPriceProjectEntity();
            customerPriceProjectEntity.CustomerLevelID = customerLevelProjectDiscountPriceConfigAddForm.CustomerLevelID;
            customerPriceProjectEntity.ProjectID = customerLevelProjectDiscountPriceConfigAddForm.ProjectID;

            list.add(customerPriceProjectEntity);
        }

        customerLevelProjectDiscountConfigMapper.createCustomerLevelProjectDiscountPriceConfig(list);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateCustomerLevelProjectDiscountConfig(CustomerLevelProjectCardDiscountConfigUpdateForm form) {
        customerLevelProjectDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByProjectID(form.ProjectID);

        List<CustomerPriceProjectEntity> list = new ArrayList<>();
        for (CustomerLevelProjectDiscountUpdateForm customerLevelProjectDiscountUpdateForm : form.CustomerLevelDiscountPriceList) {
            CustomerPriceProjectEntity customerPriceProjectEntity = new CustomerPriceProjectEntity();
            customerPriceProjectEntity.CustomerLevelID = customerLevelProjectDiscountUpdateForm.CustomerLevelID;
            customerPriceProjectEntity.ProjectID = form.ProjectID;
            customerPriceProjectEntity.PriceType = customerLevelProjectDiscountUpdateForm.PriceType;
            if (customerLevelProjectDiscountUpdateForm.DiscountPrice != null){
                if (customerPriceProjectEntity.PriceType.equals(1)) {
                    customerPriceProjectEntity.DiscountPrice = customerLevelProjectDiscountUpdateForm.DiscountPrice.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
                } else {
                    customerPriceProjectEntity.DiscountPrice = customerLevelProjectDiscountUpdateForm.DiscountPrice;
                }
            }

            list.add(customerPriceProjectEntity);
        }

        customerLevelProjectDiscountConfigMapper.createCustomerLevelProjectDiscountPriceConfig(list);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput batchUpdateCustomerLevelProjectDiscountConfig(List<CustomerLevelProjectCardDiscountConfigUpdateForm> forms) {

        forms.forEach(form -> {
            customerLevelProjectDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByProjectID(form.ProjectID);

            List<CustomerPriceProjectEntity> list = new ArrayList<>();
            for (CustomerLevelProjectDiscountUpdateForm customerLevelProjectDiscountUpdateForm : form.CustomerLevelDiscountPriceList) {
                CustomerPriceProjectEntity customerPriceProjectEntity = new CustomerPriceProjectEntity();
                customerPriceProjectEntity.CustomerLevelID = customerLevelProjectDiscountUpdateForm.CustomerLevelID;
                customerPriceProjectEntity.ProjectID = form.ProjectID;
                customerPriceProjectEntity.PriceType = customerLevelProjectDiscountUpdateForm.PriceType;
                if (customerLevelProjectDiscountUpdateForm.DiscountPrice != null){
                    if (customerPriceProjectEntity.PriceType.equals(1)) {
                        customerPriceProjectEntity.DiscountPrice = customerLevelProjectDiscountUpdateForm.DiscountPrice.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
                    } else {
                        customerPriceProjectEntity.DiscountPrice = customerLevelProjectDiscountUpdateForm.DiscountPrice;
                    }
                }

                list.add(customerPriceProjectEntity);
            }

            customerLevelProjectDiscountConfigMapper.createCustomerLevelProjectDiscountPriceConfig(list);
        });

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput deleteCustomerLevelProjectDiscountConfig(CustomerLevelProjectDiscountConfigDeleteForm form) {
        customerLevelProjectDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByProjectID(form.ProjectID);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput batchDeleteCustomerLevelProjectDiscountConfig(List<CustomerLevelProjectDiscountConfigDeleteForm> forms) {

        forms.forEach(form -> {
            customerLevelProjectDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByProjectID(form.ProjectID);
        });
        return BaseOutput.success();
    }
}
