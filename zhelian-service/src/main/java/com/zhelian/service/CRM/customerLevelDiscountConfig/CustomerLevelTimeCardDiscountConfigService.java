package com.zhelian.service.CRM.customerLevelDiscountConfig;


import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.CRM.CustomerLevelTimeCardDiscountConfigMapper;
import com.zhelian.model.CRM.entity.*;
import com.zhelian.model.CRM.form.input.*;
import com.zhelian.model.CRM.form.output.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static java.math.BigDecimal.ROUND_HALF_UP;

@Service
public class CustomerLevelTimeCardDiscountConfigService {

    @Autowired
    CustomerLevelTimeCardDiscountConfigMapper customerLevelTimeCardDiscountConfigMapper;

    public BaseOutput customerLevelDiscountAll() {
        List<CustomerLevelTimeCardCategoryDiscountOutputForm> list = customerLevelTimeCardDiscountConfigMapper.customerLevelDiscountAll();
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput createCustomerLevelDiscountConfig(List<CustomerLevelTimeCardDiscountConfigAddForm> form) {

        customerLevelTimeCardDiscountConfigMapper.deleteCustomerLevelDiscountConfig();
        List<CustomerPriceTimeCardSchemeEntity> list = new ArrayList<>();
        for (CustomerLevelTimeCardDiscountConfigAddForm customerLevelTimeCardDiscountConfigAddForm : form) {
            CustomerPriceTimeCardSchemeEntity customerPriceTimeCardSchemeEntity = new CustomerPriceTimeCardSchemeEntity();
            customerPriceTimeCardSchemeEntity.CustomerLevelID = customerLevelTimeCardDiscountConfigAddForm.CustomerLevelID;
            if (customerLevelTimeCardDiscountConfigAddForm.DiscountPrice != null){
                customerPriceTimeCardSchemeEntity.DiscountPrice = customerLevelTimeCardDiscountConfigAddForm.DiscountPrice.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
            }
            list.add(customerPriceTimeCardSchemeEntity);
        }
        customerLevelTimeCardDiscountConfigMapper.createCustomerLevelDiscountConfig(list);
        return BaseOutput.success();
    }


    public BaseOutput customerLevelCategoryDiscountAll(CustomerLevelTimeCardCategoryDiscountConfigQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CustomerLevelTimeCardCategoryDiscountConfigOutputForm> list = customerLevelTimeCardDiscountConfigMapper.customerLevelCategoryDiscountAll(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput category() {
        List<CustomerLevelDiscountPriceTimeCardCategoryOutputForm> list = customerLevelTimeCardDiscountConfigMapper.category();
        return BaseOutputForm.success(list);
    }

    public BaseOutput selectedCategory() {
        List<Integer> list = customerLevelTimeCardDiscountConfigMapper.selectedCategory();
        return BaseOutputForm.success(list);
    }
    
    @Transactional
    public BaseOutput createCustomerLevelCategoryDiscountConfig(List<CustomerLevelTimeCardCategoryDiscountConfigAddForm> form) {

        List<CustomerPriceTimeCardCategoryEntity> list = new ArrayList<>();
        for (CustomerLevelTimeCardCategoryDiscountConfigAddForm customerLevelTimeCardCategoryDiscountConfigAddForm : form) {
            CustomerPriceTimeCardCategoryEntity customerPriceTimeCardCategoryEntity = new CustomerPriceTimeCardCategoryEntity();
            customerPriceTimeCardCategoryEntity.CustomerLevelID = customerLevelTimeCardCategoryDiscountConfigAddForm.CustomerLevelID;
            customerPriceTimeCardCategoryEntity.TimeCardCategoryID = customerLevelTimeCardCategoryDiscountConfigAddForm.TimeCardCategoryID;
            list.add(customerPriceTimeCardCategoryEntity);
        }
        customerLevelTimeCardDiscountConfigMapper.createCustomerLevelCategoryDiscountConfig(list);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateCustomerLevelCategoryDiscountConfig(CustomerLevelTimeCardCategoryDiscountConfigUpdateForm form) {

        customerLevelTimeCardDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByTimeCardCategoryID(form.TimeCardCategoryID);

        List<CustomerPriceTimeCardCategoryEntity> list = new ArrayList<>();
        for (CustomerLevelTimeCardCategoryDiscountUpdateForm customerLevelTimeCardCategoryUpdateForm : form.CustomerLevelDiscountPriceList) {
            CustomerPriceTimeCardCategoryEntity customerPriceTimeCardCategoryEntity = new CustomerPriceTimeCardCategoryEntity();
            customerPriceTimeCardCategoryEntity.CustomerLevelID = customerLevelTimeCardCategoryUpdateForm.CustomerLevelID;
            customerPriceTimeCardCategoryEntity.TimeCardCategoryID = form.TimeCardCategoryID;
            if (customerLevelTimeCardCategoryUpdateForm.DiscountPrice != null){
                customerPriceTimeCardCategoryEntity.DiscountPrice = customerLevelTimeCardCategoryUpdateForm.DiscountPrice.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
            }
            list.add(customerPriceTimeCardCategoryEntity);
        }
        customerLevelTimeCardDiscountConfigMapper.createCustomerLevelCategoryDiscountConfig(list);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput batchUpdateCustomerLevelCategoryDiscountConfig(List<CustomerLevelTimeCardCategoryDiscountConfigUpdateForm> forms) {


        forms.forEach(form -> {
            customerLevelTimeCardDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByTimeCardCategoryID(form.TimeCardCategoryID);

            List<CustomerPriceTimeCardCategoryEntity> list = new ArrayList<>();
            for (CustomerLevelTimeCardCategoryDiscountUpdateForm customerLevelTimeCardCategoryUpdateForm : form.CustomerLevelDiscountPriceList) {
                CustomerPriceTimeCardCategoryEntity customerPriceTimeCardCategoryEntity = new CustomerPriceTimeCardCategoryEntity();
                customerPriceTimeCardCategoryEntity.CustomerLevelID = customerLevelTimeCardCategoryUpdateForm.CustomerLevelID;
                customerPriceTimeCardCategoryEntity.TimeCardCategoryID = form.TimeCardCategoryID;
                if (customerLevelTimeCardCategoryUpdateForm.DiscountPrice != null){
                    customerPriceTimeCardCategoryEntity.DiscountPrice = customerLevelTimeCardCategoryUpdateForm.DiscountPrice.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
                }
                list.add(customerPriceTimeCardCategoryEntity);
            }
            customerLevelTimeCardDiscountConfigMapper.createCustomerLevelCategoryDiscountConfig(list);
        });

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput deleteCustomerLevelCategoryDiscountConfig(CustomerLevelTimeCardCategoryDiscountConfigDeleteForm form) {

        customerLevelTimeCardDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByTimeCardCategoryID(form.TimeCardCategoryID);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput batchDeleteCustomerLevelCategoryDiscountConfig(List<CustomerLevelTimeCardCategoryDiscountConfigDeleteForm> forms) {

        forms.forEach(form -> {
            customerLevelTimeCardDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByTimeCardCategoryID(form.TimeCardCategoryID);
        });
        return BaseOutput.success();
    }

    public BaseOutput timeCard(CustomerLevelTimeCardDiscountPriceQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CustomerLevelDiscountPriceTimeCardAllOutputForm> list = customerLevelTimeCardDiscountConfigMapper.timeCard(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput selectedTimeCard() {
        List<Integer> list = customerLevelTimeCardDiscountConfigMapper.selectedTimeCard();
        return BaseOutputForm.success(list);
    }

    public BaseOutput customerLevelTimeCardDiscountPriceAll(CustomerLevelTimeCardDiscountPriceConfigQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CustomerLevelDiscountPriceTimeCardConfigAllOutputForm> list = customerLevelTimeCardDiscountConfigMapper.customerLevelTimeCardDiscountPriceAll(form.Name);
        return BasePageInfo.success(list);
    }

    @Transactional
    public BaseOutput createCustomerLevelTimeCardDiscountPriceConfig(List<CustomerLevelTimeCardDiscountPriceConfigAddForm> form) {

        List<CustomerPriceTimeCardEntity> list = new ArrayList<>();
        for (CustomerLevelTimeCardDiscountPriceConfigAddForm customerLevelTimeCardDiscountPriceConfigAddForm : form) {
            CustomerPriceTimeCardEntity customerPriceTimeCardEntity = new CustomerPriceTimeCardEntity();
            customerPriceTimeCardEntity.CustomerLevelID = customerLevelTimeCardDiscountPriceConfigAddForm.CustomerLevelID;
            customerPriceTimeCardEntity.TimeCardID = customerLevelTimeCardDiscountPriceConfigAddForm.TimeCardID;

            list.add(customerPriceTimeCardEntity);
        }

        customerLevelTimeCardDiscountConfigMapper.createCustomerLevelTimeCardDiscountPriceConfig(list);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateCustomerLevelTimeCardDiscountConfig(CustomerLevelTimeCardCardDiscountConfigUpdateForm form) {
        customerLevelTimeCardDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByTimeCardID(form.TimeCardID);

        List<CustomerPriceTimeCardEntity> list = new ArrayList<>();
        for (CustomerLevelTimeCardDiscountUpdateForm customerLevelTimeCardDiscountUpdateForm : form.CustomerLevelDiscountPriceList) {
            CustomerPriceTimeCardEntity customerPriceTimeCardEntity = new CustomerPriceTimeCardEntity();
            customerPriceTimeCardEntity.CustomerLevelID = customerLevelTimeCardDiscountUpdateForm.CustomerLevelID;
            customerPriceTimeCardEntity.TimeCardID = form.TimeCardID;
            customerPriceTimeCardEntity.PriceType = customerLevelTimeCardDiscountUpdateForm.PriceType;
            if (customerLevelTimeCardDiscountUpdateForm.DiscountPrice != null){
                if (customerPriceTimeCardEntity.PriceType.equals(1)) {
                    customerPriceTimeCardEntity.DiscountPrice = customerLevelTimeCardDiscountUpdateForm.DiscountPrice.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
                } else {
                    customerPriceTimeCardEntity.DiscountPrice = customerLevelTimeCardDiscountUpdateForm.DiscountPrice;
                }
            }

            list.add(customerPriceTimeCardEntity);
        }

        customerLevelTimeCardDiscountConfigMapper.createCustomerLevelTimeCardDiscountPriceConfig(list);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput batchUpdateCustomerLevelTimeCardDiscountConfig(List<CustomerLevelTimeCardCardDiscountConfigUpdateForm> forms) {

        forms.forEach(form -> {
            customerLevelTimeCardDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByTimeCardID(form.TimeCardID);

            List<CustomerPriceTimeCardEntity> list = new ArrayList<>();
            for (CustomerLevelTimeCardDiscountUpdateForm customerLevelTimeCardDiscountUpdateForm : form.CustomerLevelDiscountPriceList) {
                CustomerPriceTimeCardEntity customerPriceTimeCardEntity = new CustomerPriceTimeCardEntity();
                customerPriceTimeCardEntity.CustomerLevelID = customerLevelTimeCardDiscountUpdateForm.CustomerLevelID;
                customerPriceTimeCardEntity.TimeCardID = form.TimeCardID;
                customerPriceTimeCardEntity.PriceType = customerLevelTimeCardDiscountUpdateForm.PriceType;
                if (customerLevelTimeCardDiscountUpdateForm.DiscountPrice != null){
                    if (customerPriceTimeCardEntity.PriceType.equals(1)) {
                        customerPriceTimeCardEntity.DiscountPrice = customerLevelTimeCardDiscountUpdateForm.DiscountPrice.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
                    } else {
                        customerPriceTimeCardEntity.DiscountPrice = customerLevelTimeCardDiscountUpdateForm.DiscountPrice;
                    }
                }

                list.add(customerPriceTimeCardEntity);
            }

            customerLevelTimeCardDiscountConfigMapper.createCustomerLevelTimeCardDiscountPriceConfig(list);
        });

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput deleteCustomerLevelTimeCardDiscountConfig(CustomerLevelTimeCardDiscountConfigDeleteForm form) {
        customerLevelTimeCardDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByTimeCardID(form.TimeCardID);
        return BaseOutput.success();
    }


    @Transactional
    public BaseOutput batchDeleteCustomerLevelTimeCardDiscountConfig(List<CustomerLevelTimeCardDiscountConfigDeleteForm> forms) {

        forms.forEach(form -> {
            customerLevelTimeCardDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByTimeCardID(form.TimeCardID);
        });
        return BaseOutput.success();
    }
}
