package com.zhelian.service.CRM.followUpStatus;

import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.CRM.FollowUpStatusMapper;
import com.zhelian.model.CRM.entity.FollowUpStatusEntity;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.PayMethodAddForm;
import com.zhelian.model.ibeauty.form.input.PayMethodMoveForm;
import com.zhelian.model.ibeauty.form.input.PayMethodQueryForm;
import com.zhelian.model.ibeauty.form.input.PayMethodUpdateForm;
import com.zhelian.model.ibeauty.form.output.PayMethodOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;


@Service
public class FollowUpStatusService {
    @Autowired
    FollowUpStatusMapper followUpStatusMapper;

    public BaseOutput all(PayMethodQueryForm form) {
        List<PayMethodOutputForm> list = followUpStatusMapper.followUpStatusList(form.Name, form.Active);
        return BaseOutputForm.success(list);
    }

    public BaseOutput create(PayMethodAddForm form) {
        Integer Sequence = followUpStatusMapper.getMaxSequence();
        FollowUpStatusEntity followUpStatusEntity = new FollowUpStatusEntity();
        followUpStatusEntity.Name = form.Name;
        followUpStatusEntity.Sequence = Sequence != null ? Sequence + 1 : 1;
        followUpStatusMapper.createFollowUpStatus(followUpStatusEntity);
        return BaseOutput.success();
    }

    public BaseOutput update(PayMethodUpdateForm form) {
        FollowUpStatusEntity followUpStatusEntity = new FollowUpStatusEntity();
        followUpStatusEntity.ID = form.ID;
        followUpStatusEntity.Name = form.Name;
        followUpStatusEntity.Active = form.Active;
        followUpStatusMapper.updateFollowUpStatus(followUpStatusEntity);

        return BaseOutput.success();
    }

    public BaseOutput move(PayMethodMoveForm form) {
        //重新排序
        LinkedList<Integer> linkedList = followUpStatusMapper.getIDOrderBySequenceASC(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<FollowUpStatusEntity> list = new ArrayList<>();

        for (Integer ID : linkedList) {
            FollowUpStatusEntity followUpStatusEntity = new FollowUpStatusEntity();
            followUpStatusEntity.ID = ID;
            followUpStatusEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(followUpStatusEntity);
        }
        followUpStatusMapper.updateSequence(list);
        return BaseOutput.success();
    }

}