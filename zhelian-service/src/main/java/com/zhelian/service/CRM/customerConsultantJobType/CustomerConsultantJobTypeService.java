package com.zhelian.service.CRM.customerConsultantJobType;

import com.zhelian.mapper.CRM.CustomerConsultantJobTypeMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.CustomerConsultantJobTypeEntity;
import com.zhelian.model.ibeauty.form.input.CustomerConsultantJobTypeAddForm;
import com.zhelian.model.ibeauty.form.input.CustomerConsultantJobTypeDeleteForm;
import com.zhelian.model.ibeauty.form.output.CustomerConsultantJobTypeOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class CustomerConsultantJobTypeService {

    @Autowired
    CustomerConsultantJobTypeMapper customerConsultantJobTypeMapper;

    public BaseOutput allCustomerConsultantJobType() {
        List<CustomerConsultantJobTypeOutputForm> list = customerConsultantJobTypeMapper.allCustomerConsultantJobType();
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateCustomerConsultantJobTypeSale(CustomerConsultantJobTypeAddForm form) {

        customerConsultantJobTypeMapper.deleteAllCustomerConsultantJobTypeSale();

        List<CustomerConsultantJobTypeEntity> list = new ArrayList<>();
        if (form.JobTypeID != null && form.JobTypeID.length > 0) {
            for (Integer typeID : form.JobTypeID) {
                CustomerConsultantJobTypeEntity customerConsultantJobTypeEntity = new CustomerConsultantJobTypeEntity();
                customerConsultantJobTypeEntity.JobTypeID = typeID;
                list.add(customerConsultantJobTypeEntity);
            }
            customerConsultantJobTypeMapper.updateCustomerConsultantJobTypeSale(list);
        }

        return BaseOutput.success();
    }

    public BaseOutput deleteCustomerConsultantJobTypeSale(CustomerConsultantJobTypeDeleteForm form) {
        customerConsultantJobTypeMapper.deleteCustomerConsultantJobTypeSale(form.JobTypeID);
        return BaseOutput.success();
    }
}



