package com.zhelian.service.CRM.artSpeak;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.CRM.ArtSpeakCategoryMapper;
import com.zhelian.model.CRM.entity.ArtSpeakCategoryEntity;
import com.zhelian.model.CRM.form.input.ArtSpeakCategoryAddForm;
import com.zhelian.model.CRM.form.input.ArtSpeakCategoryMoveForm;
import com.zhelian.model.CRM.form.input.ArtSpeakCategoryQueryForm;
import com.zhelian.model.CRM.form.input.ArtSpeakCategoryUpdateForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.output.TopProjectCategoryOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import static com.zhelian.core.utils.ListUtil.listToTree;

@Service
public class ArtSpeakCategoryService {

    @Autowired
    ArtSpeakCategoryMapper artSpeakCategoryMapper;

    public BaseOutput artSpeakCategoryList(ArtSpeakCategoryQueryForm form) {
        List<ArtSpeakCategoryEntity> list = artSpeakCategoryMapper.artSpeakCategoryList(form.Name, form.Active);
        JSONArray result = listToTree(JSONArray.parseArray(JSON.toJSONString(list, JSONWriter.Feature.WriteMapNullValue)), "ID", "ParentID", "Child");
        return BaseOutputForm.success(result);
    }

    public BaseOutput getValidArtSpeakCategoryValid() {
        List<ArtSpeakCategoryEntity> list = artSpeakCategoryMapper.getValidArtSpeakCategoryValid();
        JSONArray result = listToTree(JSONArray.parseArray(JSON.toJSONString(list, JSONWriter.Feature.WriteMapNullValue)), "ID", "ParentID", "Child");
        return BaseOutputForm.success(result);
    }

    public BaseOutput getTopArtSpeakList() {
        List<TopProjectCategoryOutputForm> list = artSpeakCategoryMapper.getTopArtSpeakList();
        return BaseOutputForm.success(list);
    }

    public BaseOutput addArtSpeakCategory(ArtSpeakCategoryAddForm form) {
        Integer Sequence = artSpeakCategoryMapper.getMaxSequence();
        ArtSpeakCategoryEntity artSpeakCategoryEntity = new ArtSpeakCategoryEntity();
        if (form.ParentID != 0) {
            ArtSpeakCategoryEntity artSpeakCategoryEntity1 = artSpeakCategoryMapper.getArtSpeakCategoryEntity(form.ParentID);
            if (artSpeakCategoryEntity1 != null && artSpeakCategoryEntity1.ParentID != 0) {
                return BaseOutput.failed("上级分类不属于一级分类");
            }
        }
        artSpeakCategoryEntity.ParentID = form.ParentID;
        artSpeakCategoryEntity.Name = form.Name;
        artSpeakCategoryEntity.Sequence = Sequence != null ? Sequence + 1 : 1;
        artSpeakCategoryMapper.addArtSpeakCategory(artSpeakCategoryEntity);
        return BaseOutput.success();
    }

    public BaseOutput updateArtSpeakCategory(ArtSpeakCategoryUpdateForm form) {
        if (form.Active == false) {
            if (form.ParentID == 0) {
                Integer childCount = artSpeakCategoryMapper.childActiveCount(form.ID);
                if (childCount > 0)
                    return BaseOutput.failed("该分类下的子类为有效，不可以改为无效");
            } else {
                Integer count = artSpeakCategoryMapper.artSpeakByCategoryCount(form.ID);
                if (count > 0)
                    return BaseOutput.failed("该分类下有话术，不可以改为无效");
            }
        } else {
            if (form.ParentID != 0) {
                ArtSpeakCategoryEntity artSpeakCategoryEntity = artSpeakCategoryMapper.getArtSpeakCategoryEntity(form.ParentID);
                if (artSpeakCategoryEntity.Active == false)
                    return BaseOutput.failed("该分类的父集为无效，不可以改为有效");
            }

        }


        ArtSpeakCategoryEntity artSpeakCategoryEntity = new ArtSpeakCategoryEntity();

        artSpeakCategoryEntity.ID = form.ID;
        artSpeakCategoryEntity.Name = form.Name;
        artSpeakCategoryEntity.Active = form.Active;
        artSpeakCategoryEntity.ParentID = form.ParentID;

        artSpeakCategoryMapper.updateArtSpeakCategory(artSpeakCategoryEntity);
        return BaseOutput.success();
    }

    public BaseOutput move(ArtSpeakCategoryMoveForm form) {

        //重新排序
        LinkedList<Integer> linkedList = artSpeakCategoryMapper.getASCList(form.MoveID, form.DestParentID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<ArtSpeakCategoryEntity> list = new ArrayList<>();
        for (Integer ID : linkedList) {
            ArtSpeakCategoryEntity artSpeakCategoryEntity = new ArtSpeakCategoryEntity();
            artSpeakCategoryEntity.ID = ID;
            artSpeakCategoryEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(artSpeakCategoryEntity);
        }
        artSpeakCategoryMapper.updateSequence(list);
        return BaseOutput.success();
    }

}
