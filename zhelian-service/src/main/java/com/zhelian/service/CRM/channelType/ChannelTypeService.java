package com.zhelian.service.CRM.channelType;

import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.CRM.ChannelTypeMapper;
import com.zhelian.model.CRM.entity.ChannelTypeEntity;
import com.zhelian.model.CRM.form.input.ChannelTypeAddForm;
import com.zhelian.model.CRM.form.input.ChannelTypeMoveForm;
import com.zhelian.model.CRM.form.input.ChannelTypeQueryForm;
import com.zhelian.model.CRM.form.input.ChannelTypeUpdateForm;
import com.zhelian.model.CRM.form.output.ChannelTypeOutputForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;


@Service
public class ChannelTypeService {

    @Autowired
    ChannelTypeMapper channelTypeMapper;


    public BaseOutput create(ChannelTypeAddForm form) {
        Integer Sequence = channelTypeMapper.getMaxSequence();
        ChannelTypeEntity entity = new ChannelTypeEntity();
        entity.Name = form.Name;
        entity.Active = form.Active;
        entity.Sequence = Sequence != null ? Sequence + 1 : 1;
        channelTypeMapper.create(entity);
        return BaseOutput.success();
    }

    public BaseOutput updateChannelType(ChannelTypeUpdateForm form) {
        if (form.Active == false) {
            Integer count = channelTypeMapper.channelByChannelTypeCount(form.ID);
            if (count > 0)
                return BaseOutput.failed("该类型下存在渠道正在使用，不可以改为无效");
        }
        ChannelTypeEntity entity = new ChannelTypeEntity();
        entity.ID = form.ID;
        entity.Name = form.Name;
        entity.Active = form.Active;
        channelTypeMapper.updateChannelType(entity);
        return BaseOutput.success();
    }

    public BaseOutput list(ChannelTypeQueryForm form) {
        return BaseOutputForm.success(channelTypeMapper.getPageList(form));
    }

    public BaseOutput move(ChannelTypeMoveForm form) {
        //重新排序
        LinkedList<Integer> linkedList = channelTypeMapper.getIDOrderBySequenceASC(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<ChannelTypeEntity> list = new ArrayList<>();

        for (Integer ID : linkedList) {
            ChannelTypeEntity entity = new ChannelTypeEntity();
            entity.ID = ID;
            entity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(entity);
        }
        channelTypeMapper.updateSequence(list);
        return BaseOutput.success();
    }

    public BaseOutput all(ChannelTypeQueryForm form) {
        List<ChannelTypeOutputForm> pageList = channelTypeMapper.all(form);
        return BaseOutputForm.success(pageList);
    }

}
