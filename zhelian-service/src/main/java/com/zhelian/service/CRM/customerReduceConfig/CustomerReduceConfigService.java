package com.zhelian.service.CRM.customerReduceConfig;


import com.zhelian.mapper.CRM.CustomerLevelMapper;
import com.zhelian.mapper.CRM.CustomerReduceConfigMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.CustomerReduceConfigEntity;
import com.zhelian.model.ibeauty.form.input.CustomerReduceConfigAddForm;
import com.zhelian.model.ibeauty.form.output.CustomerReduceConfigOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;


@Service
public class CustomerReduceConfigService {

    @Autowired
    CustomerReduceConfigMapper customerReduceConfigMapper;

    @Autowired
    CustomerLevelMapper customerLevelMapper;

    public BaseOutput all() {
        CustomerReduceConfigOutputForm customerReduceConfigEntity = customerReduceConfigMapper.getCustomerReduceConfig();
        return BaseOutputForm.success(customerReduceConfigEntity);
    }


    @Transactional
    public BaseOutput update(CustomerReduceConfigAddForm form) {
        CustomerReduceConfigEntity customerReduceConfig = customerReduceConfigMapper.getCustomerReduceConfigEntity();
        if (form.IsReduce != customerReduceConfig.IsReduce || form.ReduceRule != customerReduceConfig.ReduceRule || form.ReduceMode != customerReduceConfig.ReduceMode || form.VaildModeDay != customerReduceConfig.VaildModeDay || form.RegularModeType != customerReduceConfig.RegularModeType || form.RegularModeMonth != customerReduceConfig.RegularModeMonth) {
            customerLevelMapper.updateCustomerLevelSetting(true);

        }
        customerReduceConfig.IsReduce = form.IsReduce;
        customerReduceConfig.ReduceRule = form.ReduceRule;
        customerReduceConfig.ReduceMode = form.ReduceMode;
        customerReduceConfig.VaildModeDay = form.VaildModeDay;
        customerReduceConfig.RegularModeType = form.RegularModeType;
        customerReduceConfig.RegularModeMonth = form.RegularModeMonth;
        if (form.RegularModeType.equals("10")) {
            customerReduceConfig.RegularModeUpdateDate = addMonth(1);
        } else if (form.RegularModeType.equals("20")) {
            customerReduceConfig.RegularModeUpdateDate = addMonth(3);
        } else if (form.RegularModeType.equals("30")) {
            customerReduceConfig.RegularModeUpdateDate = addMonth(6);
        } else if (form.RegularModeType.equals("40")) {
            customerReduceConfig.RegularModeUpdateDate = addMonth(12);
        }
        customerReduceConfigMapper.updateCustomerReduceConfig(customerReduceConfig);
        return BaseOutput.success();

    }


    public Date addMonth(Integer i) {
        if (i == 6) {
            Calendar c = Calendar.getInstance();
            int month = c.get(Calendar.MONTH) + 1;
            int year = c.get(Calendar.YEAR);
            if (month > 6) {
                year = c.get(Calendar.YEAR) + 1;
            }
            try {
                c.set(year, 6, 1);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return c.getTime();
        }
        if (i == 12) {
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-01-01");
            Calendar c = Calendar.getInstance();
            try {
                c.setTime(sf.parse(sf.format(new Date())));
            } catch (ParseException e) {
                e.printStackTrace();
            }
            c.add(Calendar.YEAR, 1);
            return c.getTime();
        }
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-01");
        Calendar c = Calendar.getInstance();
        try {
            c.setTime(sf.parse(sf.format(new Date())));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        c.add(Calendar.MONTH, i);
        return c.getTime();
    }
}