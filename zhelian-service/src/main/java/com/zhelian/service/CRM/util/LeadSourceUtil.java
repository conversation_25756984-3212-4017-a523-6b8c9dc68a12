/**
 * <AUTHOR>
 * @date 2025-01-15
 * @description 线索来源工具类
 * @version 1.0
 */
package com.zhelian.service.CRM.util;

import com.zhelian.model.CRM.enums.LeadSourceEnum;

/**
 * 线索来源工具类
 * 提供线索来源相关的工具方法
 */
public class LeadSourceUtil {
    
    /**
     * 验证线索来源是否有效
     * @param leadSource 线索来源代码
     * @return 是否有效
     */
    public static boolean isValidLeadSource(String leadSource) {
        return LeadSourceEnum.isValidCode(leadSource);
    }
    
    /**
     * 获取线索来源描述
     * @param leadSource 线索来源代码
     * @return 线索来源描述，如果无效返回"未知来源"
     */
    public static String getLeadSourceDescription(String leadSource) {
        LeadSourceEnum leadSourceEnum = LeadSourceEnum.getByCode(leadSource);
        return leadSourceEnum != null ? leadSourceEnum.getDescription() : "未知来源";
    }
    
    /**
     * 根据接口类型自动判断线索来源
     * @param apiType 接口类型标识
     * @return 对应的线索来源代码
     */
    public static String getLeadSourceByApiType(String apiType) {
        if (apiType == null || apiType.trim().isEmpty()) {
            return LeadSourceEnum.ZIRAN_DAODIAN.getCode();
        }

        switch (apiType.toUpperCase()) {
            case "DOUYIN_XINXILIU":
            case "抖音信息流":
                return LeadSourceEnum.DOUYIN_XINXILIU.getCode();
            case "DOUYIN_TUANGOU":
            case "抖音团购":
                return LeadSourceEnum.DOUYIN_TUANGOU.getCode();
            case "BENDI_XIANSUO_TUIGUANG":
            case "本地线索推广":
                return LeadSourceEnum.BENDI_XIANSUO_TUIGUANG.getCode();
            case "MEITUAN_DIANPING":
            case "美团点评":
                return LeadSourceEnum.MEITUAN_DIANPING.getCode();
            case "BENDI_SHENGHUO_ZHIBO":
            case "本地生活直播":
                return LeadSourceEnum.BENDI_SHENGHUO_ZHIBO.getCode();
            case "DOUYIN_BENDITUISIXIN":
            case "抖音本地推-私信":
                return LeadSourceEnum.DOUYIN_BENDITUISIXIN.getCode();
            case "WEIXIN_PENGYOUQUAN":
            case "微信朋友圈":
                return LeadSourceEnum.WEIXIN_PENGYOUQUAN.getCode();
            case "DOUYIN_ZAIXIANZIXUN":
            case "抖音-在线咨询":
                return LeadSourceEnum.DOUYIN_ZAIXIANZIXUN.getCode();
            case "GAODE_DITU":
            case "高德地图":
                return LeadSourceEnum.GAODE_DITU.getCode();
            case "XIAOHONGSHU":
            case "小红书":
                return LeadSourceEnum.XIAOHONGSHU.getCode();
            case "ZIRAN_DAODIAN":
            case "自然到店":
                return LeadSourceEnum.ZIRAN_DAODIAN.getCode();
            case "LAO_DAI_XIN":
            case "老带新":
                return LeadSourceEnum.LAO_DAI_XIN.getCode();
            default:
                return LeadSourceEnum.ZIRAN_DAODIAN.getCode();
        }
    }
    
    /**
     * 设置默认线索来源（如果为空）
     * @param leadSource 原线索来源
     * @param defaultSource 默认线索来源
     * @return 最终的线索来源
     */
    public static String setDefaultLeadSource(String leadSource, LeadSourceEnum defaultSource) {
        if (leadSource == null || leadSource.trim().isEmpty()) {
            return defaultSource.getCode();
        }
        
        // 验证是否有效，无效则使用默认值
        if (!isValidLeadSource(leadSource)) {
            return defaultSource.getCode();
        }
        
        return leadSource;
    }
    
    /**
     * 设置默认线索来源为自然到店
     * @param leadSource 原线索来源
     * @return 最终的线索来源
     */
    public static String setDefaultLeadSource(String leadSource) {
        return setDefaultLeadSource(leadSource, LeadSourceEnum.ZIRAN_DAODIAN);
    }
}
