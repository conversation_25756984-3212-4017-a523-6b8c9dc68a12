package com.zhelian.service.CRM.followUp;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.autoNumber.AutoNumberUtils;
import com.zhelian.core.utils.DateTimeUtil;
import com.zhelian.core.utils.OSSClientUtil;
import com.zhelian.core.utils.YingXiaoYunUtil;
import com.zhelian.core.utils.enumeration.BillChannel;
import com.zhelian.mapper.CRM.CustomerMapper;
import com.zhelian.mapper.CRM.FollowUpMapper;
import com.zhelian.mapper.ibeauty.AppointmentBillMapper;
import com.zhelian.mapper.khs.EmployeeMapper;
import com.zhelian.mapper.yingxiaoyun.YingXiaoYunAppointmentBillMapper;
import com.zhelian.mapper.yingxiaoyun.YingXiaoYunTenantMapper;
import com.zhelian.model.CRM.entity.FollowUpRecordAttachmentEntity;
import com.zhelian.model.CRM.entity.FollowUpRecordEntity;
import com.zhelian.model.CRM.form.input.*;
import com.zhelian.model.CRM.form.output.FollowUpRecordOutputForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.AppointmentBillEntity;
import com.zhelian.model.ibeauty.entity.AppointmentBillProcessEntity;
import com.zhelian.model.ibeauty.entity.CustomerEntity;
import com.zhelian.model.ibeauty.entity.CustomerServicerEntity;
import com.zhelian.model.khs.entity.EmployeeEntity;
import com.zhelian.model.yingxiaoyun.entity.YingXiaoYunCustomerEntity;
import com.zhelian.model.yingxiaoyun.entity.YingXiaoYunTenantEntity;
import com.zhelian.model.yingxiaoyun.form.output.YXYAttachmentOutPutForm;
import com.zhelian.model.yingxiaoyun.form.output.YXYFollowUpListOutPutForm;
import com.zhelian.model.yingxiaoyun.form.output.YXYFollowUpOutPutForm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Service
public class FollowUpService {

    @Autowired
    FollowUpMapper followUpMapper;
    @Autowired
    CustomerMapper customerMapper;
    @Autowired
    EmployeeMapper employeeMapper;
    @Autowired
    YingXiaoYunTenantMapper yingXiaoYunTenantMapper;
    @Autowired
    YingXiaoYunAppointmentBillMapper yingXiaoYunAppointmentBillMapper;
    @Autowired
    AppointmentBillMapper appointmentBillMapper;

    private static final Logger logger = LoggerFactory.getLogger(FollowUpService.class);

    public BaseOutput list(FollowUpRecordQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<FollowUpRecordOutputForm> list = followUpMapper.followUpList(SystemUserUtil.getSystemUserID(), form.Name, form.IsFollowUp, form.FollowUpMethodID, form.FollowUpStatusID, form.StartDate, form.EndDate, form.IsShowOwnFollowUp, form.FollowUpBy, form.FollowUpEntity);
        return BasePageInfo.success(list);
    }

    public BaseOutput all(FollowUpRecordQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<FollowUpRecordOutputForm> list = followUpMapper.allFollowUp(SystemUserUtil.getSystemUserID(), form.Name, form.IsFollowUp, form.FollowUpMethodID, form.FollowUpStatusID, form.StartDate, form.EndDate, form.IsShowOwnFollowUp != null && form.IsShowOwnFollowUp == true ? true : null, form.FollowUpBy, form.FollowUpEntity,form.CustomerLevelID,form.ChannelName);
        return BasePageInfo.success(list);
    }

    @Transactional
    public BaseOutput create(FollowUpRecordAddForm form) throws Exception {
        FollowUpRecordEntity followUpRecordEntity = new FollowUpRecordEntity();
        followUpRecordEntity.CustomerID = form.CustomerID;
        followUpRecordEntity.FollowUpMethodID = form.FollowUpMethodID;
        followUpRecordEntity.FollowUpStatusID = form.FollowUpStatusID;
        followUpRecordEntity.FollowUpContent = form.FollowUpContent;
        followUpRecordEntity.IsFollowUp = true;
        followUpRecordEntity.PlannedOn = new Date();
        followUpRecordEntity.PlannedRemark = "";
        followUpRecordEntity.FollowUpOn = new Date();
        followUpRecordEntity.FollowUpBy = SystemUserUtil.getSystemUserID();
        followUpRecordEntity.CreatedOn = new Date();
        followUpRecordEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        followUpRecordEntity.LeadSource = form.LeadSource;
        followUpMapper.createFollowUpRecord(followUpRecordEntity);

        List<FollowUpRecordAttachmentEntity> list = new ArrayList<>();
        if (form.Attachment != null && form.Attachment.size() > 0) {
            for (FollowUpRecordAttachmentAddForm followUpRecordAttachmentAddForm : form.Attachment) {
                FollowUpRecordAttachmentEntity followUpRecordAttachmentEntity = new FollowUpRecordAttachmentEntity();
                followUpRecordAttachmentEntity.FollowUpRecordID = followUpRecordEntity.ID;
                followUpRecordAttachmentEntity.AttachmentURL = followUpRecordAttachmentAddForm.AttachmentURL;
                followUpRecordAttachmentEntity.AttachmentType = followUpRecordAttachmentAddForm.AttachmentType;
                list.add(followUpRecordAttachmentEntity);
            }

            followUpMapper.createFollowUpRecordAttachment(list);
        }
        if (form.IsNextFollowUp) {
            FollowUpRecordEntity nextFollowUpRecordEntity = new FollowUpRecordEntity();
            nextFollowUpRecordEntity.CustomerID = form.CustomerID;
            nextFollowUpRecordEntity.FollowUpContent = "";
            nextFollowUpRecordEntity.IsFollowUp = false;
            nextFollowUpRecordEntity.PlannedOn = DateTimeUtil.toDate(form.PlannedOn);
            nextFollowUpRecordEntity.PlannedRemark = form.PlannedRemark;
            nextFollowUpRecordEntity.FollowUpBy = SystemUserUtil.getSystemUserID();
            nextFollowUpRecordEntity.CreatedOn = new Date();
            nextFollowUpRecordEntity.CreatedBy = SystemUserUtil.getSystemUserID();
            followUpMapper.createFollowUpRecord(nextFollowUpRecordEntity);
        }

        send(followUpRecordEntity, list);
        return BaseOutput.success();
    }


    @Transactional
    public BaseOutput followUp(FollowUpRecordUpdateForm form) throws Exception {
        FollowUpRecordEntity followUpRecordEntity = followUpMapper.getFollowUpRecord(form.ID);
        followUpRecordEntity.FollowUpMethodID = form.FollowUpMethodID;
        followUpRecordEntity.FollowUpStatusID = form.FollowUpStatusID;
        followUpRecordEntity.FollowUpContent = form.FollowUpContent;
        followUpRecordEntity.IsFollowUp = true;
        followUpRecordEntity.FollowUpOn = new Date();

        followUpMapper.updateFollowUpRecord(followUpRecordEntity);

        List<FollowUpRecordAttachmentEntity> list = new ArrayList<>();
        if (form.Attachment != null && form.Attachment.size() > 0) {
            for (FollowUpRecordAttachmentAddForm followUpRecordAttachmentAddForm : form.Attachment) {
                FollowUpRecordAttachmentEntity followUpRecordAttachmentEntity = new FollowUpRecordAttachmentEntity();
                followUpRecordAttachmentEntity.FollowUpRecordID = followUpRecordEntity.ID;
                followUpRecordAttachmentEntity.AttachmentURL = followUpRecordAttachmentAddForm.AttachmentURL;
                followUpRecordAttachmentEntity.AttachmentType = followUpRecordAttachmentAddForm.AttachmentType;
                list.add(followUpRecordAttachmentEntity);
            }

            followUpMapper.createFollowUpRecordAttachment(list);
        }
        if (form.IsNextFollowUp) {
            FollowUpRecordEntity nextFollowUpRecordEntity = new FollowUpRecordEntity();
            nextFollowUpRecordEntity.CustomerID = form.CustomerID;
            nextFollowUpRecordEntity.FollowUpContent = "";
            nextFollowUpRecordEntity.IsFollowUp = false;
            nextFollowUpRecordEntity.PlannedOn = DateTimeUtil.toDate(form.PlannedOn);
            nextFollowUpRecordEntity.PlannedRemark = form.PlannedRemark;
            nextFollowUpRecordEntity.FollowUpBy = SystemUserUtil.getSystemUserID();
            nextFollowUpRecordEntity.CreatedOn = new Date();
            nextFollowUpRecordEntity.CreatedBy = SystemUserUtil.getSystemUserID();
            followUpMapper.createFollowUpRecord(nextFollowUpRecordEntity);
        }

        send(followUpRecordEntity, list);
        return BaseOutput.success();
    }


    @Transactional
    public BaseOutput update(FollowUpRecordUpdateForm form) {
        FollowUpRecordEntity followUpRecordEntity = followUpMapper.getFollowUpRecord(form.ID);
        followUpRecordEntity.FollowUpMethodID = form.FollowUpMethodID;
        followUpRecordEntity.FollowUpStatusID = form.FollowUpStatusID;
        followUpRecordEntity.FollowUpContent = form.FollowUpContent;

        followUpMapper.updateFollowUpRecord(followUpRecordEntity);

        List<FollowUpRecordAttachmentEntity> followUpAttachment = followUpMapper.getFollowUpAttachment(form.ID);
        if (followUpAttachment.size() > 0) {
            followUpMapper.deleteFollowUpAttachment(form.ID);
        }

        List<FollowUpRecordAttachmentEntity> list = new ArrayList<>();
        if (form.Attachment != null && form.Attachment.size() > 0) {
            for (FollowUpRecordAttachmentAddForm followUpRecordAttachmentAddForm : form.Attachment) {
                FollowUpRecordAttachmentEntity followUpRecordAttachmentEntity = new FollowUpRecordAttachmentEntity();
                followUpRecordAttachmentEntity.FollowUpRecordID = followUpRecordEntity.ID;
                followUpRecordAttachmentEntity.AttachmentURL = followUpRecordAttachmentAddForm.AttachmentURL;
                followUpRecordAttachmentEntity.AttachmentType = followUpRecordAttachmentAddForm.AttachmentType;
                list.add(followUpRecordAttachmentEntity);
            }

            followUpMapper.createFollowUpRecordAttachment(list);
        }

        send(followUpRecordEntity, list);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput assign(FollowUpRecordAddExtendForm form) throws Exception {
        FollowUpRecordEntity followUpRecordEntity = new FollowUpRecordEntity();
        followUpRecordEntity.CustomerID = form.CustomerID;
        followUpRecordEntity.FollowUpContent = "";
        followUpRecordEntity.IsFollowUp = false;
        followUpRecordEntity.FollowUpBy = form.FollowUpBy;
        followUpRecordEntity.PlannedOn = DateTimeUtil.toDate(form.PlannedOn);
        followUpRecordEntity.PlannedRemark = form.PlannedRemark;
        followUpRecordEntity.CreatedOn = new Date();
        followUpRecordEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        followUpMapper.createFollowUpRecord(followUpRecordEntity);

        if (form.Status.equals("20")) {
            customerMapper.deleteServicerByCustomerID(form.CustomerID, form.ServicerID);
            CustomerServicerEntity entity = new CustomerServicerEntity();
            entity.CustomerID = form.CustomerID;
            entity.ServicerID = form.ServicerID;
            entity.EmployeeID = form.FollowUpBy;
            customerMapper.insertCustomerServicer(entity);
        } else if (form.Status.equals("30")) {

            Integer count = customerMapper.getCustomerServicer(form.CustomerID, form.ServicerID, form.FollowUpBy);
            if (count == 0) {
                CustomerServicerEntity entity = new CustomerServicerEntity();
                entity.CustomerID = form.CustomerID;
                entity.ServicerID = form.ServicerID;
                entity.EmployeeID = form.FollowUpBy;
                customerMapper.insertCustomerServicer(entity);
            }
        }

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput anewAssign(FollowUpRecordAnewAssignAddExtendForm form) throws Exception {
        FollowUpRecordEntity followUpRecord = followUpMapper.getFollowUpRecord(form.ID);

        if (followUpRecord.IsFollowUp){
            return BaseOutput.success("已跟进，无法重新指派");
        }
        followUpRecord.FollowUpBy = form.FollowUpBy;
        followUpRecord.CreatedOn = new Date();
        followUpRecord.CreatedBy = SystemUserUtil.getSystemUserID();
        followUpMapper.updateFollowUpRecord(followUpRecord);
        return BaseOutput.success();
    }


    @Transactional
    public BaseOutput delete(FollowUpRecordDeleteForm form) {
        followUpMapper.deleteFollowUpRecord(form.ID);

        List<FollowUpRecordAttachmentEntity> followUpAttachment = followUpMapper.getFollowUpAttachment(form.ID);
        if (followUpAttachment.size() > 0) {
            for (FollowUpRecordAttachmentEntity followUpRecordAttachmentEntity : followUpAttachment) {
                String url = followUpRecordAttachmentEntity.AttachmentURL;
                String[] split = url.split("/");
                OSSClientUtil.deleteObject(split[split.length - 1]);
            }
            followUpMapper.deleteFollowUpAttachment(form.ID);
        }
        return BaseOutput.success();
    }

    public BaseOutput entityList() {
        return BaseOutputForm.success(followUpMapper.getEmployeePermissionEntity(SystemUserUtil.getSystemUserID()));
    }

    public BaseOutput employeeList() {
        return BaseOutputForm.success(followUpMapper.getEmployeePermissionEntityEmployee(SystemUserUtil.getSystemUserID()));
    }

    public BaseOutput createAppointmentBill(FollowUpAppointmentAddForm form) throws Exception {

        AppointmentBillEntity appointmentBillEntity = new AppointmentBillEntity();
        appointmentBillEntity.ID = AutoNumberUtils.getIBeautyAppointmentBill();
        appointmentBillEntity.EntityID = form.EntityID;
        appointmentBillEntity.CustomerID = form.CustomerID;
        appointmentBillEntity.Status = "10";
        appointmentBillEntity.AppointmentDate = DateTimeUtil.toDate(form.AppointmentDate);
        appointmentBillEntity.Period = 30;
        appointmentBillEntity.Remark = form.Remark;
        appointmentBillEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        appointmentBillEntity.Channel = BillChannel.BillChannelPC.getID();
        appointmentBillEntity.CreatedOn = new Date();
        appointmentBillMapper.createAppointmentBill(appointmentBillEntity);

        AppointmentBillProcessEntity appointmentBillProcessEntity = new AppointmentBillProcessEntity();
        appointmentBillProcessEntity.AppointmentBillID = appointmentBillEntity.ID;
        appointmentBillProcessEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        appointmentBillProcessEntity.CreatedOn = new Date();
        appointmentBillProcessEntity.ActionType = "10";
        appointmentBillMapper.createAppointmentBillProcess(appointmentBillProcessEntity);


        return BaseOutput.success();
    }

    public void send(FollowUpRecordEntity followUpRecordEntity, List<FollowUpRecordAttachmentEntity> list) {
        try {
            YXYFollowUpOutPutForm yxyInputForm = new YXYFollowUpOutPutForm();
            YingXiaoYunTenantEntity yingXiaoYunTenant = yingXiaoYunTenantMapper.getYingXiaoYunTenantEntity();
            if (yingXiaoYunTenant != null && yingXiaoYunTenant.Active) {
                CustomerEntity customer = customerMapper.getCustomer(followUpRecordEntity.CustomerID);
                EmployeeEntity employee = employeeMapper.getEmployee(SystemUserUtil.getSystemUserID());
                YingXiaoYunCustomerEntity yingXiaoYunCustomer = yingXiaoYunAppointmentBillMapper.getYingXiaoYunCustomer(customer.PhoneNumber);
                if (yingXiaoYunCustomer != null) {
                    List<YXYFollowUpListOutPutForm> followUpList = new ArrayList<>();
                    List<YXYAttachmentOutPutForm> attachmentOutPutFormList = new ArrayList<>();
                    yxyInputForm.EnterpriseCode = SystemUserUtil.getSystemEnterpriseCode();
                    yxyInputForm.TenantCode = yingXiaoYunTenant.TenantID;
                    yxyInputForm.PhoneNumber = customer.PhoneNumber;
                    YXYFollowUpListOutPutForm yxyFollowUpListOutPutForm = new YXYFollowUpListOutPutForm();
                    yxyFollowUpListOutPutForm.FollowUpRecordID = followUpRecordEntity.ID;
                    yxyFollowUpListOutPutForm.FollowUpContent = followUpRecordEntity.FollowUpContent;
                    yxyFollowUpListOutPutForm.FollowUpOn = DateTimeUtil.toDate(followUpRecordEntity.FollowUpOn);
                    yxyFollowUpListOutPutForm.FollowUpBy = employee.Name;
                    yxyFollowUpListOutPutForm.CreatedOn = DateTimeUtil.toDate(followUpRecordEntity.CreatedOn);
                    if (list != null && list.size() > 0) {
                        for (FollowUpRecordAttachmentEntity attachmentEntity : list) {
                            YXYAttachmentOutPutForm attachmentOutPutForm = new YXYAttachmentOutPutForm();
                            attachmentOutPutForm.AttachmentURL = attachmentEntity.AttachmentURL;
                            attachmentOutPutFormList.add(attachmentOutPutForm);
                        }
                    }
                    yxyFollowUpListOutPutForm.Attachment = attachmentOutPutFormList;
                    followUpList.add(yxyFollowUpListOutPutForm);
                    yxyInputForm.FollowUpList = followUpList;
                    JSONObject param = new JSONObject();
                    param.put("code", "ZL_CreateOrUpdateFollowUpRecord");
                    param.put("content", JSON.toJSONString(yxyInputForm));
                    JSONObject param2 = new JSONObject();
                    param2.put("request", param);
                    YingXiaoYunUtil.sendHttpPost(param2);
                }
            }


        } catch (Exception e) {
            logger.error("跟进数据传输失败！", e);
            e.printStackTrace();
        }
    }
}