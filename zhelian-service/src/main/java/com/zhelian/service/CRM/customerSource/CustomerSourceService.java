package com.zhelian.service.CRM.customerSource;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.CRM.CustomerSourceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.CustomerSourceEntity;
import com.zhelian.model.ibeauty.form.input.CustomerSourceAddForm;
import com.zhelian.model.ibeauty.form.input.CustomerSourceMoveForm;
import com.zhelian.model.ibeauty.form.input.CustomerSourceQueryForm;
import com.zhelian.model.ibeauty.form.input.CustomerSourceUpdateForm;
import com.zhelian.model.ibeauty.form.output.CustomerSourceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import static com.zhelian.core.utils.ListUtil.listToTree;

/**
 * @className: CustomerSourceService
 * @description:
 * @version: V1.0
 * @author: xuehao
 * @date: 2020/3/17 2:14 PM
 */
@Service
public class CustomerSourceService {

    @Autowired
    CustomerSourceMapper customerSourceMapper;

    public BaseOutput allCustomerSource(CustomerSourceQueryForm form) {

        List<CustomerSourceOutputForm> list = customerSourceMapper.allCustomerSource(form.Name, form.Active);
        JSONArray result = listToTree(JSONArray.parseArray(JSON.toJSONString(list, JSONWriter.Feature.WriteMapNullValue)), "ID", "ParentID", "Child");
        return BaseOutputForm.success(result);
    }


    public BaseOutput createCustomerSource(CustomerSourceAddForm form) {
        Integer Sequence = customerSourceMapper.getMaxSequence();
        CustomerSourceEntity customerSourceEntity = new CustomerSourceEntity();
        if (form.ParentID != null) {
            CustomerSourceEntity customerSourceEntity1 = customerSourceMapper.getCustomerSourceEntity(form.ParentID);
            if (customerSourceEntity1 != null && customerSourceEntity1.ParentID != 0) {
                return BaseOutput.failed("上级来源不属于一级来源");
            }
        }
        if (form.ParentID != null)
            customerSourceEntity.ParentID = form.ParentID;
        customerSourceEntity.Name = form.Name;
        customerSourceEntity.Sequence = Sequence != null ? Sequence + 1 : 1;
        customerSourceMapper.createCustomerSource(customerSourceEntity);

        return BaseOutput.success();

    }

    public BaseOutput updateCustomerSource(CustomerSourceUpdateForm form) {

        CustomerSourceEntity customerSourceEntity1 = customerSourceMapper.getCustomerSourceEntity(form.ID);
        if (form.Active == false) {
            if (customerSourceEntity1.ParentID == 0) {
                Integer childCount = customerSourceMapper.childActiveCount(form.ID);
                if (childCount > 0)
                    return BaseOutput.failed("下级存在有效的来源，当前来源不可改为无效");

                Integer count = customerSourceMapper.customerByCustomerSourceCount(form.ID);
                if (count > 0)
                    return BaseOutput.failed("该来源下存在会员，不可以改为无效");
            } else {
                Integer count = customerSourceMapper.customerByCustomerSourceCount(form.ID);
                if (count > 0)
                    return BaseOutput.failed("该来源下存在会员，不可以改为无效");
            }
        } else {
            if (customerSourceEntity1.ParentID != 0) {
                CustomerSourceEntity customerSourceEntity = customerSourceMapper.getCustomerSourceEntity(customerSourceEntity1.ParentID);
                if (customerSourceEntity.Active == false)
                    return BaseOutput.failed("上级来源无效，不能修改当前来源为有效");
            }
        }
        CustomerSourceEntity customerSourceEntity = new CustomerSourceEntity();
        customerSourceEntity.Active = form.Active;
        customerSourceEntity.Name = form.Name;
        customerSourceEntity.ID = form.ID;
        customerSourceMapper.updateCustomerSource(customerSourceEntity);
        return BaseOutput.success();
    }

    public BaseOutput move(CustomerSourceMoveForm form) {

        CustomerSourceEntity customerSourceEntity1 = customerSourceMapper.getCustomerSourceEntity(form.MoveID);

        LinkedList<Integer> linkedList = customerSourceMapper.getASCList(form.MoveID, customerSourceEntity1.ParentID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<CustomerSourceEntity> list = new ArrayList<>();
        for (Integer ID : linkedList) {
            CustomerSourceEntity customerSourceEntity = new CustomerSourceEntity();
            customerSourceEntity.ID = ID;
            customerSourceEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(customerSourceEntity);
        }

        customerSourceMapper.updateSequence(list);
        return BaseOutput.success();
    }

    public BaseOutput getCustomerSource() {
        List<CustomerSourceOutputForm> list = customerSourceMapper.getCustomerSource();
        return BaseOutputForm.success(list);
    }
}