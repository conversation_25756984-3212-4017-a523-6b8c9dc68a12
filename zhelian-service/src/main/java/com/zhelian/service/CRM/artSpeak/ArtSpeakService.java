package com.zhelian.service.CRM.artSpeak;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.CRM.ArtSpeakMapper;
import com.zhelian.model.CRM.entity.ArtSpeakEntity;
import com.zhelian.model.CRM.form.input.ArtSpeakAddOrUpdateForm;
import com.zhelian.model.CRM.form.input.ArtSpeakQueryForm;
import com.zhelian.model.CRM.form.output.ArtSpeakCategoryChildOutputForm;
import com.zhelian.model.CRM.form.output.ArtSpeakCategoryOutputForm;
import com.zhelian.model.CRM.form.output.ArtSpeakOutputForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import static com.zhelian.core.utils.ListUtil.listToTree;

@Service
public class ArtSpeakService {

    @Autowired
    ArtSpeakMapper artSpeakMapper;

    public BaseOutput artSpeakList(ArtSpeakQueryForm form) {

        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<ArtSpeakOutputForm> list = artSpeakMapper.artSpeakList(form.Name, form.ArtSpeakCategoryID);
        return BasePageInfo.success(list);
    }

    @Transactional
    public BaseOutput insertArtSpeak(ArtSpeakAddOrUpdateForm form) {

        if (artSpeakMapper.getArtSpeakName(form) > 0) {
            return BaseOutput.failed("话术名称已经存在，请重新填写");
        }
        ArtSpeakEntity artSpeakEntity = new ArtSpeakEntity();
        artSpeakEntity.Name = form.Name;
        artSpeakEntity.ArtSpeakCategoryID = form.ArtSpeakCategoryID;
        artSpeakEntity.Content = form.Content;
        artSpeakMapper.insertArtSpeak(artSpeakEntity);
        return BaseOutput.success();
    }

    public BaseOutput updateArtSpeak(ArtSpeakAddOrUpdateForm form) {

        if (artSpeakMapper.getArtSpeakName(form) > 0) {
            return BaseOutput.failed("话术名称已经存在，请重新填写");
        }
        ArtSpeakEntity artSpeakEntity = new ArtSpeakEntity();
        artSpeakEntity.ID = form.ID;
        artSpeakEntity.Name = form.Name;
        artSpeakEntity.ArtSpeakCategoryID = form.ArtSpeakCategoryID;
        artSpeakEntity.Content = form.Content;
        artSpeakMapper.updateArtSpeak(artSpeakEntity);

        return BaseOutput.success();
    }


    public BaseOutput getArtSpeakList(ArtSpeakOutputForm form) {
        List<Integer> parentList = new ArrayList<>();
        List<ArtSpeakCategoryChildOutputForm> list = artSpeakMapper.getArtSpeakParentList(form.Name);
        for (ArtSpeakCategoryChildOutputForm artSpeakCategoryChildOutputForm : list) {
            if (!parentList.contains(artSpeakCategoryChildOutputForm.ParentID))
                parentList.add(artSpeakCategoryChildOutputForm.ParentID);
        }
        List<ArtSpeakCategoryOutputForm> topList;
        JSONArray result = new JSONArray();
        if (parentList.size() > 0) {
            topList = artSpeakMapper.getTopArtSpeakList(parentList);
            for (ArtSpeakCategoryOutputForm artSpeakCategoryOutputForm : topList) {
                List<ArtSpeakCategoryChildOutputForm> child = new ArrayList<>();
                for (ArtSpeakCategoryChildOutputForm artSpeakCategoryChildOutputForm : list) {
                    if (artSpeakCategoryChildOutputForm.ParentID.equals(artSpeakCategoryOutputForm.ID)) {
                        child.add(artSpeakCategoryChildOutputForm);
                    }
                }
                artSpeakCategoryOutputForm.Child = child;
            }
            result = listToTree(JSONArray.parseArray(JSON.toJSONString(topList, JSONWriter.Feature.WriteMapNullValue)), "ID", "ParentID", "Child");
        }
        return BaseOutputForm.success(result);
    }

    public BaseOutput deleteArtSpeakByID(ArtSpeakAddOrUpdateForm form) {
        artSpeakMapper.deleteArtSpeakByID(form.ID);
        return BaseOutput.success();
    }
}
