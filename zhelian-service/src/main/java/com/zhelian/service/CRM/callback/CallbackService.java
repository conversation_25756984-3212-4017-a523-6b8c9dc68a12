package com.zhelian.service.CRM.callback;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.utils.DateTimeUtil;
import com.zhelian.core.utils.YingXiaoYunUtil;
import com.zhelian.mapper.CRM.CallbackMapper;
import com.zhelian.mapper.CRM.CallbackRuleMapper;
import com.zhelian.mapper.CRM.CustomerMapper;
import com.zhelian.mapper.khs.EmployeeMapper;
import com.zhelian.mapper.khs.EntityMapper;
import com.zhelian.mapper.yingxiaoyun.YingXiaoYunAppointmentBillMapper;
import com.zhelian.mapper.yingxiaoyun.YingXiaoYunTenantMapper;
import com.zhelian.model.CRM.entity.CallbackRecordEntity;
import com.zhelian.model.CRM.entity.CallbackRecordProjectAttachmentEntity;
import com.zhelian.model.CRM.entity.CallbackRecordProjectEntity;
import com.zhelian.model.CRM.form.input.*;
import com.zhelian.model.CRM.form.output.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.CustomerEntity;
import com.zhelian.model.ibeauty.form.output.TreatBillHandlerEmployeeOutputForm;
import com.zhelian.model.ibeauty.form.output.TreatBillHandlerOutputForm;
import com.zhelian.model.khs.entity.EmployeeEntity;
import com.zhelian.model.yingxiaoyun.entity.YingXiaoYunCustomerEntity;
import com.zhelian.model.yingxiaoyun.entity.YingXiaoYunTenantEntity;
import com.zhelian.model.yingxiaoyun.form.output.YXYCallBackListOutPutForm;
import com.zhelian.model.yingxiaoyun.form.output.YXYCallBackOutListProjectPutForm;
import com.zhelian.model.yingxiaoyun.form.output.YXYCallBackOutPutForm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class CallbackService {

    @Autowired
    EntityMapper entityMapper;
    @Autowired
    CallbackRuleMapper callbackRuleMapper;
    @Autowired
    YingXiaoYunTenantMapper yingXiaoYunTenantMapper;
    @Autowired
    YingXiaoYunAppointmentBillMapper yingXiaoYunAppointmentBillMapper;
    @Autowired
    CallbackMapper callbackMapper;
    @Autowired
    CustomerMapper customerMapper;
    @Autowired
    EmployeeMapper employeeMapper;

    private static final Logger logger = LoggerFactory.getLogger(CallbackService.class);

    public BaseOutput list(CallbackQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CallbackOutputForm> list = callbackMapper.getCallbackList(SystemUserUtil.getSystemUserID(), form.Name, form.IsCallback, form.CallbackMethodID, form.StartDate, form.EndDate, form.CallbackStatusID);
        return BasePageInfo.success(list);
    }

    public BaseOutput all(CallbackQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CallbackOutputForm> list = callbackMapper.allCallbackList(SystemUserUtil.getSystemUserID(), form.Name, form.IsCallback, form.CallbackMethodID, form.StartDate, form.EndDate, form.CallbackBy, form.CallbackEntityID, form.IsShowOwnCallback != null && form.IsShowOwnCallback == true ? true : null, form.CallbackStatusID,form.CustomerLevelID,form.ChannelName);
        return BasePageInfo.success(list);
    }


    public BaseOutput detail(CallbackDetailQueryForm form) {
        CallbackRecordDetailOutputForm callbackRecordDetail = callbackMapper.callbackRecordDetail(form.CallbackRecordID);
        return BaseOutputForm.success(callbackRecordDetail);
    }


    @Transactional
    public BaseOutput create(CallbackAddForm form) {
        CallbackRecordEntity callbackRecordEntity = callbackMapper.getCallbackRecord(form.CallbackRecordID);
        callbackRecordEntity.CallbackMethodID = form.CallbackMethodID;
        callbackRecordEntity.CallbackStatusID = form.CallbackStatusID;
        callbackRecordEntity.IsCallback = true;
        callbackRecordEntity.CallbackOn = new Date();
        callbackMapper.updateCallbackRecord(callbackRecordEntity);

        List<CallbackRecordProjectAttachmentEntity> list = new ArrayList<>();
        if (form.Content != null) {
            for (CallbackContentAddForm callbackContentAddForm : form.Content) {
                CallbackRecordProjectEntity callbackRecordProjectEntity = callbackMapper.getCallbackRecordProject(callbackContentAddForm.CallbackRecordProjectID);
                callbackRecordProjectEntity.CallbackContent = callbackContentAddForm.CallbackContent;
                callbackMapper.updateCallbackRecordProject(callbackRecordProjectEntity);

                for (FollowUpRecordAttachmentAddForm goodsImageUrlForm : callbackContentAddForm.Attachment) {
                    CallbackRecordProjectAttachmentEntity callbackRecordProjectAttachmentEntity = new CallbackRecordProjectAttachmentEntity();
                    callbackRecordProjectAttachmentEntity.CallbackRecordProjectID = callbackContentAddForm.CallbackRecordProjectID;
                    callbackRecordProjectAttachmentEntity.AttachmentType = goodsImageUrlForm.AttachmentType;
                    callbackRecordProjectAttachmentEntity.AttachmentURL = goodsImageUrlForm.AttachmentURL;
                    list.add(callbackRecordProjectAttachmentEntity);
                }
            }
        }

        if (list.size() > 0)
            callbackMapper.insertCallbackProjectAttachment(list);

        send(callbackRecordEntity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput update(CallbackAddForm form) {
        CallbackRecordEntity callbackRecordEntity = callbackMapper.getCallbackRecord(form.CallbackRecordID);
        callbackRecordEntity.CallbackMethodID = form.CallbackMethodID;
        callbackRecordEntity.CallbackStatusID = form.CallbackStatusID;
        callbackRecordEntity.IsCallback = true;
        callbackMapper.updateCallbackRecord(callbackRecordEntity);


        List<CallbackRecordProjectAttachmentEntity> callbackRecordProjectAttachmentEntities = callbackMapper.getCallbackRecordProjectAttachment(form.CallbackRecordID);
        if (callbackRecordProjectAttachmentEntities.size() > 0) {

            callbackMapper.deleteCallbackRecordProjectAttachment(form.CallbackRecordID);
        }

        List<CallbackRecordProjectAttachmentEntity> list = new ArrayList<>();
        if (form.Content != null) {
            for (CallbackContentAddForm callbackContentAddForm : form.Content) {
                CallbackRecordProjectEntity callbackRecordProjectEntity = callbackMapper.getCallbackRecordProject(callbackContentAddForm.CallbackRecordProjectID);
                callbackRecordProjectEntity.CallbackContent = callbackContentAddForm.CallbackContent;
                callbackMapper.updateCallbackRecordProject(callbackRecordProjectEntity);

                for (FollowUpRecordAttachmentAddForm goodsImageUrlForm : callbackContentAddForm.Attachment) {
                    CallbackRecordProjectAttachmentEntity callbackRecordProjectAttachmentEntity = new CallbackRecordProjectAttachmentEntity();
                    callbackRecordProjectAttachmentEntity.CallbackRecordProjectID = callbackContentAddForm.CallbackRecordProjectID;
                    callbackRecordProjectAttachmentEntity.AttachmentType = goodsImageUrlForm.AttachmentType;
                    callbackRecordProjectAttachmentEntity.AttachmentURL = goodsImageUrlForm.AttachmentURL;
                    list.add(callbackRecordProjectAttachmentEntity);
                }
            }
        }

        if (list.size() > 0)
            callbackMapper.insertCallbackProjectAttachment(list);

        send(callbackRecordEntity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput delete(CallbackDeleteForm form) {
        callbackMapper.deleteCallbackRecord(form.CallbackRecordID);
        callbackMapper.deleteCallbackRecordProject(form.CallbackRecordID);
        return BaseOutput.success();
    }


    @Transactional
    public BaseOutput massDistribution(CallbackMassDistributionForm form) {
        List<CallbackRecordEntity> list = callbackMapper.getCallbackRecordList(form.CallbackRecord);
        for (CallbackRecordEntity callbackRecordEntity : list) {
            if (form.HandlerType.equals("30")) {
                callbackRecordEntity.CallbackBy = form.EmployeeID;
            } else if (form.HandlerType.equals("10")) {
                List<String> servicerList = callbackMapper.getCustomerServicer(callbackRecordEntity.CustomerID);
                if (servicerList.size() > 0) {
                    if (form.ServicerID != null) {
                        List<String> serverEmployee = callbackMapper.getCustomerServicerEmployee(form.ServicerID, callbackRecordEntity.CustomerID);
                        callbackRecordEntity.CallbackBy = serverEmployee.get(new Random().nextInt(serverEmployee.size()));
                    }
                } else {
                    callbackRecordEntity.CallbackBy = form.EmployeeID;
                }
            } else {
                List<String> treatEmployee = callbackMapper.getTreatBillEmployee(form.ProjectTreatHandlerID, form.SavingCardProjectTreatHandlerID, callbackRecordEntity.ID);
                if (treatEmployee.size() > 0) {
                    callbackRecordEntity.CallbackBy = treatEmployee.get(new Random().nextInt(treatEmployee.size()));
                } else {
                    callbackRecordEntity.CallbackBy = form.EmployeeID;
                }
            }
            if (callbackRecordEntity.CallbackBy != null) {
                callbackMapper.updateCallbackRecord(callbackRecordEntity);
            }

        }
        return BaseOutput.success();
    }

    @Transactional
    public void createCallback(String BillID) {

        List<CallbackRulePlanOutputForm> callbackRuleList = callbackRuleMapper.getCallbackRuleByEntityID(SystemUserUtil.getSystemEntityID());
        for (CallbackRulePlanOutputForm callbackRuleEntity : callbackRuleList) {
            List<CallbackTreatBillProjectOutputForm> treatBillProjectEntity = callbackMapper.getTreatBillByCallbackProject(callbackRuleEntity.ID, BillID);
            for (CallbackTreatBillProjectOutputForm projectOutputForm : treatBillProjectEntity) {
                for (CallbackPlanOutputForm callbackPlanOutputForm : callbackRuleEntity.Plan) {

                    Date date = DateTimeUtil.addDay(projectOutputForm.BillDate, callbackPlanOutputForm.CallbackCycle);
                    if (callbackRuleEntity.HandlerType.equals("30")) {
                        if (callbackRuleEntity.EmployeeID != null) {
                            CallbackRecordEntity callbackRecordEntity = callbackMapper.getCallbackRecordByDateAndCallback(projectOutputForm.CustomerID, DateTimeUtil.toyyyyMMdd2(date), callbackRuleEntity.EmployeeID);

                            if (callbackRecordEntity == null) {
                                callbackRecordEntity = new CallbackRecordEntity();
                                callbackRecordEntity.CustomerID = projectOutputForm.CustomerID;
                                callbackRecordEntity.CallbackMethodID = callbackRuleEntity.CallbackMethodID;
                                callbackRecordEntity.IsCallback = false;
                                callbackRecordEntity.PlannedOn = DateTimeUtil.addDay(projectOutputForm.BillDate, callbackPlanOutputForm.CallbackCycle);
                                callbackRecordEntity.CallbackBy = callbackRuleEntity.EmployeeID;
                                callbackMapper.createCallback(callbackRecordEntity);
                            }
                            Integer number = callbackMapper.getCallbackRecordProjectByRecordIDAndBillIDAndProjectIDAndCalbackCycle(callbackRecordEntity.ID, projectOutputForm.BillID, projectOutputForm.ProjectID, callbackPlanOutputForm.CallbackCycle);
                            if (number == 0) {
                                CallbackRecordProjectEntity callbackRecordProjectEntity = new CallbackRecordProjectEntity();
                                callbackRecordProjectEntity.CallbackRecordID = callbackRecordEntity.ID;
                                callbackRecordProjectEntity.TreatBillID = projectOutputForm.BillID;
                                callbackRecordProjectEntity.ProjectID = projectOutputForm.ProjectID;
                                callbackRecordProjectEntity.CallbackCycle = callbackPlanOutputForm.CallbackCycle;
                                callbackRecordProjectEntity.CallbackRemark = callbackPlanOutputForm.CallbackContent;
                                callbackRecordProjectEntity.CallbackContent = "";
                                callbackMapper.createCallbackProject(callbackRecordProjectEntity);
                            }
                        }


                    } else if (callbackRuleEntity.HandlerType.equals("10")) {
                        List<String> list = callbackMapper.getCustomerServicer(projectOutputForm.CustomerID);
                        if (list.size() > 0) {
                            List<String> serverList = callbackMapper.getCustomerServicerEmployee(callbackRuleEntity.ServicerID, projectOutputForm.CustomerID);
                            if (serverList.size() > 0) {
                                String serverEmployee = serverList.get(new Random().nextInt(serverList.size()));
                                CallbackRecordEntity callbackRecordEntity = callbackMapper.getCallbackRecordByDateAndCallback(projectOutputForm.CustomerID, DateTimeUtil.toyyyyMMdd2(date), serverEmployee);
                                if (callbackRecordEntity == null) {
                                    callbackRecordEntity = new CallbackRecordEntity();
                                    callbackRecordEntity.CustomerID = projectOutputForm.CustomerID;
                                    callbackRecordEntity.CallbackMethodID = callbackRuleEntity.CallbackMethodID;
                                    callbackRecordEntity.IsCallback = false;
                                    callbackRecordEntity.PlannedOn = DateTimeUtil.addDay(projectOutputForm.BillDate, callbackPlanOutputForm.CallbackCycle);
                                    callbackRecordEntity.CallbackBy = serverEmployee;
                                    callbackMapper.createCallback(callbackRecordEntity);
                                }

                                Integer number = callbackMapper.getCallbackRecordProjectByRecordIDAndBillIDAndProjectIDAndCalbackCycle(callbackRecordEntity.ID, projectOutputForm.BillID, projectOutputForm.ProjectID, callbackPlanOutputForm.CallbackCycle);
                                if (number == 0) {
                                    CallbackRecordProjectEntity callbackRecordProjectEntity = new CallbackRecordProjectEntity();
                                    callbackRecordProjectEntity.CallbackRecordID = callbackRecordEntity.ID;
                                    callbackRecordProjectEntity.TreatBillID = projectOutputForm.BillID;
                                    callbackRecordProjectEntity.ProjectID = projectOutputForm.ProjectID;
                                    callbackRecordProjectEntity.CallbackCycle = callbackPlanOutputForm.CallbackCycle;
                                    callbackRecordProjectEntity.CallbackRemark = callbackPlanOutputForm.CallbackContent;
                                    callbackRecordProjectEntity.CallbackContent = "";
                                    callbackMapper.createCallbackProject(callbackRecordProjectEntity);
                                }
                            }
                        } else {
                            if (callbackRuleEntity.EmployeeID != null) {
                                CallbackRecordEntity callbackRecordEntity = callbackMapper.getCallbackRecordByDateAndCallback(projectOutputForm.CustomerID, DateTimeUtil.toyyyyMMdd2(date), callbackRuleEntity.EmployeeID);
                                if (callbackRecordEntity == null) {
                                    callbackRecordEntity = new CallbackRecordEntity();
                                    callbackRecordEntity.CustomerID = projectOutputForm.CustomerID;
                                    callbackRecordEntity.CallbackMethodID = callbackRuleEntity.CallbackMethodID;
                                    callbackRecordEntity.IsCallback = false;
                                    callbackRecordEntity.PlannedOn = DateTimeUtil.addDay(projectOutputForm.BillDate, callbackPlanOutputForm.CallbackCycle);
                                    callbackRecordEntity.CallbackBy = callbackRuleEntity.EmployeeID;
                                    callbackMapper.createCallback(callbackRecordEntity);
                                }

                                Integer number = callbackMapper.getCallbackRecordProjectByRecordIDAndBillIDAndProjectIDAndCalbackCycle(callbackRecordEntity.ID, projectOutputForm.BillID, projectOutputForm.ProjectID, callbackPlanOutputForm.CallbackCycle);
                                if (number == 0) {
                                    CallbackRecordProjectEntity callbackRecordProjectEntity = new CallbackRecordProjectEntity();
                                    callbackRecordProjectEntity.CallbackRecordID = callbackRecordEntity.ID;
                                    callbackRecordProjectEntity.TreatBillID = projectOutputForm.BillID;
                                    callbackRecordProjectEntity.ProjectID = projectOutputForm.ProjectID;
                                    callbackRecordProjectEntity.CallbackCycle = callbackPlanOutputForm.CallbackCycle;
                                    callbackRecordProjectEntity.CallbackRemark = callbackPlanOutputForm.CallbackContent;
                                    callbackRecordProjectEntity.CallbackContent = "";
                                    callbackMapper.createCallbackProject(callbackRecordProjectEntity);
                                }
                            }
                        }
                    } else {
                        if (projectOutputForm.TreatBillHandler.size() > 0) {
                            if (callbackRuleEntity.ProjectTreatHandlerID != null) {
                                if (!projectOutputForm.Type.equals("40")) {
                                    for (TreatBillHandlerOutputForm treatBillHandlerOutputForm : projectOutputForm.TreatBillHandler) {
                                        if (treatBillHandlerOutputForm.TreatHandlerID.equals(callbackRuleEntity.ProjectTreatHandlerID)) {
                                            String employeeID = "";
                                            List<BigDecimal> decimalList = treatBillHandlerOutputForm.Employee.stream().map(treatBillHandlerEmployeeOutputForm -> treatBillHandlerEmployeeOutputForm.Scale).collect(Collectors.toList());
                                            Set s = new HashSet(decimalList);
                                            Boolean isScaleEqual = s.size() == 1 ? true : false;
                                            if (isScaleEqual) {
                                                employeeID = treatBillHandlerOutputForm.Employee.get(new Random().nextInt(treatBillHandlerOutputForm.Employee.size())).EmployeeID;
                                            } else {
                                                BigDecimal reduce = treatBillHandlerOutputForm.Employee.stream().map(treatBillHandlerEmployeeOutputForm -> treatBillHandlerEmployeeOutputForm.Scale).reduce(BigDecimal::max).get();
                                                List<TreatBillHandlerEmployeeOutputForm> newList = treatBillHandlerOutputForm.Employee.stream().filter(treatBillHandlerEmployeeOutputForm -> treatBillHandlerEmployeeOutputForm.Scale.compareTo(reduce) == 0).collect(Collectors.toList());
                                                employeeID = newList.get(new Random().nextInt(newList.size())).EmployeeID;
                                            }
                                            CallbackRecordEntity callbackRecordEntity = callbackMapper.getCallbackRecordByDateAndCallback(projectOutputForm.CustomerID, DateTimeUtil.toyyyyMMdd2(date), employeeID);

                                            if (callbackRecordEntity == null) {
                                                callbackRecordEntity = new CallbackRecordEntity();
                                                callbackRecordEntity.CustomerID = projectOutputForm.CustomerID;
                                                callbackRecordEntity.CallbackMethodID = callbackRuleEntity.CallbackMethodID;
                                                callbackRecordEntity.IsCallback = false;
                                                callbackRecordEntity.PlannedOn = DateTimeUtil.addDay(projectOutputForm.BillDate, callbackPlanOutputForm.CallbackCycle);
                                                callbackRecordEntity.CallbackBy = employeeID;
                                                callbackMapper.createCallback(callbackRecordEntity);
                                            }
                                            Integer number = callbackMapper.getCallbackRecordProjectByRecordIDAndBillIDAndProjectIDAndCalbackCycle(callbackRecordEntity.ID, projectOutputForm.BillID, projectOutputForm.ProjectID, callbackPlanOutputForm.CallbackCycle);
                                            if (number == 0) {
                                                CallbackRecordProjectEntity callbackRecordProjectEntity = new CallbackRecordProjectEntity();
                                                callbackRecordProjectEntity.CallbackRecordID = callbackRecordEntity.ID;
                                                callbackRecordProjectEntity.TreatBillID = projectOutputForm.BillID;
                                                callbackRecordProjectEntity.ProjectID = projectOutputForm.ProjectID;
                                                callbackRecordProjectEntity.CallbackCycle = callbackPlanOutputForm.CallbackCycle;
                                                callbackRecordProjectEntity.CallbackRemark = callbackPlanOutputForm.CallbackContent;
                                                callbackRecordProjectEntity.CallbackContent = "";
                                                callbackMapper.createCallbackProject(callbackRecordProjectEntity);
                                            }
                                        }
                                    }
                                }

                            }
                            if (callbackRuleEntity.SavingCardProjectTreatHandlerID != null) {
                                if (projectOutputForm.Type.equals("40")) {
                                    for (TreatBillHandlerOutputForm treatBillHandlerOutputForm : projectOutputForm.TreatBillHandler) {
                                        if (treatBillHandlerOutputForm.TreatHandlerID.equals(callbackRuleEntity.ProjectTreatHandlerID)) {
                                            String employeeID = "";
                                            List<BigDecimal> decimalList = treatBillHandlerOutputForm.Employee.stream().map(treatBillHandlerEmployeeOutputForm -> treatBillHandlerEmployeeOutputForm.Scale).collect(Collectors.toList());
                                            Set s = new HashSet(decimalList);
                                            Boolean isScaleEqual = s.size() == 1 ? true : false;
                                            if (isScaleEqual) {
                                                employeeID = treatBillHandlerOutputForm.Employee.get(new Random().nextInt(treatBillHandlerOutputForm.Employee.size())).EmployeeID;
                                            } else {
                                                BigDecimal reduce = treatBillHandlerOutputForm.Employee.stream().map(treatBillHandlerEmployeeOutputForm -> treatBillHandlerEmployeeOutputForm.Scale).reduce(BigDecimal::max).get();
                                                List<TreatBillHandlerEmployeeOutputForm> newList = treatBillHandlerOutputForm.Employee.stream().filter(treatBillHandlerEmployeeOutputForm -> treatBillHandlerEmployeeOutputForm.Scale.compareTo(reduce) == 0).collect(Collectors.toList());
                                                employeeID = newList.get(new Random().nextInt(newList.size())).EmployeeID;
                                            }
                                            CallbackRecordEntity callbackRecordEntity = callbackMapper.getCallbackRecordByDateAndCallback(projectOutputForm.CustomerID, DateTimeUtil.toyyyyMMdd2(date), employeeID);

                                            if (callbackRecordEntity == null) {
                                                callbackRecordEntity = new CallbackRecordEntity();
                                                callbackRecordEntity.CustomerID = projectOutputForm.CustomerID;
                                                callbackRecordEntity.CallbackMethodID = callbackRuleEntity.CallbackMethodID;
                                                callbackRecordEntity.IsCallback = false;
                                                callbackRecordEntity.PlannedOn = DateTimeUtil.addDay(projectOutputForm.BillDate, callbackPlanOutputForm.CallbackCycle);
                                                callbackRecordEntity.CallbackBy = employeeID;
                                                callbackMapper.createCallback(callbackRecordEntity);
                                            }
                                            Integer number = callbackMapper.getCallbackRecordProjectByRecordIDAndBillIDAndProjectIDAndCalbackCycle(callbackRecordEntity.ID, projectOutputForm.BillID, projectOutputForm.ProjectID, callbackPlanOutputForm.CallbackCycle);
                                            if (number == 0) {
                                                CallbackRecordProjectEntity callbackRecordProjectEntity = new CallbackRecordProjectEntity();
                                                callbackRecordProjectEntity.CallbackRecordID = callbackRecordEntity.ID;
                                                callbackRecordProjectEntity.TreatBillID = projectOutputForm.BillID;
                                                callbackRecordProjectEntity.ProjectID = projectOutputForm.ProjectID;
                                                callbackRecordProjectEntity.CallbackCycle = callbackPlanOutputForm.CallbackCycle;
                                                callbackRecordProjectEntity.CallbackRemark = callbackPlanOutputForm.CallbackContent;
                                                callbackRecordProjectEntity.CallbackContent = "";
                                                callbackMapper.createCallbackProject(callbackRecordProjectEntity);
                                            }
                                        }
                                    }
                                }

                            }

                        } else {
                            if (callbackRuleEntity.EmployeeID != null) {
                                CallbackRecordEntity callbackRecordEntity = callbackMapper.getCallbackRecordByDateAndCallback(projectOutputForm.CustomerID, DateTimeUtil.toyyyyMMdd2(date), callbackRuleEntity.EmployeeID);
                                if (callbackRecordEntity == null) {
                                    callbackRecordEntity = new CallbackRecordEntity();
                                    callbackRecordEntity.CustomerID = projectOutputForm.CustomerID;
                                    callbackRecordEntity.CallbackMethodID = callbackRuleEntity.CallbackMethodID;
                                    callbackRecordEntity.IsCallback = false;
                                    callbackRecordEntity.PlannedOn = DateTimeUtil.addDay(projectOutputForm.BillDate, callbackPlanOutputForm.CallbackCycle);
                                    callbackRecordEntity.CallbackBy = callbackRuleEntity.EmployeeID;
                                    callbackMapper.createCallback(callbackRecordEntity);
                                }
                                Integer number = callbackMapper.getCallbackRecordProjectByRecordIDAndBillIDAndProjectIDAndCalbackCycle(callbackRecordEntity.ID, projectOutputForm.BillID, projectOutputForm.ProjectID, callbackPlanOutputForm.CallbackCycle);
                                if (number == 0) {
                                    CallbackRecordProjectEntity callbackRecordProjectEntity = new CallbackRecordProjectEntity();
                                    callbackRecordProjectEntity.CallbackRecordID = callbackRecordEntity.ID;
                                    callbackRecordProjectEntity.TreatBillID = projectOutputForm.BillID;
                                    callbackRecordProjectEntity.ProjectID = projectOutputForm.ProjectID;
                                    callbackRecordProjectEntity.CallbackCycle = callbackPlanOutputForm.CallbackCycle;
                                    callbackRecordProjectEntity.CallbackRemark = callbackPlanOutputForm.CallbackContent;
                                    callbackRecordProjectEntity.CallbackContent = "";
                                    callbackMapper.createCallbackProject(callbackRecordProjectEntity);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    public void send(CallbackRecordEntity callbackRecordEntity) {
        try {
            YXYCallBackOutPutForm yxyInputForm = new YXYCallBackOutPutForm();
            YingXiaoYunTenantEntity yingXiaoYunTenant = yingXiaoYunTenantMapper.getYingXiaoYunTenantEntity();
            if (yingXiaoYunTenant != null && yingXiaoYunTenant.Active) {

                CustomerEntity customer = customerMapper.getCustomer(callbackRecordEntity.CustomerID);
                YingXiaoYunCustomerEntity yingXiaoYunCustomer = yingXiaoYunAppointmentBillMapper.getYingXiaoYunCustomer(customer.PhoneNumber);
                if (yingXiaoYunCustomer != null) {
                    EmployeeEntity employee = employeeMapper.getEmployee(callbackRecordEntity.CallbackBy);
                    List<YXYCallBackOutListProjectPutForm> listProjectPutForms = callbackMapper.yxyCallbackRecordDetail(callbackRecordEntity.ID);
                    List<YXYCallBackListOutPutForm> callbackList = new ArrayList<>();
                    yxyInputForm.EnterpriseCode = SystemUserUtil.getSystemEnterpriseCode();
                    yxyInputForm.TenantCode = yingXiaoYunTenant.TenantID;
                    yxyInputForm.PhoneNumber = customer.PhoneNumber;
                    YXYCallBackListOutPutForm yxyCallBackListOutPutForm = new YXYCallBackListOutPutForm();
                    yxyCallBackListOutPutForm.CallbackRecordID = callbackRecordEntity.ID;
                    yxyCallBackListOutPutForm.CallbackOn = DateTimeUtil.toDate(callbackRecordEntity.CallbackOn);
                    yxyCallBackListOutPutForm.CallbackBy = employee.Name;
                    yxyCallBackListOutPutForm.CreatedOn = DateTimeUtil.toDate(callbackRecordEntity.CallbackOn);

                    if (listProjectPutForms != null && listProjectPutForms.size() > 0) {
                        yxyCallBackListOutPutForm.Projects = listProjectPutForms;
                    }
                    callbackList.add(yxyCallBackListOutPutForm);
                    yxyInputForm.CallbackList = callbackList;
                    JSONObject param = new JSONObject();
                    param.put("code", "ZL_CreateOrUpdateCallbackRecord");
                    param.put("content", JSON.toJSONString(yxyInputForm));

                    JSONObject param2 = new JSONObject();
                    param2.put("request", param);
                    YingXiaoYunUtil.sendHttpPost(param2);
                }
            }
        } catch (Exception e) {
            logger.error("回访数据传输失败！", e);
            e.printStackTrace();

        }
    }

    public BaseOutput excelDisPlayPhone(CallbackQueryForm form, HttpServletResponse response) {
        List<CallbackExcelOutputForm> list = callbackMapper.excelDisPlayPhone(SystemUserUtil.getSystemUserID(), form.Name, form.IsCallback, form.CallbackMethodID, form.StartDate, form.EndDate, form.CallbackBy, form.CallbackEntityID, form.IsShowOwnCallback != null && form.IsShowOwnCallback == true ? true : null, form.CallbackStatusID,form.CustomerLevelID,form.ChannelName);
        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("回访报表", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), CallbackExcelOutputForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("回访报表").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    public BaseOutput excelNoDisPlayPhone(CallbackQueryForm form, HttpServletResponse response) {
        List<CallbackExcelOutputForm> list = callbackMapper.excelNoDisPlayPhone(SystemUserUtil.getSystemUserID(), form.Name, form.IsCallback, form.CallbackMethodID, form.StartDate, form.EndDate, form.CallbackBy, form.CallbackEntityID, form.IsShowOwnCallback != null && form.IsShowOwnCallback == true ? true : null, form.CallbackStatusID,form.CustomerLevelID,form.ChannelName);
        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("回访报表", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), CallbackExcelOutputForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("回访报表").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }
}