package com.zhelian.service.CRM.customerFileApplicationScene;

import com.zhelian.mapper.CRM.CustomerFileApplicationSceneMapper;
import com.zhelian.model.CRM.form.input.CustomerFileApplicationSceneUpdateForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class CustomerFileApplicationSceneService {

    @Autowired
    CustomerFileApplicationSceneMapper customerFileApplicationSceneMapper;

    public BaseOutput all() {
        return BaseOutputForm.success(customerFileApplicationSceneMapper.customerFileApplicationScene());
    }

    public BaseOutput updateStatus(CustomerFileApplicationSceneUpdateForm form) {
        customerFileApplicationSceneMapper.updateStatus(form.Code, form.IsRequired);
        return BaseOutput.success();
    }
}