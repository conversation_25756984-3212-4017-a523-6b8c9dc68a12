package com.zhelian.service.CRM.reception;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.autoNumber.AutoNumberUtils;
import com.zhelian.core.utils.enumeration.BillChannel;
import com.zhelian.mapper.CRM.CustomerMapper;
import com.zhelian.mapper.CRM.ReceptionMapper;
import com.zhelian.mapper.ibeauty.AppointmentBillMapper;
import com.zhelian.model.CRM.entity.DiagnosisRecordEntity;
import com.zhelian.model.CRM.form.input.*;
import com.zhelian.model.CRM.form.output.FollowUpRecordExcelOutputForm;
import com.zhelian.model.CRM.form.output.ReceptionExcelOutputForm;
import com.zhelian.model.CRM.form.output.ReceptionOutputForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.AppointmentBillEntity;
import com.zhelian.model.ibeauty.entity.AppointmentBillProcessEntity;
import com.zhelian.model.ibeauty.entity.CustomerEntityEntity;
import com.zhelian.model.ibeauty.entity.CustomerServicerEntity;
import com.zhelian.service.ibeauty.appointment.AppointmentBillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.List;


@Service
public class ReceptionService {

    @Autowired
    ReceptionMapper receptionMapper;

    @Autowired
    CustomerMapper customerMapper;

    @Autowired
    AppointmentBillMapper appointmentBillMapper;

    @Autowired
    AppointmentBillService appointmentBillService;

    public BaseOutput list(ReceptionQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<ReceptionOutputForm> list = receptionMapper.receptionList(SystemUserUtil.getSystemEntityID(), form.Name, form.StartDate, form.EndDate, form.Status,form.CustomerLevelID,form.ChannelName);

        return BasePageInfo.success(list);
    }


    public BaseOutput confirm(ReceptionQueryExtendForm form) {
        // 获取预约单信息
        AppointmentBillEntity appointmentBillEntity = appointmentBillMapper.getAppointmentBill(form.AppointmentBillID);

        receptionMapper.confirm(form.AppointmentBillID);

        // 当顾客确认到店时，设置预约门店为顾客所属门店（如果顾客还没有所属门店）
        if (appointmentBillEntity != null) {
            // 检查顾客是否已有所属门店
            CustomerEntityEntity existingBelongEntity = customerMapper.getBelongEntity(appointmentBillEntity.CustomerID);
            if (existingBelongEntity == null) {
                // 顾客没有所属门店，将当前预约的门店设为所属门店
                Integer existingRelationID = customerMapper.getCustomerEntityID(appointmentBillEntity.CustomerID, appointmentBillEntity.EntityID);
                if (existingRelationID == null) {
                    // 如果顾客与该门店没有关联关系，创建新的关联并设为所属门店
                    CustomerEntityEntity customerEntityEntity = new CustomerEntityEntity();
                    customerEntityEntity.CustomerID = appointmentBillEntity.CustomerID;
                    customerEntityEntity.EntityID = appointmentBillEntity.EntityID;
                    customerEntityEntity.IsBelongEntity = true;
                    customerEntityEntity.IsCreateEntity = false;
                    customerMapper.insertCustomerEntity(customerEntityEntity);
                } else {
                    // 如果已有关联关系，直接更新为所属门店
                    customerMapper.updateCustomerEntityEntity(appointmentBillEntity.CustomerID, appointmentBillEntity.EntityID);
                }
            }
        }

        appointmentBillMapper.deleteAppointmentBillProcess(form.AppointmentBillID);
        AppointmentBillProcessEntity appointmentBillProcessEntity = new AppointmentBillProcessEntity();
        appointmentBillProcessEntity.AppointmentBillID = form.AppointmentBillID;
        appointmentBillProcessEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        appointmentBillProcessEntity.CreatedOn = new Date();
        appointmentBillProcessEntity.ActionType = "20";
        appointmentBillMapper.createAppointmentBillProcess(appointmentBillProcessEntity);

        appointmentBillService.send("20", form.AppointmentBillID);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput confirmAssign(ReceptionAddForm form) {
        // 获取预约单信息
        AppointmentBillEntity appointmentBillEntity = appointmentBillMapper.getAppointmentBill(form.AppointmentBillID);

        receptionMapper.confirm(form.AppointmentBillID);

        // 当顾客确认到店时，设置预约门店为顾客所属门店（如果顾客还没有所属门店）
        if (appointmentBillEntity != null) {
            // 检查顾客是否已有所属门店
            CustomerEntityEntity existingBelongEntity = customerMapper.getBelongEntity(appointmentBillEntity.CustomerID);
            if (existingBelongEntity == null) {
                // 顾客没有所属门店，将当前预约的门店设为所属门店
                Integer existingRelationID = customerMapper.getCustomerEntityID(appointmentBillEntity.CustomerID, appointmentBillEntity.EntityID);
                if (existingRelationID == null) {
                    // 如果顾客与该门店没有关联关系，创建新的关联并设为所属门店
                    CustomerEntityEntity customerEntityEntity = new CustomerEntityEntity();
                    customerEntityEntity.CustomerID = appointmentBillEntity.CustomerID;
                    customerEntityEntity.EntityID = appointmentBillEntity.EntityID;
                    customerEntityEntity.IsBelongEntity = true;
                    customerEntityEntity.IsCreateEntity = false;
                    customerMapper.insertCustomerEntity(customerEntityEntity);
                } else {
                    // 如果已有关联关系，直接更新为所属门店
                    customerMapper.updateCustomerEntityEntity(appointmentBillEntity.CustomerID, appointmentBillEntity.EntityID);
                }
            }
        }

        appointmentBillMapper.deleteAppointmentBillProcess(form.AppointmentBillID);
        AppointmentBillProcessEntity appointmentBillProcessEntity = new AppointmentBillProcessEntity();
        appointmentBillProcessEntity.AppointmentBillID = form.AppointmentBillID;
        appointmentBillProcessEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        appointmentBillProcessEntity.CreatedOn = new Date();
        appointmentBillProcessEntity.ActionType = "20";
        appointmentBillMapper.createAppointmentBillProcess(appointmentBillProcessEntity);

        DiagnosisRecordEntity record = receptionMapper.getDiagnosisRecordByAppointmentBill(form.AppointmentBillID);
        if (record != null) {
            return BaseOutput.failed("已经进行过指派，请选择其他单据");
        }

        DiagnosisRecordEntity diagnosisRecordEntity = new DiagnosisRecordEntity();
        diagnosisRecordEntity.AppointmentBillID = form.AppointmentBillID;
        diagnosisRecordEntity.CustomerID = form.CustomerID;
        diagnosisRecordEntity.Guidance = form.Guidance;
        diagnosisRecordEntity.IsDiagnosis = false;
        diagnosisRecordEntity.DiagnosisContent = "";
        diagnosisRecordEntity.DiagnosisBy = form.DiagnosisBy;
        diagnosisRecordEntity.CreatedOn = new Date();
        diagnosisRecordEntity.CreatedBy = SystemUserUtil.getSystemUserID();

        receptionMapper.diagnosisRecord(diagnosisRecordEntity);

        if (form.IsServicer) {
            if (form.Status.equals("20")) {
                customerMapper.deleteServicerByCustomerID(form.CustomerID, form.ServicerID);
                CustomerServicerEntity entity = new CustomerServicerEntity();
                entity.CustomerID = form.CustomerID;
                entity.ServicerID = form.ServicerID;
                entity.EmployeeID = form.DiagnosisBy;
                customerMapper.insertCustomerServicer(entity);
            } else if (form.Status.equals("30")) {

                Integer count = customerMapper.getCustomerServicer(form.CustomerID, form.ServicerID, form.DiagnosisBy);
                if (count == 0) {
                    CustomerServicerEntity entity = new CustomerServicerEntity();
                    entity.CustomerID = form.CustomerID;
                    entity.ServicerID = form.ServicerID;
                    entity.EmployeeID = form.DiagnosisBy;
                    customerMapper.insertCustomerServicer(entity);
                }
            }
        }

        return BaseOutput.success();
    }


    @Transactional
    public BaseOutput assign(ReceptionAddForm form) {
        receptionMapper.confirm(form.AppointmentBillID);

        DiagnosisRecordEntity record = receptionMapper.getDiagnosisRecordByAppointmentBill(form.AppointmentBillID);
        if (record != null) {
            return BaseOutput.failed("已经进行过指派，请选择其他单据");
        }

        DiagnosisRecordEntity diagnosisRecordEntity = new DiagnosisRecordEntity();
        diagnosisRecordEntity.AppointmentBillID = form.AppointmentBillID;
        diagnosisRecordEntity.CustomerID = form.CustomerID;
        diagnosisRecordEntity.Guidance = form.Guidance;
        diagnosisRecordEntity.IsDiagnosis = false;
        diagnosisRecordEntity.DiagnosisContent = "";
        diagnosisRecordEntity.DiagnosisBy = form.DiagnosisBy;
        diagnosisRecordEntity.CreatedOn = new Date();
        diagnosisRecordEntity.CreatedBy = SystemUserUtil.getSystemUserID();

        receptionMapper.diagnosisRecord(diagnosisRecordEntity);

        if (form.IsServicer) {
            if (form.Status.equals("20")) {
                customerMapper.deleteServicerByCustomerID(form.CustomerID, form.ServicerID);
                CustomerServicerEntity entity = new CustomerServicerEntity();
                entity.CustomerID = form.CustomerID;
                entity.ServicerID = form.ServicerID;
                entity.EmployeeID = form.DiagnosisBy;
                customerMapper.insertCustomerServicer(entity);
            } else if (form.Status.equals("30")) {

                Integer count = customerMapper.getCustomerServicer(form.CustomerID, form.ServicerID, form.DiagnosisBy);
                if (count == 0) {
                    CustomerServicerEntity entity = new CustomerServicerEntity();
                    entity.CustomerID = form.CustomerID;
                    entity.ServicerID = form.ServicerID;
                    entity.EmployeeID = form.DiagnosisBy;
                    customerMapper.insertCustomerServicer(entity);
                }
            }
        }

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput againAssign(ReceptionUpdateForm form) {
        DiagnosisRecordEntity diagnosisRecordEntity = receptionMapper.getDiagnosisRecord(form.DiagnosisRecordID);
        diagnosisRecordEntity.Guidance = form.Guidance;
        diagnosisRecordEntity.DiagnosisContent = "";
        diagnosisRecordEntity.DiagnosisBy = form.DiagnosisBy;
        diagnosisRecordEntity.CreatedOn = new Date();
        diagnosisRecordEntity.CreatedBy = SystemUserUtil.getSystemUserID();

        receptionMapper.updateDiagnosisRecord(diagnosisRecordEntity);

        if (form.IsServicer) {
            if (form.Status.equals("20")) {
                customerMapper.deleteServicerByCustomerID(form.CustomerID, form.ServicerID);
                CustomerServicerEntity entity = new CustomerServicerEntity();
                entity.CustomerID = form.CustomerID;
                entity.ServicerID = form.ServicerID;
                entity.EmployeeID = form.DiagnosisBy;
                customerMapper.insertCustomerServicer(entity);
            } else if (form.Status.equals("30")) {

                Integer count = customerMapper.getCustomerServicer(form.CustomerID, form.ServicerID, form.DiagnosisBy);
                if (count == 0) {
                    CustomerServicerEntity entity = new CustomerServicerEntity();
                    entity.CustomerID = form.CustomerID;
                    entity.ServicerID = form.ServicerID;
                    entity.EmployeeID = form.DiagnosisBy;
                    customerMapper.insertCustomerServicer(entity);
                }
            }
        }
        return BaseOutput.success();
    }

    public BaseOutput confirmVist(ReceptionConfirmAddForm form) {

        AppointmentBillEntity appointmentBillEntity = new AppointmentBillEntity();
        appointmentBillEntity.ID = AutoNumberUtils.getIBeautyAppointmentBill();
        appointmentBillEntity.EntityID = SystemUserUtil.getSystemEntityID();
        appointmentBillEntity.CustomerID = form.CustomerID;
        appointmentBillEntity.Status = "20";
        appointmentBillEntity.AppointmentDate = new Date();
        appointmentBillEntity.Period = 60;
        appointmentBillEntity.Remark = "";
        appointmentBillEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        appointmentBillEntity.Channel = BillChannel.BillChannelPC.getID();
        appointmentBillEntity.CreatedOn = new Date();
        appointmentBillMapper.createAppointmentBill(appointmentBillEntity);

        AppointmentBillProcessEntity appointmentBillProcessEntity = new AppointmentBillProcessEntity();
        appointmentBillProcessEntity.AppointmentBillID = appointmentBillEntity.ID;
        appointmentBillProcessEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        appointmentBillProcessEntity.CreatedOn = new Date();
        appointmentBillProcessEntity.ActionType = "20";
        appointmentBillMapper.createAppointmentBillProcess(appointmentBillProcessEntity);


        if (form.IsAssign) {
            DiagnosisRecordEntity diagnosisRecordEntity = new DiagnosisRecordEntity();
            diagnosisRecordEntity.AppointmentBillID = appointmentBillEntity.ID;
            diagnosisRecordEntity.CustomerID = form.CustomerID;
            diagnosisRecordEntity.Guidance = form.Guidance;
            diagnosisRecordEntity.DiagnosisContent = "";
            diagnosisRecordEntity.IsDiagnosis = false;
            diagnosisRecordEntity.DiagnosisBy = form.DiagnosisBy;
            diagnosisRecordEntity.CreatedOn = new Date();
            diagnosisRecordEntity.CreatedBy = SystemUserUtil.getSystemUserID();

            receptionMapper.diagnosisRecord(diagnosisRecordEntity);

            if (form.IsServicer) {
                if (form.Status.equals("20")) {
                    customerMapper.deleteServicerByCustomerID(form.CustomerID, form.ServicerID);
                    CustomerServicerEntity entity = new CustomerServicerEntity();
                    entity.CustomerID = form.CustomerID;
                    entity.ServicerID = form.ServicerID;
                    entity.EmployeeID = form.DiagnosisBy;
                    customerMapper.insertCustomerServicer(entity);
                } else if (form.Status.equals("30")) {

                    Integer count = customerMapper.getCustomerServicer(form.CustomerID, form.ServicerID, form.DiagnosisBy);
                    if (count == 0) {
                        CustomerServicerEntity entity = new CustomerServicerEntity();
                        entity.CustomerID = form.CustomerID;
                        entity.ServicerID = form.ServicerID;
                        entity.EmployeeID = form.DiagnosisBy;
                        customerMapper.insertCustomerServicer(entity);
                    }
                }
            }
        }

        return BaseOutput.success();
    }

    public BaseOutput excelDisPlayPhone(ReceptionQueryForm form, HttpServletResponse response) {
        List<ReceptionExcelOutputForm> list = receptionMapper.excelDisPlayPhone(SystemUserUtil.getSystemEntityID(), form.Name, form.StartDate, form.EndDate, form.Status,form.CustomerLevelID,form.ChannelName);
        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("接待报表", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), ReceptionExcelOutputForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("接待报表").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    public BaseOutput excelNoDisPlayPhone(ReceptionQueryForm form, HttpServletResponse response) {
        List<ReceptionExcelOutputForm> list = receptionMapper.excelNoDisPlayPhone(SystemUserUtil.getSystemEntityID(), form.Name, form.StartDate, form.EndDate, form.Status,form.CustomerLevelID,form.ChannelName);
        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("接待报表", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), ReceptionExcelOutputForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("接待报表").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }
}