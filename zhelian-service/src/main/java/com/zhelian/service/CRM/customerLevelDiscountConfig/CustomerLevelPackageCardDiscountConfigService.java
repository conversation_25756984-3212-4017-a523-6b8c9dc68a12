package com.zhelian.service.CRM.customerLevelDiscountConfig;


import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.CRM.CustomerLevelPackageCardDiscountConfigMapper;
import com.zhelian.model.CRM.entity.*;
import com.zhelian.model.CRM.entity.CustomerPricePackageCardCategoryEntity;
import com.zhelian.model.CRM.form.input.*;
import com.zhelian.model.CRM.form.output.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static java.math.BigDecimal.ROUND_HALF_UP;

@Service
public class CustomerLevelPackageCardDiscountConfigService {

    @Autowired
    CustomerLevelPackageCardDiscountConfigMapper customerLevelPackageCardDiscountConfigMapper;

    public BaseOutput customerLevelDiscountAll() {
        List<CustomerLevelPackageCardCategoryDiscountOutputForm> list = customerLevelPackageCardDiscountConfigMapper.customerLevelDiscountAll();
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput createCustomerLevelDiscountConfig(List<CustomerLevelPackageCardDiscountConfigAddForm> form) {

        customerLevelPackageCardDiscountConfigMapper.deleteCustomerLevelDiscountConfig();
        List<CustomerPricePackageCardSchemeEntity> list = new ArrayList<>();
        for (CustomerLevelPackageCardDiscountConfigAddForm customerLevelPackageCardDiscountConfigAddForm : form) {
            CustomerPricePackageCardSchemeEntity customerPricePackageCardSchemeEntity = new CustomerPricePackageCardSchemeEntity();
            customerPricePackageCardSchemeEntity.CustomerLevelID = customerLevelPackageCardDiscountConfigAddForm.CustomerLevelID;
            if (customerLevelPackageCardDiscountConfigAddForm.DiscountPrice != null){
                customerPricePackageCardSchemeEntity.DiscountPrice = customerLevelPackageCardDiscountConfigAddForm.DiscountPrice.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
            }
            list.add(customerPricePackageCardSchemeEntity);
        }
        customerLevelPackageCardDiscountConfigMapper.createCustomerLevelDiscountConfig(list);
        return BaseOutput.success();
    }


    public BaseOutput customerLevelCategoryDiscountAll(CustomerLevelPackageCardCategoryDiscountConfigQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CustomerLevelPackageCardCategoryDiscountConfigOutputForm> list = customerLevelPackageCardDiscountConfigMapper.customerLevelCategoryDiscountAll(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput category() {
        List<CustomerLevelDiscountPricePackageCardCategoryOutputForm> list = customerLevelPackageCardDiscountConfigMapper.category();
        return BaseOutputForm.success(list);
    }

    public BaseOutput selectedCategory() {
        List<Integer> list = customerLevelPackageCardDiscountConfigMapper.selectedCategory();
        return BaseOutputForm.success(list);
    }


    @Transactional
    public BaseOutput createCustomerLevelCategoryDiscountConfig(List<CustomerLevelPackageCardCategoryDiscountConfigAddForm> form) {

        List<CustomerPricePackageCardCategoryEntity> list = new ArrayList<>();
        for (CustomerLevelPackageCardCategoryDiscountConfigAddForm customerLevelPackageCardCategoryDiscountConfigAddForm : form) {
            CustomerPricePackageCardCategoryEntity customerPricePackageCardCategoryEntity = new CustomerPricePackageCardCategoryEntity();
            customerPricePackageCardCategoryEntity.CustomerLevelID = customerLevelPackageCardCategoryDiscountConfigAddForm.CustomerLevelID;
            customerPricePackageCardCategoryEntity.PackageCardCategoryID = customerLevelPackageCardCategoryDiscountConfigAddForm.PackageCardCategoryID;
            list.add(customerPricePackageCardCategoryEntity);
        }
        customerLevelPackageCardDiscountConfigMapper.createCustomerLevelCategoryDiscountConfig(list);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateCustomerLevelCategoryDiscountConfig(CustomerLevelPackageCardCategoryDiscountConfigUpdateForm form) {

        customerLevelPackageCardDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByPackageCardCategoryID(form.PackageCardCategoryID);

        List<CustomerPricePackageCardCategoryEntity> list = new ArrayList<>();
        for (CustomerLevelPackageCardCategoryDiscountUpdateForm customerLevelPackageCardCategoryUpdateForm : form.CustomerLevelDiscountPriceList) {
            CustomerPricePackageCardCategoryEntity customerPricePackageCardCategoryEntity = new CustomerPricePackageCardCategoryEntity();
            customerPricePackageCardCategoryEntity.CustomerLevelID = customerLevelPackageCardCategoryUpdateForm.CustomerLevelID;
            customerPricePackageCardCategoryEntity.PackageCardCategoryID = form.PackageCardCategoryID;
            if (customerLevelPackageCardCategoryUpdateForm.DiscountPrice != null){
                customerPricePackageCardCategoryEntity.DiscountPrice = customerLevelPackageCardCategoryUpdateForm.DiscountPrice.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
            }

            list.add(customerPricePackageCardCategoryEntity);
        }
        customerLevelPackageCardDiscountConfigMapper.createCustomerLevelCategoryDiscountConfig(list);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput batchUpdateCustomerLevelCategoryDiscountConfig(List<CustomerLevelPackageCardCategoryDiscountConfigUpdateForm> forms) {


        forms.forEach(form -> {
            customerLevelPackageCardDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByPackageCardCategoryID(form.PackageCardCategoryID);

            List<CustomerPricePackageCardCategoryEntity> list = new ArrayList<>();
            for (CustomerLevelPackageCardCategoryDiscountUpdateForm customerLevelPackageCardCategoryUpdateForm : form.CustomerLevelDiscountPriceList) {
                CustomerPricePackageCardCategoryEntity customerPricePackageCardCategoryEntity = new CustomerPricePackageCardCategoryEntity();
                customerPricePackageCardCategoryEntity.CustomerLevelID = customerLevelPackageCardCategoryUpdateForm.CustomerLevelID;
                customerPricePackageCardCategoryEntity.PackageCardCategoryID = form.PackageCardCategoryID;
                if (customerLevelPackageCardCategoryUpdateForm.DiscountPrice != null){
                    customerPricePackageCardCategoryEntity.DiscountPrice = customerLevelPackageCardCategoryUpdateForm.DiscountPrice.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
                }

                list.add(customerPricePackageCardCategoryEntity);
            }
            customerLevelPackageCardDiscountConfigMapper.createCustomerLevelCategoryDiscountConfig(list);
        });

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput deleteCustomerLevelCategoryDiscountConfig(CustomerLevelPackageCardCategoryDiscountConfigDeleteForm form) {

        customerLevelPackageCardDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByPackageCardCategoryID(form.PackageCardCategoryID);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput batchDeleteCustomerLevelCategoryDiscountConfig(List<CustomerLevelPackageCardCategoryDiscountConfigDeleteForm> forms) {

        forms.forEach(form -> {
            customerLevelPackageCardDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByPackageCardCategoryID(form.PackageCardCategoryID);
        });
        return BaseOutput.success();
    }

    public BaseOutput packageCard(CustomerLevelPackageCardDiscountPriceQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CustomerLevelDiscountPricePackageCardAllOutputForm> list = customerLevelPackageCardDiscountConfigMapper.packageCard(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput selectedPackageCard() {
        List<Integer> list = customerLevelPackageCardDiscountConfigMapper.selectedPackageCard();
        return BaseOutputForm.success(list);
    }

    public BaseOutput customerLevelPackageCardDiscountPriceAll(CustomerLevelPackageCardDiscountPriceConfigQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CustomerLevelDiscountPricePackageCardConfigAllOutputForm> list = customerLevelPackageCardDiscountConfigMapper.customerLevelPackageCardDiscountPriceAll(form.Name);
        return BasePageInfo.success(list);
    }

    @Transactional
    public BaseOutput createCustomerLevelPackageCardDiscountPriceConfig(List<CustomerLevelPackageCardDiscountPriceConfigAddForm> form) {

        List<CustomerPricePackageCardEntity> list = new ArrayList<>();
        for (CustomerLevelPackageCardDiscountPriceConfigAddForm customerLevelPackageCardDiscountPriceConfigAddForm : form) {
            CustomerPricePackageCardEntity customerPricePackageCardEntity = new CustomerPricePackageCardEntity();
            customerPricePackageCardEntity.CustomerLevelID = customerLevelPackageCardDiscountPriceConfigAddForm.CustomerLevelID;
            customerPricePackageCardEntity.PackageCardID = customerLevelPackageCardDiscountPriceConfigAddForm.PackageCardID;

            list.add(customerPricePackageCardEntity);
        }

        customerLevelPackageCardDiscountConfigMapper.createCustomerLevelPackageCardDiscountPriceConfig(list);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateCustomerLevelPackageCardDiscountConfig(CustomerLevelPackageCardCardDiscountConfigUpdateForm form) {
        customerLevelPackageCardDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByPackageCardID(form.PackageCardID);

        List<CustomerPricePackageCardEntity> list = new ArrayList<>();
        for (CustomerLevelPackageCardDiscountUpdateForm customerLevelPackageCardDiscountUpdateForm : form.CustomerLevelDiscountPriceList) {
            CustomerPricePackageCardEntity customerPricePackageCardEntity = new CustomerPricePackageCardEntity();
            customerPricePackageCardEntity.CustomerLevelID = customerLevelPackageCardDiscountUpdateForm.CustomerLevelID;
            customerPricePackageCardEntity.PackageCardID = form.PackageCardID;
            customerPricePackageCardEntity.PriceType = customerLevelPackageCardDiscountUpdateForm.PriceType;
            if (customerLevelPackageCardDiscountUpdateForm.DiscountPrice != null){
                if (customerPricePackageCardEntity.PriceType.equals(1)) {
                    customerPricePackageCardEntity.DiscountPrice = customerLevelPackageCardDiscountUpdateForm.DiscountPrice.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
                } else {
                    customerPricePackageCardEntity.DiscountPrice = customerLevelPackageCardDiscountUpdateForm.DiscountPrice;
                }
            }


            list.add(customerPricePackageCardEntity);
        }

        customerLevelPackageCardDiscountConfigMapper.createCustomerLevelPackageCardDiscountPriceConfig(list);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput batchUpdateCustomerLevelPackageCardDiscountConfig(List<CustomerLevelPackageCardCardDiscountConfigUpdateForm> forms) {

        forms.forEach(form -> {
            customerLevelPackageCardDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByPackageCardID(form.PackageCardID);

            List<CustomerPricePackageCardEntity> list = new ArrayList<>();
            for (CustomerLevelPackageCardDiscountUpdateForm customerLevelPackageCardDiscountUpdateForm : form.CustomerLevelDiscountPriceList) {
                CustomerPricePackageCardEntity customerPricePackageCardEntity = new CustomerPricePackageCardEntity();
                customerPricePackageCardEntity.CustomerLevelID = customerLevelPackageCardDiscountUpdateForm.CustomerLevelID;
                customerPricePackageCardEntity.PackageCardID = form.PackageCardID;
                customerPricePackageCardEntity.PriceType = customerLevelPackageCardDiscountUpdateForm.PriceType;
                if (customerLevelPackageCardDiscountUpdateForm.DiscountPrice != null){
                    if (customerPricePackageCardEntity.PriceType.equals(1)) {
                        customerPricePackageCardEntity.DiscountPrice = customerLevelPackageCardDiscountUpdateForm.DiscountPrice.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
                    } else {
                        customerPricePackageCardEntity.DiscountPrice = customerLevelPackageCardDiscountUpdateForm.DiscountPrice;
                    }
                }


                list.add(customerPricePackageCardEntity);
            }

            customerLevelPackageCardDiscountConfigMapper.createCustomerLevelPackageCardDiscountPriceConfig(list);
        });

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput deleteCustomerLevelPackageCardDiscountConfig(CustomerLevelPackageCardDiscountConfigDeleteForm form) {
        customerLevelPackageCardDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByPackageCardID(form.PackageCardID);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput batchDeleteCustomerLevelPackageCardDiscountConfig(List<CustomerLevelPackageCardDiscountConfigDeleteForm> forms) {

        forms.forEach(form -> {
            customerLevelPackageCardDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByPackageCardID(form.PackageCardID);
        });
        return BaseOutput.success();
    }
}
