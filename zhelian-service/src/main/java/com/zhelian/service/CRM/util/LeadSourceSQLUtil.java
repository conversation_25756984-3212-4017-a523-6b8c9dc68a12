/**
 * <AUTHOR>
 * @date 2025-01-15
 * @description 线索来源SQL工具类
 * @version 1.0
 */
package com.zhelian.service.CRM.util;

/**
 * 线索来源SQL工具类
 * 提供统一的SQL CASE语句，用于在查询中转换线索来源代码为中文描述
 */
public class LeadSourceSQLUtil {

    /**
     * 获取线索来源转换的SQL CASE语句
     * @param columnName 线索来源字段名（如：tf.LeadSource）
     * @param aliasName 别名（如：LeadSourceName）
     * @return 完整的CASE语句
     */
    public static String getLeadSourceCaseSQL(String columnName, String aliasName) {
        return String.format(
            "CASE \n" +
            "    WHEN %s = 'DOUYIN_XINXILIU' THEN '抖音信息流'\n" +
            "    WHEN %s = 'DOUYIN_TUANGOU' THEN '抖音团购'\n" +
            "    WHEN %s = 'BENDI_XIANSUO_TUIGUANG' THEN '本地线索推广'\n" +
            "    WHEN %s = 'MEITUAN_DIANPING' THEN '美团点评'\n" +
            "    WHEN %s = 'BENDI_SHENGHUO_ZHIBO' THEN '本地生活直播'\n" +
            "    WHEN %s = 'DOUYIN_BENDITUISIXIN' THEN '抖音本地推-私信'\n" +
            "    WHEN %s = 'WEIXIN_PENGYOUQUAN' THEN '微信朋友圈'\n" +
            "    WHEN %s = 'DOUYIN_ZAIXIANZIXUN' THEN '抖音-在线咨询'\n" +
            "    WHEN %s = 'GAODE_DITU' THEN '高德地图'\n" +
            "    WHEN %s = 'XIAOHONGSHU' THEN '小红书'\n" +
            "    WHEN %s = 'ZIRAN_DAODIAN' THEN '【自然到店】'\n" +
            "    WHEN %s = 'LAO_DAI_XIN' THEN '【老带新】'\n" +
            "    ELSE '未知来源'\n" +
            "END AS %s",
            columnName, columnName, columnName, columnName, columnName, columnName,
            columnName, columnName, columnName, columnName, columnName, columnName,
            aliasName
        );
    }

    /**
     * 获取默认的线索来源转换SQL（使用tf.LeadSource作为字段名）
     * @param aliasName 别名
     * @return CASE语句
     */
    public static String getDefaultLeadSourceCaseSQL(String aliasName) {
        return getLeadSourceCaseSQL("tf.LeadSource", aliasName);
    }

    /**
     * 获取线索来源转换的简化SQL（用于Excel导出等场景）
     * @param columnName 线索来源字段名
     * @return 简化的CASE语句
     */
    public static String getSimpleLeadSourceCaseSQL(String columnName) {
        return String.format(
            "CASE \n" +
            "    WHEN %s = 'DOUYIN_XINXILIU' THEN '抖音信息流'\n" +
            "    WHEN %s = 'DOUYIN_TUANGOU' THEN '抖音团购'\n" +
            "    WHEN %s = 'BENDI_XIANSUO_TUIGUANG' THEN '本地线索推广'\n" +
            "    WHEN %s = 'MEITUAN_DIANPING' THEN '美团点评'\n" +
            "    WHEN %s = 'BENDI_SHENGHUO_ZHIBO' THEN '本地生活直播'\n" +
            "    WHEN %s = 'DOUYIN_BENDITUISIXIN' THEN '抖音本地推-私信'\n" +
            "    WHEN %s = 'WEIXIN_PENGYOUQUAN' THEN '微信朋友圈'\n" +
            "    WHEN %s = 'DOUYIN_ZAIXIANZIXUN' THEN '抖音-在线咨询'\n" +
            "    WHEN %s = 'GAODE_DITU' THEN '高德地图'\n" +
            "    WHEN %s = 'XIAOHONGSHU' THEN '小红书'\n" +
            "    WHEN %s = 'ZIRAN_DAODIAN' THEN '【自然到店】'\n" +
            "    WHEN %s = 'LAO_DAI_XIN' THEN '【老带新】'\n" +
            "    ELSE '未知来源'\n" +
            "END",
            columnName, columnName, columnName, columnName, columnName, columnName,
            columnName, columnName, columnName, columnName, columnName, columnName
        );
    }

    /**
     * 生成用于统计的SQL语句
     * @return 统计各线索来源数量的SQL
     */
    public static String getLeadSourceStatisticsSQL() {
        return "SELECT \n" +
               "    LeadSource,\n" +
               "    COUNT(*) as Count,\n" +
               "    " + getSimpleLeadSourceCaseSQL("LeadSource") + " as Description\n" +
               "FROM TB_FollowUpRecord \n" +
               "GROUP BY LeadSource\n" +
               "ORDER BY Count DESC";
    }

    /**
     * 生成验证SQL语句
     * @return 验证线索来源字段的SQL
     */
    public static String getValidationSQL() {
        return "-- 验证线索来源字段转换\n" +
               "SELECT TOP 10\n" +
               "    ID,\n" +
               "    CustomerID,\n" +
               "    LeadSource,\n" +
               "    " + getSimpleLeadSourceCaseSQL("LeadSource") + " as LeadSourceName,\n" +
               "    FollowUpContent,\n" +
               "    CreatedOn\n" +
               "FROM TB_FollowUpRecord\n" +
               "WHERE LeadSource IS NOT NULL\n" +
               "ORDER BY CreatedOn DESC;";
    }
}
