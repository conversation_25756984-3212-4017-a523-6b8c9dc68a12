package com.zhelian.service.CRM.customerLevelDiscountConfig;


import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.CRM.CustomerLevelGeneralCardDiscountConfigMapper;
import com.zhelian.model.CRM.entity.*;
import com.zhelian.model.CRM.form.input.*;
import com.zhelian.model.CRM.form.output.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static java.math.BigDecimal.ROUND_HALF_UP;

@Service
public class CustomerLevelGeneralCardDiscountConfigService {

    @Autowired
    CustomerLevelGeneralCardDiscountConfigMapper customerLevelGeneralCardDiscountConfigMapper;

    public BaseOutput customerLevelDiscountAll() {
        List<CustomerLevelGeneralCardCategoryDiscountOutputForm> list = customerLevelGeneralCardDiscountConfigMapper.customerLevelDiscountAll();
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput createCustomerLevelDiscountConfig(List<CustomerLevelGeneralCardDiscountConfigAddForm> form) {

        customerLevelGeneralCardDiscountConfigMapper.deleteCustomerLevelDiscountConfig();
        List<CustomerPriceGeneralCardSchemeEntity> list = new ArrayList<>();
        for (CustomerLevelGeneralCardDiscountConfigAddForm customerLevelGeneralCardDiscountConfigAddForm : form) {
            CustomerPriceGeneralCardSchemeEntity customerPriceGeneralCardSchemeEntity = new CustomerPriceGeneralCardSchemeEntity();
            customerPriceGeneralCardSchemeEntity.CustomerLevelID = customerLevelGeneralCardDiscountConfigAddForm.CustomerLevelID;
            if (customerLevelGeneralCardDiscountConfigAddForm.DiscountPrice != null){
                customerPriceGeneralCardSchemeEntity.DiscountPrice = customerLevelGeneralCardDiscountConfigAddForm.DiscountPrice.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
            }
            list.add(customerPriceGeneralCardSchemeEntity);
        }
        customerLevelGeneralCardDiscountConfigMapper.createCustomerLevelDiscountConfig(list);
        return BaseOutput.success();
    }


    public BaseOutput customerLevelCategoryDiscountAll(CustomerLevelGeneralCardCategoryDiscountConfigQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CustomerLevelGeneralCardCategoryDiscountConfigOutputForm> list = customerLevelGeneralCardDiscountConfigMapper.customerLevelCategoryDiscountAll(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput category() {
        List<CustomerLevelDiscountPriceGeneralCardCategoryOutputForm> list = customerLevelGeneralCardDiscountConfigMapper.category();
        return BaseOutputForm.success(list);
    }

    public BaseOutput selectedCategory() {
        List<Integer> list = customerLevelGeneralCardDiscountConfigMapper.selectedCategory();
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput createCustomerLevelCategoryDiscountConfig(List<CustomerLevelGeneralCardCategoryDiscountConfigAddForm> form) {

        List<CustomerPriceGeneralCardCategoryEntity> list = new ArrayList<>();
        for (CustomerLevelGeneralCardCategoryDiscountConfigAddForm customerLevelGeneralCardCategoryDiscountConfigAddForm : form) {
            CustomerPriceGeneralCardCategoryEntity customerPriceGeneralCardCategoryEntity = new CustomerPriceGeneralCardCategoryEntity();
            customerPriceGeneralCardCategoryEntity.CustomerLevelID = customerLevelGeneralCardCategoryDiscountConfigAddForm.CustomerLevelID;
            customerPriceGeneralCardCategoryEntity.GeneralCardCategoryID = customerLevelGeneralCardCategoryDiscountConfigAddForm.GeneralCardCategoryID;
            list.add(customerPriceGeneralCardCategoryEntity);
        }
        customerLevelGeneralCardDiscountConfigMapper.createCustomerLevelCategoryDiscountConfig(list);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateCustomerLevelCategoryDiscountConfig(CustomerLevelGeneralCardCategoryDiscountConfigUpdateForm form) {

        customerLevelGeneralCardDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByGeneralCardCategoryID(form.GeneralCardCategoryID);

        List<CustomerPriceGeneralCardCategoryEntity> list = new ArrayList<>();
        for (CustomerLevelGeneralCardCategoryDiscountUpdateForm customerLevelGeneralCardCategoryUpdateForm : form.CustomerLevelDiscountPriceList) {
            CustomerPriceGeneralCardCategoryEntity customerPriceGeneralCardCategoryEntity = new CustomerPriceGeneralCardCategoryEntity();
            customerPriceGeneralCardCategoryEntity.CustomerLevelID = customerLevelGeneralCardCategoryUpdateForm.CustomerLevelID;
            customerPriceGeneralCardCategoryEntity.GeneralCardCategoryID = form.GeneralCardCategoryID;
            if (customerLevelGeneralCardCategoryUpdateForm.DiscountPrice != null){
                customerPriceGeneralCardCategoryEntity.DiscountPrice = customerLevelGeneralCardCategoryUpdateForm.DiscountPrice.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
            }
            list.add(customerPriceGeneralCardCategoryEntity);
        }
        customerLevelGeneralCardDiscountConfigMapper.createCustomerLevelCategoryDiscountConfig(list);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput batchUpdateCustomerLevelCategoryDiscountConfig(List<CustomerLevelGeneralCardCategoryDiscountConfigUpdateForm> forms) {


        forms.forEach(form -> {
            customerLevelGeneralCardDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByGeneralCardCategoryID(form.GeneralCardCategoryID);

            List<CustomerPriceGeneralCardCategoryEntity> list = new ArrayList<>();
            for (CustomerLevelGeneralCardCategoryDiscountUpdateForm customerLevelGeneralCardCategoryUpdateForm : form.CustomerLevelDiscountPriceList) {
                CustomerPriceGeneralCardCategoryEntity customerPriceGeneralCardCategoryEntity = new CustomerPriceGeneralCardCategoryEntity();
                customerPriceGeneralCardCategoryEntity.CustomerLevelID = customerLevelGeneralCardCategoryUpdateForm.CustomerLevelID;
                customerPriceGeneralCardCategoryEntity.GeneralCardCategoryID = form.GeneralCardCategoryID;
                if (customerLevelGeneralCardCategoryUpdateForm.DiscountPrice != null){
                    customerPriceGeneralCardCategoryEntity.DiscountPrice = customerLevelGeneralCardCategoryUpdateForm.DiscountPrice.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
                }
                list.add(customerPriceGeneralCardCategoryEntity);
            }
            customerLevelGeneralCardDiscountConfigMapper.createCustomerLevelCategoryDiscountConfig(list);
        });

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput deleteCustomerLevelCategoryDiscountConfig(CustomerLevelGeneralCardCategoryDiscountConfigDeleteForm form) {

        customerLevelGeneralCardDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByGeneralCardCategoryID(form.GeneralCardCategoryID);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput batchDeleteCustomerLevelCategoryDiscountConfig(List<CustomerLevelGeneralCardCategoryDiscountConfigDeleteForm> forms) {

        forms.forEach(form -> {
            customerLevelGeneralCardDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByGeneralCardCategoryID(form.GeneralCardCategoryID);
        });
        return BaseOutput.success();
    }

    public BaseOutput generalCard(CustomerLevelGeneralCardDiscountPriceQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CustomerLevelDiscountPriceGeneralCardAllOutputForm> list = customerLevelGeneralCardDiscountConfigMapper.generalCard(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput selectedGeneralCard() {
        List<Integer> list = customerLevelGeneralCardDiscountConfigMapper.selectedGeneralCard();
        return BaseOutputForm.success(list);
    }

    public BaseOutput customerLevelGeneralCardDiscountPriceAll(CustomerLevelGeneralCardDiscountPriceConfigQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CustomerLevelDiscountPriceGeneralCardConfigAllOutputForm> list = customerLevelGeneralCardDiscountConfigMapper.customerLevelGeneralCardDiscountPriceAll(form.Name);
        return BasePageInfo.success(list);
    }

    @Transactional
    public BaseOutput createCustomerLevelGeneralCardDiscountPriceConfig(List<CustomerLevelGeneralCardDiscountPriceConfigAddForm> form) {

        List<CustomerPriceGeneralCardEntity> list = new ArrayList<>();
        for (CustomerLevelGeneralCardDiscountPriceConfigAddForm customerLevelGeneralCardDiscountPriceConfigAddForm : form) {
            CustomerPriceGeneralCardEntity customerPriceGeneralCardEntity = new CustomerPriceGeneralCardEntity();
            customerPriceGeneralCardEntity.CustomerLevelID = customerLevelGeneralCardDiscountPriceConfigAddForm.CustomerLevelID;
            customerPriceGeneralCardEntity.GeneralCardID = customerLevelGeneralCardDiscountPriceConfigAddForm.GeneralCardID;

            list.add(customerPriceGeneralCardEntity);
        }

        customerLevelGeneralCardDiscountConfigMapper.createCustomerLevelGeneralCardDiscountPriceConfig(list);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateCustomerLevelGeneralCardDiscountConfig(CustomerLevelGeneralCardCardDiscountConfigUpdateForm form) {
        customerLevelGeneralCardDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByGeneralCardID(form.GeneralCardID);

        List<CustomerPriceGeneralCardEntity> list = new ArrayList<>();
        for (CustomerLevelGeneralCardDiscountUpdateForm customerLevelGeneralCardDiscountUpdateForm : form.CustomerLevelDiscountPriceList) {
            CustomerPriceGeneralCardEntity customerPriceGeneralCardEntity = new CustomerPriceGeneralCardEntity();
            customerPriceGeneralCardEntity.CustomerLevelID = customerLevelGeneralCardDiscountUpdateForm.CustomerLevelID;
            customerPriceGeneralCardEntity.GeneralCardID = form.GeneralCardID;
            customerPriceGeneralCardEntity.PriceType = customerLevelGeneralCardDiscountUpdateForm.PriceType;
            if (customerLevelGeneralCardDiscountUpdateForm.DiscountPrice != null){
                if (customerPriceGeneralCardEntity.PriceType.equals(1)) {
                    customerPriceGeneralCardEntity.DiscountPrice = customerLevelGeneralCardDiscountUpdateForm.DiscountPrice.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
                } else {
                    customerPriceGeneralCardEntity.DiscountPrice = customerLevelGeneralCardDiscountUpdateForm.DiscountPrice;
                }
            }

            list.add(customerPriceGeneralCardEntity);
        }

        customerLevelGeneralCardDiscountConfigMapper.createCustomerLevelGeneralCardDiscountPriceConfig(list);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput batchUpdateCustomerLevelGeneralCardDiscountConfig(List<CustomerLevelGeneralCardCardDiscountConfigUpdateForm> forms) {

        forms.forEach(form -> {
            customerLevelGeneralCardDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByGeneralCardID(form.GeneralCardID);

            List<CustomerPriceGeneralCardEntity> list = new ArrayList<>();
            for (CustomerLevelGeneralCardDiscountUpdateForm customerLevelGeneralCardDiscountUpdateForm : form.CustomerLevelDiscountPriceList) {
                CustomerPriceGeneralCardEntity customerPriceGeneralCardEntity = new CustomerPriceGeneralCardEntity();
                customerPriceGeneralCardEntity.CustomerLevelID = customerLevelGeneralCardDiscountUpdateForm.CustomerLevelID;
                customerPriceGeneralCardEntity.GeneralCardID = form.GeneralCardID;
                customerPriceGeneralCardEntity.PriceType = customerLevelGeneralCardDiscountUpdateForm.PriceType;
                if (customerLevelGeneralCardDiscountUpdateForm.DiscountPrice != null){
                    if (customerPriceGeneralCardEntity.PriceType.equals(1)) {
                        customerPriceGeneralCardEntity.DiscountPrice = customerLevelGeneralCardDiscountUpdateForm.DiscountPrice.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
                    } else {
                        customerPriceGeneralCardEntity.DiscountPrice = customerLevelGeneralCardDiscountUpdateForm.DiscountPrice;
                    }
                }

                list.add(customerPriceGeneralCardEntity);
            }

            customerLevelGeneralCardDiscountConfigMapper.createCustomerLevelGeneralCardDiscountPriceConfig(list);
        });

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput deleteCustomerLevelGeneralCardDiscountConfig(CustomerLevelGeneralCardDiscountConfigDeleteForm form) {
        customerLevelGeneralCardDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByGeneralCardID(form.GeneralCardID);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput batchDeleteCustomerLevelGeneralCardDiscountConfig(List<CustomerLevelGeneralCardDiscountConfigDeleteForm> forms) {

        forms.forEach(form -> {
            customerLevelGeneralCardDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByGeneralCardID(form.GeneralCardID);
        });
        return BaseOutput.success();
    }
}
