package com.zhelian.service.CRM.callbackMethod;

import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.CRM.CallbackMethodMapper;
import com.zhelian.model.CRM.entity.CallbackMethodEntity;
import com.zhelian.model.CRM.form.input.CallbackMethodAddForm;
import com.zhelian.model.CRM.form.input.CallbackMethodUpdateForm;
import com.zhelian.model.CRM.form.output.CallbackMethodOutputForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.PayMethodMoveForm;
import com.zhelian.model.ibeauty.form.input.PayMethodQueryForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;


@Service
public class CallbackMethodService {
    @Autowired
    CallbackMethodMapper callbackMethodMapper;

    public BaseOutput all(PayMethodQueryForm form) {
        List<CallbackMethodOutputForm> list = callbackMethodMapper.callbackMethodList(form.Name, form.Active);
        return BaseOutputForm.success(list);
    }

    public BaseOutput create(CallbackMethodAddForm form) {
        Integer Sequence = callbackMethodMapper.getMaxSequence();
        CallbackMethodEntity callbackMethodEntity = new CallbackMethodEntity();
        callbackMethodEntity.Name = form.Name;
        callbackMethodEntity.Sequence = Sequence != null ? Sequence + 1 : 1;
        callbackMethodMapper.createCallbackMethod(callbackMethodEntity);
        return BaseOutput.success();
    }

    public BaseOutput update(CallbackMethodUpdateForm form) {
        CallbackMethodEntity callbackMethodEntity = new CallbackMethodEntity();
        callbackMethodEntity.ID = form.ID;
        callbackMethodEntity.Name = form.Name;
        callbackMethodEntity.Active = form.Active;
        callbackMethodMapper.updateCallbackMethod(callbackMethodEntity);

        return BaseOutput.success();
    }

    public BaseOutput move(PayMethodMoveForm form) {
        //重新排序
        LinkedList<Integer> linkedList = callbackMethodMapper.getIDOrderBySequenceASC(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<CallbackMethodEntity> list = new ArrayList<>();

        for (Integer ID : linkedList) {
            CallbackMethodEntity callbackMethodEntity = new CallbackMethodEntity();
            callbackMethodEntity.ID = ID;
            callbackMethodEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(callbackMethodEntity);
        }
        callbackMethodMapper.updateSequence(list);
        return BaseOutput.success();
    }

}