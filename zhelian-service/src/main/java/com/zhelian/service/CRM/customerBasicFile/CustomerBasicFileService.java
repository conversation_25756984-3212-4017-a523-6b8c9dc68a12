package com.zhelian.service.CRM.customerBasicFile;

import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.CRM.CustomerBasicFileMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.CustomerBasicFileEntity;
import com.zhelian.model.ibeauty.form.input.CustomerBasicFileAddForm;
import com.zhelian.model.ibeauty.form.input.CustomerBasicFileQueryForm;
import com.zhelian.model.ibeauty.form.input.CustomerBasicFileUpdateForm;
import com.zhelian.model.ibeauty.form.input.CustomerFileCategoryMoveForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

@Service
public class CustomerBasicFileService {

    @Autowired
    CustomerBasicFileMapper customerBasicFileMapper;


    public BaseOutput allCustomerBasicFile(CustomerBasicFileQueryForm form) {
        return BaseOutputForm.success(customerBasicFileMapper.allCustomerBasicFile(form.Name, form.Type));
    }

    public BaseOutput createCustomerBasicFile(CustomerBasicFileAddForm form) {
        Integer Sequence = customerBasicFileMapper.getMaxSequence();
        CustomerBasicFileEntity customerBasicFileEntity = new CustomerBasicFileEntity();
        customerBasicFileEntity.Name = form.Name;
        customerBasicFileEntity.Type = form.Type;
        customerBasicFileEntity.ComponentsProperty = form.ComponentsProperty;
        customerBasicFileEntity.Sequence = Sequence != null ? Sequence + 1 : 1;

        customerBasicFileMapper.createCustomerBasicFile(customerBasicFileEntity);
        return BaseOutput.success();
    }

    public BaseOutput updateCustomerBasicFile(CustomerBasicFileUpdateForm form) {

        CustomerBasicFileEntity customerBasicFileEntity = new CustomerBasicFileEntity();
        customerBasicFileEntity.ID = form.ID;
        customerBasicFileEntity.Name = form.Name;
        customerBasicFileEntity.Type = form.Type;
        customerBasicFileEntity.ComponentsProperty = form.ComponentsProperty;
        customerBasicFileMapper.updateCustomerBasicFile(customerBasicFileEntity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput deleteCustomerBasicFile(CustomerBasicFileUpdateForm form) {
        customerBasicFileMapper.deleteCustomerBasicFile(form.ID);
        customerBasicFileMapper.deleteCustomerBasicFileInfo(form.ID);
        return BaseOutput.success();
    }

    public BaseOutput moveCustomerBasicFile(CustomerFileCategoryMoveForm form) {
        //重新排序
        LinkedList<Integer> linkedList = customerBasicFileMapper.getIDOrderBySequenceASC(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<CustomerBasicFileEntity> list = new ArrayList<>();

        for (Integer ID : linkedList) {
            CustomerBasicFileEntity customerBasicFileEntity = new CustomerBasicFileEntity();
            customerBasicFileEntity.ID = ID;
            customerBasicFileEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(customerBasicFileEntity);
        }
        customerBasicFileMapper.updateSequence(list);
        return BaseOutput.success();
    }
}