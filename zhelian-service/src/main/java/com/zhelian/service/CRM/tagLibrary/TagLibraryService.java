package com.zhelian.service.CRM.tagLibrary;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.CRM.TagLibraryMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.TagLibraryEntity;
import com.zhelian.model.ibeauty.form.input.TagLibraryAddForm;
import com.zhelian.model.ibeauty.form.input.TagLibraryDeleteForm;
import com.zhelian.model.ibeauty.form.input.TagLibraryQueryForm;
import com.zhelian.model.ibeauty.form.input.TagLibraryUpdateForm;
import com.zhelian.model.ibeauty.form.output.TagLibraryOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TagLibraryService {

    @Autowired
    TagLibraryMapper tagLibraryMapper;

    public BaseOutput listTagLibrary(TagLibraryQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<TagLibraryOutputForm> list = tagLibraryMapper.allTagLibrary(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput allTagLibrary(String name) {
        List<TagLibraryOutputForm> list = tagLibraryMapper.allTagLibrary(name);
        return BaseOutputForm.success(list);
    }

    public BaseOutput createTagLibrary(TagLibraryAddForm form) {

        Integer count = tagLibraryMapper.countTagLibrary(form.Name);
        if (count > 0)
            return BaseOutput.failed("标签已存在");
        TagLibraryEntity tagLibraryEntity = new TagLibraryEntity();
        tagLibraryEntity.Name = form.Name;
        tagLibraryMapper.createTagLibrary(tagLibraryEntity);

        TagLibraryOutputForm tagLibraryOutputForm = new TagLibraryOutputForm();
        tagLibraryOutputForm.ID = tagLibraryEntity.ID;
        tagLibraryOutputForm.Name = form.Name;
        return BaseOutputForm.success(tagLibraryOutputForm);

    }

    public BaseOutput updateTagLibrary(TagLibraryUpdateForm form) {

        Integer count = tagLibraryMapper.countExceptByID(form.Name, form.ID);
        if (count > 0)
            return BaseOutput.failed("标签已存在");
        TagLibraryEntity tagLibraryEntity = new TagLibraryEntity();
        tagLibraryEntity.ID = form.ID;
        tagLibraryEntity.Name = form.Name;
        tagLibraryMapper.updateTagLibrary(tagLibraryEntity);
        return BaseOutput.success();
    }


    public BaseOutput deleteTagLibrary(TagLibraryDeleteForm form) {
        tagLibraryMapper.deleteTagLibrary(form.ID);
        tagLibraryMapper.deleteCustomerTag(form.ID);
        tagLibraryMapper.deleteNursingLogTag(form.ID);
        tagLibraryMapper.deleteProjectLogTag(form.ID);

        return BaseOutput.success();
    }
}