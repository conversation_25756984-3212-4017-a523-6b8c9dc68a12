package com.zhelian.service.CRM.callbackStatus;

import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.CRM.CallbackStatusMapper;
import com.zhelian.model.CRM.entity.CallbackStatusEntity;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.PayMethodAddForm;
import com.zhelian.model.ibeauty.form.input.PayMethodMoveForm;
import com.zhelian.model.ibeauty.form.input.PayMethodQueryForm;
import com.zhelian.model.ibeauty.form.input.PayMethodUpdateForm;
import com.zhelian.model.ibeauty.form.output.PayMethodOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;


@Service
public class CallbackStatusService {
    @Autowired
    CallbackStatusMapper callbackStatusMapper;

    public BaseOutput all(PayMethodQueryForm form) {
        List<PayMethodOutputForm> list = callbackStatusMapper.callbackStatusList(form.Name, form.Active);
        return BaseOutputForm.success(list);
    }

    public BaseOutput create(PayMethodAddForm form) {
        Integer Sequence = callbackStatusMapper.getMaxSequence();
        CallbackStatusEntity CallbackStatusEntity = new CallbackStatusEntity();
        CallbackStatusEntity.Name = form.Name;
        CallbackStatusEntity.Sequence = Sequence != null ? Sequence + 1 : 1;
        callbackStatusMapper.createCallbackStatus(CallbackStatusEntity);
        return BaseOutput.success();
    }

    public BaseOutput update(PayMethodUpdateForm form) {
        CallbackStatusEntity CallbackStatusEntity = new CallbackStatusEntity();
        CallbackStatusEntity.ID = form.ID;
        CallbackStatusEntity.Name = form.Name;
        CallbackStatusEntity.Active = form.Active;
        callbackStatusMapper.updateCallbackStatus(CallbackStatusEntity);

        return BaseOutput.success();
    }

    public BaseOutput move(PayMethodMoveForm form) {
        //重新排序
        LinkedList<Integer> linkedList = callbackStatusMapper.getIDOrderBySequenceASC(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<CallbackStatusEntity> list = new ArrayList<>();

        for (Integer ID : linkedList) {
            CallbackStatusEntity CallbackStatusEntity = new CallbackStatusEntity();
            CallbackStatusEntity.ID = ID;
            CallbackStatusEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(CallbackStatusEntity);
        }
        callbackStatusMapper.updateSequence(list);
        return BaseOutput.success();
    }

}