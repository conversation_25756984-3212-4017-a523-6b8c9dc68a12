package com.zhelian.service.CRM.callbackRule;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.CRM.CallbackRuleMapper;
import com.zhelian.model.CRM.entity.*;
import com.zhelian.model.CRM.form.input.*;
import com.zhelian.model.CRM.form.output.CallbackRuleDetailOutputForm;
import com.zhelian.model.CRM.form.output.CallbackRuleOutputForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class CallbackRuleService {
    @Autowired
    CallbackRuleMapper callbackRuleMapper;


    public BaseOutput all(CallbackRuleQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CallbackRuleOutputForm> list = callbackRuleMapper.callbackRuleList(form.Name);
        return BasePageInfo.success(list);
    }

    @Transactional
    public BaseOutput create(CallbackRuleAddForm form) {
        CallbackRuleEntity callbackRuleEntity = new CallbackRuleEntity();
        callbackRuleEntity.Name = form.Name;
        callbackRuleEntity.HandlerType = form.HandlerType;
        callbackRuleEntity.CallbackMethodID = form.CallbackMethodID;
        if (form.HandlerType.equals("10")) {
            callbackRuleEntity.EmployeeID = form.EmployeeID;
            callbackRuleEntity.ServicerID = form.ServicerID;
        } else if (form.HandlerType.equals("20")) {
            callbackRuleEntity.EmployeeID = form.EmployeeID;
            callbackRuleEntity.ProjectTreatHandlerID = form.ProjectTreatHandlerID;
            callbackRuleEntity.SavingCardProjectTreatHandlerID = form.SavingCardProjectTreatHandlerID;
        } else {
            callbackRuleEntity.EmployeeID = form.EmployeeID;
        }

        callbackRuleMapper.createCallbackRule(callbackRuleEntity);
        insertRuleDetail(form.Plan, form.Project, form.ProjectCategory, form.Entity, callbackRuleEntity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput update(CallbackRuleUpdateForm form) {
        CallbackRuleEntity callbackRuleEntity = callbackRuleMapper.getCallbackRule(form.CallbackRuleID);
        callbackRuleEntity.Name = form.Name;
        callbackRuleEntity.CallbackMethodID = form.CallbackMethodID;
        callbackRuleEntity.HandlerType = form.HandlerType;
        callbackRuleEntity.ServicerID = null;
        callbackRuleEntity.ProjectTreatHandlerID = null;
        callbackRuleEntity.SavingCardProjectTreatHandlerID = null;
        callbackRuleEntity.EmployeeID = null;
        if (form.HandlerType.equals("10")) {
            callbackRuleEntity.EmployeeID = form.EmployeeID;
            callbackRuleEntity.ServicerID = form.ServicerID;
        } else if (form.HandlerType.equals("20")) {
            callbackRuleEntity.EmployeeID = form.EmployeeID;
            callbackRuleEntity.ProjectTreatHandlerID = form.ProjectTreatHandlerID;
            callbackRuleEntity.SavingCardProjectTreatHandlerID = form.SavingCardProjectTreatHandlerID;
        } else {
            callbackRuleEntity.EmployeeID = form.EmployeeID;
        }
        callbackRuleMapper.updateCallbackRule(callbackRuleEntity);
        callbackRuleMapper.deleteCallbackRuleProject(form.CallbackRuleID);
        callbackRuleMapper.deleteCallbackRuleProjectCategory(form.CallbackRuleID);
        callbackRuleMapper.deleteCallbackRuleEntityEntity(form.CallbackRuleID);
        callbackRuleMapper.deleteCallbackRulePlan(form.CallbackRuleID);
        insertRuleDetail(form.Plan, form.Project, form.ProjectCategory, form.Entity, callbackRuleEntity);
        return BaseOutput.success();
    }

    private void insertRuleDetail(List<CallbackPlanAddForm> plan, Integer[] project, Integer[] projectCategory, Integer[] entity, CallbackRuleEntity callbackRuleEntity) {
        List<CallbackPlanEntity> planEntityList = new ArrayList<>();
        if (plan != null) {
            plan.forEach(i -> {
                CallbackPlanEntity callbackPlanEntity = new CallbackPlanEntity();
                callbackPlanEntity.CallbackRuleID = callbackRuleEntity.ID;
                callbackPlanEntity.CallbackCycle = i.CallbackCycle;
                callbackPlanEntity.CallbackContent = i.CallbackContent;
                planEntityList.add(callbackPlanEntity);
            });
        }
        List<CallbackRuleProjectEntity> projectEntityList = new ArrayList<>();
        if (project.length > 0) {
            for (Integer i : project) {
                CallbackRuleProjectEntity callbackRuleProjectEntity = new CallbackRuleProjectEntity();
                callbackRuleProjectEntity.CallbackRuleID = callbackRuleEntity.ID;
                callbackRuleProjectEntity.ProjectID = i;
                projectEntityList.add(callbackRuleProjectEntity);
            }
        }
        List<CallbackRuleProjectCategoryEntity> projectCategoryEntityList = new ArrayList<>();
        if (projectCategory.length > 0) {
            for (Integer i : projectCategory) {
                CallbackRuleProjectCategoryEntity callbackRuleProjectCategoryEntity = new CallbackRuleProjectCategoryEntity();
                callbackRuleProjectCategoryEntity.CallbackRuleID = callbackRuleEntity.ID;
                callbackRuleProjectCategoryEntity.ProjectCategoryID = i;
                projectCategoryEntityList.add(callbackRuleProjectCategoryEntity);
            }
        }
        List<CallbackRuleEntityEntity> entityList = new ArrayList<>();
        if (entity.length > 0) {
            for (Integer i : entity) {
                CallbackRuleEntityEntity callbackRuleEntityEntity = new CallbackRuleEntityEntity();
                callbackRuleEntityEntity.CallbackRuleID = callbackRuleEntity.ID;
                callbackRuleEntityEntity.EntityID = i;
                entityList.add(callbackRuleEntityEntity);
            }
        }

        if (planEntityList.size() > 0)
            callbackRuleMapper.insertCallbackPlan(planEntityList);

        if (projectEntityList.size() > 0)
            callbackRuleMapper.insertCallbackProject(projectEntityList);

        if (projectCategoryEntityList.size() > 0)
            callbackRuleMapper.insertCallbackProjectCategory(projectCategoryEntityList);

        if (entityList.size() > 0)
            callbackRuleMapper.insertCallbackEntity(entityList);
    }

    @Transactional
    public BaseOutput delete(CallbackRuleDeleteForm form) {
        callbackRuleMapper.deleteCallbackPlan(form.CallbackPlanID);
        List<CallbackPlanEntity> list = callbackRuleMapper.getCallbackPlan(form.CallbackRuleID, form.CallbackPlanID);
        if (list.size() == 0) {
            callbackRuleMapper.deleteCallbackRule(form.CallbackRuleID);
            callbackRuleMapper.deleteCallbackRuleProject(form.CallbackRuleID);
            callbackRuleMapper.deleteCallbackRuleProjectCategory(form.CallbackRuleID);
            callbackRuleMapper.deleteCallbackRuleEntityEntity(form.CallbackRuleID);
        }
        return BaseOutput.success();
    }

    public BaseOutput detail(CallbackRuleUpdateForm form) {
        CallbackRuleDetailOutputForm callbackRule = callbackRuleMapper.getCallbackRuleDetail(form.CallbackRuleID);
        callbackRule.Plan = callbackRuleMapper.getCallbackRulePlanDetail(form.CallbackRuleID);
        callbackRule.Project = callbackRuleMapper.getProjectList(form.CallbackRuleID);
        callbackRule.Entity = callbackRuleMapper.getCallbackRuleEntityDetail(form.CallbackRuleID);
        return BaseOutputForm.success(callbackRule);
    }
}