package com.zhelian.service.CRM.customerLevelDiscountConfig;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.CRM.CustomerLevelProductDiscountConfigMapper;
import com.zhelian.model.CRM.entity.*;
import com.zhelian.model.CRM.form.input.*;
import com.zhelian.model.CRM.form.output.CustomerLevelDiscountPriceProductAllOutputForm;
import com.zhelian.model.CRM.form.output.CustomerLevelDiscountPriceProductConfigAllOutputForm;
import com.zhelian.model.CRM.form.output.CustomerLevelProductCategoryDiscountConfigOutputForm;
import com.zhelian.model.CRM.form.output.CustomerLevelProductCategoryDiscountOutputForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static java.math.BigDecimal.ROUND_HALF_UP;

@Service
public class CustomerLevelProductDiscountConfigService {

    @Autowired
    CustomerLevelProductDiscountConfigMapper customerLevelProductDiscountConfigMapper;

    public BaseOutput customerLevelDiscountAll() {
        List<CustomerLevelProductCategoryDiscountOutputForm> list = customerLevelProductDiscountConfigMapper.customerLevelDiscountAll();
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput createCustomerLevelDiscountConfig(List<CustomerLevelProductDiscountConfigAddForm> form) {

        customerLevelProductDiscountConfigMapper.deleteCustomerLevelDiscountConfig();
        List<CustomerPriceProductSchemeEntity> list = new ArrayList<>();
        for (CustomerLevelProductDiscountConfigAddForm customerLevelProductDiscountConfigAddForm : form) {
            CustomerPriceProductSchemeEntity customerPriceProductSchemeEntity = new CustomerPriceProductSchemeEntity();
            customerPriceProductSchemeEntity.CustomerLevelID = customerLevelProductDiscountConfigAddForm.CustomerLevelID;
            if (customerLevelProductDiscountConfigAddForm.DiscountPrice != null){
                customerPriceProductSchemeEntity.DiscountPrice = customerLevelProductDiscountConfigAddForm.DiscountPrice.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
            }
            list.add(customerPriceProductSchemeEntity);
        }
        customerLevelProductDiscountConfigMapper.createCustomerLevelDiscountConfig(list);
        return BaseOutput.success();
    }


    public BaseOutput customerLevelCategoryDiscountAll(CustomerLevelProductCategoryDiscountConfigQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CustomerLevelProductCategoryDiscountConfigOutputForm> list = customerLevelProductDiscountConfigMapper.customerLevelCategoryDiscountAll(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput category() {
        return BaseOutputForm.success(ListUtil.listToTree(JSONArray.parseArray(JSON.toJSONString(customerLevelProductDiscountConfigMapper.category(), JSONWriter.Feature.WriteMapNullValue)), "ID", "ParentID", "Child"));
    }

    public BaseOutput selectedCategory() {
        List<Integer> list = customerLevelProductDiscountConfigMapper.selectedCategory();
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput createCustomerLevelCategoryDiscountConfig(List<CustomerLevelProductCategoryDiscountConfigAddForm> form) {

        List<CustomerPriceProductCategoryEntity> list = new ArrayList<>();
        for (CustomerLevelProductCategoryDiscountConfigAddForm customerLevelProductCategoryDiscountConfigAddForm : form) {
            CustomerPriceProductCategoryEntity customerPriceProductCategoryEntity = new CustomerPriceProductCategoryEntity();
            customerPriceProductCategoryEntity.CustomerLevelID = customerLevelProductCategoryDiscountConfigAddForm.CustomerLevelID;
            customerPriceProductCategoryEntity.ProductCategoryID = customerLevelProductCategoryDiscountConfigAddForm.ProductCategoryID;
            list.add(customerPriceProductCategoryEntity);
        }
        customerLevelProductDiscountConfigMapper.createCustomerLevelCategoryDiscountConfig(list);
        return BaseOutput.success();
    }


    @Transactional
    public BaseOutput updateCustomerLevelCategoryDiscountConfig(CustomerLevelProductCategoryDiscountConfigUpdateForm form) {

        customerLevelProductDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByProductCategoryID(form.ProductCategoryID);

        List<CustomerPriceProductCategoryEntity> list = new ArrayList<>();
        for (CustomerLevelProductCategoryDiscountUpdateForm customerLevelProductCategoryUpdateForm : form.CustomerLevelDiscountPriceList) {
            CustomerPriceProductCategoryEntity customerPriceProductCategoryEntity = new CustomerPriceProductCategoryEntity();
            customerPriceProductCategoryEntity.CustomerLevelID = customerLevelProductCategoryUpdateForm.CustomerLevelID;
            customerPriceProductCategoryEntity.ProductCategoryID = form.ProductCategoryID;
            if (customerLevelProductCategoryUpdateForm.DiscountPrice != null){
                customerPriceProductCategoryEntity.DiscountPrice = customerLevelProductCategoryUpdateForm.DiscountPrice.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
            }
            list.add(customerPriceProductCategoryEntity);
        }
        customerLevelProductDiscountConfigMapper.createCustomerLevelCategoryDiscountConfig(list);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput batchUpdateCustomerLevelCategoryDiscountConfig(List<CustomerLevelProductCategoryDiscountConfigUpdateForm> forms) {


        forms.forEach(form -> {
            customerLevelProductDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByProductCategoryID(form.ProductCategoryID);

            List<CustomerPriceProductCategoryEntity> list = new ArrayList<>();
            for (CustomerLevelProductCategoryDiscountUpdateForm customerLevelProductCategoryUpdateForm : form.CustomerLevelDiscountPriceList) {
                CustomerPriceProductCategoryEntity customerPriceProductCategoryEntity = new CustomerPriceProductCategoryEntity();
                customerPriceProductCategoryEntity.CustomerLevelID = customerLevelProductCategoryUpdateForm.CustomerLevelID;
                customerPriceProductCategoryEntity.ProductCategoryID = form.ProductCategoryID;
                if (customerLevelProductCategoryUpdateForm.DiscountPrice != null){
                    customerPriceProductCategoryEntity.DiscountPrice = customerLevelProductCategoryUpdateForm.DiscountPrice.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
                }
                list.add(customerPriceProductCategoryEntity);
            }
            customerLevelProductDiscountConfigMapper.createCustomerLevelCategoryDiscountConfig(list);
        });

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput deleteCustomerLevelCategoryDiscountConfig(CustomerLevelProductCategoryDiscountConfigDeleteForm form) {

        customerLevelProductDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByProductCategoryID(form.ProductCategoryID);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput batchDeleteCustomerLevelCategoryDiscountConfig(List<CustomerLevelProductCategoryDiscountConfigDeleteForm> forms) {

        forms.forEach(form -> {
            customerLevelProductDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByProductCategoryID(form.ProductCategoryID);
        });
        return BaseOutput.success();
    }

    public BaseOutput product(CustomerLevelProductDiscountPriceQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CustomerLevelDiscountPriceProductAllOutputForm> list = customerLevelProductDiscountConfigMapper.product(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput selectedProduct() {
        List<Integer> list = customerLevelProductDiscountConfigMapper.selectedProduct();
        return BaseOutputForm.success(list);
    }

    public BaseOutput customerLevelProductDiscountPriceAll(CustomerLevelProductDiscountPriceConfigQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<CustomerLevelDiscountPriceProductConfigAllOutputForm> list = customerLevelProductDiscountConfigMapper.customerLevelProductDiscountPriceAll(form.Name);
        return BasePageInfo.success(list);
    }

    @Transactional
    public BaseOutput createCustomerLevelProductDiscountPriceConfig(List<CustomerLevelProductDiscountPriceConfigAddForm> form) {

        List<CustomerPriceProductEntity> list = new ArrayList<>();
        for (CustomerLevelProductDiscountPriceConfigAddForm customerLevelProductDiscountPriceConfigAddForm : form) {
            CustomerPriceProductEntity customerPriceProductEntity = new CustomerPriceProductEntity();
            customerPriceProductEntity.CustomerLevelID = customerLevelProductDiscountPriceConfigAddForm.CustomerLevelID;
            customerPriceProductEntity.ProductID = customerLevelProductDiscountPriceConfigAddForm.ProductID;
            list.add(customerPriceProductEntity);
        }

        customerLevelProductDiscountConfigMapper.createCustomerLevelProductDiscountPriceConfig(list);
        return BaseOutput.success();
    }


    @Transactional
    public BaseOutput updateCustomerLevelProductDiscountConfig(CustomerLevelProductCardDiscountConfigUpdateForm form) {
        customerLevelProductDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByProductID(form.ProductID);

        List<CustomerPriceProductEntity> list = new ArrayList<>();
        for (CustomerLevelProductDiscountUpdateForm customerLevelProductDiscountUpdateForm : form.CustomerLevelDiscountPriceList) {
            CustomerPriceProductEntity customerPriceProductEntity = new CustomerPriceProductEntity();
            customerPriceProductEntity.CustomerLevelID = customerLevelProductDiscountUpdateForm.CustomerLevelID;
            customerPriceProductEntity.ProductID = form.ProductID;
            customerPriceProductEntity.PriceType = customerLevelProductDiscountUpdateForm.PriceType;
            if (customerLevelProductDiscountUpdateForm.DiscountPrice != null){
                if (customerPriceProductEntity.PriceType.equals(1)) {
                    customerPriceProductEntity.DiscountPrice = customerLevelProductDiscountUpdateForm.DiscountPrice.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
                } else {
                    customerPriceProductEntity.DiscountPrice = customerLevelProductDiscountUpdateForm.DiscountPrice;
                }
            }

            list.add(customerPriceProductEntity);
        }

        customerLevelProductDiscountConfigMapper.createCustomerLevelProductDiscountPriceConfig(list);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput batchUpdateCustomerLevelProductDiscountConfig(List<CustomerLevelProductCardDiscountConfigUpdateForm> forms) {

        forms.forEach(form -> {
            customerLevelProductDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByProductID(form.ProductID);

            List<CustomerPriceProductEntity> list = new ArrayList<>();
            for (CustomerLevelProductDiscountUpdateForm customerLevelProductDiscountUpdateForm : form.CustomerLevelDiscountPriceList) {
                CustomerPriceProductEntity customerPriceProductEntity = new CustomerPriceProductEntity();
                customerPriceProductEntity.CustomerLevelID = customerLevelProductDiscountUpdateForm.CustomerLevelID;
                customerPriceProductEntity.ProductID = form.ProductID;
                customerPriceProductEntity.PriceType = customerLevelProductDiscountUpdateForm.PriceType;
                if (customerLevelProductDiscountUpdateForm.DiscountPrice != null){
                    if (customerPriceProductEntity.PriceType.equals(1)) {
                        customerPriceProductEntity.DiscountPrice = customerLevelProductDiscountUpdateForm.DiscountPrice.divide(new BigDecimal(10), 2, ROUND_HALF_UP);
                    } else {
                        customerPriceProductEntity.DiscountPrice = customerLevelProductDiscountUpdateForm.DiscountPrice;
                    }
                }

                list.add(customerPriceProductEntity);
            }

            customerLevelProductDiscountConfigMapper.createCustomerLevelProductDiscountPriceConfig(list);
        });

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput deleteCustomerLevelProductDiscountConfig(CustomerLevelProductDiscountConfigDeleteForm form) {
        customerLevelProductDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByProductID(form.ProductID);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput batchDeleteCustomerLevelProductDiscountConfig(List<CustomerLevelProductDiscountConfigDeleteForm> forms) {

        forms.forEach(form -> {
            customerLevelProductDiscountConfigMapper.deleteCustomerLevelCategoryDiscountConfigByProductID(form.ProductID);
        });
        return BaseOutput.success();
    }
}
