package com.zhelian.service.psi.inventoryAllocationApply;

import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.autoNumber.AutoNumberUtils;
import com.zhelian.core.utils.enumeration.BillCode;
import com.zhelian.mapper.psi.InventoryAllocationApplyMapper;
import com.zhelian.mapper.psi.StockMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.psi.entity.*;
import com.zhelian.model.psi.form.input.*;
import com.zhelian.model.psi.form.output.EntityOutputForm;
import com.zhelian.model.psi.form.output.InventoryAllocationApplyBillStatusNumberOutputForm;
import com.zhelian.model.psi.form.output.InventoryAllocationApplyOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class InventoryAllocationApplyService {

    @Autowired
    InventoryAllocationApplyMapper inventoryAllocationApplyMapper;

    @Autowired
    StockMapper stockMapper;

    public BaseOutput inventoryAllocationApplyList(InventoryAllocationApplyQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<InventoryAllocationApplyOutputForm> list = inventoryAllocationApplyMapper.inventoryAllocationApplyList(form.ID, form.OutboundEntityID, form.InboundEntityID, form.BillStatus, form.StartDate, form.EndDate, SystemUserUtil.getSystemUserID(), form.ProductName);
        return BasePageInfo.success(list);
    }

    public BaseOutput inventoryAllocationApplyInfo(String ID) {
        return BaseOutputForm.success(inventoryAllocationApplyMapper.inventoryAllocationApplyInfo(ID, SystemUserUtil.getSystemUserID()));
    }

    @Transactional
    public BaseOutput create(InventoryAllocationApplyAddForm form) {

        InventoryAllocationApplyEntity inventoryAllocationApplyEntity = new InventoryAllocationApplyEntity();
        inventoryAllocationApplyEntity.ID = AutoNumberUtils.getPSIInventoryAllocationApplyID();
        inventoryAllocationApplyEntity.BillStatus = BillCode.InventoryAllocationApplyBillStatus_10.getID();
        inventoryAllocationApplyEntity.InboundEntityID = form.InboundEntityID;
        inventoryAllocationApplyEntity.OutboundEntityID = form.OutboundEntityID;
        inventoryAllocationApplyEntity.Remark = form.Remark;
        inventoryAllocationApplyEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        inventoryAllocationApplyEntity.CreatedOn = new Date();
        inventoryAllocationApplyMapper.insertInventoryAllocationApplyEntity(inventoryAllocationApplyEntity);

        List<InventoryAllocationApplyDetailEntity> inventoryAllocationApplyDetailEntities = new ArrayList<>();
        for (InventoryAllocationApplyDetailAddForm inventoryAllocationApplyDetailAddForm : form.InventoryAllocationApplyDetail) {
            InventoryAllocationApplyDetailEntity inventoryAllocationApplyDetailEntity = new InventoryAllocationApplyDetailEntity();
            inventoryAllocationApplyDetailEntity.InventoryAllocationApplyID = inventoryAllocationApplyEntity.ID;
            inventoryAllocationApplyDetailEntity.ProductID = inventoryAllocationApplyDetailAddForm.ProductID;
            inventoryAllocationApplyDetailEntity.UnitID = inventoryAllocationApplyDetailAddForm.UnitID;
            inventoryAllocationApplyDetailEntity.MinimumUnitID = inventoryAllocationApplyDetailAddForm.MinimumUnitID;
            inventoryAllocationApplyDetailEntity.ApplyQuantity = inventoryAllocationApplyDetailAddForm.ApplyQuantity;
            inventoryAllocationApplyDetailEntity.ApplyMinimumUnitQuantity = inventoryAllocationApplyDetailAddForm.ApplyMinimumUnitQuantity;
            inventoryAllocationApplyDetailEntities.add(inventoryAllocationApplyDetailEntity);
        }
        if (inventoryAllocationApplyDetailEntities.size() > 0) {
            inventoryAllocationApplyMapper.insertInventoryAllocationApplyDetailEntity(inventoryAllocationApplyDetailEntities);
        }
        return BaseOutputForm.success(inventoryAllocationApplyEntity.ID);
    }

    @Transactional
    public BaseOutput outbound(InventoryAllocationApplyOutboundForm form) {
        InventoryAllocationApplyEntity inventoryAllocationApplyEntity = inventoryAllocationApplyMapper.inventoryAllocationApplyByID(form.ID);
        if (!inventoryAllocationApplyEntity.BillStatus.equals(BillCode.InventoryAllocationApplyBillStatus_10.getID())) {
            return BaseOutput.failed("该调拨单状态不需要配送");
        }
        if (form.BillStatus.equals(BillCode.InventoryAllocationApplyBillStatus_20.getID())) {

            inventoryAllocationApplyEntity.OutboundBillID = AutoNumberUtils.getPSIInventoryAllocationOutboundID();
            inventoryAllocationApplyEntity.ApprovedBy = SystemUserUtil.getSystemUserID();
            inventoryAllocationApplyEntity.ApprovedOn = new Date();
            inventoryAllocationApplyEntity.BillStatus = BillCode.InventoryAllocationApplyBillStatus_20.getID();
            inventoryAllocationApplyMapper.updateInventoryAllocationApply(inventoryAllocationApplyEntity);

            InventoryAllocationOutboundEntity inventoryAllocationOutboundEntity = new InventoryAllocationOutboundEntity();
            inventoryAllocationOutboundEntity.ID = inventoryAllocationApplyEntity.OutboundBillID;
            inventoryAllocationOutboundEntity.EntityID = inventoryAllocationApplyEntity.OutboundEntityID;
            inventoryAllocationOutboundEntity.OutDate = new Date();
            inventoryAllocationOutboundEntity.CreatedBy = SystemUserUtil.getSystemUserID();
            inventoryAllocationOutboundEntity.CreatedOn = new Date();
            inventoryAllocationOutboundEntity.Remark = form.Remark;
            inventoryAllocationApplyMapper.insertInventoryAllocationOutboundEntity(inventoryAllocationOutboundEntity);
            List<InventoryAllocationOutboundDetailEntity> inventoryAllocationOutboundDetailEntityArrayList = new ArrayList<>();
            for (InventoryAllocationApplyOutboundDetailForm detailForm : form.InventoryAllocationApplyDetail) {
                InventoryAllocationApplyDetailEntity inventoryAllocationApplyDetailEntity = inventoryAllocationApplyMapper.inventoryAllocationApplyDetailByID(detailForm.ID);
                inventoryAllocationApplyDetailEntity.OutboundQuantity = detailForm.OutboundQuantity;
                inventoryAllocationApplyDetailEntity.OutboundMinimumUnitQuantity = detailForm.OutboundMinimumUnitQuantity;
                inventoryAllocationApplyMapper.updateInventoryAllocationApplyDetail(inventoryAllocationApplyDetailEntity);


                StockEntity stockEntity = stockMapper.stockInfo(new StockEntity(inventoryAllocationApplyEntity.OutboundEntityID, inventoryAllocationApplyDetailEntity.ProductID));
                InventoryAllocationOutboundDetailEntity inventoryAllocationOutboundDetailEntity = new InventoryAllocationOutboundDetailEntity();
                inventoryAllocationOutboundDetailEntity.OutboundBillID = inventoryAllocationOutboundEntity.ID;
                inventoryAllocationOutboundDetailEntity.ProductID = inventoryAllocationApplyDetailEntity.ProductID;
                inventoryAllocationOutboundDetailEntity.UnitID = inventoryAllocationApplyDetailEntity.UnitID;
                inventoryAllocationOutboundDetailEntity.MinimumUnitID = inventoryAllocationApplyDetailEntity.MinimumUnitID;
                inventoryAllocationOutboundDetailEntity.Quantity = inventoryAllocationApplyDetailEntity.OutboundQuantity;
                inventoryAllocationOutboundDetailEntity.MinimumUnitQuantity = inventoryAllocationApplyDetailEntity.OutboundMinimumUnitQuantity;
                inventoryAllocationOutboundDetailEntity.StockQuantity = stockEntity.Quantity - inventoryAllocationOutboundDetailEntity.MinimumUnitQuantity;
                inventoryAllocationOutboundDetailEntityArrayList.add(inventoryAllocationOutboundDetailEntity);
                stockEntity.Quantity = inventoryAllocationOutboundDetailEntity.StockQuantity;
                stockMapper.updateStockEntity(stockEntity);
            }

            inventoryAllocationApplyMapper.insertInventoryAllocationOutboundDetailEntity(inventoryAllocationOutboundDetailEntityArrayList);

        } else {
            inventoryAllocationApplyEntity.ApprovedBy = SystemUserUtil.getSystemUserID();
            inventoryAllocationApplyEntity.ApprovedOn = new Date();
            inventoryAllocationApplyEntity.BillStatus = BillCode.InventoryAllocationApplyBillStatus_30.getID();
            inventoryAllocationApplyEntity.RejectReason = form.RejectReason;
            inventoryAllocationApplyMapper.updateInventoryAllocationApply(inventoryAllocationApplyEntity);
        }

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput inbound(InventoryAllocationApplyInboundForm form) {
        InventoryAllocationApplyEntity inventoryAllocationApplyEntity = inventoryAllocationApplyMapper.inventoryAllocationApplyByID(form.ID);
        if (!inventoryAllocationApplyEntity.BillStatus.equals(BillCode.InventoryAllocationApplyBillStatus_20.getID())) {
            return BaseOutput.failed("该调拨单状态不需要入库");
        }
        inventoryAllocationApplyEntity.InboundBillID = AutoNumberUtils.getPSIInventoryAllocationInboundID();
        inventoryAllocationApplyEntity.BillStatus = BillCode.InventoryAllocationApplyBillStatus_40.getID();
        inventoryAllocationApplyMapper.updateInventoryAllocationApply(inventoryAllocationApplyEntity);

        InventoryAllocationInboundEntity inventoryAllocationInboundEntity = new InventoryAllocationInboundEntity();
        inventoryAllocationInboundEntity.ID = inventoryAllocationApplyEntity.InboundBillID;
        inventoryAllocationInboundEntity.EntityID = inventoryAllocationApplyEntity.InboundEntityID;
        inventoryAllocationInboundEntity.InDate = new Date();
        inventoryAllocationInboundEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        inventoryAllocationInboundEntity.CreatedOn = new Date();
        inventoryAllocationInboundEntity.Remark = form.Remark;
        inventoryAllocationApplyMapper.insertInventoryAllocationInboundEntity(inventoryAllocationInboundEntity);
        List<InventoryAllocationInboundDetailEntity> inventoryAllocationInboundDetailEntityArrayList = new ArrayList<>();
        for (InventoryAllocationApplyInboundDetailForm detailForm : form.InventoryAllocationApplyDetail) {
            InventoryAllocationApplyDetailEntity inventoryAllocationApplyDetailEntity = inventoryAllocationApplyMapper.inventoryAllocationApplyDetailByID(detailForm.ID);
            inventoryAllocationApplyDetailEntity.InboundQuantity = detailForm.InboundQuantity;
            inventoryAllocationApplyDetailEntity.InboundMinimumUnitQuantity = detailForm.InboundMinimumUnitQuantity;
            inventoryAllocationApplyMapper.updateInventoryAllocationApplyDetail(inventoryAllocationApplyDetailEntity);

            StockEntity stockEntity = stockMapper.stockInfo(new StockEntity(inventoryAllocationApplyEntity.InboundEntityID, inventoryAllocationApplyDetailEntity.ProductID));
            int stockQuantity = 0;
            if (stockEntity != null) {
                stockQuantity = stockEntity.Quantity;
            }
            InventoryAllocationInboundDetailEntity inventoryAllocationInboundDetailEntity = new InventoryAllocationInboundDetailEntity();
            inventoryAllocationInboundDetailEntity.InboundBillID = inventoryAllocationInboundEntity.ID;
            inventoryAllocationInboundDetailEntity.ProductID = inventoryAllocationApplyDetailEntity.ProductID;
            inventoryAllocationInboundDetailEntity.UnitID = inventoryAllocationApplyDetailEntity.UnitID;
            inventoryAllocationInboundDetailEntity.MinimumUnitID = inventoryAllocationApplyDetailEntity.MinimumUnitID;
            inventoryAllocationInboundDetailEntity.Quantity = inventoryAllocationApplyDetailEntity.InboundQuantity;
            inventoryAllocationInboundDetailEntity.MinimumUnitQuantity = inventoryAllocationApplyDetailEntity.InboundMinimumUnitQuantity;
            inventoryAllocationInboundDetailEntity.StockQuantity = inventoryAllocationInboundDetailEntity.MinimumUnitQuantity + stockQuantity;
            inventoryAllocationInboundDetailEntityArrayList.add(inventoryAllocationInboundDetailEntity);
            if (stockEntity != null) {
                stockEntity.Quantity = inventoryAllocationInboundDetailEntity.StockQuantity;
                stockMapper.updateStockEntity(stockEntity);
            } else {
                stockMapper.insertStockEntity(new StockEntity(inventoryAllocationApplyEntity.InboundEntityID, inventoryAllocationInboundDetailEntity.ProductID, inventoryAllocationInboundDetailEntity.StockQuantity));
            }

        }
        inventoryAllocationApplyMapper.insertInventoryAllocationInboundDetailEntity(inventoryAllocationInboundDetailEntityArrayList);

        return BaseOutput.success();
    }

    public BaseOutput cancel(InventoryAllocationApplyCancelForm form) {
        InventoryAllocationApplyEntity inventoryAllocationApplyEntity = inventoryAllocationApplyMapper.inventoryAllocationApplyByID(form.ID);
        if (!inventoryAllocationApplyEntity.BillStatus.equals(BillCode.InventoryAllocationApplyBillStatus_10.getID())) {
            return BaseOutput.failed("该调拨单状态不能取消");
        }
        inventoryAllocationApplyEntity.BillStatus = BillCode.InventoryAllocationApplyBillStatus_50.getID();
        inventoryAllocationApplyMapper.updateInventoryAllocationApply(inventoryAllocationApplyEntity);
        return BaseOutput.success();
    }

    public BaseOutput billStatusNumber(InventoryAllocationApplyBillStatusNumberForm form) {
        InventoryAllocationApplyBillStatusNumberOutputForm outputForm = new InventoryAllocationApplyBillStatusNumberOutputForm();
        outputForm.BillStatus10 = inventoryAllocationApplyMapper.getStatusNumber(form.ID, form.OutboundEntityID, form.InboundEntityID, "10", form.StartDate, form.EndDate, SystemUserUtil.getSystemUserID(), form.ProductName);
        ;
        outputForm.BillStatus20 = inventoryAllocationApplyMapper.getStatusNumber(form.ID, form.OutboundEntityID, form.InboundEntityID, "20", form.StartDate, form.EndDate, SystemUserUtil.getSystemUserID(), form.ProductName);
        ;
        outputForm.BillStatus30 = inventoryAllocationApplyMapper.getStatusNumber(form.ID, form.OutboundEntityID, form.InboundEntityID, "30", form.StartDate, form.EndDate, SystemUserUtil.getSystemUserID(), form.ProductName);
        ;

        return BaseOutputForm.success(outputForm);
    }

    public BaseOutput outEntity() {
        List<EntityOutputForm> list = inventoryAllocationApplyMapper.outEntity();
        return BaseOutputForm.success(list);
    }
}
