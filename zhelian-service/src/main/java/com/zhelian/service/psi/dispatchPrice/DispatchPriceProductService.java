package com.zhelian.service.psi.dispatchPrice;

import com.zhelian.mapper.psi.DispatchPriceProductMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.psi.entity.ProductDispatchPriceEntity;
import com.zhelian.model.psi.form.input.DispatchPriceCategoryQueryForm;
import com.zhelian.model.psi.form.input.DispatchPriceProductAddForm;
import com.zhelian.model.psi.form.input.DispatchPriceProductDiscountAddForm;
import com.zhelian.model.psi.form.output.DispatchPriceProductOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class DispatchPriceProductService {

    @Autowired
    DispatchPriceProductMapper dispatchPriceProductMapper;


    public BaseOutput dispatchPriceProductList(DispatchPriceCategoryQueryForm form) {
        List<DispatchPriceProductOutputForm> list = dispatchPriceProductMapper.dispatchPriceProductList(form.ID, form.CategoryID);
        return BaseOutputForm.success(list);
    }


    @Transactional
    public BaseOutput update(DispatchPriceProductAddForm form) {
        dispatchPriceProductMapper.deleteDispatchPriceProduct(form.ID, form.CategoryID);
        List<ProductDispatchPriceEntity> list = new ArrayList<>();
        if (form.Product != null && form.Product.size() > 0) {
            for (DispatchPriceProductDiscountAddForm dispatchPriceProductDiscountAddForm : form.Product) {
                if (dispatchPriceProductDiscountAddForm.DispatchPrice != null) {
                    ProductDispatchPriceEntity productCategoryDispatchPriceEntity = new ProductDispatchPriceEntity();
                    productCategoryDispatchPriceEntity.ProductDispatchPriceSchemeID = form.ID;
                    productCategoryDispatchPriceEntity.ProductID = dispatchPriceProductDiscountAddForm.ProductID;
                    productCategoryDispatchPriceEntity.DispatchPrice = dispatchPriceProductDiscountAddForm.DispatchPrice;

                    list.add(productCategoryDispatchPriceEntity);
                }
            }
            dispatchPriceProductMapper.insertProductDispatchPrice(list);
        }
        return BaseOutput.success();
    }
}
