package com.zhelian.service.psi.product;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.ibeauty.ProductSaleMapper;
import com.zhelian.mapper.psi.ProductBarCodeMapper;
import com.zhelian.mapper.psi.ProductMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.TbProductEntity;
import com.zhelian.model.psi.entity.ProductBarCodeEntity;
import com.zhelian.model.psi.entity.ProductEntity;
import com.zhelian.model.psi.form.input.ProductAddForm;
import com.zhelian.model.psi.form.input.ProductQueryForm;
import com.zhelian.model.psi.form.input.ProductUpdateForm;
import com.zhelian.model.psi.form.output.ProductOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

@Service
public class ProductService {

    @Autowired
    ProductMapper productMapper;
    @Autowired
    ProductBarCodeMapper productBarCodeMapper;
    @Autowired
    ProductSaleMapper productSaleMapper;

    public BaseOutput productList(ProductQueryForm form) {

        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<ProductOutputForm> list = productMapper.productList(form.Name, form.Active, form.PCategoryID, form.ProductBrandID);
        return BasePageInfo.success(list);
    }

    @Transactional
    public BaseOutput insertProduct(ProductAddForm form) {

        if (StringUtils.hasText(form.BarCode) && productBarCodeMapper.countBarCodeExclude(form.BarCode, null, null) > 0) {
            return BaseOutput.failed("产品条形码已经存在，请重新填写");
        }
        ProductEntity productEntity = new ProductEntity();

        productEntity.Name = form.Name;
        productEntity.Alias = form.Alias;
        productEntity.PCategoryID = form.PCategoryID;
        productEntity.Specification = form.Specification;
        productEntity.Active = form.Active;
        productEntity.ProductBrandID = form.ProductBrandID;
        productEntity.Remark = form.Remark;
        productEntity.DeliveryPrice = form.DeliveryPrice;

        productMapper.insertProduct(productEntity);

        ProductBarCodeEntity productBarCodeEntity = new ProductBarCodeEntity();

        productBarCodeEntity.ProductID = productEntity.ID;
        productBarCodeEntity.BarCode = form.BarCode;
        productBarCodeEntity.UnitID = form.UnitID;
        productBarCodeEntity.Amount = 1;
        productBarCodeEntity.IsMinimumUnit = true;
        productBarCodeEntity.IsDefautSendReceive = true;

        productBarCodeMapper.insertProductBarCode(productBarCodeEntity);


        if (form.Price != null && !form.Price.equals("")) {
            TbProductEntity tbProductEntity = new TbProductEntity();
            tbProductEntity.Price = form.Price;
            tbProductEntity.ProductID = productEntity.ID;
            tbProductEntity.Memo = "";

            productSaleMapper.insertProduct(tbProductEntity);
        }

        return BaseOutput.success();
    }

    public BaseOutput updateProduct(ProductUpdateForm updateForm) {

        if (StringUtils.hasText(updateForm.BarCode) && productBarCodeMapper.countBarCodeExclude(updateForm.BarCode, null, updateForm.ID) > 0) {
            return BaseOutput.failed("产品条形码已存在，请重新填写");
        }

        ProductEntity productEntity = new ProductEntity();
        productEntity.ID = updateForm.ID;
        productEntity.Name = updateForm.Name;
        productEntity.Alias = updateForm.Alias;
        productEntity.PCategoryID = updateForm.PCategoryID;
        productEntity.Specification = updateForm.Specification;
        productEntity.Active = updateForm.Active;
        productEntity.ProductBrandID = updateForm.ProductBrandID;
        productEntity.Remark = updateForm.Remark;
        productEntity.DeliveryPrice = updateForm.DeliveryPrice;
        productMapper.updateProduct(productEntity);

        ProductBarCodeEntity productBarCodeEntity = new ProductBarCodeEntity();
        productBarCodeEntity.ProductID = updateForm.ID;
        productBarCodeEntity.BarCode = updateForm.BarCode;
        productBarCodeMapper.updateProductBarCodeByIsMinimumUnit(productBarCodeEntity);

        productSaleMapper.deleteProduct(updateForm.ID);
        if (updateForm.Price != null && !updateForm.Price.equals("")) {
            TbProductEntity tbProductEntity = new TbProductEntity();
            tbProductEntity.Price = updateForm.Price;
            tbProductEntity.ProductID = productEntity.ID;
            tbProductEntity.Memo = "";
            productSaleMapper.insertProduct(tbProductEntity);
        }
        return BaseOutput.success();
    }

}
