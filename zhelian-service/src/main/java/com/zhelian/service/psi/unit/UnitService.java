package com.zhelian.service.psi.unit;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.psi.UnitMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.psi.entity.UnitEntity;
import com.zhelian.model.psi.form.input.UnitAddForm;
import com.zhelian.model.psi.form.input.UnitQueryForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UnitService {

    @Autowired
    UnitMapper unitMapper;

    public BaseOutput unitList(UnitQueryForm form) {

        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<UnitEntity> list = unitMapper.unitList(form.Name, form.Active);
        return BasePageInfo.success(list);
    }

    public BaseOutput all() {
        List<UnitEntity> list = unitMapper.unitList(null, true);
        return BaseOutputForm.success(list);
    }

    public BaseOutput addUnit(UnitAddForm form) {

        UnitEntity unitEntity = new UnitEntity();
        unitEntity.Name = form.Name;
        if (unitMapper.countUnit(unitEntity) > 0) {
            return BaseOutput.failed("包装单位已存在");
        }
        unitMapper.addUnit(unitEntity);
        return BaseOutput.success();
    }

    public BaseOutput updateUnit(UnitEntity unitEntity) {
        if (unitEntity.Active == false && unitMapper.getProductCountByUnit(unitEntity.ID) > 0) {
            return BaseOutput.failed("有产品在使用当前单位");
        }

        if (unitEntity.Active && unitMapper.countUnit(unitEntity) > 0) {
            return BaseOutput.failed("包装单位名称已存在");
        }
        unitMapper.updateUnit(unitEntity);
        return BaseOutput.success();
    }

}
