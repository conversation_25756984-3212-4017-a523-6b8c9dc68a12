package com.zhelian.service.psi.inventoryCheck;

import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.autoNumber.AutoNumberUtils;
import com.zhelian.core.utils.enumeration.BillCode;
import com.zhelian.mapper.psi.InventoryCheckMapper;
import com.zhelian.mapper.psi.StockMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.psi.entity.InventoryCheckDetailEntity;
import com.zhelian.model.psi.entity.InventoryCheckEntity;
import com.zhelian.model.psi.entity.StockEntity;
import com.zhelian.model.psi.form.input.InventoryCheckAddForm;
import com.zhelian.model.psi.form.input.InventoryCheckDetailAddForm;
import com.zhelian.model.psi.form.input.InventoryCheckQueryForm;
import com.zhelian.model.psi.form.input.InventoryCheckUpdateForm;
import com.zhelian.model.psi.form.output.InventoryCheckOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class InventoryCheckService {

    @Autowired
    InventoryCheckMapper inventoryCheckMapper;

    @Autowired
    StockMapper stockMapper;

    public BaseOutput inventoryCheckList(InventoryCheckQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<InventoryCheckOutputForm> list = inventoryCheckMapper.inventoryCheckList(form.ID, form.EntityID, form.BillStatus, form.StartDate, form.EndDate, SystemUserUtil.getSystemUserID(), form.ProductName);
        return BasePageInfo.success(list);
    }

    public BaseOutput inventoryCheckInfo(String ID) {
        return BaseOutputForm.success(inventoryCheckMapper.inventoryCheckInfo(ID));
    }

    @Transactional
    public BaseOutput create(InventoryCheckAddForm form) {

        InventoryCheckEntity inventoryCheckEntity = new InventoryCheckEntity();
        inventoryCheckEntity.ID = AutoNumberUtils.getPSIInventoryCheckID();
        inventoryCheckEntity.BillStatus = form.BillStatus;
        inventoryCheckEntity.EntityID = form.EntityID;
        inventoryCheckEntity.Remark = form.Remark;
        inventoryCheckEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        inventoryCheckEntity.CreatedOn = new Date();
        inventoryCheckMapper.insertInventoryCheckEntity(inventoryCheckEntity);

        List<InventoryCheckDetailEntity> inventoryCheckDetailEntities = new ArrayList<>();
        for (InventoryCheckDetailAddForm inventoryCheckDetailAddForm : form.InventoryCheckDetail) {
            InventoryCheckDetailEntity inventoryCheckDetailEntity = new InventoryCheckDetailEntity();
            inventoryCheckDetailEntity.InventoryCheckID = inventoryCheckEntity.ID;
            inventoryCheckDetailEntity.ProductID = inventoryCheckDetailAddForm.ProductID;
            inventoryCheckDetailEntity.UnitID = inventoryCheckDetailAddForm.UnitID;
            inventoryCheckDetailEntity.StockQuantity = inventoryCheckDetailAddForm.StockQuantity;
            inventoryCheckDetailEntity.BeforeStockQuantity = inventoryCheckDetailAddForm.BeforeStockQuantity;
            inventoryCheckDetailEntity.Quantity = inventoryCheckDetailAddForm.Quantity;
            inventoryCheckDetailEntity.Remark = inventoryCheckDetailAddForm.Remark;
            inventoryCheckDetailEntities.add(inventoryCheckDetailEntity);
            if (inventoryCheckEntity.BillStatus.equals(BillCode.InventoryCheckBillStatus_20.getID())) {
                StockEntity stockEntity = stockMapper.stockInfo(new StockEntity(inventoryCheckEntity.EntityID, inventoryCheckDetailEntity.ProductID));
                if (stockEntity == null) {
                    stockMapper.insertStockEntity(new StockEntity(inventoryCheckEntity.EntityID, inventoryCheckDetailEntity.ProductID, inventoryCheckDetailEntity.StockQuantity));
                } else {
                    stockEntity.Quantity = inventoryCheckDetailEntity.StockQuantity;
                    stockMapper.updateStockEntity(stockEntity);
                }
            }
        }
        if (inventoryCheckDetailEntities.size() > 0) {
            inventoryCheckMapper.insertInventoryCheckDetailEntity(inventoryCheckDetailEntities);
        }
        return BaseOutputForm.success(inventoryCheckEntity.ID);
    }

    @Transactional
    public BaseOutput update(InventoryCheckUpdateForm form) {

        InventoryCheckEntity inventoryCheckEntity = inventoryCheckMapper.inventoryCheckInfoByID(form.ID);
        inventoryCheckEntity.ID = form.ID;
        inventoryCheckEntity.BillStatus = form.BillStatus;
        if (!form.BillStatus.equals(BillCode.InventoryCheckBillStatus_30.getID())) {
            inventoryCheckEntity.Remark = form.Remark;
        }
        inventoryCheckMapper.updateInventoryCheck(inventoryCheckEntity);
        if (!form.BillStatus.equals(BillCode.InventoryCheckBillStatus_30.getID())) {
            inventoryCheckMapper.deleteInventoryCheckDetailByInventoryCheckID(form.ID);
            List<InventoryCheckDetailEntity> inventoryCheckDetailEntities = new ArrayList<>();
            for (InventoryCheckDetailAddForm inventoryCheckDetailAddForm : form.InventoryCheckDetail) {
                InventoryCheckDetailEntity inventoryCheckDetailEntity = new InventoryCheckDetailEntity();
                inventoryCheckDetailEntity.InventoryCheckID = inventoryCheckEntity.ID;
                inventoryCheckDetailEntity.ProductID = inventoryCheckDetailAddForm.ProductID;
                inventoryCheckDetailEntity.UnitID = inventoryCheckDetailAddForm.UnitID;
                inventoryCheckDetailEntity.StockQuantity = inventoryCheckDetailAddForm.StockQuantity;
                inventoryCheckDetailEntity.BeforeStockQuantity = inventoryCheckDetailAddForm.BeforeStockQuantity;
                inventoryCheckDetailEntity.Quantity = inventoryCheckDetailAddForm.Quantity;
                inventoryCheckDetailEntity.Remark = inventoryCheckDetailAddForm.Remark;
                inventoryCheckDetailEntities.add(inventoryCheckDetailEntity);
                if (inventoryCheckEntity.BillStatus.equals(BillCode.InventoryCheckBillStatus_20.getID())) {
                    StockEntity stockEntity = stockMapper.stockInfo(new StockEntity(inventoryCheckEntity.EntityID, inventoryCheckDetailEntity.ProductID));
                    if (stockEntity == null) {
                        stockMapper.insertStockEntity(new StockEntity(inventoryCheckEntity.EntityID, inventoryCheckDetailEntity.ProductID, inventoryCheckDetailEntity.StockQuantity));
                    } else {
                        stockEntity.Quantity = inventoryCheckDetailEntity.StockQuantity;
                        stockMapper.updateStockEntity(stockEntity);
                    }
                }
            }
            if (inventoryCheckDetailEntities.size() > 0) {
                inventoryCheckMapper.insertInventoryCheckDetailEntity(inventoryCheckDetailEntities);
            }
        }
        return BaseOutputForm.success(inventoryCheckEntity.ID);
    }

}
