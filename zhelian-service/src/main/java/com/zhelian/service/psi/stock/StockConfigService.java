package com.zhelian.service.psi.stock;

import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.psi.StockConfigMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.psi.entity.StockConfigEntity;
import com.zhelian.model.psi.form.input.StockConfigAddForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class StockConfigService {

    @Autowired
    StockConfigMapper stockConfigMapper;

    public BaseOutput stockConfigAll() {
        StockConfigEntity stockConfigEntity = stockConfigMapper.stockConfigAll(SystemUserUtil.getSystemEntityID());
        if (stockConfigEntity == null) {
            stockConfigEntity = new StockConfigEntity();
            stockConfigEntity.EntityID = SystemUserUtil.getSystemEntityID();
            stockConfigEntity.IsAllowNegativeOutbound = true;
            stockConfigMapper.createStockConfig(stockConfigEntity);
        }
        return BaseOutputForm.success(stockConfigEntity);
    }

    public BaseOutput createStockConfig(StockConfigAddForm form) {
        StockConfigEntity stockConfigEntity = stockConfigMapper.stockConfigAll(SystemUserUtil.getSystemEntityID());
        if (stockConfigEntity != null) {
            stockConfigEntity.IsAllowNegativeOutbound = form.IsAllowNegativeOutbound;
            stockConfigMapper.updateStockConfig(stockConfigEntity);
        } else {
            stockConfigEntity = new StockConfigEntity();
            stockConfigEntity.EntityID = SystemUserUtil.getSystemEntityID();
            stockConfigEntity.IsAllowNegativeOutbound = form.IsAllowNegativeOutbound;
            stockConfigMapper.createStockConfig(stockConfigEntity);
        }
        return BaseOutput.success();
    }
}
