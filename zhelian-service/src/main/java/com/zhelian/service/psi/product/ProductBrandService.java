package com.zhelian.service.psi.product;

import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.psi.ProductBrandMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.entity.ProductBrandEntity;
import com.zhelian.model.ibeauty.form.input.ProductBrandAddForm;
import com.zhelian.model.ibeauty.form.input.ProductBrandMoveForm;
import com.zhelian.model.ibeauty.form.input.ProductBrandQueryForm;
import com.zhelian.model.ibeauty.form.input.ProductBrandUpdateForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

@Service
public class ProductBrandService {

    @Autowired
    ProductBrandMapper productBrandMapper;

    public BaseOutput productBrandList(ProductBrandQueryForm form) {
        return BaseOutputForm.success(productBrandMapper.productBrandList(form.Name, form.Active));
    }


    public BaseOutput createProductBrand(ProductBrandAddForm form) {
        Integer Sequence = productBrandMapper.getMaxSequence();
        ProductBrandEntity productBrandEntity = new ProductBrandEntity();
        productBrandEntity.Name = form.Name;
        productBrandEntity.Sequence = Sequence != null ? Sequence + 1 : 1;
        productBrandMapper.createProductBrand(productBrandEntity);
        return BaseOutput.success();

    }

    public BaseOutput updateProductBrand(ProductBrandUpdateForm form) {
        if (form.Active == false) {
            Integer count = productBrandMapper.productByBrandCount(form.ID);
            if (count > 0)
                return BaseOutput.failed("该品牌下有产品，不可以改为无效");
        }
        ProductBrandEntity productBrandEntity = new ProductBrandEntity();
        productBrandEntity.Active = form.Active;
        productBrandEntity.Name = form.Name;
        productBrandEntity.ID = form.ID;
        productBrandMapper.updateProductBrand(productBrandEntity);
        return BaseOutput.success();
    }

    public BaseOutput move(ProductBrandMoveForm form) {

        LinkedList<Integer> linkedList = productBrandMapper.getASCList(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<ProductBrandEntity> list = new ArrayList<>();
        for (Integer ID : linkedList) {
            ProductBrandEntity productBrandEntity = new ProductBrandEntity();
            productBrandEntity.ID = ID;
            productBrandEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(productBrandEntity);
        }
        productBrandMapper.updateSequence(list);
        return BaseOutput.success();
    }

}