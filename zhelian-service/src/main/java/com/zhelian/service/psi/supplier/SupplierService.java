package com.zhelian.service.psi.supplier;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.psi.SupplierMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.psi.entity.SupplierEntity;
import com.zhelian.model.psi.form.input.SupplierAddForm;
import com.zhelian.model.psi.form.input.SupplierQueryForm;
import com.zhelian.model.psi.form.input.SupplierUpdateForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class SupplierService {

    @Autowired
    SupplierMapper supplierMapper;

    public BaseOutput supplierList(SupplierQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SupplierEntity> list = supplierMapper.supplierList(form.Name, form.Active);
        return BasePageInfo.success(list);
    }

    @Transactional
    public BaseOutput insertSupplier(SupplierAddForm form) {

        SupplierEntity supplierEntity = new SupplierEntity();
        supplierEntity.Name = form.Name;
        supplierEntity.ContactPersonName = form.ContactPersonName;
        supplierEntity.ContactPersonMobile = form.ContactPersonMobile;
        supplierEntity.SupplierTelePhone = form.SupplierTelePhone;
        supplierEntity.ProvinceCode = form.ProvinceCode;
        supplierEntity.CityCode = form.CityCode;
        supplierEntity.AreaCode = form.AreaCode;
        supplierEntity.AddressDetail = form.AddressDetail;
        supplierEntity.WeiXin = form.WeiXin;
        supplierEntity.Email = form.Email;
        supplierEntity.Fax = form.Fax;
        supplierEntity.Remark = form.Remark;
        supplierEntity.Active = true;
        supplierMapper.insertSupplier(supplierEntity);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateSupplier(SupplierUpdateForm form) {

        SupplierEntity supplierEntity = supplierMapper.findSupplierByID(form.ID);

        if (supplierEntity == null)
            return BaseOutput.failed("供应商不存在");

        supplierEntity.Name = form.Name;
        supplierEntity.ContactPersonName = form.ContactPersonName;
        supplierEntity.ContactPersonMobile = form.ContactPersonMobile;
        supplierEntity.SupplierTelePhone = form.SupplierTelePhone;
        supplierEntity.ProvinceCode = form.ProvinceCode;
        supplierEntity.CityCode = form.CityCode;
        supplierEntity.AreaCode = form.AreaCode;
        supplierEntity.AddressDetail = form.AddressDetail;
        supplierEntity.WeiXin = form.WeiXin;
        supplierEntity.Email = form.Email;
        supplierEntity.Fax = form.Fax;
        supplierEntity.Remark = form.Remark;
        supplierEntity.Active = form.Active;
        supplierMapper.updateSupplier(supplierEntity);

        return BaseOutput.success();
    }

}
