package com.zhelian.service.psi.entityRefundProductApply;

import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.autoNumber.AutoNumberUtils;
import com.zhelian.core.utils.enumeration.BillCode;
import com.zhelian.mapper.khs.EmployeeMapper;
import com.zhelian.mapper.psi.EntityRefundProductApplyMapper;
import com.zhelian.mapper.psi.PaymentMapper;
import com.zhelian.mapper.psi.StockMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.psi.entity.*;
import com.zhelian.model.psi.form.input.*;
import com.zhelian.model.psi.form.output.EntityRefundApplyOutputForm;
import com.zhelian.model.psi.form.output.InventoryRefundApplyBillStatusNumberOutputForm;
import com.zhelian.model.psi.form.output.InventoryRefundApplyStatementOutputForm;
import com.zhelian.model.psi.form.output.entityRefundApplyInfoOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class EntityRefundProductApplyService {

    @Autowired
    EntityRefundProductApplyMapper entityRefundProductApplyMapper;

    @Autowired
    StockMapper stockMapper;

    @Autowired
    PaymentMapper paymentMapper;

    @Autowired
    EmployeeMapper employeeMapper;

    public BaseOutput entityRefundApplyList(InventoryRefundApplyQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<EntityRefundApplyOutputForm> list = entityRefundProductApplyMapper.inventoryRefundApplyList(form.ID, form.EntityID, form.BillStatus, form.StartDate, form.EndDate, SystemUserUtil.getSystemUserID(), form.ProductName);
        for (EntityRefundApplyOutputForm entityRefundApplyOutputForm : list) {
            if (entityRefundApplyOutputForm.BillStatus.equals("20")) {
                Integer count = employeeMapper.getEmployeePermissionByEntityID(entityRefundApplyOutputForm.RefundOutboundEntityID, SystemUserUtil.getSystemUserID());
                entityRefundApplyOutputForm.IsHavePermission = count == 0 ? false : true;
            } else {
                Integer count = employeeMapper.getEmployeePermissionByEntityID(entityRefundApplyOutputForm.RefundInboundEntityID, SystemUserUtil.getSystemUserID());
                entityRefundApplyOutputForm.IsHavePermission = count == 0 ? false : true;
            }
        }
        return BasePageInfo.success(list);
    }

    public BaseOutput entityRefundApplyInfo(String ID) {
        entityRefundApplyInfoOutputForm form = entityRefundProductApplyMapper.entityRefundApplyInfo(ID);
        return BaseOutputForm.success(form);
    }

    @Transactional
    public BaseOutput create(InventoryRefundApplyAddForm form) {
        List<InventoryRefundApplyDetailEntity> inventoryRefundApplyDetailEntities = new ArrayList<>();
        InventoryRefundApplyEntity inventoryRefundApplyEntity = entityRefundProductApplyMapper.inventoryRefundApplyByID(form.ID);
        if (form.ID != null) {
            inventoryRefundApplyEntity.BillStatus = form.BillStatus;
            inventoryRefundApplyEntity.RefundOutboundEntityID = form.OutboundEntityID;
            inventoryRefundApplyEntity.Remark = form.Remark;
            inventoryRefundApplyEntity.ApprovedTotalAmount = form.Amount.setScale(2, BigDecimal.ROUND_HALF_UP);
            inventoryRefundApplyEntity.CreatedBy = SystemUserUtil.getSystemUserID();

            inventoryRefundApplyEntity.CreatedOn = new Date();
            entityRefundProductApplyMapper.updateInventoryRefundApply(inventoryRefundApplyEntity);
            entityRefundProductApplyMapper.deleteInventoryRefundApplyDetail(form.ID);
            for (InventoryRefundApplyDetailAddForm inventoryRefundApplyDetailAddForm : form.InventoryRefundApplyDetail) {
                InventoryRefundApplyDetailEntity inventoryRefundApplyDetailEntity = new InventoryRefundApplyDetailEntity();
                inventoryRefundApplyDetailEntity.InventoryRefundID = inventoryRefundApplyEntity.ID;
                inventoryRefundApplyDetailEntity.ProductID = inventoryRefundApplyDetailAddForm.ProductID;
                inventoryRefundApplyDetailEntity.UnitID = inventoryRefundApplyDetailAddForm.UnitID;
                inventoryRefundApplyDetailEntity.MinimumUnitID = inventoryRefundApplyDetailAddForm.MinimumUnitID;
                inventoryRefundApplyDetailEntity.RefundQuantity = inventoryRefundApplyDetailAddForm.ApplyQuantity;
                inventoryRefundApplyDetailEntity.RefundMinimumUnitQuantity = inventoryRefundApplyDetailAddForm.ApplyMinimumUnitQuantity;
                inventoryRefundApplyDetailEntity.ApprovedPrice = inventoryRefundApplyDetailAddForm.ApprovedPrice.setScale(2, BigDecimal.ROUND_HALF_UP);
                inventoryRefundApplyDetailEntity.ApprovedTotalAmount = inventoryRefundApplyDetailAddForm.ApprovedTotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                inventoryRefundApplyDetailEntities.add(inventoryRefundApplyDetailEntity);
            }
        } else {
            inventoryRefundApplyEntity = new InventoryRefundApplyEntity();
            inventoryRefundApplyEntity.ID = AutoNumberUtils.getPSIEntityRefundID();
            inventoryRefundApplyEntity.BillStatus = form.BillStatus;
            inventoryRefundApplyEntity.RefundOutboundEntityID = form.OutboundEntityID;
            inventoryRefundApplyEntity.Remark = form.Remark;
            inventoryRefundApplyEntity.ApprovedTotalAmount = form.Amount.setScale(2, BigDecimal.ROUND_HALF_UP);
            inventoryRefundApplyEntity.CreatedBy = SystemUserUtil.getSystemUserID();
            inventoryRefundApplyEntity.CreatedOn = new Date();
            entityRefundProductApplyMapper.insertInventoryRefundApplyEntity(inventoryRefundApplyEntity);
            for (InventoryRefundApplyDetailAddForm inventoryRefundApplyDetailAddForm : form.InventoryRefundApplyDetail) {
                InventoryRefundApplyDetailEntity inventoryRefundApplyDetailEntity = new InventoryRefundApplyDetailEntity();
                inventoryRefundApplyDetailEntity.InventoryRefundID = inventoryRefundApplyEntity.ID;
                inventoryRefundApplyDetailEntity.ProductID = inventoryRefundApplyDetailAddForm.ProductID;
                inventoryRefundApplyDetailEntity.UnitID = inventoryRefundApplyDetailAddForm.UnitID;
                inventoryRefundApplyDetailEntity.MinimumUnitID = inventoryRefundApplyDetailAddForm.MinimumUnitID;
                inventoryRefundApplyDetailEntity.RefundQuantity = inventoryRefundApplyDetailAddForm.ApplyQuantity;
                inventoryRefundApplyDetailEntity.RefundMinimumUnitQuantity = inventoryRefundApplyDetailAddForm.ApplyMinimumUnitQuantity;
                inventoryRefundApplyDetailEntity.ApprovedPrice = inventoryRefundApplyDetailAddForm.ApprovedPrice.setScale(2, BigDecimal.ROUND_HALF_UP);
                inventoryRefundApplyDetailEntity.ApprovedTotalAmount = inventoryRefundApplyDetailAddForm.ApprovedTotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                inventoryRefundApplyDetailEntities.add(inventoryRefundApplyDetailEntity);
            }
        }


        if (inventoryRefundApplyDetailEntities.size() > 0) {
            entityRefundProductApplyMapper.insertInventoryRefundApplyDetailEntity(inventoryRefundApplyDetailEntities);
        }
        return BaseOutputForm.success(inventoryRefundApplyEntity.ID);
    }

    @Transactional
    public BaseOutput approved(InventoryRefundApplyApprovedForm form) {
        InventoryRefundApplyEntity inventoryRefundApplyEntity = entityRefundProductApplyMapper.inventoryRefundApplyByID(form.ID);
        if (!inventoryRefundApplyEntity.BillStatus.equals("10")) {
            return BaseOutput.failed("该退货申请单状态不需要审核");
        }
        inventoryRefundApplyEntity.ApprovedBy = SystemUserUtil.getSystemUserID();
        inventoryRefundApplyEntity.ApprovedOn = new Date();
        inventoryRefundApplyEntity.BillStatus = form.BillStatus;
        inventoryRefundApplyEntity.SettlementWay = form.SettlementWay;
        if (form.BillStatus.equals("50")) {
            inventoryRefundApplyEntity.RejectReason = form.RejectReason;
        } else {
            inventoryRefundApplyEntity.ApprovedTotalAmount = form.ApprovedTotalAmount;
            inventoryRefundApplyEntity.RefundInboundEntityID = form.InboundEntityID;
            for (InventoryRefundApplyApprovedDetailForm detailForm : form.InventoryRefundApplyDetail) {
                InventoryRefundApplyDetailEntity inventoryRefundApplyDetailEntity = entityRefundProductApplyMapper.inventoryRefundApplyDetailByID(detailForm.ID);
                inventoryRefundApplyDetailEntity.ApprovedPrice = detailForm.ApprovedPrice.setScale(2, BigDecimal.ROUND_HALF_UP);
                inventoryRefundApplyDetailEntity.ApproveQuantity = detailForm.ApproveQuantity;
                inventoryRefundApplyDetailEntity.ApproveMinimumUnitQuantity = detailForm.ApproveMinimumUnitQuantity;
                inventoryRefundApplyDetailEntity.ApprovedTotalAmount = detailForm.ApprovedTotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
                entityRefundProductApplyMapper.updateInventoryRefundApplyDetail(inventoryRefundApplyDetailEntity);
            }
        }
        entityRefundProductApplyMapper.updateInventoryRefundApply(inventoryRefundApplyEntity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput pay(InventoryRefundApplyPayForm form) {
        InventoryRefundApplyEntity inventoryRefundApplyEntity = entityRefundProductApplyMapper.inventoryRefundApplyByID(form.ID);
        if (!inventoryRefundApplyEntity.BillStatus.equals("40")) {
            return BaseOutput.failed("该退货申请单状态不需要付款");
        }
        PaymentBillEntity paymentBillEntity = new PaymentBillEntity();
        paymentBillEntity.ID = AutoNumberUtils.getPSIPaymentBillID();
        paymentBillEntity.BillType = BillCode.PaymentBillType_40.getID();
        paymentBillEntity.EntityID = inventoryRefundApplyEntity.RefundOutboundEntityID;
        paymentBillEntity.PaymentWay = form.PaymentWay;
        BigDecimal balance = form.Balance == null || form.Balance.equals("") ? BigDecimal.ZERO : form.Balance;
        BigDecimal largessBalance = form.LargessBalance == null || form.LargessBalance.equals("") ? BigDecimal.ZERO : form.LargessBalance;
        if (form.PaymentWay.equals("10")) {
            paymentBillEntity.Balance = balance;
            paymentBillEntity.LargessBalance = largessBalance;
            paymentBillEntity.ReceiptNumber = form.ReceiptNumber;
            paymentBillEntity.PaymentAccountName = form.PaymentAccountName;

        } else {

            if (balance.add(largessBalance).compareTo(inventoryRefundApplyEntity.RefundInboundTotalAmount) != 0) {
                return BaseOutput.failed("金额不正确，请重新输入");
            }


            PrepayEntity prepayEntity = paymentMapper.getPaymentBalance(inventoryRefundApplyEntity.RefundOutboundEntityID);
            if (prepayEntity == null) {
                PrepayEntity newPrepayEntity = new PrepayEntity();
                newPrepayEntity.EntityID = inventoryRefundApplyEntity.RefundOutboundEntityID;
                newPrepayEntity.Balance = balance;
                newPrepayEntity.LargessBalance = largessBalance;
                paymentMapper.createPrepay(newPrepayEntity);
            } else {
                prepayEntity.Balance = prepayEntity.Balance.add(balance);
                prepayEntity.LargessBalance = prepayEntity.LargessBalance.add(largessBalance);
                paymentMapper.updadtePrepay(prepayEntity);
            }

            paymentBillEntity.Balance = balance;
            paymentBillEntity.LargessBalance = largessBalance;
            paymentBillEntity.SourceBillType = BillCode.SourceRefundBillType_20.getID();
            paymentBillEntity.SourceBillNo = form.ID;


        }

        paymentBillEntity.Remark = form.Remark;
        paymentBillEntity.CreatedOn = new Timestamp(System.currentTimeMillis());
        paymentBillEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        paymentMapper.createPaymentBill(paymentBillEntity);


        inventoryRefundApplyEntity.BillStatus = form.BillStatus;
        entityRefundProductApplyMapper.updateInventoryRefundApply(inventoryRefundApplyEntity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput outbound(InventoryRefundApplyOutboundForm form) {
        InventoryRefundApplyEntity inventoryRefundApplyEntity = entityRefundProductApplyMapper.inventoryRefundApplyByID(form.ID);
        if (!inventoryRefundApplyEntity.BillStatus.equals("20")) {
            return BaseOutput.failed("该退货申请单状态不需要配送");
        }
        inventoryRefundApplyEntity.RefundOutboundBillID = AutoNumberUtils.getPSIEntityRefundProductDeliveryID();
        inventoryRefundApplyEntity.RefundOutboundTotalAmount = form.OutboundTotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
        inventoryRefundApplyEntity.RefundOutboundBy = SystemUserUtil.getSystemUserID();
        inventoryRefundApplyEntity.RefundOutboundOn = new Date();
        inventoryRefundApplyEntity.BillStatus = "30";
        entityRefundProductApplyMapper.updateInventoryRefundApply(inventoryRefundApplyEntity);


        List<InventoryRefundOutboundDetailEntity> inventoryRefundOutboundDetailEntityArrayList = new ArrayList<>();
        for (InventoryRefundApplyOutboundDetailForm detailForm : form.InventoryRefundApplyDetail) {
            InventoryRefundApplyDetailEntity inventoryRefundApplyDetailEntity = entityRefundProductApplyMapper.inventoryRefundApplyDetailByID(detailForm.ID);
            inventoryRefundApplyDetailEntity.RefundOutboundPrice = detailForm.OutboundPrice.setScale(2, BigDecimal.ROUND_HALF_UP);
            inventoryRefundApplyDetailEntity.RefundOutboundQuantity = detailForm.OutboundQuantity;
            inventoryRefundApplyDetailEntity.RefundOutboundMinimumUnitQuantity = detailForm.OutboundMinimumUnitQuantity;
            inventoryRefundApplyDetailEntity.RefundOutboundTotalAmount = detailForm.OutboundTotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
            entityRefundProductApplyMapper.updateInventoryRefundApplyDetail(inventoryRefundApplyDetailEntity);

            StockEntity stockEntity = stockMapper.stockInfo(new StockEntity(inventoryRefundApplyEntity.RefundOutboundEntityID, inventoryRefundApplyDetailEntity.ProductID));
            if (stockEntity != null || !inventoryRefundApplyDetailEntity.RefundOutboundQuantity.equals(0)) {
                InventoryRefundOutboundDetailEntity inventoryRefundOutboundDetailEntity = new InventoryRefundOutboundDetailEntity();
                inventoryRefundOutboundDetailEntity.RefundOutboundBillID = inventoryRefundApplyEntity.RefundOutboundBillID;
                inventoryRefundOutboundDetailEntity.ProductID = inventoryRefundApplyDetailEntity.ProductID;
                inventoryRefundOutboundDetailEntity.UnitID = inventoryRefundApplyDetailEntity.UnitID;
                inventoryRefundOutboundDetailEntity.MinimumUnitID = inventoryRefundApplyDetailEntity.MinimumUnitID;
                inventoryRefundOutboundDetailEntity.Quantity = inventoryRefundApplyDetailEntity.RefundOutboundQuantity;
                inventoryRefundOutboundDetailEntity.MinimumUnitQuantity = inventoryRefundApplyDetailEntity.RefundOutboundMinimumUnitQuantity;
                inventoryRefundOutboundDetailEntity.StockQuantity = stockEntity.Quantity - inventoryRefundApplyDetailEntity.RefundOutboundMinimumUnitQuantity;
                inventoryRefundOutboundDetailEntityArrayList.add(inventoryRefundOutboundDetailEntity);
                stockEntity.Quantity = inventoryRefundOutboundDetailEntity.StockQuantity;
                stockMapper.updateStockEntity(stockEntity);
            }
        }
        if (inventoryRefundOutboundDetailEntityArrayList != null && inventoryRefundOutboundDetailEntityArrayList.size() > 0) {
            InventoryRefundOutboundEntity inventoryRefundOutboundEntity = new InventoryRefundOutboundEntity();
            inventoryRefundOutboundEntity.ID = inventoryRefundApplyEntity.RefundOutboundBillID;
            inventoryRefundOutboundEntity.EntityID = inventoryRefundApplyEntity.RefundOutboundEntityID;
            inventoryRefundOutboundEntity.OutDate = new Date();
            inventoryRefundOutboundEntity.CreatedBy = SystemUserUtil.getSystemUserID();
            inventoryRefundOutboundEntity.CreatedOn = new Date();
            inventoryRefundOutboundEntity.Remark = form.Remark;
            entityRefundProductApplyMapper.insertInventoryRefundOutboundEntity(inventoryRefundOutboundEntity);
            entityRefundProductApplyMapper.insertInventoryRefundOutboundDetailEntity(inventoryRefundOutboundDetailEntityArrayList);
        }
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput inbound(InventoryRefundApplyInboundForm form) {
        InventoryRefundApplyEntity inventoryRefundApplyEntity = entityRefundProductApplyMapper.inventoryRefundApplyByID(form.ID);
        if (!inventoryRefundApplyEntity.BillStatus.equals("30")) {
            return BaseOutput.failed("该退货申请单状态不需要入库");
        }
        inventoryRefundApplyEntity.RefundInboundBillID = AutoNumberUtils.getPSIEntityRefundProductStorageID();
        inventoryRefundApplyEntity.RefundInboundTotalAmount = form.InboundTotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
        inventoryRefundApplyEntity.RefundInboundBy = SystemUserUtil.getSystemUserID();
        inventoryRefundApplyEntity.RefundInboundOn = new Date();
        if (inventoryRefundApplyEntity.SettlementWay.equals("10")) {
            inventoryRefundApplyEntity.BillStatus = "60";
        } else {
            inventoryRefundApplyEntity.BillStatus = "40";
        }
        entityRefundProductApplyMapper.updateInventoryRefundApply(inventoryRefundApplyEntity);

        List<InventoryRefundInboundDetailEntity> inventoryRefundInboundDetailEntityArrayList = new ArrayList<>();
        for (InventoryRefundApplyInboundDetailForm detailForm : form.InventoryRefundApplyDetail) {
            InventoryRefundApplyDetailEntity inventoryRefundApplyDetailEntity = entityRefundProductApplyMapper.inventoryRefundApplyDetailByID(detailForm.ID);
            inventoryRefundApplyDetailEntity.RefundInboundPrice = detailForm.InboundPrice.setScale(2, BigDecimal.ROUND_HALF_UP);
            inventoryRefundApplyDetailEntity.RefundInboundQuantity = detailForm.InboundQuantity;
            inventoryRefundApplyDetailEntity.RefundInboundMinimumUnitQuantity = detailForm.InboundMinimumUnitQuantity;
            inventoryRefundApplyDetailEntity.RefundInboundTotalAmount = detailForm.InboundTotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
            entityRefundProductApplyMapper.updateInventoryRefundApplyDetail(inventoryRefundApplyDetailEntity);

            StockEntity stockEntity = stockMapper.stockInfo(new StockEntity(inventoryRefundApplyEntity.RefundInboundEntityID, inventoryRefundApplyDetailEntity.ProductID));
            int stockQuantity = 0;
            if (stockEntity != null) {
                stockQuantity = stockEntity.Quantity;
            }
            InventoryRefundInboundDetailEntity inventoryRefundInboundDetailEntity = new InventoryRefundInboundDetailEntity();
            inventoryRefundInboundDetailEntity.RefundInboundBillID = inventoryRefundApplyEntity.RefundInboundBillID;
            inventoryRefundInboundDetailEntity.ProductID = inventoryRefundApplyDetailEntity.ProductID;
            inventoryRefundInboundDetailEntity.UnitID = inventoryRefundApplyDetailEntity.UnitID;
            inventoryRefundInboundDetailEntity.MinimumUnitID = inventoryRefundApplyDetailEntity.MinimumUnitID;
            inventoryRefundInboundDetailEntity.Quantity = inventoryRefundApplyDetailEntity.RefundInboundQuantity;
            inventoryRefundInboundDetailEntity.MinimumUnitQuantity = inventoryRefundApplyDetailEntity.RefundInboundMinimumUnitQuantity;
            inventoryRefundInboundDetailEntity.StockQuantity = inventoryRefundInboundDetailEntity.MinimumUnitQuantity + stockQuantity;
            inventoryRefundInboundDetailEntityArrayList.add(inventoryRefundInboundDetailEntity);
            if (stockEntity != null) {
                stockEntity.Quantity = inventoryRefundInboundDetailEntity.StockQuantity;
                stockMapper.updateStockEntity(stockEntity);
            } else {
                stockMapper.insertStockEntity(new StockEntity(inventoryRefundApplyEntity.RefundInboundEntityID, inventoryRefundInboundDetailEntity.ProductID, inventoryRefundInboundDetailEntity.StockQuantity));
            }
        }
        if (inventoryRefundInboundDetailEntityArrayList != null && inventoryRefundInboundDetailEntityArrayList.size() > 0) {
            InventoryRefundInboundEntity inventoryRefundInboundEntity = new InventoryRefundInboundEntity();
            inventoryRefundInboundEntity.ID = inventoryRefundApplyEntity.RefundInboundBillID;
            inventoryRefundInboundEntity.EntityID = inventoryRefundApplyEntity.RefundInboundEntityID;
            inventoryRefundInboundEntity.InDate = new Date();
            inventoryRefundInboundEntity.CreatedBy = SystemUserUtil.getSystemUserID();
            inventoryRefundInboundEntity.CreatedOn = new Date();
            inventoryRefundInboundEntity.Remark = form.Remark;
            entityRefundProductApplyMapper.insertInventoryRefundInboundEntity(inventoryRefundInboundEntity);
            entityRefundProductApplyMapper.insertInventoryRefundInboundDetailEntity(inventoryRefundInboundDetailEntityArrayList);
        }
        return BaseOutput.success();
    }

    public BaseOutput cancel(InventoryRefundApplyCancelForm form) {
        InventoryRefundApplyEntity inventoryRefundApplyEntity = entityRefundProductApplyMapper.inventoryRefundApplyByID(form.ID);
        if (!inventoryRefundApplyEntity.BillStatus.equals("05") && !inventoryRefundApplyEntity.BillStatus.equals("10")) {
            return BaseOutput.failed("该退货申请单状态不能取消");
        }
        inventoryRefundApplyEntity.BillStatus = "70";
        entityRefundProductApplyMapper.updateInventoryRefundApply(inventoryRefundApplyEntity);
        return BaseOutput.success();
    }

    public BaseOutput cancelRejectApply(InventoryRefundApplyCancelForm form) {
        InventoryRefundApplyEntity inventoryRefundApplyEntity = entityRefundProductApplyMapper.inventoryRefundApplyByID(form.ID);
        if (!inventoryRefundApplyEntity.BillStatus.equals("50")) {
            return BaseOutput.failed("该要货申请单状态不能取消");
        }
        inventoryRefundApplyEntity.BillStatus = "70";
        entityRefundProductApplyMapper.updateInventoryRefundApply(inventoryRefundApplyEntity);
        return BaseOutput.success();
    }

    public BaseOutput billStatusNumber(InventoryRefundApplyBillStatusNumberForm form) {

        InventoryRefundApplyBillStatusNumberOutputForm outputForm = new InventoryRefundApplyBillStatusNumberOutputForm();
        outputForm.BillStatus05 = entityRefundProductApplyMapper.getStatusNumber(form.ID, form.EntityID, "05", form.StartDate, form.EndDate, SystemUserUtil.getSystemUserID(), form.ProductName);
        ;
        outputForm.BillStatus10 = entityRefundProductApplyMapper.getStatusNumber(form.ID, form.EntityID, "10", form.StartDate, form.EndDate, SystemUserUtil.getSystemUserID(), form.ProductName);
        ;
        outputForm.BillStatus20 = entityRefundProductApplyMapper.getStatusNumber(form.ID, form.EntityID, "20", form.StartDate, form.EndDate, SystemUserUtil.getSystemUserID(), form.ProductName);
        ;
        outputForm.BillStatus30 = entityRefundProductApplyMapper.getStatusNumber(form.ID, form.EntityID, "30", form.StartDate, form.EndDate, SystemUserUtil.getSystemUserID(), form.ProductName);
        ;
        outputForm.BillStatus40 = entityRefundProductApplyMapper.getStatusNumber(form.ID, form.EntityID, "40", form.StartDate, form.EndDate, SystemUserUtil.getSystemUserID(), form.ProductName);
        ;
        outputForm.BillStatus50 = entityRefundProductApplyMapper.getStatusNumber(form.ID, form.EntityID, "50", form.StartDate, form.EndDate, SystemUserUtil.getSystemUserID(), form.ProductName);
        ;
        outputForm.BillStatus60 = entityRefundProductApplyMapper.getStatusNumber(form.ID, form.EntityID, "60", form.StartDate, form.EndDate, SystemUserUtil.getSystemUserID(), form.ProductName);
        ;
        outputForm.BillStatus70 = entityRefundProductApplyMapper.getStatusNumber(form.ID, form.EntityID, "70", form.StartDate, form.EndDate, SystemUserUtil.getSystemUserID(), form.ProductName);
        ;
        return BaseOutputForm.success(outputForm);
    }

    public BaseOutput detail(InventoryRefundApplyDetailQueryForm form) {
        InventoryRefundApplyStatementOutputForm statementForm = new InventoryRefundApplyStatementOutputForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        statementForm.detail = BasePageInfo.success(entityRefundProductApplyMapper.getInventoryRefundApplyDetail(SystemUserUtil.getSystemUserID(), form.EntityID, form.BillStatus, form.StartTime, form.EndTime, form.CategoryID, form.ProductName, form.CreatedBy, form.BrandID));
        statementForm.inventoryRefundApplySumStatementOutputForm = entityRefundProductApplyMapper.getInventoryRefundApplyDetailSum(SystemUserUtil.getSystemUserID(), form.EntityID, form.BillStatus, form.StartTime, form.EndTime, form.CategoryID, form.ProductName, form.CreatedBy, form.BrandID);
        return BaseOutputForm.success(statementForm);
    }

//    public BaseOutput excelInventoryApplyStatement(InventoryApplyDetailQueryForm form, HttpServletResponse response) {
//
//        List<Object> list = entityRefundProductApplyMapper.getInventoryApplyDetailExcel(SystemUserUtil.getSystemUserID(), form.EntityID, form.BillStatus, form.StartTime, form.EndTime, form.CategoryID, form.ProductName, form.CreatedBy, form.BrandID);
//        InventoryApplySumStatementOutputForm inventoryApplySumStatementOutputForm = entityRefundProductApplyMapper.getInventoryApplyDetailSum(SystemUserUtil.getSystemUserID(), form.EntityID, form.BillStatus, form.StartTime, form.EndTime, form.CategoryID, form.ProductName, form.CreatedBy, form.BrandID);
//        if(inventoryApplySumStatementOutputForm!=null){
//            InventoryApplyDetailExcelOutputForm statementForm=new InventoryApplyDetailExcelOutputForm();
//            statementForm.EntityName="合计";
//            statementForm.ID="";
//            statementForm.EntityName="";
//            statementForm.BillStatus="";
//            statementForm.PCategoryName="";
//            statementForm.BrandName="";
//            statementForm.ProductName="";
//            statementForm.OutboundPrice="";
//            statementForm.OutboundEntityName="";
//            statementForm.ApplyQuantity=inventoryApplySumStatementOutputForm.ApplyQuantity;
//            statementForm.OutboundQuantity=inventoryApplySumStatementOutputForm.OutboundQuantity;
//            statementForm.InboundQuantity=inventoryApplySumStatementOutputForm.InboundQuantity;
//            statementForm.UnitName="";
//            statementForm.ApprovedTotalAmount=inventoryApplySumStatementOutputForm.ApprovedTotalAmount;
//            statementForm.Specification="";
//            statementForm.MinimumUnitAmount="";
//            statementForm.CreatedOn="";
//            statementForm.CreatedByName="";
//            statementForm.Remark="";
//            list.add(statementForm);
//        }
//
//        String[] headers = new String[]{"单据号", "申请仓库/门店", "单据状态","产品分类","产品品牌","产品名称","配送单价","发货仓库","要货数量","实发数量","实收数量","单位","合计金额", "产品规格", "最小包装单位","制单时间","申请人","备注信息"};
//        try {
//            ExcelUtil.exportNewExcel(response,list,"门店要货明细","门店要货明细", headers);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return BaseOutput.success();
//    }

    public BaseOutput getEntityPrepay(InventoryApplyDetailQueryForm form) {
        return BaseOutputForm.success(entityRefundProductApplyMapper.getEntityPrepay(form.EntityID));
    }


    @Transactional
    public BaseOutput update(InventoryRefundApplyUpdateForm form) {

        InventoryRefundApplyEntity inventoryRefundApplyEntity = entityRefundProductApplyMapper.getInventoryRefundApply(form.ID);
        inventoryRefundApplyEntity.BillStatus = BillCode.InventoryRefundApplyBillStatus_10.getID();
        inventoryRefundApplyEntity.Remark = form.Remark;
        inventoryRefundApplyEntity.ApprovedTotalAmount = form.Amount.setScale(2, BigDecimal.ROUND_HALF_UP);
        inventoryRefundApplyEntity.CreatedBy = SystemUserUtil.getSystemUserID();
        inventoryRefundApplyEntity.CreatedOn = new Date();
        entityRefundProductApplyMapper.updateInventoryRefundApply(inventoryRefundApplyEntity);
        entityRefundProductApplyMapper.deleteInventoryRefundApplyDetail(form.ID);

        List<InventoryRefundApplyDetailEntity> inventoryRefundApplyDetailEntities = new ArrayList<>();
        for (InventoryRefundApplyDetailAddForm inventoryRefundApplyDetailAddForm : form.InventoryRefundApplyDetail) {
            InventoryRefundApplyDetailEntity inventoryRefundApplyDetailEntity = new InventoryRefundApplyDetailEntity();
            inventoryRefundApplyDetailEntity.InventoryRefundID = inventoryRefundApplyEntity.ID;
            inventoryRefundApplyDetailEntity.ProductID = inventoryRefundApplyDetailAddForm.ProductID;
            inventoryRefundApplyDetailEntity.UnitID = inventoryRefundApplyDetailAddForm.UnitID;
            inventoryRefundApplyDetailEntity.MinimumUnitID = inventoryRefundApplyDetailAddForm.MinimumUnitID;
            inventoryRefundApplyDetailEntity.RefundQuantity = inventoryRefundApplyDetailAddForm.ApplyQuantity;
            inventoryRefundApplyDetailEntity.RefundMinimumUnitQuantity = inventoryRefundApplyDetailAddForm.ApplyMinimumUnitQuantity;
            inventoryRefundApplyDetailEntity.ApprovedPrice = inventoryRefundApplyDetailAddForm.ApprovedPrice.setScale(2, BigDecimal.ROUND_HALF_UP);
            inventoryRefundApplyDetailEntity.ApprovedTotalAmount = inventoryRefundApplyDetailAddForm.ApprovedTotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
            inventoryRefundApplyDetailEntities.add(inventoryRefundApplyDetailEntity);
        }
        if (inventoryRefundApplyDetailEntities.size() > 0) {
            entityRefundProductApplyMapper.insertInventoryRefundApplyDetailEntity(inventoryRefundApplyDetailEntities);
        }
        return BaseOutput.success();
    }


}
