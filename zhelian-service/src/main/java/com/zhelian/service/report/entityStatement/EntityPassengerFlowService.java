package com.zhelian.service.report.entityStatement;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.report.EntityPassengerFlowMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.report.form.input.EntityTradingQueryForm;
import com.zhelian.model.report.form.output.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;


@Service
public class EntityPassengerFlowService {

    @Autowired
    EntityPassengerFlowMapper entityPassengerFlowMapper;

    public BaseOutput entityPassengerFlowStatement(EntityTradingQueryForm form) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));


        EntityPassengerFlowOutputForm passengerFlowOutputForm = new EntityPassengerFlowOutputForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        passengerFlowOutputForm.Detail = BasePageInfo.success(entityPassengerFlowMapper.getEntityPassengerFlowStatement(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate));
        passengerFlowOutputForm.SumOutputForm = entityPassengerFlowMapper.getEntityPassengerFlowStatementSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate);
        return BaseOutputForm.success(passengerFlowOutputForm);
    }

    public BaseOutput easyExcelEntityPassengerFlowStatement(EntityTradingQueryForm form, HttpServletResponse response) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));


        List<EntityPassengerFlowDetailEasyExcelOutputForm> list = entityPassengerFlowMapper.getEntityPassengerFlowStatementEasyExcel(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate);
        EntityPassengerFlowSumOutputForm entitySaleDetailAndSumStatementForm = entityPassengerFlowMapper.getEntityPassengerFlowStatementSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate);
        if (entitySaleDetailAndSumStatementForm != null) {
            EntityPassengerFlowDetailEasyExcelOutputForm entitySaleDetailStatementForm = new EntityPassengerFlowDetailEasyExcelOutputForm();
            entitySaleDetailStatementForm.entityName = "合计";
            entitySaleDetailStatementForm.customerCount = entitySaleDetailAndSumStatementForm.CustomerCount;
            entitySaleDetailStatementForm.customerSaleCount = entitySaleDetailAndSumStatementForm.CustomerSaleCount;
            entitySaleDetailStatementForm.customerTreatCount = entitySaleDetailAndSumStatementForm.CustomerTreatCount;
            entitySaleDetailStatementForm.passengerFlowCount = entitySaleDetailAndSumStatementForm.PassengerFlowCount;
            entitySaleDetailStatementForm.passengerFlowSaleCount = entitySaleDetailAndSumStatementForm.PassengerFlowSaleCount;
            entitySaleDetailStatementForm.passengerFlowTreatCount = entitySaleDetailAndSumStatementForm.PassengerFlowTreatCount;
            entitySaleDetailStatementForm.newCustomerCount = entitySaleDetailAndSumStatementForm.NewCustomerCount;
            entitySaleDetailStatementForm.newCustomerMemberCount = entitySaleDetailAndSumStatementForm.NewCustomerMemberCount;
            entitySaleDetailStatementForm.dealNewCount = entitySaleDetailAndSumStatementForm.DealNewCount;
            entitySaleDetailStatementForm.dealOldCount = entitySaleDetailAndSumStatementForm.DealOldCount;
            entitySaleDetailStatementForm.manCount = entitySaleDetailAndSumStatementForm.ManCount;
            entitySaleDetailStatementForm.womanCount = entitySaleDetailAndSumStatementForm.WomanCount;
            entitySaleDetailStatementForm.unknownCount = entitySaleDetailAndSumStatementForm.UnknownCount;
            entitySaleDetailStatementForm.individualTravelerCount = entitySaleDetailAndSumStatementForm.IndividualTravelerCount;
            entitySaleDetailStatementForm.individualTravelerRate = entitySaleDetailAndSumStatementForm.IndividualTravelerRate.toString() + "%";
            entitySaleDetailStatementForm.memberCount = entitySaleDetailAndSumStatementForm.MemberCount;
            entitySaleDetailStatementForm.memberRate = entitySaleDetailAndSumStatementForm.MemberRate.toString() + "%";
            entitySaleDetailStatementForm.unMemberCount = entitySaleDetailAndSumStatementForm.UnMemberCount;
            entitySaleDetailStatementForm.unMemberRate = entitySaleDetailAndSumStatementForm.UnMemberRate.toString() + "%";

            list.add(entitySaleDetailStatementForm);
        }

        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("门店客流报表", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), EntityPassengerFlowDetailEasyExcelOutputForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("门店客流报表").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return null;
    }

    public BaseOutput entitySalePassengerFlowStatement(EntityTradingQueryForm form) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        EntitySalePassengerFlowOutputForm passengerFlowOutputForm = new EntitySalePassengerFlowOutputForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        passengerFlowOutputForm.Detail = BasePageInfo.success(entityPassengerFlowMapper.getEntitySalePassengerFlowStatement(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate));
        passengerFlowOutputForm.SumOutputForm = entityPassengerFlowMapper.getEntitySalePassengerFlowStatementSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate);
        return BaseOutputForm.success(passengerFlowOutputForm);
    }

    public BaseOutput easyExcelEntitySalePassengerFlowStatement(EntityTradingQueryForm form, HttpServletResponse response) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));


        List<EntitySalePassengerFlowDetailEasyExcelOutputForm> list = entityPassengerFlowMapper.getEntitySalePassengerFlowStatementEasyExcel(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate);
        EntitySalePassengerFlowSumOutputForm entitySaleDetailAndSumStatementForm = entityPassengerFlowMapper.getEntitySalePassengerFlowStatementSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate);
        if (entitySaleDetailAndSumStatementForm != null) {
            EntitySalePassengerFlowDetailEasyExcelOutputForm entitySaleDetailStatementForm = new EntitySalePassengerFlowDetailEasyExcelOutputForm();
            entitySaleDetailStatementForm.entityName = "合计";
            entitySaleDetailStatementForm.customerCount = entitySaleDetailAndSumStatementForm.CustomerCount;
            entitySaleDetailStatementForm.passengerFlowCount = entitySaleDetailAndSumStatementForm.PassengerFlowCount;
            entitySaleDetailStatementForm.dealNewCount = entitySaleDetailAndSumStatementForm.DealNewCount;
            entitySaleDetailStatementForm.dealOldCount = entitySaleDetailAndSumStatementForm.DealOldCount;
            entitySaleDetailStatementForm.manCount = entitySaleDetailAndSumStatementForm.ManCount;
            entitySaleDetailStatementForm.womanCount = entitySaleDetailAndSumStatementForm.WomanCount;
            entitySaleDetailStatementForm.unknownCount = entitySaleDetailAndSumStatementForm.UnknownCount;
            entitySaleDetailStatementForm.individualTravelerCount = entitySaleDetailAndSumStatementForm.IndividualTravelerCount;
            entitySaleDetailStatementForm.individualTravelerRate = entitySaleDetailAndSumStatementForm.IndividualTravelerRate.toString() + "%";
            entitySaleDetailStatementForm.memberCount = entitySaleDetailAndSumStatementForm.MemberCount;
            entitySaleDetailStatementForm.memberRate = entitySaleDetailAndSumStatementForm.MemberRate.toString() + "%";
            entitySaleDetailStatementForm.unMemberCount = entitySaleDetailAndSumStatementForm.UnMemberCount;
            entitySaleDetailStatementForm.unMemberRate = entitySaleDetailAndSumStatementForm.UnMemberRate.toString() + "%";

            list.add(entitySaleDetailStatementForm);
        }

        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("门店销售客流报表", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), EntitySalePassengerFlowDetailEasyExcelOutputForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("门店销售客流报表").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return null;
    }

    public BaseOutput entityTreatPassengerFlowStatement(EntityTradingQueryForm form) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        EntityTreatPassengerFlowOutputForm passengerFlowOutputForm = new EntityTreatPassengerFlowOutputForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        passengerFlowOutputForm.Detail = BasePageInfo.success(entityPassengerFlowMapper.getEntityTreatPassengerFlowStatement(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate));
        passengerFlowOutputForm.SumOutputForm = entityPassengerFlowMapper.getEntityTreatPassengerFlowStatementSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate);
        return BaseOutputForm.success(passengerFlowOutputForm);
    }

    public BaseOutput easyExcelEntityTreatPassengerFlowStatement(EntityTradingQueryForm form, HttpServletResponse response) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));


        List<EntityTreatPassengerFlowDetailEasyExcelOutputForm> list = entityPassengerFlowMapper.getEntityTreatPassengerFlowStatementEasyExcel(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate);
        EntityTreatPassengerFlowSumOutputForm entityTreatDetailAndSumStatementForm = entityPassengerFlowMapper.getEntityTreatPassengerFlowStatementSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate);
        if (entityTreatDetailAndSumStatementForm != null) {
            EntityTreatPassengerFlowDetailEasyExcelOutputForm entityTreatDetailStatementForm = new EntityTreatPassengerFlowDetailEasyExcelOutputForm();
            entityTreatDetailStatementForm.entityName = "合计";
            entityTreatDetailStatementForm.customerCount = entityTreatDetailAndSumStatementForm.CustomerCount;
            entityTreatDetailStatementForm.passengerFlowCount = entityTreatDetailAndSumStatementForm.PassengerFlowCount;
            entityTreatDetailStatementForm.manCount = entityTreatDetailAndSumStatementForm.ManCount;
            entityTreatDetailStatementForm.womanCount = entityTreatDetailAndSumStatementForm.WomanCount;
            entityTreatDetailStatementForm.unknownCount = entityTreatDetailAndSumStatementForm.UnknownCount;
            entityTreatDetailStatementForm.individualTravelerCount = entityTreatDetailAndSumStatementForm.IndividualTravelerCount;
            entityTreatDetailStatementForm.individualTravelerRate = entityTreatDetailAndSumStatementForm.IndividualTravelerRate.toString() + "%";
            entityTreatDetailStatementForm.memberCount = entityTreatDetailAndSumStatementForm.MemberCount;
            entityTreatDetailStatementForm.memberRate = entityTreatDetailAndSumStatementForm.MemberRate.toString() + "%";
            entityTreatDetailStatementForm.unMemberCount = entityTreatDetailAndSumStatementForm.UnMemberCount;
            entityTreatDetailStatementForm.unMemberRate = entityTreatDetailAndSumStatementForm.UnMemberRate.toString() + "%";

            list.add(entityTreatDetailStatementForm);
        }

        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("门店消耗客流报表", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), EntityTreatPassengerFlowDetailEasyExcelOutputForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("门店消耗客流报表").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return null;
    }
}
