package com.zhelian.service.report.employeeStatement;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.zhelian.mapper.khs.EmployeeMapper;
import com.zhelian.mapper.report.EmployeePerformanceCommissionDetailMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.khs.entity.EmployeeEntity;
import com.zhelian.model.report.form.input.EmployeePerformanceCommissionDetailQueryForm;
import com.zhelian.model.report.form.output.EmployeePerformanceCommissionDetailExcelForm;
import com.zhelian.model.report.form.output.EmployeePerformanceCommissionDetailForm;
import com.zhelian.model.report.form.output.EmployeePerformanceCommissionForm;
import com.zhelian.model.report.form.output.EmployeePerformanceCommissionSumForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.TreeSet;
import java.util.stream.Collectors;

@Service
public class EmployeePerformanceCommissionService {
    @Autowired
    EmployeePerformanceCommissionDetailMapper employeePerformanceCommissionDetailMapper;
    @Autowired
    EmployeeMapper employeeMapper;

    public BaseOutput getEmployeePerformanceCommissionDetail(EmployeePerformanceCommissionDetailQueryForm form) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<EmployeePerformanceCommissionForm> list = new ArrayList<>();

        List<EmployeeEntity> entityEmployeeList = employeeMapper.entityEmployeeByJobType(form.EntityID);
        for (EmployeeEntity employeeEntity : entityEmployeeList) {
            EmployeePerformanceCommissionForm employeePerformanceCommissionForm = new EmployeePerformanceCommissionForm();
            employeePerformanceCommissionForm.EmployeeID = employeeEntity.ID;
            employeePerformanceCommissionForm.EmployeeName = employeeEntity.Name;
            employeePerformanceCommissionForm.cardDetail = employeePerformanceCommissionDetailMapper.getEmployeeCardPerformanceCommission(employeeEntity.ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName, form.TypeAsc, form.BillDateAsc);
            employeePerformanceCommissionForm.projectDetail = employeePerformanceCommissionDetailMapper.getEmployeeProjectPerformanceCommission(employeeEntity.ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName, form.TypeAsc, form.BillDateAsc);
            employeePerformanceCommissionForm.saleProductDetail = employeePerformanceCommissionDetailMapper.getEmployeeSaleProductPerformanceCommission(employeeEntity.ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName, form.TypeAsc, form.BillDateAsc);
            employeePerformanceCommissionForm.treatProductDetail = employeePerformanceCommissionDetailMapper.getEmployeeTreatProductPerformanceCommission(employeeEntity.ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName, form.TypeAsc, form.BillDateAsc);


            if (employeePerformanceCommissionForm.cardDetail.size() > 0 || employeePerformanceCommissionForm.projectDetail.size() > 0 || employeePerformanceCommissionForm.saleProductDetail.size() > 0) {

                List<EmployeePerformanceCommissionDetailForm> newCard = employeePerformanceCommissionForm.cardDetail.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.BillID + ";" + o.GoodID))), ArrayList::new));
                List<EmployeePerformanceCommissionDetailForm> newSaleProduct = employeePerformanceCommissionForm.saleProductDetail.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.BillID + ";" + o.GoodID))), ArrayList::new));
                employeePerformanceCommissionForm.employeeCardPerformanceCommissionSumForm = employeePerformanceCommissionDetailMapper.getEmployeeCardPerformanceCommissionSum(employeeEntity.ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName);
                employeePerformanceCommissionForm.employeeCardPerformanceCommissionSumForm.ArrearAmount = newCard.stream().map(p -> p.ArrearAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                employeePerformanceCommissionForm.employeeProjectPerformanceCommissionSumForm = employeePerformanceCommissionDetailMapper.getEmployeeProjectPerformanceCommissionSum(employeeEntity.ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName);
                employeePerformanceCommissionForm.employeeSaleProductPerformanceCommissionSumForm = employeePerformanceCommissionDetailMapper.getEmployeeSaleProductPerformanceCommissionSum(employeeEntity.ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName);
                employeePerformanceCommissionForm.employeeSaleProductPerformanceCommissionSumForm.ArrearAmount = newSaleProduct.stream().map(p -> p.ArrearAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                employeePerformanceCommissionForm.employeeTreatProductPerformanceCommissionSumForm = employeePerformanceCommissionDetailMapper.getEmployeeTreatProductPerformanceCommissionSum(employeeEntity.ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName);

                EmployeePerformanceCommissionSumForm employeePerformanceCommissionSumForm = new EmployeePerformanceCommissionSumForm();
                employeePerformanceCommissionSumForm.TotalAmount = (employeePerformanceCommissionForm.employeeCardPerformanceCommissionSumForm == null ? BigDecimal.ZERO : employeePerformanceCommissionForm.employeeCardPerformanceCommissionSumForm.TotalAmount).add((employeePerformanceCommissionForm.employeeProjectPerformanceCommissionSumForm == null ? BigDecimal.ZERO : employeePerformanceCommissionForm.employeeProjectPerformanceCommissionSumForm.TotalAmount)).add((employeePerformanceCommissionForm.employeeSaleProductPerformanceCommissionSumForm == null ? BigDecimal.ZERO : employeePerformanceCommissionForm.employeeSaleProductPerformanceCommissionSumForm.TotalAmount)).add((employeePerformanceCommissionForm.employeeTreatProductPerformanceCommissionSumForm == null ? BigDecimal.ZERO : employeePerformanceCommissionForm.employeeTreatProductPerformanceCommissionSumForm.TotalAmount));
                employeePerformanceCommissionSumForm.OriginAmount = (employeePerformanceCommissionForm.employeeCardPerformanceCommissionSumForm == null ? BigDecimal.ZERO : employeePerformanceCommissionForm.employeeCardPerformanceCommissionSumForm.OriginAmount).add((employeePerformanceCommissionForm.employeeProjectPerformanceCommissionSumForm == null ? BigDecimal.ZERO : employeePerformanceCommissionForm.employeeProjectPerformanceCommissionSumForm.OriginAmount)).add((employeePerformanceCommissionForm.employeeSaleProductPerformanceCommissionSumForm == null ? BigDecimal.ZERO : employeePerformanceCommissionForm.employeeSaleProductPerformanceCommissionSumForm.OriginAmount)).add((employeePerformanceCommissionForm.employeeTreatProductPerformanceCommissionSumForm == null ? BigDecimal.ZERO : employeePerformanceCommissionForm.employeeTreatProductPerformanceCommissionSumForm.OriginAmount));
                employeePerformanceCommissionSumForm.Performance = (employeePerformanceCommissionForm.employeeCardPerformanceCommissionSumForm == null ? BigDecimal.ZERO : employeePerformanceCommissionForm.employeeCardPerformanceCommissionSumForm.Performance).add((employeePerformanceCommissionForm.employeeProjectPerformanceCommissionSumForm == null ? BigDecimal.ZERO : employeePerformanceCommissionForm.employeeProjectPerformanceCommissionSumForm.Performance)).add((employeePerformanceCommissionForm.employeeSaleProductPerformanceCommissionSumForm == null ? BigDecimal.ZERO : employeePerformanceCommissionForm.employeeSaleProductPerformanceCommissionSumForm.Performance)).add((employeePerformanceCommissionForm.employeeTreatProductPerformanceCommissionSumForm == null ? BigDecimal.ZERO : employeePerformanceCommissionForm.employeeTreatProductPerformanceCommissionSumForm.Performance));
                employeePerformanceCommissionSumForm.Commission = (employeePerformanceCommissionForm.employeeCardPerformanceCommissionSumForm == null ? BigDecimal.ZERO : employeePerformanceCommissionForm.employeeCardPerformanceCommissionSumForm.Commission).add((employeePerformanceCommissionForm.employeeProjectPerformanceCommissionSumForm == null ? BigDecimal.ZERO : employeePerformanceCommissionForm.employeeProjectPerformanceCommissionSumForm.Commission)).add((employeePerformanceCommissionForm.employeeSaleProductPerformanceCommissionSumForm == null ? BigDecimal.ZERO : employeePerformanceCommissionForm.employeeSaleProductPerformanceCommissionSumForm.Commission)).add((employeePerformanceCommissionForm.employeeTreatProductPerformanceCommissionSumForm == null ? BigDecimal.ZERO : employeePerformanceCommissionForm.employeeTreatProductPerformanceCommissionSumForm.Commission));
                employeePerformanceCommissionSumForm.ArrearAmount = (employeePerformanceCommissionForm.employeeCardPerformanceCommissionSumForm == null ? BigDecimal.ZERO : employeePerformanceCommissionForm.employeeCardPerformanceCommissionSumForm.ArrearAmount).add((employeePerformanceCommissionForm.employeeProjectPerformanceCommissionSumForm == null ? BigDecimal.ZERO : employeePerformanceCommissionForm.employeeProjectPerformanceCommissionSumForm.ArrearAmount)).add((employeePerformanceCommissionForm.employeeSaleProductPerformanceCommissionSumForm == null ? BigDecimal.ZERO : employeePerformanceCommissionForm.employeeSaleProductPerformanceCommissionSumForm.ArrearAmount)).add((employeePerformanceCommissionForm.employeeTreatProductPerformanceCommissionSumForm == null ? BigDecimal.ZERO : employeePerformanceCommissionForm.employeeTreatProductPerformanceCommissionSumForm.ArrearAmount));
                employeePerformanceCommissionForm.employeePerformanceCommissionSumForm = employeePerformanceCommissionSumForm;

                employeePerformanceCommissionForm.payMethodForm = employeePerformanceCommissionDetailMapper.getEmployeePerformanceCommissionByPayMethod(employeeEntity.ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName);

                employeePerformanceCommissionForm.projectTreatForm = employeePerformanceCommissionDetailMapper.getProjectTreat(employeeEntity.ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName);
                employeePerformanceCommissionForm.generalCardTreatForm = employeePerformanceCommissionDetailMapper.getGeneralCardTreat(employeeEntity.ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName);
                employeePerformanceCommissionForm.timeCardTreatForm = employeePerformanceCommissionDetailMapper.getTimeCardTreat(employeeEntity.ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName);
                employeePerformanceCommissionForm.packageCardTreatForm = employeePerformanceCommissionDetailMapper.getPackageCardTreat(employeeEntity.ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName);
                employeePerformanceCommissionForm.savingCardTreatForm = employeePerformanceCommissionDetailMapper.getSavingCardTreat(employeeEntity.ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName);
                employeePerformanceCommissionForm.largessSavingCardTreatForm = employeePerformanceCommissionDetailMapper.getLargessSavingCardTreat(employeeEntity.ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName);
                list.add(employeePerformanceCommissionForm);
            }
        }

        return BaseOutputForm.success(list);
    }


    public BaseOutput excelEmployeePerformanceCommissionDetail(EmployeePerformanceCommissionDetailQueryForm form, HttpServletResponse response) {

        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<EmployeeEntity> newEntityEmployeeList = new ArrayList<>();
        List<EmployeeEntity> entityEmployeeList = employeeMapper.entityEmployeeByJobType(form.EntityID);
        for (int i = 0; i < entityEmployeeList.size(); i++) {
            List<EmployeePerformanceCommissionDetailForm> cardList = employeePerformanceCommissionDetailMapper.getEmployeeCardPerformanceCommission(entityEmployeeList.get(i).ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName, form.TypeAsc, form.BillDateAsc);
            List<EmployeePerformanceCommissionDetailForm> projectList = employeePerformanceCommissionDetailMapper.getEmployeeProjectPerformanceCommission(entityEmployeeList.get(i).ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName, form.TypeAsc, form.BillDateAsc);
            List<EmployeePerformanceCommissionDetailForm> productSaleList = employeePerformanceCommissionDetailMapper.getEmployeeSaleProductPerformanceCommission(entityEmployeeList.get(i).ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName, form.TypeAsc, form.BillDateAsc);
            List<EmployeePerformanceCommissionDetailForm> productTreatList = employeePerformanceCommissionDetailMapper.getEmployeeTreatProductPerformanceCommission(entityEmployeeList.get(i).ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName, form.TypeAsc, form.BillDateAsc);
            if (cardList.size() > 0 || projectList.size() > 0 || productSaleList.size() > 0 || productTreatList.size() > 0) {
                newEntityEmployeeList.add(entityEmployeeList.get(i));
            }
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = "员工业绩明细";
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream(), EmployeePerformanceCommissionDetailExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build()) {
            for (int i = 0; i < newEntityEmployeeList.size(); i++) {

                List<EmployeePerformanceCommissionDetailExcelForm> list = new ArrayList<>();

                List<EmployeePerformanceCommissionDetailExcelForm> cardList = employeePerformanceCommissionDetailMapper.getEmployeeCardPerformanceCommissionExcel(newEntityEmployeeList.get(i).ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName, form.TypeAsc, form.BillDateAsc);
                List<EmployeePerformanceCommissionDetailExcelForm> project = employeePerformanceCommissionDetailMapper.getEmployeeProjectPerformanceCommissionExcel(newEntityEmployeeList.get(i).ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName, form.TypeAsc, form.BillDateAsc);
                List<EmployeePerformanceCommissionDetailExcelForm> productSale = employeePerformanceCommissionDetailMapper.getEmployeeSaleProductPerformanceCommissionExcel(newEntityEmployeeList.get(i).ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName, form.TypeAsc, form.BillDateAsc);
                List<EmployeePerformanceCommissionDetailExcelForm> productTreat = employeePerformanceCommissionDetailMapper.getEmployeeTreatProductPerformanceCommissionExcel(newEntityEmployeeList.get(i).ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName, form.TypeAsc, form.BillDateAsc);

                List<EmployeePerformanceCommissionDetailExcelForm> newCard = cardList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.billID + ";" + o.goodID))), ArrayList::new));
                List<EmployeePerformanceCommissionDetailExcelForm> newSaleProduct = productSale.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.billID + ";" + o.goodID))), ArrayList::new));

                EmployeePerformanceCommissionDetailExcelForm cardSum = employeePerformanceCommissionDetailMapper.getEmployeeCardPerformanceCommissionSumExcel(newEntityEmployeeList.get(i).ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName);
                cardSum.arrearAmount = newCard.stream().map(p -> p.arrearAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                EmployeePerformanceCommissionDetailExcelForm projectSum = employeePerformanceCommissionDetailMapper.getEmployeeProjectPerformanceCommissionSumExcel(newEntityEmployeeList.get(i).ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName);
                EmployeePerformanceCommissionDetailExcelForm productSaleSum = employeePerformanceCommissionDetailMapper.getEmployeeSaleProductPerformanceCommissionSumExcel(newEntityEmployeeList.get(i).ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName);
                productSaleSum.arrearAmount = newSaleProduct.stream().map(p -> p.arrearAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                EmployeePerformanceCommissionDetailExcelForm productTreatSum = employeePerformanceCommissionDetailMapper.getEmployeeTreatProductPerformanceCommissionSumExcel(newEntityEmployeeList.get(i).ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName);

                EmployeePerformanceCommissionDetailExcelForm employeePerformanceCommissionSumForm = new EmployeePerformanceCommissionDetailExcelForm();
                employeePerformanceCommissionSumForm.totalAmount = (cardSum == null ? BigDecimal.ZERO : cardSum.totalAmount.equals("") ? BigDecimal.ZERO : new BigDecimal(cardSum.totalAmount)).add((projectSum == null ? BigDecimal.ZERO : projectSum.totalAmount.equals("") ? BigDecimal.ZERO : new BigDecimal(projectSum.totalAmount))).add((productSaleSum == null ? BigDecimal.ZERO : productSaleSum.totalAmount.equals("") ? BigDecimal.ZERO : new BigDecimal(productSaleSum.totalAmount))).add((productTreat == null ? BigDecimal.ZERO : productTreatSum.totalAmount.equals("") ? BigDecimal.ZERO : new BigDecimal(productTreatSum.totalAmount))).toString();
                employeePerformanceCommissionSumForm.originAmount = (cardSum == null ? BigDecimal.ZERO : cardSum.originAmount.equals("") ? BigDecimal.ZERO : new BigDecimal(cardSum.originAmount)).add((projectSum == null ? BigDecimal.ZERO : projectSum.originAmount.equals("") ? BigDecimal.ZERO : new BigDecimal(projectSum.originAmount))).add((productSaleSum == null ? BigDecimal.ZERO : productSaleSum.originAmount.equals("") ? BigDecimal.ZERO : new BigDecimal(productSaleSum.originAmount))).add((productTreat == null ? BigDecimal.ZERO : productTreatSum.originAmount.equals("") ? BigDecimal.ZERO : new BigDecimal(productTreatSum.originAmount))).toString();
                employeePerformanceCommissionSumForm.performance = (cardSum == null ? BigDecimal.ZERO : cardSum.performance.equals("") ? BigDecimal.ZERO : new BigDecimal(cardSum.performance)).add((projectSum == null ? BigDecimal.ZERO : projectSum.performance.equals("") ? BigDecimal.ZERO : new BigDecimal(projectSum.performance))).add((productSaleSum == null ? BigDecimal.ZERO : productSaleSum.performance.equals("") ? BigDecimal.ZERO : new BigDecimal(productSaleSum.performance))).add((productTreat == null ? BigDecimal.ZERO : productTreatSum.performance.equals("") ? BigDecimal.ZERO : new BigDecimal(productTreatSum.performance))).toString();
                employeePerformanceCommissionSumForm.commission = (cardSum == null ? BigDecimal.ZERO : cardSum.commission.equals("") ? BigDecimal.ZERO : new BigDecimal(cardSum.commission)).add((projectSum == null ? BigDecimal.ZERO : projectSum.commission.equals("") ? BigDecimal.ZERO : new BigDecimal(projectSum.commission))).add((productSaleSum == null ? BigDecimal.ZERO : productSaleSum.commission.equals("") ? BigDecimal.ZERO : new BigDecimal(productSaleSum.commission))).add((productTreat == null ? BigDecimal.ZERO : productTreatSum.commission.equals("") ? BigDecimal.ZERO : new BigDecimal(productTreatSum.commission))).toString();
                employeePerformanceCommissionSumForm.arrearAmount = (cardSum == null ? BigDecimal.ZERO : cardSum.arrearAmount.equals("") ? BigDecimal.ZERO : cardSum.arrearAmount.add((projectSum == null ? BigDecimal.ZERO : projectSum.arrearAmount.equals("") ? BigDecimal.ZERO : projectSum.arrearAmount)).add((productSaleSum == null ? BigDecimal.ZERO : productSaleSum.arrearAmount.equals("") ? BigDecimal.ZERO : productSaleSum.arrearAmount)).add((productTreat == null ? BigDecimal.ZERO : productTreatSum.arrearAmount.equals("") ? BigDecimal.ZERO : productTreatSum.arrearAmount)));
                EmployeePerformanceCommissionDetailExcelForm sum = fill("员工总合计", newEntityEmployeeList.get(i).Name + " / " + newEntityEmployeeList.get(i).ID, employeePerformanceCommissionSumForm.totalAmount, employeePerformanceCommissionSumForm.originAmount, employeePerformanceCommissionSumForm.performance, employeePerformanceCommissionSumForm.commission, employeePerformanceCommissionSumForm.arrearAmount);

                EmployeePerformanceCommissionDetailExcelForm cardSubSum = fill("", "卡项销售小计", cardSum.totalAmount, cardSum.originAmount, cardSum.performance, cardSum.commission, cardSum.arrearAmount);
                EmployeePerformanceCommissionDetailExcelForm projectSubSum = fill("", "服务项目小计", projectSum.totalAmount, projectSum.originAmount, projectSum.performance, projectSum.commission, projectSum.arrearAmount);
                EmployeePerformanceCommissionDetailExcelForm productSaleSubSum = fill("", "产品销售小计", productSaleSum.totalAmount, productSaleSum.originAmount, productSaleSum.performance, productSaleSum.commission, productSaleSum.arrearAmount);
                EmployeePerformanceCommissionDetailExcelForm productTreatSubSum = fill("", "产品消耗合计", productTreatSum.totalAmount, productTreatSum.originAmount, productTreatSum.performance, productTreatSum.commission, productTreatSum.arrearAmount);

                List<EmployeePerformanceCommissionDetailExcelForm> payMethod = employeePerformanceCommissionDetailMapper.getEmployeePerformanceCommissionByPayMethodExcel(newEntityEmployeeList.get(i).ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName);

                EmployeePerformanceCommissionDetailExcelForm projectTreat = employeePerformanceCommissionDetailMapper.getProjectTreatExcel(newEntityEmployeeList.get(i).ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName);
                EmployeePerformanceCommissionDetailExcelForm generalCardTreat = employeePerformanceCommissionDetailMapper.getGeneralCardTreatExcel(newEntityEmployeeList.get(i).ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName);
                EmployeePerformanceCommissionDetailExcelForm timeCardTreat = employeePerformanceCommissionDetailMapper.getTimeCardTreatExcel(newEntityEmployeeList.get(i).ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName);
                EmployeePerformanceCommissionDetailExcelForm packageCardTreat = employeePerformanceCommissionDetailMapper.getPackageCardTreatExcel(newEntityEmployeeList.get(i).ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName);
                EmployeePerformanceCommissionDetailExcelForm savingCardTreat = employeePerformanceCommissionDetailMapper.getSavingCardTreatExcel(newEntityEmployeeList.get(i).ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName);
                EmployeePerformanceCommissionDetailExcelForm largessSavingCardTreat = employeePerformanceCommissionDetailMapper.getLargessSavingCardTreatExcel(newEntityEmployeeList.get(i).ID, StartDate, EndDate, form.Type, form.BillID, form.CustomerName, form.CategoryGoodsType, form.CategoryID, form.GoodsName);


                list.addAll(cardList);
                if (cardList.size() > 0 && cardSum != null)
                    list.add(cardSum);
                list.addAll(project);
                if (project.size() > 0 && projectSum != null)
                    list.add(projectSum);
                list.addAll(productSale);
                if (productSale.size() > 0 && productSaleSum != null)
                    list.add(productSaleSum);
                list.addAll(productTreat);
                if (productTreat.size() > 0 && productTreatSum != null)
                    list.add(productTreatSum);
                if ((cardList.size() > 0 || project.size() > 0 || productSale.size() > 0 || productTreat.size() > 0) && sum != null)
                    list.add(sum);

                if (cardList.size() > 0 && cardSubSum != null)
                    list.add(cardSubSum);
                if (project.size() > 0 && projectSubSum != null)
                    list.add(projectSubSum);
                if (productSale.size() > 0 && productSaleSubSum != null)
                    list.add(productSaleSubSum);
                if (productTreat.size() > 0 && productTreatSubSum != null)
                    list.add(productTreatSubSum);

                if (payMethod != null)
                    list.addAll(payMethod);

                if (projectTreat != null && new BigDecimal(projectTreat.totalAmount).compareTo(BigDecimal.ZERO) > 0)
                    list.add(projectTreat);
                if (generalCardTreat != null && new BigDecimal(generalCardTreat.totalAmount).compareTo(BigDecimal.ZERO) > 0)
                    list.add(generalCardTreat);
                if (timeCardTreat != null && new BigDecimal(timeCardTreat.totalAmount).compareTo(BigDecimal.ZERO) > 0)
                    list.add(timeCardTreat);
                if (packageCardTreat != null && new BigDecimal(packageCardTreat.totalAmount).compareTo(BigDecimal.ZERO) > 0)
                    list.add(packageCardTreat);
                if (savingCardTreat != null && new BigDecimal(savingCardTreat.totalAmount).compareTo(BigDecimal.ZERO) > 0)
                    list.add(savingCardTreat);
                if (largessSavingCardTreat != null && new BigDecimal(largessSavingCardTreat.totalAmount).compareTo(BigDecimal.ZERO) > 0)
                    list.add(largessSavingCardTreat);


                // 每次都要创建writeSheet 这里注意必须指定sheetNo 而且sheetName必须不一样
                WriteSheet writeSheet = EasyExcel.writerSheet(i, newEntityEmployeeList.get(i).Name).build();
                // 分页去数据库查询数据 这里可以去数据库查询每一页的数据
                List<EmployeePerformanceCommissionDetailExcelForm> data = list;
                excelWriter.write(data, writeSheet);

            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return null;
    }

    private EmployeePerformanceCommissionDetailExcelForm fill(String type, String name, String totalAmount, String originAmount, String performance, String commission, BigDecimal arrearAmount) {
        EmployeePerformanceCommissionDetailExcelForm employeePerformanceCommissionSumForm = new EmployeePerformanceCommissionDetailExcelForm();
        employeePerformanceCommissionSumForm.type = type;
        employeePerformanceCommissionSumForm.billID = "";
        employeePerformanceCommissionSumForm.billDate = "";

        employeePerformanceCommissionSumForm.customerName = "";
        employeePerformanceCommissionSumForm.customerCode = "";
        employeePerformanceCommissionSumForm.goodsTypeName = "";
        employeePerformanceCommissionSumForm.parentCategoryName = "";
        employeePerformanceCommissionSumForm.categoryName = "";
        employeePerformanceCommissionSumForm.goodName = "";
        employeePerformanceCommissionSumForm.alias = "";
        employeePerformanceCommissionSumForm.isLargess = "";
        employeePerformanceCommissionSumForm.originCard = "";
        employeePerformanceCommissionSumForm.price = "";
        employeePerformanceCommissionSumForm.discount = "";
        employeePerformanceCommissionSumForm.payMethodName = name;
        employeePerformanceCommissionSumForm.totalAmount = totalAmount;
        employeePerformanceCommissionSumForm.originAmount = originAmount;
        employeePerformanceCommissionSumForm.performance = performance;
        employeePerformanceCommissionSumForm.commission = commission;
        employeePerformanceCommissionSumForm.arrearAmount = arrearAmount;
        employeePerformanceCommissionSumForm.detailInfo = "";


        return employeePerformanceCommissionSumForm;
    }
}
