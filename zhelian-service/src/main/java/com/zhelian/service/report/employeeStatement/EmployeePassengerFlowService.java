package com.zhelian.service.report.employeeStatement;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.report.EmployeePassengerFlowMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.report.form.input.EmployeePassengerFlowQueryForm;
import com.zhelian.model.report.form.output.EmployeePassengerFlowDetailStatementExcelForm;
import com.zhelian.model.report.form.output.EmployeePassengerFlowStatementForm;
import com.zhelian.model.report.form.output.EmployeePassengerFlowSumStatementForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;


@Service
public class EmployeePassengerFlowService {

    @Autowired
    EmployeePassengerFlowMapper employeePassengerFlowMapper;

    public BaseOutput getEmployeePassengerFlowStatement(EmployeePassengerFlowQueryForm form) {
        form.EmployeeID = SystemUserUtil.getSystemUserID();
        EmployeePassengerFlowStatementForm statementForm = new EmployeePassengerFlowStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        statementForm.detail = BasePageInfo.success(employeePassengerFlowMapper.getEmployeePassengerFlowStatement(form));
        statementForm.employeePassengerFlowSumStatementForm = employeePassengerFlowMapper.getEmployeePassengerFlowStatementSum(form);
        return BaseOutputForm.success(statementForm);
    }

    public BaseOutput excelEmployeePassengerFlowStatement(EmployeePassengerFlowQueryForm form, HttpServletResponse response) {
        form.EmployeeID = SystemUserUtil.getSystemUserID();
        List<EmployeePassengerFlowDetailStatementExcelForm> list = employeePassengerFlowMapper.getEmployeePassengerFlowStatementExcel(form);
        EmployeePassengerFlowSumStatementForm employeePassengerFlowSumStatementForm = employeePassengerFlowMapper.getEmployeePassengerFlowStatementSum(form);
        if (employeePassengerFlowSumStatementForm != null) {
            EmployeePassengerFlowDetailStatementExcelForm statementForm = new EmployeePassengerFlowDetailStatementExcelForm();
            statementForm.id = "合计";
            statementForm.name = "";
            statementForm.jobName = "";
            statementForm.entityName = "";
            statementForm.employeePassengerFlow = employeePassengerFlowSumStatementForm.EmployeePassengerFlow.doubleValue();
            statementForm.employeeSalePassengerFlow = employeePassengerFlowSumStatementForm.EmployeeSalePassengerFlow.doubleValue();
            statementForm.employeeTreatPassengerFlow = employeePassengerFlowSumStatementForm.EmployeeTreatPassengerFlow.doubleValue();
            list.add(statementForm);
        }
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("员工客流", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), EmployeePassengerFlowDetailStatementExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("员工客流").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }
}
