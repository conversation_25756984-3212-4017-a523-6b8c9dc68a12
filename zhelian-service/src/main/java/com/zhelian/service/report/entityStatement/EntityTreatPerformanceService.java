package com.zhelian.service.report.entityStatement;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.report.EntityTreatPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.report.form.input.EntityTreatPerformanceQueryForm;
import com.zhelian.model.report.form.output.EntityTreatPerformanceDetailOutputEasyExcelForm;
import com.zhelian.model.report.form.output.EntityTreatPerformanceOutputForm;
import com.zhelian.model.report.form.output.EntityTreatPerformanceSumOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;


@Service
public class EntityTreatPerformanceService {

    @Autowired
    EntityTreatPerformanceMapper entityTreatPerformanceMapper;

    public BaseOutput entityTreatPerformance(EntityTreatPerformanceQueryForm form) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        EntityTreatPerformanceOutputForm passengerFlowOutputForm = new EntityTreatPerformanceOutputForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        passengerFlowOutputForm.Detail = BasePageInfo.success(entityTreatPerformanceMapper.getEntityTreatPerformance(SystemUserUtil.getSystemUserID(), form.EntityID, form.TreatCardTypeName, form.CustomerName, StartDate, EndDate, form.IsLargess, form.BillType, form.CategoryID, form.GoodsTypeName,form.BillID,form.CustomerLevelID));
        passengerFlowOutputForm.SumOutputForm = entityTreatPerformanceMapper.getEntityTreatPerformanceSum(SystemUserUtil.getSystemUserID(), form.EntityID, form.TreatCardTypeName, form.CustomerName, StartDate, EndDate, form.IsLargess, form.BillType, form.CategoryID, form.GoodsTypeName,form.BillID,form.CustomerLevelID);
        return BaseOutputForm.success(passengerFlowOutputForm);
    }

    public BaseOutput easyExcelEntityTreatPerformance(EntityTreatPerformanceQueryForm form, HttpServletResponse response) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<EntityTreatPerformanceDetailOutputEasyExcelForm> list = entityTreatPerformanceMapper.getEntityTreatPerformanceEasyExcel(SystemUserUtil.getSystemUserID(), form.EntityID, form.TreatCardTypeName, form.CustomerName, StartDate, EndDate, form.IsLargess, form.BillType, form.CategoryID, form.GoodsTypeName,form.BillID,form.CustomerLevelID);
        EntityTreatPerformanceSumOutputForm entityTreatDetailAndSumStatementForm = entityTreatPerformanceMapper.getEntityTreatPerformanceSum(SystemUserUtil.getSystemUserID(), form.EntityID, form.TreatCardTypeName, form.CustomerName, StartDate, EndDate, form.IsLargess, form.BillType, form.CategoryID, form.GoodsTypeName,form.BillID,form.CustomerLevelID);
        if (entityTreatDetailAndSumStatementForm != null) {
            EntityTreatPerformanceDetailOutputEasyExcelForm entityTreatDetailStatementForm = new EntityTreatPerformanceDetailOutputEasyExcelForm();
            entityTreatDetailStatementForm.billEntityName = "合计";
            entityTreatDetailStatementForm.treatBillID = "";
            entityTreatDetailStatementForm.billDate = "";
            entityTreatDetailStatementForm.entityName = "";
            entityTreatDetailStatementForm.customerName = "";
            entityTreatDetailStatementForm.code = "";
            entityTreatDetailStatementForm.phoneNumber = "";
            entityTreatDetailStatementForm.cardName = "";
            entityTreatDetailStatementForm.treatCardTypeName = "";
            entityTreatDetailStatementForm.isLargess = "";
            entityTreatDetailStatementForm.goodName = "";
            entityTreatDetailStatementForm.categoryName = "";
            entityTreatDetailStatementForm.billType = "";
            entityTreatDetailStatementForm.price = "";
            entityTreatDetailStatementForm.quantity = "";
            entityTreatDetailStatementForm.preferentialAmount = entityTreatDetailAndSumStatementForm.PreferentialAmount;
            entityTreatDetailStatementForm.totalAmount = entityTreatDetailAndSumStatementForm.TotalAmount;
            entityTreatDetailStatementForm.payPerformance = entityTreatDetailAndSumStatementForm.PayPerformance;
            entityTreatDetailStatementForm.cardPerformance = entityTreatDetailAndSumStatementForm.CardPerformance;
            entityTreatDetailStatementForm.cardLargessPerformance = entityTreatDetailAndSumStatementForm.CardLargessPerformance;
            entityTreatDetailStatementForm.largessPerformance = entityTreatDetailAndSumStatementForm.LargessPerformance;
            list.add(entityTreatDetailStatementForm);
        }
        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("门店消耗业绩明细", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), EntityTreatPerformanceDetailOutputEasyExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("门店消耗业绩明细").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return null;
    }

}
