package com.zhelian.service.report.channel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.report.DeveloperTreatPerformanceDetailMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.report.form.input.DeveloperTreatPerformanceDetailForm;
import com.zhelian.model.report.form.output.DeveloperTreatPerformanceDetailAndSumStatementForm;
import com.zhelian.model.report.form.output.DeveloperTreatPerformanceDetailStatementEasyExcelForm;
import com.zhelian.model.report.form.output.DeveloperTreatPerformanceSumStatementForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.util.List;

@Service
public class DeveloperTreatPerformanceService {

    @Autowired
    DeveloperTreatPerformanceDetailMapper developerTreatPerformanceDetailMapper;

    public BaseOutput getDeveloperTreatPerformanceDetail(DeveloperTreatPerformanceDetailForm form) {
        form.StartDate = LocalDate.parse(form.StartDate).toString();
        form.EndDate = LocalDate.parse(form.EndDate).plusDays(1).toString();

        DeveloperTreatPerformanceDetailAndSumStatementForm detailAndSumStatementForm = new DeveloperTreatPerformanceDetailAndSumStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        detailAndSumStatementForm.developerTreatPerformanceDetailStatementFormBasePageInfo = BasePageInfo.success(developerTreatPerformanceDetailMapper.getDeveloperTreatPerformanceDetail(form));
        detailAndSumStatementForm.developerTreatPerformanceSumStatementForm = developerTreatPerformanceDetailMapper.getDeveloperTreatPerformanceSum(form);
        return BaseOutputForm.success(detailAndSumStatementForm);
    }


    public BaseOutput easyExcelDeveloperTreatPerformanceDetail(DeveloperTreatPerformanceDetailForm form, HttpServletResponse response) {
        form.StartDate = LocalDate.parse(form.StartDate).toString();
        form.EndDate = LocalDate.parse(form.EndDate).plusDays(1).toString();

        List<DeveloperTreatPerformanceDetailStatementEasyExcelForm> list = developerTreatPerformanceDetailMapper.getEasyExcelDeveloperTreatPerformanceDetail(form);
        DeveloperTreatPerformanceSumStatementForm developerTreatPerformanceSumStatementForm = developerTreatPerformanceDetailMapper.getDeveloperTreatPerformanceSum(form);
        if (developerTreatPerformanceSumStatementForm != null) {
            DeveloperTreatPerformanceDetailStatementEasyExcelForm statementForm = new DeveloperTreatPerformanceDetailStatementEasyExcelForm();
            statementForm.treatBillID = "合计";
            statementForm.billDate = "";
            statementForm.billType = "";
            statementForm.entityName = "";
            statementForm.customerName = "";
            statementForm.channelName = "";
            statementForm.channelType = "";
            statementForm.treatCardTypeName = "";
            statementForm.cardName = "";
            statementForm.categoryName = "";
            statementForm.goodName = "";
            statementForm.employeeName = "";
            statementForm.payPerformance = developerTreatPerformanceSumStatementForm.PayPerformance.doubleValue();
            statementForm.cardPerformance = developerTreatPerformanceSumStatementForm.CardPerformance.doubleValue();
            statementForm.cardLargessPerformance = developerTreatPerformanceSumStatementForm.CardLargessPerformance.doubleValue();
            statementForm.largessPerformance = developerTreatPerformanceSumStatementForm.LargessPerformance.doubleValue();
            list.add(statementForm);
        }

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("开发消耗业绩提成明细", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), DeveloperTreatPerformanceDetailStatementEasyExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("开发消耗业绩提成明细").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }
}
