package com.zhelian.service.report.channelStatement;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.report.ChannelTreatDetailMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.report.form.input.ChannelTreatDetailForm;
import com.zhelian.model.report.form.output.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class ChannelTreatDetailService {


    @Autowired
    ChannelTreatDetailMapper channelTreatDetailMapper;

    public BaseOutput getChannelTreatDetail(ChannelTreatDetailForm form) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        ChannelTreatDetailAndSumStatementForm channelTreatDetailAndSumStatementForm = new ChannelTreatDetailAndSumStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        channelTreatDetailAndSumStatementForm.channelTreatDetailStatementForms = BasePageInfo.success(channelTreatDetailMapper.getChannelTreatDetail(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.TreatCardTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.BuyEntityID, form.CustomerLevelID, form.CustomerSourceID,form.Channel,form.IntroducerName,form.ChannelName));
        channelTreatDetailAndSumStatementForm.channelTreatDetailSumStatementForm = channelTreatDetailMapper.getChannelTreatDetailSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.TreatCardTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.BuyEntityID, form.CustomerLevelID, form.CustomerSourceID,form.Channel,form.IntroducerName,form.ChannelName);
        return BaseOutputForm.success(channelTreatDetailAndSumStatementForm);
    }

    public BaseOutput easyExcelChannelTreatDetailStatement(ChannelTreatDetailForm form, HttpServletResponse response) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<ChannelTreatDetailStatementEasyExcelForm> list = channelTreatDetailMapper.getEasyExcelChannelTreatDetail(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.TreatCardTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.BuyEntityID, form.CustomerLevelID, form.CustomerSourceID,form.Channel,form.IntroducerName,form.ChannelName);
        ChannelTreatDetailSumStatementForm channelTreatDetailSum = channelTreatDetailMapper.getChannelTreatDetailSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.TreatCardTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.BuyEntityID, form.CustomerLevelID, form.CustomerSourceID,form.Channel,form.IntroducerName,form.ChannelName);
        if (channelTreatDetailSum != null) {
            ChannelTreatDetailStatementEasyExcelForm channelTreatDetailStatementEasyExcelForm = new ChannelTreatDetailStatementEasyExcelForm();
            channelTreatDetailStatementEasyExcelForm.treatBillID = "合计";
            channelTreatDetailStatementEasyExcelForm.billDate = "";
            channelTreatDetailStatementEasyExcelForm.entityName = "";
            channelTreatDetailStatementEasyExcelForm.buyEntityName = "";
            channelTreatDetailStatementEasyExcelForm.customerName = "";
            channelTreatDetailStatementEasyExcelForm.customerPhoneNumber = "";
            channelTreatDetailStatementEasyExcelForm.customerEntityName = "";
            channelTreatDetailStatementEasyExcelForm.employeeName = "";
            channelTreatDetailStatementEasyExcelForm.treatCardTypeName = "";
            channelTreatDetailStatementEasyExcelForm.cardName = "";
            channelTreatDetailStatementEasyExcelForm.goodName = "";
            channelTreatDetailStatementEasyExcelForm.price = null;
            channelTreatDetailStatementEasyExcelForm.quantity = null;
            channelTreatDetailStatementEasyExcelForm.isLargess = "";
            channelTreatDetailStatementEasyExcelForm.preferentialAmount = channelTreatDetailSum.PreferentialAmount;
            channelTreatDetailStatementEasyExcelForm.totalAmount = channelTreatDetailSum.TotalAmount;
            channelTreatDetailStatementEasyExcelForm.treatPayAmount = channelTreatDetailSum.TreatPayAmount;
            channelTreatDetailStatementEasyExcelForm.treatCardAmount = channelTreatDetailSum.TreatCardAmount;
            channelTreatDetailStatementEasyExcelForm.treatCardLargessAmount = channelTreatDetailSum.TreatCardLargessAmount;
            channelTreatDetailStatementEasyExcelForm.treatLargessAmount = channelTreatDetailSum.TreatLargessAmount;
            list.add(channelTreatDetailStatementEasyExcelForm);
        }
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("消耗明细", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), ChannelTreatDetailStatementEasyExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("消耗明细").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    public BaseOutput getChannelTreatRefundDetail(ChannelTreatDetailForm form) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        ChannelTreatRefundDetailAndSumStatementForm channelTreatRefundDetailAndSumStatementForm = new ChannelTreatRefundDetailAndSumStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        channelTreatRefundDetailAndSumStatementForm.channelTreatRefundDetailStatementForms = BasePageInfo.success(channelTreatDetailMapper.getChannelTreatRefundDetail(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.TreatCardTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.BuyEntityID, form.CustomerLevelID, form.CustomerSourceID,form.Channel,form.IntroducerName,form.ChannelName));
        channelTreatRefundDetailAndSumStatementForm.channelTreatRefundDetailSumStatementForm = channelTreatDetailMapper.getChannelTreatRefundDetailSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.TreatCardTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.BuyEntityID, form.CustomerLevelID, form.CustomerSourceID,form.Channel,form.IntroducerName,form.ChannelName);
        return BaseOutputForm.success(channelTreatRefundDetailAndSumStatementForm);
    }

    public BaseOutput easyExcelChannelTreatRefundDetailStatement(ChannelTreatDetailForm form, HttpServletResponse response) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<ChannelTreatRefundDetailStatementEasyExcelForm> list = channelTreatDetailMapper.getEasyExcelChannelTreatRefundDetail(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.TreatCardTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.BuyEntityID, form.CustomerLevelID, form.CustomerSourceID,form.Channel,form.IntroducerName,form.ChannelName);
        ChannelTreatRefundDetailSumStatementForm channelTreatDetailSum = channelTreatDetailMapper.getChannelTreatRefundDetailSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.TreatCardTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.BuyEntityID, form.CustomerLevelID, form.CustomerSourceID,form.Channel,form.IntroducerName,form.ChannelName);
        if (channelTreatDetailSum != null) {
            ChannelTreatRefundDetailStatementEasyExcelForm channelTreatRefundDetailStatementForm = new ChannelTreatRefundDetailStatementEasyExcelForm();
            channelTreatRefundDetailStatementForm.treatBillID = "合计";
            channelTreatRefundDetailStatementForm.billDate = "";
            channelTreatRefundDetailStatementForm.entityName = "";
            channelTreatRefundDetailStatementForm.buyEntityName = "";
            channelTreatRefundDetailStatementForm.customerName = "";
            channelTreatRefundDetailStatementForm.customerPhoneNumber = "";
            channelTreatRefundDetailStatementForm.customerEntityName = "";
            channelTreatRefundDetailStatementForm.employeeName = "";
            channelTreatRefundDetailStatementForm.treatCardTypeName = "";
            channelTreatRefundDetailStatementForm.cardName = "";
            channelTreatRefundDetailStatementForm.goodName = "";
            channelTreatRefundDetailStatementForm.quantity = null;
            channelTreatRefundDetailStatementForm.isLargess = "";
            channelTreatRefundDetailStatementForm.totalAmount = channelTreatDetailSum.TotalAmount;
            channelTreatRefundDetailStatementForm.treatPayAmount = channelTreatDetailSum.TreatPayAmount;
            channelTreatRefundDetailStatementForm.treatCardAmount = channelTreatDetailSum.TreatCardAmount;
            channelTreatRefundDetailStatementForm.treatCardLargessAmount = channelTreatDetailSum.TreatCardLargessAmount;
            channelTreatRefundDetailStatementForm.treatLargessAmount = channelTreatDetailSum.TreatLargessAmount;
            list.add(channelTreatRefundDetailStatementForm);
        }

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("退消耗明细", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), ChannelTreatRefundDetailStatementEasyExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("退消耗明细").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }
}
