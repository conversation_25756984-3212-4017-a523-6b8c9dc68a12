package com.zhelian.service.report.employeeStatement;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.report.EmployeeTreatPerformanceCommissionDetailMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.report.form.input.EmployeeTreatPerformanceCommissionDetailForm;
import com.zhelian.model.report.form.output.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class EmployeeTreatPerformanceCommissionService {
    @Autowired
    EmployeeTreatPerformanceCommissionDetailMapper employeeTreatPerformanceCommissionDetailMapper;

    public BaseOutput getEmployeeSalePerformanceCommissionDetail(EmployeeTreatPerformanceCommissionDetailForm form) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        EmployeeTreatPerformanceCommissionDetailAndSumStatementForm employeeTreatPerformanceCommissionDetailAndSumStatementForm = new EmployeeTreatPerformanceCommissionDetailAndSumStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        employeeTreatPerformanceCommissionDetailAndSumStatementForm.employeeTreatPerformanceCommissionDetailStatementForms = BasePageInfo.success(employeeTreatPerformanceCommissionDetailMapper.getEmployeeTreatPerformanceCommissionDetail(SystemUserUtil.getSystemUserID(), form.EntityID,  StartDate, EndDate, form.TreatCardTypeName, form.EmployeeName, form.CustomerName, form.JobID, form.IsLargess, form.BillID, form.CategoryID, form.GoodsName, form.GoodsTypeName,form.CustomerLevelID));
        employeeTreatPerformanceCommissionDetailAndSumStatementForm.employeeTreatPerformanceCommissionSumStatementForm = employeeTreatPerformanceCommissionDetailMapper.getEmployeeTreatPerformanceCommissionSum(SystemUserUtil.getSystemUserID(), form.EntityID,  StartDate, EndDate, form.TreatCardTypeName, form.EmployeeName, form.CustomerName, form.JobID, form.IsLargess, form.BillID, form.CategoryID, form.GoodsName, form.GoodsTypeName,form.CustomerLevelID);
        return BaseOutputForm.success(employeeTreatPerformanceCommissionDetailAndSumStatementForm);
    }

    public BaseOutput excelEmployeeTreatPerformanceCommissionDetail(EmployeeTreatPerformanceCommissionDetailForm form, HttpServletResponse response) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<EmployeeTreatPerformanceCommissionDetailStatementExcelForm> list = employeeTreatPerformanceCommissionDetailMapper.getExcelEmployeeTreatPerformanceCommissionDetail(SystemUserUtil.getSystemUserID(), form.EntityID,  StartDate, EndDate, form.TreatCardTypeName, form.EmployeeName, form.CustomerName, form.JobID, form.IsLargess, form.BillID, form.CategoryID, form.GoodsName, form.GoodsTypeName,form.CustomerLevelID);
        EmployeeTreatPerformanceCommissionSumStatementForm employeeSalePerformanceCommissionDetailAndSumStatementForm = employeeTreatPerformanceCommissionDetailMapper.getEmployeeTreatPerformanceCommissionSum(SystemUserUtil.getSystemUserID(), form.EntityID,  StartDate, EndDate, form.TreatCardTypeName, form.EmployeeName, form.CustomerName, form.JobID, form.IsLargess, form.BillID, form.CategoryID, form.GoodsName, form.GoodsTypeName,form.CustomerLevelID);
        if (employeeSalePerformanceCommissionDetailAndSumStatementForm != null) {
            EmployeeTreatPerformanceCommissionDetailStatementExcelForm statementForm = new EmployeeTreatPerformanceCommissionDetailStatementExcelForm();
            statementForm.treatBillID = "合计";
            statementForm.billDate = "";
            statementForm.entityName = "";
            statementForm.employeeID = "";
            statementForm.employeeName = "";
            statementForm.jobName = "";
            statementForm.customerName = "";
            statementForm.customerSourceName = "";
            statementForm.customerLevelName = "";
            statementForm.channelName = "";
            statementForm.billRemark = "";
            statementForm.treatCardTypeName = "";
            statementForm.cardName = "";
            statementForm.categoryName = "";
            statementForm.goodName = "";
            statementForm.isLargess = "";
            statementForm.accountRemark = "";
            statementForm.price = null;
            statementForm.quantity = null;
            statementForm.preferentialAmount = null;
            statementForm.totalAmount = employeeSalePerformanceCommissionDetailAndSumStatementForm.TotalAmount;
            statementForm.payAmount = employeeSalePerformanceCommissionDetailAndSumStatementForm.PayAmount;
            statementForm.cardDeductionAmount = employeeSalePerformanceCommissionDetailAndSumStatementForm.CardDeductionAmount;
            statementForm.largessCardDeductionAmount = employeeSalePerformanceCommissionDetailAndSumStatementForm.LargessCardDeductionAmount;
            statementForm.scale = "";
            statementForm.payPerformance = employeeSalePerformanceCommissionDetailAndSumStatementForm.PayPerformance;
            statementForm.payPerformanceCommission = employeeSalePerformanceCommissionDetailAndSumStatementForm.PayPerformanceCommission;
            statementForm.cardPerformance = employeeSalePerformanceCommissionDetailAndSumStatementForm.CardPerformance;
            statementForm.cardPerformanceCommission = employeeSalePerformanceCommissionDetailAndSumStatementForm.CardPerformanceCommission;
            statementForm.cardLargessPerformance = employeeSalePerformanceCommissionDetailAndSumStatementForm.CardLargessPerformance;
            statementForm.cardLargessPerformanceCommission = employeeSalePerformanceCommissionDetailAndSumStatementForm.CardLargessPerformanceCommission;
            statementForm.largessPerformance = employeeSalePerformanceCommissionDetailAndSumStatementForm.LargessPerformance;
            statementForm.largessPerformanceCommission = employeeSalePerformanceCommissionDetailAndSumStatementForm.LargessPerformanceCommission;
            statementForm.specialBenefitCommission = employeeSalePerformanceCommissionDetailAndSumStatementForm.SpecialBenefitCommission;
            list.add(statementForm);
        }

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("员工消耗业绩", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), EmployeeTreatPerformanceCommissionDetailStatementExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("员工消耗业绩").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    public BaseOutput getEmployeeTreatPerformanceCommissionStatistics(EmployeeTreatPerformanceCommissionDetailForm form) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        EmployeeTreatPerformanceCommissionStatisticsStatementForm statementForm = new EmployeeTreatPerformanceCommissionStatisticsStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        statementForm.employeeTreatPerformanceCommissionDetailStatementForms = BasePageInfo.success(employeeTreatPerformanceCommissionDetailMapper.getEmployeeTreatPerformanceCommissionDetailStatistics(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.EmployeeName, form.JobID));
        statementForm.employeeTreatPerformanceCommissionSumStatementForm = employeeTreatPerformanceCommissionDetailMapper.getEmployeeTreatPerformanceCommissionSumStatistics(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.EmployeeName, form.JobID);
        return BaseOutputForm.success(statementForm);
    }

    public BaseOutput statisticsExcel(EmployeeTreatPerformanceCommissionDetailForm form, HttpServletResponse response) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<EmployeeTreatPerformanceCommissionDetailStatisticsStatementExcelForm> list = employeeTreatPerformanceCommissionDetailMapper.getExcelEmployeeTreatPerformanceCommissionDetailStatistics(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.EmployeeName, form.JobID);
        EmployeeTreatPerformanceCommissionSumStatisticsStatementForm statisticsStatementForm = employeeTreatPerformanceCommissionDetailMapper.getEmployeeTreatPerformanceCommissionSumStatistics(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.EmployeeName, form.JobID);
        if (statisticsStatementForm != null) {
            EmployeeTreatPerformanceCommissionDetailStatisticsStatementExcelForm statementForm = new EmployeeTreatPerformanceCommissionDetailStatisticsStatementExcelForm();
            statementForm.employeeID = "合计";
            statementForm.employeeName = "";
            statementForm.jobName = "";
            statementForm.entityName = "";
            statementForm.treatPayPerformance = statisticsStatementForm.TreatPayPerformance;
            statementForm.treatPayCommission = statisticsStatementForm.TreatPayCommission;
            statementForm.treatCardPerformance = statisticsStatementForm.TreatCardPerformance;
            statementForm.treatCardCommission = statisticsStatementForm.TreatCardCommission;
            statementForm.treatCardLargessPerformance = statisticsStatementForm.TreatCardLargessPerformance;
            statementForm.treatCardLargessCommission = statisticsStatementForm.TreatCardLargessCommission;
            statementForm.treatLargessPerformance = statisticsStatementForm.TreatLargessPerformance;
            statementForm.treatLargessCommission = statisticsStatementForm.TreatLargessCommission;
            statementForm.treatSpecialBenefitCommission = statisticsStatementForm.TreatSpecialBenefitCommission;
            statementForm.refundTreatPayPerformance = statisticsStatementForm.RefundTreatPayPerformance;
            statementForm.refundTreatPayCommission = statisticsStatementForm.RefundTreatPayCommission;
            statementForm.refundTreatCardPerformance = statisticsStatementForm.RefundTreatCardPerformance;
            statementForm.refundTreatCardCommission = statisticsStatementForm.RefundTreatCardCommission;
            statementForm.refundTreatCardLargessPerformance = statisticsStatementForm.RefundTreatCardLargessPerformance;
            statementForm.refundTreatCardLargessCommission = statisticsStatementForm.RefundTreatCardLargessCommission;
            statementForm.refundTreatLargessPerformance = statisticsStatementForm.RefundTreatLargessPerformance;
            statementForm.refundTreatLargessCommission = statisticsStatementForm.RefundTreatLargessCommission;
            statementForm.refundTreatSpecialBenefitCommission = statisticsStatementForm.RefundTreatSpecialBenefitCommission;
            statementForm.totalPayPerformance = statisticsStatementForm.TotalPayPerformance;
            statementForm.totalPayCommission = statisticsStatementForm.TotalPayCommission;
            statementForm.totalCardPerformance = statisticsStatementForm.TotalCardPerformance;
            statementForm.totalCardCommission = statisticsStatementForm.TotalCardCommission;
            statementForm.totalCardLargessPerformance = statisticsStatementForm.TotalCardLargessPerformance;
            statementForm.totalCardLargessCommission = statisticsStatementForm.TotalCardLargessCommission;
            statementForm.totalLargessPerformance = statisticsStatementForm.TotalLargessPerformance;
            statementForm.totalLargessCommission = statisticsStatementForm.TotalLargessCommission;
            statementForm.totalSpecialBenefitCommission = statisticsStatementForm.TotalSpecialBenefitCommission;
            list.add(statementForm);
        }

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("员工消耗业绩统计", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), EmployeeTreatPerformanceCommissionDetailStatisticsStatementExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("员工消耗业绩统计").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }
}
