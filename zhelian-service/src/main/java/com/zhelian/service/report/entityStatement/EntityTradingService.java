package com.zhelian.service.report.entityStatement;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.khs.EntityMapper;
import com.zhelian.mapper.report.EntityTradingMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.report.form.input.EntityTradingQueryForm;
import com.zhelian.model.report.form.output.EntityTradingDetailOutputForm;
import com.zhelian.model.report.form.output.EntityTradingOutputForm;
import com.zhelian.model.report.form.output.EntityTradingPayMethodDetailOutputForm;
import com.zhelian.model.report.form.output.EntityTradingSumOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class EntityTradingService {

    @Autowired
    EntityTradingMapper entityTradingMapper;
    @Autowired
    EntityMapper entityMapper;


    public BaseOutput tradingStatement(EntityTradingQueryForm form) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        EntityTradingSumOutputForm entityTradingSumOutputForm = new EntityTradingSumOutputForm();
        entityTradingSumOutputForm = entityTradingMapper.getTradingStatementSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate);
        entityTradingSumOutputForm.EntityName = "合计";
        entityTradingSumOutputForm.PayMethod = entityTradingMapper.getTradingStatementSumPayMethod(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate);


        EntityTradingOutputForm entityTradingOutputForm = new EntityTradingOutputForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        entityTradingOutputForm.Detail = BasePageInfo.success(entityTradingMapper.getTradingStatement(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate));
        entityTradingOutputForm.SumOutputForm = entityTradingSumOutputForm;

        return BaseOutputForm.success(entityTradingOutputForm);
    }


    public BaseOutput easyExcelTradingStatement(EntityTradingQueryForm form, HttpServletResponse response) throws IOException {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));


        List<EntityTradingPayMethodDetailOutputForm> payMethodList = entityTradingMapper.getTradingStatementSumPayMethod(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate);
        List<String> payMethod = payMethodList.stream().map(i -> i.Type).collect(Collectors.toList());

        List<EntityTradingDetailOutputForm> list = entityTradingMapper.getTradingStatement(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate);
        EntityTradingSumOutputForm entityTradingSumOutputForm = entityTradingMapper.getTradingStatementSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate);
        EntityTradingDetailOutputForm totalForm = new EntityTradingDetailOutputForm();
        totalForm.EntityName = "合计";
        totalForm.SalePayAmount = entityTradingSumOutputForm.SalePayAmount;
        totalForm.SaleSavingCardDeductionAmount = entityTradingSumOutputForm.SaleSavingCardDeductionAmount;
        totalForm.SaleLargessSavingCardDeductionAmount = entityTradingSumOutputForm.SaleLargessSavingCardDeductionAmount;
        totalForm.SaleLargessAmount = entityTradingSumOutputForm.SaleLargessAmount;
        totalForm.ArrearPayAmount = entityTradingSumOutputForm.ArrearPayAmount;
        totalForm.ArrearSavingCardDeductionAmount = entityTradingSumOutputForm.ArrearSavingCardDeductionAmount;
        totalForm.ArrearLargessSavingCardDeductionAmount = entityTradingSumOutputForm.ArrearLargessSavingCardDeductionAmount;
        totalForm.RefundPayAmount = entityTradingSumOutputForm.RefundPayAmount;
        totalForm.RefundSavingCardDeductionAmount = entityTradingSumOutputForm.RefundSavingCardDeductionAmount;
        totalForm.RefundLargessSavingCardDeductionAmount = entityTradingSumOutputForm.RefundLargessSavingCardDeductionAmount;
        totalForm.RefundLargessAmount = entityTradingSumOutputForm.RefundLargessAmount;
        totalForm.TreatPayAmount = entityTradingSumOutputForm.TreatPayAmount;
        totalForm.TreatCardDeductionAmount = entityTradingSumOutputForm.TreatCardDeductionAmount;
        totalForm.TreatLargesCardDeductionAmount = entityTradingSumOutputForm.TreatLargesCardDeductionAmount;
        totalForm.TreatLargessAmount = entityTradingSumOutputForm.TreatLargessAmount;
        totalForm.RefundTreatPayAmount = entityTradingSumOutputForm.RefundTreatPayAmount;
        totalForm.RefundTreatCardDeductionAmount = entityTradingSumOutputForm.RefundTreatCardDeductionAmount;
        totalForm.RefundTreatLargesCardDeductionAmount = entityTradingSumOutputForm.RefundTreatLargesCardDeductionAmount;
        totalForm.RefundTreatLargessAmount = entityTradingSumOutputForm.RefundTreatLargessAmount;
        totalForm.PayMethod = payMethodList;
        list.add(totalForm);


        List<List<Object>> total = new ArrayList<>();
        for (EntityTradingDetailOutputForm entityTradingDetailOutputForm : list) {
            List<Object> newList = new ArrayList<>();
            newList.add(entityTradingDetailOutputForm.EntityName);
            for (String s : payMethod) {
                BigDecimal amount = BigDecimal.ZERO;
                for (EntityTradingPayMethodDetailOutputForm entityTradingPayMethodDetailOutputForm : entityTradingDetailOutputForm.PayMethod) {
                    if (s.equals(entityTradingPayMethodDetailOutputForm.Type)) {
                        amount = entityTradingPayMethodDetailOutputForm.Amount;
                        continue;
                    }
                }
                newList.add(amount);
            }
            newList.add(entityTradingDetailOutputForm.SalePayAmount);
            newList.add(entityTradingDetailOutputForm.SaleSavingCardDeductionAmount);
            newList.add(entityTradingDetailOutputForm.SaleLargessSavingCardDeductionAmount);
            newList.add(entityTradingDetailOutputForm.SaleLargessAmount);
            newList.add(entityTradingDetailOutputForm.ArrearPayAmount);
            newList.add(entityTradingDetailOutputForm.ArrearSavingCardDeductionAmount);
            newList.add(entityTradingDetailOutputForm.ArrearLargessSavingCardDeductionAmount);
            newList.add(entityTradingDetailOutputForm.RefundPayAmount);
            newList.add(entityTradingDetailOutputForm.RefundSavingCardDeductionAmount);
            newList.add(entityTradingDetailOutputForm.RefundLargessSavingCardDeductionAmount);
            newList.add(entityTradingDetailOutputForm.RefundLargessAmount);
            newList.add(entityTradingDetailOutputForm.TreatPayAmount);
            newList.add(entityTradingDetailOutputForm.TreatCardDeductionAmount);
            newList.add(entityTradingDetailOutputForm.TreatLargesCardDeductionAmount);
            newList.add(entityTradingDetailOutputForm.TreatLargessAmount);
            newList.add(entityTradingDetailOutputForm.RefundTreatPayAmount);
            newList.add(entityTradingDetailOutputForm.RefundTreatCardDeductionAmount);
            newList.add(entityTradingDetailOutputForm.RefundTreatLargesCardDeductionAmount);
            newList.add(entityTradingDetailOutputForm.RefundTreatLargessAmount);
            total.add(newList);
        }
        //response输出文件流
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileName = URLEncoder.encode("门店交易表", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream()).head(head(payMethod)).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("门店交易").doWrite(total);


        return null;

    }

    private List<List<String>> head(List<String> productHead) {
        List<List<String>> list = new ArrayList<List<String>>();
        List<String> head0 = new ArrayList<>();
        head0.add("门店");
        list.add(head0);
        List<String> head2 = new ArrayList<>();
        for (String s : productHead) {
            head2 = new ArrayList<>();
            head2.add("实际收款金额");
            head2.add(s);
            list.add(head2);
        }


        String[] sale = {"实收金额", "卡抵扣金额", "赠送卡抵扣金额", "赠送金额"};
        List<String> head3 = new ArrayList<>();
        for (String s : sale) {
            head3 = new ArrayList<>();
            head3.add("销售金额");
            head3.add(s);
            list.add(head3);
        }

        String[] sale4 = {"实收金额", "卡抵扣金额", "赠送卡抵扣金额"};
        List<String> head4 = new ArrayList<>();
        for (String s : sale4) {
            head4 = new ArrayList<>();
            head4.add("补欠款信息");
            head4.add(s);
            list.add(head4);
        }

        String[] refundSale = {"退款金额", "退回卡金额", "退回赠送卡金额", "退回赠送金额"};
        List<String> head5 = new ArrayList<>();
        for (String s : refundSale) {
            head5 = new ArrayList<>();
            head5.add("退款金额");
            head5.add(s);
            list.add(head5);
        }
        String[] treat = {"现金金额", "卡抵扣金额", "赠送卡抵扣", "赠送金额"};
        List<String> head6 = new ArrayList<>();
        for (String s : treat) {
            head6 = new ArrayList<>();
            head6.add("消耗金额");
            head6.add(s);
            list.add(head6);
        }
        String[] refundTreat = {"退回金额", "退卡扣金额", "退赠卡扣金额", "赠送金额"};
        List<String> head7 = new ArrayList<>();
        for (String s : refundTreat) {
            head7 = new ArrayList<>();
            head7.add("退消耗金额");
            head7.add(s);
            list.add(head7);
        }

        return list;
    }
}
