package com.zhelian.service.report.channelStatement;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.report.ChannelSaleDetailMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.report.form.input.ChannelSaleArrearDetailForm;
import com.zhelian.model.report.form.input.ChannelSaleDetailForm;
import com.zhelian.model.report.form.input.ChannelSaleRefundDetailForm;
import com.zhelian.model.report.form.output.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class ChannelSaleDetailService {

    @Autowired
    ChannelSaleDetailMapper channelSaleDetailMapper;

    public BaseOutput getChannelSaleDetail(ChannelSaleDetailForm form) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        ChannelSaleDetailAndSumStatementForm channelSaleDetailAndSumStatementForm = new ChannelSaleDetailAndSumStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        channelSaleDetailAndSumStatementForm.channelSaleDetailStatementForms = BasePageInfo.success(channelSaleDetailMapper.getChannelSaleDetail(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.IsLargess, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName));
        channelSaleDetailAndSumStatementForm.channelSaleDetailSumStatementForm = channelSaleDetailMapper.getChannelSaleDetailSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.IsLargess, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName);
        return BaseOutputForm.success(channelSaleDetailAndSumStatementForm);
    }

    public BaseOutput easyExcelChannelSaleDetailStatement(ChannelSaleDetailForm form, HttpServletResponse response) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<ChannelSaleDetailStatementEasyExcelForm> list = channelSaleDetailMapper.getEasyExcelChannelSaleDetail(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.IsLargess, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName);
        ChannelSaleDetailSumStatementForm channelSaleDetailAndSumStatementForm = channelSaleDetailMapper.getChannelSaleDetailSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.IsLargess, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName);
        if (channelSaleDetailAndSumStatementForm != null) {
            ChannelSaleDetailStatementEasyExcelForm channelSaleDetailStatementForm = new ChannelSaleDetailStatementEasyExcelForm();
            channelSaleDetailStatementForm.billID = "合计";
            channelSaleDetailStatementForm.billDate = "";
            channelSaleDetailStatementForm.entityName = "";
            channelSaleDetailStatementForm.customerName = "";
            channelSaleDetailStatementForm.customerPhoneNumber = "";
            channelSaleDetailStatementForm.createdOn = "";
            channelSaleDetailStatementForm.customerEntityName = "";
            channelSaleDetailStatementForm.employeeName = "";
            channelSaleDetailStatementForm.goodsTypeName = "";
            channelSaleDetailStatementForm.categoryName = "";
            channelSaleDetailStatementForm.goodName = "";
            channelSaleDetailStatementForm.price = null;
            channelSaleDetailStatementForm.quantity = null;
            channelSaleDetailStatementForm.largess = "";
            channelSaleDetailStatementForm.preferentialAmount = channelSaleDetailAndSumStatementForm.PreferentialAmount.doubleValue();
            channelSaleDetailStatementForm.totalAmount = channelSaleDetailAndSumStatementForm.TotalAmount.doubleValue();
            channelSaleDetailStatementForm.arrearAmount = channelSaleDetailAndSumStatementForm.ArrearAmount.doubleValue();
            channelSaleDetailStatementForm.largessAmount = channelSaleDetailAndSumStatementForm.LargessAmount.doubleValue();
            channelSaleDetailStatementForm.payAmount = channelSaleDetailAndSumStatementForm.PayAmount.doubleValue();
            channelSaleDetailStatementForm.savingCardDeductionAmount = channelSaleDetailAndSumStatementForm.SavingCardDeductionAmount.doubleValue();
            channelSaleDetailStatementForm.largessSavingCardDeductionAmount = channelSaleDetailAndSumStatementForm.LargessSavingCardDeductionAmount.doubleValue();
            list.add(channelSaleDetailStatementForm);
        }

        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("销售明细", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), ChannelSaleDetailStatementEasyExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("销售明细").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    public BaseOutput getChannelSaleArrearDetail(ChannelSaleArrearDetailForm form) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        ChannelSaleArrearDetailAndSumStatementForm channelSaleArrearDetailAndSumStatementForm = new ChannelSaleArrearDetailAndSumStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        channelSaleArrearDetailAndSumStatementForm.channelSaleArrearDetailStatementForm = BasePageInfo.success(channelSaleDetailMapper.getChannelSaleArrearDetail(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName));
        channelSaleArrearDetailAndSumStatementForm.channelSaleArrearDetailSumStatementForm = channelSaleDetailMapper.getChannelSaleArrearDetailSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName);
        return BaseOutputForm.success(channelSaleArrearDetailAndSumStatementForm);
    }

    public BaseOutput easyExcelChannelSaleArrearDetail(ChannelSaleDetailForm form, HttpServletResponse response) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<ChannelSaleArrearDetailStatementEasyExcelForm> list = channelSaleDetailMapper.getEasyExcelChannelSaleArrearDetail(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName);
        ChannelSaleArrearDetailSumStatementForm channelSaleArrearDetailSum = channelSaleDetailMapper.getChannelSaleArrearDetailSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName);
        if (channelSaleArrearDetailSum != null) {
            ChannelSaleArrearDetailStatementEasyExcelForm channelSaleArrearDetailStatementForm = new ChannelSaleArrearDetailStatementEasyExcelForm();
            channelSaleArrearDetailStatementForm.billID = "合计";
            channelSaleArrearDetailStatementForm.billDate = "";
            channelSaleArrearDetailStatementForm.entityName = "";
            channelSaleArrearDetailStatementForm.customerName = "";
            channelSaleArrearDetailStatementForm.customerPhoneNumber = "";
            channelSaleArrearDetailStatementForm.createdOn = "";
            channelSaleArrearDetailStatementForm.customerEntityName = "";
            channelSaleArrearDetailStatementForm.employeeName = "";
            channelSaleArrearDetailStatementForm.goodsTypeName = "";
            channelSaleArrearDetailStatementForm.categoryName = "";
            channelSaleArrearDetailStatementForm.goodName = "";
            channelSaleArrearDetailStatementForm.buyAmount = null;
            channelSaleArrearDetailStatementForm.payAmount = channelSaleArrearDetailSum.PayAmount;
            channelSaleArrearDetailStatementForm.savingCardDeductionAmount = channelSaleArrearDetailSum.SavingCardDeductionAmount;
            channelSaleArrearDetailStatementForm.largessSavingCardDeductionAmount = channelSaleArrearDetailSum.LargessSavingCardDeductionAmount;
            list.add(channelSaleArrearDetailStatementForm);
        }

        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("补欠款明细", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), ChannelSaleArrearDetailStatementEasyExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("补欠款明细").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    public BaseOutput getChannelSaleRefundDetail(ChannelSaleRefundDetailForm form) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        ChannelSaleRefundDetailAndSumStatementForm channelSaleRefundDetailAndSumStatementForm = new ChannelSaleRefundDetailAndSumStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        channelSaleRefundDetailAndSumStatementForm.channelSaleRefundDetailStatementForms = BasePageInfo.success(channelSaleDetailMapper.getChannelSaleRefundDetail(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName));
        channelSaleRefundDetailAndSumStatementForm.channelSaleRefundDetailSumStatementForm = channelSaleDetailMapper.getChannelSaleRefundDetailSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName);
        return BaseOutputForm.success(channelSaleRefundDetailAndSumStatementForm);
    }

    public BaseOutput easyExcelChannelSaleRefundDetailStatement(ChannelSaleDetailForm form, HttpServletResponse response) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<ChannelSaleRefundDetailStatementEasyExcelForm> list = channelSaleDetailMapper.getEasyExcelChannelSaleRefundDetail(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName);
        ChannelSaleRefundDetailSumStatementForm channelSaleRefundDetailSumStatementForm = channelSaleDetailMapper.getChannelSaleRefundDetailSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName);
        if (channelSaleRefundDetailSumStatementForm != null) {
            ChannelSaleRefundDetailStatementEasyExcelForm channelSaleRefundDetailStatementForm = new ChannelSaleRefundDetailStatementEasyExcelForm();
            channelSaleRefundDetailStatementForm.billID = "合计";
            channelSaleRefundDetailStatementForm.billDate = "";
            channelSaleRefundDetailStatementForm.entityName = "";
            channelSaleRefundDetailStatementForm.customerName = "";
            channelSaleRefundDetailStatementForm.customerPhoneNumber = "";
            channelSaleRefundDetailStatementForm.createdOn = "";
            channelSaleRefundDetailStatementForm.customerEntityName = "";
            channelSaleRefundDetailStatementForm.employeeName = "";
            channelSaleRefundDetailStatementForm.goodsTypeName = "";
            channelSaleRefundDetailStatementForm.categoryName = "";
            channelSaleRefundDetailStatementForm.goodName = "";
            channelSaleRefundDetailStatementForm.quantity = null;
            channelSaleRefundDetailStatementForm.isLargess = "";
            channelSaleRefundDetailStatementForm.originAmount = channelSaleRefundDetailSumStatementForm.OriginAmount;
            channelSaleRefundDetailStatementForm.totalAmount = channelSaleRefundDetailSumStatementForm.TotalAmount;
            channelSaleRefundDetailStatementForm.largessAmount = channelSaleRefundDetailSumStatementForm.LargessAmount;
            channelSaleRefundDetailStatementForm.payAmount = channelSaleRefundDetailSumStatementForm.PayAmount;
            channelSaleRefundDetailStatementForm.savingCardDeductionAmount = channelSaleRefundDetailSumStatementForm.SavingCardDeductionAmount;
            channelSaleRefundDetailStatementForm.largessSavingCardDeductionAmount = channelSaleRefundDetailSumStatementForm.LargessSavingCardDeductionAmount;
            list.add(channelSaleRefundDetailStatementForm);
        }

        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("退款明细", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), ChannelSaleRefundDetailStatementEasyExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("退款明细").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }
}
