package com.zhelian.service.report.entityStatement;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.report.EntitySaleDetailMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.report.form.input.EntitySaleArrearDetailForm;
import com.zhelian.model.report.form.input.EntitySaleDetailForm;
import com.zhelian.model.report.form.input.EntitySaleRefundDetailForm;
import com.zhelian.model.report.form.output.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class EntitySaleDetailService {

    @Autowired
    EntitySaleDetailMapper entitySaleDetailMapper;

    public BaseOutput getEntitySaleDetail(EntitySaleDetailForm form) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        EntitySaleDetailAndSumStatementForm entitySaleDetailAndSumStatementForm = new EntitySaleDetailAndSumStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        entitySaleDetailAndSumStatementForm.entitySaleDetailStatementForms = BasePageInfo.success(entitySaleDetailMapper.getEntitySaleDetail(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.IsLargess, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName));
        entitySaleDetailAndSumStatementForm.entitySaleDetailSumStatementForm = entitySaleDetailMapper.getEntitySaleDetailSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.IsLargess, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName);
        return BaseOutputForm.success(entitySaleDetailAndSumStatementForm);
    }

    public BaseOutput easyExcelEntitySaleDetailStatement(EntitySaleDetailForm form, HttpServletResponse response) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<EntitySaleDetailStatementEasyExcelForm> list = entitySaleDetailMapper.getEasyExcelEntitySaleDetail(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.IsLargess, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName);
        EntitySaleDetailSumStatementForm entitySaleDetailAndSumStatementForm = entitySaleDetailMapper.getEntitySaleDetailSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.IsLargess, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName);
        if (entitySaleDetailAndSumStatementForm != null) {
            EntitySaleDetailStatementEasyExcelForm entitySaleDetailStatementForm = new EntitySaleDetailStatementEasyExcelForm();
            entitySaleDetailStatementForm.billID = "合计";
            entitySaleDetailStatementForm.billDate = "";
            entitySaleDetailStatementForm.entityName = "";
            entitySaleDetailStatementForm.customerName = "";
            entitySaleDetailStatementForm.customerPhoneNumber = "";
            entitySaleDetailStatementForm.createdOn = "";
            entitySaleDetailStatementForm.customerEntityName = "";
            entitySaleDetailStatementForm.employeeName = "";
            entitySaleDetailStatementForm.goodsTypeName = "";
            entitySaleDetailStatementForm.categoryName = "";
            entitySaleDetailStatementForm.goodName = "";
            entitySaleDetailStatementForm.price = null;
            entitySaleDetailStatementForm.quantity = null;
            entitySaleDetailStatementForm.largess = "";
            entitySaleDetailStatementForm.preferentialAmount = entitySaleDetailAndSumStatementForm.PreferentialAmount.doubleValue();
            entitySaleDetailStatementForm.totalAmount = entitySaleDetailAndSumStatementForm.TotalAmount.doubleValue();
            entitySaleDetailStatementForm.arrearAmount = entitySaleDetailAndSumStatementForm.ArrearAmount.doubleValue();
            entitySaleDetailStatementForm.largessAmount = entitySaleDetailAndSumStatementForm.LargessAmount.doubleValue();
            entitySaleDetailStatementForm.payAmount = entitySaleDetailAndSumStatementForm.PayAmount.doubleValue();
            entitySaleDetailStatementForm.savingCardDeductionAmount = entitySaleDetailAndSumStatementForm.SavingCardDeductionAmount.doubleValue();
            entitySaleDetailStatementForm.largessSavingCardDeductionAmount = entitySaleDetailAndSumStatementForm.LargessSavingCardDeductionAmount.doubleValue();
            list.add(entitySaleDetailStatementForm);
        }

        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("销售明细", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), EntitySaleDetailStatementEasyExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("销售明细").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    public BaseOutput easyExcelDisPlayPhoneEntitySaleDetailStatement(EntitySaleDetailForm form, HttpServletResponse response) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<EntitySaleDetailStatementEasyExcelForm> list = entitySaleDetailMapper.getEasyExcelDisPlayPhoneEntitySaleDetail(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.IsLargess, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName);
        EntitySaleDetailSumStatementForm entitySaleDetailAndSumStatementForm = entitySaleDetailMapper.getEntitySaleDetailSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.IsLargess, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName);
        if (entitySaleDetailAndSumStatementForm != null) {
            EntitySaleDetailStatementEasyExcelForm entitySaleDetailStatementForm = new EntitySaleDetailStatementEasyExcelForm();
            entitySaleDetailStatementForm.billID = "合计";
            entitySaleDetailStatementForm.billDate = "";
            entitySaleDetailStatementForm.entityName = "";
            entitySaleDetailStatementForm.customerName = "";
            entitySaleDetailStatementForm.customerPhoneNumber = "";
            entitySaleDetailStatementForm.createdOn = "";
            entitySaleDetailStatementForm.customerEntityName = "";
            entitySaleDetailStatementForm.employeeName = "";
            entitySaleDetailStatementForm.goodsTypeName = "";
            entitySaleDetailStatementForm.categoryName = "";
            entitySaleDetailStatementForm.goodName = "";
            entitySaleDetailStatementForm.price = null;
            entitySaleDetailStatementForm.quantity = null;
            entitySaleDetailStatementForm.largess = "";
            entitySaleDetailStatementForm.preferentialAmount = entitySaleDetailAndSumStatementForm.PreferentialAmount.doubleValue();
            entitySaleDetailStatementForm.totalAmount = entitySaleDetailAndSumStatementForm.TotalAmount.doubleValue();
            entitySaleDetailStatementForm.arrearAmount = entitySaleDetailAndSumStatementForm.ArrearAmount.doubleValue();
            entitySaleDetailStatementForm.largessAmount = entitySaleDetailAndSumStatementForm.LargessAmount.doubleValue();
            entitySaleDetailStatementForm.payAmount = entitySaleDetailAndSumStatementForm.PayAmount.doubleValue();
            entitySaleDetailStatementForm.savingCardDeductionAmount = entitySaleDetailAndSumStatementForm.SavingCardDeductionAmount.doubleValue();
            entitySaleDetailStatementForm.largessSavingCardDeductionAmount = entitySaleDetailAndSumStatementForm.LargessSavingCardDeductionAmount.doubleValue();
            list.add(entitySaleDetailStatementForm);
        }

        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("销售明细", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), EntitySaleDetailStatementEasyExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("销售明细").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    public BaseOutput getEntitySaleArrearDetail(EntitySaleArrearDetailForm form) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        EntitySaleArrearDetailAndSumStatementForm entitySaleArrearDetailAndSumStatementForm = new EntitySaleArrearDetailAndSumStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        entitySaleArrearDetailAndSumStatementForm.entitySaleArrearDetailStatementForm = BasePageInfo.success(entitySaleDetailMapper.getEntitySaleArrearDetail(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName));
        entitySaleArrearDetailAndSumStatementForm.entitySaleArrearDetailSumStatementForm = entitySaleDetailMapper.getEntitySaleArrearDetailSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName);
        return BaseOutputForm.success(entitySaleArrearDetailAndSumStatementForm);
    }

    public BaseOutput easyExcelEntitySaleArrearDetail(EntitySaleDetailForm form, HttpServletResponse response) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<EntitySaleArrearDetailStatementEasyExcelForm> list = entitySaleDetailMapper.getEasyExcelEntitySaleArrearDetail(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName);
        EntitySaleArrearDetailSumStatementForm entitySaleArrearDetailSum = entitySaleDetailMapper.getEntitySaleArrearDetailSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName);
        if (entitySaleArrearDetailSum != null) {
            EntitySaleArrearDetailStatementEasyExcelForm entitySaleArrearDetailStatementForm = new EntitySaleArrearDetailStatementEasyExcelForm();
            entitySaleArrearDetailStatementForm.billID = "合计";
            entitySaleArrearDetailStatementForm.billDate = "";
            entitySaleArrearDetailStatementForm.entityName = "";
            entitySaleArrearDetailStatementForm.customerName = "";
            entitySaleArrearDetailStatementForm.customerPhoneNumber = "";
            entitySaleArrearDetailStatementForm.createdOn = "";
            entitySaleArrearDetailStatementForm.customerEntityName = "";
            entitySaleArrearDetailStatementForm.employeeName = "";
            entitySaleArrearDetailStatementForm.goodsTypeName = "";
            entitySaleArrearDetailStatementForm.categoryName = "";
            entitySaleArrearDetailStatementForm.goodName = "";
            entitySaleArrearDetailStatementForm.buyAmount = null;
            entitySaleArrearDetailStatementForm.payAmount = entitySaleArrearDetailSum.PayAmount;
            entitySaleArrearDetailStatementForm.savingCardDeductionAmount = entitySaleArrearDetailSum.SavingCardDeductionAmount;
            entitySaleArrearDetailStatementForm.largessSavingCardDeductionAmount = entitySaleArrearDetailSum.LargessSavingCardDeductionAmount;
            list.add(entitySaleArrearDetailStatementForm);
        }

        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("补欠款明细", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), EntitySaleArrearDetailStatementEasyExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("补欠款明细").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    public BaseOutput easyExcelDisPlayPhoneEntitySaleArrearDetailStatement(EntitySaleDetailForm form, HttpServletResponse response) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<EntitySaleArrearDetailStatementEasyExcelForm> list = entitySaleDetailMapper.getEasyExcelDisPlayPhoneEntitySaleArrearDetail(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName);
        EntitySaleArrearDetailSumStatementForm entitySaleArrearDetailSum = entitySaleDetailMapper.getEntitySaleArrearDetailSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName);
        if (entitySaleArrearDetailSum != null) {
            EntitySaleArrearDetailStatementEasyExcelForm entitySaleArrearDetailStatementForm = new EntitySaleArrearDetailStatementEasyExcelForm();
            entitySaleArrearDetailStatementForm.billID = "合计";
            entitySaleArrearDetailStatementForm.billDate = "";
            entitySaleArrearDetailStatementForm.entityName = "";
            entitySaleArrearDetailStatementForm.customerName = "";
            entitySaleArrearDetailStatementForm.customerPhoneNumber = "";
            entitySaleArrearDetailStatementForm.createdOn = "";
            entitySaleArrearDetailStatementForm.customerEntityName = "";
            entitySaleArrearDetailStatementForm.employeeName = "";
            entitySaleArrearDetailStatementForm.goodsTypeName = "";
            entitySaleArrearDetailStatementForm.categoryName = "";
            entitySaleArrearDetailStatementForm.goodName = "";
            entitySaleArrearDetailStatementForm.buyAmount = null;
            entitySaleArrearDetailStatementForm.payAmount = entitySaleArrearDetailSum.PayAmount;
            entitySaleArrearDetailStatementForm.savingCardDeductionAmount = entitySaleArrearDetailSum.SavingCardDeductionAmount;
            entitySaleArrearDetailStatementForm.largessSavingCardDeductionAmount = entitySaleArrearDetailSum.LargessSavingCardDeductionAmount;
            list.add(entitySaleArrearDetailStatementForm);
        }

        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("补欠款明细", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), EntitySaleArrearDetailStatementEasyExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("补欠款明细").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    public BaseOutput getEntitySaleRefundDetail(EntitySaleRefundDetailForm form) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        EntitySaleRefundDetailAndSumStatementForm entitySaleRefundDetailAndSumStatementForm = new EntitySaleRefundDetailAndSumStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        entitySaleRefundDetailAndSumStatementForm.entitySaleRefundDetailStatementForms = BasePageInfo.success(entitySaleDetailMapper.getEntitySaleRefundDetail(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName));
        entitySaleRefundDetailAndSumStatementForm.entitySaleRefundDetailSumStatementForm = entitySaleDetailMapper.getEntitySaleRefundDetailSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName);
        return BaseOutputForm.success(entitySaleRefundDetailAndSumStatementForm);
    }

    public BaseOutput easyExcelEntitySaleRefundDetailStatement(EntitySaleDetailForm form, HttpServletResponse response) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<EntitySaleRefundDetailStatementEasyExcelForm> list = entitySaleDetailMapper.getEasyExcelEntitySaleRefundDetail(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName);
        EntitySaleRefundDetailSumStatementForm entitySaleRefundDetailSumStatementForm = entitySaleDetailMapper.getEntitySaleRefundDetailSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName);
        if (entitySaleRefundDetailSumStatementForm != null) {
            EntitySaleRefundDetailStatementEasyExcelForm entitySaleRefundDetailStatementForm = new EntitySaleRefundDetailStatementEasyExcelForm();
            entitySaleRefundDetailStatementForm.billID = "合计";
            entitySaleRefundDetailStatementForm.billDate = "";
            entitySaleRefundDetailStatementForm.entityName = "";
            entitySaleRefundDetailStatementForm.customerName = "";
            entitySaleRefundDetailStatementForm.customerPhoneNumber = "";
            entitySaleRefundDetailStatementForm.createdOn = "";
            entitySaleRefundDetailStatementForm.customerEntityName = "";
            entitySaleRefundDetailStatementForm.employeeName = "";
            entitySaleRefundDetailStatementForm.goodsTypeName = "";
            entitySaleRefundDetailStatementForm.categoryName = "";
            entitySaleRefundDetailStatementForm.goodName = "";
            entitySaleRefundDetailStatementForm.quantity = null;
            entitySaleRefundDetailStatementForm.isLargess = "";
            entitySaleRefundDetailStatementForm.originAmount = entitySaleRefundDetailSumStatementForm.OriginAmount;
            entitySaleRefundDetailStatementForm.totalAmount = entitySaleRefundDetailSumStatementForm.TotalAmount;
            entitySaleRefundDetailStatementForm.largessAmount = entitySaleRefundDetailSumStatementForm.LargessAmount;
            entitySaleRefundDetailStatementForm.payAmount = entitySaleRefundDetailSumStatementForm.PayAmount;
            entitySaleRefundDetailStatementForm.savingCardDeductionAmount = entitySaleRefundDetailSumStatementForm.SavingCardDeductionAmount;
            entitySaleRefundDetailStatementForm.largessSavingCardDeductionAmount = entitySaleRefundDetailSumStatementForm.LargessSavingCardDeductionAmount;
            list.add(entitySaleRefundDetailStatementForm);
        }

        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("退款明细", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), EntitySaleRefundDetailStatementEasyExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("退款明细").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    public BaseOutput easyExcelDisPlayPhoneEntitySaleRefundDetailStatement(EntitySaleDetailForm form, HttpServletResponse response) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<EntitySaleRefundDetailStatementEasyExcelForm> list = entitySaleDetailMapper.getEasyExcelDisPlayPhoneEntitySaleRefundDetail(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName);
        EntitySaleRefundDetailSumStatementForm entitySaleRefundDetailSumStatementForm = entitySaleDetailMapper.getEntitySaleRefundDetailSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.GoodsTypeName, form.GoodName, form.CustomerName, form.BelongEntityID, form.CreateStartDate, form.CreateEndDate, form.CustomerLevelID, form.CustomerSourceID, form.CategoryID,form.Channel,form.IntroducerName,form.ChannelName);
        if (entitySaleRefundDetailSumStatementForm != null) {
            EntitySaleRefundDetailStatementEasyExcelForm entitySaleRefundDetailStatementForm = new EntitySaleRefundDetailStatementEasyExcelForm();
            entitySaleRefundDetailStatementForm.billID = "合计";
            entitySaleRefundDetailStatementForm.billDate = "";
            entitySaleRefundDetailStatementForm.entityName = "";
            entitySaleRefundDetailStatementForm.customerName = "";
            entitySaleRefundDetailStatementForm.customerPhoneNumber = "";
            entitySaleRefundDetailStatementForm.createdOn = "";
            entitySaleRefundDetailStatementForm.customerEntityName = "";
            entitySaleRefundDetailStatementForm.employeeName = "";
            entitySaleRefundDetailStatementForm.goodsTypeName = "";
            entitySaleRefundDetailStatementForm.categoryName = "";
            entitySaleRefundDetailStatementForm.goodName = "";
            entitySaleRefundDetailStatementForm.quantity = null;
            entitySaleRefundDetailStatementForm.isLargess = "";
            entitySaleRefundDetailStatementForm.originAmount = entitySaleRefundDetailSumStatementForm.OriginAmount;
            entitySaleRefundDetailStatementForm.totalAmount = entitySaleRefundDetailSumStatementForm.TotalAmount;
            entitySaleRefundDetailStatementForm.largessAmount = entitySaleRefundDetailSumStatementForm.LargessAmount;
            entitySaleRefundDetailStatementForm.payAmount = entitySaleRefundDetailSumStatementForm.PayAmount;
            entitySaleRefundDetailStatementForm.savingCardDeductionAmount = entitySaleRefundDetailSumStatementForm.SavingCardDeductionAmount;
            entitySaleRefundDetailStatementForm.largessSavingCardDeductionAmount = entitySaleRefundDetailSumStatementForm.LargessSavingCardDeductionAmount;
            list.add(entitySaleRefundDetailStatementForm);
        }

        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("退款明细", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), EntitySaleRefundDetailStatementEasyExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("退款明细").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }
}
