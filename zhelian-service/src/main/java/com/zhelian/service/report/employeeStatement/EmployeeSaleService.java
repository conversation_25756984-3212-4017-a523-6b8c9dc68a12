package com.zhelian.service.report.employeeStatement;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.report.EmployeeSaleMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.report.form.input.EmployeeSalePerformanceCommissionDetailForm;
import com.zhelian.model.report.form.output.EmployeeSaleDetailAndSumStatementForm;
import com.zhelian.model.report.form.output.EmployeeSaleDetailStatisticsStatementExcelForm;
import com.zhelian.model.report.form.output.EmployeeSaleSumStatementForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class EmployeeSaleService {
    @Autowired
    EmployeeSaleMapper employeeSaleMapper;

    public BaseOutput getEmployeeSaleStatistics(EmployeeSalePerformanceCommissionDetailForm form) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        EmployeeSaleDetailAndSumStatementForm employeeSaleDetailAndSumStatementForm = new EmployeeSaleDetailAndSumStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        employeeSaleDetailAndSumStatementForm.employeeSaleStatementForms = BasePageInfo.success(employeeSaleMapper.getEmployeeSaleStatistics(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.EmployeeName, form.JobID));
        employeeSaleDetailAndSumStatementForm.employeeSaleSumStatementForm = employeeSaleMapper.getEmployeeSaleSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.EmployeeName, form.JobID);
        return BaseOutputForm.success(employeeSaleDetailAndSumStatementForm);
    }

    public BaseOutput statisticsExcel(EmployeeSalePerformanceCommissionDetailForm form, HttpServletResponse response) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<EmployeeSaleDetailStatisticsStatementExcelForm> list = employeeSaleMapper.getExcelEmployeeSaleStatistics(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.EmployeeName, form.JobID);
        EmployeeSaleSumStatementForm statisticsSum = employeeSaleMapper.getEmployeeSaleSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.EmployeeName, form.JobID);
        if (statisticsSum != null) {
            EmployeeSaleDetailStatisticsStatementExcelForm statementForm = new EmployeeSaleDetailStatisticsStatementExcelForm();
            statementForm.employeeID = "合计";
            statementForm.employeeName = "";
            statementForm.jobName = "";
            statementForm.entityName = "";
            statementForm.saleQuantity = statisticsSum.SaleQuantity;
            statementForm.salePayAmount = statisticsSum.SalePayAmount;
            statementForm.saleSavingCardDeductionAmount = statisticsSum.SaleSavingCardDeductionAmount;
            statementForm.saleLargessSavingCardDeductionAmount = statisticsSum.SaleLargessSavingCardDeductionAmount;
            statementForm.saleLargessAmount = statisticsSum.SaleLargessAmount;
            statementForm.arrearAmount = statisticsSum.ArrearAmount;
            statementForm.arrearSavingCardDeductionAmount = statisticsSum.ArrearSavingCardDeductionAmount;
            statementForm.arrearLargessSavingCardDeductionAmount = statisticsSum.ArrearLargessSavingCardDeductionAmount;
            statementForm.refundSalePayAmount = statisticsSum.RefundSalePayAmount;
            statementForm.refundSaleSavingCardDeductionAmount = statisticsSum.RefundSaleSavingCardDeductionAmount;
            statementForm.refundSaleLargessSavingCardDeductionAmount = statisticsSum.RefundSaleLargessSavingCardDeductionAmount;
            statementForm.refundSaleLargessAmount = statisticsSum.RefundSaleLargessAmount;

            list.add(statementForm);
        }

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("员工销售统计", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), EmployeeSaleDetailStatisticsStatementExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("员工销售统计").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return null;
    }
}
