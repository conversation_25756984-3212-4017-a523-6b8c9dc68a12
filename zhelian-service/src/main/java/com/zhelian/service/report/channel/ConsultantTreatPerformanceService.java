package com.zhelian.service.report.channel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.report.ConsultantTreatPerformanceDetailMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.report.form.input.ConsultantTreatPerformanceDetailForm;
import com.zhelian.model.report.form.output.ConsultantTreatPerformanceDetailAndSumStatementForm;
import com.zhelian.model.report.form.output.ConsultantTreatPerformanceDetailStatementEasyExcelForm;
import com.zhelian.model.report.form.output.ConsultantTreatPerformanceSumStatementForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.util.List;

@Service
public class ConsultantTreatPerformanceService {

    @Autowired
    ConsultantTreatPerformanceDetailMapper consultantTreatPerformanceDetailMapper;

    public BaseOutput getConsultantTreatPerformanceDetail(ConsultantTreatPerformanceDetailForm form) {
        form.StartDate = LocalDate.parse(form.StartDate).toString();
        form.EndDate = LocalDate.parse(form.EndDate).plusDays(1).toString();

        ConsultantTreatPerformanceDetailAndSumStatementForm detailAndSumStatementForm = new ConsultantTreatPerformanceDetailAndSumStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        detailAndSumStatementForm.consultantTreatPerformanceDetailStatementFormBasePageInfo = BasePageInfo.success(consultantTreatPerformanceDetailMapper.getConsultantTreatPerformanceDetail(form));
        detailAndSumStatementForm.consultantTreatPerformanceSumStatementForm = consultantTreatPerformanceDetailMapper.getConsultantTreatPerformanceSum(form);
        return BaseOutputForm.success(detailAndSumStatementForm);
    }


    public BaseOutput easyExcelConsultantTreatPerformanceDetail(ConsultantTreatPerformanceDetailForm form, HttpServletResponse response) {
        form.StartDate = LocalDate.parse(form.StartDate).toString();
        form.EndDate = LocalDate.parse(form.EndDate).plusDays(1).toString();

        List<ConsultantTreatPerformanceDetailStatementEasyExcelForm> list = consultantTreatPerformanceDetailMapper.getEasyExcelConsultantTreatPerformanceDetail(form);
        ConsultantTreatPerformanceSumStatementForm consultantTreatPerformanceSumStatementForm = consultantTreatPerformanceDetailMapper.getConsultantTreatPerformanceSum(form);
        if (consultantTreatPerformanceSumStatementForm != null) {
            ConsultantTreatPerformanceDetailStatementEasyExcelForm statementForm = new ConsultantTreatPerformanceDetailStatementEasyExcelForm();
            statementForm.treatBillID = "合计";
            statementForm.billDate = "";
            statementForm.billType = "";
            statementForm.entityName = "";
            statementForm.customerName = "";
            statementForm.channelName = "";
            statementForm.channelType = "";
            statementForm.treatCardTypeName = "";
            statementForm.cardName = "";
            statementForm.categoryName = "";
            statementForm.goodName = "";
            statementForm.employeeName = "";
            statementForm.payPerformance = consultantTreatPerformanceSumStatementForm.PayPerformance.doubleValue();
            statementForm.cardPerformance = consultantTreatPerformanceSumStatementForm.CardPerformance.doubleValue();
            statementForm.cardLargessPerformance = consultantTreatPerformanceSumStatementForm.CardLargessPerformance.doubleValue();
            statementForm.largessPerformance = consultantTreatPerformanceSumStatementForm.LargessPerformance.doubleValue();
            list.add(statementForm);
        }

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("咨询消耗业绩提成明细", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), ConsultantTreatPerformanceDetailStatementEasyExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("咨询消耗业绩提成明细").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }
}
