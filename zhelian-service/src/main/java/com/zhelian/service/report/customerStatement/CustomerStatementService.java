package com.zhelian.service.report.customerStatement;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.report.CustomerStatementMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.report.form.input.AccountStatisticsStatementQueryForm;
import com.zhelian.model.report.form.input.ArrearsDetailStatementQueryForm;
import com.zhelian.model.report.form.input.DynamicSaleAndDynamicConsumeStatementQueryForm;
import com.zhelian.model.report.form.output.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

@Service
public class CustomerStatementService {

    @Autowired
    CustomerStatementMapper customerStatementMapper;

    public BaseOutput customerDynamicSaleAndDynamicConsume(DynamicSaleAndDynamicConsumeStatementQueryForm form) {
        CustomerDynamicSaleAndDynamicConsumeStatementForm consumeStatementForm = new CustomerDynamicSaleAndDynamicConsumeStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        consumeStatementForm.detail = BasePageInfo.success(customerStatementMapper.getCustomerDynamicSaleAndDynamicConsume(SystemUserUtil.getSystemUserID(), form.Name, form.LevelID, form.SourceID, form.StartCreatedOn, form.EndCreatedOn, form.EntityID, form.StartTime, form.EndTime));
        consumeStatementForm.customerDynamicSaleAndDynamicConsumeSumStatementForm = customerStatementMapper.getCustomerDynamicSaleAndDynamicConsumeSum(SystemUserUtil.getSystemUserID(), form.Name, form.LevelID, form.SourceID, form.StartCreatedOn, form.EndCreatedOn, form.EntityID, form.StartTime, form.EndTime);
        return BaseOutputForm.success(consumeStatementForm);
    }


    public BaseOutput dynamicSaleAndDynamicConsumeEasyExcel(DynamicSaleAndDynamicConsumeStatementQueryForm form, HttpServletResponse response) {

        List<CustomerDynamicSaleAndDynamicConsumeDetailStatementEasyExcelForm> list = customerStatementMapper.getCustomerDynamicSaleAndDynamicConsumeEasyExcel(SystemUserUtil.getSystemUserID(), form.Name, form.LevelID, form.SourceID, form.StartCreatedOn, form.EndCreatedOn, form.EntityID, form.StartTime, form.EndTime);
        CustomerDynamicSaleAndDynamicConsumeSumStatementForm customerDynamicSaleAndDynamicConsumeSumStatementForm = customerStatementMapper.getCustomerDynamicSaleAndDynamicConsumeSum(SystemUserUtil.getSystemUserID(), form.Name, form.LevelID, form.SourceID, form.StartCreatedOn, form.EndCreatedOn, form.EntityID, form.StartTime, form.EndTime);
        if (customerDynamicSaleAndDynamicConsumeSumStatementForm != null) {
            CustomerDynamicSaleAndDynamicConsumeDetailStatementEasyExcelForm consumeDetailStatementForm = new CustomerDynamicSaleAndDynamicConsumeDetailStatementEasyExcelForm();
            consumeDetailStatementForm.name = "合计";
            consumeDetailStatementForm.code = "";
            consumeDetailStatementForm.phoneNumber = "";
            consumeDetailStatementForm.levelName = "";
            consumeDetailStatementForm.sourceName = "";
            consumeDetailStatementForm.birthday = "";
            consumeDetailStatementForm.createdOn = "";
            consumeDetailStatementForm.entityName = "";
            consumeDetailStatementForm.totalSaleAmount = customerDynamicSaleAndDynamicConsumeSumStatementForm.TotalSaleAmount.doubleValue();
            consumeDetailStatementForm.totalSaleQuantity = customerDynamicSaleAndDynamicConsumeSumStatementForm.TotalSaleQuantity.doubleValue();
            consumeDetailStatementForm.totalSalePrice = customerDynamicSaleAndDynamicConsumeSumStatementForm.TotalSalePrice.doubleValue();
            consumeDetailStatementForm.currentSaleQuantity = customerDynamicSaleAndDynamicConsumeSumStatementForm.CurrentSaleQuantity.doubleValue();
            consumeDetailStatementForm.currentSaleAmount = customerDynamicSaleAndDynamicConsumeSumStatementForm.CurrentSaleAmount.doubleValue();
            consumeDetailStatementForm.currentConsumeProjectCount = customerDynamicSaleAndDynamicConsumeSumStatementForm.CurrentConsumeProjectCount.doubleValue();
            consumeDetailStatementForm.currentConsumeProjectQuantity = customerDynamicSaleAndDynamicConsumeSumStatementForm.CurrentConsumeProjectQuantity.doubleValue();
            consumeDetailStatementForm.currentConsumeProjectAmount = customerDynamicSaleAndDynamicConsumeSumStatementForm.CurrentConsumeProjectAmount.doubleValue();
            consumeDetailStatementForm.currentConsumeProjectPrice = customerDynamicSaleAndDynamicConsumeSumStatementForm.CurrentConsumeProjectPrice.doubleValue();
            consumeDetailStatementForm.lastSaleBillDate = "";
            consumeDetailStatementForm.unSaleDay = customerDynamicSaleAndDynamicConsumeSumStatementForm.UnSaleDay;
            consumeDetailStatementForm.lastConsumeBillDate = "";
            consumeDetailStatementForm.unConsumeDay = customerDynamicSaleAndDynamicConsumeSumStatementForm.UnConsumeDay;
            consumeDetailStatementForm.toShopQuantity = customerDynamicSaleAndDynamicConsumeSumStatementForm.ToShopQuantity;
            list.add(consumeDetailStatementForm);
        }

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("顾客动销动耗分析表", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), CustomerDynamicSaleAndDynamicConsumeDetailStatementEasyExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("顾客动销动耗分析表").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    public BaseOutput accountStatistics(AccountStatisticsStatementQueryForm form) {
        CustomerAccountStatisticsStatementForm consumeStatementForm = new CustomerAccountStatisticsStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        consumeStatementForm.detail = BasePageInfo.success(customerStatementMapper.getCustomerAccountStatistics(SystemUserUtil.getSystemUserID(), form.Name, form.BuyEntityID, form.EntityID, form.LevelID, form.GoodsTypeName, form.GoodsName, form.PackageCardID, form.CategoryName, form.BrandName, form.IsLargess, form.IsOverdue, form.IsShowUp != null && form.IsShowUp == true ? true : null,form.CustomerSourceID));
        consumeStatementForm.customerAccountStatisticsSumStatementForm = customerStatementMapper.getCustomerAccountStatisticsSum(SystemUserUtil.getSystemUserID(), form.Name, form.BuyEntityID, form.EntityID, form.LevelID, form.GoodsTypeName, form.GoodsName, form.PackageCardID, form.CategoryName, form.BrandName, form.IsLargess, form.IsOverdue, form.IsShowUp != null && form.IsShowUp == true ? true : null,form.CustomerSourceID);
        return BaseOutputForm.success(consumeStatementForm);
    }


    public BaseOutput accountStatisticsEasyExcel(AccountStatisticsStatementQueryForm form, HttpServletResponse response) {

        List<CustomerAccountStatisticsDetailStatementEasyExcelForm> list = customerStatementMapper.getCustomerAccountStatisticsEasyExcel(SystemUserUtil.getSystemUserID(), form.Name, form.BuyEntityID, form.EntityID, form.LevelID, form.GoodsTypeName, form.GoodsName, form.PackageCardID, form.CategoryName, form.BrandName, form.IsLargess, form.IsOverdue, form.IsShowUp != null && form.IsShowUp == true ? true : null,form.CustomerSourceID);
        CustomerAccountStatisticsSumStatementForm customerAccountStatisticsSumStatementForm = customerStatementMapper.getCustomerAccountStatisticsSum(SystemUserUtil.getSystemUserID(), form.Name, form.BuyEntityID, form.EntityID, form.LevelID, form.GoodsTypeName, form.GoodsName, form.PackageCardID, form.CategoryName, form.BrandName, form.IsLargess, form.IsOverdue, form.IsShowUp != null && form.IsShowUp == true ? true : null,form.CustomerSourceID);

        if (customerAccountStatisticsSumStatementForm != null) {
            CustomerAccountStatisticsDetailStatementEasyExcelForm consumeDetailStatementForm = new CustomerAccountStatisticsDetailStatementEasyExcelForm();
            consumeDetailStatementForm.name = "合计";
            consumeDetailStatementForm.code = "";
            consumeDetailStatementForm.phoneNumber = "";
            consumeDetailStatementForm.buyEntityName = "";
            consumeDetailStatementForm.entityName = "";
            consumeDetailStatementForm.levelName = "";
            consumeDetailStatementForm.goodsTypeName = "";
            consumeDetailStatementForm.entityName = "";
            consumeDetailStatementForm.packageCardName = "";
            consumeDetailStatementForm.categoryName = "";
            consumeDetailStatementForm.goodsName = "";
            consumeDetailStatementForm.brandName = "";
            consumeDetailStatementForm.isLargess = "";
            consumeDetailStatementForm.quantity = customerAccountStatisticsSumStatementForm.Quantity.doubleValue();
            consumeDetailStatementForm.totalAmount = customerAccountStatisticsSumStatementForm.TotalAmount.doubleValue();
            consumeDetailStatementForm.balance = customerAccountStatisticsSumStatementForm.Balance.doubleValue();
            consumeDetailStatementForm.balanceAmount = customerAccountStatisticsSumStatementForm.BalanceAmount.doubleValue();
            consumeDetailStatementForm.payAmount = customerAccountStatisticsSumStatementForm.PayAmount.doubleValue();
            consumeDetailStatementForm.largessAmount = customerAccountStatisticsSumStatementForm.LargessAmount.doubleValue();
            consumeDetailStatementForm.largessMoney = customerAccountStatisticsSumStatementForm.LargessMoney.doubleValue();
            consumeDetailStatementForm.arrearAmount = customerAccountStatisticsSumStatementForm.ArrearAmount.doubleValue();
            consumeDetailStatementForm.buyDate = "";
            consumeDetailStatementForm.validDate = "";
            consumeDetailStatementForm.isOverdue = "";
            list.add(consumeDetailStatementForm);
        }

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("顾客余量统计表", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), CustomerAccountStatisticsDetailStatementEasyExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("顾客余量统计表").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    public BaseOutput accountStatisticsEasyExcelDisPlayPhone(AccountStatisticsStatementQueryForm form, HttpServletResponse response) {

        List<CustomerAccountStatisticsDetailStatementEasyExcelForm> list = customerStatementMapper.getCustomerAccountStatisticsEasyExcelDisPlayPhone(SystemUserUtil.getSystemUserID(), form.Name, form.BuyEntityID, form.EntityID, form.LevelID, form.GoodsTypeName, form.GoodsName, form.PackageCardID, form.CategoryName, form.BrandName, form.IsLargess, form.IsOverdue, form.IsShowUp != null && form.IsShowUp == true ? true : null,form.CustomerSourceID);
        CustomerAccountStatisticsSumStatementForm customerAccountStatisticsSumStatementForm = customerStatementMapper.getCustomerAccountStatisticsSum(SystemUserUtil.getSystemUserID(), form.Name, form.BuyEntityID, form.EntityID, form.LevelID, form.GoodsTypeName, form.GoodsName, form.PackageCardID, form.CategoryName, form.BrandName, form.IsLargess, form.IsOverdue, form.IsShowUp != null && form.IsShowUp == true ? true : null,form.CustomerSourceID);

        if (customerAccountStatisticsSumStatementForm != null) {
            CustomerAccountStatisticsDetailStatementEasyExcelForm consumeDetailStatementForm = new CustomerAccountStatisticsDetailStatementEasyExcelForm();
            consumeDetailStatementForm.name = "合计";
            consumeDetailStatementForm.code = "";
            consumeDetailStatementForm.phoneNumber = "";
            consumeDetailStatementForm.buyEntityName = "";
            consumeDetailStatementForm.entityName = "";
            consumeDetailStatementForm.levelName = "";
            consumeDetailStatementForm.goodsTypeName = "";
            consumeDetailStatementForm.entityName = "";
            consumeDetailStatementForm.packageCardName = "";
            consumeDetailStatementForm.categoryName = "";
            consumeDetailStatementForm.goodsName = "";
            consumeDetailStatementForm.brandName = "";
            consumeDetailStatementForm.isLargess = "";
            consumeDetailStatementForm.quantity = customerAccountStatisticsSumStatementForm.Quantity.doubleValue();
            consumeDetailStatementForm.totalAmount = customerAccountStatisticsSumStatementForm.TotalAmount.doubleValue();
            consumeDetailStatementForm.balance = customerAccountStatisticsSumStatementForm.Balance.doubleValue();
            consumeDetailStatementForm.balanceAmount = customerAccountStatisticsSumStatementForm.BalanceAmount.doubleValue();
            consumeDetailStatementForm.payAmount = customerAccountStatisticsSumStatementForm.PayAmount.doubleValue();
            consumeDetailStatementForm.largessAmount = customerAccountStatisticsSumStatementForm.LargessAmount.doubleValue();
            consumeDetailStatementForm.largessMoney = customerAccountStatisticsSumStatementForm.LargessMoney.doubleValue();
            consumeDetailStatementForm.arrearAmount = customerAccountStatisticsSumStatementForm.ArrearAmount.doubleValue();
            consumeDetailStatementForm.buyDate = "";
            consumeDetailStatementForm.validDate = "";
            consumeDetailStatementForm.isOverdue = "";
            list.add(consumeDetailStatementForm);
        }

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("顾客余量统计表", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), CustomerAccountStatisticsDetailStatementEasyExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("顾客余量统计表").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    public BaseOutput arrearsDetail(ArrearsDetailStatementQueryForm form) {
        CustomerArrearsStatementForm consumeStatementForm = new CustomerArrearsStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        consumeStatementForm.detail = BasePageInfo.success(customerStatementMapper.getCustomerArrear(SystemUserUtil.getSystemUserID(), form.Name, form.BuyEntityID, form.EntityID, form.StartTime, form.EndTime, form.GoodsTypeName, form.GoodsName, form.CategoryName));
        consumeStatementForm.customerArrearsSumStatementForm = customerStatementMapper.getCustomerArrearSum(SystemUserUtil.getSystemUserID(), form.Name, form.BuyEntityID, form.EntityID, form.StartTime, form.EndTime, form.GoodsTypeName, form.GoodsName, form.CategoryName);
        return BaseOutputForm.success(consumeStatementForm);
    }

    public BaseOutput arrearsDetailEasyExcel(ArrearsDetailStatementQueryForm form, HttpServletResponse response) {
        List<CustomerArrearsDetailStatementEasyExcelForm> list = customerStatementMapper.getCustomerArrearEasyExcel(SystemUserUtil.getSystemUserID(), form.Name, form.BuyEntityID, form.EntityID, form.StartTime, form.EndTime, form.GoodsTypeName, form.GoodsName, form.CategoryName);

        CustomerArrearsSumStatementForm customerArrearsSumStatementForm = customerStatementMapper.getCustomerArrearSum(SystemUserUtil.getSystemUserID(), form.Name, form.BuyEntityID, form.EntityID, form.StartTime, form.EndTime, form.GoodsTypeName, form.GoodsName, form.CategoryName);
        if (customerArrearsSumStatementForm != null) {
            CustomerArrearsDetailStatementEasyExcelForm consumeDetailStatementForm = new CustomerArrearsDetailStatementEasyExcelForm();
            consumeDetailStatementForm.name = "合计";
            consumeDetailStatementForm.code = "";
            consumeDetailStatementForm.phoneNumber = "";
            consumeDetailStatementForm.entityName = "";
            consumeDetailStatementForm.levelName = "";
            consumeDetailStatementForm.billID = "";
            consumeDetailStatementForm.buyDate = "";
            consumeDetailStatementForm.buyEntityName = "";
            consumeDetailStatementForm.employeeName = "";
            consumeDetailStatementForm.goodsTypeName = "";
            consumeDetailStatementForm.categoryName = "";
            consumeDetailStatementForm.goodsName = "";
            consumeDetailStatementForm.packageCardName = "";
            consumeDetailStatementForm.totalAmount = customerArrearsSumStatementForm.TotalAmount.doubleValue();
            consumeDetailStatementForm.payAmount = customerArrearsSumStatementForm.PayAmount.doubleValue();
            consumeDetailStatementForm.savingCardDeductionAmount = customerArrearsSumStatementForm.SavingCardDeductionAmount.doubleValue();
            consumeDetailStatementForm.savingCardDeductionLargessAmount = customerArrearsSumStatementForm.SavingCardDeductionLargessAmount.doubleValue();
            consumeDetailStatementForm.originMoney = customerArrearsSumStatementForm.OriginMoney.doubleValue();
            consumeDetailStatementForm.fillingMoney = customerArrearsSumStatementForm.FillingMoney.doubleValue();
            consumeDetailStatementForm.arrearAmount = customerArrearsSumStatementForm.ArrearAmount.doubleValue();
            list.add(consumeDetailStatementForm);
        }

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("顾客欠款明细表", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), CustomerArrearsDetailStatementEasyExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("顾客欠款明细表").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    public BaseOutput arrearsDetailEasyExcelDisPlayPhone(ArrearsDetailStatementQueryForm form, HttpServletResponse response) {
        List<CustomerArrearsDetailStatementEasyExcelForm> list = customerStatementMapper.getCustomerArrearEasyExcelDisPlayPhone(SystemUserUtil.getSystemUserID(), form.Name, form.BuyEntityID, form.EntityID, form.StartTime, form.EndTime, form.GoodsTypeName, form.GoodsName, form.CategoryName);

        CustomerArrearsSumStatementForm customerArrearsSumStatementForm = customerStatementMapper.getCustomerArrearSum(SystemUserUtil.getSystemUserID(), form.Name, form.BuyEntityID, form.EntityID, form.StartTime, form.EndTime, form.GoodsTypeName, form.GoodsName, form.CategoryName);
        if (customerArrearsSumStatementForm != null) {
            CustomerArrearsDetailStatementEasyExcelForm consumeDetailStatementForm = new CustomerArrearsDetailStatementEasyExcelForm();
            consumeDetailStatementForm.name = "合计";
            consumeDetailStatementForm.code = "";
            consumeDetailStatementForm.phoneNumber = "";
            consumeDetailStatementForm.entityName = "";
            consumeDetailStatementForm.levelName = "";
            consumeDetailStatementForm.billID = "";
            consumeDetailStatementForm.buyDate = "";
            consumeDetailStatementForm.buyEntityName = "";
            consumeDetailStatementForm.employeeName = "";
            consumeDetailStatementForm.goodsTypeName = "";
            consumeDetailStatementForm.categoryName = "";
            consumeDetailStatementForm.goodsName = "";
            consumeDetailStatementForm.packageCardName = "";
            consumeDetailStatementForm.totalAmount = customerArrearsSumStatementForm.TotalAmount.doubleValue();
            consumeDetailStatementForm.payAmount = customerArrearsSumStatementForm.PayAmount.doubleValue();
            consumeDetailStatementForm.savingCardDeductionAmount = customerArrearsSumStatementForm.SavingCardDeductionAmount.doubleValue();
            consumeDetailStatementForm.savingCardDeductionLargessAmount = customerArrearsSumStatementForm.SavingCardDeductionLargessAmount.doubleValue();
            consumeDetailStatementForm.originMoney = customerArrearsSumStatementForm.OriginMoney.doubleValue();
            consumeDetailStatementForm.fillingMoney = customerArrearsSumStatementForm.FillingMoney.doubleValue();
            consumeDetailStatementForm.arrearAmount = customerArrearsSumStatementForm.ArrearAmount.doubleValue();
            list.add(consumeDetailStatementForm);
        }

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("顾客欠款明细表", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), CustomerArrearsDetailStatementEasyExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("顾客欠款明细表").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }
}
