package com.zhelian.service.report.entity;

import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.report.ReportEntityMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportEntityService {

    @Autowired
    ReportEntityMapper reportEntityMapper;

    public BaseOutput getStoreEntityList() {
        return BaseOutputForm.success(reportEntityMapper.getStoreEntityListByEmployeeID(SystemUserUtil.getSystemUserID()));
    }
}
