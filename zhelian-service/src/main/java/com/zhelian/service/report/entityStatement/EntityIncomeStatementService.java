package com.zhelian.service.report.entityStatement;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.report.EntityIncomeStatementMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.report.form.input.EntityDateIncomeQueryForm;
import com.zhelian.model.report.form.input.EntityIncomeQueryForm;
import com.zhelian.model.report.form.output.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;


@Service
public class EntityIncomeStatementService {

    @Autowired
    EntityIncomeStatementMapper entityIncomeStatementMapper;

    public BaseOutput getDateIncomeStatement(EntityDateIncomeQueryForm form) {
        EntityDateIncomeStatementForm statementForm = new EntityDateIncomeStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        statementForm.detail = BasePageInfo.success(entityIncomeStatementMapper.getDateIncomeStatement(SystemUserUtil.getSystemUserID(), form.EntityID, form.StartTime, form.EndTime));
        statementForm.entityIncomeSumStatementForm = entityIncomeStatementMapper.getDateIncomeStatementSum(SystemUserUtil.getSystemUserID(), form.EntityID, form.StartTime, form.EndTime);
        return BaseOutputForm.success(statementForm);
    }

    public BaseOutput excelDateIncomeStatement(EntityDateIncomeQueryForm form, HttpServletResponse response) {

        List<EntityDateIncomeDetailStatementExcelForm> list = entityIncomeStatementMapper.getDateIncomeStatementExcel(SystemUserUtil.getSystemUserID(), form.EntityID, form.StartTime, form.EndTime);
        EntityIncomeSumStatementForm entityIncomeSumStatementForm = entityIncomeStatementMapper.getDateIncomeStatementSum(SystemUserUtil.getSystemUserID(), form.EntityID, form.StartTime, form.EndTime);
        if (entityIncomeSumStatementForm != null) {
            EntityDateIncomeDetailStatementExcelForm statementForm = new EntityDateIncomeDetailStatementExcelForm();
            statementForm.billDate = "合计";
            statementForm.entityName = "";
            statementForm.beautyRecharge = entityIncomeSumStatementForm.BeautyRecharge;
            statementForm.beautyProduct = entityIncomeSumStatementForm.BeautyProduct;
            statementForm.beautyProject = entityIncomeSumStatementForm.BeautyProject;
            statementForm.beautyTotal = entityIncomeSumStatementForm.BeautyTotal;
            statementForm.hairRecharge = entityIncomeSumStatementForm.HairRecharge;
            statementForm.hairProduct = entityIncomeSumStatementForm.HairProduct;
            statementForm.hairProject = entityIncomeSumStatementForm.HairProject;
            statementForm.hairTotal = entityIncomeSumStatementForm.HairTotal;
            statementForm.beautyTreatProduct = entityIncomeSumStatementForm.BeautyTreatProduct;
            statementForm.beautyTreatProject = entityIncomeSumStatementForm.BeautyTreatProject;
            statementForm.beautyTreatTotal = entityIncomeSumStatementForm.BeautyTreatTotal;
            statementForm.hairTreatProduct = entityIncomeSumStatementForm.HairTreatProduct;
            statementForm.hairTreatProject = entityIncomeSumStatementForm.HairTreatProject;
            statementForm.hairTreatTotal = entityIncomeSumStatementForm.HairTreatTotal;
            statementForm.beautySavingCardRate = entityIncomeSumStatementForm.BeautySavingCardRate;
            statementForm.haidSavingCardRate = entityIncomeSumStatementForm.HaidSavingCardRate;
            statementForm.beautySavingCard = entityIncomeSumStatementForm.BeautySavingCard;
            statementForm.haidSavingCard = entityIncomeSumStatementForm.HaidSavingCard;
            statementForm.beautyTotalMoney = entityIncomeSumStatementForm.BeautyTotalMoney;
            statementForm.hairTotalMoney = entityIncomeSumStatementForm.HairTotalMoney;
            list.add(statementForm);
        }

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("营销收入明细", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), EntityDateIncomeDetailStatementExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("营销收入明细").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return null;
    }

    public BaseOutput getEntityIncomeStatement(EntityIncomeQueryForm form) {
        form.EmployeeID = SystemUserUtil.getSystemUserID();
        EntityIncomeStatementForm statementForm = new EntityIncomeStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        statementForm.detail = BasePageInfo.success(entityIncomeStatementMapper.getEntityIncomeStatement(form));
        statementForm.entityIncomeSumStatementForm = entityIncomeStatementMapper.getEntityIncomeStatementSum(form);
        return BaseOutputForm.success(statementForm);
    }

    public BaseOutput excelEntityIncomeStatement(EntityIncomeQueryForm form, HttpServletResponse response) {
        form.EmployeeID = SystemUserUtil.getSystemUserID();
        List<EntityIncomeDetailStatementExcelForm> list = entityIncomeStatementMapper.getEntityIncomeStatementExcel(form);
        EntityIncomeSumStatementForm entityIncomeSumStatementForm = entityIncomeStatementMapper.getEntityIncomeStatementSum(form);
        if (entityIncomeSumStatementForm != null) {
            EntityIncomeDetailStatementExcelForm statementForm = new EntityIncomeDetailStatementExcelForm();
            statementForm.entityName = "合计";
            statementForm.beautyRecharge = entityIncomeSumStatementForm.BeautyRecharge;
            statementForm.beautyProduct = entityIncomeSumStatementForm.BeautyProduct;
            statementForm.beautyProject = entityIncomeSumStatementForm.BeautyProject;
            statementForm.beautyTotal = entityIncomeSumStatementForm.BeautyTotal;
            statementForm.hairRecharge = entityIncomeSumStatementForm.HairRecharge;
            statementForm.hairProduct = entityIncomeSumStatementForm.HairProduct;
            statementForm.hairProject = entityIncomeSumStatementForm.HairProject;
            statementForm.hairTotal = entityIncomeSumStatementForm.HairTotal;
            statementForm.beautyTreatProduct = entityIncomeSumStatementForm.BeautyTreatProduct;
            statementForm.beautyTreatProject = entityIncomeSumStatementForm.BeautyTreatProject;
            statementForm.beautyTreatTotal = entityIncomeSumStatementForm.BeautyTreatTotal;
            statementForm.hairTreatProduct = entityIncomeSumStatementForm.HairTreatProduct;
            statementForm.hairTreatProject = entityIncomeSumStatementForm.HairTreatProject;
            statementForm.hairTreatTotal = entityIncomeSumStatementForm.HairTreatTotal;
            statementForm.beautySavingCardRate = entityIncomeSumStatementForm.BeautySavingCardRate;
            statementForm.haidSavingCardRate = entityIncomeSumStatementForm.HaidSavingCardRate;
            statementForm.beautySavingCard = entityIncomeSumStatementForm.BeautySavingCard;
            statementForm.haidSavingCard = entityIncomeSumStatementForm.HaidSavingCard;
            statementForm.beautyTotalMoney = entityIncomeSumStatementForm.BeautyTotalMoney;
            statementForm.hairTotalMoney = entityIncomeSumStatementForm.HairTotalMoney;
            list.add(statementForm);
        }
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("营销收入明细", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), EntityIncomeDetailStatementExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("营销收入明细").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return null;
    }


    public BaseOutput getIncomeStatement(EntityDateIncomeQueryForm form) {
        IncomeStatementForm statementForm = new IncomeStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        statementForm.detail = BasePageInfo.success(entityIncomeStatementMapper.getIncomeStatement(SystemUserUtil.getSystemUserID(), form.EntityID, form.StartTime, form.EndTime));
        statementForm.incomeSumStatementForm = entityIncomeStatementMapper.getIncomeStatementSum(SystemUserUtil.getSystemUserID(), form.EntityID, form.StartTime, form.EndTime);
        return BaseOutputForm.success(statementForm);
    }

    public BaseOutput excelIncomeStatement(EntityDateIncomeQueryForm form, HttpServletResponse response) {

        List<IncomeDetailStatementExcelForm> list = entityIncomeStatementMapper.getIncomeStatementExcel(SystemUserUtil.getSystemUserID(), form.EntityID, form.StartTime, form.EndTime);
        IncomeSumStatementForm entityIncomeSumStatementForm = entityIncomeStatementMapper.getIncomeStatementSum(SystemUserUtil.getSystemUserID(), form.EntityID, form.StartTime, form.EndTime);
        if (entityIncomeSumStatementForm != null) {
            IncomeDetailStatementExcelForm statementForm = new IncomeDetailStatementExcelForm();
            statementForm.entityName = "合计";
            statementForm.beautyTotal = entityIncomeSumStatementForm.BeautyTotal;
            statementForm.beautyTreatTotal = entityIncomeSumStatementForm.BeautyTreatTotal;
            statementForm.beautyTotalMoney = entityIncomeSumStatementForm.BeautyTotalMoney;
            statementForm.hairTotal = entityIncomeSumStatementForm.HairTotal;
            statementForm.hairTreatTotal = entityIncomeSumStatementForm.HairTreatTotal;
            statementForm.hairTotalMoney = entityIncomeSumStatementForm.HairTotalMoney;
            statementForm.toShopRate = "";
            statementForm.againBuyRate = "";
            statementForm.conversionRate = "";
            list.add(statementForm);
        }
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("营销收入汇总", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), IncomeDetailStatementExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("营销收入汇总").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }
}
