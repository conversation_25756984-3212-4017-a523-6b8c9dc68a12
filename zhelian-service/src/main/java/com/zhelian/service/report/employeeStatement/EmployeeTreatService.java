package com.zhelian.service.report.employeeStatement;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.report.EmployeeTreatMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.report.form.input.EmployeeTreatPerformanceCommissionDetailForm;
import com.zhelian.model.report.form.output.EmployeeTreatCardDetailStatisticsStatementExcelForm;
import com.zhelian.model.report.form.output.EmployeeTreatGoodDetailAndSumStatementForm;
import com.zhelian.model.report.form.output.EmployeeTreatGoodDetailStatisticsStatementExcelForm;
import com.zhelian.model.report.form.output.EmployeeTreatGoodSumStatementForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class EmployeeTreatService {
    @Autowired
    EmployeeTreatMapper employeeTreatMapper;

    public BaseOutput getEmployeeTreatStatistics(EmployeeTreatPerformanceCommissionDetailForm form) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        EmployeeTreatGoodDetailAndSumStatementForm employeeStatementForms = new EmployeeTreatGoodDetailAndSumStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        employeeStatementForms.employeeTreatGoodStatementForms = BasePageInfo.success(employeeTreatMapper.getEmployeeTreatGoodStatistics(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.EmployeeName, form.JobID));
        employeeStatementForms.employeeTreatGoodSumStatementForm = employeeTreatMapper.getEmployeeTreatGoodSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.EmployeeName, form.JobID);
        return BaseOutputForm.success(employeeStatementForms);
    }

    public BaseOutput goodStatisticsExcel(EmployeeTreatPerformanceCommissionDetailForm form, HttpServletResponse response) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<EmployeeTreatGoodDetailStatisticsStatementExcelForm> list = employeeTreatMapper.getExcelEmployeeTreatGoodStatistics(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.EmployeeName, form.JobID);
        EmployeeTreatGoodSumStatementForm statisticsSum = employeeTreatMapper.getEmployeeTreatGoodSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.EmployeeName, form.JobID);
        if (statisticsSum != null) {
            EmployeeTreatGoodDetailStatisticsStatementExcelForm statementForm = new EmployeeTreatGoodDetailStatisticsStatementExcelForm();
            statementForm.employeeID = "合计";
            statementForm.employeeName = "";
            statementForm.jobName = "";
            statementForm.entityName = "";
            statementForm.treatQuantity = statisticsSum.TreatQuantity;
            statementForm.treatPayAmount = statisticsSum.TreatPayAmount;
            statementForm.treatCardDeductionAmount = statisticsSum.TreatCardDeductionAmount;
            statementForm.treatLargessCardDeductionAmount = statisticsSum.TreatLargessCardDeductionAmount;
            statementForm.treatLargessAmount = statisticsSum.TreatLargessAmount;
            statementForm.refundTreatPayAmount = statisticsSum.RefundTreatPayAmount;
            statementForm.refundTreatCardDeductionAmount = statisticsSum.RefundTreatCardDeductionAmount;
            statementForm.refundTreatLargessCardDeductionAmount = statisticsSum.RefundTreatLargessCardDeductionAmount;
            statementForm.refundTreatLargessAmount = statisticsSum.RefundTreatLargessAmount;
            list.add(statementForm);
        }

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("员工消耗商品统计", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), EmployeeTreatGoodDetailStatisticsStatementExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("员工消耗商品统计").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return null;
    }

    public BaseOutput getEmployeeTreatProjectStatistics(EmployeeTreatPerformanceCommissionDetailForm form) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        EmployeeTreatGoodDetailAndSumStatementForm employeeStatementForms = new EmployeeTreatGoodDetailAndSumStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        employeeStatementForms.employeeTreatGoodStatementForms = BasePageInfo.success(employeeTreatMapper.getEmployeeTreatProjectStatistics(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.EmployeeName, form.JobID));
        employeeStatementForms.employeeTreatGoodSumStatementForm = employeeTreatMapper.getEmployeeTreatProjectSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.EmployeeName, form.JobID);
        return BaseOutputForm.success(employeeStatementForms);
    }

    public BaseOutput projectStatisticsExcel(EmployeeTreatPerformanceCommissionDetailForm form, HttpServletResponse response) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<EmployeeTreatGoodDetailStatisticsStatementExcelForm> list = employeeTreatMapper.getExcelEmployeeTreatProjectStatistics(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.EmployeeName, form.JobID);
        EmployeeTreatGoodSumStatementForm statisticsSum = employeeTreatMapper.getEmployeeTreatProjectSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.EmployeeName, form.JobID);
        if (statisticsSum != null) {
            EmployeeTreatGoodDetailStatisticsStatementExcelForm statementForm = new EmployeeTreatGoodDetailStatisticsStatementExcelForm();
            statementForm.employeeID = "合计";
            statementForm.employeeName = "";
            statementForm.jobName = "";
            statementForm.entityName = "";
            statementForm.treatQuantity = statisticsSum.TreatQuantity;
            statementForm.treatPayAmount = statisticsSum.TreatPayAmount;
            statementForm.treatCardDeductionAmount = statisticsSum.TreatCardDeductionAmount;
            statementForm.treatLargessCardDeductionAmount = statisticsSum.TreatLargessCardDeductionAmount;
            statementForm.treatLargessAmount = statisticsSum.TreatLargessAmount;
            statementForm.refundTreatPayAmount = statisticsSum.RefundTreatPayAmount;
            statementForm.refundTreatCardDeductionAmount = statisticsSum.RefundTreatCardDeductionAmount;
            statementForm.refundTreatLargessCardDeductionAmount = statisticsSum.RefundTreatLargessCardDeductionAmount;
            statementForm.refundTreatLargessAmount = statisticsSum.RefundTreatLargessAmount;
            list.add(statementForm);
        }

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("员工消耗项目统计", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), EmployeeTreatGoodDetailStatisticsStatementExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("员工消耗项目统计").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return null;
    }

    public BaseOutput getEmployeeTreatProductStatistics(EmployeeTreatPerformanceCommissionDetailForm form) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        EmployeeTreatGoodDetailAndSumStatementForm employeeStatementForms = new EmployeeTreatGoodDetailAndSumStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        employeeStatementForms.employeeTreatGoodStatementForms = BasePageInfo.success(employeeTreatMapper.getEmployeeTreatProductStatistics(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.EmployeeName, form.JobID));
        employeeStatementForms.employeeTreatGoodSumStatementForm = employeeTreatMapper.getEmployeeTreatProductSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.EmployeeName, form.JobID);
        return BaseOutputForm.success(employeeStatementForms);
    }

    public BaseOutput productStatisticsExcel(EmployeeTreatPerformanceCommissionDetailForm form, HttpServletResponse response) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<EmployeeTreatGoodDetailStatisticsStatementExcelForm> list = employeeTreatMapper.getExcelEmployeeTreatProductStatistics(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.EmployeeName, form.JobID);
        EmployeeTreatGoodSumStatementForm statisticsSum = employeeTreatMapper.getEmployeeTreatProductSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.EmployeeName, form.JobID);
        if (statisticsSum != null) {
            EmployeeTreatGoodDetailStatisticsStatementExcelForm statementForm = new EmployeeTreatGoodDetailStatisticsStatementExcelForm();
            statementForm.employeeID = "合计";
            statementForm.employeeName = "";
            statementForm.jobName = "";
            statementForm.entityName = "";
            statementForm.treatQuantity = statisticsSum.TreatQuantity;
            statementForm.treatPayAmount = statisticsSum.TreatPayAmount;
            statementForm.treatCardDeductionAmount = statisticsSum.TreatCardDeductionAmount;
            statementForm.treatLargessCardDeductionAmount = statisticsSum.TreatLargessCardDeductionAmount;
            statementForm.treatLargessAmount = statisticsSum.TreatLargessAmount;
            statementForm.refundTreatPayAmount = statisticsSum.RefundTreatPayAmount;
            statementForm.refundTreatCardDeductionAmount = statisticsSum.RefundTreatCardDeductionAmount;
            statementForm.refundTreatLargessCardDeductionAmount = statisticsSum.RefundTreatLargessCardDeductionAmount;
            statementForm.refundTreatLargessAmount = statisticsSum.RefundTreatLargessAmount;
            list.add(statementForm);
        }

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("员工消耗产品统计", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), EmployeeTreatGoodDetailStatisticsStatementExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("员工消耗产品统计").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return null;
    }

    public BaseOutput treatCardStatistics(EmployeeTreatPerformanceCommissionDetailForm form) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        EmployeeTreatGoodDetailAndSumStatementForm employeeStatementForms = new EmployeeTreatGoodDetailAndSumStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        employeeStatementForms.employeeTreatGoodStatementForms = BasePageInfo.success(employeeTreatMapper.getEmployeeTreatCardStatistics(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.EmployeeName, form.JobID));
        employeeStatementForms.employeeTreatGoodSumStatementForm = employeeTreatMapper.getEmployeeTreatCardSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.EmployeeName, form.JobID);
        return BaseOutputForm.success(employeeStatementForms);
    }

    public BaseOutput cardStatisticsExcel(EmployeeTreatPerformanceCommissionDetailForm form, HttpServletResponse response) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<EmployeeTreatCardDetailStatisticsStatementExcelForm> list = employeeTreatMapper.getExcelEmployeeTreatCardStatistics(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.EmployeeName, form.JobID);
        EmployeeTreatGoodSumStatementForm statisticsSum = employeeTreatMapper.getEmployeeTreatCardSum(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate, form.EmployeeName, form.JobID);
        if (statisticsSum != null) {
            EmployeeTreatCardDetailStatisticsStatementExcelForm statementForm = new EmployeeTreatCardDetailStatisticsStatementExcelForm();
            statementForm.employeeID = "合计";
            statementForm.employeeName = "";
            statementForm.jobName = "";
            statementForm.entityName = "";
            statementForm.treatQuantity = statisticsSum.TreatQuantity;
            statementForm.treatPayAmount = statisticsSum.TreatPayAmount;
            statementForm.treatCardDeductionAmount = statisticsSum.TreatCardDeductionAmount;
            statementForm.treatLargessCardDeductionAmount = statisticsSum.TreatLargessCardDeductionAmount;
            statementForm.treatLargessAmount = statisticsSum.TreatLargessAmount;
            statementForm.refundTreatPayAmount = statisticsSum.RefundTreatPayAmount;
            statementForm.refundTreatCardDeductionAmount = statisticsSum.RefundTreatCardDeductionAmount;
            statementForm.refundTreatLargessCardDeductionAmount = statisticsSum.RefundTreatLargessCardDeductionAmount;
            statementForm.refundTreatLargessAmount = statisticsSum.RefundTreatLargessAmount;
            list.add(statementForm);
        }

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("员工消耗卡次数统计", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), EmployeeTreatCardDetailStatisticsStatementExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("员工消耗卡次数统计").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return null;
    }
}
