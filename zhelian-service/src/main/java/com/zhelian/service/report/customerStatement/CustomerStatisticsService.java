package com.zhelian.service.report.customerStatement;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.report.CustomerStatisticsMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.report.form.input.CustomerStatisticsQueryForm;
import com.zhelian.model.report.form.output.CustomerStatisticsDateDetailOutputForm;
import com.zhelian.model.report.form.output.CustomerStatisticsDetailOutputForm;
import com.zhelian.model.report.form.output.CustomerStatisticsOutputForm;
import com.zhelian.model.report.form.output.CustomerStatisticsSumOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class CustomerStatisticsService {

    @Autowired
    CustomerStatisticsMapper customerStatisticsMapper;

    public BaseOutput monthlySaleStatistics(CustomerStatisticsQueryForm form) {
        String searchDate = form.SearchDate + "-" + "01";
        LocalDate time = LocalDate.parse(searchDate);
        String startDate = time.with(TemporalAdjusters.firstDayOfMonth()).toString();
        String endDate = time.with(TemporalAdjusters.lastDayOfMonth()).toString();

        CustomerStatisticsOutputForm statisticsOutputForm = new CustomerStatisticsOutputForm();

        CustomerStatisticsSumOutputForm customerStatisticsSumOutputForm = new CustomerStatisticsSumOutputForm();
        customerStatisticsSumOutputForm.Count = customerStatisticsMapper.getCustomerStatisticsSum(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        customerStatisticsSumOutputForm.Name = "合计";
        customerStatisticsSumOutputForm.DateData = customerStatisticsMapper.getCustomerStatisticsSumMonth(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);

        PageHelper.startPage(form.PageNum, form.getPageSize());
        statisticsOutputForm.Detail = BasePageInfo.success(customerStatisticsMapper.getCustomerStatistics(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate));
        statisticsOutputForm.SumOutputForm = customerStatisticsSumOutputForm;
        return BaseOutputForm.success(statisticsOutputForm);

    }


    public BaseOutput easyExcelMonthlySaleStatistics(CustomerStatisticsQueryForm form, HttpServletResponse response) {
        String searchDate = form.SearchDate + "-" + "01";
        LocalDate time = LocalDate.parse(searchDate);
        String startDate = time.with(TemporalAdjusters.firstDayOfMonth()).toString();
        String endDate = time.with(TemporalAdjusters.lastDayOfMonth()).toString();

        List<CustomerStatisticsDateDetailOutputForm> dateList = customerStatisticsMapper.getCustomerStatisticsSumMonth(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        List<String> date = dateList.stream().map(i -> i.Date).collect(Collectors.toList());

        List<CustomerStatisticsDetailOutputForm> list = customerStatisticsMapper.getCustomerStatisticsExcel(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        CustomerStatisticsDetailOutputForm totalForm = new CustomerStatisticsDetailOutputForm();
        totalForm.Name = "合计";
        totalForm.PhoneNumber = "";
        totalForm.Code = "";
        totalForm.EntityName = "";
        totalForm.CreatedOn = "";
        totalForm.SourceName = "";
        totalForm.LevelName = "";
        totalForm.Count = customerStatisticsMapper.getCustomerStatisticsSum(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        totalForm.DateData = dateList;
        list.add(totalForm);

        List<List<Object>> total = new ArrayList<>();
        for (CustomerStatisticsDetailOutputForm statisticsDetailOutputForm : list) {
            List<Object> newList = new ArrayList<>();
            newList.add(statisticsDetailOutputForm.Name);
            newList.add(statisticsDetailOutputForm.PhoneNumber);
            newList.add(statisticsDetailOutputForm.Code);
            newList.add(statisticsDetailOutputForm.EntityName);
            newList.add(statisticsDetailOutputForm.CreatedOn);
            newList.add(statisticsDetailOutputForm.SourceName);
            newList.add(statisticsDetailOutputForm.LevelName);
            newList.add(statisticsDetailOutputForm.TotalAmount);
            newList.add(statisticsDetailOutputForm.Count);

            for (String s : date) {

                BigDecimal amount = BigDecimal.ZERO;
                Integer count = 0;
                for (CustomerStatisticsDateDetailOutputForm detailOutputForm : statisticsDetailOutputForm.DateData) {
                    if (s.equals(detailOutputForm.Date)) {
                        amount = detailOutputForm.Amount;
                        count = detailOutputForm.Count;
                        continue;
                    }
                }
                newList.add(amount);
                newList.add(count);
            }
            total.add(newList);
        }

        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("客户消费月度统计", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream()).head(head(date)).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("客户消费月度统计").doWrite(total);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }


        return null;
    }

    public BaseOutput easyExcelDisPlayPhoneMonthlySaleStatistics(CustomerStatisticsQueryForm form, HttpServletResponse response) {
        String searchDate = form.SearchDate + "-" + "01";
        LocalDate time = LocalDate.parse(searchDate);
        String startDate = time.with(TemporalAdjusters.firstDayOfMonth()).toString();
        String endDate = time.with(TemporalAdjusters.lastDayOfMonth()).toString();

        List<CustomerStatisticsDateDetailOutputForm> dateList = customerStatisticsMapper.getCustomerStatisticsSumMonth(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        List<String> date = dateList.stream().map(i -> i.Date).collect(Collectors.toList());

        List<CustomerStatisticsDetailOutputForm> list = customerStatisticsMapper.getCustomerStatisticsExcelDisPlayPhone(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        CustomerStatisticsDetailOutputForm totalForm = new CustomerStatisticsDetailOutputForm();
        totalForm.Name = "合计";
        totalForm.PhoneNumber = "";
        totalForm.Code = "";
        totalForm.EntityName = "";
        totalForm.CreatedOn = "";
        totalForm.SourceName = "";
        totalForm.LevelName = "";
        totalForm.Count = customerStatisticsMapper.getCustomerStatisticsSum(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        totalForm.DateData = dateList;
        list.add(totalForm);

        List<List<Object>> total = new ArrayList<>();
        for (CustomerStatisticsDetailOutputForm statisticsDetailOutputForm : list) {
            List<Object> newList = new ArrayList<>();
            newList.add(statisticsDetailOutputForm.Name);
            newList.add(statisticsDetailOutputForm.PhoneNumber);
            newList.add(statisticsDetailOutputForm.Code);
            newList.add(statisticsDetailOutputForm.EntityName);
            newList.add(statisticsDetailOutputForm.CreatedOn);
            newList.add(statisticsDetailOutputForm.SourceName);
            newList.add(statisticsDetailOutputForm.LevelName);
            newList.add(statisticsDetailOutputForm.TotalAmount);
            newList.add(statisticsDetailOutputForm.Count);

            for (String s : date) {

                BigDecimal amount = BigDecimal.ZERO;
                Integer count = 0;
                for (CustomerStatisticsDateDetailOutputForm detailOutputForm : statisticsDetailOutputForm.DateData) {
                    if (s.equals(detailOutputForm.Date)) {
                        amount = detailOutputForm.Amount;
                        count = detailOutputForm.Count;
                        continue;
                    }
                }
                newList.add(amount);
                newList.add(count);
            }
            total.add(newList);
        }

        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("客户消费月度统计", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream()).head(head(date)).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("客户消费月度统计").doWrite(total);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }


        return null;
    }

    private List<List<String>> head(List<String> Head) {
        List<List<String>> list = new ArrayList<List<String>>();
        List<String> head0 = new ArrayList<>();
        head0.add("客户姓名");
        list.add(head0);
        List<String> head1 = new ArrayList<>();
        head1.add("手机号");
        list.add(head1);
        List<String> head2 = new ArrayList<>();
        head2.add("客户编号");
        list.add(head2);
        List<String> head3 = new ArrayList<>();
        head3.add("所属组织");
        list.add(head3);
        List<String> head4 = new ArrayList<>();
        head4.add("注册日期");
        list.add(head4);
        List<String> head5 = new ArrayList<>();
        head5.add("信息来源");
        list.add(head5);
        List<String> head6 = new ArrayList<>();
        head6.add("会员等级");
        list.add(head6);
        List<String> head7 = new ArrayList<>();
        head7.add("合计金额");
        list.add(head7);
        List<String> head8 = new ArrayList<>();
        head8.add("合计次数");
        list.add(head8);


        String[] refundSale = {"金额", "到店"};
        List<String> head9 = new ArrayList<>();
        for (String a : Head) {
            for (String s : refundSale) {
                head9 = new ArrayList<>();
                head9.add(a);
                head9.add(s);
                list.add(head9);
            }
        }


        return list;
    }

    private List<List<String>> headYear(List<String> Head) {
        List<List<String>> list = new ArrayList<List<String>>();
        List<String> head0 = new ArrayList<>();
        head0.add("客户姓名");
        list.add(head0);
        List<String> head1 = new ArrayList<>();
        head1.add("手机号");
        list.add(head1);
        List<String> head2 = new ArrayList<>();
        head2.add("客户编号");
        list.add(head2);
        List<String> head3 = new ArrayList<>();
        head3.add("所属组织");
        list.add(head3);
        List<String> head4 = new ArrayList<>();
        head4.add("注册日期");
        list.add(head4);
        List<String> head5 = new ArrayList<>();
        head5.add("信息来源");
        list.add(head5);
        List<String> head6 = new ArrayList<>();
        head6.add("会员等级");
        list.add(head6);
        List<String> head7 = new ArrayList<>();
        head7.add("合计金额");
        list.add(head7);
        List<String> head8 = new ArrayList<>();
        head8.add("合计次数");
        list.add(head8);


        String[] refundSale = {"金额", "次数"};
        List<String> head9 = new ArrayList<>();
        for (String a : Head) {
            for (String s : refundSale) {
                head9 = new ArrayList<>();
                head9.add(a);
                head9.add(s);
                list.add(head9);
            }
        }


        return list;
    }


    public BaseOutput yearSaleStatistics(CustomerStatisticsQueryForm form) {
        String searchDate = form.SearchDate + "-01-01";
        LocalDate time = LocalDate.parse(searchDate);
        String startDate = time.with(TemporalAdjusters.firstDayOfYear()).toString();
        String endDate = time.with(TemporalAdjusters.lastDayOfYear()).toString();

        CustomerStatisticsOutputForm statisticsOutputForm = new CustomerStatisticsOutputForm();

        CustomerStatisticsSumOutputForm customerStatisticsSumOutputForm = new CustomerStatisticsSumOutputForm();
        customerStatisticsSumOutputForm.Count = customerStatisticsMapper.getCustomerStatisticsSumYear(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        customerStatisticsSumOutputForm.Name = "合计";
        customerStatisticsSumOutputForm.DateData = customerStatisticsMapper.getCustomerStatisticsSumYearDateData(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);

        PageHelper.startPage(form.PageNum, form.getPageSize());
        statisticsOutputForm.Detail = BasePageInfo.success(customerStatisticsMapper.getCustomerStatisticsYear(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate));
        statisticsOutputForm.SumOutputForm = customerStatisticsSumOutputForm;
        return BaseOutputForm.success(statisticsOutputForm);


    }

    public BaseOutput easyExcelYearSaleStatistics(CustomerStatisticsQueryForm form, HttpServletResponse response) {
        String searchDate = form.SearchDate + "-01-01";
        LocalDate time = LocalDate.parse(searchDate);
        String startDate = time.with(TemporalAdjusters.firstDayOfYear()).toString();
        String endDate = time.with(TemporalAdjusters.lastDayOfYear()).toString();

        List<CustomerStatisticsDateDetailOutputForm> dateList = customerStatisticsMapper.getCustomerStatisticsSumYearDateData(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        List<String> date = dateList.stream().map(i -> i.Date).collect(Collectors.toList());

        List<CustomerStatisticsDetailOutputForm> list = customerStatisticsMapper.getCustomerStatisticsYearExcel(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        CustomerStatisticsDetailOutputForm totalForm = new CustomerStatisticsDetailOutputForm();
        totalForm.Name = "合计";
        totalForm.PhoneNumber = "";
        totalForm.Code = "";
        totalForm.EntityName = "";
        totalForm.CreatedOn = "";
        totalForm.SourceName = "";
        totalForm.LevelName = "";
        totalForm.Count = customerStatisticsMapper.getCustomerStatisticsSumYear(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        totalForm.DateData = dateList;
        list.add(totalForm);

        List<List<Object>> total = new ArrayList<>();
        for (CustomerStatisticsDetailOutputForm statisticsDetailOutputForm : list) {
            List<Object> newList = new ArrayList<>();
            newList.add(statisticsDetailOutputForm.Name);
            newList.add(statisticsDetailOutputForm.PhoneNumber);
            newList.add(statisticsDetailOutputForm.Code);
            newList.add(statisticsDetailOutputForm.EntityName);
            newList.add(statisticsDetailOutputForm.CreatedOn);
            newList.add(statisticsDetailOutputForm.SourceName);
            newList.add(statisticsDetailOutputForm.LevelName);
            newList.add(statisticsDetailOutputForm.TotalAmount);
            newList.add(statisticsDetailOutputForm.Count);

            for (String s : date) {

                BigDecimal amount = BigDecimal.ZERO;
                Integer count = 0;
                for (CustomerStatisticsDateDetailOutputForm detailOutputForm : statisticsDetailOutputForm.DateData) {
                    if (s.equals(detailOutputForm.Date)) {
                        amount = detailOutputForm.Amount;
                        count = detailOutputForm.Count;
                        continue;
                    }
                }
                newList.add(amount);
                newList.add(count);
            }
            total.add(newList);
        }

        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("客户消费年度统计", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream()).head(headYear(date)).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("客户消费年度统计").doWrite(total);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }


        return null;
    }

    public BaseOutput easyExcelDisPlayPhoneYearSaleStatistics(CustomerStatisticsQueryForm form, HttpServletResponse response) {
        String searchDate = form.SearchDate + "-01-01";
        LocalDate time = LocalDate.parse(searchDate);
        String startDate = time.with(TemporalAdjusters.firstDayOfYear()).toString();
        String endDate = time.with(TemporalAdjusters.lastDayOfYear()).toString();

        List<CustomerStatisticsDateDetailOutputForm> dateList = customerStatisticsMapper.getCustomerStatisticsSumYearDateData(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        List<String> date = dateList.stream().map(i -> i.Date).collect(Collectors.toList());

        List<CustomerStatisticsDetailOutputForm> list = customerStatisticsMapper.getCustomerStatisticsYearExcelDisPlayPhone(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        CustomerStatisticsDetailOutputForm totalForm = new CustomerStatisticsDetailOutputForm();
        totalForm.Name = "合计";
        totalForm.PhoneNumber = "";
        totalForm.Code = "";
        totalForm.EntityName = "";
        totalForm.CreatedOn = "";
        totalForm.SourceName = "";
        totalForm.LevelName = "";
        totalForm.Count = customerStatisticsMapper.getCustomerStatisticsSumYear(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        totalForm.DateData = dateList;
        list.add(totalForm);

        List<List<Object>> total = new ArrayList<>();
        for (CustomerStatisticsDetailOutputForm statisticsDetailOutputForm : list) {
            List<Object> newList = new ArrayList<>();
            newList.add(statisticsDetailOutputForm.Name);
            newList.add(statisticsDetailOutputForm.PhoneNumber);
            newList.add(statisticsDetailOutputForm.Code);
            newList.add(statisticsDetailOutputForm.EntityName);
            newList.add(statisticsDetailOutputForm.CreatedOn);
            newList.add(statisticsDetailOutputForm.SourceName);
            newList.add(statisticsDetailOutputForm.LevelName);
            newList.add(statisticsDetailOutputForm.TotalAmount);
            newList.add(statisticsDetailOutputForm.Count);

            for (String s : date) {

                BigDecimal amount = BigDecimal.ZERO;
                Integer count = 0;
                for (CustomerStatisticsDateDetailOutputForm detailOutputForm : statisticsDetailOutputForm.DateData) {
                    if (s.equals(detailOutputForm.Date)) {
                        amount = detailOutputForm.Amount;
                        count = detailOutputForm.Count;
                        continue;
                    }
                }
                newList.add(amount);
                newList.add(count);
            }
            total.add(newList);
        }

        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("客户消费年度统计", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream()).head(headYear(date)).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("客户消费年度统计").doWrite(total);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }


        return null;
    }

    public BaseOutput monthlyTreatStatistics(CustomerStatisticsQueryForm form) {
        String searchDate = form.SearchDate + "-" + "01";
        LocalDate time = LocalDate.parse(searchDate);
        String startDate = time.with(TemporalAdjusters.firstDayOfMonth()).toString();
        String endDate = time.with(TemporalAdjusters.lastDayOfMonth()).toString();

        CustomerStatisticsOutputForm statisticsOutputForm = new CustomerStatisticsOutputForm();

        CustomerStatisticsSumOutputForm customerStatisticsSumOutputForm = new CustomerStatisticsSumOutputForm();
        customerStatisticsSumOutputForm.Count = customerStatisticsMapper.getCustomerTreatStatisticsSum(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        customerStatisticsSumOutputForm.Name = "合计";
        customerStatisticsSumOutputForm.DateData = customerStatisticsMapper.getCustomerTreatStatisticsSumMonth(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);

        PageHelper.startPage(form.PageNum, form.getPageSize());
        statisticsOutputForm.Detail = BasePageInfo.success(customerStatisticsMapper.getCustomerTreatStatistics(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate));
        statisticsOutputForm.SumOutputForm = customerStatisticsSumOutputForm;
        return BaseOutputForm.success(statisticsOutputForm);

    }

    public BaseOutput easyExcelMonthlyTreatStatistics(CustomerStatisticsQueryForm form, HttpServletResponse response) {
        String searchDate = form.SearchDate + "-" + "01";
        LocalDate time = LocalDate.parse(searchDate);
        String startDate = time.with(TemporalAdjusters.firstDayOfMonth()).toString();
        String endDate = time.with(TemporalAdjusters.lastDayOfMonth()).toString();

        List<CustomerStatisticsDateDetailOutputForm> dateList = customerStatisticsMapper.getCustomerTreatStatisticsSumMonth(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        List<String> date = dateList.stream().map(i -> i.Date).collect(Collectors.toList());

        List<CustomerStatisticsDetailOutputForm> list = customerStatisticsMapper.getCustomerTreatStatisticsExcel(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        CustomerStatisticsDetailOutputForm totalForm = new CustomerStatisticsDetailOutputForm();
        totalForm.Name = "合计";
        totalForm.PhoneNumber = "";
        totalForm.Code = "";
        totalForm.EntityName = "";
        totalForm.CreatedOn = "";
        totalForm.SourceName = "";
        totalForm.LevelName = "";
        totalForm.Count = customerStatisticsMapper.getCustomerTreatStatisticsSum(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        totalForm.DateData = dateList;
        list.add(totalForm);

        List<List<Object>> total = new ArrayList<>();
        for (CustomerStatisticsDetailOutputForm statisticsDetailOutputForm : list) {
            List<Object> newList = new ArrayList<>();
            newList.add(statisticsDetailOutputForm.Name);
            newList.add(statisticsDetailOutputForm.PhoneNumber);
            newList.add(statisticsDetailOutputForm.Code);
            newList.add(statisticsDetailOutputForm.EntityName);
            newList.add(statisticsDetailOutputForm.CreatedOn);
            newList.add(statisticsDetailOutputForm.SourceName);
            newList.add(statisticsDetailOutputForm.LevelName);
            newList.add(statisticsDetailOutputForm.TotalAmount);
            newList.add(statisticsDetailOutputForm.Count);

            for (String s : date) {

                BigDecimal amount = BigDecimal.ZERO;
                Integer count = 0;
                for (CustomerStatisticsDateDetailOutputForm detailOutputForm : statisticsDetailOutputForm.DateData) {
                    if (s.equals(detailOutputForm.Date)) {
                        amount = detailOutputForm.Amount;
                        count = detailOutputForm.Count;
                        continue;
                    }
                }
                newList.add(amount);
                newList.add(count);
            }
            total.add(newList);
        }

        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("客户消耗项目月度统计", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream()).head(head(date)).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("客户消耗项目月度统计").doWrite(total);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }


        return null;
    }

    public BaseOutput easyExcelDisPlayPhoneMonthlyTreatStatistics(CustomerStatisticsQueryForm form, HttpServletResponse response) {
        String searchDate = form.SearchDate + "-" + "01";
        LocalDate time = LocalDate.parse(searchDate);
        String startDate = time.with(TemporalAdjusters.firstDayOfMonth()).toString();
        String endDate = time.with(TemporalAdjusters.lastDayOfMonth()).toString();

        List<CustomerStatisticsDateDetailOutputForm> dateList = customerStatisticsMapper.getCustomerTreatStatisticsSumMonth(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        List<String> date = dateList.stream().map(i -> i.Date).collect(Collectors.toList());

        List<CustomerStatisticsDetailOutputForm> list = customerStatisticsMapper.getCustomerTreatStatisticsExcelDisPlayPhone(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        CustomerStatisticsDetailOutputForm totalForm = new CustomerStatisticsDetailOutputForm();
        totalForm.Name = "合计";
        totalForm.PhoneNumber = "";
        totalForm.Code = "";
        totalForm.EntityName = "";
        totalForm.CreatedOn = "";
        totalForm.SourceName = "";
        totalForm.LevelName = "";
        totalForm.Count = customerStatisticsMapper.getCustomerTreatStatisticsSum(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        totalForm.DateData = dateList;
        list.add(totalForm);

        List<List<Object>> total = new ArrayList<>();
        for (CustomerStatisticsDetailOutputForm statisticsDetailOutputForm : list) {
            List<Object> newList = new ArrayList<>();
            newList.add(statisticsDetailOutputForm.Name);
            newList.add(statisticsDetailOutputForm.PhoneNumber);
            newList.add(statisticsDetailOutputForm.Code);
            newList.add(statisticsDetailOutputForm.EntityName);
            newList.add(statisticsDetailOutputForm.CreatedOn);
            newList.add(statisticsDetailOutputForm.SourceName);
            newList.add(statisticsDetailOutputForm.LevelName);
            newList.add(statisticsDetailOutputForm.TotalAmount);
            newList.add(statisticsDetailOutputForm.Count);

            for (String s : date) {

                BigDecimal amount = BigDecimal.ZERO;
                Integer count = 0;
                for (CustomerStatisticsDateDetailOutputForm detailOutputForm : statisticsDetailOutputForm.DateData) {
                    if (s.equals(detailOutputForm.Date)) {
                        amount = detailOutputForm.Amount;
                        count = detailOutputForm.Count;
                        continue;
                    }
                }
                newList.add(amount);
                newList.add(count);
            }
            total.add(newList);
        }

        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("客户消耗项目月度统计", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream()).head(head(date)).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("客户消耗项目月度统计").doWrite(total);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }


        return null;
    }

    public BaseOutput yearTreatStatistics(CustomerStatisticsQueryForm form) {
        String searchDate = form.SearchDate + "-01-01";
        LocalDate time = LocalDate.parse(searchDate);
        String startDate = time.with(TemporalAdjusters.firstDayOfYear()).toString();
        String endDate = time.with(TemporalAdjusters.lastDayOfYear()).toString();

        CustomerStatisticsOutputForm statisticsOutputForm = new CustomerStatisticsOutputForm();

        CustomerStatisticsSumOutputForm customerStatisticsSumOutputForm = new CustomerStatisticsSumOutputForm();
        customerStatisticsSumOutputForm.Count = customerStatisticsMapper.getCustomerTreatStatisticsSumYear(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        customerStatisticsSumOutputForm.Name = "合计";
        customerStatisticsSumOutputForm.DateData = customerStatisticsMapper.getCustomerTreatStatisticsSumYearDateData(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);

        PageHelper.startPage(form.PageNum, form.getPageSize());
        statisticsOutputForm.Detail = BasePageInfo.success(customerStatisticsMapper.getCustomerTreatStatisticsYear(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate));
        statisticsOutputForm.SumOutputForm = customerStatisticsSumOutputForm;
        return BaseOutputForm.success(statisticsOutputForm);


    }

    public BaseOutput yearTreatStatisticsExcel(CustomerStatisticsQueryForm form, HttpServletResponse response) {
        String searchDate = form.SearchDate + "-01-01";
        LocalDate time = LocalDate.parse(searchDate);
        String startDate = time.with(TemporalAdjusters.firstDayOfYear()).toString();
        String endDate = time.with(TemporalAdjusters.lastDayOfYear()).toString();


        List<CustomerStatisticsDateDetailOutputForm> dateList = customerStatisticsMapper.getCustomerTreatStatisticsSumYearDateData(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        List<String> date = dateList.stream().map(i -> i.Date).collect(Collectors.toList());

        List<CustomerStatisticsDetailOutputForm> list = customerStatisticsMapper.getCustomerTreatStatisticsYearExcel(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        CustomerStatisticsDetailOutputForm totalForm = new CustomerStatisticsDetailOutputForm();
        totalForm.Name = "合计";
        totalForm.PhoneNumber = "";
        totalForm.Code = "";
        totalForm.EntityName = "";
        totalForm.CreatedOn = "";
        totalForm.SourceName = "";
        totalForm.LevelName = "";
        totalForm.Count = customerStatisticsMapper.getCustomerTreatStatisticsSumYear(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        totalForm.DateData = dateList;
        list.add(totalForm);

        List<List<Object>> total = new ArrayList<>();
        for (CustomerStatisticsDetailOutputForm statisticsDetailOutputForm : list) {
            List<Object> newList = new ArrayList<>();
            newList.add(statisticsDetailOutputForm.Name);
            newList.add(statisticsDetailOutputForm.PhoneNumber);
            newList.add(statisticsDetailOutputForm.Code);
            newList.add(statisticsDetailOutputForm.EntityName);
            newList.add(statisticsDetailOutputForm.CreatedOn);
            newList.add(statisticsDetailOutputForm.SourceName);
            newList.add(statisticsDetailOutputForm.LevelName);
            newList.add(statisticsDetailOutputForm.TotalAmount);
            newList.add(statisticsDetailOutputForm.Count);

            for (String s : date) {

                BigDecimal amount = BigDecimal.ZERO;
                Integer count = 0;
                for (CustomerStatisticsDateDetailOutputForm detailOutputForm : statisticsDetailOutputForm.DateData) {
                    if (s.equals(detailOutputForm.Date)) {
                        amount = detailOutputForm.Amount;
                        count = detailOutputForm.Count;
                        continue;
                    }
                }
                newList.add(amount);
                newList.add(count);
            }
            total.add(newList);
        }

        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("客户消耗项目年度统计", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream()).head(headYear(date)).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("客户消耗项目年度统计").doWrite(total);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }


        return null;
    }

    public BaseOutput yearTreatStatisticsExcelDisPlayPhone(CustomerStatisticsQueryForm form, HttpServletResponse response) {
        String searchDate = form.SearchDate + "-01-01";
        LocalDate time = LocalDate.parse(searchDate);
        String startDate = time.with(TemporalAdjusters.firstDayOfYear()).toString();
        String endDate = time.with(TemporalAdjusters.lastDayOfYear()).toString();


        List<CustomerStatisticsDateDetailOutputForm> dateList = customerStatisticsMapper.getCustomerTreatStatisticsSumYearDateData(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        List<String> date = dateList.stream().map(i -> i.Date).collect(Collectors.toList());

        List<CustomerStatisticsDetailOutputForm> list = customerStatisticsMapper.getCustomerTreatStatisticsYearExcelDisPlayPhone(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        CustomerStatisticsDetailOutputForm totalForm = new CustomerStatisticsDetailOutputForm();
        totalForm.Name = "合计";
        totalForm.PhoneNumber = "";
        totalForm.Code = "";
        totalForm.EntityName = "";
        totalForm.CreatedOn = "";
        totalForm.SourceName = "";
        totalForm.LevelName = "";
        totalForm.Count = customerStatisticsMapper.getCustomerTreatStatisticsSumYear(SystemUserUtil.getSystemUserID(), form.EntityID, form.Name, form.SourceID, form.LevelID, form.CreatedOnStartDate, form.CreatedOnEndDate, startDate, endDate);
        totalForm.DateData = dateList;
        list.add(totalForm);

        List<List<Object>> total = new ArrayList<>();
        for (CustomerStatisticsDetailOutputForm statisticsDetailOutputForm : list) {
            List<Object> newList = new ArrayList<>();
            newList.add(statisticsDetailOutputForm.Name);
            newList.add(statisticsDetailOutputForm.PhoneNumber);
            newList.add(statisticsDetailOutputForm.Code);
            newList.add(statisticsDetailOutputForm.EntityName);
            newList.add(statisticsDetailOutputForm.CreatedOn);
            newList.add(statisticsDetailOutputForm.SourceName);
            newList.add(statisticsDetailOutputForm.LevelName);
            newList.add(statisticsDetailOutputForm.TotalAmount);
            newList.add(statisticsDetailOutputForm.Count);

            for (String s : date) {

                BigDecimal amount = BigDecimal.ZERO;
                Integer count = 0;
                for (CustomerStatisticsDateDetailOutputForm detailOutputForm : statisticsDetailOutputForm.DateData) {
                    if (s.equals(detailOutputForm.Date)) {
                        amount = detailOutputForm.Amount;
                        count = detailOutputForm.Count;
                        continue;
                    }
                }
                newList.add(amount);
                newList.add(count);
            }
            total.add(newList);
        }

        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("客户消耗项目年度统计", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream()).head(headYear(date)).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("客户消耗项目年度统计").doWrite(total);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }


        return null;
    }
}
