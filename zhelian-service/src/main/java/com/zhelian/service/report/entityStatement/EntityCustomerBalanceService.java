package com.zhelian.service.report.entityStatement;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.report.EntityCustomerBalanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.report.form.input.EntityTradingQueryForm;
import com.zhelian.model.report.form.output.EntityCustomerBalanceDetailOutputEasyExcelForm;
import com.zhelian.model.report.form.output.EntityCustomerBalanceOutputForm;
import com.zhelian.model.report.form.output.EntityCustomerBalanceSumOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

@Service
public class EntityCustomerBalanceService {

    @Autowired
    EntityCustomerBalanceMapper entityCustomerBalanceMapper;


    public BaseOutput entityCustomerBalanceStatement(EntityTradingQueryForm form) {

        EntityCustomerBalanceOutputForm entityCustomerBalanceOutputForm = new EntityCustomerBalanceOutputForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        entityCustomerBalanceOutputForm.Detail = BasePageInfo.success(entityCustomerBalanceMapper.getEntityCustomerBalanceStatement(SystemUserUtil.getSystemUserID(), form.EntityID));
        entityCustomerBalanceOutputForm.SumOutputForm = entityCustomerBalanceMapper.getEntityCustomerBalanceStatementSum(SystemUserUtil.getSystemUserID(), form.EntityID);
        return BaseOutputForm.success(entityCustomerBalanceOutputForm);
    }

    public BaseOutput easyExcelEntityCustomerBalanceStatement(EntityTradingQueryForm form, HttpServletResponse response) {
        List<EntityCustomerBalanceDetailOutputEasyExcelForm> list = entityCustomerBalanceMapper.getEntityCustomerBalanceStatementEasyExcel(SystemUserUtil.getSystemUserID(), form.EntityID);
        EntityCustomerBalanceSumOutputForm entitySaleDetailAndSumStatementForm = entityCustomerBalanceMapper.getEntityCustomerBalanceStatementSum(SystemUserUtil.getSystemUserID(), form.EntityID);
        if (entitySaleDetailAndSumStatementForm != null) {
            EntityCustomerBalanceDetailOutputEasyExcelForm entitySaleDetailStatementForm = new EntityCustomerBalanceDetailOutputEasyExcelForm();
            entitySaleDetailStatementForm.entityName = "合计";
            entitySaleDetailStatementForm.projectValidSheet = entitySaleDetailAndSumStatementForm.ProjectValidSheet;
            entitySaleDetailStatementForm.projectBalance = entitySaleDetailAndSumStatementForm.ProjectBalance;
            entitySaleDetailStatementForm.projectBalanceAmount = entitySaleDetailAndSumStatementForm.ProjectBalanceAmount;
            entitySaleDetailStatementForm.projectBalanceLargessAmount = entitySaleDetailAndSumStatementForm.ProjectBalanceLargessAmount;
            entitySaleDetailStatementForm.largessProjectValidSheet = entitySaleDetailAndSumStatementForm.LargessProjectValidSheet;
            entitySaleDetailStatementForm.largessProjectBalance = entitySaleDetailAndSumStatementForm.LargessProjectBalance;
            entitySaleDetailStatementForm.largessProjectBalanceLargessAmount = entitySaleDetailAndSumStatementForm.LargessProjectBalanceLargessAmount;
            entitySaleDetailStatementForm.productValidSheet = entitySaleDetailAndSumStatementForm.ProductValidSheet;
            entitySaleDetailStatementForm.productBalance = entitySaleDetailAndSumStatementForm.ProductBalance;
            entitySaleDetailStatementForm.productBalanceAmount = entitySaleDetailAndSumStatementForm.ProductBalanceAmount;
            entitySaleDetailStatementForm.productBalanceLargessAmount = entitySaleDetailAndSumStatementForm.ProductBalanceLargessAmount;
            entitySaleDetailStatementForm.largessProductValidSheet = entitySaleDetailAndSumStatementForm.LargessProductValidSheet;
            entitySaleDetailStatementForm.largessProductBalance = entitySaleDetailAndSumStatementForm.LargessProductBalance;
            entitySaleDetailStatementForm.largessProductBalanceLargessAmount = entitySaleDetailAndSumStatementForm.LargessProductBalanceLargessAmount;
            entitySaleDetailStatementForm.generalCardValidSheet = entitySaleDetailAndSumStatementForm.GeneralCardValidSheet;
            entitySaleDetailStatementForm.generalCardBalance = entitySaleDetailAndSumStatementForm.GeneralCardBalance;
            entitySaleDetailStatementForm.generalCardBalanceAmount = entitySaleDetailAndSumStatementForm.GeneralCardBalanceAmount;
            entitySaleDetailStatementForm.generalCardBalanceLargessAmount = entitySaleDetailAndSumStatementForm.GeneralCardBalanceLargessAmount;
            entitySaleDetailStatementForm.largessGeneralCardValidSheet = entitySaleDetailAndSumStatementForm.LargessGeneralCardValidSheet;
            entitySaleDetailStatementForm.largessGeneralCardBalance = entitySaleDetailAndSumStatementForm.LargessGeneralCardBalance;
            entitySaleDetailStatementForm.largessGeneralCardBalanceLargessAmount = entitySaleDetailAndSumStatementForm.LargessGeneralCardBalanceLargessAmount;
            entitySaleDetailStatementForm.timeCardValidSheet = entitySaleDetailAndSumStatementForm.TimeCardValidSheet;
            entitySaleDetailStatementForm.timeCardLargessValidSheet = entitySaleDetailAndSumStatementForm.TimeCardLargessValidSheet;
            entitySaleDetailStatementForm.savingCardValidSheet = entitySaleDetailAndSumStatementForm.SavingCardValidSheet;
            entitySaleDetailStatementForm.savingCardBalanceAmount = entitySaleDetailAndSumStatementForm.SavingCardBalanceAmount;
            entitySaleDetailStatementForm.savingCardBalanceLargessAmount = entitySaleDetailAndSumStatementForm.SavingCardBalanceLargessAmount;
            list.add(entitySaleDetailStatementForm);
        }

        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("门店余量报表", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), EntityCustomerBalanceDetailOutputEasyExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("门店余量").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }


        return null;
    }
}
