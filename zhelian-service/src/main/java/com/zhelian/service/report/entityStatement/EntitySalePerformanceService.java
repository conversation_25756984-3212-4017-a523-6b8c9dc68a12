package com.zhelian.service.report.entityStatement;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.report.EntitySalePerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.report.form.input.EntitySalePerformanceQueryForm;
import com.zhelian.model.report.form.output.EntitySalePerformanceDetailOutputEasyExcelForm;
import com.zhelian.model.report.form.output.EntitySalePerformanceOutputForm;
import com.zhelian.model.report.form.output.EntitySalePerformanceSumOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;


@Service
public class EntitySalePerformanceService {

    @Autowired
    EntitySalePerformanceMapper entitySalePerformanceMapper;

    public BaseOutput entitySalePerformance(EntitySalePerformanceQueryForm form) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        EntitySalePerformanceOutputForm passengerFlowOutputForm = new EntitySalePerformanceOutputForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        passengerFlowOutputForm.Detail = BasePageInfo.success(entitySalePerformanceMapper.getEntitySalePerformance(SystemUserUtil.getSystemUserID(), form.EntityID, form.GoodsTypeName, form.CustomerName, StartDate, EndDate, form.BillType, form.CategoryID, form.BillEntityID, form.BillID,form.CustomerLevelID));
        passengerFlowOutputForm.SumOutputForm = entitySalePerformanceMapper.getEntitySalePerformanceSum(SystemUserUtil.getSystemUserID(), form.EntityID, form.GoodsTypeName, form.CustomerName, StartDate, EndDate, form.BillType, form.CategoryID, form.BillEntityID, form.BillID,form.CustomerLevelID);
        return BaseOutputForm.success(passengerFlowOutputForm);
    }

    public BaseOutput easyExcelEntitySalePerformance(EntitySalePerformanceQueryForm form, HttpServletResponse response) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<EntitySalePerformanceDetailOutputEasyExcelForm> list = entitySalePerformanceMapper.getEntitySalePerformanceEasyExcel(SystemUserUtil.getSystemUserID(), form.EntityID, form.GoodsTypeName, form.CustomerName, StartDate, EndDate, form.BillType, form.CategoryID, form.BillEntityID, form.BillID,form.CustomerLevelID);
        EntitySalePerformanceSumOutputForm entitySaleDetailAndSumStatementForm = entitySalePerformanceMapper.getEntitySalePerformanceSum(SystemUserUtil.getSystemUserID(), form.EntityID, form.GoodsTypeName, form.CustomerName, StartDate, EndDate, form.BillType, form.CategoryID, form.BillEntityID, form.BillID,form.CustomerLevelID);
        if (entitySaleDetailAndSumStatementForm != null) {
            EntitySalePerformanceDetailOutputEasyExcelForm entitySaleDetailStatementForm = new EntitySalePerformanceDetailOutputEasyExcelForm();
            entitySaleDetailStatementForm.entityName = "合计";
            entitySaleDetailStatementForm.saleBillID = "";
            entitySaleDetailStatementForm.billDate = "";
            entitySaleDetailStatementForm.billEntityName = "";
            entitySaleDetailStatementForm.billType = "";
            entitySaleDetailStatementForm.customerName = "";
            entitySaleDetailStatementForm.code = "";
            entitySaleDetailStatementForm.phoneNumber = "";
            entitySaleDetailStatementForm.goodName = "";
            entitySaleDetailStatementForm.categoryName = "";
            entitySaleDetailStatementForm.goodsTypeName = "";
            entitySaleDetailStatementForm.price = "";
            entitySaleDetailStatementForm.quantity = "";
            entitySaleDetailStatementForm.isLargess = "";
            entitySaleDetailStatementForm.preferentialAmount = entitySaleDetailAndSumStatementForm.PreferentialAmount;
            entitySaleDetailStatementForm.totalAmount = entitySaleDetailAndSumStatementForm.TotalAmount;
            entitySaleDetailStatementForm.arrearAmount = entitySaleDetailAndSumStatementForm.ArrearAmount;
            entitySaleDetailStatementForm.payPerformance = entitySaleDetailAndSumStatementForm.PayPerformance;
            entitySaleDetailStatementForm.savingCardPerformance = entitySaleDetailAndSumStatementForm.SavingCardPerformance;
            entitySaleDetailStatementForm.savingCardLargessPerformance = entitySaleDetailAndSumStatementForm.SavingCardLargessPerformance;

            list.add(entitySaleDetailStatementForm);
        }
        try {
            //response输出文件流
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("门店销售业绩明细", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), EntitySalePerformanceDetailOutputEasyExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("门店销售业绩明细").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return null;
    }
}
