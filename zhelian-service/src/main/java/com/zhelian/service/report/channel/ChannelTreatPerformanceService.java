package com.zhelian.service.report.channel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.report.ChannelTreatPerformanceDetailMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.report.form.input.ChannelTreatPerformanceDetailForm;
import com.zhelian.model.report.form.output.ChannelTreatPerformanceDetailAndSumStatementForm;
import com.zhelian.model.report.form.output.ChannelTreatPerformanceDetailStatementEasyExcelForm;
import com.zhelian.model.report.form.output.ChannelTreatPerformanceSumStatementForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.List;

@Service
public class ChannelTreatPerformanceService {
    @Autowired
    ChannelTreatPerformanceDetailMapper channelTreatPerformanceDetailMapper;

    public BaseOutput getChannelSalePerformanceDetail(ChannelTreatPerformanceDetailForm form) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        ChannelTreatPerformanceDetailAndSumStatementForm detailAndSumStatementForm = new ChannelTreatPerformanceDetailAndSumStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        detailAndSumStatementForm.channelTreatPerformanceDetailStatementFormBasePageInfo = BasePageInfo.success(channelTreatPerformanceDetailMapper.getChannelTreatPerformanceDetail(form.EntityID, StartDate, EndDate, form.TreatCardTypeName, form.GoodsName, form.ChannelName, form.BillID, form.ChannelTypeID,form.CustomerName));
        detailAndSumStatementForm.channelTreatPerformanceSumStatementForm = channelTreatPerformanceDetailMapper.getChannelTreatPerformanceSum(form.EntityID, StartDate, EndDate, form.TreatCardTypeName, form.GoodsName, form.ChannelName, form.BillID, form.ChannelTypeID,form.CustomerName);
        return BaseOutputForm.success(detailAndSumStatementForm);
    }

    public BaseOutput easyExcelChannelTreatPerformanceDetail(ChannelTreatPerformanceDetailForm form, HttpServletResponse response) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd");
        String StartDate = simpleDateFormat.format(form.StartDate);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(form.EndDate);
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        String EndDate = simpleDateFormat.format(calendar.getTime());

        List<ChannelTreatPerformanceDetailStatementEasyExcelForm> list = channelTreatPerformanceDetailMapper.getEasyExcelChannelTreatPerformanceDetail(form.EntityID, StartDate, EndDate, form.TreatCardTypeName, form.GoodsName, form.ChannelName, form.BillID, form.ChannelTypeID,form.CustomerName);
        ChannelTreatPerformanceSumStatementForm channelTreatPerformanceSumStatementForm = channelTreatPerformanceDetailMapper.getChannelTreatPerformanceSum(form.EntityID, StartDate, EndDate, form.TreatCardTypeName, form.GoodsName, form.ChannelName, form.BillID, form.ChannelTypeID,form.CustomerName);
        if (channelTreatPerformanceSumStatementForm != null) {
            ChannelTreatPerformanceDetailStatementEasyExcelForm statementForm = new ChannelTreatPerformanceDetailStatementEasyExcelForm();
            statementForm.treatBillID = "合计";
            statementForm.billDate = "";
            statementForm.billType = "";
            statementForm.entityName = "";
            statementForm.customerName = "";
            statementForm.phoneNumber = "";
            statementForm.code = "";
            statementForm.channelName = "";
            statementForm.channelType = "";
            statementForm.treatCardTypeName = "";
            statementForm.cardName = "";
            statementForm.categoryName = "";
            statementForm.goodName = "";
            statementForm.payPerformance = channelTreatPerformanceSumStatementForm.PayPerformance.doubleValue();
            statementForm.cardPerformance = channelTreatPerformanceSumStatementForm.CardPerformance.doubleValue();
            statementForm.cardLargessPerformance = channelTreatPerformanceSumStatementForm.CardLargessPerformance.doubleValue();
            statementForm.largessPerformance = channelTreatPerformanceSumStatementForm.LargessPerformance.doubleValue();
            list.add(statementForm);
        }

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("渠道消耗业绩提成明细", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), ChannelTreatPerformanceDetailStatementEasyExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("渠道消耗业绩提成明细").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }
}
