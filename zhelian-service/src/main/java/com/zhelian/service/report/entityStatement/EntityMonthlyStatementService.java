package com.zhelian.service.report.entityStatement;

import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.report.EntityMonthlyStatementMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.report.form.input.MonthlyStatementQueryForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

@Service
public class EntityMonthlyStatementService {

    @Autowired
    EntityMonthlyStatementMapper entityMonthlyStatementMapper;

    public BaseOutput getEntityMonthlyStatementPayOverview(MonthlyStatementQueryForm form) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        return BaseOutputForm.success(entityMonthlyStatementMapper.getEntityMonthlyStatementPayOverview(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate));
    }

    public BaseOutput getEntityMonthlyStatementSaleOverview(MonthlyStatementQueryForm form) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        return BaseOutputForm.success(entityMonthlyStatementMapper.getEntityMonthlyStatementSaleOverview(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate));
    }

    public BaseOutput getEntityMonthlyStatementTreatOverview(MonthlyStatementQueryForm form) {
        LocalDate localStartDate = form.StartDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localEndDate = form.EndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
        String StartDate = localStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String EndDate = localEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        return BaseOutputForm.success(entityMonthlyStatementMapper.getEntityMonthlyStatementTreatOverview(SystemUserUtil.getSystemUserID(), form.EntityID, StartDate, EndDate));
    }
}
