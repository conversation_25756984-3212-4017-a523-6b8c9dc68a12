package com.zhelian.service.khs.saleProductPerformance;


import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.SalePackageCardProductCategoryHandlerPerformanceMapper;
import com.zhelian.mapper.khs.SaleProductCategoryHandlerPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.khs.entity.SalePackageCardProductCategoryHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SalePackageCardProductCategoryHandlerPerformanceEntity;
import com.zhelian.model.khs.entity.SaleProductCategoryHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SaleProductCategoryHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleGoodCategoryHandlerPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleProductCategoryHandlerPerformanceService {

    @Autowired
    SaleProductCategoryHandlerPerformanceMapper saleProductCategoryHandlerPerformanceMapper;
    @Autowired
    SalePackageCardProductCategoryHandlerPerformanceMapper salePackageCardProductCategoryHandlerPerformanceMapper;


    public BaseOutput allSaleProductCategoryHandlerPerformance(SaleProductCategoryHandlerCommissionQueryForm form) {
        List<SaleGoodCategoryHandlerPerformanceOutputForm> list = saleProductCategoryHandlerPerformanceMapper.allSaleProductCategoryHandlerPerformance(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSalePackageCardProductCategoryHandlerPerformance(SaleProductCategoryHandlerCommissionQueryForm form) {
        List<SaleGoodCategoryHandlerPerformanceOutputForm> list = salePackageCardProductCategoryHandlerPerformanceMapper.allSalePackageCardProductCategoryHandlerPerformance(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleProductCategoryHandlerPerformance(SaleProductCategoryHandlerCommissionAddForm form) {
        saleProductCategoryHandlerPerformanceMapper.deleteSaleProductCategoryHandler(form.EntityID, form.CategoryID);
        saleProductCategoryHandlerPerformanceMapper.deleteSaleProductCategoryHandlerJobType(form.EntityID, form.CategoryID);
        List<SaleProductCategoryHandlerPerformanceEntity> listSaleProductCategoryHandlerPerformance = new ArrayList<>();
        List<SaleProductCategoryHandlerJobTypePerformanceEntity> listSaleProductCategoryHandlerJobTypePerformance = new ArrayList<>();
        if (form.ProductHandler != null && form.ProductHandler.size() > 0) {
            for (SaleProductCategoryHandlerAddForm saleProductCategoryHandlerAddForm : form.ProductHandler) {
                if (saleProductCategoryHandlerAddForm.PayRate != null ||
                        saleProductCategoryHandlerAddForm.SavingCardRate != null ||
                        saleProductCategoryHandlerAddForm.SavingCardLargessRate != null) {
                    SaleProductCategoryHandlerPerformanceEntity saleProductCategoryHandlerPerformanceEntity = new SaleProductCategoryHandlerPerformanceEntity();
                    saleProductCategoryHandlerPerformanceEntity.EntityID = form.EntityID;
                    saleProductCategoryHandlerPerformanceEntity.ProductCategoryID = form.CategoryID;
                    saleProductCategoryHandlerPerformanceEntity.ProductSaleHandlerID = saleProductCategoryHandlerAddForm.SaleHandlerID;
                    saleProductCategoryHandlerPerformanceEntity.PayRate = saleProductCategoryHandlerAddForm.PayRate;
                    saleProductCategoryHandlerPerformanceEntity.SavingCardRate = saleProductCategoryHandlerAddForm.SavingCardRate;
                    saleProductCategoryHandlerPerformanceEntity.SavingCardLargessRate = saleProductCategoryHandlerAddForm.SavingCardLargessRate;
                    listSaleProductCategoryHandlerPerformance.add(saleProductCategoryHandlerPerformanceEntity);
                }

                if (saleProductCategoryHandlerAddForm.HandlerJobTypeCommission != null && saleProductCategoryHandlerAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (SaleProductCategoryHandlerJobTypeCommissionAddForm saleProductCategoryHandlerJobTypeCommissionAddForm : saleProductCategoryHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleProductCategoryHandlerJobTypeCommissionAddForm.PayRate != null ||
                                saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardRate != null ||
                                saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null) {
                            SaleProductCategoryHandlerJobTypePerformanceEntity saleProductCategoryHandlerJobTypePerformanceEntity = new SaleProductCategoryHandlerJobTypePerformanceEntity();
                            saleProductCategoryHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            saleProductCategoryHandlerJobTypePerformanceEntity.ProductCategoryID = form.CategoryID;
                            saleProductCategoryHandlerJobTypePerformanceEntity.ProductSaleHandlerID = saleProductCategoryHandlerAddForm.SaleHandlerID;
                            saleProductCategoryHandlerJobTypePerformanceEntity.JobTypeID = saleProductCategoryHandlerJobTypeCommissionAddForm.JobTypeID;
                            saleProductCategoryHandlerJobTypePerformanceEntity.PayRate = saleProductCategoryHandlerJobTypeCommissionAddForm.PayRate;
                            saleProductCategoryHandlerJobTypePerformanceEntity.SavingCardRate = saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardRate;
                            saleProductCategoryHandlerJobTypePerformanceEntity.SavingCardLargessRate = saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            listSaleProductCategoryHandlerJobTypePerformance.add(saleProductCategoryHandlerJobTypePerformanceEntity);
                        }
                    }
                }
            }
        }


        salePackageCardProductCategoryHandlerPerformanceMapper.deleteSalePackageCardProductCategoryHandler(form.EntityID, form.CategoryID);
        salePackageCardProductCategoryHandlerPerformanceMapper.deleteSalePackageCardProductCategoryHandlerJobType(form.EntityID, form.CategoryID);
        List<SalePackageCardProductCategoryHandlerPerformanceEntity> listSalePackageCardProductCategoryHandlerPerformance = new ArrayList<>();
        List<SalePackageCardProductCategoryHandlerJobTypePerformanceEntity> listSalePackageCardProductCategoryHandlerJobTypePerformance = new ArrayList<>();
        if (form.PackageCardProductHandler != null && form.PackageCardProductHandler.size() > 0) {
            for (SaleProductCategoryHandlerAddForm saleProductCategoryHandlerAddForm : form.PackageCardProductHandler) {
                if (saleProductCategoryHandlerAddForm.PayRate != null ||
                        saleProductCategoryHandlerAddForm.SavingCardRate != null ||
                        saleProductCategoryHandlerAddForm.SavingCardLargessRate != null) {
                    SalePackageCardProductCategoryHandlerPerformanceEntity salePackageCardProductCategoryHandlerPerformanceEntity = new SalePackageCardProductCategoryHandlerPerformanceEntity();
                    salePackageCardProductCategoryHandlerPerformanceEntity.EntityID = form.EntityID;
                    salePackageCardProductCategoryHandlerPerformanceEntity.ProductCategoryID = form.CategoryID;
                    salePackageCardProductCategoryHandlerPerformanceEntity.PackageCardSaleHandlerID = saleProductCategoryHandlerAddForm.SaleHandlerID;
                    salePackageCardProductCategoryHandlerPerformanceEntity.PayRate = saleProductCategoryHandlerAddForm.PayRate;
                    salePackageCardProductCategoryHandlerPerformanceEntity.SavingCardRate = saleProductCategoryHandlerAddForm.SavingCardRate;
                    salePackageCardProductCategoryHandlerPerformanceEntity.SavingCardLargessRate = saleProductCategoryHandlerAddForm.SavingCardLargessRate;
                    listSalePackageCardProductCategoryHandlerPerformance.add(salePackageCardProductCategoryHandlerPerformanceEntity);
                }

                if (saleProductCategoryHandlerAddForm.HandlerJobTypeCommission != null && saleProductCategoryHandlerAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (SaleProductCategoryHandlerJobTypeCommissionAddForm saleProductCategoryHandlerJobTypeCommissionAddForm : saleProductCategoryHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleProductCategoryHandlerJobTypeCommissionAddForm.PayRate != null ||
                                saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardRate != null ||
                                saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate != null) {
                            SalePackageCardProductCategoryHandlerJobTypePerformanceEntity salePackageCardProductCategoryHandlerJobTypePerformanceEntity = new SalePackageCardProductCategoryHandlerJobTypePerformanceEntity();
                            salePackageCardProductCategoryHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            salePackageCardProductCategoryHandlerJobTypePerformanceEntity.ProductCategoryID = form.CategoryID;
                            salePackageCardProductCategoryHandlerJobTypePerformanceEntity.PackageCardSaleHandlerID = saleProductCategoryHandlerAddForm.SaleHandlerID;
                            salePackageCardProductCategoryHandlerJobTypePerformanceEntity.JobTypeID = saleProductCategoryHandlerJobTypeCommissionAddForm.JobTypeID;
                            salePackageCardProductCategoryHandlerJobTypePerformanceEntity.PayRate = saleProductCategoryHandlerJobTypeCommissionAddForm.PayRate;
                            salePackageCardProductCategoryHandlerJobTypePerformanceEntity.SavingCardRate = saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardRate;
                            salePackageCardProductCategoryHandlerJobTypePerformanceEntity.SavingCardLargessRate = saleProductCategoryHandlerJobTypeCommissionAddForm.SavingCardLargessRate;
                            listSalePackageCardProductCategoryHandlerJobTypePerformance.add(salePackageCardProductCategoryHandlerJobTypePerformanceEntity);
                        }
                    }
                }
            }
        }

        if (listSaleProductCategoryHandlerPerformance != null && listSaleProductCategoryHandlerPerformance.size() > 0) {
            List<List<SaleProductCategoryHandlerPerformanceEntity>> subs = Lists.partition(listSaleProductCategoryHandlerPerformance, 20);
            for (List<SaleProductCategoryHandlerPerformanceEntity> entityList : subs) {
                saleProductCategoryHandlerPerformanceMapper.updateSaleProductCategoryHandlerPerformance(entityList);
            }
        }
        if (listSaleProductCategoryHandlerJobTypePerformance != null && listSaleProductCategoryHandlerJobTypePerformance.size() > 0) {
            List<List<SaleProductCategoryHandlerJobTypePerformanceEntity>> subs = Lists.partition(listSaleProductCategoryHandlerJobTypePerformance, 20);
            for (List<SaleProductCategoryHandlerJobTypePerformanceEntity> entityList : subs) {
                saleProductCategoryHandlerPerformanceMapper.insertSaleProductCategoryHandlerJobTypePerformance(entityList);
            }
        }
        if (listSalePackageCardProductCategoryHandlerPerformance != null && listSalePackageCardProductCategoryHandlerPerformance.size() > 0) {
            List<List<SalePackageCardProductCategoryHandlerPerformanceEntity>> subs = Lists.partition(listSalePackageCardProductCategoryHandlerPerformance, 20);
            for (List<SalePackageCardProductCategoryHandlerPerformanceEntity> entityList : subs) {
                salePackageCardProductCategoryHandlerPerformanceMapper.updateSalePackageCardProductCategoryHandlerPerformance(entityList);
            }
        }
        if (listSalePackageCardProductCategoryHandlerJobTypePerformance != null && listSalePackageCardProductCategoryHandlerJobTypePerformance.size() > 0) {
            List<List<SalePackageCardProductCategoryHandlerJobTypePerformanceEntity>> subs = Lists.partition(listSalePackageCardProductCategoryHandlerJobTypePerformance, 20);
            for (List<SalePackageCardProductCategoryHandlerJobTypePerformanceEntity> entityList : subs) {
                salePackageCardProductCategoryHandlerPerformanceMapper.insertSalePackageCardProductCategoryHandlerJobTypePerformance(entityList);
            }
        }

        return BaseOutput.success();
    }


}