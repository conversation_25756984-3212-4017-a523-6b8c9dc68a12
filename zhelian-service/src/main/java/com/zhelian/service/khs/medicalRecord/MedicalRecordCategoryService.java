package com.zhelian.service.khs.medicalRecord;

import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.khs.MedicalRecordCategoryMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.khs.entity.MedicalRecordCategoryEntity;
import com.zhelian.model.khs.entity.MedicalRecordTemplateEntity;
import com.zhelian.model.khs.form.input.MedicalRecordCategoryAddForm;
import com.zhelian.model.khs.form.input.MedicalRecordCategoryMoveForm;
import com.zhelian.model.khs.form.input.MedicalRecordCategoryQueryForm;
import com.zhelian.model.khs.form.input.MedicalRecordCategoryUpdateForm;
import com.zhelian.model.khs.form.output.MedicalRecordCategoryOutForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

@Service
public class MedicalRecordCategoryService {

    @Autowired
    MedicalRecordCategoryMapper medicalRecordCategoryMapper;

    public BaseOutput list(MedicalRecordCategoryQueryForm form) {
        List<MedicalRecordCategoryOutForm> outForm = medicalRecordCategoryMapper.list(form.Name,form.Active);
        return BaseOutputForm.success(outForm);
    }

    @Transactional
    public BaseOutput create(MedicalRecordCategoryAddForm form) {
        Integer Sequence = medicalRecordCategoryMapper.getMaxSequence();
        MedicalRecordCategoryEntity entity = new MedicalRecordCategoryEntity();
        entity.Name = form.Name;
        entity.Active = true;
        entity.Sequence = Sequence != null ? Sequence + 1 : 1;
        medicalRecordCategoryMapper.create(entity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput update(MedicalRecordCategoryUpdateForm form) {

        if (!form.Active) {
            List<MedicalRecordTemplateEntity> templateEntityList = medicalRecordCategoryMapper.getMedicalRecordTemplateByCategoryID(form.ID);
            if (templateEntityList != null && templateEntityList.size() > 0) {
                return BaseOutput.success("分类下存在模版无法删除");
            }
        }

        MedicalRecordCategoryEntity entity = new MedicalRecordCategoryEntity();
        entity.ID = form.ID;
        entity.Name = form.Name;
        entity.Active = form.Active;
        medicalRecordCategoryMapper.update(entity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput move(MedicalRecordCategoryMoveForm form) {
        LinkedList<Integer> linkedList = medicalRecordCategoryMapper.getASCList(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<MedicalRecordCategoryEntity> list = new ArrayList<>();
        for (Integer ID : linkedList) {
            MedicalRecordCategoryEntity entity = new MedicalRecordCategoryEntity();
            entity.ID = ID;
            entity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(entity);
        }

        medicalRecordCategoryMapper.updateSequence(list);
        return BaseOutput.success();
    }
}
