package com.zhelian.service.khs.medicalRecordEntry;

import com.zhelian.mapper.khs.MedicalRecordEntryContentMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.khs.entity.MedicalRecordEntryContentEntity;
import com.zhelian.model.khs.form.input.MedicalRecordEntryContentAddForm;
import com.zhelian.model.khs.form.input.MedicalRecordEntryContentDeleteForm;
import com.zhelian.model.khs.form.input.MedicalRecordEntryContentUpdateForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class MedicalRecordEntryContentService {

    @Autowired
    MedicalRecordEntryContentMapper medicalRecordEntryContentMapper;

    @Transactional
    public BaseOutput create(MedicalRecordEntryContentAddForm form) {
        MedicalRecordEntryContentEntity entity = new MedicalRecordEntryContentEntity();
        entity.Name = form.Name;
        entity.MedicalRecordEntryLabelID = form.LabelID;
        medicalRecordEntryContentMapper.create(entity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput update(MedicalRecordEntryContentUpdateForm form) {
        MedicalRecordEntryContentEntity entity = new MedicalRecordEntryContentEntity();
        entity.ID = form.ID;
        entity.Name = form.Name;
        medicalRecordEntryContentMapper.update(entity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput delete(MedicalRecordEntryContentDeleteForm form) {
        medicalRecordEntryContentMapper.delete(form.ID);
        return BaseOutput.success();
    }
}
