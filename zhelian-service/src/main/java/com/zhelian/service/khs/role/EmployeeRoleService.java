package com.zhelian.service.khs.role;

import com.zhelian.core.redis.utils.RedisAuthenticationUtil;
import com.zhelian.mapper.khs.EmployeeRoleMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.khs.entity.EmployeeRoleEntity;
import com.zhelian.model.khs.form.input.EmployeeRoleBatchUpdateForm;
import com.zhelian.model.khs.form.input.EmployeeRoleQueryForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class EmployeeRoleService {

    @Autowired
    EmployeeRoleMapper employeeRoleMapper;


    public BaseOutput employeeRole(EmployeeRoleQueryForm form) {

        return BaseOutputForm.success(employeeRoleMapper.employeeRole(form.EmployeeID));
    }


    @Transactional
    public BaseOutput updateUserRoleBatch(EmployeeRoleBatchUpdateForm form) {

        employeeRoleMapper.deleteEmployeeRoleByEmployeeList(form.EmployeeID);

        if (form.RoleID != null && form.RoleID.size() > 0) {
            List<EmployeeRoleEntity> list = new ArrayList<>();
            for (String EmployeeID : form.EmployeeID) {
                RedisAuthenticationUtil.delete(EmployeeID);
                for (Integer RoleID : form.RoleID) {
                    EmployeeRoleEntity employeeRoleEntity = new EmployeeRoleEntity();
                    employeeRoleEntity.EmployeeID = EmployeeID;
                    employeeRoleEntity.RoleID = RoleID;
                    list.add(employeeRoleEntity);
                }
            }
            employeeRoleMapper.insertEmployeeRoleByList(list);
        }
        return BaseOutput.success();
    }
}
