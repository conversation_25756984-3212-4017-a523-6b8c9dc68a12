package com.zhelian.service.khs.saleProjectEntityPerformance;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.SaleProjectCategoryEntityPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionSchemeAddForm;
import com.zhelian.model.khs.entity.SaleGoodCategoryEntityPerformanceEntity;
import com.zhelian.model.khs.entity.SaleGoodEntityPerformanceSchemeEntity;
import com.zhelian.model.khs.form.input.SaleGoodCategoryAddForm;
import com.zhelian.model.khs.form.input.SaleGoodCategoryPerformanceAddForm;
import com.zhelian.model.khs.form.input.SaleGoodChildCategoryPerformanceAddForm;
import com.zhelian.model.khs.form.output.SaleGoodCategoryEntityPerformanceOutputForm;
import com.zhelian.model.khs.form.output.SaleGoodCategoryOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import static com.zhelian.core.utils.ListUtil.listToTree;


@Service
public class SaleProjectCategoryEntityPerformanceService {

    @Autowired
    SaleProjectCategoryEntityPerformanceMapper saleProjectCategoryEntityPerformanceMapper;


    public BaseOutput allSaleProjectCategoryEntityPerformance(SaleProductCommissionSchemeAddForm form) {
        SaleGoodCategoryOutputForm saleProjectCategoryOutputForm = saleProjectCategoryEntityPerformanceMapper.saleProjectCategoryEntityPerformance(form.EntityID);
        List<SaleGoodCategoryEntityPerformanceOutputForm> all = saleProjectCategoryEntityPerformanceMapper.allSaleProjectCategoryEntityPerformance(form.EntityID);
        JSONArray result = listToTree(JSONArray.parseArray(JSON.toJSONString(all, JSONWriter.Feature.WriteMapNullValue)), "CategoryID", "ParentID", "Child");
        saleProjectCategoryOutputForm.Category = result;
        return BaseOutputForm.success(saleProjectCategoryOutputForm);
    }

    @Transactional
    public BaseOutput updateSaleProjectCategoryEntityPerformance(SaleGoodCategoryAddForm form) {
        SaleGoodEntityPerformanceSchemeEntity saleProjectPerformanceSchemeEntity = new SaleGoodEntityPerformanceSchemeEntity();
        saleProjectPerformanceSchemeEntity.EntityID = form.EntityID;
        saleProjectPerformanceSchemeEntity.PayRate = form.PayRate;
        saleProjectPerformanceSchemeEntity.SavingCardRate = form.SavingCardRate;
        saleProjectPerformanceSchemeEntity.SavingCardLargessRate = form.SavingCardLargessRate;

        saleProjectCategoryEntityPerformanceMapper.updateSaleProjectEntityPerformanceScheme(saleProjectPerformanceSchemeEntity);

        saleProjectCategoryEntityPerformanceMapper.deleteSaleProjectCategoryEntityPerformanceEntity(form.EntityID);

        List<SaleGoodCategoryEntityPerformanceEntity> list = new ArrayList<>();
        if (form.Category != null && form.Category.size() > 0) {
            for (SaleGoodCategoryPerformanceAddForm saleProjectCategoryPerformanceAddForm : form.Category) {
                if (saleProjectCategoryPerformanceAddForm.PayRate != null ||
                        saleProjectCategoryPerformanceAddForm.SavingCardRate != null ||
                        saleProjectCategoryPerformanceAddForm.SavingCardLargessRate != null) {
                    SaleGoodCategoryEntityPerformanceEntity saleProjectCategoryPerformanceEntity = new SaleGoodCategoryEntityPerformanceEntity();
                    saleProjectCategoryPerformanceEntity.EntityID = form.EntityID;
                    saleProjectCategoryPerformanceEntity.GoodCategoryID = saleProjectCategoryPerformanceAddForm.CategoryID;
                    saleProjectCategoryPerformanceEntity.PayRate = saleProjectCategoryPerformanceAddForm.PayRate;
                    saleProjectCategoryPerformanceEntity.SavingCardRate = saleProjectCategoryPerformanceAddForm.SavingCardRate;
                    saleProjectCategoryPerformanceEntity.SavingCardLargessRate = saleProjectCategoryPerformanceAddForm.SavingCardLargessRate;
                    list.add(saleProjectCategoryPerformanceEntity);
                }
                if (saleProjectCategoryPerformanceAddForm.Child != null && saleProjectCategoryPerformanceAddForm.Child.size() > 0) {

                    for (SaleGoodChildCategoryPerformanceAddForm saleGoodChildCategoryCommissionAddForm : saleProjectCategoryPerformanceAddForm.Child) {
                        if (saleGoodChildCategoryCommissionAddForm.PayRate != null ||
                                saleGoodChildCategoryCommissionAddForm.SavingCardRate != null ||
                                saleGoodChildCategoryCommissionAddForm.SavingCardLargessRate != null) {
                            SaleGoodCategoryEntityPerformanceEntity saleGoodCategoryEntityPerformanceEntity = new SaleGoodCategoryEntityPerformanceEntity();
                            saleGoodCategoryEntityPerformanceEntity.EntityID = form.EntityID;
                            saleGoodCategoryEntityPerformanceEntity.GoodCategoryID = saleGoodChildCategoryCommissionAddForm.CategoryID;
                            saleGoodCategoryEntityPerformanceEntity.PayRate = saleGoodChildCategoryCommissionAddForm.PayRate;
                            saleGoodCategoryEntityPerformanceEntity.SavingCardRate = saleGoodChildCategoryCommissionAddForm.SavingCardRate;
                            saleGoodCategoryEntityPerformanceEntity.SavingCardLargessRate = saleGoodChildCategoryCommissionAddForm.SavingCardLargessRate;
                            list.add(saleGoodCategoryEntityPerformanceEntity);
                        }
                    }
                }
            }
        }

        if (list != null && list.size() > 0) {
            List<List<SaleGoodCategoryEntityPerformanceEntity>> subs = Lists.partition(list, 20);
            for (List<SaleGoodCategoryEntityPerformanceEntity> entityList : subs) {
                saleProjectCategoryEntityPerformanceMapper.insertSaleProjectCategoryEntityPerformanceEntity(entityList);
            }
        }
        return BaseOutput.success();
    }

}