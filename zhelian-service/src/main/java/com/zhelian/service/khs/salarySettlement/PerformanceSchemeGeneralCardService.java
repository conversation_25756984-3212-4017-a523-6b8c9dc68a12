package com.zhelian.service.khs.salarySettlement;


import com.zhelian.mapper.khs.PerformanceSchemeGeneralCardMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.khs.entity.SaleGeneralCardPerformanceEntity;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodAddForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodQueryForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodsAddForm;
import com.zhelian.model.khs.form.output.PerformanceSchemeGoodOutForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class PerformanceSchemeGeneralCardService {

    @Autowired
    PerformanceSchemeGeneralCardMapper performanceSchemeGeneralCardMapper;

    public BaseOutput all(PerformanceSchemeGoodQueryForm form) {
        List<PerformanceSchemeGoodOutForm> list = performanceSchemeGeneralCardMapper.getAllPerformanceSchemeGeneralCard(form.PerformanceSchemeID, form.CategoryID);
        return BaseOutputForm.success(list);

    }

    @Transactional
    public BaseOutput create(PerformanceSchemeGoodsAddForm form) {
        performanceSchemeGeneralCardMapper.deleteSaleGeneralCardPerformanceByPerformanceSchemeIDAndCategory(form.PerformanceSchemeID, form.CategoryID);
        List<SaleGeneralCardPerformanceEntity> list = new ArrayList<>();
        if (form.Good != null && form.Good.size() > 0) {
            for (PerformanceSchemeGoodAddForm performanceSchemeGoodAddForm : form.Good) {
                if (performanceSchemeGoodAddForm.PayPerformanceRate != null || performanceSchemeGoodAddForm.SavingCardPerformanceRate != null ||
                        performanceSchemeGoodAddForm.SavingCardPerformanceLargessRate != null) {
                    SaleGeneralCardPerformanceEntity saleGeneralCardPerformanceEntity = new SaleGeneralCardPerformanceEntity();
                    saleGeneralCardPerformanceEntity.PerformanceSchemeID = form.PerformanceSchemeID;
                    saleGeneralCardPerformanceEntity.GeneralCardID = performanceSchemeGoodAddForm.GoodID;
                    saleGeneralCardPerformanceEntity.PayPerformanceRate = performanceSchemeGoodAddForm.PayPerformanceRate;
                    saleGeneralCardPerformanceEntity.SavingCardPerformanceRate = performanceSchemeGoodAddForm.SavingCardPerformanceRate;
                    saleGeneralCardPerformanceEntity.SavingCardPerformanceLargessRate = performanceSchemeGoodAddForm.SavingCardPerformanceLargessRate;
                    list.add(saleGeneralCardPerformanceEntity);
                }
            }
            performanceSchemeGeneralCardMapper.insertSaleGeneralCardPerformance(list);
        }
        return BaseOutput.success();
    }
}
