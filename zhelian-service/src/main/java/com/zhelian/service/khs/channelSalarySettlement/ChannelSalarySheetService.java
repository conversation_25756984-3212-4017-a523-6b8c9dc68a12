package com.zhelian.service.khs.channelSalarySettlement;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.utils.DateTimeUtil;
import com.zhelian.mapper.khs.ChannelSalarySheetMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.khs.entity.ChannelSalarySettlementIntervalEntity;
import com.zhelian.model.khs.form.input.ChannelSalarySheetDetailQueryForm;
import com.zhelian.model.khs.form.input.ChannelSalarySheetQueryForm;
import com.zhelian.model.khs.form.input.SettlementIntervalAddForm;
import com.zhelian.model.khs.form.output.ChannelSalarySheetDetailOutForm;
import com.zhelian.model.khs.form.output.ChannelSalarySheetOutExcelForm;
import com.zhelian.model.khs.form.output.ChannelSalarySheetOutForm;
import com.zhelian.model.khs.form.output.SalarySettlementIntervalOutForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;


@Service
public class ChannelSalarySheetService {

    @Autowired
    ChannelSalarySheetMapper channelSalarySheetMapper;

    public BaseOutput createSettlementInterval(SettlementIntervalAddForm form) throws Exception {
        ChannelSalarySettlementIntervalEntity salarySettlementIntervalEntity = new ChannelSalarySettlementIntervalEntity();
        salarySettlementIntervalEntity.SettlementMonth = form.SettlementMonth;
        salarySettlementIntervalEntity.StartDate = DateTimeUtil.toDate(form.StartDate);
        salarySettlementIntervalEntity.EndDate = DateTimeUtil.toDate(form.EndDate);
        channelSalarySheetMapper.createSettlementInterval(salarySettlementIntervalEntity);
        return BaseOutput.success();
    }

    public BaseOutput allSettlementInterval() {
        List<SalarySettlementIntervalOutForm> list = channelSalarySheetMapper.allSettlementInterval();
        return BaseOutputForm.success(list);
    }

    public BaseOutput all(ChannelSalarySheetQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<ChannelSalarySheetOutForm> list = channelSalarySheetMapper.all(form.Name, form.ChannelSalarySettlementIntervalID, form.ParentID);
        return BasePageInfo.success(list);
    }

    public BaseOutput settlement(ChannelSalarySheetQueryForm form) {
        channelSalarySheetMapper.settlement(form.ChannelSalarySettlementIntervalID);
        return BaseOutput.success();
    }

    public BaseOutput excel(ChannelSalarySheetQueryForm form, HttpServletResponse response) {
        List<ChannelSalarySheetOutExcelForm> list = channelSalarySheetMapper.excelChannelSalary(form.Name, form.ChannelSalarySettlementIntervalID, form.ParentID);
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("渠道业绩提成明细", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), ChannelSalarySheetOutExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("渠道业绩提成明细").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    public BaseOutput detail(ChannelSalarySheetDetailQueryForm form) {

        List<ChannelSalarySheetDetailOutForm> list = channelSalarySheetMapper.detail(form.ChannelID, form.ChannelSalarySettlementIntervalID);
        return BaseOutputForm.success(list);
    }
}
