package com.zhelian.service.khs.saleTimeCardEntityPerformance;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.khs.SaleTimeCardCategoryEntityPerformanceMapper;
import com.zhelian.mapper.khs.SaleTimeCardEntityPerformanceMapper;
import com.zhelian.mapper.khs.SaleTimeCardEntityPerformanceSchemeMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCommissionSchemeDeleteForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCommissionSchemeQueryForm;
import com.zhelian.model.ibeauty.form.output.SaleTimeCardCommissionSchemeOutputForm;
import com.zhelian.model.khs.entity.SaleGoodEntityPerformanceSchemeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Service
public class SaleTimeCardEntityPerformanceSchemeService {

    @Autowired
    SaleTimeCardEntityPerformanceSchemeMapper saleTimeCardEntityPerformanceSchemeMapper;
    @Autowired
    SaleTimeCardCategoryEntityPerformanceMapper saleTimeCardCategoryEntityPerformanceMapper;
    @Autowired
    SaleTimeCardEntityPerformanceMapper saleTimeCardEntityPerformanceMapper;


    public BaseOutput saleTimeCardEntityPerformanceSchemeList(SaleTimeCardCommissionSchemeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleTimeCardCommissionSchemeOutputForm> list = saleTimeCardEntityPerformanceSchemeMapper.saleTimeCardEntityPerformanceSchemeList(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput createSaleTimeCardEntityPerformanceScheme(SaleTimeCardCommissionSchemeAddForm form) {
        Integer count = saleTimeCardEntityPerformanceSchemeMapper.getSaleTimeCardEntityPerformanceScheme(form.EntityID);
        if (count > 0)
            return BaseOutput.failed("组织单位已经存在");

        SaleGoodEntityPerformanceSchemeEntity saleTimeCardEntityPerformanceSchemeEntity = new SaleGoodEntityPerformanceSchemeEntity();
        saleTimeCardEntityPerformanceSchemeEntity.EntityID = form.EntityID;

        saleTimeCardEntityPerformanceSchemeMapper.createSaleTimeCardEntityPerformanceScheme(saleTimeCardEntityPerformanceSchemeEntity);
        return BaseOutput.success();

    }

    @Transactional
    public BaseOutput deleteSaleTimeCardEntityPerformanceScheme(SaleTimeCardCommissionSchemeDeleteForm form) {
        saleTimeCardEntityPerformanceSchemeMapper.deleteSaleTimeCardEntityPerformanceScheme(form.EntityID);
        saleTimeCardCategoryEntityPerformanceMapper.deleteSaleTimeCardCategoryEntityPerformanceEntity(form.EntityID);
        saleTimeCardEntityPerformanceMapper.deleteSaleTimeCardEntityPerformanceByEntityID(form.EntityID);

        return BaseOutput.success();
    }
}