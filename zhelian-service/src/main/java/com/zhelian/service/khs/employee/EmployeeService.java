package com.zhelian.service.khs.employee;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.redis.utils.RedisAuthenticationUtil;
import com.zhelian.core.utils.OSSClientUtil;
import com.zhelian.mapper.khs.EmployeeEntityMapper;
import com.zhelian.mapper.khs.EmployeeMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.form.input.QueryPageForm;
import com.zhelian.model.khs.entity.EmployeeEntity;
import com.zhelian.model.khs.entity.EmployeeEntityAddForm;
import com.zhelian.model.khs.entity.EmployeeEntityEntity;
import com.zhelian.model.khs.entity.EmployeeExtendEntity;
import com.zhelian.model.khs.form.input.*;
import com.zhelian.model.khs.form.output.BelongEmployeeListForm;
import com.zhelian.model.khs.form.output.EmployeeExportOutputForm;
import com.zhelian.model.khs.form.output.EmployeeListForm;
import com.zhelian.model.miniprogram.form.input.EmployeeAvatarAddForm;
import com.zhelian.model.miniprogram.form.input.EmployeeDetailQueryForm;
import com.zhelian.model.miniprogram.form.output.EmployeeDetailOutputForm;
import com.zhelian.model.miniprogram.form.output.EmployeeOutputForm;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

@Service
public class EmployeeService {

    @Autowired
    EmployeeMapper employeeMapper;
    @Autowired
    EmployeeEntityMapper employeeEntityMapper;


    /**
     * 功能描述:
     * <员工列表，带分页>
     *
     * @param: form
     * @return: com.zhelian.model.base.BasePageInfo
     * @exception:
     * @author: WangXianXian
     * @date: 2020/2/20 4:24 PM
     */
    public BaseOutput getEmpList(EmployeeQueryForm form) {

        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<EmployeeExtendEntity> list = employeeMapper.getEmpList(form);
        return BasePageInfo.success(list);
    }


    @Transactional
    public BaseOutput updateEmp(EmployeeUpdateForm form) {

        employeeMapper.updateEmp(form);

        employeeEntityMapper.deleteEmployeeEntityByEmployeeID(form.ID);

        List<EmployeeEntityEntity> list = new ArrayList<>();
        Assembly(list, form);

        employeeEntityMapper.insertEmployeeEntityList(list);

        if (!form.State) {
            RedisAuthenticationUtil.delete(form.ID);
        }
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput addEmp(EmployeeAddForm form) {

        EmployeeEntity employeeEntity = employeeMapper.getEmployeeByID(form.ID);

        if (employeeEntity != null)
            return BaseOutput.failed("员工已存在");

        employeeEntity = new EmployeeEntity();
        employeeEntity.Password = DigestUtils.md5Hex(form.ID);
        employeeEntity.ID = form.ID;
        employeeEntity.Name = form.Name;
        employeeEntity.Alias = form.Alias;
        employeeEntity.PhoneNumber = form.PhoneNumber;
        employeeEntity.JobID = form.JobID;
        employeeEntity.Gender = form.Gender;
        employeeEntity.IDNumber = form.IDNumber;
        employeeEntity.Birthdate = form.Birthdate;
        employeeEntity.Address = form.Address;
        employeeEntity.EntryDate = form.EntryDate;
        employeeEntity.TrialExpiryDate = form.TrialExpiryDate;
        employeeEntity.ContractStartDate = form.ContractStartDate;
        employeeEntity.ContractEndDate = form.ContractEndDate;
        employeeEntity.SocialSecurityPaymentDate = form.SocialSecurityPaymentDate;
        employeeEntity.Remark = form.Remark;
        employeeEntity.State = true;
        employeeMapper.addEmp(employeeEntity);

        List<EmployeeEntityEntity> list = new ArrayList<>();

        Assembly(list, form);

        employeeEntityMapper.insertEmployeeEntityList(list);

        return BaseOutput.success();
    }

    void Assembly(List<EmployeeEntityEntity> list, EmployeeAddForm form) {
        for (EmployeeEntityAddForm entityAddForm : form.Entity) {
            EmployeeEntityEntity employeeEntityEntity = new EmployeeEntityEntity();
            employeeEntityEntity.EmployeeID = form.ID;
            employeeEntityEntity.EntityID = entityAddForm.EntityID;
            employeeEntityEntity.IsPrimaryEntity = entityAddForm.IsPrimaryEntity;
            list.add(employeeEntityEntity);
        }
    }


    @Transactional
    public BaseOutput updateEmployeeJobTypeBatch(EmployeeJobAddForm form) {

        employeeMapper.updateEmployeeJobList(form);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateEmployeeEntityBatch(EmployeeEntityBatchUpdateForm form) {

        employeeEntityMapper.deleteEmployeeEntityByEmployeeList(form.EmployeeID);

        List<EmployeeEntityEntity> list = new ArrayList<>();


        for (EmployeeEntityAddForm entityAddForm : form.Entity) {
            for (String E : form.EmployeeID) {
                EmployeeEntityEntity employeeEntityEntity = new EmployeeEntityEntity();
                employeeEntityEntity.EmployeeID = E;
                employeeEntityEntity.EntityID = entityAddForm.EntityID;
                employeeEntityEntity.IsPrimaryEntity = entityAddForm.IsPrimaryEntity;
                list.add(employeeEntityEntity);
            }
        }


        employeeEntityMapper.insertEmployeeEntityList(list);

        return BaseOutput.success();
    }

    public BaseOutput getEmployeeDetail(EmployeeDetailQueryForm form) {
        EmployeeDetailOutputForm employeeDetailOutputForm = employeeEntityMapper.getEmployeeDetail(form.EmployeeID);

        return BaseOutputForm.success(employeeDetailOutputForm);
    }

    public BaseOutput empList() {
        List<EmployeeOutputForm> list = employeeMapper.empList(SystemUserUtil.getSystemEntityID());
        return BaseOutputForm.success(list);
    }

    public BaseOutput excelEmployee(EmployeeQueryForm form, HttpServletResponse response) {
        List<EmployeeExportOutputForm> list = employeeMapper.getExcelEmpList(form);
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("员工信息", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), EmployeeExportOutputForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("员工信息").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    //获取员工离职状态
    @Transactional
    public EmployeeEntity getEmployeeByID(String employeeID) {
        return employeeMapper.getEmployeeByID(employeeID);
    }


    public BaseOutput entityEmployeeList(EntityEmployeeQueryForm form) {
        List<EmployeeEntity> list = employeeMapper.getEntityEmployeeList(form.EntityID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allEmployee(EmployeeQueryForm form) {
        List<EmployeeOutputForm> list = employeeMapper.allEmployee(form.SearchKey);
        return BaseOutputForm.success(list);
    }

    public BaseOutput uploadAvatar(EmployeeAvatarAddForm form) {
        EmployeeEntity employee = employeeMapper.getEmployee(form.EmployeeID);
        if (StringUtils.hasText(employee.Avatar)) {
            String url = employee.Avatar;
            String[] split = url.split("/");
            OSSClientUtil.deleteObject(split[split.length - 1]);
        }
        employee.Avatar = OSSClientUtil.putOSSBase64Img(form.ImageUrl);
        employeeMapper.updateEmployee(employee);

        return BaseOutputForm.success(employee.Avatar);
    }

    public BaseOutput avatar(EmployeeAvatarAddForm form) {
        EmployeeEntity employee = employeeMapper.getEmployee(form.EmployeeID);
        return BaseOutputForm.success(employee.Avatar);
    }

    public BaseOutput data(EmployeeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<EmployeeOutputForm> list = employeeMapper.allEmployee(form.SearchKey);
        return BasePageInfo.success(list);
    }

    public BaseOutput permissionEmployeeByPage(QueryPageForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<EmployeeListForm> list = employeeMapper.getEmployeePermissionEntityEmployee(SystemUserUtil.getSystemUserID(), form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput permissionEmployee(QueryPageForm form) {
        List<EmployeeListForm> list = employeeMapper.getEmployeePermissionEntityEmployee(SystemUserUtil.getSystemUserID(), form.Name);
        return BaseOutputForm.success(list);
    }

    public BaseOutput belongCurrentEntity(EmployeeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<BelongEmployeeListForm> list = employeeMapper.belongCurrentEntity(SystemUserUtil.getSystemEntityID(), form.SearchKey);
        return BasePageInfo.success(list);
    }

    public BaseOutput belongCurrentNoPrimaryEntity(EmployeeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<BelongEmployeeListForm> list = employeeMapper.belongCurrentNoPrimaryEntity(SystemUserUtil.getSystemEntityID(), form.SearchKey);
        return BasePageInfo.success(list);
    }

    public BaseOutput belongCurrentNoEntity(EmployeeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<BelongEmployeeListForm> list = employeeMapper.belongCurrentNoEntity(SystemUserUtil.getSystemEntityID(), form.SearchKey);
        return BasePageInfo.success(list);
    }

    public BaseOutput createBelongEntity(EmployeeDeleteBelongEntityForm form) {
        EmployeeEntityEntity employeeEntity = employeeMapper.getEmployeeEntityEntity(SystemUserUtil.getSystemEntityID(), form.EmployeeID);
        if (employeeEntity != null) {
            return BaseOutput.failed("该员工已在当前门店中，请刷新页面重新进入");
        }
        EmployeeEntityEntity employeeEntityEntity = new EmployeeEntityEntity();
        employeeEntityEntity.EmployeeID = form.EmployeeID;
        employeeEntityEntity.EntityID = SystemUserUtil.getSystemEntityID();
        employeeEntityEntity.IsPrimaryEntity = false;
        employeeMapper.createBelongEntity(employeeEntityEntity);
        return BaseOutput.success();
    }

    public BaseOutput deleteBelongEntity(EmployeeDeleteBelongEntityForm form) {
        EmployeeEntityEntity employeeEntity = employeeMapper.getEmployeeEntityEntity(SystemUserUtil.getSystemEntityID(), form.EmployeeID);
        if (employeeEntity == null) {
            return BaseOutput.failed("该员工已不在当前门店中，请刷新页面重新进入");
        }
        employeeMapper.deleteBelongEntity(SystemUserUtil.getSystemEntityID(), form.EmployeeID);
        return BaseOutput.success();
    }

    public BaseOutput listAll(QueryPageForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<EmployeeOutputForm> list = employeeMapper.listAllActiveEmployees(form.Name);
        return BasePageInfo.success(list);
    }

}
