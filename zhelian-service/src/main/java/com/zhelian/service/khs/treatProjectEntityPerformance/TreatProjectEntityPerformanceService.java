package com.zhelian.service.khs.treatProjectEntityPerformance;


import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.TreatProjectEntityPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCommissionQueryForm;
import com.zhelian.model.khs.entity.TreatGoodEntityPerformanceEntity;
import com.zhelian.model.khs.form.input.TreatGoodPerformanceAddForm;
import com.zhelian.model.khs.form.input.TreatGoodsPerformanceAddForm;
import com.zhelian.model.khs.form.output.TreatGoodPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatProjectEntityPerformanceService {

    @Autowired
    TreatProjectEntityPerformanceMapper treatProjectEntityPerformanceMapper;

    public BaseOutput allTreatProjectEntityPerformance(TreatProjectCommissionQueryForm form) {
        List<TreatGoodPerformanceOutputForm> list = treatProjectEntityPerformanceMapper.allTreatProjectEntityPerformance(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatProjectEntityPerformance(TreatGoodsPerformanceAddForm form) {

        treatProjectEntityPerformanceMapper.deleteTreatProjectEntityPerformanceByEntityAndCategory(form.EntityID, form.CategoryID);
        List<TreatGoodEntityPerformanceEntity> list = new ArrayList<>();
        if (form.Good != null && form.Good.size() > 0) {
            for (TreatGoodPerformanceAddForm treatProjectEntityPerformanceAddForm : form.Good) {
                if (treatProjectEntityPerformanceAddForm.PayRate != null ||
                        treatProjectEntityPerformanceAddForm.CardRate != null ||
                        treatProjectEntityPerformanceAddForm.CardLargessRate != null ||
                        treatProjectEntityPerformanceAddForm.LargessRate != null) {
                    TreatGoodEntityPerformanceEntity treatProjectEntityPerformanceEntity = new TreatGoodEntityPerformanceEntity();
                    treatProjectEntityPerformanceEntity.EntityID = form.EntityID;
                    treatProjectEntityPerformanceEntity.GoodID = treatProjectEntityPerformanceAddForm.GoodID;
                    treatProjectEntityPerformanceEntity.PayRate = treatProjectEntityPerformanceAddForm.PayRate;
                    treatProjectEntityPerformanceEntity.CardRate = treatProjectEntityPerformanceAddForm.CardRate;
                    treatProjectEntityPerformanceEntity.CardLargessRate = treatProjectEntityPerformanceAddForm.CardLargessRate;
                    treatProjectEntityPerformanceEntity.LargessRate = treatProjectEntityPerformanceAddForm.LargessRate;
                    list.add(treatProjectEntityPerformanceEntity);
                }
            }
            if (list != null && list.size() > 0) {
                List<List<TreatGoodEntityPerformanceEntity>> subs = Lists.partition(list, 20);
                for (List<TreatGoodEntityPerformanceEntity> entityList : subs) {
                    treatProjectEntityPerformanceMapper.insertTreatProjectEntityPerformance(entityList);
                }
            }
        }
        return BaseOutput.success();
    }

}