package com.zhelian.service.khs.salarySettlement;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.utils.DateTimeUtil;
import com.zhelian.mapper.khs.SalarySheetMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.khs.entity.SalarySettlementIntervalEntity;
import com.zhelian.model.khs.form.input.SalarySheetCommissionQueryForm;
import com.zhelian.model.khs.form.input.SalarySheetDetailQueryForm;
import com.zhelian.model.khs.form.input.SalarySheetQueryForm;
import com.zhelian.model.khs.form.input.SettlementIntervalAddForm;
import com.zhelian.model.khs.form.output.*;
import com.zhelian.model.report.form.output.SalaryEmployeeSalePerformanceCommissionStatementForm;
import com.zhelian.model.report.form.output.SalaryEmployeeTreatPerformanceCommissionStatementForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

@Service
public class SalarySheetService {

    @Autowired
    SalarySheetMapper salarySheetMapper;

    public BaseOutput all(SalarySheetQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SalarySheetOutForm> list = salarySheetMapper.getSalary(form.Name, form.SalarySettlementIntervalID, form.JobID, form.EntityID, form.IsHideZero);
        return BasePageInfo.success(list);
    }


    @Transactional(rollbackFor = Exception.class)
    public void salary(Integer salarySettlementIntervalID) {
        salarySheetMapper.settlement(salarySettlementIntervalID);
    }

    public BaseOutput employeeCommission(SalarySheetCommissionQueryForm form) {
        List<SalaryEmployeePerformanceCommissionOutForm> list = salarySheetMapper.employeeCommission(form.EmployeeID, form.SalarySettlementIntervalID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput teamCommission(SalarySheetCommissionQueryForm form) {
        List<SalaryTeamPerformanceCommissionOutForm> list = salarySheetMapper.teamCommission(form.EmployeeID, form.SalarySettlementIntervalID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput entityCommission(SalarySheetCommissionQueryForm form) {
        List<SalaryEntityPerformanceCommissionOutForm> list = salarySheetMapper.entityCommission(form.EmployeeID, form.SalarySettlementIntervalID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput saleCommission(SalarySheetDetailQueryForm form) {
        SalarySettlementIntervalOutForm salarySettlementIntervalEntity = salarySheetMapper.getSalarySettlementIntervalEntity(form.SalarySettlementIntervalID);
        SalaryEmployeeSalePerformanceCommissionStatementForm employeeSalePerformanceCommissionDetailAndSumStatementForm = new SalaryEmployeeSalePerformanceCommissionStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        employeeSalePerformanceCommissionDetailAndSumStatementForm.employeeSalePerformanceCommissionDetailStatementForms = BasePageInfo.success(salarySheetMapper.getEmployeeSalePerformanceCommissionDetail(form.EmployeeID, salarySettlementIntervalEntity.StartDate, salarySettlementIntervalEntity.EndDate));
        employeeSalePerformanceCommissionDetailAndSumStatementForm.employeeSalePerformanceCommissionSumStatementForm = salarySheetMapper.getEmployeeSalePerformanceCommissionSum(form.EmployeeID, salarySettlementIntervalEntity.StartDate, salarySettlementIntervalEntity.EndDate);
        return BaseOutputForm.success(employeeSalePerformanceCommissionDetailAndSumStatementForm);
    }

    public BaseOutput treatCommission(SalarySheetDetailQueryForm form) {
        SalarySettlementIntervalOutForm salarySettlementIntervalEntity = salarySheetMapper.getSalarySettlementIntervalEntity(form.SalarySettlementIntervalID);
        SalaryEmployeeTreatPerformanceCommissionStatementForm employeeTreatPerformanceCommissionDetailAndSumStatementForm = new SalaryEmployeeTreatPerformanceCommissionStatementForm();
        PageHelper.startPage(form.PageNum, form.getPageSize());
        employeeTreatPerformanceCommissionDetailAndSumStatementForm.employeeTreatPerformanceCommissionDetailStatementForms = BasePageInfo.success(salarySheetMapper.getEmployeeTreatPerformanceCommissionDetail(form.EmployeeID, salarySettlementIntervalEntity.StartDate, salarySettlementIntervalEntity.EndDate));
        employeeTreatPerformanceCommissionDetailAndSumStatementForm.employeeTreatPerformanceCommissionSumStatementForm = salarySheetMapper.getEmployeeTreatPerformanceCommissionSum(form.EmployeeID, salarySettlementIntervalEntity.StartDate, salarySettlementIntervalEntity.EndDate);
        return BaseOutputForm.success(employeeTreatPerformanceCommissionDetailAndSumStatementForm);
    }

    public BaseOutput createSettlementInterval(SettlementIntervalAddForm form) throws Exception {
        SalarySettlementIntervalEntity salarySettlementIntervalEntity = new SalarySettlementIntervalEntity();
        salarySettlementIntervalEntity.SettlementMonth = form.SettlementMonth;
        salarySettlementIntervalEntity.StartDate = DateTimeUtil.toDate(form.StartDate);
        salarySettlementIntervalEntity.EndDate = DateTimeUtil.toDate(form.EndDate);
        salarySettlementIntervalEntity.IsInSettlement = false;
        salarySheetMapper.createSettlementInterval(salarySettlementIntervalEntity);
        return BaseOutput.success();
    }

    public BaseOutput allSettlementInterval() {
        List<SalarySettlementIntervalOutForm> list = salarySheetMapper.allSettlementInterval();
        return BaseOutputForm.success(list);
    }

    public BaseOutput easyExcel(SalarySheetQueryForm form, HttpServletResponse response) {
        List<SalarySheetEasyExcelOutForm> list = salarySheetMapper.easyExcelSalary(form);
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("业绩提成结算", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), SalarySheetEasyExcelOutForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("业绩提成结算").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }
}
