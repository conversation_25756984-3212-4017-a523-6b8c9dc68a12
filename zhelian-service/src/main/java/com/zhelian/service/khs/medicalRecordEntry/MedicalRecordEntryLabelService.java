package com.zhelian.service.khs.medicalRecordEntry;

import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.khs.MedicalRecordEntryContentMapper;
import com.zhelian.mapper.khs.MedicalRecordEntryLabelMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.khs.entity.MedicalRecordEntryLabelEntity;
import com.zhelian.model.khs.form.input.*;
import com.zhelian.model.khs.form.output.MedicalRecordEntryLabelOutForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

@Service
public class MedicalRecordEntryLabelService {

    @Autowired
    MedicalRecordEntryLabelMapper medicalRecordEntryLabelMapper;

    @Autowired
    MedicalRecordEntryContentMapper medicalRecordEntryContentMapper;

    public BaseOutput list(MedicalRecordEntryLabelQueryForm form) {
        List<MedicalRecordEntryLabelOutForm> outForm = medicalRecordEntryLabelMapper.list(form.Name,form.CategoryID);
        return BaseOutputForm.success(outForm);
    }

    @Transactional
    public BaseOutput create(MedicalRecordEntryLabelAddForm form) {
        Integer Sequence = medicalRecordEntryLabelMapper.getMaxSequence();
        MedicalRecordEntryLabelEntity entity = new MedicalRecordEntryLabelEntity();
        entity.Name = form.Name;
        entity.MedicalRecordEntryCategoryID = form.CategoryID;
        entity.Sequence = Sequence != null ? Sequence + 1 : 1;
        medicalRecordEntryLabelMapper.create(entity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput update(MedicalRecordEntryLabelUpdateForm form) {
        MedicalRecordEntryLabelEntity entity = new MedicalRecordEntryLabelEntity();
        entity.ID = form.ID;
        entity.Name = form.Name;
        medicalRecordEntryLabelMapper.update(entity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput delete(MedicalRecordEntryLabelDeleteForm form) {
        medicalRecordEntryContentMapper.deleteByLabelID(form.ID);
        medicalRecordEntryLabelMapper.delete(form.ID);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput move(MedicalRecordEntryLabelMoveForm form) {
        LinkedList<Integer> linkedList = medicalRecordEntryLabelMapper.getASCList(form.MoveID, form.CategoryID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<MedicalRecordEntryLabelEntity> list = new ArrayList<>();
        for (Integer ID : linkedList) {
            MedicalRecordEntryLabelEntity entity = new MedicalRecordEntryLabelEntity();
            entity.ID = ID;
            entity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(entity);
        }

        medicalRecordEntryLabelMapper.updateSequence(list);
        return BaseOutput.success();
    }
}
