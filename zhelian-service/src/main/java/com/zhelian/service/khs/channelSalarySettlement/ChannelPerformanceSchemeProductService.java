package com.zhelian.service.khs.channelSalarySettlement;


import com.zhelian.mapper.khs.ChannelPerformanceSchemeProductMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.khs.entity.SaleProductPerformanceEntity;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodAddForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodQueryForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodsAddForm;
import com.zhelian.model.khs.form.output.PerformanceSchemeGoodOutForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class ChannelPerformanceSchemeProductService {

    @Autowired
    ChannelPerformanceSchemeProductMapper channelPerformanceSchemeProductMapper;

    public BaseOutput all(PerformanceSchemeGoodQueryForm form) {
        List<PerformanceSchemeGoodOutForm> list = channelPerformanceSchemeProductMapper.getAllPerformanceSchemeProduct(form.PerformanceSchemeID, form.CategoryID);
        return BaseOutputForm.success(list);

    }

    @Transactional
    public BaseOutput create(PerformanceSchemeGoodsAddForm form) {
        channelPerformanceSchemeProductMapper.deleteSaleProductPerformanceByPerformanceSchemeIDAndCategory(form.PerformanceSchemeID, form.CategoryID);
        List<SaleProductPerformanceEntity> list = new ArrayList<>();
        if (form.Good != null && form.Good.size() > 0) {
            for (PerformanceSchemeGoodAddForm performanceSchemeGoodAddForm : form.Good) {
                if (performanceSchemeGoodAddForm.PayPerformanceRate != null || performanceSchemeGoodAddForm.SavingCardPerformanceRate != null ||
                        performanceSchemeGoodAddForm.SavingCardPerformanceLargessRate != null) {
                    SaleProductPerformanceEntity saleProductCommissionEntity = new SaleProductPerformanceEntity();
                    saleProductCommissionEntity.PerformanceSchemeID = form.PerformanceSchemeID;
                    saleProductCommissionEntity.ProductID = performanceSchemeGoodAddForm.GoodID;
                    saleProductCommissionEntity.PayPerformanceRate = performanceSchemeGoodAddForm.PayPerformanceRate;
                    saleProductCommissionEntity.SavingCardPerformanceRate = performanceSchemeGoodAddForm.SavingCardPerformanceRate;
                    saleProductCommissionEntity.SavingCardPerformanceLargessRate = performanceSchemeGoodAddForm.SavingCardPerformanceLargessRate;
                    list.add(saleProductCommissionEntity);
                }
            }
            channelPerformanceSchemeProductMapper.insertSaleProductPerformance(list);
        }
        return BaseOutput.success();
    }
}
