package com.zhelian.service.khs.saleProductPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.SalePackageCardProductSchemeHandlerPerformanceMapper;
import com.zhelian.mapper.khs.SaleProductSchemeHandlerPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.SaleProductSchemeHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductSchemeHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProductSchemeHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductsSchemeHandlerCommissionAddForm;
import com.zhelian.model.khs.entity.SalePackageCardProductSchemeHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SalePackageCardProductSchemeHandlerPerformanceEntity;
import com.zhelian.model.khs.entity.SaleProductSchemeHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SaleProductSchemeHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleGoodSchemeHandlerPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleProductSchemeHandlerPerformanceService {

    @Autowired
    SaleProductSchemeHandlerPerformanceMapper saleProductSchemeHandlerPerformanceMapper;
    @Autowired
    SalePackageCardProductSchemeHandlerPerformanceMapper salePackageCardProductSchemeHandlerPerformanceMapper;

    public BaseOutput allSaleProductSchemeHandlerPerformance(SaleProductSchemeHandlerCommissionQueryForm form) {
        List<SaleGoodSchemeHandlerPerformanceOutputForm> list = saleProductSchemeHandlerPerformanceMapper.allSaleProductSchemeHandlerPerformance(form.EntityID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSalePackageCardProductSchemeHandlerPerformance(SaleProductSchemeHandlerCommissionQueryForm form) {
        List<SaleGoodSchemeHandlerPerformanceOutputForm> list = salePackageCardProductSchemeHandlerPerformanceMapper.allSalePackageCardProductSchemeHandlerPerformance(form.EntityID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleProductSchemeHandlerPerformance(SaleProductsSchemeHandlerCommissionAddForm form) {

        saleProductSchemeHandlerPerformanceMapper.deleteSaleProductSchemeHandler(form.EntityID);
        saleProductSchemeHandlerPerformanceMapper.deleteSaleProductSchemeHandlerJobType(form.EntityID);
        if (form.ProductHandler != null && form.ProductHandler.size() > 0) {
            List<SaleProductSchemeHandlerPerformanceEntity> listSaleProductSchemeHandlerPerformanceEntity = new ArrayList<>();
            List<SaleProductSchemeHandlerJobTypePerformanceEntity> listSaleProductSchemeHandlerJobTypePerformanceEntity = new ArrayList<>();
            for (SaleProductSchemeHandlerCommissionAddForm saleProductSchemeHandlerAddForm : form.ProductHandler) {
                if (saleProductSchemeHandlerAddForm.PayRate != null ||
                        saleProductSchemeHandlerAddForm.SavingCardRate != null ||
                        saleProductSchemeHandlerAddForm.SavingCardLargessRate != null) {
                    SaleProductSchemeHandlerPerformanceEntity saleProductSchemeHandlerPerformanceEntity = new SaleProductSchemeHandlerPerformanceEntity();
                    saleProductSchemeHandlerPerformanceEntity.EntityID = form.EntityID;
                    saleProductSchemeHandlerPerformanceEntity.ProductSaleHandlerID = saleProductSchemeHandlerAddForm.SaleHandlerID;
                    saleProductSchemeHandlerPerformanceEntity.PayRate = saleProductSchemeHandlerAddForm.PayRate;
                    saleProductSchemeHandlerPerformanceEntity.SavingCardRate = saleProductSchemeHandlerAddForm.SavingCardRate;
                    saleProductSchemeHandlerPerformanceEntity.SavingCardLargessRate = saleProductSchemeHandlerAddForm.SavingCardLargessRate;
                    listSaleProductSchemeHandlerPerformanceEntity.add(saleProductSchemeHandlerPerformanceEntity);
                }
                if (saleProductSchemeHandlerAddForm.HandlerJobTypeCommission != null && saleProductSchemeHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleProductSchemeHandlerJobTypeCommissionAddForm saleProductSchemeHandlerJobTypePerformanceAddForm : saleProductSchemeHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleProductSchemeHandlerJobTypePerformanceAddForm.PayRate != null ||
                                saleProductSchemeHandlerJobTypePerformanceAddForm.SavingCardRate != null ||
                                saleProductSchemeHandlerJobTypePerformanceAddForm.SavingCardLargessRate != null) {
                            SaleProductSchemeHandlerJobTypePerformanceEntity saleProductSchemeHandlerJobTypePerformanceEntity = new SaleProductSchemeHandlerJobTypePerformanceEntity();
                            saleProductSchemeHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            saleProductSchemeHandlerJobTypePerformanceEntity.ProductSaleHandlerID = saleProductSchemeHandlerAddForm.SaleHandlerID;
                            saleProductSchemeHandlerJobTypePerformanceEntity.JobTypeID = saleProductSchemeHandlerJobTypePerformanceAddForm.JobTypeID;
                            saleProductSchemeHandlerJobTypePerformanceEntity.PayRate = saleProductSchemeHandlerJobTypePerformanceAddForm.PayRate;
                            saleProductSchemeHandlerJobTypePerformanceEntity.SavingCardRate = saleProductSchemeHandlerJobTypePerformanceAddForm.SavingCardRate;
                            saleProductSchemeHandlerJobTypePerformanceEntity.SavingCardLargessRate = saleProductSchemeHandlerJobTypePerformanceAddForm.SavingCardLargessRate;
                            listSaleProductSchemeHandlerJobTypePerformanceEntity.add(saleProductSchemeHandlerJobTypePerformanceEntity);
                        }
                    }
                }
            }
            if (listSaleProductSchemeHandlerPerformanceEntity != null && listSaleProductSchemeHandlerPerformanceEntity.size() > 0) {
                List<List<SaleProductSchemeHandlerPerformanceEntity>> subs = Lists.partition(listSaleProductSchemeHandlerPerformanceEntity, 20);
                for (List<SaleProductSchemeHandlerPerformanceEntity> entityList : subs) {
                    saleProductSchemeHandlerPerformanceMapper.insertSaleProductSchemeHandlerPerformance(entityList);
                }
            }
            if (listSaleProductSchemeHandlerJobTypePerformanceEntity != null && listSaleProductSchemeHandlerJobTypePerformanceEntity.size() > 0) {
                List<List<SaleProductSchemeHandlerJobTypePerformanceEntity>> subs = Lists.partition(listSaleProductSchemeHandlerJobTypePerformanceEntity, 20);
                for (List<SaleProductSchemeHandlerJobTypePerformanceEntity> entityList : subs) {
                    saleProductSchemeHandlerPerformanceMapper.insertSaleProductSchemeHandlerJobTypePerformance(entityList);
                }
            }
        }

        salePackageCardProductSchemeHandlerPerformanceMapper.deleteSalePackageCardProductSchemeHandler(form.EntityID);
        salePackageCardProductSchemeHandlerPerformanceMapper.deleteSalePackageCardProductSchemeHandlerJobType(form.EntityID);
        if (form.PackageCardProductHandler != null && form.PackageCardProductHandler.size() > 0) {
            List<SalePackageCardProductSchemeHandlerPerformanceEntity> listSalePackageCardProductSchemeHandlerPerformanceEntity = new ArrayList<>();
            List<SalePackageCardProductSchemeHandlerJobTypePerformanceEntity> listSalePackageCardProductSchemeHandlerJobTypePerformanceEntity = new ArrayList<>();
            for (SaleProductSchemeHandlerCommissionAddForm saleProductSchemeHandlerAddForm : form.PackageCardProductHandler) {
                if (saleProductSchemeHandlerAddForm.PayRate != null ||
                        saleProductSchemeHandlerAddForm.SavingCardRate != null ||
                        saleProductSchemeHandlerAddForm.SavingCardLargessRate != null) {
                    SalePackageCardProductSchemeHandlerPerformanceEntity saleProductSchemeHandlerPerformanceEntity = new SalePackageCardProductSchemeHandlerPerformanceEntity();
                    saleProductSchemeHandlerPerformanceEntity.EntityID = form.EntityID;
                    saleProductSchemeHandlerPerformanceEntity.PackageCardSaleHandlerID = saleProductSchemeHandlerAddForm.SaleHandlerID;
                    saleProductSchemeHandlerPerformanceEntity.PayFixed = saleProductSchemeHandlerAddForm.PayFixed;
                    saleProductSchemeHandlerPerformanceEntity.PayRate = saleProductSchemeHandlerAddForm.PayRate;
                    saleProductSchemeHandlerPerformanceEntity.SavingCardFixed = saleProductSchemeHandlerAddForm.SavingCardFixed;
                    saleProductSchemeHandlerPerformanceEntity.SavingCardRate = saleProductSchemeHandlerAddForm.SavingCardRate;
                    saleProductSchemeHandlerPerformanceEntity.SavingCardLargessFixed = saleProductSchemeHandlerAddForm.SavingCardLargessFixed;
                    saleProductSchemeHandlerPerformanceEntity.SavingCardLargessRate = saleProductSchemeHandlerAddForm.SavingCardLargessRate;
                    saleProductSchemeHandlerPerformanceEntity.SpecialBenefit = saleProductSchemeHandlerAddForm.SpecialBenefit;
                    listSalePackageCardProductSchemeHandlerPerformanceEntity.add(saleProductSchemeHandlerPerformanceEntity);
                }
                if (saleProductSchemeHandlerAddForm.HandlerJobTypeCommission != null && saleProductSchemeHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleProductSchemeHandlerJobTypeCommissionAddForm saleProductSchemeHandlerJobTypePerformanceAddForm : saleProductSchemeHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleProductSchemeHandlerJobTypePerformanceAddForm.PayRate != null || saleProductSchemeHandlerJobTypePerformanceAddForm.PayFixed != null ||
                                saleProductSchemeHandlerJobTypePerformanceAddForm.SavingCardRate != null || saleProductSchemeHandlerJobTypePerformanceAddForm.SavingCardFixed != null ||
                                saleProductSchemeHandlerJobTypePerformanceAddForm.SavingCardLargessRate != null || saleProductSchemeHandlerJobTypePerformanceAddForm.SavingCardLargessFixed != null ||
                                saleProductSchemeHandlerJobTypePerformanceAddForm.SpecialBenefit != null) {
                            SalePackageCardProductSchemeHandlerJobTypePerformanceEntity saleProductSchemeHandlerJobTypePerformanceEntity = new SalePackageCardProductSchemeHandlerJobTypePerformanceEntity();
                            saleProductSchemeHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            saleProductSchemeHandlerJobTypePerformanceEntity.PackageCardSaleHandlerID = saleProductSchemeHandlerAddForm.SaleHandlerID;
                            saleProductSchemeHandlerJobTypePerformanceEntity.JobTypeID = saleProductSchemeHandlerJobTypePerformanceAddForm.JobTypeID;
                            saleProductSchemeHandlerJobTypePerformanceEntity.PayFixed = saleProductSchemeHandlerJobTypePerformanceAddForm.PayFixed;
                            saleProductSchemeHandlerJobTypePerformanceEntity.PayRate = saleProductSchemeHandlerJobTypePerformanceAddForm.PayRate;
                            saleProductSchemeHandlerJobTypePerformanceEntity.SavingCardFixed = saleProductSchemeHandlerJobTypePerformanceAddForm.SavingCardFixed;
                            saleProductSchemeHandlerJobTypePerformanceEntity.SavingCardRate = saleProductSchemeHandlerJobTypePerformanceAddForm.SavingCardRate;
                            saleProductSchemeHandlerJobTypePerformanceEntity.SavingCardLargessFixed = saleProductSchemeHandlerJobTypePerformanceAddForm.SavingCardLargessFixed;
                            saleProductSchemeHandlerJobTypePerformanceEntity.SavingCardLargessRate = saleProductSchemeHandlerJobTypePerformanceAddForm.SavingCardLargessRate;
                            saleProductSchemeHandlerJobTypePerformanceEntity.SpecialBenefit = saleProductSchemeHandlerJobTypePerformanceAddForm.SpecialBenefit;
                            listSalePackageCardProductSchemeHandlerJobTypePerformanceEntity.add(saleProductSchemeHandlerJobTypePerformanceEntity);
                        }
                    }
                }
            }
            if (listSalePackageCardProductSchemeHandlerPerformanceEntity != null && listSalePackageCardProductSchemeHandlerPerformanceEntity.size() > 0) {
                List<List<SalePackageCardProductSchemeHandlerPerformanceEntity>> subs = Lists.partition(listSalePackageCardProductSchemeHandlerPerformanceEntity, 20);
                for (List<SalePackageCardProductSchemeHandlerPerformanceEntity> entityList : subs) {
                    salePackageCardProductSchemeHandlerPerformanceMapper.insertSalePackageCardProductSchemeHandlerPerformance(entityList);
                }
            }
            if (listSalePackageCardProductSchemeHandlerJobTypePerformanceEntity != null && listSalePackageCardProductSchemeHandlerJobTypePerformanceEntity.size() > 0) {
                List<List<SalePackageCardProductSchemeHandlerJobTypePerformanceEntity>> subs = Lists.partition(listSalePackageCardProductSchemeHandlerJobTypePerformanceEntity, 20);
                for (List<SalePackageCardProductSchemeHandlerJobTypePerformanceEntity> entityList : subs) {
                    salePackageCardProductSchemeHandlerPerformanceMapper.insertSalePackageCardProductSchemeHandlerJobTypePerformance(entityList);
                }
            }
        }
        return BaseOutput.success();
    }
}