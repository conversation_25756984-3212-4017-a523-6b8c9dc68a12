package com.zhelian.service.khs.treatProductPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.TreatProductSchemeHandlerPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductSchemeHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatProductSchemeHandlersCommissionAddForm;
import com.zhelian.model.khs.entity.TreatProductSchemeHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.TreatProductSchemeHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.TreatGoodCategoryHandlerPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatProductSchemeHandlerPerformanceService {

    @Autowired
    TreatProductSchemeHandlerPerformanceMapper treatProductSchemeHandlerPerformanceMapper;

    public BaseOutput allTreatProductSchemeHandlerPerformance(TreatProductSchemeHandlerCommissionQueryForm form) {
        List<TreatGoodCategoryHandlerPerformanceOutputForm> list = treatProductSchemeHandlerPerformanceMapper.allTreatProductSchemeHandlerPerformance(form.EntityID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatProductSchemeHandlerPerformance(TreatProductSchemeHandlersCommissionAddForm form) {

        treatProductSchemeHandlerPerformanceMapper.deleteTreatProductSchemeHandler(form.EntityID);
        treatProductSchemeHandlerPerformanceMapper.deleteTreatProductSchemeHandlerJobType(form.EntityID);

        if (form.Handler != null && form.Handler.size() > 0) {

            List listTreatProductSchemeHandlerPerformanceEntity = new ArrayList<>();

            List listTreatProductSchemeHandlerJobTypePerformanceEntity = new ArrayList<>();

            for (TreatProductHandlerCommissionAddForm treatProductSchemeHandlerAddForm : form.Handler) {
                if (treatProductSchemeHandlerAddForm.PayRate != null ||
                        treatProductSchemeHandlerAddForm.CardRate != null ||
                        treatProductSchemeHandlerAddForm.CardLargessRate != null ||
                        treatProductSchemeHandlerAddForm.LargessRate != null) {
                    TreatProductSchemeHandlerPerformanceEntity treatProductSchemeHandlerPerformanceEntity = new TreatProductSchemeHandlerPerformanceEntity();
                    treatProductSchemeHandlerPerformanceEntity.EntityID = form.EntityID;
                    treatProductSchemeHandlerPerformanceEntity.ProductTreatHandlerID = treatProductSchemeHandlerAddForm.TreatHandlerID;
                    treatProductSchemeHandlerPerformanceEntity.PayRate = treatProductSchemeHandlerAddForm.PayRate;
                    treatProductSchemeHandlerPerformanceEntity.CardRate = treatProductSchemeHandlerAddForm.CardRate;
                    treatProductSchemeHandlerPerformanceEntity.CardLargessRate = treatProductSchemeHandlerAddForm.CardLargessRate;
                    treatProductSchemeHandlerPerformanceEntity.LargessRate = treatProductSchemeHandlerAddForm.LargessRate;
                    listTreatProductSchemeHandlerPerformanceEntity.add(treatProductSchemeHandlerPerformanceEntity);
                }

                if (treatProductSchemeHandlerAddForm.HandlerJobTypeCommission != null && treatProductSchemeHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (TreatProductCategoryHandlerJobTypeCommissionAddForm treatProductSchemeHandlerJobTypePerformanceAddForm : treatProductSchemeHandlerAddForm.HandlerJobTypeCommission) {
                        if (treatProductSchemeHandlerJobTypePerformanceAddForm.PayRate != null ||
                                treatProductSchemeHandlerJobTypePerformanceAddForm.CardRate != null ||
                                treatProductSchemeHandlerJobTypePerformanceAddForm.CardLargessRate != null ||
                                treatProductSchemeHandlerJobTypePerformanceAddForm.LargessRate != null) {
                            TreatProductSchemeHandlerJobTypePerformanceEntity treatProductSchemeHandlerJobTypePerformanceEntity = new TreatProductSchemeHandlerJobTypePerformanceEntity();
                            treatProductSchemeHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            treatProductSchemeHandlerJobTypePerformanceEntity.ProductTreatHandlerID = treatProductSchemeHandlerAddForm.TreatHandlerID;
                            treatProductSchemeHandlerJobTypePerformanceEntity.JobTypeID = treatProductSchemeHandlerJobTypePerformanceAddForm.JobTypeID;
                            treatProductSchemeHandlerJobTypePerformanceEntity.PayRate = treatProductSchemeHandlerJobTypePerformanceAddForm.PayRate;
                            treatProductSchemeHandlerJobTypePerformanceEntity.CardRate = treatProductSchemeHandlerJobTypePerformanceAddForm.CardRate;
                            treatProductSchemeHandlerJobTypePerformanceEntity.CardLargessRate = treatProductSchemeHandlerJobTypePerformanceAddForm.CardLargessRate;
                            treatProductSchemeHandlerJobTypePerformanceEntity.LargessRate = treatProductSchemeHandlerJobTypePerformanceAddForm.LargessRate;
                            listTreatProductSchemeHandlerJobTypePerformanceEntity.add(treatProductSchemeHandlerJobTypePerformanceEntity);
                        }
                    }

                }
            }
            if (listTreatProductSchemeHandlerPerformanceEntity != null && listTreatProductSchemeHandlerPerformanceEntity.size() > 0) {
                List<List<TreatProductSchemeHandlerPerformanceEntity>> subs = Lists.partition(listTreatProductSchemeHandlerPerformanceEntity, 20);
                for (List<TreatProductSchemeHandlerPerformanceEntity> entityList : subs) {
                    treatProductSchemeHandlerPerformanceMapper.updateTreatProductSchemeHandlerPerformance(entityList);
                }
            }
            if (listTreatProductSchemeHandlerJobTypePerformanceEntity != null && listTreatProductSchemeHandlerJobTypePerformanceEntity.size() > 0) {
                List<List<TreatProductSchemeHandlerJobTypePerformanceEntity>> subs = Lists.partition(listTreatProductSchemeHandlerJobTypePerformanceEntity, 20);
                for (List<TreatProductSchemeHandlerJobTypePerformanceEntity> entityList : subs) {
                    treatProductSchemeHandlerPerformanceMapper.insertTreatProductSchemeHandlerJobTypePerformance(entityList);
                }
            }
        }
        return BaseOutput.success();
    }
}