package com.zhelian.service.khs.salarySettlement;


import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.khs.TeamCommissionSchemeMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.khs.entity.TeamCommissionEmployeeEntity;
import com.zhelian.model.khs.entity.TeamCommissionEntity;
import com.zhelian.model.khs.entity.TeamCommissionSchemeEntity;
import com.zhelian.model.khs.entity.TeamEmployeeEntity;
import com.zhelian.model.khs.form.input.TeamCommissionAddForm;
import com.zhelian.model.khs.form.input.TeamCommissionSchemeAddForm;
import com.zhelian.model.khs.form.input.TeamCommissionSchemeQueryForm;
import com.zhelian.model.khs.form.input.TeamCommissionSchemeUpdateForm;
import com.zhelian.model.khs.form.output.EmployeeCommissionOutForm;
import com.zhelian.model.khs.form.output.TeamCalculateEmployeeOutForm;
import com.zhelian.model.khs.form.output.TeamCommissionSchemeOutForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class TeamCommissionSchemeService {

    @Autowired
    TeamCommissionSchemeMapper teamCommissionSchemeMapper;

    public BaseOutput all(TeamCommissionSchemeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<TeamCommissionSchemeOutForm> list = teamCommissionSchemeMapper.getTeamCommissionScheme(form.Name, form.PerformanceEvaluationSchemeID, form.PerformanceCalculationSchemeID, form.Active);
        return BasePageInfo.success(list);
    }

    @Transactional
    public BaseOutput create(TeamCommissionSchemeAddForm form) {
        TeamCommissionSchemeEntity teamCommissionSchemeEntity = new TeamCommissionSchemeEntity();
        teamCommissionSchemeEntity.Name = form.Name;
        teamCommissionSchemeEntity.PerformanceEvaluationSchemeID = form.PerformanceEvaluationSchemeID;
        teamCommissionSchemeEntity.PerformanceCalculationSchemeID = form.PerformanceCalculationSchemeID;
        teamCommissionSchemeEntity.Calculation = form.Calculation;
        teamCommissionSchemeMapper.createTeamCommissionScheme(teamCommissionSchemeEntity);

        updateDetail(teamCommissionSchemeEntity.ID, form.Commission, form.Employee, form.CommissionEmployee);

        return BaseOutput.success();
    }

    public void updateDetail(Integer commissionSchemeID, List<TeamCommissionAddForm> commission, List<String> employee, List<String> commissionEmployee) {
        List<TeamCommissionEntity> commissionEntities = new ArrayList<>();
        List<TeamCommissionEmployeeEntity> teamCommissionEmployeeEntities = new ArrayList<>();
        List<TeamEmployeeEntity> employeeEntities = new ArrayList<>();


        if (commission != null && commission.size() > 0) {
            commission.forEach(i -> {
                TeamCommissionEntity teamCommissionEntity = new TeamCommissionEntity();
                teamCommissionEntity.TeamCommissionSchemeID = commissionSchemeID;
                teamCommissionEntity.BeginPerformance = i.BeginPerformance;
                teamCommissionEntity.EndPerformance = i.EndPerformance;
                teamCommissionEntity.Rate = i.Rate;
                teamCommissionEntity.Fixed = i.Fixed;
                commissionEntities.add(teamCommissionEntity);
            });

        }
        if (employee != null && employee.size() > 0) {
            employee.forEach(i -> {
                TeamEmployeeEntity teamCommissionEntity = new TeamEmployeeEntity();
                teamCommissionEntity.TeamCommissionSchemeID = commissionSchemeID;
                teamCommissionEntity.EmployeeID = i;
                employeeEntities.add(teamCommissionEntity);
            });

        }
        if (commissionEmployee != null && commissionEmployee.size() > 0) {
            commissionEmployee.forEach(i -> {
                TeamCommissionEmployeeEntity teamCommissionEmployeeEntity = new TeamCommissionEmployeeEntity();
                teamCommissionEmployeeEntity.TeamCommissionSchemeID = commissionSchemeID;
                teamCommissionEmployeeEntity.EmployeeID = i;
                teamCommissionEmployeeEntities.add(teamCommissionEmployeeEntity);
            });
        }

        if (commissionEntities.size() > 0) teamCommissionSchemeMapper.createTeamCommission(commissionEntities);

        if (employeeEntities.size() > 0) teamCommissionSchemeMapper.createTeamEmployee(employeeEntities);

        if (teamCommissionEmployeeEntities.size() > 0)
            teamCommissionSchemeMapper.createTeamCommissionEmployee(teamCommissionEmployeeEntities);

    }

    @Transactional
    public BaseOutput update(TeamCommissionSchemeUpdateForm form) {
        TeamCommissionSchemeEntity teamCommissionSchemeEntity = new TeamCommissionSchemeEntity();
        teamCommissionSchemeEntity.ID = form.ID;
        teamCommissionSchemeEntity.Name = form.Name;
        teamCommissionSchemeEntity.PerformanceEvaluationSchemeID = form.PerformanceEvaluationSchemeID;
        teamCommissionSchemeEntity.PerformanceCalculationSchemeID = form.PerformanceCalculationSchemeID;
        teamCommissionSchemeEntity.Calculation = form.Calculation;
        teamCommissionSchemeEntity.Active = form.Active;
        teamCommissionSchemeMapper.updateTeamCommissionScheme(teamCommissionSchemeEntity);
        teamCommissionSchemeMapper.deleteTeamCommission(form.ID);
        teamCommissionSchemeMapper.deleteTeamEmployee(form.ID);
        teamCommissionSchemeMapper.deleteTeamCommissionEmployee(form.ID);
        updateDetail(teamCommissionSchemeEntity.ID, form.Commission, form.Employee, form.CommissionEmployee);
        return BaseOutput.success();
    }

    public BaseOutput employee(TeamCommissionSchemeUpdateForm form) {
        List<TeamCalculateEmployeeOutForm> list = teamCommissionSchemeMapper.employee(form.ID);
        return BaseOutputForm.success(list);

    }

    public BaseOutput comissionEmployee(TeamCommissionSchemeUpdateForm form) {
        List<TeamCalculateEmployeeOutForm> list = teamCommissionSchemeMapper.comissionEmployee(form.ID);
        return BaseOutputForm.success(list);

    }

    public BaseOutput commission(TeamCommissionSchemeUpdateForm form) {
        List<EmployeeCommissionOutForm> list = teamCommissionSchemeMapper.commission(form.ID);
        return BaseOutputForm.success(list);

    }
}
