package com.zhelian.service.khs.saleProductEntityPerformance;


import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.SaleProductEntityPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryHandlerCommissionQueryForm;
import com.zhelian.model.khs.entity.SaleGoodPerformanceEntity;
import com.zhelian.model.khs.form.input.SaleGoodCommissionAddForm;
import com.zhelian.model.khs.form.input.SaleGoodsPerformanceAddForm;
import com.zhelian.model.khs.form.output.SaleGoodPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleProductEntityPerformanceService {

    @Autowired
    SaleProductEntityPerformanceMapper saleProductEntityPerformanceMapper;

    public BaseOutput allSaleProductEntityPerformance(SaleProductCategoryHandlerCommissionQueryForm form) {
        List<SaleGoodPerformanceOutputForm> list = saleProductEntityPerformanceMapper.allSaleProductEntityPerformance(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleProductEntityPerformance(SaleGoodsPerformanceAddForm form) {

        saleProductEntityPerformanceMapper.deleteSaleProductEntityPerformanceByEntityIDAndCategory(form.EntityID, form.CategoryID);
        List<SaleGoodPerformanceEntity> list = new ArrayList<>();
        if (form.Good != null && form.Good.size() > 0) {
            for (SaleGoodCommissionAddForm saleProductEntityPerformanceAddForm : form.Good) {
                if (saleProductEntityPerformanceAddForm.PayRate != null ||
                        saleProductEntityPerformanceAddForm.SavingCardRate != null ||
                        saleProductEntityPerformanceAddForm.SavingCardLargessRate != null) {
                    SaleGoodPerformanceEntity saleProductEntityPerformanceEntity = new SaleGoodPerformanceEntity();
                    saleProductEntityPerformanceEntity.EntityID = form.EntityID;
                    saleProductEntityPerformanceEntity.GoodID = saleProductEntityPerformanceAddForm.GoodID;
                    saleProductEntityPerformanceEntity.PayRate = saleProductEntityPerformanceAddForm.PayRate;
                    saleProductEntityPerformanceEntity.SavingCardRate = saleProductEntityPerformanceAddForm.SavingCardRate;
                    saleProductEntityPerformanceEntity.SavingCardLargessRate = saleProductEntityPerformanceAddForm.SavingCardLargessRate;
                    list.add(saleProductEntityPerformanceEntity);
                }
            }

            if (list != null && list.size() > 0) {
                List<List<SaleGoodPerformanceEntity>> subs = Lists.partition(list, 20);
                for (List<SaleGoodPerformanceEntity> entityList : subs) {
                    saleProductEntityPerformanceMapper.insertSaleProductEntityPerformance(entityList);
                }
            }
        }
        return BaseOutput.success();
    }

}