package com.zhelian.service.khs.salarySettlement;


import com.zhelian.mapper.khs.PerformanceSchemeProjectMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.khs.entity.SaleProjectPerformanceEntity;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodAddForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodQueryForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodsAddForm;
import com.zhelian.model.khs.form.output.PerformanceSchemeGoodOutForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class PerformanceSchemeProjectService {

    @Autowired
    PerformanceSchemeProjectMapper performanceSchemeProjectMapper;

    public BaseOutput all(PerformanceSchemeGoodQueryForm form) {
        List<PerformanceSchemeGoodOutForm> list = performanceSchemeProjectMapper.getAllPerformanceSchemeProject(form.PerformanceSchemeID, form.CategoryID);
        return BaseOutputForm.success(list);

    }

    @Transactional
    public BaseOutput create(PerformanceSchemeGoodsAddForm form) {

        performanceSchemeProjectMapper.deleteSaleProjectPerformanceByPerformanceSchemeIDAndCategory(form.PerformanceSchemeID, form.CategoryID);
        List<SaleProjectPerformanceEntity> list = new ArrayList<>();
        if (form.Good != null && form.Good.size() > 0) {
            for (PerformanceSchemeGoodAddForm performanceSchemeGoodAddForm : form.Good) {
                if (performanceSchemeGoodAddForm.PayPerformanceRate != null || performanceSchemeGoodAddForm.SavingCardPerformanceRate != null ||
                        performanceSchemeGoodAddForm.SavingCardPerformanceLargessRate != null) {
                    SaleProjectPerformanceEntity saleProjectCommissionEntity = new SaleProjectPerformanceEntity();
                    saleProjectCommissionEntity.PerformanceSchemeID = form.PerformanceSchemeID;
                    saleProjectCommissionEntity.ProjectID = performanceSchemeGoodAddForm.GoodID;
                    saleProjectCommissionEntity.PayPerformanceRate = performanceSchemeGoodAddForm.PayPerformanceRate;
                    saleProjectCommissionEntity.SavingCardPerformanceRate = performanceSchemeGoodAddForm.SavingCardPerformanceRate;
                    saleProjectCommissionEntity.SavingCardPerformanceLargessRate = performanceSchemeGoodAddForm.SavingCardPerformanceLargessRate;
                    list.add(saleProjectCommissionEntity);
                }
            }
            performanceSchemeProjectMapper.insertSaleProjectPerformance(list);
        }
        return BaseOutput.success();
    }
}
