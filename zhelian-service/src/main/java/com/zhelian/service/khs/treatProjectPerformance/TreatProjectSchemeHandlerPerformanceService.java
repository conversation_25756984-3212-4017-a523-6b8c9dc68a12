package com.zhelian.service.khs.treatProjectPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.TreatProjectSchemeHandlerPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectSchemeHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectSchemeHandlersCommissionAddForm;
import com.zhelian.model.khs.entity.TreatProjectSchemeHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.TreatProjectSchemeHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.TreatGoodCategoryHandlerPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatProjectSchemeHandlerPerformanceService {

    @Autowired
    TreatProjectSchemeHandlerPerformanceMapper treatProjectSchemeHandlerPerformanceMapper;

    public BaseOutput allTreatProjectSchemeHandlerPerformance(TreatProjectSchemeHandlerCommissionQueryForm form) {
        List<TreatGoodCategoryHandlerPerformanceOutputForm> list = treatProjectSchemeHandlerPerformanceMapper.allTreatProjectSchemeHandlerPerformance(form.EntityID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatProjectSchemeHandlerPerformance(TreatProjectSchemeHandlersCommissionAddForm form) {

        treatProjectSchemeHandlerPerformanceMapper.deleteTreatProjectSchemeHandler(form.EntityID);
        treatProjectSchemeHandlerPerformanceMapper.deleteTreatProjectSchemeHandlerJobType(form.EntityID);

        if (form.Handler != null && form.Handler.size() > 0) {

            List listTreatProjectSchemeHandlerPerformanceEntity = new ArrayList<>();

            List listTreatProjectSchemeHandlerJobTypePerformanceEntity = new ArrayList<>();

            for (TreatProjectHandlerCommissionAddForm treatProjectSchemeHandlerAddForm : form.Handler) {
                if (treatProjectSchemeHandlerAddForm.PayRate != null ||
                        treatProjectSchemeHandlerAddForm.CardRate != null ||
                        treatProjectSchemeHandlerAddForm.CardLargessRate != null ||
                        treatProjectSchemeHandlerAddForm.LargessRate != null) {
                    TreatProjectSchemeHandlerPerformanceEntity treatProjectSchemeHandlerPerformanceEntity = new TreatProjectSchemeHandlerPerformanceEntity();
                    treatProjectSchemeHandlerPerformanceEntity.EntityID = form.EntityID;
                    treatProjectSchemeHandlerPerformanceEntity.ProjectTreatHandlerID = treatProjectSchemeHandlerAddForm.TreatHandlerID;
                    treatProjectSchemeHandlerPerformanceEntity.PayRate = treatProjectSchemeHandlerAddForm.PayRate;
                    treatProjectSchemeHandlerPerformanceEntity.CardRate = treatProjectSchemeHandlerAddForm.CardRate;
                    treatProjectSchemeHandlerPerformanceEntity.CardLargessRate = treatProjectSchemeHandlerAddForm.CardLargessRate;
                    treatProjectSchemeHandlerPerformanceEntity.LargessRate = treatProjectSchemeHandlerAddForm.LargessRate;
                    listTreatProjectSchemeHandlerPerformanceEntity.add(treatProjectSchemeHandlerPerformanceEntity);
                }

                if (treatProjectSchemeHandlerAddForm.HandlerJobTypeCommission != null && treatProjectSchemeHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (TreatProjectCategoryHandlerJobTypeCommissionAddForm treatProjectSchemeHandlerJobTypePerformanceAddForm : treatProjectSchemeHandlerAddForm.HandlerJobTypeCommission) {
                        if (treatProjectSchemeHandlerJobTypePerformanceAddForm.PayRate != null ||
                                treatProjectSchemeHandlerJobTypePerformanceAddForm.CardRate != null ||
                                treatProjectSchemeHandlerJobTypePerformanceAddForm.CardLargessRate != null ||
                                treatProjectSchemeHandlerJobTypePerformanceAddForm.LargessRate != null) {
                            TreatProjectSchemeHandlerJobTypePerformanceEntity treatProjectSchemeHandlerJobTypePerformanceEntity = new TreatProjectSchemeHandlerJobTypePerformanceEntity();
                            treatProjectSchemeHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            treatProjectSchemeHandlerJobTypePerformanceEntity.ProjectTreatHandlerID = treatProjectSchemeHandlerAddForm.TreatHandlerID;
                            treatProjectSchemeHandlerJobTypePerformanceEntity.JobTypeID = treatProjectSchemeHandlerJobTypePerformanceAddForm.JobTypeID;
                            treatProjectSchemeHandlerJobTypePerformanceEntity.PayRate = treatProjectSchemeHandlerJobTypePerformanceAddForm.PayRate;
                            treatProjectSchemeHandlerJobTypePerformanceEntity.CardRate = treatProjectSchemeHandlerJobTypePerformanceAddForm.CardRate;
                            treatProjectSchemeHandlerJobTypePerformanceEntity.CardLargessRate = treatProjectSchemeHandlerJobTypePerformanceAddForm.CardLargessRate;
                            treatProjectSchemeHandlerJobTypePerformanceEntity.LargessRate = treatProjectSchemeHandlerJobTypePerformanceAddForm.LargessRate;
                            listTreatProjectSchemeHandlerJobTypePerformanceEntity.add(treatProjectSchemeHandlerJobTypePerformanceEntity);
                        }
                    }

                }
            }
            if (listTreatProjectSchemeHandlerPerformanceEntity != null && listTreatProjectSchemeHandlerPerformanceEntity.size() > 0) {
                List<List<TreatProjectSchemeHandlerPerformanceEntity>> subs = Lists.partition(listTreatProjectSchemeHandlerPerformanceEntity, 20);
                for (List<TreatProjectSchemeHandlerPerformanceEntity> entityList : subs) {
                    treatProjectSchemeHandlerPerformanceMapper.updateTreatProjectSchemeHandlerPerformance(entityList);
                }
            }
            if (listTreatProjectSchemeHandlerJobTypePerformanceEntity != null && listTreatProjectSchemeHandlerJobTypePerformanceEntity.size() > 0) {
                List<List<TreatProjectSchemeHandlerJobTypePerformanceEntity>> subs = Lists.partition(listTreatProjectSchemeHandlerJobTypePerformanceEntity, 20);
                for (List<TreatProjectSchemeHandlerJobTypePerformanceEntity> entityList : subs) {
                    treatProjectSchemeHandlerPerformanceMapper.insertTreatProjectSchemeHandlerJobTypePerformance(entityList);
                }
            }
        }
        return BaseOutput.success();
    }
}