package com.zhelian.service.khs.role;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.zhelian.core.redis.utils.RedisAuthenticationUtil;
import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.khs.EmployeeMapper;
import com.zhelian.mapper.khs.RoleMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.khs.entity.EmployeeEntity;
import com.zhelian.model.khs.entity.RoleEntity;
import com.zhelian.model.khs.entity.RoleMenuPermissionEntity;
import com.zhelian.model.khs.form.input.*;
import com.zhelian.model.khs.form.output.RoleMenuPermissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Service
public class RoleService {

    @Autowired
    RoleMapper roleMapper;

    @Autowired
    EmployeeMapper employeeMapper;

    public BaseOutput roleList(RoleQueryForm form) {

        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<RoleEntity> list = roleMapper.roleList(form.RoleName);
        return BasePageInfo.success(list);
    }

    public BaseOutput allRole(AllRoleQueryForm form) {
        List<RoleEntity> list = roleMapper.roleList(form.RoleName);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput addRole(RoleAddForm form) {


        RoleEntity roleEntity = new RoleEntity();
        roleEntity.Name = form.Name;
        roleMapper.addRole(roleEntity);

        List<RoleMenuPermissionEntity> list = new ArrayList<>();

        assembly(list, form.MenuPermission, roleEntity.ID);

        roleMapper.insertRoleMenuPermissionList(list);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateRole(RoleUpdateForm form) {

        roleMapper.updateRole(form);

        roleMapper.deleteRoleMenuPermissionByID(form.ID);

        List<RoleMenuPermissionEntity> list = new ArrayList<>();

        assembly(list, form.MenuPermission, form.ID);

        if (list != null && list.size() > 0) {
            List<List<RoleMenuPermissionEntity>> subs = Lists.partition(list, 20);
            for (List<RoleMenuPermissionEntity> entityList : subs) {
                roleMapper.insertRoleMenuPermissionList(entityList);
            }
        }

        List<EmployeeEntity> employeeEntities = employeeMapper.getEmployeeByRoleID(form.ID);
        if (employeeEntities.size() > 0) {
            employeeEntities.forEach(employeeEntity -> {
                RedisAuthenticationUtil.delete(employeeEntity.ID);
            });
        }

        return BaseOutput.success();
    }


    void assembly(List<RoleMenuPermissionEntity> list, Set<String> MenuPermission, int RoleID) {
        for (String menu : MenuPermission) {
            RoleMenuPermissionEntity roleMenuPermissionEntity = new RoleMenuPermissionEntity();
            roleMenuPermissionEntity.MenuPermissionCode = menu;
            roleMenuPermissionEntity.RoleID = RoleID;
            list.add(roleMenuPermissionEntity);
        }
    }

    public BaseOutput getRoleMenuPermissionlist(RoleMenuPermissionQueryForm form) {

        List<RoleMenuPermissionOutputForm> roleMenuPermissionOutputForms = roleMapper.getRoleMenuPermissionByType(form);
        JSONArray result = ListUtil.listToTree(JSONArray.parseArray(JSON.toJSONString(roleMenuPermissionOutputForms, JSONWriter.Feature.WriteMapNullValue)), "Code", "ParentCode", "Child");
        return BaseOutputForm.success(result);
    }

}
