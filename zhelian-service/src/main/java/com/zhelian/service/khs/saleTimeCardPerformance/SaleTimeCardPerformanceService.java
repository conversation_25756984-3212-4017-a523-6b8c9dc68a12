package com.zhelian.service.khs.saleTimeCardPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.SaleTimeCardPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardsCommissionAddForm;
import com.zhelian.model.khs.entity.TBSaleTimeCardPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleTimeCardPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleTimeCardPerformanceService {

    @Autowired
    SaleTimeCardPerformanceMapper saleTimeCardPerformanceMapper;


    public BaseOutput allSaleTimeCardPerformance(SaleTimeCardCategoryHandlerCommissionQueryForm form) {
        List<SaleTimeCardPerformanceOutputForm> list = saleTimeCardPerformanceMapper.allSaleTimeCardPerformance(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleTimeCardPerformance(SaleTimeCardsCommissionAddForm form) {

        saleTimeCardPerformanceMapper.deleteSaleTimeCardPerformanceByEntityIDAndCategory(form.EntityID, form.CategoryID);
        List list = new ArrayList<>();
        if (form.TimeCard != null && form.TimeCard.size() > 0) {
            for (SaleTimeCardCommissionAddForm saleTimeCardPerformanceAddForm : form.TimeCard) {
                if (saleTimeCardPerformanceAddForm.PayRate != null ||
                        saleTimeCardPerformanceAddForm.SavingCardRate != null ||
                        saleTimeCardPerformanceAddForm.SavingCardLargessRate != null) {
                    TBSaleTimeCardPerformanceEntity saleTimeCardPerformanceEntity = new TBSaleTimeCardPerformanceEntity();
                    saleTimeCardPerformanceEntity.EntityID = form.EntityID;
                    saleTimeCardPerformanceEntity.TimeCardID = saleTimeCardPerformanceAddForm.TimeCardID;
                    saleTimeCardPerformanceEntity.PayRate = saleTimeCardPerformanceAddForm.PayRate;
                    saleTimeCardPerformanceEntity.SavingCardRate = saleTimeCardPerformanceAddForm.SavingCardRate;
                    saleTimeCardPerformanceEntity.SavingCardLargessRate = saleTimeCardPerformanceAddForm.SavingCardLargessRate;
                    list.add(saleTimeCardPerformanceEntity);
                }
            }

            if (list != null && list.size() > 0) {
                List<List<TBSaleTimeCardPerformanceEntity>> subs = Lists.partition(list, 20);
                for (List<TBSaleTimeCardPerformanceEntity> entityList : subs) {
                    saleTimeCardPerformanceMapper.insertSaleTimeCardPerformance(entityList);
                }
            }
        }
        return BaseOutput.success();
    }

}