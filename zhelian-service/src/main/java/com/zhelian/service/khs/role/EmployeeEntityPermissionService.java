package com.zhelian.service.khs.role;

import com.google.common.collect.Lists;
import com.zhelian.core.redis.utils.RedisAuthenticationUtil;
import com.zhelian.mapper.khs.EmployeeEntityPermissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.khs.entity.EmployeeEntityPermissionEntity;
import com.zhelian.model.khs.form.input.EmployeeEntityPermissionBatchUpdateForm;
import com.zhelian.model.khs.form.input.EmployeeEntityPermissionQueryForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class EmployeeEntityPermissionService {

    @Autowired
    EmployeeEntityPermissionMapper employeeEntityPermissionMapper;


    public BaseOutput employeeEntityPermission(EmployeeEntityPermissionQueryForm form) {
        return BaseOutputForm.success(employeeEntityPermissionMapper.employeeEntityPermission(form.EmployeeID));
    }


    @Transactional
    public BaseOutput updateUserEntityPermissionBatch(EmployeeEntityPermissionBatchUpdateForm form) {

        employeeEntityPermissionMapper.deleteEmployeeEntityPermissionByEmployeeList(form.EmployeeID);


        List<EmployeeEntityPermissionEntity> list = new ArrayList<>();
        if (form.EntityID != null && form.EntityID.size() > 0) {
            for (String EmployeeID : form.EmployeeID) {
                RedisAuthenticationUtil.delete(EmployeeID);
                for (Integer EntityID : form.EntityID) {
                    EmployeeEntityPermissionEntity employeeEntityPermissionEntity = new EmployeeEntityPermissionEntity();
                    employeeEntityPermissionEntity.EmployeeID = EmployeeID;
                    employeeEntityPermissionEntity.EntityID = EntityID;
                    list.add(employeeEntityPermissionEntity);
                }
            }
            if (list != null && list.size() > 0) {
                List<List<EmployeeEntityPermissionEntity>> subs = Lists.partition(list, 20);
                for (List<EmployeeEntityPermissionEntity> entityList : subs) {
                    employeeEntityPermissionMapper.insertEmployeeEntityPermissionByList(entityList);
                }
            }
        }

        return BaseOutput.success();
    }
}
