package com.zhelian.service.khs.salarySettlement;


import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.khs.EntityCommissionSchemeMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.khs.entity.EntityCommissionEmployeeEntity;
import com.zhelian.model.khs.entity.EntityCommissionEmployeeEntityEntity;
import com.zhelian.model.khs.entity.EntityCommissionEntity;
import com.zhelian.model.khs.entity.EntityCommissionSchemeEntity;
import com.zhelian.model.khs.form.input.*;
import com.zhelian.model.khs.form.output.EmployeeCommissionOutForm;
import com.zhelian.model.khs.form.output.EntityCalculateEmployeeOutForm;
import com.zhelian.model.khs.form.output.EntityCommissionSchemeOutForm;
import com.zhelian.model.khs.form.output.EntityEmployeeOutForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class EntityCommissionSchemeService {

    @Autowired
    EntityCommissionSchemeMapper entityCommissionSchemeMapper;


    public BaseOutput all(EntityCommissionSchemeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<EntityCommissionSchemeOutForm> list = entityCommissionSchemeMapper.getEntityCommissionScheme(form.Name, form.PerformanceEvaluationSchemeID, form.PerformanceCalculationSchemeID, form.Active);
        return BasePageInfo.success(list);
    }

    @Transactional
    public BaseOutput create(EntityCommissionSchemeAddForm form) {
        EntityCommissionSchemeEntity entityCommissionSchemeEntity = new EntityCommissionSchemeEntity();
        entityCommissionSchemeEntity.Name = form.Name;
        entityCommissionSchemeEntity.PerformanceEvaluationSchemeID = form.PerformanceEvaluationSchemeID;
        entityCommissionSchemeEntity.PerformanceCalculationSchemeID = form.PerformanceCalculationSchemeID;
        entityCommissionSchemeEntity.DeductPersonalEvaluationPerformance = form.DeductPersonalEvaluationPerformance;
        entityCommissionSchemeEntity.DeductPersonalCalculationPerformance = form.DeductPersonalCalculationPerformance;
        entityCommissionSchemeEntity.Calculation = form.Calculation;
        entityCommissionSchemeMapper.createEntityCommissionScheme(entityCommissionSchemeEntity);
        List<EntityCommissionEntity> commissionEntities = new ArrayList<>();
        List<EntityCommissionEmployeeEntityEntity> employeeEntity = new ArrayList<>();
        if (form.Commission != null && form.Commission.size() > 0) {
            for (EntityCommissionAddForm entityCommissionAddForm : form.Commission) {
                EntityCommissionEntity entityCommissionEntity = new EntityCommissionEntity();
                entityCommissionEntity.EntityCommissionSchemeID = entityCommissionSchemeEntity.ID;
                entityCommissionEntity.BeginPerformance = entityCommissionAddForm.BeginPerformance;
                entityCommissionEntity.EndPerformance = entityCommissionAddForm.EndPerformance;
                entityCommissionEntity.Rate = entityCommissionAddForm.Rate;
                entityCommissionEntity.Fixed = entityCommissionAddForm.Fixed;
                commissionEntities.add(entityCommissionEntity);
            }
        }
        if (form.Handler != null && form.Handler.size() > 0) {
            for (EntityCommissionHandlerAddForm entityCommissionHandlerAddForm : form.Handler) {
                EntityCommissionEmployeeEntity employeeCommissionEntity = new EntityCommissionEmployeeEntity();
                employeeCommissionEntity.CommissionSchemeID = entityCommissionSchemeEntity.ID;
                employeeCommissionEntity.EmployeeID = entityCommissionHandlerAddForm.EmployeeID;
                entityCommissionSchemeMapper.createEntityCommissionEmployee(employeeCommissionEntity);
                for (Integer i : entityCommissionHandlerAddForm.Entity) {
                    EntityCommissionEmployeeEntityEntity entityCommissionEmployeeEntityEntity = new EntityCommissionEmployeeEntityEntity();
                    entityCommissionEmployeeEntityEntity.EntityCommissionEmployeeID = employeeCommissionEntity.ID;
                    entityCommissionEmployeeEntityEntity.EntityID = i;
                    employeeEntity.add(entityCommissionEmployeeEntityEntity);
                }
            }
        }
        if (commissionEntities.size() > 0)
            entityCommissionSchemeMapper.createEnitityCommission(commissionEntities);

        if (employeeEntity.size() > 0)
            entityCommissionSchemeMapper.createEmployeeCommissionEntity(employeeEntity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput update(EntityCommissionSchemeUpdateForm form) {
        EntityCommissionSchemeEntity entityCommissionSchemeEntity = new EntityCommissionSchemeEntity();
        entityCommissionSchemeEntity.ID = form.ID;
        entityCommissionSchemeEntity.Name = form.Name;
        entityCommissionSchemeEntity.PerformanceEvaluationSchemeID = form.PerformanceEvaluationSchemeID;
        entityCommissionSchemeEntity.PerformanceCalculationSchemeID = form.PerformanceCalculationSchemeID;
        entityCommissionSchemeEntity.DeductPersonalCalculationPerformance = form.DeductPersonalCalculationPerformance;
        entityCommissionSchemeEntity.DeductPersonalEvaluationPerformance = form.DeductPersonalEvaluationPerformance;
        entityCommissionSchemeEntity.Calculation = form.Calculation;
        entityCommissionSchemeEntity.Active = form.Active;
        entityCommissionSchemeMapper.updateEntityCommissionScheme(entityCommissionSchemeEntity);
        entityCommissionSchemeMapper.deleteCommission(form.ID);
        entityCommissionSchemeMapper.deleteCommissionEmployee(form.ID);
        entityCommissionSchemeMapper.deleteCommissionEmployeeEntity(form.ID);
        List<EntityCommissionEntity> commissionEntities = new ArrayList<>();
        List<EntityCommissionEmployeeEntityEntity> employeeEntity = new ArrayList<>();
        if (form.Commission != null && form.Commission.size() > 0) {
            for (EntityCommissionAddForm entityCommissionAddForm : form.Commission) {
                EntityCommissionEntity entityCommissionEntity = new EntityCommissionEntity();
                entityCommissionEntity.EntityCommissionSchemeID = entityCommissionSchemeEntity.ID;
                entityCommissionEntity.BeginPerformance = entityCommissionAddForm.BeginPerformance;
                entityCommissionEntity.EndPerformance = entityCommissionAddForm.EndPerformance;
                entityCommissionEntity.Rate = entityCommissionAddForm.Rate;
                entityCommissionEntity.Fixed = entityCommissionAddForm.Fixed;
                commissionEntities.add(entityCommissionEntity);
            }
        }
        if (form.Handler != null && form.Handler.size() > 0) {
            for (EntityCommissionHandlerAddForm entityCommissionHandlerAddForm : form.Handler) {
                EntityCommissionEmployeeEntity employeeCommissionEntity = new EntityCommissionEmployeeEntity();
                employeeCommissionEntity.CommissionSchemeID = entityCommissionSchemeEntity.ID;
                employeeCommissionEntity.EmployeeID = entityCommissionHandlerAddForm.EmployeeID;
                entityCommissionSchemeMapper.createEntityCommissionEmployee(employeeCommissionEntity);
                for (Integer i : entityCommissionHandlerAddForm.Entity) {
                    EntityCommissionEmployeeEntityEntity entityCommissionEmployeeEntityEntity = new EntityCommissionEmployeeEntityEntity();
                    entityCommissionEmployeeEntityEntity.EntityCommissionEmployeeID = employeeCommissionEntity.ID;
                    entityCommissionEmployeeEntityEntity.EntityID = i;
                    employeeEntity.add(entityCommissionEmployeeEntityEntity);
                }
            }
        }
        if (commissionEntities.size() > 0)
            entityCommissionSchemeMapper.createEnitityCommission(commissionEntities);

        if (employeeEntity.size() > 0)
            entityCommissionSchemeMapper.createEmployeeCommissionEntity(employeeEntity);
        return BaseOutput.success();
    }

    public BaseOutput commission(EntityCommissionQueryForm form) {
        List<EmployeeCommissionOutForm> list = entityCommissionSchemeMapper.getEntityCommission(form.ID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allEmployee(EntityCommissionQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<EntityEmployeeOutForm> list = entityCommissionSchemeMapper.allEmployee(form.Name, form.JobID, form.State);
        return BasePageInfo.success(list);
    }

    public BaseOutput employee(EntityCommissionQueryForm form) {
        List<EntityCalculateEmployeeOutForm> list = entityCommissionSchemeMapper.employee(form.ID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput employeeCalculateEntity(EntityCommissionQueryForm form) {
        return BaseOutputForm.success(entityCommissionSchemeMapper.employeeCalculateEntity(form.ID));
    }
}
