package com.zhelian.service.khs.treatTimeCardPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.TreatTimeCardProjectHandlerPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCommissionQueryExtendForm;
import com.zhelian.model.ibeauty.form.input.TreatCardHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardProjectHandlersCommissionAddForm;
import com.zhelian.model.khs.entity.TreatTimeCardProjectHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.TreatTimeCardProjectHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.TreatGoodCategoryHandlerPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatTimeCardProjectHandlerPerformanceService {

    @Autowired
    TreatTimeCardProjectHandlerPerformanceMapper treatTimeCardProjectHandlerPerformanceMapper;


    public BaseOutput allTreatTimeCardProjectHandlerPerformance(TreatCardCommissionQueryExtendForm form) {
        List<TreatGoodCategoryHandlerPerformanceOutputForm> list = treatTimeCardProjectHandlerPerformanceMapper.allTreatTimeCardProjectHandlerPerformance(form.EntityID, form.CardID, form.ProjectID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatTimeCardProjectHandlerPerformance(TreatCardProjectHandlersCommissionAddForm form) {

        treatTimeCardProjectHandlerPerformanceMapper.deleteTreatTimeCardProjectHandler(form.EntityID, form.CardID, form.ProjectID);
        treatTimeCardProjectHandlerPerformanceMapper.deleteTreatTimeCardProjectHandlerJobType(form.EntityID, form.CardID, form.ProjectID);
        List<TreatTimeCardProjectHandlerPerformanceEntity> treatTimeCardHandlerPerformanceList = new ArrayList<>();
        List<TreatTimeCardProjectHandlerJobTypePerformanceEntity> treatTimeCardHandlerJobTypePerformanceList = new ArrayList<>();
        if (form.CardHandler != null && form.CardHandler.size() > 0) {
            for (TreatCardHandlerCommissionAddForm treatCardHandlerPerformanceAddForm : form.CardHandler) {
                if (treatCardHandlerPerformanceAddForm.PayRate != null ||
                        treatCardHandlerPerformanceAddForm.CardRate != null ||
                        treatCardHandlerPerformanceAddForm.CardLargessRate != null ||
                        treatCardHandlerPerformanceAddForm.LargessRate != null) {
                    TreatTimeCardProjectHandlerPerformanceEntity treatTimeCardHandlerPerformanceEntity = new TreatTimeCardProjectHandlerPerformanceEntity();
                    treatTimeCardHandlerPerformanceEntity.EntityID = form.EntityID;
                    treatTimeCardHandlerPerformanceEntity.TimeCardID = form.CardID;
                    treatTimeCardHandlerPerformanceEntity.TimeCardProjectTreatHandlerID = treatCardHandlerPerformanceAddForm.TreatHandlerID;
                    treatTimeCardHandlerPerformanceEntity.ProjectID = form.ProjectID;
                    treatTimeCardHandlerPerformanceEntity.PayRate = treatCardHandlerPerformanceAddForm.PayRate;
                    treatTimeCardHandlerPerformanceEntity.CardRate = treatCardHandlerPerformanceAddForm.CardRate;
                    treatTimeCardHandlerPerformanceEntity.CardLargessRate = treatCardHandlerPerformanceAddForm.CardLargessRate;
                    treatTimeCardHandlerPerformanceEntity.LargessRate = treatCardHandlerPerformanceAddForm.LargessRate;
                    treatTimeCardHandlerPerformanceList.add(treatTimeCardHandlerPerformanceEntity);
                }
                if (treatCardHandlerPerformanceAddForm.HandlerJobTypeCommission != null && treatCardHandlerPerformanceAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (TreatCardCategoryHandlerJobTypeCommissionAddForm typePerformanceAddForm : treatCardHandlerPerformanceAddForm.HandlerJobTypeCommission) {
                        if (typePerformanceAddForm.PayRate != null ||
                                typePerformanceAddForm.CardRate != null ||
                                typePerformanceAddForm.CardLargessRate != null ||
                                typePerformanceAddForm.LargessRate != null) {
                            TreatTimeCardProjectHandlerJobTypePerformanceEntity PerformanceEntity = new TreatTimeCardProjectHandlerJobTypePerformanceEntity();
                            PerformanceEntity.EntityID = form.EntityID;
                            PerformanceEntity.TimeCardID = form.CardID;
                            PerformanceEntity.TimeCardProjectTreatHandlerID = treatCardHandlerPerformanceAddForm.TreatHandlerID;
                            PerformanceEntity.JobTypeID = typePerformanceAddForm.JobTypeID;
                            PerformanceEntity.ProjectID = form.ProjectID;
                            PerformanceEntity.PayRate = typePerformanceAddForm.PayRate;
                            PerformanceEntity.CardRate = typePerformanceAddForm.CardRate;
                            PerformanceEntity.CardLargessRate = typePerformanceAddForm.CardLargessRate;
                            PerformanceEntity.LargessRate = typePerformanceAddForm.LargessRate;
                            treatTimeCardHandlerJobTypePerformanceList.add(PerformanceEntity);
                        }
                    }
                }
            }
        }
        if (treatTimeCardHandlerPerformanceList != null && treatTimeCardHandlerPerformanceList.size() > 0) {
            List<List<TreatTimeCardProjectHandlerPerformanceEntity>> subs = Lists.partition(treatTimeCardHandlerPerformanceList, 20);
            for (List<TreatTimeCardProjectHandlerPerformanceEntity> entityList : subs) {
                treatTimeCardProjectHandlerPerformanceMapper.insertTreatTimeCardProjectHandlerPerformance(entityList);
            }
        }
        if (treatTimeCardHandlerJobTypePerformanceList != null && treatTimeCardHandlerJobTypePerformanceList.size() > 0) {
            List<List<TreatTimeCardProjectHandlerJobTypePerformanceEntity>> subs = Lists.partition(treatTimeCardHandlerJobTypePerformanceList, 20);
            for (List<TreatTimeCardProjectHandlerJobTypePerformanceEntity> entityList : subs) {
                treatTimeCardProjectHandlerPerformanceMapper.insertTreatTimeCardProjectHandlerJobTypePerformance(entityList);
            }
        }

        return BaseOutput.success();
    }

}