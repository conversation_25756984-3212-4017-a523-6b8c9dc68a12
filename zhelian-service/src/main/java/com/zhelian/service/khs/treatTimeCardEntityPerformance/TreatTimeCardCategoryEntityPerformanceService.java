package com.zhelian.service.khs.treatTimeCardEntityPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.TreatTimeCardCategoryEntityPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryCommissionQueryForm;
import com.zhelian.model.khs.entity.TreatGoodCategoryEntityPerformanceEntity;
import com.zhelian.model.khs.entity.TreatGoodEntityPerformanceSchemeEntity;
import com.zhelian.model.khs.form.input.TreatGoodCategoryAddForm;
import com.zhelian.model.khs.form.input.TreatGoodCategoryPerformanceAddForm;
import com.zhelian.model.khs.form.output.TreatCardGoodCategoryOutputForm;
import com.zhelian.model.khs.form.output.TreatGoodCategoryPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatTimeCardCategoryEntityPerformanceService {

    @Autowired
    TreatTimeCardCategoryEntityPerformanceMapper treatTimeCardCategoryEntityPerformanceMapper;


    public BaseOutput allTreatTimeCardCategoryEntityPerformance(TreatProductCategoryCommissionQueryForm form) {
        TreatCardGoodCategoryOutputForm treatTimeCardCategoryOutputForm = treatTimeCardCategoryEntityPerformanceMapper.treatTimeCardCategoryEntityPerformance(form.EntityID);
        List<TreatGoodCategoryPerformanceOutputForm> all = treatTimeCardCategoryEntityPerformanceMapper.allTreatTimeCardCategoryEntityPerformance(form.EntityID);
        treatTimeCardCategoryOutputForm.Category = all;
        return BaseOutputForm.success(treatTimeCardCategoryOutputForm);
    }

    @Transactional
    public BaseOutput updateTreatTimeCardCategoryEntityPerformance(TreatGoodCategoryAddForm form) {
        TreatGoodEntityPerformanceSchemeEntity treatTimeCardEntityPerformanceSchemeEntity = new TreatGoodEntityPerformanceSchemeEntity();
        treatTimeCardEntityPerformanceSchemeEntity.EntityID = form.EntityID;
        treatTimeCardEntityPerformanceSchemeEntity.PayRate = form.PayRate;
        treatTimeCardEntityPerformanceSchemeEntity.CardRate = form.CardRate;
        treatTimeCardEntityPerformanceSchemeEntity.CardLargessRate = form.CardLargessRate;
        treatTimeCardEntityPerformanceSchemeEntity.LargessRate = form.LargessRate;

        treatTimeCardCategoryEntityPerformanceMapper.updateTreatTimeCardEntityPerformanceScheme(treatTimeCardEntityPerformanceSchemeEntity);

        treatTimeCardCategoryEntityPerformanceMapper.deleteTreatTimeCardCategoryEntityPerformanceEntity(form.EntityID);

        List<TreatGoodCategoryEntityPerformanceEntity> list = new ArrayList<>();
        if (form.Category != null && form.Category.size() > 0) {
            for (TreatGoodCategoryPerformanceAddForm treatTimeCardCategoryEntityPerformanceAddForm : form.Category) {
                if (treatTimeCardCategoryEntityPerformanceAddForm.PayRate != null ||
                        treatTimeCardCategoryEntityPerformanceAddForm.CardRate != null ||
                        treatTimeCardCategoryEntityPerformanceAddForm.CardLargessRate != null ||
                        treatTimeCardCategoryEntityPerformanceAddForm.LargessRate != null) {
                    TreatGoodCategoryEntityPerformanceEntity treatTimeCardCategoryEntityPerformanceEntity = new TreatGoodCategoryEntityPerformanceEntity();
                    treatTimeCardCategoryEntityPerformanceEntity.EntityID = form.EntityID;
                    treatTimeCardCategoryEntityPerformanceEntity.GoodCategoryID = treatTimeCardCategoryEntityPerformanceAddForm.CategoryID;
                    treatTimeCardCategoryEntityPerformanceEntity.PayRate = treatTimeCardCategoryEntityPerformanceAddForm.PayRate;
                    treatTimeCardCategoryEntityPerformanceEntity.CardRate = treatTimeCardCategoryEntityPerformanceAddForm.CardRate;
                    treatTimeCardCategoryEntityPerformanceEntity.CardLargessRate = treatTimeCardCategoryEntityPerformanceAddForm.CardLargessRate;
                    treatTimeCardCategoryEntityPerformanceEntity.LargessRate = treatTimeCardCategoryEntityPerformanceAddForm.LargessRate;
                    list.add(treatTimeCardCategoryEntityPerformanceEntity);
                }
            }
        }
        if (list != null && list.size() > 0) {
            List<List<TreatGoodCategoryEntityPerformanceEntity>> subs = Lists.partition(list, 20);
            for (List<TreatGoodCategoryEntityPerformanceEntity> entityList : subs) {
                treatTimeCardCategoryEntityPerformanceMapper.insertTreatTimeCardCategoryEntityPerformanceEntity(entityList);
            }
        }
        return BaseOutput.success();
    }

}