package com.zhelian.service.khs.channelSalarySettlement;


import com.zhelian.mapper.khs.ChannelPerformanceSchemeTreatSavingCardMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.khs.entity.TreatChannelGoodPerformanceEntity;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodQueryForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeTreatGoodAddForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeTreatGoodsAddForm;
import com.zhelian.model.khs.form.output.PerformanceSchemeTreatGoodOutForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class ChannelPerformanceSchemeTreatSavingCardService {

    @Autowired
    ChannelPerformanceSchemeTreatSavingCardMapper channelPerformanceSchemeTreatSavingCardMapper;

    public BaseOutput all(PerformanceSchemeGoodQueryForm form) {
        List<PerformanceSchemeTreatGoodOutForm> list = channelPerformanceSchemeTreatSavingCardMapper.getAllPerformanceSchemeSavingCard(form.PerformanceSchemeID, form.CategoryID);
        return BaseOutputForm.success(list);

    }

    @Transactional
    public BaseOutput create(PerformanceSchemeTreatGoodsAddForm form) {
        channelPerformanceSchemeTreatSavingCardMapper.deleteTreatSavingCardPerformanceByPerformanceSchemeIDAndCategory(form.PerformanceSchemeID, form.CategoryID);
        List<TreatChannelGoodPerformanceEntity> list = new ArrayList<>();
        if (form.Good != null && form.Good.size() > 0) {
            for (PerformanceSchemeTreatGoodAddForm performanceSchemeGoodAddForm : form.Good) {
                if (performanceSchemeGoodAddForm.PerformancePayRate != null || performanceSchemeGoodAddForm.PerformanceCardRate != null || performanceSchemeGoodAddForm.PerformanceLargessRate != null || performanceSchemeGoodAddForm.PerformanceLargessRate != null) {
                    TreatChannelGoodPerformanceEntity treatChannelGoodPerformanceEntity = new TreatChannelGoodPerformanceEntity();
                    treatChannelGoodPerformanceEntity.ChannelPerformanceSchemeID = form.PerformanceSchemeID;
                    treatChannelGoodPerformanceEntity.GoodID = performanceSchemeGoodAddForm.GoodID;
                    treatChannelGoodPerformanceEntity.PerformanceCardRate = performanceSchemeGoodAddForm.PerformanceCardRate;
                    treatChannelGoodPerformanceEntity.PerformanceCardLargessRate = performanceSchemeGoodAddForm.PerformanceCardLargessRate;
                    list.add(treatChannelGoodPerformanceEntity);
                }
            }
            channelPerformanceSchemeTreatSavingCardMapper.insertTreatChannelSavingCardPerformance(list);
        }
        return BaseOutput.success();
    }
}
