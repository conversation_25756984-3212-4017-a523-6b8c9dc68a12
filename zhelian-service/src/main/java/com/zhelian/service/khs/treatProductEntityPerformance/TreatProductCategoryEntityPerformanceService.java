package com.zhelian.service.khs.treatProductEntityPerformance;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.TreatProductCategoryEntityPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryCommissionQueryForm;
import com.zhelian.model.khs.entity.TreatGoodCategoryEntityPerformanceEntity;
import com.zhelian.model.khs.entity.TreatGoodEntityPerformanceSchemeEntity;
import com.zhelian.model.khs.form.input.TreatGoodCategoryAddForm;
import com.zhelian.model.khs.form.input.TreatGoodCategoryPerformanceAddForm;
import com.zhelian.model.khs.form.input.TreatGoodChildCategoryPerformanceAddForm;
import com.zhelian.model.khs.form.output.TreatGoodCategoryOutputForm;
import com.zhelian.model.khs.form.output.TreatGoodCategoryPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import static com.zhelian.core.utils.ListUtil.listToTree;


@Service
public class TreatProductCategoryEntityPerformanceService {

    @Autowired
    TreatProductCategoryEntityPerformanceMapper treatProductCategoryEntityPerformanceMapper;


    public BaseOutput allTreatProductCategoryEntityPerformance(TreatProductCategoryCommissionQueryForm form) {
        TreatGoodCategoryOutputForm treatProductCategoryOutputForm = treatProductCategoryEntityPerformanceMapper.treatProductCategoryEntityPerformance(form.EntityID);
        List<TreatGoodCategoryPerformanceOutputForm> all = treatProductCategoryEntityPerformanceMapper.allTreatProductCategoryEntityPerformance(form.EntityID);
        JSONArray result = listToTree(JSONArray.parseArray(JSON.toJSONString(all, JSONWriter.Feature.WriteMapNullValue)), "CategoryID", "ParentID", "Child");
        treatProductCategoryOutputForm.Category = result;
        return BaseOutputForm.success(treatProductCategoryOutputForm);
    }

    @Transactional
    public BaseOutput updateTreatProductCategoryEntityPerformance(TreatGoodCategoryAddForm form) {
        TreatGoodEntityPerformanceSchemeEntity treatProductEntityPerformanceSchemeEntity = new TreatGoodEntityPerformanceSchemeEntity();
        treatProductEntityPerformanceSchemeEntity.EntityID = form.EntityID;
        treatProductEntityPerformanceSchemeEntity.PayRate = form.PayRate;
        treatProductEntityPerformanceSchemeEntity.CardRate = form.CardRate;
        treatProductEntityPerformanceSchemeEntity.CardLargessRate = form.CardLargessRate;
        treatProductEntityPerformanceSchemeEntity.LargessRate = form.LargessRate;

        treatProductCategoryEntityPerformanceMapper.updateTreatProductEntityPerformanceScheme(treatProductEntityPerformanceSchemeEntity);

        treatProductCategoryEntityPerformanceMapper.deleteTreatProductCategoryEntityPerformanceEntity(form.EntityID);

        List<TreatGoodCategoryEntityPerformanceEntity> list = new ArrayList<>();
        if (form.Category != null && form.Category.size() > 0) {
            for (TreatGoodCategoryPerformanceAddForm treatProductCategoryEntityPerformanceAddForm : form.Category) {
                if (treatProductCategoryEntityPerformanceAddForm.PayRate != null ||
                        treatProductCategoryEntityPerformanceAddForm.CardRate != null ||
                        treatProductCategoryEntityPerformanceAddForm.CardLargessRate != null ||
                        treatProductCategoryEntityPerformanceAddForm.LargessRate != null) {
                    TreatGoodCategoryEntityPerformanceEntity treatProductCategoryEntityPerformanceEntity = new TreatGoodCategoryEntityPerformanceEntity();
                    treatProductCategoryEntityPerformanceEntity.EntityID = form.EntityID;
                    treatProductCategoryEntityPerformanceEntity.GoodCategoryID = treatProductCategoryEntityPerformanceAddForm.CategoryID;
                    treatProductCategoryEntityPerformanceEntity.PayRate = treatProductCategoryEntityPerformanceAddForm.PayRate;
                    treatProductCategoryEntityPerformanceEntity.CardRate = treatProductCategoryEntityPerformanceAddForm.CardRate;
                    treatProductCategoryEntityPerformanceEntity.CardLargessRate = treatProductCategoryEntityPerformanceAddForm.CardLargessRate;
                    treatProductCategoryEntityPerformanceEntity.LargessRate = treatProductCategoryEntityPerformanceAddForm.LargessRate;
                    list.add(treatProductCategoryEntityPerformanceEntity);
                }

                for (TreatGoodChildCategoryPerformanceAddForm treatProductChildCategoryEntityPerformanceAddForm : treatProductCategoryEntityPerformanceAddForm.Child) {
                    if (treatProductChildCategoryEntityPerformanceAddForm.PayRate != null ||
                            treatProductChildCategoryEntityPerformanceAddForm.CardRate != null ||
                            treatProductChildCategoryEntityPerformanceAddForm.CardLargessRate != null ||
                            treatProductChildCategoryEntityPerformanceAddForm.LargessRate != null) {
                        TreatGoodCategoryEntityPerformanceEntity treatProductCategoryEntityPerformanceEntity2 = new TreatGoodCategoryEntityPerformanceEntity();
                        treatProductCategoryEntityPerformanceEntity2.EntityID = form.EntityID;
                        treatProductCategoryEntityPerformanceEntity2.GoodCategoryID = treatProductChildCategoryEntityPerformanceAddForm.CategoryID;
                        treatProductCategoryEntityPerformanceEntity2.PayRate = treatProductChildCategoryEntityPerformanceAddForm.PayRate;
                        treatProductCategoryEntityPerformanceEntity2.CardRate = treatProductChildCategoryEntityPerformanceAddForm.CardRate;
                        treatProductCategoryEntityPerformanceEntity2.CardLargessRate = treatProductChildCategoryEntityPerformanceAddForm.CardLargessRate;
                        treatProductCategoryEntityPerformanceEntity2.LargessRate = treatProductChildCategoryEntityPerformanceAddForm.LargessRate;
                        list.add(treatProductCategoryEntityPerformanceEntity2);
                    }
                }
            }
        }
        if (list != null && list.size() > 0) {
            List<List<TreatGoodCategoryEntityPerformanceEntity>> subs = Lists.partition(list, 20);
            for (List<TreatGoodCategoryEntityPerformanceEntity> entityList : subs) {
                treatProductCategoryEntityPerformanceMapper.insertTreatProductCategoryEntityPerformanceEntity(entityList);
            }
        }
        return BaseOutput.success();
    }

}