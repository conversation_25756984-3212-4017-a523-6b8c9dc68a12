package com.zhelian.service.khs.salarySettlement;


import com.zhelian.mapper.khs.PerformanceSchemeTreatProductMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.khs.entity.TreatProductPerformanceEntity;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodQueryForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeTreatGoodAddForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeTreatGoodsAddForm;
import com.zhelian.model.khs.form.output.PerformanceSchemeTreatGoodOutForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class PerformanceSchemeTreatProductService {

    @Autowired
    PerformanceSchemeTreatProductMapper performanceSchemeTreatProductMapper;

    public BaseOutput all(PerformanceSchemeGoodQueryForm form) {
        List<PerformanceSchemeTreatGoodOutForm> list = performanceSchemeTreatProductMapper.getAllPerformanceSchemeProduct(form.PerformanceSchemeID, form.CategoryID);
        return BaseOutputForm.success(list);

    }

    @Transactional
    public BaseOutput create(PerformanceSchemeTreatGoodsAddForm form) {
        performanceSchemeTreatProductMapper.deleteTreatProductPerformanceByPerformanceSchemeIDAndCategory(form.PerformanceSchemeID, form.CategoryID);
        List<TreatProductPerformanceEntity> list = new ArrayList<>();
        if (form.Good != null && form.Good.size() > 0) {
            for (PerformanceSchemeTreatGoodAddForm performanceSchemeGoodAddForm : form.Good) {
                if (performanceSchemeGoodAddForm.PerformancePayRate != null || performanceSchemeGoodAddForm.PerformanceCardRate != null || performanceSchemeGoodAddForm.PerformanceLargessRate != null || performanceSchemeGoodAddForm.PerformanceLargessRate != null) {
                    TreatProductPerformanceEntity TreatProductCommissionEntity = new TreatProductPerformanceEntity();
                    TreatProductCommissionEntity.PerformanceSchemeID = form.PerformanceSchemeID;
                    TreatProductCommissionEntity.ProductID = performanceSchemeGoodAddForm.GoodID;
                    TreatProductCommissionEntity.PerformancePayRate = performanceSchemeGoodAddForm.PerformancePayRate;
                    TreatProductCommissionEntity.PerformanceCardRate = performanceSchemeGoodAddForm.PerformanceCardRate;
                    TreatProductCommissionEntity.PerformanceCardLargessRate = performanceSchemeGoodAddForm.PerformanceCardLargessRate;
                    TreatProductCommissionEntity.PerformanceLargessRate = performanceSchemeGoodAddForm.PerformanceLargessRate;
                    list.add(TreatProductCommissionEntity);
                }
            }
            performanceSchemeTreatProductMapper.insertTreatProductPerformance(list);
        }
        return BaseOutput.success();
    }
}
