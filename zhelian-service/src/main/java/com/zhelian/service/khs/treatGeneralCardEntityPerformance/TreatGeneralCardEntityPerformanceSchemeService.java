package com.zhelian.service.khs.treatGeneralCardEntityPerformance;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.khs.TreatGeneralCardCategoryEntityPerformanceMapper;
import com.zhelian.mapper.khs.TreatGeneralCardEntityPerformanceMapper;
import com.zhelian.mapper.khs.TreatGeneralCardEntityPerformanceSchemeMapper;
import com.zhelian.mapper.khs.TreatGeneralCardProjectEntityPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.form.input.TreatProductCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCommissionSchemeDeleteForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCommissionSchemeQueryForm;
import com.zhelian.model.ibeauty.form.output.TreatProductCommissionSchemeOutputForm;
import com.zhelian.model.khs.entity.TreatGoodEntityPerformanceSchemeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Service
public class TreatGeneralCardEntityPerformanceSchemeService {

    @Autowired
    TreatGeneralCardEntityPerformanceSchemeMapper treatGeneralCardEntityPerformanceSchemeMapper;
    @Autowired
    TreatGeneralCardCategoryEntityPerformanceMapper treatGeneralCardCategoryEntityPerformanceMapper;
    @Autowired
    TreatGeneralCardEntityPerformanceMapper treatGeneralCardEntityPerformanceMapper;
    @Autowired
    TreatGeneralCardProjectEntityPerformanceMapper treatGeneralCardProjectEntityPerformanceMapper;

    public BaseOutput treatGeneralCardEntityPerformanceSchemeList(TreatProductCommissionSchemeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<TreatProductCommissionSchemeOutputForm> list = treatGeneralCardEntityPerformanceSchemeMapper.treatGeneralCardEntityPerformanceSchemeList(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput createTreatGeneralCardEntityPerformanceScheme(TreatProductCommissionSchemeAddForm form) {
        Integer count = treatGeneralCardEntityPerformanceSchemeMapper.getTreatGeneralCardEntityPerformanceScheme(form.EntityID);
        if (count > 0)
            return BaseOutput.failed("组织单位已经存在");
        TreatGoodEntityPerformanceSchemeEntity treatGeneralCardEntityPerformanceSchemeEntity = new TreatGoodEntityPerformanceSchemeEntity();
        treatGeneralCardEntityPerformanceSchemeEntity.EntityID = form.EntityID;
        treatGeneralCardEntityPerformanceSchemeMapper.createTreatGeneralCardEntityPerformanceScheme(treatGeneralCardEntityPerformanceSchemeEntity);
        return BaseOutput.success();

    }

    @Transactional
    public BaseOutput deleteTreatGeneralCardEntityPerformanceScheme(TreatProductCommissionSchemeDeleteForm form) {
        treatGeneralCardEntityPerformanceSchemeMapper.deleteTreatGeneralCardEntityPerformanceScheme(form.EntityID);
        treatGeneralCardCategoryEntityPerformanceMapper.deleteTreatGeneralCardCategoryEntityPerformanceEntity(form.EntityID);
        treatGeneralCardEntityPerformanceMapper.deleteTreatGeneralCardEntityPerformanceByEntity(form.EntityID);
        treatGeneralCardProjectEntityPerformanceMapper.deleteTreatGeneralCardProjectEntityPerformanceByEntityID(form.EntityID);

        return BaseOutput.success();
    }
}