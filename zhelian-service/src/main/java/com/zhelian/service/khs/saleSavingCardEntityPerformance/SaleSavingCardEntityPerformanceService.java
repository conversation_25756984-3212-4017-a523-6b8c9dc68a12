package com.zhelian.service.khs.saleSavingCardEntityPerformance;


import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.SaleSavingCardEntityPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardCategoryHandlerCommissionQueryForm;
import com.zhelian.model.khs.entity.SaleGoodPerformanceEntity;
import com.zhelian.model.khs.form.input.SaleGoodCommissionAddForm;
import com.zhelian.model.khs.form.input.SaleGoodsPerformanceAddForm;
import com.zhelian.model.khs.form.output.SaleSavingCardPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleSavingCardEntityPerformanceService {

    @Autowired
    SaleSavingCardEntityPerformanceMapper saleSavingCardEntityPerformanceMapper;

    public BaseOutput allSaleSavingCardEntityPerformance(SaleSavingCardCategoryHandlerCommissionQueryForm form) {
        List<SaleSavingCardPerformanceOutputForm> list = saleSavingCardEntityPerformanceMapper.allSaleSavingCardEntityPerformance(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleSavingCardEntityPerformance(SaleGoodsPerformanceAddForm form) {

        saleSavingCardEntityPerformanceMapper.deleteSaleSavingCardEntityPerformanceByEntityIDAndCategory(form.EntityID, form.CategoryID);
        List<SaleGoodPerformanceEntity> list = new ArrayList<>();
        if (form.Good != null && form.Good.size() > 0) {
            for (SaleGoodCommissionAddForm saleSavingCardEntityPerformanceAddForm : form.Good) {
                if (saleSavingCardEntityPerformanceAddForm.PayRate != null ||
                        saleSavingCardEntityPerformanceAddForm.SavingCardRate != null ||
                        saleSavingCardEntityPerformanceAddForm.SavingCardLargessRate != null) {
                    SaleGoodPerformanceEntity saleSavingCardEntityPerformanceEntity = new SaleGoodPerformanceEntity();
                    saleSavingCardEntityPerformanceEntity.EntityID = form.EntityID;
                    saleSavingCardEntityPerformanceEntity.GoodID = saleSavingCardEntityPerformanceAddForm.GoodID;
                    saleSavingCardEntityPerformanceEntity.PayRate = saleSavingCardEntityPerformanceAddForm.PayRate;
                    saleSavingCardEntityPerformanceEntity.SavingCardRate = saleSavingCardEntityPerformanceAddForm.SavingCardRate;
                    saleSavingCardEntityPerformanceEntity.SavingCardLargessRate = saleSavingCardEntityPerformanceAddForm.SavingCardLargessRate;
                    list.add(saleSavingCardEntityPerformanceEntity);
                }
            }
        }
        if (list != null && list.size() > 0) {
            List<List<SaleGoodPerformanceEntity>> subs = Lists.partition(list, 20);
            for (List<SaleGoodPerformanceEntity> entityList : subs) {
                saleSavingCardEntityPerformanceMapper.insertSaleSavingCardEntityPerformance(entityList);
            }
        }
        return BaseOutput.success();
    }

}