package com.zhelian.service.khs.treatSavingCardPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.TreatSavingCardCategoryPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCategoryCommissionQueryForm;
import com.zhelian.model.ibeauty.form.output.TreatSavingCardCategoryOutputForm;
import com.zhelian.model.ibeauty.form.output.TreatSavingCardChildCategoryCommissionOutputForm;
import com.zhelian.model.khs.entity.TreatSavingCardCategoryPerformanceEntity;
import com.zhelian.model.khs.entity.TreatSavingCardPerformanceSchemeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class TreatSavingCardCategoryPerformanceService {

    @Autowired
    TreatSavingCardCategoryPerformanceMapper treatSavingCardCategoryPerformanceMapper;


    public BaseOutput alltreatSavingCardCategoryPerformance(TreatSavingCardProjectCategoryCommissionQueryForm form) {
        TreatSavingCardCategoryOutputForm treatSavingCardCategoryOutputForm = treatSavingCardCategoryPerformanceMapper.treatSavingCardCategoryPerformance(form.EntityID);
        List<TreatSavingCardChildCategoryCommissionOutputForm> all = treatSavingCardCategoryPerformanceMapper.allTreatSavingCardCategoryPerformance(form.EntityID);
        treatSavingCardCategoryOutputForm.Category = all;
        return BaseOutputForm.success(treatSavingCardCategoryOutputForm);
    }

    @Transactional
    public BaseOutput updateTreatSavingCardCategoryPerformance(TreatSavingCardProjectCategoryAddForm form) {
        TreatSavingCardPerformanceSchemeEntity treatSavingCardPerformanceSchemeEntity = new TreatSavingCardPerformanceSchemeEntity();
        treatSavingCardPerformanceSchemeEntity.EntityID = form.EntityID;
        treatSavingCardPerformanceSchemeEntity.CardRate = form.Rate;
        treatSavingCardPerformanceSchemeEntity.CardLargessRate = form.LargessRate;

        treatSavingCardCategoryPerformanceMapper.updateTreatSavingCardPerformanceScheme(treatSavingCardPerformanceSchemeEntity);

        treatSavingCardCategoryPerformanceMapper.deleteTreatSavingCardCategoryPerformanceEntity(form.EntityID);


        List<TreatSavingCardCategoryPerformanceEntity> list = new ArrayList<>();
        if (form.Category != null && form.Category.size() > 0) {

            for (TreatSavingCardProjectCategoryCommissionAddForm treatSavingCardProjectCategoryPerformanceAddForm : form.Category) {
                if (treatSavingCardProjectCategoryPerformanceAddForm.Rate != null ||
                        treatSavingCardProjectCategoryPerformanceAddForm.LargessRate != null) {
                    TreatSavingCardCategoryPerformanceEntity treatSavingCardCategoryPerformanceEntity = new TreatSavingCardCategoryPerformanceEntity();
                    treatSavingCardCategoryPerformanceEntity.EntityID = form.EntityID;
                    treatSavingCardCategoryPerformanceEntity.SavingCardCategoryID = treatSavingCardProjectCategoryPerformanceAddForm.CategoryID;
                    treatSavingCardCategoryPerformanceEntity.CardRate = treatSavingCardProjectCategoryPerformanceAddForm.Rate;
                    treatSavingCardCategoryPerformanceEntity.CardLargessRate = treatSavingCardProjectCategoryPerformanceAddForm.LargessRate;
                    list.add(treatSavingCardCategoryPerformanceEntity);
                }
            }

        }
        if (list != null && list.size() > 0) {
            List<List<TreatSavingCardCategoryPerformanceEntity>> subs = Lists.partition(list, 20);
            for (List<TreatSavingCardCategoryPerformanceEntity> entityList : subs) {
                treatSavingCardCategoryPerformanceMapper.insertTreatSavingCardCategoryPerformanceEntity(entityList);
            }
        }
        return BaseOutput.success();
    }
}