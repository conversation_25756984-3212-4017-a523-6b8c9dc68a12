package com.zhelian.service.khs.saleProjectPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.SalePackageCardProjectSchemeHandlerPerformanceMapper;
import com.zhelian.mapper.khs.SaleProjectSchemeHandlerPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectSchemeHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectSchemeHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectSchemeHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectsSchemeHandlerCommissionAddForm;
import com.zhelian.model.khs.entity.SalePackageCardProjectSchemeHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SalePackageCardProjectSchemeHandlerPerformanceEntity;
import com.zhelian.model.khs.entity.SaleProjectSchemeHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SaleProjectSchemeHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleGoodCategoryHandlerPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleProjectSchemeHandlerPerformanceService {

    @Autowired
    SaleProjectSchemeHandlerPerformanceMapper saleProjectSchemeHandlerPerformanceMapper;
    @Autowired
    SalePackageCardProjectSchemeHandlerPerformanceMapper salePackageCardProjectSchemeHandlerPerformanceMapper;

    public BaseOutput allSaleProjectSchemeHandlerPerformance(SaleProjectSchemeHandlerCommissionQueryForm form) {
        List<SaleGoodCategoryHandlerPerformanceOutputForm> list = saleProjectSchemeHandlerPerformanceMapper.allSaleProjectSchemeHandlerPerformance(form.EntityID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSalePackageCardProjectSchemeHandlerPerformance(SaleProjectSchemeHandlerCommissionQueryForm form) {
        List<SaleGoodCategoryHandlerPerformanceOutputForm> list = salePackageCardProjectSchemeHandlerPerformanceMapper.allSalePackageCardProjectSchemeHandlerPerformance(form.EntityID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleProjectSchemeHandlerPerformance(SaleProjectsSchemeHandlerCommissionAddForm form) {

        saleProjectSchemeHandlerPerformanceMapper.deleteSaleProjectSchemeHandler(form.EntityID);
        saleProjectSchemeHandlerPerformanceMapper.deleteSaleProjectSchemeHandlerJobType(form.EntityID);
        List<SaleProjectSchemeHandlerPerformanceEntity> listSaleProjectSchemeHandlerPerformanceEntity = new ArrayList<>();
        List<SaleProjectSchemeHandlerJobTypePerformanceEntity> listSaleProjectSchemeHandlerJobTypePerformanceEntity = new ArrayList<>();
        if (form.ProjectHandler != null && form.ProjectHandler.size() > 0) {
            for (SaleProjectSchemeHandlerCommissionAddForm saleProjectSchemeHandlerAddForm : form.ProjectHandler) {
                if (saleProjectSchemeHandlerAddForm.PayRate != null ||
                        saleProjectSchemeHandlerAddForm.SavingCardRate != null ||
                        saleProjectSchemeHandlerAddForm.SavingCardLargessRate != null) {
                    SaleProjectSchemeHandlerPerformanceEntity saleProjectSchemeHandlerPerformanceEntity = new SaleProjectSchemeHandlerPerformanceEntity();
                    saleProjectSchemeHandlerPerformanceEntity.EntityID = form.EntityID;
                    saleProjectSchemeHandlerPerformanceEntity.ProjectSaleHandlerID = saleProjectSchemeHandlerAddForm.SaleHandlerID;
                    saleProjectSchemeHandlerPerformanceEntity.PayRate = saleProjectSchemeHandlerAddForm.PayRate;
                    saleProjectSchemeHandlerPerformanceEntity.SavingCardRate = saleProjectSchemeHandlerAddForm.SavingCardRate;
                    saleProjectSchemeHandlerPerformanceEntity.SavingCardLargessRate = saleProjectSchemeHandlerAddForm.SavingCardLargessRate;
                    listSaleProjectSchemeHandlerPerformanceEntity.add(saleProjectSchemeHandlerPerformanceEntity);
                }

                if (saleProjectSchemeHandlerAddForm.HandlerJobTypeCommission != null && saleProjectSchemeHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleProjectSchemeHandlerJobTypeCommissionAddForm saleProjectSchemeHandlerJobTypePerformanceAddForm : saleProjectSchemeHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleProjectSchemeHandlerJobTypePerformanceAddForm.PayRate != null ||
                                saleProjectSchemeHandlerJobTypePerformanceAddForm.SavingCardRate != null ||
                                saleProjectSchemeHandlerJobTypePerformanceAddForm.SavingCardLargessRate != null) {
                            SaleProjectSchemeHandlerJobTypePerformanceEntity saleProjectSchemeHandlerJobTypePerformanceEntity = new SaleProjectSchemeHandlerJobTypePerformanceEntity();
                            saleProjectSchemeHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            saleProjectSchemeHandlerJobTypePerformanceEntity.ProjectSaleHandlerID = saleProjectSchemeHandlerAddForm.SaleHandlerID;
                            saleProjectSchemeHandlerJobTypePerformanceEntity.JobTypeID = saleProjectSchemeHandlerJobTypePerformanceAddForm.JobTypeID;
                            saleProjectSchemeHandlerJobTypePerformanceEntity.PayRate = saleProjectSchemeHandlerJobTypePerformanceAddForm.PayRate;
                            saleProjectSchemeHandlerJobTypePerformanceEntity.SavingCardRate = saleProjectSchemeHandlerJobTypePerformanceAddForm.SavingCardRate;
                            saleProjectSchemeHandlerJobTypePerformanceEntity.SavingCardLargessRate = saleProjectSchemeHandlerJobTypePerformanceAddForm.SavingCardLargessRate;
                            listSaleProjectSchemeHandlerJobTypePerformanceEntity.add(saleProjectSchemeHandlerJobTypePerformanceEntity);
                        }
                    }

                }
            }


        }

        salePackageCardProjectSchemeHandlerPerformanceMapper.deleteSalePackageCardProjectSchemeHandler(form.EntityID);
        salePackageCardProjectSchemeHandlerPerformanceMapper.deleteSalePackageCardProjectSchemeHandlerJobType(form.EntityID);
        List<SalePackageCardProjectSchemeHandlerPerformanceEntity> listSalePackageCardProjectSchemeHandlerPerformanceEntity = new ArrayList<>();
        List<SalePackageCardProjectSchemeHandlerJobTypePerformanceEntity> listSalePackageCardProjectSchemeHandlerJobTypePerformanceEntity = new ArrayList<>();
        if (form.PackageCardProjectHandler != null && form.PackageCardProjectHandler.size() > 0) {
            for (SaleProjectSchemeHandlerCommissionAddForm saleProjectSchemeHandlerPerformanceAddForm : form.PackageCardProjectHandler) {
                if (saleProjectSchemeHandlerPerformanceAddForm.PayRate != null ||
                        saleProjectSchemeHandlerPerformanceAddForm.SavingCardRate != null ||
                        saleProjectSchemeHandlerPerformanceAddForm.SavingCardLargessRate != null) {
                    SalePackageCardProjectSchemeHandlerPerformanceEntity salePackageCardProjectSchemeHandlerPerformanceEntity = new SalePackageCardProjectSchemeHandlerPerformanceEntity();
                    salePackageCardProjectSchemeHandlerPerformanceEntity.EntityID = form.EntityID;
                    salePackageCardProjectSchemeHandlerPerformanceEntity.PackageCardSaleHandlerID = saleProjectSchemeHandlerPerformanceAddForm.SaleHandlerID;
                    salePackageCardProjectSchemeHandlerPerformanceEntity.PayRate = saleProjectSchemeHandlerPerformanceAddForm.PayRate;
                    salePackageCardProjectSchemeHandlerPerformanceEntity.SavingCardRate = saleProjectSchemeHandlerPerformanceAddForm.SavingCardRate;
                    salePackageCardProjectSchemeHandlerPerformanceEntity.SavingCardLargessRate = saleProjectSchemeHandlerPerformanceAddForm.SavingCardLargessRate;
                    listSalePackageCardProjectSchemeHandlerPerformanceEntity.add(salePackageCardProjectSchemeHandlerPerformanceEntity);
                }
                if (saleProjectSchemeHandlerPerformanceAddForm.HandlerJobTypeCommission != null && saleProjectSchemeHandlerPerformanceAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleProjectSchemeHandlerJobTypeCommissionAddForm saleProjectSchemeHandlerJobTypePerformanceAddForm : saleProjectSchemeHandlerPerformanceAddForm.HandlerJobTypeCommission) {
                        if (saleProjectSchemeHandlerJobTypePerformanceAddForm.PayRate != null || saleProjectSchemeHandlerJobTypePerformanceAddForm.PayFixed != null ||
                                saleProjectSchemeHandlerJobTypePerformanceAddForm.SavingCardRate != null || saleProjectSchemeHandlerJobTypePerformanceAddForm.SavingCardFixed != null ||
                                saleProjectSchemeHandlerJobTypePerformanceAddForm.SavingCardLargessRate != null || saleProjectSchemeHandlerJobTypePerformanceAddForm.SavingCardLargessFixed != null ||
                                saleProjectSchemeHandlerJobTypePerformanceAddForm.SpecialBenefit != null) {
                            SalePackageCardProjectSchemeHandlerJobTypePerformanceEntity salePackageCardProjectSchemeHandlerJobTypePerformanceEntity = new SalePackageCardProjectSchemeHandlerJobTypePerformanceEntity();
                            salePackageCardProjectSchemeHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            salePackageCardProjectSchemeHandlerJobTypePerformanceEntity.PackageCardSaleHandlerID = saleProjectSchemeHandlerPerformanceAddForm.SaleHandlerID;
                            salePackageCardProjectSchemeHandlerJobTypePerformanceEntity.JobTypeID = saleProjectSchemeHandlerJobTypePerformanceAddForm.JobTypeID;
                            salePackageCardProjectSchemeHandlerJobTypePerformanceEntity.PayRate = saleProjectSchemeHandlerJobTypePerformanceAddForm.PayRate;
                            salePackageCardProjectSchemeHandlerJobTypePerformanceEntity.SavingCardRate = saleProjectSchemeHandlerJobTypePerformanceAddForm.SavingCardRate;
                            salePackageCardProjectSchemeHandlerJobTypePerformanceEntity.SavingCardLargessRate = saleProjectSchemeHandlerJobTypePerformanceAddForm.SavingCardLargessRate;
                            listSalePackageCardProjectSchemeHandlerJobTypePerformanceEntity.add(salePackageCardProjectSchemeHandlerJobTypePerformanceEntity);
                        }
                    }
                }
            }
        }
        if (listSaleProjectSchemeHandlerPerformanceEntity != null && listSaleProjectSchemeHandlerPerformanceEntity.size() > 0) {
            List<List<SaleProjectSchemeHandlerPerformanceEntity>> subs = Lists.partition(listSaleProjectSchemeHandlerPerformanceEntity, 20);
            for (List<SaleProjectSchemeHandlerPerformanceEntity> entityList : subs) {
                saleProjectSchemeHandlerPerformanceMapper.updateSaleProjectSchemeHandlerPerformance(entityList);
            }
        }
        if (listSaleProjectSchemeHandlerJobTypePerformanceEntity != null && listSaleProjectSchemeHandlerJobTypePerformanceEntity.size() > 0) {
            List<List<SaleProjectSchemeHandlerJobTypePerformanceEntity>> subs = Lists.partition(listSaleProjectSchemeHandlerJobTypePerformanceEntity, 20);
            for (List<SaleProjectSchemeHandlerJobTypePerformanceEntity> entityList : subs) {
                saleProjectSchemeHandlerPerformanceMapper.insertSaleProjectSchemeHandlerJobTypePerformance(entityList);
            }
        }
        if (listSalePackageCardProjectSchemeHandlerPerformanceEntity != null && listSalePackageCardProjectSchemeHandlerPerformanceEntity.size() > 0) {
            List<List<SalePackageCardProjectSchemeHandlerPerformanceEntity>> subs = Lists.partition(listSalePackageCardProjectSchemeHandlerPerformanceEntity, 20);
            for (List<SalePackageCardProjectSchemeHandlerPerformanceEntity> entityList : subs) {
                salePackageCardProjectSchemeHandlerPerformanceMapper.insertSalePackageCardProjectSchemeHandlerPerformance(entityList);
            }
        }
        if (listSalePackageCardProjectSchemeHandlerJobTypePerformanceEntity != null && listSalePackageCardProjectSchemeHandlerJobTypePerformanceEntity.size() > 0) {
            List<List<SalePackageCardProjectSchemeHandlerJobTypePerformanceEntity>> subs = Lists.partition(listSalePackageCardProjectSchemeHandlerJobTypePerformanceEntity, 20);
            for (List<SalePackageCardProjectSchemeHandlerJobTypePerformanceEntity> entityList : subs) {
                salePackageCardProjectSchemeHandlerPerformanceMapper.insertSalePackageCardProjectSchemeHandlerJobTypePerformance(entityList);
            }
        }

        return BaseOutput.success();
    }
}