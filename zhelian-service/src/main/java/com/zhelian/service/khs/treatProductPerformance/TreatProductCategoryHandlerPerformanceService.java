package com.zhelian.service.khs.treatProductPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.TreatProductCategoryHandlerPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.khs.entity.TreatProductCategoryHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.TreatProductCategoryHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.TreatGoodCategoryHandlerPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatProductCategoryHandlerPerformanceService {

    @Autowired
    TreatProductCategoryHandlerPerformanceMapper treatProductCategoryHandlerPerformanceMapper;


    public BaseOutput allTreatProductCategoryHandlerPerformance(TreatProductCategoryHandlerCommissionQueryForm form) {
        List<TreatGoodCategoryHandlerPerformanceOutputForm> list = treatProductCategoryHandlerPerformanceMapper.allTreatProductCategoryHandlerPerformance(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatProductCategoryHandlerPerformance(TreatProductCategoryHandlerCommissionAddForm form) {


        treatProductCategoryHandlerPerformanceMapper.deleteTreatProductCategoryHandler(form.EntityID, form.CategoryID);
        treatProductCategoryHandlerPerformanceMapper.deleteTreatProductCategoryHandlerJobType(form.EntityID, form.CategoryID);

        if (form.Handler != null && form.Handler.size() > 0) {

            List<TreatProductCategoryHandlerPerformanceEntity> listTreatProductCategoryHandlerPerformance = new ArrayList<>();

            List<TreatProductCategoryHandlerJobTypePerformanceEntity> listTreatProductCategoryHandlerJobTypePerformance = new ArrayList<>();


            for (TreatProductCategoryHandlerAddForm treatProductCategoryHandlerAddForm : form.Handler) {
                if (treatProductCategoryHandlerAddForm.PayRate != null ||
                        treatProductCategoryHandlerAddForm.CardRate != null ||
                        treatProductCategoryHandlerAddForm.CardLargessRate != null ||
                        treatProductCategoryHandlerAddForm.LargessRate != null) {
                    TreatProductCategoryHandlerPerformanceEntity treatProductCategoryHandlerPerformanceEntity = new TreatProductCategoryHandlerPerformanceEntity();
                    treatProductCategoryHandlerPerformanceEntity.EntityID = form.EntityID;
                    treatProductCategoryHandlerPerformanceEntity.ProductCategoryID = form.CategoryID;
                    treatProductCategoryHandlerPerformanceEntity.ProductTreatHandlerID = treatProductCategoryHandlerAddForm.TreatHandlerID;
                    treatProductCategoryHandlerPerformanceEntity.PayRate = treatProductCategoryHandlerAddForm.PayRate;
                    treatProductCategoryHandlerPerformanceEntity.CardRate = treatProductCategoryHandlerAddForm.CardRate;
                    treatProductCategoryHandlerPerformanceEntity.CardLargessRate = treatProductCategoryHandlerAddForm.CardLargessRate;
                    treatProductCategoryHandlerPerformanceEntity.LargessRate = treatProductCategoryHandlerAddForm.LargessRate;
                    listTreatProductCategoryHandlerPerformance.add(treatProductCategoryHandlerPerformanceEntity);
                }

                if (treatProductCategoryHandlerAddForm.HandlerJobTypeCommission != null && treatProductCategoryHandlerAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (TreatProductCategoryHandlerJobTypeCommissionAddForm treatProductCategoryHandlerJobTypePerformanceAddForm : treatProductCategoryHandlerAddForm.HandlerJobTypeCommission) {
                        if (treatProductCategoryHandlerJobTypePerformanceAddForm.PayRate != null ||
                                treatProductCategoryHandlerJobTypePerformanceAddForm.CardRate != null ||
                                treatProductCategoryHandlerJobTypePerformanceAddForm.CardLargessRate != null ||
                                treatProductCategoryHandlerJobTypePerformanceAddForm.LargessRate != null) {
                            TreatProductCategoryHandlerJobTypePerformanceEntity treatProductCategoryHandlerJobTypePerformanceEntity = new TreatProductCategoryHandlerJobTypePerformanceEntity();
                            treatProductCategoryHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            treatProductCategoryHandlerJobTypePerformanceEntity.ProductCategoryID = form.CategoryID;
                            treatProductCategoryHandlerJobTypePerformanceEntity.ProductTreatHandlerID = treatProductCategoryHandlerAddForm.TreatHandlerID;
                            treatProductCategoryHandlerJobTypePerformanceEntity.JobTypeID = treatProductCategoryHandlerJobTypePerformanceAddForm.JobTypeID;
                            treatProductCategoryHandlerJobTypePerformanceEntity.PayRate = treatProductCategoryHandlerJobTypePerformanceAddForm.PayRate;
                            treatProductCategoryHandlerJobTypePerformanceEntity.CardRate = treatProductCategoryHandlerJobTypePerformanceAddForm.CardRate;
                            treatProductCategoryHandlerJobTypePerformanceEntity.CardLargessRate = treatProductCategoryHandlerJobTypePerformanceAddForm.CardLargessRate;
                            treatProductCategoryHandlerJobTypePerformanceEntity.LargessRate = treatProductCategoryHandlerJobTypePerformanceAddForm.LargessRate;
                            listTreatProductCategoryHandlerJobTypePerformance.add(treatProductCategoryHandlerJobTypePerformanceEntity);
                        }
                    }
                }
            }
            if (listTreatProductCategoryHandlerPerformance != null && listTreatProductCategoryHandlerPerformance.size() > 0) {
                List<List<TreatProductCategoryHandlerPerformanceEntity>> subs = Lists.partition(listTreatProductCategoryHandlerPerformance, 20);
                for (List<TreatProductCategoryHandlerPerformanceEntity> entityList : subs) {
                    treatProductCategoryHandlerPerformanceMapper.updateTreatProductCategoryHandlerPerformance(entityList);
                }
            }
            if (listTreatProductCategoryHandlerJobTypePerformance != null && listTreatProductCategoryHandlerJobTypePerformance.size() > 0) {
                List<List<TreatProductCategoryHandlerJobTypePerformanceEntity>> subs = Lists.partition(listTreatProductCategoryHandlerJobTypePerformance, 20);
                for (List<TreatProductCategoryHandlerJobTypePerformanceEntity> entityList : subs) {
                    treatProductCategoryHandlerPerformanceMapper.insertTreatProductCategoryHandlerJobTypePerformance(entityList);
                }
            }

        }
        return BaseOutput.success();
    }
}