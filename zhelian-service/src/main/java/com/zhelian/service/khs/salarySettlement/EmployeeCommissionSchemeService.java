package com.zhelian.service.khs.salarySettlement;


import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.khs.EmployeeCommissionSchemeMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.khs.entity.EmployeeCommissionEntity;
import com.zhelian.model.khs.entity.EmployeeCommissionEntityEntity;
import com.zhelian.model.khs.entity.EmployeeCommissionGoodSaleHandlerJobTypeEntity;
import com.zhelian.model.khs.entity.EmployeeCommissionSchemeEntity;
import com.zhelian.model.khs.form.input.*;
import com.zhelian.model.khs.form.output.EmployeeCommissionAllHandlerOutForm;
import com.zhelian.model.khs.form.output.EmployeeCommissionOutForm;
import com.zhelian.model.khs.form.output.EmployeeCommissionSchemeOutForm;
import com.zhelian.model.khs.form.output.EmployeeOutForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class EmployeeCommissionSchemeService {

    @Autowired
    EmployeeCommissionSchemeMapper employeeCommissionSchemeMapper;


    public BaseOutput all(EmployeeCommissionSchemeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<EmployeeCommissionSchemeOutForm> list = employeeCommissionSchemeMapper.getEmployeeCommissionScheme(form.Name, form.PerformanceEvaluationSchemeID, form.PerformanceCalculationSchemeID, form.Active);
        return BasePageInfo.success(list);
    }

    @Transactional
    public BaseOutput create(EmployeeCommissionSchemeAddForm form) {
        EmployeeCommissionSchemeEntity employeeCommissionSchemeEntity = new EmployeeCommissionSchemeEntity();
        employeeCommissionSchemeEntity.Name = form.Name;
        employeeCommissionSchemeEntity.PerformanceEvaluationSchemeID = form.PerformanceEvaluationSchemeID;
        employeeCommissionSchemeEntity.PerformanceEvaluationRange = form.PerformanceEvaluationRange;
        employeeCommissionSchemeEntity.PerformanceCalculationSchemeID = form.PerformanceCalculationSchemeID;
        employeeCommissionSchemeEntity.PerformanceCalculationRange = form.PerformanceCalculationRange;

        employeeCommissionSchemeEntity.Calculation = form.Calculation;
        employeeCommissionSchemeMapper.createEmployeeCommissionScheme(employeeCommissionSchemeEntity);
        updateDetail(employeeCommissionSchemeEntity.ID, form.Commission, form.ProductHandler, form.ProjectHandler, form.GeneralCardHandler, form.TimeCardHandler, form.SavingCardHandler, form.PackageCardHandler, form.TreatProductHandler, form.TreatProjectHandler, form.TreatSavingCardHandler, form.Entity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput update(EmployeeCommissionSchemeAddForm form) {
        EmployeeCommissionSchemeEntity employeeCommissionSchemeEntity = new EmployeeCommissionSchemeEntity();
        employeeCommissionSchemeEntity.ID = form.ID;
        employeeCommissionSchemeEntity.Name = form.Name;
        employeeCommissionSchemeEntity.PerformanceEvaluationSchemeID = form.PerformanceEvaluationSchemeID;
        employeeCommissionSchemeEntity.PerformanceEvaluationRange = form.PerformanceEvaluationRange;
        employeeCommissionSchemeEntity.PerformanceCalculationSchemeID = form.PerformanceCalculationSchemeID;
        employeeCommissionSchemeEntity.PerformanceCalculationRange = form.PerformanceCalculationRange;
        employeeCommissionSchemeEntity.Calculation = form.Calculation;
        employeeCommissionSchemeEntity.Active = form.Active;
        employeeCommissionSchemeMapper.updateEmployeeCommissionScheme(employeeCommissionSchemeEntity);
        employeeCommissionSchemeMapper.deleteEmployeeCommission(form.ID);
        employeeCommissionSchemeMapper.deleteEmployeeCommissionEntity(form.ID);
        employeeCommissionSchemeMapper.deleteEmployeeCommissionProductHandler(form.ID);
        employeeCommissionSchemeMapper.deleteEmployeeCommissionProjectHandler(form.ID);
        employeeCommissionSchemeMapper.deleteEmployeeCommissionGeneralCardHandler(form.ID);
        employeeCommissionSchemeMapper.deleteEmployeeCommissionTimeCardHandler(form.ID);
        employeeCommissionSchemeMapper.deleteEmployeeCommissionSavingCardHandler(form.ID);
        employeeCommissionSchemeMapper.deleteEmployeeCommissionPackageCardHandler(form.ID);
        employeeCommissionSchemeMapper.deleteEmployeeCommissionTreatProductHandler(form.ID);
        employeeCommissionSchemeMapper.deleteEmployeeCommissionTreatProjectHandler(form.ID);
        employeeCommissionSchemeMapper.deleteEmployeeCommissionTreatSavingCardHandler(form.ID);
        updateDetail(form.ID, form.Commission, form.ProductHandler, form.ProjectHandler, form.GeneralCardHandler, form.TimeCardHandler, form.SavingCardHandler, form.PackageCardHandler, form.TreatProductHandler, form.TreatProjectHandler, form.TreatSavingCardHandler, form.Entity);
        return BaseOutput.success();
    }

    public void updateDetail(Integer ID, List<EmployeeCommissionAddForm> Commission, List<EmployeeCommissionHandlerAddForm> ProductHandler, List<EmployeeCommissionHandlerAddForm> ProjectHandler, List<EmployeeCommissionHandlerAddForm> GeneralCardHandler, List<EmployeeCommissionHandlerAddForm> TimeCardHandler, List<EmployeeCommissionHandlerAddForm> SavingCardHandler, List<EmployeeCommissionHandlerAddForm> PackageCardHandler, List<EmployeeCommissionHandlerAddForm> TreatProductHandler, List<EmployeeCommissionHandlerAddForm> TreatProjectHandler, List<EmployeeCommissionHandlerAddForm> TreatSavingCardHandler, Integer[] Entity) {
        List<EmployeeCommissionEntity> commissionEntities = new ArrayList<>();
        List<EmployeeCommissionEntityEntity> entityEntities = new ArrayList<>();
        List<EmployeeCommissionGoodSaleHandlerJobTypeEntity> productHandler = new ArrayList<>();
        List<EmployeeCommissionGoodSaleHandlerJobTypeEntity> projectHandler = new ArrayList<>();
        List<EmployeeCommissionGoodSaleHandlerJobTypeEntity> generalCardHandler = new ArrayList<>();
        List<EmployeeCommissionGoodSaleHandlerJobTypeEntity> timeCardHandler = new ArrayList<>();
        List<EmployeeCommissionGoodSaleHandlerJobTypeEntity> savingCardHandler = new ArrayList<>();
        List<EmployeeCommissionGoodSaleHandlerJobTypeEntity> packageCardHandler = new ArrayList<>();
        List<EmployeeCommissionGoodSaleHandlerJobTypeEntity> treatProductHandler = new ArrayList<>();
        List<EmployeeCommissionGoodSaleHandlerJobTypeEntity> treatprojectHandler = new ArrayList<>();
        List<EmployeeCommissionGoodSaleHandlerJobTypeEntity> treatSavingCardHandler = new ArrayList<>();
        if (Commission != null && Commission.size() > 0) {
            for (EmployeeCommissionAddForm employeeCommissionAddForm : Commission) {
                EmployeeCommissionEntity employeeCommissionEntity = new EmployeeCommissionEntity();
                employeeCommissionEntity.EmployeeCommissionSchemeID = ID;
                employeeCommissionEntity.BeginPerformance = employeeCommissionAddForm.BeginPerformance;
                employeeCommissionEntity.EndPerformance = employeeCommissionAddForm.EndPerformance;
                employeeCommissionEntity.Rate = employeeCommissionAddForm.Rate;
                employeeCommissionEntity.Fixed = employeeCommissionAddForm.Fixed;
                commissionEntities.add(employeeCommissionEntity);
            }
        }
        if (ProductHandler != null && ProductHandler.size() > 0) {
            for (EmployeeCommissionHandlerAddForm employeeCommissionHandlerAddForm : ProductHandler) {
                for (Integer i : employeeCommissionHandlerAddForm.JobTypeID) {
                    EmployeeCommissionGoodSaleHandlerJobTypeEntity employeeCommissionEntity = new EmployeeCommissionGoodSaleHandlerJobTypeEntity();
                    employeeCommissionEntity.EmployeeCommissionSchemeID = ID;
                    employeeCommissionEntity.GoodHandlerID = employeeCommissionHandlerAddForm.GoodHandlerID;
                    employeeCommissionEntity.JobTypeID = i;
                    productHandler.add(employeeCommissionEntity);
                }
            }

        }
        if (ProjectHandler != null && ProjectHandler.size() > 0) {
            for (EmployeeCommissionHandlerAddForm employeeCommissionHandlerAddForm : ProjectHandler) {
                for (Integer i : employeeCommissionHandlerAddForm.JobTypeID) {
                    EmployeeCommissionGoodSaleHandlerJobTypeEntity employeeCommissionEntity = new EmployeeCommissionGoodSaleHandlerJobTypeEntity();
                    employeeCommissionEntity.EmployeeCommissionSchemeID = ID;
                    employeeCommissionEntity.GoodHandlerID = employeeCommissionHandlerAddForm.GoodHandlerID;
                    employeeCommissionEntity.JobTypeID = i;
                    projectHandler.add(employeeCommissionEntity);
                }
            }
        }
        if (GeneralCardHandler != null && GeneralCardHandler.size() > 0) {
            for (EmployeeCommissionHandlerAddForm employeeCommissionHandlerAddForm : GeneralCardHandler) {
                for (Integer i : employeeCommissionHandlerAddForm.JobTypeID) {
                    EmployeeCommissionGoodSaleHandlerJobTypeEntity employeeCommissionEntity = new EmployeeCommissionGoodSaleHandlerJobTypeEntity();
                    employeeCommissionEntity.EmployeeCommissionSchemeID = ID;
                    employeeCommissionEntity.GoodHandlerID = employeeCommissionHandlerAddForm.GoodHandlerID;
                    employeeCommissionEntity.JobTypeID = i;
                    generalCardHandler.add(employeeCommissionEntity);
                }
            }
        }
        if (TimeCardHandler != null && TimeCardHandler.size() > 0) {
            for (EmployeeCommissionHandlerAddForm employeeCommissionHandlerAddForm : TimeCardHandler) {
                for (Integer i : employeeCommissionHandlerAddForm.JobTypeID) {
                    EmployeeCommissionGoodSaleHandlerJobTypeEntity employeeCommissionEntity = new EmployeeCommissionGoodSaleHandlerJobTypeEntity();
                    employeeCommissionEntity.EmployeeCommissionSchemeID = ID;
                    employeeCommissionEntity.GoodHandlerID = employeeCommissionHandlerAddForm.GoodHandlerID;
                    employeeCommissionEntity.JobTypeID = i;
                    timeCardHandler.add(employeeCommissionEntity);
                }
            }
        }
        if (SavingCardHandler != null && SavingCardHandler.size() > 0) {
            for (EmployeeCommissionHandlerAddForm employeeCommissionHandlerAddForm : SavingCardHandler) {
                for (Integer i : employeeCommissionHandlerAddForm.JobTypeID) {
                    EmployeeCommissionGoodSaleHandlerJobTypeEntity employeeCommissionEntity = new EmployeeCommissionGoodSaleHandlerJobTypeEntity();
                    employeeCommissionEntity.EmployeeCommissionSchemeID = ID;
                    employeeCommissionEntity.GoodHandlerID = employeeCommissionHandlerAddForm.GoodHandlerID;
                    employeeCommissionEntity.JobTypeID = i;
                    savingCardHandler.add(employeeCommissionEntity);
                }
            }
        }
        if (PackageCardHandler != null && PackageCardHandler.size() > 0) {
            for (EmployeeCommissionHandlerAddForm employeeCommissionHandlerAddForm : PackageCardHandler) {
                for (Integer i : employeeCommissionHandlerAddForm.JobTypeID) {
                    EmployeeCommissionGoodSaleHandlerJobTypeEntity employeeCommissionEntity = new EmployeeCommissionGoodSaleHandlerJobTypeEntity();
                    employeeCommissionEntity.EmployeeCommissionSchemeID = ID;
                    employeeCommissionEntity.GoodHandlerID = employeeCommissionHandlerAddForm.GoodHandlerID;
                    employeeCommissionEntity.JobTypeID = i;
                    packageCardHandler.add(employeeCommissionEntity);
                }
            }
        }
        if (TreatProductHandler != null && TreatProductHandler.size() > 0) {
            for (EmployeeCommissionHandlerAddForm employeeCommissionHandlerAddForm : TreatProductHandler) {
                for (Integer i : employeeCommissionHandlerAddForm.JobTypeID) {
                    EmployeeCommissionGoodSaleHandlerJobTypeEntity employeeCommissionEntity = new EmployeeCommissionGoodSaleHandlerJobTypeEntity();
                    employeeCommissionEntity.EmployeeCommissionSchemeID = ID;
                    employeeCommissionEntity.GoodHandlerID = employeeCommissionHandlerAddForm.GoodHandlerID;
                    employeeCommissionEntity.JobTypeID = i;
                    treatProductHandler.add(employeeCommissionEntity);
                }
            }
        }
        if (TreatProjectHandler != null && TreatProjectHandler.size() > 0) {
            for (EmployeeCommissionHandlerAddForm employeeCommissionHandlerAddForm : TreatProjectHandler) {
                for (Integer i : employeeCommissionHandlerAddForm.JobTypeID) {
                    EmployeeCommissionGoodSaleHandlerJobTypeEntity employeeCommissionEntity = new EmployeeCommissionGoodSaleHandlerJobTypeEntity();
                    employeeCommissionEntity.EmployeeCommissionSchemeID = ID;
                    employeeCommissionEntity.GoodHandlerID = employeeCommissionHandlerAddForm.GoodHandlerID;
                    employeeCommissionEntity.JobTypeID = i;
                    treatprojectHandler.add(employeeCommissionEntity);
                }
            }
        }
        if (TreatSavingCardHandler != null && TreatSavingCardHandler.size() > 0) {
            for (EmployeeCommissionHandlerAddForm employeeCommissionHandlerAddForm : TreatSavingCardHandler) {
                for (Integer i : employeeCommissionHandlerAddForm.JobTypeID) {
                    EmployeeCommissionGoodSaleHandlerJobTypeEntity employeeCommissionEntity = new EmployeeCommissionGoodSaleHandlerJobTypeEntity();
                    employeeCommissionEntity.EmployeeCommissionSchemeID = ID;
                    employeeCommissionEntity.GoodHandlerID = employeeCommissionHandlerAddForm.GoodHandlerID;
                    employeeCommissionEntity.JobTypeID = i;
                    treatSavingCardHandler.add(employeeCommissionEntity);
                }
            }
        }
        if (Entity != null && Entity.length > 0) {
            for (Integer i : Entity) {
                EmployeeCommissionEntityEntity employeeCommissionEntity = new EmployeeCommissionEntityEntity();
                employeeCommissionEntity.EmployeeCommissionSchemeID = ID;
                employeeCommissionEntity.EntityID = i;
                entityEntities.add(employeeCommissionEntity);
            }
        }

        if (commissionEntities.size() > 0)
            employeeCommissionSchemeMapper.createEmployeeCommission(commissionEntities);

        if (entityEntities.size() > 0)
            employeeCommissionSchemeMapper.createEmployeeCommissionEntities(entityEntities);

        if (productHandler.size() > 0)
            employeeCommissionSchemeMapper.createEmployeeCommissionProductHandler(productHandler);

        if (projectHandler.size() > 0)
            employeeCommissionSchemeMapper.createEmployeeCommissionProjectHandler(projectHandler);

        if (generalCardHandler.size() > 0)
            employeeCommissionSchemeMapper.createEmployeeCommissionGeneralCardHandler(generalCardHandler);

        if (timeCardHandler.size() > 0)
            employeeCommissionSchemeMapper.createEmployeeCommissionTimeCardHandler(timeCardHandler);

        if (savingCardHandler.size() > 0)
            employeeCommissionSchemeMapper.createEmployeeCommissionSavingCardHandler(savingCardHandler);

        if (packageCardHandler.size() > 0)
            employeeCommissionSchemeMapper.createEmployeeCommissionPackageCardHandler(packageCardHandler);

        if (treatProductHandler.size() > 0)
            employeeCommissionSchemeMapper.createEmployeeCommissionTreatProductHandler(treatProductHandler);

        if (treatprojectHandler.size() > 0)
            employeeCommissionSchemeMapper.createEmployeeCommissionTreatProjectHandler(treatprojectHandler);

        if (treatSavingCardHandler.size() > 0)
            employeeCommissionSchemeMapper.createEmployeeCommissionTreatSavingCardHandler(treatSavingCardHandler);
    }


    public BaseOutput allHandler() {
        EmployeeOutForm employeeOutForm = new EmployeeOutForm();
        employeeOutForm.product = employeeCommissionSchemeMapper.getSaleProductHandler();
        employeeOutForm.project = employeeCommissionSchemeMapper.getSaleProjectHandler();
        employeeOutForm.generalCard = employeeCommissionSchemeMapper.getSaleGeneralCardHandler();
        employeeOutForm.timeCard = employeeCommissionSchemeMapper.getSaleTimeCardHandler();
        employeeOutForm.savingCard = employeeCommissionSchemeMapper.getSaleSavingCardHandler();
        employeeOutForm.packageCard = employeeCommissionSchemeMapper.getSalePackageCardHandler();
        employeeOutForm.treatProduct = employeeCommissionSchemeMapper.getTreatProductHandler();
        employeeOutForm.treatProject = employeeCommissionSchemeMapper.getTreatProjectHandler();
        employeeOutForm.treatSavingCard = employeeCommissionSchemeMapper.getTreatSavingCardHandler();
        return BaseOutputForm.success(employeeOutForm);
    }

    public BaseOutput commission(EmployeeCommissionQueryForm form) {
        List<EmployeeCommissionOutForm> list = employeeCommissionSchemeMapper.getEmployeeCommission(form.ID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput entity(EmployeeCommissionQueryForm form) {
        Integer[] list = employeeCommissionSchemeMapper.getEmployeeCommissionEntity(form.ID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput handler(EmployeeCommissionQueryForm form) {
        EmployeeCommissionAllHandlerOutForm employeeCommissionAllHandlerOutForm = new EmployeeCommissionAllHandlerOutForm();
        employeeCommissionAllHandlerOutForm.product = employeeCommissionSchemeMapper.getEmployeeCommissionProductHandler(form.ID);
        employeeCommissionAllHandlerOutForm.project = employeeCommissionSchemeMapper.getEmployeeCommissionProjectHandler(form.ID);
        employeeCommissionAllHandlerOutForm.generalCard = employeeCommissionSchemeMapper.getEmployeeCommissionGeneralCardHandler(form.ID);
        employeeCommissionAllHandlerOutForm.timeCard = employeeCommissionSchemeMapper.getEmployeeCommissionTimeCardHandler(form.ID);
        employeeCommissionAllHandlerOutForm.savingCard = employeeCommissionSchemeMapper.getEmployeeCommissionSavingCardHandler(form.ID);
        employeeCommissionAllHandlerOutForm.packageCard = employeeCommissionSchemeMapper.getEmployeeCommissionPackageCardHandler(form.ID);
        employeeCommissionAllHandlerOutForm.treatProduct = employeeCommissionSchemeMapper.getEmployeeCommissionTreatProductHandler(form.ID);
        employeeCommissionAllHandlerOutForm.treatProject = employeeCommissionSchemeMapper.getEmployeeCommissionTreatProjectHandler(form.ID);
        employeeCommissionAllHandlerOutForm.treatSavingCard = employeeCommissionSchemeMapper.getEmployeeCommissionTreatSavingCardHandler(form.ID);
        return BaseOutputForm.success(employeeCommissionAllHandlerOutForm);
    }
}
