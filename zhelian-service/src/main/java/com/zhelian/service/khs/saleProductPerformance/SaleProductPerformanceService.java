package com.zhelian.service.khs.saleProductPerformance;


import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.SaleProductPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductsCommissionAddForm;
import com.zhelian.model.khs.entity.TBSaleProductPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleProductPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleProductPerformanceService {

    @Autowired
    SaleProductPerformanceMapper saleProductPerformanceMapper;

    public BaseOutput allSaleProductPerformance(SaleProductCategoryHandlerCommissionQueryForm form) {
        List<SaleProductPerformanceOutputForm> list = saleProductPerformanceMapper.allSaleProductPerformance(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleProductPerformance(SaleProductsCommissionAddForm form) {

        saleProductPerformanceMapper.deleteSaleProductPerformanceByEntityIDAndCategory(form.EntityID, form.CategoryID);
        List<TBSaleProductPerformanceEntity> list = new ArrayList<>();
        if (form.Product != null && form.Product.size() > 0) {
            for (SaleProductCommissionAddForm saleProductPerformanceAddForm : form.Product) {
                if (saleProductPerformanceAddForm.PayRate != null ||
                        saleProductPerformanceAddForm.SavingCardRate != null ||
                        saleProductPerformanceAddForm.SavingCardLargessRate != null) {
                    TBSaleProductPerformanceEntity saleProductPerformanceEntity = new TBSaleProductPerformanceEntity();
                    saleProductPerformanceEntity.EntityID = form.EntityID;
                    saleProductPerformanceEntity.ProductID = saleProductPerformanceAddForm.ProductID;
                    saleProductPerformanceEntity.PayRate = saleProductPerformanceAddForm.PayRate;
                    saleProductPerformanceEntity.SavingCardRate = saleProductPerformanceAddForm.SavingCardRate;
                    saleProductPerformanceEntity.SavingCardLargessRate = saleProductPerformanceAddForm.SavingCardLargessRate;
                    list.add(saleProductPerformanceEntity);
                }
            }
            if (list != null && list.size() > 0) {
                List<List<TBSaleProductPerformanceEntity>> subs = Lists.partition(list, 20);
                for (List<TBSaleProductPerformanceEntity> entityList : subs) {
                    saleProductPerformanceMapper.insertSaleProductPerformance(entityList);
                }
            }
        }
        return BaseOutput.success();
    }

}