package com.zhelian.service.khs.role;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.khs.MenuPermissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.khs.form.input.MenuPermissionQueryForm;
import com.zhelian.model.khs.form.output.MenuPermissionOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MenuPermissionService {
    @Autowired
    MenuPermissionMapper menuPermissionMapper;

    public BaseOutput getMenuPermissionlist(MenuPermissionQueryForm menuPermissionQueryForm) {
        List<MenuPermissionOutputForm> menuPermissionOutputForms = menuPermissionMapper.getMenuPermissionByType(menuPermissionQueryForm.MenuPermissionType);
        JSONArray result = ListUtil.listToTree(JSONArray.parseArray(JSON.toJSONString(menuPermissionOutputForms, JSONWriter.Feature.WriteMapNullValue)), "Code", "ParentCode", "Child");
        return BaseOutputForm.success(result);
    }

}
