package com.zhelian.service.khs.medicalRecordEntry;

import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.khs.MedicalRecordEntryCategoryMapper;
import com.zhelian.mapper.khs.MedicalRecordEntryLabelMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.khs.entity.MedicalRecordCatalogEntity;
import com.zhelian.model.khs.entity.MedicalRecordEntryCategoryEntity;
import com.zhelian.model.khs.entity.MedicalRecordEntryLabelEntity;
import com.zhelian.model.khs.entity.MedicalRecordTemplateEntity;
import com.zhelian.model.khs.form.input.*;
import com.zhelian.model.khs.form.output.MedicalRecordEntryCategoryOutForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

@Service
public class MedicalRecordEntryCategoryService {

    @Autowired
    MedicalRecordEntryCategoryMapper medicalRecordEntryCategoryMapper;

    @Autowired
    MedicalRecordEntryLabelMapper medicalRecordEntryLabelMapper;

    public BaseOutput list() {
        List<MedicalRecordEntryCategoryOutForm> outForm = medicalRecordEntryCategoryMapper.list();
        return BaseOutputForm.success(outForm);
    }

    @Transactional
    public BaseOutput create(MedicalRecordEntryCategoryAddForm form) {
        Integer Sequence = medicalRecordEntryCategoryMapper.getMaxSequence();
        MedicalRecordEntryCategoryEntity entity = new MedicalRecordEntryCategoryEntity();
        entity.Name = form.Name;
        entity.Sequence = Sequence != null ? Sequence + 1 : 1;
        medicalRecordEntryCategoryMapper.create(entity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput update(MedicalRecordEntryCategoryUpdateForm form) {
        MedicalRecordEntryCategoryEntity entity = new MedicalRecordEntryCategoryEntity();
        entity.ID = form.ID;
        entity.Name = form.Name;
        medicalRecordEntryCategoryMapper.update(entity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput delete(MedicalRecordEntryCategoryDeleteForm form) {
        List<MedicalRecordEntryLabelEntity> labelEntity = medicalRecordEntryLabelMapper.getMedicalRecordEntryLabelEntityByCategoryID(form.ID);

        if (labelEntity != null && labelEntity.size() > 0) {
            return BaseOutput.failed("该分类下有标签无法删除");
        }
        medicalRecordEntryCategoryMapper.delete(form.ID);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput move(MedicalRecordEntryCategoryMoveForm form) {

        MedicalRecordEntryCategoryEntity medicalRecordEntryCategoryEntity = medicalRecordEntryCategoryMapper.getMedicalRecordEntryCategoryEntityByID(form.MoveID);

        if (medicalRecordEntryCategoryEntity == null)
            return BaseOutput.failed("模版不存在");

        //重新排序，正序查出列表
        List<MedicalRecordEntryCategoryEntity> list = medicalRecordEntryCategoryMapper.medicalRecordEntryCategoryEntityListByID(form.MoveID);

        if (list == null) {
            list = new ArrayList<>();
            list.add(medicalRecordEntryCategoryEntity);
        } else {
            //前面有单位的话，顶替前面单位，没有单位的话则插入最后一个，最终根据下标重新排序
            if (form.BeforeID != null) {
                MedicalRecordEntryCategoryEntity beforeMedicalRecordEntryCategory = new MedicalRecordEntryCategoryEntity();

                for (MedicalRecordEntryCategoryEntity entity1 : list) {
                    if (entity1.ID.equals(form.BeforeID)) {
                        beforeMedicalRecordEntryCategory = entity1;
                    }
                }
                if (beforeMedicalRecordEntryCategory == null)
                    return BaseOutput.failed("前面单位不存在");

                list.add(list.indexOf(beforeMedicalRecordEntryCategory), medicalRecordEntryCategoryEntity);

            } else {
                //插入最后一位
                list.add(medicalRecordEntryCategoryEntity);
            }
        }

        for (MedicalRecordEntryCategoryEntity entity1 : list) {
            entity1.Sequence = list.indexOf(entity1) + 1;
        }

        medicalRecordEntryCategoryMapper.updateSequence(list);

        return BaseOutput.success();

    }

}
