package com.zhelian.service.khs.saleProjectPerformance;


import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.SaleProjectPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectsCommissionAddForm;
import com.zhelian.model.khs.entity.TBSaleProjectPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleProjectPerformacneOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleProjectPerformanceService {

    @Autowired
    SaleProjectPerformanceMapper saleProjectPerformanceMapper;

    public BaseOutput allSaleProjectPerformance(SaleProjectCategoryHandlerCommissionQueryForm form) {
        List<SaleProjectPerformacneOutputForm> list = saleProjectPerformanceMapper.allSaleProjectPerformance(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleProjectPerformance(SaleProjectsCommissionAddForm form) {

        saleProjectPerformanceMapper.deleteSaleProjectPerformanceByEntityAndCategory(form.EntityID, form.CategoryID);
        List<TBSaleProjectPerformanceEntity> list = new ArrayList<>();
        if (form.Project != null && form.Project.size() > 0) {
            for (SaleProjectCommissionAddForm saleProjectPerformanceAddForm : form.Project) {
                if (saleProjectPerformanceAddForm.PayRate != null ||
                        saleProjectPerformanceAddForm.SavingCardRate != null ||
                        saleProjectPerformanceAddForm.SavingCardLargessRate != null) {
                    TBSaleProjectPerformanceEntity saleProjectPerformanceEntity = new TBSaleProjectPerformanceEntity();
                    saleProjectPerformanceEntity.EntityID = form.EntityID;
                    saleProjectPerformanceEntity.ProjectID = saleProjectPerformanceAddForm.ProjectID;
                    saleProjectPerformanceEntity.PayRate = saleProjectPerformanceAddForm.PayRate;
                    saleProjectPerformanceEntity.SavingCardRate = saleProjectPerformanceAddForm.SavingCardRate;
                    saleProjectPerformanceEntity.SavingCardLargessRate = saleProjectPerformanceAddForm.SavingCardLargessRate;
                    list.add(saleProjectPerformanceEntity);
                }
            }
            if (list != null && list.size() > 0) {
                List<List<TBSaleProjectPerformanceEntity>> subs = Lists.partition(list, 20);
                for (List<TBSaleProjectPerformanceEntity> entityList : subs) {
                    saleProjectPerformanceMapper.insertSaleProjectPerformance(entityList);
                }
            }
        }
        return BaseOutput.success();
    }

}