package com.zhelian.service.khs.treatSavingCardEntityPerformance;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.khs.TreatSavingCardCategoryEntityPerformanceMapper;
import com.zhelian.mapper.khs.TreatSavingCardEntityPerformanceMapper;
import com.zhelian.mapper.khs.TreatSavingCardEntityPerformanceSchemeMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.form.input.TreatProductCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCommissionSchemeDeleteForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCommissionSchemeQueryForm;
import com.zhelian.model.ibeauty.form.output.TreatProductCommissionSchemeOutputForm;
import com.zhelian.model.khs.entity.TreatGoodEntityPerformanceSchemeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Service
public class TreatSavingCardEntityPerformanceSchemeService {

    @Autowired
    TreatSavingCardEntityPerformanceSchemeMapper treatSavingCardEntityPerformanceSchemeMapper;
    @Autowired
    TreatSavingCardCategoryEntityPerformanceMapper treatSavingCardCategoryEntityPerformanceMapper;
    @Autowired
    TreatSavingCardEntityPerformanceMapper treatSavingCardEntityPerformanceMapper;

    public BaseOutput treatSavingCardEntityPerformanceSchemeList(TreatProductCommissionSchemeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<TreatProductCommissionSchemeOutputForm> list = treatSavingCardEntityPerformanceSchemeMapper.treatSavingCardEntityPerformanceSchemeList(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput createTreatSavingCardEntityPerformanceScheme(TreatProductCommissionSchemeAddForm form) {
        Integer count = treatSavingCardEntityPerformanceSchemeMapper.getTreatSavingCardEntityPerformanceScheme(form.EntityID);
        if (count > 0)
            return BaseOutput.failed("组织单位已经存在");
        TreatGoodEntityPerformanceSchemeEntity treatSavingCardEntityPerformanceSchemeEntity = new TreatGoodEntityPerformanceSchemeEntity();
        treatSavingCardEntityPerformanceSchemeEntity.EntityID = form.EntityID;
        treatSavingCardEntityPerformanceSchemeMapper.createTreatSavingCardEntityPerformanceScheme(treatSavingCardEntityPerformanceSchemeEntity);
        return BaseOutput.success();

    }

    @Transactional
    public BaseOutput deleteTreatSavingCardEntityPerformanceScheme(TreatProductCommissionSchemeDeleteForm form) {
        treatSavingCardEntityPerformanceSchemeMapper.deleteTreatSavingCardEntityPerformanceScheme(form.EntityID);
        treatSavingCardCategoryEntityPerformanceMapper.deleteTreatSavingCardCategoryEntityPerformanceEntity(form.EntityID);
        treatSavingCardEntityPerformanceMapper.deleteTreatSavingCardEntityPerformanceByEntity(form.EntityID);

        return BaseOutput.success();
    }
}