package com.zhelian.service.khs.treatTimeCardPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.TreatTimeCardHandlerPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatCardHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardHandlersCommissionAddForm;
import com.zhelian.model.khs.entity.TreatTimeCardHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.TreatTimeCardHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.TreatGoodCategoryHandlerPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class TreatTimeCardHandlerPerformanceService {

    @Autowired
    TreatTimeCardHandlerPerformanceMapper treatTimeCardHandlerPerformanceMapper;


    public BaseOutput allTreatTimeCardHandlerPerformance(TreatCardCommissionQueryForm form) {
        List<TreatGoodCategoryHandlerPerformanceOutputForm> list = treatTimeCardHandlerPerformanceMapper.allTreatTimeCardHandlerPerformance(form.EntityID, form.CardID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatTimeCardHandlerPerformance(TreatCardHandlersCommissionAddForm form) {

        treatTimeCardHandlerPerformanceMapper.deleteTreatTimeCardHandler(form.EntityID, form.CardID);
        treatTimeCardHandlerPerformanceMapper.deleteTreatTimeCardHandlerJobType(form.EntityID, form.CardID);
        List<TreatTimeCardHandlerPerformanceEntity> treatTimeCardHandlerPerformanceList = new ArrayList<>();
        List<TreatTimeCardHandlerJobTypePerformanceEntity> treatTimeCardHandlerJobTypePerformanceList = new ArrayList<>();
        if (form.CardHandler != null && form.CardHandler.size() > 0) {
            for (TreatCardHandlerCommissionAddForm treatCardHandlerPerformanceAddForm : form.CardHandler) {
                if (treatCardHandlerPerformanceAddForm.PayRate != null ||
                        treatCardHandlerPerformanceAddForm.CardRate != null ||
                        treatCardHandlerPerformanceAddForm.CardLargessRate != null ||
                        treatCardHandlerPerformanceAddForm.LargessRate != null) {
                    TreatTimeCardHandlerPerformanceEntity treatTimeCardHandlerPerformanceEntity = new TreatTimeCardHandlerPerformanceEntity();
                    treatTimeCardHandlerPerformanceEntity.EntityID = form.EntityID;
                    treatTimeCardHandlerPerformanceEntity.TimeCardID = form.CardID;
                    treatTimeCardHandlerPerformanceEntity.TimeCardProjectTreatHandlerID = treatCardHandlerPerformanceAddForm.TreatHandlerID;
                    treatTimeCardHandlerPerformanceEntity.PayRate = treatCardHandlerPerformanceAddForm.PayRate;
                    treatTimeCardHandlerPerformanceEntity.CardRate = treatCardHandlerPerformanceAddForm.CardRate;
                    treatTimeCardHandlerPerformanceEntity.CardLargessRate = treatCardHandlerPerformanceAddForm.CardLargessRate;
                    treatTimeCardHandlerPerformanceEntity.LargessRate = treatCardHandlerPerformanceAddForm.LargessRate;
                    treatTimeCardHandlerPerformanceList.add(treatTimeCardHandlerPerformanceEntity);
                }
                if (treatCardHandlerPerformanceAddForm.HandlerJobTypeCommission != null && treatCardHandlerPerformanceAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (TreatCardCategoryHandlerJobTypeCommissionAddForm typePerformanceAddForm : treatCardHandlerPerformanceAddForm.HandlerJobTypeCommission) {
                        if (typePerformanceAddForm.PayRate != null ||
                                typePerformanceAddForm.CardRate != null ||
                                typePerformanceAddForm.CardLargessRate != null ||
                                typePerformanceAddForm.LargessRate != null) {
                            TreatTimeCardHandlerJobTypePerformanceEntity PerformanceEntity = new TreatTimeCardHandlerJobTypePerformanceEntity();
                            PerformanceEntity.EntityID = form.EntityID;
                            PerformanceEntity.TimeCardID = form.CardID;
                            PerformanceEntity.TimeCardProjectTreatHandlerID = treatCardHandlerPerformanceAddForm.TreatHandlerID;
                            PerformanceEntity.JobTypeID = typePerformanceAddForm.JobTypeID;
                            PerformanceEntity.PayRate = typePerformanceAddForm.PayRate;
                            PerformanceEntity.CardRate = typePerformanceAddForm.CardRate;
                            PerformanceEntity.CardLargessRate = typePerformanceAddForm.CardLargessRate;
                            PerformanceEntity.LargessRate = typePerformanceAddForm.LargessRate;
                            treatTimeCardHandlerJobTypePerformanceList.add(PerformanceEntity);
                        }
                    }
                }
            }
        }
        if (treatTimeCardHandlerPerformanceList != null && treatTimeCardHandlerPerformanceList.size() > 0) {
            List<List<TreatTimeCardHandlerPerformanceEntity>> subs = Lists.partition(treatTimeCardHandlerPerformanceList, 20);
            for (List<TreatTimeCardHandlerPerformanceEntity> entityList : subs) {
                treatTimeCardHandlerPerformanceMapper.insertTreatTimeCardHandlerPerformance(entityList);
            }
        }
        if (treatTimeCardHandlerJobTypePerformanceList != null && treatTimeCardHandlerJobTypePerformanceList.size() > 0) {
            List<List<TreatTimeCardHandlerJobTypePerformanceEntity>> subs = Lists.partition(treatTimeCardHandlerJobTypePerformanceList, 20);
            for (List<TreatTimeCardHandlerJobTypePerformanceEntity> entityList : subs) {
                treatTimeCardHandlerPerformanceMapper.insertTreatTimeCardHandlerJobTypePerformance(entityList);
            }
        }

        return BaseOutput.success();
    }
}