package com.zhelian.service.khs.saleProjectEntityPerformance;


import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.SaleProjectEntityPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryHandlerCommissionQueryForm;
import com.zhelian.model.khs.entity.SaleGoodPerformanceEntity;
import com.zhelian.model.khs.form.input.SaleGoodCommissionAddForm;
import com.zhelian.model.khs.form.input.SaleGoodsPerformanceAddForm;
import com.zhelian.model.khs.form.output.SaleGoodPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleProjectEntityPerformanceService {

    @Autowired
    SaleProjectEntityPerformanceMapper saleProjectEntityPerformanceMapper;

    public BaseOutput allSaleProjectEntityPerformance(SaleProjectCategoryHandlerCommissionQueryForm form) {
        List<SaleGoodPerformanceOutputForm> list = saleProjectEntityPerformanceMapper.allSaleProjectEntityPerformance(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleProjectEntityPerformance(SaleGoodsPerformanceAddForm form) {

        saleProjectEntityPerformanceMapper.deleteSaleProjectEntityPerformanceByEntityIDAndCategory(form.EntityID, form.CategoryID);
        List<SaleGoodPerformanceEntity> list = new ArrayList<>();
        if (form.Good != null && form.Good.size() > 0) {
            for (SaleGoodCommissionAddForm saleProjectEntityPerformanceAddForm : form.Good) {
                if (saleProjectEntityPerformanceAddForm.PayRate != null ||
                        saleProjectEntityPerformanceAddForm.SavingCardRate != null ||
                        saleProjectEntityPerformanceAddForm.SavingCardLargessRate != null) {
                    SaleGoodPerformanceEntity saleProjectEntityPerformanceEntity = new SaleGoodPerformanceEntity();
                    saleProjectEntityPerformanceEntity.EntityID = form.EntityID;
                    saleProjectEntityPerformanceEntity.GoodID = saleProjectEntityPerformanceAddForm.GoodID;
                    saleProjectEntityPerformanceEntity.PayRate = saleProjectEntityPerformanceAddForm.PayRate;
                    saleProjectEntityPerformanceEntity.SavingCardRate = saleProjectEntityPerformanceAddForm.SavingCardRate;
                    saleProjectEntityPerformanceEntity.SavingCardLargessRate = saleProjectEntityPerformanceAddForm.SavingCardLargessRate;
                    list.add(saleProjectEntityPerformanceEntity);
                }
            }
        }
        if (list != null && list.size() > 0) {
            List<List<SaleGoodPerformanceEntity>> subs = Lists.partition(list, 20);
            for (List<SaleGoodPerformanceEntity> entityList : subs) {
                saleProjectEntityPerformanceMapper.insertSaleProjectEntityPerformance(entityList);
            }
        }
        return BaseOutput.success();
    }

}