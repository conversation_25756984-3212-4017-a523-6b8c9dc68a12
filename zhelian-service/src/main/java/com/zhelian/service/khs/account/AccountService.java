package com.zhelian.service.khs.account;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.zhelian.core.authorization.user.BasicAuthenticationUser;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.redis.utils.RedisAuthenticationUtil;
import com.zhelian.core.redis.utils.RedisRestPasswordVerificationCodeUtil;
import com.zhelian.core.shortMessage.ShortMessageUtil;
import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.CRM.CustomerMapper;
import com.zhelian.mapper.enterprise.EnterpriseMapper;
import com.zhelian.mapper.ibeauty.EmployeeLoginEntityMapper;
import com.zhelian.mapper.khs.EmployeeEntityPermissionMapper;
import com.zhelian.mapper.khs.EmployeeMapper;
import com.zhelian.mapper.khs.EntityMapper;
import com.zhelian.mapper.khs.MenuPermissionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.enterprise.entity.EnterpriseEntity;
import com.zhelian.model.ibeauty.entity.EmployeeLoginEntityEntity;
import com.zhelian.model.ibeauty.entity.EmployeeLoginEntityExtendEntity;
import com.zhelian.model.khs.entity.EmployeeEntity;
import com.zhelian.model.khs.entity.EmployeeEntityPermissionExtendEntity;
import com.zhelian.model.khs.entity.EmployeeExtendEntity;
import com.zhelian.model.khs.entity.EntityEntity;
import com.zhelian.model.khs.form.input.*;
import com.zhelian.model.khs.form.output.*;
import com.zhelian.model.miniprogram.entity.EmployeeLoginEntityMiniProgramEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

@Service
public class AccountService {

    @Autowired
    EnterpriseMapper enterpriseMapper;
    @Autowired
    EntityMapper entityMapper;
    @Autowired
    EmployeeMapper employeeMapper;
    @Autowired
    EmployeeLoginEntityMapper employeeLoginEntityMapper;
    @Autowired
    MenuPermissionMapper menuPermissionMapper;
    @Autowired
    EmployeeEntityPermissionMapper employeeEntityPermissionMapper;
    @Autowired
    CustomerMapper customerMapper;

    public BaseOutput loginVerification(LoginInputForm loginInputForm, String loginChannel) {

        EnterpriseEntity enterpriseEntity = enterpriseMapper.getEnterpriseByCode(loginInputForm.EnterpriseCode.trim());
        if (enterpriseEntity == null) {
            return BaseOutput.failed("未找到商户信息");
        }
        if (enterpriseEntity.LicenseExpriationDate.before(new Date())) {
            return BaseOutput.failed("商户授权已过期");
        }


        EmployeeExtendEntity employeeEntity = employeeMapper.getEmployeeExtendByID(loginInputForm.Username.trim());
        if (employeeEntity == null) {
            return BaseOutput.failed("用户不存在");
        }

        if (!employeeEntity.State) {
            return BaseOutput.failed("用户已被停用");
        }

        if (!loginInputForm.Password.trim().equalsIgnoreCase(employeeEntity.Password)) {
            return BaseOutput.failed("密码不正确");
        }

        if (loginChannel.equals("PC") && menuPermissionMapper.getTopMenuByEmployeeIDAndMenuType(employeeEntity.ID, "1").size() <= 0) {
            return BaseOutput.failed("用户没有PC菜单权限");
        }

        List<EmployeeEntityPermissionExtendEntity> employeeEntityPermissionExtendEntities = employeeEntityPermissionMapper.getEmployeeEntityPermissionByEmployeeID(employeeEntity.ID);
        if (employeeEntityPermissionExtendEntities.size() <= 0) {
            return BaseOutput.failed("用户没有权限部门");
        }

        BasicAuthenticationUser basicAuthenticationUser = new BasicAuthenticationUser();
        basicAuthenticationUser.EnterpriseCode = enterpriseEntity.EnterpriseCode;
        basicAuthenticationUser.LicenseExpriationDate = enterpriseEntity.LicenseExpriationDate;
        basicAuthenticationUser.UserID = employeeEntity.ID;

        LoginOutputForm loginOutputForm = new LoginOutputForm();
        EmployeeLoginEntityExtendEntity employeeLoginEntityExtendEntity = employeeLoginEntityMapper.getEmployeeLoginEntityByEmployeeID(employeeEntity.ID);
        if (employeeLoginEntityExtendEntity != null) {
            basicAuthenticationUser.EntityID = employeeLoginEntityExtendEntity.EntityID;
            loginOutputForm.EntityID = employeeLoginEntityExtendEntity.EntityID;
            loginOutputForm.EntityName = employeeLoginEntityExtendEntity.EntityName;
            loginOutputForm.IsStore = employeeLoginEntityExtendEntity.IsStore;
            loginOutputForm.IsWarehouse = employeeLoginEntityExtendEntity.IsWarehouse;
        } else {
            basicAuthenticationUser.EntityID = employeeEntityPermissionExtendEntities.get(0).EntityID;
            loginOutputForm.EntityID = employeeEntityPermissionExtendEntities.get(0).EntityID;
            loginOutputForm.EntityName = employeeEntityPermissionExtendEntities.get(0).EntityName;
            loginOutputForm.IsStore = employeeEntityPermissionExtendEntities.get(0).IsStore;
            loginOutputForm.IsWarehouse = employeeEntityPermissionExtendEntities.get(0).IsWarehouse;

            employeeLoginEntityMapper.deleteEmployeeLoginEntityByEmployeeID(employeeEntity.ID);

            EmployeeLoginEntityEntity employeeLoginEntityEntity = new EmployeeLoginEntityEntity();
            employeeLoginEntityEntity.EmployeeID = employeeEntity.ID;
            employeeLoginEntityEntity.EntityID = employeeEntityPermissionExtendEntities.get(0).EntityID;
            employeeLoginEntityMapper.addEmployeeLoginEntity(employeeLoginEntityEntity);
        }

        loginOutputForm.EmployeeName = employeeEntity.Name;
        loginOutputForm.PhoneNumber = employeeEntity.PhoneNumber;
        loginOutputForm.JobName = employeeEntity.JobName;
        loginOutputForm.EmployeeID = employeeEntity.ID;
        loginOutputForm.AuthToken = basicAuthenticationUser.getAuthToken();
        RedisAuthenticationUtil.set(basicAuthenticationUser.EnterpriseCode, basicAuthenticationUser.UserID, loginOutputForm.AuthToken);
        return BaseOutputForm.success(loginOutputForm, "登陆成功");
    }

    public BaseOutput getPCMenuPermission() {
        List<EmployeeMenuPermissionOutputForm> menuPermissionOutputForms = menuPermissionMapper.getEmployeeMenuPermissionByType("1", SystemUserUtil.getSystemUserID());
        JSONArray result = ListUtil.listToTree(JSONArray.parseArray(JSON.toJSONString(menuPermissionOutputForms, JSONWriter.Feature.WriteMapNullValue)), "Code", "ParentCode", "Child");
        return BaseOutputForm.success(result);
    }

    public BaseOutput getMiniprogramMenuPermission() {
        List<EmployeeMimiProgramMenuPermissionOutputForm> menuPermissionOutputForms = menuPermissionMapper.getEmployeeMiniprogramMenuPermissionByType("2", SystemUserUtil.getSystemUserID());
        JSONArray result = ListUtil.listToTree(JSONArray.parseArray(JSON.toJSONString(menuPermissionOutputForms, JSONWriter.Feature.WriteMapNullValue)), "Code", "ParentCode", "Child");
        return BaseOutputForm.success(result);
    }

    public BaseOutput getPCPermissionRouter() {
        List<RouterOutputForm> routerOutputForms = menuPermissionMapper.getEmployeeRouterByByEmployeeID("1", SystemUserUtil.getSystemUserID());
        if (SystemUserUtil.getSystemUserID().equals("yanchangqing") && SystemUserUtil.getSystemEnterpriseCode().equals("develop")) {
            //DataBaseSchema
            RouterOutputForm schemaRouterOutputForm = new RouterOutputForm();
            schemaRouterOutputForm.RouterPath = "/System/Schema";
            schemaRouterOutputForm.RouterComponentPath = "views/system/schema";
            schemaRouterOutputForm.RouterName = "DataBaseSchema";
            RouterMetaOutputForm schemaRouterMetaOutputForm = new RouterMetaOutputForm();
            schemaRouterMetaOutputForm.Title = "DataBaseSchema";
            schemaRouterOutputForm.RouterMeta = schemaRouterMetaOutputForm;
            routerOutputForms.add(schemaRouterOutputForm);
        }

        //系统首页
        RouterOutputForm homeRouterOutputForm = new RouterOutputForm();
        homeRouterOutputForm.RouterPath = "/";
        homeRouterOutputForm.RouterComponentPath = "views/homePage/homePage";
        homeRouterOutputForm.RouterName = "Home";
        RouterMetaOutputForm notFoundRouterMetaOutputForm = new RouterMetaOutputForm();
        notFoundRouterMetaOutputForm.Title = "系统首页";
        homeRouterOutputForm.RouterMeta = notFoundRouterMetaOutputForm;
        routerOutputForms.add(homeRouterOutputForm);

        return BaseOutputForm.success(routerOutputForms);
    }

    public BaseOutput getEmployeeEntityPermission(String employeeID) {

        List<EntityEntity> list = employeeEntityPermissionMapper.entityPermission(employeeID);
        JSONArray result = ListUtil.listToTree(JSONArray.parseArray(JSON.toJSONString(list, JSONWriter.Feature.WriteMapNullValue)), "ID", "ParentID", "Child");

        return BaseOutputForm.success(result);
    }

    public BaseOutput switchEntity(SwitchEntityForm form) {

        EnterpriseEntity enterpriseEntity = enterpriseMapper.getEnterpriseByCode(SystemUserUtil.getSystemEnterpriseCode());
        if (enterpriseEntity == null) {
            return BaseOutput.failed("未找到商户信息");
        }
        if (enterpriseEntity.LicenseExpriationDate.before(new Date())) {
            return BaseOutput.failed("商户授权已过期");
        }

        BasicAuthenticationUser basicAuthenticationUser = new BasicAuthenticationUser();
        basicAuthenticationUser.EnterpriseCode = enterpriseEntity.EnterpriseCode;
        basicAuthenticationUser.LicenseExpriationDate = enterpriseEntity.LicenseExpriationDate;
        basicAuthenticationUser.UserID = SystemUserUtil.getSystemUserID();

        SwitchEntityOutputForm outputForm = new SwitchEntityOutputForm();
        EntityEntity entityInfo = entityMapper.entityInfo(form.EntityID);
        if (entityInfo == null) {
            return BaseOutput.failed("组织单位已停用");
        } else {
            basicAuthenticationUser.EntityID = entityInfo.ID;
            outputForm.EntityName = entityInfo.EntityName;
            outputForm.IsStore = entityInfo.IsStore;
            outputForm.IsWarehouse = entityInfo.IsWarehouse;
            employeeLoginEntityMapper.deleteEmployeeLoginEntityByEmployeeID(SystemUserUtil.getSystemUserID());

            EmployeeLoginEntityEntity employeeLoginEntityEntity = new EmployeeLoginEntityEntity();
            employeeLoginEntityEntity.EmployeeID = SystemUserUtil.getSystemUserID();
            employeeLoginEntityEntity.EntityID = entityInfo.ID;
            employeeLoginEntityMapper.addEmployeeLoginEntity(employeeLoginEntityEntity);
        }

        outputForm.AuthToken = basicAuthenticationUser.getAuthToken();
        RedisAuthenticationUtil.set(basicAuthenticationUser.EnterpriseCode, basicAuthenticationUser.UserID, outputForm.AuthToken);
        return BaseOutputForm.success(outputForm, "切换成功");
    }

    public BaseOutput changePassword(ChangePasswordForm form) {

        String userID = SystemUserUtil.getSystemUserID();
        EmployeeEntity employeeEntity = employeeMapper.getEmployeeByID(userID);
        if (!employeeEntity.Password.equals(form.OldPassword))
            return BaseOutput.failed("旧密码错误");

        employeeMapper.updateEmpPassWord(form.NewPassword, userID);
        RedisAuthenticationUtil.delete(userID);
        return BaseOutput.success();
    }

    public BaseOutput sendVerificationCode(SendVerificationCodeForm form) {

        EnterpriseEntity enterpriseEntity = enterpriseMapper.getEnterpriseByCode(form.EnterpriseCode.trim());
        if (enterpriseEntity == null) {
            return BaseOutput.failed("未找到商户信息");
        }
        if (enterpriseEntity.LicenseExpriationDate.before(new Date())) {
            return BaseOutput.failed("商户授权已过期");
        }

        EmployeeEntity employeeEntity = employeeMapper.getEmployeeByID(form.Username);
        if (employeeEntity == null)
            return BaseOutput.failed("您操作的账号信息不存在，请联系您的管理员");

        if (!StringUtils.hasText(employeeEntity.PhoneNumber)) {
            return BaseOutput.failed("您操作的账号信息不存在手机号，请联系您的管理员");
        }

        ShortMessageUtil.SendRestPasswordVerificationCode(enterpriseEntity.EnterpriseCode, employeeEntity.ID, employeeEntity.PhoneNumber);
        return BaseOutput.success("发送成功");
    }

    public BaseOutput resetPassword(ResetPasswordForm form) {
        EnterpriseEntity enterpriseEntity = enterpriseMapper.getEnterpriseByCode(form.EnterpriseCode.trim());
        if (enterpriseEntity == null) {
            return BaseOutput.failed("未找到商户信息");
        }
        if (enterpriseEntity.LicenseExpriationDate.before(new Date())) {
            return BaseOutput.failed("商户授权已过期");
        }


        EmployeeEntity employeeEntity = employeeMapper.getEmployeeByID(form.Username);

        if (employeeEntity == null)
            return BaseOutput.failed("您操作的账号信息不存在，请联系您的管理员");

        if (!form.VerificationCode.equals(RedisRestPasswordVerificationCodeUtil.get(enterpriseEntity.EnterpriseCode, employeeEntity.ID))) {
            return BaseOutput.failed("验证码不正确或已过期，请重新获取");
        }
        employeeMapper.updateEmpPassWord(form.Password, employeeEntity.ID);
        RedisRestPasswordVerificationCodeUtil.delete(enterpriseEntity.EnterpriseCode, employeeEntity.ID);
        RedisAuthenticationUtil.delete(enterpriseEntity.EnterpriseCode, employeeEntity.ID);
        return BaseOutput.success("重置成功");
    }

    public BaseOutput switchMiniprogramEntity(SwitchEntityForm form) {

        EnterpriseEntity enterpriseEntity = enterpriseMapper.getEnterpriseByCode(SystemUserUtil.getSystemEnterpriseCode());
        if (enterpriseEntity == null) {
            return BaseOutput.failed("未找到商户信息");
        }
        if (enterpriseEntity.LicenseExpriationDate.before(new Date())) {
            return BaseOutput.failed("商户授权已过期");
        }

        BasicAuthenticationUser basicAuthenticationUser = new BasicAuthenticationUser();
        basicAuthenticationUser.EnterpriseCode = enterpriseEntity.EnterpriseCode;
        basicAuthenticationUser.LicenseExpriationDate = enterpriseEntity.LicenseExpriationDate;
        basicAuthenticationUser.UserID = SystemUserUtil.getSystemUserID();

        SwitchEntityOutputForm outputForm = new SwitchEntityOutputForm();
        EntityEntity entityInfo = entityMapper.entityInfo(form.EntityID);
        if (entityInfo == null) {
            return BaseOutput.failed("组织单位已停用");
        } else {
            basicAuthenticationUser.EntityID = entityInfo.ID;
            outputForm.EntityName = entityInfo.EntityName;
            outputForm.IsStore = entityInfo.IsStore;
            outputForm.IsWarehouse = entityInfo.IsWarehouse;
            employeeLoginEntityMapper.deleteEmployeeLoginEntityMiniprogramByEmployeeID(SystemUserUtil.getSystemUserID());

            EmployeeLoginEntityMiniProgramEntity employeeLoginEntityMiniProgramEntity = new EmployeeLoginEntityMiniProgramEntity();
            employeeLoginEntityMiniProgramEntity.EmployeeID = SystemUserUtil.getSystemUserID();
            employeeLoginEntityMiniProgramEntity.EntityID = entityInfo.ID;
            employeeLoginEntityMapper.addEmployeeLoginEntityMiniprogram(employeeLoginEntityMiniProgramEntity);
        }

        outputForm.AuthToken = basicAuthenticationUser.getAuthToken();
        RedisAuthenticationUtil.set(basicAuthenticationUser.EnterpriseCode, basicAuthenticationUser.UserID, outputForm.AuthToken);
        return BaseOutputForm.success(outputForm, "切换成功");
    }
}
