package com.zhelian.service.khs.saleTimeCardPerformance;


import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.SalePackageCardTimeCardSchemeHandlerPerformanceMapper;
import com.zhelian.mapper.khs.SaleTimeCardSchemeHandlerPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardSchemeHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardSchemeHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardSchemeHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardsSchemeHandlerCommissionAddForm;
import com.zhelian.model.khs.entity.SalePackageCardTimeCardSchemeHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SalePackageCardTimeCardSchemeHandlerPerformanceEntity;
import com.zhelian.model.khs.entity.SaleTimeCardSchemeHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SaleTimeCardSchemeHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleGoodCategoryHandlerPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleTimeCardSchemeHandlerPerformanceService {

    @Autowired
    SaleTimeCardSchemeHandlerPerformanceMapper saleTimeCardSchemeHandlerPerformanceMapper;

    @Autowired
    SalePackageCardTimeCardSchemeHandlerPerformanceMapper salePackageCardTimeCardSchemeHandlerPerformanceMapper;

    public BaseOutput allSaleTimeCardSchemeHandlerPerformance(SaleTimeCardSchemeHandlerCommissionQueryForm form) {
        List<SaleGoodCategoryHandlerPerformanceOutputForm> list = saleTimeCardSchemeHandlerPerformanceMapper.allSaleTimeCardSchemeHandlerPerformance(form.EntityID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSalePackageCardTimeCardSchemeHandlerPerformance(SaleTimeCardSchemeHandlerCommissionQueryForm form) {
        List<SaleGoodCategoryHandlerPerformanceOutputForm> list = salePackageCardTimeCardSchemeHandlerPerformanceMapper.allSalePackageCardTimeCardSchemeHandlerPerformance(form.EntityID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleTimeCardSchemeHandlerPerformance(SaleTimeCardsSchemeHandlerCommissionAddForm form) {

        saleTimeCardSchemeHandlerPerformanceMapper.deleteSaleTimeCardSchemeHandler(form.EntityID);
        saleTimeCardSchemeHandlerPerformanceMapper.deleteSaleTimeCardSchemeHandlerJobType(form.EntityID);
        List<SaleTimeCardSchemeHandlerPerformanceEntity> listSaleTimeCardSchemeHandlerPerformanceEntity = new ArrayList<>();
        List<SaleTimeCardSchemeHandlerJobTypePerformanceEntity> listSaleTimeCardSchemeHandlerJobTypePerformanceEntity = new ArrayList<>();
        if (form.TimeCardHandler != null && form.TimeCardHandler.size() > 0) {
            for (SaleTimeCardSchemeHandlerCommissionAddForm saleTimeCardSchemeHandlerAddForm : form.TimeCardHandler) {
                if (saleTimeCardSchemeHandlerAddForm.PayRate != null ||
                        saleTimeCardSchemeHandlerAddForm.SavingCardRate != null ||
                        saleTimeCardSchemeHandlerAddForm.SavingCardLargessRate != null) {
                    SaleTimeCardSchemeHandlerPerformanceEntity saleTimeCardSchemeHandlerPerformanceEntity = new SaleTimeCardSchemeHandlerPerformanceEntity();
                    saleTimeCardSchemeHandlerPerformanceEntity.EntityID = form.EntityID;
                    saleTimeCardSchemeHandlerPerformanceEntity.TimeCardSaleHandlerID = saleTimeCardSchemeHandlerAddForm.SaleHandlerID;
                    saleTimeCardSchemeHandlerPerformanceEntity.PayRate = saleTimeCardSchemeHandlerAddForm.PayRate;
                    saleTimeCardSchemeHandlerPerformanceEntity.SavingCardRate = saleTimeCardSchemeHandlerAddForm.SavingCardRate;
                    saleTimeCardSchemeHandlerPerformanceEntity.SavingCardLargessRate = saleTimeCardSchemeHandlerAddForm.SavingCardLargessRate;
                    listSaleTimeCardSchemeHandlerPerformanceEntity.add(saleTimeCardSchemeHandlerPerformanceEntity);
                }

                if (saleTimeCardSchemeHandlerAddForm.HandlerJobTypeCommission != null && saleTimeCardSchemeHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleTimeCardSchemeHandlerJobTypeCommissionAddForm saleTimeCardSchemeHandlerJobTypePerformanceAddForm : saleTimeCardSchemeHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleTimeCardSchemeHandlerJobTypePerformanceAddForm.PayRate != null ||
                                saleTimeCardSchemeHandlerJobTypePerformanceAddForm.SavingCardRate != null ||
                                saleTimeCardSchemeHandlerJobTypePerformanceAddForm.SavingCardLargessRate != null) {
                            SaleTimeCardSchemeHandlerJobTypePerformanceEntity saleTimeCardSchemeHandlerJobTypePerformanceEntity = new SaleTimeCardSchemeHandlerJobTypePerformanceEntity();
                            saleTimeCardSchemeHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            saleTimeCardSchemeHandlerJobTypePerformanceEntity.TimeCardSaleHandlerID = saleTimeCardSchemeHandlerAddForm.SaleHandlerID;
                            saleTimeCardSchemeHandlerJobTypePerformanceEntity.JobTypeID = saleTimeCardSchemeHandlerJobTypePerformanceAddForm.JobTypeID;
                            saleTimeCardSchemeHandlerJobTypePerformanceEntity.PayFixed = saleTimeCardSchemeHandlerJobTypePerformanceAddForm.PayFixed;
                            saleTimeCardSchemeHandlerJobTypePerformanceEntity.PayRate = saleTimeCardSchemeHandlerJobTypePerformanceAddForm.PayRate;
                            saleTimeCardSchemeHandlerJobTypePerformanceEntity.SavingCardFixed = saleTimeCardSchemeHandlerJobTypePerformanceAddForm.SavingCardFixed;
                            saleTimeCardSchemeHandlerJobTypePerformanceEntity.SavingCardRate = saleTimeCardSchemeHandlerJobTypePerformanceAddForm.SavingCardRate;
                            saleTimeCardSchemeHandlerJobTypePerformanceEntity.SavingCardLargessFixed = saleTimeCardSchemeHandlerJobTypePerformanceAddForm.SavingCardLargessFixed;
                            saleTimeCardSchemeHandlerJobTypePerformanceEntity.SavingCardLargessRate = saleTimeCardSchemeHandlerJobTypePerformanceAddForm.SavingCardLargessRate;
                            saleTimeCardSchemeHandlerJobTypePerformanceEntity.SpecialBenefit = saleTimeCardSchemeHandlerJobTypePerformanceAddForm.SpecialBenefit;
                            listSaleTimeCardSchemeHandlerJobTypePerformanceEntity.add(saleTimeCardSchemeHandlerJobTypePerformanceEntity);
                        }
                    }
                }
            }
        }


        salePackageCardTimeCardSchemeHandlerPerformanceMapper.deleteSalePackageCardTimeCardSchemeHandler(form.EntityID);
        salePackageCardTimeCardSchemeHandlerPerformanceMapper.deleteSalePackageCardTimeCardSchemeHandlerJobType(form.EntityID);
        List<SalePackageCardTimeCardSchemeHandlerPerformanceEntity> listSalePackageCardTimeCardSchemeHandlerPerformanceEntity = new ArrayList<>();
        List<SalePackageCardTimeCardSchemeHandlerJobTypePerformanceEntity> listSalePackageCardTimeCardSchemeHandlerJobTypePerformanceEntity = new ArrayList<>();
        if (form.PackageCardTimeCardHandler != null && form.PackageCardTimeCardHandler.size() > 0) {
            for (SaleTimeCardSchemeHandlerCommissionAddForm saleTimeCardSchemeHandlerAddForm : form.PackageCardTimeCardHandler) {
                if (saleTimeCardSchemeHandlerAddForm.PayRate != null ||
                        saleTimeCardSchemeHandlerAddForm.SavingCardRate != null ||
                        saleTimeCardSchemeHandlerAddForm.SavingCardLargessRate != null) {
                    SalePackageCardTimeCardSchemeHandlerPerformanceEntity salePackageCardTimeCardSchemeHandlerPerformanceEntity = new SalePackageCardTimeCardSchemeHandlerPerformanceEntity();
                    salePackageCardTimeCardSchemeHandlerPerformanceEntity.EntityID = form.EntityID;
                    salePackageCardTimeCardSchemeHandlerPerformanceEntity.PackageCardSaleHandlerID = saleTimeCardSchemeHandlerAddForm.SaleHandlerID;
                    salePackageCardTimeCardSchemeHandlerPerformanceEntity.PayRate = saleTimeCardSchemeHandlerAddForm.PayRate;
                    salePackageCardTimeCardSchemeHandlerPerformanceEntity.SavingCardRate = saleTimeCardSchemeHandlerAddForm.SavingCardRate;
                    salePackageCardTimeCardSchemeHandlerPerformanceEntity.SavingCardLargessRate = saleTimeCardSchemeHandlerAddForm.SavingCardLargessRate;
                    listSalePackageCardTimeCardSchemeHandlerPerformanceEntity.add(salePackageCardTimeCardSchemeHandlerPerformanceEntity);
                }

                if (saleTimeCardSchemeHandlerAddForm.HandlerJobTypeCommission != null && saleTimeCardSchemeHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleTimeCardSchemeHandlerJobTypeCommissionAddForm saleTimeCardSchemeHandlerJobTypePerformanceAddForm : saleTimeCardSchemeHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleTimeCardSchemeHandlerJobTypePerformanceAddForm.PayRate != null ||
                                saleTimeCardSchemeHandlerJobTypePerformanceAddForm.SavingCardRate != null ||
                                saleTimeCardSchemeHandlerJobTypePerformanceAddForm.SavingCardLargessRate != null) {
                            SalePackageCardTimeCardSchemeHandlerJobTypePerformanceEntity salePackageCardTimeCardSchemeHandlerJobTypePerformanceEntity = new SalePackageCardTimeCardSchemeHandlerJobTypePerformanceEntity();
                            salePackageCardTimeCardSchemeHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            salePackageCardTimeCardSchemeHandlerJobTypePerformanceEntity.PackageCardSaleHandlerID = saleTimeCardSchemeHandlerAddForm.SaleHandlerID;
                            salePackageCardTimeCardSchemeHandlerJobTypePerformanceEntity.JobTypeID = saleTimeCardSchemeHandlerJobTypePerformanceAddForm.JobTypeID;
                            salePackageCardTimeCardSchemeHandlerJobTypePerformanceEntity.PayRate = saleTimeCardSchemeHandlerJobTypePerformanceAddForm.PayRate;
                            salePackageCardTimeCardSchemeHandlerJobTypePerformanceEntity.SavingCardRate = saleTimeCardSchemeHandlerJobTypePerformanceAddForm.SavingCardRate;
                            salePackageCardTimeCardSchemeHandlerJobTypePerformanceEntity.SavingCardLargessRate = saleTimeCardSchemeHandlerJobTypePerformanceAddForm.SavingCardLargessRate;
                            listSalePackageCardTimeCardSchemeHandlerJobTypePerformanceEntity.add(salePackageCardTimeCardSchemeHandlerJobTypePerformanceEntity);
                        }
                    }
                }
            }
        }

        if (listSaleTimeCardSchemeHandlerPerformanceEntity != null && listSaleTimeCardSchemeHandlerPerformanceEntity.size() > 0) {
            List<List<SaleTimeCardSchemeHandlerPerformanceEntity>> subs = Lists.partition(listSaleTimeCardSchemeHandlerPerformanceEntity, 20);
            for (List<SaleTimeCardSchemeHandlerPerformanceEntity> entityList : subs) {
                saleTimeCardSchemeHandlerPerformanceMapper.updateSaleTimeCardSchemeHandlerPerformance(entityList);
            }
        }
        if (listSaleTimeCardSchemeHandlerJobTypePerformanceEntity != null && listSaleTimeCardSchemeHandlerJobTypePerformanceEntity.size() > 0) {
            List<List<SaleTimeCardSchemeHandlerJobTypePerformanceEntity>> subs = Lists.partition(listSaleTimeCardSchemeHandlerJobTypePerformanceEntity, 20);
            for (List<SaleTimeCardSchemeHandlerJobTypePerformanceEntity> entityList : subs) {
                saleTimeCardSchemeHandlerPerformanceMapper.insertSaleTimeCardSchemeHandlerJobTypePerformance(entityList);
            }
        }
        if (listSalePackageCardTimeCardSchemeHandlerPerformanceEntity != null && listSalePackageCardTimeCardSchemeHandlerPerformanceEntity.size() > 0) {
            List<List<SalePackageCardTimeCardSchemeHandlerPerformanceEntity>> subs = Lists.partition(listSalePackageCardTimeCardSchemeHandlerPerformanceEntity, 20);
            for (List<SalePackageCardTimeCardSchemeHandlerPerformanceEntity> entityList : subs) {
                salePackageCardTimeCardSchemeHandlerPerformanceMapper.updateSalePackageCardTimeCardSchemeHandlerPerformance(entityList);
            }
        }
        if (listSalePackageCardTimeCardSchemeHandlerJobTypePerformanceEntity != null && listSalePackageCardTimeCardSchemeHandlerJobTypePerformanceEntity.size() > 0) {
            List<List<SalePackageCardTimeCardSchemeHandlerJobTypePerformanceEntity>> subs = Lists.partition(listSalePackageCardTimeCardSchemeHandlerJobTypePerformanceEntity, 20);
            for (List<SalePackageCardTimeCardSchemeHandlerJobTypePerformanceEntity> entityList : subs) {
                salePackageCardTimeCardSchemeHandlerPerformanceMapper.insertSalePackageCardTimeCardSchemeHandlerJobTypePerformance(entityList);
            }
        }

        return BaseOutput.success();
    }
}