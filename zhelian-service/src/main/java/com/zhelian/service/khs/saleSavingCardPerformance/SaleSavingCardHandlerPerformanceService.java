package com.zhelian.service.khs.saleSavingCardPerformance;


import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.SalePackageCardSavingCardHandlerPerformanceMapper;
import com.zhelian.mapper.khs.SaleSavingCardHandlerPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardHandlersCommissionAddForm;
import com.zhelian.model.khs.entity.SalePackageCardSavingCardHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SalePackageCardSavingCardHandlerPerformanceEntity;
import com.zhelian.model.khs.entity.SaleSavingCardHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SaleSavingCardHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleSavingCardCategoryHandlerPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleSavingCardHandlerPerformanceService {

    @Autowired
    SaleSavingCardHandlerPerformanceMapper saleSavingCardHandlerPerformanceMapper;

    @Autowired
    SalePackageCardSavingCardHandlerPerformanceMapper salePackageCardSavingCardHandlerPerformanceMapper;

    public BaseOutput allSaleSavingCardHandlerPerformance(SaleSavingCardHandlerCommissionQueryForm form) {
        List<SaleSavingCardCategoryHandlerPerformanceOutputForm> list = saleSavingCardHandlerPerformanceMapper.allSaleSavingCardHandlerPerformance(form.EntityID, form.SavingCardID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSalePackageCardSavingCardHandlerPerformance(SaleSavingCardHandlerCommissionQueryForm form) {
        List<SaleSavingCardCategoryHandlerPerformanceOutputForm> list = salePackageCardSavingCardHandlerPerformanceMapper.allSalePackageCardSavingCardHandlerPerformance(form.EntityID, form.SavingCardID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleSavingCardHandlerPerformance(SaleSavingCardHandlersCommissionAddForm form) {

        saleSavingCardHandlerPerformanceMapper.deleteSaleSavingCardHandler(form.EntityID, form.SavingCardID);
        saleSavingCardHandlerPerformanceMapper.deleteSaleSavingCardHandlerJobType(form.EntityID, form.SavingCardID);
        List<SaleSavingCardHandlerPerformanceEntity> listSaleSavingCardHandlerPerformanceEntity = new ArrayList<>();
        List<SaleSavingCardHandlerJobTypePerformanceEntity> listSaleSavingCardHandlerJobTypePerformanceEntity = new ArrayList<>();
        if (form.SavingCardHandler != null && form.SavingCardHandler.size() > 0) {
            for (SaleSavingCardHandlerCommissionAddForm saleSavingCardHandlerAddForm : form.SavingCardHandler) {
                if (saleSavingCardHandlerAddForm.PayRate != null) {
                    SaleSavingCardHandlerPerformanceEntity saleSavingCardHandlerPerformanceEntity = new SaleSavingCardHandlerPerformanceEntity();
                    saleSavingCardHandlerPerformanceEntity.EntityID = form.EntityID;
                    saleSavingCardHandlerPerformanceEntity.SavingCardID = form.SavingCardID;
                    saleSavingCardHandlerPerformanceEntity.SavingCardSaleHandlerID = saleSavingCardHandlerAddForm.SaleHandlerID;
                    saleSavingCardHandlerPerformanceEntity.PayRate = saleSavingCardHandlerAddForm.PayRate;
                    listSaleSavingCardHandlerPerformanceEntity.add(saleSavingCardHandlerPerformanceEntity);
                }

                if (saleSavingCardHandlerAddForm.HandlerJobTypeCommission != null && saleSavingCardHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleSavingCardCategoryHandlerJobTypeCommissionAddForm saleSavingCardCategoryHandlerJobTypePerformanceAddForm : saleSavingCardHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleSavingCardCategoryHandlerJobTypePerformanceAddForm.PayRate != null) {
                            SaleSavingCardHandlerJobTypePerformanceEntity saleSavingCardHandlerJobTypePerformanceEntity = new SaleSavingCardHandlerJobTypePerformanceEntity();
                            saleSavingCardHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            saleSavingCardHandlerJobTypePerformanceEntity.SavingCardID = form.SavingCardID;
                            saleSavingCardHandlerJobTypePerformanceEntity.SavingCardSaleHandlerID = saleSavingCardHandlerAddForm.SaleHandlerID;
                            saleSavingCardHandlerJobTypePerformanceEntity.JobTypeID = saleSavingCardCategoryHandlerJobTypePerformanceAddForm.JobTypeID;
                            saleSavingCardHandlerJobTypePerformanceEntity.PayRate = saleSavingCardCategoryHandlerJobTypePerformanceAddForm.PayRate;
                            listSaleSavingCardHandlerJobTypePerformanceEntity.add(saleSavingCardHandlerJobTypePerformanceEntity);
                        }
                    }
                }
            }
        }

        salePackageCardSavingCardHandlerPerformanceMapper.deleteSalePackageCardSavingCardHandler(form.EntityID, form.SavingCardID);
        salePackageCardSavingCardHandlerPerformanceMapper.deleteSalePackageCardSavingCardHandlerJobType(form.EntityID, form.SavingCardID);
        List<SalePackageCardSavingCardHandlerPerformanceEntity> listSalePackageCardSavingCardHandlerPerformanceEntity = new ArrayList<>();
        List<SalePackageCardSavingCardHandlerJobTypePerformanceEntity> listSalePackageCardSavingCardHandlerJobTypePerformanceEntity = new ArrayList<>();
        if (form.PackageCardSavingCardHandler != null && form.PackageCardSavingCardHandler.size() > 0) {
            for (SaleSavingCardHandlerCommissionAddForm saleSavingCardHandlerAddForm : form.PackageCardSavingCardHandler) {
                if (saleSavingCardHandlerAddForm.PayRate != null) {
                    SalePackageCardSavingCardHandlerPerformanceEntity salePackageCardSavingCardHandlerPerformanceEntity = new SalePackageCardSavingCardHandlerPerformanceEntity();
                    salePackageCardSavingCardHandlerPerformanceEntity.EntityID = form.EntityID;
                    salePackageCardSavingCardHandlerPerformanceEntity.SavingCardID = form.SavingCardID;
                    salePackageCardSavingCardHandlerPerformanceEntity.PackageCardSaleHandlerID = saleSavingCardHandlerAddForm.SaleHandlerID;
                    salePackageCardSavingCardHandlerPerformanceEntity.PayRate = saleSavingCardHandlerAddForm.PayRate;
                    listSalePackageCardSavingCardHandlerPerformanceEntity.add(salePackageCardSavingCardHandlerPerformanceEntity);
                }

                if (saleSavingCardHandlerAddForm.HandlerJobTypeCommission != null && saleSavingCardHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleSavingCardCategoryHandlerJobTypeCommissionAddForm saleSavingCardCategoryHandlerJobTypePerformanceAddForm : saleSavingCardHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleSavingCardCategoryHandlerJobTypePerformanceAddForm.PayFixed != null || saleSavingCardCategoryHandlerJobTypePerformanceAddForm.PayRate != null ||
                                saleSavingCardCategoryHandlerJobTypePerformanceAddForm.SpecialBenefit != null) {
                            SalePackageCardSavingCardHandlerJobTypePerformanceEntity salePackageCardSavingCardHandlerJobTypePerformanceEntity = new SalePackageCardSavingCardHandlerJobTypePerformanceEntity();
                            salePackageCardSavingCardHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            salePackageCardSavingCardHandlerJobTypePerformanceEntity.SavingCardID = form.SavingCardID;
                            salePackageCardSavingCardHandlerJobTypePerformanceEntity.PackageCardSaleHandlerID = saleSavingCardHandlerAddForm.SaleHandlerID;
                            salePackageCardSavingCardHandlerJobTypePerformanceEntity.JobTypeID = saleSavingCardCategoryHandlerJobTypePerformanceAddForm.JobTypeID;
                            salePackageCardSavingCardHandlerJobTypePerformanceEntity.PayRate = saleSavingCardCategoryHandlerJobTypePerformanceAddForm.PayRate;
                            listSalePackageCardSavingCardHandlerJobTypePerformanceEntity.add(salePackageCardSavingCardHandlerJobTypePerformanceEntity);
                        }
                    }
                }
            }
        }
        if (listSaleSavingCardHandlerPerformanceEntity != null && listSaleSavingCardHandlerPerformanceEntity.size() > 0) {
            List<List<SaleSavingCardHandlerPerformanceEntity>> subs = Lists.partition(listSaleSavingCardHandlerPerformanceEntity, 20);
            for (List<SaleSavingCardHandlerPerformanceEntity> entityList : subs) {
                saleSavingCardHandlerPerformanceMapper.updateSaleSavingCardHandlerPerformance(entityList);
            }
        }
        if (listSaleSavingCardHandlerJobTypePerformanceEntity != null && listSaleSavingCardHandlerJobTypePerformanceEntity.size() > 0) {
            List<List<SaleSavingCardHandlerJobTypePerformanceEntity>> subs = Lists.partition(listSaleSavingCardHandlerJobTypePerformanceEntity, 20);
            for (List<SaleSavingCardHandlerJobTypePerformanceEntity> entityList : subs) {
                saleSavingCardHandlerPerformanceMapper.insertSaleSavingCardHandlerJobTypePerformance(entityList);
            }
        }
        if (listSalePackageCardSavingCardHandlerPerformanceEntity != null && listSalePackageCardSavingCardHandlerPerformanceEntity.size() > 0) {
            List<List<SalePackageCardSavingCardHandlerPerformanceEntity>> subs = Lists.partition(listSalePackageCardSavingCardHandlerPerformanceEntity, 20);
            for (List<SalePackageCardSavingCardHandlerPerformanceEntity> entityList : subs) {
                salePackageCardSavingCardHandlerPerformanceMapper.updateSalePackageCardSavingCardHandlerPerformance(entityList);
            }
        }
        if (listSalePackageCardSavingCardHandlerJobTypePerformanceEntity != null && listSalePackageCardSavingCardHandlerJobTypePerformanceEntity.size() > 0) {
            List<List<SalePackageCardSavingCardHandlerJobTypePerformanceEntity>> subs = Lists.partition(listSalePackageCardSavingCardHandlerJobTypePerformanceEntity, 20);
            for (List<SalePackageCardSavingCardHandlerJobTypePerformanceEntity> entityList : subs) {
                salePackageCardSavingCardHandlerPerformanceMapper.insertSalePackageCardSavingCardHandlerJobTypePerformance(entityList);
            }
        }
        return BaseOutput.success();
    }
}