package com.zhelian.service.khs.salarySettlement;


import com.zhelian.mapper.khs.PerformanceSchemeTreatProjectMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.khs.entity.TreatProjectPerformanceEntity;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodQueryForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeTreatGoodAddForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeTreatGoodsAddForm;
import com.zhelian.model.khs.form.output.PerformanceSchemeTreatGoodOutForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class PerformanceSchemeTreatProjectService {

    @Autowired
    PerformanceSchemeTreatProjectMapper performanceSchemeTreatProjectMapper;

    public BaseOutput all(PerformanceSchemeGoodQueryForm form) {
        List<PerformanceSchemeTreatGoodOutForm> list = performanceSchemeTreatProjectMapper.getAllPerformanceSchemeProject(form.PerformanceSchemeID, form.CategoryID);
        return BaseOutputForm.success(list);

    }

    @Transactional
    public BaseOutput create(PerformanceSchemeTreatGoodsAddForm form) {
        performanceSchemeTreatProjectMapper.deleteTreatProjectPerformanceByPerformanceSchemeIDAndCategory(form.PerformanceSchemeID, form.CategoryID);
        List<TreatProjectPerformanceEntity> list = new ArrayList<>();
        if (form.Good != null && form.Good.size() > 0) {
            for (PerformanceSchemeTreatGoodAddForm performanceSchemeGoodAddForm : form.Good) {
                if (performanceSchemeGoodAddForm.PerformancePayRate != null || performanceSchemeGoodAddForm.PerformanceCardRate != null || performanceSchemeGoodAddForm.PerformanceLargessRate != null || performanceSchemeGoodAddForm.PerformanceLargessRate != null) {
                    TreatProjectPerformanceEntity TreatProjectCommissionEntity = new TreatProjectPerformanceEntity();
                    TreatProjectCommissionEntity.PerformanceSchemeID = form.PerformanceSchemeID;
                    TreatProjectCommissionEntity.ProjectID = performanceSchemeGoodAddForm.GoodID;
                    TreatProjectCommissionEntity.PerformancePayRate = performanceSchemeGoodAddForm.PerformancePayRate;
                    TreatProjectCommissionEntity.PerformanceCardRate = performanceSchemeGoodAddForm.PerformanceCardRate;
                    TreatProjectCommissionEntity.PerformanceCardLargessRate = performanceSchemeGoodAddForm.PerformanceCardLargessRate;
                    TreatProjectCommissionEntity.PerformanceLargessRate = performanceSchemeGoodAddForm.PerformanceLargessRate;
                    list.add(TreatProjectCommissionEntity);
                }
            }
            performanceSchemeTreatProjectMapper.insertTreatProjectPerformance(list);
        }
        return BaseOutput.success();
    }
}
