package com.zhelian.service.khs.saleProductPerformance;


import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.SalePackageCardProductHandlerPerformanceMapper;
import com.zhelian.mapper.khs.SaleProductHandlerPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProductHandlersCommissionAddForm;
import com.zhelian.model.khs.entity.SalePackageCardProductHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SalePackageCardProductHandlerPerformanceEntity;
import com.zhelian.model.khs.entity.SaleProductHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SaleProductHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleGoodCategoryHandlerPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleProductHandlerPerformanceService {

    @Autowired
    SaleProductHandlerPerformanceMapper saleProductHandlerPerformanceMapper;
    @Autowired
    SalePackageCardProductHandlerPerformanceMapper salePackageCardProductHandlerPerformanceMapper;

    public BaseOutput allSaleProductHandlerPerformance(SaleProductHandlerCommissionQueryForm form) {
        List<SaleGoodCategoryHandlerPerformanceOutputForm> list = saleProductHandlerPerformanceMapper.allSaleProductHandlerPerformance(form.EntityID, form.ProductID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSalePackageCardProductHandlerPerformance(SaleProductHandlerCommissionQueryForm form) {
        List<SaleGoodCategoryHandlerPerformanceOutputForm> list = salePackageCardProductHandlerPerformanceMapper.allSalePackageCardProductHandlerPerformance(form.EntityID, form.ProductID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleProductHandlerPerformance(SaleProductHandlersCommissionAddForm form) {

        saleProductHandlerPerformanceMapper.deleteSaleProductHandler(form.EntityID, form.ProductID);
        saleProductHandlerPerformanceMapper.deleteSaleProductHandlerJobType(form.EntityID, form.ProductID);
        List<SaleProductHandlerPerformanceEntity> listSaleProductHandlerPerformanceEntity = new ArrayList<>();
        List<SaleProductHandlerJobTypePerformanceEntity> listSaleProductHandlerJobTypePerformanceEntity = new ArrayList<>();
        if (form.ProductHandler != null && form.ProductHandler.size() > 0) {
            for (SaleProductHandlerCommissionAddForm saleProductHandlerAddForm : form.ProductHandler) {
                if (saleProductHandlerAddForm.PayRate != null ||
                        saleProductHandlerAddForm.SavingCardRate != null ||
                        saleProductHandlerAddForm.SavingCardLargessRate != null) {
                    SaleProductHandlerPerformanceEntity saleProductHandlerPerformanceEntity = new SaleProductHandlerPerformanceEntity();
                    saleProductHandlerPerformanceEntity.EntityID = form.EntityID;
                    saleProductHandlerPerformanceEntity.ProductID = form.ProductID;
                    saleProductHandlerPerformanceEntity.ProductSaleHandlerID = saleProductHandlerAddForm.SaleHandlerID;
                    saleProductHandlerPerformanceEntity.PayRate = saleProductHandlerAddForm.PayRate;
                    saleProductHandlerPerformanceEntity.SavingCardRate = saleProductHandlerAddForm.SavingCardRate;
                    saleProductHandlerPerformanceEntity.SavingCardLargessRate = saleProductHandlerAddForm.SavingCardLargessRate;
                    listSaleProductHandlerPerformanceEntity.add(saleProductHandlerPerformanceEntity);
                }

                if (saleProductHandlerAddForm.HandlerJobTypeCommission != null && saleProductHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleProductCategoryHandlerJobTypeCommissionAddForm saleProductCategoryHandlerJobTypePerformanceAddForm : saleProductHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleProductCategoryHandlerJobTypePerformanceAddForm.PayRate != null ||
                                saleProductCategoryHandlerJobTypePerformanceAddForm.SavingCardRate != null ||
                                saleProductCategoryHandlerJobTypePerformanceAddForm.SavingCardLargessRate != null) {
                            SaleProductHandlerJobTypePerformanceEntity saleProductHandlerJobTypePerformanceEntity = new SaleProductHandlerJobTypePerformanceEntity();
                            saleProductHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            saleProductHandlerJobTypePerformanceEntity.ProductID = form.ProductID;
                            saleProductHandlerJobTypePerformanceEntity.ProductSaleHandlerID = saleProductHandlerAddForm.SaleHandlerID;
                            saleProductHandlerJobTypePerformanceEntity.JobTypeID = saleProductCategoryHandlerJobTypePerformanceAddForm.JobTypeID;
                            saleProductHandlerJobTypePerformanceEntity.PayFixed = saleProductCategoryHandlerJobTypePerformanceAddForm.PayFixed;
                            saleProductHandlerJobTypePerformanceEntity.PayRate = saleProductCategoryHandlerJobTypePerformanceAddForm.PayRate;
                            saleProductHandlerJobTypePerformanceEntity.SavingCardFixed = saleProductCategoryHandlerJobTypePerformanceAddForm.SavingCardFixed;
                            saleProductHandlerJobTypePerformanceEntity.SavingCardRate = saleProductCategoryHandlerJobTypePerformanceAddForm.SavingCardRate;
                            saleProductHandlerJobTypePerformanceEntity.SavingCardLargessFixed = saleProductCategoryHandlerJobTypePerformanceAddForm.SavingCardLargessFixed;
                            saleProductHandlerJobTypePerformanceEntity.SavingCardLargessRate = saleProductCategoryHandlerJobTypePerformanceAddForm.SavingCardLargessRate;
                            saleProductHandlerJobTypePerformanceEntity.SpecialBenefit = saleProductCategoryHandlerJobTypePerformanceAddForm.SpecialBenefit;
                            listSaleProductHandlerJobTypePerformanceEntity.add(saleProductHandlerJobTypePerformanceEntity);
                        }
                    }
                }
            }
        }

        salePackageCardProductHandlerPerformanceMapper.deleteSalePackageCardProductHandler(form.EntityID, form.ProductID);
        salePackageCardProductHandlerPerformanceMapper.deleteSalePackageCardProductHandlerJobType(form.EntityID, form.ProductID);
        List<SalePackageCardProductHandlerPerformanceEntity> listSalePackageCardProductHandlerPerformanceEntity = new ArrayList<>();
        List<SalePackageCardProductHandlerJobTypePerformanceEntity> listSalePackageCardProductHandlerJobTypePerformanceEntity = new ArrayList<>();
        if (form.PackageCardProductHandler != null && form.PackageCardProductHandler.size() > 0) {
            for (SaleProductHandlerCommissionAddForm saleProductHandlerAddForm : form.PackageCardProductHandler) {
                if (saleProductHandlerAddForm.PayRate != null ||
                        saleProductHandlerAddForm.SavingCardRate != null ||
                        saleProductHandlerAddForm.SavingCardLargessRate != null) {
                    SalePackageCardProductHandlerPerformanceEntity salePackageCardProductHandlerPerformanceEntity = new SalePackageCardProductHandlerPerformanceEntity();
                    salePackageCardProductHandlerPerformanceEntity.EntityID = form.EntityID;
                    salePackageCardProductHandlerPerformanceEntity.ProductID = form.ProductID;
                    salePackageCardProductHandlerPerformanceEntity.PackageCardSaleHandlerID = saleProductHandlerAddForm.SaleHandlerID;
                    salePackageCardProductHandlerPerformanceEntity.PayRate = saleProductHandlerAddForm.PayRate;
                    salePackageCardProductHandlerPerformanceEntity.SavingCardRate = saleProductHandlerAddForm.SavingCardRate;
                    salePackageCardProductHandlerPerformanceEntity.SavingCardLargessRate = saleProductHandlerAddForm.SavingCardLargessRate;
                    listSalePackageCardProductHandlerPerformanceEntity.add(salePackageCardProductHandlerPerformanceEntity);
                }

                if (saleProductHandlerAddForm.HandlerJobTypeCommission != null && saleProductHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleProductCategoryHandlerJobTypeCommissionAddForm saleProductCategoryHandlerJobTypePerformanceAddForm : saleProductHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleProductCategoryHandlerJobTypePerformanceAddForm.PayRate != null ||
                                saleProductCategoryHandlerJobTypePerformanceAddForm.SavingCardRate != null ||
                                saleProductCategoryHandlerJobTypePerformanceAddForm.SavingCardLargessRate != null) {
                            SalePackageCardProductHandlerJobTypePerformanceEntity salePackageCardProductHandlerJobTypePerformanceEntity = new SalePackageCardProductHandlerJobTypePerformanceEntity();
                            salePackageCardProductHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            salePackageCardProductHandlerJobTypePerformanceEntity.ProductID = form.ProductID;
                            salePackageCardProductHandlerJobTypePerformanceEntity.PackageCardSaleHandlerID = saleProductHandlerAddForm.SaleHandlerID;
                            salePackageCardProductHandlerJobTypePerformanceEntity.JobTypeID = saleProductCategoryHandlerJobTypePerformanceAddForm.JobTypeID;
                            salePackageCardProductHandlerJobTypePerformanceEntity.PayRate = saleProductCategoryHandlerJobTypePerformanceAddForm.PayRate;
                            salePackageCardProductHandlerJobTypePerformanceEntity.SavingCardRate = saleProductCategoryHandlerJobTypePerformanceAddForm.SavingCardRate;
                            salePackageCardProductHandlerJobTypePerformanceEntity.SavingCardLargessRate = saleProductCategoryHandlerJobTypePerformanceAddForm.SavingCardLargessRate;
                            listSalePackageCardProductHandlerJobTypePerformanceEntity.add(salePackageCardProductHandlerJobTypePerformanceEntity);
                        }
                    }
                }
            }
        }
        if (listSaleProductHandlerPerformanceEntity != null && listSaleProductHandlerPerformanceEntity.size() > 0) {
            List<List<SaleProductHandlerPerformanceEntity>> subs = Lists.partition(listSaleProductHandlerPerformanceEntity, 20);
            for (List<SaleProductHandlerPerformanceEntity> entityList : subs) {
                saleProductHandlerPerformanceMapper.updateSaleProductHandlerPerformance(entityList);
            }
        }
        if (listSaleProductHandlerJobTypePerformanceEntity != null && listSaleProductHandlerJobTypePerformanceEntity.size() > 0) {
            List<List<SaleProductHandlerJobTypePerformanceEntity>> subs = Lists.partition(listSaleProductHandlerJobTypePerformanceEntity, 20);
            for (List<SaleProductHandlerJobTypePerformanceEntity> entityList : subs) {
                saleProductHandlerPerformanceMapper.insertSaleProductHandlerJobTypePerformance(entityList);
            }
        }
        if (listSalePackageCardProductHandlerPerformanceEntity != null && listSalePackageCardProductHandlerPerformanceEntity.size() > 0) {
            List<List<SalePackageCardProductHandlerPerformanceEntity>> subs = Lists.partition(listSalePackageCardProductHandlerPerformanceEntity, 20);
            for (List<SalePackageCardProductHandlerPerformanceEntity> entityList : subs) {
                salePackageCardProductHandlerPerformanceMapper.updateSalePackageCardProductHandlerPerformance(entityList);
            }
        }
        if (listSalePackageCardProductHandlerJobTypePerformanceEntity != null && listSalePackageCardProductHandlerJobTypePerformanceEntity.size() > 0) {
            List<List<SalePackageCardProductHandlerJobTypePerformanceEntity>> subs = Lists.partition(listSalePackageCardProductHandlerJobTypePerformanceEntity, 20);
            for (List<SalePackageCardProductHandlerJobTypePerformanceEntity> entityList : subs) {
                salePackageCardProductHandlerPerformanceMapper.insertSalePackageCardProductHandlerJobTypePerformance(entityList);
            }
        }
        return BaseOutput.success();
    }
}