package com.zhelian.service.khs.employee;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.khs.JobTypeMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.khs.entity.JobTypeEntity;
import com.zhelian.model.khs.form.input.AllTypeQueryForm;
import com.zhelian.model.khs.form.input.JobTypeAddForm;
import com.zhelian.model.khs.form.input.JobTypeQueryForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class JobTypeService {

    @Autowired
    JobTypeMapper jobTypeMapper;

    public BaseOutput getJobType(JobTypeQueryForm jobTypeQueryForm) {
        PageHelper.startPage(jobTypeQueryForm.PageNum, jobTypeQueryForm.getPageSize());
        List<JobTypeEntity> jobTypeEntities = jobTypeMapper.getJobType(jobTypeQueryForm.JobTypeName, jobTypeQueryForm.Active);
        return BasePageInfo.success(jobTypeEntities);
    }

    public BaseOutput all(AllTypeQueryForm jobTypeQueryForm) {
        List<JobTypeEntity> jobTypeEntities = jobTypeMapper.getJobType(jobTypeQueryForm.JobTypeName, true);
        return BaseOutputForm.success(jobTypeEntities);
    }

    public BaseOutput addJobType(JobTypeAddForm jobTypeAddForm) {
        if (jobTypeMapper.countJobTypeByName(jobTypeAddForm.JobName) > 0)
            return BaseOutput.failed("职务已经存在");
        jobTypeMapper.addJobType(jobTypeAddForm);
        return BaseOutput.success();
    }

    public BaseOutput updateJobType(JobTypeEntity jobTypeEntity) {
        JobTypeEntity jt = jobTypeMapper.getJobTypeByID(jobTypeEntity.ID);
        if (jt == null) {
            return BaseOutput.failed("该记录不存在");
        }
        if (!jt.JobName.equals(jobTypeEntity.JobName)) {
            if (jobTypeMapper.countJobTypeByName(jobTypeEntity.JobName) > 0)
                return BaseOutput.failed("职务已经存在");
        }
        jobTypeMapper.updateJobType(jobTypeEntity);
        return BaseOutput.success();
    }
}
