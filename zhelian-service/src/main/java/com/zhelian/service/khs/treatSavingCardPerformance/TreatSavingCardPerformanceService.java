package com.zhelian.service.khs.treatSavingCardPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.TreatSavingCardPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardsCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.TreatSavingCardCommissionOutputForm;
import com.zhelian.model.khs.entity.TreatSavingCardPerformanceEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatSavingCardPerformanceService {

    @Autowired
    TreatSavingCardPerformanceMapper treatSavingCardPerformanceMapper;

    public BaseOutput allTreatSavingCardPerformance(TreatSavingCardProjectCommissionQueryForm form) {
        List<TreatSavingCardCommissionOutputForm> list = treatSavingCardPerformanceMapper.allTreatSavingCardPerformance(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatSavingCardPerformance(TreatSavingCardsCommissionAddForm form) {
        treatSavingCardPerformanceMapper.deleteTreatSavingCardPerformanceByEntityAndCategory(form.EntityID, form.CategoryID);
        List<TreatSavingCardPerformanceEntity> list = new ArrayList<>();
        if (form.SavingCard != null && form.SavingCard.size() > 0) {
            for (TreatSavingCardCommissionAddForm treatSavingCardPerformanceAddForm : form.SavingCard) {
                if (treatSavingCardPerformanceAddForm.Rate != null ||
                        treatSavingCardPerformanceAddForm.LargessRate != null) {
                    TreatSavingCardPerformanceEntity treatSavingCardPerformanceEntity = new TreatSavingCardPerformanceEntity();
                    treatSavingCardPerformanceEntity.EntityID = form.EntityID;
                    treatSavingCardPerformanceEntity.SavingCardID = treatSavingCardPerformanceAddForm.SavingCardID;
                    treatSavingCardPerformanceEntity.CardRate = treatSavingCardPerformanceAddForm.Rate;
                    treatSavingCardPerformanceEntity.CardLargessRate = treatSavingCardPerformanceAddForm.LargessRate;
                    list.add(treatSavingCardPerformanceEntity);
                }
            }
            if (list != null && list.size() > 0) {
                List<List<TreatSavingCardPerformanceEntity>> subs = Lists.partition(list, 20);
                for (List<TreatSavingCardPerformanceEntity> entityList : subs) {
                    treatSavingCardPerformanceMapper.insertTreatSavingPerformance(entityList);
                }
            }
        }
        return BaseOutput.success();
    }

}