package com.zhelian.service.khs.treatGeneralCardPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.TreatGeneralCardProjectPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatCardProjectCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardProjectsCommissionAddForm;
import com.zhelian.model.khs.entity.TreatGeneralCardProjectPerformanceEntity;
import com.zhelian.model.khs.form.output.TreatCardPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatGeneralCardProjectPerformanceService {

    @Autowired
    TreatGeneralCardProjectPerformanceMapper treatGeneralCardProjectPerformanceMapper;


    public BaseOutput allTreatGeneralCardProjectPerformance(TreatCardCommissionQueryForm form) {
        List<TreatCardPerformanceOutputForm> list = treatGeneralCardProjectPerformanceMapper.allTreatGeneralCardProjectPerformance(form.EntityID, form.CardID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatGeneralCardProjectPerformance(TreatCardProjectsCommissionAddForm form) {

        treatGeneralCardProjectPerformanceMapper.deleteTreatGeneralCardProjectPerformanceByEntityIDAndCard(form.EntityID, form.CardID);
        List<TreatGeneralCardProjectPerformanceEntity> list = new ArrayList<>();
        if (form.Project != null && form.Project.size() > 0) {
            for (TreatCardProjectCommissionAddForm treatCardPerformanceAddForm : form.Project) {
                if (treatCardPerformanceAddForm.PayRate != null ||
                        treatCardPerformanceAddForm.CardRate != null ||
                        treatCardPerformanceAddForm.CardLargessRate != null ||
                        treatCardPerformanceAddForm.LargessRate != null) {
                    TreatGeneralCardProjectPerformanceEntity treatGeneralCardPerformanceEntity = new TreatGeneralCardProjectPerformanceEntity();
                    treatGeneralCardPerformanceEntity.EntityID = form.EntityID;
                    treatGeneralCardPerformanceEntity.GeneralCardID = form.CardID;
                    treatGeneralCardPerformanceEntity.ProjectID = treatCardPerformanceAddForm.ProjectID;
                    treatGeneralCardPerformanceEntity.PayRate = treatCardPerformanceAddForm.PayRate;
                    treatGeneralCardPerformanceEntity.CardRate = treatCardPerformanceAddForm.CardRate;
                    treatGeneralCardPerformanceEntity.CardLargessRate = treatCardPerformanceAddForm.CardLargessRate;
                    treatGeneralCardPerformanceEntity.LargessRate = treatCardPerformanceAddForm.LargessRate;
                    list.add(treatGeneralCardPerformanceEntity);
                }
            }
            if (list != null && list.size() > 0) {
                List<List<TreatGeneralCardProjectPerformanceEntity>> subs = Lists.partition(list, 20);
                for (List<TreatGeneralCardProjectPerformanceEntity> entityList : subs) {
                    treatGeneralCardProjectPerformanceMapper.insertTreatGeneralCardProjectPerformance(entityList);
                }
            }
        }
        return BaseOutput.success();
    }

}