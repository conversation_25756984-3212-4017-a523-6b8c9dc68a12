package com.zhelian.service.khs.channelEmployeeSalarySettlement;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.khs.ChannelEmployeeTeamCommissionSchemeMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.khs.entity.ChannelEmployeeCommissionSchemeEntity;
import com.zhelian.model.khs.entity.ChannelEmployeeTeamCommissionEmployeeEntity;
import com.zhelian.model.khs.entity.ChannelEmployeeTeamCommissionEntity;
import com.zhelian.model.khs.entity.ChannelEmployeeTeamEmployeeEntity;
import com.zhelian.model.khs.form.input.ChannelCommissionAddForm;
import com.zhelian.model.khs.form.input.ChannelCommissionSchemeQueryForm;
import com.zhelian.model.khs.form.input.ChannelEmployeeTeamCommissionSchemeAddForm;
import com.zhelian.model.khs.form.input.EmployeeCommissionQueryForm;
import com.zhelian.model.khs.form.output.ChannelCommissionSchemeOutForm;
import com.zhelian.model.khs.form.output.EmployeeCommissionOutForm;
import com.zhelian.model.khs.form.output.TeamCalculateEmployeeOutForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class ChannelEmployeeTeamCommissionSchemeService {

    @Autowired
    ChannelEmployeeTeamCommissionSchemeMapper channelEmployeeTeamCommissionSchemeMapper;


    public BaseOutput all(ChannelCommissionSchemeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<ChannelCommissionSchemeOutForm> list = channelEmployeeTeamCommissionSchemeMapper.getChannelEmployeeTeamCommissionScheme(form.Name, form.PerformanceSchemeID, form.Active);
        return BasePageInfo.success(list);
    }

    @Transactional
    public BaseOutput create(ChannelEmployeeTeamCommissionSchemeAddForm form) {
        ChannelEmployeeCommissionSchemeEntity channelEmployeeCommissionSchemeEntity = new ChannelEmployeeCommissionSchemeEntity();
        channelEmployeeCommissionSchemeEntity.Name = form.Name;
        channelEmployeeCommissionSchemeEntity.ChannelEmployeePerformanceSchemeID = form.PerformanceSchemeID;
        channelEmployeeCommissionSchemeEntity.Calculation = form.Calculation;
        channelEmployeeTeamCommissionSchemeMapper.createChannelEmployeeTeamCommissionScheme(channelEmployeeCommissionSchemeEntity);
        updateDetail(channelEmployeeCommissionSchemeEntity.ID, form.Commission, form.CommissionEmployee, form.Employee);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput update(ChannelEmployeeTeamCommissionSchemeAddForm form) {
        ChannelEmployeeCommissionSchemeEntity channelEmployeeCommissionSchemeEntity = new ChannelEmployeeCommissionSchemeEntity();
        channelEmployeeCommissionSchemeEntity.ID = form.ID;
        channelEmployeeCommissionSchemeEntity.Name = form.Name;
        channelEmployeeCommissionSchemeEntity.ChannelEmployeePerformanceSchemeID = form.PerformanceSchemeID;
        channelEmployeeCommissionSchemeEntity.Calculation = form.Calculation;
        channelEmployeeCommissionSchemeEntity.Active = form.Active;
        channelEmployeeTeamCommissionSchemeMapper.updateChannelEmployeeTeamCommissionScheme(channelEmployeeCommissionSchemeEntity);
        channelEmployeeTeamCommissionSchemeMapper.deleteChannelEmployeeTeamCommission(form.ID);
        channelEmployeeTeamCommissionSchemeMapper.deleteChannelEmployeeTeamCommissionEmployee(form.ID);
        channelEmployeeTeamCommissionSchemeMapper.deleteChannelEmployeeTeamEmployee(form.ID);
        updateDetail(channelEmployeeCommissionSchemeEntity.ID, form.Commission, form.CommissionEmployee, form.Employee);
        return BaseOutput.success();
    }

    public void updateDetail(Integer ID, List<ChannelCommissionAddForm> Commission, List<String> CommissionEmployee, List<String> Employee) {
        List<ChannelEmployeeTeamCommissionEntity> commissionEntities = new ArrayList<>();
        List<ChannelEmployeeTeamCommissionEmployeeEntity> commissionEmployeeEntities = new ArrayList<>();
        List<ChannelEmployeeTeamEmployeeEntity> employeeEntities = new ArrayList<>();

        if (Commission != null && Commission.size() > 0) {
            Commission.stream().forEach(i -> {
                ChannelEmployeeTeamCommissionEntity channelEmployeeTeamCommissionEntity = new ChannelEmployeeTeamCommissionEntity();
                channelEmployeeTeamCommissionEntity.ChannelEmployeeTeamCommissionSchemeID = ID;
                channelEmployeeTeamCommissionEntity.BeginPerformance = i.BeginPerformance;
                channelEmployeeTeamCommissionEntity.EndPerformance = i.EndPerformance;
                channelEmployeeTeamCommissionEntity.Rate = i.Rate;
                channelEmployeeTeamCommissionEntity.Fixed = i.Fixed;
                commissionEntities.add(channelEmployeeTeamCommissionEntity);
            });
        }

        if (CommissionEmployee != null && CommissionEmployee.size() > 0) {
            CommissionEmployee.stream().forEach(i -> {
                ChannelEmployeeTeamCommissionEmployeeEntity channelEmployeeTeamCommissionEmployeeEntity = new ChannelEmployeeTeamCommissionEmployeeEntity();
                channelEmployeeTeamCommissionEmployeeEntity.ChannelEmployeeTeamCommissionSchemeID = ID;
                channelEmployeeTeamCommissionEmployeeEntity.EmployeeID = i;
                commissionEmployeeEntities.add(channelEmployeeTeamCommissionEmployeeEntity);
            });
        }

        if (Employee != null && Employee.size() > 0) {
            Employee.stream().forEach(i -> {
                ChannelEmployeeTeamEmployeeEntity channelEmployeeTeamEmployeeEntity = new ChannelEmployeeTeamEmployeeEntity();
                channelEmployeeTeamEmployeeEntity.ChannelEmployeeTeamCommissionSchemeID = ID;
                channelEmployeeTeamEmployeeEntity.EmployeeID = i;
                employeeEntities.add(channelEmployeeTeamEmployeeEntity);
            });
        }

        if (commissionEntities.size() > 0)
            channelEmployeeTeamCommissionSchemeMapper.createChannelEmployeeTeamCommission(commissionEntities);

        if (commissionEmployeeEntities.size() > 0)
            channelEmployeeTeamCommissionSchemeMapper.createChannelEmployeeTeamCommissionEmployee(commissionEmployeeEntities);

        if (employeeEntities.size() > 0)
            channelEmployeeTeamCommissionSchemeMapper.createChannelEmployeeTeamEmployee(employeeEntities);
    }

    public BaseOutput commission(EmployeeCommissionQueryForm form) {
        List<EmployeeCommissionOutForm> list = channelEmployeeTeamCommissionSchemeMapper.commission(form.ID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput commissionEmployee(EmployeeCommissionQueryForm form) {
        List<TeamCalculateEmployeeOutForm> list = channelEmployeeTeamCommissionSchemeMapper.commissionEmployee(form.ID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput employee(EmployeeCommissionQueryForm form) {
        List<TeamCalculateEmployeeOutForm> list = channelEmployeeTeamCommissionSchemeMapper.employee(form.ID);
        return BaseOutputForm.success(list);
    }
}
