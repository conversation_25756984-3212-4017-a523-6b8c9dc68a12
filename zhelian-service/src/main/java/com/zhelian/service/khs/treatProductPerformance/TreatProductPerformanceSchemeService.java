package com.zhelian.service.khs.treatProductPerformance;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.khs.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.form.input.TreatProductCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCommissionSchemeDeleteForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCommissionSchemeQueryForm;
import com.zhelian.model.ibeauty.form.output.TreatProductCommissionSchemeOutputForm;
import com.zhelian.model.khs.entity.TreatProductPerformanceSchemeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class TreatProductPerformanceSchemeService {

    @Autowired
    TreatProductPerformanceSchemeMapper treatProductPerformanceSchemeMapper;
    @Autowired
    TreatProductCategoryPerformanceMapper treatProductCategoryPerformanceMapper;
    @Autowired
    TreatProductCategoryHandlerPerformanceMapper treatProductCategoryHandlerPerformanceMapper;
    @Autowired
    TreatProductPerformanceMapper treatProductPerformanceMapper;
    @Autowired
    TreatProductHandlerPerformanceMapper treatProductHandlerPerformanceMapper;
    @Autowired
    TreatProductSchemeHandlerPerformanceMapper treatProductSchemeHandlerPerformanceMapper;

    public BaseOutput treatProductPerformanceSchemeList(TreatProductCommissionSchemeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<TreatProductCommissionSchemeOutputForm> list = treatProductPerformanceSchemeMapper.treatProductPerformanceSchemeList(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput createTreatProductPerformanceScheme(TreatProductCommissionSchemeAddForm form) {
        Integer count = treatProductPerformanceSchemeMapper.getTreatProductPerformanceScheme(form.EntityID);
        if (count > 0)
            return BaseOutput.failed("组织单位已经存在");
        TreatProductPerformanceSchemeEntity treatProductPerformanceSchemeEntity = new TreatProductPerformanceSchemeEntity();
        treatProductPerformanceSchemeEntity.EntityID = form.EntityID;
        treatProductPerformanceSchemeMapper.createTreatProductPerformanceScheme(treatProductPerformanceSchemeEntity);
        return BaseOutput.success();

    }

    public BaseOutput deleteTreatProductPerformanceScheme(TreatProductCommissionSchemeDeleteForm form) {
        treatProductPerformanceSchemeMapper.deleteTreatProductPerformanceScheme(form.EntityID);
        treatProductCategoryPerformanceMapper.deleteTreatProductCategoryPerformanceEntity(form.EntityID);
        treatProductCategoryHandlerPerformanceMapper.deleteTreatProductCategoryHandlerPerformanceByEntity(form.EntityID);
        treatProductCategoryHandlerPerformanceMapper.deleteTreatProductCategoryHandlerJobTypePerformanceByEntity(form.EntityID);
        treatProductPerformanceMapper.deleteTreatProductPerformanceByEntity(form.EntityID);
        treatProductHandlerPerformanceMapper.deleteTreatProductHandlerPerformanceByEntity(form.EntityID);
        treatProductHandlerPerformanceMapper.deleteTreatProductHandlerJobTypePerformanceByEntity(form.EntityID);
        treatProductSchemeHandlerPerformanceMapper.deleteTreatProductSchemeHandler(form.EntityID);
        treatProductSchemeHandlerPerformanceMapper.deleteTreatProductSchemeHandlerJobType(form.EntityID);
        return BaseOutput.success();
    }
}