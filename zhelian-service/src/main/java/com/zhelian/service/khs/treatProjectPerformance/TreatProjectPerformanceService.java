package com.zhelian.service.khs.treatProjectPerformance;


import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.TreatProjectPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectsCommissionAddForm;
import com.zhelian.model.khs.entity.TBTreatProjectPerformanceEntity;
import com.zhelian.model.khs.form.output.TreatProjectPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatProjectPerformanceService {

    @Autowired
    TreatProjectPerformanceMapper treatProjectPerformanceMapper;

    public BaseOutput allTreatProjectPerformance(TreatProjectCommissionQueryForm form) {
        List<TreatProjectPerformanceOutputForm> list = treatProjectPerformanceMapper.allTreatProjectPerformance(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatProjectPerformance(TreatProjectsCommissionAddForm form) {

        treatProjectPerformanceMapper.deleteTreatProjectPerformanceByEntityAndCategory(form.EntityID, form.CategoryID);
        List<TBTreatProjectPerformanceEntity> list = new ArrayList<>();
        if (form.Project != null && form.Project.size() > 0) {
            for (TreatProjectCommissionAddForm treatProjectPerformanceAddForm : form.Project) {
                if (treatProjectPerformanceAddForm.PayRate != null ||
                        treatProjectPerformanceAddForm.CardRate != null ||
                        treatProjectPerformanceAddForm.CardLargessRate != null ||
                        treatProjectPerformanceAddForm.LargessRate != null) {
                    TBTreatProjectPerformanceEntity treatProjectPerformanceEntity = new TBTreatProjectPerformanceEntity();
                    treatProjectPerformanceEntity.EntityID = form.EntityID;
                    treatProjectPerformanceEntity.ProjectID = treatProjectPerformanceAddForm.ProjectID;
                    treatProjectPerformanceEntity.PayRate = treatProjectPerformanceAddForm.PayRate;
                    treatProjectPerformanceEntity.CardRate = treatProjectPerformanceAddForm.CardRate;
                    treatProjectPerformanceEntity.CardLargessRate = treatProjectPerformanceAddForm.CardLargessRate;
                    treatProjectPerformanceEntity.LargessRate = treatProjectPerformanceAddForm.LargessRate;
                    list.add(treatProjectPerformanceEntity);
                }
            }
            if (list != null && list.size() > 0) {
                List<List<TBTreatProjectPerformanceEntity>> subs = Lists.partition(list, 20);
                for (List<TBTreatProjectPerformanceEntity> entityList : subs) {
                    treatProjectPerformanceMapper.insertTreatProjectPerformance(entityList);
                }
            }
        }
        return BaseOutput.success();
    }

}