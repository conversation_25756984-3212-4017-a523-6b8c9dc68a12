package com.zhelian.service.khs.channelSalarySettlement;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.khs.ChannelCommissionSchemeMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.khs.entity.ChannelCommissionEntity;
import com.zhelian.model.khs.entity.ChannelCommissionRangeEntity;
import com.zhelian.model.khs.entity.ChannelCommissionSchemeEntity;
import com.zhelian.model.khs.form.input.ChannelCommissionAddForm;
import com.zhelian.model.khs.form.input.ChannelCommissionSchemeAddForm;
import com.zhelian.model.khs.form.input.ChannelCommissionSchemeQueryForm;
import com.zhelian.model.khs.form.input.EmployeeCommissionQueryForm;
import com.zhelian.model.khs.form.output.ChannelCommissionSchemeOutForm;
import com.zhelian.model.khs.form.output.EmployeeCommissionOutForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class ChannelCommissionSchemeService {

    @Autowired
    ChannelCommissionSchemeMapper channelCommissionSchemeMapper;


    public BaseOutput all(ChannelCommissionSchemeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<ChannelCommissionSchemeOutForm> list = channelCommissionSchemeMapper.getChannelCommissionScheme(form.Name, form.PerformanceSchemeID, form.Active);
        return BasePageInfo.success(list);
    }

    @Transactional
    public BaseOutput create(ChannelCommissionSchemeAddForm form) {
        ChannelCommissionSchemeEntity channelCommissionSchemeEntity = new ChannelCommissionSchemeEntity();
        channelCommissionSchemeEntity.Name = form.Name;
        channelCommissionSchemeEntity.ChannelPerformanceSchemeID = form.PerformanceSchemeID;
        channelCommissionSchemeEntity.Calculation = form.Calculation;
        channelCommissionSchemeMapper.createChannelCommissionScheme(channelCommissionSchemeEntity);
        updateDetail(channelCommissionSchemeEntity.ID, form.Commission, form.JobType);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput update(ChannelCommissionSchemeAddForm form) {
        ChannelCommissionSchemeEntity channelCommissionSchemeEntity = new ChannelCommissionSchemeEntity();
        channelCommissionSchemeEntity.ID = form.ID;
        channelCommissionSchemeEntity.Name = form.Name;
        channelCommissionSchemeEntity.ChannelPerformanceSchemeID = form.PerformanceSchemeID;
        channelCommissionSchemeEntity.Calculation = form.Calculation;
        channelCommissionSchemeEntity.Active = form.Active;
        channelCommissionSchemeMapper.updateChannelCommissionScheme(channelCommissionSchemeEntity);
        channelCommissionSchemeMapper.deleteChannelCommission(form.ID);
        channelCommissionSchemeMapper.deleteChannelCommissionEntity(form.ID);

        updateDetail(channelCommissionSchemeEntity.ID, form.Commission, form.JobType);
        return BaseOutput.success();
    }

    public void updateDetail(Integer ID, List<ChannelCommissionAddForm> Commission, Integer[] Range) {
        List<ChannelCommissionEntity> commissionEntities = new ArrayList<>();
        List<ChannelCommissionRangeEntity> entityEntities = new ArrayList<>();

        if (Commission != null && Commission.size() > 0) {
            for (ChannelCommissionAddForm ChannelCommissionAddForm : Commission) {
                ChannelCommissionEntity ChannelCommissionEntity = new ChannelCommissionEntity();
                ChannelCommissionEntity.ChannelCommissionSchemeID = ID;
                ChannelCommissionEntity.BeginPerformance = ChannelCommissionAddForm.BeginPerformance;
                ChannelCommissionEntity.EndPerformance = ChannelCommissionAddForm.EndPerformance;
                ChannelCommissionEntity.Rate = ChannelCommissionAddForm.Rate;
                ChannelCommissionEntity.Fixed = ChannelCommissionAddForm.Fixed;
                commissionEntities.add(ChannelCommissionEntity);
            }
        }

        if (Range != null && Range.length > 0) {
            for (Integer i : Range) {
                ChannelCommissionRangeEntity ChannelCommissionEntity = new ChannelCommissionRangeEntity();
                ChannelCommissionEntity.ChannelCommissionSchemeID = ID;
                ChannelCommissionEntity.ChannelID = i;
                entityEntities.add(ChannelCommissionEntity);
            }
        }

        if (commissionEntities.size() > 0)
            channelCommissionSchemeMapper.createChannelCommission(commissionEntities);

        if (entityEntities.size() > 0)
            channelCommissionSchemeMapper.createChannelCommissionEntities(entityEntities);

    }

    public BaseOutput commission(EmployeeCommissionQueryForm form) {
        List<EmployeeCommissionOutForm> list = channelCommissionSchemeMapper.getChannelCommission(form.ID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput range(EmployeeCommissionQueryForm form) {
        Integer[] list = channelCommissionSchemeMapper.getChannelCommissionRange(form.ID);
        return BaseOutputForm.success(list);
    }


}
