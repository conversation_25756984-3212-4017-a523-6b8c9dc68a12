package com.zhelian.service.khs.saleProjectPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.SalePackageCardProjectCategoryHandlerPerformanceMapper;
import com.zhelian.mapper.khs.SaleProjectCategoryHandlerPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.khs.entity.SalePackageCardProjectCategoryHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SalePackageCardProjectCategoryHandlerPerformanceEntity;
import com.zhelian.model.khs.entity.SaleProjectCategoryHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SaleProjectCategoryHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleGoodCategoryHandlerPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleProjectCategoryHandlerPerformanceService {

    @Autowired
    SaleProjectCategoryHandlerPerformanceMapper saleProjectCategoryHandlerPerformanceMapper;
    @Autowired
    SalePackageCardProjectCategoryHandlerPerformanceMapper salePackageCardProjectCategoryHandlerPerformanceMapper;


    public BaseOutput allSaleProjectCategoryHandlerPerformance(SaleProjectCategoryHandlerCommissionQueryForm form) {
        List<SaleGoodCategoryHandlerPerformanceOutputForm> list = saleProjectCategoryHandlerPerformanceMapper.allSaleProjectCategoryHandlerPerformance(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSalePakageCardProjectCategoryHandlerPerformance(SaleProjectCategoryHandlerCommissionQueryForm form) {
        List<SaleGoodCategoryHandlerPerformanceOutputForm> list = salePackageCardProjectCategoryHandlerPerformanceMapper.allSalePakageCardProjectCategoryHandlerPerformance(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleProjectCategoryHandlerPerformance(SaleProjectCategoryHandlerCommissionAddForm form) {

        saleProjectCategoryHandlerPerformanceMapper.deleteSaleProjectCategoryHandler(form.EntityID, form.CategoryID);
        saleProjectCategoryHandlerPerformanceMapper.deleteSaleProjectCategoryHandlerJobType(form.EntityID, form.CategoryID);

        List<SaleProjectCategoryHandlerPerformanceEntity> listSaleProjectCategoryHandlerPerformance = new ArrayList<>();
        List<SaleProjectCategoryHandlerJobTypePerformanceEntity> listSaleProjectCategoryHandlerJobTypePerformance = new ArrayList<>();
        if (form.ProjectHandler != null && form.ProjectHandler.size() > 0) {
            for (SaleProjectCategoryHandlerAddForm saleProjectCategoryHandlerAddForm : form.ProjectHandler) {
                if (saleProjectCategoryHandlerAddForm.PayRate != null || saleProjectCategoryHandlerAddForm.PayFixed != null ||
                        saleProjectCategoryHandlerAddForm.SavingCardRate != null || saleProjectCategoryHandlerAddForm.SavingCardFixed != null ||
                        saleProjectCategoryHandlerAddForm.SavingCardLargessRate != null || saleProjectCategoryHandlerAddForm.SavingCardLargessFixed != null ||
                        saleProjectCategoryHandlerAddForm.SpecialBenefit != null) {
                    SaleProjectCategoryHandlerPerformanceEntity saleProjectCategoryHandlerPerformanceEntity = new SaleProjectCategoryHandlerPerformanceEntity();
                    saleProjectCategoryHandlerPerformanceEntity.EntityID = form.EntityID;
                    saleProjectCategoryHandlerPerformanceEntity.ProjectCategoryID = form.CategoryID;
                    saleProjectCategoryHandlerPerformanceEntity.ProjectSaleHandlerID = saleProjectCategoryHandlerAddForm.SaleHandlerID;
                    saleProjectCategoryHandlerPerformanceEntity.PayRate = saleProjectCategoryHandlerAddForm.PayRate;
                    saleProjectCategoryHandlerPerformanceEntity.SavingCardRate = saleProjectCategoryHandlerAddForm.SavingCardRate;
                    saleProjectCategoryHandlerPerformanceEntity.SavingCardLargessRate = saleProjectCategoryHandlerAddForm.SavingCardLargessRate;
                    listSaleProjectCategoryHandlerPerformance.add(saleProjectCategoryHandlerPerformanceEntity);
                }

                if (saleProjectCategoryHandlerAddForm.HandlerJobTypeCommission != null && saleProjectCategoryHandlerAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (SaleProjectCategoryHandlerJobTypeCommissionAddForm saleProjectCategoryHandlerJobTypePerformanceAddForm : saleProjectCategoryHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleProjectCategoryHandlerJobTypePerformanceAddForm.PayRate != null ||
                                saleProjectCategoryHandlerJobTypePerformanceAddForm.SavingCardRate != null ||
                                saleProjectCategoryHandlerJobTypePerformanceAddForm.SavingCardLargessRate != null) {
                            SaleProjectCategoryHandlerJobTypePerformanceEntity saleProjectCategoryHandlerJobTypePerformanceEntity = new SaleProjectCategoryHandlerJobTypePerformanceEntity();
                            saleProjectCategoryHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            saleProjectCategoryHandlerJobTypePerformanceEntity.ProjectCategoryID = form.CategoryID;
                            saleProjectCategoryHandlerJobTypePerformanceEntity.ProjectSaleHandlerID = saleProjectCategoryHandlerAddForm.SaleHandlerID;
                            saleProjectCategoryHandlerJobTypePerformanceEntity.JobTypeID = saleProjectCategoryHandlerJobTypePerformanceAddForm.JobTypeID;
                            saleProjectCategoryHandlerJobTypePerformanceEntity.PayRate = saleProjectCategoryHandlerJobTypePerformanceAddForm.PayRate;
                            saleProjectCategoryHandlerJobTypePerformanceEntity.SavingCardRate = saleProjectCategoryHandlerJobTypePerformanceAddForm.SavingCardRate;
                            saleProjectCategoryHandlerJobTypePerformanceEntity.SavingCardLargessRate = saleProjectCategoryHandlerJobTypePerformanceAddForm.SavingCardLargessRate;
                            listSaleProjectCategoryHandlerJobTypePerformance.add(saleProjectCategoryHandlerJobTypePerformanceEntity);
                        }
                    }
                }
            }
        }

        salePackageCardProjectCategoryHandlerPerformanceMapper.deleteSalePackageCardProjectCategoryHandler(form.EntityID, form.CategoryID);
        salePackageCardProjectCategoryHandlerPerformanceMapper.deleteSalePackageCardProjectCategoryHandlerJobType(form.EntityID, form.CategoryID);

        List<SalePackageCardProjectCategoryHandlerPerformanceEntity> listSalePackageCardProjectCategoryHandlerPerformance = new ArrayList<>();
        List<SalePackageCardProjectCategoryHandlerJobTypePerformanceEntity> listSalePackageCardProjectCategoryHandlerJobTypePerformance = new ArrayList<>();
        if (form.ProjectHandler != null && form.ProjectHandler.size() > 0) {
            for (SaleProjectCategoryHandlerAddForm saleProjectCategoryHandlerAddForm : form.PackageCardProjectHandler) {
                if (saleProjectCategoryHandlerAddForm.PayRate != null || saleProjectCategoryHandlerAddForm.PayFixed != null ||
                        saleProjectCategoryHandlerAddForm.SavingCardRate != null || saleProjectCategoryHandlerAddForm.SavingCardFixed != null ||
                        saleProjectCategoryHandlerAddForm.SavingCardLargessRate != null || saleProjectCategoryHandlerAddForm.SavingCardLargessFixed != null ||
                        saleProjectCategoryHandlerAddForm.SpecialBenefit != null) {
                    SalePackageCardProjectCategoryHandlerPerformanceEntity salePackageCardProjectCategoryHandlerPerformanceEntity = new SalePackageCardProjectCategoryHandlerPerformanceEntity();
                    salePackageCardProjectCategoryHandlerPerformanceEntity.EntityID = form.EntityID;
                    salePackageCardProjectCategoryHandlerPerformanceEntity.ProjectCategoryID = form.CategoryID;
                    salePackageCardProjectCategoryHandlerPerformanceEntity.PackageCardSaleHandlerID = saleProjectCategoryHandlerAddForm.SaleHandlerID;
                    salePackageCardProjectCategoryHandlerPerformanceEntity.PayRate = saleProjectCategoryHandlerAddForm.PayRate;
                    salePackageCardProjectCategoryHandlerPerformanceEntity.SavingCardRate = saleProjectCategoryHandlerAddForm.SavingCardRate;
                    salePackageCardProjectCategoryHandlerPerformanceEntity.SavingCardLargessRate = saleProjectCategoryHandlerAddForm.SavingCardLargessRate;
                    listSalePackageCardProjectCategoryHandlerPerformance.add(salePackageCardProjectCategoryHandlerPerformanceEntity);
                }

                if (saleProjectCategoryHandlerAddForm.HandlerJobTypeCommission != null && saleProjectCategoryHandlerAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (SaleProjectCategoryHandlerJobTypeCommissionAddForm saleProjectCategoryHandlerJobTypePerformanceAddForm : saleProjectCategoryHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleProjectCategoryHandlerJobTypePerformanceAddForm.PayRate != null ||
                                saleProjectCategoryHandlerJobTypePerformanceAddForm.SavingCardRate != null ||
                                saleProjectCategoryHandlerJobTypePerformanceAddForm.SavingCardLargessRate != null) {
                            SalePackageCardProjectCategoryHandlerJobTypePerformanceEntity salePackageCardProjectCategoryHandlerJobTypePerformanceEntity = new SalePackageCardProjectCategoryHandlerJobTypePerformanceEntity();
                            salePackageCardProjectCategoryHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            salePackageCardProjectCategoryHandlerJobTypePerformanceEntity.ProjectCategoryID = form.CategoryID;
                            salePackageCardProjectCategoryHandlerJobTypePerformanceEntity.PackageCardSaleHandlerID = saleProjectCategoryHandlerAddForm.SaleHandlerID;
                            salePackageCardProjectCategoryHandlerJobTypePerformanceEntity.JobTypeID = saleProjectCategoryHandlerJobTypePerformanceAddForm.JobTypeID;
                            salePackageCardProjectCategoryHandlerJobTypePerformanceEntity.PayRate = saleProjectCategoryHandlerJobTypePerformanceAddForm.PayRate;
                            salePackageCardProjectCategoryHandlerJobTypePerformanceEntity.SavingCardRate = saleProjectCategoryHandlerJobTypePerformanceAddForm.SavingCardRate;
                            salePackageCardProjectCategoryHandlerJobTypePerformanceEntity.SavingCardLargessRate = saleProjectCategoryHandlerJobTypePerformanceAddForm.SavingCardLargessRate;
                            listSalePackageCardProjectCategoryHandlerJobTypePerformance.add(salePackageCardProjectCategoryHandlerJobTypePerformanceEntity);
                        }
                    }
                }
            }
        }
        if (listSaleProjectCategoryHandlerPerformance != null && listSaleProjectCategoryHandlerPerformance.size() > 0) {
            List<List<SaleProjectCategoryHandlerPerformanceEntity>> subs = Lists.partition(listSaleProjectCategoryHandlerPerformance, 20);
            for (List<SaleProjectCategoryHandlerPerformanceEntity> entityList : subs) {
                saleProjectCategoryHandlerPerformanceMapper.updateSaleProjectCategoryHandlerPerformance(entityList);
            }
        }

        if (listSaleProjectCategoryHandlerJobTypePerformance != null && listSaleProjectCategoryHandlerJobTypePerformance.size() > 0) {
            List<List<SaleProjectCategoryHandlerJobTypePerformanceEntity>> subs = Lists.partition(listSaleProjectCategoryHandlerJobTypePerformance, 20);
            for (List<SaleProjectCategoryHandlerJobTypePerformanceEntity> entityList : subs) {
                saleProjectCategoryHandlerPerformanceMapper.insertSaleProjectCategoryHandlerJobTypePerformance(entityList);
            }
        }
        if (listSalePackageCardProjectCategoryHandlerPerformance != null && listSalePackageCardProjectCategoryHandlerPerformance.size() > 0) {
            List<List<SalePackageCardProjectCategoryHandlerPerformanceEntity>> subs = Lists.partition(listSalePackageCardProjectCategoryHandlerPerformance, 20);
            for (List<SalePackageCardProjectCategoryHandlerPerformanceEntity> entityList : subs) {
                salePackageCardProjectCategoryHandlerPerformanceMapper.updateSalePackageCardProjectCategoryHandlerPerformance(entityList);
            }
        }
        if (listSalePackageCardProjectCategoryHandlerJobTypePerformance != null && listSalePackageCardProjectCategoryHandlerJobTypePerformance.size() > 0) {
            List<List<SalePackageCardProjectCategoryHandlerJobTypePerformanceEntity>> subs = Lists.partition(listSalePackageCardProjectCategoryHandlerJobTypePerformance, 20);
            for (List<SalePackageCardProjectCategoryHandlerJobTypePerformanceEntity> entityList : subs) {
                salePackageCardProjectCategoryHandlerPerformanceMapper.insertSalePackageCardProjectCategoryHandlerJobTypePerformance(entityList);
            }
        }


        return BaseOutput.success();
    }
}