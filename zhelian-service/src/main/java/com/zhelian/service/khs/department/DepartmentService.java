package com.zhelian.service.khs.department;

import com.zhelian.core.utils.ListUtil;
import com.zhelian.mapper.khs.DepartmentMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.khs.entity.DepartmentEntity;
import com.zhelian.model.khs.entity.DepartmentEntityEntity;
import com.zhelian.model.khs.form.input.DepartmentAddForm;
import com.zhelian.model.khs.form.input.DepartmentMoveForm;
import com.zhelian.model.khs.form.input.DepartmentQueryForm;
import com.zhelian.model.khs.form.input.DepartmentUpdateForm;
import com.zhelian.model.khs.form.output.DepartmentOutForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

@Service
public class DepartmentService {

    @Autowired
    DepartmentMapper departmentMapper;

    public BaseOutput all(DepartmentQueryForm form) {
        List<DepartmentOutForm> outForm = departmentMapper.all(form.Name,form.Active);
        return BaseOutputForm.success(outForm);
    }

    @Transactional
    public BaseOutput create(DepartmentAddForm form) {
        Integer Sequence = departmentMapper.getMaxSequence();
        DepartmentEntity entity = new DepartmentEntity();
        entity.Name = form.Name;
        entity.Active = true;
        entity.Sequence = Sequence != null ? Sequence + 1 : 1;
        departmentMapper.create(entity);

        if (form.Entity != null && form.Entity.length > 0) {
            List<DepartmentEntityEntity> list = new ArrayList<>();
            for (Integer EntityID : form.Entity) {
                DepartmentEntityEntity departmentEntityEntity = new DepartmentEntityEntity();
                departmentEntityEntity.DepartmentID = entity.ID;
                departmentEntityEntity.EntityID = EntityID;
                list.add(departmentEntityEntity);
            }
            departmentMapper.saveEntity(list);
        }

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput update(DepartmentUpdateForm form) {
        DepartmentEntity entity = new DepartmentEntity();
        entity.ID = form.ID;
        entity.Name = form.Name;
        entity.Active = form.Active;
        departmentMapper.update(entity);

        departmentMapper.deleteEntity(form.ID);
        if (form.Entity != null && form.Entity.length > 0) {
            List<DepartmentEntityEntity> list = new ArrayList<>();
            for (Integer EntityID : form.Entity) {
                DepartmentEntityEntity departmentEntityEntity = new DepartmentEntityEntity();
                departmentEntityEntity.DepartmentID = entity.ID;
                departmentEntityEntity.EntityID = EntityID;
                list.add(departmentEntityEntity);
            }
            departmentMapper.saveEntity(list);
        }
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput move(DepartmentMoveForm form) {
        LinkedList<Integer> linkedList = departmentMapper.getASCList(form.MoveID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<DepartmentEntity> list = new ArrayList<>();
        for (Integer ID : linkedList) {
            DepartmentEntity entity = new DepartmentEntity();
            entity.ID = ID;
            entity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(entity);
        }

        departmentMapper.updateSequence(list);
        return BaseOutput.success();
    }
}
