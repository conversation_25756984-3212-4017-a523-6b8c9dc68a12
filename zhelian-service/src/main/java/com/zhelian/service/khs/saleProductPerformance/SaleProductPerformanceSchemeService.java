package com.zhelian.service.khs.saleProductPerformance;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.khs.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionSchemeDeleteForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionSchemeQueryForm;
import com.zhelian.model.ibeauty.form.output.SaleProductCommissionSchemeOutputForm;
import com.zhelian.model.khs.entity.SaleProductPerformanceSchemeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class SaleProductPerformanceSchemeService {

    @Autowired
    SaleProductPerformanceSchemeMapper saleProductPerformanceSchemeMapper;
    @Autowired
    SaleProductCategoryPerformanceMapper saleProductCategoryPerformanceMapper;
    @Autowired
    SaleProductCategoryHandlerPerformanceMapper saleProductCategoryHandlerPerformanceMapper;
    @Autowired
    SalePackageCardProductCategoryHandlerPerformanceMapper salePackageCardProductCategoryHandlerPerformanceMapper;
    @Autowired
    SaleProductPerformanceMapper saleProductPerformanceMapper;
    @Autowired
    SaleProductHandlerPerformanceMapper saleProductHandlerPerformanceMapper;
    @Autowired
    SalePackageCardProductHandlerPerformanceMapper salePackageCardProductHandlerPerformanceMapper;
    @Autowired
    SaleProductSchemeHandlerPerformanceMapper saleProductSchemeHandlerPerformanceMapper;
    @Autowired
    SalePackageCardProductSchemeHandlerPerformanceMapper salePackageCardProductSchemeHandlerPerformanceMapper;

    public BaseOutput saleProductPerformanceSchemeList(SaleProductCommissionSchemeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleProductCommissionSchemeOutputForm> list = saleProductPerformanceSchemeMapper.saleProductPerformanceSchemeList(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput createSaleProductPerformanceScheme(SaleProductCommissionSchemeAddForm form) {
        Integer count = saleProductPerformanceSchemeMapper.getSaleProductPerformanceScheme(form.EntityID);
        if (count > 0)
            return BaseOutput.failed("组织单位已经存在");

        SaleProductPerformanceSchemeEntity saleProductPerformanceSchemeEntity = new SaleProductPerformanceSchemeEntity();
        saleProductPerformanceSchemeEntity.EntityID = form.EntityID;

        saleProductPerformanceSchemeMapper.createSaleProductPerformanceScheme(saleProductPerformanceSchemeEntity);
        return BaseOutput.success();

    }

    public BaseOutput deleteSaleProductPerformanceScheme(SaleProductCommissionSchemeDeleteForm form) {
        saleProductPerformanceSchemeMapper.deleteSaleProductPerformanceScheme(form.EntityID);
        saleProductCategoryPerformanceMapper.deleteSaleProductCategoryPerformanceEntity(form.EntityID);
        saleProductCategoryHandlerPerformanceMapper.deleteSaleProductCategoryHandlerPerformanceByEntityID(form.EntityID);
        saleProductCategoryHandlerPerformanceMapper.deleteSaleProductCategoryHandlerJobTypePerformanceByEntityID(form.EntityID);
        saleProductPerformanceMapper.deleteSaleProductPerformanceByEntityID(form.EntityID);
        saleProductHandlerPerformanceMapper.deleteSaleProductHandlerPerformanceByEntityID(form.EntityID);
        saleProductHandlerPerformanceMapper.deleteSaleProductHandlerJobTypePerformanceByEntityID(form.EntityID);
        saleProductSchemeHandlerPerformanceMapper.deleteSaleProductSchemeHandler(form.EntityID);
        saleProductSchemeHandlerPerformanceMapper.deleteSaleProductSchemeHandlerJobType(form.EntityID);
        return BaseOutput.success();
    }
}