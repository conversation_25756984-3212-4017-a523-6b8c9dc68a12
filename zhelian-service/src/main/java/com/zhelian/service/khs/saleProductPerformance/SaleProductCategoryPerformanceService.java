package com.zhelian.service.khs.saleProductPerformance;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.SaleProductCategoryPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductChildCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionSchemeAddForm;
import com.zhelian.model.khs.entity.SaleProductPerformanceSchemeEntity;
import com.zhelian.model.khs.entity.TBSaleProductCategoryPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleGoodCategoryPerformanceOutputForm;
import com.zhelian.model.khs.form.output.SaleGoodCategorySchemeOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import static com.zhelian.core.utils.ListUtil.listToTree;


@Service
public class SaleProductCategoryPerformanceService {

    @Autowired
    SaleProductCategoryPerformanceMapper saleProductCategoryPerformanceMapper;


    public BaseOutput allSaleProductCategoryPerformance(SaleProductCommissionSchemeAddForm form) {
        SaleGoodCategorySchemeOutputForm saleProductCategoryOutputForm = saleProductCategoryPerformanceMapper.saleProductCategoryPerformance(form.EntityID);
        List<SaleGoodCategoryPerformanceOutputForm> all = saleProductCategoryPerformanceMapper.allSaleProductCategoryPerformance(form.EntityID);
        JSONArray result = listToTree(JSONArray.parseArray(JSON.toJSONString(all, JSONWriter.Feature.WriteMapNullValue)), "CategoryID", "ParentID", "Child");
        saleProductCategoryOutputForm.Category = result;
        return BaseOutputForm.success(saleProductCategoryOutputForm);
    }

    @Transactional
    public BaseOutput updateSaleProductCategoryPerformance(SaleProductCategoryAddForm form) {
        SaleProductPerformanceSchemeEntity saleProductPerformanceSchemeEntity = new SaleProductPerformanceSchemeEntity();
        saleProductPerformanceSchemeEntity.EntityID = form.EntityID;
        saleProductPerformanceSchemeEntity.PayRate = form.PayRate;
        saleProductPerformanceSchemeEntity.SavingCardRate = form.SavingCardRate;
        saleProductPerformanceSchemeEntity.SavingCardLargessRate = form.SavingCardLargessRate;

        saleProductCategoryPerformanceMapper.updateSaleProductPerformanceScheme(saleProductPerformanceSchemeEntity);

        saleProductCategoryPerformanceMapper.deleteSaleProductCategoryPerformanceEntity(form.EntityID);

        List<TBSaleProductCategoryPerformanceEntity> list = new ArrayList<>();
        if (form.Category != null && form.Category.size() > 0) {
            for (SaleProductCategoryCommissionAddForm saleProductCategoryPerformanceAddForm : form.Category) {
                if (saleProductCategoryPerformanceAddForm.PayRate != null ||
                        saleProductCategoryPerformanceAddForm.SavingCardRate != null ||
                        saleProductCategoryPerformanceAddForm.SavingCardLargessRate != null) {
                    TBSaleProductCategoryPerformanceEntity saleProductCategoryPerformanceEntity = new TBSaleProductCategoryPerformanceEntity();
                    saleProductCategoryPerformanceEntity.EntityID = form.EntityID;
                    saleProductCategoryPerformanceEntity.ProductCategoryID = saleProductCategoryPerformanceAddForm.CategoryID;
                    saleProductCategoryPerformanceEntity.PayRate = saleProductCategoryPerformanceAddForm.PayRate;
                    saleProductCategoryPerformanceEntity.SavingCardRate = saleProductCategoryPerformanceAddForm.SavingCardRate;
                    saleProductCategoryPerformanceEntity.SavingCardLargessRate = saleProductCategoryPerformanceAddForm.SavingCardLargessRate;
                    list.add(saleProductCategoryPerformanceEntity);
                }
                if (saleProductCategoryPerformanceAddForm.Child != null && saleProductCategoryPerformanceAddForm.Child.size() > 0) {

                    for (SaleProductChildCategoryCommissionAddForm saleProductChildCategoryPerformanceAddForm : saleProductCategoryPerformanceAddForm.Child) {
                        if (saleProductChildCategoryPerformanceAddForm.PayRate != null ||
                                saleProductChildCategoryPerformanceAddForm.SavingCardRate != null ||
                                saleProductChildCategoryPerformanceAddForm.SavingCardLargessRate != null) {
                            TBSaleProductCategoryPerformanceEntity saleProductCategoryPerformanceEntity2 = new TBSaleProductCategoryPerformanceEntity();
                            saleProductCategoryPerformanceEntity2.EntityID = form.EntityID;
                            saleProductCategoryPerformanceEntity2.ProductCategoryID = saleProductChildCategoryPerformanceAddForm.CategoryID;
                            saleProductCategoryPerformanceEntity2.PayRate = saleProductChildCategoryPerformanceAddForm.PayRate;
                            saleProductCategoryPerformanceEntity2.SavingCardRate = saleProductChildCategoryPerformanceAddForm.SavingCardRate;
                            saleProductCategoryPerformanceEntity2.SavingCardLargessRate = saleProductChildCategoryPerformanceAddForm.SavingCardLargessRate;
                            list.add(saleProductCategoryPerformanceEntity2);
                        }
                    }
                }
            }
        }
        if (list != null && list.size() > 0) {
            List<List<TBSaleProductCategoryPerformanceEntity>> subs = Lists.partition(list, 20);
            for (List<TBSaleProductCategoryPerformanceEntity> entityList : subs) {
                saleProductCategoryPerformanceMapper.insertSaleProductCategoryPerformanceEntity(entityList);
            }
        }

        return BaseOutput.success();
    }

}