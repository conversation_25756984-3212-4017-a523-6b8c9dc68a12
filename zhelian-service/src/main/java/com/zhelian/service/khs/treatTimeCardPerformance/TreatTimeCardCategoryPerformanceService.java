package com.zhelian.service.khs.treatTimeCardPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.TreatTimeCardCategoryPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCategoryCommissionQueryForm;
import com.zhelian.model.khs.entity.TreatTimeCardCategoryPerformanceEntity;
import com.zhelian.model.khs.entity.TreatTimeCardPerformanceSchemeEntity;
import com.zhelian.model.khs.form.output.TreatCardGoodCategoryOutputForm;
import com.zhelian.model.khs.form.output.TreatGoodCategoryPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatTimeCardCategoryPerformanceService {

    @Autowired
    TreatTimeCardCategoryPerformanceMapper treatTimeCardCategoryPerformanceMapper;


    public BaseOutput alltreatTimeCardCategoryPerformance(TreatSavingCardProjectCategoryCommissionQueryForm form) {
        TreatCardGoodCategoryOutputForm saleGeneralCardCategoryOutputForm = treatTimeCardCategoryPerformanceMapper.treatTimeCardProjectCategoryPerformance(form.EntityID);
        List<TreatGoodCategoryPerformanceOutputForm> all = treatTimeCardCategoryPerformanceMapper.allSaleTimeCardCategoryPerformance(form.EntityID);
        saleGeneralCardCategoryOutputForm.Category = all;
        return BaseOutputForm.success(saleGeneralCardCategoryOutputForm);
    }

    @Transactional
    public BaseOutput updateTreatTimeCardCategoryPerformance(TreatCardCategoryAddForm form) {
        TreatTimeCardPerformanceSchemeEntity treatTimeCardProjectPerformanceSchemeEntity = new TreatTimeCardPerformanceSchemeEntity();
        treatTimeCardProjectPerformanceSchemeEntity.EntityID = form.EntityID;
        treatTimeCardProjectPerformanceSchemeEntity.PayRate = form.PayRate;
        treatTimeCardProjectPerformanceSchemeEntity.CardRate = form.CardRate;
        treatTimeCardProjectPerformanceSchemeEntity.CardLargessRate = form.CardLargessRate;
        treatTimeCardProjectPerformanceSchemeEntity.LargessRate = form.LargessRate;

        treatTimeCardCategoryPerformanceMapper.updateTreatTimeCardPerformanceScheme(treatTimeCardProjectPerformanceSchemeEntity);
        treatTimeCardCategoryPerformanceMapper.deleteTreatTimeCardCategoryPerformance(form.EntityID);

        if (form.Category != null && form.Category.size() > 0) {
            List<TreatTimeCardCategoryPerformanceEntity> list = new ArrayList<>();
            for (TreatCardCategoryCommissionAddForm treatCardCategoryPerformanceAddForm : form.Category) {
                if (treatCardCategoryPerformanceAddForm.PayRate != null ||
                        treatCardCategoryPerformanceAddForm.CardRate != null ||
                        treatCardCategoryPerformanceAddForm.CardLargessRate != null ||
                        treatCardCategoryPerformanceAddForm.LargessRate != null) {
                    TreatTimeCardCategoryPerformanceEntity treatTimeCardCategoryPerformanceEntity = new TreatTimeCardCategoryPerformanceEntity();
                    treatTimeCardCategoryPerformanceEntity.EntityID = form.EntityID;
                    treatTimeCardCategoryPerformanceEntity.TimeCardCategoryID = treatCardCategoryPerformanceAddForm.CategoryID;
                    treatTimeCardCategoryPerformanceEntity.PayRate = treatCardCategoryPerformanceAddForm.PayRate;
                    treatTimeCardCategoryPerformanceEntity.CardRate = treatCardCategoryPerformanceAddForm.CardRate;
                    treatTimeCardCategoryPerformanceEntity.CardLargessRate = treatCardCategoryPerformanceAddForm.CardLargessRate;
                    treatTimeCardCategoryPerformanceEntity.LargessRate = treatCardCategoryPerformanceAddForm.LargessRate;
                    list.add(treatTimeCardCategoryPerformanceEntity);
                }
            }
            if (list != null && list.size() > 0) {
                List<List<TreatTimeCardCategoryPerformanceEntity>> subs = Lists.partition(list, 20);
                for (List<TreatTimeCardCategoryPerformanceEntity> entityList : subs) {
                    treatTimeCardCategoryPerformanceMapper.insertTreatTimeCardCategoryPerformanceEntity(entityList);
                }
            }
        }

        return BaseOutput.success();
    }
}