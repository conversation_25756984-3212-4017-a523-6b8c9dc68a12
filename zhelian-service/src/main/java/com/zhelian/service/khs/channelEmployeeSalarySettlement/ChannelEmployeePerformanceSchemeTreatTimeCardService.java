package com.zhelian.service.khs.channelEmployeeSalarySettlement;

import com.zhelian.mapper.khs.ChannelEmployeePerformanceSchemeTreatTimeCardMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.khs.entity.TreatChannelGoodPerformanceEntity;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodQueryForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeTreatGoodAddForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeTreatGoodsAddForm;
import com.zhelian.model.khs.form.output.PerformanceSchemeTreatGoodOutForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class ChannelEmployeePerformanceSchemeTreatTimeCardService {
    @Autowired
    ChannelEmployeePerformanceSchemeTreatTimeCardMapper channelEmployeePerformanceSchemeTreatTimeCardMapper;

    public BaseOutput all(PerformanceSchemeGoodQueryForm form) {
        List<PerformanceSchemeTreatGoodOutForm> list = channelEmployeePerformanceSchemeTreatTimeCardMapper.getAllPerformanceSchemeTimeCard(form.PerformanceSchemeID, form.CategoryID);
        return BaseOutputForm.success(list);

    }

    @Transactional
    public BaseOutput create(PerformanceSchemeTreatGoodsAddForm form) {
        channelEmployeePerformanceSchemeTreatTimeCardMapper.deleteTreatTimeCardPerformanceByPerformanceSchemeIDAndCategory(form.PerformanceSchemeID, form.CategoryID);
        List<TreatChannelGoodPerformanceEntity> list = new ArrayList<>();
        if (form.Good != null && form.Good.size() > 0) {
            for (PerformanceSchemeTreatGoodAddForm performanceSchemeGoodAddForm : form.Good) {
                if (performanceSchemeGoodAddForm.PerformancePayRate != null
                        || performanceSchemeGoodAddForm.PerformanceCardRate != null
                        || performanceSchemeGoodAddForm.PerformanceCardLargessRate != null
                        || performanceSchemeGoodAddForm.PerformanceLargessRate != null) {
                    TreatChannelGoodPerformanceEntity treatChannelGoodPerformanceEntity = new TreatChannelGoodPerformanceEntity();
                    treatChannelGoodPerformanceEntity.ChannelPerformanceSchemeID = form.PerformanceSchemeID;
                    treatChannelGoodPerformanceEntity.GoodID = performanceSchemeGoodAddForm.GoodID;
                    treatChannelGoodPerformanceEntity.PerformancePayRate = performanceSchemeGoodAddForm.PerformancePayRate;
                    treatChannelGoodPerformanceEntity.PerformanceCardRate = performanceSchemeGoodAddForm.PerformanceCardRate;
                    treatChannelGoodPerformanceEntity.PerformanceCardLargessRate = performanceSchemeGoodAddForm.PerformanceCardLargessRate;
                    treatChannelGoodPerformanceEntity.PerformanceLargessRate = performanceSchemeGoodAddForm.PerformanceLargessRate;
                    list.add(treatChannelGoodPerformanceEntity);
                }
            }
            channelEmployeePerformanceSchemeTreatTimeCardMapper.insertTreatChannelTimeCardPerformance(list);
        }
        return BaseOutput.success();
    }
}
