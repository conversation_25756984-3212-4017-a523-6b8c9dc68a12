package com.zhelian.service.khs.saleProjectPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.SalePackageCardProjectHandlerPerformanceMapper;
import com.zhelian.mapper.khs.SaleProjectHandlerPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectHandlersCommissionAddForm;
import com.zhelian.model.khs.entity.SalePackageCardProjectHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SalePackageCardProjectHandlerPerformanceEntity;
import com.zhelian.model.khs.entity.SaleProjectHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SaleProjectHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleGoodCategoryHandlerPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleProjectHandlerPerformanceService {

    @Autowired
    SaleProjectHandlerPerformanceMapper saleProjectHandlerPerformanceMapper;
    @Autowired
    SalePackageCardProjectHandlerPerformanceMapper salePackageCardProjectHandlerPerformanceMapper;

    public BaseOutput allSaleProjectHandlerPerformance(SaleProjectHandlerCommissionQueryForm form) {
        List<SaleGoodCategoryHandlerPerformanceOutputForm> list = saleProjectHandlerPerformanceMapper.allSaleProjectHandlerPerformance(form.EntityID, form.ProjectID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSalePackageCardProjectHandlerPerformance(SaleProjectHandlerCommissionQueryForm form) {
        List<SaleGoodCategoryHandlerPerformanceOutputForm> list = salePackageCardProjectHandlerPerformanceMapper.allSalePackageCardProjectHandlerPerformance(form.EntityID, form.ProjectID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleProjectHandlerPerformance(SaleProjectHandlersCommissionAddForm form) {

        saleProjectHandlerPerformanceMapper.deleteSaleProjectHandler(form.EntityID, form.ProjectID);
        saleProjectHandlerPerformanceMapper.deleteSaleProjectHandlerJobType(form.EntityID, form.ProjectID);
        List<SaleProjectHandlerPerformanceEntity> listSaleProjectHandlerPerformanceEntity = new ArrayList<>();
        List<SaleProjectHandlerJobTypePerformanceEntity> listSaleProjectHandlerJobTypePerformanceEntity = new ArrayList<>();

        if (form.ProjectHandler != null && form.ProjectHandler.size() > 0) {
            for (SaleProjectHandlerCommissionAddForm saleProjectHandlerAddForm : form.ProjectHandler) {
                if (saleProjectHandlerAddForm.PayRate != null ||
                        saleProjectHandlerAddForm.SavingCardRate != null ||
                        saleProjectHandlerAddForm.SavingCardLargessRate != null) {
                    SaleProjectHandlerPerformanceEntity saleProjectHandlerPerformanceEntity = new SaleProjectHandlerPerformanceEntity();
                    saleProjectHandlerPerformanceEntity.EntityID = form.EntityID;
                    saleProjectHandlerPerformanceEntity.ProjectID = form.ProjectID;
                    saleProjectHandlerPerformanceEntity.ProjectSaleHandlerID = saleProjectHandlerAddForm.SaleHandlerID;
                    saleProjectHandlerPerformanceEntity.PayRate = saleProjectHandlerAddForm.PayRate;
                    saleProjectHandlerPerformanceEntity.SavingCardRate = saleProjectHandlerAddForm.SavingCardRate;
                    saleProjectHandlerPerformanceEntity.SavingCardLargessRate = saleProjectHandlerAddForm.SavingCardLargessRate;
                    listSaleProjectHandlerPerformanceEntity.add(saleProjectHandlerPerformanceEntity);
                }

                if (saleProjectHandlerAddForm.HandlerJobTypeCommission != null && saleProjectHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleProjectCategoryHandlerJobTypeCommissionAddForm saleProjectCategoryHandlerJobTypePerformanceAddForm : saleProjectHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleProjectCategoryHandlerJobTypePerformanceAddForm.PayRate != null ||
                                saleProjectCategoryHandlerJobTypePerformanceAddForm.SavingCardRate != null ||
                                saleProjectCategoryHandlerJobTypePerformanceAddForm.SavingCardLargessRate != null) {
                            SaleProjectHandlerJobTypePerformanceEntity saleProjectHandlerJobTypePerformanceEntity = new SaleProjectHandlerJobTypePerformanceEntity();
                            saleProjectHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            saleProjectHandlerJobTypePerformanceEntity.ProjectID = form.ProjectID;
                            saleProjectHandlerJobTypePerformanceEntity.ProjectSaleHandlerID = saleProjectHandlerAddForm.SaleHandlerID;
                            saleProjectHandlerJobTypePerformanceEntity.JobTypeID = saleProjectCategoryHandlerJobTypePerformanceAddForm.JobTypeID;
                            saleProjectHandlerJobTypePerformanceEntity.PayRate = saleProjectCategoryHandlerJobTypePerformanceAddForm.PayRate;
                            saleProjectHandlerJobTypePerformanceEntity.SavingCardRate = saleProjectCategoryHandlerJobTypePerformanceAddForm.SavingCardRate;
                            saleProjectHandlerJobTypePerformanceEntity.SavingCardLargessRate = saleProjectCategoryHandlerJobTypePerformanceAddForm.SavingCardLargessRate;
                            listSaleProjectHandlerJobTypePerformanceEntity.add(saleProjectHandlerJobTypePerformanceEntity);
                        }
                    }
                }
            }
        }

        salePackageCardProjectHandlerPerformanceMapper.deleteSalePackageCardProjectHandler(form.EntityID, form.ProjectID);
        salePackageCardProjectHandlerPerformanceMapper.deleteSalePackageCardProjectHandlerJobType(form.EntityID, form.ProjectID);
        List<SalePackageCardProjectHandlerPerformanceEntity> listSalePackageCardProjectHandlerPerformanceEntity = new ArrayList<>();
        List<SalePackageCardProjectHandlerJobTypePerformanceEntity> listSalePackageCardPackageCardProjectHandlerJobTypePerformanceEntity = new ArrayList<>();

        if (form.PackageCardProjectHandler != null && form.PackageCardProjectHandler.size() > 0) {
            for (SaleProjectHandlerCommissionAddForm saleProjectHandlerAddForm : form.PackageCardProjectHandler) {
                if (saleProjectHandlerAddForm.PayRate != null ||
                        saleProjectHandlerAddForm.SavingCardRate != null ||
                        saleProjectHandlerAddForm.SavingCardLargessRate != null) {
                    SalePackageCardProjectHandlerPerformanceEntity salePackageCardProjectHandlerPerformanceEntity = new SalePackageCardProjectHandlerPerformanceEntity();
                    salePackageCardProjectHandlerPerformanceEntity.EntityID = form.EntityID;
                    salePackageCardProjectHandlerPerformanceEntity.ProjectID = form.ProjectID;
                    salePackageCardProjectHandlerPerformanceEntity.PackageCardSaleHandlerID = saleProjectHandlerAddForm.SaleHandlerID;
                    salePackageCardProjectHandlerPerformanceEntity.PayRate = saleProjectHandlerAddForm.PayRate;
                    salePackageCardProjectHandlerPerformanceEntity.SavingCardRate = saleProjectHandlerAddForm.SavingCardRate;
                    salePackageCardProjectHandlerPerformanceEntity.SavingCardLargessRate = saleProjectHandlerAddForm.SavingCardLargessRate;
                    listSalePackageCardProjectHandlerPerformanceEntity.add(salePackageCardProjectHandlerPerformanceEntity);
                }

                if (saleProjectHandlerAddForm.HandlerJobTypeCommission != null && saleProjectHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleProjectCategoryHandlerJobTypeCommissionAddForm saleProjectCategoryHandlerJobTypePerformanceAddForm : saleProjectHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleProjectCategoryHandlerJobTypePerformanceAddForm.PayRate != null ||
                                saleProjectCategoryHandlerJobTypePerformanceAddForm.SavingCardRate != null ||
                                saleProjectCategoryHandlerJobTypePerformanceAddForm.SavingCardLargessRate != null) {
                            SalePackageCardProjectHandlerJobTypePerformanceEntity salePackageCardProjectHandlerJobTypePerformanceEntity = new SalePackageCardProjectHandlerJobTypePerformanceEntity();
                            salePackageCardProjectHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            salePackageCardProjectHandlerJobTypePerformanceEntity.ProjectID = form.ProjectID;
                            salePackageCardProjectHandlerJobTypePerformanceEntity.PackageCardSaleHandlerID = saleProjectHandlerAddForm.SaleHandlerID;
                            salePackageCardProjectHandlerJobTypePerformanceEntity.JobTypeID = saleProjectCategoryHandlerJobTypePerformanceAddForm.JobTypeID;
                            salePackageCardProjectHandlerJobTypePerformanceEntity.PayRate = saleProjectCategoryHandlerJobTypePerformanceAddForm.PayRate;
                            salePackageCardProjectHandlerJobTypePerformanceEntity.SavingCardRate = saleProjectCategoryHandlerJobTypePerformanceAddForm.SavingCardRate;
                            salePackageCardProjectHandlerJobTypePerformanceEntity.SavingCardLargessRate = saleProjectCategoryHandlerJobTypePerformanceAddForm.SavingCardLargessRate;
                            listSalePackageCardPackageCardProjectHandlerJobTypePerformanceEntity.add(salePackageCardProjectHandlerJobTypePerformanceEntity);
                        }
                    }
                }
            }
        }

        if (listSaleProjectHandlerPerformanceEntity != null && listSaleProjectHandlerPerformanceEntity.size() > 0) {
            List<List<SaleProjectHandlerPerformanceEntity>> subs = Lists.partition(listSaleProjectHandlerPerformanceEntity, 20);
            for (List<SaleProjectHandlerPerformanceEntity> entityList : subs) {
                saleProjectHandlerPerformanceMapper.updateSaleProjectHandlerPerformance(entityList);
            }
        }

        if (listSaleProjectHandlerJobTypePerformanceEntity != null && listSaleProjectHandlerJobTypePerformanceEntity.size() > 0) {
            List<List<SaleProjectHandlerJobTypePerformanceEntity>> subs = Lists.partition(listSaleProjectHandlerJobTypePerformanceEntity, 20);
            for (List<SaleProjectHandlerJobTypePerformanceEntity> entityList : subs) {
                saleProjectHandlerPerformanceMapper.insertSaleProjectHandlerJobTypePerformance(entityList);
            }
        }
        if (listSalePackageCardProjectHandlerPerformanceEntity != null && listSalePackageCardProjectHandlerPerformanceEntity.size() > 0) {
            List<List<SalePackageCardProjectHandlerPerformanceEntity>> subs = Lists.partition(listSalePackageCardProjectHandlerPerformanceEntity, 20);
            for (List<SalePackageCardProjectHandlerPerformanceEntity> entityList : subs) {
                salePackageCardProjectHandlerPerformanceMapper.updateSalePackageCardProjectHandlerPerformance(entityList);
            }
        }
        if (listSalePackageCardPackageCardProjectHandlerJobTypePerformanceEntity != null && listSalePackageCardPackageCardProjectHandlerJobTypePerformanceEntity.size() > 0) {
            List<List<SalePackageCardProjectHandlerJobTypePerformanceEntity>> subs = Lists.partition(listSalePackageCardPackageCardProjectHandlerJobTypePerformanceEntity, 20);
            for (List<SalePackageCardProjectHandlerJobTypePerformanceEntity> entityList : subs) {
                salePackageCardProjectHandlerPerformanceMapper.insertSalePackageCardProjectHandlerJobTypePerformance(entityList);
            }
        }

        return BaseOutput.success();
    }
}