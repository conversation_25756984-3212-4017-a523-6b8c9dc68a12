package com.zhelian.service.khs.saleTimeCardPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.SaleTimeCardCategoryPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCommissionSchemeAddForm;
import com.zhelian.model.khs.entity.SaleTimeCardPerformanceSchemeEntity;
import com.zhelian.model.khs.entity.TBSaleTimeCardCategoryPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleCardGoodCategoryOutputForm;
import com.zhelian.model.khs.form.output.SaleGoodCategoryEntityPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleTimeCardCategoryPerformanceService {

    @Autowired
    SaleTimeCardCategoryPerformanceMapper saleTimeCardCategoryPerformanceMapper;


    public BaseOutput allSaleTimeCardCategoryPerformance(SaleTimeCardCommissionSchemeAddForm form) {
        SaleCardGoodCategoryOutputForm saleTimeCardCategoryOutputForm = saleTimeCardCategoryPerformanceMapper.saleTimeCardCategoryPerformance(form.EntityID);
        List<SaleGoodCategoryEntityPerformanceOutputForm> all = saleTimeCardCategoryPerformanceMapper.allSaleTimeCardCategoryPerformance(form.EntityID);
        saleTimeCardCategoryOutputForm.Category = all;
        return BaseOutputForm.success(saleTimeCardCategoryOutputForm);
    }

    @Transactional
    public BaseOutput updateSaleTimeCardCategoryPerformance(SaleTimeCardCategoryAddForm form) {
        SaleTimeCardPerformanceSchemeEntity saleTimeCardPerformanceSchemeEntity = new SaleTimeCardPerformanceSchemeEntity();
        saleTimeCardPerformanceSchemeEntity.EntityID = form.EntityID;
        saleTimeCardPerformanceSchemeEntity.PayRate = form.PayRate;
        saleTimeCardPerformanceSchemeEntity.SavingCardRate = form.SavingCardRate;
        saleTimeCardPerformanceSchemeEntity.SavingCardLargessRate = form.SavingCardLargessRate;

        saleTimeCardCategoryPerformanceMapper.updateSaleTimeCardPerformanceScheme(saleTimeCardPerformanceSchemeEntity);
        saleTimeCardCategoryPerformanceMapper.deleteSaleTimeCardCategoryPerformance(form.EntityID);
        if (form.Category != null && form.Category.size() > 0) {
            List<TBSaleTimeCardCategoryPerformanceEntity> list = new ArrayList<>();
            for (SaleTimeCardCategoryCommissionAddForm saleTimeCardCategoryPerformanceAddForm : form.Category) {
                if (saleTimeCardCategoryPerformanceAddForm.PayRate != null ||
                        saleTimeCardCategoryPerformanceAddForm.SavingCardRate != null ||
                        saleTimeCardCategoryPerformanceAddForm.SavingCardLargessRate != null) {
                    TBSaleTimeCardCategoryPerformanceEntity saleTimeCardCategoryPerformanceEntity = new TBSaleTimeCardCategoryPerformanceEntity();
                    saleTimeCardCategoryPerformanceEntity.EntityID = form.EntityID;
                    saleTimeCardCategoryPerformanceEntity.TimeCardCategoryID = saleTimeCardCategoryPerformanceAddForm.CategoryID;
                    saleTimeCardCategoryPerformanceEntity.PayRate = saleTimeCardCategoryPerformanceAddForm.PayRate;
                    saleTimeCardCategoryPerformanceEntity.SavingCardRate = saleTimeCardCategoryPerformanceAddForm.SavingCardRate;
                    saleTimeCardCategoryPerformanceEntity.SavingCardLargessRate = saleTimeCardCategoryPerformanceAddForm.SavingCardLargessRate;
                    list.add(saleTimeCardCategoryPerformanceEntity);
                }
            }

            if (list != null && list.size() > 0) {
                List<List<TBSaleTimeCardCategoryPerformanceEntity>> subs = Lists.partition(list, 20);
                for (List<TBSaleTimeCardCategoryPerformanceEntity> entityList : subs) {
                    saleTimeCardCategoryPerformanceMapper.insertSaleTimeCardCategoryPerformanceEntity(entityList);
                }
            }
        }

        return BaseOutput.success();
    }

}