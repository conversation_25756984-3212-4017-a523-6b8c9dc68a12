package com.zhelian.service.khs.treatProductPerformance;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.TreatProductCategoryPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatProductChildCategoryCommissionAddForm;
import com.zhelian.model.khs.entity.TBTreatProductCategoryPerformanceEntity;
import com.zhelian.model.khs.entity.TreatProductPerformanceSchemeEntity;
import com.zhelian.model.khs.form.output.TreatGoodCategoryOutputForm;
import com.zhelian.model.khs.form.output.TreatGoodCategoryPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import static com.zhelian.core.utils.ListUtil.listToTree;


@Service
public class TreatProductCategoryPerformanceService {

    @Autowired
    TreatProductCategoryPerformanceMapper treatProductCategoryPerformanceMapper;


    public BaseOutput allTreatProductCategoryPerformance(TreatProductCategoryCommissionQueryForm form) {
        TreatGoodCategoryOutputForm treatProductCategoryOutputForm = treatProductCategoryPerformanceMapper.treatProductCategoryPerformance(form.EntityID);
        List<TreatGoodCategoryPerformanceOutputForm> all = treatProductCategoryPerformanceMapper.allTreatProductCategoryPerformance(form.EntityID);
        JSONArray result = listToTree(JSONArray.parseArray(JSON.toJSONString(all, JSONWriter.Feature.WriteMapNullValue)), "CategoryID", "ParentID", "Child");
        treatProductCategoryOutputForm.Category = result;
        return BaseOutputForm.success(treatProductCategoryOutputForm);
    }

    @Transactional
    public BaseOutput updateTreatProductCategoryPerformance(TreatProductCategoryAddForm form) {
        TreatProductPerformanceSchemeEntity treatProductPerformanceSchemeEntity = new TreatProductPerformanceSchemeEntity();
        treatProductPerformanceSchemeEntity.EntityID = form.EntityID;
        treatProductPerformanceSchemeEntity.PayRate = form.PayRate;
        treatProductPerformanceSchemeEntity.CardRate = form.CardRate;
        treatProductPerformanceSchemeEntity.CardLargessRate = form.CardLargessRate;
        treatProductPerformanceSchemeEntity.LargessRate = form.LargessRate;

        treatProductCategoryPerformanceMapper.updateTreatProductPerformanceScheme(treatProductPerformanceSchemeEntity);

        treatProductCategoryPerformanceMapper.deleteTreatProductCategoryPerformanceEntity(form.EntityID);

        List<TBTreatProductCategoryPerformanceEntity> list = new ArrayList<>();
        if (form.Category != null && form.Category.size() > 0) {
            for (TreatProductCategoryCommissionAddForm treatProductCategoryPerformanceAddForm : form.Category) {
                if (treatProductCategoryPerformanceAddForm.PayRate != null ||
                        treatProductCategoryPerformanceAddForm.CardRate != null ||
                        treatProductCategoryPerformanceAddForm.CardLargessRate != null ||
                        treatProductCategoryPerformanceAddForm.LargessRate != null) {
                    TBTreatProductCategoryPerformanceEntity treatProductCategoryPerformanceEntity = new TBTreatProductCategoryPerformanceEntity();
                    treatProductCategoryPerformanceEntity.EntityID = form.EntityID;
                    treatProductCategoryPerformanceEntity.ProductCategoryID = treatProductCategoryPerformanceAddForm.CategoryID;
                    treatProductCategoryPerformanceEntity.PayRate = treatProductCategoryPerformanceAddForm.PayRate;
                    treatProductCategoryPerformanceEntity.CardRate = treatProductCategoryPerformanceAddForm.CardRate;
                    treatProductCategoryPerformanceEntity.CardLargessRate = treatProductCategoryPerformanceAddForm.CardLargessRate;
                    treatProductCategoryPerformanceEntity.LargessRate = treatProductCategoryPerformanceAddForm.LargessRate;
                    list.add(treatProductCategoryPerformanceEntity);
                }

                for (TreatProductChildCategoryCommissionAddForm treatProductChildCategoryPerformanceAddForm : treatProductCategoryPerformanceAddForm.Child) {
                    if (treatProductChildCategoryPerformanceAddForm.PayRate != null ||
                            treatProductChildCategoryPerformanceAddForm.CardRate != null ||
                            treatProductChildCategoryPerformanceAddForm.CardLargessRate != null ||
                            treatProductChildCategoryPerformanceAddForm.LargessRate != null) {
                        TBTreatProductCategoryPerformanceEntity treatProductCategoryPerformanceEntity2 = new TBTreatProductCategoryPerformanceEntity();
                        treatProductCategoryPerformanceEntity2.EntityID = form.EntityID;
                        treatProductCategoryPerformanceEntity2.ProductCategoryID = treatProductChildCategoryPerformanceAddForm.CategoryID;
                        treatProductCategoryPerformanceEntity2.PayRate = treatProductChildCategoryPerformanceAddForm.PayRate;
                        treatProductCategoryPerformanceEntity2.CardRate = treatProductChildCategoryPerformanceAddForm.CardRate;
                        treatProductCategoryPerformanceEntity2.CardLargessRate = treatProductChildCategoryPerformanceAddForm.CardLargessRate;
                        treatProductCategoryPerformanceEntity2.LargessRate = treatProductChildCategoryPerformanceAddForm.LargessRate;
                        list.add(treatProductCategoryPerformanceEntity2);
                    }
                }
            }
        }
        if (list != null && list.size() > 0) {
            List<List<TBTreatProductCategoryPerformanceEntity>> subs = Lists.partition(list, 20);
            for (List<TBTreatProductCategoryPerformanceEntity> entityList : subs) {
                treatProductCategoryPerformanceMapper.insertTreatProductCategoryPerformanceEntity(entityList);
            }
        }
        return BaseOutput.success();
    }

}