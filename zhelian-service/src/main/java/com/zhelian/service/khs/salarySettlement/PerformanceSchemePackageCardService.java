package com.zhelian.service.khs.salarySettlement;


import com.zhelian.mapper.khs.PerformanceSchemePackageCardMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.khs.entity.SalePackageCardPerformanceEntity;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodAddForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodQueryForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodsAddForm;
import com.zhelian.model.khs.form.output.PerformanceSchemeGoodOutForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class PerformanceSchemePackageCardService {

    @Autowired
    PerformanceSchemePackageCardMapper performanceSchemePackageCardMapper;

    public BaseOutput all(PerformanceSchemeGoodQueryForm form) {
        List<PerformanceSchemeGoodOutForm> list = performanceSchemePackageCardMapper.getAllPerformanceSchemePackageCard(form.PerformanceSchemeID, form.CategoryID);
        return BaseOutputForm.success(list);

    }

    @Transactional
    public BaseOutput create(PerformanceSchemeGoodsAddForm form) {
        performanceSchemePackageCardMapper.deleteSalePackageCardPerformanceByPerformanceSchemeIDAndCategory(form.PerformanceSchemeID, form.CategoryID);
        List<SalePackageCardPerformanceEntity> list = new ArrayList<>();
        if (form.Good != null && form.Good.size() > 0) {
            for (PerformanceSchemeGoodAddForm performanceSchemeGoodAddForm : form.Good) {
                if (performanceSchemeGoodAddForm.PayPerformanceRate != null || performanceSchemeGoodAddForm.SavingCardPerformanceRate != null ||
                        performanceSchemeGoodAddForm.SavingCardPerformanceLargessRate != null) {
                    SalePackageCardPerformanceEntity salePackageCardPerformanceEntity = new SalePackageCardPerformanceEntity();
                    salePackageCardPerformanceEntity.PerformanceSchemeID = form.PerformanceSchemeID;
                    salePackageCardPerformanceEntity.PackageCardID = performanceSchemeGoodAddForm.GoodID;
                    salePackageCardPerformanceEntity.PayPerformanceRate = performanceSchemeGoodAddForm.PayPerformanceRate;
                    salePackageCardPerformanceEntity.SavingCardPerformanceRate = performanceSchemeGoodAddForm.SavingCardPerformanceRate;
                    salePackageCardPerformanceEntity.SavingCardPerformanceLargessRate = performanceSchemeGoodAddForm.SavingCardPerformanceLargessRate;
                    list.add(salePackageCardPerformanceEntity);
                }
            }
            performanceSchemePackageCardMapper.insertSalePackageCardPerformance(list);
        }
        return BaseOutput.success();
    }
}
