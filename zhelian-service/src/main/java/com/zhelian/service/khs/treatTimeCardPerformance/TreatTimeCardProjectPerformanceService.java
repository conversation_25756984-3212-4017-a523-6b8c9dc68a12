package com.zhelian.service.khs.treatTimeCardPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.TreatTimeCardProjectPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatCardProjectCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardProjectsCommissionAddForm;
import com.zhelian.model.khs.entity.TreatTimeCardProjectPerformanceEntity;
import com.zhelian.model.khs.form.output.TreatCardPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatTimeCardProjectPerformanceService {

    @Autowired
    TreatTimeCardProjectPerformanceMapper treatTimeCardProjectPerformanceMapper;


    public BaseOutput allTreatTimeCardProjectPerformance(TreatCardCommissionQueryForm form) {
        List<TreatCardPerformanceOutputForm> list = treatTimeCardProjectPerformanceMapper.allTreatTimeCardProjectPerformance(form.EntityID, form.CardID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatTimeCardProjectPerformance(TreatCardProjectsCommissionAddForm form) {

        treatTimeCardProjectPerformanceMapper.deleteTreatTimeCardProjectPerformanceByEntityIDAndCard(form.EntityID, form.CardID);
        List<TreatTimeCardProjectPerformanceEntity> list = new ArrayList<>();
        if (form.Project != null && form.Project.size() > 0) {
            for (TreatCardProjectCommissionAddForm treatCardPerformanceAddForm : form.Project) {
                if (treatCardPerformanceAddForm.PayRate != null ||
                        treatCardPerformanceAddForm.CardRate != null ||
                        treatCardPerformanceAddForm.CardLargessRate != null ||
                        treatCardPerformanceAddForm.LargessRate != null) {
                    TreatTimeCardProjectPerformanceEntity treatTimeCardPerformanceEntity = new TreatTimeCardProjectPerformanceEntity();
                    treatTimeCardPerformanceEntity.EntityID = form.EntityID;
                    treatTimeCardPerformanceEntity.TimeCardID = form.CardID;
                    treatTimeCardPerformanceEntity.ProjectID = treatCardPerformanceAddForm.ProjectID;
                    treatTimeCardPerformanceEntity.PayRate = treatCardPerformanceAddForm.PayRate;
                    treatTimeCardPerformanceEntity.CardRate = treatCardPerformanceAddForm.CardRate;
                    treatTimeCardPerformanceEntity.CardLargessRate = treatCardPerformanceAddForm.CardLargessRate;
                    treatTimeCardPerformanceEntity.LargessRate = treatCardPerformanceAddForm.LargessRate;
                    list.add(treatTimeCardPerformanceEntity);
                }
            }
            if (list != null && list.size() > 0) {
                List<List<TreatTimeCardProjectPerformanceEntity>> subs = Lists.partition(list, 20);
                for (List<TreatTimeCardProjectPerformanceEntity> entityList : subs) {
                    treatTimeCardProjectPerformanceMapper.insertTreatTimeCardProjectPerformance(entityList);
                }
            }
        }
        return BaseOutput.success();
    }

}