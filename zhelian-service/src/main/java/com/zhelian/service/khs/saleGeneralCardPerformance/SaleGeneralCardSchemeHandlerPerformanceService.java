package com.zhelian.service.khs.saleGeneralCardPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.SaleGeneralCardSchemeHandlerPerformanceMapper;
import com.zhelian.mapper.khs.SalePackageCardGeneralCardSchemeHandlerPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardSchemeHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardSchemeHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardSchemeHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardsSchemeHandlerCommissionAddForm;
import com.zhelian.model.khs.entity.SaleGeneralCardSchemeHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SaleGeneralCardSchemeHandlerPerformanceEntity;
import com.zhelian.model.khs.entity.SalePackageCardGeneralCardSchemeHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SalePackageCardGeneralCardSchemeHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleGoodCategoryHandlerPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleGeneralCardSchemeHandlerPerformanceService {

    @Autowired
    SaleGeneralCardSchemeHandlerPerformanceMapper saleGeneralCardSchemeHandlerPerformanceMapper;
    @Autowired
    SalePackageCardGeneralCardSchemeHandlerPerformanceMapper salePackageCardGeneralCardSchemeHandlerPerformanceMapper;

    public BaseOutput allSaleGeneralCardSchemeHandlerPerformance(SaleGeneralCardSchemeHandlerCommissionQueryForm form) {
        List<SaleGoodCategoryHandlerPerformanceOutputForm> list = saleGeneralCardSchemeHandlerPerformanceMapper.allSaleGeneralCardSchemeHandlerPerformance(form.EntityID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSalePackageCardGeneralCardSchemeHandlerPerformance(SaleGeneralCardSchemeHandlerCommissionQueryForm form) {
        List<SaleGoodCategoryHandlerPerformanceOutputForm> list = salePackageCardGeneralCardSchemeHandlerPerformanceMapper.allSalePackageCardGeneralCardSchemeHandlerPerformance(form.EntityID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleGeneralCardSchemeHandlerPerformance(SaleGeneralCardsSchemeHandlerCommissionAddForm form) {

        saleGeneralCardSchemeHandlerPerformanceMapper.deleteSaleGeneralCardSchemeHandler(form.EntityID);
        saleGeneralCardSchemeHandlerPerformanceMapper.deleteSaleGeneralCardSchemeHandlerJobType(form.EntityID);
        List<SaleGeneralCardSchemeHandlerPerformanceEntity> listSaleGeneralCardSchemeHandlerPerformanceEntity = new ArrayList<>();
        List<SaleGeneralCardSchemeHandlerJobTypePerformanceEntity> listSaleGeneralCardSchemeHandlerJobTypePerformanceEntity = new ArrayList<>();
        if (form.GeneralCardHandler != null && form.GeneralCardHandler.size() > 0) {
            for (SaleGeneralCardSchemeHandlerCommissionAddForm saleGeneralCardSchemeHandlerAddForm : form.GeneralCardHandler) {
                if (saleGeneralCardSchemeHandlerAddForm.PayRate != null ||
                        saleGeneralCardSchemeHandlerAddForm.SavingCardRate != null ||
                        saleGeneralCardSchemeHandlerAddForm.SavingCardLargessRate != null) {
                    SaleGeneralCardSchemeHandlerPerformanceEntity saleGeneralCardSchemeHandlerPerformanceEntity = new SaleGeneralCardSchemeHandlerPerformanceEntity();
                    saleGeneralCardSchemeHandlerPerformanceEntity.EntityID = form.EntityID;
                    saleGeneralCardSchemeHandlerPerformanceEntity.GeneralCardSaleHandlerID = saleGeneralCardSchemeHandlerAddForm.SaleHandlerID;
                    saleGeneralCardSchemeHandlerPerformanceEntity.PayRate = saleGeneralCardSchemeHandlerAddForm.PayRate;
                    saleGeneralCardSchemeHandlerPerformanceEntity.SavingCardRate = saleGeneralCardSchemeHandlerAddForm.SavingCardRate;
                    saleGeneralCardSchemeHandlerPerformanceEntity.SavingCardLargessRate = saleGeneralCardSchemeHandlerAddForm.SavingCardLargessRate;
                    listSaleGeneralCardSchemeHandlerPerformanceEntity.add(saleGeneralCardSchemeHandlerPerformanceEntity);
                }

                if (saleGeneralCardSchemeHandlerAddForm.HandlerJobTypeCommission != null && saleGeneralCardSchemeHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleGeneralCardSchemeHandlerJobTypeCommissionAddForm saleGeneralCardSchemeHandlerJobTypePerformanceAddForm : saleGeneralCardSchemeHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleGeneralCardSchemeHandlerJobTypePerformanceAddForm.PayRate != null ||
                                saleGeneralCardSchemeHandlerJobTypePerformanceAddForm.SavingCardRate != null ||
                                saleGeneralCardSchemeHandlerJobTypePerformanceAddForm.SavingCardLargessRate != null) {
                            SaleGeneralCardSchemeHandlerJobTypePerformanceEntity saleGeneralCardSchemeHandlerJobTypePerformanceEntity = new SaleGeneralCardSchemeHandlerJobTypePerformanceEntity();
                            saleGeneralCardSchemeHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            saleGeneralCardSchemeHandlerJobTypePerformanceEntity.GeneralCardSaleHandlerID = saleGeneralCardSchemeHandlerAddForm.SaleHandlerID;
                            saleGeneralCardSchemeHandlerJobTypePerformanceEntity.JobTypeID = saleGeneralCardSchemeHandlerJobTypePerformanceAddForm.JobTypeID;
                            saleGeneralCardSchemeHandlerJobTypePerformanceEntity.PayRate = saleGeneralCardSchemeHandlerJobTypePerformanceAddForm.PayRate;
                            saleGeneralCardSchemeHandlerJobTypePerformanceEntity.SavingCardRate = saleGeneralCardSchemeHandlerJobTypePerformanceAddForm.SavingCardRate;
                            saleGeneralCardSchemeHandlerJobTypePerformanceEntity.SavingCardLargessRate = saleGeneralCardSchemeHandlerJobTypePerformanceAddForm.SavingCardLargessRate;
                            listSaleGeneralCardSchemeHandlerJobTypePerformanceEntity.add(saleGeneralCardSchemeHandlerJobTypePerformanceEntity);
                        }
                    }
                }
            }
        }

        salePackageCardGeneralCardSchemeHandlerPerformanceMapper.deleteSalePackageCardGeneralCardSchemeHandler(form.EntityID);
        salePackageCardGeneralCardSchemeHandlerPerformanceMapper.deleteSalePackageCardGeneralCardSchemeHandlerJobType(form.EntityID);
        List<SalePackageCardGeneralCardSchemeHandlerPerformanceEntity> listSalePackageCardGeneralCardSchemeHandlerPerformanceEntity = new ArrayList<>();
        List<SalePackageCardGeneralCardSchemeHandlerJobTypePerformanceEntity> listSalePackageCardGeneralCardSchemeHandlerJobTypePerformanceEntity = new ArrayList<>();
        if (form.PackageCardGeneralCardHandler != null && form.PackageCardGeneralCardHandler.size() > 0) {
            for (SaleGeneralCardSchemeHandlerCommissionAddForm saleGeneralCardSchemeHandlerAddForm : form.PackageCardGeneralCardHandler) {
                if (saleGeneralCardSchemeHandlerAddForm.PayRate != null ||
                        saleGeneralCardSchemeHandlerAddForm.SavingCardRate != null ||
                        saleGeneralCardSchemeHandlerAddForm.SavingCardLargessRate != null) {
                    SalePackageCardGeneralCardSchemeHandlerPerformanceEntity salePackageCardGeneralCardSchemeHandlerPerformanceEntity = new SalePackageCardGeneralCardSchemeHandlerPerformanceEntity();
                    salePackageCardGeneralCardSchemeHandlerPerformanceEntity.EntityID = form.EntityID;
                    salePackageCardGeneralCardSchemeHandlerPerformanceEntity.PackageCardSaleHandlerID = saleGeneralCardSchemeHandlerAddForm.SaleHandlerID;
                    salePackageCardGeneralCardSchemeHandlerPerformanceEntity.PayRate = saleGeneralCardSchemeHandlerAddForm.PayRate;
                    salePackageCardGeneralCardSchemeHandlerPerformanceEntity.SavingCardRate = saleGeneralCardSchemeHandlerAddForm.SavingCardRate;
                    salePackageCardGeneralCardSchemeHandlerPerformanceEntity.SavingCardLargessRate = saleGeneralCardSchemeHandlerAddForm.SavingCardLargessRate;
                    listSalePackageCardGeneralCardSchemeHandlerPerformanceEntity.add(salePackageCardGeneralCardSchemeHandlerPerformanceEntity);
                }

                if (saleGeneralCardSchemeHandlerAddForm.HandlerJobTypeCommission != null && saleGeneralCardSchemeHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleGeneralCardSchemeHandlerJobTypeCommissionAddForm saleGeneralCardSchemeHandlerJobTypePerformanceAddForm : saleGeneralCardSchemeHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleGeneralCardSchemeHandlerJobTypePerformanceAddForm.PayRate != null ||
                                saleGeneralCardSchemeHandlerJobTypePerformanceAddForm.SavingCardRate != null ||
                                saleGeneralCardSchemeHandlerJobTypePerformanceAddForm.SavingCardLargessRate != null) {
                            SalePackageCardGeneralCardSchemeHandlerJobTypePerformanceEntity salePackageCardGeneralCardSchemeHandlerJobTypePerformanceEntity = new SalePackageCardGeneralCardSchemeHandlerJobTypePerformanceEntity();
                            salePackageCardGeneralCardSchemeHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            salePackageCardGeneralCardSchemeHandlerJobTypePerformanceEntity.PackageCardSaleHandlerID = saleGeneralCardSchemeHandlerAddForm.SaleHandlerID;
                            salePackageCardGeneralCardSchemeHandlerJobTypePerformanceEntity.JobTypeID = saleGeneralCardSchemeHandlerJobTypePerformanceAddForm.JobTypeID;
                            salePackageCardGeneralCardSchemeHandlerJobTypePerformanceEntity.PayRate = saleGeneralCardSchemeHandlerJobTypePerformanceAddForm.PayRate;
                            salePackageCardGeneralCardSchemeHandlerJobTypePerformanceEntity.SavingCardRate = saleGeneralCardSchemeHandlerJobTypePerformanceAddForm.SavingCardRate;
                            salePackageCardGeneralCardSchemeHandlerJobTypePerformanceEntity.SavingCardLargessRate = saleGeneralCardSchemeHandlerJobTypePerformanceAddForm.SavingCardLargessRate;
                            listSalePackageCardGeneralCardSchemeHandlerJobTypePerformanceEntity.add(salePackageCardGeneralCardSchemeHandlerJobTypePerformanceEntity);
                        }
                    }
                }
            }
        }

        if (listSaleGeneralCardSchemeHandlerPerformanceEntity != null && listSaleGeneralCardSchemeHandlerPerformanceEntity.size() > 0) {
            List<List<SaleGeneralCardSchemeHandlerPerformanceEntity>> subs = Lists.partition(listSaleGeneralCardSchemeHandlerPerformanceEntity, 20);
            for (List<SaleGeneralCardSchemeHandlerPerformanceEntity> entityList : subs) {
                saleGeneralCardSchemeHandlerPerformanceMapper.updateSaleGeneralCardSchemeHandlerPerformance(entityList);
            }
        }
        if (listSaleGeneralCardSchemeHandlerJobTypePerformanceEntity != null && listSaleGeneralCardSchemeHandlerJobTypePerformanceEntity.size() > 0) {
            List<List<SaleGeneralCardSchemeHandlerJobTypePerformanceEntity>> subs = Lists.partition(listSaleGeneralCardSchemeHandlerJobTypePerformanceEntity, 20);
            for (List<SaleGeneralCardSchemeHandlerJobTypePerformanceEntity> entityList : subs) {
                saleGeneralCardSchemeHandlerPerformanceMapper.insertSaleGeneralCardSchemeHandlerJobTypePerformance(entityList);
            }
        }
        if (listSalePackageCardGeneralCardSchemeHandlerPerformanceEntity != null && listSalePackageCardGeneralCardSchemeHandlerPerformanceEntity.size() > 0) {
            List<List<SalePackageCardGeneralCardSchemeHandlerPerformanceEntity>> subs = Lists.partition(listSalePackageCardGeneralCardSchemeHandlerPerformanceEntity, 20);
            for (List<SalePackageCardGeneralCardSchemeHandlerPerformanceEntity> entityList : subs) {
                salePackageCardGeneralCardSchemeHandlerPerformanceMapper.updateSalePackageCardGeneralCardSchemeHandlerPerformance(entityList);
            }
        }
        if (listSalePackageCardGeneralCardSchemeHandlerJobTypePerformanceEntity != null && listSalePackageCardGeneralCardSchemeHandlerJobTypePerformanceEntity.size() > 0) {
            List<List<SalePackageCardGeneralCardSchemeHandlerJobTypePerformanceEntity>> subs = Lists.partition(listSalePackageCardGeneralCardSchemeHandlerJobTypePerformanceEntity, 20);
            for (List<SalePackageCardGeneralCardSchemeHandlerJobTypePerformanceEntity> entityList : subs) {
                salePackageCardGeneralCardSchemeHandlerPerformanceMapper.insertSalePackageCardGeneralCardSchemeHandlerJobTypePerformance(entityList);
            }
        }

        return BaseOutput.success();
    }
}