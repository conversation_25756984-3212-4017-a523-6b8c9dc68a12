package com.zhelian.service.khs.saleSavingCardPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.SaleSavingCardCategoryPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.output.SaleSavingCardCategoryCommissionOutputForm;
import com.zhelian.model.ibeauty.form.output.SaleSavingCardCategoryOutputForm;
import com.zhelian.model.khs.entity.SaleSavingCardPerformanceSchemeEntity;
import com.zhelian.model.khs.entity.TBSaleSavingCardCategoryPerformanceEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleSavingCardCategoryPerformanceService {

    @Autowired
    SaleSavingCardCategoryPerformanceMapper saleSavingCardCategoryPerformanceMapper;


    public BaseOutput allSaleSavingCardCategoryPerformance(SaleSavingCardCommissionSchemeAddForm form) {
        SaleSavingCardCategoryOutputForm saleSavingCardCategoryOutputForm = saleSavingCardCategoryPerformanceMapper.saleSavingCardCategoryPerformance(form.EntityID);
        List<SaleSavingCardCategoryCommissionOutputForm> all = saleSavingCardCategoryPerformanceMapper.allSaleSavingCardCategoryPerformance(form.EntityID);
        saleSavingCardCategoryOutputForm.Category = all;
        return BaseOutputForm.success(saleSavingCardCategoryOutputForm);
    }

    @Transactional
    public BaseOutput updateSaleSavingCardCategoryPerformance(SaleSavingCardCategoryAddForm form) {
        SaleSavingCardPerformanceSchemeEntity saleSavingCardPerformanceSchemeEntity = new SaleSavingCardPerformanceSchemeEntity();
        saleSavingCardPerformanceSchemeEntity.EntityID = form.EntityID;
        saleSavingCardPerformanceSchemeEntity.PayRate = form.PayRate;

        saleSavingCardCategoryPerformanceMapper.updateSaleSavingCardPerformanceScheme(saleSavingCardPerformanceSchemeEntity);

        saleSavingCardCategoryPerformanceMapper.deleteSaleSavingCardCategoryPerformance(form.EntityID);

        if (form.Category != null && form.Category.size() > 0) {
            List<TBSaleSavingCardCategoryPerformanceEntity> list = new ArrayList<>();
            for (SaleSavingCardCategoryCommissionAddForm saleSavingCardCategoryPerformanceAddForm : form.Category) {
                if (saleSavingCardCategoryPerformanceAddForm.PayFixed != null || saleSavingCardCategoryPerformanceAddForm.PayRate != null ||
                        saleSavingCardCategoryPerformanceAddForm.SpecialBenefit != null) {
                    TBSaleSavingCardCategoryPerformanceEntity saleSavingCardCategoryPerformanceEntity = new TBSaleSavingCardCategoryPerformanceEntity();
                    saleSavingCardCategoryPerformanceEntity.EntityID = form.EntityID;
                    saleSavingCardCategoryPerformanceEntity.SavingCardCategoryID = saleSavingCardCategoryPerformanceAddForm.CategoryID;
                    saleSavingCardCategoryPerformanceEntity.PayRate = saleSavingCardCategoryPerformanceAddForm.PayRate;
                    list.add(saleSavingCardCategoryPerformanceEntity);
                }
            }

            if (list != null && list.size() > 0) {
                List<List<TBSaleSavingCardCategoryPerformanceEntity>> subs = Lists.partition(list, 20);
                for (List<TBSaleSavingCardCategoryPerformanceEntity> entityList : subs) {
                    saleSavingCardCategoryPerformanceMapper.insertSaleSavingCardCategoryPerformanceEntity(entityList);
                }
            }
        }

        return BaseOutput.success();
    }

}