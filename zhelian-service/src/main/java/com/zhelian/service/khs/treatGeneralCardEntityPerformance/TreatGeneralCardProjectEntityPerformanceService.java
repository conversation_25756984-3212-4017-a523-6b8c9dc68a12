package com.zhelian.service.khs.treatGeneralCardEntityPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.TreatGeneralCardProjectEntityPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCommissionQueryForm;
import com.zhelian.model.khs.entity.TreatGoodProjectEntityPerformanceEntity;
import com.zhelian.model.khs.form.input.TreatCardProjectsEntityPerformanceAddForm;
import com.zhelian.model.khs.form.input.TreatGoodPerformanceAddForm;
import com.zhelian.model.khs.form.output.TreatGoodPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatGeneralCardProjectEntityPerformanceService {

    @Autowired
    TreatGeneralCardProjectEntityPerformanceMapper treatGeneralCardProjectEntityPerformanceMapper;


    public BaseOutput allTreatGeneralCardProjectEntityPerformance(TreatCardCommissionQueryForm form) {
        List<TreatGoodPerformanceOutputForm> list = treatGeneralCardProjectEntityPerformanceMapper.allTreatGeneralCardProjectEntityPerformance(form.EntityID, form.CardID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatGeneralCardProjectEntityPerformance(TreatCardProjectsEntityPerformanceAddForm form) {

        treatGeneralCardProjectEntityPerformanceMapper.deleteTreatGeneralCardProjectEntityPerformanceByEntityIDAndCard(form.EntityID, form.CardID);
        List<TreatGoodProjectEntityPerformanceEntity> list = new ArrayList<>();
        if (form.Good != null && form.Good.size() > 0) {
            for (TreatGoodPerformanceAddForm treatCardEntityPerformanceAddForm : form.Good) {
                if (treatCardEntityPerformanceAddForm.PayRate != null ||
                        treatCardEntityPerformanceAddForm.CardRate != null ||
                        treatCardEntityPerformanceAddForm.CardLargessRate != null ||
                        treatCardEntityPerformanceAddForm.LargessRate != null) {
                    TreatGoodProjectEntityPerformanceEntity treatGeneralCardEntityPerformanceEntity = new TreatGoodProjectEntityPerformanceEntity();
                    treatGeneralCardEntityPerformanceEntity.EntityID = form.EntityID;
                    treatGeneralCardEntityPerformanceEntity.GoodID = form.CardID;
                    treatGeneralCardEntityPerformanceEntity.ProjectID = treatCardEntityPerformanceAddForm.GoodID;
                    treatGeneralCardEntityPerformanceEntity.PayRate = treatCardEntityPerformanceAddForm.PayRate;
                    treatGeneralCardEntityPerformanceEntity.CardRate = treatCardEntityPerformanceAddForm.CardRate;
                    treatGeneralCardEntityPerformanceEntity.CardLargessRate = treatCardEntityPerformanceAddForm.CardLargessRate;
                    treatGeneralCardEntityPerformanceEntity.LargessRate = treatCardEntityPerformanceAddForm.LargessRate;
                    list.add(treatGeneralCardEntityPerformanceEntity);
                }
            }
            if (list != null && list.size() > 0) {
                List<List<TreatGoodProjectEntityPerformanceEntity>> subs = Lists.partition(list, 20);
                for (List<TreatGoodProjectEntityPerformanceEntity> entityList : subs) {
                    treatGeneralCardProjectEntityPerformanceMapper.insertTreatGeneralCardProjectEntityPerformance(entityList);
                }
            }
        }
        return BaseOutput.success();
    }

}