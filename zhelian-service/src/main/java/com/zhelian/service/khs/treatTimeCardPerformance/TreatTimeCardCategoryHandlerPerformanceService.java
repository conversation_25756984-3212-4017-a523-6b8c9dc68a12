package com.zhelian.service.khs.treatTimeCardPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.TreatTimeCardCategoryHandlerPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatGeneralCardCategoryHandlerAddForm;
import com.zhelian.model.khs.entity.TreatTimeCardCategoryHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.TreatTimeCardCategoryHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.TreatGoodCategoryHandlerPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatTimeCardCategoryHandlerPerformanceService {

    @Autowired
    TreatTimeCardCategoryHandlerPerformanceMapper treatTimeCardCategoryHandlerPerformanceMapper;


    public BaseOutput allTreatTimeCardCategoryHandlerPerformance(TreatCardCategoryHandlerCommissionQueryForm form) {
        List<TreatGoodCategoryHandlerPerformanceOutputForm> list = treatTimeCardCategoryHandlerPerformanceMapper.allTreatTimeCardCategoryHandlerPerformance(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatTimeCardCategoryHandlerPerformance(TreatCardCategoryHandlerCommissionAddForm form) {
        treatTimeCardCategoryHandlerPerformanceMapper.deleteTreatTimeCardCategoryHandler(form.EntityID, form.CategoryID);
        treatTimeCardCategoryHandlerPerformanceMapper.deleteTreatTimeCardCategoryHandlerJobType(form.EntityID, form.CategoryID);
        List<TreatTimeCardCategoryHandlerPerformanceEntity> list = new ArrayList<>();
        List<TreatTimeCardCategoryHandlerJobTypePerformanceEntity> handlerJobTypePerformanceList = new ArrayList<>();
        if (form.CardHandler != null && form.CardHandler.size() > 0) {
            for (TreatGeneralCardCategoryHandlerAddForm treatTimeCardCategoryHandlerAddForm : form.CardHandler) {
                if (treatTimeCardCategoryHandlerAddForm.PayRate != null ||
                        treatTimeCardCategoryHandlerAddForm.CardRate != null ||
                        treatTimeCardCategoryHandlerAddForm.CardLargessRate != null ||
                        treatTimeCardCategoryHandlerAddForm.LargessRate != null) {
                    TreatTimeCardCategoryHandlerPerformanceEntity treatTimeCardCategoryHandlerPerformanceEntity = new TreatTimeCardCategoryHandlerPerformanceEntity();
                    treatTimeCardCategoryHandlerPerformanceEntity.EntityID = form.EntityID;
                    treatTimeCardCategoryHandlerPerformanceEntity.TimeCardCategoryID = form.CategoryID;
                    treatTimeCardCategoryHandlerPerformanceEntity.TimeCardTreatHandlerID = treatTimeCardCategoryHandlerAddForm.TreatHandlerID;
                    treatTimeCardCategoryHandlerPerformanceEntity.PayRate = treatTimeCardCategoryHandlerAddForm.PayRate;
                    treatTimeCardCategoryHandlerPerformanceEntity.CardRate = treatTimeCardCategoryHandlerAddForm.CardRate;
                    treatTimeCardCategoryHandlerPerformanceEntity.CardLargessRate = treatTimeCardCategoryHandlerAddForm.CardLargessRate;
                    treatTimeCardCategoryHandlerPerformanceEntity.LargessRate = treatTimeCardCategoryHandlerAddForm.LargessRate;
                    list.add(treatTimeCardCategoryHandlerPerformanceEntity);
                }
                if (treatTimeCardCategoryHandlerAddForm.HandlerJobTypeCommission != null && treatTimeCardCategoryHandlerAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (TreatCardCategoryHandlerJobTypeCommissionAddForm treatCardCategoryHandlerJobTypePerformanceAddForm : treatTimeCardCategoryHandlerAddForm.HandlerJobTypeCommission) {
                        if (treatCardCategoryHandlerJobTypePerformanceAddForm.PayRate != null ||
                                treatCardCategoryHandlerJobTypePerformanceAddForm.CardRate != null ||
                                treatCardCategoryHandlerJobTypePerformanceAddForm.CardLargessRate != null ||
                                treatCardCategoryHandlerJobTypePerformanceAddForm.LargessFixed != null) {
                            TreatTimeCardCategoryHandlerJobTypePerformanceEntity PerformanceEntity = new TreatTimeCardCategoryHandlerJobTypePerformanceEntity();
                            PerformanceEntity.EntityID = form.EntityID;
                            PerformanceEntity.TimeCardCategoryID = form.CategoryID;
                            PerformanceEntity.TimeCardTreatHandlerID = treatTimeCardCategoryHandlerAddForm.TreatHandlerID;
                            PerformanceEntity.JobTypeID = treatCardCategoryHandlerJobTypePerformanceAddForm.JobTypeID;
                            PerformanceEntity.PayRate = treatCardCategoryHandlerJobTypePerformanceAddForm.PayRate;
                            PerformanceEntity.CardRate = treatCardCategoryHandlerJobTypePerformanceAddForm.CardRate;
                            PerformanceEntity.CardLargessRate = treatCardCategoryHandlerJobTypePerformanceAddForm.CardLargessRate;
                            PerformanceEntity.LargessRate = treatCardCategoryHandlerJobTypePerformanceAddForm.LargessRate;
                            handlerJobTypePerformanceList.add(PerformanceEntity);
                        }
                    }
                }
            }
        }
        if (list != null && list.size() > 0) {
            List<List<TreatTimeCardCategoryHandlerPerformanceEntity>> subs = Lists.partition(list, 20);
            for (List<TreatTimeCardCategoryHandlerPerformanceEntity> entityList : subs) {
                treatTimeCardCategoryHandlerPerformanceMapper.insertTreatTimeCardCategoryHandlerPerformance(entityList);
            }
        }
        if (handlerJobTypePerformanceList != null && handlerJobTypePerformanceList.size() > 0) {
            List<List<TreatTimeCardCategoryHandlerJobTypePerformanceEntity>> subs = Lists.partition(handlerJobTypePerformanceList, 20);
            for (List<TreatTimeCardCategoryHandlerJobTypePerformanceEntity> entityList : subs) {
                treatTimeCardCategoryHandlerPerformanceMapper.insertTreatTimeCardCategoryHandlerJobTypePerformance(entityList);
            }
        }

        return BaseOutput.success();
    }
}