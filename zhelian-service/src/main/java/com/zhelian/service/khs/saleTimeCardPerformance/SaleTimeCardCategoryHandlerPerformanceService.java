package com.zhelian.service.khs.saleTimeCardPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.SalePackageCardTimeCardCategoryHandlerPerformanceMapper;
import com.zhelian.mapper.khs.SaleTimeCardCategoryHandlerPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.khs.entity.SalePackageCardTimeCardCategoryHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SalePackageCardTimeCardCategoryHandlerPerformanceEntity;
import com.zhelian.model.khs.entity.SaleTimeCardCategoryHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SaleTimeCardCategoryHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleGoodCategoryHandlerPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleTimeCardCategoryHandlerPerformanceService {

    @Autowired
    SaleTimeCardCategoryHandlerPerformanceMapper saleTimeCardCategoryHandlerPerformanceMapper;
    @Autowired
    SalePackageCardTimeCardCategoryHandlerPerformanceMapper salePackageCardTimeCardCategoryHandlerPerformanceMapper;


    public BaseOutput allSaleTimeCardCategoryHandlerPerformance(SaleTimeCardCategoryHandlerCommissionQueryForm form) {
        List<SaleGoodCategoryHandlerPerformanceOutputForm> list = saleTimeCardCategoryHandlerPerformanceMapper.allSaleTimeCardCategoryHandlerPerformance(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSalePackageCardTimeCardCategoryHandlerPerformance(SaleTimeCardCategoryHandlerCommissionQueryForm form) {
        List<SaleGoodCategoryHandlerPerformanceOutputForm> list = salePackageCardTimeCardCategoryHandlerPerformanceMapper.allSalePackageCardTimeCardCategoryHandlerPerformance(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleTimeCardCategoryHandlerPerformance(SaleTimeCardCategoryHandlerCommissionAddForm form) {


        saleTimeCardCategoryHandlerPerformanceMapper.deleteSaleTimeCardCategoryHandler(form.EntityID, form.CategoryID);
        saleTimeCardCategoryHandlerPerformanceMapper.deleteSaleTimeCardCategoryHandlerJobType(form.EntityID, form.CategoryID);
        List list = new ArrayList();
        List handlerJobTypePerformanceList = new ArrayList<>();
        if (form.TimeCardHandler != null && form.TimeCardHandler.size() > 0) {
            for (SaleTimeCardCategoryHandlerAddForm saleTimeCardCategoryHandlerAddForm : form.TimeCardHandler) {
                if (saleTimeCardCategoryHandlerAddForm.PayRate != null || saleTimeCardCategoryHandlerAddForm.PayFixed != null ||
                        saleTimeCardCategoryHandlerAddForm.SavingCardRate != null || saleTimeCardCategoryHandlerAddForm.SavingCardFixed != null ||
                        saleTimeCardCategoryHandlerAddForm.SavingCardLargessRate != null || saleTimeCardCategoryHandlerAddForm.SavingCardLargessFixed != null ||
                        saleTimeCardCategoryHandlerAddForm.SpecialBenefit != null) {
                    SaleTimeCardCategoryHandlerPerformanceEntity saleTimeCardCategoryHandlerPerformanceEntity = new SaleTimeCardCategoryHandlerPerformanceEntity();
                    saleTimeCardCategoryHandlerPerformanceEntity.EntityID = form.EntityID;
                    saleTimeCardCategoryHandlerPerformanceEntity.TimeCardCategoryID = form.CategoryID;
                    saleTimeCardCategoryHandlerPerformanceEntity.TimeCardSaleHandlerID = saleTimeCardCategoryHandlerAddForm.SaleHandlerID;
                    saleTimeCardCategoryHandlerPerformanceEntity.PayRate = saleTimeCardCategoryHandlerAddForm.PayRate;
                    saleTimeCardCategoryHandlerPerformanceEntity.SavingCardRate = saleTimeCardCategoryHandlerAddForm.SavingCardRate;
                    saleTimeCardCategoryHandlerPerformanceEntity.SavingCardLargessRate = saleTimeCardCategoryHandlerAddForm.SavingCardLargessRate;
                    list.add(saleTimeCardCategoryHandlerPerformanceEntity);
                }
                if (saleTimeCardCategoryHandlerAddForm.HandlerJobTypeCommission != null && saleTimeCardCategoryHandlerAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (SaleTimeCardCategoryHandlerJobTypeCommissionAddForm saleTimeCardCategoryHandlerJobTypePerformanceAddForm : saleTimeCardCategoryHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleTimeCardCategoryHandlerJobTypePerformanceAddForm.PayRate != null ||
                                saleTimeCardCategoryHandlerJobTypePerformanceAddForm.SavingCardRate != null ||
                                saleTimeCardCategoryHandlerJobTypePerformanceAddForm.SavingCardLargessRate != null) {
                            SaleTimeCardCategoryHandlerJobTypePerformanceEntity saleTimeCardCategoryHandlerJobTypePerformanceEntity = new SaleTimeCardCategoryHandlerJobTypePerformanceEntity();
                            saleTimeCardCategoryHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            saleTimeCardCategoryHandlerJobTypePerformanceEntity.TimeCardCategoryID = form.CategoryID;
                            saleTimeCardCategoryHandlerJobTypePerformanceEntity.TimeCardSaleHandlerID = saleTimeCardCategoryHandlerAddForm.SaleHandlerID;
                            saleTimeCardCategoryHandlerJobTypePerformanceEntity.JobTypeID = saleTimeCardCategoryHandlerJobTypePerformanceAddForm.JobTypeID;
                            saleTimeCardCategoryHandlerJobTypePerformanceEntity.PayRate = saleTimeCardCategoryHandlerJobTypePerformanceAddForm.PayRate;
                            saleTimeCardCategoryHandlerJobTypePerformanceEntity.SavingCardRate = saleTimeCardCategoryHandlerJobTypePerformanceAddForm.SavingCardRate;
                            saleTimeCardCategoryHandlerJobTypePerformanceEntity.SavingCardLargessRate = saleTimeCardCategoryHandlerJobTypePerformanceAddForm.SavingCardLargessRate;
                            handlerJobTypePerformanceList.add(saleTimeCardCategoryHandlerJobTypePerformanceEntity);
                        }
                    }
                }
            }
        }

        salePackageCardTimeCardCategoryHandlerPerformanceMapper.deleteSalePackageCardTimeCardCategoryHandler(form.EntityID, form.CategoryID);
        salePackageCardTimeCardCategoryHandlerPerformanceMapper.deleteSalePackageCardTimeCardCategoryHandlerJobType(form.EntityID, form.CategoryID);
        List packageList = new ArrayList();
        List packageHandlerJobTypePerformanceList = new ArrayList<>();
        if (form.PackageCardTimeCardHandler != null && form.PackageCardTimeCardHandler.size() > 0) {
            for (SaleTimeCardCategoryHandlerAddForm saleTimeCardCategoryHandlerAddForm : form.PackageCardTimeCardHandler) {
                if (saleTimeCardCategoryHandlerAddForm.PayRate != null || saleTimeCardCategoryHandlerAddForm.PayFixed != null ||
                        saleTimeCardCategoryHandlerAddForm.SavingCardRate != null || saleTimeCardCategoryHandlerAddForm.SavingCardFixed != null ||
                        saleTimeCardCategoryHandlerAddForm.SavingCardLargessRate != null || saleTimeCardCategoryHandlerAddForm.SavingCardLargessFixed != null ||
                        saleTimeCardCategoryHandlerAddForm.SpecialBenefit != null) {
                    SalePackageCardTimeCardCategoryHandlerPerformanceEntity salePackageCardTimeCardCategoryHandlerPerformanceEntity = new SalePackageCardTimeCardCategoryHandlerPerformanceEntity();
                    salePackageCardTimeCardCategoryHandlerPerformanceEntity.EntityID = form.EntityID;
                    salePackageCardTimeCardCategoryHandlerPerformanceEntity.TimeCardCategoryID = form.CategoryID;
                    salePackageCardTimeCardCategoryHandlerPerformanceEntity.PackageCardSaleHandlerID = saleTimeCardCategoryHandlerAddForm.SaleHandlerID;
                    salePackageCardTimeCardCategoryHandlerPerformanceEntity.PayRate = saleTimeCardCategoryHandlerAddForm.PayRate;
                    salePackageCardTimeCardCategoryHandlerPerformanceEntity.SavingCardRate = saleTimeCardCategoryHandlerAddForm.SavingCardRate;
                    salePackageCardTimeCardCategoryHandlerPerformanceEntity.SavingCardLargessRate = saleTimeCardCategoryHandlerAddForm.SavingCardLargessRate;
                    packageList.add(salePackageCardTimeCardCategoryHandlerPerformanceEntity);
                }
                if (saleTimeCardCategoryHandlerAddForm.HandlerJobTypeCommission != null && saleTimeCardCategoryHandlerAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (SaleTimeCardCategoryHandlerJobTypeCommissionAddForm saleTimeCardCategoryHandlerJobTypePerformanceAddForm : saleTimeCardCategoryHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleTimeCardCategoryHandlerJobTypePerformanceAddForm.PayRate != null || saleTimeCardCategoryHandlerJobTypePerformanceAddForm.PayFixed != null ||
                                saleTimeCardCategoryHandlerJobTypePerformanceAddForm.SavingCardRate != null || saleTimeCardCategoryHandlerJobTypePerformanceAddForm.SavingCardFixed != null ||
                                saleTimeCardCategoryHandlerJobTypePerformanceAddForm.SavingCardLargessRate != null || saleTimeCardCategoryHandlerJobTypePerformanceAddForm.SavingCardLargessFixed != null ||
                                saleTimeCardCategoryHandlerJobTypePerformanceAddForm.SpecialBenefit != null) {
                            SalePackageCardTimeCardCategoryHandlerJobTypePerformanceEntity salePackageCardTimeCardCategoryHandlerJobTypePerformanceEntity = new SalePackageCardTimeCardCategoryHandlerJobTypePerformanceEntity();
                            salePackageCardTimeCardCategoryHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            salePackageCardTimeCardCategoryHandlerJobTypePerformanceEntity.TimeCardCategoryID = form.CategoryID;
                            salePackageCardTimeCardCategoryHandlerJobTypePerformanceEntity.PackageCardSaleHandlerID = saleTimeCardCategoryHandlerAddForm.SaleHandlerID;
                            salePackageCardTimeCardCategoryHandlerJobTypePerformanceEntity.JobTypeID = saleTimeCardCategoryHandlerJobTypePerformanceAddForm.JobTypeID;
                            salePackageCardTimeCardCategoryHandlerJobTypePerformanceEntity.PayRate = saleTimeCardCategoryHandlerJobTypePerformanceAddForm.PayRate;
                            salePackageCardTimeCardCategoryHandlerJobTypePerformanceEntity.SavingCardRate = saleTimeCardCategoryHandlerJobTypePerformanceAddForm.SavingCardRate;
                            salePackageCardTimeCardCategoryHandlerJobTypePerformanceEntity.SavingCardLargessRate = saleTimeCardCategoryHandlerJobTypePerformanceAddForm.SavingCardLargessRate;
                            packageHandlerJobTypePerformanceList.add(salePackageCardTimeCardCategoryHandlerJobTypePerformanceEntity);
                        }
                    }
                }
            }
        }
        if (list != null && list.size() > 0) {
            List<List<SaleTimeCardCategoryHandlerPerformanceEntity>> subs = Lists.partition(list, 20);
            for (List<SaleTimeCardCategoryHandlerPerformanceEntity> entityList : subs) {
                saleTimeCardCategoryHandlerPerformanceMapper.insertSaleTimeCardCategoryHandlerPerformance(entityList);
            }
        }
        if (handlerJobTypePerformanceList != null && handlerJobTypePerformanceList.size() > 0) {
            List<List<SaleTimeCardCategoryHandlerJobTypePerformanceEntity>> subs = Lists.partition(handlerJobTypePerformanceList, 20);
            for (List<SaleTimeCardCategoryHandlerJobTypePerformanceEntity> entityList : subs) {
                saleTimeCardCategoryHandlerPerformanceMapper.insertSaleTimeCardCategoryHandlerJobTypePerformance(entityList);
            }
        }
        if (packageList != null && packageList.size() > 0) {
            List<List<SalePackageCardTimeCardCategoryHandlerPerformanceEntity>> subs = Lists.partition(packageList, 20);
            for (List<SalePackageCardTimeCardCategoryHandlerPerformanceEntity> entityList : subs) {
                salePackageCardTimeCardCategoryHandlerPerformanceMapper.insertSalePackageCardTimeCardCategoryHandlerPerformance(entityList);
            }
        }
        if (packageHandlerJobTypePerformanceList != null && packageHandlerJobTypePerformanceList.size() > 0) {
            List<List<SalePackageCardTimeCardCategoryHandlerJobTypePerformanceEntity>> subs = Lists.partition(packageHandlerJobTypePerformanceList, 20);
            for (List<SalePackageCardTimeCardCategoryHandlerJobTypePerformanceEntity> entityList : subs) {
                salePackageCardTimeCardCategoryHandlerPerformanceMapper.insertSalePackageCardTimeCardCategoryHandlerJobTypePerformance(entityList);
            }
        }

        return BaseOutput.success();
    }
}