package com.zhelian.service.khs.treatProductPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.TreatProductHandlerPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatProductHandlersCommissionAddForm;
import com.zhelian.model.khs.entity.TreatProductHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.TreatProductHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.TreatGoodCategoryHandlerPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatProductHandlerPerformanceService {

    @Autowired
    TreatProductHandlerPerformanceMapper treatProductHandlerPerformanceMapper;

    public BaseOutput allTreatProductHandlerPerformance(TreatProductHandlerCommissionQueryForm form) {
        List<TreatGoodCategoryHandlerPerformanceOutputForm> list = treatProductHandlerPerformanceMapper.allTreatProductHandlerPerformance(form.EntityID, form.ProductID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatProductHandlerPerformance(TreatProductHandlersCommissionAddForm form) {

        treatProductHandlerPerformanceMapper.deleteTreatProductHandler(form.EntityID, form.ProductID);
        treatProductHandlerPerformanceMapper.deleteTreatProductHandlerJobType(form.EntityID, form.ProductID);

        if (form.Handler != null && form.Handler.size() > 0) {

            List<TreatProductHandlerPerformanceEntity> listTreatProductHandlerPerformanceEntity = new ArrayList<>();

            List<TreatProductHandlerJobTypePerformanceEntity> listTreatProductHandlerJobTypePerformanceEntity = new ArrayList<>();

            for (TreatProductHandlerCommissionAddForm treatProductHandlerAddForm : form.Handler) {
                if (treatProductHandlerAddForm.PayRate != null ||
                        treatProductHandlerAddForm.CardRate != null ||
                        treatProductHandlerAddForm.CardLargessRate != null ||
                        treatProductHandlerAddForm.LargessRate != null) {
                    TreatProductHandlerPerformanceEntity treatProductHandlerPerformanceEntity = new TreatProductHandlerPerformanceEntity();
                    treatProductHandlerPerformanceEntity.EntityID = form.EntityID;
                    treatProductHandlerPerformanceEntity.ProductID = form.ProductID;
                    treatProductHandlerPerformanceEntity.ProductTreatHandlerID = treatProductHandlerAddForm.TreatHandlerID;
                    treatProductHandlerPerformanceEntity.PayRate = treatProductHandlerAddForm.PayRate;
                    treatProductHandlerPerformanceEntity.CardRate = treatProductHandlerAddForm.CardRate;
                    treatProductHandlerPerformanceEntity.CardLargessRate = treatProductHandlerAddForm.CardLargessRate;
                    treatProductHandlerPerformanceEntity.LargessRate = treatProductHandlerAddForm.LargessRate;
                    listTreatProductHandlerPerformanceEntity.add(treatProductHandlerPerformanceEntity);
                }
                if (treatProductHandlerAddForm.HandlerJobTypeCommission != null && treatProductHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (TreatProductCategoryHandlerJobTypeCommissionAddForm treatProductCategoryHandlerJobTypePerformanceAddForm : treatProductHandlerAddForm.HandlerJobTypeCommission) {
                        if (treatProductCategoryHandlerJobTypePerformanceAddForm.PayRate != null ||
                                treatProductCategoryHandlerJobTypePerformanceAddForm.LargessRate != null ||
                                treatProductCategoryHandlerJobTypePerformanceAddForm.CardLargessRate != null ||
                                treatProductCategoryHandlerJobTypePerformanceAddForm.LargessRate != null) {
                            TreatProductHandlerJobTypePerformanceEntity treatProductHandlerJobTypePerformanceEntity = new TreatProductHandlerJobTypePerformanceEntity();
                            treatProductHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            treatProductHandlerJobTypePerformanceEntity.ProductID = form.ProductID;
                            treatProductHandlerJobTypePerformanceEntity.ProductTreatHandlerID = treatProductHandlerAddForm.TreatHandlerID;
                            treatProductHandlerJobTypePerformanceEntity.JobTypeID = treatProductCategoryHandlerJobTypePerformanceAddForm.JobTypeID;
                            treatProductHandlerJobTypePerformanceEntity.PayRate = treatProductCategoryHandlerJobTypePerformanceAddForm.PayRate;
                            treatProductHandlerJobTypePerformanceEntity.CardRate = treatProductCategoryHandlerJobTypePerformanceAddForm.CardRate;
                            treatProductHandlerJobTypePerformanceEntity.CardLargessRate = treatProductCategoryHandlerJobTypePerformanceAddForm.CardLargessRate;
                            treatProductHandlerJobTypePerformanceEntity.LargessRate = treatProductCategoryHandlerJobTypePerformanceAddForm.LargessRate;
                            listTreatProductHandlerJobTypePerformanceEntity.add(treatProductHandlerJobTypePerformanceEntity);
                        }
                    }
                }
            }

            if (listTreatProductHandlerPerformanceEntity != null && listTreatProductHandlerPerformanceEntity.size() > 0) {
                List<List<TreatProductHandlerPerformanceEntity>> subs = Lists.partition(listTreatProductHandlerPerformanceEntity, 20);
                for (List<TreatProductHandlerPerformanceEntity> entityList : subs) {
                    treatProductHandlerPerformanceMapper.updateTreatProductHandlerPerformance(entityList);
                }
            }
            if (listTreatProductHandlerJobTypePerformanceEntity != null && listTreatProductHandlerJobTypePerformanceEntity.size() > 0) {
                List<List<TreatProductHandlerJobTypePerformanceEntity>> subs = Lists.partition(listTreatProductHandlerJobTypePerformanceEntity, 20);
                for (List<TreatProductHandlerJobTypePerformanceEntity> entityList : subs) {
                    treatProductHandlerPerformanceMapper.insertTreatProductHandlerJobTypePerformance(entityList);
                }
            }
        }
        return BaseOutput.success();
    }
}