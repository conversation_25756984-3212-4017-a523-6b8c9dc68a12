package com.zhelian.service.khs.channelSalarySettlement;


import com.zhelian.mapper.khs.ChannelPerformanceSchemeSavingCardMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.khs.entity.SaleSavingCardPerformanceEntity;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodAddForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodQueryForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodsAddForm;
import com.zhelian.model.khs.form.output.PerformanceSchemeSavingCarOutForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class ChannelPerformanceSchemeSavingCardService {

    @Autowired
    ChannelPerformanceSchemeSavingCardMapper channelPerformanceSchemeSavingCardMapper;

    public BaseOutput all(PerformanceSchemeGoodQueryForm form) {
        List<PerformanceSchemeSavingCarOutForm> list = channelPerformanceSchemeSavingCardMapper.getAllPerformanceSchemeSavingCard(form.PerformanceSchemeID, form.CategoryID);
        return BaseOutputForm.success(list);

    }

    @Transactional
    public BaseOutput create(PerformanceSchemeGoodsAddForm form) {
        channelPerformanceSchemeSavingCardMapper.deleteSaleSavingCardPerformanceByPerformanceSchemeIDAndCategory(form.PerformanceSchemeID, form.CategoryID);
        List<SaleSavingCardPerformanceEntity> list = new ArrayList<>();
        if (form.Good != null && form.Good.size() > 0) {
            for (PerformanceSchemeGoodAddForm performanceSchemeGoodAddForm : form.Good) {
                if (performanceSchemeGoodAddForm.PayPerformanceRate != null || performanceSchemeGoodAddForm.SavingCardPerformanceRate != null ||
                        performanceSchemeGoodAddForm.SavingCardPerformanceLargessRate != null) {
                    SaleSavingCardPerformanceEntity saleSavingCardPerformanceEntity = new SaleSavingCardPerformanceEntity();
                    saleSavingCardPerformanceEntity.PerformanceSchemeID = form.PerformanceSchemeID;
                    saleSavingCardPerformanceEntity.SavingCardID = performanceSchemeGoodAddForm.GoodID;
                    saleSavingCardPerformanceEntity.PayPerformanceRate = performanceSchemeGoodAddForm.PayPerformanceRate;
                    list.add(saleSavingCardPerformanceEntity);
                }
            }
            channelPerformanceSchemeSavingCardMapper.insertSaleSavingCardPerformance(list);
        }
        return BaseOutput.success();
    }
}
