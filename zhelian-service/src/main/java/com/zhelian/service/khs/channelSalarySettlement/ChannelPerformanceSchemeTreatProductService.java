package com.zhelian.service.khs.channelSalarySettlement;


import com.zhelian.mapper.khs.ChannelPerformanceSchemeTreatProductMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.khs.entity.TreatChannelGoodPerformanceEntity;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodQueryForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeTreatGoodAddForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeTreatGoodsAddForm;
import com.zhelian.model.khs.form.output.PerformanceSchemeTreatGoodOutForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class ChannelPerformanceSchemeTreatProductService {

    @Autowired
    ChannelPerformanceSchemeTreatProductMapper channelPerformanceSchemeTreatProductMapper;

    public BaseOutput all(PerformanceSchemeGoodQueryForm form) {
        List<PerformanceSchemeTreatGoodOutForm> list = channelPerformanceSchemeTreatProductMapper.getAllPerformanceSchemeProduct(form.PerformanceSchemeID, form.CategoryID);
        return BaseOutputForm.success(list);

    }

    @Transactional
    public BaseOutput create(PerformanceSchemeTreatGoodsAddForm form) {
        channelPerformanceSchemeTreatProductMapper.deleteTreatProductPerformanceByPerformanceSchemeIDAndCategory(form.PerformanceSchemeID, form.CategoryID);
        List<TreatChannelGoodPerformanceEntity> list = new ArrayList<>();
        if (form.Good != null && form.Good.size() > 0) {
            for (PerformanceSchemeTreatGoodAddForm performanceSchemeGoodAddForm : form.Good) {
                if (performanceSchemeGoodAddForm.PerformancePayRate != null || performanceSchemeGoodAddForm.PerformanceCardRate != null || performanceSchemeGoodAddForm.PerformanceLargessRate != null || performanceSchemeGoodAddForm.PerformanceLargessRate != null) {
                    TreatChannelGoodPerformanceEntity TreatProductCommissionEntity = new TreatChannelGoodPerformanceEntity();
                    TreatProductCommissionEntity.ChannelPerformanceSchemeID = form.PerformanceSchemeID;
                    TreatProductCommissionEntity.GoodID = performanceSchemeGoodAddForm.GoodID;
                    TreatProductCommissionEntity.PerformancePayRate = performanceSchemeGoodAddForm.PerformancePayRate;
                    TreatProductCommissionEntity.PerformanceCardRate = performanceSchemeGoodAddForm.PerformanceCardRate;
                    TreatProductCommissionEntity.PerformanceCardLargessRate = performanceSchemeGoodAddForm.PerformanceCardLargessRate;
                    TreatProductCommissionEntity.PerformanceLargessRate = performanceSchemeGoodAddForm.PerformanceLargessRate;
                    list.add(TreatProductCommissionEntity);
                }
            }
            channelPerformanceSchemeTreatProductMapper.insertTreatProductPerformance(list);
        }
        return BaseOutput.success();
    }
}
