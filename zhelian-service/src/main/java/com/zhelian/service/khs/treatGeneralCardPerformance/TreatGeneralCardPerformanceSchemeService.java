package com.zhelian.service.khs.treatGeneralCardPerformance;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.khs.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.form.input.TreatProjectCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCommissionSchemeDeleteForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCommissionSchemeQueryForm;
import com.zhelian.model.ibeauty.form.output.TreatProjectCommissionSchemeOutputForm;
import com.zhelian.model.khs.entity.TreatGeneralCardPerformanceSchemeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Service
public class TreatGeneralCardPerformanceSchemeService {

    @Autowired
    TreatGeneralCardPerformanceSchemeMapper treatGeneralCardPerformanceSchemeMapper;
    @Autowired
    TreatGeneralCardCategoryPerformanceMapper treatGeneralCardCategoryPerformanceMapper;
    @Autowired
    TreatGeneralCardCategoryHandlerPerformanceMapper treatGeneralCardCategoryHandlerPerformanceMapper;
    @Autowired
    TreatGeneralCardPerformanceMapper treatGeneralCardPerformanceMapper;
    @Autowired
    TreatGeneralCardHandlerPerformanceMapper treatGeneralCardHandlerPerformanceMapper;

    @Autowired
    TreatGeneralCardSchemeHandlerPerformanceMapper treatGeneralCardSchemeHandlerPerformanceMapper;


    public BaseOutput treatGeneralCardPerformanceSchemeList(TreatProjectCommissionSchemeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<TreatProjectCommissionSchemeOutputForm> list = treatGeneralCardPerformanceSchemeMapper.treatGeneralCardPerformanceSchemeList(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput createTreatGeneralCardPerformanceScheme(TreatProjectCommissionSchemeAddForm form) {
        Integer count = treatGeneralCardPerformanceSchemeMapper.getTreatGeneralCardPerformanceScheme(form.EntityID);
        if (count > 0)
            return BaseOutput.failed("组织单位已经存在");
        TreatGeneralCardPerformanceSchemeEntity treatGeneralCardProjectPerformanceSchemeEntity = new TreatGeneralCardPerformanceSchemeEntity();
        treatGeneralCardProjectPerformanceSchemeEntity.EntityID = form.EntityID;
        treatGeneralCardPerformanceSchemeMapper.createTreatGeneralCardPerformanceScheme(treatGeneralCardProjectPerformanceSchemeEntity);
        return BaseOutput.success();

    }

    @Transactional
    public BaseOutput deleteTreatGeneralCardPerformanceScheme(TreatProjectCommissionSchemeDeleteForm form) {
        treatGeneralCardPerformanceSchemeMapper.deleteTreatGeneralCardPerformanceScheme(form.EntityID);
        treatGeneralCardCategoryPerformanceMapper.deleteTreatGeneralCardCategoryPerformance(form.EntityID);
        treatGeneralCardCategoryHandlerPerformanceMapper.deleteTreatGeneralCardCategoryHandlerByEntityID(form.EntityID);
        treatGeneralCardCategoryHandlerPerformanceMapper.deleteTreatGeneralCardCategoryHandlerJobTypeByEntityID(form.EntityID);
        treatGeneralCardPerformanceMapper.deleteTreatGeneralCardPerformanceByEntityID(form.EntityID);
        treatGeneralCardHandlerPerformanceMapper.deleteTreatGeneralCardHandlerPerformanceByEntityID(form.EntityID);
        treatGeneralCardHandlerPerformanceMapper.deleteTreatGeneralCardHandlerJobTypePerformanceByEntityID(form.EntityID);
        treatGeneralCardSchemeHandlerPerformanceMapper.deleteTreatGeneralCardSchemeHandler(form.EntityID);
        treatGeneralCardSchemeHandlerPerformanceMapper.deleteTreatGeneralCardSchemeHandlerJobType(form.EntityID);
        return BaseOutput.success();
    }
}