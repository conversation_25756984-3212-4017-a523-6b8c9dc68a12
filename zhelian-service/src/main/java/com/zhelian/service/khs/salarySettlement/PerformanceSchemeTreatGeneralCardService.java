package com.zhelian.service.khs.salarySettlement;


import com.zhelian.mapper.khs.PerformanceSchemeTreatGeneralCardMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.khs.entity.TreatGoodPerformanceEntity;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodQueryForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeTreatGoodAddForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeTreatGoodsAddForm;
import com.zhelian.model.khs.form.output.PerformanceSchemeTreatGoodOutForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class PerformanceSchemeTreatGeneralCardService {

    @Autowired
    PerformanceSchemeTreatGeneralCardMapper performanceSchemeTreatGeneralCardMapper;

    public BaseOutput all(PerformanceSchemeGoodQueryForm form) {
        List<PerformanceSchemeTreatGoodOutForm> list = performanceSchemeTreatGeneralCardMapper.getAllPerformanceSchemeGeneralCard(form.PerformanceSchemeID, form.CategoryID);
        return BaseOutputForm.success(list);

    }

    @Transactional
    public BaseOutput create(PerformanceSchemeTreatGoodsAddForm form) {
        performanceSchemeTreatGeneralCardMapper.deleteTreatGeneralCardPerformanceByPerformanceSchemeIDAndCategory(form.PerformanceSchemeID, form.CategoryID);
        List<TreatGoodPerformanceEntity> list = new ArrayList<>();
        if (form.Good != null && form.Good.size() > 0) {
            for (PerformanceSchemeTreatGoodAddForm performanceSchemeGoodAddForm : form.Good) {
                if (performanceSchemeGoodAddForm.PerformancePayRate != null || performanceSchemeGoodAddForm.PerformanceCardRate != null || performanceSchemeGoodAddForm.PerformanceLargessRate != null || performanceSchemeGoodAddForm.PerformanceLargessRate != null) {
                    TreatGoodPerformanceEntity treatGoodPerformanceEntity = new TreatGoodPerformanceEntity();
                    treatGoodPerformanceEntity.PerformanceSchemeID = form.PerformanceSchemeID;
                    treatGoodPerformanceEntity.GoodID = performanceSchemeGoodAddForm.GoodID;
                    treatGoodPerformanceEntity.PerformancePayRate = performanceSchemeGoodAddForm.PerformancePayRate;
                    treatGoodPerformanceEntity.PerformanceCardRate = performanceSchemeGoodAddForm.PerformanceCardRate;
                    treatGoodPerformanceEntity.PerformanceCardLargessRate = performanceSchemeGoodAddForm.PerformanceCardLargessRate;
                    treatGoodPerformanceEntity.PerformanceLargessRate = performanceSchemeGoodAddForm.PerformanceLargessRate;
                    list.add(treatGoodPerformanceEntity);
                }
            }
            performanceSchemeTreatGeneralCardMapper.insertTreatGeneralCardPerformance(list);
        }
        return BaseOutput.success();
    }
}
