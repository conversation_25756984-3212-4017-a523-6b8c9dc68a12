package com.zhelian.service.khs.saleProjectPerformance;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.SaleProjectCategoryPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectChildCategoryCommissionAddForm;
import com.zhelian.model.khs.entity.SaleProjectPerformanceSchemeEntity;
import com.zhelian.model.khs.entity.TBSaleProjectCategoryPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleGoodCategoryOutputForm;
import com.zhelian.model.khs.form.output.SaleGoodCategoryPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import static com.zhelian.core.utils.ListUtil.listToTree;


@Service
public class SaleProjectCategoryPerformanceService {

    @Autowired
    SaleProjectCategoryPerformanceMapper saleProjectCategoryPerformanceMapper;


    public BaseOutput allSaleProjectCategoryPerformance(SaleProjectCategoryCommissionQueryForm form) {
        SaleGoodCategoryOutputForm saleProjectCategoryOutputForm = saleProjectCategoryPerformanceMapper.saleProjectCategoryPerformance(form.EntityID);
        List<SaleGoodCategoryPerformanceOutputForm> all = saleProjectCategoryPerformanceMapper.allSaleProjectCategoryPerformance(form.EntityID);
        JSONArray result = listToTree(JSONArray.parseArray(JSON.toJSONString(all, JSONWriter.Feature.WriteMapNullValue)), "CategoryID", "ParentID", "Child");
        saleProjectCategoryOutputForm.Category = result;
        return BaseOutputForm.success(saleProjectCategoryOutputForm);
    }

    @Transactional
    public BaseOutput updateSaleProjectCategoryPerformance(SaleProjectCategoryAddForm form) {
        SaleProjectPerformanceSchemeEntity saleProjectPerformanceSchemeEntity = new SaleProjectPerformanceSchemeEntity();
        saleProjectPerformanceSchemeEntity.EntityID = form.EntityID;
        saleProjectPerformanceSchemeEntity.PayRate = form.PayRate;
        saleProjectPerformanceSchemeEntity.SavingCardRate = form.SavingCardRate;
        saleProjectPerformanceSchemeEntity.SavingCardLargessRate = form.SavingCardLargessRate;

        saleProjectCategoryPerformanceMapper.updateSaleProjectPerformanceScheme(saleProjectPerformanceSchemeEntity);

        saleProjectCategoryPerformanceMapper.deleteSaleProjectCategoryPerformanceEntity(form.EntityID);


        List<TBSaleProjectCategoryPerformanceEntity> list = new ArrayList<>();

        if (form.Category != null && form.Category.size() > 0) {
            for (SaleProjectCategoryCommissionAddForm saleProjectCategoryPerformanceAddForm : form.Category) {
                if (saleProjectCategoryPerformanceAddForm.PayRate != null ||
                        saleProjectCategoryPerformanceAddForm.SavingCardRate != null ||
                        saleProjectCategoryPerformanceAddForm.SavingCardLargessRate != null) {
                    TBSaleProjectCategoryPerformanceEntity saleProjectCategoryPerformanceEntity = new TBSaleProjectCategoryPerformanceEntity();
                    saleProjectCategoryPerformanceEntity.EntityID = form.EntityID;
                    saleProjectCategoryPerformanceEntity.ProjectCategoryID = saleProjectCategoryPerformanceAddForm.CategoryID;
                    saleProjectCategoryPerformanceEntity.PayRate = saleProjectCategoryPerformanceAddForm.PayRate;
                    saleProjectCategoryPerformanceEntity.SavingCardRate = saleProjectCategoryPerformanceAddForm.SavingCardRate;
                    saleProjectCategoryPerformanceEntity.SavingCardLargessRate = saleProjectCategoryPerformanceAddForm.SavingCardLargessRate;
                    list.add(saleProjectCategoryPerformanceEntity);
                }
                if (saleProjectCategoryPerformanceAddForm.Child != null && saleProjectCategoryPerformanceAddForm.Child.size() > 0) {

                    for (SaleProjectChildCategoryCommissionAddForm saleProjectChildCategoryPerformanceAddForm : saleProjectCategoryPerformanceAddForm.Child) {
                        if (saleProjectChildCategoryPerformanceAddForm.PayRate != null ||
                                saleProjectChildCategoryPerformanceAddForm.SavingCardRate != null ||
                                saleProjectChildCategoryPerformanceAddForm.SavingCardLargessRate != null) {
                            TBSaleProjectCategoryPerformanceEntity tbSaleProjectCategoryPerformanceEntity = new TBSaleProjectCategoryPerformanceEntity();
                            tbSaleProjectCategoryPerformanceEntity.EntityID = form.EntityID;
                            tbSaleProjectCategoryPerformanceEntity.ProjectCategoryID = saleProjectChildCategoryPerformanceAddForm.CategoryID;
                            tbSaleProjectCategoryPerformanceEntity.PayRate = saleProjectChildCategoryPerformanceAddForm.PayRate;
                            tbSaleProjectCategoryPerformanceEntity.SavingCardRate = saleProjectChildCategoryPerformanceAddForm.SavingCardRate;
                            tbSaleProjectCategoryPerformanceEntity.SavingCardLargessRate = saleProjectChildCategoryPerformanceAddForm.SavingCardLargessRate;
                            list.add(tbSaleProjectCategoryPerformanceEntity);
                        }
                    }
                }
            }
        }
        if (list != null && list.size() > 0) {
            List<List<TBSaleProjectCategoryPerformanceEntity>> subs = Lists.partition(list, 20);
            for (List<TBSaleProjectCategoryPerformanceEntity> entityList : subs) {
                saleProjectCategoryPerformanceMapper.insertSaleProjectCategoryPerformanceEntity(entityList);
            }
        }

        return BaseOutput.success();
    }

}