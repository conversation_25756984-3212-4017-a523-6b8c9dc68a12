package com.zhelian.service.khs.treatProjectEntityPerformance;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.TreatProjectCategoryEntityPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCategoryCommissionQueryForm;
import com.zhelian.model.khs.entity.TreatGoodCategoryEntityPerformanceEntity;
import com.zhelian.model.khs.entity.TreatGoodEntityPerformanceSchemeEntity;
import com.zhelian.model.khs.form.input.TreatGoodCategoryAddForm;
import com.zhelian.model.khs.form.input.TreatGoodCategoryPerformanceAddForm;
import com.zhelian.model.khs.form.input.TreatGoodChildCategoryPerformanceAddForm;
import com.zhelian.model.khs.form.output.TreatGoodCategoryOutputForm;
import com.zhelian.model.khs.form.output.TreatGoodCategoryPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import static com.zhelian.core.utils.ListUtil.listToTree;


@Service
public class TreatProjectCategoryEntityPerformanceService {

    @Autowired
    TreatProjectCategoryEntityPerformanceMapper treatProjectCategoryEntityPerformanceMapper;


    public BaseOutput allTreatProjectCategoryEntityPerformance(TreatProjectCategoryCommissionQueryForm form) {
        TreatGoodCategoryOutputForm treatProjectCategoryOutputForm = treatProjectCategoryEntityPerformanceMapper.treatProjectCategoryEntityPerformance(form.EntityID);
        List<TreatGoodCategoryPerformanceOutputForm> all = treatProjectCategoryEntityPerformanceMapper.allTreatProjectCategoryEntityPerformance(form.EntityID);
        JSONArray result = listToTree(JSONArray.parseArray(JSON.toJSONString(all, JSONWriter.Feature.WriteMapNullValue)), "CategoryID", "ParentID", "Child");
        treatProjectCategoryOutputForm.Category = result;
        return BaseOutputForm.success(treatProjectCategoryOutputForm);
    }

    @Transactional
    public BaseOutput updateTreatProjectCategoryEntityPerformance(TreatGoodCategoryAddForm form) {
        TreatGoodEntityPerformanceSchemeEntity treatProjectEntityPerformanceSchemeEntity = new TreatGoodEntityPerformanceSchemeEntity();
        treatProjectEntityPerformanceSchemeEntity.EntityID = form.EntityID;
        treatProjectEntityPerformanceSchemeEntity.PayRate = form.PayRate;
        treatProjectEntityPerformanceSchemeEntity.CardRate = form.CardRate;
        treatProjectEntityPerformanceSchemeEntity.CardLargessRate = form.CardLargessRate;
        treatProjectEntityPerformanceSchemeEntity.LargessRate = form.LargessRate;

        treatProjectCategoryEntityPerformanceMapper.updateTreatProjectEntityPerformanceScheme(treatProjectEntityPerformanceSchemeEntity);

        treatProjectCategoryEntityPerformanceMapper.deleteTreatProjectCategoryEntityPerformanceEntity(form.EntityID);

        List<TreatGoodCategoryEntityPerformanceEntity> list = new ArrayList<>();
        if (form.Category != null && form.Category.size() > 0) {
            for (TreatGoodCategoryPerformanceAddForm treatProjectCategoryEntityPerformanceAddForm : form.Category) {
                if (treatProjectCategoryEntityPerformanceAddForm.PayRate != null ||
                        treatProjectCategoryEntityPerformanceAddForm.CardRate != null ||
                        treatProjectCategoryEntityPerformanceAddForm.CardLargessRate != null ||
                        treatProjectCategoryEntityPerformanceAddForm.LargessRate != null) {
                    TreatGoodCategoryEntityPerformanceEntity treatProjectCategoryEntityPerformanceEntity = new TreatGoodCategoryEntityPerformanceEntity();
                    treatProjectCategoryEntityPerformanceEntity.EntityID = form.EntityID;
                    treatProjectCategoryEntityPerformanceEntity.GoodCategoryID = treatProjectCategoryEntityPerformanceAddForm.CategoryID;
                    treatProjectCategoryEntityPerformanceEntity.PayRate = treatProjectCategoryEntityPerformanceAddForm.PayRate;
                    treatProjectCategoryEntityPerformanceEntity.CardRate = treatProjectCategoryEntityPerformanceAddForm.CardRate;
                    treatProjectCategoryEntityPerformanceEntity.CardLargessRate = treatProjectCategoryEntityPerformanceAddForm.CardLargessRate;
                    treatProjectCategoryEntityPerformanceEntity.LargessRate = treatProjectCategoryEntityPerformanceAddForm.LargessRate;
                    list.add(treatProjectCategoryEntityPerformanceEntity);
                }

                for (TreatGoodChildCategoryPerformanceAddForm treatProjectChildCategoryEntityPerformanceAddForm : treatProjectCategoryEntityPerformanceAddForm.Child) {
                    if (treatProjectChildCategoryEntityPerformanceAddForm.PayRate != null ||
                            treatProjectChildCategoryEntityPerformanceAddForm.CardRate != null ||
                            treatProjectChildCategoryEntityPerformanceAddForm.CardLargessRate != null ||
                            treatProjectChildCategoryEntityPerformanceAddForm.LargessRate != null) {
                        TreatGoodCategoryEntityPerformanceEntity treatProjectCategoryEntityPerformanceEntity2 = new TreatGoodCategoryEntityPerformanceEntity();
                        treatProjectCategoryEntityPerformanceEntity2.EntityID = form.EntityID;
                        treatProjectCategoryEntityPerformanceEntity2.GoodCategoryID = treatProjectChildCategoryEntityPerformanceAddForm.CategoryID;
                        treatProjectCategoryEntityPerformanceEntity2.PayRate = treatProjectChildCategoryEntityPerformanceAddForm.PayRate;
                        treatProjectCategoryEntityPerformanceEntity2.CardRate = treatProjectChildCategoryEntityPerformanceAddForm.CardRate;
                        treatProjectCategoryEntityPerformanceEntity2.CardLargessRate = treatProjectChildCategoryEntityPerformanceAddForm.CardLargessRate;
                        treatProjectCategoryEntityPerformanceEntity2.LargessRate = treatProjectChildCategoryEntityPerformanceAddForm.LargessRate;
                        list.add(treatProjectCategoryEntityPerformanceEntity2);
                    }
                }
            }
        }
        if (list != null && list.size() > 0) {
            List<List<TreatGoodCategoryEntityPerformanceEntity>> subs = Lists.partition(list, 20);
            for (List<TreatGoodCategoryEntityPerformanceEntity> entityList : subs) {
                treatProjectCategoryEntityPerformanceMapper.insertTreatProjectCategoryEntityPerformanceEntity(entityList);
            }
        }
        return BaseOutput.success();
    }

}