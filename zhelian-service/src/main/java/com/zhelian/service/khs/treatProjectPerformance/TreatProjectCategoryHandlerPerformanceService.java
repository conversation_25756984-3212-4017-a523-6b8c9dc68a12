package com.zhelian.service.khs.treatProjectPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.TreatProjectCategoryHandlerPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCategoryHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.khs.entity.TreatProjectCategoryHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.TreatProjectCategoryHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.TreatGoodCategoryHandlerPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatProjectCategoryHandlerPerformanceService {

    @Autowired
    TreatProjectCategoryHandlerPerformanceMapper treatProjectCategoryHandlerPerformanceMapper;


    public BaseOutput allTreatProjectCategoryHandlerPerformance(TreatProjectCategoryHandlerCommissionQueryForm form) {
        List<TreatGoodCategoryHandlerPerformanceOutputForm> list = treatProjectCategoryHandlerPerformanceMapper.allTreatProjectCategoryHandlerPerformance(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatProjectCategoryHandlerPerformance(TreatProjectCategoryHandlerCommissionAddForm form) {


        treatProjectCategoryHandlerPerformanceMapper.deleteTreatProjectCategoryHandler(form.EntityID, form.CategoryID);
        treatProjectCategoryHandlerPerformanceMapper.deleteTreatProjectCategoryHandlerJobType(form.EntityID, form.CategoryID);

        if (form.Handler != null && form.Handler.size() > 0) {

            List<TreatProjectCategoryHandlerPerformanceEntity> listTreatProjectCategoryHandlerPerformance = new ArrayList<>();

            List<TreatProjectCategoryHandlerJobTypePerformanceEntity> listTreatProjectCategoryHandlerJobTypePerformance = new ArrayList<>();


            for (TreatProjectCategoryHandlerAddForm treatProjectCategoryHandlerAddForm : form.Handler) {
                if (treatProjectCategoryHandlerAddForm.PayRate != null || treatProjectCategoryHandlerAddForm.PayFixed != null ||
                        treatProjectCategoryHandlerAddForm.CardRate != null || treatProjectCategoryHandlerAddForm.CardFixed != null ||
                        treatProjectCategoryHandlerAddForm.CardLargessRate != null || treatProjectCategoryHandlerAddForm.CardLargessFixed != null ||
                        treatProjectCategoryHandlerAddForm.LargessRate != null || treatProjectCategoryHandlerAddForm.LargessFixed != null ||
                        treatProjectCategoryHandlerAddForm.SpecialBenefit != null) {
                    TreatProjectCategoryHandlerPerformanceEntity treatProjectCategoryHandlerPerformanceEntity = new TreatProjectCategoryHandlerPerformanceEntity();
                    treatProjectCategoryHandlerPerformanceEntity.EntityID = form.EntityID;
                    treatProjectCategoryHandlerPerformanceEntity.ProjectCategoryID = form.CategoryID;
                    treatProjectCategoryHandlerPerformanceEntity.ProjectTreatHandlerID = treatProjectCategoryHandlerAddForm.TreatHandlerID;
                    treatProjectCategoryHandlerPerformanceEntity.PayRate = treatProjectCategoryHandlerAddForm.PayRate;
                    treatProjectCategoryHandlerPerformanceEntity.CardRate = treatProjectCategoryHandlerAddForm.CardRate;
                    treatProjectCategoryHandlerPerformanceEntity.CardLargessRate = treatProjectCategoryHandlerAddForm.CardLargessRate;
                    treatProjectCategoryHandlerPerformanceEntity.LargessRate = treatProjectCategoryHandlerAddForm.LargessRate;
                    listTreatProjectCategoryHandlerPerformance.add(treatProjectCategoryHandlerPerformanceEntity);
                }

                if (treatProjectCategoryHandlerAddForm.HandlerJobTypeCommission != null && treatProjectCategoryHandlerAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (TreatProjectCategoryHandlerJobTypeCommissionAddForm treatProjectCategoryHandlerJobTypePerformanceAddForm : treatProjectCategoryHandlerAddForm.HandlerJobTypeCommission) {
                        if (treatProjectCategoryHandlerJobTypePerformanceAddForm.PayRate != null ||
                                treatProjectCategoryHandlerJobTypePerformanceAddForm.CardRate != null ||
                                treatProjectCategoryHandlerJobTypePerformanceAddForm.CardLargessRate != null ||
                                treatProjectCategoryHandlerJobTypePerformanceAddForm.LargessRate != null) {
                            TreatProjectCategoryHandlerJobTypePerformanceEntity treatProjectCategoryHandlerJobTypePerformanceEntity = new TreatProjectCategoryHandlerJobTypePerformanceEntity();
                            treatProjectCategoryHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            treatProjectCategoryHandlerJobTypePerformanceEntity.ProjectCategoryID = form.CategoryID;
                            treatProjectCategoryHandlerJobTypePerformanceEntity.ProjectTreatHandlerID = treatProjectCategoryHandlerAddForm.TreatHandlerID;
                            treatProjectCategoryHandlerJobTypePerformanceEntity.JobTypeID = treatProjectCategoryHandlerJobTypePerformanceAddForm.JobTypeID;
                            treatProjectCategoryHandlerJobTypePerformanceEntity.PayRate = treatProjectCategoryHandlerJobTypePerformanceAddForm.PayRate;
                            treatProjectCategoryHandlerJobTypePerformanceEntity.CardRate = treatProjectCategoryHandlerJobTypePerformanceAddForm.CardRate;
                            treatProjectCategoryHandlerJobTypePerformanceEntity.CardLargessRate = treatProjectCategoryHandlerJobTypePerformanceAddForm.CardLargessRate;
                            treatProjectCategoryHandlerJobTypePerformanceEntity.LargessRate = treatProjectCategoryHandlerJobTypePerformanceAddForm.LargessRate;
                            listTreatProjectCategoryHandlerJobTypePerformance.add(treatProjectCategoryHandlerJobTypePerformanceEntity);
                        }
                    }
                }
            }
            if (listTreatProjectCategoryHandlerPerformance != null && listTreatProjectCategoryHandlerPerformance.size() > 0) {
                List<List<TreatProjectCategoryHandlerPerformanceEntity>> subs = Lists.partition(listTreatProjectCategoryHandlerPerformance, 20);
                for (List<TreatProjectCategoryHandlerPerformanceEntity> entityList : subs) {
                    treatProjectCategoryHandlerPerformanceMapper.updateTreatProjectCategoryHandlerPerformance(entityList);
                }
            }
            if (listTreatProjectCategoryHandlerJobTypePerformance != null && listTreatProjectCategoryHandlerJobTypePerformance.size() > 0) {
                List<List<TreatProjectCategoryHandlerJobTypePerformanceEntity>> subs = Lists.partition(listTreatProjectCategoryHandlerJobTypePerformance, 20);
                for (List<TreatProjectCategoryHandlerJobTypePerformanceEntity> entityList : subs) {
                    treatProjectCategoryHandlerPerformanceMapper.insertTreatProjectCategoryHandlerJobTypePerformance(entityList);
                }
            }

        }
        return BaseOutput.success();
    }
}