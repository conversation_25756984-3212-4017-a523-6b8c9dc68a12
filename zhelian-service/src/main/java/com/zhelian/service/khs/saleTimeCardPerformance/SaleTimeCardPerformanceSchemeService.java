package com.zhelian.service.khs.saleTimeCardPerformance;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.khs.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCommissionSchemeDeleteForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCommissionSchemeQueryForm;
import com.zhelian.model.ibeauty.form.output.SaleTimeCardCommissionSchemeOutputForm;
import com.zhelian.model.khs.entity.SaleTimeCardPerformanceSchemeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class SaleTimeCardPerformanceSchemeService {

    @Autowired
    SaleTimeCardPerformanceSchemeMapper saleTimeCardPerformanceSchemeMapper;
    @Autowired
    SaleTimeCardCategoryPerformanceMapper saleTimeCardCategoryPerformanceMapper;
    @Autowired
    SaleTimeCardCategoryHandlerPerformanceMapper saleTimeCardCategoryHandlerPerformanceMapper;
    @Autowired
    SalePackageCardTimeCardCategoryHandlerPerformanceMapper salePackageCardTimeCardCategoryHandlerPerformanceMapper;
    @Autowired
    SaleTimeCardPerformanceMapper saleTimeCardPerformanceMapper;
    @Autowired
    SaleTimeCardHandlerPerformanceMapper saleTimeCardHandlerPerformanceMapper;
    @Autowired
    SalePackageCardTimeCardHandlerPerformanceMapper salePackageCardTimeCardHandlerPerformanceMapper;
    @Autowired
    SaleTimeCardSchemeHandlerPerformanceMapper saleTimeCardSchemeHandlerPerformanceMapper;
    @Autowired
    SalePackageCardTimeCardSchemeHandlerPerformanceMapper salePackageCardTimeCardSchemeHandlerPerformanceMapper;

    public BaseOutput saleTimeCardPerformanceSchemeList(SaleTimeCardCommissionSchemeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleTimeCardCommissionSchemeOutputForm> list = saleTimeCardPerformanceSchemeMapper.saleTimeCardPerformanceSchemeList(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput createSaleTimeCardPerformanceScheme(SaleTimeCardCommissionSchemeAddForm form) {

        if (saleTimeCardPerformanceSchemeMapper.findSaleTimeCardPerformanceSchemeByEntityID(form.EntityID) != null)
            return BaseOutput.failed("该组织单位已经创建提佣方案");

        SaleTimeCardPerformanceSchemeEntity saleTimeCardPerformanceSchemeEntity = new SaleTimeCardPerformanceSchemeEntity();
        saleTimeCardPerformanceSchemeEntity.EntityID = form.EntityID;

        saleTimeCardPerformanceSchemeMapper.createSaleTimeCardPerformanceScheme(saleTimeCardPerformanceSchemeEntity);
        return BaseOutput.success();

    }

    @Transactional
    public BaseOutput deleteSaleTimeCardPerformanceScheme(SaleTimeCardCommissionSchemeDeleteForm form) {
        saleTimeCardPerformanceSchemeMapper.deleteSaleTimeCardPerformanceScheme(form.EntityID);
        saleTimeCardCategoryPerformanceMapper.deleteSaleTimeCardCategoryPerformance(form.EntityID);
        saleTimeCardCategoryHandlerPerformanceMapper.deleteSaleTimeCardCategoryHandlerPerformance(form.EntityID);
        saleTimeCardCategoryHandlerPerformanceMapper.deleteSaleTimeCardCategoryHandlerJobTypePerformance(form.EntityID);
        saleTimeCardPerformanceMapper.deleteSaleTimeCardPerformanceByEntityID(form.EntityID);
        saleTimeCardHandlerPerformanceMapper.deleteSaleTimeCardHandlerPerformance(form.EntityID);
        saleTimeCardHandlerPerformanceMapper.deleteSaleTimeCardHandlerJobTypePerformance(form.EntityID);
        saleTimeCardSchemeHandlerPerformanceMapper.deleteSaleTimeCardSchemeHandler(form.EntityID);
        saleTimeCardSchemeHandlerPerformanceMapper.deleteSaleTimeCardSchemeHandlerJobType(form.EntityID);
        salePackageCardTimeCardCategoryHandlerPerformanceMapper.deleteSalePackageCardTimeCardCategoryHandlerPerformance(form.EntityID);
        salePackageCardTimeCardCategoryHandlerPerformanceMapper.deleteSalePackageCardTimeCardCategoryHandlerJobTypePerformance(form.EntityID);
        salePackageCardTimeCardHandlerPerformanceMapper.deleteSalePackageCardTimeCardHandlerPerformance(form.EntityID);
        salePackageCardTimeCardHandlerPerformanceMapper.deleteSalePackageCardTimeCardHandlerJobTypePerformance(form.EntityID);
        salePackageCardTimeCardSchemeHandlerPerformanceMapper.deleteSalePackageCardTimeCardSchemeHandler(form.EntityID);
        salePackageCardTimeCardSchemeHandlerPerformanceMapper.deleteSalePackageCardTimeCardSchemeHandlerJobType(form.EntityID);
        return BaseOutput.success();
    }

}