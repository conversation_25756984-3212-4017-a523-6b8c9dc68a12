package com.zhelian.service.khs.treatGeneralCardPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.TreatGeneralCardCategoryPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCategoryCommissionQueryForm;
import com.zhelian.model.khs.entity.TreatGeneralCardCategoryPerformanceEntity;
import com.zhelian.model.khs.entity.TreatGeneralCardPerformanceSchemeEntity;
import com.zhelian.model.khs.form.output.TreatCardGoodCategoryOutputForm;
import com.zhelian.model.khs.form.output.TreatGoodCategoryPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatGeneralCardCategoryPerformanceService {

    @Autowired
    TreatGeneralCardCategoryPerformanceMapper treatGeneralCardCategoryPerformanceMapper;


    public BaseOutput alltreatGeneralCardProjectCategoryPerformance(TreatSavingCardProjectCategoryCommissionQueryForm form) {
        TreatCardGoodCategoryOutputForm saleGeneralCardCategoryOutputForm = treatGeneralCardCategoryPerformanceMapper.treatGeneralCardProjectCategoryPerformance(form.EntityID);
        List<TreatGoodCategoryPerformanceOutputForm> all = treatGeneralCardCategoryPerformanceMapper.allSaleGeneralCardCategoryPerformance(form.EntityID);
        saleGeneralCardCategoryOutputForm.Category = all;
        return BaseOutputForm.success(saleGeneralCardCategoryOutputForm);
    }

    @Transactional
    public BaseOutput updateTreatGeneralCardProjectCategoryPerformance(TreatCardCategoryAddForm form) {
        TreatGeneralCardPerformanceSchemeEntity treatGeneralCardProjectPerformanceSchemeEntity = new TreatGeneralCardPerformanceSchemeEntity();
        treatGeneralCardProjectPerformanceSchemeEntity.EntityID = form.EntityID;
        treatGeneralCardProjectPerformanceSchemeEntity.PayRate = form.PayRate;
        treatGeneralCardProjectPerformanceSchemeEntity.CardRate = form.CardRate;
        treatGeneralCardProjectPerformanceSchemeEntity.CardLargessRate = form.CardLargessRate;
        treatGeneralCardProjectPerformanceSchemeEntity.LargessRate = form.LargessRate;

        treatGeneralCardCategoryPerformanceMapper.updateTreatGeneralCardPerformanceScheme(treatGeneralCardProjectPerformanceSchemeEntity);
        treatGeneralCardCategoryPerformanceMapper.deleteTreatGeneralCardCategoryPerformance(form.EntityID);

        if (form.Category != null && form.Category.size() > 0) {
            List<TreatGeneralCardCategoryPerformanceEntity> list = new ArrayList<>();
            for (TreatCardCategoryCommissionAddForm treatCardCategoryPerformanceAddForm : form.Category) {
                if (treatCardCategoryPerformanceAddForm.PayRate != null ||
                        treatCardCategoryPerformanceAddForm.CardRate != null ||
                        treatCardCategoryPerformanceAddForm.CardLargessRate != null ||
                        treatCardCategoryPerformanceAddForm.LargessRate != null) {
                    TreatGeneralCardCategoryPerformanceEntity treatGeneralCardCategoryPerformanceEntity = new TreatGeneralCardCategoryPerformanceEntity();
                    treatGeneralCardCategoryPerformanceEntity.EntityID = form.EntityID;
                    treatGeneralCardCategoryPerformanceEntity.GeneralCardCategoryID = treatCardCategoryPerformanceAddForm.CategoryID;
                    treatGeneralCardCategoryPerformanceEntity.PayRate = treatCardCategoryPerformanceAddForm.PayRate;
                    treatGeneralCardCategoryPerformanceEntity.CardRate = treatCardCategoryPerformanceAddForm.CardRate;
                    treatGeneralCardCategoryPerformanceEntity.CardLargessRate = treatCardCategoryPerformanceAddForm.CardLargessRate;
                    treatGeneralCardCategoryPerformanceEntity.LargessRate = treatCardCategoryPerformanceAddForm.LargessRate;
                    list.add(treatGeneralCardCategoryPerformanceEntity);
                }
            }
            if (list != null && list.size() > 0) {
                List<List<TreatGeneralCardCategoryPerformanceEntity>> subs = Lists.partition(list, 20);
                for (List<TreatGeneralCardCategoryPerformanceEntity> entityList : subs) {
                    treatGeneralCardCategoryPerformanceMapper.insertTreatGeneralCardCategoryPerformanceEntity(entityList);
                }
            }
        }

        return BaseOutput.success();
    }
}