package com.zhelian.service.khs.entity;

import com.zhelian.mapper.khs.EntityConditionMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.khs.entity.EntityConditionEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Description:
 * @author: zhanglongyang
 * @date: 2021/05/18
 * @time: 13:57
 */
@Transactional
@Service
public class EntityConditionService {

    @Autowired
    EntityConditionMapper entityConditionMapper;

    //给门店分配分群条件
    public BaseOutput addEntityCondition(EntityConditionEntity entityCondition) {
        entityConditionMapper.addEntityCondition(entityCondition);
        return BaseOutput.success();
    }


}
