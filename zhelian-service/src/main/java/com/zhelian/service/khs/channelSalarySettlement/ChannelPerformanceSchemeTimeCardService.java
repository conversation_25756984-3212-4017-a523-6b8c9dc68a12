package com.zhelian.service.khs.channelSalarySettlement;


import com.zhelian.mapper.khs.ChannelPerformanceSchemeTimeCardMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.khs.entity.SaleTimeCardPerformanceEntity;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodAddForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodQueryForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodsAddForm;
import com.zhelian.model.khs.form.output.PerformanceSchemeGoodOutForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class ChannelPerformanceSchemeTimeCardService {

    @Autowired
    ChannelPerformanceSchemeTimeCardMapper channelPerformanceSchemeTimeCardMapper;

    public BaseOutput all(PerformanceSchemeGoodQueryForm form) {
        List<PerformanceSchemeGoodOutForm> list = channelPerformanceSchemeTimeCardMapper.getAllPerformanceSchemeTimeCard(form.PerformanceSchemeID, form.CategoryID);
        return BaseOutputForm.success(list);

    }

    @Transactional
    public BaseOutput create(PerformanceSchemeGoodsAddForm form) {
        channelPerformanceSchemeTimeCardMapper.deleteSaleTimeCardPerformanceByPerformanceSchemeIDAndCategory(form.PerformanceSchemeID, form.CategoryID);
        List<SaleTimeCardPerformanceEntity> list = new ArrayList<>();
        if (form.Good != null && form.Good.size() > 0) {
            for (PerformanceSchemeGoodAddForm performanceSchemeGoodAddForm : form.Good) {
                if (performanceSchemeGoodAddForm.PayPerformanceRate != null || performanceSchemeGoodAddForm.SavingCardPerformanceRate != null ||
                        performanceSchemeGoodAddForm.SavingCardPerformanceLargessRate != null) {
                    SaleTimeCardPerformanceEntity saleTimeCardPerformanceEntity = new SaleTimeCardPerformanceEntity();
                    saleTimeCardPerformanceEntity.PerformanceSchemeID = form.PerformanceSchemeID;
                    saleTimeCardPerformanceEntity.TimeCardID = performanceSchemeGoodAddForm.GoodID;
                    saleTimeCardPerformanceEntity.PayPerformanceRate = performanceSchemeGoodAddForm.PayPerformanceRate;
                    saleTimeCardPerformanceEntity.SavingCardPerformanceRate = performanceSchemeGoodAddForm.SavingCardPerformanceRate;
                    saleTimeCardPerformanceEntity.SavingCardPerformanceLargessRate = performanceSchemeGoodAddForm.SavingCardPerformanceLargessRate;
                    list.add(saleTimeCardPerformanceEntity);
                }
            }
            channelPerformanceSchemeTimeCardMapper.insertSaleTimeCardPerformance(list);
        }
        return BaseOutput.success();
    }
}
