package com.zhelian.service.khs.entity;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.github.pagehelper.PageHelper;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.redis.utils.RedisAuthenticationUtil;
import com.zhelian.core.utils.ListUtil;
import com.zhelian.core.utils.YingXiaoYunUtil;
import com.zhelian.mapper.enterprise.EnterpriseMapper;
import com.zhelian.mapper.khs.EmployeeMapper;
import com.zhelian.mapper.khs.EntityMapper;
import com.zhelian.mapper.yingxiaoyun.YingXiaoYunTenantMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.TMEntityPhotoEntity;
import com.zhelian.model.ibeauty.form.input.GoodsMoveForm;
import com.zhelian.model.ibeauty.form.input.QueryForm;
import com.zhelian.model.ibeauty.form.input.QueryPageForm;
import com.zhelian.model.khs.entity.EmployeeExtendEntity;
import com.zhelian.model.khs.entity.EntityEntity;
import com.zhelian.model.khs.entity.TMEntityEntity;
import com.zhelian.model.khs.form.input.*;
import com.zhelian.model.khs.form.output.OnlineEntityOutputForm;
import com.zhelian.model.khs.form.output.StoreListExcelForm;
import com.zhelian.model.khs.form.output.StoreListForm;
import com.zhelian.model.miniprogram.form.input.MiniEntityQueryForm;
import com.zhelian.model.yingxiaoyun.entity.YingXiaoYunTenantEntity;
import com.zhelian.model.yingxiaoyun.form.input.YXYInputForm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

@Service
public class EntityService {

    @Autowired
    EntityMapper entityMapper;
    @Autowired
    EnterpriseMapper enterpriseMapper;
    @Autowired
    EmployeeMapper employeeMapper;
    @Autowired
    YingXiaoYunTenantMapper yingXiaoYunTenantMapper;
    private static final Logger logger = LoggerFactory.getLogger(EntityService.class);

    /**
     * 功能描述:
     * <组织架构，树形结构>
     *
     * @param: form
     * @return: com.zhelian.model.base.BaseOutputForm
     * @exception:
     * @author: WangXianXian
     * @date: 2020/2/20 4:24 PM
     */
    public BaseOutput entityList(EntityQueryForm form) {

        List<EntityEntity> list = entityMapper.entityList(form);
        JSONArray result = ListUtil.listToTree(JSONArray.parseArray(JSON.toJSONString(list, JSONWriter.Feature.WriteMapNullValue)), "ID", "ParentID", "Child");
        return BaseOutputForm.success(result);
    }

    public BaseOutput getStoreList() {
        List<StoreListForm> list = entityMapper.getStoreList(SystemUserUtil.getSystemUserID());
        return BaseOutputForm.success(list);
    }


    public BaseOutput addEntity(EntityAddForm form) {

        EntityEntity entity = entityMapper.entityInfo(form.ParentID);

        if (form.ParentID != null && form.ParentID.equals(0) && entity == null)
            return BaseOutput.failed("上级单位不存在");
        if (form.IsStore && enterpriseMapper.getEnterpriseByCode(SystemUserUtil.getSystemEnterpriseCode()).LicenseNumber <= entityMapper.getIsStoreNum()) {
            return BaseOutput.failed("已达到门店最大数量，如想新增门店请联系服务商");
        }
        Integer Sequence = entityMapper.entitySequenceByParentID(form.ParentID);
        EntityEntity entityAdd = new EntityEntity();
        entityAdd.ParentID = form.ParentID;
        entityAdd.EntityName = form.EntityName;
        entityAdd.IsStore = form.IsStore;
        entityAdd.IsWarehouse = form.IsWarehouse;
        entityAdd.Sequence = Sequence == null ? 1 : Sequence + 1;
        entityAdd.Active = true;
        entityAdd.ProvinceCode = form.ProvinceCode;
        entityAdd.CityCode = form.CityCode;
        entityAdd.AreaCode = form.AreaCode;
        entityAdd.AddressDetail = form.AddressDetail;
        entityAdd.Longitude = form.Longitude;
        entityAdd.Latitude = form.Latitude;
        entityAdd.Remark = form.Remark;
        entityMapper.addEntity(entityAdd);
        if (form.IsStore) {
            TMEntityEntity entityEntity = new TMEntityEntity();
            entityEntity.EntityID = entityAdd.ID;
            entityEntity.EntityLogoURL = form.EntityLogoURL;
            entityEntity.ServiceTelephoneNumber = form.ServiceTelephoneNumber;
            entityEntity.BusinessStartTime = form.BusinessStartTime;
            entityEntity.BusinessEndTime = form.BusinessEndTime;
            entityEntity.Description = form.Description;
            entityEntity.Active = form.MarketingEntityActive;
            entityMapper.addTMEntity(entityEntity);

            if (form.EntityBusinessDate.size() > 0) {
                List<JSONObject> list = new ArrayList<>();
                for (EntityBusinessDateForm dateForm : form.EntityBusinessDate) {
                    JSONObject js = new JSONObject();
                    js.put("Data", dateForm.Data);
                    js.put("EntityID", entityAdd.ID);
                    list.add(js);
                }
                entityMapper.insertEntityBusinessDate(list);
            }
            if (form.PhotoURLList.size() > 0) {
                List<JSONObject> list = new ArrayList<>();
                for (int a = 0; a < form.PhotoURLList.size(); a++) {
                    JSONObject js = new JSONObject();
                    js.put("PhotoURL", form.PhotoURLList.get(a).PhotoURL);
                    js.put("EntityID", entityAdd.ID);
                    js.put("Sequence", a + 1);
                    list.add(js);
                }
                entityMapper.insertEntityPhotoURL(list);
            }
        }

        if (form.IsStore) {
            try {
                YingXiaoYunTenantEntity yingXiaoYunTenant = yingXiaoYunTenantMapper.getYingXiaoYunTenantEntity();
                if (yingXiaoYunTenant != null && yingXiaoYunTenant.Active) {
                    YXYInputForm yxyInputForm = new YXYInputForm();
                    yxyInputForm.EnterpriseCode = SystemUserUtil.getSystemEnterpriseCode();
                    yxyInputForm.TenantCode = yingXiaoYunTenant.TenantID;
                    yxyInputForm.EntityID = entityAdd.ID;
                    yxyInputForm.EntityName = entityAdd.EntityName;
                    yxyInputForm.Address = form.AddressDetail;
                    yxyInputForm.Longitude = form.Longitude;
                    yxyInputForm.Latitude = form.Latitude;
                    yxyInputForm.Status = "10";
                    JSONObject param = new JSONObject();
                    param.put("code", "ZL_CreateOrUpdateStore");
                    param.put("content", JSON.toJSONString(yxyInputForm));
                    JSONObject param2 = new JSONObject();
                    param2.put("request", param);
                    YingXiaoYunUtil.sendHttpPost(param2);
                }
            } catch (Exception e) {
                logger.error("门店数据营销云传输失败！", e);
                e.printStackTrace();
            }
        }

        return BaseOutputForm.success();
    }


    public BaseOutput updateEntity(EntityUpdateForm form) {
        EntityEntity entity = entityMapper.entityDetail(form.ID);
        Boolean isSend = entity.IsStore != form.IsStore || form.Active != entity.Active ? true : false;
        if (!entity.IsStore && form.IsStore && form.Active && enterpriseMapper.getEnterpriseByCode(SystemUserUtil.getSystemEnterpriseCode()).LicenseNumber <= entityMapper.getIsStoreNum()) {
            return BaseOutput.failed("已达到门店最大数量，如想新增门店请联系服务商");
        }
        //部门改为无效时，判断 是否包含有效员工和门店，包含的话，不能执行
        if (!form.Active) {
            List<EntityEntity> list = entityMapper.entityListByParentID(form.ID, null, true);
            if (list.size() > 0)
                return BaseOutput.failed("下级包含有效部门");
            EmployeeQueryForm form1 = new EmployeeQueryForm();
            form1.EntityID = form.ID;
            form1.State = true;
            List<EmployeeExtendEntity> employeeExtendEntities = employeeMapper.getEmpList(form1);
            if (employeeExtendEntities.size() > 0)
                return BaseOutput.failed("部门包含有效员工");
        }
        if (form.IsStore != entity.IsStore || form.IsWarehouse != entity.IsWarehouse) {
            List<String> list = entityMapper.getEmployeeLoginEntityByEntity(form.ID);
            if (list.size() > 0) {
                list.forEach(i -> {
                    RedisAuthenticationUtil.delete(i);
                });
            }
        }
        entityMapper.updateEntity(form);

        if (form.IsStore) {
            TMEntityEntity tmEntityEntity = entityMapper.tmEntityInfo(form.ID);
            if (tmEntityEntity == null) {
                TMEntityEntity entityEntity = new TMEntityEntity();
                entityEntity.EntityID = form.ID;
                entityEntity.EntityLogoURL = form.EntityLogoURL;
                entityEntity.ServiceTelephoneNumber = form.ServiceTelephoneNumber;
                entityEntity.BusinessStartTime = form.BusinessStartTime;
                entityEntity.BusinessEndTime = form.BusinessEndTime;
                entityEntity.Description = form.Description;
                entityEntity.Active = form.MarketingEntityActive;
                entityMapper.addTMEntity(entityEntity);
                if (form.PhotoURLList.size() > 0) {
                    List<JSONObject> list = new ArrayList<>();
                    for (int a = 0; a < form.PhotoURLList.size(); a++) {
                        JSONObject js = new JSONObject();
                        js.put("PhotoURL", form.PhotoURLList.get(a).PhotoURL);
                        js.put("EntityID", form.ID);
                        js.put("Sequence", a + 1);
                        list.add(js);
                    }
                    entityMapper.insertEntityPhotoURL(list);
                }
            } else {

                TMEntityEntity entityEntity = new TMEntityEntity();
                entityEntity.EntityID = form.ID;
                entityEntity.EntityLogoURL = form.EntityLogoURL;
                entityEntity.ServiceTelephoneNumber = form.ServiceTelephoneNumber;
                entityEntity.BusinessStartTime = form.BusinessStartTime;
                entityEntity.BusinessEndTime = form.BusinessEndTime;
                entityEntity.Description = form.Description;
                entityEntity.Active = form.MarketingEntityActive;
                entityMapper.updateTMEntity(entityEntity);

                if (form.EntityBusinessDate.size() > 0) {
                    entityMapper.deleteEntityBusinessDateByID(form.ID);
                    List<JSONObject> list = new ArrayList<>();
                    for (EntityBusinessDateForm dateForm : form.EntityBusinessDate) {
                        JSONObject js = new JSONObject();
                        js.put("Data", dateForm.Data);
                        js.put("EntityID", form.ID);
                        list.add(js);
                    }
                    entityMapper.insertEntityBusinessDate(list);
                }

                entityMapper.deleteEntityPhotoURLByID(form.ID);
                if (form.PhotoURLList.size() > 0) {
                    List<JSONObject> list = new ArrayList<>();
                    for (int a = 0; a < form.PhotoURLList.size(); a++) {
                        JSONObject js = new JSONObject();
                        js.put("PhotoURL", form.PhotoURLList.get(a).PhotoURL);
                        js.put("EntityID", form.ID);
                        js.put("Sequence", a + 1);
                        list.add(js);
                    }
                    entityMapper.insertEntityPhotoURL(list);
                }
            }
        }

        if (isSend) {
            try {
                YingXiaoYunTenantEntity yingXiaoYunTenant = yingXiaoYunTenantMapper.getYingXiaoYunTenantEntity();
                if (yingXiaoYunTenant != null && yingXiaoYunTenant.Active) {
                    YXYInputForm yxyInputForm = new YXYInputForm();
                    yxyInputForm.EnterpriseCode = SystemUserUtil.getSystemEnterpriseCode();
                    yxyInputForm.TenantCode = yingXiaoYunTenant.TenantID;
                    yxyInputForm.EntityID = entity.ID;
                    yxyInputForm.EntityName = entity.EntityName;
                    yxyInputForm.Address = form.AddressDetail;
                    yxyInputForm.Longitude = form.Longitude;
                    yxyInputForm.Latitude = form.Latitude;
                    yxyInputForm.Status = form.Active == false || form.IsStore == false ? "20" : "10";


                    JSONObject param = new JSONObject();
                    param.put("code", "ZL_CreateOrUpdateStore");
                    param.put("content", JSON.toJSONString(yxyInputForm));
                    JSONObject param2 = new JSONObject();
                    param2.put("request", param);
                    YingXiaoYunUtil.sendHttpPost(param2);
                }
            } catch (Exception e) {
                logger.error("门店数据营销云传输失败！", e);
                e.printStackTrace();
            }
        }


        return BaseOutput.success();
    }


    @Transactional
    public BaseOutput move(EntityMoveForm form) {

        EntityEntity parentEntity = entityMapper.entityInfo(form.DestParentID);
        if (parentEntity == null)
            return BaseOutput.failed("上级单位不存在");

        EntityEntity entity = entityMapper.entityInfo(form.MoveEntityID);
        if (entity == null)
            return BaseOutput.failed("单位不存在");

        entity.ParentID = form.DestParentID;

        //重新排序，正序查出列表
        List<EntityEntity> list = entityMapper.entityListByParentID(form.DestParentID, form.MoveEntityID, null);

        if (list == null) {
            list = new ArrayList<>();
            list.add(entity);
        } else {
            //前面有单位的话，顶替前面单位，没有单位的话则插入最后一个，最终根据下标重新排序
            if (form.BeforeEntityID != null) {
                EntityEntity beforeEntityEntity = new EntityEntity();

                for (EntityEntity entity1 : list) {
                    if (entity1.ID.equals(form.BeforeEntityID)) {
                        beforeEntityEntity = entity1;
                    }
                }
                if (beforeEntityEntity == null)
                    return BaseOutput.failed("前面单位不存在");

                list.add(list.indexOf(beforeEntityEntity), entity);

            } else {
                //插入最后一位
                list.add(entity);
            }
        }

        for (EntityEntity entity1 : list) {
            entity1.Sequence = list.indexOf(entity1) + 1;
        }

        entityMapper.updateEntitySequence(list);

        return BaseOutput.success();
    }

    public BaseOutput onlineEntityList() {
        OnlineEntityOutputForm form = entityMapper.onlineEntityList(SystemUserUtil.getSystemEntityID());
        return BaseOutputForm.success(form);
    }

    public BaseOutput getOnlineEntityByID(EntityQueryForm queryForm) {
        OnlineEntityOutputForm form = entityMapper.onlineEntityList(queryForm.ID);
        return BaseOutputForm.success(form);
    }

    public BaseOutput updateOnlineEntity(TMEntityUpdateForm form) {
        TMEntityEntity entityEntity = entityMapper.tmEntityInfo(form.ID);
        EntityUpdateForm entityUpdateForm = new EntityUpdateForm();
        entityUpdateForm.ID = form.ID;
        entityUpdateForm.ProvinceCode = form.ProvinceCode;
        entityUpdateForm.CityCode = form.CityCode;
        entityUpdateForm.AreaCode = form.AreaCode;
        entityUpdateForm.AddressDetail = form.AddressDetail;
        entityUpdateForm.Longitude = form.Longitude;
        entityUpdateForm.Latitude = form.Latitude;
        entityMapper.updateEntity(entityUpdateForm);

        if (entityEntity != null) {
            entityEntity.EntityID = form.ID;
            entityEntity.EntityLogoURL = form.EntityLogoURL;

            entityEntity.ServiceTelephoneNumber = form.ServiceTelephoneNumber;
            entityEntity.BusinessStartTime = form.BusinessStartTime;
            entityEntity.BusinessEndTime = form.BusinessEndTime;
            entityEntity.Description = form.Description;
            entityEntity.Active = form.MarketingEntityActive;
            entityMapper.updateTMEntity(entityEntity);
        } else {
            entityEntity = new TMEntityEntity();
            entityEntity.EntityID = form.ID;
            entityEntity.EntityLogoURL = form.EntityLogoURL;

            entityEntity.ServiceTelephoneNumber = form.ServiceTelephoneNumber;
            entityEntity.BusinessStartTime = form.BusinessStartTime;
            entityEntity.BusinessEndTime = form.BusinessEndTime;
            entityEntity.Description = form.Description;
            entityEntity.Active = form.MarketingEntityActive;
            entityMapper.addTMEntity(entityEntity);
        }

        if (form.EntityBusinessDate.size() > 0) {
            entityMapper.deleteEntityBusinessDateByID(form.ID);
            List<JSONObject> list = new ArrayList<>();
            for (EntityBusinessDateForm dateForm : form.EntityBusinessDate) {
                JSONObject js = new JSONObject();
                js.put("Data", dateForm.Data);
                js.put("EntityID", form.ID);
                list.add(js);
            }
            entityMapper.insertEntityBusinessDate(list);
        }
        entityMapper.deleteEntityPhotoURLByID(form.ID);
        if (form.PhotoURLList.size() > 0) {
            List<JSONObject> list = new ArrayList<>();
            for (int a = 0; a < form.PhotoURLList.size(); a++) {
                JSONObject js = new JSONObject();
                js.put("PhotoURL", form.PhotoURLList.get(a).PhotoURL);
                js.put("EntityID", form.ID);
                js.put("Sequence", a + 1);
                list.add(js);
            }
            entityMapper.insertEntityPhotoURL(list);
        }
        return BaseOutput.success();
    }


    public BaseOutput moveImage(GoodsMoveForm form) {

        LinkedList<Integer> linkedList = entityMapper.getASCList(form.MoveID, form.ID);

        ListUtil.moveLinkedList(linkedList, form.MoveID, form.BeforeID);

        List<TMEntityPhotoEntity> list = new ArrayList<>();
        for (Integer ID : linkedList) {
            TMEntityPhotoEntity tmEntityPhotoEntity = new TMEntityPhotoEntity();
            tmEntityPhotoEntity.ID = ID;
            tmEntityPhotoEntity.EntityID = form.ID;
            tmEntityPhotoEntity.Sequence = linkedList.indexOf(ID) + 1;
            list.add(tmEntityPhotoEntity);
        }

        entityMapper.updateSequence(list);
        return BaseOutput.success();
    }

    public BaseOutput allEntity() {
        List<StoreListForm> list = entityMapper.allEntity();
        return BaseOutputForm.success(list);
    }

    public BaseOutput allWarehouse() {
        List<StoreListForm> list = entityMapper.allWarehouse();
        return BaseOutputForm.success(list);
    }

    public BaseOutput excel(EntityQueryForm form, HttpServletResponse response) {
        List<StoreListExcelForm> list = entityMapper.excelEntity(form.SearchKey);
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("单位明细", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), StoreListExcelForm.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("单位明细").doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return BaseOutput.success();
    }


    public BaseOutput all(MiniEntityQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<StoreListForm> list = entityMapper.all(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput permissionEtityByPage(QueryPageForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<StoreListForm> list = entityMapper.getEmployeePermissionEntity(SystemUserUtil.getSystemUserID(), form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput permissionEtity(QueryForm form) {
        List<StoreListForm> list = entityMapper.getEmployeePermissionEntity(SystemUserUtil.getSystemUserID(), form.Name);
        return BaseOutputForm.success(list);
    }
}
