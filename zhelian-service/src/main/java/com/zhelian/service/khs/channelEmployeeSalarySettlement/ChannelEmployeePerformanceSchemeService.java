package com.zhelian.service.khs.channelEmployeeSalarySettlement;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONWriter;
import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.khs.ChannelEmployeePerformanceSchemeMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.khs.entity.*;
import com.zhelian.model.khs.form.input.*;
import com.zhelian.model.khs.form.output.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import static com.zhelian.core.utils.ListUtil.listToTree;

@Service
public class ChannelEmployeePerformanceSchemeService {

    @Autowired
    ChannelEmployeePerformanceSchemeMapper channelEmployeePerformanceSchemeMapper;

    public BaseOutput all(PerformanceSchemeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<ChannelEmployeePerformanceSchemeEntity> list = channelEmployeePerformanceSchemeMapper.getPerformanceScheme(form.Name, form.Active);
        return BasePageInfo.success(list);

    }

    public BaseOutput valid() {
        List<ChannelEmployeePerformanceSchemeEntity> list = channelEmployeePerformanceSchemeMapper.getPerformanceScheme(null, true);
        return BaseOutputForm.success(list);

    }

    public BaseOutput create(PerformanceSchemeAddForm form) {
        ChannelEmployeePerformanceSchemeEntity performanceSchemeEntity = new ChannelEmployeePerformanceSchemeEntity();
        performanceSchemeEntity.Name = form.Name;
        channelEmployeePerformanceSchemeMapper.createPerformanceScheme(performanceSchemeEntity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput update(ChannelEmployeePerformanceSchemeUpdateForm form) {
        if (form.Active == false) {
            Integer count = channelEmployeePerformanceSchemeMapper.getPerformanceSchemeCount(form.ID);
            if (count > 0)
                return BaseOutput.failed("该方案下有提成方案，不可以改为无效");
        }

        ChannelEmployeePerformanceSchemeEntity performanceSchemeEntity = new ChannelEmployeePerformanceSchemeEntity();
        performanceSchemeEntity.ID = form.ID;
        performanceSchemeEntity.Name = form.Name;
        performanceSchemeEntity.Active = form.Active;
        performanceSchemeEntity.IsCalculateChannelDeveloper = form.IsCalculateChannelDeveloper;
        performanceSchemeEntity.IsCalculateChannelConsultant = form.IsCalculateChannelConsultant;
        channelEmployeePerformanceSchemeMapper.updatePerformanceScheme(performanceSchemeEntity);
        channelEmployeePerformanceSchemeMapper.deleteProductCategory(form.ID);
        channelEmployeePerformanceSchemeMapper.deleteProjectCategory(form.ID);
        channelEmployeePerformanceSchemeMapper.deleteGeneralCardCategory(form.ID);
        channelEmployeePerformanceSchemeMapper.deleteTimeCardCategory(form.ID);
        channelEmployeePerformanceSchemeMapper.deleteSavingCardCategory(form.ID);
        channelEmployeePerformanceSchemeMapper.deleteTreatProductCategory(form.ID);
        channelEmployeePerformanceSchemeMapper.deleteTreatProjectCategory(form.ID);
        channelEmployeePerformanceSchemeMapper.deleteTreatGeneralCardCategory(form.ID);
        channelEmployeePerformanceSchemeMapper.deleteTreatTimeCardCategory(form.ID);
        channelEmployeePerformanceSchemeMapper.deleteTreatSavingCardCategory(form.ID);

        updateDetail(form.ID, form.ProductCategory, form.ProjectCategory, form.GeneralCardCategory, form.TimeCardCategory, form.SavingCardCategory, form.TreatProductCategory, form.TreatProjectCategory, form.TreatGeneralCardCategory, form.TreatTimeCardCategory, form.TreatSavingCardCategory);
        return BaseOutput.success();
    }


    public BaseOutput detail(PerformanceSchemeQueryForm form) {
        ChannelPerformanceSchemeOutForm performanceSchemeOutForm = new ChannelPerformanceSchemeOutForm();
        List<PerformanceSchemeGoodCategoryOutForm> productCategory = channelEmployeePerformanceSchemeMapper.getPerformanceSchemeProduct(form.ID);
        List<PerformanceSchemeGoodCategoryOutForm> projectCategory = channelEmployeePerformanceSchemeMapper.getPerformanceSchemeProject(form.ID);
        List<PerformanceSchemeGoodCardCategoryOutForm> generalCardCategory = channelEmployeePerformanceSchemeMapper.getPerformanceSchemeGeneralCard(form.ID);
        List<PerformanceSchemeGoodCardCategoryOutForm> timeCardCategory = channelEmployeePerformanceSchemeMapper.getPerformanceSchemeTimeCard(form.ID);
        List<PerformanceSchemeSavingCardCategoryOutForm> savingCardCategory = channelEmployeePerformanceSchemeMapper.getPerformanceSchemeSavingCard(form.ID);
        List<PerformanceSchemeTreatGoodCategoryOutForm> treatProductCategory = channelEmployeePerformanceSchemeMapper.getPerformanceSchemeTreatProduct(form.ID);
        List<PerformanceSchemeTreatGoodCategoryOutForm> treatProjectCategory = channelEmployeePerformanceSchemeMapper.getPerformanceSchemeTreatProject(form.ID);
        List<ChannelPerformanceSchemeTreaCardCategoryOutForm> treatGeneralCardCategory = channelEmployeePerformanceSchemeMapper.getPerformanceSchemeTreatGeneralCard(form.ID);
        List<ChannelPerformanceSchemeTreaCardCategoryOutForm> treatTimeCardCategory = channelEmployeePerformanceSchemeMapper.getPerformanceSchemeTreatTimeCard(form.ID);
        List<ChannelPerformanceSchemeTreaSavingCardCategoryOutForm> treatSavingCardCategory = channelEmployeePerformanceSchemeMapper.getPerformanceSchemeTreatSavingCard(form.ID);
        performanceSchemeOutForm.ProductCategory = listToTree(JSONArray.parseArray(JSON.toJSONString(productCategory, JSONWriter.Feature.WriteMapNullValue)), "CategoryID", "ParentID", "Child");
        performanceSchemeOutForm.ProjectCategory = listToTree(JSONArray.parseArray(JSON.toJSONString(projectCategory, JSONWriter.Feature.WriteMapNullValue)), "CategoryID", "ParentID", "Child");
        performanceSchemeOutForm.GeneralCardCategory = generalCardCategory;
        performanceSchemeOutForm.TimeCardCategory = timeCardCategory;
        performanceSchemeOutForm.SavingCardCategory = savingCardCategory;
        performanceSchemeOutForm.TreatProductCategory = listToTree(JSONArray.parseArray(JSON.toJSONString(treatProductCategory, JSONWriter.Feature.WriteMapNullValue)), "CategoryID", "ParentID", "Child");
        performanceSchemeOutForm.TreatProjectCategory = listToTree(JSONArray.parseArray(JSON.toJSONString(treatProjectCategory, JSONWriter.Feature.WriteMapNullValue)), "CategoryID", "ParentID", "Child");
        performanceSchemeOutForm.TreatGeneralCardCategory = treatGeneralCardCategory;
        performanceSchemeOutForm.TreatTimeCardCategory = treatTimeCardCategory;
        performanceSchemeOutForm.TreatSavingCardCategory = treatSavingCardCategory;
        return BaseOutputForm.success(performanceSchemeOutForm);

    }

    public void updateDetail(Integer ID, List<PerformanceSchemeSaleCategoryAddForm> productCategory, List<PerformanceSchemeSaleCategoryAddForm> projectCategory, List<PerformanceSchemeSaleCategoryAddForm> generalCardCategory, List<PerformanceSchemeSaleCategoryAddForm> timeCardCategory, List<PerformanceSchemeSaleCategoryAddForm> savingCardCategory, List<PerformanceSchemeTreatCategoryAddForm> treatProductCategory
            , List<PerformanceSchemeTreatCategoryAddForm> treatProjectCategory, List<PerformanceSchemeTreatCategoryAddForm> treatGeneralCardCategory, List<PerformanceSchemeTreatCategoryAddForm> treatTimeCardCategory, List<PerformanceSchemeTreatCategoryAddForm> treatSavingCardCategory) {
        List<SaleProductCategoryPerformanceEntity> productList = new ArrayList<>();
        List<SaleProjectCategoryPerformanceEntity> projectList = new ArrayList<>();
        List<SaleGeneralCardCategoryPerformanceEntity> generalCardList = new ArrayList<>();
        List<SaleTimeCardCategoryPerformanceEntity> timeCardList = new ArrayList<>();
        List<SaleSavingCardCategoryPerformanceEntity> savingCardList = new ArrayList<>();
        List<TreatProductCategoryPerformanceEntity> treatProductList = new ArrayList<>();
        List<TreatProjectCategoryPerformanceEntity> treatProjectList = new ArrayList<>();
        List<TreatChannelCardCategoryPerformanceEntity> treatGeneralCardList = new ArrayList<>();
        List<TreatChannelCardCategoryPerformanceEntity> treatTimeCardList = new ArrayList<>();
        List<TreatChannelCardCategoryPerformanceEntity> treatSavingCardList = new ArrayList<>();
        if (productCategory != null && productCategory.size() > 0) {
            for (PerformanceSchemeSaleCategoryAddForm performanceSchemeSaleCategoryAddForm : productCategory) {
                if (performanceSchemeSaleCategoryAddForm.PayPerformanceRate != null || performanceSchemeSaleCategoryAddForm.SavingCardPerformanceRate != null ||
                        performanceSchemeSaleCategoryAddForm.SavingCardPerformanceLargessRate != null) {
                    SaleProductCategoryPerformanceEntity saleProductCategoryPerformanceEntity = new SaleProductCategoryPerformanceEntity();
                    saleProductCategoryPerformanceEntity.PerformanceSchemeID = ID;
                    saleProductCategoryPerformanceEntity.ProductCategoryID = performanceSchemeSaleCategoryAddForm.CategoryID;
                    saleProductCategoryPerformanceEntity.PayPerformanceRate = performanceSchemeSaleCategoryAddForm.PayPerformanceRate;
                    saleProductCategoryPerformanceEntity.SavingCardPerformanceRate = performanceSchemeSaleCategoryAddForm.SavingCardPerformanceRate;
                    saleProductCategoryPerformanceEntity.SavingCardPerformanceLargessRate = performanceSchemeSaleCategoryAddForm.SavingCardPerformanceLargessRate;
                    productList.add(saleProductCategoryPerformanceEntity);
                }
                if (performanceSchemeSaleCategoryAddForm.Child != null && performanceSchemeSaleCategoryAddForm.Child.size() > 0) {
                    for (PerformanceSchemeSaleCategoryChildAddForm performanceSchemeSaleCategoryChildAddForm : performanceSchemeSaleCategoryAddForm.Child) {
                        if (performanceSchemeSaleCategoryChildAddForm.PayPerformanceRate != null || performanceSchemeSaleCategoryChildAddForm.SavingCardPerformanceRate != null ||
                                performanceSchemeSaleCategoryChildAddForm.SavingCardPerformanceLargessRate != null) {
                            SaleProductCategoryPerformanceEntity saleProductCategoryPerformanceEntity = new SaleProductCategoryPerformanceEntity();
                            saleProductCategoryPerformanceEntity.PerformanceSchemeID = ID;
                            saleProductCategoryPerformanceEntity.ProductCategoryID = performanceSchemeSaleCategoryChildAddForm.CategoryID;
                            saleProductCategoryPerformanceEntity.PayPerformanceRate = performanceSchemeSaleCategoryChildAddForm.PayPerformanceRate;
                            saleProductCategoryPerformanceEntity.SavingCardPerformanceRate = performanceSchemeSaleCategoryChildAddForm.SavingCardPerformanceRate;
                            saleProductCategoryPerformanceEntity.SavingCardPerformanceLargessRate = performanceSchemeSaleCategoryChildAddForm.SavingCardPerformanceLargessRate;
                            productList.add(saleProductCategoryPerformanceEntity);
                        }
                    }
                }
            }
        }
        if (projectCategory != null && projectCategory.size() > 0) {
            for (PerformanceSchemeSaleCategoryAddForm performanceSchemeSaleCategoryAddForm : projectCategory) {
                if (performanceSchemeSaleCategoryAddForm.PayPerformanceRate != null || performanceSchemeSaleCategoryAddForm.SavingCardPerformanceRate != null ||
                        performanceSchemeSaleCategoryAddForm.SavingCardPerformanceLargessRate != null) {
                    SaleProjectCategoryPerformanceEntity saleProjectCategoryPerformanceEntity = new SaleProjectCategoryPerformanceEntity();
                    saleProjectCategoryPerformanceEntity.PerformanceSchemeID = ID;
                    saleProjectCategoryPerformanceEntity.ProjectCategoryID = performanceSchemeSaleCategoryAddForm.CategoryID;
                    saleProjectCategoryPerformanceEntity.PayPerformanceRate = performanceSchemeSaleCategoryAddForm.PayPerformanceRate;
                    saleProjectCategoryPerformanceEntity.SavingCardPerformanceRate = performanceSchemeSaleCategoryAddForm.SavingCardPerformanceRate;
                    saleProjectCategoryPerformanceEntity.SavingCardPerformanceLargessRate = performanceSchemeSaleCategoryAddForm.SavingCardPerformanceLargessRate;
                    projectList.add(saleProjectCategoryPerformanceEntity);
                }
                if (performanceSchemeSaleCategoryAddForm.Child != null && performanceSchemeSaleCategoryAddForm.Child.size() > 0) {
                    for (PerformanceSchemeSaleCategoryChildAddForm performanceSchemeSaleCategoryChildAddForm : performanceSchemeSaleCategoryAddForm.Child) {
                        if (performanceSchemeSaleCategoryChildAddForm.PayPerformanceRate != null || performanceSchemeSaleCategoryChildAddForm.SavingCardPerformanceRate != null ||
                                performanceSchemeSaleCategoryChildAddForm.SavingCardPerformanceLargessRate != null) {
                            SaleProjectCategoryPerformanceEntity saleProjectCategoryPerformanceEntity = new SaleProjectCategoryPerformanceEntity();
                            saleProjectCategoryPerformanceEntity.PerformanceSchemeID = ID;
                            saleProjectCategoryPerformanceEntity.ProjectCategoryID = performanceSchemeSaleCategoryChildAddForm.CategoryID;
                            saleProjectCategoryPerformanceEntity.PayPerformanceRate = performanceSchemeSaleCategoryChildAddForm.PayPerformanceRate;
                            saleProjectCategoryPerformanceEntity.SavingCardPerformanceRate = performanceSchemeSaleCategoryChildAddForm.SavingCardPerformanceRate;
                            saleProjectCategoryPerformanceEntity.SavingCardPerformanceLargessRate = performanceSchemeSaleCategoryChildAddForm.SavingCardPerformanceLargessRate;
                            projectList.add(saleProjectCategoryPerformanceEntity);
                        }
                    }
                }
            }
        }
        if (generalCardCategory != null && generalCardCategory.size() > 0) {
            for (PerformanceSchemeSaleCategoryAddForm performanceSchemeSaleCategoryAddForm : generalCardCategory) {
                if (performanceSchemeSaleCategoryAddForm.PayPerformanceRate != null || performanceSchemeSaleCategoryAddForm.SavingCardPerformanceRate != null ||
                        performanceSchemeSaleCategoryAddForm.SavingCardPerformanceLargessRate != null) {
                    SaleGeneralCardCategoryPerformanceEntity saleGeneralCardCategoryPerformanceEntity = new SaleGeneralCardCategoryPerformanceEntity();
                    saleGeneralCardCategoryPerformanceEntity.PerformanceSchemeID = ID;
                    saleGeneralCardCategoryPerformanceEntity.GeneralCardCategoryID = performanceSchemeSaleCategoryAddForm.CategoryID;
                    saleGeneralCardCategoryPerformanceEntity.PayPerformanceRate = performanceSchemeSaleCategoryAddForm.PayPerformanceRate;
                    saleGeneralCardCategoryPerformanceEntity.SavingCardPerformanceRate = performanceSchemeSaleCategoryAddForm.SavingCardPerformanceRate;
                    saleGeneralCardCategoryPerformanceEntity.SavingCardPerformanceLargessRate = performanceSchemeSaleCategoryAddForm.SavingCardPerformanceLargessRate;
                    generalCardList.add(saleGeneralCardCategoryPerformanceEntity);
                }
            }
        }
        if (timeCardCategory != null && timeCardCategory.size() > 0) {
            for (PerformanceSchemeSaleCategoryAddForm performanceSchemeSaleCategoryAddForm : timeCardCategory) {
                if (performanceSchemeSaleCategoryAddForm.PayPerformanceRate != null || performanceSchemeSaleCategoryAddForm.SavingCardPerformanceRate != null ||
                        performanceSchemeSaleCategoryAddForm.SavingCardPerformanceLargessRate != null) {
                    SaleTimeCardCategoryPerformanceEntity saleTimeCardCategoryPerformanceEntity = new SaleTimeCardCategoryPerformanceEntity();
                    saleTimeCardCategoryPerformanceEntity.PerformanceSchemeID = ID;
                    saleTimeCardCategoryPerformanceEntity.TimeCardCategoryID = performanceSchemeSaleCategoryAddForm.CategoryID;
                    saleTimeCardCategoryPerformanceEntity.PayPerformanceRate = performanceSchemeSaleCategoryAddForm.PayPerformanceRate;
                    saleTimeCardCategoryPerformanceEntity.SavingCardPerformanceRate = performanceSchemeSaleCategoryAddForm.SavingCardPerformanceRate;
                    saleTimeCardCategoryPerformanceEntity.SavingCardPerformanceLargessRate = performanceSchemeSaleCategoryAddForm.SavingCardPerformanceLargessRate;
                    timeCardList.add(saleTimeCardCategoryPerformanceEntity);
                }
            }
        }
        if (savingCardCategory != null && savingCardCategory.size() > 0) {
            for (PerformanceSchemeSaleCategoryAddForm performanceSchemeSaleCategoryAddForm : savingCardCategory) {
                if (performanceSchemeSaleCategoryAddForm.PayPerformanceRate != null || performanceSchemeSaleCategoryAddForm.SavingCardPerformanceRate != null ||
                        performanceSchemeSaleCategoryAddForm.SavingCardPerformanceLargessRate != null) {
                    SaleSavingCardCategoryPerformanceEntity saleSavingCardCategoryPerformanceEntity = new SaleSavingCardCategoryPerformanceEntity();
                    saleSavingCardCategoryPerformanceEntity.PerformanceSchemeID = ID;
                    saleSavingCardCategoryPerformanceEntity.SavingCardCategoryID = performanceSchemeSaleCategoryAddForm.CategoryID;
                    saleSavingCardCategoryPerformanceEntity.PayPerformanceRate = performanceSchemeSaleCategoryAddForm.PayPerformanceRate;
                    savingCardList.add(saleSavingCardCategoryPerformanceEntity);
                }
            }
        }
        if (treatProductCategory != null && treatProductCategory.size() > 0) {
            for (PerformanceSchemeTreatCategoryAddForm performanceSchemeTreatCategoryAddForm : treatProductCategory) {
                if (performanceSchemeTreatCategoryAddForm.PerformancePayRate != null || performanceSchemeTreatCategoryAddForm.PerformanceCardRate != null || performanceSchemeTreatCategoryAddForm.PerformanceCardLargessRate != null || performanceSchemeTreatCategoryAddForm.PerformanceLargessRate != null) {
                    TreatProductCategoryPerformanceEntity treatProductCategoryPerformanceEntity = new TreatProductCategoryPerformanceEntity();
                    treatProductCategoryPerformanceEntity.PerformanceSchemeID = ID;
                    treatProductCategoryPerformanceEntity.ProductCategoryID = performanceSchemeTreatCategoryAddForm.CategoryID;
                    treatProductCategoryPerformanceEntity.PerformancePayRate = performanceSchemeTreatCategoryAddForm.PerformancePayRate;
                    treatProductCategoryPerformanceEntity.PerformanceCardRate = performanceSchemeTreatCategoryAddForm.PerformanceCardRate;
                    treatProductCategoryPerformanceEntity.PerformanceCardLargessRate = performanceSchemeTreatCategoryAddForm.PerformanceCardLargessRate;
                    treatProductCategoryPerformanceEntity.PerformanceLargessRate = performanceSchemeTreatCategoryAddForm.PerformanceLargessRate;
                    treatProductList.add(treatProductCategoryPerformanceEntity);
                }
                if (performanceSchemeTreatCategoryAddForm.Child != null && performanceSchemeTreatCategoryAddForm.Child.size() > 0) {
                    for (PerformanceSchemeTreatCategoryChildAddForm performanceSchemeTreatCategoryChildAddForm : performanceSchemeTreatCategoryAddForm.Child) {
                        if (performanceSchemeTreatCategoryChildAddForm.PerformancePayRate != null || performanceSchemeTreatCategoryChildAddForm.PerformanceCardRate != null || performanceSchemeTreatCategoryChildAddForm.PerformanceCardLargessRate != null || performanceSchemeTreatCategoryChildAddForm.PerformanceLargessRate != null) {
                            TreatProductCategoryPerformanceEntity treatProductCategoryPerformanceEntity = new TreatProductCategoryPerformanceEntity();
                            treatProductCategoryPerformanceEntity.PerformanceSchemeID = ID;
                            treatProductCategoryPerformanceEntity.ProductCategoryID = performanceSchemeTreatCategoryChildAddForm.CategoryID;
                            treatProductCategoryPerformanceEntity.PerformancePayRate = performanceSchemeTreatCategoryChildAddForm.PerformancePayRate;
                            treatProductCategoryPerformanceEntity.PerformanceCardRate = performanceSchemeTreatCategoryChildAddForm.PerformanceCardRate;
                            treatProductCategoryPerformanceEntity.PerformanceCardLargessRate = performanceSchemeTreatCategoryChildAddForm.PerformanceCardLargessRate;
                            treatProductCategoryPerformanceEntity.PerformanceLargessRate = performanceSchemeTreatCategoryChildAddForm.PerformanceLargessRate;
                            treatProductList.add(treatProductCategoryPerformanceEntity);
                        }
                    }
                }
            }
        }
        if (treatProjectCategory != null && treatProjectCategory.size() > 0) {
            for (PerformanceSchemeTreatCategoryAddForm performanceSchemeTreatCategoryAddForm : treatProjectCategory) {
                if (performanceSchemeTreatCategoryAddForm.PerformancePayRate != null || performanceSchemeTreatCategoryAddForm.PerformanceCardRate != null || performanceSchemeTreatCategoryAddForm.PerformanceCardLargessRate != null || performanceSchemeTreatCategoryAddForm.PerformanceLargessRate != null) {
                    TreatProjectCategoryPerformanceEntity treatProjectCategoryPerformanceEntity = new TreatProjectCategoryPerformanceEntity();
                    treatProjectCategoryPerformanceEntity.PerformanceSchemeID = ID;
                    treatProjectCategoryPerformanceEntity.ProjectCategoryID = performanceSchemeTreatCategoryAddForm.CategoryID;
                    treatProjectCategoryPerformanceEntity.PerformancePayRate = performanceSchemeTreatCategoryAddForm.PerformancePayRate;
                    treatProjectCategoryPerformanceEntity.PerformanceCardRate = performanceSchemeTreatCategoryAddForm.PerformanceCardRate;
                    treatProjectCategoryPerformanceEntity.PerformanceCardLargessRate = performanceSchemeTreatCategoryAddForm.PerformanceCardLargessRate;
                    treatProjectCategoryPerformanceEntity.PerformanceLargessRate = performanceSchemeTreatCategoryAddForm.PerformanceLargessRate;
                    treatProjectList.add(treatProjectCategoryPerformanceEntity);
                }
                if (performanceSchemeTreatCategoryAddForm.Child != null && performanceSchemeTreatCategoryAddForm.Child.size() > 0) {
                    for (PerformanceSchemeTreatCategoryChildAddForm performanceSchemeTreatCategoryChildAddForm : performanceSchemeTreatCategoryAddForm.Child) {
                        if (performanceSchemeTreatCategoryChildAddForm.PerformancePayRate != null || performanceSchemeTreatCategoryChildAddForm.PerformanceCardRate != null || performanceSchemeTreatCategoryChildAddForm.PerformanceCardLargessRate != null || performanceSchemeTreatCategoryChildAddForm.PerformanceLargessRate != null) {
                            TreatProjectCategoryPerformanceEntity treatProjectCategoryPerformanceEntity = new TreatProjectCategoryPerformanceEntity();
                            treatProjectCategoryPerformanceEntity.PerformanceSchemeID = ID;
                            treatProjectCategoryPerformanceEntity.ProjectCategoryID = performanceSchemeTreatCategoryChildAddForm.CategoryID;
                            treatProjectCategoryPerformanceEntity.PerformancePayRate = performanceSchemeTreatCategoryChildAddForm.PerformancePayRate;
                            treatProjectCategoryPerformanceEntity.PerformanceCardRate = performanceSchemeTreatCategoryChildAddForm.PerformanceCardRate;
                            treatProjectCategoryPerformanceEntity.PerformanceCardLargessRate = performanceSchemeTreatCategoryChildAddForm.PerformanceCardLargessRate;
                            treatProjectCategoryPerformanceEntity.PerformanceLargessRate = performanceSchemeTreatCategoryChildAddForm.PerformanceLargessRate;
                            treatProjectList.add(treatProjectCategoryPerformanceEntity);
                        }
                    }
                }
            }
        }
        if (treatGeneralCardCategory != null && treatGeneralCardCategory.size() > 0) {
            for (PerformanceSchemeTreatCategoryAddForm performanceSchemeTreatCategoryAddForm : treatGeneralCardCategory) {
                if (performanceSchemeTreatCategoryAddForm.PerformancePayRate != null || performanceSchemeTreatCategoryAddForm.PerformanceCardRate != null || performanceSchemeTreatCategoryAddForm.PerformanceCardLargessRate != null || performanceSchemeTreatCategoryAddForm.PerformanceLargessRate != null) {
                    TreatChannelCardCategoryPerformanceEntity categoryPerformanceEntity = new TreatChannelCardCategoryPerformanceEntity();
                    categoryPerformanceEntity.ChannelPerformanceSchemeID = ID;
                    categoryPerformanceEntity.CardCategoryID = performanceSchemeTreatCategoryAddForm.CategoryID;
                    categoryPerformanceEntity.PerformancePayRate = performanceSchemeTreatCategoryAddForm.PerformancePayRate;
                    categoryPerformanceEntity.PerformanceCardRate = performanceSchemeTreatCategoryAddForm.PerformanceCardRate;
                    categoryPerformanceEntity.PerformanceCardLargessRate = performanceSchemeTreatCategoryAddForm.PerformanceCardLargessRate;
                    categoryPerformanceEntity.PerformanceLargessRate = performanceSchemeTreatCategoryAddForm.PerformanceLargessRate;
                    treatGeneralCardList.add(categoryPerformanceEntity);
                }
            }
        }
        if (treatTimeCardCategory != null && treatTimeCardCategory.size() > 0) {
            for (PerformanceSchemeTreatCategoryAddForm performanceSchemeTreatCategoryAddForm : treatTimeCardCategory) {
                if (performanceSchemeTreatCategoryAddForm.PerformancePayRate != null || performanceSchemeTreatCategoryAddForm.PerformanceCardRate != null || performanceSchemeTreatCategoryAddForm.PerformanceCardLargessRate != null || performanceSchemeTreatCategoryAddForm.PerformanceLargessRate != null) {
                    TreatChannelCardCategoryPerformanceEntity categoryPerformanceEntity = new TreatChannelCardCategoryPerformanceEntity();
                    categoryPerformanceEntity.ChannelPerformanceSchemeID = ID;
                    categoryPerformanceEntity.CardCategoryID = performanceSchemeTreatCategoryAddForm.CategoryID;
                    categoryPerformanceEntity.PerformancePayRate = performanceSchemeTreatCategoryAddForm.PerformancePayRate;
                    categoryPerformanceEntity.PerformanceCardRate = performanceSchemeTreatCategoryAddForm.PerformanceCardRate;
                    categoryPerformanceEntity.PerformanceCardLargessRate = performanceSchemeTreatCategoryAddForm.PerformanceCardLargessRate;
                    categoryPerformanceEntity.PerformanceLargessRate = performanceSchemeTreatCategoryAddForm.PerformanceLargessRate;
                    treatTimeCardList.add(categoryPerformanceEntity);
                }
            }
        }
        if (treatSavingCardCategory != null && treatSavingCardCategory.size() > 0) {
            for (PerformanceSchemeTreatCategoryAddForm performanceSchemeTreatCategoryAddForm : treatSavingCardCategory) {
                if (performanceSchemeTreatCategoryAddForm.PerformancePayRate != null || performanceSchemeTreatCategoryAddForm.PerformanceCardRate != null || performanceSchemeTreatCategoryAddForm.PerformanceCardLargessRate != null || performanceSchemeTreatCategoryAddForm.PerformanceLargessRate != null) {
                    TreatChannelCardCategoryPerformanceEntity categoryPerformanceEntity = new TreatChannelCardCategoryPerformanceEntity();
                    categoryPerformanceEntity.ChannelPerformanceSchemeID = ID;
                    categoryPerformanceEntity.CardCategoryID = performanceSchemeTreatCategoryAddForm.CategoryID;
                    categoryPerformanceEntity.PerformanceCardRate = performanceSchemeTreatCategoryAddForm.PerformanceCardRate;
                    categoryPerformanceEntity.PerformanceCardLargessRate = performanceSchemeTreatCategoryAddForm.PerformanceCardLargessRate;
                    treatSavingCardList.add(categoryPerformanceEntity);
                }
            }
        }
        if (productList != null && productList.size() > 0)
            channelEmployeePerformanceSchemeMapper.createSaleProductCategoryPerformanceEntity(productList);
        if (projectList != null && projectList.size() > 0)
            channelEmployeePerformanceSchemeMapper.createSaleProjectCategoryPerformanceEntity(projectList);
        if (generalCardList != null && generalCardList.size() > 0)
            channelEmployeePerformanceSchemeMapper.createSaleGeneralCardCategoryPerformanceEntity(generalCardList);
        if (timeCardList != null && timeCardList.size() > 0)
            channelEmployeePerformanceSchemeMapper.createSaleTimeCardCategoryPerformanceEntity(timeCardList);
        if (savingCardList != null && savingCardList.size() > 0)
            channelEmployeePerformanceSchemeMapper.createSaleSavingCardCategoryPerformanceEntity(savingCardList);
        if (treatProductList != null && treatProductList.size() > 0)
            channelEmployeePerformanceSchemeMapper.createTreatProductCategoryPerformanceEntity(treatProductList);
        if (treatProjectList != null && treatProjectList.size() > 0)
            channelEmployeePerformanceSchemeMapper.createTreatProjectCategoryPerformanceEntity(treatProjectList);
        if (treatGeneralCardList != null && treatGeneralCardList.size() > 0)
            channelEmployeePerformanceSchemeMapper.createTreatGeneralCardCategoryPerformanceEntity(treatGeneralCardList);
        if (treatTimeCardList != null && treatTimeCardList.size() > 0)
            channelEmployeePerformanceSchemeMapper.createTreatTimeCardCategoryPerformanceEntity(treatTimeCardList);
        if (treatSavingCardList != null && treatSavingCardList.size() > 0)
            channelEmployeePerformanceSchemeMapper.createTreatSavingCardCategoryPerformanceEntity(treatSavingCardList);
    }
}
