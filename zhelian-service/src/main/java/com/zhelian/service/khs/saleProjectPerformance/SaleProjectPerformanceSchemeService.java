package com.zhelian.service.khs.saleProjectPerformance;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.khs.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionSchemeDeleteForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionSchemeQueryForm;
import com.zhelian.model.ibeauty.form.output.SaleProductCommissionSchemeOutputForm;
import com.zhelian.model.khs.entity.SaleProjectPerformanceSchemeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class SaleProjectPerformanceSchemeService {

    @Autowired
    SaleProjectPerformanceSchemeMapper saleProjectPerformanceSchemeMapper;
    @Autowired
    SaleProjectCategoryPerformanceMapper saleProjectCategoryPerformanceMapper;
    @Autowired
    SaleProjectCategoryHandlerPerformanceMapper saleProjectCategoryHandlerPerformanceMapper;
    @Autowired
    SalePackageCardProjectCategoryHandlerPerformanceMapper salePackageCardProjectCategoryHandlerPerformanceMapper;
    @Autowired
    SaleProjectPerformanceMapper saleProjectPerformanceMapper;
    @Autowired
    SaleProjectHandlerPerformanceMapper saleProjectHandlerPerformanceMapper;
    @Autowired
    SalePackageCardProjectHandlerPerformanceMapper salePackageCardProjectHandlerPerformanceMapper;
    @Autowired
    SaleProjectSchemeHandlerPerformanceMapper saleProjectSchemeHandlerPerformanceMapper;
    @Autowired
    SalePackageCardProjectSchemeHandlerPerformanceMapper salePackageCardProjectSchemeHandlerPerformanceMapper;

    public BaseOutput saleProjectPerformanceSchemeList(SaleProductCommissionSchemeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<SaleProductCommissionSchemeOutputForm> list = saleProjectPerformanceSchemeMapper.saleProjectPerformanceSchemeList(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput createSaleProjectPerformanceScheme(SaleProductCommissionSchemeAddForm form) {
        Integer count = saleProjectPerformanceSchemeMapper.getSaleProjectPerformanceScheme(form.EntityID);
        if (count > 0)
            return BaseOutput.failed("组织单位已经存在");
        SaleProjectPerformanceSchemeEntity saleProjectPerformanceSchemeEntity = new SaleProjectPerformanceSchemeEntity();
        saleProjectPerformanceSchemeEntity.EntityID = form.EntityID;
        saleProjectPerformanceSchemeMapper.createSaleProjectPerformanceScheme(saleProjectPerformanceSchemeEntity);
        return BaseOutput.success();

    }

    public BaseOutput deleteSaleProjectPerformanceScheme(SaleProductCommissionSchemeDeleteForm form) {
        saleProjectPerformanceSchemeMapper.deleteSaleProjectPerformanceScheme(form.EntityID);
        saleProjectCategoryPerformanceMapper.deleteSaleProjectCategoryPerformanceEntity(form.EntityID);
        saleProjectCategoryHandlerPerformanceMapper.deleteSaleProjectCategoryHandlerPerformanceByEntity(form.EntityID);
        saleProjectCategoryHandlerPerformanceMapper.deleteSaleProjectCategoryHandlerJobTypePerformanceByEntity(form.EntityID);
        saleProjectPerformanceMapper.deleteSaleProjectPerformanceByEntity(form.EntityID);
        saleProjectHandlerPerformanceMapper.deleteSaleProjectHandlerPerformanceByEntity(form.EntityID);
        saleProjectHandlerPerformanceMapper.deleteSaleProjectHandlerJobTypePerformanceByEntity(form.EntityID);
        saleProjectSchemeHandlerPerformanceMapper.deleteSaleProjectSchemeHandler(form.EntityID);
        saleProjectSchemeHandlerPerformanceMapper.deleteSaleProjectSchemeHandlerJobType(form.EntityID);
        salePackageCardProjectCategoryHandlerPerformanceMapper.deletePackageCardSaleProjectCategoryHandlerPerformanceByEntity(form.EntityID);
        salePackageCardProjectCategoryHandlerPerformanceMapper.deletePackageCardSaleProjectCategoryHandlerJobTypePerformanceByEntity(form.EntityID);
        salePackageCardProjectHandlerPerformanceMapper.deleteSalePackageCardProjectHandlerPerformanceByEntity(form.EntityID);
        salePackageCardProjectHandlerPerformanceMapper.deleteSalePackageCardProjectHandlerJobTypePerformanceByEntity(form.EntityID);
        salePackageCardProjectSchemeHandlerPerformanceMapper.deleteSalePackageCardProjectSchemeHandler(form.EntityID);
        salePackageCardProjectSchemeHandlerPerformanceMapper.deleteSalePackageCardProjectSchemeHandlerJobType(form.EntityID);
        return BaseOutput.success();
    }


}