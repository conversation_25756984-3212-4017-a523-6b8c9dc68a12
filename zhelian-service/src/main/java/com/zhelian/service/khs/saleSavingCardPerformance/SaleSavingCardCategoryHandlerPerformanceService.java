package com.zhelian.service.khs.saleSavingCardPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.SalePackageCardSavingCardCategoryHandlerPerformanceMapper;
import com.zhelian.mapper.khs.SaleSavingCardCategoryHandlerPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardCategoryHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.khs.entity.SalePackageCardSavingCardCategoryHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SalePackageCardSavingCardCategoryHandlerPerformanceEntity;
import com.zhelian.model.khs.entity.SaleSavingCardCategoryHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SaleSavingCardCategoryHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleSavingCardCategoryHandlerPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class SaleSavingCardCategoryHandlerPerformanceService {

    @Autowired
    SaleSavingCardCategoryHandlerPerformanceMapper saleSavingCardCategoryHandlerPerformanceMapper;

    @Autowired
    SalePackageCardSavingCardCategoryHandlerPerformanceMapper salePackageCardSavingCardCategoryHandlerPerformanceMapper;


    public BaseOutput allSaleSavingCardCategoryHandlerPerformance(SaleSavingCardCategoryHandlerCommissionQueryForm form) {
        List<SaleSavingCardCategoryHandlerPerformanceOutputForm> list = saleSavingCardCategoryHandlerPerformanceMapper.allSaleSavingCardCategoryHandlerPerformance(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    public BaseOutput allSalePackageCardSavingCardCategoryHandlerPerformance(SaleSavingCardCategoryHandlerCommissionQueryForm form) {
        List<SaleSavingCardCategoryHandlerPerformanceOutputForm> list = salePackageCardSavingCardCategoryHandlerPerformanceMapper.allSalePackageCardSavingCardCategoryHandlerPerformance(form.EntityID, form.CategoryID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateSaleSavingCardCategoryHandlerPerformance(SaleSavingCardCategoryHandlerCommissionAddForm form) {


        saleSavingCardCategoryHandlerPerformanceMapper.deleteSaleSavingCardCategoryHandler(form.EntityID, form.CategoryID);
        saleSavingCardCategoryHandlerPerformanceMapper.deleteSaleSavingCardCategoryHandlerJobType(form.EntityID, form.CategoryID);
        List list = new ArrayList();
        List handlerJobTypePerformanceList = new ArrayList<>();
        if (form.SavingCardHandler != null && form.SavingCardHandler.size() > 0) {
            for (SaleSavingCardCategoryHandlerAddForm saleSavingCardCategoryHandlerAddForm : form.SavingCardHandler) {
                if (saleSavingCardCategoryHandlerAddForm.PayFixed != null || saleSavingCardCategoryHandlerAddForm.PayRate != null ||
                        saleSavingCardCategoryHandlerAddForm.SpecialBenefit != null) {
                    SaleSavingCardCategoryHandlerPerformanceEntity saleSavingCardCategoryHandlerPerformanceEntity = new SaleSavingCardCategoryHandlerPerformanceEntity();
                    saleSavingCardCategoryHandlerPerformanceEntity.EntityID = form.EntityID;
                    saleSavingCardCategoryHandlerPerformanceEntity.SavingCardCategoryID = form.CategoryID;
                    saleSavingCardCategoryHandlerPerformanceEntity.SavingCardSaleHandlerID = saleSavingCardCategoryHandlerAddForm.SaleHandlerID;
                    saleSavingCardCategoryHandlerPerformanceEntity.PayRate = saleSavingCardCategoryHandlerAddForm.PayRate;
                    list.add(saleSavingCardCategoryHandlerPerformanceEntity);
                }
                if (saleSavingCardCategoryHandlerAddForm.HandlerJobTypeCommission != null && saleSavingCardCategoryHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleSavingCardCategoryHandlerJobTypeCommissionAddForm saleSavingCardCategoryHandlerJobTypePerformanceAddForm : saleSavingCardCategoryHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleSavingCardCategoryHandlerJobTypePerformanceAddForm.PayFixed != null || saleSavingCardCategoryHandlerJobTypePerformanceAddForm.PayRate != null ||
                                saleSavingCardCategoryHandlerJobTypePerformanceAddForm.SpecialBenefit != null) {
                            SaleSavingCardCategoryHandlerJobTypePerformanceEntity saleSavingCardCategoryHandlerJobTypePerformanceEntity = new SaleSavingCardCategoryHandlerJobTypePerformanceEntity();
                            saleSavingCardCategoryHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            saleSavingCardCategoryHandlerJobTypePerformanceEntity.SavingCardCategoryID = form.CategoryID;
                            saleSavingCardCategoryHandlerJobTypePerformanceEntity.SavingCardSaleHandlerID = saleSavingCardCategoryHandlerAddForm.SaleHandlerID;
                            saleSavingCardCategoryHandlerJobTypePerformanceEntity.JobTypeID = saleSavingCardCategoryHandlerJobTypePerformanceAddForm.JobTypeID;
                            saleSavingCardCategoryHandlerJobTypePerformanceEntity.PayRate = saleSavingCardCategoryHandlerJobTypePerformanceAddForm.PayRate;
                            handlerJobTypePerformanceList.add(saleSavingCardCategoryHandlerJobTypePerformanceEntity);
                        }
                    }
                }
            }
        }

        salePackageCardSavingCardCategoryHandlerPerformanceMapper.deleteSalePackageCardSavingCardCategoryHandler(form.EntityID, form.CategoryID);
        salePackageCardSavingCardCategoryHandlerPerformanceMapper.deleteSalePackageCardSavingCardCategoryHandlerJobType(form.EntityID, form.CategoryID);
        List packageList = new ArrayList();
        List packageCardHandlerJobTypePerformanceList = new ArrayList<>();
        if (form.PackageCardSavingCardHandler != null && form.PackageCardSavingCardHandler.size() > 0) {
            for (SaleSavingCardCategoryHandlerAddForm saleSavingCardCategoryHandlerAddForm : form.PackageCardSavingCardHandler) {
                if (saleSavingCardCategoryHandlerAddForm.PayFixed != null || saleSavingCardCategoryHandlerAddForm.PayRate != null ||
                        saleSavingCardCategoryHandlerAddForm.SpecialBenefit != null) {
                    SalePackageCardSavingCardCategoryHandlerPerformanceEntity salePackageCardSavingCardCategoryHandlerPerformanceEntity = new SalePackageCardSavingCardCategoryHandlerPerformanceEntity();
                    salePackageCardSavingCardCategoryHandlerPerformanceEntity.EntityID = form.EntityID;
                    salePackageCardSavingCardCategoryHandlerPerformanceEntity.SavingCardCategoryID = form.CategoryID;
                    salePackageCardSavingCardCategoryHandlerPerformanceEntity.PackageCardSaleHandlerID = saleSavingCardCategoryHandlerAddForm.SaleHandlerID;
                    salePackageCardSavingCardCategoryHandlerPerformanceEntity.PayRate = saleSavingCardCategoryHandlerAddForm.PayRate;
                    packageList.add(salePackageCardSavingCardCategoryHandlerPerformanceEntity);
                }
                if (saleSavingCardCategoryHandlerAddForm.HandlerJobTypeCommission != null && saleSavingCardCategoryHandlerAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (SaleSavingCardCategoryHandlerJobTypeCommissionAddForm saleSavingCardCategoryHandlerJobTypePerformanceAddForm : saleSavingCardCategoryHandlerAddForm.HandlerJobTypeCommission) {
                        if (saleSavingCardCategoryHandlerJobTypePerformanceAddForm.PayFixed != null || saleSavingCardCategoryHandlerJobTypePerformanceAddForm.PayRate != null ||
                                saleSavingCardCategoryHandlerJobTypePerformanceAddForm.SpecialBenefit != null) {
                            SalePackageCardSavingCardCategoryHandlerJobTypePerformanceEntity salePackageCardSavingCardCategoryHandlerJobTypePerformanceEntity = new SalePackageCardSavingCardCategoryHandlerJobTypePerformanceEntity();
                            salePackageCardSavingCardCategoryHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            salePackageCardSavingCardCategoryHandlerJobTypePerformanceEntity.SavingCardCategoryID = form.CategoryID;
                            salePackageCardSavingCardCategoryHandlerJobTypePerformanceEntity.PackageCardSaleHandlerID = saleSavingCardCategoryHandlerAddForm.SaleHandlerID;
                            salePackageCardSavingCardCategoryHandlerJobTypePerformanceEntity.JobTypeID = saleSavingCardCategoryHandlerJobTypePerformanceAddForm.JobTypeID;
                            salePackageCardSavingCardCategoryHandlerJobTypePerformanceEntity.PayRate = saleSavingCardCategoryHandlerJobTypePerformanceAddForm.PayRate;
                            packageCardHandlerJobTypePerformanceList.add(salePackageCardSavingCardCategoryHandlerJobTypePerformanceEntity);
                        }
                    }
                }
            }
        }
        if (list != null && list.size() > 0) {
            List<List<SaleSavingCardCategoryHandlerPerformanceEntity>> subs = Lists.partition(list, 20);
            for (List<SaleSavingCardCategoryHandlerPerformanceEntity> entityList : subs) {
                saleSavingCardCategoryHandlerPerformanceMapper.insertSaleSavingCardCategoryHandlerPerformance(entityList);
            }
        }
        if (handlerJobTypePerformanceList != null && handlerJobTypePerformanceList.size() > 0) {
            List<List<SaleSavingCardCategoryHandlerJobTypePerformanceEntity>> subs = Lists.partition(handlerJobTypePerformanceList, 20);
            for (List<SaleSavingCardCategoryHandlerJobTypePerformanceEntity> entityList : subs) {
                saleSavingCardCategoryHandlerPerformanceMapper.insertSaleSavingCardCategoryHandlerJobTypePerformance(entityList);
            }
        }
        if (packageList != null && packageList.size() > 0) {
            List<List<SalePackageCardSavingCardCategoryHandlerPerformanceEntity>> subs = Lists.partition(packageList, 20);
            for (List<SalePackageCardSavingCardCategoryHandlerPerformanceEntity> entityList : subs) {
                salePackageCardSavingCardCategoryHandlerPerformanceMapper.insertSalePackageCardSavingCardCategoryHandlerPerformance(entityList);
            }
        }
        if (packageCardHandlerJobTypePerformanceList != null && packageCardHandlerJobTypePerformanceList.size() > 0) {
            List<List<SalePackageCardSavingCardCategoryHandlerJobTypePerformanceEntity>> subs = Lists.partition(packageCardHandlerJobTypePerformanceList, 20);
            for (List<SalePackageCardSavingCardCategoryHandlerJobTypePerformanceEntity> entityList : subs) {
                salePackageCardSavingCardCategoryHandlerPerformanceMapper.insertSalePackageCardSavingCardCategoryHandlerJobTypePerformance(entityList);
            }
        }
        return BaseOutput.success();
    }
}