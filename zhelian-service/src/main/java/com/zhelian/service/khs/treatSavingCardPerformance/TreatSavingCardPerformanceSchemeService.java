package com.zhelian.service.khs.treatSavingCardPerformance;

import com.github.pagehelper.PageHelper;
import com.zhelian.mapper.khs.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCommissionSchemeForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCommissionSchemeQueryForm;
import com.zhelian.model.ibeauty.form.output.TreatSavingCardProjectCommissionSchemeOutputForm;
import com.zhelian.model.khs.entity.TreatSavingCardPerformanceSchemeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class TreatSavingCardPerformanceSchemeService {

    @Autowired
    TreatSavingCardPerformanceSchemeMapper treatSavingCardPerformanceSchemeMapper;

    @Autowired
    TreatSavingCardCategoryPerformanceMapper treatSavingCardCategoryPerformanceMapper;
    @Autowired
    TreatSavingCardCategoryHandlerPerformanceMapper treatSavingCardCategoryHandlerPerformanceMapper;
    @Autowired
    TreatSavingCardPerformanceMapper treatSavingCardPerformanceMapper;
    @Autowired
    TreatSavingCardHandlerPerformanceMapper treatSavingCardHandlerPerformanceMapper;
    @Autowired
    TreatSavingCardSchemeHandlerPerformanceMapper treatSavingCardSchemeHandlerPerformanceMapper;

    public BaseOutput treatSavingCardPerformanceSchemeList(TreatSavingCardProjectCommissionSchemeQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        List<TreatSavingCardProjectCommissionSchemeOutputForm> list = treatSavingCardPerformanceSchemeMapper.treatSavingCardPerformanceSchemeList(form.Name);
        return BasePageInfo.success(list);
    }

    public BaseOutput createTreatSavingCardPerformanceScheme(TreatSavingCardProjectCommissionSchemeForm form) {
        Integer count = treatSavingCardPerformanceSchemeMapper.getTreatSavingCardPerformanceScheme(form.EntityID);
        if (count > 0)
            return BaseOutput.failed("组织单位已经存在");
        TreatSavingCardPerformanceSchemeEntity treatSavingCardPerformanceSchemeEntity = new TreatSavingCardPerformanceSchemeEntity();
        treatSavingCardPerformanceSchemeEntity.EntityID = form.EntityID;
        treatSavingCardPerformanceSchemeMapper.createTreatSavingCardPerformanceScheme(treatSavingCardPerformanceSchemeEntity);
        return BaseOutput.success();

    }

    public BaseOutput deleteTreatSavingCardPerformanceScheme(TreatSavingCardProjectCommissionSchemeForm form) {
        treatSavingCardPerformanceSchemeMapper.deleteTreatSavingCardPerformanceScheme(form.EntityID);
        treatSavingCardCategoryPerformanceMapper.deleteTreatSavingCardCategoryPerformanceEntity(form.EntityID);
        treatSavingCardCategoryHandlerPerformanceMapper.deleteTreatCategoryHandlerPerformanceByEntity(form.EntityID);
        treatSavingCardCategoryHandlerPerformanceMapper.deleteTreatCategoryHandlerJobTypePerformanceByEntity(form.EntityID);
        treatSavingCardPerformanceMapper.deleteTreatSavingCardPerformanceByEntity(form.EntityID);
        treatSavingCardHandlerPerformanceMapper.deleteTreatHandlerPerformanceByEntity(form.EntityID);
        treatSavingCardHandlerPerformanceMapper.deleteTreatHandlerJobTypePerformanceByEntity(form.EntityID);
        treatSavingCardSchemeHandlerPerformanceMapper.deleteTreatSavingCardSchemeHandler(form.EntityID);
        treatSavingCardSchemeHandlerPerformanceMapper.deleteTreatSavingCardSchemeHandlerJobType(form.EntityID);

        return BaseOutput.success();
    }
}