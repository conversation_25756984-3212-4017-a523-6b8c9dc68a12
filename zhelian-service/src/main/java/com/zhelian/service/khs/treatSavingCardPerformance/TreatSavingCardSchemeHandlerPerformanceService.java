package com.zhelian.service.khs.treatSavingCardPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.TreatSavingCardSchemeHandlerPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectSchemeHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectSchemeHandlersCommissionAddForm;
import com.zhelian.model.ibeauty.form.output.TreatSavingCardProjectHandlerCommissionOutputForm;
import com.zhelian.model.khs.entity.TreatSavingCardSchemeHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.TreatSavingCardSchemeHandlerPerformanceEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class TreatSavingCardSchemeHandlerPerformanceService {

    @Autowired
    TreatSavingCardSchemeHandlerPerformanceMapper treatSavingCardSchemeHandlerPerformanceMapper;

    public BaseOutput allTreatSavingCardSchemeHandlerPerformance(TreatSavingCardProjectSchemeHandlerCommissionQueryForm form) {
        List<TreatSavingCardProjectHandlerCommissionOutputForm> list = treatSavingCardSchemeHandlerPerformanceMapper.allTreatSavingCardSchemeHandlerPerformance(form.EntityID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatSchemeHandlerPerformance(TreatSavingCardProjectSchemeHandlersCommissionAddForm form) {

        treatSavingCardSchemeHandlerPerformanceMapper.deleteTreatSavingCardSchemeHandler(form.EntityID);
        treatSavingCardSchemeHandlerPerformanceMapper.deleteTreatSavingCardSchemeHandlerJobType(form.EntityID);

        if (form.Handler != null && form.Handler.size() > 0) {

            List listTreatSavingCardSchemeHandlerPerformanceEntity = new ArrayList<>();

            List listTreatSavingCardSchemeHandlerJobTypePerformanceEntity = new ArrayList<>();

            for (TreatSavingCardProjectHandlerCommissionAddForm treatSavingCardProjectHandlerPerformanceAddForm : form.Handler) {
                if (treatSavingCardProjectHandlerPerformanceAddForm.Rate != null ||
                        treatSavingCardProjectHandlerPerformanceAddForm.LargessRate != null) {
                    TreatSavingCardSchemeHandlerPerformanceEntity treatSavingCardProjectSchemeHandlerPerformanceEntity = new TreatSavingCardSchemeHandlerPerformanceEntity();
                    treatSavingCardProjectSchemeHandlerPerformanceEntity.EntityID = form.EntityID;
                    treatSavingCardProjectSchemeHandlerPerformanceEntity.SavingCardProjectTreatHandlerID = treatSavingCardProjectHandlerPerformanceAddForm.TreatHandlerID;
                    treatSavingCardProjectSchemeHandlerPerformanceEntity.CardRate = treatSavingCardProjectHandlerPerformanceAddForm.Rate;
                    treatSavingCardProjectSchemeHandlerPerformanceEntity.CardLargessRate = treatSavingCardProjectHandlerPerformanceAddForm.LargessRate;
                    listTreatSavingCardSchemeHandlerPerformanceEntity.add(treatSavingCardProjectSchemeHandlerPerformanceEntity);
                }

                if (treatSavingCardProjectHandlerPerformanceAddForm.HandlerJobTypeCommission != null && treatSavingCardProjectHandlerPerformanceAddForm.HandlerJobTypeCommission.size() > 0) {
                    for (TreatSavingCardProjectCategoryHandlerJobTypeCommissionAddForm treatSavingCardProjectCategoryHandlerJobTypePerformanceAddForm : treatSavingCardProjectHandlerPerformanceAddForm.HandlerJobTypeCommission) {
                        if (treatSavingCardProjectCategoryHandlerJobTypePerformanceAddForm.Rate != null || treatSavingCardProjectCategoryHandlerJobTypePerformanceAddForm.Fixed != null ||
                                treatSavingCardProjectCategoryHandlerJobTypePerformanceAddForm.LargessRate != null || treatSavingCardProjectCategoryHandlerJobTypePerformanceAddForm.LargessFixed != null ||
                                treatSavingCardProjectCategoryHandlerJobTypePerformanceAddForm.SpecialBenefit != null) {
                            TreatSavingCardSchemeHandlerJobTypePerformanceEntity treatSavingCardProjectSchemeHandlerJobTypePerformanceEntity = new TreatSavingCardSchemeHandlerJobTypePerformanceEntity();
                            treatSavingCardProjectSchemeHandlerJobTypePerformanceEntity.EntityID = form.EntityID;
                            treatSavingCardProjectSchemeHandlerJobTypePerformanceEntity.SavingCardProjectTreatHandlerID = treatSavingCardProjectHandlerPerformanceAddForm.TreatHandlerID;
                            treatSavingCardProjectSchemeHandlerJobTypePerformanceEntity.JobTypeID = treatSavingCardProjectCategoryHandlerJobTypePerformanceAddForm.JobTypeID;
                            treatSavingCardProjectSchemeHandlerJobTypePerformanceEntity.CardRate = treatSavingCardProjectCategoryHandlerJobTypePerformanceAddForm.Rate;
                            treatSavingCardProjectSchemeHandlerJobTypePerformanceEntity.CardLargessRate = treatSavingCardProjectCategoryHandlerJobTypePerformanceAddForm.LargessRate;

                            listTreatSavingCardSchemeHandlerJobTypePerformanceEntity.add(treatSavingCardProjectSchemeHandlerJobTypePerformanceEntity);
                        }
                    }

                }
            }
            if (listTreatSavingCardSchemeHandlerPerformanceEntity != null && listTreatSavingCardSchemeHandlerPerformanceEntity.size() > 0) {
                List<List<TreatSavingCardSchemeHandlerPerformanceEntity>> subs = Lists.partition(listTreatSavingCardSchemeHandlerPerformanceEntity, 20);
                for (List<TreatSavingCardSchemeHandlerPerformanceEntity> entityList : subs) {
                    treatSavingCardSchemeHandlerPerformanceMapper.insertTreatSavingCardSchemeHandlerPerformance(entityList);
                }
            }
            if (listTreatSavingCardSchemeHandlerJobTypePerformanceEntity != null && listTreatSavingCardSchemeHandlerJobTypePerformanceEntity.size() > 0) {
                List<List<TreatSavingCardSchemeHandlerJobTypePerformanceEntity>> subs = Lists.partition(listTreatSavingCardSchemeHandlerJobTypePerformanceEntity, 20);
                for (List<TreatSavingCardSchemeHandlerJobTypePerformanceEntity> entityList : subs) {
                    treatSavingCardSchemeHandlerPerformanceMapper.insertTreatSavingCardSchemeHandlerJobTypePerformance(entityList);
                }
            }
        }
        return BaseOutput.success();
    }
}