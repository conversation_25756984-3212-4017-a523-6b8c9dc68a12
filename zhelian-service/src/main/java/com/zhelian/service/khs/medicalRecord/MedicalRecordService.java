package com.zhelian.service.khs.medicalRecord;

import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.mapper.khs.MedicalRecordMapper;
import com.zhelian.mapper.khs.MedicalRecordCategoryMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.khs.entity.MedicalRecordCatalogEntity;
import com.zhelian.model.khs.entity.MedicalRecordCategoryEntity;
import com.zhelian.model.khs.entity.MedicalRecordTemplateEntity;
import com.zhelian.model.khs.form.input.*;
import com.zhelian.model.khs.form.output.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class MedicalRecordService {


    @Autowired
    MedicalRecordMapper medicalRecordMapper;

    @Autowired
    MedicalRecordCategoryMapper medicalRecordCategoryMapper;

    public BaseOutput list(MedicalRecordCatalogQueryForm form) {
        List<MedicalRecordOutForm> outForm = medicalRecordMapper.list(form.Name,form.CategoryID);
        return BaseOutputForm.success(outForm);
    }

    @Transactional
    public BaseOutput createCatalog(MedicalRecordCatalogAddForm form) {
        Integer Sequence = medicalRecordMapper.getCatalogMaxSequence();
        MedicalRecordCatalogEntity entity = new MedicalRecordCatalogEntity();
        entity.Name = form.Name;
        entity.Sequence = Sequence != null ? Sequence + 1 : 1;
        entity.MedicalRecordCategoryID = form.CategoryID ;
        medicalRecordMapper.createCatalog(entity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput updateCatalog(MedicalRecordCatalogUpdateForm form) {

        MedicalRecordCatalogEntity entity = new MedicalRecordCatalogEntity();
        entity.ID = form.ID;
        entity.Name = form.Name;
        entity.MedicalRecordCategoryID = form.CategoryID ;
        medicalRecordMapper.updateCatalog(entity);

        //更新目录下的病例
        List<MedicalRecordTemplateEntity> templateEntityList = medicalRecordMapper.getMedicalRecordTemplateByCatalogID(entity.ID);
        for (MedicalRecordTemplateEntity medicalRecordTemplateEntity : templateEntityList) {
            medicalRecordTemplateEntity.MedicalRecordCategoryID = entity.MedicalRecordCategoryID;
            medicalRecordTemplateEntity.MedicalRecordCatalogID = entity.ID;
            medicalRecordMapper.updateTemplate(medicalRecordTemplateEntity);
        }

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput deleteCatalog(MedicalRecordCatalogDeleteForm form) {
        List<MedicalRecordTemplateEntity> templateEntityList = medicalRecordMapper.getMedicalRecordTemplateByCatalogID(form.ID);
        if (templateEntityList != null && templateEntityList.size() > 0) {
            return BaseOutput.success("分类下存在模版无法删除");
        }
        medicalRecordMapper.deleteCatalog(form.ID);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput moveCatalog(MedicalRecordCatalogMoveForm form) {

        MedicalRecordCategoryEntity medicalRecordCategory = medicalRecordCategoryMapper.getMedicalRecordCategoryByID(form.CategoryID);
        if (medicalRecordCategory == null)
            return BaseOutput.failed("上级分类不存在");

        MedicalRecordCatalogEntity medicalRecordCatalogEntity = medicalRecordMapper.getMedicalRecordCatalogByID(form.MoveID);

        if (medicalRecordCatalogEntity == null)
            return BaseOutput.failed("目录不存在");

        medicalRecordCatalogEntity.MedicalRecordCategoryID = form.CategoryID;

        //重新排序，正序查出列表
        List<MedicalRecordCatalogEntity> list = medicalRecordMapper.medicalRecordCatalogEntityListByIDAndCategoryID(form.CategoryID, form.MoveID);

        if (list == null) {
            list = new ArrayList<>();
            list.add(medicalRecordCatalogEntity);
        } else {
            //前面有单位的话，顶替前面单位，没有单位的话则插入最后一个，最终根据下标重新排序
            if (form.BeforeID != null) {
                MedicalRecordCatalogEntity beforeMedicalRecordCatalog = new MedicalRecordCatalogEntity();

                for (MedicalRecordCatalogEntity entity1 : list) {
                    if (entity1.ID.equals(form.BeforeID)) {
                        beforeMedicalRecordCatalog = entity1;
                    }
                }
                if (beforeMedicalRecordCatalog == null)
                    return BaseOutput.failed("前面单位不存在");

                list.add(list.indexOf(beforeMedicalRecordCatalog), medicalRecordCatalogEntity);

            } else {
                //插入最后一位
                list.add(medicalRecordCatalogEntity);
            }
        }

        for (MedicalRecordCatalogEntity entity1 : list) {
            entity1.Sequence = list.indexOf(entity1) + 1;
        }

        medicalRecordMapper.updateCatalogSequence(list);

        return BaseOutput.success();
    }

    public BaseOutput getTemplate(MedicalRecordTemplateQueryForm form) {
        MedicalRecordTemplateOutForm outForm = medicalRecordMapper.getTemplate(form.ID);
        return BaseOutputForm.success(outForm);
    }

    @Transactional
    public BaseOutput createTemplate(MedicalRecordTemplateAddForm form) {
        Integer Sequence = medicalRecordMapper.getTemplateMaxSequence();
        MedicalRecordTemplateEntity entity = new MedicalRecordTemplateEntity();
        entity.Name = form.Name;
        entity.Sequence = Sequence != null ? Sequence + 1 : 1;
        entity.MedicalRecordCategoryID = form.CategoryID;
        entity.MedicalRecordCatalogID = form.CatalogID;
        entity.TemplateContent = form.Content;
        entity.TemplatePrintJSON = form.PrintJSON;
        entity.CreatedBy = SystemUserUtil.getSystemUserID();
        entity.CreatedOn = new Date();
        medicalRecordMapper.createTemplate(entity);
        MedicalRecordTemplateCreateOutForm outForm = new MedicalRecordTemplateCreateOutForm();
        outForm.ID = entity.ID;
        outForm.MedicalRecordCatalogID = entity.MedicalRecordCatalogID;
        return BaseOutputForm.success(outForm);
    }

    @Transactional
    public BaseOutput updateTemplate(MedicalRecordTemplateUpdateForm form) {
        MedicalRecordTemplateEntity entity = new MedicalRecordTemplateEntity();
        entity.ID = form.ID;
        entity.Name = form.Name;
        entity.TemplateContent = form.Content;
        entity.TemplatePrintJSON = form.PrintJSON;
        entity.MedicalRecordCategoryID = form.CategoryID;
        entity.MedicalRecordCatalogID = form.CatalogID;
        medicalRecordMapper.updateTemplate(entity);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput deleteTemplate(MedicalRecordTemplateDeleteForm form) {
        medicalRecordMapper.deleteTemplate(form.ID);
        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput moveTemplate(MedicalRecordTemplateMoveForm form) {

        MedicalRecordCatalogEntity medicalRecordCatalogEntity = medicalRecordMapper.getMedicalRecordCatalogByID(form.CatalogID);
        if (medicalRecordCatalogEntity == null)
            return BaseOutput.failed("上级目录不存在");

        MedicalRecordTemplateEntity medicalRecordTemplateEntity = medicalRecordMapper.getMedicalRecordTemplateByID(form.MoveID);

        if (medicalRecordTemplateEntity == null)
            return BaseOutput.failed("模版不存在");

        medicalRecordTemplateEntity.MedicalRecordCatalogID = form.CatalogID;
        medicalRecordTemplateEntity.MedicalRecordCategoryID = medicalRecordCatalogEntity.MedicalRecordCategoryID;

        //重新排序，正序查出列表
        List<MedicalRecordTemplateEntity> list = medicalRecordMapper.medicalRecordTemplateEntityListByIDAndCatalogID(form.CatalogID, form.MoveID);

        if (list == null) {
            list = new ArrayList<>();
            list.add(medicalRecordTemplateEntity);
        } else {
            //前面有单位的话，顶替前面单位，没有单位的话则插入最后一个，最终根据下标重新排序
            if (form.BeforeID != null) {
                MedicalRecordTemplateEntity beforeMedicalRecordTemplate = new MedicalRecordTemplateEntity();

                for (MedicalRecordTemplateEntity entity1 : list) {
                    if (entity1.ID.equals(form.BeforeID)) {
                        beforeMedicalRecordTemplate = entity1;
                    }
                }
                if (beforeMedicalRecordTemplate == null)
                    return BaseOutput.failed("前面单位不存在");

                list.add(list.indexOf(beforeMedicalRecordTemplate), medicalRecordTemplateEntity);

            } else {
                //插入最后一位
                list.add(medicalRecordTemplateEntity);
            }
        }

        for (MedicalRecordTemplateEntity entity1 : list) {
            entity1.Sequence = list.indexOf(entity1) + 1;
        }

        medicalRecordMapper.updateTemplateSequence(list);

        return BaseOutput.success();
    }

    public BaseOutput categoryAll() {
        List<MedicalRecordCategoryAllOutForm> outForm = medicalRecordMapper.categoryAll();
        return BaseOutputForm.success(outForm);
    }

    public BaseOutput labelContentAll(MedicalRecordLabelContentAllQueryForm form) {
        List<MedicalRecordLabelContentAllOutForm> outForm = medicalRecordMapper.labelContentAll(form.CategoryID);
        return BaseOutputForm.success(outForm);
    }

}
