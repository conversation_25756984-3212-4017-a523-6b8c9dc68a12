package com.zhelian.service.khs.treatGeneralCardPerformance;

import com.google.common.collect.Lists;
import com.zhelian.mapper.khs.TreatGeneralCardHandlerPerformanceMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatCardHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardHandlersCommissionAddForm;
import com.zhelian.model.khs.entity.TreatGeneralCardHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.TreatGeneralCardHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.TreatGoodCategoryHandlerPerformanceOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class TreatGeneralCardHandlerPerformanceService {

    @Autowired
    TreatGeneralCardHandlerPerformanceMapper treatGeneralCardHandlerPerformanceMapper;


    public BaseOutput allTreatGeneralCardHandlerPerformance(TreatCardCommissionQueryForm form) {
        List<TreatGoodCategoryHandlerPerformanceOutputForm> list = treatGeneralCardHandlerPerformanceMapper.allTreatGeneralCardHandlerPerformance(form.EntityID, form.CardID);
        return BaseOutputForm.success(list);
    }

    @Transactional
    public BaseOutput updateTreatGeneralCardHandlerPerformance(TreatCardHandlersCommissionAddForm form) {

        treatGeneralCardHandlerPerformanceMapper.deleteTreatGeneralCardHandler(form.EntityID, form.CardID);
        treatGeneralCardHandlerPerformanceMapper.deleteTreatGeneralCardHandlerJobType(form.EntityID, form.CardID);
        List<TreatGeneralCardHandlerPerformanceEntity> treatGeneralCardHandlerPerformanceList = new ArrayList<>();
        List<TreatGeneralCardHandlerJobTypePerformanceEntity> treatGeneralCardHandlerJobTypePerformanceList = new ArrayList<>();
        if (form.CardHandler != null && form.CardHandler.size() > 0) {
            for (TreatCardHandlerCommissionAddForm treatCardHandlerPerformanceAddForm : form.CardHandler) {
                if (treatCardHandlerPerformanceAddForm.PayRate != null ||
                        treatCardHandlerPerformanceAddForm.CardRate != null ||
                        treatCardHandlerPerformanceAddForm.CardLargessRate != null ||
                        treatCardHandlerPerformanceAddForm.LargessRate != null) {
                    TreatGeneralCardHandlerPerformanceEntity treatGeneralCardHandlerPerformanceEntity = new TreatGeneralCardHandlerPerformanceEntity();
                    treatGeneralCardHandlerPerformanceEntity.EntityID = form.EntityID;
                    treatGeneralCardHandlerPerformanceEntity.GeneralCardID = form.CardID;
                    treatGeneralCardHandlerPerformanceEntity.GeneralCardProjectTreatHandlerID = treatCardHandlerPerformanceAddForm.TreatHandlerID;
                    treatGeneralCardHandlerPerformanceEntity.PayRate = treatCardHandlerPerformanceAddForm.PayRate;
                    treatGeneralCardHandlerPerformanceEntity.CardRate = treatCardHandlerPerformanceAddForm.CardRate;
                    treatGeneralCardHandlerPerformanceEntity.CardLargessRate = treatCardHandlerPerformanceAddForm.CardLargessRate;
                    treatGeneralCardHandlerPerformanceEntity.LargessRate = treatCardHandlerPerformanceAddForm.LargessRate;
                    treatGeneralCardHandlerPerformanceList.add(treatGeneralCardHandlerPerformanceEntity);
                }
                if (treatCardHandlerPerformanceAddForm.HandlerJobTypeCommission != null && treatCardHandlerPerformanceAddForm.HandlerJobTypeCommission.size() > 0) {

                    for (TreatCardCategoryHandlerJobTypeCommissionAddForm typePerformanceAddForm : treatCardHandlerPerformanceAddForm.HandlerJobTypeCommission) {
                        if (typePerformanceAddForm.PayRate != null ||
                                typePerformanceAddForm.CardRate != null ||
                                typePerformanceAddForm.CardLargessRate != null ||
                                typePerformanceAddForm.LargessRate != null) {
                            TreatGeneralCardHandlerJobTypePerformanceEntity PerformanceEntity = new TreatGeneralCardHandlerJobTypePerformanceEntity();
                            PerformanceEntity.EntityID = form.EntityID;
                            PerformanceEntity.GeneralCardID = form.CardID;
                            PerformanceEntity.GeneralCardProjectTreatHandlerID = treatCardHandlerPerformanceAddForm.TreatHandlerID;
                            PerformanceEntity.JobTypeID = typePerformanceAddForm.JobTypeID;
                            PerformanceEntity.PayRate = typePerformanceAddForm.PayRate;
                            PerformanceEntity.CardRate = typePerformanceAddForm.CardRate;
                            PerformanceEntity.CardLargessRate = typePerformanceAddForm.CardLargessRate;
                            PerformanceEntity.LargessRate = typePerformanceAddForm.LargessRate;
                            treatGeneralCardHandlerJobTypePerformanceList.add(PerformanceEntity);
                        }
                    }
                }
            }
        }
        if (treatGeneralCardHandlerPerformanceList != null && treatGeneralCardHandlerPerformanceList.size() > 0) {
            List<List<TreatGeneralCardHandlerPerformanceEntity>> subs = Lists.partition(treatGeneralCardHandlerPerformanceList, 20);
            for (List<TreatGeneralCardHandlerPerformanceEntity> entityList : subs) {
                treatGeneralCardHandlerPerformanceMapper.insertTreatGeneralCardHandlerPerformance(entityList);
            }
        }

        if (treatGeneralCardHandlerJobTypePerformanceList != null && treatGeneralCardHandlerJobTypePerformanceList.size() > 0) {
            List<List<TreatGeneralCardHandlerJobTypePerformanceEntity>> subs = Lists.partition(treatGeneralCardHandlerJobTypePerformanceList, 20);
            for (List<TreatGeneralCardHandlerJobTypePerformanceEntity> entityList : subs) {
                treatGeneralCardHandlerPerformanceMapper.insertTreatGeneralCardHandlerJobTypePerformance(entityList);
            }
        }


        return BaseOutput.success();
    }
}