package com.zhelian.service.micromall.wechat;

import cn.binarywang.wx.miniapp.api.WxMaCodeService;
import cn.binarywang.wx.miniapp.bean.code.WxMaCodeCommitRequest;
import cn.binarywang.wx.miniapp.bean.code.WxMaCodeExtConfig;
import cn.binarywang.wx.miniapp.bean.code.WxMaCodeSubmitAuditItem;
import cn.binarywang.wx.miniapp.bean.code.WxMaCodeSubmitAuditRequest;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.zhelian.core.authorization.user.BasicAuthenticationUser;
import com.zhelian.core.authorization.user.SystemUser;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.weixin.open.service.WxOpenService;
import com.zhelian.mapper.enterprise.EnterpriseMiniprogramMapper;
import com.zhelian.mapper.enterprise.EnterpriseMiniprogramTemplateVersionMapper;
import com.zhelian.mapper.enterprise.EnterpriseWxPayMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.enterprise.entity.EnterpriseMiniprogramEntity;
import com.zhelian.model.enterprise.entity.EnterpriseMiniprogramTemplateVersionEntity;
import com.zhelian.model.micromall.form.output.MiniProgramAuthInformation;
import com.zhelian.model.wechat.form.output.PreAuthUrl;
import com.zhelian.model.wechat.form.output.Privacy;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.open.api.impl.WxOpenMaPrivacyServiceImpl;
import me.chanjar.weixin.open.bean.ma.privacy.GetPrivacySettingResult;
import me.chanjar.weixin.open.bean.ma.privacy.PrivacyOwnerSetting;
import me.chanjar.weixin.open.bean.ma.privacy.SetPrivacySetting;
import me.chanjar.weixin.open.bean.result.WxOpenQueryAuthResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class MiniProgramService {
    @Autowired
    protected WxOpenService wxOpenService;
    @Autowired
    private EnterpriseMiniprogramMapper enterpriseOplatformMapper;
    @Autowired
    private EnterpriseMiniprogramTemplateVersionMapper enterpriseMiniprogramTemplateVersionMapper;
    @Autowired
    private EnterpriseWxPayMapper enterpriseWxPayMapper;

    public BaseOutput getComponentloginpage(String token, String host) {
        try {
            PreAuthUrl preAuthUrl = new PreAuthUrl();
            preAuthUrl.URL = wxOpenService.getWxOpenComponentService().getPreAuthUrl(host + "/api/open/micromall/miniprogram/authverify?Authorization=" + token, "2", null);
            return BaseOutputForm.success(preAuthUrl);
        } catch (Exception e) {
            return BaseOutput.failed(e.getMessage());
        }
    }

    public BaseOutput authVerify(String Authorization, String authorizationCode) {
        try {
            BasicAuthenticationUser basicAuthenticationUser = new BasicAuthenticationUser();
            SystemUser user = basicAuthenticationUser.getSystemUser(Authorization);
            WxOpenQueryAuthResult wxOpenQueryAuthResult = wxOpenService.getWxOpenComponentService().getQueryAuth(authorizationCode);
            EnterpriseMiniprogramEntity enterpriseMiniprogramEntity = enterpriseOplatformMapper.getEnterpriseMiniprogram(user.EnterpriseCode);
            if (enterpriseMiniprogramEntity != null) {
                enterpriseMiniprogramEntity.AuthorizerAppID = wxOpenQueryAuthResult.getAuthorizationInfo().getAuthorizerAppid();
                enterpriseMiniprogramEntity.AuthorizationCode = authorizationCode;
                enterpriseMiniprogramEntity.IsPublish = false;
                enterpriseOplatformMapper.updateEnterpriseMiniprogram(enterpriseMiniprogramEntity);
                return BaseOutput.success();
            } else {
                enterpriseMiniprogramEntity = new EnterpriseMiniprogramEntity();
                enterpriseMiniprogramEntity.EnterpriseCode = user.EnterpriseCode;
                enterpriseMiniprogramEntity.AuthorizerAppID = wxOpenQueryAuthResult.getAuthorizationInfo().getAuthorizerAppid();
                enterpriseMiniprogramEntity.AuthorizationCode = authorizationCode;
                enterpriseMiniprogramEntity.IsPublish = false;
                enterpriseOplatformMapper.createEnterpriseMiniprogram(enterpriseMiniprogramEntity);
            }

            List<String> domain = new ArrayList<>();
            domain.add("https://api.mall.bellecare.cn");
            //设置服务器域名
            wxOpenService.getWxOpenComponentService().getWxMaServiceByAppid(wxOpenQueryAuthResult.getAuthorizationInfo().getAuthorizerAppid()).modifyDomain("set", domain, domain, domain, domain);

            //插件申请-weui扩展库
            try {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("action", "apply");
                jsonObject.put("plugin_appid", "wxfa43a4a7041a84de");
                wxOpenService.getWxOpenComponentService().getWxMaServiceByAppid(wxOpenQueryAuthResult.getAuthorizationInfo().getAuthorizerAppid()).post("https://api.weixin.qq.com/wxa/plugin", jsonObject);
            } catch (Exception e) {
                throw e;
            }
            //插件申请-腾讯位置服务路线规划
            try {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("action", "apply");
                jsonObject.put("plugin_appid", "wx50b5593e81dd937a");
                wxOpenService.getWxOpenComponentService().getWxMaServiceByAppid(wxOpenQueryAuthResult.getAuthorizationInfo().getAuthorizerAppid()).post("https://api.weixin.qq.com/wxa/plugin", jsonObject);
            } catch (Exception e) {
                throw e;
            }
            //插件申请-腾讯位置服务城市选择器
            try {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("action", "apply");
                jsonObject.put("plugin_appid", "wx63ffb7b7894e99ae");
                wxOpenService.getWxOpenComponentService().getWxMaServiceByAppid(wxOpenQueryAuthResult.getAuthorizationInfo().getAuthorizerAppid()).post("https://api.weixin.qq.com/wxa/plugin", jsonObject);
            } catch (Exception e) {
                throw e;
            }
            //插件申请-腾讯位置服务地图选点
            try {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("action", "apply");
                jsonObject.put("plugin_appid", "wx76a9a06e5b4e693e");
                wxOpenService.getWxOpenComponentService().getWxMaServiceByAppid(wxOpenQueryAuthResult.getAuthorizationInfo().getAuthorizerAppid()).post("https://api.weixin.qq.com/wxa/plugin", jsonObject);
            } catch (Exception e) {
                throw e;
            }
            //配置小程序用户隐私保护指引
            setPrivacysSetting(wxOpenQueryAuthResult.getAuthorizationInfo().getAuthorizerAppid());

            //申请申请隐私
            applyPrivacy(enterpriseMiniprogramEntity.AuthorizerAppID);

            return BaseOutput.success();
        } catch (Exception e) {
            return BaseOutput.failed(e.getMessage());
        }
    }

    public BaseOutput authinfo() {
        try {
            EnterpriseMiniprogramEntity enterpriseMiniprogramEntity = enterpriseOplatformMapper.getEnterpriseMiniprogram(SystemUserUtil.getSystemEnterpriseCode());
            if (enterpriseMiniprogramEntity == null) {
                return BaseOutput.failed(1100, "当前商户未绑定小程序");
            }
            MiniProgramAuthInformation miniProgramAuthInformation = new MiniProgramAuthInformation();
            miniProgramAuthInformation.wxOpenAuthorizerInfoResult = wxOpenService.getWxOpenComponentService().getAuthorizerInfo(enterpriseMiniprogramEntity.AuthorizerAppID);
            miniProgramAuthInformation.IsPublish = enterpriseMiniprogramEntity.IsPublish;

            return BaseOutputForm.success(miniProgramAuthInformation);
        } catch (Exception e) {
            return BaseOutput.failed(e.getMessage());
        }
    }

    public BaseOutput cancelAuth() {
        enterpriseOplatformMapper.deleteEnterpriseMiniprogram(SystemUserUtil.getSystemEnterpriseCode());
        enterpriseWxPayMapper.deleteEnterpriseWxPay(SystemUserUtil.getSystemEnterpriseCode());
        return BaseOutput.success();
    }

    public BaseOutput submitAudit() {
        return submitAuditByEnterpriseCode(SystemUserUtil.getSystemEnterpriseCode());
    }

    public BaseOutput submitAuditByEnterpriseCode(String enterpriseCode) {
        EnterpriseMiniprogramEntity enterpriseMiniprogramEntity = enterpriseOplatformMapper.getEnterpriseMiniprogram(enterpriseCode);
        if (enterpriseMiniprogramEntity == null) {
            return BaseOutput.failed(1100, "当前商户未绑定小程序，请先绑定小程序");
        }

        EnterpriseMiniprogramTemplateVersionEntity enterpriseMiniprogramTemplateVersionEntity = enterpriseMiniprogramTemplateVersionMapper.getTopOneEnterpriseMiniprogramTemplateVersion();
        if (enterpriseMiniprogramTemplateVersionEntity == null) {
            return BaseOutput.failed(1100, "未获取小程序模板信息");
        }

        Map<String, Object> ext = new HashMap<>();
        ext.put("EnterpriseCode", enterpriseMiniprogramEntity.EnterpriseCode);
        WxMaCodeCommitRequest wxMaCodeCommitRequest = WxMaCodeCommitRequest.builder().templateId(enterpriseMiniprogramTemplateVersionEntity.TemplateID.longValue()).userVersion(enterpriseMiniprogramTemplateVersionEntity.Vesion).userDesc("微商场").extConfig(WxMaCodeExtConfig.builder().extAppid(enterpriseMiniprogramEntity.AuthorizerAppID).ext(ext).build()).build();
        WxMaCodeService wxMaCodeService = wxOpenService.getWxOpenComponentService().getWxMaServiceByAppid(enterpriseMiniprogramEntity.AuthorizerAppID).getCodeService();

        //配置小程序用户隐私保护指引
        try {
            setPrivacysSetting(enterpriseMiniprogramEntity.AuthorizerAppID);
        } catch (Exception e) {
            return BaseOutput.failed(e.getMessage());
        }

        //申请申请隐私
        try {
            applyPrivacy(enterpriseMiniprogramEntity.AuthorizerAppID);
        } catch (Exception e) {
            return BaseOutput.failed(e.getMessage());
        }

        //上传小程序代码并生成体验版
        try {
            wxMaCodeService.commit(wxMaCodeCommitRequest);
        } catch (Exception e) {
            return BaseOutput.failed(e.getMessage());
        }

        try {
            Thread.sleep(20 * 1000);
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
        }
        WxMaCodeSubmitAuditRequest wxMaCodeSubmitAuditRequest = WxMaCodeSubmitAuditRequest.builder().itemList(Arrays.asList(WxMaCodeSubmitAuditItem.builder().tag("美容 美发 微商城").title("微商城").build())).build();

        //提交审核
        try {
            wxMaCodeService.submitAudit(wxMaCodeSubmitAuditRequest);
            enterpriseMiniprogramEntity.IsPublish = false;
            enterpriseOplatformMapper.updateEnterpriseMiniprogram(enterpriseMiniprogramEntity);
        } catch (Exception e) {
            return BaseOutput.failed(e.getMessage());
        }

        return BaseOutput.success();
    }

    public BaseOutput getLatestAuditStatus() {
        return getLatestAuditStatusByEnterpriseCode(SystemUserUtil.getSystemEnterpriseCode());
    }

    public BaseOutput getLatestAuditStatusByEnterpriseCode(String enterpriseCode) {
        EnterpriseMiniprogramEntity enterpriseMiniprogramEntity = enterpriseOplatformMapper.getEnterpriseMiniprogram(enterpriseCode);
        if (enterpriseMiniprogramEntity == null) {
            return BaseOutput.failed(1100, "当前商户未绑定小程序，请先绑定小程序");
        }
        WxMaCodeService wxMaCodeService = wxOpenService.getWxOpenComponentService().getWxMaServiceByAppid(enterpriseMiniprogramEntity.AuthorizerAppID).getCodeService();


        try {
            return BaseOutputForm.success(wxMaCodeService.getLatestAuditStatus());
        } catch (Exception e) {
            return BaseOutput.failed(e.getMessage());
        }
    }

    public BaseOutput release() {
        EnterpriseMiniprogramEntity enterpriseMiniprogramEntity = enterpriseOplatformMapper.getEnterpriseMiniprogram(SystemUserUtil.getSystemEnterpriseCode());
        if (enterpriseMiniprogramEntity == null) {
            return BaseOutput.failed(1100, "当前商户未绑定小程序，请先绑定小程序");
        }
        WxMaCodeService wxMaCodeService = wxOpenService.getWxOpenComponentService().getWxMaServiceByAppid(enterpriseMiniprogramEntity.AuthorizerAppID).getCodeService();

        try {
            wxMaCodeService.release();
            enterpriseMiniprogramEntity.IsPublish = true;
            enterpriseOplatformMapper.updateEnterpriseMiniprogram(enterpriseMiniprogramEntity);
            return BaseOutput.success();
        } catch (Exception e) {
            return BaseOutput.failed(e.getMessage());
        }
    }

    public BaseOutput release(String appId) {
        EnterpriseMiniprogramEntity enterpriseMiniprogramEntity = enterpriseOplatformMapper.getEnterpriseMiniprogramByAppID(appId);

        if (enterpriseMiniprogramEntity == null) {
            return BaseOutput.failed(1100, "当前商户未绑定小程序，请先绑定小程序");
        }
        WxMaCodeService wxMaCodeService = wxOpenService.getWxOpenComponentService().getWxMaServiceByAppid(enterpriseMiniprogramEntity.AuthorizerAppID).getCodeService();


        try {
            wxMaCodeService.release();
            enterpriseMiniprogramEntity.IsPublish = true;
            enterpriseOplatformMapper.updateEnterpriseMiniprogram(enterpriseMiniprogramEntity);
            return BaseOutput.success();
        } catch (Exception e) {
            return BaseOutput.failed(e.getMessage());
        }
    }

    public BaseOutput authtoken() {
        EnterpriseMiniprogramEntity enterpriseMiniprogramEntity = enterpriseOplatformMapper.getEnterpriseMiniprogram(SystemUserUtil.getSystemEnterpriseCode());
        if (enterpriseMiniprogramEntity == null) {
            return BaseOutput.failed("当前商户未绑定小程序，请先绑定小程序");
        }
        try {
            return BaseOutput.success(wxOpenService.getWxOpenComponentService().getAuthorizerAccessToken(enterpriseMiniprogramEntity.AuthorizerAppID, true));
        } catch (Exception e) {
            return BaseOutput.failed(e.getMessage());
        }
    }

    //获取体验版二维码
    public byte[] qrcode(String enterpriseCode) throws Exception {
        EnterpriseMiniprogramEntity enterpriseMiniprogramEntity = enterpriseOplatformMapper.getEnterpriseMiniprogram(enterpriseCode);
        if (enterpriseMiniprogramEntity == null) {
            throw new Exception("当前商户未绑定小程序，请先绑定小程序");
        }
        WxMaCodeService wxMaCodeService = wxOpenService.getWxOpenComponentService().getWxMaServiceByAppid(enterpriseMiniprogramEntity.AuthorizerAppID).getCodeService();

        try {
            return wxMaCodeService.getQrCode("");
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    //配置小程序用户隐私保护指引
//    private void setPrivacysSetting(String appid) throws WxErrorException {
//        //获取小程序用户隐私保护指引
//        WxOpenMaPrivacyServiceImpl wxOpenMaPrivacyService = new WxOpenMaPrivacyServiceImpl(wxOpenService.getWxOpenComponentService().getWxMaServiceByAppid(appid));
//        GetPrivacySettingResult getPrivacySettingResult = wxOpenMaPrivacyService.getPrivacySetting(2);
//
//        List<SetPrivacySetting.Setting> settinglist = new ArrayList<>();
//        if (getPrivacySettingResult.getSettingList().stream().filter(a -> a.getPrivacyKey().equals("UserInfo")).collect(Collectors.toList()).size() == 0) {
//            settinglist.add(new SetPrivacySetting.Setting("UserInfo", "为了注册、登录小程序, 开发者收集你的用户信息（微信昵称、头像、性别、地区）"));
//        }
//        if (getPrivacySettingResult.getSettingList().stream().filter(a -> a.getPrivacyKey().equals("PhoneNumber")).collect(Collectors.toList()).size() == 0) {
//            settinglist.add(new SetPrivacySetting.Setting("PhoneNumber", "为了注册、登录小程序, 开发者收集你的手机号码"));
//        }
//
//        if (getPrivacySettingResult.getSettingList().stream().filter(a -> a.getPrivacyKey().equals("AlbumWriteOnly")).collect(Collectors.toList()).size() == 0) {
//            settinglist.add(new SetPrivacySetting.Setting("AlbumWriteOnly", "为了保存商品图片, 开发者需要写入相册内容"));
//        }
//
//        if (getPrivacySettingResult.getSettingList().stream().filter(a -> a.getPrivacyKey().equals("Location")).collect(Collectors.toList()).size() == 0) {
//            settinglist.add(new SetPrivacySetting.Setting("Location", "为了查询距离您最进的门店，开发者需要访问您的位置信息"));
//        }
//
//        if (getPrivacySettingResult.getSettingList().stream().filter(a -> a.getPrivacyKey().equals("Album")).collect(Collectors.toList()).size() == 0) {
//            settinglist.add(new SetPrivacySetting.Setting("Album", "用来优化展示您的个性形象"));
//        }
//
//        if (getPrivacySettingResult.getSettingList().stream().filter(a -> a.getPrivacyKey().equals("MessageFile")).collect(Collectors.toList()).size() == 0) {
//            settinglist.add(new SetPrivacySetting.Setting("MessageFile", "用于完善您的用户信息(补充、优化您的头像)"));
//        }
//
//        if (settinglist.size() > 0) {
//            PrivacyOwnerSetting ownersetting = new PrivacyOwnerSetting();
//            ownersetting.setContactEmail("<EMAIL>");
//            ownersetting.setContactPhone("15261573707");
//            ownersetting.setNoticeMethod("通过弹窗方式告知用户");
//            wxOpenMaPrivacyService.setPrivacySetting(new SetPrivacySetting(2, ownersetting, settinglist));
//        }
//    }

    //配置小程序用户隐私保护指引
    private void setPrivacysSetting(String appid) throws WxErrorException {


        //获取小程序用户隐私保护指引
        WxOpenMaPrivacyServiceImpl wxOpenMaPrivacyService = new WxOpenMaPrivacyServiceImpl(wxOpenService.getWxOpenComponentService().getWxMaServiceByAppid(appid));
        GetPrivacySettingResult getPrivacySettingResult = wxOpenMaPrivacyService.getPrivacySetting(2);

        List<JSONObject> setting_list = new ArrayList<>();

//        if (getPrivacySettingResult.getSettingList().stream().filter(a -> a.getPrivacyKey().equals("UserInfo")).collect(Collectors.toList()).size() == 0) {
            JSONObject UserInfo = new JSONObject();
            UserInfo.put("privacy_key", "UserInfo");
            UserInfo.put("privacy_text", "为了注册、登录小程序, 开发者收集你的用户信息（微信昵称、头像、性别、地区）");
            setting_list.add(UserInfo);
//        }
//        if (getPrivacySettingResult.getSettingList().stream().filter(a -> a.getPrivacyKey().equals("PhoneNumber")).collect(Collectors.toList()).size() == 0) {
            JSONObject PhoneNumber = new JSONObject();
            PhoneNumber.put("privacy_key", "PhoneNumber");
            PhoneNumber.put("privacy_text", "为了注册、登录小程序, 开发者收集你的手机号码");
            setting_list.add(PhoneNumber);
//        }

//        if (getPrivacySettingResult.getSettingList().stream().filter(a -> a.getPrivacyKey().equals("AlbumWriteOnly")).collect(Collectors.toList()).size() == 0) {
            JSONObject AlbumWriteOnly = new JSONObject();
            AlbumWriteOnly.put("privacy_key", "AlbumWriteOnly");
            AlbumWriteOnly.put("privacy_text", "为了保存商品图片, 开发者需要写入相册内容");
            setting_list.add(AlbumWriteOnly);
//        }


//        if (getPrivacySettingResult.getSettingList().stream().filter(a -> a.getPrivacyKey().equals("Location")).collect(Collectors.toList()).size() == 0) {
            JSONObject Location = new JSONObject();
            Location.put("privacy_key", "Location");
            Location.put("privacy_text", "为了查询距离您最进的门店，开发者需要访问您的位置信息");
            setting_list.add(Location);
//        }
//        if (getPrivacySettingResult.getSettingList().stream().filter(a -> a.getPrivacyKey().equals("Album")).collect(Collectors.toList()).size() == 0) {
            JSONObject Album = new JSONObject();
            Album.put("privacy_key", "Album");
            Album.put("privacy_text", "用来优化展示您的个性形象");
            setting_list.add(Album);
//        }

//        if (getPrivacySettingResult.getSettingList().stream().filter(a -> a.getPrivacyKey().equals("MessageFile")).collect(Collectors.toList()).size() == 0) {
            JSONObject MessageFile = new JSONObject();
            MessageFile.put("privacy_key", "MessageFile");
            MessageFile.put("privacy_text", "用于完善您的用户信息(补充、优化您的头像)");
            setting_list.add(MessageFile);
//        }

        JSONObject Clipboard = new JSONObject();
        Clipboard.put("privacy_key", "Clipboard");
        Clipboard.put("privacy_text", "用于快速填写文本框内容（比如：搜索信息，备注信息）");
        setting_list.add(Clipboard);


//        if(setting_list.size()>0) {
            JSONObject owner_setting = new JSONObject();
            owner_setting.put("contact_email", "<EMAIL>");
            owner_setting.put("contact_phone", "15261573707");
            owner_setting.put("notice_method", "通过弹窗方式告知用户");

            JSONObject request = new JSONObject();
            request.put("owner_setting", owner_setting);
            request.put("setting_list", setting_list);
            request.put("privacy_ver", 2);

            wxOpenService.getWxOpenComponentService().getWxMaServiceByAppid(appid).post("https://api.weixin.qq.com/cgi-bin/component/setprivacysetting", request);
//        }

    }

    //申请隐私
    public void applyPrivacy(String appid) throws WxErrorException {

        String response = wxOpenService.getWxOpenComponentService().getWxMaServiceByAppid(appid).get("https://api.weixin.qq.com/wxa/security/get_privacy_interface", "");
        List<Privacy> privacies = JSON.parseArray(JSON.parseObject(response).getString("interface_list"), Privacy.class);

        //wx.getLocation
        Privacy getLocationPrivacy = privacies.stream().filter(a -> a.api_name.equals("wx.getLocation")).collect(Collectors.toList()).get(0);
        if (getLocationPrivacy.status != 3 && getLocationPrivacy.status != 5) {
            JSONObject request = new JSONObject();
            request.put("api_name", "wx.getLocation");
            request.put("content", "获取当前的位置，查找最近的门店");

            ArrayList<String> video_list = new ArrayList<>();
            video_list.add("https://mfl-saas-data.oss-cn-shanghai.aliyuncs.com/web/mall/privacy/video/chooseStore.mp4");
            request.put("video_list", video_list);

            ArrayList<String> pic_list = new ArrayList<>();
            pic_list.add("https://mfl-saas-data.oss-cn-shanghai.aliyuncs.com/web/mall/privacy/pic/home.png");
            pic_list.add("https://mfl-saas-data.oss-cn-shanghai.aliyuncs.com/web/mall/privacy/pic/chooseStore.jpg");
            request.put("video_list", pic_list);

            wxOpenService.getWxOpenComponentService().getWxMaServiceByAppid(appid).post("https://api.weixin.qq.com/wxa/security/apply_privacy_interface", request);
        } else if (getLocationPrivacy.status == 3) {
            throw new RuntimeException("getLocation权限正在审核中，审核成功才能提交发布，请耐心等待。");
        }


        //wx.chooseLocation
        Privacy chooseLocationPrivacy = privacies.stream().filter(a -> a.api_name.equals("wx.chooseLocation")).collect(Collectors.toList()).get(0);
        if (chooseLocationPrivacy.status != 3 && chooseLocationPrivacy.status != 5) {
            JSONObject request = new JSONObject();
            request.put("api_name", "wx.chooseLocation");
            request.put("content", "打开地图选择位置");
            wxOpenService.getWxOpenComponentService().getWxMaServiceByAppid(appid).post("https://api.weixin.qq.com/wxa/security/apply_privacy_interface", request);
        } else if (getLocationPrivacy.status == 3) {
            throw new RuntimeException("chooseLocation权限正在审核中，审核成功才能提交发布，请耐心等待。");
        }
    }

}
