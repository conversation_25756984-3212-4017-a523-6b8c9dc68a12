package com.zhelian.service.micromall;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import com.alibaba.fastjson2.JSONObject;
import com.zhelian.core.authorization.customer.BasicAuthenticationCustomer;
import com.zhelian.core.authorization.customer.SystemCustomerUtil;

import com.zhelian.core.redis.utils.RedisMicroMallLoginVerificationCodeUtil;
import com.zhelian.core.shortMessage.ShortMessageUtil;
import com.zhelian.core.utils.DecryptUtil;
import com.zhelian.core.weixin.open.service.WxOpenService;
import com.zhelian.mapper.CRM.CustomerMapper;
import com.zhelian.mapper.enterprise.EnterpriseMapper;
import com.zhelian.mapper.enterprise.EnterpriseOplatformMapper;
import com.zhelian.mapper.khs.EntityMapper;
import com.zhelian.mapper.micromall.HomePageMapper;
import com.zhelian.model.MC.entity.EnterpriseMiniprogramEntity;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.enterprise.entity.EnterpriseEntity;
import com.zhelian.model.ibeauty.entity.CustomerEntity;
import com.zhelian.model.khs.entity.EntityEntity;
import com.zhelian.model.khs.form.input.SwitchEntityForm;
import com.zhelian.model.khs.form.output.SwitchEntityOutputForm;
import com.zhelian.model.micromall.entity.TMCustomerEntity;
import com.zhelian.model.micromall.form.input.MicroMallCustomerInfoForm;
import com.zhelian.model.micromall.form.input.MicroMallLoginForm;
import com.zhelian.model.micromall.form.input.WechatUserInfoAddForm;
import com.zhelian.model.micromall.form.output.AuthTokenOutputForm;
import com.zhelian.model.micromall.form.output.EntityListOutForm;
import com.zhelian.model.micromall.form.output.MicroMallLoginOutputForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@Service
public class MicroMallAccountService {

    @Autowired
    CustomerMapper customerMapper;

    @Autowired
    EnterpriseMapper enterpriseMapper;

    @Autowired
    WxOpenService wxOpenService;

    @Autowired
    EnterpriseOplatformMapper enterpriseOplatformMapper;

    @Autowired
    HomePageMapper homePageMapper;

    @Autowired
    EntityMapper entityMapper;

    /**
     * 微商城商户token
     */
    public BaseOutput microMallAuthorizer(MicroMallLoginForm form) {
        EnterpriseEntity enterpriseEntity = enterpriseMapper.getEnterpriseByCode(form.EnterpriseCode.trim());
        if (enterpriseEntity == null) {
            return BaseOutput.failed("商户号不存在");
        }
        AuthTokenOutputForm authTokenOutputForm = new AuthTokenOutputForm();
        
        BasicAuthenticationCustomer basicAuthenticationCustomer = new BasicAuthenticationCustomer();
        basicAuthenticationCustomer.EnterpriseCode = enterpriseEntity.EnterpriseCode;
        basicAuthenticationCustomer.LicenseExpriationDate = enterpriseEntity.LicenseExpriationDate;
        basicAuthenticationCustomer.EntityID = entityMapper.getEntityListOrDerByID();
        authTokenOutputForm.AuthToken = basicAuthenticationCustomer.getAuthToken();
        return BaseOutputForm.success(authTokenOutputForm);
    }

    public BaseOutput microMallLogin(MicroMallLoginForm microMallLoginForm) {
        String code = microMallLoginForm.code;
        MicroMallLoginOutputForm microMallLoginOutputForm = new MicroMallLoginOutputForm();
        String unionid = null;
        String openId = null;
        String session_key = null;
        try {
            EnterpriseMiniprogramEntity enterpriseMiniprogram = enterpriseOplatformMapper.getEnterpriseMiniprogram(SystemCustomerUtil.getSystemEnterpriseCode());
            WxMaJscode2SessionResult wxMaJscode2SessionResult = wxOpenService.getWxOpenComponentService().miniappJscode2Session(enterpriseMiniprogram.AuthorizerAppID, code);
            unionid = wxMaJscode2SessionResult.getUnionid();
            openId = wxMaJscode2SessionResult.getOpenid();
            session_key = wxMaJscode2SessionResult.getSessionKey();
        } catch (Exception e) {
            return BaseOutput.failed(e.getMessage());
        }
        //通过openID判断用户是否存在TB_Customer
        CustomerEntity customerEntity = customerMapper.getCustomerByOpenID(openId);
        if (customerEntity == null) {
            microMallLoginOutputForm.unionID = unionid;
            microMallLoginOutputForm.openID = openId;
            microMallLoginOutputForm.sessionKey = session_key;
            microMallLoginOutputForm.IsHave = false;
        } else {
            if (unionid != null) {
                customerEntity.UnionID = unionid;
                customerMapper.updateCustomer(customerEntity);
            }
            microMallLoginOutputForm.IsHave = true;
            EntityListOutForm belongEntityDetail = customerMapper.getBelongEntityDetail(customerEntity.ID);
            if (belongEntityDetail == null) {
                EnterpriseEntity enterpriseEntity = enterpriseMapper.getEnterpriseByCode(SystemCustomerUtil.getSystemEnterpriseCode());
                BasicAuthenticationCustomer basicAuthenticationCustomer = new BasicAuthenticationCustomer();
                basicAuthenticationCustomer.EnterpriseCode = enterpriseEntity.EnterpriseCode;
                basicAuthenticationCustomer.CusetomerID = customerEntity.ID;
                basicAuthenticationCustomer.LicenseExpriationDate = enterpriseEntity.LicenseExpriationDate;
                basicAuthenticationCustomer.EntityID = entityMapper.getEntityListOrDerByID();
                microMallLoginOutputForm.authToken = basicAuthenticationCustomer.getAuthToken();
                microMallLoginOutputForm.EntityOutputForm = belongEntityDetail;
            } else {
                EnterpriseEntity enterpriseEntity = enterpriseMapper.getEnterpriseByCode(SystemCustomerUtil.getSystemEnterpriseCode());
                BasicAuthenticationCustomer basicAuthenticationCustomer = new BasicAuthenticationCustomer();
                basicAuthenticationCustomer.EnterpriseCode = enterpriseEntity.EnterpriseCode;
                basicAuthenticationCustomer.CusetomerID = customerEntity.ID;
                basicAuthenticationCustomer.LicenseExpriationDate = enterpriseEntity.LicenseExpriationDate;
                basicAuthenticationCustomer.EntityID = belongEntityDetail.ID;
                microMallLoginOutputForm.authToken = basicAuthenticationCustomer.getAuthToken();
                microMallLoginOutputForm.EntityOutputForm = belongEntityDetail;
            }
        }
        return BaseOutputForm.success(microMallLoginOutputForm);
    }


    public BaseOutput saveCustomer(WechatUserInfoAddForm wechatUserInfoAddForm) {
        if (StringUtils.hasText(wechatUserInfoAddForm.openID) && !StringUtils.hasText(wechatUserInfoAddForm.phoneNumber)) {
            TMCustomerEntity tmCustomerEntity = customerMapper.getTmCustomerByOpenID(wechatUserInfoAddForm.openID);
            if (tmCustomerEntity == null) {
                tmCustomerEntity = new TMCustomerEntity();
                tmCustomerEntity.OpenID = wechatUserInfoAddForm.openID;
                tmCustomerEntity.Name = wechatUserInfoAddForm.nickName;
                tmCustomerEntity.PhoneNumber = wechatUserInfoAddForm.phoneNumber;
                tmCustomerEntity.UnionID = wechatUserInfoAddForm.unionID;
                tmCustomerEntity.Avatar = wechatUserInfoAddForm.avatarUrl;
                tmCustomerEntity.CreatedOn = new Timestamp(System.currentTimeMillis());
                tmCustomerEntity.Gender = (wechatUserInfoAddForm.gender);
                customerMapper.insertTmCustomer(tmCustomerEntity);
            } else {
                tmCustomerEntity.Name = wechatUserInfoAddForm.nickName;
                tmCustomerEntity.PhoneNumber = wechatUserInfoAddForm.phoneNumber;
                tmCustomerEntity.UnionID = wechatUserInfoAddForm.unionID;
                tmCustomerEntity.Avatar = wechatUserInfoAddForm.avatarUrl;
                tmCustomerEntity.CreatedOn = new Timestamp(System.currentTimeMillis());
                tmCustomerEntity.Gender = (wechatUserInfoAddForm.gender);
                customerMapper.updateTmCustomer(tmCustomerEntity);
            }
        } else {
            if (!wechatUserInfoAddForm.verificationCode.equals(RedisMicroMallLoginVerificationCodeUtil.get(SystemCustomerUtil.getSystemEnterpriseCode(), wechatUserInfoAddForm.phoneNumber))) {
                return BaseOutput.failed("验证码不正确或已过期，请重新获取");
            }
            List<CustomerEntity> customer = customerMapper.getCustomerListByPhoneNumber(wechatUserInfoAddForm.phoneNumber);
            if (customer != null && customer.size() > 1) {
                return BaseOutput.failed("当前手机号在系统存在2个或2个以上账户，请联系工作人员查验，在进行登录");
            }


            CustomerEntity customerEntity = customerMapper.getCustomerByPhoneNumber(wechatUserInfoAddForm.phoneNumber);
            if (customerEntity == null) {
                customerEntity = new CustomerEntity();
                customerEntity.OpenID = wechatUserInfoAddForm.openID;
                customerEntity.Name = wechatUserInfoAddForm.nickName;
                customerEntity.PhoneNumber = wechatUserInfoAddForm.phoneNumber;
                customerEntity.UnionID = wechatUserInfoAddForm.unionID;
                customerEntity.Avatar = wechatUserInfoAddForm.avatarUrl;
                customerEntity.CreatedOn = new Timestamp(System.currentTimeMillis());
                customerEntity.Gender = (wechatUserInfoAddForm.gender);
                customerEntity.IsMember = false;
                customerEntity.IsLockMemberLevel = false;
                customerMapper.insertCustomer(customerEntity);
            } else {
                customerEntity.OpenID = wechatUserInfoAddForm.openID;
                customerEntity.PhoneNumber = wechatUserInfoAddForm.phoneNumber;
                customerEntity.UnionID = wechatUserInfoAddForm.unionID;
                customerEntity.Avatar = wechatUserInfoAddForm.avatarUrl;
                customerEntity.CreatedOn = new Timestamp(System.currentTimeMillis());
                customerEntity.Gender = wechatUserInfoAddForm.gender;
                customerMapper.updateCustomer(customerEntity);
            }
            EntityListOutForm belongEntityDetail = customerMapper.getBelongEntityDetail(customerEntity.ID);
            MicroMallLoginOutputForm microMallLoginOutputForm = new MicroMallLoginOutputForm();
            if (belongEntityDetail == null) {
                if (wechatUserInfoAddForm.Longitude != null && wechatUserInfoAddForm.Latitude != null) {
                    EntityListOutForm distance = customerMapper.getDistance(wechatUserInfoAddForm.Longitude, wechatUserInfoAddForm.Latitude);
                    EnterpriseEntity enterpriseEntity = enterpriseMapper.getEnterpriseByCode(SystemCustomerUtil.getSystemEnterpriseCode());
                    BasicAuthenticationCustomer basicAuthenticationCustomer = new BasicAuthenticationCustomer();
                    basicAuthenticationCustomer.EnterpriseCode = enterpriseEntity.EnterpriseCode;
                    basicAuthenticationCustomer.CusetomerID = customerEntity.ID;
                    basicAuthenticationCustomer.LicenseExpriationDate = enterpriseEntity.LicenseExpriationDate;
                    basicAuthenticationCustomer.EntityID = distance.ID;
                    microMallLoginOutputForm.authToken = basicAuthenticationCustomer.getAuthToken();
                    microMallLoginOutputForm.EntityOutputForm = belongEntityDetail;
                }
            } else {
                EnterpriseEntity enterpriseEntity = enterpriseMapper.getEnterpriseByCode(SystemCustomerUtil.getSystemEnterpriseCode());
                BasicAuthenticationCustomer basicAuthenticationCustomer = new BasicAuthenticationCustomer();
                basicAuthenticationCustomer.EnterpriseCode = enterpriseEntity.EnterpriseCode;
                basicAuthenticationCustomer.CusetomerID = customerEntity.ID;
                basicAuthenticationCustomer.LicenseExpriationDate = enterpriseEntity.LicenseExpriationDate;
                basicAuthenticationCustomer.EntityID = belongEntityDetail.ID;
                microMallLoginOutputForm.authToken = basicAuthenticationCustomer.getAuthToken();
                microMallLoginOutputForm.EntityOutputForm = belongEntityDetail;
            }
            return BaseOutputForm.success(microMallLoginOutputForm);
        }

        return BaseOutput.success();
    }

    public BaseOutput updateCustomer(MicroMallLoginForm form) {
        JSONObject param = null;
        String phoneNumber = null;
        try {
            param = DecryptUtil.decodeAES(form.encryptedData, form.sessionKey, form.iv);
            phoneNumber = param.getString("phoneNumber");
        } catch (Exception e) {
            return BaseOutput.failed("Decrypt解密失败！");
        }
        List<CustomerEntity> customerEntityList = customerMapper.getCustomerListByPhoneNumber(phoneNumber);
        if (customerEntityList != null && customerEntityList.size() > 1) {
            return BaseOutput.failed("当前手机号在系统存在2个或2个以上账户，请联系工作人员查验，在进行登录");
        }


        CustomerEntity customer = customerMapper.getCustomerByPhoneNumber(phoneNumber);
        EntityListOutForm belongEntityDetail;
        Integer customerID;
        TMCustomerEntity tmCustomerEntity = customerMapper.getTmCustomerByOpenID(form.openID);
        if (null == customer) {
            CustomerEntity customerEntity = new CustomerEntity();
            customerEntity.OpenID = tmCustomerEntity.OpenID;
            customerEntity.Name = tmCustomerEntity.Name;
            customerEntity.PhoneNumber = phoneNumber;
            customerEntity.UnionID = tmCustomerEntity.UnionID;
            customerEntity.Avatar = tmCustomerEntity.Avatar;
            customerEntity.CreatedOn = tmCustomerEntity.CreatedOn;
            customerEntity.Gender = tmCustomerEntity.Gender;
            customerEntity.IsMember = false;
            customerEntity.IsLockMemberLevel = false;
            customerMapper.insertCustomer(customerEntity);
            customerID = customerEntity.ID;
            belongEntityDetail = customerMapper.getBelongEntityDetail(customerEntity.ID);
        } else {
            customer.UnionID = tmCustomerEntity.UnionID;
            customer.Avatar = tmCustomerEntity.Avatar;
            customer.CreatedOn = tmCustomerEntity.CreatedOn;
            customer.Gender = tmCustomerEntity.Gender;
            customer.OpenID = tmCustomerEntity.OpenID;
            customerMapper.updateCustomer(customer);
            customerID = customer.ID;
            belongEntityDetail = customerMapper.getBelongEntityDetail(customer.ID);
        }
        MicroMallLoginOutputForm microMallLoginOutputForm = new MicroMallLoginOutputForm();
        if (belongEntityDetail == null) {
            if (form.Longitude != null && form.Latitude != null) {
                EntityListOutForm distance = customerMapper.getDistance(form.Longitude, form.Latitude);
                EnterpriseEntity enterpriseEntity = enterpriseMapper.getEnterpriseByCode(SystemCustomerUtil.getSystemEnterpriseCode());
                BasicAuthenticationCustomer basicAuthenticationCustomer = new BasicAuthenticationCustomer();
                basicAuthenticationCustomer.EnterpriseCode = enterpriseEntity.EnterpriseCode;
                basicAuthenticationCustomer.CusetomerID = customerID;
                basicAuthenticationCustomer.LicenseExpriationDate = enterpriseEntity.LicenseExpriationDate;
                basicAuthenticationCustomer.EntityID = distance.ID;
                microMallLoginOutputForm.authToken = basicAuthenticationCustomer.getAuthToken();

                microMallLoginOutputForm.EntityOutputForm = distance;
            }
        } else {
            EnterpriseEntity enterpriseEntity = enterpriseMapper.getEnterpriseByCode(SystemCustomerUtil.getSystemEnterpriseCode());
            BasicAuthenticationCustomer basicAuthenticationCustomer = new BasicAuthenticationCustomer();
            basicAuthenticationCustomer.EnterpriseCode = enterpriseEntity.EnterpriseCode;
            basicAuthenticationCustomer.CusetomerID = customerID;
            basicAuthenticationCustomer.LicenseExpriationDate = enterpriseEntity.LicenseExpriationDate;
            basicAuthenticationCustomer.EntityID = belongEntityDetail.ID;
            microMallLoginOutputForm.authToken = basicAuthenticationCustomer.getAuthToken();
            microMallLoginOutputForm.EntityOutputForm = belongEntityDetail;
        }
        customerMapper.deleteTmCustomer(form.openID);
        return BaseOutputForm.success(microMallLoginOutputForm);
    }

    public BaseOutput sendMicroMallVerificationCode(MicroMallLoginForm form) {
        ShortMessageUtil.SendMicroMallCode(SystemCustomerUtil.getSystemEnterpriseCode(), form.phoneNumber);
        return BaseOutput.success("发送成功");
    }

    public BaseOutput switchMicroMallEntity(SwitchEntityForm form) {
        EnterpriseEntity enterpriseEntity = enterpriseMapper.getEnterpriseByCode(SystemCustomerUtil.getSystemEnterpriseCode());
        if (enterpriseEntity == null) {
            return BaseOutput.failed("未找到商户信息");
        }
        if (enterpriseEntity.LicenseExpriationDate.before(new Date())) {
            return BaseOutput.failed("商户授权已过期");
        }

        BasicAuthenticationCustomer basicAuthenticationCustomer = new BasicAuthenticationCustomer();
        basicAuthenticationCustomer.EnterpriseCode = enterpriseEntity.EnterpriseCode;
        basicAuthenticationCustomer.LicenseExpriationDate = enterpriseEntity.LicenseExpriationDate;
        basicAuthenticationCustomer.CusetomerID = SystemCustomerUtil.getSystemCustomerID();
        basicAuthenticationCustomer.EntityID = form.EntityID;

        SwitchEntityOutputForm outputForm = new SwitchEntityOutputForm();
        EntityEntity entityInfo = entityMapper.entityInfo(form.EntityID);
        if (entityInfo == null) {
            return BaseOutput.failed("组织单位已停用");
        }
        outputForm.AuthToken = basicAuthenticationCustomer.getAuthToken();
        return BaseOutputForm.success(outputForm, "切换成功");
    }

    public BaseOutput updateCustomerInfo(MicroMallCustomerInfoForm form) {
        CustomerEntity customer = customerMapper.getCustomer(form.CustomerID);
        customer.Name= form.Name;
        customer.Avatar= form.Avatar;
        customerMapper.updateCustomer(customer);
        return BaseOutput.success();
    }
}
