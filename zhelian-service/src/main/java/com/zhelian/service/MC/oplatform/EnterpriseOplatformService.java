package com.zhelian.service.MC.oplatform;

import cn.binarywang.wx.miniapp.api.WxMaCodeService;
import cn.binarywang.wx.miniapp.bean.code.WxMaCodeAuditStatus;
import cn.binarywang.wx.miniapp.bean.code.WxMaCodeCommitRequest;
import cn.binarywang.wx.miniapp.bean.code.WxMaCodeExtConfig;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.core.utils.OSSClientUtil;
import com.zhelian.core.weixin.open.service.WxOpenService;
import com.zhelian.mapper.enterprise.EnterpriseOplatformMapper;
import com.zhelian.mapper.enterprise.EnterpriseWxPayMapper;
import com.zhelian.model.MC.entity.EnterpriseMiniprogramEntity;
import com.zhelian.model.MC.entity.EnterpriseOffiaccountEntity;
import com.zhelian.model.MC.entity.EnterpriseWxPayEntity;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.open.api.impl.WxOpenOAuth2ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

@Service
public class EnterpriseOplatformService {
    @Autowired
    private WxOpenService wxOpenService;

    @Autowired
    private EnterpriseOplatformMapper enterpriseOplatformMapper;

    @Autowired
    private EnterpriseWxPayMapper enterpriseWxPayMapper;


    WxOpenOAuth2ServiceImpl wxOpenOAuth2Service;

    public BaseOutput createEnterpriseOffiaccount(String EnterpriseCode, String AuthorizerAppID, String AuthorizationCode) {
        EnterpriseOffiaccountEntity enterpriseOffiaccountEntity = enterpriseOplatformMapper.getEnterpriseOffiaccount(EnterpriseCode);

        if (enterpriseOffiaccountEntity != null) {
            enterpriseOplatformMapper.updateEnterpriseOffiaccount(EnterpriseCode, AuthorizerAppID, AuthorizationCode);
            return BaseOutput.success();
        }

        enterpriseOplatformMapper.createEnterpriseOffiaccount(EnterpriseCode, AuthorizerAppID, AuthorizationCode);

        return BaseOutput.success();
    }

    public BaseOutput createEnterpriseMiniprogram(String EnterpriseCode, String AuthorizerAppID, String AuthorizationCode) {
        EnterpriseMiniprogramEntity enterpriseMiniprogramEntity = enterpriseOplatformMapper.getEnterpriseMiniprogram(EnterpriseCode);
        if (enterpriseMiniprogramEntity != null) {
            enterpriseOplatformMapper.updateEnterpriseMiniprogram(EnterpriseCode, AuthorizerAppID, AuthorizationCode);
            return BaseOutput.success();
        }
        enterpriseOplatformMapper.createEnterpriseMiniprogram(EnterpriseCode, AuthorizerAppID, AuthorizationCode);
        return BaseOutput.success();
    }

    public BaseOutput miniprogramAuthorizerInfo(String EnterpriseCode) {

        EnterpriseMiniprogramEntity enterpriseMiniprogramEntity = enterpriseOplatformMapper.getEnterpriseMiniprogram(EnterpriseCode);

        if (enterpriseMiniprogramEntity == null) {
            return BaseOutput.success();
        }
        try {
            Map<String, Object> ext = new HashMap<>();
            ext.put("EnterpriseCode", EnterpriseCode);

            WxMaCodeCommitRequest commitRequest = WxMaCodeCommitRequest
                    .builder()
                    .templateId(15L)
                    .userVersion("1.0.0")
                    .userDesc("微商场")
                    .extConfig(WxMaCodeExtConfig.builder()
//                            .extEnable(true)
                            .extAppid(enterpriseMiniprogramEntity.AuthorizerAppID)
                            .ext(ext)
//                            .window(
//                                    WxMaCodeExtConfig.PageConfig
//                                            .builder()
//                                            .navigationBarBackgroundColor("#0074d9")
//                                            .build()
//                            )
                            .build())
                    .build();
            WxMaCodeService codeService = wxOpenService.getWxOpenComponentService().getWxMaServiceByAppid(enterpriseMiniprogramEntity.AuthorizerAppID).getCodeService();
            //上传代码
//            codeService.commit(commitRequest);
            //获取体验版二维码
//            byte[] a = codeService.getQrCode(null);
//            ByteArrayInputStream bais = new ByteArrayInputStream(a);
//            BufferedImage bi1 = ImageIO.read(bais);
//            File w2 = new File("D://XCX.jpeg");// 可以是jpg,png,gif格式
//            ImageIO.write(bi1, "jpeg", w2);// 不管输出什么格式图片，此处不需改动
            //获取已上传的代码的页面列表
//            List<String> page =  codeService.getPage();
            //提交审核
//            WxMaCodeSubmitAuditRequest auditRequest = WxMaCodeSubmitAuditRequest
//                    .builder()
//                    .itemList(Arrays.asList(
//                            WxMaCategory
//                                    .builder()
//                                    .tag("美容 微商城")
//                                    .title("微商城")
//                                    .build()
//                    )).build();
//
//            long auditId = codeService.submitAudit(auditRequest);

            //查看审核记过
            WxMaCodeAuditStatus wxMaCodeAuditStatus = codeService.getLatestAuditStatus();

            //发布
//            codeService.release();

            return BaseOutputForm.success(wxOpenService.getWxOpenComponentService().getAuthorizerInfo(enterpriseMiniprogramEntity.AuthorizerAppID));
        } catch (Exception e) {
            return BaseOutput.failed(e.getMessage());
        }
    }

    public BaseOutput offiaccountAuthorizerInfo() {
        EnterpriseOffiaccountEntity enterpriseOffiaccountEntity = enterpriseOplatformMapper.getEnterpriseOffiaccount(SystemUserUtil.getSystemEnterpriseCode());
        if (enterpriseOffiaccountEntity == null) {
            return BaseOutput.success();
        }
        try {
            return BaseOutputForm.success(wxOpenService.getWxOpenComponentService().getAuthorizerInfo(enterpriseOffiaccountEntity.AuthorizerAppID));
        } catch (WxErrorException e) {
            if (e.getError().getErrorCode() == 61003) {
                enterpriseOplatformMapper.deleteEnterpriseOffiaccount(SystemUserUtil.getSystemEnterpriseCode());
                return BaseOutput.success();
            } else {
                return BaseOutput.failed(e.getError().getErrorMsgEn());
            }
        }
    }

    public BaseOutput miniprogramRemoveBind(String EnterpriseCode) {
        enterpriseOplatformMapper.deleteEnterpriseMiniprogram(EnterpriseCode);
        return BaseOutput.success();
    }

    public BaseOutput offiaccountRemoveBind() {
        enterpriseOplatformMapper.deleteEnterpriseOffiaccount(SystemUserUtil.getSystemEnterpriseCode());
        return BaseOutput.success();
    }

    public BaseOutput createEnterpriseMiniprogramPayMethod(String MchId, String MchKey, MultipartFile file, Integer PayMethod) throws Exception {
        byte[] bytes = file.getBytes();
        String filename = file.getOriginalFilename();
        String fileSuffix = filename.substring(filename.lastIndexOf(".") + 1);
        String folderName = "WeiXinPay/" + SystemUserUtil.getSystemEnterpriseCode();
        String url = OSSClientUtil.putOSSFolderObject(folderName, filename, bytes, fileSuffix);
        EnterpriseWxPayEntity wxPayEntity = enterpriseWxPayMapper.getEnterpriseWxPay(SystemUserUtil.getSystemEnterpriseCode());
        if (wxPayEntity != null) {
            wxPayEntity.EnterpriseCode = SystemUserUtil.getSystemEnterpriseCode();
            wxPayEntity.MchId = MchId;
            wxPayEntity.MchKey = MchKey;
            wxPayEntity.KeyPath = url;
            wxPayEntity.PayMethod = PayMethod;
            enterpriseWxPayMapper.updateEnterpriseWxPay(wxPayEntity);
            return BaseOutput.success();
        }
        EnterpriseWxPayEntity enterpriseWxPayEntity = new EnterpriseWxPayEntity();
        enterpriseWxPayEntity.EnterpriseCode = SystemUserUtil.getSystemEnterpriseCode();
        enterpriseWxPayEntity.MchId = MchId;
        enterpriseWxPayEntity.MchKey = MchKey;
        enterpriseWxPayEntity.KeyPath = url;
        enterpriseWxPayEntity.PayMethod = PayMethod;
        enterpriseWxPayMapper.createEnterpriseWxPay(enterpriseWxPayEntity);
        return BaseOutput.success();
    }

    public BaseOutput getEnterpriseMiniprogramPayMethod() {
        EnterpriseWxPayEntity wxPayEntity = enterpriseWxPayMapper.getEnterpriseWxPay(SystemUserUtil.getSystemEnterpriseCode());
        return BaseOutputForm.success(wxPayEntity);
    }
}