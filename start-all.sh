#!/bin/bash

# SQM ERP - 统一启动脚本
# 支持启动所有服务或选择性启动特定服务

echo "=========================================="
echo "SQM ERP 服务启动管理器"
echo "=========================================="

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 服务配置
declare -A SERVICES
SERVICES[web]="zhelian-web:8080:Web API 服务"
SERVICES[job]="zhelian-web-job:9000:定时任务服务"
SERVICES[miniprogram]="zhelian-web-miniprogram:8081:小程序 API 服务"
SERVICES[micromall]="zhelian-web-miniprogram-mincroMall:8082:微商城小程序 API 服务"

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项] [服务名...]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -l, --list     列出所有可用服务"
    echo "  -a, --all      启动所有服务"
    echo "  -s, --status   检查服务状态"
    echo ""
    echo "服务名:"
    echo "  web            Web API 服务 (端口 8080)"
    echo "  job            定时任务服务 (端口 9000)"
    echo "  miniprogram    小程序 API 服务 (端口 8081)"
    echo "  micromall      微商城小程序 API 服务 (端口 8082)"
    echo ""
    echo "示例:"
    echo "  $0 web                    # 启动 Web API 服务"
    echo "  $0 web miniprogram        # 启动 Web API 和小程序服务"
    echo "  $0 -a                     # 启动所有服务"
    echo "  $0 -s                     # 检查所有服务状态"
}

# 列出所有服务
list_services() {
    echo "可用服务列表:"
    echo "----------------------------------------"
    for service in "${!SERVICES[@]}"; do
        IFS=':' read -r module port desc <<< "${SERVICES[$service]}"
        printf "%-12s %-8s %s\n" "$service" "($port)" "$desc"
    done
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # 端口被占用
    else
        return 1  # 端口空闲
    fi
}

# 检查服务状态
check_status() {
    echo "服务状态检查:"
    echo "----------------------------------------"
    for service in "${!SERVICES[@]}"; do
        IFS=':' read -r module port desc <<< "${SERVICES[$service]}"
        if check_port $port; then
            echo "✓ $service ($port) - 运行中"
        else
            echo "✗ $service ($port) - 未运行"
        fi
    done
}

# 启动单个服务
start_service() {
    local service=$1
    if [[ -z "${SERVICES[$service]}" ]]; then
        echo "错误: 未知服务 '$service'"
        echo "使用 '$0 -l' 查看可用服务"
        return 1
    fi
    
    IFS=':' read -r module port desc <<< "${SERVICES[$service]}"
    
    echo "启动服务: $desc"
    
    # 检查端口是否被占用
    if check_port $port; then
        echo "警告: 端口 $port 已被占用"
        lsof -Pi :$port -sTCP:LISTEN
        read -p "是否继续启动? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "跳过启动 $service"
            return 1
        fi
    fi
    
    # 启动对应的脚本
    case $service in
        web)
            ./start-web.sh
            ;;
        job)
            ./start-job.sh
            ;;
        miniprogram)
            ./start-miniprogram.sh
            ;;
        micromall)
            ./start-micromall.sh
            ;;
    esac
}

# 启动所有服务
start_all_services() {
    echo "准备启动所有服务..."
    echo ""
    
    # 检查所有端口状态
    local occupied_ports=()
    for service in "${!SERVICES[@]}"; do
        IFS=':' read -r module port desc <<< "${SERVICES[$service]}"
        if check_port $port; then
            occupied_ports+=("$service:$port")
        fi
    done
    
    if [ ${#occupied_ports[@]} -gt 0 ]; then
        echo "警告: 以下端口已被占用:"
        for item in "${occupied_ports[@]}"; do
            IFS=':' read -r service port <<< "$item"
            echo "  - $service (端口 $port)"
        done
        echo ""
        read -p "是否继续启动所有服务? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "启动已取消"
            return 1
        fi
    fi
    
    echo "开始启动服务..."
    echo "注意: 所有服务将在后台启动"
    echo "使用 '$0 -s' 检查服务状态"
    echo ""
    
    # 在后台启动所有服务
    for service in web job miniprogram micromall; do
        echo "启动 $service..."
        nohup ./start-$service.sh > logs/start-$service.log 2>&1 &
        sleep 2
    done
    
    echo ""
    echo "所有服务启动完成!"
    echo "等待 10 秒后检查服务状态..."
    sleep 10
    check_status
}

# 检查 Java 环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到 Java 环境，请确保已安装 JDK 8 或更高版本"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 解析命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    -l|--list)
        list_services
        exit 0
        ;;
    -s|--status)
        check_status
        exit 0
        ;;
    -a|--all)
        start_all_services
        exit 0
        ;;
    "")
        echo "错误: 请指定要启动的服务或使用 -h 查看帮助"
        echo ""
        show_help
        exit 1
        ;;
    *)
        # 启动指定的服务
        for service in "$@"; do
            if [[ $service == -* ]]; then
                echo "错误: 未知选项 '$service'"
                show_help
                exit 1
            fi
            start_service "$service"
        done
        ;;
esac
