#!/bin/bash

# SQM ERP - 微商城小程序 API 启动脚本
# 模块: zhelian-web-miniprogram-mincroMall
# 端口: 8082
# 主类: com.zhelian.SpringBootMiniprogramMincroMallAPI

echo "=========================================="
echo "启动 SQM ERP 微商城小程序 API 服务"
echo "模块: zhelian-web-miniprogram-mincroMall"
echo "端口: 8082"
echo "=========================================="

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 检查 Java 环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到 Java 环境，请确保已安装 JDK 8 或更高版本"
    exit 1
fi

# 显示 Java 版本
echo "Java 版本信息:"
java -version

# 检查端口是否被占用
if lsof -Pi :8082 -sTCP:LISTEN -t >/dev/null ; then
    echo "警告: 端口 8082 已被占用"
    echo "正在查找占用进程..."
    lsof -Pi :8082 -sTCP:LISTEN
    read -p "是否继续启动? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "启动已取消"
        exit 1
    fi
fi

# 创建日志目录
mkdir -p logs

# 设置 JVM 参数
JVM_ARGS="-Xms512m -Xmx1g -XX:+UseG1GC -XX:+UseStringDeduplication -Dfile.encoding=UTF-8 -Duser.timezone=Asia/Shanghai"

# 设置 Spring Boot 配置
# 接收第一个参数作为 profile，默认为 dev
PROFILE=${1:-dev}
echo "当前激活的 Profile: $PROFILE"
SPRING_ARGS="--spring.profiles.active=${PROFILE}"

echo "开始安装项目依赖 (Profile: $PROFILE)..."
mvn clean install -P ${PROFILE} -pl zhelian-mapper,zhelian-model,zhelian-service -am

if [ $? -ne 0 ]; then
    echo "错误: 依赖模块安装失败"
    exit 1
fi

echo "开始编译微商城模块 (Profile: $PROFILE)..."
mvn compile -P ${PROFILE} -pl zhelian-web-miniprogram-mincroMall

if [ $? -ne 0 ]; then
    echo "错误: 微商城模块编译失败"
    exit 1
fi

echo "启动微商城小程序 API 服务 (Profile: $PROFILE)..."
echo "访问地址: http://localhost:8082"
echo "日志文件: logs/web-miniprogram-mincorMall.log"
echo "按 Ctrl+C 停止服务"
echo

# 启动应用
mvn spring-boot:run -P ${PROFILE} -pl zhelian-web-miniprogram-mincroMall -Dspring-boot.run.jvmArguments="$JVM_ARGS" -Dspring-boot.run.arguments="$SPRING_ARGS"
