package com.zhelian.api.account;

import com.zhelian.mapper.CRM.CustomerMapper;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.khs.form.input.SwitchEntityForm;
import com.zhelian.model.micromall.form.input.MicroMallCustomerInfoForm;
import com.zhelian.model.micromall.form.input.MicroMallLoginForm;
import com.zhelian.model.micromall.form.input.WechatUserInfoAddForm;
import com.zhelian.service.micromall.MicroMallAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
public class AccountController {

    @Autowired
    MicroMallAccountService accountService;
    @Autowired
    CustomerMapper customerMapper;


    /**
     * 微商城商户授权
     *
     * @param microMallLoginForm
     * @return
     */
    @RequestMapping(value = "/account/microMallAuthorizer", method = RequestMethod.POST)
    public BaseOutput microMallAuthorizer(@RequestBody MicroMallLoginForm microMallLoginForm) {
        return accountService.microMallAuthorizer(microMallLoginForm);
    }

    /**
     * 微商城小程序登录
     *
     * @param microMallLoginForm
     * @return
     */
    @RequestMapping(value = "/account/microMallLogin", method = RequestMethod.POST)
    public BaseOutput microMallLogin(@RequestBody MicroMallLoginForm microMallLoginForm) {
        return accountService.microMallLogin(microMallLoginForm);
    }

    @RequestMapping(value = "/account/saveCustomer", method = RequestMethod.POST)
    public BaseOutput saveCustomer(@RequestBody WechatUserInfoAddForm wechatUserInfoAddForm) {
        return accountService.saveCustomer(wechatUserInfoAddForm);
    }

    @RequestMapping(value = "/account/updateCustomer", method = RequestMethod.POST)
    public BaseOutput updateCustomer(@RequestBody MicroMallLoginForm microMallLoginForm) {
        return accountService.updateCustomer(microMallLoginForm);
    }

    @RequestMapping(value = "/account/sendMicroMallVerificationCode", method = {RequestMethod.POST})
    public BaseOutput sendMicroMallVerificationCode(@RequestBody MicroMallLoginForm form) {
        if (!StringUtils.hasText(form.phoneNumber)) {
            return BaseOutput.failed("手机号不能为空");
        }
        return accountService.sendMicroMallVerificationCode(form);
    }

    @RequestMapping(value = "/account/switchMicroMallEntity", method = {RequestMethod.POST})
    public BaseOutput switchMicroMallEntity(@RequestBody SwitchEntityForm form) {
        return accountService.switchMicroMallEntity(form);
    }

    @RequestMapping(value = "/account/updateCustomerInfo", method = RequestMethod.POST)
    public BaseOutput updateCustomerInfo(@RequestBody MicroMallCustomerInfoForm form) {
        return accountService.updateCustomerInfo(form);
    }
}
