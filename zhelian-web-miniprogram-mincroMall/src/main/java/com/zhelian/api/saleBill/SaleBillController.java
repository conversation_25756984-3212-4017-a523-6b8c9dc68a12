package com.zhelian.api.saleBill;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleBillAddForm;
import com.zhelian.model.micromall.form.input.SaleBillCancelForm;
import com.zhelian.model.micromall.form.output.SaleBillOutForm;
import com.zhelian.service.micromall.saleBill.MicroMallSaleBillService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api/saleBill")
public class SaleBillController {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private MicroMallSaleBillService saleBillService;

    @RequestMapping(value = "/createSaleBill", method = {RequestMethod.POST})
    public BaseOutput createSaleBill(@RequestBody SaleBillAddForm form) throws Exception {

        return saleBillService.create(form);
    }

    @RequestMapping(value = "/savePaySuccess", method = {RequestMethod.POST})
    public SaleBillOutForm savePaySuccess(@RequestBody String xmlParame) throws Exception {
        return saleBillService.analysisXML(xmlParame);
    }

    /**
     * 取消销售订单
     *
     * @return
     */
    @RequestMapping(value = "/cancelSaleBill", method = RequestMethod.POST)
    public BaseOutput cancelSaleBill(@RequestBody SaleBillCancelForm form) {
        return saleBillService.cancelSaleBill(form);
    }
}