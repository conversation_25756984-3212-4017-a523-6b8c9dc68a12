package com.zhelian.api.seckill;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.micromall.form.input.SeckillBillAddForm;
import com.zhelian.model.micromall.form.output.SeckillBillOutForm;
import com.zhelian.service.micromall.seckill.SeckillBillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api/seckillBill")
public class SeckillBillController {

    @Autowired
    SeckillBillService seckillBillService;


    @RequestMapping(value = "/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody SeckillBillAddForm form) throws Exception {
        if (form.SeckillID == null)
            return BaseOutput.failed("活动编号不能为空");
        if (form.Amount == null)
            return BaseOutput.failed("订单金额不能为空");
        if (form.Quantity == null)
            return BaseOutput.failed("订单数量不能为空");
        if (form.Price == null)
            return BaseOutput.failed("商品原价不能为空");
        if (form.GoodsType == null)
            return BaseOutput.failed("商品类型不能为空");
        if (form.GoodsID == null)
            return BaseOutput.failed("商品编号不能为空");
        if (form.BillDate == null)
            return BaseOutput.failed("订单时间不能为空");

        return seckillBillService.create(form);
    }

    @RequestMapping(value = "/createSeckill", method = {RequestMethod.POST})
    public BaseOutput createSeckill(@RequestBody SeckillBillAddForm form) throws Exception {

        return seckillBillService.createSeckill(form);
    }

    /**
     * 支付回调
     *
     * @return
     */
    @RequestMapping(value = "/savePaySuccess", method = {RequestMethod.POST})
    public SeckillBillOutForm savePaySuccess(@RequestBody String xmlParame) throws Exception {
        return seckillBillService.analysisXML(xmlParame);
    }


}