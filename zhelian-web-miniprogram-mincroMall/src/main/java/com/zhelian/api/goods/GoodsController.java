package com.zhelian.api.goods;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.micromall.form.input.GoodsCustomerDiscountQueryForm;
import com.zhelian.model.micromall.form.input.GoodsQueryForm;
import com.zhelian.service.micromall.good.GoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api/goods")
public class GoodsController {

    @Autowired
    GoodsService goodsService;

    @RequestMapping(value = "/getGoodsDetails", method = {RequestMethod.POST})
    public BaseOutput getGoodsDetails(@RequestBody GoodsQueryForm form) {
        if (!StringUtils.hasText(form.GoodsType)) {
            return BaseOutput.failed("商品类型不能空");
        }
        return goodsService.getGoodsDetails(form);
    }

    @RequestMapping(value = "/getApplyEntity", method = {RequestMethod.POST})
    public BaseOutput getApplyEntity(@RequestBody GoodsQueryForm form) {
        return goodsService.getApplyEntity(form);
    }

    @RequestMapping(value = "/getApplyGoods", method = {RequestMethod.POST})
    public BaseOutput getApplyGoods(@RequestBody GoodsQueryForm form) {
        if (!StringUtils.hasText(form.GoodsType)) {
            return BaseOutput.failed("商品类型不能空");
        }
        return goodsService.getApplyGoods(form);
    }

    @RequestMapping(value = "/searchGoods", method = {RequestMethod.POST})
    public BaseOutput searchGoods(@RequestBody GoodsQueryForm form) {
        return goodsService.searchGoods(form);
    }

    @RequestMapping(value = "/getGoodsCustomerDiscount", method = {RequestMethod.POST})
    public BaseOutput getGoodsCustomerDiscount(@RequestBody GoodsCustomerDiscountQueryForm form) {
        if (!StringUtils.hasText(form.GoodsType)) {
            return BaseOutput.failed("商品类型不能空");
        }

        if (form.GoodsID == null) {
            return BaseOutput.failed("商品编号不能空");
        }
        return goodsService.getGoodsCustomerDiscount(form);
    }
}