package com.zhelian.api.goods;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.micromall.form.input.CategoryQueryForm;
import com.zhelian.service.micromall.good.GoodsCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api/goodsCategory")
public class GoodsCategoryController {

    @Autowired
    GoodsCategoryService goodsCategoryService;

    @RequestMapping(value = "/findCategoryAndGoods", method = {RequestMethod.POST})
    public BaseOutput findCategoryAndGoods(@RequestBody CategoryQueryForm form) {
        return goodsCategoryService.findCategoryAndGoods(form);
    }

    @RequestMapping(value = "/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody CategoryQueryForm form) {
        return goodsCategoryService.all(form);
    }

}