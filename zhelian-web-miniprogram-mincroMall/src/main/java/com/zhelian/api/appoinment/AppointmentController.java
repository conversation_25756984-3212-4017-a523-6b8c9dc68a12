package com.zhelian.api.appoinment;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.AppointmentCustomerForm;
import com.zhelian.model.ibeauty.form.input.AppointmentCustomerNumberForm;
import com.zhelian.model.micromall.form.input.AppointmentAddForm;
import com.zhelian.model.micromall.form.input.AppointmentQueryForm;
import com.zhelian.model.micromall.form.input.AppointmentUpdateForm;
import com.zhelian.service.micromall.appointment.MicroMallAppointmentCommonService;
import com.zhelian.service.micromall.appointment.MicroMallAppointmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api/appointment")
public class AppointmentController {

    @Autowired
    MicroMallAppointmentService appointmentService;

    @Autowired
    MicroMallAppointmentCommonService appointmentBillCommonService;


    @RequestMapping(value = "/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody AppointmentAddForm form) throws Exception {
        if (form.AppointmentDate == null)
            return BaseOutput.failed("预约日期不能为空");
        if (form.Period == null)
            return BaseOutput.failed("时长不能为空");
        return appointmentBillCommonService.microMallCreate(form);
    }

    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody AppointmentUpdateForm form) throws Exception {
        if (form.ID == null)
            return BaseOutput.failed("预约单不能为空");
        if (form.AppointmentDate == null)
            return BaseOutput.failed("预约日期不能为空");
        if (form.Period == null)
            return BaseOutput.failed("时长不能为空");
        return appointmentBillCommonService.microMallUpdate(form);
    }

    @RequestMapping(value = "/updateAppointment", method = {RequestMethod.POST})
    public BaseOutput updateAppointment(@RequestBody AppointmentUpdateForm form) throws Exception {
        return appointmentService.updateAppointment(form);
    }

    @RequestMapping(value = "/updateStatus", method = {RequestMethod.POST})
    public BaseOutput updateAppointmentStatus(@RequestBody AppointmentUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("预约单不能为空");
        if (form.Status == null)
            return BaseOutput.failed("状态不能为空");
        return appointmentBillCommonService.updateStatus(form);
    }

    @RequestMapping(value = "/entityList", method = {RequestMethod.POST})
    public BaseOutput entityList(@RequestBody AppointmentQueryForm form) {
        return appointmentService.entityList(form);
    }

    @RequestMapping(value = "/findCategoryAndProject", method = {RequestMethod.POST})
    public BaseOutput findCategoryAndProject(@RequestBody AppointmentQueryForm form) {
        return appointmentService.findCategoryAndProject(form);
    }

    @RequestMapping(value = "/getAppointmentList", method = {RequestMethod.POST})
    public BaseOutput getAppointmentList(@RequestBody AppointmentQueryForm form) {
        return appointmentService.getAppointmentList(form);
    }

    @RequestMapping(value = "/config", method = {RequestMethod.POST})
    public BaseOutput config() {
        return appointmentService.appointmentConfig();
    }

    @RequestMapping(value = "/getAppointmentServicerEmployee", method = {RequestMethod.POST})
    public BaseOutput getAppointmentServicerEmployee(@RequestBody AppointmentQueryForm form) {
        return appointmentService.getAppointmentServicerEmployee(form);
    }

    @RequestMapping(value = "/getAppointmentServicerEmployeeByDate", method = {RequestMethod.POST})
    public BaseOutput getAppointmentServicerEmployeeByDate(@RequestBody AppointmentQueryForm form) {
        return appointmentService.getAppointmentServicerEmployeeByDate(form);
    }

    @RequestMapping(value = "/getAppointmentDateByServicerEmployee", method = {RequestMethod.POST})
    public BaseOutput getAppointmentDateByServicerEmployee(@RequestBody AppointmentQueryForm form) {
        return appointmentService.getAppointmentDateByServicerEmployee(form);
    }

    @RequestMapping(value = "/getCustomerAppointmentAll", method = {RequestMethod.POST})
    public BaseOutput getCustomerAppointmentAll(@RequestBody AppointmentCustomerForm form) {
        if (form.AppointmentDate == null)
            return BaseOutput.failed("预约时间不能为空");
        return appointmentService.getCustomerAppointmentAll(form);
    }

    @RequestMapping(value = "/getCustomerAppointmentNumber", method = {RequestMethod.POST})
    public BaseOutput getCustomerAppointmentNumber(@RequestBody AppointmentCustomerNumberForm form) {
        if (form.AppointmentDate == null)
            return BaseOutput.failed("预约时间不能为空");
        return appointmentService.getCustomerAppointmentNumber(form);
    }
}