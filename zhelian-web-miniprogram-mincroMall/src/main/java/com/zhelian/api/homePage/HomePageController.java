package com.zhelian.api.homePage;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.micromall.form.input.HomePageQueryForm;
import com.zhelian.service.micromall.homePage.HomePageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api/homePage")
public class HomePageController {

    @Autowired
    HomePageService homePageService;

    @RequestMapping(value = "/all", method = {RequestMethod.POST})
    public BaseOutput all() {
        return homePageService.all();
    }

    @RequestMapping(value = "/getEntityList", method = {RequestMethod.POST})
    public BaseOutput getEntityList(@RequestBody HomePageQueryForm form) {
        return homePageService.getEntityList(form);
    }

    @RequestMapping(value = "/getCurrentEntity", method = {RequestMethod.POST})
    public BaseOutput getCurrentEntity() {
        return homePageService.getCurrentEntity();
    }

    @RequestMapping(value = "/customPage", method = {RequestMethod.POST})
    public BaseOutput customPageAll(@RequestBody HomePageQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        return homePageService.customPageAll(form);
    }
}