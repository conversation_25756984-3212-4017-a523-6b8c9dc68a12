package com.zhelian.api.cart;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.micromall.form.input.CartAddOrUpdateForm;
import com.zhelian.model.micromall.form.input.CartDetailAddForm;
import com.zhelian.service.micromall.cart.CartService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api/cart")
public class CartController {

    @Autowired
    CartService cartService;


    @RequestMapping(value = "/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody CartDetailAddForm form) {
        return cartService.addCart(form);
    }

    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody CartDetailAddForm form) {
        return cartService.updateCart(form);
    }

    @RequestMapping(value = "/delete", method = {RequestMethod.POST})
    public BaseOutput delete(@RequestBody CartAddOrUpdateForm form) {
        if (form.IDList == null)
            return BaseOutput.failed("ID不能为空");
        return cartService.deleteCart(form.IDList);
    }

    @RequestMapping(value = "/getCartList", method = {RequestMethod.POST})
    public BaseOutput getCartList() {
        return cartService.getCartList();
    }

}