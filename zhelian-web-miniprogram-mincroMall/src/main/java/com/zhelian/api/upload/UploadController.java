package com.zhelian.api.upload;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.AttachmentAddForm;
import com.zhelian.service.ibeauty.upload.UploadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping(value = "/api")
public class UploadController {

    @Autowired
    UploadService uploadService;

    /**
     * 上传图片
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/upload/addAttachment", method = {RequestMethod.POST})
    public BaseOutput addAttachment(@RequestBody AttachmentAddForm form) {
        return uploadService.addAttachment(form);
    }

    /**
     * 上传文件
     *
     * @return
     */

    @RequestMapping(value = "/upload/uploadFile", method = RequestMethod.POST)
    public BaseOutput uploadFile(@RequestParam("file") MultipartFile multipartFile) throws Exception {
        return uploadService.uploadFile(multipartFile);
    }
}