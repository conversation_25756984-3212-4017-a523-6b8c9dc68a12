package com.zhelian.config;

import com.zhelian.core.authorization.customer.BasicPreAuthenticatedProcessingFilter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;

@Configuration
public class SecurityConfiguration {
    @Bean
    public SecurityFilterChain filterChaine(HttpSecurity http) throws Exception {
        http.csrf().disable();
        http.sessionManagement().sessionCreationPolicy(SessionCreationPolicy.NEVER);
        http.authorizeRequests().antMatchers(HttpMethod.OPTIONS, "/**").permitAll().antMatchers("/api/excel/**", "/druid/**", "/favicon.ico", "/api/login", "/api/resetPassword", "/api/account/sendVerificationCode", "/api/account/resetPassword", "/api/open/**", "/api/receive_ticket", "/api/*/callback", "/api/goodsCategory/**", "/api/account/microMallAuthorizer", "/api/saleBill/savePaySuccess", "/api/grouponBill/savePaySuccess", "/api/seckillBill/savePaySuccess", "/").permitAll().anyRequest().authenticated();
        http.addFilter(new BasicPreAuthenticatedProcessingFilter());
        return http.build();
    }
}
