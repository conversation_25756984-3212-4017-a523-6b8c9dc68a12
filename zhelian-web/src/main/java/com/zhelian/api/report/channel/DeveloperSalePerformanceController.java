package com.zhelian.api.report.channel;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.DeveloperPerformanceDetailForm;
import com.zhelian.service.report.channel.DeveloperSalePerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/api/developerSalePerformanceDetailStatement")
public class DeveloperSalePerformanceController {

    @Autowired
    DeveloperSalePerformanceService developerSalePerformanceService;

    @PostMapping(value = "/list")
    public BaseOutput getDeveloperSalePerformanceDetail(@RequestBody DeveloperPerformanceDetailForm form) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return developerSalePerformanceService.getDeveloperSalePerformanceDetail(form);
    }


    @PostMapping(value = "/excel")
    public BaseOutput easyExcelDeveloperSalePerformanceDetail(@RequestBody DeveloperPerformanceDetailForm form, HttpServletResponse response) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return developerSalePerformanceService.easyExcelDeveloperSalePerformanceDetail(form, response);
    }
}
