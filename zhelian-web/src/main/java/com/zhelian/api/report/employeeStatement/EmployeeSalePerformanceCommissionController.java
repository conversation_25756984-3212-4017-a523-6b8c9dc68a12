package com.zhelian.api.report.employeeStatement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.EmployeeSalePerformanceCommissionDetailForm;
import com.zhelian.service.report.employeeStatement.EmployeeSalePerformanceCommissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/api", method = {RequestMethod.POST})
public class EmployeeSalePerformanceCommissionController {

    @Autowired
    EmployeeSalePerformanceCommissionService employeeSalePerformanceCommissionService;

    @RequestMapping(value = "/employeeSalePerformanceCommissionDetailStatement/list")
    public BaseOutput getEmployeeSalePerformanceCommissionDetail(@RequestBody EmployeeSalePerformanceCommissionDetailForm form) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return employeeSalePerformanceCommissionService.getEmployeeSalePerformanceCommissionDetail(form);
    }

    @PostMapping(value = "/employeeSalePerformanceCommissionDetailStatement/excel")
    public BaseOutput excelEmployeeSalePerformanceCommissionDetail(@RequestBody EmployeeSalePerformanceCommissionDetailForm form, HttpServletResponse response) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return employeeSalePerformanceCommissionService.excelEmployeeSalePerformanceCommissionDetail(form, response);
    }

    @RequestMapping(value = "/employeeSalePerformanceCommissionDetailStatement/statistics")
    public BaseOutput getEmployeeSalePerformanceCommissionStatistics(@RequestBody EmployeeSalePerformanceCommissionDetailForm form) {
        return employeeSalePerformanceCommissionService.getEmployeeSalePerformanceCommissionStatistics(form);
    }

    @RequestMapping(value = "/employeeSalePerformanceCommissionDetailStatement/statisticsExcel")
    public BaseOutput statisticsExcel(@RequestBody EmployeeSalePerformanceCommissionDetailForm form, HttpServletResponse response) {
        return employeeSalePerformanceCommissionService.statisticsExcel(form, response);
    }


}
