package com.zhelian.api.report.entityStatement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.EntityTreatGoodsDetailForm;
import com.zhelian.service.report.entityStatement.EntityTreatGoodsDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/api", method = {RequestMethod.POST})
public class EntityTreatGoodsDetailStatementController {

    @Autowired
    EntityTreatGoodsDetailService entityTreatGoodsDetailService;

    @RequestMapping(value = "/entityTreatGoodsDetailStatement/list")
    public BaseOutput getEntityTreatGoodsDetailStatement(@RequestBody EntityTreatGoodsDetailForm form) {
        if (form.StartTime == null || form.EndTime == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entityTreatGoodsDetailService.getEntityTreatGoodsDetailStatement(form);
    }


    @PostMapping(value = "/entityTreatGoodsDetailStatement/excel")
    public BaseOutput easyExcelEntityTreatDetailStatement(@RequestBody EntityTreatGoodsDetailForm form, HttpServletResponse response) {
        if (form.StartTime == null || form.EndTime == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entityTreatGoodsDetailService.easyExcelEntityTreatGoodsDetail(form, response);
    }

}
