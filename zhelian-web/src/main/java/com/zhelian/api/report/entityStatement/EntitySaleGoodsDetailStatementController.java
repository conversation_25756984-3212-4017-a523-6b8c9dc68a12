package com.zhelian.api.report.entityStatement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.EntitySaleGoodsDetailForm;
import com.zhelian.service.report.entityStatement.EntitySaleGoodsDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/api", method = {RequestMethod.POST})
public class EntitySaleGoodsDetailStatementController {

    @Autowired
    EntitySaleGoodsDetailService entitySaleGoodsDetailService;

    @RequestMapping(value = "/entitySaleGoodsDetailStatement/category")
    public BaseOutput category() {
        return entitySaleGoodsDetailService.category();
    }

    @RequestMapping(value = "/entitySaleGoodsDetailStatement/productAndProjectCategory")
    public BaseOutput productAndProjectCategory() {
        return entitySaleGoodsDetailService.productAndProjectCategory();
    }

    @RequestMapping(value = "/entitySaleGoodsDetailStatement/brand")
    public BaseOutput brand() {
        return entitySaleGoodsDetailService.brand();
    }

    @RequestMapping(value = "/entitySaleGoodsDetailStatement/list")
    public BaseOutput getEntitySaleGoodsDetailStatement(@RequestBody EntitySaleGoodsDetailForm form) {
        if (form.StartTime == null || form.EndTime == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entitySaleGoodsDetailService.getEntitySaleGoodsDetailStatement(form);
    }


    @PostMapping(value = "/entitySaleGoodsDetailStatement/excel")
    public BaseOutput easyExcelEntitySaleDetailStatement(@RequestBody EntitySaleGoodsDetailForm form, HttpServletResponse response) {
        if (form.StartTime == null || form.EndTime == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entitySaleGoodsDetailService.easyExcelEntitySaleGoodsDetail(form, response);
    }
}
