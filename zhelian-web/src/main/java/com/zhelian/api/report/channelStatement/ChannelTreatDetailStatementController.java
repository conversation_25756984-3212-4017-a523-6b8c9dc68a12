package com.zhelian.api.report.channelStatement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.ChannelTreatDetailForm;
import com.zhelian.service.report.channelStatement.ChannelTreatDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/api", method = {RequestMethod.POST})
public class ChannelTreatDetailStatementController {

    @Autowired
    ChannelTreatDetailService channelTreatDetailService;

    @RequestMapping(value = "/channelTreatDetailStatement/list")
    public BaseOutput getChannelTreatDetailStatement(@RequestBody ChannelTreatDetailForm form) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return channelTreatDetailService.getChannelTreatDetail(form);
    }


    @RequestMapping(value = "/channelTreatDetailStatement/excel")
    public BaseOutput easyExcelChannelTreatDetailStatement(@RequestBody ChannelTreatDetailForm form, HttpServletResponse response) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return channelTreatDetailService.easyExcelChannelTreatDetailStatement(form, response);
    }

    @RequestMapping(value = "/channelTreatRefundDetailStatement/list")
    public BaseOutput getChannelTreatRefundDetailStatement(@RequestBody ChannelTreatDetailForm form) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return channelTreatDetailService.getChannelTreatRefundDetail(form);
    }


    @RequestMapping(value = "/channelTreatRefundDetailStatement/excel")
    public BaseOutput easyExcelChannelTreatRefundDetailStatement(@RequestBody ChannelTreatDetailForm form, HttpServletResponse response) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return channelTreatDetailService.easyExcelChannelTreatRefundDetailStatement(form, response);
    }
}
