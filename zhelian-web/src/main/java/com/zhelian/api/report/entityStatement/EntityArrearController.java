package com.zhelian.api.report.entityStatement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.EntityTradingQueryForm;
import com.zhelian.service.report.entityStatement.EntityArrearService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/api", method = {RequestMethod.POST})
public class EntityArrearController {

    @Autowired
    EntityArrearService entityArrearService;


    @RequestMapping(value = "/entityArrear/list")
    public BaseOutput entityArrearStatement(@RequestBody EntityTradingQueryForm form) {

        return entityArrearService.entityArrearStatement(form);
    }

    @RequestMapping(value = "/entityArrear/excel")
    public BaseOutput easyExcelEntityArrearStatement(@RequestBody EntityTradingQueryForm form, HttpServletResponse response) {

        return entityArrearService.easyExcelEntityArrearStatement(form, response);
    }
}
