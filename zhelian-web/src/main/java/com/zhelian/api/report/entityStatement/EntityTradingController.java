package com.zhelian.api.report.entityStatement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.EntityTradingQueryForm;
import com.zhelian.service.report.entityStatement.EntityTradingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@RestController
@RequestMapping(value = "/api", method = {RequestMethod.POST})
public class EntityTradingController {

    @Autowired
    EntityTradingService entityTradingService;


    @RequestMapping(value = "/entityTrading/list")
    public BaseOutput tradingStatement(@RequestBody EntityTradingQueryForm form) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entityTradingService.tradingStatement(form);
    }

    @RequestMapping(value = "/entityTrading/excel")
    public BaseOutput easyExcelTradingStatement(@RequestBody EntityTradingQueryForm form, HttpServletResponse response) throws IOException {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entityTradingService.easyExcelTradingStatement(form, response);
    }
}
