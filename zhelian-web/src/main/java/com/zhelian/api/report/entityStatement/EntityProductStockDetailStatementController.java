package com.zhelian.api.report.entityStatement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.EntityProductStockDetailForm;
import com.zhelian.service.report.entityStatement.EntityProductStockDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api", method = {RequestMethod.POST})
public class EntityProductStockDetailStatementController {
    @Autowired
    EntityProductStockDetailService entityProductStockDetailService;

    @RequestMapping(value = "/entityProductStockDetailStatement/list")
    public BaseOutput getEntityProductStockDetailStatement(@RequestBody EntityProductStockDetailForm form) {
        if (form.StartTime == null || form.EndTime == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entityProductStockDetailService.getEntityProductStockDetail(form);
    }

}
