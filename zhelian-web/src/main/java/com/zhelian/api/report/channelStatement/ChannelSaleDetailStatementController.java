package com.zhelian.api.report.channelStatement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.ChannelSaleArrearDetailForm;
import com.zhelian.model.report.form.input.ChannelSaleDetailForm;
import com.zhelian.model.report.form.input.ChannelSaleRefundDetailForm;
import com.zhelian.service.report.channelStatement.ChannelSaleDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/api", method = {RequestMethod.POST})
public class ChannelSaleDetailStatementController {

    @Autowired
    ChannelSaleDetailService channelSaleDetailService;

    @RequestMapping(value = "/channelSaleDetailStatement/list")
    public BaseOutput getChannelSaleDetailStatement(@RequestBody ChannelSaleDetailForm form) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return channelSaleDetailService.getChannelSaleDetail(form);
    }


    @PostMapping(value = "/channelSaleDetailStatement/excel")
    public BaseOutput easyExcelChannelSaleDetailStatement(@RequestBody ChannelSaleDetailForm form, HttpServletResponse response) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return channelSaleDetailService.easyExcelChannelSaleDetailStatement(form, response);
    }

    @RequestMapping(value = "/channelSaleArrearDetailStatement/list")
    public BaseOutput getChannelSaleArrearDetailStatement(@RequestBody ChannelSaleArrearDetailForm form) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return channelSaleDetailService.getChannelSaleArrearDetail(form);
    }


    @RequestMapping(value = "/channelSaleArrearDetailStatement/excel")
    public BaseOutput easyExcelChannelSaleArrearDetailStatement(@RequestBody ChannelSaleDetailForm form, HttpServletResponse response) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return channelSaleDetailService.easyExcelChannelSaleArrearDetail(form, response);
    }

    @RequestMapping(value = "/channelSaleRefundDetailStatement/list")
    public BaseOutput getChannelSaleRefundDetailStatement(@RequestBody ChannelSaleRefundDetailForm form) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return channelSaleDetailService.getChannelSaleRefundDetail(form);
    }

    @RequestMapping(value = "/channelSaleRefundDetailStatement/excel")
    public BaseOutput easyExcelChannelSaleRefundDetailStatement(@RequestBody ChannelSaleDetailForm form, HttpServletResponse response) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return channelSaleDetailService.easyExcelChannelSaleRefundDetailStatement(form, response);
    }
}
