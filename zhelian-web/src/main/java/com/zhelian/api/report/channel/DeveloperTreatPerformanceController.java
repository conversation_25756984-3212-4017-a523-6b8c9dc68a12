package com.zhelian.api.report.channel;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.DeveloperTreatPerformanceDetailForm;
import com.zhelian.service.report.channel.DeveloperTreatPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "api/developerTreatPerformanceDetailStatement")
public class DeveloperTreatPerformanceController {

    @Autowired
    DeveloperTreatPerformanceService developerTreatPerformanceService;

    @PostMapping(value = "/list")
    public BaseOutput getDeveloperTreatPerformanceDetail(@RequestBody DeveloperTreatPerformanceDetailForm form) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return developerTreatPerformanceService.getDeveloperTreatPerformanceDetail(form);
    }


    @PostMapping(value = "/excel")
    public BaseOutput easyExcelDeveloperTreatPerformanceDetail(@RequestBody DeveloperTreatPerformanceDetailForm form, HttpServletResponse response) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return developerTreatPerformanceService.easyExcelDeveloperTreatPerformanceDetail(form, response);
    }
}
