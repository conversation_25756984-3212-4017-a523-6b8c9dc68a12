package com.zhelian.api.report.employeeStatement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.EmployeeSaleAndTreatDetailForm;
import com.zhelian.service.report.employeeStatement.EmployeeSaleAndTreatDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/api", method = {RequestMethod.POST})
public class EmployeeSaleAndTreatDetailStatementController {
    @Autowired
    EmployeeSaleAndTreatDetailService employeeSaleAndTreatDetailService;

    @RequestMapping(value = "/employeeSaleAndTreatDetailStatement/list")
    public BaseOutput getEmployeeTreatDetailStatement(@RequestBody EmployeeSaleAndTreatDetailForm form) {
        if (form.StartTime == null || form.EndTime == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return employeeSaleAndTreatDetailService.getEmployeeSaleAndTreatDetail(form);
    }

    @PostMapping(value = "/employeeSaleAndTreatDetailStatement/excel")
    public BaseOutput excelEmployeeSaleDetailStatement(@RequestBody EmployeeSaleAndTreatDetailForm form, HttpServletResponse response) {

        return employeeSaleAndTreatDetailService.getEmployeeSaleAndTreatDetailExcel(form, response);
    }
}
