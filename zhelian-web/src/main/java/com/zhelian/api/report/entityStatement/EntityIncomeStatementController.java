package com.zhelian.api.report.entityStatement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.EntityDateIncomeQueryForm;
import com.zhelian.model.report.form.input.EntityIncomeQueryForm;
import com.zhelian.service.report.entityStatement.EntityIncomeStatementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/api", method = {RequestMethod.POST})
public class EntityIncomeStatementController {

    @Autowired
    EntityIncomeStatementService entityIncomeStatementService;

    @RequestMapping(value = "/entityIncomeStatement/dateDetail")
    public BaseOutput getDateDetailIncomeStatement(@RequestBody EntityDateIncomeQueryForm form) {
        if (form.StartTime == null || form.EndTime == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entityIncomeStatementService.getDateIncomeStatement(form);
    }

    @RequestMapping(value = "/entityIncomeStatement/dateDetailExcel")
    public BaseOutput getDateDetailIncomeStatementExcel(@RequestBody EntityDateIncomeQueryForm form, HttpServletResponse response) {
        if (form.StartTime == null || form.StartTime == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entityIncomeStatementService.excelDateIncomeStatement(form, response);
    }

    @RequestMapping(value = "/entityIncomeStatement/entityDetail")
    public BaseOutput getEntityIncomeStatement(@RequestBody EntityIncomeQueryForm form) {
        if (form.StartTime == null || form.EndTime == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entityIncomeStatementService.getEntityIncomeStatement(form);
    }


    @RequestMapping(value = "/entityIncomeStatement/entityDetailExcel")
    public BaseOutput getEntityDetailIncomeStatementExcel(@RequestBody EntityIncomeQueryForm form, HttpServletResponse response) {
        if (form.StartTime == null || form.StartTime == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entityIncomeStatementService.excelEntityIncomeStatement(form, response);
    }

    @RequestMapping(value = "/entityIncomeStatement/list")
    public BaseOutput getIncomeStatement(@RequestBody EntityDateIncomeQueryForm form) {
        if (form.StartTime == null || form.EndTime == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entityIncomeStatementService.getIncomeStatement(form);
    }

    @RequestMapping(value = "/entityIncomeStatement/excel")
    public BaseOutput getlIncomeStatementExcel(@RequestBody EntityDateIncomeQueryForm form, HttpServletResponse response) {
        if (form.StartTime == null || form.StartTime == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entityIncomeStatementService.excelIncomeStatement(form, response);
    }
}
