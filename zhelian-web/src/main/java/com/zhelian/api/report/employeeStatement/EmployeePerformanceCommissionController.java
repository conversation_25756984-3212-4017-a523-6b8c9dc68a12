package com.zhelian.api.report.employeeStatement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.EmployeePerformanceCommissionDetailQueryForm;
import com.zhelian.service.report.employeeStatement.EmployeePerformanceCommissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/api", method = {RequestMethod.POST})
public class EmployeePerformanceCommissionController {

    @Autowired
    EmployeePerformanceCommissionService employeePerformanceCommissionService;

    /**
     * 银之湖
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/employeePerformanceCommissionDetailStatement/list")
    public BaseOutput getEmployeePerformanceCommissionDetail(@RequestBody EmployeePerformanceCommissionDetailQueryForm form) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return employeePerformanceCommissionService.getEmployeePerformanceCommissionDetail(form);
    }

    @PostMapping(value = "/employeePerformanceCommissionDetailStatement/excel")
    public BaseOutput excelEmployeePerformanceCommissionDetail(@RequestBody EmployeePerformanceCommissionDetailQueryForm form, HttpServletResponse response) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return employeePerformanceCommissionService.excelEmployeePerformanceCommissionDetail(form, response);
    }
}
