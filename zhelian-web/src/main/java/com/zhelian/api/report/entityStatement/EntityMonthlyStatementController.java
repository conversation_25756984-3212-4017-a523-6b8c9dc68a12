package com.zhelian.api.report.entityStatement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.MonthlyStatementQueryForm;
import com.zhelian.service.report.entityStatement.EntityMonthlyStatementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api", method = {RequestMethod.POST})
public class EntityMonthlyStatementController {

    @Autowired
    EntityMonthlyStatementService entityMonthlyStatementService;

    @RequestMapping(value = "/entityMonthlyStatement/payOverview")
    public BaseOutput getEntityMonthlyStatementPayOverview(@RequestBody MonthlyStatementQueryForm form) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entityMonthlyStatementService.getEntityMonthlyStatementPayOverview(form);
    }

    @RequestMapping(value = "/entityMonthlyStatement/saleOverview")
    public BaseOutput getEntityMonthlyStatementSaleOverview(@RequestBody MonthlyStatementQueryForm form) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entityMonthlyStatementService.getEntityMonthlyStatementSaleOverview(form);
    }

    @RequestMapping(value = "/entityMonthlyStatement/treatOverview")
    public BaseOutput getEntityMonthlyStatementTreatOverview(@RequestBody MonthlyStatementQueryForm form) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entityMonthlyStatementService.getEntityMonthlyStatementTreatOverview(form);
    }
}
