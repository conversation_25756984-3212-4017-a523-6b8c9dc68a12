package com.zhelian.api.report.entityStatement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.EntityTradingQueryForm;
import com.zhelian.service.report.entityStatement.EntityPassengerFlowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * 门店客流报表
 */
@RestController
@RequestMapping(value = "/api", method = {RequestMethod.POST})
public class EntityPassengerFlowController {

    @Autowired
    EntityPassengerFlowService entityPassengerFlowService;


    @RequestMapping(value = "/entityPassengerFlow/list")
    public BaseOutput entityPassengerFlowStatement(@RequestBody EntityTradingQueryForm form) {

        return entityPassengerFlowService.entityPassengerFlowStatement(form);
    }

    @RequestMapping(value = "/entityPassengerFlow/excel")
    public BaseOutput easyExcelEntityPassengerFlowStatement(@RequestBody EntityTradingQueryForm form, HttpServletResponse response) {

        return entityPassengerFlowService.easyExcelEntityPassengerFlowStatement(form, response);
    }

    /**
     * 门店销售客流
     * @param form
     * @return
     */
    @PostMapping(value = "/entityPassengerFlow/saleList")
    public BaseOutput entitySalePassengerFlowStatement(@RequestBody EntityTradingQueryForm form) {

        return entityPassengerFlowService.entitySalePassengerFlowStatement(form);
    }

    /**
     * 门店销售客流导出
     * @param form
     * @return
     */
    @PostMapping(value = "/entityPassengerFlow/saleExcel")
    public BaseOutput easyExcelEntitySalePassengerFlowStatement(@RequestBody EntityTradingQueryForm form, HttpServletResponse response) {

        return entityPassengerFlowService.easyExcelEntitySalePassengerFlowStatement(form,response);
    }

    /**
     * 门店消耗客流
     * @param form
     * @return
     */
    @PostMapping(value = "/entityPassengerFlow/treatList")
    public BaseOutput entityTreatPassengerFlowStatement(@RequestBody EntityTradingQueryForm form) {

        return entityPassengerFlowService.entityTreatPassengerFlowStatement(form);
    }

    /**
     * 门店消耗客流导出
     * @param form
     * @return
     */
    @PostMapping(value = "/entityPassengerFlow/treatExcel")
    public BaseOutput easyExcelEntityTreatPassengerFlowStatement(@RequestBody EntityTradingQueryForm form, HttpServletResponse response) {

        return entityPassengerFlowService.easyExcelEntityTreatPassengerFlowStatement(form,response);
    }
}
