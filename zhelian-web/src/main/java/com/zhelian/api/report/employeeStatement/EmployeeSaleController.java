package com.zhelian.api.report.employeeStatement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.EmployeeSalePerformanceCommissionDetailForm;
import com.zhelian.service.report.employeeStatement.EmployeeSaleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/api", method = {RequestMethod.POST})
public class EmployeeSaleController {

    @Autowired
    EmployeeSaleService employeeSaleService;


    @RequestMapping(value = "/employeeSaleStatement/statistics")
    public BaseOutput getEmployeeSaleStatistics(@RequestBody EmployeeSalePerformanceCommissionDetailForm form) {
        return employeeSaleService.getEmployeeSaleStatistics(form);
    }

    @RequestMapping(value = "/employeeSaleStatement/statisticsExcel")
    public BaseOutput statisticsExcel(@RequestBody EmployeeSalePerformanceCommissionDetailForm form, HttpServletResponse response) {
        return employeeSaleService.statisticsExcel(form, response);
    }


}
