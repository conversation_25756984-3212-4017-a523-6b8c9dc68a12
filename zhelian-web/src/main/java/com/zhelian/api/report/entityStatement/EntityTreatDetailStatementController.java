package com.zhelian.api.report.entityStatement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.EntityTreatDetailForm;
import com.zhelian.service.report.entityStatement.EntityTreatDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/api", method = {RequestMethod.POST})
public class EntityTreatDetailStatementController {
    @Autowired
    EntityTreatDetailService entityTreatDetailService;

    @RequestMapping(value = "/entityTreatDetailStatement/list")
    public BaseOutput getEntityTreatDetailStatement(@RequestBody EntityTreatDetailForm form) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entityTreatDetailService.getEntityTreatDetail(form);
    }


    @RequestMapping(value = "/entityTreatDetailStatement/excel")
    public BaseOutput easyExcelEntityTreatDetailStatement(@RequestBody EntityTreatDetailForm form, HttpServletResponse response) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entityTreatDetailService.easyExcelEntityTreatDetailStatement(form, response);
    }

    @RequestMapping(value = "/entityTreatDetailStatement/excelDisPlayPhone")
    public BaseOutput easyExcelDisPlayPhoneEntityTreatDetailStatement(@RequestBody EntityTreatDetailForm form, HttpServletResponse response) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entityTreatDetailService.easyExcelDisPlayPhoneEntityTreatDetailStatement(form, response);
    }

    @RequestMapping(value = "/entityTreatRefundDetailStatement/list")
    public BaseOutput getEntityTreatRefundDetailStatement(@RequestBody EntityTreatDetailForm form) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entityTreatDetailService.getEntityTreatRefundDetail(form);
    }


    @RequestMapping(value = "/entityTreatRefundDetailStatement/excel")
    public BaseOutput easyExcelEntityTreatRefundDetailStatement(@RequestBody EntityTreatDetailForm form, HttpServletResponse response) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entityTreatDetailService.easyExcelEntityTreatRefundDetailStatement(form, response);
    }

    @RequestMapping(value = "/entityTreatRefundDetailStatement/excelDisPlayPhone")
    public BaseOutput easyExcelDisPlayPhoneEntityTreatRefundDetailStatement(@RequestBody EntityTreatDetailForm form, HttpServletResponse response) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entityTreatDetailService.easyExcelDisPlayPhoneEntityTreatRefundDetailStatement(form, response);
    }
}
