package com.zhelian.api.report.entityStatement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.EntityTradingQueryForm;
import com.zhelian.service.report.entityStatement.EntityCustomerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/api", method = {RequestMethod.POST})
public class EntityCustomerController {

    @Autowired
    EntityCustomerService entityCustomerService;


    @PostMapping(value = "/entityCustomer/list")
    public BaseOutput entityCustomerStatement(@RequestBody EntityTradingQueryForm form) {

        return entityCustomerService.entityCustomerStatement(form);
    }


    @PostMapping(value = "/entityCustomer/excel")
    public BaseOutput easyExcelEntityCustomerStatement(@RequestBody EntityTradingQueryForm form, HttpServletResponse response) {

        return entityCustomerService.easyExcelEntityCustomerStatement(form, response);
    }
}
