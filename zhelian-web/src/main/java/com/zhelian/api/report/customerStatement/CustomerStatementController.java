package com.zhelian.api.report.customerStatement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.AccountStatisticsStatementQueryForm;
import com.zhelian.model.report.form.input.ArrearsDetailStatementQueryForm;
import com.zhelian.model.report.form.input.DynamicSaleAndDynamicConsumeStatementQueryForm;
import com.zhelian.service.report.customerStatement.CustomerStatementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/api", method = {RequestMethod.POST})
public class CustomerStatementController {

    @Autowired
    CustomerStatementService customerStatementService;

    @RequestMapping(value = "/customerStatement/dynamicSaleAndDynamicConsume")
    public BaseOutput customerDynamicSaleAndDynamicConsume(@RequestBody DynamicSaleAndDynamicConsumeStatementQueryForm form) {

        return customerStatementService.customerDynamicSaleAndDynamicConsume(form);
    }

    @PostMapping(value = "/customerStatement/dynamicSaleAndDynamicConsumeExcel")
    public BaseOutput easyExcelEntitySaleDetailStatement(@RequestBody DynamicSaleAndDynamicConsumeStatementQueryForm form, HttpServletResponse response) {

        return customerStatementService.dynamicSaleAndDynamicConsumeEasyExcel(form, response);
    }

    @RequestMapping(value = "/customerStatement/accountStatistics")
    public BaseOutput accountStatistics(@RequestBody AccountStatisticsStatementQueryForm form) {

        return customerStatementService.accountStatistics(form);
    }

    @PostMapping(value = "/customerStatement/accountStatisticsExcel")
    public BaseOutput accountStatisticsEasyExcel(@RequestBody AccountStatisticsStatementQueryForm form, HttpServletResponse response) {

        return customerStatementService.accountStatisticsEasyExcel(form, response);
    }

    @PostMapping(value = "/customerStatement/accountStatisticsExcelDisPlayPhone")
    public BaseOutput accountStatisticsEasyExcelDisPlayPhone(@RequestBody AccountStatisticsStatementQueryForm form, HttpServletResponse response) {

        return customerStatementService.accountStatisticsEasyExcelDisPlayPhone(form, response);
    }

    @RequestMapping(value = "/customerStatement/arrearsDetail")
    public BaseOutput arrearsDetail(@RequestBody ArrearsDetailStatementQueryForm form) {

        return customerStatementService.arrearsDetail(form);
    }

    @RequestMapping(value = "/customerStatement/arrearsDetailExcel")
    public BaseOutput arrearsDetailEasyExcel(@RequestBody ArrearsDetailStatementQueryForm form, HttpServletResponse response) {

        return customerStatementService.arrearsDetailEasyExcel(form, response);
    }

    @RequestMapping(value = "/customerStatement/arrearsDetailExcelDisPlayPhone")
    public BaseOutput arrearsDetailEasyExcelDisPlayPhone(@RequestBody ArrearsDetailStatementQueryForm form, HttpServletResponse response) {

        return customerStatementService.arrearsDetailEasyExcelDisPlayPhone(form, response);
    }
}
