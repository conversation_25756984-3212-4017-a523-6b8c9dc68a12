package com.zhelian.api.report.customerStatement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.CustomerBalanceStatementQueryForm;
import com.zhelian.service.report.customerStatement.CustomerBalanceStatementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api", method = {RequestMethod.POST})
public class CustomerBalanceStatementController {

    @Autowired
    CustomerBalanceStatementService customerBalanceStatementService;

    @RequestMapping(value = "/customerBalanceStatement/list")
    public BaseOutput customerBalance(@RequestBody CustomerBalanceStatementQueryForm form) {

        return customerBalanceStatementService.customerBalance(form);
    }


}
