package com.zhelian.api.report.employeeStatement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.EmployeeTreatPerformanceCommissionDetailForm;
import com.zhelian.service.report.employeeStatement.EmployeeTreatPerformanceCommissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/api", method = {RequestMethod.POST})
public class EmployeeTreatPerformanceCommissionController {
    @Autowired
    EmployeeTreatPerformanceCommissionService employeeTreatPerformanceCommissionService;

    @RequestMapping(value = "/employeeTreatPerformanceCommissionDetailStatement/list")
    public BaseOutput getEmployeeTreatPerformanceCommissionDetail(@RequestBody EmployeeTreatPerformanceCommissionDetailForm form) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return employeeTreatPerformanceCommissionService.getEmployeeSalePerformanceCommissionDetail(form);
    }

    @PostMapping(value = "/employeeTreatPerformanceCommissionDetailStatement/excel")
    public BaseOutput excelEmployeeTreatPerformanceCommissionDetail(@RequestBody EmployeeTreatPerformanceCommissionDetailForm form, HttpServletResponse response) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return employeeTreatPerformanceCommissionService.excelEmployeeTreatPerformanceCommissionDetail(form, response);
    }

    @RequestMapping(value = "/employeeTreatPerformanceCommissionDetailStatement/statistics")
    public BaseOutput getEmployeeSalePerformanceCommissionStatistics(@RequestBody EmployeeTreatPerformanceCommissionDetailForm form) {
        return employeeTreatPerformanceCommissionService.getEmployeeTreatPerformanceCommissionStatistics(form);
    }

    @RequestMapping(value = "/employeeTreatPerformanceCommissionDetailStatement/statisticsExcel")
    public BaseOutput statisticsExcel(@RequestBody EmployeeTreatPerformanceCommissionDetailForm form, HttpServletResponse response) {
        return employeeTreatPerformanceCommissionService.statisticsExcel(form, response);
    }
}
