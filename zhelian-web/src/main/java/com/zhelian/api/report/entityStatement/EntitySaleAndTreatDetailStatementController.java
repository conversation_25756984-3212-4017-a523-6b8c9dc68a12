package com.zhelian.api.report.entityStatement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.EntitySaleAndTreatDetailForm;
import com.zhelian.service.report.entityStatement.EntitySaleAndTreatDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api", method = {RequestMethod.POST})
public class EntitySaleAndTreatDetailStatementController {
    @Autowired
    EntitySaleAndTreatDetailService entitySaleAndTreatDetailService;

    @RequestMapping(value = "/entitySaleAndTreatDetailStatement/list")
    public BaseOutput getEntityTreatDetailStatement(@RequestBody EntitySaleAndTreatDetailForm form) {
        if (form.StartTime == null || form.EndTime == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entitySaleAndTreatDetailService.getEntitySaleAndTreatDetail(form);
    }

}
