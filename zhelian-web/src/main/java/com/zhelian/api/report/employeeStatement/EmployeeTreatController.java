package com.zhelian.api.report.employeeStatement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.EmployeeTreatPerformanceCommissionDetailForm;
import com.zhelian.service.report.employeeStatement.EmployeeTreatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/api", method = {RequestMethod.POST})
public class EmployeeTreatController {

    @Autowired
    EmployeeTreatService employeeTreatService;


    @RequestMapping(value = "/employeeTreatStatement/goodStatistics")
    public BaseOutput getEmployeeTreatStatistics(@RequestBody EmployeeTreatPerformanceCommissionDetailForm form) {
        return employeeTreatService.getEmployeeTreatStatistics(form);
    }

    @RequestMapping(value = "/employeeTreatStatement/goodStatisticsExcel")
    public BaseOutput goodStatisticsExcel(@RequestBody EmployeeTreatPerformanceCommissionDetailForm form, HttpServletResponse response) {
        return employeeTreatService.goodStatisticsExcel(form, response);
    }

    @RequestMapping(value = "/employeeTreatStatement/projectStatistics")
    public BaseOutput getEmployeeTreatProjectStatistics(@RequestBody EmployeeTreatPerformanceCommissionDetailForm form) {
        return employeeTreatService.getEmployeeTreatProjectStatistics(form);
    }

    @RequestMapping(value = "/employeeTreatStatement/projectStatisticsExcel")
    public BaseOutput projectStatisticsExcel(@RequestBody EmployeeTreatPerformanceCommissionDetailForm form, HttpServletResponse response) {
        return employeeTreatService.projectStatisticsExcel(form, response);
    }

    @RequestMapping(value = "/employeeTreatStatement/productStatistics")
    public BaseOutput getEmployeeTreatProductStatistics(@RequestBody EmployeeTreatPerformanceCommissionDetailForm form) {
        return employeeTreatService.getEmployeeTreatProductStatistics(form);
    }

    @RequestMapping(value = "/employeeTreatStatement/productStatisticsExcel")
    public BaseOutput productStatisticsExcel(@RequestBody EmployeeTreatPerformanceCommissionDetailForm form, HttpServletResponse response) {
        return employeeTreatService.productStatisticsExcel(form, response);
    }

    @RequestMapping(value = "/employeeTreatStatement/cardStatistics")
    public BaseOutput treatCardStatistics(@RequestBody EmployeeTreatPerformanceCommissionDetailForm form) {
        return employeeTreatService.treatCardStatistics(form);
    }

    @RequestMapping(value = "/employeeTreatStatement/cardStatisticsExcel")
    public BaseOutput cardStatisticsExcel(@RequestBody EmployeeTreatPerformanceCommissionDetailForm form, HttpServletResponse response) {
        return employeeTreatService.cardStatisticsExcel(form, response);
    }
}
