package com.zhelian.api.report.customerStatement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.CustomerStatisticsQueryForm;
import com.zhelian.service.report.customerStatement.CustomerStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/api", method = {RequestMethod.POST})
public class CustomerStatisticsController {

    @Autowired
    CustomerStatisticsService customerStatisticsService;

    @RequestMapping(value = "/customerStatistics/monthlySaleStatistics")
    public BaseOutput monthlySaleStatistics(@RequestBody CustomerStatisticsQueryForm form) {
        if (!StringUtils.hasText(form.SearchDate)) return BaseOutput.failed("时间筛选不能为空");

        return customerStatisticsService.monthlySaleStatistics(form);
    }

    @RequestMapping(value = "/customerStatistics/monthlySaleStatisticsExcel")
    public BaseOutput easyExcelMonthlySaleStatistics(@RequestBody CustomerStatisticsQueryForm form, HttpServletResponse response) {

        return customerStatisticsService.easyExcelMonthlySaleStatistics(form, response);
    }

    @RequestMapping(value = "/customerStatistics/monthlySaleStatisticsExcelDisPlayPhone")
    public BaseOutput easyExcelDisPlayPhoneMonthlySaleStatistics(@RequestBody CustomerStatisticsQueryForm form, HttpServletResponse response) {

        return customerStatisticsService.easyExcelDisPlayPhoneMonthlySaleStatistics(form, response);
    }

    @RequestMapping(value = "/customerStatistics/yearSaleStatistics")
    public BaseOutput yearSaleStatistics(@RequestBody CustomerStatisticsQueryForm form) {
        if (!StringUtils.hasText(form.SearchDate)) return BaseOutput.failed("时间筛选不能为空");

        return customerStatisticsService.yearSaleStatistics(form);
    }

    @RequestMapping(value = "/customerStatistics/yearSaleStatisticsExcel")
    public BaseOutput yearSaleStatisticsExcel(@RequestBody CustomerStatisticsQueryForm form, HttpServletResponse response) {

        return customerStatisticsService.easyExcelYearSaleStatistics(form, response);
    }

    @RequestMapping(value = "/customerStatistics/yearSaleStatisticsExcelDisPlayPhone")
    public BaseOutput yearSaleStatisticsExcelDisPlayPhone(@RequestBody CustomerStatisticsQueryForm form, HttpServletResponse response) {

        return customerStatisticsService.easyExcelDisPlayPhoneYearSaleStatistics(form, response);
    }


    @RequestMapping(value = "/customerStatistics/monthlyTreatStatistics")
    public BaseOutput monthlyTreatStatistics(@RequestBody CustomerStatisticsQueryForm form) {
        if (!StringUtils.hasText(form.SearchDate)) return BaseOutput.failed("时间筛选不能为空");

        return customerStatisticsService.monthlyTreatStatistics(form);
    }

    @RequestMapping(value = "/customerStatistics/monthlyTreatStatisticsExcel")
    public BaseOutput monthlyTreatStatisticsExcel(@RequestBody CustomerStatisticsQueryForm form, HttpServletResponse response) {

        return customerStatisticsService.easyExcelMonthlyTreatStatistics(form, response);
    }

    @RequestMapping(value = "/customerStatistics/monthlyTreatStatisticsExcelDisPlayPhone")
    public BaseOutput monthlyTreatStatisticsExcelDisPlayPhone(@RequestBody CustomerStatisticsQueryForm form, HttpServletResponse response) {

        return customerStatisticsService.easyExcelDisPlayPhoneMonthlyTreatStatistics(form, response);
    }

    @RequestMapping(value = "/customerStatistics/yearTreatStatistics")
    public BaseOutput yearTreatStatistics(@RequestBody CustomerStatisticsQueryForm form) {
        if (!StringUtils.hasText(form.SearchDate)) return BaseOutput.failed("时间筛选不能为空");

        return customerStatisticsService.yearTreatStatistics(form);
    }

    @RequestMapping(value = "/customerStatistics/yearTreatStatisticsExcel")
    public BaseOutput yearTreatStatisticsExcel(@RequestBody CustomerStatisticsQueryForm form, HttpServletResponse response) {

        return customerStatisticsService.yearTreatStatisticsExcel(form, response);
    }

    @RequestMapping(value = "/customerStatistics/yearTreatStatisticsExcelDisPlayPhone")
    public BaseOutput yearTreatStatisticsExcelDisPlayPhone(@RequestBody CustomerStatisticsQueryForm form, HttpServletResponse response) {

        return customerStatisticsService.yearTreatStatisticsExcelDisPlayPhone(form, response);
    }
}
