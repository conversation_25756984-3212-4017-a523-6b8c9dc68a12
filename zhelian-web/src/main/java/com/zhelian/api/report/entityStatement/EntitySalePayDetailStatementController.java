package com.zhelian.api.report.entityStatement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.EntitySalePayDetailForm;
import com.zhelian.service.report.entityStatement.EntitySalePayDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/api", method = {RequestMethod.POST})
public class EntitySalePayDetailStatementController {

    @Autowired
    EntitySalePayDetailService entitySalePayDetailService;

    @RequestMapping(value = "/entitySalePayDetailStatement/list")
    public BaseOutput getEntitySalePayDetailStatement(@RequestBody EntitySalePayDetailForm form) {
        if (form.StartTime == null || form.EndTime == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entitySalePayDetailService.getEntitySalePayDetailStatement(form);
    }

    @PostMapping(value = "/entitySalePayDetailStatement/excel")
    public BaseOutput easyExcelEntitySaleDetailStatement(@RequestBody EntitySalePayDetailForm form, HttpServletResponse response) {
        if (form.StartTime == null || form.EndTime == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entitySalePayDetailService.easyExcelEntitySalePayDetail(form, response);
    }

    @PostMapping(value = "/entitySalePayDetailStatement/excelDisPlayPhone")
    public BaseOutput easyExcelDisPlayPhoneEntitySaleDetailStatement(@RequestBody EntitySalePayDetailForm form, HttpServletResponse response) {
        if (form.StartTime == null || form.EndTime == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entitySalePayDetailService.easyExcelDisPlayPhoneEntitySaleDetailStatement(form, response);
    }
}
