package com.zhelian.api.report.entityStatement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.EntitySaleArrearDetailForm;
import com.zhelian.model.report.form.input.EntitySaleDetailForm;
import com.zhelian.model.report.form.input.EntitySaleRefundDetailForm;
import com.zhelian.service.report.entityStatement.EntitySaleDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/api", method = {RequestMethod.POST})
public class EntitySaleDetailStatementController {

    @Autowired
    EntitySaleDetailService entitySaleDetailService;

    @RequestMapping(value = "/entitySaleDetailStatement/list")
    public BaseOutput getEntitySaleDetailStatement(@RequestBody EntitySaleDetailForm form) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entitySaleDetailService.getEntitySaleDetail(form);
    }


    @PostMapping(value = "/entitySaleDetailStatement/excel")
    public BaseOutput easyExcelEntitySaleDetailStatement(@RequestBody EntitySaleDetailForm form, HttpServletResponse response) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entitySaleDetailService.easyExcelEntitySaleDetailStatement(form, response);
    }

    @PostMapping(value = "/entitySaleDetailStatement/excelDisPlayPhone")
    public BaseOutput easyExcelDisPlayPhoneEntitySaleDetailStatement(@RequestBody EntitySaleDetailForm form, HttpServletResponse response) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entitySaleDetailService.easyExcelDisPlayPhoneEntitySaleDetailStatement(form, response);
    }

    @RequestMapping(value = "/entitySaleArrearDetailStatement/list")
    public BaseOutput getEntitySaleArrearDetailStatement(@RequestBody EntitySaleArrearDetailForm form) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entitySaleDetailService.getEntitySaleArrearDetail(form);
    }


    @RequestMapping(value = "/entitySaleArrearDetailStatement/excel")
    public BaseOutput easyExcelEntitySaleArrearDetailStatement(@RequestBody EntitySaleDetailForm form, HttpServletResponse response) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entitySaleDetailService.easyExcelEntitySaleArrearDetail(form, response);
    }

    @RequestMapping(value = "/entitySaleArrearDetailStatement/excelDisPlayPhone")
    public BaseOutput easyExcelDisPlayPhoneEntitySaleArrearDetailStatement(@RequestBody EntitySaleDetailForm form, HttpServletResponse response) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entitySaleDetailService.easyExcelDisPlayPhoneEntitySaleArrearDetailStatement(form, response);
    }

    @RequestMapping(value = "/entitySaleRefundDetailStatement/list")
    public BaseOutput getEntitySaleRefundDetailStatement(@RequestBody EntitySaleRefundDetailForm form) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entitySaleDetailService.getEntitySaleRefundDetail(form);
    }

    @RequestMapping(value = "/entitySaleRefundDetailStatement/excel")
    public BaseOutput easyExcelEntitySaleRefundDetailStatement(@RequestBody EntitySaleDetailForm form, HttpServletResponse response) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entitySaleDetailService.easyExcelEntitySaleRefundDetailStatement(form, response);
    }

    @RequestMapping(value = "/entitySaleRefundDetailStatement/excelDisPlayPhone")
    public BaseOutput easyExcelDisPlayPhoneEntitySaleRefundDetailStatement(@RequestBody EntitySaleDetailForm form, HttpServletResponse response) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return entitySaleDetailService.easyExcelDisPlayPhoneEntitySaleRefundDetailStatement(form, response);
    }
}
