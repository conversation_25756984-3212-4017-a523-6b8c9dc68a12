package com.zhelian.api.report.entityStatement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.EntitySalePerformanceQueryForm;
import com.zhelian.service.report.entityStatement.EntitySalePerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/api", method = {RequestMethod.POST})
public class EntitySalePerformanceController {

    @Autowired
    EntitySalePerformanceService salePerformanceService;


    @RequestMapping(value = "/entitySalePerformance/list")
    public BaseOutput entitySalePerformance(@RequestBody EntitySalePerformanceQueryForm form) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return salePerformanceService.entitySalePerformance(form);
    }


    @PostMapping(value = "/entitySalePerformance/excel")
    public BaseOutput easyExcelEntitySalePerformance(@RequestBody EntitySalePerformanceQueryForm form, HttpServletResponse response) {
        if (form.StartDate == null || form.EndDate == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return salePerformanceService.easyExcelEntitySalePerformance(form, response);
    }
}
