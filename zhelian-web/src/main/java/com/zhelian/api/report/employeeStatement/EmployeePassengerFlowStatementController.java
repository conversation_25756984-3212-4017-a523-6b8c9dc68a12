package com.zhelian.api.report.employeeStatement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.report.form.input.EmployeePassengerFlowQueryForm;
import com.zhelian.service.report.employeeStatement.EmployeePassengerFlowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/api", method = {RequestMethod.POST})
public class EmployeePassengerFlowStatementController {
    @Autowired
    EmployeePassengerFlowService employeePassengerFlowService;

    @RequestMapping(value = "/employeePassengerFlowStatement/list")
    public BaseOutput getEmployeePassengerFlowStatement(@RequestBody EmployeePassengerFlowQueryForm form) {
        if (form.StartTime == null || form.EndTime == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return employeePassengerFlowService.getEmployeePassengerFlowStatement(form);
    }

    @PostMapping(value = "/employeePassengerFlowStatement/excel")
    public BaseOutput excelEmployeePassengerFlowStatement(@RequestBody EmployeePassengerFlowQueryForm form, HttpServletResponse response) {
        if (form.StartTime == null || form.EndTime == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return employeePassengerFlowService.excelEmployeePassengerFlowStatement(form, response);
    }
}
