package com.zhelian.api.ibeauty.treatGeneralCardCommission;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardsCommissionAddForm;
import com.zhelian.service.ibeauty.treatGeneralCardCommission.TreatGeneralCardCommissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

@RestController
@RequestMapping(value = "/api")
public class TreatGeneralCardCommissionController {

    @Autowired
    TreatGeneralCardCommissionService treatGeneralCardCommissionService;

    static boolean checkRate(TreatCardsCommissionAddForm form) {
        for (TreatCardCommissionAddForm handlerCommissionAddForm : form.Card) {
            if ((handlerCommissionAddForm.PayRate != null && handlerCommissionAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerCommissionAddForm.CardRate != null && handlerCommissionAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerCommissionAddForm.CardLargessRate != null && handlerCommissionAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerCommissionAddForm.LargessRate != null && handlerCommissionAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
            )
                return false;

        }
        return true;
    }

    @RequestMapping(value = "/treatGeneralCardCommission/all", method = {RequestMethod.POST})
    public BaseOutput allTreatGeneralCardCommission(@RequestBody TreatCardCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return treatGeneralCardCommissionService.allTreatGeneralCardCommission(form);
    }

    @RequestMapping(value = "/treatGeneralCardCommission/update", method = {RequestMethod.POST})
    public BaseOutput updateTreatGeneralCardCommission(@RequestBody TreatCardsCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatGeneralCardCommissionService.updateTreatGeneralCardCommission(form);
    }
}

