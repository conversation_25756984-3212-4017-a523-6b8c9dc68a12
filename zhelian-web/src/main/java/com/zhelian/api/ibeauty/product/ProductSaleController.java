package com.zhelian.api.ibeauty.product;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.service.ibeauty.product.ProductSaleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * @Description:
 * @Param:
 * @returns:
 * @Author: xuehao
 * @Date: 2020/3/21 9:33
 */
@RestController
@RequestMapping(value = "/api")
public class ProductSaleController {

    @Autowired
    ProductSaleService ProductService;

    @RequestMapping(value = "/productSale/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody ProductQueryForm form) {
        return ProductService.productList(form);
    }

    @RequestMapping(value = "/productSale/getProductDetails", method = {RequestMethod.POST})
    public BaseOutput getProductDetails(@RequestBody ProductSaleEntityQueryForm form) {
        return ProductService.getProductDetails(form);
    }

    @RequestMapping(value = "/productSale/productSaleEntity", method = {RequestMethod.POST})
    public BaseOutput productSaleEntity(@RequestBody ProductSaleEntityQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        return ProductService.productSaleEntity(form);
    }

    @RequestMapping(value = "/productSale/productModifyPriceEntity", method = {RequestMethod.POST})
    public BaseOutput productModifyPriceEntity(@RequestBody ProductSaleEntityQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        return ProductService.productModifyPriceEntity(form);
    }

    @RequestMapping(value = "/productSale/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody ProductUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("产品ID不能为空");
        if (form.Price == null || form.Price.compareTo(BigDecimal.ZERO) == 0)
            return BaseOutput.failed("产品单价不能为null或0");
        if (form.IsAllowSell == null)
            return BaseOutput.failed("是否上架不能为空");
        if (form.IsAllowLargess == null)
            return BaseOutput.failed("是否允许赠送不能为空");

        return ProductService.update(form);
    }

    @RequestMapping(value = "/productSale/all", method = {RequestMethod.POST})
    public BaseOutput all() {
        return ProductService.allProduct();
    }

    @RequestMapping(value = "/productSale/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody GoodsMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");

        return ProductService.move(form);
    }

    @RequestMapping(value = "/productSale/bitchUpdateProductSaleEntity", method = {RequestMethod.POST})
    public BaseOutput bitchUpdateProductSaleEntity(@RequestBody GoodsBitchUpdateForm form) {
        if (form.GoodsID.length == 0)
            return BaseOutput.failed("请选择产品");

        return ProductService.bitchUpdateProductSaleEntity(form);
    }
}