package com.zhelian.api.ibeauty.treatBill;

import com.aliyuncs.utils.StringUtils;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.service.ibeauty.treatBill.TreatGoodsAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/treatGoodsAccount")
public class TreatGoodsAccountController {

    @Autowired
    private TreatGoodsAccountService treatGoodsAccountService;

    /**
     * 功能描述：项目账户列表
     *
     * <AUTHOR>
     * @date 2020/7/1 8:55
     */
    @PostMapping("/projectAccount")
    public BaseOutput getProjectAccount(@RequestBody TreatProjectAccountForm form) {
        if (form.CustomerID == null) {
            return BaseOutput.failed("顾客编号不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return treatGoodsAccountService.getProjectAccount(form);
    }

    /**
     * 功能描述：产品账户列表
     *
     * <AUTHOR>
     * @date 2020/7/1 10:27
     */
    @PostMapping("/productAccount")
    public BaseOutput getProductAccount(@RequestBody TreatProductAccountForm form) {
        if (form.CustomerID == null) {
            return BaseOutput.failed("顾客编号不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return treatGoodsAccountService.getProductAccount(form);
    }

    /**
     * 功能描述：储值卡账户列表
     *
     * <AUTHOR>
     * @date 2020/7/1 11:26
     */
    @PostMapping("/savingCardAccount")
    public BaseOutput savingCardAccount(@RequestBody TreatSavingCardAccountForm form) {
        if (form.CustomerID == null) {
            return BaseOutput.failed("顾客编号不能为空");
        }
        if (StringUtils.isEmpty(form.BillDate)) {
            return BaseOutput.failed("开单时间不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return treatGoodsAccountService.getSavingCardAccount(form);
    }

    /**
     * 功能描述：储值卡消耗适用项目
     *
     * <AUTHOR>
     * @date 2020/7/1 14:27
     */
    @PostMapping("/savingCardAccountProject")
    public BaseOutput savingCardAccountProject(@RequestBody TreatSavingCardAccountProjectForm form) {
        if (form.SavingCardID == null) {
            return BaseOutput.failed("储值卡ID不能为空");
        }
        if (form.Type == null) {
            return BaseOutput.failed("参数Type不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return treatGoodsAccountService.getSavingCardProjectList(form);
    }

    /**
     * 功能描述：储值卡消耗适用项目--分类
     *
     * <AUTHOR>
     */

    @PostMapping("/savingCardAccountProjectCategory")
    public BaseOutput savingCardAccountProjectCategory(@RequestBody TreatSavingCardAccountProjectForm form) {
        if (form.SavingCardID == null) {
            return BaseOutput.failed("储值卡ID不能为空");
        }
        if (form.Type == null) {
            return BaseOutput.failed("参数Type不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return treatGoodsAccountService.savingCardAccountProjectCategory(form);
    }

    /**
     * 功能描述：储值卡消耗适用项目--分类下的项目
     *
     * <AUTHOR>
     */

    @PostMapping("/savingCardAccountProjectByCategory")
    public BaseOutput savingCardAccountProjectByCategory(@RequestBody TreatSavingCardAccountProjectForm form) {
        if (form.SavingCardID == null) {
            return BaseOutput.failed("储值卡ID不能为空");
        }
        if (form.Type == null) {
            return BaseOutput.failed("参数Type不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return treatGoodsAccountService.savingCardAccountProjectByCategory(form);
    }

    @PostMapping("/savingCardAccountProjectCustomerDiscount")
    public BaseOutput savingCardAccountProjectCustomerDiscount(@RequestBody TreatSavingCardAccountProjectCustomerDiscountForm form) {
        if (form.ProjectID == null) {
            return BaseOutput.failed("项目编号不能为空");
        }
        if (form.CustomerID == null) {
            return BaseOutput.failed("顾客编号不能为空");
        }
        return treatGoodsAccountService.savingCardAccountProjectCustomerDiscount(form);
    }


    /**
     * 功能描述：通用次卡账户列表
     *
     * <AUTHOR>
     * @date 2020/7/2 15:10
     */
    @PostMapping("/generalCardAccount")
    public BaseOutput generalCardAccount(@RequestBody TreatGeneralCardAccountForm form) {
        if (form.CustomerID == null) {
            return BaseOutput.failed("顾客编号不能为空");
        }
        if (StringUtils.isEmpty(form.BillDate)) {
            return BaseOutput.failed("开单时间不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return treatGoodsAccountService.generalCardAccount(form);
    }

    /**
     * 功能描述：通用次卡消耗适用项目
     *
     * <AUTHOR>
     * @date 2020/7/3 10:06
     */
    @PostMapping("/generalCardAccountProject")
    public BaseOutput generalCardAccountProject(@RequestBody GeneralCardProjectInputForm form) {
        if (form.GeneralCardID == null) {
            return BaseOutput.failed("通用次卡ID不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return treatGoodsAccountService.generalCardAccountProject(form);
    }


    /**
     * 通用次卡消耗适用项目--分类
     *
     * @param form
     * @return
     */
    @PostMapping("/generalCardAccountProjectCategory")
    public BaseOutput generalCardAccountProjectCategory(@RequestBody GeneralCardProjectInputForm form) {
        if (form.GeneralCardID == null) {
            return BaseOutput.failed("通用次卡ID不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return treatGoodsAccountService.generalCardAccountProjectCategory(form);
    }

    /**
     * 通用次卡消耗适用项目--分类下的项目
     *
     * @param form
     * @return
     */
    @PostMapping("/generalCardAccountProjectByCategory")
    public BaseOutput generalCardAccountProjectByCategory(@RequestBody GeneralCardProjectInputForm form) {
        if (form.GeneralCardID == null) {
            return BaseOutput.failed("通用次卡ID不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return treatGoodsAccountService.generalCardAccountProjectByCategory(form);
    }

    /**
     * 通用次卡--历史项目
     *
     * @param form
     * @return
     */
    @PostMapping("/generalCardHistoricalData")
    public BaseOutput generalCardHistoricalData(@RequestBody GeneralCardProjectInputForm form) {
        if (form.GeneralCardAccountID == null) {
            return BaseOutput.failed("通用次卡ID不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return treatGoodsAccountService.generalCardHistoricalData(form);
    }

    /**
     * 功能描述：时效卡账户列表
     *
     * <AUTHOR>
     * @date 2020/7/3 16:50
     */
    @PostMapping("/timeCardAccount")
    public BaseOutput timeCardAccount(@RequestBody TreatTimeCardAccountForm form) {
        if (form.CustomerID == null) {
            return BaseOutput.failed("顾客编号不能为空");
        }
        if (StringUtils.isEmpty(form.BillDate)) {
            return BaseOutput.failed("开单时间不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return treatGoodsAccountService.timeCardAccount(form);
    }

    /**
     * 功能描述：时效卡消耗适用项目
     *
     * <AUTHOR>
     * @date 2020/7/6 9:26
     */
    @PostMapping("/timeCardAccountProject")
    public BaseOutput timeCardAccountProject(@RequestBody TimeCardProjectInputForm form) {
        if (form.TimeCardID == null) {
            return BaseOutput.failed("时效卡ID不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return treatGoodsAccountService.timeCardAccountProject(form);
    }

    /**
     * 时效卡消耗适用项目--分类
     *
     * @param form
     * @return
     */
    @PostMapping("/timeCardAccountProjectCategory")
    public BaseOutput timeCardAccountProjectCategory(@RequestBody TimeCardProjectInputForm form) {
        if (form.TimeCardID == null) {
            return BaseOutput.failed("时效卡ID不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return treatGoodsAccountService.timeCardAccountProjectCategory(form);
    }

    /**
     * 时效卡消耗适用项目--分类下的项目
     *
     * @param form
     * @return
     */
    @PostMapping("/timeCardAccountProjectByCategory")
    public BaseOutput timeCardAccountProjectByCategory(@RequestBody TimeCardProjectInputForm form) {
        if (form.TimeCardID == null) {
            return BaseOutput.failed("时效卡ID不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return treatGoodsAccountService.timeCardAccountProjectByCategory(form);
    }


    /**
     * 功能描述：套餐卡账户列表
     *
     * <AUTHOR>
     * @date 2020/7/6 16:49
     */
    @PostMapping("/packageCardAccount")
    public BaseOutput packageCardAccount(@RequestBody TreatPackageCardAccountForm form) {
        if (form.CustomerID == null) {
            return BaseOutput.failed("顾客编号不能为空");
        }
        if (StringUtils.isEmpty(form.BillDate)) {
            return BaseOutput.failed("开单时间不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return treatGoodsAccountService.packageCardAccount(form);
    }

    /**
     * 功能描述：套餐卡账户明细
     *
     * <AUTHOR>
     * @date 2020/7/6 17:47
     */
    @PostMapping("/packageCardAccountDetails")
    public BaseOutput packageCardAccountDetails(@RequestBody PackageCardAccountDetailsInputForm form) {
        if (form.PackageCardAccountID == null) {
            return BaseOutput.failed("套餐卡账户ID不能为空");
        }
        if (StringUtils.isEmpty(form.BillDate)) {
            return BaseOutput.failed("开单时间不能为空");
        }
        return treatGoodsAccountService.packageCardAccountDetails(form);
    }
}
