package com.zhelian.api.ibeauty.project;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.ProjectCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.ProjectCategoryMoveForm;
import com.zhelian.model.ibeauty.form.input.ProjectCategoryQueryForm;
import com.zhelian.model.ibeauty.form.input.ProjectCategoryUpdateForm;
import com.zhelian.service.ibeauty.project.ProjectCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @className: ProjectCategoryController
 * @description:
 * @version: V1.0
 * @author: x<PERSON><PERSON>
 * @date: 2020/3/17 2:11 PM
 */
@RestController
@RequestMapping(value = "/api")
public class ProjectCategoryController {

    @Autowired
    ProjectCategoryService projectCategoryService;

    @RequestMapping(value = "/projectCategory/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody ProjectCategoryQueryForm form) {
        return projectCategoryService.projectCategoryList(form);
    }

    @RequestMapping(value = "/projectCategory/valid", method = {RequestMethod.POST})
    public BaseOutput ValidProjectCategoryValid() {
        return projectCategoryService.getValidProjectCategoryValid();
    }

    @RequestMapping(value = "/projectCategory/top", method = {RequestMethod.POST})
    public BaseOutput top(@RequestBody ProjectCategoryQueryForm form) {
        return projectCategoryService.getTopProjectList(form);
    }

    @RequestMapping(value = "/projectCategory/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody ProjectCategoryAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("项目分类名称不能为空");
        if (form.ParentID == null)
            return BaseOutput.failed("上级分类不能为空");

        return projectCategoryService.createProjectCategory(form);
    }

    @RequestMapping(value = "/projectCategory/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody ProjectCategoryUpdateForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("项目分类名称不能为空");
        if (form.ParentID == null)
            return BaseOutput.failed("上级分类不能为空");
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (form.Active == null)
            return BaseOutput.failed("有效性不能为空");

        return projectCategoryService.updateProjectCategory(form);
    }

    @RequestMapping(value = "/projectCategory/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody ProjectCategoryMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");

        return projectCategoryService.move(form);
    }
}