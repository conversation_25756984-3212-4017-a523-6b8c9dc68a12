package com.zhelian.api.ibeauty.Additional;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingAddForm;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingQueryForm;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingUpdateForm;
import com.zhelian.service.ibeauty.Additional.AdditionalSpendingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;

/**
 * 功能描述:
 * <>
 *
 * @return:
 * @exception:
 * @author: zhoutianxiang
 * @date: 2020/4/26 10:38 AM
 */
@RestController
@RequestMapping(value = "/api")
public class AdditionalSpendingController {

    @Autowired
    AdditionalSpendingService additionalSpendingService;

    @RequestMapping(value = "/additionalSpending/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody AdditionalSpendingQueryForm form) {
        return additionalSpendingService.additionalSpendingList(form);
    }

    @RequestMapping(value = "/additionalSpending/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody AdditionalSpendingAddForm form) {
        if (form.SpendingCategoryID == null) {
            return BaseOutput.failed("支出类型不能为空");
        }
        if (form.EntityID == null) {
            return BaseOutput.failed("所属门店不能为空");
        }
        if (form.SpendingDate == null) {
            return BaseOutput.failed("付款日期不能为空");
        }
        if (form.PaymentsID == null) {
            return BaseOutput.failed("支付方式不能为空");
        }
        if (form.Amount == null || form.Amount.compareTo(BigDecimal.ZERO) == 0) {
            return BaseOutput.failed("金额不能为空");
        }
        if (!StringUtils.hasText(form.Payer))
            return BaseOutput.failed("付款人不能为空");
        return additionalSpendingService.insertAdditionalSpending(form);
    }

    @RequestMapping(value = "/additionalSpending/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody AdditionalSpendingUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("编号不能为空");
        if (form.SpendingCategoryID == null) {
            return BaseOutput.failed("支出类型不能为空");
        }
        if (form.EntityID == null) {
            return BaseOutput.failed("所属门店不能为空");
        }
        if (form.SpendingDate == null) {
            return BaseOutput.failed("付款日期不能为空");
        }
        if (form.PaymentsID == null) {
            return BaseOutput.failed("支付方式不能为空");
        }
        if (form.Amount == null || form.Amount.compareTo(BigDecimal.ZERO) == 0) {
            return BaseOutput.failed("金额不能为空");
        }
        if (!StringUtils.hasText(form.Payer))
            return BaseOutput.failed("付款人不能为空");

        return additionalSpendingService.updateAdditionalSpending(form);
    }

    @RequestMapping(value = "/additionalSpending/delete", method = {RequestMethod.POST})
    public BaseOutput delete(@RequestBody AdditionalSpendingUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        return additionalSpendingService.deleteAdditionalSpendingByID(form);
    }

    @RequestMapping(value = "/additionalSpending/getAttachment", method = {RequestMethod.POST})
    public BaseOutput getAttachment(@RequestBody AdditionalSpendingUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        return additionalSpendingService.getAttachment(form);
    }

    @RequestMapping(value = "/additionalSpending/excel", method = {RequestMethod.POST})
    public BaseOutput excel2(@RequestBody AdditionalSpendingQueryForm form, HttpServletResponse response) {

        return additionalSpendingService.easyExcelAttachment(form, response);
    }
}