package com.zhelian.api.ibeauty.Additional;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingOrIncomeOrPaymentsCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingOrIncomeOrPaymentsCategoryQueryForm;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingOrIncomeOrPaymentsCategoryUpdateForm;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingOrIncomeOrPaymentsMoveForm;
import com.zhelian.service.ibeauty.Additional.AdditionalSpendingCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @className: AdditionalSpendingCategoryController
 * @description:
 * @version: V1.0
 * @author: zhoutianxiang
 * @date: 2020/4/25 4:36 PM
 */
@RestController
@RequestMapping(value = "/api")
public class AdditionalSpendingCategoryController {

    @Autowired
    AdditionalSpendingCategoryService additionalSpendingCategoryService;

    @RequestMapping(value = "/additionalSpendingCategory/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody AdditionalSpendingOrIncomeOrPaymentsCategoryQueryForm form) {
        return additionalSpendingCategoryService.all(form);
    }

    @RequestMapping(value = "/additionalSpendingCategory/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody AdditionalSpendingOrIncomeOrPaymentsCategoryAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("支出类型名称不能为空");

        return additionalSpendingCategoryService.create(form);
    }

    @RequestMapping(value = "/additionalSpendingCategory/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody AdditionalSpendingOrIncomeOrPaymentsCategoryUpdateForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("支出类型名称不能为空");
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (form.Active == null)
            return BaseOutput.failed("有效性不能为空");

        return additionalSpendingCategoryService.update(form);
    }

    @RequestMapping(value = "/additionalSpendingCategory/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody AdditionalSpendingOrIncomeOrPaymentsMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");
        return additionalSpendingCategoryService.move(form);
    }

    @RequestMapping(value = "/additionalSpendingCategory/getAdditionalSpendingCategory", method = {RequestMethod.POST})
    public BaseOutput all() {
        return additionalSpendingCategoryService.getAdditionalSpendingCategory();
    }
}