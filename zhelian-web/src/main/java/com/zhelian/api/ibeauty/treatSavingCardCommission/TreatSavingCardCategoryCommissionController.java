package com.zhelian.api.ibeauty.treatSavingCardCommission;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCategoryCommissionQueryForm;
import com.zhelian.service.ibeauty.treatSavingCardCommission.TreatSavingCardCategoryCommissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class TreatSavingCardCategoryCommissionController {

    @Autowired
    TreatSavingCardCategoryCommissionService treatSavingCardCategoryCommissionService;

    static boolean checkRate(TreatSavingCardProjectCategoryAddForm form) {

        for (TreatSavingCardProjectCategoryCommissionAddForm treatSavingCardProjectCategoryCommissionAddForm : form.Category) {
            if ((treatSavingCardProjectCategoryCommissionAddForm.Rate != null && treatSavingCardProjectCategoryCommissionAddForm.Rate.compareTo(new BigDecimal(100)) > 0)
                    || (treatSavingCardProjectCategoryCommissionAddForm.LargessRate != null && treatSavingCardProjectCategoryCommissionAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0))
                return false;
        }
        return true;
    }

    @RequestMapping(value = "/treatSavingCardCategoryCommission/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody TreatSavingCardProjectCategoryCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return treatSavingCardCategoryCommissionService.alltreatSavingCardCategoryCommission(form);
    }

    @RequestMapping(value = "/treatSavingCardCategoryCommission/update", method = {RequestMethod.POST})
    public BaseOutput updateTreatSavingCardCategoryCommission(@RequestBody TreatSavingCardProjectCategoryAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatSavingCardCategoryCommissionService.updateTreatSavingCardCategoryCommission(form);
    }
}

