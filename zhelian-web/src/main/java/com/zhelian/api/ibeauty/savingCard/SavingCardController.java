package com.zhelian.api.ibeauty.savingCard;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.service.ibeauty.savingCard.SavingCardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;

@RestController
@RequestMapping("/api")
public class SavingCardController {

    @Autowired
    SavingCardService savingCardService;

    @RequestMapping(value = "/savingCard/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody SavingCardQueryForm form) {
        return savingCardService.savingCardList(form);
    }

    @RequestMapping(value = "/savingCard/getSavingCardDetails", method = {RequestMethod.POST})
    public BaseOutput getSavingCardDetails(@RequestBody SavingCardQueryEntityForm form) {
        return savingCardService.getSavingCardDetails(form);
    }


    @RequestMapping(value = "/savingCard/savingCardSaleEntity", method = {RequestMethod.POST})
    public BaseOutput savingCardSaleEntity(@RequestBody SavingCardQueryEntityForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        return savingCardService.savingCardSaleEntity(form);
    }

    @RequestMapping(value = "/savingCard/savingCardConsumeEntity", method = {RequestMethod.POST})
    public BaseOutput savingCardConsumeEntity(@RequestBody SavingCardQueryEntityForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        return savingCardService.savingCardConsumeEntity(form);
    }

    public static boolean checkRechargeRulePrice(List<SavingCardRechargeRulesAddForm> RechargeRules) {
        if (RechargeRules != null && RechargeRules.size() > 0) {
            for (SavingCardRechargeRulesAddForm savingCardRechargeRulesAddForm : RechargeRules) {
                if (savingCardRechargeRulesAddForm.Price == null || savingCardRechargeRulesAddForm.LargessPrice == null)
                    return false;
            }
        }
        return true;
    }

    public static boolean checkDiscount(SavingCardGoodsForm GoodsRange) {
        if (GoodsRange.Product != null && GoodsRange.Product.size() > 0) {
            for (SavingCardGoodsAddForm goodsAddForm : GoodsRange.Product) {
                if (goodsAddForm.PriceType.equals(1) && goodsAddForm.DiscountPrice.compareTo(new BigDecimal(10)) > 0)
                    return false;
            }
        }
        if (GoodsRange.Project != null && GoodsRange.Project.size() > 0) {
            for (SavingCardGoodsAddForm goodsAddForm : GoodsRange.Project) {
                if (goodsAddForm.PriceType.equals(1) && goodsAddForm.DiscountPrice.compareTo(new BigDecimal(10)) > 0)
                    return false;
            }
        }
        if (GoodsRange.GeneralCard != null && GoodsRange.GeneralCard.size() > 0) {
            for (SavingCardGoodsAddForm goodsAddForm : GoodsRange.GeneralCard) {
                if (goodsAddForm.PriceType.equals(1) && goodsAddForm.DiscountPrice.compareTo(new BigDecimal(10)) > 0)
                    return false;
            }
        }
        if (GoodsRange.TimeCard != null && GoodsRange.TimeCard.size() > 0) {
            for (SavingCardGoodsAddForm goodsAddForm : GoodsRange.TimeCard) {
                if (goodsAddForm.PriceType.equals(1) && goodsAddForm.DiscountPrice.compareTo(new BigDecimal(10)) > 0)
                    return false;
            }
        }
        if (GoodsRange.PackageCard != null && GoodsRange.PackageCard.size() > 0) {
            for (SavingCardGoodsAddForm goodsAddForm : GoodsRange.PackageCard) {
                if (goodsAddForm.PriceType.equals(1) && goodsAddForm.DiscountPrice.compareTo(new BigDecimal(10)) > 0)
                    return false;
            }
        }

        if (GoodsRange.ProductCategory != null && GoodsRange.ProductCategory.size() > 0) {
            for (SavingCardGoodsCategoryAddForm categoryAddForm : GoodsRange.ProductCategory) {
                if (categoryAddForm.Discount.compareTo(new BigDecimal(10)) > 0)
                    return false;
            }
        }
        if (GoodsRange.ProjectCategory != null && GoodsRange.ProjectCategory.size() > 0) {
            for (SavingCardGoodsCategoryAddForm categoryAddForm : GoodsRange.ProjectCategory) {
                if (categoryAddForm.Discount.compareTo(new BigDecimal(10)) > 0)
                    return false;
            }
        }
        if (GoodsRange.GeneralCardCategory != null && GoodsRange.GeneralCardCategory.size() > 0) {
            for (SavingCardGoodsCategoryAddForm categoryAddForm : GoodsRange.GeneralCardCategory) {
                if (categoryAddForm.Discount.compareTo(new BigDecimal(10)) > 0)
                    return false;
            }
        }
        if (GoodsRange.TimeCardCategory != null && GoodsRange.TimeCardCategory.size() > 0) {
            for (SavingCardGoodsCategoryAddForm categoryAddForm : GoodsRange.TimeCardCategory) {
                if (categoryAddForm.Discount.compareTo(new BigDecimal(10)) > 0)
                    return false;
            }
        }
        if (GoodsRange.PackageCardCategory != null && GoodsRange.PackageCardCategory.size() > 0) {
            for (SavingCardGoodsCategoryAddForm categoryAddForm : GoodsRange.PackageCardCategory) {
                if (categoryAddForm.Discount.compareTo(new BigDecimal(10)) > 0)
                    return false;
            }
        }
        return true;
    }

    @RequestMapping(value = "/savingCard/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody SavingCardUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("储值卡名称不能为空");
        if (form.SavingCardCategoryID == null)
            return BaseOutput.failed("储值卡类型不能为空");
        if (form.Price == null)
            return BaseOutput.failed("储值卡售价不能为空");
        if (form.SaleStartDate == null)
            return BaseOutput.failed("售卖开始时间不能为空");
        if (form.SaleEndingDate == null)
            return BaseOutput.failed("售卖结束时间不能为空");
        if (form.ValidType == null)
            return BaseOutput.failed("有效期类型不能为空");
        if (!form.ValidType.equals(3) && form.ValidDay == null)
            return BaseOutput.failed("有效期天数不能为空");
        if (form.IsAllowSell == null)
            return BaseOutput.failed("是否上架不能为空");
        if (form.IsSettingLargess == null)
            return BaseOutput.failed("是否设置赠额使用范围不能为空");
        if (form.IsAllowSaleDeduct == null)
            return BaseOutput.failed("是否销售抵扣不能为空");
        if (form.IsAllowConsumeDeduct == null)
            return BaseOutput.failed("是否消耗抵扣不能为空");
        if (form.IsGoodsRange == null)
            return BaseOutput.failed("是否有权益范围不能为空");
        if (form.IsSettingLargess && form.IsLargessGoodsRange == null)
            return BaseOutput.failed("赠送是否有权益范围不能为空");
        if (form.IsGoodsRange && form.GoodsRange == null)
            return BaseOutput.failed("可用范围不能为空");
        if (form.IsLargessGoodsRange && form.GoodsRangeLargess == null)
            return BaseOutput.failed("赠送可用范围不能为空");
        if (form.GoodsRange != null) {
            if (!checkDiscount(form.GoodsRange))
                return BaseOutput.failed("折扣不能大于10");
        }
        if (form.IsUseRechargeRules == true && form.SavingCardRechargeRules == null)
            return BaseOutput.failed("设置阶梯价格不能为空");

        if (form.GoodsRangeLargess != null) {
            if (!checkDiscount(form.GoodsRangeLargess))
                return BaseOutput.failed("折扣不能大于10");
        }
        return savingCardService.update(form);
    }


    @RequestMapping(value = "/savingCard/product", method = {RequestMethod.POST})
    public BaseOutput product() {

        return savingCardService.product();
    }

    @RequestMapping(value = "/savingCard/project", method = {RequestMethod.POST})
    public BaseOutput project() {

        return savingCardService.project();
    }

    @RequestMapping(value = "/savingCard/generalCard", method = {RequestMethod.POST})
    public BaseOutput generalCard() {

        return savingCardService.generalCard();
    }

    @RequestMapping(value = "/savingCard/timeCard", method = {RequestMethod.POST})
    public BaseOutput timeCard() {

        return savingCardService.timeCard();
    }

    @RequestMapping(value = "/savingCard/packageCard", method = {RequestMethod.POST})
    public BaseOutput packageCard() {

        return savingCardService.packageCard();
    }

    @RequestMapping(value = "/savingCard/savingCardGoods", method = {RequestMethod.POST})
    public BaseOutput savingCardGoods(@RequestBody SavingCardGoodsQueryEntityForm form) {
        if (form.ID == null)
            return BaseOutput.failed("储值卡编号不能为空");
        if (form.IsLargess == null)
            return BaseOutput.failed("是否赠送不能为空");
        return savingCardService.savingCardGoods(form);
    }

    @RequestMapping(value = "/savingCard/rechargeRules", method = {RequestMethod.POST})
    public BaseOutput savingCardRechargeRules(@RequestBody SavingCardQueryEntityForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        return savingCardService.savingCardRechargeRules(form);
    }

    @RequestMapping(value = "/savingCard/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody SavingCardAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("储值卡名称不能为空");
        if (form.SavingCardCategoryID == null)
            return BaseOutput.failed("储值卡类型不能为空");
        if (form.Price == null)
            return BaseOutput.failed("储值卡售价不能为空");
        if (form.SaleStartDate == null)
            return BaseOutput.failed("售卖开始时间不能为空");
        if (form.SaleEndingDate == null)
            return BaseOutput.failed("售卖结束时间不能为空");
        if (form.ValidType == null)
            return BaseOutput.failed("有效期类型不能为空");
        if (!form.ValidType.equals(3) && form.ValidDay == null)
            return BaseOutput.failed("有效期天数不能为空");
        if (form.IsAllowSell == null)
            return BaseOutput.failed("是否上架不能为空");
        if (form.IsSettingLargess == null)
            return BaseOutput.failed("是否设置赠额使用范围不能为空");
        if (form.IsAllowSaleDeduct == null)
            return BaseOutput.failed("是否销售抵扣不能为空");
        if (form.IsAllowConsumeDeduct == null)
            return BaseOutput.failed("是否消耗抵扣不能为空");
        if (form.IsGoodsRange == null)
            return BaseOutput.failed("是否有权益范围不能为空");
        if (form.IsUseRechargeRules == null)
            return BaseOutput.failed("是否使用阶梯价格不能为空");
        if (form.IsModifyPrice == null)
            return BaseOutput.failed("是否可以修改本金不能为空");
        if (form.IsModifyLargessPrice == null)
            return BaseOutput.failed("是否可以修改赠金不能为空");
        if (form.IsSettingLargess && form.IsLargessGoodsRange == null)
            return BaseOutput.failed("赠送是否有权益范围不能为空");
        if (form.IsGoodsRange && form.GoodsRange == null)
            return BaseOutput.failed("可用范围不能为空");
        if (form.IsLargessGoodsRange && form.GoodsRangeLargess == null)
            return BaseOutput.failed("赠送可用范围不能为空");

        if (form.GoodsRange != null) {
            if (!checkDiscount(form.GoodsRange))
                return BaseOutput.failed("折扣不能大于10");
        }
        if (form.GoodsRangeLargess != null) {
            if (!checkDiscount(form.GoodsRangeLargess))
                return BaseOutput.failed("折扣不能大于10");
        }
        if (form.IsUseRechargeRules == true && form.SavingCardRechargeRules == null)
            return BaseOutput.failed("设置阶梯价格不能为空");

        if (form.SavingCardRechargeRules != null) {
            if (!checkRechargeRulePrice(form.SavingCardRechargeRules))
                return BaseOutput.failed("金额不能为空");
        }
        return savingCardService.create(form);
    }

    @RequestMapping(value = "/savingCard/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody GoodsMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");

        return savingCardService.move(form);
    }

    @RequestMapping(value = "/savingCard/bitchUpdateSavingCardSaleEntity", method = {RequestMethod.POST})
    public BaseOutput bitchUpdateSavingCardSaleEntity(@RequestBody GoodsBitchUpdateForm form) {
        if (form.GoodsID.length == 0)
            return BaseOutput.failed("请选择储值卡");

        return savingCardService.bitchUpdateSavingCardSaleEntity(form);
    }

    @RequestMapping(value = "/savingCard/bitchUpdateSavingCardConsumeEntity", method = {RequestMethod.POST})
    public BaseOutput bitchUpdateSavingCardConsumeEntity(@RequestBody GoodsBitchUpdateForm form) {
        if (form.GoodsID.length == 0)
            return BaseOutput.failed("请选择储值卡");

        return savingCardService.bitchUpdateSavingCardConsumeEntity(form);
    }
}
