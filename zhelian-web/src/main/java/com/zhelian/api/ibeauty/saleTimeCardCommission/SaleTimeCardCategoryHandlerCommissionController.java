package com.zhelian.api.ibeauty.saleTimeCardCommission;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.service.ibeauty.saleTimeCardCommission.SaleTimeCardCategoryHandlerCommissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * @Description:
 * @Param:
 * @returns:
 * @Author: 86158
 * @Date: 2020/4/2 13:26
 */
@RestController
@RequestMapping(value = "/api")
public class SaleTimeCardCategoryHandlerCommissionController {

    @Autowired
    SaleTimeCardCategoryHandlerCommissionService saleTimeCardCategoryHandlerCommissionService;


    @RequestMapping(value = "/saleTimeCardCategoryHandlerCommission/all", method = {RequestMethod.POST})
    public BaseOutput allSaleTimeCardCategoryHandlerCommission(@RequestBody SaleTimeCardCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return saleTimeCardCategoryHandlerCommissionService.allSaleTimeCardCategoryHandlerCommission(form);
    }

    @RequestMapping(value = "/saleTimeCardCategoryHandlerCommission/packageCard", method = {RequestMethod.POST})
    public BaseOutput allSalePackageCardTimeCardCategoryHandlerCommission(@RequestBody SaleTimeCardCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return saleTimeCardCategoryHandlerCommissionService.allSalePackageCardTimeCardCategoryHandlerCommission(form);
    }

    @RequestMapping(value = "/saleTimeCardCategoryHandlerCommission/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleTimeCardCategoryHandlerCommission(@RequestBody SaleTimeCardCategoryHandlerCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");

        return saleTimeCardCategoryHandlerCommissionService.updateSaleTimeCardCategoryHandlerCommission(form);
    }

    static boolean checkRate(SaleTimeCardCategoryHandlerCommissionAddForm form) {
        if (form.TimeCardHandler != null) {
            for (SaleTimeCardCategoryHandlerAddForm handlerAddForm : form.TimeCardHandler) {

                if ((handlerAddForm.PayRate != null && handlerAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerAddForm.SavingCardRate != null && handlerAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerAddForm.SavingCardLargessRate != null && handlerAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                    return false;

                for (SaleTimeCardCategoryHandlerJobTypeCommissionAddForm jobTypeCommissionAddForm : handlerAddForm.HandlerJobTypeCommission) {

                    if ((jobTypeCommissionAddForm.PayRate != null && jobTypeCommissionAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypeCommissionAddForm.SavingCardRate != null && jobTypeCommissionAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypeCommissionAddForm.SavingCardLargessRate != null && jobTypeCommissionAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                        return false;
                }
            }
        }
        return true;
    }
}

