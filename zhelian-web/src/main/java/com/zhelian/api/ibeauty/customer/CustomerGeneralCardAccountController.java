package com.zhelian.api.ibeauty.customer;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.AccountQueryForm;
import com.zhelian.service.ibeauty.customer.CustomerGeneralCardAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api/customerGeneralCardAccount")
public class CustomerGeneralCardAccountController {

    @Autowired
    CustomerGeneralCardAccountService customerGeneralCardAccountService;

    @RequestMapping(value = "/treatBill", method = {RequestMethod.POST})
    public BaseOutput treatBill(@RequestBody AccountQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("账户编号不能为空");
        return customerGeneralCardAccountService.treatBill(form);
    }

    @RequestMapping(value = "/refundBill", method = {RequestMethod.POST})
    public BaseOutput refundBill(@RequestBody AccountQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("账户编号不能为空");
        return customerGeneralCardAccountService.refundBill(form);
    }

    @RequestMapping(value = "/refundTreatBill", method = {RequestMethod.POST})
    public BaseOutput refundTreatBill(@RequestBody AccountQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("账户编号不能为空");
        return customerGeneralCardAccountService.refundTreatBill(form);
    }

    @RequestMapping(value = "/transferBill", method = {RequestMethod.POST})
    public BaseOutput transferBill(@RequestBody AccountQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("账户编号不能为空");
        return customerGeneralCardAccountService.transferBill(form);
    }

    @RequestMapping(value = "/repayment", method = {RequestMethod.POST})
    public BaseOutput repayment(@RequestBody AccountQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("账户编号不能为空");
        return customerGeneralCardAccountService.repayment(form);
    }

    @RequestMapping(value = "/arrearAmountProcess", method = {RequestMethod.POST})
    public BaseOutput arrearAmountProcess(@RequestBody AccountQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("账户编号不能为空");
        return customerGeneralCardAccountService.arrearAmountProcess(form);
    }
}