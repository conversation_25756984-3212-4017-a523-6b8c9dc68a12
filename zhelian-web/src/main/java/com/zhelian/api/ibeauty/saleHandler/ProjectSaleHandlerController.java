package com.zhelian.api.ibeauty.saleHandler;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerMoveForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerUpdateForm;
import com.zhelian.service.ibeauty.saleHandler.ProjectSaleHandlerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description:
 * @Param: No such property: code for class: Script1
 * @returns:
 * @Author: xuehao
 * @Date: 2020/3/30 16:20
 */
@RestController
@RequestMapping(value = "/api")
public class ProjectSaleHandlerController {

    @Autowired
    ProjectSaleHandlerService projectSaleHandlerService;

    @RequestMapping(value = "/projectSaleHandler/all", method = {RequestMethod.POST})
    public BaseOutput allProjectSaleHandler(@RequestBody SaleHandlerQueryForm form) {
        return projectSaleHandlerService.allProjectSaleHandler(form);
    }

    @RequestMapping(value = "/projectSaleHandler/create", method = {RequestMethod.POST})
    public BaseOutput createProjectSaleHandler(@RequestBody SaleHandlerAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("经手人名称不能为空");
        if (form.EntityID == null)
            return BaseOutput.failed("门店ID不能为空");
        if (form.JobType == null || form.JobType.length == 0)
            return BaseOutput.failed("适用职务不能为空");


        return projectSaleHandlerService.createProjectSaleHandler(form);
    }

    @RequestMapping(value = "/projectSaleHandler/update", method = {RequestMethod.POST})
    public BaseOutput updateProjectSaleHandler(@RequestBody SaleHandlerUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("经手人名称不能为空");
        if (form.EntityID == null)
            return BaseOutput.failed("门店ID不能为空");
        if (form.Active == null)
            return BaseOutput.failed("是否有效不能为空");
        if (form.JobType == null || form.JobType.length == 0)
            return BaseOutput.failed("适用职务不能为空");


        return projectSaleHandlerService.updateProjectSaleHandler(form);
    }

    @RequestMapping(value = "/projectSaleHandler/move", method = {RequestMethod.POST})
    public BaseOutput moveProjectSaleHandler(@RequestBody SaleHandlerMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");
        return projectSaleHandlerService.moveProjectSaleHandler(form);
    }
}