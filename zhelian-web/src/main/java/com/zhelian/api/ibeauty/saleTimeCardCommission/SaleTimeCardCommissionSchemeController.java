package com.zhelian.api.ibeauty.saleTimeCardCommission;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCommissionSchemeDeleteForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCommissionSchemeQueryForm;
import com.zhelian.service.ibeauty.saleTimeCardCommission.SaleTimeCardCommissionSchemeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description:
 * @Param:
 * @returns:
 * @Author: 86158
 * @Date: 2020/4/2 13:26
 */
@RestController
@RequestMapping(value = "/api")
public class SaleTimeCardCommissionSchemeController {

    @Autowired
    SaleTimeCardCommissionSchemeService saleTimeCardCommissionSchemeService;

    @RequestMapping(value = "/saleTimeCardCommissionScheme/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody SaleTimeCardCommissionSchemeQueryForm form) {
        return saleTimeCardCommissionSchemeService.saleTimeCardCommissionSchemeList(form);
    }

    @RequestMapping(value = "/saleTimeCardCommissionScheme/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody SaleTimeCardCommissionSchemeAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return saleTimeCardCommissionSchemeService.createSaleTimeCardCommissionScheme(form);
    }

    @RequestMapping(value = "/saleTimeCardCommissionScheme/delete", method = {RequestMethod.POST})
    public BaseOutput delete(@RequestBody SaleTimeCardCommissionSchemeDeleteForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return saleTimeCardCommissionSchemeService.deleteSaleTimeCardCommissionScheme(form);
    }

}

