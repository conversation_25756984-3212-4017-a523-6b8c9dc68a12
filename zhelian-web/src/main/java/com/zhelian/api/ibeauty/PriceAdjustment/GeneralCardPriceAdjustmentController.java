package com.zhelian.api.ibeauty.PriceAdjustment;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.GeneralCardPriceAdjustmentAddForm;
import com.zhelian.model.ibeauty.form.input.GeneralCardPriceAdjustmentDeleteForm;
import com.zhelian.model.ibeauty.form.input.GeneralCardPriceAdjustmentQueryForm;
import com.zhelian.service.ibeauty.PriceAdjustment.GeneralCardPriceAdjustmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class GeneralCardPriceAdjustmentController {

    @Autowired
    GeneralCardPriceAdjustmentService generalCardPriceAdjustmentService;

    @RequestMapping(value = "/generalCardPriceAdjustment/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody GeneralCardPriceAdjustmentQueryForm form) {
        return generalCardPriceAdjustmentService.generalCardPriceAdjustmentList(form);
    }

    @RequestMapping(value = "/generalCardPriceAdjustment/create", method = {RequestMethod.POST})
    public BaseOutput createGeneralCardPriceAdjustment(@RequestBody GeneralCardPriceAdjustmentAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("门店编号不能为空");
        if (form.GeneralCardID == null)
            return BaseOutput.failed("通用次卡编号不能为空");
        if (form.Price == null)
            return BaseOutput.failed("调整价格不能为空");

        return generalCardPriceAdjustmentService.createGeneralCardPriceAdjustment(form);
    }

    @RequestMapping(value = "/generalCardPriceAdjustment/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody GeneralCardPriceAdjustmentAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("门店编号不能为空");
        if (form.GeneralCardID == null)
            return BaseOutput.failed("通用次卡编号不能为空");
        if (form.Price == null)
            return BaseOutput.failed("调整价格不能为空");

        return generalCardPriceAdjustmentService.updateGeneralCardPriceAdjustmentEntity(form);
    }

    @RequestMapping(value = "/generalCardPriceAdjustment/delete", method = {RequestMethod.POST})
    public BaseOutput delete(@RequestBody GeneralCardPriceAdjustmentDeleteForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("门店编号不能为空");
        if (form.GeneralCardID == null)
            return BaseOutput.failed("通用次卡编号不能为空");

        return generalCardPriceAdjustmentService.deleteGeneralCardPriceAdjustmentEntity(form);
    }
}