package com.zhelian.api.ibeauty.generalCard;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.service.ibeauty.generalCard.GeneralCardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

@RestController
@RequestMapping("/api")
public class GeneralCardController {

    @Autowired
    GeneralCardService service;

    @RequestMapping(value = "/generalCard/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody GeneralCardQueryForm form) {
        return service.generalCardList(form);
    }

    @RequestMapping(value = "/generalCard/getGeneralCardDetails", method = {RequestMethod.POST})
    public BaseOutput getGeneralCardDetails(@RequestBody GeneralCardQueryEntityForm form) {
        return service.getGeneralCardDetails(form);
    }

    @RequestMapping(value = "/generalCard/generalCardSaleEntity", method = {RequestMethod.POST})
    public BaseOutput generalCardSaleEntity(@RequestBody GeneralCardQueryEntityForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        return service.generalCardSaleEntity(form);
    }

    @RequestMapping(value = "/generalCard/generalCardConsumeEntity", method = {RequestMethod.POST})
    public BaseOutput generalCardConsumeEntity(@RequestBody GeneralCardQueryEntityForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        return service.generalCardConsumeEntity(form);
    }

    @RequestMapping(value = "/generalCard/generalCardModifyPriceEntity", method = {RequestMethod.POST})
    public BaseOutput generalCardModifyPriceEntity(@RequestBody GeneralCardQueryEntityForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        return service.generalCardModifyPriceEntity(form);
    }

    @RequestMapping(value = "/generalCard/generalCardProject", method = {RequestMethod.POST})
    public BaseOutput generalCardProject(@RequestBody GeneralCardQueryEntityForm form) {
        return service.generalCardProject(form);
    }

    @RequestMapping(value = "/generalCard/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody GeneralCardAddForm form) {
        if (!StringUtils.hasText(form.Name)) {
            return BaseOutput.failed("通用次卡名称不能为空");
        }
        if (form.GeneralCardCategoryID == null) {
            return BaseOutput.failed("通用次卡类型不能为空");
        }
        if (form.Price == null || form.Price.compareTo(BigDecimal.ZERO) == 0) {
            return BaseOutput.failed("通用次卡售价不能为空");
        }
        if (form.Amount == null) {
            return BaseOutput.failed("通用次卡次数不能为空");
        }
        if (form.SaleStartDate == null) {
            return BaseOutput.failed("售卖开始时间不能为空");
        }
        if (form.SaleEndingDate == null) {
            return BaseOutput.failed("售卖结束时间不能为空");
        }
        if (form.ValidType == null) {
            return BaseOutput.failed("有效期类型不能为空");
        }
        if (!form.ValidType.equals(3) && form.ValidDay == null) {
            return BaseOutput.failed("有效期天数不能为空");
        }
        if (form.IsAllowSell == null) {
            return BaseOutput.failed("是否上架不能为空");
        }
        if (form.IsAllowLargess == null) {
            return BaseOutput.failed("是否允许赠送不能为空");
        }

        if (form.TreatProjectNumber == null) {
            return BaseOutput.failed("消耗项目数量不能为空");
        }
        return service.create(form);
    }

    @RequestMapping(value = "/generalCard/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody GeneralCardUpdateForm form) {
        if (!StringUtils.hasText(form.Name)) {
            return BaseOutput.failed("通用次卡名称不能为空");
        }
        if (form.GeneralCardCategoryID == null) {
            return BaseOutput.failed("通用次卡类型不能为空");
        }
        if (form.Price == null || form.Price.compareTo(BigDecimal.ZERO) == 0) {
            return BaseOutput.failed("通用次卡售价不能为空");
        }
        if (form.Amount == null) {
            return BaseOutput.failed("通用次卡次数不能为空");
        }
        if (form.SaleStartDate == null) {
            return BaseOutput.failed("售卖开始时间不能为空");
        }
        if (form.SaleEndingDate == null) {
            return BaseOutput.failed("售卖结束时间不能为空");
        }
        if (form.ValidType == null) {
            return BaseOutput.failed("有效期类型不能为空");
        }
        if (!form.ValidType.equals(3) && form.ValidDay == null) {
            return BaseOutput.failed("有效期天数不能为空");
        }
        if (form.IsAllowSell == null) {
            return BaseOutput.failed("是否上架不能为空");
        }
        if (form.IsAllowLargess == null) {
            return BaseOutput.failed("是否允许赠送不能为空");
        }

        if (form.TreatProjectNumber == null) {
            return BaseOutput.failed("消耗项目数量不能为空");
        }
        return service.update(form);
    }

    @RequestMapping(value = "/generalCard/findCategoryAndProject", method = {RequestMethod.POST})
    public BaseOutput findCategoryAndProject() {
        return service.findCategoryAndProject();
    }

    @RequestMapping(value = "/generalCard/all", method = {RequestMethod.POST})
    public BaseOutput all() {
        return service.allGeneralCard();
    }

    @RequestMapping(value = "/generalCard/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody GoodsMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");

        return service.move(form);
    }

    @RequestMapping(value = "/generalCard/bitchUpdateGeneralCardSaleEntity", method = {RequestMethod.POST})
    public BaseOutput bitchUpdateProductSaleEntity(@RequestBody GoodsBitchUpdateForm form) {
        if (form.GoodsID.length == 0)
            return BaseOutput.failed("请选择通用次卡");

        return service.bitchUpdateGeneralCardSaleEntity(form);
    }

    @RequestMapping(value = "/generalCard/bitchUpdateGeneralCardConsumeEntity", method = {RequestMethod.POST})
    public BaseOutput bitchUpdateGeneralCardConsumeEntity(@RequestBody GoodsBitchUpdateForm form) {
        if (form.GoodsID.length == 0)
            return BaseOutput.failed("请选择通用次卡");

        return service.bitchUpdateGeneralCardConsumeEntity(form);
    }
}
