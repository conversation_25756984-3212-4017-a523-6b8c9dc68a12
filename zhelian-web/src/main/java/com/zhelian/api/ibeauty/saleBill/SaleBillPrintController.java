package com.zhelian.api.ibeauty.saleBill;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleBillInfoQueryForm;
import com.zhelian.service.ibeauty.saleBill.SaleBillPrintService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
public class SaleBillPrintController {

    @Autowired
    SaleBillPrintService saleBillPrintService;

    @RequestMapping(value = "/saleBill/printContent", method = {RequestMethod.POST})
    public BaseOutput getSaleBillPrintContent(@RequestBody SaleBillInfoQueryForm form) {
        return saleBillPrintService.getSaleBillPrintContent(form);
    }


    @RequestMapping(value = "/saleBill/printSaleBillContent", method = {RequestMethod.POST})
    public BaseOutput getSaleBill(@RequestBody SaleBillInfoQueryForm form) {
        return saleBillPrintService.getSaleBill(form);
    }

    @RequestMapping(value = "/saleBill/printRefundSaleBillContent", method = {RequestMethod.POST})
    public BaseOutput getRefundSaleBill(@RequestBody SaleBillInfoQueryForm form) {
        return saleBillPrintService.getRefundSaleBill(form);
    }

    @RequestMapping(value = "/saleBill/printArrearSaleBillContent", method = {RequestMethod.POST})
    public BaseOutput getArrearSaleBill(@RequestBody SaleBillInfoQueryForm form) {
        return saleBillPrintService.getArrearSaleBill(form);
    }
}
