package com.zhelian.api.ibeauty.SealingAccount;
import com.zhelian.core.authorization.user.SystemUserUtil;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.service.ibeauty.SealingAccount.SealingAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
public class SealingAccountController {

    @Autowired
    SealingAccountService sealingAccountService;

    @RequestMapping(value = "/sealingAccount/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody SealingAccountQueryForm form) {
        return sealingAccountService.sealingAccountList(form);
    }

    @RequestMapping(value = "/sealingAccount/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody SealingAccountAddForm form) {
        if (form.StartAccountPeriod==null)
            return BaseOutput.failed("账期时间开始周期不能为空");
        if (form.StartAccountDate==null)
            return BaseOutput.failed("账期时间开始日期不能为空");
        if (form.EndAccountPeriod==null)
            return BaseOutput.failed("账期时间结束周期不能为空");
        if (form.EndAccountDate==null)
            return BaseOutput.failed("账期时间结算日期不能为空");
        if (form.CloseAccountDate==null)
            return BaseOutput.failed("关账日期不能为空");
        if (form.ReplacementOrder==null)
            return BaseOutput.failed("是否限制补单不能为空");
        if (form.CancelOrder==null)
            return BaseOutput.failed("是否限制取消订单不能为空");
        if (form.ModifyBillDate==null)
            return BaseOutput.failed("是否限制修改订单时间不能为空");
        if (form.ModifyBillPayMethod==null)
            return BaseOutput.failed("是否限制修改订单支付方式不能为空");
        if (form.ModifyEntityPerformance==null)
            return BaseOutput.failed("是否限制修改门店业绩不能为空");
        if (form.ModifyEmployeePerformanceCommission==null)
            return BaseOutput.failed("是否限制修改员工业绩提成不能为空");
        if (form.IncomeAndSpending==null)
            return BaseOutput.failed("是否限制日常收支不能为空");
        return sealingAccountService.createSealingAccount(form);
    }

    @RequestMapping(value = "/sealingAccount/update", method = {RequestMethod.POST})
    public BaseOutput updateSealingAccount(@RequestBody SealingAccountUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (form.StartAccountPeriod==null)
            return BaseOutput.failed("账期时间开始周期不能为空");
        if (form.StartAccountDate==null)
            return BaseOutput.failed("账期时间开始日期不能为空");
        if (form.EndAccountPeriod==null)
            return BaseOutput.failed("账期时间结束周期不能为空");
        if (form.EndAccountDate==null)
            return BaseOutput.failed("账期时间结算日期不能为空");
        if (form.CloseAccountDate==null)
            return BaseOutput.failed("关账日期不能为空");
        if (form.ReplacementOrder==null)
            return BaseOutput.failed("是否限制补单不能为空");
        if (form.CancelOrder==null)
            return BaseOutput.failed("是否限制取消订单不能为空");
        if (form.ModifyBillDate==null)
            return BaseOutput.failed("是否限制修改订单时间不能为空");
        if (form.ModifyBillPayMethod==null)
            return BaseOutput.failed("是否限制修改订单支付方式不能为空");
        if (form.ModifyEntityPerformance==null)
            return BaseOutput.failed("是否限制修改门店业绩不能为空");
        if (form.ModifyEmployeePerformanceCommission==null)
            return BaseOutput.failed("是否限制修改员工业绩提成不能为空");
        if (form.IncomeAndSpending==null)
            return BaseOutput.failed("是否限制日常收支不能为空");
        return sealingAccountService.updateSealingAccount(form);
    }

    @RequestMapping(value = "/sealingAccount/delete", method = {RequestMethod.POST})
    public BaseOutput delete(@RequestBody SealingAccountUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        return sealingAccountService.delete(form);
    }


    @RequestMapping(value = "/sealingAccount/entity", method = {RequestMethod.POST})
    public BaseOutput entity() {
        return sealingAccountService.getEmployeeEntityPermission();
    }

    @RequestMapping(value = "/sealingAccount/getReplacementOrderRestriction", method = {RequestMethod.POST})
    public BaseOutput getReplacementOrderRestriction() {
        return sealingAccountService.getReplacementOrderRestriction();
    }

    @RequestMapping(value = "/sealingAccount/getCancelOrderRestriction", method = {RequestMethod.POST})
    public BaseOutput getCancelOrderRestriction() {
        return sealingAccountService.getCancelOrderRestriction();
    }

    @RequestMapping(value = "/sealingAccount/getModifyBillDateRestriction", method = {RequestMethod.POST})
    public BaseOutput getModifyBillDateRestriction() {
        return sealingAccountService.getModifyBillDateRestriction();
    }

    @RequestMapping(value = "/sealingAccount/getModifyBillPayMethodRestriction", method = {RequestMethod.POST})
    public BaseOutput getModifyBillPayMethodRestriction() {
        return sealingAccountService.getModifyBillPayMethodRestriction();
    }

    @RequestMapping(value = "/sealingAccount/getModifyEntityPerformanceRestriction", method = {RequestMethod.POST})
    public BaseOutput getModifyEntityPerformanceRestriction() {
        return sealingAccountService.getModifyEntityPerformanceRestriction();
    }

    @RequestMapping(value = "/sealingAccount/getModifyEmployeePerformanceCommissionRestriction", method = {RequestMethod.POST})
    public BaseOutput getModifyEmployeePerformanceCommissionRestriction() {
        return sealingAccountService.getModifyEmployeePerformanceCommissionRestriction();
    }

    @RequestMapping(value = "/sealingAccount/getModifyIncomeAndSpendingRestriction", method = {RequestMethod.POST})
    public BaseOutput getModifyIncomeAndSpendingRestriction() {
        return sealingAccountService.getModifyIncomeAndSpendingRestriction();
    }
}