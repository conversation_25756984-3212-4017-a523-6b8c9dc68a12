package com.zhelian.api.ibeauty.savingCard;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SavingCardCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.SavingCardCategoryMoveForm;
import com.zhelian.model.ibeauty.form.input.SavingCardCategoryQueryForm;
import com.zhelian.model.ibeauty.form.input.SavingCardCategoryUpdateForm;
import com.zhelian.service.ibeauty.savingCard.SavingCardCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description:
 * @Param:
 * @returns:
 * @Author: xuehao
 * @Date: 2020/3/19 10:43
 */
@RestController
@RequestMapping(value = "/api")
public class SavingCardCategoryController {

    @Autowired
    SavingCardCategoryService savingCardCategoryService;

    @RequestMapping(value = "/savingCardCategory/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody SavingCardCategoryQueryForm form) {
        return savingCardCategoryService.savingCardCategoryList(form);
    }

    @RequestMapping(value = "/savingCardCategory/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody SavingCardCategoryAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("储值卡分类名称不能为空");

        return savingCardCategoryService.createSavingCardCategory(form);
    }

    @RequestMapping(value = "/savingCardCategory/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody SavingCardCategoryUpdateForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("储值卡分类名称不能为空");
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (form.Active == null)
            return BaseOutput.failed("有效性不能为空");

        return savingCardCategoryService.updateSavingCardCategory(form);
    }

    @RequestMapping(value = "/savingCardCategory/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody SavingCardCategoryMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");

        return savingCardCategoryService.move(form);
    }
}