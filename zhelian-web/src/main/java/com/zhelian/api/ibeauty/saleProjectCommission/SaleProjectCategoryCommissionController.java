package com.zhelian.api.ibeauty.saleProjectCommission;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectChildCategoryCommissionAddForm;
import com.zhelian.service.ibeauty.saleProjectCommission.SaleProjectCategoryCommissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * @Description:
 * @Param:
 * @returns:
 * @Author: 86158
 * @Date: 2020/4/2 13:26
 */
@RestController
@RequestMapping(value = "/api")
public class SaleProjectCategoryCommissionController {

    @Autowired
    SaleProjectCategoryCommissionService saleProjectCategoryCommissionService;


    @RequestMapping(value = "/saleProjectCategoryCommission/all", method = {RequestMethod.POST})
    public BaseOutput allSaleProjectCategoryCommission(@RequestBody SaleProjectCategoryCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return saleProjectCategoryCommissionService.allSaleProjectCategoryCommission(form);
    }

    @RequestMapping(value = "/saleProjectCategoryCommission/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleProjectCategoryCommission(@RequestBody SaleProjectCategoryAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return saleProjectCategoryCommissionService.updateSaleProjectCategoryCommission(form);
    }

    static boolean checkRate(SaleProjectCategoryAddForm form) {

        if (form.Category != null && form.Category.size() > 0) {
            for (SaleProjectCategoryCommissionAddForm saleProjectCategoryCommissionAddForm : form.Category) {

                if ((saleProjectCategoryCommissionAddForm.PayRate != null && saleProjectCategoryCommissionAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (saleProjectCategoryCommissionAddForm.SavingCardRate != null && saleProjectCategoryCommissionAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                        || (saleProjectCategoryCommissionAddForm.SavingCardLargessRate != null && saleProjectCategoryCommissionAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                    return false;
                if (saleProjectCategoryCommissionAddForm.Child != null && saleProjectCategoryCommissionAddForm.Child.size() > 0) {
                    for (SaleProjectChildCategoryCommissionAddForm saleProjectChildCategoryCommissionAddForm : saleProjectCategoryCommissionAddForm.Child) {

                        if ((saleProjectChildCategoryCommissionAddForm.PayRate != null && saleProjectChildCategoryCommissionAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                                || (saleProjectChildCategoryCommissionAddForm.SavingCardRate != null && saleProjectChildCategoryCommissionAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                                || (saleProjectChildCategoryCommissionAddForm.SavingCardLargessRate != null && saleProjectChildCategoryCommissionAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                            return false;

                    }
                }


            }
        }
        return true;
    }
}

