package com.zhelian.api.ibeauty.project;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.service.ibeauty.project.ProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


/**
 * @Description:
 * @Param:
 * @returns:
 * @Author: xuehao
 * @Date: 2020/3/21 14:31
 */
@RestController
@RequestMapping(value = "/api")
public class ProjectController {

    @Autowired
    ProjectService ProjectService;

    @RequestMapping(value = "/project/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody ProjectQueryForm form) {
        return ProjectService.projectList(form);
    }

    @RequestMapping(value = "/project/getProjectDetails", method = {RequestMethod.POST})
    public BaseOutput getProjectDetails(@RequestBody ProjectSaleEntityQueryForm form) {
        return ProjectService.getProjectDetails(form);
    }

    @RequestMapping(value = "/project/projectSaleEntity", method = {RequestMethod.POST})
    public BaseOutput projectSaleEntity(@RequestBody ProjectSaleEntityQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        return ProjectService.projectSaleEntity(form);
    }

    @RequestMapping(value = "/project/projectConsumeEntity", method = {RequestMethod.POST})
    public BaseOutput projectConsumeEntity(@RequestBody ProjectConsumeEntityQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        return ProjectService.projectConsumeEntity(form);
    }

    @RequestMapping(value = "/project/projectModifyPriceEntity", method = {RequestMethod.POST})
    public BaseOutput projectModifyPriceEntity(@RequestBody ProjectConsumeEntityQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        return ProjectService.projectModifyPriceEntity(form);
    }

    @RequestMapping(value = "/project/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody ProjectAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("项目名称不能为空");
        if (form.Price == null || form.Price.compareTo(BigDecimal.ZERO) == 0)
            return BaseOutput.failed("项目单价不能为null或0");
        if (form.ProjectCategoryID == null)
            return BaseOutput.failed("项目分类ID不能为空");
        if (form.TreatTime == null)
            return BaseOutput.failed("项目耗时不能为空");
        if (form.IsAllowSell == null)
            return BaseOutput.failed("是否上架不能为空");
        if (form.IsAllowLargess == null)
            return BaseOutput.failed("是否允许赠送不能为空");
        if (form.IsAllowConsume == null)
            return BaseOutput.failed("是否允许消耗不能为空");
        return ProjectService.createProject(form);
    }

    @RequestMapping(value = "/project/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody ProjectUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("项目名称不能为空");
        if (form.Price == null || form.Price.compareTo(BigDecimal.ZERO) == 0)
            return BaseOutput.failed("项目单价不能为null或0");
        if (form.ProjectCategoryID == null)
            return BaseOutput.failed("项目分类ID不能为空");
        if (form.TreatTime == null)
            return BaseOutput.failed("项目耗时不能为空");
        if (form.IsAllowSell == null)
            return BaseOutput.failed("是否上架不能为空");
        if (form.IsAllowLargess == null)
            return BaseOutput.failed("是否允许赠送不能为空");
        if (form.IsAllowConsume == null)
            return BaseOutput.failed("是否允许消耗不能为空");
        return ProjectService.updateProject(form);
    }

    @RequestMapping(value = "/project/all", method = {RequestMethod.POST})
    public BaseOutput all() {
        return ProjectService.allProject();
    }

    @RequestMapping(value = "/project/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody GoodsMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");

        return ProjectService.move(form);
    }

    @RequestMapping(value = "/project/bitchUpdateProjectSaleEntity", method = {RequestMethod.POST})
    public BaseOutput bitchUpdateProductSaleEntity(@RequestBody GoodsBitchUpdateForm form) {
        if (form.GoodsID.length == 0)
            return BaseOutput.failed("请选择项目");

        return ProjectService.bitchUpdateProjectSaleEntity(form);
    }

    @RequestMapping(value = "/project/bitchUpdateProjectConsumeEntity", method = {RequestMethod.POST})
    public BaseOutput bitchUpdateProjectConsumeEntity(@RequestBody GoodsBitchUpdateForm form) {
        if (form.GoodsID.length == 0)
            return BaseOutput.failed("请选择项目");

        return ProjectService.bitchUpdateProjectConsumeEntity(form);
    }
}