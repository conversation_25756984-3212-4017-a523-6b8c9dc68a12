package com.zhelian.api.ibeauty.saleTimeCardCommission;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardsCommissionAddForm;
import com.zhelian.service.ibeauty.saleTimeCardCommission.SaleTimeCardCommissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * @Description:
 * @Param:
 * @returns:
 * @Author: 86158
 * @Date: 2020/4/2 13:26
 */
@RestController
@RequestMapping(value = "/api")
public class SaleTimeCardCommissionController {

    @Autowired
    SaleTimeCardCommissionService saleTimeCardCommissionService;


    @RequestMapping(value = "/saleTimeCardCommission/all", method = {RequestMethod.POST})
    public BaseOutput allSaleTimeCardCommission(@RequestBody SaleTimeCardCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return saleTimeCardCommissionService.allSaleTimeCardCommission(form);
    }

    @RequestMapping(value = "/saleTimeCardCommission/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleTimeCardCommission(@RequestBody SaleTimeCardsCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");

        return saleTimeCardCommissionService.updateSaleTimeCardCommission(form);
    }

    static boolean checkRate(SaleTimeCardsCommissionAddForm form) {

        for (SaleTimeCardCommissionAddForm timeCardCommissionAddForm : form.TimeCard) {

            if ((timeCardCommissionAddForm.PayRate != null && timeCardCommissionAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (timeCardCommissionAddForm.SavingCardRate != null && timeCardCommissionAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                    || (timeCardCommissionAddForm.SavingCardLargessRate != null && timeCardCommissionAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                return false;
        }
        return true;
    }

}

