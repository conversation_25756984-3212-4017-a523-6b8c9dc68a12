package com.zhelian.api.ibeauty.treatBill;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.SaleAllHandlerForm;
import com.zhelian.model.ibeauty.form.input.TreatAllHandlerForm;
import com.zhelian.service.ibeauty.treatBill.TreatHandlerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @program: zhelian
 * @description: 消耗经手人
 * @author: wjz
 * @create: 2020-07-07 11:10
 **/
@RestController
@RequestMapping(value = "/api/treatHandler")
public class TreatHandlerController {
    @Autowired
    TreatHandlerService treatHandlerService;

    /**
     * @Description: 获取项目消耗经手人
     * @Param: []
     * @return: com.zhelian.model.base.BaseOutput
     * @Author: wjz
     * @Date: 2020/7/7
     */
    @RequestMapping(value = "/projectHandler", method = {RequestMethod.POST})
    public BaseOutput projectHandler() {
        return treatHandlerService.projectHandler();
    }

    /**
     * @Description: 获取产品消耗经手人
     * @Param: []
     * @return: com.zhelian.model.base.BaseOutput
     * @Author: wjz
     * @Date: 2020/7/7
     */
    @RequestMapping(value = "/productHandler", method = {RequestMethod.POST})
    public BaseOutput productHandler() {
        return treatHandlerService.productHandler();
    }

    /**
     * @Description: 获取储值卡消耗经手人
     * @Param: []
     * @return: com.zhelian.model.base.BaseOutput
     * @Author: wjz
     * @Date: 2020/7/7
     */
    @RequestMapping(value = "/savingCardHandler", method = {RequestMethod.POST})
    public BaseOutput savingCardHandler() {
        return treatHandlerService.savingCardHandler();
    }

    @RequestMapping(value = "/allHandler", method = {RequestMethod.POST})
    public BaseOutput allHandler(@RequestBody TreatAllHandlerForm form) {
        if (form.GoodTypes == null || form.GoodTypes.size() <= 0){
            BaseOutputForm.failed("缺少商品类型");
        }
        return treatHandlerService.allHandler(form.GoodTypes);
    }
}
