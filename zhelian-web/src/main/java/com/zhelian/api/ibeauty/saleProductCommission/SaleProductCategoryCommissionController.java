package com.zhelian.api.ibeauty.saleProductCommission;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionSchemeAddForm;
import com.zhelian.service.ibeauty.saleProductCommission.SaleProductCategoryCommissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * @Description:
 * @Param:
 * @returns:
 * @Author: 86158
 * @Date: 2020/4/2 13:26
 */
@RestController
@RequestMapping(value = "/api")
public class SaleProductCategoryCommissionController {

    @Autowired
    SaleProductCategoryCommissionService saleProductCategoryCommissionService;


    @RequestMapping(value = "/saleProductCategoryCommission/all", method = {RequestMethod.POST})
    public BaseOutput allSaleProductCategoryCommission(@RequestBody SaleProductCommissionSchemeAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return saleProductCategoryCommissionService.allSaleProductCategoryCommission(form);
    }

    @RequestMapping(value = "/saleProductCategoryCommission/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleProductCategoryCommission(@RequestBody SaleProductCategoryAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return saleProductCategoryCommissionService.updateSaleProductCategoryCommission(form);
    }

    static boolean checkRate(SaleProductCategoryAddForm form) {

        for (SaleProductCategoryCommissionAddForm saleProductCategoryCommissionAddForm : form.Category) {

            if ((saleProductCategoryCommissionAddForm.PayRate != null && saleProductCategoryCommissionAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (saleProductCategoryCommissionAddForm.SavingCardRate != null && saleProductCategoryCommissionAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                    || (saleProductCategoryCommissionAddForm.SavingCardLargessRate != null && saleProductCategoryCommissionAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                return false;
        }
        return true;
    }
}

