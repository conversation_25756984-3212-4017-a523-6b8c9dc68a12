package com.zhelian.api.ibeauty.appointment;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.service.ibeauty.appointment.AppointmentBillCommonService;
import com.zhelian.service.ibeauty.appointment.AppointmentBillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


@RestController
@RequestMapping(value = "/api/appointmentBill")
public class AppointmentBillController {

    @Autowired
    AppointmentBillService appointmentBillService;
    @Autowired
    AppointmentBillCommonService appointmentBillCommonService;

    @RequestMapping(value = "/servicer", method = {RequestMethod.POST})
    public BaseOutput servicer() {

        return appointmentBillService.getAppointmentBillServicer();
    }

    @RequestMapping(value = "/servicerEmployee", method = {RequestMethod.POST})
    public BaseOutput servicerEmployee(@RequestBody AppointmentBillEmpQueryForm form) {
        if (form.AppointmentDate == null)
            return BaseOutput.failed("预约日期不能为空");
        return appointmentBillService.getAppointmentBillServicerEmployee(form);
    }

    @RequestMapping(value = "/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody AppointmentBillEmpQueryForm form) {
        if (form.AppointmentDate == null)
            return BaseOutput.failed("预约日期不能为空");
        if (form.ServicerID == null)
            return BaseOutput.failed("预约角色编号不能为空");
        return appointmentBillService.getAppointmentBillEmpAndCust(form);
    }

    @RequestMapping(value = "/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody AppointmentBillAddForm form) throws Exception {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        if (form.AppointmentDate == null)
            return BaseOutput.failed("预约日期不能为空");
        if (form.Period == null)
            return BaseOutput.failed("时长不能为空");
        if (!checkProject(form.Project)) {
            return BaseOutput.failed("项目ID不能为空");
        }

        return appointmentBillCommonService.create(form);
    }

    static boolean checkProject(List<AppointmentBillProjectAddForm> list) {
        for (AppointmentBillProjectAddForm appointmentBillProjectAddForm : list) {
            if (appointmentBillProjectAddForm.ProjectID == null)
                return false;
        }
        return true;
    }

    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody AppointmentBillUpdateForm form) throws Exception {
        if (form.ID == null)
            return BaseOutput.failed("预约单不能为空");
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        if (form.AppointmentDate == null)
            return BaseOutput.failed("预约日期不能为空");
        if (form.Status == null)
            return BaseOutput.failed("状态不能为空");
        if (form.Period == null)
            return BaseOutput.failed("时长不能为空");
        if (!checkProject(form.Project)) {
            return BaseOutput.failed("项目ID不能为空");
        }
        return appointmentBillCommonService.update(form);
    }

    @RequestMapping(value = "/updateStatus", method = {RequestMethod.POST})
    public BaseOutput updateStatus(@RequestBody AppointmentBillUpdateStatusForm form) {
        if (form.ID == null)
            return BaseOutput.failed("预约单不能为空");
        if (form.Status == null)
            return BaseOutput.failed("状态不能为空");

        return appointmentBillCommonService.updateStatus(form);
    }

    @RequestMapping(value = "/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody AppointmentBillQueryForm form) {
        return appointmentBillService.appointmentBillList(form);
    }

    @RequestMapping(value = "/info", method = {RequestMethod.POST})
    public BaseOutput info(@RequestBody AppointmentBillInfoQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("预约单不能为空");
        return appointmentBillService.appointmentBillInfo(form);
    }

    @RequestMapping(value = "/detail", method = {RequestMethod.POST})
    public BaseOutput detail(@RequestBody AppointmentBillInfoQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("预约ID不能为空");
        return appointmentBillService.appointmentBillInfo(form);
    }

    @RequestMapping(value = "/cancel", method = {RequestMethod.POST})
    public BaseOutput cancel(@RequestBody AppointmentBillUpdateStatusForm form) {
        if (form.ID == null)
            return BaseOutput.failed("预约ID不能为空");
        if (form.Status == null || !form.Status.equals("30"))
            return BaseOutput.failed("取消状态参数错误");
        return appointmentBillService.updateStatus(form);
    }

    @RequestMapping(value = "/excel", method = {RequestMethod.POST})
    public BaseOutput excel(@RequestBody AppointmentBillQueryForm form, HttpServletResponse response) {
        return appointmentBillService.excel(form, response);
    }

    @RequestMapping(value = "/process", method = {RequestMethod.POST})
    public BaseOutput process(@RequestBody AppointmentBillQueryForm form) {
        if (form.AppointmentBillID == null)
            return BaseOutput.failed("预约单不能为空");
        return appointmentBillService.process(form);
    }

    @RequestMapping(value = "/getCustomerAppointmentAll", method = {RequestMethod.POST})
    public BaseOutput getCustomerAppointmentAll(@RequestBody AppointmentCustomerForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("客户编号不能为空");
        if (form.AppointmentDate == null)
            return BaseOutput.failed("预约时间不能为空");
        return appointmentBillService.getCustomerAppointmentAll(form);
    }

    @RequestMapping(value = "/getCustomerAppointmentNumber", method = {RequestMethod.POST})
    public BaseOutput getCustomerAppointmentNumber(@RequestBody AppointmentCustomerNumberForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("客户编号不能为空");
        if (form.AppointmentDate == null)
            return BaseOutput.failed("预约时间不能为空");
        return appointmentBillService.getCustomerAppointmentNumber(form);
    }
}