package com.zhelian.api.ibeauty.saleHandler;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerMoveForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerUpdateForm;
import com.zhelian.service.ibeauty.saleHandler.SavingCardSaleHandlerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description:
 * @Param: No such property: code for class: Script1
 * @returns:
 * @Author: xuehao
 * @Date: 2020/3/31 10:11
 */
@RestController
@RequestMapping(value = "/api")
public class SavingCardSaleHandlerController {

    @Autowired
    SavingCardSaleHandlerService savingCardSaleHandlerService;

    @RequestMapping(value = "/savingCardSaleHandler/all", method = {RequestMethod.POST})
    public BaseOutput allSavingCardSaleHandler(@RequestBody SaleHandlerQueryForm form) {
        return savingCardSaleHandlerService.allSavingCardSaleHandler(form);
    }

    @RequestMapping(value = "/savingCardSaleHandler/create", method = {RequestMethod.POST})
    public BaseOutput createSavingCardSaleHandler(@RequestBody SaleHandlerAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("经手人名称不能为空");
        if (form.EntityID == null)
            return BaseOutput.failed("门店ID不能为空");
        if (form.JobType == null || form.JobType.length == 0)
            return BaseOutput.failed("适用职务不能为空");


        return savingCardSaleHandlerService.createSavingCardSaleHandler(form);
    }

    @RequestMapping(value = "/savingCardSaleHandler/update", method = {RequestMethod.POST})
    public BaseOutput updateSavingCardSaleHandler(@RequestBody SaleHandlerUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("经手人名称不能为空");
        if (form.EntityID == null)
            return BaseOutput.failed("门店ID不能为空");
        if (form.Active == null)
            return BaseOutput.failed("是否有效不能为空");
        if (form.JobType == null || form.JobType.length == 0)
            return BaseOutput.failed("适用职务不能为空");


        return savingCardSaleHandlerService.updateSavingCardSaleHandler(form);
    }

    @RequestMapping(value = "/savingCardSaleHandler/move", method = {RequestMethod.POST})
    public BaseOutput moveSavingCardSaleHandler(@RequestBody SaleHandlerMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");
        return savingCardSaleHandlerService.moveSavingCardSaleHandler(form);
    }
}