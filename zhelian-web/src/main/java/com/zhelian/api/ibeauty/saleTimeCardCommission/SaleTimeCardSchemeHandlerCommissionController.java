package com.zhelian.api.ibeauty.saleTimeCardCommission;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardSchemeHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardSchemeHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardSchemeHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardsSchemeHandlerCommissionAddForm;
import com.zhelian.service.ibeauty.saleTimeCardCommission.SaleTimeCardSchemeHandlerCommissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * @Description:
 * @Param:
 * @returns:
 * @Author: 86158
 * @Date: 2020/4/2 13:26
 */
@RestController
@RequestMapping(value = "/api")
public class SaleTimeCardSchemeHandlerCommissionController {

    @Autowired
    SaleTimeCardSchemeHandlerCommissionService saleTimeCardSchemeHandlerCommissionService;


    @RequestMapping(value = "/saleTimeCardSchemeHandlerCommission/all", method = {RequestMethod.POST})
    public BaseOutput allSaleProSchemeHandlerCommission(@RequestBody SaleTimeCardSchemeHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");

        return saleTimeCardSchemeHandlerCommissionService.allSaleTimeCardSchemeHandlerCommission(form);
    }

    @RequestMapping(value = "/saleTimeCardSchemeHandlerCommission/packageCard", method = {RequestMethod.POST})
    public BaseOutput allSalePackageCardTimeCardSchemeHandlerCommission(@RequestBody SaleTimeCardSchemeHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");

        return saleTimeCardSchemeHandlerCommissionService.allSalePackageCardTimeCardSchemeHandlerCommission(form);
    }

    @RequestMapping(value = "/saleTimeCardSchemeHandlerCommission/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleTimeCardSchemeHandlerCommission(@RequestBody SaleTimeCardsSchemeHandlerCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return saleTimeCardSchemeHandlerCommissionService.updateSaleTimeCardSchemeHandlerCommission(form);
    }

    static boolean checkRate(SaleTimeCardsSchemeHandlerCommissionAddForm form) {
        if (form.TimeCardHandler != null) {
            for (SaleTimeCardSchemeHandlerCommissionAddForm handlerCommissionAddForm : form.TimeCardHandler) {

                if ((handlerCommissionAddForm.PayRate != null && handlerCommissionAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerCommissionAddForm.SavingCardRate != null && handlerCommissionAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerCommissionAddForm.SavingCardLargessRate != null && handlerCommissionAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                    return false;

                for (SaleTimeCardSchemeHandlerJobTypeCommissionAddForm jobTypeCommissionAddForm : handlerCommissionAddForm.HandlerJobTypeCommission) {


                    if ((jobTypeCommissionAddForm.PayRate != null && jobTypeCommissionAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypeCommissionAddForm.SavingCardRate != null && jobTypeCommissionAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypeCommissionAddForm.SavingCardLargessRate != null && jobTypeCommissionAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                        return false;
                }

            }
        }
        return true;
    }
}

