package com.zhelian.api.ibeauty.customer;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.AccountQueryForm;
import com.zhelian.service.ibeauty.customer.CustomerProjectAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api/customerProjectAccount")
public class CustomerProjectAccountController {

    @Autowired
    CustomerProjectAccountService customerProjectAccountService;

    @RequestMapping(value = "/treatBill", method = {RequestMethod.POST})
    public BaseOutput treatBill(@RequestBody AccountQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("账户编号不能为空");
        return customerProjectAccountService.treatBill(form);
    }

    @RequestMapping(value = "/refundBill", method = {RequestMethod.POST})
    public BaseOutput refundBill(@RequestBody AccountQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("账户编号不能为空");
        return customerProjectAccountService.refundBill(form);
    }

    @RequestMapping(value = "/refundTreatBill", method = {RequestMethod.POST})
    public BaseOutput refundTreatBill(@RequestBody AccountQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("账户编号不能为空");
        return customerProjectAccountService.refundTreatBill(form);
    }

    @RequestMapping(value = "/transferBill", method = {RequestMethod.POST})
    public BaseOutput transferBill(@RequestBody AccountQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("账户编号不能为空");
        return customerProjectAccountService.transferBill(form);
    }

    @RequestMapping(value = "/repayment", method = {RequestMethod.POST})
    public BaseOutput repayment(@RequestBody AccountQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("账户编号不能为空");
        return customerProjectAccountService.repayment(form);
    }

    @RequestMapping(value = "/arrearAmountProcess", method = {RequestMethod.POST})
    public BaseOutput arrearAmountProcess(@RequestBody AccountQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("账户编号不能为空");
        return customerProjectAccountService.arrearAmountProcess(form);
    }
}