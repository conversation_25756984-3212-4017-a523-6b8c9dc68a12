package com.zhelian.api.ibeauty.saleBill;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.service.ibeauty.saleGoods.SaleGoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class SaleGoodsController {

    @Autowired
    SaleGoodsService saleGoodsService;

    @RequestMapping(value = "/saleGoods/all", method = {RequestMethod.POST})
    public BaseOutput allSaleGoods(@RequestBody SaleGoodsQueryForm form) {
        if (form.Name == null) {
            return BaseOutput.failed("卡项名称搜索不能为空");
        } else {
            form.Name = form.Name.trim();
        }
        if (form.BillDate == null)
            return BaseOutput.failed("开单时间不能为空");
        return saleGoodsService.allSaleGoods(form);
    }

    @RequestMapping(value = "/saleGoods/projectCustomerDiscount", method = {RequestMethod.POST})
    public BaseOutput projectCustomerDiscount(@RequestBody SaleProjectCustomerDiscountQueryForm form) {
        if (form.ProjectID == null) {
            return BaseOutput.failed("项目编号不能为空");
        }
        if (form.CustomerID == null) {
            return BaseOutput.failed("顾客编号不能为空");
        }
        return saleGoodsService.projectCustomerDiscount(form);
    }

    @RequestMapping(value = "/saleGoods/project", method = {RequestMethod.POST})
    public BaseOutput allSaleGoodsProject(@RequestBody SaleGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.allSaleGoodsProject(form);
    }

    @RequestMapping(value = "/saleGoods/projectCategory", method = {RequestMethod.POST})
    public BaseOutput allSaleGoodsProjectCategory(@RequestBody SaleGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.allSaleGoodsProjectCategory(form);
    }

    @RequestMapping(value = "/saleGoods/projectByCategory", method = {RequestMethod.POST})
    public BaseOutput projectByCategory(@RequestBody SaleGoodsQueryForm form) {
        if (form.CategoryID == null) {
            return BaseOutput.failed("分类编号不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.projectByCategory(form);
    }

    @RequestMapping(value = "/saleGoods/productCustomerDiscount", method = {RequestMethod.POST})
    public BaseOutput productCustomerDiscount(@RequestBody SaleProductCustomerDiscountQueryForm form) {
        if (form.ProductID == null) {
            return BaseOutput.failed("产品编号不能为空");
        }
        if (form.CustomerID == null) {
            return BaseOutput.failed("顾客编号不能为空");
        }
        return saleGoodsService.productCustomerDiscount(form);
    }

    @RequestMapping(value = "/saleGoods/product", method = {RequestMethod.POST})
    public BaseOutput saleGoodsProduct(@RequestBody SaleGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.saleGoodsProduct(form);
    }

    @RequestMapping(value = "/saleGoods/productCategory", method = {RequestMethod.POST})
    public BaseOutput saleGoodsProductCategory(@RequestBody SaleGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.saleGoodsProductCategory(form);
    }

    @RequestMapping(value = "/saleGoods/productByCategory", method = {RequestMethod.POST})
    public BaseOutput saleGoodsProductByCategory(@RequestBody SaleGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.saleGoodsProductByCategory(form);
    }

    @RequestMapping(value = "/saleGoods/generalCardCustomerDiscount", method = {RequestMethod.POST})
    public BaseOutput generalCardCustomerDiscount(@RequestBody SaleGeneralCardCustomerDiscountQueryForm form) {
        if (form.GeneralCardID == null) {
            return BaseOutput.failed("通用次卡编号不能为空");
        }
        if (form.CustomerID == null) {
            return BaseOutput.failed("顾客编号不能为空");
        }
        return saleGoodsService.generalCardCustomerDiscount(form);
    }

    @RequestMapping(value = "/saleGoods/generalCard", method = {RequestMethod.POST})
    public BaseOutput saleGoodsGeneralCard(@RequestBody SaleGoodsQueryForm form) {
        if (form.BillDate == null) {
            return BaseOutput.failed("开单时间不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.saleGoodsGeneralCard(form);
    }

    @RequestMapping(value = "/saleGoods/generalCardCategory", method = {RequestMethod.POST})
    public BaseOutput saleGoodsGeneralCardCategory(@RequestBody SaleGoodsQueryForm form) {
        if (form.BillDate == null) {
            return BaseOutput.failed("开单时间不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.saleGoodsGeneralCardCategory(form);
    }

    @RequestMapping(value = "/saleGoods/generalCardByCategory", method = {RequestMethod.POST})
    public BaseOutput saleGoodsGeneralCardByCategory(@RequestBody SaleGoodsQueryForm form) {
        if (form.BillDate == null) {
            return BaseOutput.failed("开单时间不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.saleGoodsGeneralCardByCategory(form);
    }

    @RequestMapping(value = "/saleGoods/timeCardCustomerDiscount", method = {RequestMethod.POST})
    public BaseOutput timeCardCustomerDiscount(@RequestBody SaleTimeCardCustomerDiscountQueryForm form) {
        if (form.TimeCardID == null) {
            return BaseOutput.failed("时效卡编号不能为空");
        }
        if (form.CustomerID == null) {
            return BaseOutput.failed("顾客编号不能为空");
        }
        return saleGoodsService.timeCardCustomerDiscount(form);
    }

    @RequestMapping(value = "/saleGoods/timeCard", method = {RequestMethod.POST})
    public BaseOutput saleGoodsTimeCard(@RequestBody SaleGoodsQueryForm form) {
        if (form.BillDate == null) {
            return BaseOutput.failed("开单时间不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.saleGoodsTimeCard(form);
    }

    @RequestMapping(value = "/saleGoods/timeCardCategory", method = {RequestMethod.POST})
    public BaseOutput allSaleGoodsTimeCardCategory(@RequestBody SaleGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.allSaleGoodsTimeCardCategory(form);
    }

    @RequestMapping(value = "/saleGoods/timeCardByCategory", method = {RequestMethod.POST})
    public BaseOutput timeCardByCategory(@RequestBody SaleGoodsQueryForm form) {
        if (form.CategoryID == null) {
            return BaseOutput.failed("分类编号不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.timeCardByCategory(form);
    }

    @RequestMapping(value = "/saleGoods/savingCard", method = {RequestMethod.POST})
    public BaseOutput saleGoodsSavingCard(@RequestBody SaleGoodsQueryForm form) {
        if (form.BillDate == null) {
            return BaseOutput.failed("开单时间不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.saleGoodsSavingCard(form);
    }

    @RequestMapping(value = "/saleGoods/savingCardCategory", method = {RequestMethod.POST})
    public BaseOutput allSaleGoodsSavingCardCategory(@RequestBody SaleGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.allSaleGoodsSavingCardCategory(form);
    }

    @RequestMapping(value = "/saleGoods/savingCardByCategory", method = {RequestMethod.POST})
    public BaseOutput savingCardByCategory(@RequestBody SaleGoodsQueryForm form) {
        if (form.CategoryID == null) {
            return BaseOutput.failed("分类编号不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.savingCardByCategory(form);
    }

    @RequestMapping(value = "/saleGoods/packageCardCustomerDiscount", method = {RequestMethod.POST})
    public BaseOutput packageCardCustomerDiscount(@RequestBody SalePackageCardCustomerDiscountQueryForm form) {
        if (form.PackageCardID == null) {
            return BaseOutput.failed("套餐卡编号不能为空");
        }
        if (form.CustomerID == null) {
            return BaseOutput.failed("顾客编号不能为空");
        }
        return saleGoodsService.packageCardCustomerDiscount(form);
    }

    @RequestMapping(value = "/saleGoods/packageCard", method = {RequestMethod.POST})
    public BaseOutput saleGoodsPackageCard(@RequestBody SaleGoodsQueryForm form) {
        if (form.BillDate == null) {
            return BaseOutput.failed("开单时间不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.saleGoodsPackageCard(form);
    }

    @RequestMapping(value = "/saleGoods/packageCardCategory", method = {RequestMethod.POST})
    public BaseOutput saleGoodsPackageCardCategory(@RequestBody SaleGoodsQueryForm form) {
        if (form.BillDate == null) {
            return BaseOutput.failed("开单时间不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.saleGoodsPackageCardCategory(form);
    }

    @RequestMapping(value = "/saleGoods/packageCardByCategory", method = {RequestMethod.POST})
    public BaseOutput saleGoodsPackageCardByCategory(@RequestBody SaleGoodsQueryForm form) {
        if (form.BillDate == null) {
            return BaseOutput.failed("开单时间不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.saleGoodsPackageCardByCategory(form);
    }

    @RequestMapping(value = "/saleGoods/goodsType", method = {RequestMethod.POST})
    public BaseOutput goodsType(@RequestBody SaleGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }

        return saleGoodsService.goodsType(form);
    }

    @RequestMapping(value = "/saleGoods/goods", method = {RequestMethod.POST})
    public BaseOutput goods(@RequestBody SaleGoodsQueryForm form) {
        if (form.GoodsType == null) {
            return BaseOutput.failed("商品类型不能为空");
        }
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.goods(form);
    }

    @RequestMapping(value = "/saleGoods/generalCardProjectCategory", method = {RequestMethod.POST})
    public BaseOutput generalCardProjectCategory(@RequestBody SalePreviewGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.generalCardProjectCategory(form);
    }

    @RequestMapping(value = "/saleGoods/generalCardProjectByCategory", method = {RequestMethod.POST})
    public BaseOutput generalCardProjectByCategory(@RequestBody SalePreviewGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.generalCardProjectByCategory(form);
    }

    @RequestMapping(value = "/saleGoods/timeCardProjectCategory", method = {RequestMethod.POST})
    public BaseOutput timeCardProjectCategory(@RequestBody SalePreviewGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.timeCardProjectCategory(form);
    }

    @RequestMapping(value = "/saleGoods/timeCardProjectByCategory", method = {RequestMethod.POST})
    public BaseOutput timeCardProjectByCategory(@RequestBody SalePreviewGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.timeCardProjectByCategory(form);
    }

    @RequestMapping(value = "/saleGoods/savingCardProjectCategory", method = {RequestMethod.POST})
    public BaseOutput savingCardProjectCategory(@RequestBody SalePreviewGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.savingCardProjectCategory(form);
    }

    @RequestMapping(value = "/saleGoods/savingCardProjectByCategory", method = {RequestMethod.POST})
    public BaseOutput savingCardProjectByCategory(@RequestBody SalePreviewGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.savingCardProjectByCategory(form);
    }

    @RequestMapping(value = "/saleGoods/savingCardLargessProjectCategory", method = {RequestMethod.POST})
    public BaseOutput savingCardLargessProjectCategory(@RequestBody SalePreviewGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.savingCardLargessProjectCategory(form);
    }

    @RequestMapping(value = "/saleGoods/savingCardLargessProjectByCategory", method = {RequestMethod.POST})
    public BaseOutput savingCardLargessProjectByCategory(@RequestBody SalePreviewGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.savingCardLargessProjectByCategory(form);
    }

    @RequestMapping(value = "/saleGoods/packageCardGoodsCategory", method = {RequestMethod.POST})
    public BaseOutput packageCardGoodsCategory(@RequestBody SalePreviewGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.packageCardGoodsByCategory(form);
    }

    @RequestMapping(value = "/saleGoods/packageCardProductByCategory", method = {RequestMethod.POST})
    public BaseOutput packageCardProductByCategory(@RequestBody SalePreviewGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.packageCardProductByCategory(form);
    }

    @RequestMapping(value = "/saleGoods/packageCardProjectByCategory", method = {RequestMethod.POST})
    public BaseOutput packageCardProjectByCategory(@RequestBody SalePreviewGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.packageCardProjectByCategory(form);
    }

    @RequestMapping(value = "/saleGoods/packageCardGeneralCardByCategory", method = {RequestMethod.POST})
    public BaseOutput packageCardGeneralCardCardByCategory(@RequestBody SalePreviewGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.packageCardGeneralCardByCategory(form);
    }

    @RequestMapping(value = "/saleGoods/packageCardTimeCardByCategory", method = {RequestMethod.POST})
    public BaseOutput packageCardTimeCardByCategory(@RequestBody SalePreviewGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.packageCardTimeCardByCategory(form);
    }

    @RequestMapping(value = "/saleGoods/packageCardSavingCardByCategory", method = {RequestMethod.POST})
    public BaseOutput packageCardSavingCardByCategory(@RequestBody SalePreviewGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.packageCardSavingCardByCategory(form);
    }


    @RequestMapping(value = "/saleGoods/packageCardLargessGoodsCategory", method = {RequestMethod.POST})
    public BaseOutput packageCardLargessGoodsCategory(@RequestBody SalePreviewGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.packageCardLargessGoodsByCategory(form);
    }

    @RequestMapping(value = "/saleGoods/packageCardLargessProductByCategory", method = {RequestMethod.POST})
    public BaseOutput packageCardLargessProductByCategory(@RequestBody SalePreviewGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.packageCardLargessProductByCategory(form);
    }

    @RequestMapping(value = "/saleGoods/packageCardLargessProjectByCategory", method = {RequestMethod.POST})
    public BaseOutput packageCardLargessProjectByCategory(@RequestBody SalePreviewGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.packageCardLargessProjectByCategory(form);
    }

    @RequestMapping(value = "/saleGoods/packageCardLargessGeneralCardByCategory", method = {RequestMethod.POST})
    public BaseOutput packageCardLargessGeneralCardCardByCategory(@RequestBody SalePreviewGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.packageCardLargessGeneralCardByCategory(form);
    }

    @RequestMapping(value = "/saleGoods/packageCardLargessTimeCardByCategory", method = {RequestMethod.POST})
    public BaseOutput packageCardLargessTimeCardByCategory(@RequestBody SalePreviewGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.packageCardLargessTimeCardByCategory(form);
    }

    @RequestMapping(value = "/saleGoods/packageCardLargessSavingCardByCategory", method = {RequestMethod.POST})
    public BaseOutput packageCardLargessSavingCardByCategory(@RequestBody SalePreviewGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.packageCardLargessSavingCardByCategory(form);
    }
}