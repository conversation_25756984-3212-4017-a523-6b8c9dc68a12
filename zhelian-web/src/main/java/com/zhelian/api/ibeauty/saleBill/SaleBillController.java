package com.zhelian.api.ibeauty.saleBill;


import com.zhelian.core.utils.enumeration.BillChannel;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.miniprogram.form.input.SaleBillSignatureAddForm;
import com.zhelian.service.ibeauty.saleBill.SaleBillCommonService;
import com.zhelian.service.ibeauty.saleBill.SaleBillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class SaleBillController {

    @Autowired
    SaleBillService saleBillService;
    @Autowired
    SaleBillCommonService saleBillCommonService;

    @RequestMapping(value = "/saleBill/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody SaleBillAddForm form) throws Exception {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");

        if (form.Amount.compareTo(form.PayAmount.add(form.CardDeductionAmount).add(form.ArrearAmount)) != 0) {
            return BaseOutput.failed("订单金额不正确");
        }

        BigDecimal PayAmount = BigDecimal.ZERO;
        BigDecimal ArrearAmount = BigDecimal.ZERO;
        BigDecimal CardDeductionAmount = BigDecimal.ZERO;

        for (SaleBillProductForm productForm : form.Product) {
            if (!productForm.IsLargess) {
                if (productForm.TotalAmount.compareTo(productForm.PayAmount.add(productForm.ArrearAmount).add(productForm.SavingCardDeductionAmount)) != 0) {
                    return BaseOutput.failed("产品金额不正确");
                }
                PayAmount = PayAmount.add(productForm.PayAmount);
                ArrearAmount = ArrearAmount.add(productForm.ArrearAmount);
                CardDeductionAmount = CardDeductionAmount.add(productForm.SavingCardDeductionAmount);
            }
        }
        for (SaleBillProjectForm projectForm : form.Project) {
            if (!projectForm.IsLargess) {
                if (projectForm.TotalAmount.compareTo(projectForm.PayAmount.add(projectForm.ArrearAmount).add(projectForm.SavingCardDeductionAmount)) != 0) {
                    return BaseOutput.failed("项目金额不正确");
                }
                PayAmount = PayAmount.add(projectForm.PayAmount);
                ArrearAmount = ArrearAmount.add(projectForm.ArrearAmount);
                CardDeductionAmount = CardDeductionAmount.add(projectForm.SavingCardDeductionAmount);
            }
        }
        for (SaleBillGeneralCardForm generalCardForm : form.GeneralCard) {
            if (!generalCardForm.IsLargess) {
                if (generalCardForm.TotalAmount.compareTo(generalCardForm.PayAmount.add(generalCardForm.ArrearAmount).add(generalCardForm.SavingCardDeductionAmount)) != 0) {
                    return BaseOutput.failed("通用次卡金额不正确");
                }
                PayAmount = PayAmount.add(generalCardForm.PayAmount);
                ArrearAmount = ArrearAmount.add(generalCardForm.ArrearAmount);
                CardDeductionAmount = CardDeductionAmount.add(generalCardForm.SavingCardDeductionAmount);
            }
        }
        for (SaleBillTimeCardForm timeCardForm : form.TimeCard) {
            if (!timeCardForm.IsLargess) {
                if (timeCardForm.TotalAmount.compareTo(timeCardForm.PayAmount.add(timeCardForm.ArrearAmount).add(timeCardForm.SavingCardDeductionAmount)) != 0) {
                    return BaseOutput.failed("时效卡金额不正确");
                }
                PayAmount = PayAmount.add(timeCardForm.PayAmount);
                ArrearAmount = ArrearAmount.add(timeCardForm.ArrearAmount);
                CardDeductionAmount = CardDeductionAmount.add(timeCardForm.SavingCardDeductionAmount);
            }
        }
        for (SaleBillSavingCardForm savingCardForm : form.SavingCard) {
            if (savingCardForm.ArrearAmount == null) {
                savingCardForm.ArrearAmount = BigDecimal.ZERO;
            }
            if (savingCardForm.TotalAmount.compareTo(savingCardForm.PayAmount.add(savingCardForm.ArrearAmount)) != 0) {
                return BaseOutput.failed("储值卡金额不正确");
            }
            PayAmount = PayAmount.add(savingCardForm.PayAmount);
            ArrearAmount = ArrearAmount.add(savingCardForm.ArrearAmount);
        }
        for (SaleBillPackageCardForm packageCardForm : form.PackageCard) {
            if (!packageCardForm.IsLargess) {
                BigDecimal PackageCardTotalAmount = BigDecimal.ZERO;
                BigDecimal PackageCardPayAmount = BigDecimal.ZERO;
                BigDecimal PackageCardArrearAmount = BigDecimal.ZERO;
                if (packageCardForm.TotalAmount.compareTo(packageCardForm.PayAmount.add(packageCardForm.ArrearAmount).add(packageCardForm.SavingCardDeductionAmount)) != 0) {
                    return BaseOutput.failed("套餐卡金额不正确");
                }

                for (SaleBillPackageCardGoodsDetailForm productForm : packageCardForm.PackageCardGoods.Product) {
                    PackageCardTotalAmount = PackageCardTotalAmount.add(productForm.TotalAmount);
                    PackageCardPayAmount = PackageCardPayAmount.add(productForm.PayAmount);
                    PackageCardArrearAmount = PackageCardArrearAmount.add(productForm.ArrearAmount);
                }
                for (SaleBillPackageCardGoodsDetailForm projectForm : packageCardForm.PackageCardGoods.Project) {
                    PackageCardTotalAmount = PackageCardTotalAmount.add(projectForm.TotalAmount);
                    PackageCardPayAmount = PackageCardPayAmount.add(projectForm.PayAmount);
                    PackageCardArrearAmount = PackageCardArrearAmount.add(projectForm.ArrearAmount);
                }
                for (SaleBillPackageCardGoodsDetailForm generalCardForm : packageCardForm.PackageCardGoods.GeneralCard) {
                    PackageCardTotalAmount = PackageCardTotalAmount.add(generalCardForm.TotalAmount);
                    PackageCardPayAmount = PackageCardPayAmount.add(generalCardForm.PayAmount);
                    PackageCardArrearAmount = PackageCardArrearAmount.add(generalCardForm.ArrearAmount);
                }
                for (SaleBillPackageCardGoodsDetailForm timeCardForm : packageCardForm.PackageCardGoods.TimeCard) {
                    PackageCardTotalAmount = PackageCardTotalAmount.add(timeCardForm.TotalAmount);
                    PackageCardPayAmount = PackageCardPayAmount.add(timeCardForm.PayAmount);
                    PackageCardArrearAmount = PackageCardArrearAmount.add(timeCardForm.ArrearAmount);
                }
                for (SaleBillPackageCardGoodsDetailForm savingCardForm : packageCardForm.PackageCardGoods.SavingCard) {
                    PackageCardTotalAmount = PackageCardTotalAmount.add(savingCardForm.TotalAmount);
                    PackageCardPayAmount = PackageCardPayAmount.add(savingCardForm.TotalAmount.subtract(savingCardForm.ArrearAmount));
                    PackageCardArrearAmount = PackageCardArrearAmount.add(savingCardForm.ArrearAmount);
                }
                if (packageCardForm.TotalAmount.compareTo(PackageCardTotalAmount) != 0) {
                    return BaseOutput.failed("套餐卡金额和明细金额不匹配");
                }
                if (packageCardForm.ArrearAmount.compareTo(PackageCardArrearAmount) != 0) {
                    return BaseOutput.failed("套餐卡欠款金额和明细欠款金额不匹配");
                }
                if (packageCardForm.PayAmount.compareTo(PackageCardPayAmount) != 0) {
                    return BaseOutput.failed("套餐卡实付金额和明细实付金额不匹配");
                }

                PayAmount = PayAmount.add(packageCardForm.PayAmount);
                ArrearAmount = ArrearAmount.add(packageCardForm.ArrearAmount);
                CardDeductionAmount = CardDeductionAmount.add(packageCardForm.SavingCardDeductionAmount);
            }
        }
        //明细 PayAmount 包含储值卡抵扣金额，这里减去
        for (SaleBillSavingCardDeductionForm deductionForm : form.SavingCardDeduction) {
            PayAmount = PayAmount.subtract(deductionForm.DeductionAmount);
            CardDeductionAmount = CardDeductionAmount.add(deductionForm.DeductionAmount);
        }

        if (form.PayAmount.compareTo(PayAmount) != 0) {
            return BaseOutput.failed("订单实付金额不正确");
        }
        if (form.ArrearAmount.compareTo(ArrearAmount) != 0) {
            return BaseOutput.failed("订单欠款金额不正确");
        }
        if (form.CardDeductionAmount.compareTo(CardDeductionAmount) != 0) {
            return BaseOutput.failed("订单储值卡抵扣金额不正确");
        }

        return saleBillCommonService.create(form, false, BillChannel.BillChannelPC.getID());
    }

    @RequestMapping(value = "/saleBill/list", method = {RequestMethod.POST})
    public BaseOutput saleBillList(@RequestBody SaleBillQueryForm form) {
        return saleBillService.saleBillList(form);
    }

    @RequestMapping(value = "/saleBill/info", method = {RequestMethod.POST})
    public BaseOutput info(@RequestBody SaleBillInfoQueryForm form) {
        if (form.SaleBillID == null)
            return BaseOutput.failed("订单编号不能为空");
        return saleBillService.saleBillInfo(form);
    }

    @RequestMapping(value = "/saleBill/employeePerformanceCommission", method = {RequestMethod.POST})
    public BaseOutput employeePerformanceCommission(@RequestBody SaleBillInfoQueryForm form) {
        if (form.SaleBillID == null)
            return BaseOutput.failed("订单编号不能为空");
        return saleBillService.employeePerformanceCommission(form);
    }

    @RequestMapping(value = "/saleBill/updateEmployeePerformanceCommission", method = {RequestMethod.POST})
    public BaseOutput updateEmployeePerformanceCommission(@RequestBody SaleBillEmployeePerformanceCommissionUpdateForm form) {
        if (form.SaleBillID == null)
            return BaseOutput.failed("订单编号不能为空");
        if (!checkBillGoodID(form))
            return BaseOutput.failed("订单明细编号不能为空");
        if (!checkSaleHandlerID(form))
            return BaseOutput.failed("经手人编号不能为空");
        if (!checkScaleISNull(form))
            return BaseOutput.failed("比例不能为空");

        return saleBillService.updateEmployeePerformanceCommission(form);
    }

    @RequestMapping(value = "/saleBill/cancel", method = {RequestMethod.POST})
    public BaseOutput cancel(@RequestBody SaleBillInfoQueryForm form) {
        if (form.SaleBillID == null)
            return BaseOutput.failed("订单编号不能为空");
        return saleBillCommonService.cancelBill(form);
    }

    /**
     * 业绩计算
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/saleBill/employeeCommission", method = {RequestMethod.POST})
    public BaseOutput employeeCommission(@RequestBody CalculateEmployeePerformanceCommissionForm form) {
        if (form.GoodsID == null)
            return BaseOutput.failed("商品编号不能为空");
        if (form.Type == null)
            return BaseOutput.failed("商品类型不能为空");
        if (form.Quantity == null)
            return BaseOutput.failed("商品数量不能为空");
        if (form.IsPackageCard == null)
            return BaseOutput.failed("是否套餐卡不能为空");
        if (form.PayAmount == null)
            return BaseOutput.failed("现金支付金额不能为空");
        if (form.CardDeductionAmount == null)
            return BaseOutput.failed("非赠送卡支付金额不能为空");
        if (form.CardDeductionLargessAmount == null)
            return BaseOutput.failed("赠送卡支付金额不能为空");
        if (!checkHandler(form))
            return BaseOutput.failed("经手人编号不能为空");
        if (!checkEmployee(form))
            return BaseOutput.failed("员工编号不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");

        return saleBillService.calculateEmployeePerformanceCommissionForm(form);
    }

    static boolean checkArrearHandler(CalculateArrearEmployeePerformanceCommissionForm form) {
        for (SaleBillHandlerForm saleBillHandlerForm : form.SaleHandler) {
            if (saleBillHandlerForm.SaleHandlerID == null)
                return false;
        }
        return true;
    }


    @RequestMapping(value = "/saleBill/updateRemark", method = {RequestMethod.POST})
    public BaseOutput updateRemark(@RequestBody SaleBillRemarkUpdateForm form) {
        if (form.SaleBillID == null)
            return BaseOutput.failed("订单编号不能为空");
        if (form.Remark == null)
            return BaseOutput.failed("备注不能为空");
        return saleBillService.updateRemark(form);
    }

    /**
     * 挂单
     *
     * @param form
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/saleBill/createPendingOrder", method = {RequestMethod.POST})
    public BaseOutput createPendingOrder(@RequestBody SaleBillAddForm form) throws Exception {

        return saleBillService.createPendingOrder(form, BillChannel.BillChannelPC.getID());
    }

    @RequestMapping(value = "/saleBill/deletePendingOrder", method = {RequestMethod.POST})
    public BaseOutput deletePendingOrder(@RequestBody TreatBillInfoQueryForm form) {
        return saleBillService.deletePendingOrder(form);
    }

    static boolean checkBillGoodID(SaleBillEmployeePerformanceCommissionUpdateForm form) {
        for (SaleBillProductEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.ProductEmployee) {
            if (performanceCommissionUpdateForm.SaleBillProductID == null)
                return false;
        }
        for (SaleBillProjectEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.ProjectEmployee) {
            if (performanceCommissionUpdateForm.SaleBillProjectID == null)
                return false;
        }
        for (SaleBillGeneralCardEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.GeneralCardEmployee) {
            if (performanceCommissionUpdateForm.SaleBillGeneralCardID == null)
                return false;
        }
        for (SaleBillTimeCardEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.TimeCardEmployee) {
            if (performanceCommissionUpdateForm.SaleBillTimeCardID == null)
                return false;
        }
        for (SaleBillSavingCardEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.SavingCardEmployee) {
            if (performanceCommissionUpdateForm.SaleBillSavingCardID == null)
                return false;
        }

        for (SaleBillPackageCardProductEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.PackageCardEmployee.ProductEmployee) {
            if (performanceCommissionUpdateForm.SaleBillPackageCardID == null || performanceCommissionUpdateForm.SaleBillPackageCardProductID == null)
                return false;
        }
        for (SaleBillPackageCardProjectEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.PackageCardEmployee.ProjectEmployee) {
            if (performanceCommissionUpdateForm.SaleBillPackageCardID == null || performanceCommissionUpdateForm.SaleBillPackageCardProjectID == null)
                return false;
        }
        for (SaleBillPackageCardGeneralCardEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.PackageCardEmployee.GeneralCardEmployee) {
            if (performanceCommissionUpdateForm.SaleBillPackageCardID == null || performanceCommissionUpdateForm.SaleBillPackageCardGeneralCardID == null)
                return false;
        }
        for (SaleBillPackageCardTimeCardEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.PackageCardEmployee.TimeCardEmployee) {
            if (performanceCommissionUpdateForm.SaleBillPackageCardID == null || performanceCommissionUpdateForm.SaleBillPackageCardTimeCardID == null)
                return false;
        }
        for (SaleBillPackageCardSavingCardEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.PackageCardEmployee.SavingCardEmployee) {
            if (performanceCommissionUpdateForm.SaleBillPackageCardID == null || performanceCommissionUpdateForm.SaleBillPackageCardSavingCardID == null)
                return false;
        }

        return true;
    }

    static boolean checkSaleHandlerID(SaleBillEmployeePerformanceCommissionUpdateForm form) {
        for (SaleBillProductEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.ProductEmployee) {
            if (performanceCommissionUpdateForm.SaleHandlerID == null)
                return false;
        }
        for (SaleBillProjectEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.ProjectEmployee) {
            if (performanceCommissionUpdateForm.SaleHandlerID == null)
                return false;
        }
        for (SaleBillGeneralCardEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.GeneralCardEmployee) {
            if (performanceCommissionUpdateForm.SaleHandlerID == null)
                return false;
        }
        for (SaleBillTimeCardEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.TimeCardEmployee) {
            if (performanceCommissionUpdateForm.SaleHandlerID == null)
                return false;
        }
        for (SaleBillSavingCardEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.SavingCardEmployee) {
            if (performanceCommissionUpdateForm.SaleHandlerID == null)
                return false;
        }

        for (SaleBillPackageCardProductEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.PackageCardEmployee.ProductEmployee) {
            if (performanceCommissionUpdateForm.SaleHandlerID == null)
                return false;
        }
        for (SaleBillPackageCardProjectEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.PackageCardEmployee.ProjectEmployee) {
            if (performanceCommissionUpdateForm.SaleHandlerID == null)
                return false;
        }
        for (SaleBillPackageCardGeneralCardEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.PackageCardEmployee.GeneralCardEmployee) {
            if (performanceCommissionUpdateForm.SaleHandlerID == null)
                return false;
        }
        for (SaleBillPackageCardTimeCardEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.PackageCardEmployee.TimeCardEmployee) {
            if (performanceCommissionUpdateForm.SaleHandlerID == null)
                return false;
        }
        for (SaleBillPackageCardSavingCardEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.PackageCardEmployee.SavingCardEmployee) {
            if (performanceCommissionUpdateForm.SaleHandlerID == null)
                return false;
        }

        return true;
    }

    static boolean checkScaleISNull(SaleBillEmployeePerformanceCommissionUpdateForm form) {
        for (SaleBillProductEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.ProductEmployee) {
            if (performanceCommissionUpdateForm.Scale == null || performanceCommissionUpdateForm.Scale.equals(""))
                return false;
        }
        for (SaleBillProjectEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.ProjectEmployee) {
            if (performanceCommissionUpdateForm.Scale == null || performanceCommissionUpdateForm.Scale.equals(""))
                return false;
        }
        for (SaleBillGeneralCardEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.GeneralCardEmployee) {
            if (performanceCommissionUpdateForm.Scale == null || performanceCommissionUpdateForm.Scale.equals(""))
                return false;
        }
        for (SaleBillTimeCardEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.TimeCardEmployee) {
            if (performanceCommissionUpdateForm.Scale == null || performanceCommissionUpdateForm.Scale.equals(""))
                return false;
        }
        for (SaleBillSavingCardEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.SavingCardEmployee) {
            if (performanceCommissionUpdateForm.Scale == null || performanceCommissionUpdateForm.Scale.equals(""))
                return false;
        }

        for (SaleBillPackageCardProductEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.PackageCardEmployee.ProductEmployee) {
            if (performanceCommissionUpdateForm.Scale == null || performanceCommissionUpdateForm.Scale.equals(""))
                return false;
        }
        for (SaleBillPackageCardProjectEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.PackageCardEmployee.ProjectEmployee) {
            if (performanceCommissionUpdateForm.Scale == null || performanceCommissionUpdateForm.Scale.equals(""))
                return false;
        }
        for (SaleBillPackageCardGeneralCardEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.PackageCardEmployee.GeneralCardEmployee) {
            if (performanceCommissionUpdateForm.Scale == null || performanceCommissionUpdateForm.Scale.equals(""))
                return false;
        }
        for (SaleBillPackageCardTimeCardEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.PackageCardEmployee.TimeCardEmployee) {
            if (performanceCommissionUpdateForm.Scale == null || performanceCommissionUpdateForm.Scale.equals(""))
                return false;
        }
        for (SaleBillPackageCardSavingCardEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.PackageCardEmployee.SavingCardEmployee) {
            if (performanceCommissionUpdateForm.Scale == null || performanceCommissionUpdateForm.Scale.equals(""))
                return false;
        }

        return true;
    }

    static boolean checkScale(SaleBillEmployeePerformanceCommissionUpdateForm form) {
        for (SaleBillProductEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.ProductEmployee) {
            if (performanceCommissionUpdateForm.Scale != null && performanceCommissionUpdateForm.Scale.compareTo(new BigDecimal(100)) > 0)
                return false;
        }
        for (SaleBillProjectEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.ProjectEmployee) {
            if (performanceCommissionUpdateForm.Scale != null && performanceCommissionUpdateForm.Scale.compareTo(new BigDecimal(100)) > 0)
                return false;
        }
        for (SaleBillGeneralCardEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.GeneralCardEmployee) {
            if (performanceCommissionUpdateForm.Scale != null && performanceCommissionUpdateForm.Scale.compareTo(new BigDecimal(100)) > 0)
                return false;
        }
        for (SaleBillTimeCardEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.TimeCardEmployee) {
            if (performanceCommissionUpdateForm.Scale != null && performanceCommissionUpdateForm.Scale.compareTo(new BigDecimal(100)) > 0)
                return false;
        }
        for (SaleBillSavingCardEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.SavingCardEmployee) {
            if (performanceCommissionUpdateForm.Scale != null && performanceCommissionUpdateForm.Scale.compareTo(new BigDecimal(100)) > 0)
                return false;
        }

        for (SaleBillPackageCardProductEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.PackageCardEmployee.ProductEmployee) {
            if (performanceCommissionUpdateForm.Scale != null && performanceCommissionUpdateForm.Scale.compareTo(new BigDecimal(100)) > 0)
                return false;
        }
        for (SaleBillPackageCardProjectEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.PackageCardEmployee.ProjectEmployee) {
            if (performanceCommissionUpdateForm.Scale != null && performanceCommissionUpdateForm.Scale.compareTo(new BigDecimal(100)) > 0)
                return false;
        }
        for (SaleBillPackageCardGeneralCardEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.PackageCardEmployee.GeneralCardEmployee) {
            if (performanceCommissionUpdateForm.Scale != null && performanceCommissionUpdateForm.Scale.compareTo(new BigDecimal(100)) > 0)
                return false;
        }
        for (SaleBillPackageCardTimeCardEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.PackageCardEmployee.TimeCardEmployee) {
            if (performanceCommissionUpdateForm.Scale != null && performanceCommissionUpdateForm.Scale.compareTo(new BigDecimal(100)) > 0)
                return false;
        }
        for (SaleBillPackageCardSavingCardEmployeePerformanceCommissionUpdateForm performanceCommissionUpdateForm : form.PackageCardEmployee.SavingCardEmployee) {
            if (performanceCommissionUpdateForm.Scale != null && performanceCommissionUpdateForm.Scale.compareTo(new BigDecimal(100)) > 0)
                return false;
        }

        return true;
    }

    static boolean checkHandler(CalculateEmployeePerformanceCommissionForm form) {
        for (SaleBillHandlerForm saleBillHandlerForm : form.SaleHandler) {
            if (saleBillHandlerForm.SaleHandlerID == null)
                return false;
        }
        return true;
    }

    static boolean checkEmployee(CalculateEmployeePerformanceCommissionForm form) {
        for (SaleBillHandlerForm saleBillHandlerForm : form.SaleHandler) {
            if (saleBillHandlerForm.EmployeeID == null)
                return false;
        }
        return true;
    }

    static boolean checkRate(CalculateEmployeePerformanceCommissionForm form) {
        for (SaleBillHandlerForm saleBillHandlerForm : form.SaleHandler) {
            if (saleBillHandlerForm.Scale != null && saleBillHandlerForm.Scale.compareTo(new BigDecimal(100)) > 0)
                return false;
        }
        return true;
    }

    /**
     * 业绩计算
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/saleBill/arrearEmployeeCommission", method = {RequestMethod.POST})
    public BaseOutput arrearEmployeeCommission(@RequestBody CalculateArrearEmployeePerformanceCommissionForm form) {
        if (form.SaleBillGoodsID == null)
            return BaseOutput.failed("销售明细ID不能为空");
        if (form.GoodsID == null)
            return BaseOutput.failed("商品ID不能为空");
        if (form.IsPackageCard == null)
            return BaseOutput.failed("是否套餐不能为空");
        if (form.Type == null)
            return BaseOutput.failed("商品类型不能为空");
        if (!checkArrearHandler(form))
            return BaseOutput.failed("经手人编号不能为空");
        if (!checkArrearEmployee(form))
            return BaseOutput.failed("员工编号不能为空");
        if (!checkArrearRate(form))
            return BaseOutput.failed("比例不能大于100");

        return saleBillService.calculateArrearEmployeeCommission(form);
    }

    static boolean checkArrearEmployee(CalculateArrearEmployeePerformanceCommissionForm form) {
        for (SaleBillHandlerForm saleBillHandlerForm : form.SaleHandler) {
            if (saleBillHandlerForm.EmployeeID == null)
                return false;
        }
        return true;
    }

    static boolean checkArrearRate(CalculateArrearEmployeePerformanceCommissionForm form) {
        for (SaleBillHandlerForm saleBillHandlerForm : form.SaleHandler) {
            if (saleBillHandlerForm.Scale != null && saleBillHandlerForm.Scale.compareTo(new BigDecimal(100)) > 0)
                return false;
        }
        return true;
    }

    /**
     * 上传图片
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/saleBill/addSaleBillAttachment", method = {RequestMethod.POST})
    public BaseOutput addSaleBillAttachment(@RequestBody SaleBillAttachmentAddForm form) {
        return saleBillService.addSaleBillAttachment(form);
    }

    @RequestMapping(value = "/saleBill/addTreatBillAttachment", method = {RequestMethod.POST})
    public BaseOutput addTreatBillAttachment(@RequestBody TreatBillAttachmentAddForm form) {
        return saleBillService.addTreatBillAttachment(form);
    }

    @RequestMapping(value = "/saleBill/deleteSaleBillAttachment", method = {RequestMethod.POST})
    public BaseOutput deleteSaleBillAttachment(@RequestBody DeleteSaleBillOrTreatBillAttachmentForm form) {
        return saleBillService.deleteSaleBillAttachment(form);
    }

    @RequestMapping(value = "/saleBill/deleteTreatBillAttachment", method = {RequestMethod.POST})
    public BaseOutput deleteTreatBillAttachment(@RequestBody DeleteSaleBillOrTreatBillAttachmentForm form) {
        return saleBillService.deleteTreatBillAttachment(form);
    }

    @RequestMapping(value = "/saleBill/getSaleBillAttachment", method = {RequestMethod.POST})
    public BaseOutput getSaleBillAttachment(@RequestBody SaleBillOrTreatBillAttachmentQueryForm form) {
        return saleBillService.getSaleBillAttachment(form);
    }

    @RequestMapping(value = "/saleBill/getTreatBillAttachment", method = {RequestMethod.POST})
    public BaseOutput getTreatBillAttachment(@RequestBody SaleBillOrTreatBillAttachmentQueryForm form) {
        return saleBillService.getTreatBillAttachment(form);
    }

    /**
     * 签名
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/saleBill/getSignature", method = {RequestMethod.POST})
    public BaseOutput getSignature(@RequestBody SaleBillSignatureAddForm form) {
        return saleBillService.getSignature(form);
    }

    /**
     * 充值储值卡
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/saleBill/recharge", method = {RequestMethod.POST})
    public BaseOutput recharge(@RequestBody SaleBillRechargeSavingCardAddForm form) throws Exception {

        BigDecimal PayAmount = BigDecimal.ZERO;

        for (SaleBillRechargeSavingCardForm savingCardForm : form.SavingCard) {
            PayAmount = PayAmount.add(savingCardForm.Amount);
        }
        if (form.Amount.compareTo(PayAmount) != 0) {
            return BaseOutput.failed("订单实付金额不正确");
        }

        return saleBillCommonService.recharge(form, BillChannel.BillChannelPC.getID());
    }

    /**
     * 修改时间
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/saleBill/updateBillDate", method = {RequestMethod.POST})
    public BaseOutput updateBillDate(@RequestBody SaleBillUpdateDateAddForm form) {
        if (form.BillID == null)
            return BaseOutput.failed("订单编号不能为空");
        if (form.BillDate == null)
            return BaseOutput.failed("订单时间不能为空");

        return saleBillService.updateBillDate(form);
    }

    /**
     * 修改支付方式
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/saleBill/updatePayMethod", method = {RequestMethod.POST})
    public BaseOutput updatePayMethod(@RequestBody SaleBillUpdatePayMethodUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("编号不能为空");
        if (form.PayMethodID == null)
            return BaseOutput.failed("支付方式不能为空");

        return saleBillService.updatePayMethod(form);
    }

    /**
     * 修改明细备注
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/saleBill/updateProductRemark", method = {RequestMethod.POST})
    public BaseOutput updateProductRemark(@RequestBody SaleBillDetailRemarkUpdateForm form) {
        if (form.SaleBillGoodID == null)
            return BaseOutput.failed("编号不能为空");

        return saleBillService.updateProductRemark(form);
    }

    @RequestMapping(value = "/saleBill/updateProjectRemark", method = {RequestMethod.POST})
    public BaseOutput updateProjectRemark(@RequestBody SaleBillDetailRemarkUpdateForm form) {
        if (form.SaleBillGoodID == null)
            return BaseOutput.failed("编号不能为空");

        return saleBillService.updateProjectRemark(form);
    }

    @RequestMapping(value = "/saleBill/updateGeneralCardRemark", method = {RequestMethod.POST})
    public BaseOutput updateGeneralCardRemark(@RequestBody SaleBillDetailRemarkUpdateForm form) {
        if (form.SaleBillGoodID == null)
            return BaseOutput.failed("编号不能为空");

        return saleBillService.updateGeneralCardRemark(form);
    }

    @RequestMapping(value = "/saleBill/updateTimeCardRemark", method = {RequestMethod.POST})
    public BaseOutput updateTimeCardRemark(@RequestBody SaleBillDetailRemarkUpdateForm form) {
        if (form.SaleBillGoodID == null)
            return BaseOutput.failed("编号不能为空");

        return saleBillService.updateTimeCardRemark(form);
    }

    @RequestMapping(value = "/saleBill/updateSavingCardRemark", method = {RequestMethod.POST})
    public BaseOutput updateSavingCardRemark(@RequestBody SaleBillDetailRemarkUpdateForm form) {
        if (form.SaleBillGoodID == null)
            return BaseOutput.failed("编号不能为空");

        return saleBillService.updateSavingCardRemark(form);
    }

    @RequestMapping(value = "/saleBill/updatePackageCardRemark", method = {RequestMethod.POST})
    public BaseOutput updatePackageCardRemark(@RequestBody SaleBillDetailRemarkUpdateForm form) {
        if (form.SaleBillGoodID == null)
            return BaseOutput.failed("编号不能为空");

        return saleBillService.updatePackageCardRemark(form);
    }

    @RequestMapping(value = "/saleBill/updatePackageCardProductRemark", method = {RequestMethod.POST})
    public BaseOutput updatePackageCardProductRemark(@RequestBody SaleBillDetailRemarkUpdateForm form) {
        if (form.SaleBillGoodID == null)
            return BaseOutput.failed("编号不能为空");

        return saleBillService.updatePackageCardProductRemark(form);
    }

    @RequestMapping(value = "/saleBill/updatePackageCardProjectRemark", method = {RequestMethod.POST})
    public BaseOutput updatePackageCardProjectRemark(@RequestBody SaleBillDetailRemarkUpdateForm form) {
        if (form.SaleBillGoodID == null)
            return BaseOutput.failed("编号不能为空");

        return saleBillService.updatePackageCardProjectRemark(form);
    }

    @RequestMapping(value = "/saleBill/updatePackageCardGeneralCardRemark", method = {RequestMethod.POST})
    public BaseOutput updatePackageCardGeneralCardRemark(@RequestBody SaleBillDetailRemarkUpdateForm form) {
        if (form.SaleBillGoodID == null)
            return BaseOutput.failed("编号不能为空");

        return saleBillService.updatePackageCardGeneralCardRemark(form);
    }

    @RequestMapping(value = "/saleBill/updatePackageCardTimeCardRemark", method = {RequestMethod.POST})
    public BaseOutput updatePackageCardTimeCardRemark(@RequestBody SaleBillDetailRemarkUpdateForm form) {
        if (form.SaleBillGoodID == null)
            return BaseOutput.failed("编号不能为空");

        return saleBillService.updatePackageCardTimeCardRemark(form);
    }

    @RequestMapping(value = "/saleBill/updatePackageCardSavingCardRemark", method = {RequestMethod.POST})
    public BaseOutput updatePackageCardSavingCardRemark(@RequestBody SaleBillDetailRemarkUpdateForm form) {
        if (form.SaleBillGoodID == null)
            return BaseOutput.failed("编号不能为空");

        return saleBillService.updatePackageCardSavingCardRemark(form);
    }

    @RequestMapping(value = "/saleBill/pendingOrderInfo", method = {RequestMethod.POST})
    public BaseOutput pendingOrderInfo(@RequestBody SaleBillInfoQueryForm form) {
        if (form.SaleBillID == null)
            return BaseOutput.failed("订单编号不能为空");
        return saleBillService.pendingOrderInfo(form);
    }

    @RequestMapping(value = "/saleBill/pendingList", method = {RequestMethod.POST})
    public BaseOutput pendingList(@RequestBody SaleBillQueryForm form) {
        return saleBillService.pendingList(form);
    }

    /**
     * 充值挂单
     *
     * @param form
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/saleBill/createRechargePendingOrder", method = {RequestMethod.POST})
    public BaseOutput createRechargePendingOrder(@RequestBody SaleBillRechargeSavingCardAddForm form) throws Exception {

        BigDecimal PayAmount = BigDecimal.ZERO;

        for (SaleBillRechargeSavingCardForm savingCardForm : form.SavingCard) {
            PayAmount = PayAmount.add(savingCardForm.Amount);
        }
        if (form.Amount.compareTo(PayAmount) != 0) {
            return BaseOutput.failed("订单实付金额不正确");
        }

        return saleBillService.createRechargePendingOrder(form, BillChannel.BillChannelPC.getID());
    }

    @RequestMapping(value = "/saleBill/entityPerformance", method = {RequestMethod.POST})
    public BaseOutput entityPerformanceCommission(@RequestBody SaleBillInfoQueryForm form) {
        if (form.SaleBillID == null)
            return BaseOutput.failed("订单编号不能为空");
        return saleBillService.entityPerformance(form);
    }

    /**
     * 业绩计算
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/saleBill/calculateEntityPerformance", method = {RequestMethod.POST})
    public BaseOutput calculateEntityPerformance(@RequestBody CalculateEntityPerformanceForm form) {
        if (form.GoodsID == null)
            return BaseOutput.failed("商品编号不能为空");
        if (form.Type == null)
            return BaseOutput.failed("商品类型不能为空");
        if (form.PayAmount == null)
            return BaseOutput.failed("现金支付金额不能为空");
        if (form.CardDeductionAmount == null)
            return BaseOutput.failed("非赠送卡支付金额不能为空");
        if (form.CardDeductionLargessAmount == null)
            return BaseOutput.failed("赠送卡支付金额不能为空");

        return saleBillService.calculateEntityPerformance(form);
    }

    @RequestMapping(value = "/saleBill/updateEntityPerformance", method = {RequestMethod.POST})
    public BaseOutput updateEntityPerformance(@RequestBody SaleBillEntityPerformanceUpdateForm form) {
        if (form.SaleBillID == null)
            return BaseOutput.failed("订单编号不能为空");
        return saleBillService.updateEntityPerformance(form);
    }

    @RequestMapping(value = "/saleBill/employeeDiscount", method = {RequestMethod.POST})
    public BaseOutput employeeDiscount() {
        return saleBillService.employeeDiscount();
    }


}