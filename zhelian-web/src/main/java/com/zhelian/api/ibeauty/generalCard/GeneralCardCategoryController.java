package com.zhelian.api.ibeauty.generalCard;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.GeneralCardCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.GeneralCardCategoryMoveForm;
import com.zhelian.model.ibeauty.form.input.GeneralCardCategoryQueryForm;
import com.zhelian.model.ibeauty.form.input.GeneralCardCategoryUpdateForm;
import com.zhelian.service.ibeauty.generalCard.GeneralCardCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
public class GeneralCardCategoryController {

    @Autowired
    GeneralCardCategoryService service;

    @RequestMapping(value = "/generalCardCategory/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody GeneralCardCategoryQueryForm form) {
        return service.generalCardCategoryAll(form);
    }

    @RequestMapping(value = "/generalCardCategory/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody GeneralCardCategoryAddForm form) {
        if (!StringUtils.hasText(form.Name)) {
            return BaseOutput.failed("通用次卡类型名称不能为空");
        }
        return service.createGeneralCardCategory(form);
    }

    @RequestMapping(value = "/generalCardCategory/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody GeneralCardCategoryUpdateForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("通用次卡类型名称不能为空");
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (form.Active == null)
            return BaseOutput.failed("有效性不能为空");
        return service.updateGeneralCardCategory(form);
    }

    @RequestMapping(value = "/generalCardCategory/move", method = RequestMethod.POST)
    public BaseOutput move(@RequestBody GeneralCardCategoryMoveForm form) {
        if (form.MoveID == null) return BaseOutput.failed("MoveID不能为空");
        return service.moveGeneralCardCategorySequence(form);
    }

}
