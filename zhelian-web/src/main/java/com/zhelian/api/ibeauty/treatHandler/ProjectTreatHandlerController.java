package com.zhelian.api.ibeauty.treatHandler;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerMoveForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerUpdateForm;
import com.zhelian.service.ibeauty.treatHandler.ProjectTreatHandlerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description:
 * @Param: No such property: code for class: Script1
 * @returns:
 * @Author: xuehao
 * @Date: 2020/3/31 13:42
 */
@RestController
@RequestMapping(value = "/api")
public class ProjectTreatHandlerController {

    @Autowired
    ProjectTreatHandlerService projectTreatHandlerService;

    @RequestMapping(value = "/projectTreatHandler/all", method = {RequestMethod.POST})
    public BaseOutput allProjectTreatHandler(@RequestBody SaleHandlerQueryForm form) {
        return projectTreatHandlerService.allProjectTreatHandler(form);
    }

    @RequestMapping(value = "/projectTreatHandler/create", method = {RequestMethod.POST})
    public BaseOutput createProjectTreatHandler(@RequestBody SaleHandlerAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("经手人名称不能为空");
        if (form.EntityID == null)
            return BaseOutput.failed("门店ID不能为空");
        if (form.JobType == null || form.JobType.length == 0)
            return BaseOutput.failed("适用职务不能为空");


        return projectTreatHandlerService.createProjectTreatHandler(form);
    }

    @RequestMapping(value = "/projectTreatHandler/update", method = {RequestMethod.POST})
    public BaseOutput updateProjectTreatHandler(@RequestBody SaleHandlerUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("经手人名称不能为空");
        if (form.EntityID == null)
            return BaseOutput.failed("门店ID不能为空");
        if (form.Active == null)
            return BaseOutput.failed("是否有效不能为空");
        if (form.JobType == null || form.JobType.length == 0)
            return BaseOutput.failed("适用职务不能为空");


        return projectTreatHandlerService.updateProjectTreatHandler(form);
    }

    @RequestMapping(value = "/projectTreatHandler/move", method = {RequestMethod.POST})
    public BaseOutput moveProjectTreatHandler(@RequestBody SaleHandlerMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");
        return projectTreatHandlerService.moveProjectTreatHandler(form);
    }
}