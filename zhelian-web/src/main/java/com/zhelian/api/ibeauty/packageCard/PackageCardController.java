package com.zhelian.api.ibeauty.packageCard;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.service.ibeauty.packageCard.PackageCardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


/**
 * @Description:
 * @Param:
 * @returns:
 * @Author: xuehao
 * @Date: 2020/3/27 9:03
 */
@RestController
@RequestMapping(value = "/api")
public class PackageCardController {

    @Autowired
    PackageCardService packageCardService;

    @RequestMapping(value = "/packageCard/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody PackageCardQueryForm form) {
        return packageCardService.packageCardList(form);
    }

    @RequestMapping(value = "/packageCard/getPackageCardDetails", method = {RequestMethod.POST})
    public BaseOutput getPackageCardDetails(@RequestBody PackageCardGoodsQueryForm form) {
        return packageCardService.getPackageCardDetails(form);
    }

    @RequestMapping(value = "/packageCard/savingCard", method = {RequestMethod.POST})
    public BaseOutput savingCard() {
        return packageCardService.savingCard();
    }

    @RequestMapping(value = "/packageCard/packageCardGoods", method = {RequestMethod.POST})
    public BaseOutput packageCardGoods(@RequestBody PackageCardGoodsQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (form.IsLargess == null)
            return BaseOutput.failed("是否赠送不能为空");
        return packageCardService.packageCardGoods(form);
    }

    @RequestMapping(value = "/packageCard/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody PackageCardAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("套餐卡名称不能为空");
        if (form.PackageCardCategoryID == null)
            return BaseOutput.failed("套餐卡类别ID不能为空");
        if (form.SaleStartDate == null)
            return BaseOutput.failed("售卖开始时间不能为空");
        if (form.SaleEndingDate == null)
            return BaseOutput.failed("售卖结束时间不能为空");
        if (form.ValidType == null)
            return BaseOutput.failed("有效期类型不能为空");
        if (!form.ValidType.equals(3) && form.ValidDay == null)
            return BaseOutput.failed("有效期天数不能为空");
        if (form.IsAllowSell == null)
            return BaseOutput.failed("是否上架不能为空");
        if (form.IsAllowLargess == null)
            return BaseOutput.failed("是否可赠送不能为空");
        return packageCardService.create(form);
    }


    @RequestMapping(value = "/packageCard/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody PackageCardUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("套餐卡ID不能为空");
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("套餐卡名称不能为空");
        if (form.PackageCardCategoryID == null)
            return BaseOutput.failed("套餐卡类别ID不能为空");
        if (form.SaleStartDate == null)
            return BaseOutput.failed("售卖开始时间不能为空");
        if (form.SaleEndingDate == null)
            return BaseOutput.failed("售卖结束时间不能为空");
        if (form.ValidType == null)
            return BaseOutput.failed("有效期类型不能为空");
        if (!form.ValidType.equals(3) && form.ValidDay == null)
            return BaseOutput.failed("有效期天数不能为空");
        if (form.IsAllowSell == null)
            return BaseOutput.failed("是否上架不能为空");
        if (form.IsAllowLargess == null)
            return BaseOutput.failed("是否可赠送不能为空");
        return packageCardService.update(form);
    }

    @RequestMapping(value = "/packageCard/packageCardSaleEntity", method = {RequestMethod.POST})
    public BaseOutput packageCardSaleEntity(@RequestBody PackageCardSaleEntityQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        return packageCardService.packageCardSaleEntity(form);
    }

    @RequestMapping(value = "/packageCard/packageCardModifyPriceEntity", method = {RequestMethod.POST})
    public BaseOutput packageCardModifyPriceEntity(@RequestBody PackageCardSaleEntityQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        return packageCardService.packageCardModifyPriceEntity(form);
    }

    @RequestMapping(value = "/packageCard/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody GoodsMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");

        return packageCardService.move(form);
    }

    @RequestMapping(value = "/packageCard/all", method = {RequestMethod.POST})
    public BaseOutput list() {
        return packageCardService.allPackageCard();
    }

    @RequestMapping(value = "/packageCard/bitchUpdatePackageCardSaleEntity", method = {RequestMethod.POST})
    public BaseOutput bitchUpdatePackageCardSaleEntity(@RequestBody GoodsBitchUpdateForm form) {
        if (form.GoodsID.length == 0)
            return BaseOutput.failed("请选择套餐卡");
        return packageCardService.bitchUpdatePackageCardSaleEntity(form);
    }

}