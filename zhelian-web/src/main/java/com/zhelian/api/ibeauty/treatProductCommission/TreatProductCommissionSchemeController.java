package com.zhelian.api.ibeauty.treatProductCommission;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatProductCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCommissionSchemeDeleteForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCommissionSchemeQueryForm;
import com.zhelian.service.ibeauty.treatProductCommission.TreatProductCommissionSchemeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
public class TreatProductCommissionSchemeController {

    @Autowired
    TreatProductCommissionSchemeService treatProductCommissionSchemeService;

    @RequestMapping(value = "/treatProductCommissionScheme/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody TreatProductCommissionSchemeQueryForm form) {
        return treatProductCommissionSchemeService.treatProductCommissionSchemeList(form);
    }

    @RequestMapping(value = "/treatProductCommissionScheme/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody TreatProductCommissionSchemeAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return treatProductCommissionSchemeService.createTreatProductCommissionScheme(form);
    }

    @RequestMapping(value = "/treatProductCommissionScheme/delete", method = {RequestMethod.POST})
    public BaseOutput delete(@RequestBody TreatProductCommissionSchemeDeleteForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return treatProductCommissionSchemeService.deleteTreatProductCommissionScheme(form);
    }
}

