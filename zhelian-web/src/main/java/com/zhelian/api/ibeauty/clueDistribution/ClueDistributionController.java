package com.zhelian.api.ibeauty.clueDistribution;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.service.ibeauty.clueDistribution.ClueDistributionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;


/**
 * 飞鱼线索区域配置
 */
@RestController
@RequestMapping(value = "/api")
public class ClueDistributionController {

    @Autowired
    ClueDistributionService clueDistributionService;

    /**
     * 默认负责员工查询
     * @return
     */
    @RequestMapping(value = "/clueDistribution/employeeConfigAll", method = {RequestMethod.POST})
    public BaseOutput employeeConfigAll() {
        return clueDistributionService.employeeConfigAll();
    }

    /**
     * 保存默认负责员工查询
     * @param form
     * @return
     */
    @RequestMapping(value = "/clueDistribution/addEmployeeConfig", method = {RequestMethod.POST})
    public BaseOutput addEmployeeConfig(@RequestBody ClueDistributionEmployeeConfigAddForm form) {
        return clueDistributionService.addEmployeeConfig(form);
    }

    /**
     * 分页查询线索区域数据
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/clueDistribution/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody ClueDistributionQueryForm form) {
        return clueDistributionService.list(form);
    }

    /**
     * 查询线索区域负责员工详情
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/clueDistribution/detail", method = {RequestMethod.POST})
    public BaseOutput detail(@RequestBody ClueDistributionDetailQueryForm form) {
        if (form.ID == null) {
            return BaseOutput.failed("编号不能为空");
        }
        return clueDistributionService.detail(form);
    }

    /**
     * 保存线索区域
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/clueDistribution/add", method = {RequestMethod.POST})
    public BaseOutput add(@RequestBody ClueDistributionAddForm form) {

        if (!StringUtils.hasText(form.ProvinceCode)) return BaseOutput.failed("请选择省份");
        if (!StringUtils.hasText(form.CityCode)) return BaseOutput.failed("请选择城市");
        if (form.Detail == null || form.Detail.size() <= 0) return BaseOutput.failed("请选择分配员工");

        return clueDistributionService.add(form);
    }

    /**
     * 更新线索区域
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/clueDistribution/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody ClueDistributionUpdateForm form) {
        if (form.ID == null) return BaseOutput.failed("编号不能为空");
        if (form.Active == null) return BaseOutput.failed("有效期不能为空");
        if (form.Detail == null || form.Detail.size() <= 0) return BaseOutput.failed("请选择分配员工");

        return clueDistributionService.update(form);
    }

    /**
     * 接收巨量token
     * @param state
     * @param auth_code
     * @return
     */
    @RequestMapping(value = "/clueDistribution/authCode", method = {RequestMethod.GET})
    public BaseOutput access_token(@RequestParam(value = "state") String state, @RequestParam(value = "auth_code") String auth_code) {
        return clueDistributionService.authCode(state,auth_code);
    }


//    @RequestMapping(value = "/clueDistribution/save", method = {RequestMethod.POST})
//    public BaseOutput save(@RequestBody JSONArray form) {
//        DynamicsDataSourceHelper.setDataSource("yuyantang");
//        clueDistributionService.save(form,"1");
//        return BaseOutput.success();
//    }
}
