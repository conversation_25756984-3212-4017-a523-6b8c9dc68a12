package com.zhelian.api.ibeauty.timeCard;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.service.ibeauty.timeCard.TimeCardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

@RestController
@RequestMapping("/api")
public class TimeCardController {

    @Autowired
    TimeCardService service;

    @RequestMapping(value = "/timeCard/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody TimeCardQueryForm form) {
        return service.timeCardList(form);
    }

    @RequestMapping(value = "/timeCard/getTimeCardDetails", method = {RequestMethod.POST})
    public BaseOutput getTimeCardDetails(@RequestBody TimeCardQueryEntityForm form) {
        return service.getTimeCardDetails(form);
    }

    @RequestMapping(value = "/timeCard/timeCardSaleEntity", method = {RequestMethod.POST})
    public BaseOutput timeCardSaleEntity(@RequestBody TimeCardQueryEntityForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        return service.timeCardSaleEntity(form);
    }

    @RequestMapping(value = "/timeCard/timeCardConsumeEntity", method = {RequestMethod.POST})
    public BaseOutput timeCardConsumeEntity(@RequestBody TimeCardQueryEntityForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        return service.timeCardConsumeEntity(form);
    }

    @RequestMapping(value = "/timeCard/timeCardModifyPriceEntity", method = {RequestMethod.POST})
    public BaseOutput timeCardModifyPriceEntity(@RequestBody TimeCardQueryEntityForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        return service.timeCardModifyPriceEntity(form);
    }

    @RequestMapping(value = "/timeCard/timeCardProject", method = {RequestMethod.POST})
    public BaseOutput timeCardProject(@RequestBody TimeCardQueryEntityForm form) {
        return service.timeCardProject(form);
    }

    @RequestMapping(value = "/timeCard/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody TimeCardAddForm form) {
        if (!StringUtils.hasText(form.Name)) {
            return BaseOutput.failed("时效卡名称不能为空");
        }
        if (form.TimeCardCategoryID == null) {
            return BaseOutput.failed("时效卡类型不能为空");
        }
        if (form.Price == null || form.Price.compareTo(BigDecimal.ZERO) == 0) {
            return BaseOutput.failed("时效卡售价不能为空");
        }
        if (form.PerformanceAmount == null) {
            return BaseOutput.failed("业绩提成次数不能为空");
        }
        if (form.PerformanceAmount <= 0) {
            return BaseOutput.failed("业绩提成次数大于0");
        }
        if (form.SaleStartDate == null) {
            return BaseOutput.failed("售卖开始时间不能为空");
        }
        if (form.SaleEndingDate == null) {
            return BaseOutput.failed("售卖结束时间不能为空");
        }
        if (form.ValidType == null) {
            return BaseOutput.failed("有效期类型不能为空");
        }
        if (!form.ValidType.equals(3) && form.ValidDay == null) {
            return BaseOutput.failed("有效期天数不能为空");
        }
        if (form.IsAllowSell == null) {
            return BaseOutput.failed("未确认是否上架");
        }
        if (form.IsAllowLargess == null) {
            return BaseOutput.failed("未确认是否可赠送");
        }

        if (form.ConsumeCycle != null && form.ConsumeCycle.intValue() > 3650) {
            return BaseOutput.failed("消耗周期不能大于10年");
        }
        if (form.TimeCardProject.size() == 0) {
            return BaseOutput.failed("未添加适用项目");
        }
        return service.createTimeCard(form);
    }

    @RequestMapping(value = "/timeCard/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody TimeCardUpdateForm form) {
        if (!StringUtils.hasText(form.Name)) {
            return BaseOutput.failed("时效卡名称不能为空");
        }
        if (form.TimeCardCategoryID == null) {
            return BaseOutput.failed("时效卡类型不能为空");
        }
        if (form.Price == null || form.Price.compareTo(BigDecimal.ZERO) == 0) {
            return BaseOutput.failed("时效卡售价不能为空");
        }
        if (form.PerformanceAmount == null) {
            return BaseOutput.failed("业绩提成次数不能为空");
        }
        if (form.PerformanceAmount <= 0) {
            return BaseOutput.failed("业绩提成次数大于0");
        }
        if (form.SaleStartDate == null) {
            return BaseOutput.failed("售卖开始时间不能为空");
        }
        if (form.SaleEndingDate == null) {
            return BaseOutput.failed("售卖结束时间不能为空");
        }
        if (form.ValidType == null) {
            return BaseOutput.failed("有效期类型不能为空");
        }
        if (!form.ValidType.equals(3) && form.ValidDay == null) {
            return BaseOutput.failed("有效期天数不能为空");
        }
        if (form.IsAllowSell == null) {
            return BaseOutput.failed("未确认是否上架");
        }
        if (form.IsAllowLargess == null) {
            return BaseOutput.failed("未确认是否可赠送");
        }

        if (form.TimeCardProject.size() == 0) {
            return BaseOutput.failed("未添加适用项目");
        }
        if (form.ConsumeCycle != null && form.ConsumeCycle.intValue() > 3650) {
            return BaseOutput.failed("消耗周期不能大于10年");
        }
        return service.updateTimeCard(form);
    }

    @RequestMapping(value = "/timeCard/all", method = {RequestMethod.POST})
    public BaseOutput all() {
        return service.allTimeCard();
    }

    @RequestMapping(value = "/timeCard/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody GoodsMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");

        return service.move(form);
    }

    @RequestMapping(value = "/timeCard/bitchUpdateTimeCardSaleEntity", method = {RequestMethod.POST})
    public BaseOutput bitchUpdateTimeCardSaleEntity(@RequestBody GoodsBitchUpdateForm form) {
        if (form.GoodsID.length == 0)
            return BaseOutput.failed("请选择产品");

        return service.bitchUpdateTimeCardSaleEntity(form);
    }

    @RequestMapping(value = "/timeCard/bitchUpdateTimeCardConsumeEntity", method = {RequestMethod.POST})
    public BaseOutput bitchUpdateTimeCardConsumeEntity(@RequestBody GoodsBitchUpdateForm form) {
        if (form.GoodsID.length == 0)
            return BaseOutput.failed("请选择产品");

        return service.bitchUpdateTimeCardConsumeEntity(form);
    }
}
