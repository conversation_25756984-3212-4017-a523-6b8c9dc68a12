package com.zhelian.api.ibeauty.treatSavingCardCommission;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCommissionSchemeForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCommissionSchemeQueryForm;
import com.zhelian.service.ibeauty.treatSavingCardCommission.TreatSavingCardCommissionSchemeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
public class TreatSavingCardCommissionSchemeController {

    @Autowired
    TreatSavingCardCommissionSchemeService treatSavingCardCommissionSchemeService;

    @RequestMapping(value = "/treatSavingCardCommissionScheme/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody TreatSavingCardProjectCommissionSchemeQueryForm form) {
        return treatSavingCardCommissionSchemeService.treatSavingCardCommissionSchemeList(form);
    }

    @RequestMapping(value = "/treatSavingCardCommissionScheme/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody TreatSavingCardProjectCommissionSchemeForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return treatSavingCardCommissionSchemeService.createTreatSavingCardCommissionScheme(form);
    }

    @RequestMapping(value = "/treatSavingCardCommissionScheme/delete", method = {RequestMethod.POST})
    public BaseOutput delete(@RequestBody TreatSavingCardProjectCommissionSchemeForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return treatSavingCardCommissionSchemeService.deleteTreatSavingCardCommissionScheme(form);
    }
}

