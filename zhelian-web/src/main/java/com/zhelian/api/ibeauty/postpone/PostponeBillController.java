package com.zhelian.api.ibeauty.postpone;


import com.zhelian.core.utils.enumeration.BillChannel;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.PostponeBillAddForm;
import com.zhelian.model.ibeauty.form.input.PostponeBillInfoQueryForm;
import com.zhelian.model.ibeauty.form.input.PostponeBillQueryForm;
import com.zhelian.model.ibeauty.form.input.PostponeBillUpdateForm;
import com.zhelian.service.ibeauty.postpone.PostponeBillCommonService;
import com.zhelian.service.ibeauty.postpone.PostponeBillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class PostponeBillController {

    @Autowired
    PostponeBillService postPoneBillService;
    @Autowired
    PostponeBillCommonService postponeBillCommonService;

    @RequestMapping(value = "/postponeBill/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody PostponeBillAddForm form) throws Exception {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return postPoneBillService.create(form, BillChannel.BillChannelPC.getID());
    }

    @RequestMapping(value = "/postponeBill/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody PostponeBillQueryForm form) {
        return postPoneBillService.postponeBillList(form);
    }

    @RequestMapping(value = "/postponeBill/info", method = {RequestMethod.POST})
    public BaseOutput info(@RequestBody PostponeBillInfoQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("延期申请编号不能为空");
        return postPoneBillService.postponeBillinfo(form);
    }

    @RequestMapping(value = "/postponeBill/approvalList", method = {RequestMethod.POST})
    public BaseOutput approvalList(@RequestBody PostponeBillQueryForm form) {
        return postPoneBillService.postponeBillApprovalList(form);
    }

    @RequestMapping(value = "/postponeBill/revoke", method = {RequestMethod.POST})
    public BaseOutput revoke(@RequestBody PostponeBillInfoQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("延期申请编号不能为空");
        return postPoneBillService.revoke(form);
    }

    @RequestMapping(value = "/postponeBill/approval", method = {RequestMethod.POST})
    public BaseOutput approval(@RequestBody PostponeBillUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("延期申请编号不能为空");
        if (form.ApprovalStatus == null)
            return BaseOutput.failed("审批状态不能为空");
        return postponeBillCommonService.approval(form);
    }
}