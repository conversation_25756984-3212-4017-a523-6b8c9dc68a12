package com.zhelian.api.ibeauty.PriceAdjustment;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.ProductPriceAdjustmentAddForm;
import com.zhelian.model.ibeauty.form.input.ProductPriceAdjustmentDeleteForm;
import com.zhelian.model.ibeauty.form.input.ProductPriceAdjustmentQueryForm;
import com.zhelian.service.ibeauty.PriceAdjustment.ProductPriceAdjustmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class ProductPriceAdjustmentController {

    @Autowired
    ProductPriceAdjustmentService productPriceAdjustmentService;

    @RequestMapping(value = "/productPriceAdjustment/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody ProductPriceAdjustmentQueryForm form) {
        return productPriceAdjustmentService.productPriceAdjustmentList(form);
    }

    @RequestMapping(value = "/productPriceAdjustment/create", method = {RequestMethod.POST})
    public BaseOutput createProductPriceAdjustment(@RequestBody ProductPriceAdjustmentAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("门店编号不能为空");
        if (form.ProductID == null)
            return BaseOutput.failed("产品编号不能为空");
        if (form.Price == null)
            return BaseOutput.failed("调整价格不能为空");

        return productPriceAdjustmentService.createProductPriceAdjustment(form);
    }

    @RequestMapping(value = "/productPriceAdjustment/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody ProductPriceAdjustmentAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("门店编号不能为空");
        if (form.ProductID == null)
            return BaseOutput.failed("产品编号不能为空");
        if (form.Price == null)
            return BaseOutput.failed("调整价格不能为空");

        return productPriceAdjustmentService.updateProductPriceAdjustmentEntity(form);
    }

    @RequestMapping(value = "/productPriceAdjustment/delete", method = {RequestMethod.POST})
    public BaseOutput delete(@RequestBody ProductPriceAdjustmentDeleteForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("门店编号不能为空");
        if (form.ProductID == null)
            return BaseOutput.failed("产品编号不能为空");

        return productPriceAdjustmentService.deleteProductPriceAdjustmentEntity(form);
    }
}