package com.zhelian.api.ibeauty.customer;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.entity.CustomerGroupConditionEntity;
import com.zhelian.service.ibeauty.customer.CustomerGroupConditionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(value = "/api/CustomerGroupCondition")
public class CustomerGroupConditionController {

    @Autowired
    CustomerGroupConditionService customerGroupConditionService;


    //不同的分组类型下加入不同的条件
    @PostMapping(value = "/addGroupCondition")
    public BaseOutput addGroupCondition(@RequestBody List<CustomerGroupConditionEntity> list) {
        for (int b = 0; b < list.size(); b++) {
            customerGroupConditionService.addGroupCondition(list.get(b));
        }
        return BaseOutput.success();
    }

    //删除分群类型下的分群条件
    @PostMapping(value = "/delGroupCondition")
    public BaseOutput delGroupCondition(@RequestBody CustomerGroupConditionEntity groupConditionEntity) {
        return customerGroupConditionService.delGroupCondition(groupConditionEntity);
    }

    //获取一个分群类型下的分群条件
    @PostMapping(value = "/getManyGroupCondition")
    public BaseOutput getManyGroupCondition(@RequestParam(value = "Cg_Id", required = false) Integer Cg_Id) {
        return customerGroupConditionService.getManyGroupCondition(Cg_Id);
    }

    //修改分群条件下的数值
    @PostMapping(value = "/updateGroupCondition")
    public BaseOutput updateGroupCondition(@RequestBody List<CustomerGroupConditionEntity> list) {
        for (int a = 0; a < list.size(); a++) {
            customerGroupConditionService.updateGroupCondition(list.get(a));
        }
        return BaseOutput.success();
    }


}
