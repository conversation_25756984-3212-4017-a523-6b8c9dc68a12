package com.zhelian.api.ibeauty.saleBill;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.FastSaleGoodsQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleGoodsQueryForm;
import com.zhelian.service.ibeauty.saleGoods.SaleGoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class FastSaleGoodsController {

    @Autowired
    SaleGoodsService saleGoodsService;

    @RequestMapping(value = "/fastSaleGoods/all", method = {RequestMethod.POST})
    public BaseOutput allFastSaleGoods(@RequestBody FastSaleGoodsQueryForm form) {
        if (form.Name == null) {
            return BaseOutput.failed("卡项名称搜索不能为空");
        } else {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.allFastSaleGoods(form);
    }

    @RequestMapping(value = "/fastSaleGoods/goodsType", method = {RequestMethod.POST})
    public BaseOutput fastSaleGoodsType(@RequestBody FastSaleGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.fastGoodsType(form);
    }

    @RequestMapping(value = "/fastSaleGoods/goods", method = {RequestMethod.POST})
    public BaseOutput fastSaleGoods(@RequestBody SaleGoodsQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleGoodsService.goods(form);
    }
}