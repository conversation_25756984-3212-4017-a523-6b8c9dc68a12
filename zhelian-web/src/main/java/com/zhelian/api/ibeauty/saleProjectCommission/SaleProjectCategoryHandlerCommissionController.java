package com.zhelian.api.ibeauty.saleProjectCommission;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.service.ibeauty.saleProjectCommission.SaleProjectCategoryHandlerCommissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


/**
 * @Description:
 * @Param:
 * @returns:
 * @Author: 86158
 * @Date: 2020/4/2 13:26
 */
@RestController
@RequestMapping(value = "/api")
public class SaleProjectCategoryHandlerCommissionController {

    @Autowired
    SaleProjectCategoryHandlerCommissionService saleProjectCategoryHandlerCommissionService;

    @RequestMapping(value = "/saleProjectCategoryHandlerCommission/all", method = {RequestMethod.POST})
    public BaseOutput allSaleProjectCategoryHandlerCommission(@RequestBody SaleProjectCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return saleProjectCategoryHandlerCommissionService.allSaleProjectCategoryHandlerCommission(form);
    }

    @RequestMapping(value = "/saleProjectCategoryHandlerCommission/packageCard", method = {RequestMethod.POST})
    public BaseOutput allSalePakageCardProjectCategoryHandlerCommission(@RequestBody SaleProjectCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return saleProjectCategoryHandlerCommissionService.allSalePakageCardProjectCategoryHandlerCommission(form);
    }

    @RequestMapping(value = "/saleProjectCategoryHandlerCommission/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleProjectCategoryHandlerCommission(@RequestBody SaleProjectCategoryHandlerCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return saleProjectCategoryHandlerCommissionService.updateSaleProjectCategoryHandlerCommission(form);
    }


    static boolean checkRate(SaleProjectCategoryHandlerCommissionAddForm form) {
        if (form.ProjectHandler != null) {
            for (SaleProjectCategoryHandlerAddForm handlerAddForm : form.ProjectHandler) {

                if ((handlerAddForm.PayRate != null && handlerAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerAddForm.SavingCardRate != null && handlerAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerAddForm.SavingCardLargessRate != null && handlerAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                    return false;

                for (SaleProjectCategoryHandlerJobTypeCommissionAddForm jobTypeCommissionAddForm : handlerAddForm.HandlerJobTypeCommission) {

                    if ((jobTypeCommissionAddForm.PayRate != null && jobTypeCommissionAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypeCommissionAddForm.SavingCardRate != null && jobTypeCommissionAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypeCommissionAddForm.SavingCardLargessRate != null && jobTypeCommissionAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                        return false;
                }
            }
        }
        return true;
    }
}

