package com.zhelian.api.ibeauty.timeCard;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TimeCardCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.TimeCardCategoryMoveForm;
import com.zhelian.model.ibeauty.form.input.TimeCardCategoryQueryForm;
import com.zhelian.model.ibeauty.form.input.TimeCardCategoryUpdateForm;
import com.zhelian.service.ibeauty.timeCard.TimeCardCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description:
 * @Param:
 * @returns:
 * @Author: xuehao
 * @Date: 2020/3/18 15:29
 */
@RestController
@RequestMapping(value = "/api")
public class TimeCardCategoryController {

    @Autowired
    TimeCardCategoryService timeCardCategoryService;

    @RequestMapping(value = "/timeCardCategory/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody TimeCardCategoryQueryForm form) {
        return timeCardCategoryService.timeCardCategoryList(form);
    }

    @RequestMapping(value = "/timeCardCategory/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody TimeCardCategoryAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("时效卡分类名称不能为空");

        return timeCardCategoryService.createTimeCardCategory(form);
    }

    @RequestMapping(value = "/timeCardCategory/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody TimeCardCategoryUpdateForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("时效卡分类名称不能为空");
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (form.Active == null)
            return BaseOutput.failed("有效性不能为空");

        return timeCardCategoryService.updateTimeCardCategory(form);
    }

    @RequestMapping(value = "/timeCardCategory/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody TimeCardCategoryMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");

        return timeCardCategoryService.move(form);
    }
}