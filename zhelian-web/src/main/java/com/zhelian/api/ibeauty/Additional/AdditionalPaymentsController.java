package com.zhelian.api.ibeauty.Additional;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingOrIncomeOrPaymentsCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingOrIncomeOrPaymentsCategoryQueryForm;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingOrIncomeOrPaymentsCategoryUpdateForm;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingOrIncomeOrPaymentsMoveForm;
import com.zhelian.service.ibeauty.Additional.AdditionalPaymentsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @className: AdditionalPaymentsController
 * @description:
 * @version: V1.0
 * @author: zhoutianxiang
 * @date: 2020/4/25 4:36 PM
 */
@RestController
@RequestMapping(value = "/api")
public class AdditionalPaymentsController {

    @Autowired
    AdditionalPaymentsService additionalPaymentsService;

    @RequestMapping(value = "/additionalPayments/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody AdditionalSpendingOrIncomeOrPaymentsCategoryQueryForm form) {
        return additionalPaymentsService.all(form);
    }

    @RequestMapping(value = "/additionalPayments/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody AdditionalSpendingOrIncomeOrPaymentsCategoryAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("支付方式名称不能为空");

        return additionalPaymentsService.create(form);
    }

    @RequestMapping(value = "/additionalPayments/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody AdditionalSpendingOrIncomeOrPaymentsCategoryUpdateForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("支付方式名称不能为空");
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (form.Active == null)
            return BaseOutput.failed("有效性不能为空");

        return additionalPaymentsService.update(form);
    }

    @RequestMapping(value = "/additionalPayments/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody AdditionalSpendingOrIncomeOrPaymentsMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");
        return additionalPaymentsService.move(form);
    }

    @RequestMapping(value = "/additionalPayments/getAdditionalPayments", method = {RequestMethod.POST})
    public BaseOutput all() {
        return additionalPaymentsService.getAdditionalPayments();
    }
}