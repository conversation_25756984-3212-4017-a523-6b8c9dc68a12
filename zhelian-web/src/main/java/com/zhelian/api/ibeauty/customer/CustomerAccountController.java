package com.zhelian.api.ibeauty.customer;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.service.ibeauty.customer.CustomerAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api/customerAccount")
public class CustomerAccountController {

    @Autowired
    CustomerAccountService customerAccountService;

    @RequestMapping(value = "/productAccount", method = {RequestMethod.POST})
    public BaseOutput productAccount(@RequestBody CustomerAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        if (form.Active == null)
            return BaseOutput.failed("是否可使用不能为空");
        return customerAccountService.productAccount(form);
    }

    @RequestMapping(value = "/projectAccount", method = {RequestMethod.POST})
    public BaseOutput projectAccount(@RequestBody CustomerAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        if (form.Active == null)
            return BaseOutput.failed("是否可使用不能为空");
        return customerAccountService.projectAccount(form);
    }

    @RequestMapping(value = "/generalCardAccount", method = {RequestMethod.POST})
    public BaseOutput generalCardAccount(@RequestBody CustomerAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        if (form.Active == null)
            return BaseOutput.failed("是否可使用不能为空");
        return customerAccountService.generalCardAccount(form);
    }

    @RequestMapping(value = "/timeCardAccount", method = {RequestMethod.POST})
    public BaseOutput timeCardAccount(@RequestBody CustomerAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        if (form.Active == null)
            return BaseOutput.failed("是否可使用不能为空");
        return customerAccountService.timeCardAccount(form);
    }

    @RequestMapping(value = "/savingCardAccount", method = {RequestMethod.POST})
    public BaseOutput savingCardAccount(@RequestBody CustomerAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        if (form.Active == null)
            return BaseOutput.failed("是否可使用不能为空");
        return customerAccountService.savingCardAccount(form);
    }

    @RequestMapping(value = "/packageCardAccount", method = {RequestMethod.POST})
    public BaseOutput packageCardAccount(@RequestBody CustomerAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        if (form.Active == null)
            return BaseOutput.failed("是否可使用不能为空");
        return customerAccountService.packageCardAccount(form);
    }

    @RequestMapping(value = "/createProjectAccountArrearAmountProcess", method = {RequestMethod.POST})
    public BaseOutput createProjectAccountArrearAmountProcess(@RequestBody CustomerArrearAmountProcessAddForm form) {
        if (form.ProjectAccountID == null)
            return BaseOutput.failed("项目账户不能为空");
        if (form.Amount == null)
            return BaseOutput.failed("平账金额不能为空");
        if (form.Quantity == null)
            return BaseOutput.failed("平账数量不能为空");
        return customerAccountService.createProjectAccountArrearAmountProcess(form);
    }

    @RequestMapping(value = "/createProductAccountArrearAmountProcess", method = {RequestMethod.POST})
    public BaseOutput createProductAccountArrearAmountProcess(@RequestBody CustomerProductArrearAmountProcessAddForm form) {
        if (form.ProductAccountID == null)
            return BaseOutput.failed("产品账户不能为空");
        if (form.Amount == null)
            return BaseOutput.failed("平账金额不能为空");
        if (form.Quantity == null)
            return BaseOutput.failed("平账数量不能为空");
        return customerAccountService.createProductAccountArrearAmountProcess(form);
    }

    @RequestMapping(value = "/createGeneralCardAccountArrearAmountProcess", method = {RequestMethod.POST})
    public BaseOutput createGeneralCardAccountArrearAmountProcess(@RequestBody CustomerGeneralCardArrearAmountProcessAddForm form) {
        if (form.GeneralCardAccountID == null)
            return BaseOutput.failed("通用次卡账户不能为空");
        if (form.Amount == null)
            return BaseOutput.failed("平账金额不能为空");
        if (form.Quantity == null)
            return BaseOutput.failed("平账数量不能为空");
        return customerAccountService.createGeneralCardAccountArrearAmountProcess(form);
    }

    @RequestMapping(value = "/createTimeCardAccountArrearAmountProcess", method = {RequestMethod.POST})
    public BaseOutput createTimeCardAccountArrearAmountProcess(@RequestBody CustomerTimeCardArrearAmountProcessAddForm form) {
        if (form.TimeCardAccountID == null)
            return BaseOutput.failed("时效卡账户不能为空");
        if (form.Amount == null)
            return BaseOutput.failed("平账金额不能为空");
        return customerAccountService.createTimeCardAccountArrearAmountProcess(form);
    }

    @RequestMapping(value = "/createSavingCardAccountArrearAmountProcess", method = {RequestMethod.POST})
    public BaseOutput createSavingCardAccountArrearAmountProcess(@RequestBody CustomerSavingCardArrearAmountProcessAddForm form) {
        if (form.SavingCardAccountID == null)
            return BaseOutput.failed("储值卡账户不能为空");
        if (form.Amount == null)
            return BaseOutput.failed("平账金额不能为空");
        return customerAccountService.createSavingCardAccountArrearAmountProcess(form);
    }

    @RequestMapping(value = "/packageCardAccountDetails", method = {RequestMethod.POST})
    public BaseOutput packageCardAccountDetails(@RequestBody CustomerPackageCardAccountQueryForm form) {
        if (form.PackageCardAccountID == null)
            return BaseOutput.failed("套餐卡账户编号不能为空");
        return customerAccountService.packageCardAccountDetails(form);
    }

    @RequestMapping(value = "/rechargeSavingCardAccount", method = {RequestMethod.POST})
    public BaseOutput rechargeSavingCardAccount(@RequestBody CustomerAccountQueryForm form) {

        return customerAccountService.rechargeSavingCardAccount(form);
    }
}