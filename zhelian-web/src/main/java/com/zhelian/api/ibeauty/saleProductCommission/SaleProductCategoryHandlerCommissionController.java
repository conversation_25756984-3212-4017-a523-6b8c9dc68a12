package com.zhelian.api.ibeauty.saleProductCommission;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.service.ibeauty.saleProductCommission.SaleProductCategoryHandlerCommissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * @Description:
 * @Param:
 * @returns:
 * @Author: 86158
 * @Date: 2020/4/2 13:26
 */
@RestController
@RequestMapping(value = "/api")
public class SaleProductCategoryHandlerCommissionController {

    @Autowired
    SaleProductCategoryHandlerCommissionService saleProductCategoryHandlerCommissionService;

    @RequestMapping(value = "/saleProductCategoryHandlerCommission/all", method = {RequestMethod.POST})
    public BaseOutput allSaleProductCategoryHandlerCommission(@RequestBody SaleProductCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return saleProductCategoryHandlerCommissionService.allSaleProductCategoryHandlerCommission(form);
    }

    @RequestMapping(value = "/saleProductCategoryHandlerCommission/packageCard", method = {RequestMethod.POST})
    public BaseOutput allSalePackageCardProductCategoryHandlerCommission(@RequestBody SaleProductCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return saleProductCategoryHandlerCommissionService.allSalePackageCardProductCategoryHandlerCommission(form);
    }

    @RequestMapping(value = "/saleProductCategoryHandlerCommission/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleProductCategoryHandlerCommission(@RequestBody SaleProductCategoryHandlerCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return saleProductCategoryHandlerCommissionService.updateSaleProductCategoryHandlerCommission(form);
    }


    static boolean checkRate(SaleProductCategoryHandlerCommissionAddForm form) {
        if (form.ProductHandler != null) {
            for (SaleProductCategoryHandlerAddForm handlerAddForm : form.ProductHandler) {

                if ((handlerAddForm.PayRate != null && handlerAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerAddForm.SavingCardRate != null && handlerAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerAddForm.SavingCardLargessRate != null && handlerAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                    return false;

                for (SaleProductCategoryHandlerJobTypeCommissionAddForm jobTypeCommissionAddForm : handlerAddForm.HandlerJobTypeCommission) {

                    if ((jobTypeCommissionAddForm.PayRate != null && jobTypeCommissionAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypeCommissionAddForm.SavingCardRate != null && jobTypeCommissionAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypeCommissionAddForm.SavingCardLargessRate != null && jobTypeCommissionAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                        return false;
                }
            }
        }
        return true;
    }
}

