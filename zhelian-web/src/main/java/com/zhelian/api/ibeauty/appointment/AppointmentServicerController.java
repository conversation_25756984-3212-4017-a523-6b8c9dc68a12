package com.zhelian.api.ibeauty.appointment;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.AppointmentServicerAddForm;
import com.zhelian.model.ibeauty.form.input.AppointmentServicerMoveForm;
import com.zhelian.model.ibeauty.form.input.AppointmentServicerQueryForm;
import com.zhelian.model.ibeauty.form.input.AppointmentServicerUpdateForm;
import com.zhelian.service.ibeauty.appointment.AppointmentServicerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/appointmentServicer")
public class AppointmentServicerController {

    @Autowired
    AppointmentServicerService appointmentServicerService;

    @PostMapping(value = "/all")
    public BaseOutput all(@RequestBody AppointmentServicerQueryForm form) {
        return appointmentServicerService.all(form);
    }

    @PostMapping(value = "/detail")
    public BaseOutput detail(@RequestBody AppointmentServicerQueryForm form) {
        return appointmentServicerService.detail(form);
    }

    @PostMapping(value = "/create")
    public BaseOutput create(@RequestBody AppointmentServicerAddForm form) {
        if (!StringUtils.hasText(form.Name)) {
            return BaseOutput.failed("可预约角色名称不可为空");
        }
        return appointmentServicerService.create(form);
    }

    @PostMapping(value = "/update")
    public BaseOutput update(@RequestBody AppointmentServicerUpdateForm form) {
        if (form.ID == null) {
            return BaseOutput.failed("可预约角色ID不可为空");
        }
        if (!StringUtils.hasText(form.Name)) {
            return BaseOutput.failed("可预约角色名称不可为空");
        }
        return appointmentServicerService.update(form);
    }

    @PostMapping(value = "/move")
    public BaseOutput move(@RequestBody AppointmentServicerMoveForm form) {
        return appointmentServicerService.move(form);
    }

    @PostMapping(value = "/listByEntity")
    public BaseOutput listByEntity() {
        return appointmentServicerService.appointmentServicerByEntity();
    }

}
