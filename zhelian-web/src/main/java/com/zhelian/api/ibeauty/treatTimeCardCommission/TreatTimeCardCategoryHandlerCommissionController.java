package com.zhelian.api.ibeauty.treatTimeCardCommission;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatGeneralCardCategoryHandlerAddForm;
import com.zhelian.service.ibeauty.treatTimeCardCommission.TreatTimeCardCategoryHandlerCommissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class TreatTimeCardCategoryHandlerCommissionController {

    @Autowired
    TreatTimeCardCategoryHandlerCommissionService treatTimeCardCategoryHandlerCommissionService;

    static boolean checkRate(TreatCardCategoryHandlerCommissionAddForm form) {
        for (TreatGeneralCardCategoryHandlerAddForm handlerCommissionAddForm : form.CardHandler) {
            if ((handlerCommissionAddForm.PayRate != null && handlerCommissionAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerCommissionAddForm.CardRate != null && handlerCommissionAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerCommissionAddForm.CardLargessRate != null && handlerCommissionAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerCommissionAddForm.LargessRate != null && handlerCommissionAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
            )
                return false;

            for (TreatCardCategoryHandlerJobTypeCommissionAddForm typeCommissionAddForm : handlerCommissionAddForm.HandlerJobTypeCommission) {
                if ((typeCommissionAddForm.PayRate != null && typeCommissionAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (typeCommissionAddForm.CardRate != null && typeCommissionAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                        || (typeCommissionAddForm.CardLargessRate != null && typeCommissionAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                        || (typeCommissionAddForm.LargessRate != null && typeCommissionAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
                )
                    return false;
            }
        }
        return true;
    }

    @RequestMapping(value = "/treatTimeCardCategoryHandlerCommission/all", method = {RequestMethod.POST})
    public BaseOutput allTreatTimeCardCategoryHandlerCommission(@RequestBody TreatCardCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return treatTimeCardCategoryHandlerCommissionService.allTreatTimeCardCategoryHandlerCommission(form);
    }

    @RequestMapping(value = "/treatTimeCardCategoryHandlerCommission/update", method = {RequestMethod.POST})
    public BaseOutput updateTreatTimeCardCategoryHandlerCommission(@RequestBody TreatCardCategoryHandlerCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatTimeCardCategoryHandlerCommissionService.updateTreatTimeCardCategoryHandlerCommission(form);
    }
}

