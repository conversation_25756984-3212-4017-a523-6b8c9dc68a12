package com.zhelian.api.ibeauty.saleProductCommission;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductsCommissionAddForm;
import com.zhelian.service.ibeauty.saleProductCommission.SaleProductCommissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * @Description:
 * @Param:
 * @returns:
 * @Author: 86158
 * @Date: 2020/4/2 13:26
 */
@RestController
@RequestMapping(value = "/api")
public class SaleProductCommissionController {

    @Autowired
    SaleProductCommissionService saleProductCommissionService;

    @RequestMapping(value = "/saleProductCommission/all", method = {RequestMethod.POST})
    public BaseOutput allSaleProductCommission(@RequestBody SaleProductCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return saleProductCommissionService.allSaleProductCommission(form);
    }

    @RequestMapping(value = "/saleProductCommission/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleProductCommission(@RequestBody SaleProductsCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return saleProductCommissionService.updateSaleProductCommission(form);
    }

    static boolean checkRate(SaleProductsCommissionAddForm form) {

        for (SaleProductCommissionAddForm saleProductCommissionAddForm : form.Product) {

            if ((saleProductCommissionAddForm.PayRate != null && saleProductCommissionAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (saleProductCommissionAddForm.SavingCardRate != null && saleProductCommissionAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                    || (saleProductCommissionAddForm.SavingCardLargessRate != null && saleProductCommissionAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                return false;
        }
        return true;
    }
}

