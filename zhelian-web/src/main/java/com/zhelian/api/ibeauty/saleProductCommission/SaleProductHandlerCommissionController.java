package com.zhelian.api.ibeauty.saleProductCommission;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProductHandlersCommissionAddForm;
import com.zhelian.service.ibeauty.saleProductCommission.SaleProductHandlerCommissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * @Description:
 * @Param:
 * @returns:
 * @Author: 86158
 * @Date: 2020/4/2 13:26
 */
@RestController
@RequestMapping(value = "/api")
public class SaleProductHandlerCommissionController {

    @Autowired
    SaleProductHandlerCommissionService saleProductHandlerCommissionService;


    @RequestMapping(value = "/saleProductHandlerCommission/all", method = {RequestMethod.POST})
    public BaseOutput allSaleProductHandlerCommission(@RequestBody SaleProductHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.ProductID == null)
            return BaseOutput.failed("产品编号不能为空");
        return saleProductHandlerCommissionService.allSaleProductHandlerCommission(form);
    }

    @RequestMapping(value = "/saleProductHandlerCommission/packageCard", method = {RequestMethod.POST})
    public BaseOutput allSalePackageCardProductHandlerCommission(@RequestBody SaleProductHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.ProductID == null)
            return BaseOutput.failed("产品编号不能为空");
        return saleProductHandlerCommissionService.allSalePackageCardProductHandlerCommission(form);
    }

    @RequestMapping(value = "/saleProductHandlerCommission/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleProductHandlerCommission(@RequestBody SaleProductHandlersCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.ProductID == null)
            return BaseOutput.failed("产品编号不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return saleProductHandlerCommissionService.updateSaleProductHandlerCommission(form);
    }

    static boolean checkRate(SaleProductHandlersCommissionAddForm form) {
        if (form.ProductHandler != null) {
            for (SaleProductHandlerCommissionAddForm handlerCommissionAddForm : form.ProductHandler) {

                if ((handlerCommissionAddForm.PayRate != null && handlerCommissionAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerCommissionAddForm.SavingCardRate != null && handlerCommissionAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerCommissionAddForm.SavingCardLargessRate != null && handlerCommissionAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                    return false;

                for (SaleProductCategoryHandlerJobTypeCommissionAddForm jobTypeCommissionAddForm : handlerCommissionAddForm.HandlerJobTypeCommission) {


                    if ((jobTypeCommissionAddForm.PayRate != null && jobTypeCommissionAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypeCommissionAddForm.SavingCardRate != null && jobTypeCommissionAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypeCommissionAddForm.SavingCardLargessRate != null && jobTypeCommissionAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                        return false;
                }

            }
        }
        return true;
    }
}

