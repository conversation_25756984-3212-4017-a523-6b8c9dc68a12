package com.zhelian.api.ibeauty.appointment;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.AppointmentProjectAddForm;
import com.zhelian.model.ibeauty.form.input.AppointmentProjectQueryForm;
import com.zhelian.service.ibeauty.appointment.AppointmentProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/appointmentProject")
public class AppointmentProjectController {

    @Autowired
    AppointmentProjectService appointmentProjectService;

    @PostMapping(value = "/all")
    public BaseOutput all(@RequestBody AppointmentProjectQueryForm form) {
        return appointmentProjectService.all(form);
    }

    @PostMapping(value = "/config")
    public BaseOutput config(@RequestBody AppointmentProjectAddForm form) {
        return appointmentProjectService.config(form);
    }

    @PostMapping(value = "/findCategoryAndProject")
    public BaseOutput findCategoryAndProject() {
        return appointmentProjectService.findCategoryAndProject();
    }
}
