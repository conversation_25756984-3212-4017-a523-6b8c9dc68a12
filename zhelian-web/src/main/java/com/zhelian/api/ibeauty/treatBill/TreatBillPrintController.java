package com.zhelian.api.ibeauty.treatBill;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatBillInfoQueryForm;
import com.zhelian.service.ibeauty.treatBill.TreatBillPrintService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
public class TreatBillPrintController {

    @Autowired
    TreatBillPrintService treatBillPrintService;


    @RequestMapping(value = "/treatBill/printTreatBillContent", method = {RequestMethod.POST})
    public BaseOutput getTreatBill(@RequestBody TreatBillInfoQueryForm form) {
        return treatBillPrintService.getTreatBill(form);
    }

    @RequestMapping(value = "/treatBill/printRefundTreatBillContent", method = {RequestMethod.POST})
    public BaseOutput getRefundTreatBill(@RequestBody TreatBillInfoQueryForm form) {
        return treatBillPrintService.getRefundTreatBill(form);
    }
}
