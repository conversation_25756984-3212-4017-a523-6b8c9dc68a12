package com.zhelian.api.ibeauty.customer;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.entity.CustomerEmployeeEntity;
import com.zhelian.model.khs.entity.EmployeeEntity;
import com.zhelian.service.ibeauty.customer.CustomerEmployeeService;
import com.zhelian.service.khs.employee.EmployeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value = "/api/customerEmployee")
public class CustomerEmployeeController {

    @Autowired
    CustomerEmployeeService customerEmployeeService;
    @Autowired
    EmployeeService employeeService;

    //会员批量分配员工
    @PostMapping(value = "/addCustomerEmployee")
    public BaseOutput addCustomerEmployee(@RequestBody List<CustomerEmployeeEntity> list) {
        if (list.size() >= 1) {
            String employeeId = list.get(0).EmployeeID;//员工id
            EmployeeEntity employeeEntity = employeeService.getEmployeeByID(employeeId);
            if (employeeEntity.State == false) {
                customerEmployeeService.delEmployeeId(employeeId);
                return BaseOutput.failed("该员工已经离职");
            } else {
                for (int i = 0; i < list.size(); i++) {
                    CustomerEmployeeEntity customerEmployee = list.get(i);
                    customerEmployeeService.addCustomerEmployeeId(customerEmployee);
                }
            }
        } else {
            return BaseOutput.failed("请选择会员进行分配!");
        }
        return BaseOutput.success();
    }


}
