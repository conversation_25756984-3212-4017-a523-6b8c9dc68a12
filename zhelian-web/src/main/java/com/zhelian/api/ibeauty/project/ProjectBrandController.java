package com.zhelian.api.ibeauty.project;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.ProjectBrandAddForm;
import com.zhelian.model.ibeauty.form.input.ProjectBrandMoveForm;
import com.zhelian.model.ibeauty.form.input.ProjectBrandQueryForm;
import com.zhelian.model.ibeauty.form.input.ProjectBrandUpdateForm;
import com.zhelian.service.ibeauty.project.ProjectBrandService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @className: ProjectBrandController
 * @description:
 * @version: V1.0
 * @author: xue<PERSON>
 * @date: 2021/3/26 2:11 PM
 */
@RestController
@RequestMapping(value = "/api")
public class ProjectBrandController {

    @Autowired
    ProjectBrandService projectBrandService;

    @RequestMapping(value = "/projectBrand/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody ProjectBrandQueryForm form) {
        return projectBrandService.projectBrandList(form);
    }


    @RequestMapping(value = "/projectBrand/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody ProjectBrandAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("项目品牌名称不能为空");

        return projectBrandService.createProjectBrand(form);
    }

    @RequestMapping(value = "/projectBrand/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody ProjectBrandUpdateForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("项目品牌名称不能为空");
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (form.Active == null)
            return BaseOutput.failed("有效性不能为空");

        return projectBrandService.updateProjectBrand(form);
    }

    @RequestMapping(value = "/projectBrand/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody ProjectBrandMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");

        return projectBrandService.move(form);
    }
}