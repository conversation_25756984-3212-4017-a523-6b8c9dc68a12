package com.zhelian.api.ibeauty.saleBill;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleCustomerQueryForm;
import com.zhelian.service.ibeauty.saleCustomer.SaleCustomerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class SaleCustomerController {

    @Autowired
    SaleCustomerService saleCustomerService;

    @RequestMapping(value = "/saleCustomer/customer", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody SaleCustomerQueryForm form) {
        if (form.Name != null) {
            form.Name = form.Name.trim();
        }
        return saleCustomerService.saleCustomer(form);
    }

}