package com.zhelian.api.ibeauty.padShow;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.pad.form.input.GoodsDisplayGoodsAddForm;
import com.zhelian.model.pad.form.input.GoodsDisplayQueryForm;
import com.zhelian.model.pad.form.input.GoodsDisplayUpdateForm;
import com.zhelian.service.ibeauty.padShow.GoodsDisplayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api/goodsDisplay")
public class GoodsDisplayController {

    @Autowired
    GoodsDisplayService goodsDisplayService;

    @RequestMapping(value = "/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody GoodsDisplayQueryForm form) {
        return goodsDisplayService.allGoodsDisplay(form);
    }

    @RequestMapping(value = "/memo", method = {RequestMethod.POST})
    public BaseOutput memo(@RequestBody GoodsDisplayQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("编号不能为空");
        return goodsDisplayService.getMemo(form);
    }

    @RequestMapping(value = "/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody GoodsDisplayGoodsAddForm form) {
        if (form.CategoryID == null)
            return BaseOutput.failed("分类不能为空");
        if (form.Name == null)
            return BaseOutput.failed("名称不能为空");
        return goodsDisplayService.createGoodsDisplay(form);
    }

    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody GoodsDisplayUpdateForm form) {
        if (form.CategoryID == null)
            return BaseOutput.failed("分类不能为空");
        if (form.Name == null)
            return BaseOutput.failed("名称不能为空");
        return goodsDisplayService.updateGoodsDisplay(form);
    }

    @RequestMapping(value = "/delete", method = {RequestMethod.POST})
    public BaseOutput delete(@RequestBody GoodsDisplayUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("编号不能为空");
        return goodsDisplayService.delete(form);
    }

    @RequestMapping(value = "/list", method = {RequestMethod.POST})
    public BaseOutput list() {
        return goodsDisplayService.list();
    }
}