package com.zhelian.api.ibeauty.saleSavingCardCommission;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardSchemeHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardSchemeHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardSchemeHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardsSchemeHandlerCommissionAddForm;
import com.zhelian.service.ibeauty.saleSavingCardCommission.SaleSavingCardSchemeHandlerCommissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * @Description:
 * @Param:
 * @returns:
 * @Author: 86158
 * @Date: 2020/4/2 13:26
 */
@RestController
@RequestMapping(value = "/api")
public class SaleSavingCardSchemeHandlerCommissionController {

    @Autowired
    SaleSavingCardSchemeHandlerCommissionService saleSavingCardSchemeHandlerCommissionService;


    @RequestMapping(value = "/saleSavingCardSchemeHandlerCommission/all", method = {RequestMethod.POST})
    public BaseOutput allSaleProSchemeHandlerCommission(@RequestBody SaleSavingCardSchemeHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");

        return saleSavingCardSchemeHandlerCommissionService.allSaleSavingCardSchemeHandlerCommission(form);
    }

    @RequestMapping(value = "/saleSavingCardSchemeHandlerCommission/packageCard", method = {RequestMethod.POST})
    public BaseOutput allSalePackageCardSavingCardSchemeHandlerCommission(@RequestBody SaleSavingCardSchemeHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");

        return saleSavingCardSchemeHandlerCommissionService.allSalePackageCardSavingCardSchemeHandlerCommission(form);
    }

    @RequestMapping(value = "/saleSavingCardSchemeHandlerCommission/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleSavingCardSchemeHandlerCommission(@RequestBody SaleSavingCardsSchemeHandlerCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return saleSavingCardSchemeHandlerCommissionService.updateSaleSavingCardSchemeHandlerCommission(form);
    }

    static boolean checkRate(SaleSavingCardsSchemeHandlerCommissionAddForm form) {
        if (form.SavingCardHandler != null) {
            for (SaleSavingCardSchemeHandlerCommissionAddForm handlerCommissionAddForm : form.SavingCardHandler) {

                if ((handlerCommissionAddForm.PayRate != null && handlerCommissionAddForm.PayRate.compareTo(new BigDecimal(100)) > 0))
                    return false;

                for (SaleSavingCardSchemeHandlerJobTypeCommissionAddForm jobTypeCommissionAddForm : handlerCommissionAddForm.HandlerJobTypeCommission) {


                    if (jobTypeCommissionAddForm.PayRate != null && jobTypeCommissionAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        return false;
                }
            }
        }
        return true;
    }
}

