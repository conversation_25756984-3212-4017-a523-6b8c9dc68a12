package com.zhelian.api.ibeauty.customer;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.entity.CustomerGroupEntity;
import com.zhelian.service.ibeauty.customer.CustomerGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.Date;

@RestController
@RequestMapping(value = "/api/CustomerGroup")
public class CustomerGroupController {

    @Autowired
    CustomerGroupService groupService;

    //会员分群信息的新增
    @PostMapping(value = "/addGroup")
    public BaseOutput addGroup(@RequestBody CustomerGroupEntity group) {
        if (group.groupDefine == null) {
            return BaseOutput.failed("人群定义不能为空!");
        } else if (group.founders == null) {
            return BaseOutput.failed("创建人不能为空!");
        } else if (group.groupName == null) {
            return BaseOutput.failed("人群名称不能为空!");
        } else {
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            group.creatTime = df.format(new Date());
            groupService.addCustomerGroup(group);
            return BaseOutput.success();
        }
    }

    //会员分群信息的更新
    @PostMapping(value = "/updateGroup")
    public BaseOutput updateGroup(@RequestBody CustomerGroupEntity group) {
        groupService.updateCustomerGroup(group);
        return BaseOutput.success();
    }


    //会员分群信息的删除
    @PostMapping(value = "/delGroup")
    public BaseOutput delCustomerGroup(@RequestParam String groupName) {
        groupService.delCustomerGroup(groupName);
        return BaseOutput.success();
    }


    //会员分群信息查询
    @PostMapping(value = "getGroup")
    public BaseOutput getTodayCustomer(@RequestBody CustomerGroupEntity group) {
        return groupService.getGroupList(group);
    }


}
