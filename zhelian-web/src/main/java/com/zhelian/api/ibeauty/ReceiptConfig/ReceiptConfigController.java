package com.zhelian.api.ibeauty.ReceiptConfig;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.ReceiptConfigAddForm;
import com.zhelian.service.ibeauty.ReceiptConfig.ReceiptConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class ReceiptConfigController {

    @Autowired
    ReceiptConfigService receiptConfigService;

    @RequestMapping(value = "/receiptConfig/all", method = {RequestMethod.POST})
    public BaseOutput all() {
        return receiptConfigService.receiptConfigAll();
    }


    @RequestMapping(value = "/receiptConfig/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody ReceiptConfigAddForm form) {
        if (form.PrintWidth == null)
            return BaseOutput.failed("打印宽度不能为空");
        if (form.PrintQuantity == null)
            return BaseOutput.failed("打印数量不能为空");
        if (form.NameEncrypt == null)
            return BaseOutput.failed("客户姓名加密不能为空");
        if (form.MobileEncrypt == null)
            return BaseOutput.failed("客户手机号不能为空");
        if (form.SaleGoodsOriginPrice == null)
            return BaseOutput.failed("销售-商品原价不能为空");
        if (form.SalePromotions == null)
            return BaseOutput.failed("销售-手动改价和卡优惠不能为空");
        if (form.SaleTotalAmount == null)
            return BaseOutput.failed("销售-合计不能为空");
        if (form.SaleRefundBuyQuantity == null)
            return BaseOutput.failed("销售退款-购买数量不能为空");
        if (form.SaleRefundBuyAmount == null)
            return BaseOutput.failed("销售退款-购买金额不能为空");
        if (form.SaleRefundableAmount == null)
            return BaseOutput.failed("销售退款-可退金额不能为空");
        if (form.SaleArrearBuyQuantity == null)
            return BaseOutput.failed("销售补尾款-购买数量不能为空");
        if (form.SaleArrearBuyAmount == null)
            return BaseOutput.failed("销售补尾款-购买金额不能为空");
        if (form.TreatGoodsOriginPrice == null)
            return BaseOutput.failed("消耗-商品原价不能为空");
        if (form.TreatPromotions == null)
            return BaseOutput.failed("消耗-手动改价和卡优惠不能为空");
        if (form.TreatAmount == null)
            return BaseOutput.failed("消耗-消耗金额不能为空");

        return receiptConfigService.createReceiptConfig(form);
    }


    @RequestMapping(value = "/receiptConfig/bill", method = {RequestMethod.POST})
    public BaseOutput bill() {
        return receiptConfigService.receiptConfigBill();
    }
}