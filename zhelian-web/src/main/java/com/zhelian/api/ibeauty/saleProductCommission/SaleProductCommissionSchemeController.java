package com.zhelian.api.ibeauty.saleProductCommission;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionSchemeDeleteForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionSchemeQueryForm;
import com.zhelian.service.ibeauty.saleProductCommission.SaleProductCommissionSchemeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description:
 * @Param:
 * @returns:
 * @Author: 86158
 * @Date: 2020/4/2 13:26
 */
@RestController
@RequestMapping(value = "/api")
public class SaleProductCommissionSchemeController {

    @Autowired
    SaleProductCommissionSchemeService saleProductCommissionSchemeService;

    @RequestMapping(value = "/saleProductCommissionScheme/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody SaleProductCommissionSchemeQueryForm form) {
        return saleProductCommissionSchemeService.saleProductCommissionSchemeList(form);
    }

    @RequestMapping(value = "/saleProductCommissionScheme/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody SaleProductCommissionSchemeAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return saleProductCommissionSchemeService.createSaleProductCommissionScheme(form);
    }

    @RequestMapping(value = "/saleProductCommissionScheme/delete", method = {RequestMethod.POST})
    public BaseOutput delete(@RequestBody SaleProductCommissionSchemeDeleteForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return saleProductCommissionSchemeService.deleteSaleProductCommissionScheme(form);
    }
}

