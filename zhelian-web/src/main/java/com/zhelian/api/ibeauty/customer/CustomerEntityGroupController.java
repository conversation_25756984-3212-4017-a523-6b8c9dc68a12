package com.zhelian.api.ibeauty.customer;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.service.ibeauty.customer.CustomerEntityGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * @Description: 获取当前门店下的所有会员分群信息
 * @author: zhanglongyang
 * @date: 2021/05/19
 * @time: 14:55
 */
@RestController
@RequestMapping(value = "/api/CustomerEntityGroup")
public class CustomerEntityGroupController {

    @Autowired
    CustomerEntityGroupService customerEntityGroupService;


    //获取当前门店的所有会员分群信息
    @PostMapping(value = "/getAllCustomerGroup")
    public BaseOutput getCustomerEntityGroup(@RequestBody Map map) {
        return customerEntityGroupService.getCustomerEntityGroup(map);

    }


    //获取一个分群类型下的分群条件。
    @PostMapping(value = "/getGroupRule")
    public BaseOutput getCustomerEntityGroup(@RequestParam(value = "Cg_Id", required = false) Integer Cg_Id) {
//        customerEntityGroupService.getGroupRule(Cg_Id);

        return null;
    }

}
