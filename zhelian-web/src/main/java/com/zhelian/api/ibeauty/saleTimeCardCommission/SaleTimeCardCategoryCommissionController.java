package com.zhelian.api.ibeauty.saleTimeCardCommission;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCommissionSchemeAddForm;
import com.zhelian.service.ibeauty.saleTimeCardCommission.SaleTimeCardCategoryCommissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * @Description:
 * @Param:
 * @returns:
 * @Author: 86158
 * @Date: 2020/4/2 13:26
 */
@RestController
@RequestMapping(value = "/api")
public class SaleTimeCardCategoryCommissionController {

    @Autowired
    SaleTimeCardCategoryCommissionService saleTimeCardCategoryCommissionService;


    @RequestMapping(value = "/saleTimeCardCategoryCommission/all", method = {RequestMethod.POST})
    public BaseOutput allSaleTimeCardCategoryCommission(@RequestBody SaleTimeCardCommissionSchemeAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return saleTimeCardCategoryCommissionService.allSaleTimeCardCategoryCommission(form);
    }

    @RequestMapping(value = "/saleTimeCardCategoryCommission/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleTimeCardCategoryCommission(@RequestBody SaleTimeCardCategoryAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");

        return saleTimeCardCategoryCommissionService.updateSaleTimeCardCategoryCommission(form);
    }

    static boolean checkRate(SaleTimeCardCategoryAddForm form) {

        for (SaleTimeCardCategoryCommissionAddForm cardCategoryCommissionAddForm : form.Category) {

            if ((cardCategoryCommissionAddForm.PayRate != null && cardCategoryCommissionAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (cardCategoryCommissionAddForm.SavingCardRate != null && cardCategoryCommissionAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                    || (cardCategoryCommissionAddForm.SavingCardLargessRate != null && cardCategoryCommissionAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                return false;
        }
        return true;
    }
}

