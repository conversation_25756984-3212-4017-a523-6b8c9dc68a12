package com.zhelian.api.ibeauty.Additional;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.AdditionalIncomeAddForm;
import com.zhelian.model.ibeauty.form.input.AdditionalIncomeQueryForm;
import com.zhelian.model.ibeauty.form.input.AdditionalIncomeUpdateForm;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingUpdateForm;
import com.zhelian.service.ibeauty.Additional.AdditionalIncomeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;

/**
 * 功能描述:
 * <>
 *
 * @return:
 * @exception:
 * @author: zhoutianxiang
 * @date: 2020/4/26 10:38 AM
 */
@RestController
@RequestMapping(value = "/api")
public class AdditionalIncomeController {

    @Autowired
    AdditionalIncomeService additionalIncomeService;

    @RequestMapping(value = "/additionalIncome/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody AdditionalIncomeQueryForm form) {
        return additionalIncomeService.additionalIncomeList(form);
    }

    @RequestMapping(value = "/additionalIncome/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody AdditionalIncomeAddForm form) {
        if (form.IncomeCategoryID == null) {
            return BaseOutput.failed("收入类型不能为空");
        }
        if (form.EntityID == null) {
            return BaseOutput.failed("所属门店不能为空");
        }
        if (form.InComeDate == null) {
            return BaseOutput.failed("收款日期不能为空");
        }
        if (form.PaymentsID == null) {
            return BaseOutput.failed("收款方式不能为空");
        }
        if (form.Amount == null || form.Amount.compareTo(BigDecimal.ZERO) == 0) {
            return BaseOutput.failed("金额不能为空");
        }
        if (!StringUtils.hasText(form.IncomeSource))
            return BaseOutput.failed("收入来源不能为空");
        return additionalIncomeService.insertAdditionalIncome(form);
    }

    @RequestMapping(value = "/additionalIncome/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody AdditionalIncomeUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("编号不能为空");
        if (form.IncomeCategoryID == null) {
            return BaseOutput.failed("支出类型不能为空");
        }
        if (form.EntityID == null) {
            return BaseOutput.failed("所属门店不能为空");
        }
        if (form.InComeDate == null) {
            return BaseOutput.failed("付款日期不能为空");
        }
        if (form.PaymentsID == null) {
            return BaseOutput.failed("收款方式不能为空");
        }
        if (form.Amount == null || form.Amount.compareTo(BigDecimal.ZERO) == 0) {
            return BaseOutput.failed("金额不能为空");
        }
        if (!StringUtils.hasText(form.IncomeSource))
            return BaseOutput.failed("收入来源不能为空");

        return additionalIncomeService.updateAdditionalIncome(form);
    }

    @RequestMapping(value = "/additionalIncome/delete", method = {RequestMethod.POST})
    public BaseOutput delete(@RequestBody AdditionalSpendingUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        return additionalIncomeService.deleteAdditionalIncomeByID(form);
    }

    @RequestMapping(value = "/additionalIncome/getAttachment", method = {RequestMethod.POST})
    public BaseOutput getAttachment(@RequestBody AdditionalSpendingUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        return additionalIncomeService.getAttachment(form);
    }

    @RequestMapping(value = "/additionalIncome/excel", method = {RequestMethod.POST})
    public BaseOutput easyExcel(@RequestBody AdditionalIncomeQueryForm form, HttpServletResponse response) {
        return additionalIncomeService.easyExcelAdditionalIncomeList(form, response);
    }
}