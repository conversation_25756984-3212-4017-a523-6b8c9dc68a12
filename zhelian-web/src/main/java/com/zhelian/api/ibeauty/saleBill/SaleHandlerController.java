package com.zhelian.api.ibeauty.saleBill;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.ibeauty.form.input.SaleAllHandlerForm;
import com.zhelian.model.ibeauty.form.output.SaleAllHandlerOutputForm;
import com.zhelian.service.ibeauty.saleHandler.SaleHandlerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class SaleHandlerController {

    @Autowired
    SaleHandlerService saleHandlerService;

    @RequestMapping(value = "/saleHandler/productHandler", method = {RequestMethod.POST})
    public BaseOutput productHandler() {
        return saleHandlerService.productHandler();
    }

    @RequestMapping(value = "/saleHandler/projectHandler", method = {RequestMethod.POST})
    public BaseOutput projectHandler() {
        return saleHandlerService.projectHandler();
    }

    @RequestMapping(value = "/saleHandler/generalCardHandler", method = {RequestMethod.POST})
    public BaseOutput generalCardHandler() {
        return saleHandlerService.generalCardHandler();
    }

    @RequestMapping(value = "/saleHandler/timeCardHandler", method = {RequestMethod.POST})
    public BaseOutput timeCardHandler() {
        return saleHandlerService.timeCardHandler();
    }

    @RequestMapping(value = "/saleHandler/savingCardHandler", method = {RequestMethod.POST})
    public BaseOutput savingCardHandler() {
        return saleHandlerService.savingCardHandler();
    }

    @RequestMapping(value = "/saleHandler/packageCardHandler", method = {RequestMethod.POST})
    public BaseOutput packageCardHandler() {
        return saleHandlerService.packageCardHandler();
    }

    @RequestMapping(value = "/saleHandler/allHandler", method = {RequestMethod.POST})
    public BaseOutput allHandler(@RequestBody SaleAllHandlerForm form) {
        if (form.GoodTypes == null || form.GoodTypes.size() <= 0){
            BaseOutputForm.failed("缺少商品类型");
        }
        return saleHandlerService.allHandler(form.GoodTypes);
    }
}