package com.zhelian.api.ibeauty.arrear;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.ArrearAccountQueryForm;
import com.zhelian.model.ibeauty.form.input.ArrearPackageCardAccountQueryForm;
import com.zhelian.model.ibeauty.form.input.ArrearTimeAccountQueryForm;
import com.zhelian.service.ibeauty.arrear.ArrearAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class ArrearAccountController {

    @Autowired
    ArrearAccountService arrearAccountService;

    @RequestMapping(value = "/arrearAccount/projectAccount", method = {RequestMethod.POST})
    public BaseOutput projectAccount(@RequestBody ArrearAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        return arrearAccountService.projectAccount(form);
    }

    @RequestMapping(value = "/arrearAccount/productAccount", method = {RequestMethod.POST})
    public BaseOutput productAccount(@RequestBody ArrearAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        return arrearAccountService.productAccount(form);
    }

    @RequestMapping(value = "/arrearAccount/savingCardAccount", method = {RequestMethod.POST})
    public BaseOutput savingCardAccount(@RequestBody ArrearAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        return arrearAccountService.savingCardAccount(form);
    }

    @RequestMapping(value = "/arrearAccount/generalCardAccount", method = {RequestMethod.POST})
    public BaseOutput generalCardAccount(@RequestBody ArrearAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        return arrearAccountService.generalCardAccount(form);
    }

    @RequestMapping(value = "/arrearAccount/timeCardAccount", method = {RequestMethod.POST})
    public BaseOutput timeCardAccount(@RequestBody ArrearTimeAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        if (form.BillDate == null)
            return BaseOutput.failed("开单时间不能为空");
        return arrearAccountService.timeCardAccount(form);
    }

    @RequestMapping(value = "/arrearAccount/packageCardAccount", method = {RequestMethod.POST})
    public BaseOutput packageCardAccount(@RequestBody ArrearAccountQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        return arrearAccountService.packageCardAccount(form);
    }

    @RequestMapping(value = "/arrearAccount/packageCardAccountDetails", method = {RequestMethod.POST})
    public BaseOutput packageCardAccountDetails(@RequestBody ArrearPackageCardAccountQueryForm form) {
        if (form.PackageCardAccountID == null)
            return BaseOutput.failed("套餐卡账户编号不能为空");
        if (form.BillDate == null)
            return BaseOutput.failed("开单时间不能为空");
        return arrearAccountService.packageCardAccountDetails(form);
    }

}