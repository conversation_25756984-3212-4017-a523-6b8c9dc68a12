package com.zhelian.api.ibeauty.treatSavingCardCommission;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectSchemeHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectSchemeHandlersCommissionAddForm;
import com.zhelian.service.ibeauty.treatSavingCardCommission.TreatSavingCardSchemeHandlerCommissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

@RestController
@RequestMapping(value = "/api")
public class TreatSavingCardSchemeHandlerCommissionController {

    @Autowired
    TreatSavingCardSchemeHandlerCommissionService treatSavingCardSchemeHandlerCommissionService;

    static boolean checkRate(TreatSavingCardProjectSchemeHandlersCommissionAddForm form) {

        for (TreatSavingCardProjectHandlerCommissionAddForm handlerCommissionAddForm : form.Handler) {

            if ((handlerCommissionAddForm.Rate != null && handlerCommissionAddForm.Rate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerCommissionAddForm.LargessRate != null && handlerCommissionAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0))
                return false;

            for (TreatSavingCardProjectCategoryHandlerJobTypeCommissionAddForm jobTypeCommissionAddForm : handlerCommissionAddForm.HandlerJobTypeCommission) {

                if ((jobTypeCommissionAddForm.Rate != null && jobTypeCommissionAddForm.Rate.compareTo(new BigDecimal(100)) > 0)
                        || (jobTypeCommissionAddForm.LargessRate != null && jobTypeCommissionAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0))
                    return false;
            }
        }
        return true;
    }

    @RequestMapping(value = "/treatSavingCardSchemeHandlerCommission/all", method = {RequestMethod.POST})
    public BaseOutput allTreatSavingCardSchemeHandlerCommission(@RequestBody TreatSavingCardProjectSchemeHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");

        return treatSavingCardSchemeHandlerCommissionService.allTreatSavingCardSchemeHandlerCommission(form);
    }

    @RequestMapping(value = "/treatSavingCardSchemeHandlerCommission/update", method = {RequestMethod.POST})
    public BaseOutput updateTreatSavingCardSchemeHandlerCommission(@RequestBody TreatSavingCardProjectSchemeHandlersCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatSavingCardSchemeHandlerCommissionService.updateTreatSchemeHandlerCommission(form);
    }
}

