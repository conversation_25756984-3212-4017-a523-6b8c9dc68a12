/**
 * fileName: PayMethodController.java
 * author: <PERSON><PERSON>ian<PERSON><PERSON>
 * date: 2020/3/4 4:36 PM
 * copyright(c) 2019-2020 xxx公司
 */
package com.zhelian.api.ibeauty.paymethod;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.service.ibeauty.paymethod.PayMethodService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @className: PayMethodController
 * @description:
 * @version: V1.0
 * @author: WangXianXian
 * @date: 2020/3/4 4:36 PM
 */
@RestController
@RequestMapping(value = "/api")
public class PayMethodController {

    @Autowired
    PayMethodService payMethodService;

    @RequestMapping(value = "/paymethod/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody PayMethodQueryForm form) {
        return payMethodService.all(form);
    }

    @RequestMapping(value = "/paymethod/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody PayMethodAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("支付方式名称不能为空");

        return payMethodService.create(form);
    }

    @RequestMapping(value = "/paymethod/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody PayMethodUpdateForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("支付方式名称不能为空");
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (form.Active == null)
            return BaseOutput.failed("有效性不能为空");

        return payMethodService.update(form);
    }

    @RequestMapping(value = "/paymethod/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody PayMethodMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");
        return payMethodService.move(form);
    }

    @RequestMapping(value = "/paymethod/paymethodEntity", method = {RequestMethod.POST})
    public BaseOutput paymethodEntity(@RequestBody ProjectSaleEntityQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        return payMethodService.paymethodEntity(form);
    }
}