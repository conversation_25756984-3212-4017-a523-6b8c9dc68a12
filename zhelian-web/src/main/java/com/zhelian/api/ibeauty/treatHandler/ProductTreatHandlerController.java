package com.zhelian.api.ibeauty.treatHandler;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleHandlerMoveForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerUpdateForm;
import com.zhelian.model.ibeauty.form.input.TreatHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.TreatHandlerQueryForm;
import com.zhelian.service.ibeauty.treatHandler.ProductTreatHandlerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class ProductTreatHandlerController {

    @Autowired
    ProductTreatHandlerService productTreatHandlerService;

    @RequestMapping(value = "/productTreatHandler/all", method = {RequestMethod.POST})
    public BaseOutput allProductTreatHandler(@RequestBody TreatHandlerQueryForm form) {
        return productTreatHandlerService.allProductTreatHandler(form);
    }

    @RequestMapping(value = "/productTreatHandler/create", method = {RequestMethod.POST})
    public BaseOutput createProductTreatHandler(@RequestBody TreatHandlerAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("经手人名称不能为空");
        if (form.EntityID == null)
            return BaseOutput.failed("门店ID不能为空");
        if (form.JobType == null || form.JobType.length == 0)
            return BaseOutput.failed("适用职务不能为空");


        return productTreatHandlerService.createProductTreatHandler(form);
    }

    @RequestMapping(value = "/productTreatHandler/update", method = {RequestMethod.POST})
    public BaseOutput updateProductTreatHandler(@RequestBody SaleHandlerUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("经手人名称不能为空");
        if (form.EntityID == null)
            return BaseOutput.failed("门店ID不能为空");
        if (form.Active == null)
            return BaseOutput.failed("是否有效不能为空");
        if (form.JobType == null || form.JobType.length == 0)
            return BaseOutput.failed("适用职务不能为空");


        return productTreatHandlerService.updateProductTreatHandler(form);
    }

    @RequestMapping(value = "/productTreatHandler/move", method = {RequestMethod.POST})
    public BaseOutput moveProductTreatHandler(@RequestBody SaleHandlerMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");
        return productTreatHandlerService.moveProductTreatHandler(form);
    }
}