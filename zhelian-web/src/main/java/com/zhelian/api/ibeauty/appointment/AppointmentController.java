package com.zhelian.api.ibeauty.appointment;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.service.ibeauty.arrear.AppointmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping(value = "/api/appointment")
public class AppointmentController {

    @Autowired
    AppointmentService appointmentService;

    @RequestMapping(value = "/config", method = {RequestMethod.POST})
    public BaseOutput config() {
        return appointmentService.appointmentConfig();
    }

    @RequestMapping(value = "/updateConfig", method = {RequestMethod.POST})
    public BaseOutput updateConfig(@RequestBody AppointmentConfigUpdateForm form) {
        return appointmentService.updateAppointmentConfig(form);
    }

    @RequestMapping(value = "/schedule", method = {RequestMethod.POST})
    public BaseOutput schedule() {
        return appointmentService.schedule();
    }

    @RequestMapping(value = "/createSchedule", method = {RequestMethod.POST})
    public BaseOutput createSchedule(@RequestBody AppointmentScheduleAddForm form) {
        return appointmentService.createSchedule(form);
    }

    @RequestMapping(value = "/updateSchedule", method = {RequestMethod.POST})
    public BaseOutput updateSchedule(@RequestBody AppointmentScheduleUpdateForm form) {
        return appointmentService.updateSchedule(form);
    }

    @RequestMapping(value = "/deleteSchedule", method = {RequestMethod.POST})
    public BaseOutput deleteSchedule(@RequestBody AppointmentScheduleUpdateForm form) {
        return appointmentService.deleteSchedule(form);
    }

    @RequestMapping(value = "/employeeSchedule", method = {RequestMethod.POST})
    public BaseOutput employeeSchedule(@RequestBody AppointmentEmployeeScheduleQueryForm form) {
        return appointmentService.employeeSchedule(form);
    }

    @RequestMapping(value = "/updateEmployeeSchedule", method = {RequestMethod.POST})
    public BaseOutput updateEmployeeSchedule(@RequestBody List<AppointmentEmployeeScheduleUpdateForm> form) throws Exception {
        return appointmentService.updateEmployeeSchedule(form);
    }

    @RequestMapping(value = "/findCategoryAndProject", method = {RequestMethod.POST})
    public BaseOutput findCategoryAndProject(@RequestBody AppointmentBillProjectQueryForm form) {
        return appointmentService.findCategoryAndProject(form);
    }

}