package com.zhelian.api.ibeauty.treatBill;


import com.zhelian.core.utils.enumeration.BillChannel;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.miniprogram.form.input.SaleBillSignatureAddForm;
import com.zhelian.service.ibeauty.treatBill.TreatBillCommonService;
import com.zhelian.service.ibeauty.treatBill.TreatBillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api/treatBill")
public class TreatBillController {

    @Autowired
    TreatBillService treatBillService;
    @Autowired
    TreatBillCommonService treatBillCommonService;

    @RequestMapping(value = "/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody TreatBillAddForm form) throws Exception {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");

        if (form.BillDate == null)
            return BaseOutput.failed("订单时间不能为空");
        BigDecimal TotalAmount = BigDecimal.ZERO;

        for (TreatBillProjectAddForm projectAddForm : form.Project) {
            TotalAmount = TotalAmount.add(projectAddForm.TotalAmount);
        }
        for (TreatBillProductAddForm productAddForm : form.Product) {
            TotalAmount = TotalAmount.add(productAddForm.TotalAmount);
        }
        for (TreatBillGeneralCardAddForm generalCardAddForm : form.GeneralCard) {
            TotalAmount = TotalAmount.add(generalCardAddForm.TotalAmount);
        }
        for (TreatBillTimeCardAddForm timeCardAddForm : form.TimeCard) {
            TotalAmount = TotalAmount.add(timeCardAddForm.TotalAmount);
        }
        for (TreatBillSavingCardAddForm savingCardAddForm : form.SavingCard) {
            TotalAmount = TotalAmount.add(savingCardAddForm.TotalAmount);

            if (savingCardAddForm.TotalAmount.compareTo(savingCardAddForm.Price.multiply(BigDecimal.valueOf(savingCardAddForm.Quantity)).subtract(savingCardAddForm.CardPreferentialAmount).subtract(savingCardAddForm.PricePreferentialAmount).subtract(savingCardAddForm.MemberPreferentialAmount)) != 0) {
                return BaseOutput.failed("储值卡消耗金额不正确");
            }
        }
        if (TotalAmount.compareTo(form.Amount) != 0) {
            return BaseOutput.failed("订单金额不正确");
        }

        return treatBillCommonService.create(form, false, BillChannel.BillChannelPC.getID());
    }

    @RequestMapping(value = "/list", method = {RequestMethod.POST})
    public BaseOutput treatBillList(@RequestBody TreatBillQueryForm form) {
        return treatBillService.treatBillList(form);
    }

    @RequestMapping(value = "/info", method = {RequestMethod.POST})
    public BaseOutput treatBillInfo(@RequestBody TreatBillInfoQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("订单编号不能为空");
        return treatBillService.treatBillInfo(form);
    }

    @RequestMapping(value = "/employeePerformanceCommission", method = {RequestMethod.POST})
    public BaseOutput treatBillEmployeePerformanceCommission(@RequestBody TreatBillInfoQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("订单编号不能为空");
        return treatBillService.getTreatBillEmployeePerformanceCommission(form);
    }

    @RequestMapping(value = "/updateEmployeePerformanceCommission", method = {RequestMethod.POST})
    public BaseOutput updateEmployeePerformanceCommission(@RequestBody TreatBillPerformanceCommissionUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("订单编号不能为空");
        if (!checkPeroformance(form))
            return BaseOutput.failed("业绩比例不能为空");
        return treatBillService.updateTreatBillEmployeePerformanceCommission(form);
    }

    @RequestMapping(value = "/cancel", method = {RequestMethod.POST})
    public BaseOutput cancel(@RequestBody TreatBillInfoQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("订单编号不能为空");
        return treatBillCommonService.cancelTreatBill(form);
    }

    static boolean checkPeroformance(TreatBillPerformanceCommissionUpdateForm form) {
        for (TreatBillProductPerformanceCommissionUpdateForm treatBillProductPerformanceCommissionUpdateForm : form.Product) {
            for (TreatBillHandlerUpdateForm treatBillHandlerUpdateForm : treatBillProductPerformanceCommissionUpdateForm.TreatBillHandler) {
                for (TreatBillHandlerEmployeeUpdateForm treatBillHandlerEmployeeUpdateForm : treatBillHandlerUpdateForm.Employee) {
                    if (treatBillHandlerEmployeeUpdateForm.Scale == null || treatBillHandlerEmployeeUpdateForm.Scale.equals("") || treatBillHandlerEmployeeUpdateForm.PayPerformance == null || treatBillHandlerEmployeeUpdateForm.PayRateCommission == null
                            || treatBillHandlerEmployeeUpdateForm.PayFixedCommission == null || treatBillHandlerEmployeeUpdateForm.CardPerformance == null || treatBillHandlerEmployeeUpdateForm.CardRateCommission == null
                            || treatBillHandlerEmployeeUpdateForm.CardFixedCommission == null || treatBillHandlerEmployeeUpdateForm.CardLargessPerformance == null
                            || treatBillHandlerEmployeeUpdateForm.CardLargessRateCommission == null || treatBillHandlerEmployeeUpdateForm.CardLargessFixedCommission == null
                            || treatBillHandlerEmployeeUpdateForm.LargessPerformance == null || treatBillHandlerEmployeeUpdateForm.LargessRateCommission == null || treatBillHandlerEmployeeUpdateForm.LargessFixedCommission == null
                            || treatBillHandlerEmployeeUpdateForm.SpecialBenefitCommission == null
                    )
                        return false;
                }
            }
        }

        for (TreatBillProjectPerformanceCommissionUpdateForm treatBillProductPerformanceCommissionUpdateForm : form.Project) {
            for (TreatBillHandlerUpdateForm treatBillHandlerUpdateForm : treatBillProductPerformanceCommissionUpdateForm.TreatBillHandler) {
                for (TreatBillHandlerEmployeeUpdateForm treatBillHandlerEmployeeUpdateForm : treatBillHandlerUpdateForm.Employee) {
                    if (treatBillHandlerEmployeeUpdateForm.Scale == null || treatBillHandlerEmployeeUpdateForm.Scale.equals("") || treatBillHandlerEmployeeUpdateForm.PayPerformance == null || treatBillHandlerEmployeeUpdateForm.PayRateCommission == null
                            || treatBillHandlerEmployeeUpdateForm.PayFixedCommission == null || treatBillHandlerEmployeeUpdateForm.CardPerformance == null || treatBillHandlerEmployeeUpdateForm.CardRateCommission == null
                            || treatBillHandlerEmployeeUpdateForm.CardFixedCommission == null || treatBillHandlerEmployeeUpdateForm.CardLargessPerformance == null
                            || treatBillHandlerEmployeeUpdateForm.CardLargessRateCommission == null || treatBillHandlerEmployeeUpdateForm.CardLargessFixedCommission == null
                            || treatBillHandlerEmployeeUpdateForm.LargessPerformance == null || treatBillHandlerEmployeeUpdateForm.LargessRateCommission == null || treatBillHandlerEmployeeUpdateForm.LargessFixedCommission == null
                            || treatBillHandlerEmployeeUpdateForm.SpecialBenefitCommission == null
                    )
                        return false;
                }
            }
        }

        for (TreatBillGeneralCardPerformanceCommissionUpdateForm treatBillProductPerformanceCommissionUpdateForm : form.GeneralCard) {
            for (TreatBillGeneralCardProjectUpdateForm treatBillGeneralCardProjectUpdateForm : treatBillProductPerformanceCommissionUpdateForm.Project) {
                for (TreatBillHandlerUpdateForm treatBillHandlerUpdateForm : treatBillGeneralCardProjectUpdateForm.TreatBillHandler) {
                    for (TreatBillHandlerEmployeeUpdateForm treatBillHandlerEmployeeUpdateForm : treatBillHandlerUpdateForm.Employee) {
                        if (treatBillHandlerEmployeeUpdateForm.Scale == null || treatBillHandlerEmployeeUpdateForm.Scale.equals("") || treatBillHandlerEmployeeUpdateForm.PayPerformance == null || treatBillHandlerEmployeeUpdateForm.PayRateCommission == null
                                || treatBillHandlerEmployeeUpdateForm.PayFixedCommission == null || treatBillHandlerEmployeeUpdateForm.CardPerformance == null || treatBillHandlerEmployeeUpdateForm.CardRateCommission == null
                                || treatBillHandlerEmployeeUpdateForm.CardFixedCommission == null || treatBillHandlerEmployeeUpdateForm.CardLargessPerformance == null
                                || treatBillHandlerEmployeeUpdateForm.CardLargessRateCommission == null || treatBillHandlerEmployeeUpdateForm.CardLargessFixedCommission == null
                                || treatBillHandlerEmployeeUpdateForm.LargessPerformance == null || treatBillHandlerEmployeeUpdateForm.LargessRateCommission == null || treatBillHandlerEmployeeUpdateForm.LargessFixedCommission == null
                                || treatBillHandlerEmployeeUpdateForm.SpecialBenefitCommission == null
                        )
                            return false;
                    }
                }
            }
        }

        for (TreatBillTimeCardPerformanceCommissionUpdateForm treatBillProductPerformanceCommissionUpdateForm : form.TimeCard) {
            for (TreatBillTimeCardProjectUpdateForm treatBillGeneralCardProjectUpdateForm : treatBillProductPerformanceCommissionUpdateForm.Project) {
                for (TreatBillHandlerUpdateForm treatBillHandlerUpdateForm : treatBillGeneralCardProjectUpdateForm.TreatBillHandler) {
                    for (TreatBillHandlerEmployeeUpdateForm treatBillHandlerEmployeeUpdateForm : treatBillHandlerUpdateForm.Employee) {
                        if (treatBillHandlerEmployeeUpdateForm.Scale == null || treatBillHandlerEmployeeUpdateForm.Scale.equals("") || treatBillHandlerEmployeeUpdateForm.PayPerformance == null || treatBillHandlerEmployeeUpdateForm.PayRateCommission == null
                                || treatBillHandlerEmployeeUpdateForm.PayFixedCommission == null || treatBillHandlerEmployeeUpdateForm.CardPerformance == null || treatBillHandlerEmployeeUpdateForm.CardRateCommission == null
                                || treatBillHandlerEmployeeUpdateForm.CardFixedCommission == null || treatBillHandlerEmployeeUpdateForm.CardLargessPerformance == null
                                || treatBillHandlerEmployeeUpdateForm.CardLargessRateCommission == null || treatBillHandlerEmployeeUpdateForm.CardLargessFixedCommission == null
                                || treatBillHandlerEmployeeUpdateForm.LargessPerformance == null || treatBillHandlerEmployeeUpdateForm.LargessRateCommission == null || treatBillHandlerEmployeeUpdateForm.LargessFixedCommission == null
                                || treatBillHandlerEmployeeUpdateForm.SpecialBenefitCommission == null
                        )
                            return false;
                    }
                }
            }
        }

        for (TreatBillSavingCardPerformanceCommissionUpdateForm treatBillProductPerformanceCommissionUpdateForm : form.SavingCard) {
            for (TreatBillSavingCardProjectUpdateForm treatBillGeneralCardProjectUpdateForm : treatBillProductPerformanceCommissionUpdateForm.Project) {
                for (TreatBillHandlerUpdateForm treatBillHandlerUpdateForm : treatBillGeneralCardProjectUpdateForm.TreatBillHandler) {
                    for (TreatBillHandlerEmployeeUpdateForm treatBillHandlerEmployeeUpdateForm : treatBillHandlerUpdateForm.Employee) {
                        if (treatBillHandlerEmployeeUpdateForm.Scale == null || treatBillHandlerEmployeeUpdateForm.Scale.equals("") || treatBillHandlerEmployeeUpdateForm.CardPerformance == null || treatBillHandlerEmployeeUpdateForm.CardRateCommission == null
                                || treatBillHandlerEmployeeUpdateForm.CardFixedCommission == null
                                || treatBillHandlerEmployeeUpdateForm.CardLargessPerformance == null || treatBillHandlerEmployeeUpdateForm.CardLargessRateCommission == null || treatBillHandlerEmployeeUpdateForm.CardLargessFixedCommission == null
                                || treatBillHandlerEmployeeUpdateForm.SpecialBenefitCommission == null
                        )
                            return false;
                    }
                }
            }
        }

        for (TreatBillPackageCardPerformanceCommissionUpdateForm treatBillPackageCardPerformanceCommissionUpdateForm : form.PackageCard) {
            for (TreatBillProductPerformanceCommissionUpdateForm treatBillProductPerformanceCommissionUpdateForm : treatBillPackageCardPerformanceCommissionUpdateForm.Product) {
                for (TreatBillHandlerUpdateForm treatBillHandlerUpdateForm : treatBillProductPerformanceCommissionUpdateForm.TreatBillHandler) {
                    for (TreatBillHandlerEmployeeUpdateForm treatBillHandlerEmployeeUpdateForm : treatBillHandlerUpdateForm.Employee) {
                        if (treatBillHandlerEmployeeUpdateForm.Scale == null || treatBillHandlerEmployeeUpdateForm.Scale.equals("") || treatBillHandlerEmployeeUpdateForm.PayPerformance == null || treatBillHandlerEmployeeUpdateForm.PayRateCommission == null
                                || treatBillHandlerEmployeeUpdateForm.PayFixedCommission == null || treatBillHandlerEmployeeUpdateForm.CardPerformance == null || treatBillHandlerEmployeeUpdateForm.CardRateCommission == null
                                || treatBillHandlerEmployeeUpdateForm.CardFixedCommission == null || treatBillHandlerEmployeeUpdateForm.CardLargessPerformance == null
                                || treatBillHandlerEmployeeUpdateForm.CardLargessRateCommission == null || treatBillHandlerEmployeeUpdateForm.CardLargessFixedCommission == null
                                || treatBillHandlerEmployeeUpdateForm.LargessPerformance == null || treatBillHandlerEmployeeUpdateForm.LargessRateCommission == null || treatBillHandlerEmployeeUpdateForm.LargessFixedCommission == null
                                || treatBillHandlerEmployeeUpdateForm.SpecialBenefitCommission == null
                        )
                            return false;
                    }
                }
            }

            for (TreatBillProjectPerformanceCommissionUpdateForm treatBillProductPerformanceCommissionUpdateForm : treatBillPackageCardPerformanceCommissionUpdateForm.Project) {
                for (TreatBillHandlerUpdateForm treatBillHandlerUpdateForm : treatBillProductPerformanceCommissionUpdateForm.TreatBillHandler) {
                    for (TreatBillHandlerEmployeeUpdateForm treatBillHandlerEmployeeUpdateForm : treatBillHandlerUpdateForm.Employee) {
                        if (treatBillHandlerEmployeeUpdateForm.Scale == null || treatBillHandlerEmployeeUpdateForm.Scale.equals("") || treatBillHandlerEmployeeUpdateForm.PayPerformance == null || treatBillHandlerEmployeeUpdateForm.PayRateCommission == null
                                || treatBillHandlerEmployeeUpdateForm.PayFixedCommission == null || treatBillHandlerEmployeeUpdateForm.CardPerformance == null || treatBillHandlerEmployeeUpdateForm.CardRateCommission == null
                                || treatBillHandlerEmployeeUpdateForm.CardFixedCommission == null || treatBillHandlerEmployeeUpdateForm.CardLargessPerformance == null
                                || treatBillHandlerEmployeeUpdateForm.CardLargessRateCommission == null || treatBillHandlerEmployeeUpdateForm.CardLargessFixedCommission == null
                                || treatBillHandlerEmployeeUpdateForm.LargessPerformance == null || treatBillHandlerEmployeeUpdateForm.LargessRateCommission == null || treatBillHandlerEmployeeUpdateForm.LargessFixedCommission == null
                                || treatBillHandlerEmployeeUpdateForm.SpecialBenefitCommission == null
                        )
                            return false;
                    }
                }
            }

            for (TreatBillGeneralCardPerformanceCommissionUpdateForm treatBillProductPerformanceCommissionUpdateForm : treatBillPackageCardPerformanceCommissionUpdateForm.GeneralCard) {
                for (TreatBillGeneralCardProjectUpdateForm treatBillGeneralCardProjectUpdateForm : treatBillProductPerformanceCommissionUpdateForm.Project) {
                    for (TreatBillHandlerUpdateForm treatBillHandlerUpdateForm : treatBillGeneralCardProjectUpdateForm.TreatBillHandler) {
                        for (TreatBillHandlerEmployeeUpdateForm treatBillHandlerEmployeeUpdateForm : treatBillHandlerUpdateForm.Employee) {
                            if (treatBillHandlerEmployeeUpdateForm.Scale == null || treatBillHandlerEmployeeUpdateForm.Scale.equals("") || treatBillHandlerEmployeeUpdateForm.PayPerformance == null || treatBillHandlerEmployeeUpdateForm.PayRateCommission == null
                                    || treatBillHandlerEmployeeUpdateForm.PayFixedCommission == null || treatBillHandlerEmployeeUpdateForm.CardPerformance == null || treatBillHandlerEmployeeUpdateForm.CardRateCommission == null
                                    || treatBillHandlerEmployeeUpdateForm.CardFixedCommission == null || treatBillHandlerEmployeeUpdateForm.CardLargessPerformance == null
                                    || treatBillHandlerEmployeeUpdateForm.CardLargessRateCommission == null || treatBillHandlerEmployeeUpdateForm.CardLargessFixedCommission == null
                                    || treatBillHandlerEmployeeUpdateForm.LargessPerformance == null || treatBillHandlerEmployeeUpdateForm.LargessRateCommission == null || treatBillHandlerEmployeeUpdateForm.LargessFixedCommission == null
                                    || treatBillHandlerEmployeeUpdateForm.SpecialBenefitCommission == null
                            )
                                return false;
                        }
                    }
                }
            }

            for (TreatBillTimeCardPerformanceCommissionUpdateForm treatBillProductPerformanceCommissionUpdateForm : treatBillPackageCardPerformanceCommissionUpdateForm.TimeCard) {
                for (TreatBillTimeCardProjectUpdateForm treatBillGeneralCardProjectUpdateForm : treatBillProductPerformanceCommissionUpdateForm.Project) {
                    for (TreatBillHandlerUpdateForm treatBillHandlerUpdateForm : treatBillGeneralCardProjectUpdateForm.TreatBillHandler) {
                        for (TreatBillHandlerEmployeeUpdateForm treatBillHandlerEmployeeUpdateForm : treatBillHandlerUpdateForm.Employee) {
                            if (treatBillHandlerEmployeeUpdateForm.Scale == null || treatBillHandlerEmployeeUpdateForm.Scale.equals("") || treatBillHandlerEmployeeUpdateForm.PayPerformance == null || treatBillHandlerEmployeeUpdateForm.PayRateCommission == null
                                    || treatBillHandlerEmployeeUpdateForm.PayFixedCommission == null || treatBillHandlerEmployeeUpdateForm.CardPerformance == null || treatBillHandlerEmployeeUpdateForm.CardRateCommission == null
                                    || treatBillHandlerEmployeeUpdateForm.CardFixedCommission == null || treatBillHandlerEmployeeUpdateForm.CardLargessPerformance == null
                                    || treatBillHandlerEmployeeUpdateForm.CardLargessRateCommission == null || treatBillHandlerEmployeeUpdateForm.CardLargessFixedCommission == null
                                    || treatBillHandlerEmployeeUpdateForm.LargessPerformance == null || treatBillHandlerEmployeeUpdateForm.LargessRateCommission == null || treatBillHandlerEmployeeUpdateForm.LargessFixedCommission == null
                                    || treatBillHandlerEmployeeUpdateForm.SpecialBenefitCommission == null
                            )
                                return false;
                        }
                    }
                }
            }

            for (TreatBillSavingCardPerformanceCommissionUpdateForm treatBillProductPerformanceCommissionUpdateForm : treatBillPackageCardPerformanceCommissionUpdateForm.SavingCard) {
                for (TreatBillSavingCardProjectUpdateForm treatBillGeneralCardProjectUpdateForm : treatBillProductPerformanceCommissionUpdateForm.Project) {
                    for (TreatBillHandlerUpdateForm treatBillHandlerUpdateForm : treatBillGeneralCardProjectUpdateForm.TreatBillHandler) {
                        for (TreatBillHandlerEmployeeUpdateForm treatBillHandlerEmployeeUpdateForm : treatBillHandlerUpdateForm.Employee) {
                            if (treatBillHandlerEmployeeUpdateForm.Scale == null || treatBillHandlerEmployeeUpdateForm.Scale.equals("") || treatBillHandlerEmployeeUpdateForm.CardPerformance == null || treatBillHandlerEmployeeUpdateForm.CardRateCommission == null
                                    || treatBillHandlerEmployeeUpdateForm.CardFixedCommission == null
                                    || treatBillHandlerEmployeeUpdateForm.CardLargessPerformance == null || treatBillHandlerEmployeeUpdateForm.CardLargessRateCommission == null || treatBillHandlerEmployeeUpdateForm.CardLargessFixedCommission == null
                                    || treatBillHandlerEmployeeUpdateForm.SpecialBenefitCommission == null
                            )
                                return false;
                        }
                    }
                }
            }

        }


        return true;
    }

    @RequestMapping(value = "/updateRemark", method = {RequestMethod.POST})
    public BaseOutput updateRemark(@RequestBody TreatBillRemarkUpdateForm form) {
        if (form.TreatBillID == null)
            return BaseOutput.failed("订单编号不能为空");
        if (form.Remark == null)
            return BaseOutput.failed("备注不能为空");
        return treatBillService.updateRemark(form);
    }

    @RequestMapping(value = "/getSignature", method = {RequestMethod.POST})
    public BaseOutput getSignature(@RequestBody SaleBillSignatureAddForm form) {
        return treatBillService.getSignature(form);
    }

    /**
     * 修改时间
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/updateBillDate", method = {RequestMethod.POST})
    public BaseOutput updateBillDate(@RequestBody SaleBillUpdateDateAddForm form) {
        if (form.BillID == null)
            return BaseOutput.failed("订单编号不能为空");
        if (form.BillDate == null)
            return BaseOutput.failed("订单时间不能为空");

        return treatBillService.updateBillDate(form);
    }

    /**
     * 挂单
     *
     * @param form
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/createPendingOrder", method = {RequestMethod.POST})
    public BaseOutput createPendingOrder(@RequestBody TreatBillAddForm form) throws Exception {
        if (form.BillDate == null)
            return BaseOutput.failed("订单时间不能为空");
        BigDecimal TotalAmount = BigDecimal.ZERO;

        for (TreatBillProjectAddForm projectAddForm : form.Project) {
            TotalAmount = TotalAmount.add(projectAddForm.TotalAmount);
        }
        for (TreatBillProductAddForm productAddForm : form.Product) {
            TotalAmount = TotalAmount.add(productAddForm.TotalAmount);
        }
        for (TreatBillGeneralCardAddForm generalCardAddForm : form.GeneralCard) {
            TotalAmount = TotalAmount.add(generalCardAddForm.TotalAmount);
        }
        for (TreatBillTimeCardAddForm timeCardAddForm : form.TimeCard) {
            TotalAmount = TotalAmount.add(timeCardAddForm.TotalAmount);
        }
        for (TreatBillSavingCardAddForm savingCardAddForm : form.SavingCard) {
            TotalAmount = TotalAmount.add(savingCardAddForm.TotalAmount);

            if (savingCardAddForm.TotalAmount.compareTo(savingCardAddForm.Price.multiply(BigDecimal.valueOf(savingCardAddForm.Quantity)).subtract(savingCardAddForm.CardPreferentialAmount).subtract(savingCardAddForm.PricePreferentialAmount).subtract(savingCardAddForm.MemberPreferentialAmount)) != 0) {
                return BaseOutput.failed("储值卡消耗金额不正确");
            }
        }
        if (TotalAmount.compareTo(form.Amount) != 0) {
            return BaseOutput.failed("订单金额不正确");
        }

        return treatBillService.createPendingOrder(form, BillChannel.BillChannelPC.getID());
    }

    @RequestMapping(value = "/pendingOrderInfo", method = {RequestMethod.POST})
    public BaseOutput pendingOrderInfo(@RequestBody TreatBillInfoQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("订单编号不能为空");
        return treatBillService.pendingOrderInfo(form);
    }

    @RequestMapping(value = "/deletePendingOrder", method = {RequestMethod.POST})
    public BaseOutput deletePendingOrder(@RequestBody TreatBillInfoQueryForm form) {
        return treatBillService.deletePendingOrder(form);
    }

    static boolean checkHandler(CalculateTreatBillEmployeePerformanceCommissionForm form) {
        for (TreatBillHandlerForm treatBillHandlerForm : form.TreatHandler) {
            if (treatBillHandlerForm.TreatHandlerID == null)
                return false;
        }
        return true;
    }

    static boolean checkEmployee(CalculateTreatBillEmployeePerformanceCommissionForm form) {
        for (TreatBillHandlerForm treatBillHandlerForm : form.TreatHandler) {
            if (treatBillHandlerForm.EmployeeID == null)
                return false;
        }
        return true;
    }

    static boolean checkRate(CalculateTreatBillEmployeePerformanceCommissionForm form) {
        for (TreatBillHandlerForm treatBillHandlerForm : form.TreatHandler) {
            if (treatBillHandlerForm.Scale != null && treatBillHandlerForm.Scale.compareTo(new BigDecimal(100)) > 0)
                return false;
        }
        return true;
    }

    @RequestMapping(value = "/employeeCommission", method = {RequestMethod.POST})
    public BaseOutput employeeCommission(@RequestBody CalculateTreatBillEmployeePerformanceCommissionForm form) {
        if (form.TreatBillGoodsID == null)
            return BaseOutput.failed("订单明细编号不能为空");
        if (form.GoodsID == null)
            return BaseOutput.failed("商品编号不能为空");
        if (form.Quantity == null)
            return BaseOutput.failed("数量不能为空");
        if (form.Type == null)
            return BaseOutput.failed("商品类型不能为空");
        if (!checkHandler(form))
            return BaseOutput.failed("经手人编号不能为空");
        if (!checkEmployee(form))
            return BaseOutput.failed("员工编号不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatBillService.calculateEmployeePerformanceCommissionForm(form);
    }

    @RequestMapping(value = "/pendingList", method = {RequestMethod.POST})
    public BaseOutput pendingList(@RequestBody TreatBillQueryForm form) {
        return treatBillService.pendingList(form);
    }

    /**
     * 获取消耗项目适用的模板
     */
    @RequestMapping(value = "/templateByProject", method = {RequestMethod.POST})
    public BaseOutput templateByProject(@RequestBody TemplateByProjectQueryForm form) {
        return treatBillService.templateByProject(form);
    }

    /**
     * 所有耗材
     */
    @RequestMapping(value = "/product", method = {RequestMethod.POST})
    public BaseOutput product(@RequestBody QueryPageForm form) {
        return treatBillService.product(form);
    }


    /**
     * 获取耗材的库存
     */
    @RequestMapping(value = "/productStock", method = {RequestMethod.POST})
    public BaseOutput productStock(@RequestBody TreatBillProductStockQueryForm form) {
        return treatBillService.productStock(form);
    }

    @RequestMapping(value = "/productEntity", method = {RequestMethod.POST})
    public BaseOutput productEntity() {
        return treatBillService.productEntity();
    }

    @RequestMapping(value = "/entityPerformance", method = {RequestMethod.POST})
    public BaseOutput entityPerformanceCommission(@RequestBody TreatBillInfoQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("订单编号不能为空");
        return treatBillService.entityPerformance(form);
    }

    /**
     * 业绩计算
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/calculateEntityPerformance", method = {RequestMethod.POST})
    public BaseOutput calculateEntityPerformance(@RequestBody CalculateTreatBillEntityPerformanceForm form) {
        if (form.GoodsID == null)
            return BaseOutput.failed("商品编号不能为空");
        if (form.Type == null)
            return BaseOutput.failed("商品类型不能为空");
        if (form.CardDeductionAmount == null)
            return BaseOutput.failed("非赠送卡支付金额不能为空");
        if (form.LargessCardDeductionAmount == null)
            return BaseOutput.failed("赠送卡支付金额不能为空");

        return treatBillService.calculateEntityPerformance(form);
    }

    @RequestMapping(value = "/updateEntityPerformance", method = {RequestMethod.POST})
    public BaseOutput updateEntityPerformance(@RequestBody TreatBillEntityPerformanceUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("订单编号不能为空");
        return treatBillService.updateEntityPerformance(form);
    }

    /**
     * 修改明细备注
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/updateProductRemark", method = {RequestMethod.POST})
    public BaseOutput updateProductRemark(@RequestBody TreatBillDetailRemarkUpdateForm form) {
        if (form.TreatBillGoodID == null)
            return BaseOutput.failed("编号不能为空");

        return treatBillService.updateProductRemark(form);
    }

    @RequestMapping(value = "/updateProjectRemark", method = {RequestMethod.POST})
    public BaseOutput updateProjectRemark(@RequestBody TreatBillDetailRemarkUpdateForm form) {
        if (form.TreatBillGoodID == null)
            return BaseOutput.failed("编号不能为空");

        return treatBillService.updateProjectRemark(form);
    }

    @RequestMapping(value = "/updateGeneralCardRemark", method = {RequestMethod.POST})
    public BaseOutput updateGeneralCardRemark(@RequestBody TreatBillDetailRemarkUpdateForm form) {
        if (form.TreatBillGoodID == null)
            return BaseOutput.failed("编号不能为空");

        return treatBillService.updateGeneralCardRemark(form);
    }

    @RequestMapping(value = "/updateTimeCardRemark", method = {RequestMethod.POST})
    public BaseOutput updateTimeCardRemark(@RequestBody TreatBillDetailRemarkUpdateForm form) {
        if (form.TreatBillGoodID == null)
            return BaseOutput.failed("编号不能为空");

        return treatBillService.updateTimeCardRemark(form);
    }

    @RequestMapping(value = "/updateSavingCardRemark", method = {RequestMethod.POST})
    public BaseOutput updateSavingCardRemark(@RequestBody TreatBillDetailRemarkUpdateForm form) {
        if (form.TreatBillGoodID == null)
            return BaseOutput.failed("编号不能为空");

        return treatBillService.updateSavingCardRemark(form);
    }

}