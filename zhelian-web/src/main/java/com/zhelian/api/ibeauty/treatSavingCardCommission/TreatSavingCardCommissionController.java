package com.zhelian.api.ibeauty.treatSavingCardCommission;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardsCommissionAddForm;
import com.zhelian.service.ibeauty.treatSavingCardCommission.TreatSavingCardCommissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class TreatSavingCardCommissionController {

    @Autowired
    TreatSavingCardCommissionService treatSavingCardCommissionService;

    static boolean checkRate(TreatSavingCardsCommissionAddForm form) {

        for (TreatSavingCardCommissionAddForm treatSavingCardCommissionAddForm : form.SavingCard) {

            if ((treatSavingCardCommissionAddForm.Rate != null && treatSavingCardCommissionAddForm.Rate.compareTo(new BigDecimal(100)) > 0)
                    || (treatSavingCardCommissionAddForm.LargessRate != null && treatSavingCardCommissionAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0))
                return false;
        }
        return true;
    }

    @RequestMapping(value = "/treatSavingCardCommission/all", method = {RequestMethod.POST})
    public BaseOutput allTreatSavingCardCommission(@RequestBody TreatSavingCardProjectCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return treatSavingCardCommissionService.allTreatSavingCardCommission(form);
    }

    @RequestMapping(value = "/treatSavingCardCommission/update", method = {RequestMethod.POST})
    public BaseOutput updateTreatSavingCardCommission(@RequestBody TreatSavingCardsCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatSavingCardCommissionService.updateTreatSavingCardCommission(form);
    }
}

