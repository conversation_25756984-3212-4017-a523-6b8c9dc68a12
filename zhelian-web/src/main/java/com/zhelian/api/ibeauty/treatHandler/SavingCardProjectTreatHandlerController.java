package com.zhelian.api.ibeauty.treatHandler;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleHandlerMoveForm;
import com.zhelian.model.ibeauty.form.input.TreatHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.TreatHandlerQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatHandlerUpdateForm;
import com.zhelian.service.ibeauty.treatHandler.SavingCardProjectTreatHandlerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class SavingCardProjectTreatHandlerController {

    @Autowired
    SavingCardProjectTreatHandlerService savingCardProjectTreatHandlerService;

    @RequestMapping(value = "/savingCardProjectTreatHandler/all", method = {RequestMethod.POST})
    public BaseOutput allSavingCardProjectTreatHandler(@RequestBody TreatHandlerQueryForm form) {
        return savingCardProjectTreatHandlerService.allSavingCardProjectTreatHandler(form);
    }

    @RequestMapping(value = "/savingCardProjectTreatHandler/create", method = {RequestMethod.POST})
    public BaseOutput createSavingCardProjectTreatHandler(@RequestBody TreatHandlerAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("经手人名称不能为空");
        if (form.EntityID == null)
            return BaseOutput.failed("门店ID不能为空");
        if (form.JobType == null || form.JobType.length == 0)
            return BaseOutput.failed("适用职务不能为空");

        return savingCardProjectTreatHandlerService.createSavingCardProjectTreatHandler(form);
    }

    @RequestMapping(value = "/savingCardProjectTreatHandler/update", method = {RequestMethod.POST})
    public BaseOutput updateSavingCardProjectTreatHandler(@RequestBody TreatHandlerUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("经手人名称不能为空");
        if (form.EntityID == null)
            return BaseOutput.failed("门店ID不能为空");
        if (form.Active == null)
            return BaseOutput.failed("是否有效不能为空");
        if (form.JobType == null || form.JobType.length == 0)
            return BaseOutput.failed("适用职务不能为空");


        return savingCardProjectTreatHandlerService.updateSavingCardProjectTreatHandler(form);
    }

    @RequestMapping(value = "/savingCardProjectTreatHandler/move", method = {RequestMethod.POST})
    public BaseOutput moveSavingCardProjectTreatHandler(@RequestBody SaleHandlerMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");
        return savingCardProjectTreatHandlerService.moveSavingCardProjectTreatHandler(form);
    }
}