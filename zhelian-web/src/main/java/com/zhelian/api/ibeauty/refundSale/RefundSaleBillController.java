package com.zhelian.api.ibeauty.refundSale;


import com.zhelian.core.utils.enumeration.BillChannel;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.service.ibeauty.refund.RefundSaleBillCommonService;
import com.zhelian.service.ibeauty.refund.RefundSaleBillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
public class RefundSaleBillController {

    @Autowired
    RefundSaleBillService refundBillService;

    @Autowired
    RefundSaleBillCommonService refundSaleBillCommonService;

    @RequestMapping(value = "/refundBill/pay", method = {RequestMethod.POST})
    public BaseOutput refundBillPay(@RequestBody RefundBillPayQueryForm form) {
        if (!checkAccount(form))
            return BaseOutput.failed("账户编号不能为空");
        if (!checkAmount(form))
            return BaseOutput.failed("退款金额不能为空");
        return refundBillService.refundBillPay(form);
    }

    @RequestMapping(value = "/refundBill/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody RefundBillAddForm form) throws Exception {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        if (form.BillDate == null)
            return BaseOutput.failed("开单日期不能为空");
        if (form.TotalAmount == null)
            return BaseOutput.failed("订单金额不能为空");
        if (form.PayAmount == null)
            return BaseOutput.failed("现金不能为空");
        if (form.CardDeductionAmount == null)
            return BaseOutput.failed("储值卡金额不能为空");
        if (form.CardDeductionLargessAmount == null)
            return BaseOutput.failed("储值卡赠送金额不能为空");
        if (!checkTotalAmount(form))
            return BaseOutput.failed("退款金额不能为空");
        if (form.TotalAmount.compareTo(form.PayAmount.add(form.CardDeductionAmount).add(form.CardDeductionLargessAmount)) != 0)
            return BaseOutput.failed("订单金额不正确");
        if (!checkSavingCardDeduction(form))
            return BaseOutput.failed("储值卡金额不能为空");

        return refundBillService.create(form, BillChannel.BillChannelPC.getID());
    }

    @RequestMapping(value = "/refundBill/approvalList", method = {RequestMethod.POST})
    public BaseOutput approvalList(@RequestBody RefundBillQueryForm form) {
        return refundBillService.approvalList(form);
    }

    @RequestMapping(value = "/refundBill/info", method = {RequestMethod.POST})
    public BaseOutput info(@RequestBody RefundBillInfoQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("订单编号不能为空");
        return refundBillService.refundBillInfo(form);
    }

    @RequestMapping(value = "/refundBill/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody RefundBillQueryForm form) {
        return refundBillService.refundBillList(form);
    }

    @RequestMapping(value = "/refundBill/revoke", method = {RequestMethod.POST})
    public BaseOutput revoke(@RequestBody RefundBillInfoQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("退款编号不能为空");
        return refundBillService.revoke(form);
    }

    @RequestMapping(value = "/refundBill/approval", method = {RequestMethod.POST})
    public BaseOutput approval(@RequestBody RefundBillApprovalAddForm form) {
        if (form.ID == null)
            return BaseOutput.failed("退款编号不能为空");
        if (form.ApprovalStatus == null)
            return BaseOutput.failed("退款状态不能为空");
        return refundSaleBillCommonService.approval(form, BillChannel.BillChannelPC.getID());
    }

    @RequestMapping(value = "/refundBill/updatePayMethod", method = {RequestMethod.POST})
    public BaseOutput approval(@RequestBody SaleBillUpdatePayMethodUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("编号不能为空");
        if (form.PayMethodID == null)
            return BaseOutput.failed("支付方式不能为空");
        return refundBillService.updatePayMethod(form);
    }

    static boolean checkSavingCardDeduction(RefundBillAddForm form) {
        for (RefundBillSavingCardDeductionForm refundBillSavingCardDeductionForm : form.SavingCardDeduction) {
            if (refundBillSavingCardDeductionForm.DeductionAmount == null || refundBillSavingCardDeductionForm.LargessDeductionAmount == null)
                return false;
        }

        return true;
    }

    static boolean checkAccount(RefundBillPayQueryForm form) {
        for (RefundBillPayProductForm refundBillPayForm : form.Product) {
            if (refundBillPayForm.ProductAccountID == null)
                return false;
        }
        for (RefundBillPayProjectForm refundBillPayForm : form.Project) {
            if (refundBillPayForm.ProjectAccountID == null)
                return false;
        }
        for (RefundBillPayGeneralCardForm refundBillPayForm : form.GeneralCard) {
            if (refundBillPayForm.GeneralCardAccountID == null)
                return false;
        }
        for (RefundBillPayTimeCardForm refundBillPayForm : form.TimeCard) {
            if (refundBillPayForm.TimeCardAccountID == null)
                return false;
        }
        for (RefundBillPaySavingCardForm refundBillPayForm : form.SavingCard) {
            if (refundBillPayForm.SavingCardAccountID == null)
                return false;
        }
        for (RefundBillPayPackageCardForm refundBillPayPackageCardForm : form.PackageCard) {
            for (RefundBillPayProductForm refundBillPayForm : refundBillPayPackageCardForm.Product) {
                if (refundBillPayForm.ProductAccountID == null)
                    return false;
            }
            for (RefundBillPayProjectForm refundBillPayForm : refundBillPayPackageCardForm.Project) {
                if (refundBillPayForm.ProjectAccountID == null)
                    return false;
            }
            for (RefundBillPayGeneralCardForm refundBillPayForm : refundBillPayPackageCardForm.GeneralCard) {
                if (refundBillPayForm.GeneralCardAccountID == null)
                    return false;
            }
            for (RefundBillPayTimeCardForm refundBillPayForm : refundBillPayPackageCardForm.TimeCard) {
                if (refundBillPayForm.TimeCardAccountID == null)
                    return false;
            }
            for (RefundBillPaySavingCardForm refundBillPayForm : refundBillPayPackageCardForm.SavingCard) {
                if (refundBillPayForm.SavingCardAccountID == null)
                    return false;
            }
        }
        return true;
    }

    static boolean checkAmount(RefundBillPayQueryForm form) {
        for (RefundBillPayProductForm refundBillPayForm : form.Product) {
            if (refundBillPayForm.TotalAmount == null)
                return false;
        }
        for (RefundBillPayProjectForm refundBillPayForm : form.Project) {
            if (refundBillPayForm.TotalAmount == null)
                return false;
        }
        for (RefundBillPayGeneralCardForm refundBillPayForm : form.GeneralCard) {
            if (refundBillPayForm.TotalAmount == null)
                return false;
        }
        for (RefundBillPayTimeCardForm refundBillPayForm : form.TimeCard) {
            if (refundBillPayForm.TotalAmount == null)
                return false;
        }
        for (RefundBillPaySavingCardForm refundBillPayForm : form.SavingCard) {
            if (refundBillPayForm.Amount == null || refundBillPayForm.LargessAmount == null)
                return false;
        }
        for (RefundBillPayPackageCardForm refundBillPayForm : form.PackageCard) {
            for (RefundBillPayProductForm transerProductAddForm : refundBillPayForm.Product) {
                if (transerProductAddForm.TotalAmount == null)
                    return false;
            }
            for (RefundBillPayProjectForm transerProjectAddForm : refundBillPayForm.Project) {
                if (transerProjectAddForm.TotalAmount == null)
                    return false;
            }
            for (RefundBillPayGeneralCardForm transerGeneralCardAddForm : refundBillPayForm.GeneralCard) {
                if (transerGeneralCardAddForm.TotalAmount == null)
                    return false;
            }
            for (RefundBillPayTimeCardForm transerGeneralCardAddForm : refundBillPayForm.TimeCard) {
                if (transerGeneralCardAddForm.TotalAmount == null)
                    return false;
            }
            for (RefundBillPaySavingCardForm transerSavingCardAddForm : refundBillPayForm.SavingCard) {
                if (transerSavingCardAddForm.Amount == null || transerSavingCardAddForm.LargessAmount == null)
                    return false;
            }
        }
        return true;
    }

    static boolean checkTotalAmount(RefundBillAddForm form) {
        if (form.Product != null && form.Product.size() > 0){
            for (RefundBillProductForm refundBillPayForm : form.Product) {
                if (refundBillPayForm.TotalAmount == null)
                    return false;
            }
        }
        if (form.Project != null && form.Project.size() > 0) {
            for (RefundBillProjectForm refundBillPayForm : form.Project) {
                if (refundBillPayForm.TotalAmount == null)
                    return false;
            }
        }
        if (form.GeneralCard != null && form.GeneralCard.size() > 0) {
            for (RefundBillGeneralCardForm refundBillPayForm : form.GeneralCard) {
                if (refundBillPayForm.TotalAmount == null)
                    return false;
            }
        }
        if (form.TimeCard != null && form.TimeCard.size() > 0) {
            for (RefundBillTimeCardForm refundBillPayForm : form.TimeCard) {
                if (refundBillPayForm.TotalAmount == null)
                    return false;
            }
        }
        if (form.SavingCard != null && form.SavingCard.size() > 0) {
            for (RefundBillSavingCardForm refundBillPayForm : form.SavingCard) {
                if (refundBillPayForm.Amount == null || refundBillPayForm.LargessAmount == null)
                    return false;
            }
        }
        if (form.PackageCard != null && form.PackageCard.size() > 0) {
            for (RefundBillPackageCardForm refundBillPayForm : form.PackageCard) {
                if (refundBillPayForm.Product != null && refundBillPayForm.Product.size() > 0) {
                    for (RefundBillProductForm transerProductAddForm : refundBillPayForm.Product) {
                        if (transerProductAddForm.TotalAmount == null)
                            return false;
                    }
                }
                if (refundBillPayForm.Project != null && refundBillPayForm.Project.size() > 0) {
                    for (RefundBillProjectForm transerProjectAddForm : refundBillPayForm.Project) {
                        if (transerProjectAddForm.TotalAmount == null)
                            return false;
                    }
                }
                if (refundBillPayForm.GeneralCard != null && refundBillPayForm.GeneralCard.size() > 0) {
                    for (RefundBillGeneralCardForm transerGeneralCardAddForm : refundBillPayForm.GeneralCard) {
                        if (transerGeneralCardAddForm.TotalAmount == null)
                            return false;
                    }
                }
                if (refundBillPayForm.TimeCard != null && refundBillPayForm.TimeCard.size() > 0) {
                    for (RefundBillTimeCardForm transerGeneralCardAddForm : refundBillPayForm.TimeCard) {
                        if (transerGeneralCardAddForm.TotalAmount == null)
                            return false;
                    }
                }
                if (refundBillPayForm.SavingCard != null && refundBillPayForm.SavingCard.size() > 0) {
                    for (RefundBillSavingCardForm transerSavingCardAddForm : refundBillPayForm.SavingCard) {
                        if (transerSavingCardAddForm.Amount == null || transerSavingCardAddForm.LargessAmount == null)
                            return false;
                    }
                }
            }
        }
        return true;
    }
}