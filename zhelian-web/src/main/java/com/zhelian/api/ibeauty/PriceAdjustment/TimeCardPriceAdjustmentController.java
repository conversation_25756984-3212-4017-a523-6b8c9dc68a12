package com.zhelian.api.ibeauty.PriceAdjustment;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TimeCardPriceAdjustmentAddForm;
import com.zhelian.model.ibeauty.form.input.TimeCardPriceAdjustmentDeleteForm;
import com.zhelian.model.ibeauty.form.input.TimeCardPriceAdjustmentQueryForm;
import com.zhelian.service.ibeauty.PriceAdjustment.TimeCardPriceAdjustmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class TimeCardPriceAdjustmentController {

    @Autowired
    TimeCardPriceAdjustmentService timeCardPriceAdjustmentService;

    @RequestMapping(value = "/timeCardPriceAdjustment/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody TimeCardPriceAdjustmentQueryForm form) {
        return timeCardPriceAdjustmentService.timeCardPriceAdjustmentList(form);
    }

    @RequestMapping(value = "/timeCardPriceAdjustment/create", method = {RequestMethod.POST})
    public BaseOutput createTimeCardPriceAdjustment(@RequestBody TimeCardPriceAdjustmentAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("门店编号不能为空");
        if (form.TimeCardID == null)
            return BaseOutput.failed("时效卡编号不能为空");
        if (form.Price == null)
            return BaseOutput.failed("调整价格不能为空");

        return timeCardPriceAdjustmentService.createTimeCardPriceAdjustment(form);
    }

    @RequestMapping(value = "/timeCardPriceAdjustment/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody TimeCardPriceAdjustmentAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("门店编号不能为空");
        if (form.TimeCardID == null)
            return BaseOutput.failed("时效卡编号不能为空");
        if (form.Price == null)
            return BaseOutput.failed("调整价格不能为空");

        return timeCardPriceAdjustmentService.updateTimeCardPriceAdjustmentEntity(form);
    }

    @RequestMapping(value = "/timeCardPriceAdjustment/delete", method = {RequestMethod.POST})
    public BaseOutput delete(@RequestBody TimeCardPriceAdjustmentDeleteForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("门店编号不能为空");
        if (form.TimeCardID == null)
            return BaseOutput.failed("时效卡编号不能为空");

        return timeCardPriceAdjustmentService.deleteTimeCardPriceAdjustmentEntity(form);
    }
}