package com.zhelian.api.ibeauty.PriceAdjustment;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.ProjectPriceAdjustmentAddForm;
import com.zhelian.model.ibeauty.form.input.ProjectPriceAdjustmentDeleteForm;
import com.zhelian.model.ibeauty.form.input.ProjectPriceAdjustmentQueryForm;
import com.zhelian.service.ibeauty.PriceAdjustment.ProjectPriceAdjustmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class ProjectPriceAdjustmentController {

    @Autowired
    ProjectPriceAdjustmentService projectPriceAdjustmentService;

    @RequestMapping(value = "/projectPriceAdjustment/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody ProjectPriceAdjustmentQueryForm form) {
        return projectPriceAdjustmentService.projectPriceAdjustmentList(form);
    }

    @RequestMapping(value = "/projectPriceAdjustment/create", method = {RequestMethod.POST})
    public BaseOutput createProjectPriceAdjustment(@RequestBody ProjectPriceAdjustmentAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("门店编号不能为空");
        if (form.ProjectID == null)
            return BaseOutput.failed("项目编号不能为空");
        if (form.Price == null)
            return BaseOutput.failed("调整价格不能为空");

        return projectPriceAdjustmentService.createProjectPriceAdjustment(form);
    }

    @RequestMapping(value = "/projectPriceAdjustment/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody ProjectPriceAdjustmentAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("门店编号不能为空");
        if (form.ProjectID == null)
            return BaseOutput.failed("项目编号不能为空");
        if (form.Price == null)
            return BaseOutput.failed("调整价格不能为空");

        return projectPriceAdjustmentService.updateProjectPriceAdjustmentEntity(form);
    }

    @RequestMapping(value = "/projectPriceAdjustment/delete", method = {RequestMethod.POST})
    public BaseOutput delete(@RequestBody ProjectPriceAdjustmentDeleteForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("门店编号不能为空");
        if (form.ProjectID == null)
            return BaseOutput.failed("项目编号不能为空");

        return projectPriceAdjustmentService.deleteProjectPriceAdjustmentEntity(form);
    }
}