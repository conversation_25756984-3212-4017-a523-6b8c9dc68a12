package com.zhelian.api.ibeauty.transer;


import com.zhelian.core.utils.enumeration.BillChannel;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.miniprogram.form.input.SaleBillSignatureAddForm;
import com.zhelian.service.ibeauty.transer.TranserCommonService;
import com.zhelian.service.ibeauty.transer.TranserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class TranserController {

    @Autowired
    TranserService transerService;
    @Autowired
    TranserCommonService transerCommonService;

    @RequestMapping(value = "/transer/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody TranserAddForm form) throws Exception {
        if (form.TransferOutCustomerID == null)
            return BaseOutput.failed("转出会员编号不能为空");
        if (form.TransferInCustomerID == null)
            return BaseOutput.failed("转入会员编号不能为空");
        if (!checkQuantity(form))
            return BaseOutput.failed("转账数量请大于0");
        return transerCommonService.create(form, BillChannel.BillChannelPC.getID());
    }

    @RequestMapping(value = "/transer/list", method = {RequestMethod.POST})
    public BaseOutput transer(@RequestBody TranserQueryForm form) {
        return transerService.transerList(form);
    }

    @RequestMapping(value = "/transer/info", method = {RequestMethod.POST})
    public BaseOutput info(@RequestBody TranserInfoQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("转账订单编号不能为空");
        return transerService.transerInfo(form);
    }

    @RequestMapping(value = "/transer/cancel", method = {RequestMethod.POST})
    public BaseOutput cancel(@RequestBody TranserCancelForm form) {
        if (form.ID == null)
            return BaseOutput.failed("转账订单编号不能为空");
        return transerCommonService.cancel(form);
    }

    @RequestMapping(value = "/transer/getSignature", method = {RequestMethod.POST})
    public BaseOutput getSignature(@RequestBody SaleBillSignatureAddForm form) {
        return transerService.getSignature(form);
    }

    @RequestMapping(value = "/transer/addTransferBillAttachment", method = {RequestMethod.POST})
    public BaseOutput addTranserBillAttachment(@RequestBody TransferBillAttachmentAddForm form) {
        return transerService.addTransferBillAttachment(form);
    }

    @RequestMapping(value = "/transer/deleteTransferBillAttachment", method = {RequestMethod.POST})
    public BaseOutput deleteTranserBillAttachment(@RequestBody DeleteTransferBillBillAttachmentForm form) {
        return transerService.deleteTransferBillAttachment(form);
    }

    @RequestMapping(value = "/transer/getTransferBillAttachment", method = {RequestMethod.POST})
    public BaseOutput getTranserBillAttachment(@RequestBody TransferBillAttachmentQueryForm form) {
        return transerService.getTranserBillAttachment(form);
    }

    static boolean checkQuantity(TranserAddForm form) {
        for (TranserProductAddForm transerProductAddForm : form.Product) {
            if (transerProductAddForm.Quantity == null || transerProductAddForm.Quantity.equals(0))
                return false;
        }
        for (TranserProjectAddForm transerProjectAddForm : form.Project) {
            if (transerProjectAddForm.Quantity == null || transerProjectAddForm.Quantity.equals(0))
                return false;
        }
        for (TranserGeneralCardAddForm transerGeneralCardAddForm : form.GeneralCard) {
            if (transerGeneralCardAddForm.Quantity == null || transerGeneralCardAddForm.Quantity.equals(0))
                return false;
        }
        for (TranserPackageCardAddForm transerPackageCardAddForm : form.PackageCard) {
            for (TranserProductAddForm transerProductAddForm : transerPackageCardAddForm.Product) {
                if (transerProductAddForm.Quantity == null || transerProductAddForm.Quantity.equals(0))
                    return false;
            }
            for (TranserProjectAddForm transerProjectAddForm : transerPackageCardAddForm.Project) {
                if (transerProjectAddForm.Quantity == null || transerProjectAddForm.Quantity.equals(0))
                    return false;
            }
            for (TranserGeneralCardAddForm transerGeneralCardAddForm : transerPackageCardAddForm.GeneralCard) {
                if (transerGeneralCardAddForm.Quantity == null || transerGeneralCardAddForm.Quantity.equals(0))
                    return false;
            }
        }
        return true;
    }
}