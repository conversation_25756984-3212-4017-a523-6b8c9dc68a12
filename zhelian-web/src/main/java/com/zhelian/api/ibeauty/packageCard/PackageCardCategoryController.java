package com.zhelian.api.ibeauty.packageCard;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.PackageCardCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.PackageCardCategoryMoveForm;
import com.zhelian.model.ibeauty.form.input.PackageCardCategoryQueryForm;
import com.zhelian.model.ibeauty.form.input.PackageCardCategoryUpdateForm;
import com.zhelian.service.ibeauty.packageCard.PackageCardCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
public class PackageCardCategoryController {

    @Autowired
    PackageCardCategoryService service;

    @RequestMapping(value = "/packageCardCategory/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody PackageCardCategoryQueryForm form) {
        return service.packageCardCategoryAll(form);
    }

    @RequestMapping(value = "/packageCardCategory/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody PackageCardCategoryAddForm form) {
        if (!StringUtils.hasText(form.Name)) {
            return BaseOutput.failed("套餐卡名称不能为空");
        }
        return service.createPackageCardCategory(form);
    }

    @RequestMapping(value = "/packageCardCategory/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody PackageCardCategoryUpdateForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("套餐卡名称不能为空");
        return service.updatePackageCardCategory(form);
    }

    @RequestMapping(value = "/packageCardCategory/move", method = RequestMethod.POST)
    public BaseOutput move(@RequestBody PackageCardCategoryMoveForm form) {
        if (form.MoveID == null) return BaseOutput.failed("MoveID不能为空");
        return service.move(form);
    }

}
