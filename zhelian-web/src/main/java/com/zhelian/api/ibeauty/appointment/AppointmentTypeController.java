package com.zhelian.api.ibeauty.appointment;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.AppointmentTypeAddForm;
import com.zhelian.model.ibeauty.form.input.AppointmentTypeMoveForm;
import com.zhelian.model.ibeauty.form.input.AppointmentTypeQueryForm;
import com.zhelian.service.ibeauty.appointment.AppointmentTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/appointmentType")
public class AppointmentTypeController {

    @Autowired
    AppointmentTypeService appointmentTypeService;

    @PostMapping(value = "/all")
    public BaseOutput all(@RequestBody AppointmentTypeQueryForm form) {
        return appointmentTypeService.all(form);
    }

    @PostMapping(value = "/create")
    public BaseOutput create(@RequestBody AppointmentTypeAddForm form) {
        if (!StringUtils.hasText(form.Name)) {
            return BaseOutput.failed("预约类型名称不可以为空");
        }
        return appointmentTypeService.create(form);
    }

    @PostMapping(value = "/update")
    public BaseOutput update(@RequestBody AppointmentTypeAddForm form) {
        if (!StringUtils.hasText(form.Name)) {
            return BaseOutput.failed("预约类型名称不可以为空");
        }
        return appointmentTypeService.update(form);
    }

    @PostMapping(value = "/move")
    public BaseOutput move(@RequestBody AppointmentTypeMoveForm form) {
        return appointmentTypeService.move(form);
    }

}
