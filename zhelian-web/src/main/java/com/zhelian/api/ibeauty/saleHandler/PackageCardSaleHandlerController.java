package com.zhelian.api.ibeauty.saleHandler;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerMoveForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleHandlerUpdateForm;
import com.zhelian.service.ibeauty.saleHandler.PackageCardSaleHandlerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description:
 * @Param: No such property: code for class: Script1
 * @returns:
 * @Author: 86158
 * @Date: 2020/3/31 11:33
 */
@RestController
@RequestMapping(value = "/api")
public class PackageCardSaleHandlerController {

    @Autowired
    PackageCardSaleHandlerService packageCardSaleHandlerService;

    @RequestMapping(value = "/packageCardSaleHandler/all", method = {RequestMethod.POST})
    public BaseOutput allPackageCardSaleHandler(@RequestBody SaleHandlerQueryForm form) {
        return packageCardSaleHandlerService.allPackageCardSaleHandler(form);
    }

    @RequestMapping(value = "/packageCardSaleHandler/create", method = {RequestMethod.POST})
    public BaseOutput createPackageCardSaleHandler(@RequestBody SaleHandlerAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("经手人名称不能为空");
        if (form.EntityID == null)
            return BaseOutput.failed("门店ID不能为空");
        if (form.JobType == null || form.JobType.length == 0)
            return BaseOutput.failed("适用职务不能为空");


        return packageCardSaleHandlerService.createPackageCardSaleHandler(form);
    }

    @RequestMapping(value = "/packageCardSaleHandler/update", method = {RequestMethod.POST})
    public BaseOutput updatePackageCardSaleHandler(@RequestBody SaleHandlerUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("经手人名称不能为空");
        if (form.EntityID == null)
            return BaseOutput.failed("门店ID不能为空");
        if (form.Active == null)
            return BaseOutput.failed("是否有效不能为空");
        if (form.JobType == null || form.JobType.length == 0)
            return BaseOutput.failed("适用职务不能为空");


        return packageCardSaleHandlerService.updatePackageCardSaleHandler(form);
    }

    @RequestMapping(value = "/packageCardSaleHandler/move", method = {RequestMethod.POST})
    public BaseOutput movePackageCardSaleHandler(@RequestBody SaleHandlerMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");
        return packageCardSaleHandlerService.movePackageCardSaleHandler(form);
    }
}