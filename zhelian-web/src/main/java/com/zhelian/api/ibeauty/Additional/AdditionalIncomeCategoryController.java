package com.zhelian.api.ibeauty.Additional;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingOrIncomeOrPaymentsCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingOrIncomeOrPaymentsCategoryQueryForm;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingOrIncomeOrPaymentsCategoryUpdateForm;
import com.zhelian.model.ibeauty.form.input.AdditionalSpendingOrIncomeOrPaymentsMoveForm;
import com.zhelian.service.ibeauty.Additional.AdditionalIncomeCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @className: AdditionalIncomeCategoryController
 * @description:
 * @version: V1.0
 * @author: zhoutianxiang
 * @date: 2020/4/25 4:36 PM
 */
@RestController
@RequestMapping(value = "/api")
public class AdditionalIncomeCategoryController {

    @Autowired
    AdditionalIncomeCategoryService additionalIncomeCategoryService;

    @RequestMapping(value = "/additionalIncomeCategory/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody AdditionalSpendingOrIncomeOrPaymentsCategoryQueryForm form) {
        return additionalIncomeCategoryService.all(form);
    }

    @RequestMapping(value = "/additionalIncomeCategory/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody AdditionalSpendingOrIncomeOrPaymentsCategoryAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("收入类型名称不能为空");

        return additionalIncomeCategoryService.create(form);
    }

    @RequestMapping(value = "/additionalIncomeCategory/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody AdditionalSpendingOrIncomeOrPaymentsCategoryUpdateForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("收入类型名称不能为空");
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (form.Active == null)
            return BaseOutput.failed("有效性不能为空");

        return additionalIncomeCategoryService.update(form);
    }

    @RequestMapping(value = "/additionalIncomeCategory/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody AdditionalSpendingOrIncomeOrPaymentsMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");
        return additionalIncomeCategoryService.move(form);
    }

    @RequestMapping(value = "/additionalIncomeCategory/getAdditionalIncomeCategory", method = {RequestMethod.POST})
    public BaseOutput all() {
        return additionalIncomeCategoryService.getAdditionalIncomeCategory();
    }
}