package com.zhelian.api.ibeauty.treatProductCommission;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatProductHandlersCommissionAddForm;
import com.zhelian.service.ibeauty.treatProductCommission.TreatProductHandlerCommissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

@RestController
@RequestMapping(value = "/api")
public class TreatProductHandlerCommissionController {

    @Autowired
    TreatProductHandlerCommissionService treatProductHandlerCommissionService;


    @RequestMapping(value = "/treatProductHandlerCommission/all", method = {RequestMethod.POST})
    public BaseOutput allTreatProductHandlerCommission(@RequestBody TreatProductHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.ProductID == null)
            return BaseOutput.failed("产品编号不能为空");
        return treatProductHandlerCommissionService.allTreatProductHandlerCommission(form);
    }

    @RequestMapping(value = "/treatProductHandlerCommission/update", method = {RequestMethod.POST})
    public BaseOutput updateTreatProductHandlerCommission(@RequestBody TreatProductHandlersCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.ProductID == null)
            return BaseOutput.failed("产品编号不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatProductHandlerCommissionService.updateTreatProductHandlerCommission(form);
    }

    static boolean checkRate(TreatProductHandlersCommissionAddForm form) {
        if (form.Handler != null) {
            for (TreatProductHandlerCommissionAddForm handlerCommissionAddForm : form.Handler) {

                if ((handlerCommissionAddForm.PayRate != null && handlerCommissionAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerCommissionAddForm.CardRate != null && handlerCommissionAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerCommissionAddForm.CardLargessRate != null && handlerCommissionAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerCommissionAddForm.LargessRate != null && handlerCommissionAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
                )
                    return false;

                for (TreatProductCategoryHandlerJobTypeCommissionAddForm jobTypeCommissionAddForm : handlerCommissionAddForm.HandlerJobTypeCommission) {

                    if ((jobTypeCommissionAddForm.PayRate != null && jobTypeCommissionAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypeCommissionAddForm.CardRate != null && jobTypeCommissionAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypeCommissionAddForm.CardLargessRate != null && jobTypeCommissionAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypeCommissionAddForm.LargessRate != null && jobTypeCommissionAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
                    )
                        return false;
                }

            }
        }
        return true;
    }
}

