package com.zhelian.api.ibeauty.padShow;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.ProjectCategoryMoveForm;
import com.zhelian.model.pad.form.input.GoodsDisplayCategoryAddForm;
import com.zhelian.model.pad.form.input.GoodsDisplayCategoryQueryForm;
import com.zhelian.service.ibeauty.padShow.GoodsDisplayCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api/goodsDisplayCategory")
public class GoodsDisplayCategoryController {

    @Autowired
    GoodsDisplayCategoryService goodsDisplayCategoryService;

    @RequestMapping(value = "/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody GoodsDisplayCategoryQueryForm form) {
        return goodsDisplayCategoryService.all(form);
    }

    @RequestMapping(value = "/valid", method = {RequestMethod.POST})
    public BaseOutput valid() {
        return goodsDisplayCategoryService.valid();
    }

    @RequestMapping(value = "/memo", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody GoodsDisplayCategoryAddForm form) {
        if (form.ID == null)
            return BaseOutput.failed("编号不能为空");
        return goodsDisplayCategoryService.getMemo(form);
    }

    @RequestMapping(value = "/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody GoodsDisplayCategoryAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("项目分类名称不能为空");
        if (form.ParentID == null)
            return BaseOutput.failed("上级分类不能为空");
        return goodsDisplayCategoryService.createGoodsDisplayCategory(form);
    }

    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody GoodsDisplayCategoryAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("项目分类名称不能为空");
        if (form.ParentID == null)
            return BaseOutput.failed("上级分类不能为空");
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (form.Active == null)
            return BaseOutput.failed("有效性不能为空");
        return goodsDisplayCategoryService.updateGoodsDisplayCategory(form);
    }

    @RequestMapping(value = "/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody ProjectCategoryMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");

        return goodsDisplayCategoryService.move(form);
    }
}