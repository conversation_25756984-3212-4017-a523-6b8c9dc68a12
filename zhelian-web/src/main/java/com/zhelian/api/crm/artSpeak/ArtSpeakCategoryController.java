/**
 * fileName: PayMethodController.java
 * author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * date: 2020/3/4 4:36 PM
 * copyright(c) 2019-2020 xxx公司
 */
package com.zhelian.api.crm.artSpeak;

import com.zhelian.model.CRM.form.input.ArtSpeakCategoryAddForm;
import com.zhelian.model.CRM.form.input.ArtSpeakCategoryMoveForm;
import com.zhelian.model.CRM.form.input.ArtSpeakCategoryQueryForm;
import com.zhelian.model.CRM.form.input.ArtSpeakCategoryUpdateForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.service.CRM.artSpeak.ArtSpeakCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 功能描述:
 * <>
 *
 * @return:
 * @exception:
 * @author: zhoutianxiang
 * @date: 2020/4/17 1:49 PM
 */
@RestController
@RequestMapping(value = "/api")
public class ArtSpeakCategoryController {

    @Autowired
    ArtSpeakCategoryService artSpeakCategoryService;

    @RequestMapping(value = "/artSpeakCategory/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody ArtSpeakCategoryQueryForm form) {
        return artSpeakCategoryService.artSpeakCategoryList(form);
    }

    @RequestMapping(value = "/artSpeakCategory/valid", method = {RequestMethod.POST})
    public BaseOutput ValidArtSpeakCategoryValid() {
        return artSpeakCategoryService.getValidArtSpeakCategoryValid();
    }

    @RequestMapping(value = "/artSpeakCategory/top", method = {RequestMethod.POST})
    public BaseOutput top() {
        return artSpeakCategoryService.getTopArtSpeakList();
    }

    @RequestMapping(value = "/artSpeakCategory/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody ArtSpeakCategoryAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("话术分类名称不能为空");
        if (form.ParentID == null)
            return BaseOutput.failed("上级分类不能为空");

        return artSpeakCategoryService.addArtSpeakCategory(form);
    }

    @RequestMapping(value = "/artSpeakCategory/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody ArtSpeakCategoryUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("产品分类名称不能为空");
        if (form.Active == null)
            return BaseOutput.failed("有效性不能为空");

        return artSpeakCategoryService.updateArtSpeakCategory(form);
    }

    @RequestMapping(value = "/artSpeakCategory/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody ArtSpeakCategoryMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");
        return artSpeakCategoryService.move(form);
    }

}