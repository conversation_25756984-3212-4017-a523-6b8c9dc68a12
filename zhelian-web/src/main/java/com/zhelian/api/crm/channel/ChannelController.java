package com.zhelian.api.crm.channel;

import com.zhelian.model.CRM.form.input.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.ChannelBatchUpdateForm;
import com.zhelian.model.ibeauty.form.input.ChannelTransferConsultantForm;
import com.zhelian.model.ibeauty.form.input.ChannelTransferDeveloperForm;
import com.zhelian.model.miniprogram.form.input.ChannelDetailQueryForm;
import com.zhelian.service.CRM.channel.ChannelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/api")
public class ChannelController {

    @Autowired
    ChannelService channelService;


    @RequestMapping(value = "/channel/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody ChannelQueryForm form) {
        return channelService.list(form);
    }

    @RequestMapping(value = "/channel/excel", method = {RequestMethod.POST})
    public BaseOutput excel(@RequestBody ChannelQueryForm form, HttpServletResponse response) {
        return channelService.excel(form,response);
    }

    @RequestMapping(value = "/channel/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody ChannelAddForm form) throws Exception {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("名称不能为空");
        if (form.ChannelTypeID == null)
            return BaseOutput.failed("渠道类型不能为空");
        return channelService.create(form);
    }

    @RequestMapping(value = "/channel/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody ChannelUpdateForm form) throws Exception {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("名称不能为空");

        return channelService.updateChannel(form);
    }

    @RequestMapping(value = "/channel/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody ChannelMoveForm form) {
        if (form.MoveChannelID == 0)
            return BaseOutput.failed("移动渠道ID不能为空");

        return channelService.move(form);
    }

    @RequestMapping(value = "/channel/all", method = {RequestMethod.POST})
    public BaseOutput all() {
        return channelService.channelList(null);
    }

    @RequestMapping(value = "/channel/treeList", method = {RequestMethod.POST})
    public BaseOutput treeList(@RequestBody ChannelQueryForm form) {
        return channelService.channelList(form);
    }

    @RequestMapping(value = "/channel/updateParent", method = {RequestMethod.POST})
    public BaseOutput updateParent(@RequestBody ChannelUpdateForm form) {
        return channelService.updateChannelparent(form);
    }

    @RequestMapping(value = "/channel/detail", method = {RequestMethod.POST})
    public BaseOutput detail(@RequestBody ChannelDetailQueryForm form) {
        return channelService.detail(form);
    }

    @RequestMapping(value = "/channel/employeeAll", method = {RequestMethod.POST})
    public BaseOutput employeeAll() {
        return channelService.empList();
    }


    @RequestMapping(value = "/channel/info", method = {RequestMethod.POST})
    public BaseOutput info(@RequestBody ChannelQueryForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("渠道名称不能为空");
        return channelService.all(form);
    }

    @RequestMapping(value = "/channel/customerInfo", method = {RequestMethod.POST})
    public BaseOutput customerInfo(@RequestBody ChannelQueryForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("渠道名称不能为空");
        return channelService.customerInfo(form);
    }

    @RequestMapping(value = "/channel/approvalList", method = {RequestMethod.POST})
    public BaseOutput approvalList(@RequestBody ChannelApprovalQueryForm form) {
        return channelService.approvalList(form);
    }

    @RequestMapping(value = "/channel/approvalDetail", method = {RequestMethod.POST})
    public BaseOutput approvalDetail(@RequestBody ChannelDetailQueryForm form) {
        return channelService.approvalDetail(form);
    }

    @RequestMapping(value = "/channel/approval", method = {RequestMethod.POST})
    public BaseOutput approval(@RequestBody ChannelApprovalAddForm form) {
        return channelService.approval(form);
    }

    @RequestMapping(value = "/channel/batchUpdateConsultant", method = {RequestMethod.POST})
    public BaseOutput batchUpdateConsultant(@RequestBody ChannelBatchUpdateForm form) {
        if (form.ChannelID.length == 0)
            return BaseOutput.failed("请选择渠道");
        return channelService.batchUpdateConsultant(form);
    }

    @RequestMapping(value = "/channel/batchUpdateDeveloper", method = {RequestMethod.POST})
    public BaseOutput batchUpdateDeveloper(@RequestBody ChannelBatchUpdateForm form) {
        if (form.ChannelID.length == 0)
            return BaseOutput.failed("请选择渠道");
        return channelService.batchUpdateDeveloper(form);
    }

    @RequestMapping(value = "/channel/transferDeveloper", method = {RequestMethod.POST})
    public BaseOutput transferDeveloper(@RequestBody ChannelTransferDeveloperForm form) {
        if (!StringUtils.hasText(form.OutEmployeeID))
            return BaseOutput.failed("请选择转出人编号");
        if (!StringUtils.hasText(form.InEmployeeID))
            return BaseOutput.failed("请选择转入人编号");
        return channelService.transferDeveloper(form);
    }

    @RequestMapping(value = "/channel/transferConsultant", method = {RequestMethod.POST})
    public BaseOutput transferConsultant(@RequestBody ChannelTransferConsultantForm form) {
        if (!StringUtils.hasText(form.OutEmployeeID))
            return BaseOutput.failed("请选择转出人编号");
        if (!StringUtils.hasText(form.InEmployeeID))
            return BaseOutput.failed("请选择转入人编号");
        return channelService.transferConsultant(form);
    }
}
