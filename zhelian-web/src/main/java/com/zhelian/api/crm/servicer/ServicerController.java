package com.zhelian.api.crm.servicer;

import com.zhelian.model.CRM.form.input.ElectronicMedicalRecordTemplateMoveForm;
import com.zhelian.model.CRM.form.input.ServicerAddForm;
import com.zhelian.model.CRM.form.input.ServicerQueryForm;
import com.zhelian.model.CRM.form.input.ServicerUpdateForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.CustomerDetailQueryForm;
import com.zhelian.service.CRM.servicer.ServicerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
public class ServicerController {

    @Autowired
    ServicerService service;


    @RequestMapping(value = "/servicer/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody ServicerQueryForm form) {
        return service.list(form);
    }

    @RequestMapping(value = "/servicer/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody ServicerAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("名称不能为空");

        return service.create(form);
    }

    @RequestMapping(value = "/servicer/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody ServicerUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("名称不能为空");

        return service.updateServicer(form);
    }


    @RequestMapping(value = "/servicer/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody ElectronicMedicalRecordTemplateMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");
        return service.move(form);
    }

    @RequestMapping(value = "/servicer/getAllCustomerServicerRange", method = {RequestMethod.POST})
    public BaseOutput getAllCustomerServicerRange() {
        return service.getAllustomerServicerRange();
    }

    @RequestMapping(value = "/servicer/getCustomerServicerRange", method = {RequestMethod.POST})
    public BaseOutput getCustomerServicerRange() {
        return service.getCustomerServicerRange();
    }


    /**
     * 工作台跟进-服务人员
     *
     * @param
     * @param
     * @return
     */
    @RequestMapping(value = "/servicer/servicer", method = {RequestMethod.POST})
    public BaseOutput servicer(@RequestBody CustomerDetailQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客ID不能为空");
        return service.servicer(form);

    }

    /**
     * 工作台接待-服务人员
     *
     * @param
     * @param
     * @return
     */
    @RequestMapping(value = "/servicer/diagnosisServicer", method = {RequestMethod.POST})
    public BaseOutput diagnosisServicer(@RequestBody CustomerDetailQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客ID不能为空");
        return service.diagnosisServicer(form);

    }

    @RequestMapping(value = "/servicer/customerDetailServicer", method = {RequestMethod.POST})
    public BaseOutput getCustomerDetailServicer(@RequestBody CustomerDetailQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客ID不能为空");
        return service.getCustomerDetailServicer(form);

    }
}
