package com.zhelian.api.crm.customerConsultantJobType;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.CustomerConsultantJobTypeAddForm;
import com.zhelian.model.ibeauty.form.input.CustomerConsultantJobTypeDeleteForm;
import com.zhelian.service.CRM.customerConsultantJobType.CustomerConsultantJobTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/api")
public class CustomerConsultantJobTypeController {

    @Autowired
    CustomerConsultantJobTypeService customerConsultantJobTypeService;

    @RequestMapping(value = "/customerConsultantJobType/all", method = {RequestMethod.POST})
    public BaseOutput all() {
        return customerConsultantJobTypeService.allCustomerConsultantJobType();
    }

    @RequestMapping(value = "/customerConsultantJobType/update", method = {RequestMethod.POST})
    public BaseOutput updateCustomerConsultantJobTypeSale(@RequestBody CustomerConsultantJobTypeAddForm form) {
        return customerConsultantJobTypeService.updateCustomerConsultantJobTypeSale(form);
    }

    @RequestMapping(value = "/customerConsultantJobType/delete", method = {RequestMethod.POST})
    public BaseOutput deleteCustomerConsultantJobTypeSale(@RequestBody CustomerConsultantJobTypeDeleteForm form) {
        if (form.JobTypeID == null)
            return BaseOutput.failed("职务编号不能为空");

        return customerConsultantJobTypeService.deleteCustomerConsultantJobTypeSale(form);
    }
}
