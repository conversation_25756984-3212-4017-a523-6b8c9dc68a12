package com.zhelian.api.crm.callbackStatus;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.PayMethodAddForm;
import com.zhelian.model.ibeauty.form.input.PayMethodMoveForm;
import com.zhelian.model.ibeauty.form.input.PayMethodQueryForm;
import com.zhelian.model.ibeauty.form.input.PayMethodUpdateForm;
import com.zhelian.service.CRM.callbackStatus.CallbackStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class CallbackStatusController {

    @Autowired
    CallbackStatusService callbackStatusService;

    @RequestMapping(value = "/callbackStatus/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody PayMethodQueryForm form) {
        return callbackStatusService.all(form);
    }

    @RequestMapping(value = "/callbackStatus/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody PayMethodAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("状态名称不能为空");

        return callbackStatusService.create(form);
    }

    @RequestMapping(value = "/callbackStatus/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody PayMethodUpdateForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("方式名称不能为空");
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (form.Active == null)
            return BaseOutput.failed("有效性不能为空");

        return callbackStatusService.update(form);
    }

    @RequestMapping(value = "/callbackStatus/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody PayMethodMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");
        return callbackStatusService.move(form);
    }

}