package com.zhelian.api.crm.customerLevel;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.CustomerLevelAddForm;
import com.zhelian.model.ibeauty.form.input.CustomerLevelQueryForm;
import com.zhelian.model.ibeauty.form.input.CustomerLevelUpdateForm;
import com.zhelian.service.CRM.customerLevel.CustomerLevelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class CustomerLevelController {

    @Autowired
    CustomerLevelService customerLevelService;

    @RequestMapping(value = "/customerLevel/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody CustomerLevelQueryForm form) {
        return customerLevelService.allCustomerLevel(form);
    }

    @RequestMapping(value = "/customerLevel/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody CustomerLevelAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("等级名称不能为空");

        return customerLevelService.createCustomerLevel(form);
    }

    @RequestMapping(value = "/customerLevel/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody CustomerLevelUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("等级名称不能为空");


        return customerLevelService.updateCustomerLevel(form);
    }

    @RequestMapping(value = "/customerLevel/delete", method = {RequestMethod.POST})
    public BaseOutput delete(@RequestBody CustomerLevelUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        return customerLevelService.delete(form);
    }

    @RequestMapping(value = "/customerLevel/status", method = {RequestMethod.POST})
    public BaseOutput status() {
        return customerLevelService.status();
    }

    @RequestMapping(value = "/customerLevel/updateAllCustomerLevel", method = {RequestMethod.POST})
    public BaseOutput updateAllCustomerLevel() {
        return customerLevelService.updateAllCustomerLevel();
    }

}