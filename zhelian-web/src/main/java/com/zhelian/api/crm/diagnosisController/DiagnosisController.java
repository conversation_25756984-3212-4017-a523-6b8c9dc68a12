package com.zhelian.api.crm.diagnosisController;

import com.zhelian.model.CRM.form.input.DiagnosisAddForm;
import com.zhelian.model.CRM.form.input.DiagnosisQueryForm;
import com.zhelian.model.CRM.form.input.TransferDiagnosisAddForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.service.CRM.diagnosis.DiagnosisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;


@RestController
@RequestMapping(value = "/api")
public class DiagnosisController {

    @Autowired
    DiagnosisService diagnosisService;

    @RequestMapping(value = "/diagnosis/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody DiagnosisQueryForm form) {
        return diagnosisService.list(form);
    }

    @RequestMapping(value = "/diagnosis/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody DiagnosisQueryForm form) {
        return diagnosisService.all(form);
    }

    @RequestMapping(value = "/diagnosis/diagnosis", method = {RequestMethod.POST})
    public BaseOutput diagnosis(@RequestBody DiagnosisAddForm form) throws Exception {
        if (form.DiagnosisID == null)
            return BaseOutput.failed("指派ID不能为空");
        if (form.IsNextFollowUp == null)
            return BaseOutput.failed("下次是否跟进不能为空");
        return diagnosisService.diagnosis(form);
    }

    @RequestMapping(value = "/diagnosis/transferDiagnosis", method = {RequestMethod.POST})
    public BaseOutput transferDiagnosis(@RequestBody TransferDiagnosisAddForm form) {
        if (form.DiagnosisID == null)
            return BaseOutput.failed("指派ID不能为空");
        if (form.DiagnosisBy == null)
            return BaseOutput.failed("员工ID不能为空");
        return diagnosisService.transferDiagnosisAddForm(form);
    }

    @RequestMapping(value = "/diagnosis/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody DiagnosisAddForm form) throws Exception {
        if (form.DiagnosisID == null)
            return BaseOutput.failed("指派ID不能为空");
        return diagnosisService.update(form);
    }

    @RequestMapping(value = "/diagnosis/excelDisPlayPhone", method = {RequestMethod.POST})
    public BaseOutput excelDisPlayPhone(@RequestBody DiagnosisQueryForm form, HttpServletResponse response) {
        return diagnosisService.excelDisPlayPhone(form,response);
    }
    @RequestMapping(value = "/diagnosis/excelNoDisPlayPhone", method = {RequestMethod.POST})
    public BaseOutput excelNoDisPlayPhone(@RequestBody DiagnosisQueryForm form, HttpServletResponse response) {
        return diagnosisService.excelNoDisPlayPhone(form,response);
    }
}