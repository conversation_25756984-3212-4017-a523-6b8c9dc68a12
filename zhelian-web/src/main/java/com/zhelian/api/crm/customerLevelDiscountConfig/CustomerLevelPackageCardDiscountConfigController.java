package com.zhelian.api.crm.customerLevelDiscountConfig;

import com.zhelian.model.CRM.form.input.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.service.CRM.customerLevelDiscountConfig.CustomerLevelPackageCardDiscountConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value = "/api")
public class CustomerLevelPackageCardDiscountConfigController {

    @Autowired
    CustomerLevelPackageCardDiscountConfigService customerLevelPackageCardDiscountConfigService;

    /**
     * 查询客户级别折扣配置
     *
     * @return
     */
    @PostMapping(value = "/customerPricePackageCardConfig/customerLevelDiscountAll")
    public BaseOutput customerLevelDiscountAll() {
        return customerLevelPackageCardDiscountConfigService.customerLevelDiscountAll();
    }

    /**
     * 创建客户级别折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPricePackageCardConfig/createCustomerLevelDiscountConfig")
    public BaseOutput createCustomerLevelDiscountConfig(@RequestBody List<CustomerLevelPackageCardDiscountConfigAddForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelPackageCardDiscountConfigService.createCustomerLevelDiscountConfig(form);
    }

    /**
     * 查询客户级别分类折扣配置
     *
     * @return
     */
    @PostMapping(value = "/customerPricePackageCardConfig/customerLevelCategoryDiscountAll")
    public BaseOutput customerLevelCategoryDiscountAll(@RequestBody CustomerLevelPackageCardCategoryDiscountConfigQueryForm form) {
        return customerLevelPackageCardDiscountConfigService.customerLevelCategoryDiscountAll(form);
    }

    /**
     * 查询分类
     *
     * @return
     */
    @PostMapping(value = "/customerPricePackageCardConfig/category")
    public BaseOutput category() {
        return customerLevelPackageCardDiscountConfigService.category();
    }

    /**
     * 查询已选分类
     * @return
     */
    @PostMapping(value = "/customerPricePackageCardConfig/selectedCategory")
    public BaseOutput selectedCategory() {
        return customerLevelPackageCardDiscountConfigService.selectedCategory();
    }

    /**
     * 创建客户级别分类折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPricePackageCardConfig/createCustomerLevelCategoryDiscountConfig")
    public BaseOutput createCustomerLevelCategoryDiscountConfig(@RequestBody List<CustomerLevelPackageCardCategoryDiscountConfigAddForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelPackageCardDiscountConfigService.createCustomerLevelCategoryDiscountConfig(form);
    }


    /**
     * 更新客户级别分类折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPricePackageCardConfig/updateCustomerLevelCategoryDiscountConfig")
    public BaseOutput updateCustomerLevelCategoryDiscountConfig(@RequestBody CustomerLevelPackageCardCategoryDiscountConfigUpdateForm form) {
        if (form.CustomerLevelDiscountPriceList == null) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        if (form.PackageCardCategoryID == null) {
            return BaseOutputForm.failed("分类不能为空");
        }

        return customerLevelPackageCardDiscountConfigService.updateCustomerLevelCategoryDiscountConfig(form);
    }

    /**
     * 批量更新客户级别分类折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPricePackageCardConfig/batchUpdateCustomerLevelCategoryDiscountConfig")
    public BaseOutput batchUpdateCustomerLevelCategoryDiscountConfig(@RequestBody List<CustomerLevelPackageCardCategoryDiscountConfigUpdateForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelPackageCardDiscountConfigService.batchUpdateCustomerLevelCategoryDiscountConfig(form);
    }

    /**
     * 删除客户级别分类折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPricePackageCardConfig/deleteCustomerLevelCategoryDiscountConfig")
    public BaseOutput deleteCustomerLevelCategoryDiscountConfig(@RequestBody CustomerLevelPackageCardCategoryDiscountConfigDeleteForm form) {
        if (form.PackageCardCategoryID == null) {
            return BaseOutputForm.failed("分类不能为空");
        }

        return customerLevelPackageCardDiscountConfigService.deleteCustomerLevelCategoryDiscountConfig(form);
    }

    /**
     * 批量删除客户级别分类折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPricePackageCardConfig/batchDeleteCustomerLevelCategoryDiscountConfig")
    public BaseOutput batchDeleteCustomerLevelCategoryDiscountConfig(@RequestBody List<CustomerLevelPackageCardCategoryDiscountConfigDeleteForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelPackageCardDiscountConfigService.batchDeleteCustomerLevelCategoryDiscountConfig(form);
    }

    /**
     * 查询套餐卡
     *
     * @return
     */
    @PostMapping(value = "/customerPricePackageCardConfig/packageCard")
    public BaseOutput packageCard(@RequestBody CustomerLevelPackageCardDiscountPriceQueryForm form) {
        return customerLevelPackageCardDiscountConfigService.packageCard(form);
    }

    /**
     * 查询已选套餐卡
     * @return
     */
    @PostMapping(value = "/customerPricePackageCardConfig/selectedPackageCard")
    public BaseOutput selectedPackageCard() {
        return customerLevelPackageCardDiscountConfigService.selectedPackageCard();
    }

    /**
     * 查询客户级别套餐卡折扣配置
     *
     * @return
     */
    @PostMapping(value = "/customerPricePackageCardConfig/customerLevelPackageCardDiscountPriceAll")
    public BaseOutput customerLevelPackageCardDiscountPriceAll(@RequestBody CustomerLevelPackageCardDiscountPriceConfigQueryForm form) {
        return customerLevelPackageCardDiscountConfigService.customerLevelPackageCardDiscountPriceAll(form);
    }

    /**
     * 创建客户级别套餐卡折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPricePackageCardConfig/createCustomerLevelPackageCardDiscountPriceConfig")
    public BaseOutput createCustomerLevelPackageCardDiscountPriceConfig(@RequestBody List<CustomerLevelPackageCardDiscountPriceConfigAddForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelPackageCardDiscountConfigService.createCustomerLevelPackageCardDiscountPriceConfig(form);
    }

    /**
     * 更新客户级别套餐卡折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPricePackageCardConfig/updateCustomerLevelPackageCardDiscountConfig")
    public BaseOutput updateCustomerLevelPackageCardDiscountConfig(@RequestBody CustomerLevelPackageCardCardDiscountConfigUpdateForm form) {
        if (form.CustomerLevelDiscountPriceList == null) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        if (form.PackageCardID == null) {
            return BaseOutputForm.failed("分类不能为空");
        }

        return customerLevelPackageCardDiscountConfigService.updateCustomerLevelPackageCardDiscountConfig(form);
    }

    /**
     * 批量更新客户级别套餐卡折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPricePackageCardConfig/batchUpdateCustomerLevelPackageCardDiscountConfig")
    public BaseOutput batchUpdateCustomerLevelPackageCardDiscountConfig(@RequestBody List<CustomerLevelPackageCardCardDiscountConfigUpdateForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelPackageCardDiscountConfigService.batchUpdateCustomerLevelPackageCardDiscountConfig(form);
    }

    /**
     * 删除客户级别套餐卡折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPricePackageCardConfig/deleteCustomerLevelPackageCardDiscountConfig")
    public BaseOutput deleteCustomerLevelPackageCardDiscountConfig(@RequestBody CustomerLevelPackageCardDiscountConfigDeleteForm form) {
        if (form.PackageCardID == null) {
            return BaseOutputForm.failed("分类不能为空");
        }

        return customerLevelPackageCardDiscountConfigService.deleteCustomerLevelPackageCardDiscountConfig(form);
    }

    /**
     * 批量除客户级别套餐卡折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPricePackageCardConfig/batchDeleteCustomerLevelPackageCardDiscountConfig")
    public BaseOutput batchDeleteCustomerLevelPackageCardDiscountConfig(@RequestBody List<CustomerLevelPackageCardDiscountConfigDeleteForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelPackageCardDiscountConfigService.batchDeleteCustomerLevelPackageCardDiscountConfig(form);
    }

}
