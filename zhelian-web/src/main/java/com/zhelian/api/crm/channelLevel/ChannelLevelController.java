package com.zhelian.api.crm.channelLevel;

import com.zhelian.model.CRM.form.input.ChannelTypeAddForm;
import com.zhelian.model.CRM.form.input.ChannelTypeMoveForm;
import com.zhelian.model.CRM.form.input.ChannelTypeQueryForm;
import com.zhelian.model.CRM.form.input.ChannelTypeUpdateForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.service.CRM.channelLevel.ChannelLevelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
public class ChannelLevelController {

    @Autowired
    ChannelLevelService channelLevelService;


    @RequestMapping(value = "/channelLevel/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody ChannelTypeQueryForm form) {
        return channelLevelService.list(form);
    }

    @RequestMapping(value = "/channelLevel/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody ChannelTypeAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("名称不能为空");

        return channelLevelService.create(form);
    }

    @RequestMapping(value = "/channelLevel/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody ChannelTypeUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("名称不能为空");

        return channelLevelService.updateChannelLevel(form);
    }

    @RequestMapping(value = "/channelLevel/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody ChannelTypeMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");
        return channelLevelService.move(form);
    }

    @RequestMapping(value = "/channelLevel/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody ChannelTypeQueryForm form) {
        return channelLevelService.all(form);
    }

}
