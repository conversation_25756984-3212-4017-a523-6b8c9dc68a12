package com.zhelian.api.crm.customerLevelDiscountConfig;

import com.zhelian.model.CRM.form.input.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.service.CRM.customerLevelDiscountConfig.CustomerLevelProductDiscountConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value = "/api")
public class CustomerLevelProductDiscountConfigController {

    @Autowired
    CustomerLevelProductDiscountConfigService customerLevelProductDiscountConfigService;

    /**
     * 查询客户级别折扣配置
     *
     * @return
     */
    @PostMapping(value = "/customerPriceProductConfig/customerLevelDiscountAll")
    public BaseOutput customerLevelDiscountAll() {
        return customerLevelProductDiscountConfigService.customerLevelDiscountAll();
    }

    /**
     * 创建客户级别折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceProductConfig/createCustomerLevelDiscountConfig")
    public BaseOutput createCustomerLevelDiscountConfig(@RequestBody List<CustomerLevelProductDiscountConfigAddForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelProductDiscountConfigService.createCustomerLevelDiscountConfig(form);
    }

    /**
     * 查询客户级别分类折扣配置
     *
     * @return
     */
    @PostMapping(value = "/customerPriceProductConfig/customerLevelCategoryDiscountAll")
    public BaseOutput customerLevelCategoryDiscountAll(@RequestBody CustomerLevelProductCategoryDiscountConfigQueryForm form) {
        return customerLevelProductDiscountConfigService.customerLevelCategoryDiscountAll(form);
    }

    /**
     * 查询分类
     * @return
     */
    @PostMapping(value = "/customerPriceProductConfig/category")
    public BaseOutput category() {
        return customerLevelProductDiscountConfigService.category();
    }

    /**
     * 查询已选分类
     * @return
     */
    @PostMapping(value = "/customerPriceProductConfig/selectedCategory")
    public BaseOutput selectedCategory() {
        return customerLevelProductDiscountConfigService.selectedCategory();
    }

    /**
     * 创建客户级别分类折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceProductConfig/createCustomerLevelCategoryDiscountConfig")
    public BaseOutput createCustomerLevelCategoryDiscountConfig(@RequestBody List<CustomerLevelProductCategoryDiscountConfigAddForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelProductDiscountConfigService.createCustomerLevelCategoryDiscountConfig(form);
    }

    /**
     * 更新客户级别分类折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceProductConfig/updateCustomerLevelCategoryDiscountConfig")
    public BaseOutput updateCustomerLevelCategoryDiscountConfig(@RequestBody CustomerLevelProductCategoryDiscountConfigUpdateForm form) {
        if (form.CustomerLevelDiscountPriceList == null) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        if (form.ProductCategoryID == null) {
            return BaseOutputForm.failed("分类不能为空");
        }

        return customerLevelProductDiscountConfigService.updateCustomerLevelCategoryDiscountConfig(form);
    }

    /**
     * 批量更新客户级别分类折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceProductConfig/batchUpdateCustomerLevelCategoryDiscountConfig")
    public BaseOutput batchUpdateCustomerLevelCategoryDiscountConfig(@RequestBody List<CustomerLevelProductCategoryDiscountConfigUpdateForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelProductDiscountConfigService.batchUpdateCustomerLevelCategoryDiscountConfig(form);
    }

    /**
     * 删除客户级别分类折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceProductConfig/deleteCustomerLevelCategoryDiscountConfig")
    public BaseOutput deleteCustomerLevelCategoryDiscountConfig(@RequestBody CustomerLevelProductCategoryDiscountConfigDeleteForm form) {
        if (form.ProductCategoryID == null) {
            return BaseOutputForm.failed("分类不能为空");
        }

        return customerLevelProductDiscountConfigService.deleteCustomerLevelCategoryDiscountConfig(form);
    }

    /**
     * 批量删除客户级别分类折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceProductConfig/batchDeleteCustomerLevelCategoryDiscountConfig")
    public BaseOutput batchDeleteCustomerLevelCategoryDiscountConfig(@RequestBody List<CustomerLevelProductCategoryDiscountConfigDeleteForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelProductDiscountConfigService.batchDeleteCustomerLevelCategoryDiscountConfig(form);
    }

    /**
     * 查询产品
     *
     * @return
     */
    @PostMapping(value = "/customerPriceProductConfig/product")
    public BaseOutput product(@RequestBody CustomerLevelProductDiscountPriceQueryForm form) {
        return customerLevelProductDiscountConfigService.product(form);
    }

    /**
     * 查询已选产品
     * @return
     */
    @PostMapping(value = "/customerPriceProductConfig/selectedProduct")
    public BaseOutput selectedProduct() {
        return customerLevelProductDiscountConfigService.selectedProduct();
    }

    /**
     * 查询客户级别产品折扣配置
     *
     * @return
     */
    @PostMapping(value = "/customerPriceProductConfig/customerLevelProductDiscountPriceAll")
    public BaseOutput customerLevelProductDiscountPriceAll(@RequestBody CustomerLevelProductDiscountPriceConfigQueryForm form) {
        return customerLevelProductDiscountConfigService.customerLevelProductDiscountPriceAll(form);
    }

    /**
     * 创建客户级别产品折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceProductConfig/createCustomerLevelProductDiscountPriceConfig")
    public BaseOutput createCustomerLevelProductDiscountPriceConfig(@RequestBody List<CustomerLevelProductDiscountPriceConfigAddForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelProductDiscountConfigService.createCustomerLevelProductDiscountPriceConfig(form);
    }

    /**
     * 更新客户级别通用次卡折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceProductConfig/updateCustomerLevelProductDiscountConfig")
    public BaseOutput updateCustomerLevelProductDiscountConfig(@RequestBody CustomerLevelProductCardDiscountConfigUpdateForm form) {
        if (form.CustomerLevelDiscountPriceList == null) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        if (form.ProductID == null) {
            return BaseOutputForm.failed("分类不能为空");
        }

        return customerLevelProductDiscountConfigService.updateCustomerLevelProductDiscountConfig(form);
    }

    /**
     * 批量更新客户级别通用次卡折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceProductConfig/batchUpdateCustomerLevelProductDiscountConfig")
    public BaseOutput batchUpdateCustomerLevelProductDiscountConfig(@RequestBody List<CustomerLevelProductCardDiscountConfigUpdateForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelProductDiscountConfigService.batchUpdateCustomerLevelProductDiscountConfig(form);
    }

    /**
     * 删除客户级别通用次卡折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceProductConfig/deleteCustomerLevelProductDiscountConfig")
    public BaseOutput deleteCustomerLevelProductDiscountConfig(@RequestBody CustomerLevelProductDiscountConfigDeleteForm form) {
        if (form.ProductID == null) {
            return BaseOutputForm.failed("分类不能为空");
        }

        return customerLevelProductDiscountConfigService.deleteCustomerLevelProductDiscountConfig(form);
    }

    /**
     * 批量除客户级别通用次卡折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceProductConfig/batchDeleteCustomerLevelProductDiscountConfig")
    public BaseOutput batchDeleteCustomerLevelProductDiscountConfig(@RequestBody List<CustomerLevelProductDiscountConfigDeleteForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelProductDiscountConfigService.batchDeleteCustomerLevelProductDiscountConfig(form);
    }

}
