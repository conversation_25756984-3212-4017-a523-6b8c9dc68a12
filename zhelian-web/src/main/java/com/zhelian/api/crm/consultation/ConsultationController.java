package com.zhelian.api.crm.consultation;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.CRM.form.input.BatchDeleteForm;
import com.zhelian.model.CRM.form.input.ConsultationForm;
import com.zhelian.model.CRM.form.input.ConsultationQueryForm;
import com.zhelian.model.CRM.form.output.ConsultationOutputForm;
import com.zhelian.service.CRM.consultation.ConsultationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/consultation")
public class ConsultationController {

    @Autowired
    private ConsultationService consultationService;

    @PostMapping("/create")
    public BaseOutputForm<ConsultationOutputForm> create(@RequestBody ConsultationForm form) {
        return consultationService.createConsultation(form);
    }

    @PostMapping("/delete")
    public BaseOutput delete(@RequestBody BatchDeleteForm form) {
        return consultationService.batchDeleteConsultations(form);
    }

    @PostMapping("/update")
    public BaseOutputForm<ConsultationOutputForm> update(@RequestBody ConsultationForm form) {
        return consultationService.updateConsultation(form);
    }

    @GetMapping("/view/{id}")
    public BaseOutputForm<ConsultationOutputForm> view(@PathVariable Integer id) {
        return consultationService.getConsultationById(id);
    }

    @PostMapping("/query")
    public BasePageInfo<ConsultationOutputForm> query(@RequestBody ConsultationQueryForm form) {
        return consultationService.queryConsultations(form);
    }
}