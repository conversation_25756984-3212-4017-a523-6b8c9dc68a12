package com.zhelian.api.crm.customerGrowth;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.CustomerGrowthStatusUpdateForm;
import com.zhelian.model.ibeauty.form.input.CustomerGrowthUpdateForm;
import com.zhelian.service.CRM.customerGrowth.CustomerGrowthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class CustomerGrowthController {

    @Autowired
    CustomerGrowthService customerGrowthService;

    @RequestMapping(value = "/customerGrowth/all", method = {RequestMethod.POST})
    public BaseOutput all() {
        return customerGrowthService.all();
    }

    @RequestMapping(value = "/customerGrowth/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody CustomerGrowthUpdateForm form) {
        return customerGrowthService.updateCustomerGrowth(form);
    }

    @RequestMapping(value = "/customerGrowth/status", method = {RequestMethod.POST})
    public BaseOutput status() {
        return customerGrowthService.status();
    }

    @RequestMapping(value = "/customerGrowth/updateStatus", method = {RequestMethod.POST})
    public BaseOutput updateStatus(@RequestBody CustomerGrowthStatusUpdateForm form) {
        return customerGrowthService.updateStatus(form);
    }
}