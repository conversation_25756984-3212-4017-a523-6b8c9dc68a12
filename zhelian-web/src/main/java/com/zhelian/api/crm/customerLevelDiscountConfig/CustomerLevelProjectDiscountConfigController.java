package com.zhelian.api.crm.customerLevelDiscountConfig;

import com.zhelian.model.CRM.form.input.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.service.CRM.customerLevelDiscountConfig.CustomerLevelProjectDiscountConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value = "/api")
public class CustomerLevelProjectDiscountConfigController {

    @Autowired
    CustomerLevelProjectDiscountConfigService customerLevelProjectDiscountConfigService;

    /**
     * 查询客户级别折扣配置
     *
     * @return
     */
    @PostMapping(value = "/customerPriceProjectConfig/customerLevelDiscountAll")
    public BaseOutput customerLevelDiscountAll() {
        return customerLevelProjectDiscountConfigService.customerLevelDiscountAll();
    }

    /**
     * 创建客户级别折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceProjectConfig/createCustomerLevelDiscountConfig")
    public BaseOutput createCustomerLevelDiscountConfig(@RequestBody List<CustomerLevelProjectDiscountConfigAddForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelProjectDiscountConfigService.createCustomerLevelDiscountConfig(form);
    }

    /**
     * 查询客户级别分类折扣配置
     *
     * @return
     */
    @PostMapping(value = "/customerPriceProjectConfig/customerLevelCategoryDiscountAll")
    public BaseOutput customerLevelCategoryDiscountAll(@RequestBody CustomerLevelProjectCategoryDiscountConfigQueryForm form) {
        return customerLevelProjectDiscountConfigService.customerLevelCategoryDiscountAll(form);
    }

    /**
     * 查询分类
     * @return
     */
    @PostMapping(value = "/customerPriceProjectConfig/category")
    public BaseOutput category() {
        return customerLevelProjectDiscountConfigService.category();
    }

    /**
     * 查询已选分类
     * @return
     */
    @PostMapping(value = "/customerPriceProjectConfig/selectedCategory")
    public BaseOutput selectedCategory() {
        return customerLevelProjectDiscountConfigService.selectedCategory();
    }


    /**
     * 创建客户级别分类折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceProjectConfig/createCustomerLevelCategoryDiscountConfig")
    public BaseOutput createCustomerLevelCategoryDiscountConfig(@RequestBody List<CustomerLevelProjectCategoryDiscountConfigAddForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelProjectDiscountConfigService.createCustomerLevelCategoryDiscountConfig(form);
    }

    /**
     * 更新客户级别分类折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceProjectConfig/updateCustomerLevelCategoryDiscountConfig")
    public BaseOutput updateCustomerLevelCategoryDiscountConfig(@RequestBody CustomerLevelProjectCategoryDiscountConfigUpdateForm form) {
        if (form.CustomerLevelDiscountPriceList == null) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        if (form.ProjectCategoryID == null) {
            return BaseOutputForm.failed("分类不能为空");
        }

        return customerLevelProjectDiscountConfigService.updateCustomerLevelCategoryDiscountConfig(form);
    }

    /**
     * 批量更新客户级别分类折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceProjectConfig/batchUpdateCustomerLevelCategoryDiscountConfig")
    public BaseOutput batchUpdateCustomerLevelCategoryDiscountConfig(@RequestBody List<CustomerLevelProjectCategoryDiscountConfigUpdateForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelProjectDiscountConfigService.batchUpdateCustomerLevelCategoryDiscountConfig(form);
    }

    /**
     * 删除客户级别分类折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceProjectConfig/deleteCustomerLevelCategoryDiscountConfig")
    public BaseOutput deleteCustomerLevelCategoryDiscountConfig(@RequestBody CustomerLevelProjectCategoryDiscountConfigDeleteForm form) {
        if (form.ProjectCategoryID == null) {
            return BaseOutputForm.failed("分类不能为空");
        }

        return customerLevelProjectDiscountConfigService.deleteCustomerLevelCategoryDiscountConfig(form);
    }

    /**
     * 批量删除客户级别分类折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceProjectConfig/batchDeleteCustomerLevelCategoryDiscountConfig")
    public BaseOutput batchDeleteCustomerLevelCategoryDiscountConfig(@RequestBody List<CustomerLevelProjectCategoryDiscountConfigDeleteForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelProjectDiscountConfigService.batchDeleteCustomerLevelCategoryDiscountConfig(form);
    }

    /**
     * 查询项目
     *
     * @return
     */
    @PostMapping(value = "/customerPriceProjectConfig/project")
    public BaseOutput project(@RequestBody CustomerLevelProjectDiscountPriceQueryForm form) {
        return customerLevelProjectDiscountConfigService.project(form);
    }

    /**
     * 查询已选项目
     * @return
     */
    @PostMapping(value = "/customerPriceProjectConfig/selectedProject")
    public BaseOutput selectedProject() {
        return customerLevelProjectDiscountConfigService.selectedProject();
    }


    /**
     * 查询客户级别项目折扣配置
     *
     * @return
     */
    @PostMapping(value = "/customerPriceProjectConfig/customerLevelProjectDiscountPriceAll")
    public BaseOutput customerLevelProjectDiscountPriceAll(@RequestBody CustomerLevelProjectDiscountPriceConfigQueryForm form) {
        return customerLevelProjectDiscountConfigService.customerLevelProjectDiscountPriceAll(form);
    }

    /**
     * 创建客户级别项目折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceProjectConfig/createCustomerLevelProjectDiscountPriceConfig")
    public BaseOutput createCustomerLevelProjectDiscountPriceConfig(@RequestBody List<CustomerLevelProjectDiscountPriceConfigAddForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelProjectDiscountConfigService.createCustomerLevelProjectDiscountPriceConfig(form);
    }

    /**
     * 更新客户级别项目折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceProjectConfig/updateCustomerLevelProjectDiscountConfig")
    public BaseOutput updateCustomerLevelProjectDiscountConfig(@RequestBody CustomerLevelProjectCardDiscountConfigUpdateForm form) {
        if (form.CustomerLevelDiscountPriceList == null) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        if (form.ProjectID == null) {
            return BaseOutputForm.failed("分类不能为空");
        }

        return customerLevelProjectDiscountConfigService.updateCustomerLevelProjectDiscountConfig(form);
    }

    /**
     * 批量更新客户级别项目折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceProjectConfig/batchUpdateCustomerLevelProjectDiscountConfig")
    public BaseOutput batchUpdateCustomerLevelProjectDiscountConfig(@RequestBody List<CustomerLevelProjectCardDiscountConfigUpdateForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelProjectDiscountConfigService.batchUpdateCustomerLevelProjectDiscountConfig(form);
    }

    /**
     * 删除客户级别项目折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceProjectConfig/deleteCustomerLevelProjectDiscountConfig")
    public BaseOutput deleteCustomerLevelProjectDiscountConfig(@RequestBody CustomerLevelProjectDiscountConfigDeleteForm form) {
        if (form.ProjectID == null) {
            return BaseOutputForm.failed("分类不能为空");
        }

        return customerLevelProjectDiscountConfigService.deleteCustomerLevelProjectDiscountConfig(form);
    }


    /**
     * 批量除客户级别项目折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceProjectConfig/batchDeleteCustomerLevelProjectDiscountConfig")
    public BaseOutput batchDeleteCustomerLevelProjectDiscountConfig(@RequestBody List<CustomerLevelProjectDiscountConfigDeleteForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelProjectDiscountConfigService.batchDeleteCustomerLevelProjectDiscountConfig(form);
    }
}
