package com.zhelian.api.crm.callpackRule;

import com.zhelian.model.CRM.form.input.CallbackRuleAddForm;
import com.zhelian.model.CRM.form.input.CallbackRuleDeleteForm;
import com.zhelian.model.CRM.form.input.CallbackRuleQueryForm;
import com.zhelian.model.CRM.form.input.CallbackRuleUpdateForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.service.CRM.callbackRule.CallbackRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api/callbackRule")
public class CallbackRuleController {

    @Autowired
    CallbackRuleService callbackRuleService;


    @RequestMapping(value = "/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody CallbackRuleQueryForm form) {

        return callbackRuleService.all(form);
    }

    @RequestMapping(value = "/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody CallbackRuleAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("名称不能为空");
        if (form.CallbackMethodID == null)
            return BaseOutput.failed("回访方式不能为空");
        if (form.HandlerType == null)
            return BaseOutput.failed("回访人类型不能为空");
        return callbackRuleService.create(form);
    }

    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody CallbackRuleUpdateForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("名称不能为空");
        if (form.CallbackRuleID == null)
            return BaseOutput.failed("规则ID不能为空");
        if (form.CallbackMethodID == null)
            return BaseOutput.failed("回访方式不能为空");
        if (form.HandlerType == null)
            return BaseOutput.failed("回访人类型不能为空");
        return callbackRuleService.update(form);
    }

    @RequestMapping(value = "/detail", method = {RequestMethod.POST})
    public BaseOutput detail(@RequestBody CallbackRuleUpdateForm form) {
        if (form.CallbackRuleID == null)
            return BaseOutput.failed("规则ID不能为空");

        return callbackRuleService.detail(form);
    }

    @RequestMapping(value = "/delete", method = {RequestMethod.POST})
    public BaseOutput delete(@RequestBody CallbackRuleDeleteForm form) {
        if (form.CallbackRuleID == null)
            return BaseOutput.failed("规则ID不能为空");
        if (form.CallbackPlanID == null)
            return BaseOutput.failed("计划ID不能为空");
        return callbackRuleService.delete(form);
    }
}