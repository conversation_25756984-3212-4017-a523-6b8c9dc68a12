package com.zhelian.api.crm.followUpRule;

import com.zhelian.model.CRM.form.input.FollowUpRuleAddForm;
import com.zhelian.model.CRM.form.input.FollowUpRuleDetailQueryForm;
import com.zhelian.model.CRM.form.input.FollowUpRuleQueryForm;
import com.zhelian.model.CRM.form.input.FollowUpRuleUpdateForm;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.service.CRM.followUpRule.FollowUpRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class FollowUpRuleController {

    @Autowired
    FollowUpRuleService followUpRuleService;

    @RequestMapping(value = "/followUpRule/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody FollowUpRuleQueryForm form) {
        return followUpRuleService.all(form);
    }


    @RequestMapping(value = "/followUpRule/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody FollowUpRuleAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("名称不能为空");
        if (form.FollowUpMethodID == null)
            return BaseOutput.failed("跟进方式不能为空");
        if (form.ScheduleType == null)
            return BaseOutput.failed("计划方式不能为空");
        if (form.ScheduleBeginDate == null)
            return BaseOutput.failed("计划开始时间不能为空");
        if (form.ScheduleEndDate == null)
            return BaseOutput.failed("计划结束时间不能为空");
        return followUpRuleService.create(form);
    }

    @RequestMapping(value = "/followUpRule/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody FollowUpRuleUpdateForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("名称不能为空");
        if (form.FollowUpMethodID == null)
            return BaseOutput.failed("跟进方式不能为空");
        if (form.ScheduleType == null)
            return BaseOutput.failed("计划方式不能为空");
        if (form.ScheduleBeginDate == null)
            return BaseOutput.failed("计划开始时间不能为空");
        if (form.ScheduleEndDate == null)
            return BaseOutput.failed("计划结束时间不能为空");
        if (form.Active == null)
            return BaseOutput.failed("有效性不能为空");
        return followUpRuleService.update(form);
    }

    @RequestMapping(value = "/followUpRule/condition", method = {RequestMethod.POST})
    public BaseOutput condition() {
        return followUpRuleService.condition();
    }


    @RequestMapping(value = "/followUpRule/detail", method = {RequestMethod.POST})
    public BaseOutput detail(@RequestBody FollowUpRuleDetailQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        return followUpRuleService.detail(form);
    }

    @RequestMapping(value = "/followUpRule/createFollowUp", method = {RequestMethod.POST})
    public BaseOutput createFollowUp() {

        return followUpRuleService.createFollowUp();
    }


}