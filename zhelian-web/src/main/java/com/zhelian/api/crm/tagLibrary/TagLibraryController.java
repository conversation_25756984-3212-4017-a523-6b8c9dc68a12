package com.zhelian.api.crm.tagLibrary;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TagLibraryAddForm;
import com.zhelian.model.ibeauty.form.input.TagLibraryDeleteForm;
import com.zhelian.model.ibeauty.form.input.TagLibraryQueryForm;
import com.zhelian.model.ibeauty.form.input.TagLibraryUpdateForm;
import com.zhelian.service.CRM.tagLibrary.TagLibraryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class TagLibraryController {

    @Autowired
    TagLibraryService tagLibraryService;

    @RequestMapping(value = "/tagLibrary/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody TagLibraryQueryForm form) {
        return tagLibraryService.listTagLibrary(form);
    }

    @RequestMapping(value = "/tagLibrary/all", method = {RequestMethod.POST})
    public BaseOutput all() {
        return tagLibraryService.allTagLibrary(null);
    }

    @RequestMapping(value = "/tagLibrary/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody TagLibraryAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("标签名称不能为空");

        return tagLibraryService.createTagLibrary(form);
    }

    @RequestMapping(value = "/tagLibrary/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody TagLibraryUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("标签名称不能为空");

        return tagLibraryService.updateTagLibrary(form);
    }

    @RequestMapping(value = "/tagLibrary/delete", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody TagLibraryDeleteForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        return tagLibraryService.deleteTagLibrary(form);
    }

}