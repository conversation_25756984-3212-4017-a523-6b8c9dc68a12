package com.zhelian.api.crm.customer;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.model.ibeauty.form.output.BeautyDeviceResponse;
import com.zhelian.model.ibeauty.form.output.CustomerFileDownloadQueryForm;
import com.zhelian.model.miniprogram.form.input.MiniCustomerQueryForm;
import com.zhelian.service.CRM.customer.CustomerService;
import com.zhelian.service.utils.SignatureVerificationUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;


@RestController
@RequestMapping(value = "/api")
public class CustomerController {

    @Value("${beauty.device.public-key}")
    private String beautyDevicePublicKey;

    @Autowired
    CustomerService customerService;

    @RequestMapping(value = "/customer/consultant", method = {RequestMethod.POST})
    public BaseOutput consultant() {
        return customerService.consultant(null);
    }

    @RequestMapping(value = "/customer/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody CustomerQueryForm form) {
        return customerService.customerList(form);
    }

    @RequestMapping(value = "/customer/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody CustomerQueryForm form) {
        return customerService.allCustomer(form);
    }

    @RequestMapping(value = "/customer/info", method = {RequestMethod.POST})
    public BaseOutput info(@RequestBody CustomerInfoQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        return customerService.customerInfo(form.ID);
    }

    @RequestMapping(value = "/customer/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody CustomerAddForm form) throws Exception {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("顾客名称不能为空");
        if (form.PhoneNumber == null)
            return BaseOutput.failed("手机号不能为空");
        if (form.Gender == null)
            return BaseOutput.failed("性别不能为空");
        if (form.IsMember == null)
            return BaseOutput.failed("是否会员不能为空");
        if (form.IsLockMemberLevel == null)
            return BaseOutput.failed("是否锁定会员等级不能为空");
        if (form.IsMember && form.CustomerLevelID == null) {
            return BaseOutput.failed("请选择等级");
        }

        return customerService.createCustomer(form);
    }

    @RequestMapping(value = "/customer/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody CustomerUpdateForm form) throws Exception {
        if (form.ID == null)
            return BaseOutput.failed("顾客编号不能为空");
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("顾客名称不能为空");
        if (form.PhoneNumber == null)
            return BaseOutput.failed("手机号不能为空");
        if (form.Gender == null)
            return BaseOutput.failed("性别不能为空");
        return customerService.updateCustomer(form);
    }

    @RequestMapping(value = "/customer/getCustomerTagLibrary", method = {RequestMethod.POST})
    public BaseOutput getCustomerTagLibrary(@RequestBody CustTagLibraryQueryForm form) {
        return customerService.getCustomerTagLibrary(form);
    }

    @RequestMapping(value = "/customer/updateCustomerTagLibrary", method = {RequestMethod.POST})
    public BaseOutput updateCustomerTagLibrary(@RequestBody CustTagLibraryUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return customerService.updateCustomerTagLibrary(form);
    }

    /**
     * 上传头像
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/customer/uploadImage", method = {RequestMethod.POST})
    public BaseOutput uploadImage(@RequestBody CustomerImageAddForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return customerService.uploadImage(form);
    }

    @RequestMapping(value = "/customer/accountInfo", method = {RequestMethod.POST})
    public BaseOutput accountInfo(@RequestBody CustomerImageAddForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return customerService.accountInfo(form);
    }

    @RequestMapping(value = "/customer/getDetailByCode", method = {RequestMethod.POST})
    public BaseOutput getDetailByCode(@RequestBody CustomerCodeQueryForm form) {
        if (form.Code == null)
            return BaseOutput.failed("顾客编号不能为空");
        return customerService.getDetailByCode(form);
    }

    @RequestMapping(value = "/customer/judgeCode", method = {RequestMethod.POST})
    public BaseOutput judgeCode(@RequestBody CustomerCodeQueryForm form) {
        if (form.Code == null)
            return BaseOutput.failed("顾客编号不能为空");
        return customerService.judgeCode(form);
    }

    /**
     * 顾客详情
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/customer/customerDetail", method = {RequestMethod.POST})
    public BaseOutput customerDetail(@RequestBody CustomerDetailQueryForm form) {
        return customerService.getCustomerDetail(form);
    }

    @RequestMapping(value = "/customer/createBasicFile", method = {RequestMethod.POST})
    public BaseOutput createBasicFile(@RequestBody CustomerBasicFileInfoForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");

        return customerService.createBasicFile(form);
    }

    @RequestMapping(value = "/customer/getBasicFile", method = {RequestMethod.POST})
    public BaseOutput getBasicFile(@RequestBody CustomerBasicFileInfoForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return customerService.getBasicFile(form);
    }

    @RequestMapping(value = "/customer/file", method = {RequestMethod.POST})
    public BaseOutput getFile(@RequestBody CustomerDetailQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return customerService.getFile(form);
    }

    @RequestMapping(value = "/customer/deleteFile", method = {RequestMethod.POST})
    public BaseOutput deleteFile(@RequestBody CustomerFileDeleteForm form) throws Exception {
        if (form.ID == null)
            return BaseOutput.failed("编号不能为空");
        return customerService.deleteFile(form);
    }

    @RequestMapping(value = "/customer/getFile", method = {RequestMethod.POST})
    public BaseOutput getFile(@RequestBody CustomerFileDownloadQueryForm form, HttpServletResponse response) {
        return customerService.getFile(form, response);

    }

    /**
     * 顾客跟进
     *
     * @param form
     * @param
     * @return
     */
    @RequestMapping(value = "/customer/followUp", method = {RequestMethod.POST})
    public BaseOutput followUpLog(@RequestBody CustomerDetailQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return customerService.followUpLog(form);

    }


    /**
     * 顾客接诊
     *
     * @param form
     * @param
     * @return
     */
    @RequestMapping(value = "/customer/diagnosisRecord", method = {RequestMethod.POST})
    public BaseOutput diagnosisRecord(@RequestBody CustomerDetailQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return customerService.diagnosisRecord(form);

    }

    /**
     * 项目列表
     *
     * @param form
     * @param
     * @return
     */
    @RequestMapping(value = "/customer/project", method = {RequestMethod.POST})
    public BaseOutput project(@RequestBody CustomerProjectQueryForm form) {
        return customerService.project(form);

    }

    /**
     * 照片对比新增
     *
     * @param form
     * @param
     * @return
     */
    @RequestMapping(value = "/customer/createPhotoCompare", method = {RequestMethod.POST})
    public BaseOutput createPhotoCompare(@RequestBody CustomerPhotoCompareAddForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        if (form.ProjectID == null)
            return BaseOutput.failed("项目编号不能为空");
        return customerService.createPhotoCompare(form);

    }

    /**
     * 照片对比
     *
     * @param form
     * @param
     * @return
     */
    @RequestMapping(value = "/customer/photoCompare", method = {RequestMethod.POST})
    public BaseOutput photoCompare(@RequestBody CustomerPhotoCompareQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");
        return customerService.photoCompare(form);

    }

    /**
     * 追加照片
     *
     * @param form
     * @param
     * @return
     */
    @RequestMapping(value = "/customer/addToPhoto", method = {RequestMethod.POST})
    public BaseOutput addToPhoto(@RequestBody CustomerAddToPhotoAddForm form) {
        if (form.PhotoCompareID == null)
            return BaseOutput.failed("照片对比ID不能为空");
        if (form.PhotoType == null)
            return BaseOutput.failed("照片类型不能为空");
        if (form.AttachmentURL == null)
            return BaseOutput.failed("附件URL不能为空");
        return customerService.addToPhoto(form);

    }

    /**
     * 删除图片
     *
     * @param form
     * @param
     * @return
     */
    @RequestMapping(value = "/customer/deletePhoto", method = {RequestMethod.POST})
    public BaseOutput deletePhoto(@RequestBody CustomerPhotoCompareDeleteForm form) {
        if (form.ID == null)
            return BaseOutput.failed("照片ID不能为空");
        return customerService.deletePhoto(form);

    }


    /**
     * 设置会员等级
     *
     * @param form
     * @param
     * @return
     */
    @RequestMapping(value = "/customer/saveLevel", method = {RequestMethod.POST})
    public BaseOutput saveLevel(@RequestBody CustomerLevelSetAddForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客ID不能为空");
        if (form.CustomerLevelID == null)
            return BaseOutput.failed("顾客等级ID不能为空");
        if (form.IsLockMemberLevel == null)
            return BaseOutput.failed("是否锁定会员等级不能为空");
        return customerService.saveLevel(form);

    }

    /**
     * 移除会员等级
     *
     * @param form
     * @param
     * @return
     */
    @RequestMapping(value = "/customer/deleteLevel", method = {RequestMethod.POST})
    public BaseOutput deleteLevel(@RequestBody CustomerLevelSetAddForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客ID不能为空");

        return customerService.deleteLevel(form);

    }

    /**
     * 获取顾客成长值
     *
     * @param form
     * @param
     * @return
     */
    @RequestMapping(value = "/customer/growthValue", method = {RequestMethod.POST})
    public BaseOutput growthValue(@RequestBody CustomerLevelSetAddForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客ID不能为空");

        return customerService.growthValue(form);

    }

    /**
     * 增减成长值
     *
     * @param form
     * @param
     * @return
     */
    @RequestMapping(value = "/customer/addGrowthValue", method = {RequestMethod.POST})
    public BaseOutput addGrowthValue(@RequestBody CustomerGrowthValueAddForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客ID不能为空");
        if (form.GrowthValue == null)
            return BaseOutput.failed("成长值不能为空");
        return customerService.addGrowthValue(form);

    }


    @RequestMapping(value = "/customer/record", method = {RequestMethod.POST})
    public BaseOutput record(@RequestBody CustomerRecordSessionQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客ID不能为空");
        if (form.Type == null)
            return BaseOutput.failed("类型不能为空");
        if (form.PageIndex == null)
            return BaseOutput.failed("当前页码不能为空");
        if (form.PageSize == null)
            return BaseOutput.failed("当前页展示数不能为空");
        return customerService.record(form);

    }

    @RequestMapping(value = "/customer/yingxiaoyunLabel", method = {RequestMethod.POST})
    public BaseOutput label(@RequestBody CustomerRecordSessionQueryForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客ID不能为空");

        return customerService.label(form);

    }

    @RequestMapping(value = "/customer/batchUpdateServicer", method = {RequestMethod.POST})
    public BaseOutput batchUpdateServicer(@RequestBody CustomerBatchUpdateForm form) {
        if (form.CustomerID.length == 0)
            return BaseOutput.failed("请选择顾客");
        return customerService.batchUpdateServicer(form);
    }

    @RequestMapping(value = "/customer/customerDetailSelfList", method = {RequestMethod.POST})
    public BaseOutput getCustomerDetailSelfList(@RequestBody MiniCustomerQueryForm form) {
        return customerService.getCustomerDetailSelfList(form);
    }

    @RequestMapping(value = "/customer/excelDisPlayPhone", method = {RequestMethod.POST})
    public BaseOutput excelDisPlayPhone(@RequestBody CustomerQueryForm form, HttpServletResponse response) {
        return customerService.excelDisPlayPhone(form, response);
    }

    @RequestMapping(value = "/customer/excelNoDisPlayPhone", method = {RequestMethod.POST})
    public BaseOutput excelNoDisPlayPhone(@RequestBody CustomerQueryForm form, HttpServletResponse response) {
        return customerService.excelNoDisPlayPhone(form, response);
    }

    @RequestMapping(value = "/customer/customerBelongEntity", method = {RequestMethod.POST})
    public BaseOutput customerBelongEntity(@RequestBody CustomerBasicFileInfoForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");

        return customerService.customerBelongEntity(form);
    }

    @RequestMapping(value = "/customer/updateCustomerBelongEntity", method = {RequestMethod.POST})
    public BaseOutput updateCustomerBelongEntity(@RequestBody CustomerBelongEntityUpdateForm form) {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客编号不能为空");

        return customerService.updateCustomerBelongEntity(form);
    }

    /**
     * 客户档案列表接口 - 美际小肤设备专用
     * 响应格式：{"code": 0, "message": "success", "data": [...]}
     * 包含签名校验：从HTTP头部sign字段获取签名字符串
     *
     * @param form 查询参数
     * @param sign 签名字符串（从HTTP头部获取）
     * @return 客户档案列表，符合美际小肤设备响应格式
     */
    @RequestMapping(value = "/open/customer/profile/list", method = {RequestMethod.POST})
    public BeautyDeviceResponse customerProfileList(
            @RequestBody CustomerProfileListQueryForm form,
            @RequestHeader(value = "sign") String sign) {
        // 参数验证
        if (form.channel_id == null)
            return BeautyDeviceResponse.failed("机构编号不能为空");
        if (form.store_id == null)
            return BeautyDeviceResponse.failed("门店编号不能为空");
        if (form.scanner_id == null)
            return BeautyDeviceResponse.failed("设备编号不能为空");

        // 签名校验
        if (StringUtils.hasText(sign)) {
            Map<String, String> params = SignatureVerificationUtil.buildParamsMap(form);
            boolean isSignatureValid = SignatureVerificationUtil.verifySignature(params, sign, beautyDevicePublicKey);
            
            if (!isSignatureValid) {
                return BeautyDeviceResponse.failed("签名校验失败");
            }
        } else {
            // 如果没有提供签名，返回错误
             return BeautyDeviceResponse.failed("缺少签名字段");
        }

        return customerService.customerProfileList(form);
    }
    
    /**
     * 皮肤报告接收接口 - 美际小肤设备专用
     * 响应格式：{"code": 0, "message": "success"}
     * 包含签名校验：从HTTP头部sign字段获取签名字符串
     *
     * @param form 皮肤报告数据
     * @param sign 签名字符串（从HTTP头部获取）
     * @return 接收结果，符合美际小肤设备响应格式
     */
    @RequestMapping(value = "/open/customer/skin/report/receive", method = {RequestMethod.POST})
    public BeautyDeviceResponse skinReportReceive(
            @RequestBody CustomerSkinReportReceiveForm form,
            @RequestHeader(value = "sign") String sign) {
        // 参数验证
        if (form.customer_id == null)
            return BeautyDeviceResponse.failed("客户档案编号不能为空");
        if (form.report_id == null)
            return BeautyDeviceResponse.failed("报告编号不能为空");
        if (form.channel_id == null)
            return BeautyDeviceResponse.failed("机构编号不能为空");
        if (form.store_id == null)
            return BeautyDeviceResponse.failed("门店编号不能为空");
        if (form.scanner_id == null)
            return BeautyDeviceResponse.failed("扫描设备编号不能为空");
        if (form.created_time == null)
            return BeautyDeviceResponse.failed("扫描时间不能为空");
        if (form.icon == null)
            return BeautyDeviceResponse.failed("皮肤预览ICON图片不能为空");
        if (form.view_url == null)
            return BeautyDeviceResponse.failed("皮肤报告PC端链接不能为空");
        if (form.share_url == null)
            return BeautyDeviceResponse.failed("皮肤报告分享链接不能为空");
        if (form.resources == null || form.resources.isEmpty())
            return BeautyDeviceResponse.failed("皮肤资源文件列表不能为空");
        if (form.contents == null || form.contents.isEmpty())
            return BeautyDeviceResponse.failed("皮肤报告相貌特征不能为空");

        // 签名校验
        if (StringUtils.hasText(sign)) {
            Map<String, String> params = SignatureVerificationUtil.buildParamsMap(form);
            boolean isSignatureValid = SignatureVerificationUtil.verifySignature(params, sign, beautyDevicePublicKey);
            
            if (!isSignatureValid) {
                return BeautyDeviceResponse.failed("签名校验失败");
            }
        } else {
            // 如果没有提供签名，返回错误
             return BeautyDeviceResponse.failed("缺少签名字段");
        }

        return customerService.skinReportReceive(form);
    }
}