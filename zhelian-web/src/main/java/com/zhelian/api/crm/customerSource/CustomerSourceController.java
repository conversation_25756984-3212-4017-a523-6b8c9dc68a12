package com.zhelian.api.crm.customerSource;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.CustomerSourceAddForm;
import com.zhelian.model.ibeauty.form.input.CustomerSourceMoveForm;
import com.zhelian.model.ibeauty.form.input.CustomerSourceQueryForm;
import com.zhelian.model.ibeauty.form.input.CustomerSourceUpdateForm;
import com.zhelian.service.CRM.customerSource.CustomerSourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
public class CustomerSourceController {

    @Autowired
    CustomerSourceService customerSourceService;

    @RequestMapping(value = "/customerSource/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody CustomerSourceQueryForm form) {
        return customerSourceService.allCustomerSource(form);
    }


    @RequestMapping(value = "/customerSource/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody CustomerSourceAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("顾客来源名称不能为空");

        return customerSourceService.createCustomerSource(form);
    }

    @RequestMapping(value = "/customerSource/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody CustomerSourceUpdateForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("顾客来源名称不能为空");
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (form.Active == null)
            return BaseOutput.failed("有效性不能为空");

        return customerSourceService.updateCustomerSource(form);
    }

    @RequestMapping(value = "/customerSource/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody CustomerSourceMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");

        return customerSourceService.move(form);
    }
}