package com.zhelian.api.crm.customerBasicFile;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.CustomerBasicFileAddForm;
import com.zhelian.model.ibeauty.form.input.CustomerBasicFileQueryForm;
import com.zhelian.model.ibeauty.form.input.CustomerBasicFileUpdateForm;
import com.zhelian.model.ibeauty.form.input.CustomerFileCategoryMoveForm;
import com.zhelian.service.CRM.customerBasicFile.CustomerBasicFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class CustomerBasicFileController {

    @Autowired
    CustomerBasicFileService customerBasicFileService;

    @RequestMapping(value = "/customerBasicFile/all", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody CustomerBasicFileQueryForm form) {


        return customerBasicFileService.allCustomerBasicFile(form);
    }

    @RequestMapping(value = "/customerBasicFile/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody CustomerBasicFileAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("名称不能为空");
        if (form.Type == null)
            return BaseOutput.failed("类型不能为空");
        return customerBasicFileService.createCustomerBasicFile(form);
    }

    @RequestMapping(value = "/customerBasicFile/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody CustomerBasicFileUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("名称不能为空");
        if (form.Type == null)
            return BaseOutput.failed("类型不能为空");
        return customerBasicFileService.updateCustomerBasicFile(form);
    }

    @RequestMapping(value = "/customerBasicFile/delete", method = {RequestMethod.POST})
    public BaseOutput delete(@RequestBody CustomerBasicFileUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        return customerBasicFileService.deleteCustomerBasicFile(form);
    }

    @RequestMapping(value = "/customerBasicFile/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody CustomerFileCategoryMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");
        return customerBasicFileService.moveCustomerBasicFile(form);
    }

}