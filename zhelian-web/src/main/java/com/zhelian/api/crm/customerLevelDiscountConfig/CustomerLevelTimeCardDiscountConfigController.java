package com.zhelian.api.crm.customerLevelDiscountConfig;

import com.zhelian.model.CRM.form.input.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.service.CRM.customerLevelDiscountConfig.CustomerLevelTimeCardDiscountConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value = "/api")
public class CustomerLevelTimeCardDiscountConfigController {

    @Autowired
    CustomerLevelTimeCardDiscountConfigService customerLevelTimeCardDiscountConfigService;

    /**
     * 查询客户级别折扣配置
     *
     * @return
     */
    @PostMapping(value = "/customerPriceTimeCardConfig/customerLevelDiscountAll")
    public BaseOutput customerLevelDiscountAll() {
        return customerLevelTimeCardDiscountConfigService.customerLevelDiscountAll();
    }

    /**
     * 创建客户级别折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceTimeCardConfig/createCustomerLevelDiscountConfig")
    public BaseOutput createCustomerLevelDiscountConfig(@RequestBody List<CustomerLevelTimeCardDiscountConfigAddForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelTimeCardDiscountConfigService.createCustomerLevelDiscountConfig(form);
    }

    /**
     * 查询客户级别分类折扣配置
     *
     * @return
     */
    @PostMapping(value = "/customerPriceTimeCardConfig/customerLevelCategoryDiscountAll")
    public BaseOutput customerLevelCategoryDiscountAll(@RequestBody CustomerLevelTimeCardCategoryDiscountConfigQueryForm form) {
        return customerLevelTimeCardDiscountConfigService.customerLevelCategoryDiscountAll(form);
    }

    /**
     * 查询分类
     *
     * @return
     */
    @PostMapping(value = "/customerPriceTimeCardConfig/category")
    public BaseOutput category() {
        return customerLevelTimeCardDiscountConfigService.category();
    }

    /**
     * 查询已选分类
     * @return
     */
    @PostMapping(value = "/customerPriceTimeCardConfig/selectedCategory")
    public BaseOutput selectedCategory() {
        return customerLevelTimeCardDiscountConfigService.selectedCategory();
    }

    /**
     * 创建客户级别分类折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceTimeCardConfig/createCustomerLevelCategoryDiscountConfig")
    public BaseOutput createCustomerLevelCategoryDiscountConfig(@RequestBody List<CustomerLevelTimeCardCategoryDiscountConfigAddForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelTimeCardDiscountConfigService.createCustomerLevelCategoryDiscountConfig(form);
    }

    /**
     * 更新客户级别分类折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceTimeCardConfig/updateCustomerLevelCategoryDiscountConfig")
    public BaseOutput updateCustomerLevelCategoryDiscountConfig(@RequestBody CustomerLevelTimeCardCategoryDiscountConfigUpdateForm form) {
        if (form.CustomerLevelDiscountPriceList == null) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        if (form.TimeCardCategoryID == null) {
            return BaseOutputForm.failed("分类不能为空");
        }

        return customerLevelTimeCardDiscountConfigService.updateCustomerLevelCategoryDiscountConfig(form);
    }

    /**
     * 批量更新客户级别分类折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceTimeCardConfig/batchUpdateCustomerLevelCategoryDiscountConfig")
    public BaseOutput batchUpdateCustomerLevelCategoryDiscountConfig(@RequestBody List<CustomerLevelTimeCardCategoryDiscountConfigUpdateForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelTimeCardDiscountConfigService.batchUpdateCustomerLevelCategoryDiscountConfig(form);
    }

    /**
     * 删除客户级别分类折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceTimeCardConfig/deleteCustomerLevelCategoryDiscountConfig")
    public BaseOutput deleteCustomerLevelCategoryDiscountConfig(@RequestBody CustomerLevelTimeCardCategoryDiscountConfigDeleteForm form) {
        if (form.TimeCardCategoryID == null) {
            return BaseOutputForm.failed("分类不能为空");
        }

        return customerLevelTimeCardDiscountConfigService.deleteCustomerLevelCategoryDiscountConfig(form);
    }

    /**
     * 批量删除客户级别分类折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceTimeCardConfig/batchDeleteCustomerLevelCategoryDiscountConfig")
    public BaseOutput batchDeleteCustomerLevelCategoryDiscountConfig(@RequestBody List<CustomerLevelTimeCardCategoryDiscountConfigDeleteForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelTimeCardDiscountConfigService.batchDeleteCustomerLevelCategoryDiscountConfig(form);
    }

    /**
     * 查询时效卡
     *
     * @return
     */
    @PostMapping(value = "/customerPriceTimeCardConfig/timeCard")
    public BaseOutput timeCard(@RequestBody CustomerLevelTimeCardDiscountPriceQueryForm form) {
        return customerLevelTimeCardDiscountConfigService.timeCard(form);
    }

    /**
     * 查询已选时效卡
     * @return
     */
    @PostMapping(value = "/customerPriceTimeCardConfig/selectedTimeCard")
    public BaseOutput selectedTimeCard() {
        return customerLevelTimeCardDiscountConfigService.selectedTimeCard();
    }

    /**
     * 查询客户级别时效卡折扣配置
     *
     * @return
     */
    @PostMapping(value = "/customerPriceTimeCardConfig/customerLevelTimeCardDiscountPriceAll")
    public BaseOutput customerLevelTimeCardDiscountPriceAll(@RequestBody CustomerLevelTimeCardDiscountPriceConfigQueryForm form) {
        return customerLevelTimeCardDiscountConfigService.customerLevelTimeCardDiscountPriceAll(form);
    }

    /**
     * 创建客户级别时效卡折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceTimeCardConfig/createCustomerLevelTimeCardDiscountPriceConfig")
    public BaseOutput createCustomerLevelTimeCardDiscountPriceConfig(@RequestBody List<CustomerLevelTimeCardDiscountPriceConfigAddForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelTimeCardDiscountConfigService.createCustomerLevelTimeCardDiscountPriceConfig(form);
    }


    /**
     * 更新客户级别时效卡折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceTimeCardConfig/updateCustomerLevelTimeCardDiscountConfig")
    public BaseOutput updateCustomerLevelTimeCardDiscountConfig(@RequestBody CustomerLevelTimeCardCardDiscountConfigUpdateForm form) {
        if (form.CustomerLevelDiscountPriceList == null) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        if (form.TimeCardID == null) {
            return BaseOutputForm.failed("分类不能为空");
        }

        return customerLevelTimeCardDiscountConfigService.updateCustomerLevelTimeCardDiscountConfig(form);
    }

    /**
     * 批量更新客户级别通用次卡折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceTimeCardConfig/batchUpdateCustomerLevelTimeCardDiscountConfig")
    public BaseOutput batchUpdateCustomerLevelTimeCardDiscountConfig(@RequestBody List<CustomerLevelTimeCardCardDiscountConfigUpdateForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelTimeCardDiscountConfigService.batchUpdateCustomerLevelTimeCardDiscountConfig(form);
    }

    /**
     * 删除客户级别时效卡折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceTimeCardConfig/deleteCustomerLevelTimeCardDiscountConfig")
    public BaseOutput deleteCustomerLevelTimeCardDiscountConfig(@RequestBody CustomerLevelTimeCardDiscountConfigDeleteForm form) {
        if (form.TimeCardID == null) {
            return BaseOutputForm.failed("分类不能为空");
        }

        return customerLevelTimeCardDiscountConfigService.deleteCustomerLevelTimeCardDiscountConfig(form);
    }

    /**
     * 批量除客户级别通用次卡折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceTimeCardConfig/batchDeleteCustomerLevelTimeCardDiscountConfig")
    public BaseOutput batchDeleteCustomerLevelTimeCardDiscountConfig(@RequestBody List<CustomerLevelTimeCardDiscountConfigDeleteForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelTimeCardDiscountConfigService.batchDeleteCustomerLevelTimeCardDiscountConfig(form);
    }

}
