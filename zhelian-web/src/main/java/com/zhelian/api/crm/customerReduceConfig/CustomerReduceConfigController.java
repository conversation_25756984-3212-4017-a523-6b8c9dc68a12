package com.zhelian.api.crm.customerReduceConfig;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.CustomerReduceConfigAddForm;
import com.zhelian.service.CRM.customerReduceConfig.CustomerReduceConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
public class CustomerReduceConfigController {

    @Autowired
    CustomerReduceConfigService customerReduceConfigService;

    @RequestMapping(value = "/customerReduceConfig/all", method = {RequestMethod.POST})
    public BaseOutput all() {
        return customerReduceConfigService.all();
    }


    @RequestMapping(value = "/customerReduceConfig/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody CustomerReduceConfigAddForm form) {
        if (form.IsReduce == null)
            return BaseOutput.failed("是否开启降级不能为空");
        return customerReduceConfigService.update(form);
    }

}