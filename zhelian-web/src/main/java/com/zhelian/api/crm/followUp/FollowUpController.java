package com.zhelian.api.crm.followUp;

import com.zhelian.model.CRM.form.input.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.service.CRM.followUp.FollowUpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class FollowUpController {

    @Autowired
    FollowUpService followUpService;


    @RequestMapping(value = "/followUp/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody FollowUpRecordQueryForm form) {
        return followUpService.list(form);
    }

    @RequestMapping(value = "/followUp/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody FollowUpRecordQueryForm form) {
        return followUpService.all(form);
    }

    /**
     * 新建跟进
     *
     * @param form
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/followUp/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody FollowUpRecordAddForm form) throws Exception {
        if (form.CustomerID == null)
            return BaseOutput.failed("顾客ID不能为空");
        if (form.IsNextFollowUp == null)
            return BaseOutput.failed("下次是否跟进不能为空");

        return followUpService.create(form);
    }

    /**
     * 跟进
     *
     * @param form
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/followUp/followUp", method = {RequestMethod.POST})
    public BaseOutput followUp(@RequestBody FollowUpRecordUpdateForm form) throws Exception {
        if (form.FollowUpMethodID == null)
            return BaseOutput.failed("跟进方式不能为空");
        if (form.FollowUpStatusID == null)
            return BaseOutput.failed("跟进状态不能为空");
        if (form.IsNextFollowUp == null)
            return BaseOutput.failed("下次是否跟进不能为空");
        return followUpService.followUp(form);
    }

    /**
     * 更新
     *
     * @param form
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/followUp/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody FollowUpRecordUpdateForm form) throws Exception {
        if (form.FollowUpMethodID == null)
            return BaseOutput.failed("跟进方式不能为空");
        if (form.FollowUpStatusID == null)
            return BaseOutput.failed("跟进状态不能为空");
        return followUpService.update(form);
    }


    /**
     * 指派
     *
     * @param form
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/followUp/assign", method = {RequestMethod.POST})
    public BaseOutput assign(@RequestBody FollowUpRecordAddExtendForm form) throws Exception {
        if (form.FollowUpBy == null)
            return BaseOutput.failed("指派人不能为空");
        return followUpService.assign(form);
    }

    /**
     * 重新指派
     *
     * @param form
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/followUp/anewAssign", method = {RequestMethod.POST})
    public BaseOutput anewAssign(@RequestBody FollowUpRecordAnewAssignAddExtendForm form) throws Exception {
        if (form.ID == null)
            return BaseOutput.failed("跟进编号");
        if (form.FollowUpBy == null)
            return BaseOutput.failed("指派人不能为空");
        return followUpService.anewAssign(form);
    }

    /**
     * 删除
     *
     * @param form
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/followUp/delete", method = {RequestMethod.POST})
    public BaseOutput delete(@RequestBody FollowUpRecordDeleteForm form) {
        if (form.ID == null)
            return BaseOutput.failed("跟进记录不能为空");

        return followUpService.delete(form);
    }

    @RequestMapping(value = "/followUp/followUpEntity", method = {RequestMethod.POST})
    public BaseOutput entityList() {
        return followUpService.entityList();
    }

    @RequestMapping(value = "/followUp/followUpEmployee", method = {RequestMethod.POST})
    public BaseOutput employeeList() {
        return followUpService.employeeList();
    }

    @RequestMapping(value = "/followUp/createAppointmentBill", method = {RequestMethod.POST})
    public BaseOutput createAppointmentBill(@RequestBody FollowUpAppointmentAddForm form) throws Exception {
        if (form.CustomerID == null)
            return BaseOutput.failed("会员编号不能为空");
        if (form.AppointmentDate == null)
            return BaseOutput.failed("预约日期不能为空");

        return followUpService.createAppointmentBill(form);
    }
}