package com.zhelian.api.crm.customerLevelDiscountConfig;

import com.zhelian.model.CRM.form.input.*;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.service.CRM.customerLevelDiscountConfig.CustomerLevelGeneralCardDiscountConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value = "/api")
public class CustomerLevelGeneralCardDiscountConfigController {

    @Autowired
    CustomerLevelGeneralCardDiscountConfigService customerLevelGeneralCardDiscountConfigService;

    /**
     * 查询客户级别折扣配置
     *
     * @return
     */
    @PostMapping(value = "/customerPriceGeneralCardConfig/customerLevelDiscountAll")
    public BaseOutput customerLevelDiscountAll() {
        return customerLevelGeneralCardDiscountConfigService.customerLevelDiscountAll();
    }

    /**
     * 创建客户级别折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceGeneralCardConfig/createCustomerLevelDiscountConfig")
    public BaseOutput createCustomerLevelDiscountConfig(@RequestBody List<CustomerLevelGeneralCardDiscountConfigAddForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelGeneralCardDiscountConfigService.createCustomerLevelDiscountConfig(form);
    }

    /**
     * 查询客户级别分类折扣配置
     *
     * @return
     */
    @PostMapping(value = "/customerPriceGeneralCardConfig/customerLevelCategoryDiscountAll")
    public BaseOutput customerLevelCategoryDiscountAll(@RequestBody CustomerLevelGeneralCardCategoryDiscountConfigQueryForm form) {
        return customerLevelGeneralCardDiscountConfigService.customerLevelCategoryDiscountAll(form);
    }

    /**
     * 查询分类
     *
     * @return
     */
    @PostMapping(value = "/customerPriceGeneralCardConfig/category")
    public BaseOutput category() {
        return customerLevelGeneralCardDiscountConfigService.category();
    }

    /**
     * 查询已选分类
     * @return
     */
    @PostMapping(value = "/customerPriceGeneralCardConfig/selectedCategory")
    public BaseOutput selectedCategory() {
        return customerLevelGeneralCardDiscountConfigService.selectedCategory();
    }

    /**
     * 创建客户级别分类折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceGeneralCardConfig/createCustomerLevelCategoryDiscountConfig")
    public BaseOutput createCustomerLevelCategoryDiscountConfig(@RequestBody List<CustomerLevelGeneralCardCategoryDiscountConfigAddForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelGeneralCardDiscountConfigService.createCustomerLevelCategoryDiscountConfig(form);
    }

    /**
     * 更新客户级别分类折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceGeneralCardConfig/updateCustomerLevelCategoryDiscountConfig")
    public BaseOutput updateCustomerLevelCategoryDiscountConfig(@RequestBody CustomerLevelGeneralCardCategoryDiscountConfigUpdateForm form) {
        if (form.CustomerLevelDiscountPriceList == null) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        if (form.GeneralCardCategoryID == null) {
            return BaseOutputForm.failed("分类不能为空");
        }

        return customerLevelGeneralCardDiscountConfigService.updateCustomerLevelCategoryDiscountConfig(form);
    }

    /**
     * 批量更新客户级别分类折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceGeneralCardConfig/batchUpdateCustomerLevelCategoryDiscountConfig")
    public BaseOutput batchUpdateCustomerLevelCategoryDiscountConfig(@RequestBody List<CustomerLevelGeneralCardCategoryDiscountConfigUpdateForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelGeneralCardDiscountConfigService.batchUpdateCustomerLevelCategoryDiscountConfig(form);
    }

    /**
     * 删除客户级别分类折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceGeneralCardConfig/deleteCustomerLevelCategoryDiscountConfig")
    public BaseOutput deleteCustomerLevelCategoryDiscountConfig(@RequestBody CustomerLevelGeneralCardCategoryDiscountConfigDeleteForm form) {
        if (form.GeneralCardCategoryID == null) {
            return BaseOutputForm.failed("分类不能为空");
        }

        return customerLevelGeneralCardDiscountConfigService.deleteCustomerLevelCategoryDiscountConfig(form);
    }

    /**
     * 批量删除客户级别分类折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceGeneralCardConfig/batchDeleteCustomerLevelCategoryDiscountConfig")
    public BaseOutput batchDeleteCustomerLevelCategoryDiscountConfig(@RequestBody List<CustomerLevelGeneralCardCategoryDiscountConfigDeleteForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelGeneralCardDiscountConfigService.batchDeleteCustomerLevelCategoryDiscountConfig(form);
    }

    /**
     * 查询通用次卡
     *
     * @return
     */
    @PostMapping(value = "/customerPriceGeneralCardConfig/generalCard")
    public BaseOutput generalCard(@RequestBody CustomerLevelGeneralCardDiscountPriceQueryForm form) {
        return customerLevelGeneralCardDiscountConfigService.generalCard(form);
    }

    /**
     * 查询已选通用次卡
     * @return
     */
    @PostMapping(value = "/customerPriceGeneralCardConfig/selectedGeneralCard")
    public BaseOutput selectedGeneralCard() {
        return customerLevelGeneralCardDiscountConfigService.selectedGeneralCard();
    }

    /**
     * 查询客户级别通用次卡折扣配置
     *
     * @return
     */
    @PostMapping(value = "/customerPriceGeneralCardConfig/customerLevelGeneralCardDiscountPriceAll")
    public BaseOutput customerLevelGeneralCardDiscountPriceAll(@RequestBody CustomerLevelGeneralCardDiscountPriceConfigQueryForm form) {
        return customerLevelGeneralCardDiscountConfigService.customerLevelGeneralCardDiscountPriceAll(form);
    }

    /**
     * 创建客户级别通用次卡折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceGeneralCardConfig/createCustomerLevelGeneralCardDiscountPriceConfig")
    public BaseOutput createCustomerLevelGeneralCardDiscountPriceConfig(@RequestBody List<CustomerLevelGeneralCardDiscountPriceConfigAddForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelGeneralCardDiscountConfigService.createCustomerLevelGeneralCardDiscountPriceConfig(form);
    }


    /**
     * 更新客户级别通用次卡折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceGeneralCardConfig/updateCustomerLevelGeneralCardDiscountConfig")
    public BaseOutput updateCustomerLevelGeneralCardDiscountConfig(@RequestBody CustomerLevelGeneralCardCardDiscountConfigUpdateForm form) {
        if (form.CustomerLevelDiscountPriceList == null) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        if (form.GeneralCardID == null) {
            return BaseOutputForm.failed("分类不能为空");
        }

        return customerLevelGeneralCardDiscountConfigService.updateCustomerLevelGeneralCardDiscountConfig(form);
    }

    /**
     * 批量更新客户级别通用次卡折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceGeneralCardConfig/batchUpdateCustomerLevelGeneralCardDiscountConfig")
    public BaseOutput batchUpdateCustomerLevelGeneralCardDiscountConfig(@RequestBody List<CustomerLevelGeneralCardCardDiscountConfigUpdateForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelGeneralCardDiscountConfigService.batchUpdateCustomerLevelGeneralCardDiscountConfig(form);
    }

    /**
     * 删除客户级别通用次卡折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceGeneralCardConfig/deleteCustomerLevelGeneralCardDiscountConfig")
    public BaseOutput deleteCustomerLevelGeneralCardDiscountConfig(@RequestBody CustomerLevelGeneralCardDiscountConfigDeleteForm form) {
        if (form.GeneralCardID == null) {
            return BaseOutputForm.failed("分类不能为空");
        }

        return customerLevelGeneralCardDiscountConfigService.deleteCustomerLevelGeneralCardDiscountConfig(form);
    }

    /**
     * 批量除客户级别通用次卡折扣配置
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/customerPriceGeneralCardConfig/batchDeleteCustomerLevelGeneralCardDiscountConfig")
    public BaseOutput batchDeleteCustomerLevelGeneralCardDiscountConfig(@RequestBody List<CustomerLevelGeneralCardDiscountConfigDeleteForm> form) {
        if (form == null || form.size() == 0) {
            return BaseOutputForm.failed("客户级别不能为空");
        }

        return customerLevelGeneralCardDiscountConfigService.batchDeleteCustomerLevelGeneralCardDiscountConfig(form);
    }

}
