package com.zhelian.api.khs.entity;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.GoodsMoveForm;
import com.zhelian.model.ibeauty.form.input.QueryForm;
import com.zhelian.model.ibeauty.form.input.QueryPageForm;
import com.zhelian.model.khs.form.input.*;
import com.zhelian.service.khs.entity.EntityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;


@RestController
@RequestMapping(value = "/api")
public class EntityController {

    @Autowired
    EntityService entityService;

    /**
     * 功能描述:
     * <通讯录-组织架构>
     *
     * @param: form
     * @return: com.zhelian.model.base.BaseOutputForm
     * @exception:
     * @author: WangXianXian
     * @date: 2020/2/19 3:17 PM
     */
    @RequestMapping(value = "/entity/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody EntityQueryForm form) {
        return entityService.entityList(form);
    }

    @RequestMapping(value = "/entity/all", method = {RequestMethod.POST})
    public BaseOutput all() {
        return entityService.entityList(null);
    }

    @RequestMapping(value = "/entity/getStoreList", method = {RequestMethod.POST})
    public BaseOutput getStoreList() {
        return entityService.getStoreList();
    }

    /**
     * 功能描述:
     * <4.2.创建组织架构>
     *
     * @param: form
     * @return: com.zhelian.model.base.BaseOutput
     * @exception:
     * @author: WangXianXian
     * @date: 2020/2/28 10:19 AM
     */
    @RequestMapping(value = "/entity/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody EntityAddForm form) {
        if (form.ParentID == null)
            return BaseOutput.failed("上级单位不能为空");
        if (!StringUtils.hasText(form.EntityName))
            return BaseOutput.failed("单位名称不能为空");
        if (form.IsStore == null)
            return BaseOutput.failed("是否门店不能为空");
        if (form.IsWarehouse == null)
            return BaseOutput.failed("是否仓库不能为空");
        return entityService.addEntity(form);
    }

    /**
     * 功能描述:
     * <4.3.更新组织架构>
     *
     * @param: entity
     * @return: com.zhelian.model.base.BaseOutput
     * @exception:
     * @author: WangXianXian
     * @date: 2020/2/28 10:19 AM
     */
    @RequestMapping(value = "/entity/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody EntityUpdateForm entity) {
        if (entity.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (entity.ParentID == null)
            return BaseOutput.failed("上级单位不能为空");
        if (!StringUtils.hasText(entity.EntityName))
            return BaseOutput.failed("单位名称不能为空");
        if (entity.IsStore == null)
            return BaseOutput.failed("是否门店不能为空");
        if (entity.IsWarehouse == null)
            return BaseOutput.failed("是否仓库不能为空");
        if (entity.Active == null)
            return BaseOutput.failed("有效性不能为空");
        return entityService.updateEntity(entity);
    }

    /**
     * 功能描述:
     * <4.4.调整组织架构顺序>
     *
     * @param: form
     * @return: com.zhelian.model.base.BaseOutput
     * @exception:
     * @author: WangXianXian
     * @date: 2020/2/28 10:18 AM
     */
    @RequestMapping(value = "/entity/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody EntityMoveForm form) {
        if (form.MoveEntityID == 0)
            return BaseOutput.failed("移动单位ID不能为空");
        if (form.DestParentID == 0)
            return BaseOutput.failed("移动后父级单位ID不能为空");

        return entityService.move(form);
    }

    @RequestMapping(value = "/onlineEntity/getOnlineEntityByID", method = {RequestMethod.POST})
    public BaseOutput getOnlineEntityByID(@RequestBody EntityQueryForm form) {
        return entityService.getOnlineEntityByID(form);
    }

    @RequestMapping(value = "/onlineEntity/list", method = {RequestMethod.POST})
    public BaseOutput onlineEntityList() {
        return entityService.onlineEntityList();
    }

    @RequestMapping(value = "onlineEntity/updateOnlineEntity", method = {RequestMethod.POST})
    public BaseOutput updateOnlineEntity(@RequestBody TMEntityUpdateForm form) {
        return entityService.updateOnlineEntity(form);
    }

    @RequestMapping(value = "/onlineEntity/moveImage", method = {RequestMethod.POST})
    public BaseOutput moveImage(@RequestBody GoodsMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");

        return entityService.moveImage(form);
    }

    @RequestMapping(value = "/entity/allEntity", method = {RequestMethod.POST})
    public BaseOutput allEntity() {
        return entityService.allEntity();
    }

    @RequestMapping(value = "/entity/allWarehouse", method = {RequestMethod.POST})
    public BaseOutput allWarehouse() {
        return entityService.allWarehouse();
    }

    @RequestMapping(value = "/entity/excel", method = {RequestMethod.POST})
    public BaseOutput excel(@RequestBody EntityQueryForm form, HttpServletResponse response) {
        return entityService.excel(form, response);
    }

    @RequestMapping(value = "/entity/permissionEtityByPage", method = {RequestMethod.POST})
    public BaseOutput permissionEtityByPage(@RequestBody QueryPageForm form) {
        return entityService.permissionEtityByPage(form);
    }

    @RequestMapping(value = "/entity/permissionEtity", method = {RequestMethod.POST})
    public BaseOutput permissionEtity(@RequestBody QueryForm form) {
        return entityService.permissionEtity(form);
    }
}
