package com.zhelian.api.khs.saleTimeCardPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCommissionSchemeAddForm;
import com.zhelian.service.khs.saleTimeCardPerformance.SaleTimeCardCategoryPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class SaleTimeCardCategoryPerformanceController {

    @Autowired
    SaleTimeCardCategoryPerformanceService saleTimeCardCategoryPerformanceService;

    static boolean checkRate(SaleTimeCardCategoryAddForm form) {

        for (SaleTimeCardCategoryCommissionAddForm cardCategoryPerformanceAddForm : form.Category) {

            if ((cardCategoryPerformanceAddForm.PayRate != null && cardCategoryPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (cardCategoryPerformanceAddForm.SavingCardRate != null && cardCategoryPerformanceAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                    || (cardCategoryPerformanceAddForm.SavingCardLargessRate != null && cardCategoryPerformanceAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                return false;
        }
        return true;
    }

    @RequestMapping(value = "/saleTimeCardCategoryPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allSaleTimeCardCategoryPerformance(@RequestBody SaleTimeCardCommissionSchemeAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return saleTimeCardCategoryPerformanceService.allSaleTimeCardCategoryPerformance(form);
    }

    @RequestMapping(value = "/saleTimeCardCategoryPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleTimeCardCategoryPerformance(@RequestBody SaleTimeCardCategoryAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");

        return saleTimeCardCategoryPerformanceService.updateSaleTimeCardCategoryPerformance(form);
    }
}

