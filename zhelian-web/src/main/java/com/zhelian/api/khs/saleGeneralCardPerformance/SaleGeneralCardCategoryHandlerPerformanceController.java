package com.zhelian.api.khs.saleGeneralCardPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardCategoryHandlerCommissionQueryForm;
import com.zhelian.service.khs.saleGeneralCardPerformance.SaleGeneralCardCategoryHandlerPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
public class SaleGeneralCardCategoryHandlerPerformanceController {

    @Autowired
    SaleGeneralCardCategoryHandlerPerformanceService saleGeneralCardCategoryHandlerPerformanceService;


    @RequestMapping(value = "/saleGeneralCardCategoryHandlerPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allSaleGeneralCardCategoryHandlerPerformance(@RequestBody SaleGeneralCardCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return saleGeneralCardCategoryHandlerPerformanceService.allSaleGeneralCardCategoryHandlerPerformance(form);
    }

    @RequestMapping(value = "/saleGeneralCardCategoryHandlerPerformance/packageCard", method = {RequestMethod.POST})
    public BaseOutput allSalePackageCardGeneralCardCategoryHandlerPerformance(@RequestBody SaleGeneralCardCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return saleGeneralCardCategoryHandlerPerformanceService.allSalePackageCardGeneralCardCategoryHandlerPerformance(form);
    }

    @RequestMapping(value = "/saleGeneralCardCategoryHandlerPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleGeneralCardCategoryHandlerPerformance(@RequestBody SaleGeneralCardCategoryHandlerCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return saleGeneralCardCategoryHandlerPerformanceService.updateSaleGeneralCardCategoryHandlerPerformance(form);
    }
}

