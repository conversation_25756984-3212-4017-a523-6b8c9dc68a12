package com.zhelian.api.khs.entity;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.khs.entity.EntityConditionEntity;
import com.zhelian.service.khs.entity.EntityConditionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description: 门店分群条件接口
 * @author: zhanglongyang
 * @date: 2021/05/18
 * @time: 14:00
 */
@RestController
@RequestMapping(value = "/api/EntityCondition")
public class EntityConditionController {

    @Autowired
    EntityConditionService entityConditionService;

    //门店分配分群条件
    @PostMapping(value = "/addEntityCondiition")
    public BaseOutput addEntityCondiition(@RequestBody EntityConditionEntity conditionEntity) {
        return entityConditionService.addEntityCondition(conditionEntity);
    }

}
