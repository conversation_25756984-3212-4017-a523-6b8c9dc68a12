package com.zhelian.api.khs.employee;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.khs.entity.JobTypeEntity;
import com.zhelian.model.khs.form.input.AllTypeQueryForm;
import com.zhelian.model.khs.form.input.JobTypeAddForm;
import com.zhelian.model.khs.form.input.JobTypeQueryForm;
import com.zhelian.service.khs.employee.JobTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
public class JobTypeController {

    @Autowired
    JobTypeService jobTypeService;

    @RequestMapping(value = "/jobtype/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody JobTypeQueryForm jobTypeQueryForm) {
        if (jobTypeQueryForm.PageNum <= 0) {
            return BaseOutput.failed("页码不正确");
        }
        return jobTypeService.getJobType(jobTypeQueryForm);
    }

    @RequestMapping(value = "/jobtype/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody AllTypeQueryForm jobTypeQueryForm) {

        return jobTypeService.all(jobTypeQueryForm);
    }

    @RequestMapping(value = "/jobtype/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody JobTypeAddForm jobTypeAddForm) {
        if (!StringUtils.hasText(jobTypeAddForm.JobName)) {
            return BaseOutput.failed("职务名称不能为空");
        }
        return jobTypeService.addJobType(jobTypeAddForm);
    }

    @RequestMapping(value = "/jobtype/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody JobTypeEntity jobTypeEntity) {
        if (jobTypeEntity.ID == null) {
            return BaseOutput.failed("职位ID不能为空");
        }
        if (!StringUtils.hasText(jobTypeEntity.JobName)) {
            return BaseOutput.failed("职务名称不能为空");
        }
        if (jobTypeEntity.Active == null) {
            return BaseOutput.failed("职位状态不能为空");
        }
        return jobTypeService.updateJobType(jobTypeEntity);
    }

}
