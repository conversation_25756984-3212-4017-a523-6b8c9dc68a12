package com.zhelian.api.khs.treatGeneralCardPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatCardHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardHandlersCommissionAddForm;
import com.zhelian.service.khs.treatGeneralCardPerformance.TreatGeneralCardHandlerPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class TreatGeneralCardHandlerPerformanceController {

    @Autowired
    TreatGeneralCardHandlerPerformanceService treatGeneralCardHandlerPerformanceService;

    static boolean checkRate(TreatCardHandlersCommissionAddForm form) {
        for (TreatCardHandlerCommissionAddForm handlerPerformanceAddForm : form.CardHandler) {
            if ((handlerPerformanceAddForm.PayRate != null && handlerPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerPerformanceAddForm.CardRate != null && handlerPerformanceAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerPerformanceAddForm.CardLargessRate != null && handlerPerformanceAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerPerformanceAddForm.LargessRate != null && handlerPerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
            )
                return false;

            for (TreatCardCategoryHandlerJobTypeCommissionAddForm typePerformanceAddForm : handlerPerformanceAddForm.HandlerJobTypeCommission) {
                if ((typePerformanceAddForm.PayRate != null && typePerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (typePerformanceAddForm.CardRate != null && typePerformanceAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                        || (typePerformanceAddForm.CardLargessRate != null && typePerformanceAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                        || (typePerformanceAddForm.LargessRate != null && typePerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
                )
                    return false;
            }
        }
        return true;
    }

    @RequestMapping(value = "/treatGeneralCardHandlerPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allTreatGeneralCardHandlerPerformance(@RequestBody TreatCardCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CardID == null)
            return BaseOutput.failed("通用次卡编号不能为空");
        return treatGeneralCardHandlerPerformanceService.allTreatGeneralCardHandlerPerformance(form);
    }

    @RequestMapping(value = "/treatGeneralCardHandlerPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateTreatGeneralCardHandlerPerformance(@RequestBody TreatCardHandlersCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CardID == null)
            return BaseOutput.failed("通用次卡编号不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatGeneralCardHandlerPerformanceService.updateTreatGeneralCardHandlerPerformance(form);
    }
}

