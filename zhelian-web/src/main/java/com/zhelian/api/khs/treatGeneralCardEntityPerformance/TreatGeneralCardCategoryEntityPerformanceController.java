package com.zhelian.api.khs.treatGeneralCardEntityPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryCommissionQueryForm;
import com.zhelian.model.khs.form.input.TreatGoodCategoryAddForm;
import com.zhelian.model.khs.form.input.TreatGoodCategoryPerformanceAddForm;
import com.zhelian.service.khs.treatGeneralCardEntityPerformance.TreatGeneralCardCategoryEntityPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class TreatGeneralCardCategoryEntityPerformanceController {

    @Autowired
    TreatGeneralCardCategoryEntityPerformanceService treatGeneralCardCategoryEntityPerformanceService;

    static boolean checkRate(TreatGoodCategoryAddForm form) {

        for (TreatGoodCategoryPerformanceAddForm treatProjectCategoryEntityPerformanceAddForm : form.Category) {
            if ((treatProjectCategoryEntityPerformanceAddForm.PayRate != null && treatProjectCategoryEntityPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (treatProjectCategoryEntityPerformanceAddForm.CardRate != null && treatProjectCategoryEntityPerformanceAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                    || (treatProjectCategoryEntityPerformanceAddForm.CardLargessRate != null && treatProjectCategoryEntityPerformanceAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                    || (treatProjectCategoryEntityPerformanceAddForm.LargessRate != null && treatProjectCategoryEntityPerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
            )
                return false;

        }
        return true;
    }

    @RequestMapping(value = "/treatGeneralCardCategoryEntityPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allSaleGeneralCardCategoryEntityPerformance(@RequestBody TreatProductCategoryCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return treatGeneralCardCategoryEntityPerformanceService.allTreatGeneralCardCategoryEntityPerformance(form);
    }

    @RequestMapping(value = "/treatGeneralCardCategoryEntityPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleGeneralCardCategoryEntityPerformance(@RequestBody TreatGoodCategoryAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatGeneralCardCategoryEntityPerformanceService.updateTreatGeneralCardCategoryEntityPerformance(form);
    }
}

