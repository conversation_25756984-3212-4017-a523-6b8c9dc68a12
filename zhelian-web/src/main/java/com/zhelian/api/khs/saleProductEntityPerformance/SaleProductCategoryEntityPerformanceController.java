package com.zhelian.api.khs.saleProductEntityPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionSchemeAddForm;
import com.zhelian.model.khs.form.input.SaleGoodCategoryAddForm;
import com.zhelian.model.khs.form.input.SaleGoodCategoryPerformanceAddForm;
import com.zhelian.service.khs.saleProductEntityPerformance.SaleProductCategoryEntityPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class SaleProductCategoryEntityPerformanceController {

    @Autowired
    SaleProductCategoryEntityPerformanceService saleProductCategoryEntityPerformanceService;

    static boolean checkRate(SaleGoodCategoryAddForm form) {

        for (SaleGoodCategoryPerformanceAddForm saleProductCategoryPerformanceAddForm : form.Category) {

            if ((saleProductCategoryPerformanceAddForm.PayRate != null && saleProductCategoryPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (saleProductCategoryPerformanceAddForm.SavingCardRate != null && saleProductCategoryPerformanceAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                    || (saleProductCategoryPerformanceAddForm.SavingCardLargessRate != null && saleProductCategoryPerformanceAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                return false;
        }
        return true;
    }

    @RequestMapping(value = "/saleProductCategoryEntityPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allSaleProductCategoryEntityPerformance(@RequestBody SaleProductCommissionSchemeAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return saleProductCategoryEntityPerformanceService.allSaleProductCategoryEntityPerformance(form);
    }

    @RequestMapping(value = "/saleProductCategoryEntityPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleProductCategoryEntityPerformance(@RequestBody SaleGoodCategoryAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return saleProductCategoryEntityPerformanceService.updateSaleProductCategoryEntityPerformance(form);
    }
}

