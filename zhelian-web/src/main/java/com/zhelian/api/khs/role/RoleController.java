package com.zhelian.api.khs.role;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.khs.form.input.*;
import com.zhelian.service.khs.role.RoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
public class RoleController {

    @Autowired
    RoleService roleService;

    /**
     * 功能描述:
     * <角色列表>
     *
     * @param: form
     * @return: com.zhelian.model.base.BasePageInfo
     * @exception:
     * @author: WangXianXian
     * @date: 2020/2/19 3:17 PM
     */
    @RequestMapping(value = "/role/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody RoleQueryForm form) {

        return roleService.roleList(form);
    }

    /**
     * 功能描述:
     * <新增角色>
     *
     * @param: form
     * @return: com.zhelian.model.base.BaseOutputForm
     * @exception:
     * @author: WangXianXian
     * @date: 2020/2/21 4:49 PM
     */
    @RequestMapping(value = "/role/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody RoleAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("角色名称不能为空");

        if (form.MenuPermission == null || form.MenuPermission.isEmpty())
            return BaseOutput.failed("菜单不能为空");

        return roleService.addRole(form);
    }

    /**
     * 功能描述:
     * <编辑角色>
     *
     * @param: form
     * @return: com.zhelian.model.base.BaseOutputForm
     * @exception:
     * @author: WangXianXian
     * @date: 2020/2/21 4:49 PM
     */
    @RequestMapping(value = "/role/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody RoleUpdateForm form) {

        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("角色名称不能为空");

        if (form.MenuPermission == null || form.MenuPermission.isEmpty())
            return BaseOutput.failed("菜单不能为空");

        return roleService.updateRole(form);
    }

    @RequestMapping(value = "/role/get", method = {RequestMethod.POST})
    public BaseOutput get(@RequestBody RoleMenuPermissionQueryForm form) {

        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        if (!StringUtils.hasText(form.MenuPermissionType))
            return BaseOutput.failed("菜单类型不能为空");

        return roleService.getRoleMenuPermissionlist(form);
    }

    /**
     * 功能描述:
     * <角色列表-不分页>
     *
     * @param: form
     * @return: com.zhelian.model.base.BaseOutputForm
     * @exception:
     * @author: WangXianXian
     * @date: 2020/2/19 3:17 PM
     */
    @RequestMapping(value = "/role/all", method = RequestMethod.POST)
    public BaseOutput all(@RequestBody AllRoleQueryForm form) {
        return roleService.allRole(form);
    }

}
