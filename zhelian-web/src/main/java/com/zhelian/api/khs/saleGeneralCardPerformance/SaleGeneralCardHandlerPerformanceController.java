package com.zhelian.api.khs.saleGeneralCardPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardHandlersCommissionAddForm;
import com.zhelian.service.khs.saleGeneralCardPerformance.SaleGeneralCardHandlerPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class SaleGeneralCardHandlerPerformanceController {

    @Autowired
    SaleGeneralCardHandlerPerformanceService saleGeneralCardHandlerPerformanceService;


    @RequestMapping(value = "/saleGeneralCardHandlerPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allSaleGeneralCardHandlerPerformance(@RequestBody SaleGeneralCardHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.GeneralCardID == null)
            return BaseOutput.failed("通用次卡编号不能为空");
        return saleGeneralCardHandlerPerformanceService.allSaleGeneralCardHandlerPerformance(form);
    }

    @RequestMapping(value = "/saleGeneralCardHandlerPerformance/packageCard", method = {RequestMethod.POST})
    public BaseOutput allSalePackageCardGeneralCardHandlerPerformance(@RequestBody SaleGeneralCardHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.GeneralCardID == null)
            return BaseOutput.failed("通用次卡编号不能为空");
        return saleGeneralCardHandlerPerformanceService.allSalePackageCardGeneralCardHandlerPerformance(form);
    }

    @RequestMapping(value = "/saleGeneralCardHandlerPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleGeneralCardHandlerPerformance(@RequestBody SaleGeneralCardHandlersCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.GeneralCardID == null)
            return BaseOutput.failed("通用次卡编号不能为空");
        return saleGeneralCardHandlerPerformanceService.updateSaleGeneralCardHandlerPerformance(form);
    }
}

