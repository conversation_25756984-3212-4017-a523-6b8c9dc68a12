package com.zhelian.api.khs.saleTimeCardPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.service.khs.saleTimeCardPerformance.SaleTimeCardCategoryHandlerPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class SaleTimeCardCategoryHandlerPerformanceController {

    @Autowired
    SaleTimeCardCategoryHandlerPerformanceService saleTimeCardCategoryHandlerPerformanceService;

    static boolean checkRate(SaleTimeCardCategoryHandlerCommissionAddForm form) {
        if (form.TimeCardHandler != null) {
            for (SaleTimeCardCategoryHandlerAddForm handlerAddForm : form.TimeCardHandler) {

                if ((handlerAddForm.PayRate != null && handlerAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerAddForm.SavingCardRate != null && handlerAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerAddForm.SavingCardLargessRate != null && handlerAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                    return false;

                for (SaleTimeCardCategoryHandlerJobTypeCommissionAddForm jobTypePerformanceAddForm : handlerAddForm.HandlerJobTypeCommission) {

                    if ((jobTypePerformanceAddForm.PayRate != null && jobTypePerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.SavingCardRate != null && jobTypePerformanceAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.SavingCardLargessRate != null && jobTypePerformanceAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                        return false;
                }
            }
        }
        return true;
    }

    @RequestMapping(value = "/saleTimeCardCategoryHandlerPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allSaleTimeCardCategoryHandlerPerformance(@RequestBody SaleTimeCardCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return saleTimeCardCategoryHandlerPerformanceService.allSaleTimeCardCategoryHandlerPerformance(form);
    }

    @RequestMapping(value = "/saleTimeCardCategoryHandlerPerformance/packageCard", method = {RequestMethod.POST})
    public BaseOutput allSalePackageCardTimeCardCategoryHandlerPerformance(@RequestBody SaleTimeCardCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return saleTimeCardCategoryHandlerPerformanceService.allSalePackageCardTimeCardCategoryHandlerPerformance(form);
    }

    @RequestMapping(value = "/saleTimeCardCategoryHandlerPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleTimeCardCategoryHandlerPerformance(@RequestBody SaleTimeCardCategoryHandlerCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");

        return saleTimeCardCategoryHandlerPerformanceService.updateSaleTimeCardCategoryHandlerPerformance(form);
    }
}

