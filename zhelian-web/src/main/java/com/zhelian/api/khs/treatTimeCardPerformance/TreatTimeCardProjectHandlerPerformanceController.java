package com.zhelian.api.khs.treatTimeCardPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCommissionQueryExtendForm;
import com.zhelian.model.ibeauty.form.input.TreatCardHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardProjectHandlersCommissionAddForm;
import com.zhelian.service.khs.treatTimeCardPerformance.TreatTimeCardProjectHandlerPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

@RestController
@RequestMapping(value = "/api")
public class TreatTimeCardProjectHandlerPerformanceController {

    @Autowired
    TreatTimeCardProjectHandlerPerformanceService treatTimeCardProjectHandlerPerformanceService;

    static boolean checkRate(TreatCardProjectHandlersCommissionAddForm form) {
        for (TreatCardHandlerCommissionAddForm handlerPerformanceAddForm : form.CardHandler) {
            if ((handlerPerformanceAddForm.PayRate != null && handlerPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerPerformanceAddForm.CardRate != null && handlerPerformanceAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerPerformanceAddForm.CardLargessRate != null && handlerPerformanceAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerPerformanceAddForm.LargessRate != null && handlerPerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
            )
                return false;

            for (TreatCardCategoryHandlerJobTypeCommissionAddForm typePerformanceAddForm : handlerPerformanceAddForm.HandlerJobTypeCommission) {
                if ((typePerformanceAddForm.PayRate != null && typePerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (typePerformanceAddForm.CardRate != null && typePerformanceAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                        || (typePerformanceAddForm.CardLargessRate != null && typePerformanceAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                        || (typePerformanceAddForm.LargessRate != null && typePerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
                )
                    return false;
            }
        }
        return true;
    }

    @RequestMapping(value = "/treatTimeCardProjectHandlerPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allTreatTimeCardProjectPerformance(@RequestBody TreatCardCommissionQueryExtendForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CardID == null)
            return BaseOutput.failed("通用次卡编号不能为空");
        if (form.ProjectID == null)
            return BaseOutput.failed("项目编号不能为空");
        return treatTimeCardProjectHandlerPerformanceService.allTreatTimeCardProjectHandlerPerformance(form);
    }

    @RequestMapping(value = "/treatTimeCardProjectHandlerPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateTreatTimeCardProjectPerformance(@RequestBody TreatCardProjectHandlersCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatTimeCardProjectHandlerPerformanceService.updateTreatTimeCardProjectHandlerPerformance(form);
    }
}

