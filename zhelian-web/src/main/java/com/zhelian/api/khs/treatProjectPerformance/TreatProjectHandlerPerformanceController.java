package com.zhelian.api.khs.treatProjectPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatProjectCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectHandlersCommissionAddForm;
import com.zhelian.service.khs.treatProjectPerformance.TreatProjectHandlerPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class TreatProjectHandlerPerformanceController {

    @Autowired
    TreatProjectHandlerPerformanceService treatProjectHandlerPerformanceService;

    static boolean checkRate(TreatProjectHandlersCommissionAddForm form) {
        if (form.Handler != null) {
            for (TreatProjectHandlerCommissionAddForm handlerPerformanceAddForm : form.Handler) {

                if ((handlerPerformanceAddForm.PayRate != null && handlerPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerPerformanceAddForm.CardRate != null && handlerPerformanceAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerPerformanceAddForm.CardLargessRate != null && handlerPerformanceAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerPerformanceAddForm.LargessRate != null && handlerPerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
                )
                    return false;

                for (TreatProjectCategoryHandlerJobTypeCommissionAddForm jobTypePerformanceAddForm : handlerPerformanceAddForm.HandlerJobTypeCommission) {

                    if ((jobTypePerformanceAddForm.PayRate != null && jobTypePerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.CardRate != null && jobTypePerformanceAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.CardLargessRate != null && jobTypePerformanceAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.LargessRate != null && jobTypePerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
                    )
                        return false;
                }
            }
        }
        return true;
    }

    @RequestMapping(value = "/treatProjectHandlerPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allTreatProjectHandlerPerformance(@RequestBody TreatProjectHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.ProjectID == null)
            return BaseOutput.failed("项目编号不能为空");
        return treatProjectHandlerPerformanceService.allTreatProjectHandlerPerformance(form);
    }

    @RequestMapping(value = "/treatProjectHandlerPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateTreatProjectHandlerPerformance(@RequestBody TreatProjectHandlersCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.ProjectID == null)
            return BaseOutput.failed("项目编号不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatProjectHandlerPerformanceService.updateTreatProjectHandlerPerformance(form);
    }
}

