package com.zhelian.api.khs.medicalRecord;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.khs.form.input.MedicalRecordCategoryAddForm;
import com.zhelian.model.khs.form.input.MedicalRecordCategoryMoveForm;
import com.zhelian.model.khs.form.input.MedicalRecordCategoryQueryForm;
import com.zhelian.model.khs.form.input.MedicalRecordCategoryUpdateForm;
import com.zhelian.service.khs.medicalRecord.MedicalRecordCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
public class MedicalRecordCategoryController {

    @Autowired
    MedicalRecordCategoryService medicalRecordCategoryService;

    /**
     * 查询病例分类
     *
     * @return
     */
    @PostMapping(value = "/medicalRecordCategory/list")
    public BaseOutput list(@RequestBody MedicalRecordCategoryQueryForm form) {
        return medicalRecordCategoryService.list(form);
    }

    /**
     * 创建病例分类
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/medicalRecordCategory/create")
    public BaseOutput create(@RequestBody MedicalRecordCategoryAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("Name不能为空");

        return medicalRecordCategoryService.create(form);
    }

    /**
     * 更新病例分类
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/medicalRecordCategory/update")
    public BaseOutput update(@RequestBody MedicalRecordCategoryUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("Name不能为空");

        if (form.Active == null)
            return BaseOutput.failed("Active不能为空");

        return medicalRecordCategoryService.update(form);
    }


    /**
     * 移动病例分类
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/medicalRecordCategory/move")
    public BaseOutput move(@RequestBody MedicalRecordCategoryMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");

        return medicalRecordCategoryService.move(form);
    }
}
