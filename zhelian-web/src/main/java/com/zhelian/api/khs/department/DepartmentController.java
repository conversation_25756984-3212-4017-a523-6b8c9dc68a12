package com.zhelian.api.khs.department;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.khs.form.input.DepartmentAddForm;
import com.zhelian.model.khs.form.input.DepartmentMoveForm;
import com.zhelian.model.khs.form.input.DepartmentQueryForm;
import com.zhelian.model.khs.form.input.DepartmentUpdateForm;
import com.zhelian.service.khs.department.DepartmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/department")
public class DepartmentController {

    @Autowired
    DepartmentService departmentService;

    @PostMapping(value = "/all")
    public BaseOutput all(@RequestBody DepartmentQueryForm form) {
        return departmentService.all(form);
    }

    @PostMapping(value = "/create")
    public BaseOutput create(@RequestBody DepartmentAddForm form) {

        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("名称不能为空");

        return departmentService.create(form);
    }

    @PostMapping(value = "/update")
    public BaseOutput update(@RequestBody DepartmentUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("名称不能为空");

        if (form.Active == null)
            return BaseOutput.failed("Active不能为空");

        return departmentService.update(form);
    }

    @PostMapping(value = "/move")
    public BaseOutput move(@RequestBody DepartmentMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");

        return departmentService.move(form);
    }
}
