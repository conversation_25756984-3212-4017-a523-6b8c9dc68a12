package com.zhelian.api.khs.treatProjectPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatProjectCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectsCommissionAddForm;
import com.zhelian.service.khs.treatProjectPerformance.TreatProjectPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class TreatProjectPerformanceController {

    @Autowired
    TreatProjectPerformanceService treatProjectPerformanceService;

    static boolean checkRate(TreatProjectsCommissionAddForm form) {

        for (TreatProjectCommissionAddForm treatProjectPerformanceAddForm : form.Project) {

            if ((treatProjectPerformanceAddForm.PayRate != null && treatProjectPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (treatProjectPerformanceAddForm.CardRate != null && treatProjectPerformanceAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                    || (treatProjectPerformanceAddForm.CardLargessRate != null && treatProjectPerformanceAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                    || (treatProjectPerformanceAddForm.LargessRate != null && treatProjectPerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
            )
                return false;
        }
        return true;
    }

    @RequestMapping(value = "/treatProjectPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allTreatProjectPerformance(@RequestBody TreatProjectCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return treatProjectPerformanceService.allTreatProjectPerformance(form);
    }

    @RequestMapping(value = "/treatProjectPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateTreatProjectPerformance(@RequestBody TreatProjectsCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatProjectPerformanceService.updateTreatProjectPerformance(form);
    }
}

