package com.zhelian.api.khs.saleProjectPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleProjectSchemeHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectSchemeHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectSchemeHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectsSchemeHandlerCommissionAddForm;
import com.zhelian.service.khs.saleProjectPerformance.SaleProjectSchemeHandlerPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class SaleProjectSchemeHandlerPerformanceController {

    @Autowired
    SaleProjectSchemeHandlerPerformanceService saleProjectSchemeHandlerPerformanceService;

    static boolean checkRate(SaleProjectsSchemeHandlerCommissionAddForm form) {
        if (form.ProjectHandler != null) {
            for (SaleProjectSchemeHandlerCommissionAddForm handlerPerformanceAddForm : form.ProjectHandler) {

                if ((handlerPerformanceAddForm.PayRate != null && handlerPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerPerformanceAddForm.SavingCardRate != null && handlerPerformanceAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerPerformanceAddForm.SavingCardLargessRate != null && handlerPerformanceAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                    return false;

                for (SaleProjectSchemeHandlerJobTypeCommissionAddForm jobTypePerformanceAddForm : handlerPerformanceAddForm.HandlerJobTypeCommission) {


                    if ((jobTypePerformanceAddForm.PayRate != null && jobTypePerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.SavingCardRate != null && jobTypePerformanceAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.SavingCardLargessRate != null && jobTypePerformanceAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                        return false;
                }

            }
        }
        return true;
    }

    @RequestMapping(value = "/saleProjectSchemeHandlerPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allSaleProSchemeHandlerPerformance(@RequestBody SaleProjectSchemeHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");

        return saleProjectSchemeHandlerPerformanceService.allSaleProjectSchemeHandlerPerformance(form);
    }

    @RequestMapping(value = "/saleProjectSchemeHandlerPerformance/packageCard", method = {RequestMethod.POST})
    public BaseOutput allSalepackageCardProductSchemeHandlerPerformance(@RequestBody SaleProjectSchemeHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return saleProjectSchemeHandlerPerformanceService.allSalePackageCardProjectSchemeHandlerPerformance(form);
    }

    @RequestMapping(value = "/saleProjectSchemeHandlerPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleProjectSchemeHandlerPerformance(@RequestBody SaleProjectsSchemeHandlerCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return saleProjectSchemeHandlerPerformanceService.updateSaleProjectSchemeHandlerPerformance(form);
    }
}

