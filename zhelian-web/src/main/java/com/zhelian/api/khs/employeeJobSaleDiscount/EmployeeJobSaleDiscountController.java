package com.zhelian.api.khs.employeeJobSaleDiscount;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.khs.form.input.EmployeeJobSaleDiscountAddForm;
import com.zhelian.model.khs.form.input.EmployeeJobSaleDiscountDeleteForm;
import com.zhelian.model.khs.form.input.EmployeeJobSaleDiscountQueryForm;
import com.zhelian.model.khs.form.input.EmployeeJobSaleDiscountUpdateForm;
import com.zhelian.service.khs.employeeJobSaleDiscount.EmployeeJobSaleDiscountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
public class EmployeeJobSaleDiscountController {

    @Autowired
    EmployeeJobSaleDiscountService employeeJobSaleDiscountService;

    /**
     * 获取员工职位折扣列表
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/employeeJobSaleDiscount/list")
    public BaseOutput list(@RequestBody EmployeeJobSaleDiscountQueryForm form) {
        return employeeJobSaleDiscountService.list(form);
    }

    /**
     * 创建员工职位折扣
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/employeeJobSaleDiscount/create")
    public BaseOutput create(@RequestBody EmployeeJobSaleDiscountAddForm form) {

        if (form.JobTypeID == null) {
            return BaseOutput.failed("员工职位不能为空");
        }

        return employeeJobSaleDiscountService.create(form);
    }

    /**
     * 更新员工职位折扣
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/employeeJobSaleDiscount/update")
    public BaseOutput update(@RequestBody EmployeeJobSaleDiscountUpdateForm form) {

        if (form.ID == null) {
            return BaseOutput.failed("ID不能为空");
        }

        if (form.JobTypeID == null) {
            return BaseOutput.failed("员工职位不能为空");
        }

        return employeeJobSaleDiscountService.update(form);
    }

    /**
     * 删除员工职位折扣
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/employeeJobSaleDiscount/delete")
    public BaseOutput delete(@RequestBody EmployeeJobSaleDiscountDeleteForm form) {

        if (form.ID == null) {
            return BaseOutput.failed("ID不能为空");
        }

        return employeeJobSaleDiscountService.delete(form);
    }
}
