package com.zhelian.api.khs.treatTimeCardPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatCardCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatCardProjectCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardProjectsCommissionAddForm;
import com.zhelian.service.khs.treatTimeCardPerformance.TreatTimeCardProjectPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

@RestController
@RequestMapping(value = "/api")
public class TreatTimeCardProjectPerformanceController {

    @Autowired
    TreatTimeCardProjectPerformanceService treatTimeCardProjectPerformanceService;

    static boolean checkRate(TreatCardProjectsCommissionAddForm form) {
        for (TreatCardProjectCommissionAddForm treatCardProjectPerformanceAddForm : form.Project) {
            if ((treatCardProjectPerformanceAddForm.PayRate != null && treatCardProjectPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (treatCardProjectPerformanceAddForm.CardRate != null && treatCardProjectPerformanceAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                    || (treatCardProjectPerformanceAddForm.CardLargessRate != null && treatCardProjectPerformanceAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                    || (treatCardProjectPerformanceAddForm.LargessRate != null && treatCardProjectPerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
            )
                return false;

        }
        return true;
    }

    @RequestMapping(value = "/treatTimeCardProjectPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allTreatTimeCardProjectPerformance(@RequestBody TreatCardCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CardID == null)
            return BaseOutput.failed("通用次卡编号不能为空");
        return treatTimeCardProjectPerformanceService.allTreatTimeCardProjectPerformance(form);
    }

    @RequestMapping(value = "/treatTimeCardProjectPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateTreatTimeCardProjectPerformance(@RequestBody TreatCardProjectsCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatTimeCardProjectPerformanceService.updateTreatTimeCardProjectPerformance(form);
    }
}

