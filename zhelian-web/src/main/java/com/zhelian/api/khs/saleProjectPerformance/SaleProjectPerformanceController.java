package com.zhelian.api.khs.saleProjectPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectsCommissionAddForm;
import com.zhelian.service.khs.saleProjectPerformance.SaleProjectPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class SaleProjectPerformanceController {

    @Autowired
    SaleProjectPerformanceService saleProjectPerformanceService;

    static boolean checkRate(SaleProjectsCommissionAddForm form) {

        for (SaleProjectCommissionAddForm saleProjectPerformanceAddForm : form.Project) {

            if ((saleProjectPerformanceAddForm.PayRate != null && saleProjectPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (saleProjectPerformanceAddForm.SavingCardRate != null && saleProjectPerformanceAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                    || (saleProjectPerformanceAddForm.SavingCardLargessRate != null && saleProjectPerformanceAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                return false;
        }
        return true;
    }

    @RequestMapping(value = "/saleProjectPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allSaleProjectPerformance(@RequestBody SaleProjectCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return saleProjectPerformanceService.allSaleProjectPerformance(form);
    }

    @RequestMapping(value = "/saleProjectPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleProjectPerformance(@RequestBody SaleProjectsCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return saleProjectPerformanceService.updateSaleProjectPerformance(form);
    }
}

