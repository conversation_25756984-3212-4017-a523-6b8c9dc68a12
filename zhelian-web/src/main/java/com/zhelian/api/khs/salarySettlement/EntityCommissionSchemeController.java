package com.zhelian.api.khs.salarySettlement;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.khs.form.input.*;
import com.zhelian.service.khs.salarySettlement.EntityCommissionSchemeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api/entityCommissionScheme")
public class EntityCommissionSchemeController {

    @Autowired
    EntityCommissionSchemeService entityCommissionSchemeService;


    @RequestMapping(value = "/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody EntityCommissionSchemeQueryForm form) {
        return entityCommissionSchemeService.all(form);
    }

    @RequestMapping(value = "/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody EntityCommissionSchemeAddForm form) {
        if (form.PerformanceEvaluationSchemeID == null)
            return BaseOutput.failed("业绩考核方案编号不能为空");
        if (form.PerformanceCalculationSchemeID == null)
            return BaseOutput.failed("业绩计算方案编号不能为空");
        if (form.Calculation == null)
            return BaseOutput.failed("取值方式不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return entityCommissionSchemeService.create(form);
    }

    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody EntityCommissionSchemeUpdateForm form) {
        if (form.PerformanceEvaluationSchemeID == null)
            return BaseOutput.failed("业绩考核方案编号不能为空");
        if (form.PerformanceCalculationSchemeID == null)
            return BaseOutput.failed("业绩计算方案编号不能为空");
        if (form.Calculation == null)
            return BaseOutput.failed("取值方式不能为空");
        if (form.Active == null)
            return BaseOutput.failed("有效性不能为空");
        if (!checkUpdateRate(form))
            return BaseOutput.failed("比例不能大于100");
        return entityCommissionSchemeService.update(form);
    }

    @RequestMapping(value = "/commission", method = {RequestMethod.POST})
    public BaseOutput detail(@RequestBody EntityCommissionQueryForm form) {

        return entityCommissionSchemeService.commission(form);
    }

    @RequestMapping(value = "/allEmployee", method = {RequestMethod.POST})
    public BaseOutput allEmployee(@RequestBody EntityCommissionQueryForm form) {
        return entityCommissionSchemeService.allEmployee(form);
    }

    @RequestMapping(value = "/employee", method = {RequestMethod.POST})
    public BaseOutput employee(@RequestBody EntityCommissionQueryForm form) {
        return entityCommissionSchemeService.employee(form);
    }

    @RequestMapping(value = "/employeeCalculateEntity", method = {RequestMethod.POST})
    public BaseOutput employeeCalculateEntity(@RequestBody EntityCommissionQueryForm form) {

        return entityCommissionSchemeService.employeeCalculateEntity(form);
    }

    static boolean checkRate(EntityCommissionSchemeAddForm form) {
        for (EntityCommissionAddForm entityCommissionAddForm : form.Commission) {
            if ((entityCommissionAddForm.Rate != null && entityCommissionAddForm.Rate.compareTo(new BigDecimal(100)) > 0))
                return false;
        }
        return true;
    }

    static boolean checkUpdateRate(EntityCommissionSchemeUpdateForm form) {
        for (EntityCommissionAddForm entityCommissionAddForm : form.Commission) {
            if ((entityCommissionAddForm.Rate != null && entityCommissionAddForm.Rate.compareTo(new BigDecimal(100)) > 0))
                return false;
        }
        return true;
    }
}