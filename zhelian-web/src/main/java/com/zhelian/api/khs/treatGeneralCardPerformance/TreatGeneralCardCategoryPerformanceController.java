package com.zhelian.api.khs.treatGeneralCardPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCategoryCommissionQueryForm;
import com.zhelian.service.khs.treatGeneralCardPerformance.TreatGeneralCardCategoryPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class TreatGeneralCardCategoryPerformanceController {

    @Autowired
    TreatGeneralCardCategoryPerformanceService treatGeneralCardCategoryPerformanceService;

    static boolean checkRate(TreatCardCategoryAddForm form) {
        for (TreatCardCategoryCommissionAddForm handlerPerformanceAddForm : form.Category) {
            if ((handlerPerformanceAddForm.PayRate != null && handlerPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerPerformanceAddForm.CardRate != null && handlerPerformanceAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerPerformanceAddForm.CardLargessRate != null && handlerPerformanceAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerPerformanceAddForm.LargessRate != null && handlerPerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
            )
                return false;
        }
        return true;
    }

    @RequestMapping(value = "/treatGeneralCardCategoryPerformance/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody TreatSavingCardProjectCategoryCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return treatGeneralCardCategoryPerformanceService.alltreatGeneralCardProjectCategoryPerformance(form);
    }

    @RequestMapping(value = "/treatGeneralCardCategoryPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateTreatGeneralCardProjectCategoryPerformance(@RequestBody TreatCardCategoryAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatGeneralCardCategoryPerformanceService.updateTreatGeneralCardProjectCategoryPerformance(form);
    }
}

