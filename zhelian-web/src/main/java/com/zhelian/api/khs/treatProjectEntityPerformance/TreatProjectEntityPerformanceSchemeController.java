package com.zhelian.api.khs.treatProjectEntityPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatProjectCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCommissionSchemeDeleteForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCommissionSchemeQueryForm;
import com.zhelian.service.khs.treatProjectEntityPerformance.TreatProjectEntityPerformanceSchemeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
public class TreatProjectEntityPerformanceSchemeController {

    @Autowired
    TreatProjectEntityPerformanceSchemeService treatProjectEntityPerformanceSchemeService;

    @RequestMapping(value = "/treatProjectEntityPerformanceScheme/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody TreatProjectCommissionSchemeQueryForm form) {
        return treatProjectEntityPerformanceSchemeService.treatProjectEntityPerformanceSchemeList(form);
    }

    @RequestMapping(value = "/treatProjectEntityPerformanceScheme/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody TreatProjectCommissionSchemeAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return treatProjectEntityPerformanceSchemeService.createTreatProjectEntityPerformanceScheme(form);
    }

    @RequestMapping(value = "/treatProjectEntityPerformanceScheme/delete", method = {RequestMethod.POST})
    public BaseOutput delete(@RequestBody TreatProjectCommissionSchemeDeleteForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return treatProjectEntityPerformanceSchemeService.deleteTreatProjectEntityPerformanceScheme(form);
    }
}

