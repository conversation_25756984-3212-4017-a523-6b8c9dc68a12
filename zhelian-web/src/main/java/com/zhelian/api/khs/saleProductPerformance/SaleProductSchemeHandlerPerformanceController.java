package com.zhelian.api.khs.saleProductPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleProductSchemeHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductSchemeHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProductSchemeHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductsSchemeHandlerCommissionAddForm;
import com.zhelian.service.khs.saleProductPerformance.SaleProductSchemeHandlerPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class SaleProductSchemeHandlerPerformanceController {

    @Autowired
    SaleProductSchemeHandlerPerformanceService saleProductSchemeHandlerPerformanceService;

    static boolean checkRate(SaleProductsSchemeHandlerCommissionAddForm form) {
        if (form.ProductHandler != null) {
            for (SaleProductSchemeHandlerCommissionAddForm handlerPerformanceAddForm : form.ProductHandler) {
                if ((handlerPerformanceAddForm.PayRate != null && handlerPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerPerformanceAddForm.SavingCardRate != null && handlerPerformanceAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerPerformanceAddForm.SavingCardLargessRate != null && handlerPerformanceAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                    return false;

                for (SaleProductSchemeHandlerJobTypeCommissionAddForm jobTypePerformanceAddForm : handlerPerformanceAddForm.HandlerJobTypeCommission) {


                    if ((jobTypePerformanceAddForm.PayRate != null && jobTypePerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.SavingCardRate != null && jobTypePerformanceAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.SavingCardLargessRate != null && jobTypePerformanceAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                        return false;
                }

            }
        }
        if (form.PackageCardProductHandler != null) {
            for (SaleProductSchemeHandlerCommissionAddForm handlerPerformanceAddForm : form.PackageCardProductHandler) {

                if ((handlerPerformanceAddForm.PayRate != null && handlerPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerPerformanceAddForm.SavingCardRate != null && handlerPerformanceAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerPerformanceAddForm.SavingCardLargessRate != null && handlerPerformanceAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                    return false;

                for (SaleProductSchemeHandlerJobTypeCommissionAddForm jobTypePerformanceAddForm : handlerPerformanceAddForm.HandlerJobTypeCommission) {


                    if ((jobTypePerformanceAddForm.PayRate != null && jobTypePerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.SavingCardRate != null && jobTypePerformanceAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.SavingCardLargessRate != null && jobTypePerformanceAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                        return false;
                }

            }
        }
        return true;
    }

    @RequestMapping(value = "/saleProductSchemeHandlerPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allSaleProductSchemeHandlerPerformance(@RequestBody SaleProductSchemeHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return saleProductSchemeHandlerPerformanceService.allSaleProductSchemeHandlerPerformance(form);
    }

    @RequestMapping(value = "/saleProductSchemeHandlerPerformance/packageCard", method = {RequestMethod.POST})
    public BaseOutput allSalepackageCardProductSchemeHandlerPerformance(@RequestBody SaleProductSchemeHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return saleProductSchemeHandlerPerformanceService.allSalePackageCardProductSchemeHandlerPerformance(form);
    }

    @RequestMapping(value = "/saleProductSchemeHandlerPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleProductSchemeHandlerPerformance(@RequestBody SaleProductsSchemeHandlerCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return saleProductSchemeHandlerPerformanceService.updateSaleProductSchemeHandlerPerformance(form);
    }
}


