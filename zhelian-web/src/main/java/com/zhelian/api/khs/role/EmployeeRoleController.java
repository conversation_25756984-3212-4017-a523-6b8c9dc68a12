/**
 * fileName: EmployeeRoleController.java
 * author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * date: 2020/2/29 10:49 AM
 * copyright(c) 2019-2020 xxx公司
 */
package com.zhelian.api.khs.role;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.khs.form.input.EmployeeRoleBatchUpdateForm;
import com.zhelian.model.khs.form.input.EmployeeRoleQueryForm;
import com.zhelian.service.khs.role.EmployeeRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @className: EmployeeRoleController
 * @description:
 * @version: V1.0
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2020/2/29 10:49 AM
 */
@RestController
@RequestMapping(value = "/api")
public class EmployeeRoleController {

    @Autowired
    EmployeeRoleService employeeRoleService;


    /**
     * 功能描述:
     * <6.1.获取用户角色列表>
     *
     * @param: form
     * @return: com.zhelian.model.base.BaseOutput
     * @exception:
     * @author: WangXianXian
     * @date: 2020/2/29 1:07 PM
     */
    @RequestMapping(value = "/employeeRole/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody EmployeeRoleQueryForm form) {

        if (!StringUtils.hasText(form.EmployeeID))
            return BaseOutput.failed("员工编号不能为空");

        return employeeRoleService.employeeRole(form);
    }


    /**
     * 功能描述:
     * <6.2.批量更新用户角色>
     *
     * @param: form
     * @return: com.zhelian.model.base.BaseOutput
     * @exception:
     * @author: WangXianXian
     * @date: 2020/2/29 1:08 PM
     */
    @RequestMapping(value = "/employeeRole/updateUserRoleBatch", method = {RequestMethod.POST})
    public BaseOutput updateUserRoleBatch(@RequestBody EmployeeRoleBatchUpdateForm form) {

        if (form.EmployeeID == null || form.EmployeeID.isEmpty())
            return BaseOutput.failed("员工编号不能为空");

        return employeeRoleService.updateUserRoleBatch(form);
    }

}