package com.zhelian.api.khs.channelSalarySettlement;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodQueryForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeTreatGoodAddForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeTreatGoodsAddForm;
import com.zhelian.service.khs.channelSalarySettlement.ChannelPerformanceSchemeTreatSavingCardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api/channelPerformanceSchemeTreatSavingCard")
public class ChannelPerformanceSchemeTreatSavingCardController {

    @Autowired
    ChannelPerformanceSchemeTreatSavingCardService channelPerformanceSchemeTreatSavingCardService;


    @RequestMapping(value = "/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody PerformanceSchemeGoodQueryForm form) {
        if (form.PerformanceSchemeID == null)
            return BaseOutput.failed("业绩方案不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");

        return channelPerformanceSchemeTreatSavingCardService.all(form);
    }

    @RequestMapping(value = "/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody PerformanceSchemeTreatGoodsAddForm form) {
        if (form.PerformanceSchemeID == null)
            return BaseOutput.failed("业绩方案不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return channelPerformanceSchemeTreatSavingCardService.create(form);
    }


    static boolean checkRate(PerformanceSchemeTreatGoodsAddForm form) {

        for (PerformanceSchemeTreatGoodAddForm performanceSchemeGoodAddForm : form.Good) {

            if ((performanceSchemeGoodAddForm.PerformancePayRate != null && performanceSchemeGoodAddForm.PerformancePayRate.compareTo(new BigDecimal(100)) > 0)
                    || (performanceSchemeGoodAddForm.PerformanceCardRate != null && performanceSchemeGoodAddForm.PerformanceCardRate.compareTo(new BigDecimal(100)) > 0)
                    || (performanceSchemeGoodAddForm.PerformanceCardLargessRate != null && performanceSchemeGoodAddForm.PerformanceCardLargessRate.compareTo(new BigDecimal(100)) > 0)
                    || (performanceSchemeGoodAddForm.PerformanceLargessRate != null && performanceSchemeGoodAddForm.PerformanceLargessRate.compareTo(new BigDecimal(100)) > 0)
            )
                return false;
        }
        return true;
    }
}