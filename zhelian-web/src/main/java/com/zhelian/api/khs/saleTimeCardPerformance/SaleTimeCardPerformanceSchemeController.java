package com.zhelian.api.khs.saleTimeCardPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCommissionSchemeDeleteForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCommissionSchemeQueryForm;
import com.zhelian.service.khs.saleTimeCardPerformance.SaleTimeCardPerformanceSchemeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class SaleTimeCardPerformanceSchemeController {

    @Autowired
    SaleTimeCardPerformanceSchemeService saleTimeCardPerformanceSchemeService;

    @RequestMapping(value = "/saleTimeCardPerformanceScheme/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody SaleTimeCardCommissionSchemeQueryForm form) {
        return saleTimeCardPerformanceSchemeService.saleTimeCardPerformanceSchemeList(form);
    }

    @RequestMapping(value = "/saleTimeCardPerformanceScheme/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody SaleTimeCardCommissionSchemeAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return saleTimeCardPerformanceSchemeService.createSaleTimeCardPerformanceScheme(form);
    }

    @RequestMapping(value = "/saleTimeCardPerformanceScheme/delete", method = {RequestMethod.POST})
    public BaseOutput delete(@RequestBody SaleTimeCardCommissionSchemeDeleteForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return saleTimeCardPerformanceSchemeService.deleteSaleTimeCardPerformanceScheme(form);
    }

}

