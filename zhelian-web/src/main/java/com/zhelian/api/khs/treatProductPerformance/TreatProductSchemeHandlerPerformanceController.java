package com.zhelian.api.khs.treatProductPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductSchemeHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatProductSchemeHandlersCommissionAddForm;
import com.zhelian.service.khs.treatProductPerformance.TreatProductSchemeHandlerPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

@RestController
@RequestMapping(value = "/api")
public class TreatProductSchemeHandlerPerformanceController {

    @Autowired
    TreatProductSchemeHandlerPerformanceService treatProductSchemeHandlerPerformanceService;

    static boolean checkRate(TreatProductSchemeHandlersCommissionAddForm form) {
        if (form.Handler != null) {
            for (TreatProductHandlerCommissionAddForm handlerPerformanceAddForm : form.Handler) {

                if ((handlerPerformanceAddForm.PayRate != null && handlerPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerPerformanceAddForm.CardRate != null && handlerPerformanceAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerPerformanceAddForm.CardLargessRate != null && handlerPerformanceAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerPerformanceAddForm.LargessRate != null && handlerPerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
                )
                    return false;

                for (TreatProductCategoryHandlerJobTypeCommissionAddForm jobTypePerformanceAddForm : handlerPerformanceAddForm.HandlerJobTypeCommission) {

                    if ((jobTypePerformanceAddForm.PayRate != null && jobTypePerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.CardRate != null && jobTypePerformanceAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.CardLargessRate != null && jobTypePerformanceAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.LargessRate != null && jobTypePerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0))
                        return false;
                }
            }
        }
        return true;
    }

    @RequestMapping(value = "/treatProductSchemeHandlerPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allSaleProSchemeHandlerPerformance(@RequestBody TreatProductSchemeHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");

        return treatProductSchemeHandlerPerformanceService.allTreatProductSchemeHandlerPerformance(form);
    }

    @RequestMapping(value = "/treatProductSchemeHandlerPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleProductSchemeHandlerPerformance(@RequestBody TreatProductSchemeHandlersCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatProductSchemeHandlerPerformanceService.updateTreatProductSchemeHandlerPerformance(form);
    }
}

