package com.zhelian.api.khs.medicalRecord;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.khs.form.input.*;
import com.zhelian.service.khs.medicalRecord.MedicalRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
public class MedicalRecordController {


    @Autowired
    MedicalRecordService medicalRecordService;

    /**
     * 查询病例模版
     *
     * @return
     */
    @PostMapping(value = "/medicalRecord/list")
    public BaseOutput list(@RequestBody MedicalRecordCatalogQueryForm form) {
        if (form.CategoryID == null)
            return BaseOutput.failed("CategoryID不能为空");

        return medicalRecordService.list(form);
    }

    /**
     * 创建病例目录
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/medicalRecord/createCatalog")
    public BaseOutput createCatalog(@RequestBody MedicalRecordCatalogAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("Name不能为空");

        if (form.CategoryID == null)
            return BaseOutput.failed("CategoryID不能为空");

        return medicalRecordService.createCatalog(form);
    }

    /**
     * 更新病例目录
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/medicalRecord/updateCatalog")
    public BaseOutput updateCatalog(@RequestBody MedicalRecordCatalogUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("Name不能为空");

        if (form.CategoryID == null)
            return BaseOutput.failed("CategoryID不能为空");

        return medicalRecordService.updateCatalog(form);
    }

    /**
     * 删除病例目录
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/medicalRecord/deleteCatalog")
    public BaseOutput deleteCatalog(@RequestBody MedicalRecordCatalogDeleteForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        return medicalRecordService.deleteCatalog(form);
    }


    /**
     * 移动病例目录
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/medicalRecord/moveCatalog")
    public BaseOutput moveCatalog(@RequestBody MedicalRecordCatalogMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("移动后CategoryID不能为空");

        return medicalRecordService.moveCatalog(form);
    }


    /**
     * 查询病例模版
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/medicalRecord/getTemplate")
    public BaseOutput getTemplate(@RequestBody MedicalRecordTemplateQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        return medicalRecordService.getTemplate(form);
    }

    /**
     * 创建病例模版
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/medicalRecord/createTemplate")
    public BaseOutput createTemplate(@RequestBody MedicalRecordTemplateAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("Name不能为空");

        if (form.CategoryID == null)
            return BaseOutput.failed("CategoryID不能为空");

        if (form.CatalogID == null)
            return BaseOutput.failed("CatalogID不能为空");

        return medicalRecordService.createTemplate(form);
    }

    /**
     * 更新病例模版
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/medicalRecord/updateTemplate")
    public BaseOutput updateTemplate(@RequestBody MedicalRecordTemplateUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        return medicalRecordService.updateTemplate(form);
    }

    /**
     * 删除病例模版
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/medicalRecord/deleteTemplate")
    public BaseOutput deleteTemplate(@RequestBody MedicalRecordTemplateDeleteForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        return medicalRecordService.deleteTemplate(form);
    }

    /**
     * 移动病例模版
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/medicalRecord/moveTemplate")
    public BaseOutput moveTemplate(@RequestBody MedicalRecordTemplateMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");
        if (form.CatalogID == null)
            return BaseOutput.failed("移动后CatalogID不能为空");

        return medicalRecordService.moveTemplate(form);
    }

    /**
     * 查询病例词条类别
     *
     * @return
     */
    @PostMapping(value = "/medicalRecord/categoryAll")
    public BaseOutput categoryAll() {
        return medicalRecordService.categoryAll();
    }

    /**
     * 查询病例词条标签
     *
     * @return
     */
    @PostMapping(value = "/medicalRecord/labelContentAll")
    public BaseOutput labelContentAll(@RequestBody MedicalRecordLabelContentAllQueryForm form) {
        return medicalRecordService.labelContentAll(form);
    }


}
