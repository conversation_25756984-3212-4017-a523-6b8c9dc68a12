package com.zhelian.api.khs.saleProjectPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.service.khs.saleProjectPerformance.SaleProjectCategoryHandlerPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

@RestController
@RequestMapping(value = "/api")
public class SaleProjectCategoryHandlerPerformanceController {

    @Autowired
    SaleProjectCategoryHandlerPerformanceService saleProjectCategoryHandlerPerformanceService;

    static boolean checkRate(SaleProjectCategoryHandlerCommissionAddForm form) {
        if (form.ProjectHandler != null) {
            for (SaleProjectCategoryHandlerAddForm handlerAddForm : form.ProjectHandler) {

                if ((handlerAddForm.PayRate != null && handlerAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerAddForm.SavingCardRate != null && handlerAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerAddForm.SavingCardLargessRate != null && handlerAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                    return false;

                for (SaleProjectCategoryHandlerJobTypeCommissionAddForm jobTypePerformanceAddForm : handlerAddForm.HandlerJobTypeCommission) {

                    if ((jobTypePerformanceAddForm.PayRate != null && jobTypePerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.SavingCardRate != null && jobTypePerformanceAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.SavingCardLargessRate != null && jobTypePerformanceAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                        return false;
                }
            }
        }
        return true;
    }

    @RequestMapping(value = "/saleProjectCategoryHandlerPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allSaleProjectCategoryHandlerPerformance(@RequestBody SaleProjectCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return saleProjectCategoryHandlerPerformanceService.allSaleProjectCategoryHandlerPerformance(form);
    }

    @RequestMapping(value = "/saleProjectCategoryHandlerPerformance/packageCard", method = {RequestMethod.POST})
    public BaseOutput allSalePakageCardProjectCategoryHandlerPerformance(@RequestBody SaleProjectCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return saleProjectCategoryHandlerPerformanceService.allSalePakageCardProjectCategoryHandlerPerformance(form);
    }

    @RequestMapping(value = "/saleProjectCategoryHandlerPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleProjectCategoryHandlerPerformance(@RequestBody SaleProjectCategoryHandlerCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return saleProjectCategoryHandlerPerformanceService.updateSaleProjectCategoryHandlerPerformance(form);
    }
}

