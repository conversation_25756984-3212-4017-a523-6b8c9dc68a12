/**
 * fileName: EmployeeController.java
 * author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * date: 2020/2/26 2:33 PM
 * copyright(c) 2019-2020 xxx公司
 */
package com.zhelian.api.khs.employee;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.QueryPageForm;
import com.zhelian.model.khs.entity.EmployeeEntityAddForm;
import com.zhelian.model.khs.form.input.*;
import com.zhelian.service.khs.employee.EmployeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * @className: EmployeeController
 * @description:
 * @version: V1.0
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2020/2/26 2:33 PM
 */
@RestController
@RequestMapping(value = "/api")
public class EmployeeController {

    @Autowired
    EmployeeService employeeService;

    /**
     * 功能描述:
     * <6.通讯录-员工列表>
     *
     * @param: form
     * @return: com.zhelian.model.base.BasePageInfo
     * @exception:
     * @author: WangXianXian
     * @date: 2020/2/21 4:50 PM
     */
    @RequestMapping(value = "/employee/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody EmployeeQueryForm form) {

        return employeeService.getEmpList(form);
    }


    /**
     * 功能描述:
     * <新增员工>
     *
     * @param: form
     * @return: com.zhelian.model.base.BaseOutputForm
     * @exception:
     * @author: WangXianXian
     * @date: 2020/2/21 4:49 PM
     */
    @RequestMapping(value = "/employee/create", method = {RequestMethod.POST})
    public BaseOutput addEmp(@RequestBody EmployeeAddForm form) {
        if (!StringUtils.hasText(form.ID))
            return BaseOutput.failed("员工编号不能为空");
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("员工姓名不能为空");
        if (!StringUtils.hasText(form.PhoneNumber))
            return BaseOutput.failed("员工手机号不能为空");
        if (!StringUtils.hasText(form.Gender))
            return BaseOutput.failed("员工性别不能为空");
        if (form.Entity == null || form.Entity.isEmpty())
            return BaseOutput.failed("员工所属单位不能为空");
        if (form.JobID == null)
            return BaseOutput.failed("员工职务不能为空");
        for (EmployeeEntityAddForm entity : form.Entity) {
            if (entity.EntityID == null) {
                return BaseOutput.failed("门店编号不能为空");
            }
        }
        int primaryEntityNum = 0;
        for (EmployeeEntityAddForm entity : form.Entity) {
            if (entity.IsPrimaryEntity)
                primaryEntityNum++;
        }
        if (primaryEntityNum != 1)
            return BaseOutput.failed("必须设置主部门且只有一个主部门");

        return employeeService.addEmp(form);
    }


    /**
     * 功能描述:
     * <保存员工信息>
     *
     * @param: form
     * @return: com.zhelian.model.base.BaseOutputForm
     * @exception:
     * @author: WangXianXian
     * @date: 2020/2/21 4:49 PM
     */
    @RequestMapping(value = "/employee/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody EmployeeUpdateForm form) {
        if (!StringUtils.hasText(form.ID))
            return BaseOutput.failed("员工编号不能为空");
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("员工姓名不能为空");
        if (!StringUtils.hasText(form.PhoneNumber))
            return BaseOutput.failed("员工手机号不能为空");
        if (!StringUtils.hasText(form.Gender))
            return BaseOutput.failed("员工性别不能为空");
        if (form.State == null)
            return BaseOutput.failed("员工状态不能为空");
        if (form.Entity == null || form.Entity.size() == 0)
            return BaseOutput.failed("员工所属单位不能为空");
        if (form.JobID == null)
            return BaseOutput.failed("员工职务不能为空");

        int primaryEntityNum = 0;
        for (EmployeeEntityAddForm entity : form.Entity) {
            if (entity.IsPrimaryEntity)
                primaryEntityNum++;
        }
        if (primaryEntityNum != 1)
            return BaseOutput.failed("必须设置主部门且只有一个主部门");

        return employeeService.updateEmp(form);
    }

    /**
     * 功能描述:
     * <批量设置职务>
     *
     * @param: form
     * @return: com.zhelian.model.base.BaseOutputForm
     * @exception:
     * @author: WangXianXian
     * @date: 2020/2/21 4:35 PM
     */
    @RequestMapping(value = "/employee/updateEmployeeJobTypeBatch", method = {RequestMethod.POST})
    public BaseOutput updateEmployeeJobTypeBatch(@RequestBody EmployeeJobAddForm form) {
        if (form.EmployeeID == null || form.EmployeeID.isEmpty())
            return BaseOutput.failed("员工不能为空");
        if (form.JobTypeID == null)
            return BaseOutput.failed("职务不能为空");

        return employeeService.updateEmployeeJobTypeBatch(form);
    }

    /**
     * 功能描述:
     * <批量设置所属单位>
     *
     * @param: form
     * @return: com.zhelian.model.base.BaseOutputForm
     * @exception:
     * @author: WangXianXian
     * @date: 2020/2/21 4:35 PM
     */
    @RequestMapping(value = "/employee/updateEmployeeEntityBatch", method = {RequestMethod.POST})
    public BaseOutput updateEmployeeEntityBatch(@RequestBody EmployeeEntityBatchUpdateForm form) {

        if (form.EmployeeID == null || form.EmployeeID.isEmpty())
            return BaseOutput.failed("员工不能为空");

        int primaryEntityNum = 0;
        for (EmployeeEntityAddForm entity : form.Entity) {
            if (entity.IsPrimaryEntity)
                primaryEntityNum++;
        }
        if (primaryEntityNum != 1)
            return BaseOutput.failed("必须设置主部门且只有一个主部门");

        return employeeService.updateEmployeeEntityBatch(form);
    }


    @RequestMapping(value = "/employee/excel", method = {RequestMethod.POST})
    public BaseOutput excelEmployeeSalePerformanceCommissionDetail(@RequestBody EmployeeQueryForm form, HttpServletResponse response) {

        return employeeService.excelEmployee(form, response);
    }

    @RequestMapping(value = "/employee/all", method = {RequestMethod.POST})
    public BaseOutput list() {
        return employeeService.empList();
    }

    @RequestMapping(value = "/employee/entityEmployee", method = {RequestMethod.POST})
    public BaseOutput entityEmployee(@RequestBody EntityEmployeeQueryForm entityEmployeeQueryForm) {
        return employeeService.entityEmployeeList(entityEmployeeQueryForm);
    }

    @RequestMapping(value = "/employee/search", method = {RequestMethod.POST})
    public BaseOutput allEmployee(@RequestBody EmployeeQueryForm form) {
        if (form.SearchKey == null || form.SearchKey.isEmpty())
            return BaseOutput.failed("搜索内容不能为空");
        return employeeService.allEmployee(form);
    }

    /**
     * 本店员工
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/employee/belongCurrentEntity", method = {RequestMethod.POST})
    public BaseOutput belongCurrentEntity(@RequestBody EmployeeQueryForm form) {
        return employeeService.belongCurrentEntity(form);
    }

    /**
     * 跨店员工
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/employee/belongCurrentNoPrimaryEntity", method = {RequestMethod.POST})
    public BaseOutput belongCurrentNoPrimaryEntity(@RequestBody EmployeeQueryForm form) {
        return employeeService.belongCurrentNoPrimaryEntity(form);
    }

    /**
     * 可修改员工列表
     *
     * @return
     */
    @RequestMapping(value = "/employee/belongCurrentNoEntity", method = {RequestMethod.POST})
    public BaseOutput belongCurrentNoEntity(@RequestBody EmployeeQueryForm form) {
        return employeeService.belongCurrentNoEntity(form);
    }

    /**
     * 添加员工所属门店
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/employee/createBelongEntity", method = {RequestMethod.POST})
    public BaseOutput createBelongEntity(@RequestBody EmployeeDeleteBelongEntityForm form) {
        return employeeService.createBelongEntity(form);
    }

    /**
     * 删除员工所属门店
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/employee/deleteBelongEntity", method = {RequestMethod.POST})
    public BaseOutput deleteBelongEntity(@RequestBody EmployeeDeleteBelongEntityForm form) {
        return employeeService.deleteBelongEntity(form);
    }

    /**
     * 权限下的员工带分页
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/employee/permissionEmployeeByPage", method = {RequestMethod.POST})
    public BaseOutput permissionEmployeeByPage(@RequestBody QueryPageForm form) {
        return employeeService.permissionEmployeeByPage(form);
    }

    /**
     * 权限下的员工
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/employee/permissionEmployee", method = {RequestMethod.POST})
    public BaseOutput permissionEmployee(@RequestBody QueryPageForm form) {
        return employeeService.permissionEmployee(form);
    }

    /**你好
     * 查询所有有效员工（支持姓名模糊查询）
     *
     * @param form
     * @return
     */
    @RequestMapping(value = "/employee/listAll", method = {RequestMethod.POST})
    public BaseOutput listAll(@RequestBody QueryPageForm form) {
        return employeeService.listAll(form);
    }
}
