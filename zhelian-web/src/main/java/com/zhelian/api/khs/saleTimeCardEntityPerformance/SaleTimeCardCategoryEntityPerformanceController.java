package com.zhelian.api.khs.saleTimeCardEntityPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCommissionSchemeAddForm;
import com.zhelian.model.khs.form.input.SaleGoodCategoryAddForm;
import com.zhelian.model.khs.form.input.SaleGoodCategoryPerformanceAddForm;
import com.zhelian.service.khs.saleTimeCardEntityPerformance.SaleTimeCardCategoryEntityPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class SaleTimeCardCategoryEntityPerformanceController {

    @Autowired
    SaleTimeCardCategoryEntityPerformanceService saleTimeCardCategoryEntityPerformanceService;

    static boolean checkRate(SaleGoodCategoryAddForm form) {

        for (SaleGoodCategoryPerformanceAddForm saleTimeCardCategoryPerformanceAddForm : form.Category) {

            if ((saleTimeCardCategoryPerformanceAddForm.PayRate != null && saleTimeCardCategoryPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (saleTimeCardCategoryPerformanceAddForm.SavingCardRate != null && saleTimeCardCategoryPerformanceAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                    || (saleTimeCardCategoryPerformanceAddForm.SavingCardLargessRate != null && saleTimeCardCategoryPerformanceAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                return false;
        }
        return true;
    }

    @RequestMapping(value = "/saleTimeCardCategoryEntityPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allSaleTimeCardCategoryEntityPerformance(@RequestBody SaleTimeCardCommissionSchemeAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return saleTimeCardCategoryEntityPerformanceService.allSaleTimeCardCategoryEntityPerformance(form);
    }

    @RequestMapping(value = "/saleTimeCardCategoryEntityPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleTimeCardCategoryEntityPerformance(@RequestBody SaleGoodCategoryAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return saleTimeCardCategoryEntityPerformanceService.updateSaleTimeCardCategoryEntityPerformance(form);
    }
}

