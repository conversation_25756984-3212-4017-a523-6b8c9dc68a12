package com.zhelian.api.khs.saleProjectPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionSchemeDeleteForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionSchemeQueryForm;
import com.zhelian.service.khs.saleProjectPerformance.SaleProjectPerformanceSchemeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
public class SaleProjectPerformanceSchemeController {

    @Autowired
    SaleProjectPerformanceSchemeService saleProjectPerformanceSchemeService;

    @RequestMapping(value = "/saleProjectPerformanceScheme/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody SaleProductCommissionSchemeQueryForm form) {
        return saleProjectPerformanceSchemeService.saleProjectPerformanceSchemeList(form);
    }

    @RequestMapping(value = "/saleProjectPerformanceScheme/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody SaleProductCommissionSchemeAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return saleProjectPerformanceSchemeService.createSaleProjectPerformanceScheme(form);
    }

    @RequestMapping(value = "/saleProjectPerformanceScheme/delete", method = {RequestMethod.POST})
    public BaseOutput delete(@RequestBody SaleProductCommissionSchemeDeleteForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return saleProjectPerformanceSchemeService.deleteSaleProjectPerformanceScheme(form);
    }
}

