package com.zhelian.api.khs.medicalRecordEntry;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.khs.form.input.*;
import com.zhelian.service.khs.medicalRecordEntry.MedicalRecordEntryLabelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
public class MedicalRecordEntryLabelController {

    @Autowired
    MedicalRecordEntryLabelService medicalRecordEntryLabelService;

    /**
     * 查询词条类别
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/medicalRecordEntryLabel/list")
    public BaseOutput list(@RequestBody MedicalRecordEntryLabelQueryForm form) {
        return medicalRecordEntryLabelService.list(form);
    }

    /**
     * 创建词条类别
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/medicalRecordEntryLabel/create")
    public BaseOutput create(@RequestBody MedicalRecordEntryLabelAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("Name不能为空");

        if (form.CategoryID == null)
            return BaseOutput.failed("CategoryID不能为空");

        return medicalRecordEntryLabelService.create(form);
    }

    /**
     * 更新词条类别
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/medicalRecordEntryLabel/update")
    public BaseOutput update(@RequestBody MedicalRecordEntryLabelUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("Name不能为空");

        return medicalRecordEntryLabelService.update(form);
    }

    /**
     * 删除词条类别
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/medicalRecordEntryLabel/delete")
    public BaseOutput delete(@RequestBody MedicalRecordEntryLabelDeleteForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        return medicalRecordEntryLabelService.delete(form);
    }

    /**
     * 移动词条类别
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/medicalRecordEntryLabel/move")
    public BaseOutput move(@RequestBody MedicalRecordEntryLabelMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");

        return medicalRecordEntryLabelService.move(form);
    }
}
