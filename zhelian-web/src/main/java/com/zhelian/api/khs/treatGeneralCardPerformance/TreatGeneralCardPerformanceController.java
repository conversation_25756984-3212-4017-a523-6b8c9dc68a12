package com.zhelian.api.khs.treatGeneralCardPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardsCommissionAddForm;
import com.zhelian.service.khs.treatGeneralCardPerformance.TreatGeneralCardPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

@RestController
@RequestMapping(value = "/api")
public class TreatGeneralCardPerformanceController {

    @Autowired
    TreatGeneralCardPerformanceService treatGeneralCardPerformanceService;

    static boolean checkRate(TreatCardsCommissionAddForm form) {
        for (TreatCardCommissionAddForm handlerPerformanceAddForm : form.Card) {
            if ((handlerPerformanceAddForm.PayRate != null && handlerPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerPerformanceAddForm.CardRate != null && handlerPerformanceAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerPerformanceAddForm.CardLargessRate != null && handlerPerformanceAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerPerformanceAddForm.LargessRate != null && handlerPerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
            )
                return false;

        }
        return true;
    }

    @RequestMapping(value = "/treatGeneralCardPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allTreatGeneralCardPerformance(@RequestBody TreatCardCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return treatGeneralCardPerformanceService.allTreatGeneralCardPerformance(form);
    }

    @RequestMapping(value = "/treatGeneralCardPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateTreatGeneralCardPerformance(@RequestBody TreatCardsCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatGeneralCardPerformanceService.updateTreatGeneralCardPerformance(form);
    }
}

