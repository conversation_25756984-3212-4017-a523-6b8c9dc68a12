package com.zhelian.api.khs.channelEmployeeSalarySettlement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodAddForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodQueryForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeGoodsAddForm;
import com.zhelian.service.khs.channelEmployeeSalarySettlement.ChannelEmployeePerformanceSchemeSavingCardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

@RestController
@RequestMapping(value = "/api/channelEmployeePerformanceSchemeSavingCard")
public class ChannelEmployeePerformanceSchemeSavingCardController {

    @Autowired
    ChannelEmployeePerformanceSchemeSavingCardService channelEmployeePerformanceSchemeSavingCardService;


    @RequestMapping(value = "/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody PerformanceSchemeGoodQueryForm form) {
        if (form.PerformanceSchemeID == null)
            return BaseOutput.failed("业绩方案不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return channelEmployeePerformanceSchemeSavingCardService.all(form);
    }

    @RequestMapping(value = "/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody PerformanceSchemeGoodsAddForm form) {
        if (form.PerformanceSchemeID == null)
            return BaseOutput.failed("业绩方案不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return channelEmployeePerformanceSchemeSavingCardService.create(form);
    }

    static boolean checkRate(PerformanceSchemeGoodsAddForm form) {

        for (PerformanceSchemeGoodAddForm performanceSchemeGoodAddForm : form.Good) {
            if ((performanceSchemeGoodAddForm.PayPerformanceRate != null && performanceSchemeGoodAddForm.PayPerformanceRate.compareTo(new BigDecimal(100)) > 0))
                return false;
        }
        return true;
    }
}
