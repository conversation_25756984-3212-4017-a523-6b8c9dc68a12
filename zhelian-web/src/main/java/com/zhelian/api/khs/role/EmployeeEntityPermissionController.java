/**
 * fileName: EmployeeRoleController.java
 * author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * date: 2020/2/29 10:49 AM
 * copyright(c) 2019-2020 xxx公司
 */
package com.zhelian.api.khs.role;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.khs.form.input.EmployeeEntityPermissionBatchUpdateForm;
import com.zhelian.model.khs.form.input.EmployeeEntityPermissionQueryForm;
import com.zhelian.service.khs.role.EmployeeEntityPermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @className: EmployeeRoleController
 * @description:
 * @version: V1.0
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2020/2/29 10:49 AM
 */
@RestController
@RequestMapping(value = "/api")
public class EmployeeEntityPermissionController {

    @Autowired
    EmployeeEntityPermissionService employeeEntityPermissionService;


    /**
     * 功能描述:
     * <6.3.获取用户权限范围列表>
     *
     * @param: form
     * @return: com.zhelian.model.base.BaseOutput
     * @exception:
     * @author: WangXianXian
     * @date: 2020/2/29 1:07 PM
     */
    @RequestMapping(value = "/employeeEntityPermission/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody EmployeeEntityPermissionQueryForm form) {

        if (!StringUtils.hasText(form.EmployeeID))
            return BaseOutput.failed("员工编号不能为空");

        return employeeEntityPermissionService.employeeEntityPermission(form);
    }


    /**
     * 功能描述:
     * <6.4.批量更新用户权限范围>
     *
     * @param: form
     * @return: com.zhelian.model.base.BaseOutput
     * @exception:
     * @author: WangXianXian
     * @date: 2020/2/29 1:07 PM
     */
    @RequestMapping(value = "/employeeEntityPermission/updateUserEntityBatch", method = {RequestMethod.POST})
    public BaseOutput updateUserRoleBatch(@RequestBody EmployeeEntityPermissionBatchUpdateForm form) {

        if (form.EmployeeID == null || form.EmployeeID.isEmpty())
            return BaseOutput.failed("员工编号不能为空");


        return employeeEntityPermissionService.updateUserEntityPermissionBatch(form);
    }

}