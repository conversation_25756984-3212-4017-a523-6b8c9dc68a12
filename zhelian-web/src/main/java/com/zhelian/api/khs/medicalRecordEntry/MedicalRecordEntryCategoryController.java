package com.zhelian.api.khs.medicalRecordEntry;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.khs.form.input.MedicalRecordEntryCategoryAddForm;
import com.zhelian.model.khs.form.input.MedicalRecordEntryCategoryDeleteForm;
import com.zhelian.model.khs.form.input.MedicalRecordEntryCategoryMoveForm;
import com.zhelian.model.khs.form.input.MedicalRecordEntryCategoryUpdateForm;
import com.zhelian.service.khs.medicalRecordEntry.MedicalRecordEntryCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
public class MedicalRecordEntryCategoryController {

    @Autowired
    MedicalRecordEntryCategoryService medicalRecordEntryCategoryService;

    /**
     * 查询词条分类
     *
     * @return
     */
    @PostMapping(value = "/medicalRecordEntryCategory/list")
    public BaseOutput list() {
        return medicalRecordEntryCategoryService.list();
    }

    /**
     * 创建词条分类
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/medicalRecordEntryCategory/create")
    public BaseOutput create(@RequestBody MedicalRecordEntryCategoryAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("Name不能为空");

        return medicalRecordEntryCategoryService.create(form);
    }

    /**
     * 更新词条分类
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/medicalRecordEntryCategory/update")
    public BaseOutput update(@RequestBody MedicalRecordEntryCategoryUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");

        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("Name不能为空");

        return medicalRecordEntryCategoryService.update(form);
    }

    /**
     * 删除词条分类
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/medicalRecordEntryCategory/delete")
    public BaseOutput delete(@RequestBody MedicalRecordEntryCategoryDeleteForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        return medicalRecordEntryCategoryService.delete(form);
    }


    /**
     * 移动词条分类
     *
     * @param form
     * @return
     */
    @PostMapping(value = "/medicalRecordEntryCategory/move")
    public BaseOutput move(@RequestBody MedicalRecordEntryCategoryMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");

        return medicalRecordEntryCategoryService.move(form);
    }
}
