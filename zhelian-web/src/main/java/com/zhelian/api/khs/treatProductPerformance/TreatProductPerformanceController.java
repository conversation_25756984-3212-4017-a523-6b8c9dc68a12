package com.zhelian.api.khs.treatProductPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatProductCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatProductsCommissionAddForm;
import com.zhelian.service.khs.treatProductPerformance.TreatProductPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class TreatProductPerformanceController {

    @Autowired
    TreatProductPerformanceService treatProductPerformanceService;

    static boolean checkRate(TreatProductsCommissionAddForm form) {

        for (TreatProductCommissionAddForm treatProductPerformanceAddForm : form.Product) {

            if ((treatProductPerformanceAddForm.PayRate != null && treatProductPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (treatProductPerformanceAddForm.CardRate != null && treatProductPerformanceAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                    || (treatProductPerformanceAddForm.CardLargessRate != null && treatProductPerformanceAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                    || (treatProductPerformanceAddForm.LargessRate != null && treatProductPerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
            )
                return false;
        }
        return true;
    }

    @RequestMapping(value = "/treatProductPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allTreatProductPerformance(@RequestBody TreatProductCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return treatProductPerformanceService.allTreatProductPerformance(form);
    }

    @RequestMapping(value = "/treatProductPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateTreatProductPerformance(@RequestBody TreatProductsCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatProductPerformanceService.updateTreatProductPerformance(form);
    }
}

