package com.zhelian.api.khs.salarySettlement;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.khs.form.input.*;
import com.zhelian.service.khs.salarySettlement.PerformanceSchemeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api/performanceScheme")
public class PerformanceSchemeController {

    @Autowired
    PerformanceSchemeService performanceSchemeService;

    static boolean checkRate(PerformanceSchemeUpdateForm form) {

        for (PerformanceSchemeSaleCategoryAddForm performanceSchemeGoodAddForm : form.ProductCategory) {
            if ((performanceSchemeGoodAddForm.PayPerformanceRate != null && performanceSchemeGoodAddForm.PayPerformanceRate.compareTo(new BigDecimal(100)) > 0)
                    || (performanceSchemeGoodAddForm.SavingCardPerformanceRate != null && performanceSchemeGoodAddForm.SavingCardPerformanceRate.compareTo(new BigDecimal(100)) > 0)
                    || (performanceSchemeGoodAddForm.SavingCardPerformanceLargessRate != null && performanceSchemeGoodAddForm.SavingCardPerformanceLargessRate.compareTo(new BigDecimal(100)) > 0))
                return false;
        }
        for (PerformanceSchemeSaleCategoryAddForm performanceSchemeGoodAddForm : form.ProjectCategory) {
            if ((performanceSchemeGoodAddForm.PayPerformanceRate != null && performanceSchemeGoodAddForm.PayPerformanceRate.compareTo(new BigDecimal(100)) > 0)
                    || (performanceSchemeGoodAddForm.SavingCardPerformanceRate != null && performanceSchemeGoodAddForm.SavingCardPerformanceRate.compareTo(new BigDecimal(100)) > 0)
                    || (performanceSchemeGoodAddForm.SavingCardPerformanceLargessRate != null && performanceSchemeGoodAddForm.SavingCardPerformanceLargessRate.compareTo(new BigDecimal(100)) > 0))
                return false;
        }
        for (PerformanceSchemeSaleCategoryAddForm performanceSchemeGoodAddForm : form.GeneralCardCategory) {
            if ((performanceSchemeGoodAddForm.PayPerformanceRate != null && performanceSchemeGoodAddForm.PayPerformanceRate.compareTo(new BigDecimal(100)) > 0)
                    || (performanceSchemeGoodAddForm.SavingCardPerformanceRate != null && performanceSchemeGoodAddForm.SavingCardPerformanceRate.compareTo(new BigDecimal(100)) > 0)
                    || (performanceSchemeGoodAddForm.SavingCardPerformanceLargessRate != null && performanceSchemeGoodAddForm.SavingCardPerformanceLargessRate.compareTo(new BigDecimal(100)) > 0))
                return false;
        }
        for (PerformanceSchemeSaleCategoryAddForm performanceSchemeGoodAddForm : form.TimeCardCategory) {
            if ((performanceSchemeGoodAddForm.PayPerformanceRate != null && performanceSchemeGoodAddForm.PayPerformanceRate.compareTo(new BigDecimal(100)) > 0)
                    || (performanceSchemeGoodAddForm.SavingCardPerformanceRate != null && performanceSchemeGoodAddForm.SavingCardPerformanceRate.compareTo(new BigDecimal(100)) > 0)
                    || (performanceSchemeGoodAddForm.SavingCardPerformanceLargessRate != null && performanceSchemeGoodAddForm.SavingCardPerformanceLargessRate.compareTo(new BigDecimal(100)) > 0))
                return false;
        }
        for (PerformanceSchemeSaleCategoryAddForm performanceSchemeGoodAddForm : form.SavingCardCategory) {
            if ((performanceSchemeGoodAddForm.PayPerformanceRate != null && performanceSchemeGoodAddForm.PayPerformanceRate.compareTo(new BigDecimal(100)) > 0))
                return false;
        }
        for (PerformanceSchemeTreatCategoryAddForm performanceSchemeGoodAddForm : form.TreatProductCategory) {
            if ((performanceSchemeGoodAddForm.PerformancePayRate != null && performanceSchemeGoodAddForm.PerformancePayRate.compareTo(new BigDecimal(100)) > 0)
                    || (performanceSchemeGoodAddForm.PerformanceCardRate != null && performanceSchemeGoodAddForm.PerformanceCardRate.compareTo(new BigDecimal(100)) > 0)
                    || (performanceSchemeGoodAddForm.PerformanceCardLargessRate != null && performanceSchemeGoodAddForm.PerformanceCardLargessRate.compareTo(new BigDecimal(100)) > 0)
                    || (performanceSchemeGoodAddForm.PerformanceLargessRate != null && performanceSchemeGoodAddForm.PerformanceLargessRate.compareTo(new BigDecimal(100)) > 0)
            )
                return false;
        }
        for (PerformanceSchemeTreatCategoryAddForm performanceSchemeGoodAddForm : form.TreatProjectCategory) {
            if ((performanceSchemeGoodAddForm.PerformancePayRate != null && performanceSchemeGoodAddForm.PerformancePayRate.compareTo(new BigDecimal(100)) > 0)
                    || (performanceSchemeGoodAddForm.PerformanceCardRate != null && performanceSchemeGoodAddForm.PerformanceCardRate.compareTo(new BigDecimal(100)) > 0)
                    || (performanceSchemeGoodAddForm.PerformanceCardLargessRate != null && performanceSchemeGoodAddForm.PerformanceCardLargessRate.compareTo(new BigDecimal(100)) > 0)
                    || (performanceSchemeGoodAddForm.PerformanceLargessRate != null && performanceSchemeGoodAddForm.PerformanceLargessRate.compareTo(new BigDecimal(100)) > 0)
            )
                return false;
        }
        return true;
    }

    @RequestMapping(value = "/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody PerformanceSchemeQueryForm form) {
        return performanceSchemeService.all(form);
    }

    @RequestMapping(value = "/valid", method = {RequestMethod.POST})
    public BaseOutput valid() {
        return performanceSchemeService.valid();
    }

    @RequestMapping(value = "/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody PerformanceSchemeAddForm form) {
        return performanceSchemeService.create(form);
    }

    @RequestMapping(value = "/detail", method = {RequestMethod.POST})
    public BaseOutput detail(@RequestBody PerformanceSchemeQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("业绩方案不能为空");
        return performanceSchemeService.detail(form);
    }

    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody PerformanceSchemeUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("业绩方案不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return performanceSchemeService.update(form);
    }
}