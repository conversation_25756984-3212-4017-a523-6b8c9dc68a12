package com.zhelian.api.khs.treatSavingCardPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCategoryHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.service.khs.treatSavingCardPerformance.TreatSavingCardCategoryHandlerPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class TreatSavingCardCategoryHandlerPerformanceController {

    @Autowired
    TreatSavingCardCategoryHandlerPerformanceService treatSavingCardCategoryHandlerPerformanceService;

    static boolean checkRate(TreatSavingCardProjectCategoryHandlerCommissionAddForm form) {

        for (TreatSavingCardProjectCategoryHandlerAddForm handlerAddForm : form.Handler) {

            if ((handlerAddForm.Rate != null && handlerAddForm.Rate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerAddForm.LargessRate != null && handlerAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0))
                return false;

            for (TreatSavingCardProjectCategoryHandlerJobTypeCommissionAddForm jobTypePerformanceAddForm : handlerAddForm.HandlerJobTypeCommission) {

                if ((jobTypePerformanceAddForm.Rate != null && jobTypePerformanceAddForm.Rate.compareTo(new BigDecimal(100)) > 0)
                        || (jobTypePerformanceAddForm.LargessRate != null && jobTypePerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0))
                    return false;
            }
        }
        return true;
    }

    @RequestMapping(value = "/treatSavingCardCategoryHandlerPerformance/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody TreatSavingCardProjectCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return treatSavingCardCategoryHandlerPerformanceService.allTreatSavingCardCategoryHandlerPerformance(form);
    }

    @RequestMapping(value = "/treatSavingCardCategoryHandlerPerformance/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody TreatSavingCardProjectCategoryHandlerCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatSavingCardCategoryHandlerPerformanceService.updateTreatSavingCardCategoryHandlerPerformance(form);
    }
}

