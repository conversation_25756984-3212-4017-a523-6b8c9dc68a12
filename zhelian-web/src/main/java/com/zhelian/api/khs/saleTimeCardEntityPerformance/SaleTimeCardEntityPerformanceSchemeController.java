package com.zhelian.api.khs.saleTimeCardEntityPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCommissionSchemeDeleteForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardCommissionSchemeQueryForm;
import com.zhelian.service.khs.saleTimeCardEntityPerformance.SaleTimeCardEntityPerformanceSchemeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class SaleTimeCardEntityPerformanceSchemeController {

    @Autowired
    SaleTimeCardEntityPerformanceSchemeService saleTimeCardEntityPerformanceSchemeService;

    @RequestMapping(value = "/saleTimeCardEntityPerformanceScheme/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody SaleTimeCardCommissionSchemeQueryForm form) {
        return saleTimeCardEntityPerformanceSchemeService.saleTimeCardEntityPerformanceSchemeList(form);
    }

    @RequestMapping(value = "/saleTimeCardEntityPerformanceScheme/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody SaleTimeCardCommissionSchemeAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return saleTimeCardEntityPerformanceSchemeService.createSaleTimeCardEntityPerformanceScheme(form);
    }

    @RequestMapping(value = "/saleTimeCardEntityPerformanceScheme/delete", method = {RequestMethod.POST})
    public BaseOutput delete(@RequestBody SaleTimeCardCommissionSchemeDeleteForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return saleTimeCardEntityPerformanceSchemeService.deleteSaleTimeCardEntityPerformanceScheme(form);
    }
}

