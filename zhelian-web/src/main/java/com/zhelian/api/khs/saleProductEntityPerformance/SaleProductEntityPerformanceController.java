package com.zhelian.api.khs.saleProductEntityPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryHandlerCommissionQueryForm;
import com.zhelian.model.khs.form.input.SaleGoodCommissionAddForm;
import com.zhelian.model.khs.form.input.SaleGoodsPerformanceAddForm;
import com.zhelian.service.khs.saleProductEntityPerformance.SaleProductEntityPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class SaleProductEntityPerformanceController {

    @Autowired
    SaleProductEntityPerformanceService saleProductEntityPerformanceService;

    static boolean checkRate(SaleGoodsPerformanceAddForm form) {

        for (SaleGoodCommissionAddForm saleProductEntityPerformanceAddForm : form.Good) {

            if ((saleProductEntityPerformanceAddForm.PayRate != null && saleProductEntityPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (saleProductEntityPerformanceAddForm.SavingCardRate != null && saleProductEntityPerformanceAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                    || (saleProductEntityPerformanceAddForm.SavingCardLargessRate != null && saleProductEntityPerformanceAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                return false;
        }
        return true;
    }

    @RequestMapping(value = "/saleProductEntityPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allSaleProductEntityPerformance(@RequestBody SaleProductCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return saleProductEntityPerformanceService.allSaleProductEntityPerformance(form);
    }

    @RequestMapping(value = "/saleProductEntityPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleProductEntityPerformance(@RequestBody SaleGoodsPerformanceAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return saleProductEntityPerformanceService.updateSaleProductEntityPerformance(form);
    }
}

