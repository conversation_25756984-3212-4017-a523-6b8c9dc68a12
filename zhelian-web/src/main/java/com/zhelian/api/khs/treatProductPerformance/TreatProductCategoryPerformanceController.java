package com.zhelian.api.khs.treatProductPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatProductChildCategoryCommissionAddForm;
import com.zhelian.service.khs.treatProductPerformance.TreatProductCategoryPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class TreatProductCategoryPerformanceController {

    @Autowired
    TreatProductCategoryPerformanceService treatProductCategoryPerformanceService;

    static boolean checkRate(TreatProductCategoryAddForm form) {

        for (TreatProductCategoryCommissionAddForm treatProjectCategoryPerformanceAddForm : form.Category) {
            if ((treatProjectCategoryPerformanceAddForm.PayRate != null && treatProjectCategoryPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (treatProjectCategoryPerformanceAddForm.CardRate != null && treatProjectCategoryPerformanceAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                    || (treatProjectCategoryPerformanceAddForm.CardLargessRate != null && treatProjectCategoryPerformanceAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                    || (treatProjectCategoryPerformanceAddForm.LargessRate != null && treatProjectCategoryPerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
            )
                return false;

            for (TreatProductChildCategoryCommissionAddForm treatProjectChildCategoryPerformanceAddForm : treatProjectCategoryPerformanceAddForm.Child) {

                if ((treatProjectChildCategoryPerformanceAddForm.PayRate != null && treatProjectChildCategoryPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (treatProjectChildCategoryPerformanceAddForm.CardRate != null && treatProjectChildCategoryPerformanceAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                        || (treatProjectChildCategoryPerformanceAddForm.CardLargessRate != null && treatProjectChildCategoryPerformanceAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                        || (treatProjectChildCategoryPerformanceAddForm.LargessRate != null && treatProjectChildCategoryPerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
                )
                    return false;
            }
        }
        return true;
    }

    @RequestMapping(value = "/treatProductCategoryPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allSaleProductCategoryPerformance(@RequestBody TreatProductCategoryCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return treatProductCategoryPerformanceService.allTreatProductCategoryPerformance(form);
    }

    @RequestMapping(value = "/treatProductCategoryPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleProductCategoryPerformance(@RequestBody TreatProductCategoryAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatProductCategoryPerformanceService.updateTreatProductCategoryPerformance(form);
    }
}

