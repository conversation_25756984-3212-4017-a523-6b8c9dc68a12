package com.zhelian.api.khs.saleProjectPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectHandlersCommissionAddForm;
import com.zhelian.service.khs.saleProjectPerformance.SaleProjectHandlerPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class SaleProjectHandlerPerformanceController {

    @Autowired
    SaleProjectHandlerPerformanceService saleProjectHandlerPerformanceService;

    static boolean checkRate(SaleProjectHandlersCommissionAddForm form) {
        if (form.ProjectHandler != null) {
            for (SaleProjectHandlerCommissionAddForm handlerPerformanceAddForm : form.ProjectHandler) {

                if ((handlerPerformanceAddForm.PayRate != null && handlerPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerPerformanceAddForm.SavingCardRate != null && handlerPerformanceAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerPerformanceAddForm.SavingCardLargessRate != null && handlerPerformanceAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                    return false;

                for (SaleProjectCategoryHandlerJobTypeCommissionAddForm jobTypePerformanceAddForm : handlerPerformanceAddForm.HandlerJobTypeCommission) {


                    if ((jobTypePerformanceAddForm.PayRate != null && jobTypePerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.SavingCardRate != null && jobTypePerformanceAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.SavingCardLargessRate != null && jobTypePerformanceAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                        return false;
                }

            }
        }
        return true;
    }

    @RequestMapping(value = "/saleProjectHandlerPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allSaleProjectHandlerPerformance(@RequestBody SaleProjectHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.ProjectID == null)
            return BaseOutput.failed("项目编号不能为空");
        return saleProjectHandlerPerformanceService.allSaleProjectHandlerPerformance(form);
    }

    @RequestMapping(value = "/saleProjectHandlerPerformance/packageCard", method = {RequestMethod.POST})
    public BaseOutput allSalePackageCardProjectHandlerPerformance(@RequestBody SaleProjectHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.ProjectID == null)
            return BaseOutput.failed("项目编号不能为空");
        return saleProjectHandlerPerformanceService.allSalePackageCardProjectHandlerPerformance(form);
    }

    @RequestMapping(value = "/saleProjectHandlerPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleProjectHandlerPerformance(@RequestBody SaleProjectHandlersCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.ProjectID == null)
            return BaseOutput.failed("项目编号不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return saleProjectHandlerPerformanceService.updateSaleProjectHandlerPerformance(form);
    }
}

