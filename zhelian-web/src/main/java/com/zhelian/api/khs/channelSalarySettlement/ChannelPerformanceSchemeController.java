package com.zhelian.api.khs.channelSalarySettlement;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.khs.form.input.ChannelPerformanceSchemeUpdateForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeAddForm;
import com.zhelian.model.khs.form.input.PerformanceSchemeQueryForm;
import com.zhelian.service.khs.channelSalarySettlement.ChannelPerformanceSchemeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api/channelPerformanceScheme")
public class ChannelPerformanceSchemeController {

    @Autowired
    ChannelPerformanceSchemeService channelPerformanceSchemeService;


    @RequestMapping(value = "/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody PerformanceSchemeQueryForm form) {
        return channelPerformanceSchemeService.all(form);
    }

    @RequestMapping(value = "/valid", method = {RequestMethod.POST})
    public BaseOutput valid() {
        return channelPerformanceSchemeService.valid();
    }

    @RequestMapping(value = "/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody PerformanceSchemeAddForm form) {
        return channelPerformanceSchemeService.create(form);
    }

    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody ChannelPerformanceSchemeUpdateForm form) {
        return channelPerformanceSchemeService.update(form);
    }

    @RequestMapping(value = "/detail", method = {RequestMethod.POST})
    public BaseOutput detail(@RequestBody PerformanceSchemeQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("业绩方案不能为空");
        return channelPerformanceSchemeService.detail(form);
    }

}