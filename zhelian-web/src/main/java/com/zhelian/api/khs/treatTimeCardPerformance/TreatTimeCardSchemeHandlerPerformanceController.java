package com.zhelian.api.khs.treatTimeCardPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatCardSchemeHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardSchemeHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardsSchemeHandlerCommissionAddForm;
import com.zhelian.service.khs.treatTimeCardPerformance.TreatTimeCardSchemeHandlerPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class TreatTimeCardSchemeHandlerPerformanceController {

    @Autowired
    TreatTimeCardSchemeHandlerPerformanceService saleTimeCardSchemeHandlerPerformanceService;

    static boolean checkRate(TreatCardsSchemeHandlerCommissionAddForm form) {
        for (TreatCardSchemeHandlerCommissionAddForm handlerPerformanceAddForm : form.CardHandler) {

            if ((handlerPerformanceAddForm.PayRate != null && handlerPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerPerformanceAddForm.CardRate != null && handlerPerformanceAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerPerformanceAddForm.CardLargessRate != null && handlerPerformanceAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerPerformanceAddForm.LargessRate != null && handlerPerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
            )
                return false;
            for (TreatCardSchemeHandlerJobTypeCommissionAddForm jobTypePerformanceAddForm : handlerPerformanceAddForm.HandlerJobTypeCommission) {
                if ((jobTypePerformanceAddForm.PayRate != null && jobTypePerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (jobTypePerformanceAddForm.CardRate != null && jobTypePerformanceAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                        || (jobTypePerformanceAddForm.CardLargessRate != null && jobTypePerformanceAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                        || (jobTypePerformanceAddForm.LargessRate != null && jobTypePerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
                )
                    return false;
            }
        }
        return true;
    }

    @RequestMapping(value = "/treatTimeCardSchemeHandlerPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allTreatProSchemeHandlerPerformance(@RequestBody TreatCardCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");

        return saleTimeCardSchemeHandlerPerformanceService.allTreatProSchemeHandlerPerformance(form);
    }

    @RequestMapping(value = "/treatTimeCardSchemeHandlerPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateTreatTimeCardSchemeHandlerPerformance(@RequestBody TreatCardsSchemeHandlerCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return saleTimeCardSchemeHandlerPerformanceService.updateTreatTimeCardSchemeHandlerPerformance(form);
    }
}

