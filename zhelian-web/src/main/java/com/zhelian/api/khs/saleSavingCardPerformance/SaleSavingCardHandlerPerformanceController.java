package com.zhelian.api.khs.saleSavingCardPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardHandlersCommissionAddForm;
import com.zhelian.service.khs.saleSavingCardPerformance.SaleSavingCardHandlerPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class SaleSavingCardHandlerPerformanceController {

    @Autowired
    SaleSavingCardHandlerPerformanceService saleSavingCardHandlerPerformanceService;

    static boolean checkRate(SaleSavingCardHandlersCommissionAddForm form) {

        if (form.SavingCardHandler != null) {
            for (SaleSavingCardHandlerCommissionAddForm handlerPerformanceAddForm : form.SavingCardHandler) {

                if (handlerPerformanceAddForm.PayRate != null && handlerPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    return false;

                for (SaleSavingCardCategoryHandlerJobTypeCommissionAddForm jobTypePerformanceAddForm : handlerPerformanceAddForm.HandlerJobTypeCommission) {


                    if (jobTypePerformanceAddForm.PayRate != null && jobTypePerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        return false;
                }

            }
        }
        return true;
    }

    @RequestMapping(value = "/saleSavingCardHandlerPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allSaleSavingCardHandlerPerformance(@RequestBody SaleSavingCardHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.SavingCardID == null)
            return BaseOutput.failed("储值卡编号不能为空");
        return saleSavingCardHandlerPerformanceService.allSaleSavingCardHandlerPerformance(form);
    }

    @RequestMapping(value = "/saleSavingCardHandlerPerformance/packageCard", method = {RequestMethod.POST})
    public BaseOutput allSalePackageCardSavingCardHandlerPerformance(@RequestBody SaleSavingCardHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.SavingCardID == null)
            return BaseOutput.failed("储值卡编号不能为空");
        return saleSavingCardHandlerPerformanceService.allSalePackageCardSavingCardHandlerPerformance(form);
    }

    @RequestMapping(value = "/saleSavingCardHandlerPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleSavingCardHandlerPerformance(@RequestBody SaleSavingCardHandlersCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.SavingCardID == null)
            return BaseOutput.failed("产品编号不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return saleSavingCardHandlerPerformanceService.updateSaleSavingCardHandlerPerformance(form);
    }
}

