package com.zhelian.api.khs.channelSalarySettlement;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.khs.form.input.ChannelCommissionAddForm;
import com.zhelian.model.khs.form.input.ChannelCommissionSchemeAddForm;
import com.zhelian.model.khs.form.input.ChannelCommissionSchemeQueryForm;
import com.zhelian.model.khs.form.input.EmployeeCommissionQueryForm;
import com.zhelian.service.khs.channelSalarySettlement.ChannelCommissionSchemeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api/channelCommissionScheme")
public class ChannelCommissionSchemeController {

    @Autowired
    ChannelCommissionSchemeService channelCommissionSchemeService;


    @RequestMapping(value = "/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody ChannelCommissionSchemeQueryForm form) {
        return channelCommissionSchemeService.all(form);
    }

    @RequestMapping(value = "/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody ChannelCommissionSchemeAddForm form) {
        if (form.PerformanceSchemeID == null)
            return BaseOutput.failed("业绩方案编号不能为空");
        if (form.Calculation == null)
            return BaseOutput.failed("取值方式不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return channelCommissionSchemeService.create(form);
    }

    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody ChannelCommissionSchemeAddForm form) {
        if (form.ID == null)
            return BaseOutput.failed("提成方案编号不能为空");
        if (form.PerformanceSchemeID == null)
            return BaseOutput.failed("业绩方案编号不能为空");
        if (form.Calculation == null)
            return BaseOutput.failed("取值方式不能为空");
        if (form.Active == null)
            return BaseOutput.failed("有效性不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return channelCommissionSchemeService.update(form);
    }

    @RequestMapping(value = "/commission", method = {RequestMethod.POST})
    public BaseOutput detail(@RequestBody EmployeeCommissionQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("提成方案编号不能为空");
        return channelCommissionSchemeService.commission(form);
    }

    @RequestMapping(value = "/range", method = {RequestMethod.POST})
    public BaseOutput range(@RequestBody EmployeeCommissionQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("提成方案编号不能为空");
        return channelCommissionSchemeService.range(form);
    }


    static boolean checkRate(ChannelCommissionSchemeAddForm form) {
        for (ChannelCommissionAddForm employeeCommissionAddForm : form.Commission) {
            if ((employeeCommissionAddForm.Rate != null && employeeCommissionAddForm.Rate.compareTo(new BigDecimal(100)) > 0))
                return false;
        }
        return true;
    }
}