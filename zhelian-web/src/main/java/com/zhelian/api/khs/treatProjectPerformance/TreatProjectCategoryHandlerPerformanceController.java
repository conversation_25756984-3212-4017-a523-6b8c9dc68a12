package com.zhelian.api.khs.treatProjectPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatProjectCategoryHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.service.khs.treatProjectPerformance.TreatProjectCategoryHandlerPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class TreatProjectCategoryHandlerPerformanceController {

    @Autowired
    TreatProjectCategoryHandlerPerformanceService treatProjectCategoryHandlerPerformanceService;

    static boolean checkRate(TreatProjectCategoryHandlerCommissionAddForm form) {
        if (form.Handler != null) {
            for (TreatProjectCategoryHandlerAddForm handlerAddForm : form.Handler) {

                if ((handlerAddForm.PayRate != null && handlerAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerAddForm.CardRate != null && handlerAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerAddForm.CardLargessRate != null && handlerAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerAddForm.LargessRate != null && handlerAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
                )
                    return false;

                for (TreatProjectCategoryHandlerJobTypeCommissionAddForm jobTypePerformanceAddForm : handlerAddForm.HandlerJobTypeCommission) {

                    if ((jobTypePerformanceAddForm.PayRate != null && jobTypePerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.CardRate != null && jobTypePerformanceAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.CardLargessRate != null && jobTypePerformanceAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.LargessRate != null && jobTypePerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)

                    )
                        return false;
                }
            }
        }
        return true;
    }

    @RequestMapping(value = "/treatProjectCategoryHandlerPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allTreatProjectCategoryHandlerPerformance(@RequestBody TreatProjectCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return treatProjectCategoryHandlerPerformanceService.allTreatProjectCategoryHandlerPerformance(form);
    }

    @RequestMapping(value = "/treatProjectCategoryHandlerPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateTreatProjectCategoryHandlerPerformance(@RequestBody TreatProjectCategoryHandlerCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatProjectCategoryHandlerPerformanceService.updateTreatProjectCategoryHandlerPerformance(form);
    }
}

