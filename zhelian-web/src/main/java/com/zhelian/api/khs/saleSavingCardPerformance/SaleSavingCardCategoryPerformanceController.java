package com.zhelian.api.khs.saleSavingCardPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardCommissionSchemeAddForm;
import com.zhelian.service.khs.saleSavingCardPerformance.SaleSavingCardCategoryPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

@RestController
@RequestMapping(value = "/api")
public class SaleSavingCardCategoryPerformanceController {

    @Autowired
    SaleSavingCardCategoryPerformanceService saleSavingCardCategoryPerformanceService;

    static boolean checkRate(SaleSavingCardCategoryAddForm form) {

        for (SaleSavingCardCategoryCommissionAddForm saleSavingCardCategoryPerformanceAddForm : form.Category) {

            if (saleSavingCardCategoryPerformanceAddForm.PayRate != null && saleSavingCardCategoryPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                return false;
        }
        return true;
    }

    @RequestMapping(value = "/saleSavingCardCategoryPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allSaleSavingCardCategoryPerformance(@RequestBody SaleSavingCardCommissionSchemeAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return saleSavingCardCategoryPerformanceService.allSaleSavingCardCategoryPerformance(form);
    }

    @RequestMapping(value = "/saleSavingCardCategoryPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleSavingCardCategoryPerformance(@RequestBody SaleSavingCardCategoryAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return saleSavingCardCategoryPerformanceService.updateSaleSavingCardCategoryPerformance(form);
    }
}

