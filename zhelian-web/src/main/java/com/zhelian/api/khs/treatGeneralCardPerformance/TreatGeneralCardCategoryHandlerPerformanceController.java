package com.zhelian.api.khs.treatGeneralCardPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatGeneralCardCategoryHandlerAddForm;
import com.zhelian.service.khs.treatGeneralCardPerformance.TreatGeneralCardCategoryHandlerPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class TreatGeneralCardCategoryHandlerPerformanceController {

    @Autowired
    TreatGeneralCardCategoryHandlerPerformanceService treatGeneralCardCategoryHandlerPerformanceService;

    static boolean checkRate(TreatCardCategoryHandlerCommissionAddForm form) {
        for (TreatGeneralCardCategoryHandlerAddForm handlerPerformanceAddForm : form.CardHandler) {
            if ((handlerPerformanceAddForm.PayRate != null && handlerPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerPerformanceAddForm.CardRate != null && handlerPerformanceAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerPerformanceAddForm.CardLargessRate != null && handlerPerformanceAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerPerformanceAddForm.LargessRate != null && handlerPerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
            )
                return false;

            for (TreatCardCategoryHandlerJobTypeCommissionAddForm typePerformanceAddForm : handlerPerformanceAddForm.HandlerJobTypeCommission) {
                if ((typePerformanceAddForm.PayRate != null && typePerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (typePerformanceAddForm.CardRate != null && typePerformanceAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                        || (typePerformanceAddForm.CardLargessRate != null && typePerformanceAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                        || (typePerformanceAddForm.LargessRate != null && typePerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
                )
                    return false;
            }
        }
        return true;
    }

    @RequestMapping(value = "/treatGeneralCardCategoryHandlerPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allTreatGeneralCardCategoryHandlerPerformance(@RequestBody TreatCardCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return treatGeneralCardCategoryHandlerPerformanceService.allTreatGeneralCardCategoryHandlerPerformance(form);
    }

    @RequestMapping(value = "/treatGeneralCardCategoryHandlerPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateTreatGeneralCardCategoryHandlerPerformance(@RequestBody TreatCardCategoryHandlerCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatGeneralCardCategoryHandlerPerformanceService.updateTreatGeneralCardCategoryHandlerPerformance(form);
    }
}

