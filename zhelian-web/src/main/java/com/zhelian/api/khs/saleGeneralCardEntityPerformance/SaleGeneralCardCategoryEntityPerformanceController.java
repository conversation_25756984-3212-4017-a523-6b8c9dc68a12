package com.zhelian.api.khs.saleGeneralCardEntityPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardCommissionSchemeAddForm;
import com.zhelian.model.khs.form.input.SaleGoodCategoryAddForm;
import com.zhelian.model.khs.form.input.SaleGoodCategoryPerformanceAddForm;
import com.zhelian.service.khs.saleGeneralCardEntityPerformance.SaleGeneralCardCategoryEntityPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class SaleGeneralCardCategoryEntityPerformanceController {

    @Autowired
    SaleGeneralCardCategoryEntityPerformanceService saleGeneralCardCategoryEntityPerformanceService;

    static boolean checkRate(SaleGoodCategoryAddForm form) {

        for (SaleGoodCategoryPerformanceAddForm saleGeneralCardCategoryPerformanceAddForm : form.Category) {

            if ((saleGeneralCardCategoryPerformanceAddForm.PayRate != null && saleGeneralCardCategoryPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (saleGeneralCardCategoryPerformanceAddForm.SavingCardRate != null && saleGeneralCardCategoryPerformanceAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                    || (saleGeneralCardCategoryPerformanceAddForm.SavingCardLargessRate != null && saleGeneralCardCategoryPerformanceAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                return false;
        }
        return true;
    }

    @RequestMapping(value = "/saleGeneralCardCategoryEntityPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allSaleGeneralCardCategoryEntityPerformance(@RequestBody SaleGeneralCardCommissionSchemeAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return saleGeneralCardCategoryEntityPerformanceService.allSaleGeneralCardCategoryEntityPerformance(form);
    }

    @RequestMapping(value = "/saleGeneralCardCategoryEntityPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleGeneralCardCategoryEntityPerformance(@RequestBody SaleGoodCategoryAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return saleGeneralCardCategoryEntityPerformanceService.updateSaleGeneralCardCategoryEntityPerformance(form);
    }
}

