package com.zhelian.api.khs.saleProductPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductsCommissionAddForm;
import com.zhelian.service.khs.saleProductPerformance.SaleProductPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class SaleProductPerformanceController {

    @Autowired
    SaleProductPerformanceService saleProductPerformanceService;

    static boolean checkRate(SaleProductsCommissionAddForm form) {

        for (SaleProductCommissionAddForm saleProductPerformanceAddForm : form.Product) {

            if ((saleProductPerformanceAddForm.PayRate != null && saleProductPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (saleProductPerformanceAddForm.SavingCardRate != null && saleProductPerformanceAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                    || (saleProductPerformanceAddForm.SavingCardLargessRate != null && saleProductPerformanceAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                return false;
        }
        return true;
    }

    @RequestMapping(value = "/saleProductPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allSaleProductPerformance(@RequestBody SaleProductCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return saleProductPerformanceService.allSaleProductPerformance(form);
    }

    @RequestMapping(value = "/saleProductPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleProductPerformance(@RequestBody SaleProductsCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return saleProductPerformanceService.updateSaleProductPerformance(form);
    }
}

