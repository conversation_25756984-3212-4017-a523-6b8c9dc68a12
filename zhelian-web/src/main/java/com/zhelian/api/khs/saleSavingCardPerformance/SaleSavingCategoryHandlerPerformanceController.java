package com.zhelian.api.khs.saleSavingCardPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardCategoryHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleSavingCardCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.service.khs.saleSavingCardPerformance.SaleSavingCardCategoryHandlerPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

@RestController
@RequestMapping(value = "/api")
public class SaleSavingCategoryHandlerPerformanceController {

    @Autowired
    SaleSavingCardCategoryHandlerPerformanceService saleSavingCardCategoryHandlerPerformanceService;

    static boolean checkRate(SaleSavingCardCategoryHandlerCommissionAddForm form) {
        if (form.SavingCardHandler != null) {
            for (SaleSavingCardCategoryHandlerAddForm handlerAddForm : form.SavingCardHandler) {

                if (handlerAddForm.PayRate != null && handlerAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    return false;

                for (SaleSavingCardCategoryHandlerJobTypeCommissionAddForm jobTypePerformanceAddForm : handlerAddForm.HandlerJobTypeCommission) {

                    if (jobTypePerformanceAddForm.PayRate != null && jobTypePerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        return false;
                }
            }
        }
        return true;
    }

    @RequestMapping(value = "/saleSavingCardCategoryHandlerPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allSaleSavingCardCategoryHandlerPerformance(@RequestBody SaleSavingCardCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return saleSavingCardCategoryHandlerPerformanceService.allSaleSavingCardCategoryHandlerPerformance(form);
    }

    @RequestMapping(value = "/saleSavingCardCategoryHandlerPerformance/packageCard", method = {RequestMethod.POST})
    public BaseOutput allSalePackageCardSavingCardCategoryHandlerPerformance(@RequestBody SaleSavingCardCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return saleSavingCardCategoryHandlerPerformanceService.allSalePackageCardSavingCardCategoryHandlerPerformance(form);
    }

    @RequestMapping(value = "/saleSavingCardCategoryHandlerPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleSavingCardCategoryHandlerPerformance(@RequestBody SaleSavingCardCategoryHandlerCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return saleSavingCardCategoryHandlerPerformanceService.updateSaleSavingCardCategoryHandlerPerformance(form);
    }
}

