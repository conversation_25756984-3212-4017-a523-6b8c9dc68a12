package com.zhelian.api.khs.treatSavingCardEntityPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatProductCommissionQueryForm;
import com.zhelian.model.khs.form.input.TreatGoodPerformanceAddForm;
import com.zhelian.model.khs.form.input.TreatGoodsPerformanceAddForm;
import com.zhelian.service.khs.treatSavingCardEntityPerformance.TreatSavingCardEntityPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class TreatSavingCardEntityPerformanceController {

    @Autowired
    TreatSavingCardEntityPerformanceService treatSavingCardEntityPerformanceService;

    static boolean checkRate(TreatGoodsPerformanceAddForm form) {

        for (TreatGoodPerformanceAddForm treatSavingCardEntityPerformanceAddForm : form.Good) {

            if ((treatSavingCardEntityPerformanceAddForm.PayRate != null && treatSavingCardEntityPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (treatSavingCardEntityPerformanceAddForm.CardRate != null && treatSavingCardEntityPerformanceAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                    || (treatSavingCardEntityPerformanceAddForm.CardLargessRate != null && treatSavingCardEntityPerformanceAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                    || (treatSavingCardEntityPerformanceAddForm.LargessRate != null && treatSavingCardEntityPerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
            )
                return false;
        }
        return true;
    }

    @RequestMapping(value = "/treatSavingCardEntityPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allTreatSavingCardEntityPerformance(@RequestBody TreatProductCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return treatSavingCardEntityPerformanceService.allTreatSavingCardEntityPerformance(form);
    }

    @RequestMapping(value = "/treatSavingCardEntityPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateTreatSavingCardEntityPerformance(@RequestBody TreatGoodsPerformanceAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatSavingCardEntityPerformanceService.updateTreatSavingCardEntityPerformance(form);
    }
}

