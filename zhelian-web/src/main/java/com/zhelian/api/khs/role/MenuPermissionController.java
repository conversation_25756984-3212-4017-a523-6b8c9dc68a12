package com.zhelian.api.khs.role;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.khs.form.input.MenuPermissionQueryForm;
import com.zhelian.service.khs.role.MenuPermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
public class MenuPermissionController {
    @Autowired
    MenuPermissionService menuPermissionService;

    @RequestMapping(value = "/menuPermission/list", method = {RequestMethod.POST})
    public BaseOutput getMenuPermissionlist(@RequestBody MenuPermissionQueryForm menuPermissionQueryForm) {
        if (!StringUtils.hasText(menuPermissionQueryForm.MenuPermissionType)) {
            return BaseOutput.failed("菜单类型不能为空");
        }
        return menuPermissionService.getMenuPermissionlist(menuPermissionQueryForm);
    }
}
