package com.zhelian.api.khs.saleProductPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.service.khs.saleProductPerformance.SaleProductCategoryHandlerPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class SaleProductCategoryHandlerPerformanceController {

    @Autowired
    SaleProductCategoryHandlerPerformanceService saleProductCategoryHandlerPerformanceService;

    static boolean checkRate(SaleProductCategoryHandlerCommissionAddForm form) {
        if (form.ProductHandler != null) {
            for (SaleProductCategoryHandlerAddForm handlerAddForm : form.ProductHandler) {

                if ((handlerAddForm.PayRate != null && handlerAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerAddForm.SavingCardRate != null && handlerAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerAddForm.SavingCardLargessRate != null && handlerAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                    return false;

                for (SaleProductCategoryHandlerJobTypeCommissionAddForm jobTypeCommissionAddForm : handlerAddForm.HandlerJobTypeCommission) {

                    if ((jobTypeCommissionAddForm.PayRate != null && jobTypeCommissionAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypeCommissionAddForm.SavingCardRate != null && jobTypeCommissionAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypeCommissionAddForm.SavingCardLargessRate != null && jobTypeCommissionAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                        return false;
                }
            }
        }
        return true;
    }

    @RequestMapping(value = "/saleProductCategoryHandlerPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allSaleProductCategoryHandlerPerformance(@RequestBody SaleProductCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return saleProductCategoryHandlerPerformanceService.allSaleProductCategoryHandlerPerformance(form);
    }

    @RequestMapping(value = "/saleProductCategoryHandlerPerformance/packageCard", method = {RequestMethod.POST})
    public BaseOutput allSalePackageCardProductCategoryHandlerPerformance(@RequestBody SaleProductCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return saleProductCategoryHandlerPerformanceService.allSalePackageCardProductCategoryHandlerPerformance(form);
    }

    @RequestMapping(value = "/saleProductCategoryHandlerPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleProductCategoryHandlerPerformance(@RequestBody SaleProductCategoryHandlerCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return saleProductCategoryHandlerPerformanceService.updateSaleProductCategoryHandlerPerformance(form);
    }

}

