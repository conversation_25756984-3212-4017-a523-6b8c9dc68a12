package com.zhelian.api.khs.treatSavingCardPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardHandlersCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectHandlerCommissionAddForm;
import com.zhelian.service.khs.treatSavingCardPerformance.TreatSavingCardHandlerPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

@RestController
@RequestMapping(value = "/api")
public class TreatSavingCardHandlerPerformanceController {

    @Autowired
    TreatSavingCardHandlerPerformanceService treatSavingCardHandlerPerformanceService;

    static boolean checkRate(TreatSavingCardHandlersCommissionAddForm form) {

        for (TreatSavingCardProjectHandlerCommissionAddForm handlerPerformanceAddForm : form.Handler) {

            if ((handlerPerformanceAddForm.Rate != null && handlerPerformanceAddForm.Rate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerPerformanceAddForm.LargessRate != null && handlerPerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0))
                return false;

            for (TreatSavingCardProjectCategoryHandlerJobTypeCommissionAddForm jobTypePerformanceAddForm : handlerPerformanceAddForm.HandlerJobTypeCommission) {

                if ((jobTypePerformanceAddForm.Rate != null && jobTypePerformanceAddForm.Rate.compareTo(new BigDecimal(100)) > 0)
                        || (jobTypePerformanceAddForm.LargessRate != null && jobTypePerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0))
                    return false;
            }

        }
        return true;
    }

    @RequestMapping(value = "/treatSavingCardHandlerPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allTreatSavingCardProjectHandlerPerformance(@RequestBody TreatSavingCardHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.SavingCardID == null)
            return BaseOutput.failed("储值卡编号不能为空");
        return treatSavingCardHandlerPerformanceService.allTreatSavingCardHandlerPerformance(form);
    }

    @RequestMapping(value = "/treatSavingCardHandlerPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateTreatSavingCardProjectHandlerPerformance(@RequestBody TreatSavingCardHandlersCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.SavingCardID == null)
            return BaseOutput.failed("储值卡编号不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatSavingCardHandlerPerformanceService.updateTreatSavingCardHandlerPerformance(form);
    }
}

