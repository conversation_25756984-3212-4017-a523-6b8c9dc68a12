package com.zhelian.api.khs.salarySettlement;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.khs.form.input.SalarySheetCommissionQueryForm;
import com.zhelian.model.khs.form.input.SalarySheetDetailQueryForm;
import com.zhelian.model.khs.form.input.SalarySheetQueryForm;
import com.zhelian.model.khs.form.input.SettlementIntervalAddForm;
import com.zhelian.service.khs.salarySettlement.SalarySheetCommonService;
import com.zhelian.service.khs.salarySettlement.SalarySheetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;


@RestController
@RequestMapping(value = "/api/salarySheet")
public class SalarySheetController {

    @Autowired
    SalarySheetService salarySheetService;
    @Autowired
    SalarySheetCommonService salarySheetCommonService;

    @RequestMapping(value = "/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody SalarySheetQueryForm form) {
        return salarySheetService.all(form);
    }

    @RequestMapping(value = "/settlement", method = {RequestMethod.POST})
    public BaseOutput settlement(@RequestBody SalarySheetQueryForm form) {
        if (form.SalarySettlementIntervalID == null)
            return BaseOutput.failed("结算周期ID不能为空");
        return salarySheetCommonService.settlement(form);
    }

    @RequestMapping(value = "/excel", method = {RequestMethod.POST})
    public BaseOutput easyExcel(@RequestBody SalarySheetQueryForm form, HttpServletResponse response) {
        return salarySheetService.easyExcel(form, response);
    }

    @RequestMapping(value = "/employeeCommission", method = {RequestMethod.POST})
    public BaseOutput employeeCommission(@RequestBody SalarySheetCommissionQueryForm form) {
        if (form.SalarySettlementIntervalID == null)
            return BaseOutput.failed("结算周期ID不能为空");
        if (form.EmployeeID == null)
            return BaseOutput.failed("员工ID不能为空");
        return salarySheetService.employeeCommission(form);
    }

    @RequestMapping(value = "/teamCommission", method = {RequestMethod.POST})
    public BaseOutput teamCommission(@RequestBody SalarySheetCommissionQueryForm form) {
        if (form.SalarySettlementIntervalID == null)
            return BaseOutput.failed("结算周期ID不能为空");
        if (form.EmployeeID == null)
            return BaseOutput.failed("员工ID不能为空");
        return salarySheetService.teamCommission(form);
    }

    @RequestMapping(value = "/entityCommission", method = {RequestMethod.POST})
    public BaseOutput entityCommission(@RequestBody SalarySheetCommissionQueryForm form) {
        if (form.SalarySettlementIntervalID == null)
            return BaseOutput.failed("结算周期ID不能为空");
        if (form.EmployeeID == null)
            return BaseOutput.failed("员工ID不能为空");
        return salarySheetService.entityCommission(form);
    }

    @RequestMapping(value = "/saleCommission", method = {RequestMethod.POST})
    public BaseOutput saleCommission(@RequestBody SalarySheetDetailQueryForm form) {
        if (form.SalarySettlementIntervalID == null)
            return BaseOutput.failed("结算周期ID不能为空");
        if (form.EmployeeID == null)
            return BaseOutput.failed("员工ID不能为空");
        return salarySheetService.saleCommission(form);
    }


    @RequestMapping(value = "/treatCommission", method = {RequestMethod.POST})
    public BaseOutput treatCommission(@RequestBody SalarySheetDetailQueryForm form) {
        if (form.SalarySettlementIntervalID == null)
            return BaseOutput.failed("结算周期ID不能为空");
        if (form.EmployeeID == null)
            return BaseOutput.failed("员工ID不能为空");
        return salarySheetService.treatCommission(form);
    }


    @RequestMapping(value = "/createSettlementInterval", method = {RequestMethod.POST})
    public BaseOutput createSettlementInterval(@RequestBody SettlementIntervalAddForm form) throws Exception {
        if (form.SettlementMonth == null)
            return BaseOutput.failed("结算月份不能为空");
        if (form.StartDate == null)
            return BaseOutput.failed("开始时间不能为空");
        if (form.EndDate == null)
            return BaseOutput.failed("结束时间不能为空");
        return salarySheetService.createSettlementInterval(form);
    }

    @RequestMapping(value = "/allSettlementInterval", method = {RequestMethod.POST})
    public BaseOutput allSettlementInterval() {
        return salarySheetService.allSettlementInterval();
    }
}