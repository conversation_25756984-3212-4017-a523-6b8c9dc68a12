package com.zhelian.api.khs.treatTimeCardPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.TreatCardCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCategoryCommissionQueryForm;
import com.zhelian.service.khs.treatTimeCardPerformance.TreatTimeCardCategoryPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class TreatTimeCardCategoryPerformanceController {

    @Autowired
    TreatTimeCardCategoryPerformanceService treatTimeCardCategoryPerormanceService;

    static boolean checkRate(TreatCardCategoryAddForm form) {
        for (TreatCardCategoryCommissionAddForm handlerPerormanceAddForm : form.Category) {
            if ((handlerPerormanceAddForm.PayRate != null && handlerPerormanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerPerormanceAddForm.CardRate != null && handlerPerormanceAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerPerormanceAddForm.CardLargessRate != null && handlerPerormanceAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                    || (handlerPerormanceAddForm.LargessRate != null && handlerPerormanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
            )
                return false;
        }
        return true;
    }

    @RequestMapping(value = "/treatTimeCardCategoryPerformance/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody TreatSavingCardProjectCategoryCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return treatTimeCardCategoryPerormanceService.alltreatTimeCardCategoryPerformance(form);
    }

    @RequestMapping(value = "/treatTimeCardCategoryPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateTreatTimeCardCategoryPerormance(@RequestBody TreatCardCategoryAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.PayRate != null && form.PayRate.compareTo(new BigDecimal(100)) > 0) {
            return BaseOutput.failed("比例不能大于100");
        }
        if (form.LargessRate != null && form.LargessRate.compareTo(new BigDecimal(100)) > 0) {
            return BaseOutput.failed("比例不能大于100");
        }
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatTimeCardCategoryPerormanceService.updateTreatTimeCardCategoryPerformance(form);
    }
}

