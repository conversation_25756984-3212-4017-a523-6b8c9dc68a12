package com.zhelian.api.khs.saleProductPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleProductCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProductHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProductHandlersCommissionAddForm;
import com.zhelian.service.khs.saleProductPerformance.SaleProductHandlerPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class SaleProductHandlerPerformanceController {

    @Autowired
    SaleProductHandlerPerformanceService saleProductHandlerPerformanceService;

    static boolean checkRate(SaleProductHandlersCommissionAddForm form) {
        if (form.ProductHandler != null) {
            for (SaleProductHandlerCommissionAddForm handlerPerformanceAddForm : form.ProductHandler) {

                if ((handlerPerformanceAddForm.PayRate != null && handlerPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerPerformanceAddForm.SavingCardRate != null && handlerPerformanceAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerPerformanceAddForm.SavingCardLargessRate != null && handlerPerformanceAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                    return false;

                for (SaleProductCategoryHandlerJobTypeCommissionAddForm jobTypePerformanceAddForm : handlerPerformanceAddForm.HandlerJobTypeCommission) {


                    if ((jobTypePerformanceAddForm.PayRate != null && jobTypePerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.SavingCardRate != null && jobTypePerformanceAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.SavingCardLargessRate != null && jobTypePerformanceAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                        return false;
                }

            }
        }
        return true;
    }

    @RequestMapping(value = "/saleProductHandlerPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allSaleProductHandlerPerformance(@RequestBody SaleProductHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.ProductID == null)
            return BaseOutput.failed("产品编号不能为空");
        return saleProductHandlerPerformanceService.allSaleProductHandlerPerformance(form);
    }

    @RequestMapping(value = "/saleProductHandlerPerformance/packageCard", method = {RequestMethod.POST})
    public BaseOutput allSalePackageCardProductHandlerPerformance(@RequestBody SaleProductHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.ProductID == null)
            return BaseOutput.failed("产品编号不能为空");
        return saleProductHandlerPerformanceService.allSalePackageCardProductHandlerPerformance(form);
    }

    @RequestMapping(value = "/saleProductHandlerPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleProductHandlerPerformance(@RequestBody SaleProductHandlersCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.ProductID == null)
            return BaseOutput.failed("产品编号不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return saleProductHandlerPerformanceService.updateSaleProductHandlerPerformance(form);
    }
}

