package com.zhelian.api.khs.treatProductPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryHandlerAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatProductCategoryHandlerJobTypeCommissionAddForm;
import com.zhelian.service.khs.treatProductPerformance.TreatProductCategoryHandlerPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class TreatProductCategoryHandlerPerformanceController {

    @Autowired
    TreatProductCategoryHandlerPerformanceService treatProductCategoryHandlerPerformanceService;

    static boolean checkRate(TreatProductCategoryHandlerCommissionAddForm form) {
        if (form.Handler != null) {
            for (TreatProductCategoryHandlerAddForm handlerAddForm : form.Handler) {

                if ((handlerAddForm.PayRate != null && handlerAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerAddForm.CardRate != null && handlerAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerAddForm.CardLargessRate != null && handlerAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerAddForm.LargessRate != null && handlerAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
                )
                    return false;

                for (TreatProductCategoryHandlerJobTypeCommissionAddForm jobTypePerformanceAddForm : handlerAddForm.HandlerJobTypeCommission) {

                    if ((jobTypePerformanceAddForm.PayRate != null && jobTypePerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.CardRate != null && jobTypePerformanceAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.CardLargessRate != null && jobTypePerformanceAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.LargessRate != null && jobTypePerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
                    )
                        return false;
                }
            }
        }
        return true;
    }

    @RequestMapping(value = "/treatProductCategoryHandlerPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allTreatProductCategoryHandlerPerformance(@RequestBody TreatProductCategoryHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return treatProductCategoryHandlerPerformanceService.allTreatProductCategoryHandlerPerformance(form);
    }

    @RequestMapping(value = "/treatProductCategoryHandlerPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateTreatProductCategoryHandlerPerformance(@RequestBody TreatProductCategoryHandlerCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatProductCategoryHandlerPerformanceService.updateTreatProductCategoryHandlerPerformance(form);
    }
}

