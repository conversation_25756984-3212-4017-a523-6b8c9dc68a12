package com.zhelian.api.khs.treatProjectPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatProjectCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectCategoryCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.TreatProjectChildCategoryCommissionAddForm;
import com.zhelian.service.khs.treatProjectPerformance.TreatProjectCategoryPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class TreatProjectCategoryPerformanceController {

    @Autowired
    TreatProjectCategoryPerformanceService treatProjectCategoryPerformanceService;

    static boolean checkRate(TreatProjectCategoryAddForm form) {

        for (TreatProjectCategoryCommissionAddForm treatProjectCategoryPerformanceAddForm : form.Category) {
            if ((treatProjectCategoryPerformanceAddForm.PayRate != null && treatProjectCategoryPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                    || (treatProjectCategoryPerformanceAddForm.CardRate != null && treatProjectCategoryPerformanceAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                    || (treatProjectCategoryPerformanceAddForm.CardLargessRate != null && treatProjectCategoryPerformanceAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                    || (treatProjectCategoryPerformanceAddForm.LargessRate != null && treatProjectCategoryPerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
            )
                return false;

            for (TreatProjectChildCategoryCommissionAddForm treatProjectChildCategoryPerformanceAddForm : treatProjectCategoryPerformanceAddForm.Child) {

                if ((treatProjectChildCategoryPerformanceAddForm.PayRate != null && treatProjectChildCategoryPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (treatProjectChildCategoryPerformanceAddForm.CardRate != null && treatProjectChildCategoryPerformanceAddForm.CardRate.compareTo(new BigDecimal(100)) > 0)
                        || (treatProjectChildCategoryPerformanceAddForm.CardLargessRate != null && treatProjectChildCategoryPerformanceAddForm.CardLargessRate.compareTo(new BigDecimal(100)) > 0)
                        || (treatProjectChildCategoryPerformanceAddForm.LargessRate != null && treatProjectChildCategoryPerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0)
                )
                    return false;
            }
        }
        return true;
    }

    @RequestMapping(value = "/treatProjectCategoryPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allSaleProjectCategoryPerformance(@RequestBody TreatProjectCategoryCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return treatProjectCategoryPerformanceService.allTreatProjectCategoryPerformance(form);
    }

    @RequestMapping(value = "/treatProjectCategoryPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleProjectCategoryPerformance(@RequestBody TreatProjectCategoryAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatProjectCategoryPerformanceService.updateTreatProjectCategoryPerformance(form);
    }
}

