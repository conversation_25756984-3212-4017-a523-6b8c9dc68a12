package com.zhelian.api.khs.salarySettlement;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.khs.form.input.EmployeeCommissionAddForm;
import com.zhelian.model.khs.form.input.EmployeeCommissionQueryForm;
import com.zhelian.model.khs.form.input.EmployeeCommissionSchemeAddForm;
import com.zhelian.model.khs.form.input.EmployeeCommissionSchemeQueryForm;
import com.zhelian.service.khs.salarySettlement.EmployeeCommissionSchemeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api/employeeCommissionScheme")
public class EmployeeCommissionSchemeController {

    @Autowired
    EmployeeCommissionSchemeService employeeCommissionSchemeService;


    @RequestMapping(value = "/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody EmployeeCommissionSchemeQueryForm form) {
        return employeeCommissionSchemeService.all(form);
    }

    @RequestMapping(value = "/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody EmployeeCommissionSchemeAddForm form) {
        if (form.PerformanceEvaluationSchemeID == null)
            return BaseOutput.failed("业绩考核方案编号不能为空");
        if (form.PerformanceCalculationSchemeID == null)
            return BaseOutput.failed("业绩计算方案编号不能为空");
        if (form.Calculation == null)
            return BaseOutput.failed("取值方式不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return employeeCommissionSchemeService.create(form);
    }

    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody EmployeeCommissionSchemeAddForm form) {
        if (form.PerformanceEvaluationSchemeID == null)
            return BaseOutput.failed("业绩考核方案编号不能为空");
        if (form.PerformanceCalculationSchemeID == null)
            return BaseOutput.failed("业绩计算方案编号不能为空");
        if (form.Calculation == null)
            return BaseOutput.failed("取值方式不能为空");
        if (form.Active == null)
            return BaseOutput.failed("有效性不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return employeeCommissionSchemeService.update(form);
    }

    @RequestMapping(value = "/allHandler", method = {RequestMethod.POST})
    public BaseOutput handler() {
        return employeeCommissionSchemeService.allHandler();
    }

    @RequestMapping(value = "/commission", method = {RequestMethod.POST})
    public BaseOutput detail(@RequestBody EmployeeCommissionQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("提成方案编号不能为空");
        return employeeCommissionSchemeService.commission(form);
    }

    @RequestMapping(value = "/entity", method = {RequestMethod.POST})
    public BaseOutput entity(@RequestBody EmployeeCommissionQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("提成方案编号不能为空");
        return employeeCommissionSchemeService.entity(form);
    }

    @RequestMapping(value = "/handler", method = {RequestMethod.POST})
    public BaseOutput handler(@RequestBody EmployeeCommissionQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("提成方案编号不能为空");
        return employeeCommissionSchemeService.handler(form);
    }

    static boolean checkRate(EmployeeCommissionSchemeAddForm form) {
        for (EmployeeCommissionAddForm employeeCommissionAddForm : form.Commission) {
            if ((employeeCommissionAddForm.Rate != null && employeeCommissionAddForm.Rate.compareTo(new BigDecimal(100)) > 0))
                return false;
        }
        return true;
    }
}