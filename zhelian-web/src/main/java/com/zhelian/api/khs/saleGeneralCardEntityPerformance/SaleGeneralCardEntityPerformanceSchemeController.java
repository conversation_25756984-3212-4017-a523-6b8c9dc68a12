package com.zhelian.api.khs.saleGeneralCardEntityPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardCommissionSchemeAddForm;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardCommissionSchemeDeleteForm;
import com.zhelian.model.ibeauty.form.input.SaleGeneralCardCommissionSchemeQueryForm;
import com.zhelian.service.khs.saleGeneralCardEntityPerformance.SaleGeneralCardEntityPerformanceSchemeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping(value = "/api")
public class SaleGeneralCardEntityPerformanceSchemeController {

    @Autowired
    SaleGeneralCardEntityPerformanceSchemeService saleGeneralCardEntityPerformanceSchemeService;

    @RequestMapping(value = "/saleGeneralCardEntityPerformanceScheme/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody SaleGeneralCardCommissionSchemeQueryForm form) {
        return saleGeneralCardEntityPerformanceSchemeService.saleGeneralCardEntityPerformanceSchemeList(form);
    }

    @RequestMapping(value = "/saleGeneralCardEntityPerformanceScheme/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody SaleGeneralCardCommissionSchemeAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return saleGeneralCardEntityPerformanceSchemeService.createSaleGeneralCardEntityPerformanceScheme(form);
    }

    @RequestMapping(value = "/saleGeneralCardEntityPerformanceScheme/delete", method = {RequestMethod.POST})
    public BaseOutput delete(@RequestBody SaleGeneralCardCommissionSchemeDeleteForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return saleGeneralCardEntityPerformanceSchemeService.deleteSaleGeneralCardEntityPerformanceScheme(form);
    }
}

