package com.zhelian.api.khs.saleTimeCardPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardSchemeHandlerCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardSchemeHandlerCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardSchemeHandlerJobTypeCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleTimeCardsSchemeHandlerCommissionAddForm;
import com.zhelian.service.khs.saleTimeCardPerformance.SaleTimeCardSchemeHandlerPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class SaleTimeCardSchemeHandlerPerformanceController {

    @Autowired
    SaleTimeCardSchemeHandlerPerformanceService saleTimeCardSchemeHandlerPerformanceService;

    static boolean checkRate(SaleTimeCardsSchemeHandlerCommissionAddForm form) {
        if (form.TimeCardHandler != null) {
            for (SaleTimeCardSchemeHandlerCommissionAddForm handlerPerformanceAddForm : form.TimeCardHandler) {

                if ((handlerPerformanceAddForm.PayRate != null && handlerPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerPerformanceAddForm.SavingCardRate != null && handlerPerformanceAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                        || (handlerPerformanceAddForm.SavingCardLargessRate != null && handlerPerformanceAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                    return false;

                for (SaleTimeCardSchemeHandlerJobTypeCommissionAddForm jobTypePerformanceAddForm : handlerPerformanceAddForm.HandlerJobTypeCommission) {


                    if ((jobTypePerformanceAddForm.PayRate != null && jobTypePerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.SavingCardRate != null && jobTypePerformanceAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                            || (jobTypePerformanceAddForm.SavingCardLargessRate != null && jobTypePerformanceAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                        return false;
                }

            }
        }
        return true;
    }

    @RequestMapping(value = "/saleTimeCardSchemeHandlerPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allSaleProSchemeHandlerPerformance(@RequestBody SaleTimeCardSchemeHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");

        return saleTimeCardSchemeHandlerPerformanceService.allSaleTimeCardSchemeHandlerPerformance(form);
    }

    @RequestMapping(value = "/saleTimeCardSchemeHandlerPerformance/packageCard", method = {RequestMethod.POST})
    public BaseOutput allSalePackageCardTimeCardSchemeHandlerPerformance(@RequestBody SaleTimeCardSchemeHandlerCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");

        return saleTimeCardSchemeHandlerPerformanceService.allSalePackageCardTimeCardSchemeHandlerPerformance(form);
    }

    @RequestMapping(value = "/saleTimeCardSchemeHandlerPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleTimeCardSchemeHandlerPerformance(@RequestBody SaleTimeCardsSchemeHandlerCommissionAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return saleTimeCardSchemeHandlerPerformanceService.updateSaleTimeCardSchemeHandlerPerformance(form);
    }
}

