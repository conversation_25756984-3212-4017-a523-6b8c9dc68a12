package com.zhelian.api.khs.treatSavingCardPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardProjectCategoryCommissionQueryForm;
import com.zhelian.service.khs.treatSavingCardPerformance.TreatSavingCardCategoryPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class TreatSavingCardCategoryPerformanceController {

    @Autowired
    TreatSavingCardCategoryPerformanceService treatSavingCardCategoryPerformanceService;

    static boolean checkRate(TreatSavingCardProjectCategoryAddForm form) {

        for (TreatSavingCardProjectCategoryCommissionAddForm treatSavingCardProjectCategoryPerformanceAddForm : form.Category) {
            if ((treatSavingCardProjectCategoryPerformanceAddForm.Rate != null && treatSavingCardProjectCategoryPerformanceAddForm.Rate.compareTo(new BigDecimal(100)) > 0)
                    || (treatSavingCardProjectCategoryPerformanceAddForm.LargessRate != null && treatSavingCardProjectCategoryPerformanceAddForm.LargessRate.compareTo(new BigDecimal(100)) > 0))
                return false;
        }
        return true;
    }

    @RequestMapping(value = "/treatSavingCardCategoryPerformance/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody TreatSavingCardProjectCategoryCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return treatSavingCardCategoryPerformanceService.alltreatSavingCardCategoryPerformance(form);
    }

    @RequestMapping(value = "/treatSavingCardCategoryPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateTreatSavingCardCategoryPerformance(@RequestBody TreatSavingCardProjectCategoryAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return treatSavingCardCategoryPerformanceService.updateTreatSavingCardCategoryPerformance(form);
    }
}

