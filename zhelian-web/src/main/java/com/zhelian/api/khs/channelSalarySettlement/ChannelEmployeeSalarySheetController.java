package com.zhelian.api.khs.channelSalarySettlement;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.khs.form.input.ChannelEmployeeCommissionQueryForm;
import com.zhelian.model.khs.form.input.ChannelEmployeeSalarySheetQueryForm;
import com.zhelian.model.khs.form.input.SettlementIntervalAddForm;
import com.zhelian.service.khs.channelSalarySettlement.ChannelEmployeeSalarySheetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;


@RestController
@RequestMapping(value = "/api/channelEmployeeSalarySheet")
public class ChannelEmployeeSalarySheetController {

    @Autowired
    ChannelEmployeeSalarySheetService channelEmployeeSalarySheetService;

    @RequestMapping(value = "/createSettlementInterval", method = {RequestMethod.POST})
    public BaseOutput createSettlementInterval(@RequestBody SettlementIntervalAddForm form) throws Exception {
        if (form.SettlementMonth == null)
            return BaseOutput.failed("结算月份不能为空");
        if (form.StartDate == null)
            return BaseOutput.failed("开始时间不能为空");
        if (form.EndDate == null)
            return BaseOutput.failed("结束时间不能为空");
        return channelEmployeeSalarySheetService.createSettlementInterval(form);
    }

    @RequestMapping(value = "/allSettlementInterval", method = {RequestMethod.POST})
    public BaseOutput allSettlementInterval() {
        return channelEmployeeSalarySheetService.allSettlementInterval();
    }

    @RequestMapping(value = "/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody ChannelEmployeeSalarySheetQueryForm form) {
        return channelEmployeeSalarySheetService.all(form);
    }

    @RequestMapping(value = "/excel", method = {RequestMethod.POST})
    public BaseOutput excel(@RequestBody ChannelEmployeeSalarySheetQueryForm form, HttpServletResponse response) {
        return channelEmployeeSalarySheetService.excel(form, response);
    }

    @RequestMapping(value = "/settlement", method = {RequestMethod.POST})
    public BaseOutput settlement(@RequestBody ChannelEmployeeSalarySheetQueryForm form) {
        if (form.ChannelEmployeeSalarySettlementIntervalID == null)
            return BaseOutput.failed("结算周期ID不能为空");
        return channelEmployeeSalarySheetService.settlement(form);
    }


    @RequestMapping(value = "/employeeCommission", method = {RequestMethod.POST})
    public BaseOutput employeeCommission(@RequestBody ChannelEmployeeCommissionQueryForm form) {
        if (form.ChannelEmployeeSalarySettlementIntervalID == null)
            return BaseOutput.failed("结算周期ID不能为空");
        if (form.EmployeeID == null)
            return BaseOutput.failed("员工ID不能为空");
        return channelEmployeeSalarySheetService.employeeCommission(form);
    }

    @RequestMapping(value = "/teamCommission", method = {RequestMethod.POST})
    public BaseOutput teamCommission(@RequestBody ChannelEmployeeCommissionQueryForm form) {
        if (form.ChannelEmployeeSalarySettlementIntervalID == null)
            return BaseOutput.failed("结算周期ID不能为空");
        if (form.EmployeeID == null)
            return BaseOutput.failed("员工ID不能为空");
        return channelEmployeeSalarySheetService.teamCommission(form);
    }
}