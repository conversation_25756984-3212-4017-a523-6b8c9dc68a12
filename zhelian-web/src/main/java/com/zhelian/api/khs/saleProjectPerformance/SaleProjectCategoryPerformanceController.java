package com.zhelian.api.khs.saleProjectPerformance;


import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryCommissionAddForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectCategoryCommissionQueryForm;
import com.zhelian.model.ibeauty.form.input.SaleProjectChildCategoryCommissionAddForm;
import com.zhelian.service.khs.saleProjectPerformance.SaleProjectCategoryPerformanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


@RestController
@RequestMapping(value = "/api")
public class SaleProjectCategoryPerformanceController {

    @Autowired
    SaleProjectCategoryPerformanceService saleProjectCategoryPerformanceService;

    static boolean checkRate(SaleProjectCategoryAddForm form) {

        if (form.Category != null && form.Category.size() > 0) {
            for (SaleProjectCategoryCommissionAddForm saleProjectCategoryPerformanceAddForm : form.Category) {

                if ((saleProjectCategoryPerformanceAddForm.PayRate != null && saleProjectCategoryPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                        || (saleProjectCategoryPerformanceAddForm.SavingCardRate != null && saleProjectCategoryPerformanceAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                        || (saleProjectCategoryPerformanceAddForm.SavingCardLargessRate != null && saleProjectCategoryPerformanceAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                    return false;
                if (saleProjectCategoryPerformanceAddForm.Child != null && saleProjectCategoryPerformanceAddForm.Child.size() > 0) {
                    for (SaleProjectChildCategoryCommissionAddForm saleProjectChildCategoryPerformanceAddForm : saleProjectCategoryPerformanceAddForm.Child) {

                        if ((saleProjectChildCategoryPerformanceAddForm.PayRate != null && saleProjectChildCategoryPerformanceAddForm.PayRate.compareTo(new BigDecimal(100)) > 0)
                                || (saleProjectChildCategoryPerformanceAddForm.SavingCardRate != null && saleProjectChildCategoryPerformanceAddForm.SavingCardRate.compareTo(new BigDecimal(100)) > 0)
                                || (saleProjectChildCategoryPerformanceAddForm.SavingCardLargessRate != null && saleProjectChildCategoryPerformanceAddForm.SavingCardLargessRate.compareTo(new BigDecimal(100)) > 0))
                            return false;

                    }
                }


            }
        }
        return true;
    }

    @RequestMapping(value = "/saleProjectCategoryPerformance/all", method = {RequestMethod.POST})
    public BaseOutput allSaleProjectCategoryPerformance(@RequestBody SaleProjectCategoryCommissionQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        return saleProjectCategoryPerformanceService.allSaleProjectCategoryPerformance(form);
    }

    @RequestMapping(value = "/saleProjectCategoryPerformance/update", method = {RequestMethod.POST})
    public BaseOutput updateSaleProjectCategoryPerformance(@RequestBody SaleProjectCategoryAddForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("组织单位不能为空");
        if (!checkRate(form))
            return BaseOutput.failed("比例不能大于100");
        return saleProjectCategoryPerformanceService.updateSaleProjectCategoryPerformance(form);
    }
}

