/**
 * fileName: PayMethodController.java
 * author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * date: 2020/3/4 4:36 PM
 * copyright(c) 2019-2020 xxx公司
 */
package com.zhelian.api.psi.product;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.psi.form.input.ProductAddForm;
import com.zhelian.model.psi.form.input.ProductQueryForm;
import com.zhelian.model.psi.form.input.ProductUpdateForm;
import com.zhelian.service.psi.product.ProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 功能描述:
 * <>
 *
 * @return:
 * @exception:
 * @author: WangXian<PERSON><PERSON>
 * @date: 2020/3/12 1:49 PM
 */
@RestController
@RequestMapping(value = "/api")
public class ProductController {

    @Autowired
    ProductService productService;

    @RequestMapping(value = "/product/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody ProductQueryForm form) {
        return productService.productList(form);
    }

    @RequestMapping(value = "/product/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody ProductAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("产品名称不能为空");
        if (form.PCategoryID == null)
            return BaseOutput.failed("产品分类不能为空");
        if (form.UnitID == null)
            return BaseOutput.failed("产品单位不能为空");
        if (form.Active == null)
            return BaseOutput.failed("有效性不能为空");
        return productService.insertProduct(form);
    }

    @RequestMapping(value = "/product/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody ProductUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("产品编号不能为空");
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("产品名称不能为空");
        if (form.PCategoryID == null)
            return BaseOutput.failed("产品分类不能为空");
        if (form.Active == null)
            return BaseOutput.failed("有效性不能为空");

        return productService.updateProduct(form);
    }

}