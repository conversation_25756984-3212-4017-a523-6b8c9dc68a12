package com.zhelian.api.psi.dispatchPrice;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.psi.form.input.DispatchPriceCategoryQueryForm;
import com.zhelian.model.psi.form.input.DispatchPriceProductAddForm;
import com.zhelian.service.psi.dispatchPrice.DispatchPriceProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/dispatchPriceProduct")
public class DispatchPriceProductController {

    @Autowired
    DispatchPriceProductService dispatchPriceProductService;

    @RequestMapping(value = "/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody DispatchPriceCategoryQueryForm form) {
        return dispatchPriceProductService.dispatchPriceProductList(form);
    }


    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody DispatchPriceProductAddForm form) {
        if (form.ID == null)
            return BaseOutput.failed("方案编号不能为空");
        if (form.CategoryID == null)
            return BaseOutput.failed("分类编号不能为空");
        return dispatchPriceProductService.update(form);
    }


}