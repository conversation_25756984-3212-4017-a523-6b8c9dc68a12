/**
 * fileName: PayMethodController.java
 * author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * date: 2020/3/4 4:36 PM
 * copyright(c) 2019-2020 xxx公司
 */
package com.zhelian.api.psi.stock;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.psi.form.input.*;
import com.zhelian.service.psi.stock.StockService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * 功能描述:
 * <>
 *
 * @return:
 * @exception:
 * @author: WangXianXian
 * @date: 2020/3/12 1:49 PM
 */
@RestController
@RequestMapping(value = "/api/stock")
public class StockController {

    @Autowired
    StockService stockService;

    @RequestMapping(value = "/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody StockQueryForm form) {
        return stockService.stockList(form);
    }

    @RequestMapping(value = "/stockExcel", method = {RequestMethod.POST})
    public BaseOutput easyExcel(@RequestBody StockQueryForm form, HttpServletResponse response) {
        return stockService.stockEasyExcel(form, response);
    }

    @RequestMapping(value = "/productEntity", method = {RequestMethod.POST})
    public BaseOutput productEntity(@RequestBody ProductEntityQueryForm form) {
        return stockService.productEntity(form);
    }

    /**
     * 功能描述:
     * <分页-门店库存，无库存不查>
     *
     * @param: form
     * @return: com.zhelian.model.base.BaseOutput
     * @exception:
     * @author: WangXianXian
     * @date: 2021/1/8 3:18 下午
     */
    @RequestMapping(value = "/entityProductList", method = {RequestMethod.POST})
    public BaseOutput entityStockList(@RequestBody StockQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("仓库编号不能为空");
        return stockService.entityProductList(form);
    }

    /**
     * 功能描述:
     * <分页-门店库存，包含无库存产品，无库存默认为0>
     *
     * @param: form
     * @return: com.zhelian.model.base.BaseOutput
     * @exception:
     * @author: WangXianXian
     * @date: 2021/1/8 3:18 下午
     */
    @RequestMapping(value = "/entityProductListAll", method = {RequestMethod.POST})
    public BaseOutput entityProductListAll(@RequestBody StockQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("仓库编号不能为空");
        return stockService.entityProductListAll(form);
    }

    @RequestMapping(value = "/productList", method = {RequestMethod.POST})
    public BaseOutput productList(@RequestBody StockQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("仓库编号不能为空");
        return stockService.productList(form);
    }

    @RequestMapping(value = "/inventoryCheckProductList", method = {RequestMethod.POST})
    public BaseOutput inventoryCheckProductList(@RequestBody StockQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("仓库编号不能为空");
        return stockService.inventoryCheckProductList(form);
    }

    /**
     * 功能描述:
     * <无分页-门店库存，包含无库存产品，无库存默认为0>
     *
     * @param: form
     * @return: com.zhelian.model.base.BaseOutput
     * @exception:
     * @author: WangXianXian
     * @date: 2021/1/8 3:21 下午
     */
    @RequestMapping(value = "/entityProductAll", method = {RequestMethod.POST})
    public BaseOutput entityProductAll(@RequestBody EntityProductAllQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("仓库编号不能为空");
        return stockService.entityProductAll(form);
    }

    /**
     * 功能描述:
     * <根据产品ID数组查询对应产品库存>
     *
     * @param: form
     * @return: com.zhelian.model.base.BaseOutput
     * @exception:
     * @author: WangXianXian
     * @date: 2021/1/8 3:22 下午
     */
    @RequestMapping(value = "/productStock", method = {RequestMethod.POST})
    public BaseOutput productStock(@RequestBody ProductStockQueryForm form) {
        if (form.EntityID == null) {
            return BaseOutput.failed("仓库编号不能为空");
        }
        return stockService.ProductStock(form);
    }

    @RequestMapping(value = "/inventoryDetail", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody InventoryDetailQueryForm form) {
        return stockService.inventoryDetail(form);
    }

    @RequestMapping(value = "/inventoryDetailExcel", method = {RequestMethod.POST})
    public BaseOutput inventoryDetaiEasyExcel(@RequestBody InventoryDetailQueryForm form, HttpServletResponse response) {
        return stockService.inventoryDetailEasyExcel(form, response);
    }

    @RequestMapping(value = "/productDispatchPrice", method = {RequestMethod.POST})
    public BaseOutput productDispatchPrice(@RequestBody ProductDispatchPriceQueryForm form) {
        if (form.EntityID == null) {
            return BaseOutput.failed("仓库编号不能为空");
        }
        return stockService.productDispatchPrice(form);
    }

    @RequestMapping(value = "/entityProductDeliveryPrice", method = {RequestMethod.POST})
    public BaseOutput entityProductDeliveryPrice(@RequestBody StockQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("仓库编号不能为空");
        return stockService.entityProductDeliveryPrice(form);
    }

    @RequestMapping(value = "/productQuantity", method = {RequestMethod.POST})
    public BaseOutput productQuantity(@RequestBody StockProductQueryForm form) {
        if (form.EntityID == null)
            return BaseOutput.failed("仓库编号不能为空");
        return stockService.productQuantity(form);
    }
}