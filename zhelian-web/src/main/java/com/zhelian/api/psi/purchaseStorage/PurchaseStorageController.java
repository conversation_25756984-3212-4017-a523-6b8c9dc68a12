/**
 * fileName: PayMethodController.java
 * author: <PERSON>Xian<PERSON>ian
 * date: 2020/3/4 4:36 PM
 * copyright(c) 2019-2020 xxx公司
 */
package com.zhelian.api.psi.purchaseStorage;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.psi.form.input.*;
import com.zhelian.service.psi.purchaseStorage.PurchaseStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 功能描述:
 * <>
 *
 * @return:
 * @exception:
 * @author: WangXianXian
 * @date: 2020/3/12 1:49 PM
 */
@RestController
@RequestMapping(value = "/api/purchaseStorage")
public class PurchaseStorageController {

    @Autowired
    PurchaseStorageService purchaseStorageService;

    @RequestMapping(value = "/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody PurchaseStorageQueryForm form) {
        return purchaseStorageService.purchaseStorageList(form);
    }

    @RequestMapping(value = "/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody PurchaseStorageAddForm form) throws Exception {
        if (form.InDate == null) {
            return BaseOutput.failed("入库日期不能为空");
        }
        if (form.Product == null || form.Product.size() == 0) {
            return BaseOutput.failed("入库产品不能为空");
        } else {
            for (PurchaseStorageDetailAddForm purchaseStorageDetailAddForm : form.Product) {
                if (purchaseStorageDetailAddForm.ProductID == null) {
                    return BaseOutput.failed("产品编号不能为空");
                }
                if (purchaseStorageDetailAddForm.UnitID == null) {
                    return BaseOutput.failed("产品单位不能为空");
                }
                if (purchaseStorageDetailAddForm.UnitPrice == null || purchaseStorageDetailAddForm.UnitPrice.compareTo(BigDecimal.ZERO) < 0) {
                    return BaseOutput.failed("产品采购单价不能为空且必须大于或等于0");
                }
                if (purchaseStorageDetailAddForm.Quantity == null || purchaseStorageDetailAddForm.Quantity <= 0) {
                    return BaseOutput.failed("产品采购数量不能为空且必须大于0");
                }
                if (purchaseStorageDetailAddForm.Amount == null || purchaseStorageDetailAddForm.Amount.compareTo(BigDecimal.ZERO) < 0) {
                    return BaseOutput.failed("产品采购金额不能为空且必须大于或等于0");
                }
                if (purchaseStorageDetailAddForm.MinimumUnitID == null) {
                    return BaseOutput.failed("产品最小单位不能为空");
                }
                if (purchaseStorageDetailAddForm.MinimumUnitQuantity == null || purchaseStorageDetailAddForm.MinimumUnitQuantity <= 0) {
                    return BaseOutput.failed("产品最小单位数量不能为空且必须大于0");
                }
            }
        }

        return purchaseStorageService.insertPurchaseStorage(form);
    }

    @RequestMapping(value = "/info", method = {RequestMethod.POST})
    public BaseOutput info(@RequestBody PurchaseStorageInfoQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("采购入库单号不能为空");

        return purchaseStorageService.purchaseStorageInfo(form.ID);
    }

    @RequestMapping(value = "/entity", method = {RequestMethod.POST})
    public BaseOutput entity() {
        return purchaseStorageService.warehouseList();
    }

    @RequestMapping(value = "/supplier", method = {RequestMethod.POST})
    public BaseOutput supplier() {
        return purchaseStorageService.supplier();
    }


    @RequestMapping(value = "/print", method = {RequestMethod.POST})
    public BaseOutput purchaseStoragePrint(@RequestBody PurchaseStorageInfoQueryForm form) {
        return purchaseStorageService.purchaseStoragePrint(form.ID);
    }

    @RequestMapping(value = "/detail", method = {RequestMethod.POST})
    public BaseOutput detail(@RequestBody PurchaseStorageDetailQueryForm form) {
        return purchaseStorageService.detail(form);
    }


    @PostMapping(value = "/excel")
    public BaseOutput easyExcelDetailStatement(@RequestBody PurchaseStorageDetailQueryForm form, HttpServletResponse response) {
        if (form.StartTime == null || form.EndTime == null) {
            return BaseOutput.failed("请选择查询的时间段");
        }
        return purchaseStorageService.easyExcelDetailStatement(form, response);
    }

    // 新增接口 - 采购入库流程优化

    /**
     * 创建采购入库单（待确认状态）
     * @param form 入库表单
     * @return 操作结果
     */
    @RequestMapping(value = "/createDraft", method = {RequestMethod.POST})
    public BaseOutput createDraft(@RequestBody PurchaseStorageAddForm form) throws Exception {
        // 参数验证
        if (form.InDate == null) {
            return BaseOutput.failed("入库日期不能为空");
        }
        if (form.Product == null || form.Product.size() == 0) {
            return BaseOutput.failed("入库产品不能为空");
        }

        // 产品明细验证
        for (PurchaseStorageDetailAddForm detail : form.Product) {
            if (detail.ProductID == null) {
                return BaseOutput.failed("产品编号不能为空");
            }
            if (detail.UnitID == null) {
                return BaseOutput.failed("产品单位不能为空");
            }
            if (detail.UnitPrice == null || detail.UnitPrice.compareTo(BigDecimal.ZERO) < 0) {
                return BaseOutput.failed("产品采购单价不能为空且必须大于或等于0");
            }
            if (detail.Quantity == null || detail.Quantity <= 0) {
                return BaseOutput.failed("产品采购数量不能为空且必须大于0");
            }
        }

        return purchaseStorageService.createPurchaseStorageDraft(form);
    }

    /**
     * 获取待确认入库单详情（只显示未完成明细）
     * @param form 查询表单
     * @return 详情信息
     */
    @RequestMapping(value = "/pendingInfo", method = {RequestMethod.POST})
    public BaseOutput pendingInfo(@RequestBody PurchaseStorageInfoQueryForm form) {
        if (form.ID == null) {
            return BaseOutput.failed("采购入库单号不能为空");
        }
        return purchaseStorageService.getPendingPurchaseStorageInfo(form.ID);
    }

    /**
     * 获取入库单完整详情（显示所有明细）
     * @param form 查询表单
     * @return 详情信息
     */
    @RequestMapping(value = "/fullInfo", method = {RequestMethod.POST})
    public BaseOutput fullInfo(@RequestBody PurchaseStorageInfoQueryForm form) {
        if (form.ID == null) {
            return BaseOutput.failed("采购入库单号不能为空");
        }
        return purchaseStorageService.getFullPurchaseStorageInfo(form.ID);
    }

    /**
     * 确认采购入库
     * @param form 确认表单
     * @return 操作结果
     */
    @RequestMapping(value = "/confirm", method = {RequestMethod.POST})
    public BaseOutput confirm(@RequestBody PurchaseStorageConfirmForm form) throws Exception {
        if (form.PurchaseStorageID == null) {
            return BaseOutput.failed("采购入库单号不能为空");
        }
        if (form.ProductList == null || form.ProductList.isEmpty()) {
            return BaseOutput.failed("确认产品列表不能为空");
        }

        // 验证选中的产品和本次入库数量
        List<PurchaseStorageDetailConfirmForm> selectedDetails = form.ProductList.stream()
            .filter(detail -> detail.IsSelected != null && detail.IsSelected)
            .collect(Collectors.toList());

        if (selectedDetails.isEmpty()) {
            return BaseOutput.failed("请至少选择一个产品进行确认");
        }

        for (PurchaseStorageDetailConfirmForm detail : selectedDetails) {
            if (detail.ThisTimeQuantity == null || detail.ThisTimeQuantity <= 0) {
                return BaseOutput.failed("本次入库数量不能为空且必须大于0");
            }
            if (detail.ThisTimeMinimumUnitQuantity == null || detail.ThisTimeMinimumUnitQuantity <= 0) {
                return BaseOutput.failed("本次最小单位数量不能为空且必须大于0");
            }
        }

        return purchaseStorageService.confirmPurchaseStorage(form);
    }
}