package com.zhelian.api.psi.dispatchPrice;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.psi.form.input.DispatchPriceGoodsQueryForm;
import com.zhelian.service.psi.dispatchPrice.DispatchPriceGoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/dispatchPriceGoods")
public class DispatchPriceController {

    @Autowired
    DispatchPriceGoodsService dispatchPriceGoodsService;

    @RequestMapping(value = "/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody DispatchPriceGoodsQueryForm form) {
        return dispatchPriceGoodsService.dispatchPriceGoodsList(form);
    }

}