package com.zhelian.api.psi.inventoryProductInbound;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.psi.form.input.InventoryProductInboundAddForm;
import com.zhelian.model.psi.form.input.InventoryProductInboundDetailAddForm;
import com.zhelian.model.psi.form.input.InventoryProductInboundInfoQueryForm;
import com.zhelian.model.psi.form.input.InventoryProductInboundQueryForm;
import com.zhelian.service.psi.inventoryProductInbound.InventoryProductInboundService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/inventoryProductInbound")
public class InventoryProductInboundController {

    @Autowired
    InventoryProductInboundService inventoryProductInboundService;

    @RequestMapping(value = "/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody InventoryProductInboundQueryForm form) {
        return inventoryProductInboundService.list(form);
    }

    @RequestMapping(value = "/info", method = {RequestMethod.POST})
    public BaseOutput info(@RequestBody InventoryProductInboundInfoQueryForm form) {
        if (form.ID == null)
            return BaseOutput.failed("产品入库单号不能为空");

        return inventoryProductInboundService.info(form.ID, form.InventoryType);
    }

    @RequestMapping(value = "/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody InventoryProductInboundAddForm form) throws Exception {
        if (form.Detail == null || form.Detail.size() == 0) {
            return BaseOutput.failed("产品入库单产品明细不能为空");
        }
        if (form.EntityID == null) {
            return BaseOutput.failed("入库仓库不能为空");
        }
        for (InventoryProductInboundDetailAddForm inventoryProductInboundDetailAddForm : form.Detail) {
            if (inventoryProductInboundDetailAddForm.ProductID == null) {
                return BaseOutput.failed("产品编号不能为空");
            }
            if (inventoryProductInboundDetailAddForm.UnitID == null) {
                return BaseOutput.failed("产品单位不能为空");
            }
            if (inventoryProductInboundDetailAddForm.Quantity <= 0) {
                return BaseOutput.failed("产品入库数量必须大于0");
            }
            if (inventoryProductInboundDetailAddForm.MinimumUnitID == null) {
                return BaseOutput.failed("产品最小单位不能为空");
            }
            if (inventoryProductInboundDetailAddForm.MinimumUnitQuantity <= 0) {
                return BaseOutput.failed("产品最小单位入库数量必须大于0");
            }
        }
        return inventoryProductInboundService.create(form);
    }

}