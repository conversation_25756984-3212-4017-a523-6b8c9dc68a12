/**
 * fileName: PayMethodController.java
 * author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * date: 2020/3/4 4:36 PM
 * copyright(c) 2019-2020 xxx公司
 */
package com.zhelian.api.psi.product;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.psi.form.input.ProductCategoryAddForm;
import com.zhelian.model.psi.form.input.ProductCategoryMoveForm;
import com.zhelian.model.psi.form.input.ProductCategoryQueryForm;
import com.zhelian.model.psi.form.input.ProductCategoryUpdateForm;
import com.zhelian.service.psi.product.ProductCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 功能描述:
 * <>
 *
 * @return:
 * @exception:
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2020/3/12 1:49 PM
 */
@RestController
@RequestMapping(value = "/api")
public class ProductCategoryController {

    @Autowired
    ProductCategoryService productCategoryService;

    @RequestMapping(value = "/productCategory/all", method = {RequestMethod.POST})
    public BaseOutput all(@RequestBody ProductCategoryQueryForm form) {
        return productCategoryService.productCategoryList(form);
    }

    @RequestMapping(value = "/productCategory/valid", method = {RequestMethod.POST})
    public BaseOutput ValidProductCategoryValid() {
        return productCategoryService.getValidProductCategoryValid();
    }

    @RequestMapping(value = "/productCategory/top", method = {RequestMethod.POST})
    public BaseOutput top(@RequestBody ProductCategoryQueryForm form) {
        return productCategoryService.getTopProductList(form);
    }

    @RequestMapping(value = "/productCategory/create", method = {RequestMethod.POST})
    public BaseOutput create(@RequestBody ProductCategoryAddForm form) {
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("产品分类名称不能为空");
        if (form.ParentID == null)
            return BaseOutput.failed("上级分类不能为空");

        return productCategoryService.addProductCategory(form);
    }

    @RequestMapping(value = "/productCategory/update", method = {RequestMethod.POST})
    public BaseOutput update(@RequestBody ProductCategoryUpdateForm form) {
        if (form.ID == null)
            return BaseOutput.failed("ID不能为空");
        if (!StringUtils.hasText(form.Name))
            return BaseOutput.failed("产品分类名称不能为空");
        if (form.Active == null)
            return BaseOutput.failed("有效性不能为空");

        return productCategoryService.updateProductCategory(form);
    }

    @RequestMapping(value = "/productCategory/move", method = {RequestMethod.POST})
    public BaseOutput move(@RequestBody ProductCategoryMoveForm form) {
        if (form.MoveID == null)
            return BaseOutput.failed("MoveID不能为空");
        return productCategoryService.move(form);
    }

}