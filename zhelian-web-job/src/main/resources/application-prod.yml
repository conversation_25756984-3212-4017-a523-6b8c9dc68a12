spring:
  datasource:
    druid:
      url: *********************************************************************************
      username: core37mei
      password: d3ERQ8kLXZJD6se6AAyT9z5VCdPhoeAi8KGURL3/q6daUS7RM6LZHMD6qXybK7RHJd72ozG/vX1fduoxJpydSw==
      connection-properties: config.decrypt=true;config.decrypt.key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJ3oXkaW4AeefkfAhC8VLnFeNT1ekodCjKlrjv664EKzlRorWNyoiE8zPyeJkQdiG0blcaBevCy3g/F9MRUk61cCAwEAAQ==
      filters: config
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  #redis配置
  redis:
    host: r-uf6pescx31qw6hhww6.redis.rds.aliyuncs.com
    port: 6379
    password: d83Bs8fwH2m5S6pi
    lettuce:
      pool:
        max-wait: -1ms
        max-active: 8
        max-idle: 8
        min-idle: 0
    timeout: 5000ms

aliyun:
  sms:
    regionId:
    accessKeyId:
    secret:
    signName:
  oss:
    endpoint:
    accessKeyId:
    secret:
    bucketName:
  vod:
    regionId:
    accessKeyId:
    secret:

wechat:
  open:
    componentAppId:
    componentSecret:
    componentToken:
    componentAesKey:

logging:
  level:
    root: info
    '[com.zhelian.mapper]': debug   #设置mapper.java包为debug输出
  file:
    name: /var/log/sqm/job.log
