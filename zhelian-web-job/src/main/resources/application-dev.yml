spring:
  datasource:
    druid:
      url: ********************************************************************************************
      username: core
      password: fW5Yjid436iuFlQoaHU6sOYFzXdp7kKFNigXcrgW0at1AIPFi9brehCAoo7GXYanA4gRMCfc7+LVHxljrlCf2w==
      connection-properties: config.decrypt=true;config.decrypt.key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAIwy74Uar+BnHjVhNJOjqlfWHLJ/6QmKQ71gSplydtVoNjvpSjidpJdfiGjk7uByB73KbhcoE7KLN9Tik07UZgsCAwEAAQ==
      filters: config
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  #redis配置
  redis:
    host: 127.0.0.1
    port: 6379
    password:
    lettuce:
      pool:
        max-wait: -1ms
        max-active: 8
        max-idle: 8
        min-idle: 0
    timeout: 5000ms

aliyun:
  sms:
    regionId:
    accessKeyId:
    secret:
    signName:
  oss:
    endpoint:
    accessKeyId:
    secret:
    bucketName:
  vod:
    regionId:
    accessKeyId:
    secret:

wechat:
  open:
    componentAppId:
    componentSecret:
    componentToken:
    componentAesKey:

logging:
  level:
    root: info
    '[com.zhelian.mapper]': debug   #设置mapper.java包为debug输出
  file:
    name: logs/job.log
