package com.zhelian.task.seckill;


import com.zhelian.mapper.enterprise.EnterpriseMapper;
import com.zhelian.mapper.enterprise.EnterpriseOplatformMapper;
import com.zhelian.model.MC.entity.EnterpriseWxPayEntity;
import com.zhelian.model.enterprise.entity.EnterpriseEntity;
import com.zhelian.service.micromall.seckill.SeckillBillService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SeckillSchedule {

    @Autowired
    EnterpriseMapper enterpriseMapper;
    @Autowired
    EnterpriseOplatformMapper enterpriseOplatformMapper;
    @Autowired
    SeckillBillService seckillBillService;

    private Logger logger = LoggerFactory.getLogger(getClass());

    @Scheduled(initialDelay = 1000 * 60 * 8, fixedDelay = 1000 * 60 * 10)
    public void TimeOutCancel() {
        List<EnterpriseEntity> enterpriseEntities = enterpriseMapper.getNotExpiresMiniProgramEnterprises();
        for (EnterpriseEntity enterpriseEntity : enterpriseEntities) {
            try {
                
                EnterpriseWxPayEntity enterpriseWxPay = enterpriseOplatformMapper.getEnterpriseWxPay(enterpriseEntity.EnterpriseCode);
                if (enterpriseWxPay != null) {
                    seckillBillService.cancelSeckillBill();
                }
            } catch (Exception e) {
                logger.error(e.getMessage());
            }
        }
    }
}


