package com.zhelian.task.groupon;


import com.zhelian.core.weixin.pay.WxPayConfiguration;
import com.zhelian.mapper.enterprise.EnterpriseMapper;
import com.zhelian.mapper.enterprise.EnterpriseOplatformMapper;
import com.zhelian.model.MC.entity.EnterpriseMiniprogramEntity;
import com.zhelian.model.MC.entity.EnterpriseWxPayEntity;
import com.zhelian.model.enterprise.entity.EnterpriseEntity;
import com.zhelian.service.micromall.groupon.GrouponBillService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class GrouponSchedule {

    @Autowired
    EnterpriseMapper enterpriseMapper;
    @Autowired
    EnterpriseOplatformMapper enterpriseOplatformMapper;
    @Autowired
    GrouponBillService grouponBillService;

    private Logger logger = LoggerFactory.getLogger(getClass());

    @Scheduled(initialDelay = 1000 * 60 * 4, fixedDelay = 1000 * 60 * 10)
    public void AutomaticGroupon() {
        List<EnterpriseEntity> enterpriseEntities = enterpriseMapper.getNotExpiresMiniProgramEnterprises();
        for (EnterpriseEntity enterpriseEntity : enterpriseEntities) {
            try {
                
                EnterpriseWxPayEntity enterpriseWxPay = enterpriseOplatformMapper.getEnterpriseWxPay(enterpriseEntity.EnterpriseCode);
                if (enterpriseWxPay != null) {
                    grouponBillService.autoGrouponBill(enterpriseWxPay.PayMethod);
                }
            } catch (Exception e) {
                logger.error(e.getMessage());
            }
        }
    }

    @Scheduled(initialDelay = 1000 * 60 * 6, fixedDelay = 1000 * 60 * 10)
    public void TimeOutCancel() {
        List<EnterpriseEntity> enterpriseEntities = enterpriseMapper.getNotExpiresMiniProgramEnterprises();
        for (EnterpriseEntity enterpriseEntity : enterpriseEntities) {
            try {
                
                EnterpriseMiniprogramEntity enterpriseMiniprogram = enterpriseOplatformMapper.getEnterpriseMiniprogram(enterpriseEntity.EnterpriseCode);
                EnterpriseWxPayEntity enterpriseWxPay = enterpriseOplatformMapper.getEnterpriseWxPay(enterpriseEntity.EnterpriseCode);
                if (enterpriseWxPay != null) {
                    WxPayConfiguration wxPayConfiguration = new WxPayConfiguration(enterpriseMiniprogram.AuthorizerAppID, enterpriseWxPay.MchId, enterpriseWxPay.MchKey, enterpriseWxPay.KeyPath);
                    grouponBillService.cancelTimeOutGrouponBill(wxPayConfiguration);
                }
            } catch (Exception e) {
                logger.error(e.getMessage());
            }
        }
    }
}
