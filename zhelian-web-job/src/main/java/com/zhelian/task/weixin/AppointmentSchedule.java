package com.zhelian.task.weixin;


import com.zhelian.mapper.enterprise.EnterpriseMapper;
import com.zhelian.mapper.enterprise.EnterpriseOplatformMapper;
import com.zhelian.model.enterprise.entity.EnterpriseEntity;
import com.zhelian.service.weixin.WeiXinMessageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AppointmentSchedule {

    @Autowired
    EnterpriseMapper enterpriseMapper;
    @Autowired
    EnterpriseOplatformMapper enterpriseOplatformMapper;
    @Autowired
    WeiXinMessageService weiXinMessageService;

    private Logger logger = LoggerFactory.getLogger(getClass());

    @Scheduled(initialDelay = 1000 * 60 * 10, fixedDelay = 1000 * 60 * 10)
    public void AppointmentRemaid() {
        List<EnterpriseEntity> enterpriseEntities = enterpriseMapper.getNotExpiresOffiaccountEnterprises();
        for (EnterpriseEntity enterpriseEntity2 : enterpriseEntities) {
            try {

                weiXinMessageService.setAppointmentBillRemaid(enterpriseEntity2.EnterpriseCode);
            } catch (Exception e) {
                logger.error(e.getMessage());
            }
        }
    }

}
