package com.zhelian.task.clueDistribution;

import com.zhelian.service.ibeauty.clueDistribution.ClueDistributionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

@Configuration      //1.主要用于标记配置类，兼备Component的效果。
@EnableScheduling   // 2.开启定时任务
public class ClueDistributionSchedule {

    @Autowired
    ClueDistributionService clueDistributionService;

    @Scheduled(cron = "0 0/10 * * * ?")
    public void upload() {
        clueDistributionService.refreshAccessToken();
    }
}
