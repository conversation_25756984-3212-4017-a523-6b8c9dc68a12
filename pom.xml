<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.zhelian</groupId>
    <artifactId>zhelian</artifactId>
    <version>1.0</version>
    <packaging>pom</packaging>

    <name>sql-erp-java</name>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.5</version>
        <relativePath/>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <modules>
        <module>zhelian-mapper</module>
        <module>zhelian-model</module>
        <module>zhelian-service</module>
        <module>zhelian-web</module>
        <module>zhelian-web-miniprogram</module>
        <module>zhelian-web-miniprogram-mincroMall</module>
        <module>zhelian-web-job</module>
    </modules>

</project>
