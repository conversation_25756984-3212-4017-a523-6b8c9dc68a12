server {
    listen 80;
    server_name erp.37mei.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name erp.37mei.com;

    ssl_certificate /etc/nginx/ssl/erp.37mei.com.pem;
    ssl_certificate_key /etc/nginx/ssl/erp.37mei.com.key;

    ssl_session_cache shared:SSL:1m;
    ssl_session_timeout  10m;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers 'TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384';
    ssl_prefer_server_ciphers on;

    # HSTS (HTTP Strict Transport Security)
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # OCSP Stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;

    # --- 前端性能优化 ---

    # 1. 对静态资源（JS, CSS, 图片等）设置超长浏览器缓存
    location ~* \.(?:jpg|jpeg|gif|png|ico|css|js|svg|woff|woff2)$ {
        root   /var/www/erp.37mei.com/html;
        expires 30d; # 缓存30天
        add_header Cache-Control "public";
        access_log off; # 可选：关闭静态资源的访问日志以减少I/O
    }

    # 2. 前端路由处理 (Vue/React/Angular)
    location / {
        root   /var/www/erp.37mei.com/html;
        try_files $uri $uri/ /index.html;
    }

    # --- 后端 API 代理优化 ---

    # 3. 代理到后端API服务
    location /api/ {
        proxy_buffering on;
        proxy_pass http://localhost:8080/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_connect_timeout 60s;
        proxy_read_timeout 60s;
        proxy_send_timeout 60s;

        proxy_http_version 1.1;
        proxy_set_header Connection "";
    }
}