[Unit]
Description=Zhelian Web Miniprogram MicroMall Service
After=network.target

[Service]
User=root
Group=root
Type=simple
ExecStart=/usr/bin/java -Xms512m -Xmx2g -XX:+UseG1GC -XX:+UseStringDeduplication -Dfile.encoding=UTF-8 -Duser.timezone=Asia/Shanghai -jar /opt/zhelian/zhelian-web-miniprogram-mincroMall.jar --spring.profiles.active=prod
Restart=on-failure
RestartSec=10

[Install]
WantedBy=multi-user.target