package com.zhelian.mapper.micromall;

import com.zhelian.model.micromall.entity.GrouponEntity;
import com.zhelian.model.micromall.entity.GrouponEntityEntity;
import com.zhelian.model.micromall.entity.GrouponPriceEntity;
import com.zhelian.model.micromall.form.output.GrouponDetailOutputForm;
import com.zhelian.model.micromall.form.output.GrouponInfoExtendOutputForm;
import com.zhelian.model.micromall.form.output.GrouponInfoOutputForm;
import com.zhelian.model.micromall.form.output.GrouponOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface GrouponMapper {

    List<GrouponOutputForm> getGrouponList(@Param("Name") String Name, @Param("Status") String Status);

    void createGroupon(GrouponEntity grouponEntity);

    void createGrouponEntity(List<GrouponEntityEntity> entityList);

    void createGrouponPriceEntity(List<GrouponPriceEntity> priceList);


    void updateGroupon(GrouponEntity grouponEntity);

    void deleteGrouponEntity(@Param("ID") Integer ID);

    void deleteGrouponPriceEntity(@Param("ID") Integer ID);

    GrouponInfoOutputForm getGrouponInfo(@Param("ID") Integer ID);

    Integer[] getGrouponEntity(@Param("ID") Integer ID);

    List<GrouponPriceEntity> getGrouponPrice(@Param("ID") Integer ID);

    List<GrouponDetailOutputForm> getGrouponDetailList();

    GrouponInfoExtendOutputForm getGrouponDetails(@Param("ID") Integer ID);

    GrouponInfoExtendOutputForm getGrouponByPriceID(@Param("ID") Integer ID);

    GrouponPriceEntity getGrouponPriceByID(@Param("ID") Integer ID);

    GrouponPriceEntity getGrouponPriceBySaleBillID(@Param("SaleBillID") String SaleBillID);

    String getGrouponEndDateTime(@Param("ID") Integer ID);

    GrouponEntity getGrouponByID(@Param("ID") Integer ID);

    void updateGrouponPrice(GrouponPriceEntity grouponPriceEntity);

    void deleteGrouponPriceEntityByID(List<Integer> list);

    List<Integer> getGroupPrice(@Param("GroupID") Integer GroupID,@Param("Array") Integer[] Array);

}
