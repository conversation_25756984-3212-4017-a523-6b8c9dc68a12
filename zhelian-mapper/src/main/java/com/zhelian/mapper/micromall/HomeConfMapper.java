package com.zhelian.mapper.micromall;

import com.zhelian.model.micromall.entity.HomeConfEntity;
import com.zhelian.model.micromall.form.output.ComponentsOutForm;
import com.zhelian.model.micromall.form.output.HomeConfDetailOutForm;
import com.zhelian.model.micromall.form.output.HomeConfGoodsOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface HomeConfMapper {

    List<ComponentsOutForm> getBaseComponents();

    List<ComponentsOutForm> getMarketingComponents();

    List<ComponentsOutForm> getOtherComponents();

    List<HomeConfDetailOutForm> getHomeConf();

    void deleteHomeConf();

    void insertHomeConf(HomeConfEntity homeConfEntity);

    HomeConfGoodsOutForm getGoodDetail(@Param("GoodsID") Integer GoodsID, @Param("GoodsType") String GoodsType);

    Boolean getGrouponStatus(@Param("ID") Integer ID);

    Integer getGrouponSaleRange(@Param("ID") Integer ID, @Param("EntityID") Integer EntityID);

    Boolean getSeckillStatus(@Param("ID") Integer ID);

    Integer getSeckillSaleRange(@Param("ID") Integer ID, @Param("EntityID") Integer EntityID);

    void insertHomeConfList(List<HomeConfEntity> list);
}
