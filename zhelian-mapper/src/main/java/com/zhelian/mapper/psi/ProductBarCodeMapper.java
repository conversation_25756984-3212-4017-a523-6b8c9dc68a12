package com.zhelian.mapper.psi;

import com.zhelian.model.psi.entity.ProductBarCodeEntity;
import com.zhelian.model.psi.form.output.ProductBarCodeOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProductBarCodeMapper {

    List<ProductBarCodeOutputForm> productBarCodeList(@Param("ProductName") String ProductName);

    ProductBarCodeEntity findBarCodeByID(@Param("ID") Integer ID);

    int countBarCodeExclude(@Param("BarCode") String BarCode, @Param("ID") Integer ID, @Param("ProductID") Integer ProductID);

    int countProductBarCode(@Param("ProductID") Integer ProductID, @Param("IsMinimumUnit") Boolean IsMinimumUnit, @Param("IsDefautSendReceive") Boolean IsDefautSendReceive, @Param("ID") Integer ID, @Param("UnitID") Integer UnitID);

    void insertProductBarCode(ProductBarCodeEntity productBarCodeEntity);

    void updateProductBarCode(ProductBarCodeEntity productBarCodeEntity);

    void updateProductBarCodeByIsMinimumUnit(ProductBarCodeEntity productBarCodeEntity);

    Integer getProductByBarCode(String BarCode);

}
