package com.zhelian.mapper.psi;

import com.zhelian.model.psi.entity.InventoryProductOutboundDetailEntity;
import com.zhelian.model.psi.entity.InventoryProductOutboundEntity;
import com.zhelian.model.psi.form.output.InventoryOtherOutboundInfoOutputForm;
import com.zhelian.model.psi.form.output.InventoryProductOutboundInfoOutputForm;
import com.zhelian.model.psi.form.output.InventoryProductOutboundOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface InventoryProductOutboundMapper {
    void insertInventoryProductOutboundEntity(InventoryProductOutboundEntity inventoryProductOutboundEntity);

    void insertInventoryProductOutboundDetailEntity(List<InventoryProductOutboundDetailEntity> list);

    List<InventoryProductOutboundOutputForm> inventoryProductOutboundList(@Param("ID") String ID, @Param("EntityID") Integer EntityID, @Param("InventoryType") String InventoryType, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("EmployeeID") String EmployeeID, @Param("ProductName") String ProductName);

    InventoryProductOutboundInfoOutputForm inventoryProductOutboundInfo(String ID);

    InventoryOtherOutboundInfoOutputForm inventoryAllocationOutboundInfo(String ID);

    InventoryOtherOutboundInfoOutputForm inventoryOutboundInfo(String ID);

    InventoryOtherOutboundInfoOutputForm inventoryRefundOutboundInfo(String ID);

    InventoryOtherOutboundInfoOutputForm PurchaseRefundInfo(String ID);

    InventoryOtherOutboundInfoOutputForm inventoryTreatOutboundInfo(@Param("ID") String ID);

    void updateRemark(@Param("ID") String ID, @Param("Remark") String Remark);
}
