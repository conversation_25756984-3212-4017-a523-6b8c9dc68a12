package com.zhelian.mapper.psi;

import com.zhelian.model.psi.entity.InventoryProductInboundDetailEntity;
import com.zhelian.model.psi.entity.InventoryProductInboundEntity;
import com.zhelian.model.psi.form.output.InventoryOtherInboundInfoOutputForm;
import com.zhelian.model.psi.form.output.InventoryProductInboundInfoOutputForm;
import com.zhelian.model.psi.form.output.InventoryProductInboundOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface InventoryProductInboundMapper {
    void insertInventoryProductInboundEntity(InventoryProductInboundEntity inventoryProductInboundEntity);

    void insertInventoryProductInboundDetailEntity(List<InventoryProductInboundDetailEntity> list);

    List<InventoryProductInboundOutputForm> inventoryProductInboundList(@Param("ID") String ID, @Param("EntityID") Integer EntityID, @Param("InventoryType") String InventoryType, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("EmployeeID") String EmployeeID, @Param("ProductName") String ProductName);

    InventoryProductInboundInfoOutputForm inventoryProductInboundInfo(String ID);

    InventoryOtherInboundInfoOutputForm inventoryInboundInfo(String ID);

    InventoryOtherInboundInfoOutputForm inventoryAllocationInboundInfo(String ID);

    InventoryOtherInboundInfoOutputForm inventoryRefundInboundInfo(String ID);

    InventoryOtherInboundInfoOutputForm inventoryTreatCancelInboundInfo(String ID);
}
