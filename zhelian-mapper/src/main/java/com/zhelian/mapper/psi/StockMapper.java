package com.zhelian.mapper.psi;

import com.zhelian.model.psi.entity.StockEntity;
import com.zhelian.model.psi.form.output.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface StockMapper {
    void insertStockEntity(StockEntity stockEntity);

    void updateStockEntity(StockEntity stockEntity);

    StockEntity stockInfo(StockEntity stockEntity);

    List<StockOutputForm> stockList(@Param("ProductName") String ProductName, @Param("EntityID") Integer EntityID, @Param("EmployeeID") String EmployeeID, @Param("PCategoryID") Integer PCategoryID, @Param("IsDisplayZero") Boolean IsDisplayZero, @Param("IsDisplayInvalid") Boolean IsDisplayInvalid);

    List<StockEasyExcelOutputForm> stockListEasyExcel(@Param("ProductName") String ProductName, @Param("EntityID") Integer EntityID, @Param("EmployeeID") String EmployeeID, @Param("PCategoryID") Integer PCategoryID, @Param("IsDisplayZero") Boolean IsDisplayZero, @Param("IsDisplayInvalid") Boolean IsDisplayInvalid);

    List<ProductEntityStockOutputForm> productEntity(@Param("ProductID") Integer ProductID, @Param("EmployeeID") String EmployeeID, @Param("IsDisplayZero") Boolean IsDisplayZero);

    List<EntityStockOutputForm> entityProductList(@Param("ProductName") String ProductName, @Param("EntityID") Integer EntityID, @Param("PCategoryID") Integer PCategoryID);

    List<EntityStockOutputForm> entityProductListAll(@Param("ProductName") String ProductName, @Param("EntityID") Integer EntityID, @Param("PCategoryID") Integer PCategoryID);

    List<EntityStockOutputForm> entityProductDeliveryPrice(@Param("ProductName") String ProductName, @Param("EntityID") Integer EntityID, @Param("PCategoryID") Integer PCategoryID);

    List<InventoryCheckProductOutputForm> inventoryCheckProductList(@Param("ProductName") String ProductName, @Param("EntityID") Integer EntityID, @Param("PCategoryID") Integer PCategoryID);

    List<ProductStockOutputForm> ProductStock(@Param("EntityID") Integer EntityID, @Param("array") Integer[] ProductArray);

    List<InventoryDetailOutputForm> inventoryDetail(@Param("ProductName") String ProductName, @Param("EntityID") Integer EntityID, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("InventoryType") String InventoryType, @Param("EmployeeID") String EmployeeID, @Param("PCategoryID") Integer PCategoryID, @Param("ProductID") Integer ProductID);

    List<Object> inventoryDetailExcel(@Param("ProductName") String ProductName, @Param("EntityID") Integer EntityID, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("InventoryType") String InventoryType, @Param("EmployeeID") String EmployeeID, @Param("PCategoryID") Integer PCategoryID, @Param("ProductID") Integer ProductID);

    List<InventoryDetailOutputEasyExcelForm> inventoryDetailEasyExcel(@Param("ProductName") String ProductName, @Param("EntityID") Integer EntityID, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("InventoryType") String InventoryType, @Param("EmployeeID") String EmployeeID, @Param("PCategoryID") Integer PCategoryID, @Param("ProductID") Integer ProductID);

    BigDecimal productDispatchPrice(@Param("EntityID") Integer EntityID, @Param("ProductID") Integer ProductID);

    List<EntityStockCategoryOutputForm> entityCategoryProductList(@Param("ProductName") String ProductName, @Param("EntityID") Integer EntityID);

    List<EntityStockTopCategoryOutputForm> getTopProductList(List<Integer> list);

    List<EntityPrepayOutputForm> warehouseList(@Param("EmployeeID") String EmployeeID);

    Integer productQuantity(@Param("EntityID") Integer EntityID, @Param("ProductID") Integer ProductID);
}
