package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SaleProductSchemeHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleProductSchemeHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.output.SaleProductSchemeHandlerCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleProductSchemeHandlerCommissionMapper {

    List<SaleProductSchemeHandlerCommissionOutputForm> allSaleProductSchemeHandlerCommission(@Param("EntityID") Integer EntityID);

    void deleteSaleProductSchemeHandler(@Param("EntityID") Integer EntityID);

    void deleteSaleProductSchemeHandlerJobType(@Param("EntityID") Integer EntityID);

    void insertSaleProductSchemeHandlerCommission(List<SaleProductSchemeHandlerCommissionEntity> list);

    void insertSaleProductSchemeHandlerJobTypeCommission(List<SaleProductSchemeHandlerJobTypeCommissionEntity> list);
}
