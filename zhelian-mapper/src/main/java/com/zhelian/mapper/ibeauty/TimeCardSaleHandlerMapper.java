package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.TimeCardSaleHandlerEntity;
import com.zhelian.model.ibeauty.entity.TimeCardSaleHandlerJobTypeEntity;
import com.zhelian.model.ibeauty.form.output.SaleHandlerOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;


@Repository
public interface TimeCardSaleHandlerMapper {


    List<SaleHandlerOutputForm> allTimeCardSaleHandler(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("Active") Boolean Active);

    Integer getMaxSequence();

    void createTimeCardSaleHandler(TimeCardSaleHandlerEntity timeCardSaleHandlerEntity);

    void insertTimeCardSaleHandlerJobType(List<TimeCardSaleHandlerJobTypeEntity> list);

    void updateTimeCardSaleHandler(TimeCardSaleHandlerEntity timeCardSaleHandlerEntity);

    void deleteSaleHandlerJobType(@Param("ID") Integer ID);

    LinkedList<Integer> getIDOrderBySequenceASC(Integer ID);

    void updateSequence(List<TimeCardSaleHandlerEntity> list);
}
