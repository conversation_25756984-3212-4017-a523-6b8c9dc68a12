package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SavingCardProjectTreatHandlerEntity;
import com.zhelian.model.ibeauty.entity.SavingCardProjectTreatHandlerJobTypeEntity;
import com.zhelian.model.ibeauty.form.output.TreatHandlerOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;


@Repository
public interface SavingCardProjectTreatHandlerMapper {


    List<TreatHandlerOutputForm> allSavingCardProjectTreatHandler(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("Active") Boolean Active);

    Integer getMaxSequence();

    void createSavingCardProjectTreatHandler(SavingCardProjectTreatHandlerEntity savingCardProjectTreatHandlerEntity);

    void insertHandlerJobType(List<SavingCardProjectTreatHandlerJobTypeEntity> list);

    void updateSavingCardProjectTreatHandler(SavingCardProjectTreatHandlerEntity savingCardProjectTreatHandlerEntity);

    void deleteTreatHandlerJobType(@Param("ID") Integer ID);

    LinkedList<Integer> getIDOrderBySequenceASC(Integer ID);

    void updateSequence(List<SavingCardProjectTreatHandlerEntity> list);
}
