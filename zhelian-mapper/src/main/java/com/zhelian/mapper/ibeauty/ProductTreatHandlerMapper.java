package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.ProductTreatHandlerEntity;
import com.zhelian.model.ibeauty.entity.ProductTreatHandlerJobTypeEntity;
import com.zhelian.model.ibeauty.form.output.TreatHandlerOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;


@Repository
public interface ProductTreatHandlerMapper {


    List<TreatHandlerOutputForm> allProductTreatHandler(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("Active") Boolean Active);

    Integer getMaxSequence();

    void createProductTreatHandler(ProductTreatHandlerEntity productTreatHandlerEntity);

    void insertHandlerJobType(List<ProductTreatHandlerJobTypeEntity> list);

    void updateProductTreatHandler(ProductTreatHandlerEntity productTreatHandlerEntity);

    void deleteTreatHandlerJobType(@Param("ID") Integer ID);

    LinkedList<Integer> getIDOrderBySequenceASC(Integer ID);

    void updateSequence(List<ProductTreatHandlerEntity> list);
}
