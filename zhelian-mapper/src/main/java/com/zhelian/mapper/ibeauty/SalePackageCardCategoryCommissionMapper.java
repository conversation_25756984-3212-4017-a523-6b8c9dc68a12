package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SalePackageCardCategoryCommissionEntity;
import com.zhelian.model.ibeauty.entity.SalePackageCardCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.output.SalePackageCardCategoryCommissionOutputForm;
import com.zhelian.model.ibeauty.form.output.SalePackageCardCategoryOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SalePackageCardCategoryCommissionMapper {

    List<SalePackageCardCategoryCommissionOutputForm> allSalePackageCardCategoryCommission(@Param("EntityID") Integer EntityID);

    void updateSalePackageCardCommissionScheme(SalePackageCardCommissionSchemeEntity salePackageCardCommissionSchemeEntity);

    void insertSalePackageCardCategoryCommissionEntity(List<SalePackageCardCategoryCommissionEntity> list);

    SalePackageCardCategoryOutputForm salePackageCardCategoryCommission(@Param("EntityID") Integer EntityID);

    void deleteSalePackageCardCategoryCommission(Integer EntityID);
}
