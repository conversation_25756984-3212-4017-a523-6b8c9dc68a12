package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.TreatTimeCardProjectCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.output.TreatProjectCommissionSchemeOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatTimeCardCommissionSchemeMapper {

    List<TreatProjectCommissionSchemeOutputForm> treatTimeCardCommissionSchemeList(@Param("Name") String Name);

    void createTreatTimeCardCommissionScheme(TreatTimeCardProjectCommissionSchemeEntity treatTimeCardProjectCommissionSchemeEntity);

    Integer getTreatTimeCardCommissionScheme(@Param("EntityID") Integer EntityID);

    void deleteTreatTimeCardCommissionScheme(@Param("EntityID") Integer EntityID);

}
