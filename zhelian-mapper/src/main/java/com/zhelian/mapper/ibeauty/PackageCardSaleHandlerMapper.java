package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.PackageCardSaleHandlerEntity;
import com.zhelian.model.ibeauty.entity.PackageCardSaleHandlerJobTypeEntity;
import com.zhelian.model.ibeauty.form.output.SaleHandlerOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;


@Repository
public interface PackageCardSaleHandlerMapper {


    List<SaleHandlerOutputForm> allPackageCardSaleHandler(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("Active") Boolean Active);

    Integer getMaxSequence();

    void createPackageCardSaleHandler(PackageCardSaleHandlerEntity packageCardSaleHandlerEntity);

    void insertPackageCardSaleHandlerJobType(List<PackageCardSaleHandlerJobTypeEntity> list);

    void updatePackageCardSaleHandler(PackageCardSaleHandlerEntity packageCardSaleHandlerEntity);

    void deleteSaleHandlerJobType(@Param("ID") Integer ID);

    LinkedList<Integer> getIDOrderBySequenceASC(Integer ID);

    void updateSequence(List<PackageCardSaleHandlerEntity> list);
}
