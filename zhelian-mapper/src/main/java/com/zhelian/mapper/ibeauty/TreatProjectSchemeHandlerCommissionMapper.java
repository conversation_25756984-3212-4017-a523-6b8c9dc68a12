package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.TreatProjectSchemeHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.TreatProjectSchemeHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.output.TreatProjectHandlerCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatProjectSchemeHandlerCommissionMapper {

    List<TreatProjectHandlerCommissionOutputForm> allTreatProjectSchemeHandlerCommission(@Param("EntityID") Integer EntityID);

    void deleteTreatProjectSchemeHandler(@Param("EntityID") Integer EntityID);

    void deleteTreatProjectSchemeHandlerJobType(@Param("EntityID") Integer EntityID);

    void updateTreatProjectSchemeHandlerCommission(List<TreatProjectSchemeHandlerCommissionEntity> list);

    void insertTreatProjectSchemeHandlerJobTypeCommission(List<TreatProjectSchemeHandlerJobTypeCommissionEntity> list);
}
