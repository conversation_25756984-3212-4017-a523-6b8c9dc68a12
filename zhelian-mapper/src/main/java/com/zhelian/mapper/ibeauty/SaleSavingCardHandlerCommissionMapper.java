package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SaleSavingCardHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleSavingCardHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.output.SaleSavingCardHandlerCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleSavingCardHandlerCommissionMapper {

    List<SaleSavingCardHandlerCommissionOutputForm> allSaleSavingCardHandlerCommission(@Param("EntityID") Integer EntityID, @Param("SavingCardID") Integer SavingCardID);

    void deleteSaleSavingCardHandler(@Param("EntityID") Integer EntityID, @Param("SavingCardID") Integer SavingCardID);

    void deleteSaleSavingCardHandlerJobType(@Param("EntityID") Integer EntityID, @Param("SavingCardID") Integer SavingCardID);

    void updateSaleSavingCardHandlerCommission(List<SaleSavingCardHandlerCommissionEntity> list);

    void insertSaleSavingCardHandlerJobTypeCommission(List<SaleSavingCardHandlerJobTypeCommissionEntity> list);

    void deleteSaleSavingCardHandlerCommissionByEntity(@Param("EntityID") Integer EntityID);

    void deleteSaleSavingCardHandlerJobTypeCommissionByEntity(@Param("EntityID") Integer EntityID);
}
