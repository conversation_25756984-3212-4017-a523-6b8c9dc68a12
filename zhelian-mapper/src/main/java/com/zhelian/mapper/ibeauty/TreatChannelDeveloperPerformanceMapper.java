package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.service.TreatChannelEmployeePerformanceGoodsEntity;
import com.zhelian.model.ibeauty.service.TreatChannelEmployeePerformancePackageCardGoodsEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TreatChannelDeveloperPerformanceMapper {


    //创建渠道非套餐卡业绩
    void createTreatDeveloperChannelPerformanceProduct(List<TreatChannelEmployeePerformanceGoodsEntity> list);

    void createTreatDeveloperChannelPerformanceProject(List<TreatChannelEmployeePerformanceGoodsEntity> list);

    void createTreatDeveloperChannelPerformanceGeneralCard(List<TreatChannelEmployeePerformanceGoodsEntity> list);

    void createTreatDeveloperChannelPerformanceTimeCard(List<TreatChannelEmployeePerformanceGoodsEntity> list);

    void createTreatDeveloperChannelPerformanceSavingCard(List<TreatChannelEmployeePerformanceGoodsEntity> list);

    //创建门店套餐卡业绩
    void createTreatDeveloperChannelPerformancePackageCardProduct(List<TreatChannelEmployeePerformancePackageCardGoodsEntity> list);

    void createTreatDeveloperChannelPerformancePackageCardProject(List<TreatChannelEmployeePerformancePackageCardGoodsEntity> list);

    void createTreatDeveloperChannelPerformancePackageCardGeneralCard(List<TreatChannelEmployeePerformancePackageCardGoodsEntity> list);

    void createTreatDeveloperChannelPerformancePackageCardTimeCard(List<TreatChannelEmployeePerformancePackageCardGoodsEntity> list);

    void createTreatDeveloperChannelPerformancePackageCardSavingCard(List<TreatChannelEmployeePerformancePackageCardGoodsEntity> list);


    void deleteTreatDeveloperChannelPerformanceProduct(String ID);

    void deleteTreatDeveloperChannelPerformanceProject(String ID);

    void deleteTreatDeveloperChannelPerformanceGeneralCard(String ID);

    void deleteTreatDeveloperChannelPerformanceTimeCard(String ID);

    void deleteTreatDeveloperChannelPerformanceSavingCard(String ID);

    void deleteTreatDeveloperChannelPerformancePackageCardProduct(String ID);

    void deleteTreatDeveloperChannelPerformancePackageCardProject(String ID);

    void deleteTreatDeveloperChannelPerformancePackageCardGeneralCard(String ID);

    void deleteTreatDeveloperChannelPerformancePackageCardTimeCard(String ID);

    void deleteTreatDeveloperChannelPerformancePackageCardSavingCard(String ID);
}
