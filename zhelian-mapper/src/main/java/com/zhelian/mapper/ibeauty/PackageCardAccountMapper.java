package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.PackageCardAccountEntity;
import com.zhelian.model.ibeauty.form.input.TreatPackageCardAccountForm;
import com.zhelian.model.ibeauty.form.output.*;
import com.zhelian.model.micromall.form.output.MicromallPackageCardAccountOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;


@Repository
public interface PackageCardAccountMapper {

    void createPackageCardAccount(PackageCardAccountEntity packageCardAccountEntity);

    void updatePackageCardAccountActiveFalse(@Param("SaleBillID") String SaleBillID);

    //套餐卡账户列表
    List<PackageCardAccountOutputForm> packageCardAccount(TreatPackageCardAccountForm form);

    //套餐卡账户明细
    PackageCardAccountDetailsOutputForm packageCardAccountDetails(@Param("PackageCardAccountID") Integer PackageCardAccountID, @Param("EntityID") Integer EntityID, @Param("BillDate") String BillDate, @Param("ValidDayName") String ValidDayName);

    List<PackageCardAccountOutputForm> arrearPackageCardAccount(@Param("CustomerID") String CustomerID, @Param("Name") String Name, @Param("EntityID") Integer EntityID);

    List<PackageCardProductAccountOutputForm> arrearPackageCardProductAccount(@Param("PackageCardAccountID") Integer PackageCardAccountID);

    List<PackageCardProjectAccountOutputForm> arrearPackageCardProjectAccount(@Param("PackageCardAccountID") Integer PackageCardAccountID);

    List<GeneralCardAccountOutputForm> arrearPackageCardGeneralCardAccount(@Param("PackageCardAccountID") Integer PackageCardAccountID);

    List<TimeCardAccountOutputForm> arrearPackageCardTimeCardAccount(@Param("PackageCardAccountID") Integer PackageCardAccountID, @Param("BillDate") String BillDate);

    List<SavingCardAccountForm> arrearPackageCardSavingCardAccount(@Param("PackageCardAccountID") Integer PackageCardAccountID);

    //更新套餐卡账户支付金额
    void updatePackageCardAccountTotalAmount(@Param("SaleBillPackageCardID") Integer SaleBillPackageCardID);

    PackageCardAccountEntity getPackageCardAccount(@Param("PackageCardAccountID") Integer PackageCardAccountID);

    String getPackageCardAccountValidDayName(@Param("PackageCardAccountID") Integer PackageCardAccountID);

    void updateArrearPackageCardAccount(PackageCardAccountEntity packageCardAccountEntity);

    List<PackageCardAccountOutputForm> getPostPonePackageCardAccount(@Param("CustomerID") String CustomerID, @Param("Name") String Name);

    void updatePackageCardAccountValidDate(PackageCardAccountEntity packageCardAccountEntity);

    List<PackageCardAccountOutputForm> getTranserPackageCardAccount(@Param("CustomerID") String CustomerID, @Param("Name") String Name);
    List<PackageCardAccountOutputForm> getRefundPackageCardAccount(@Param("CustomerID") String CustomerID, @Param("Name") String Name);

    List<PackageCardProductAccountOutputForm> transerPackageCardProductAccount(@Param("PackageCardAccountID") Integer PackageCardAccountID);
    List<PackageCardProductAccountOutputForm> refundPackageCardProductAccount(@Param("PackageCardAccountID") Integer PackageCardAccountID);

    List<PackageCardProjectAccountOutputForm> transerPackageCardProjectAccount(@Param("PackageCardAccountID") Integer PackageCardAccountID);
    List<PackageCardProjectAccountOutputForm> refundPackageCardProjectAccount(@Param("PackageCardAccountID") Integer PackageCardAccountID);

    List<GeneralCardAccountOutputForm> transerPackageCardGeneralCardAccount(@Param("PackageCardAccountID") Integer PackageCardAccountID);
    List<GeneralCardAccountOutputForm> refundPackageCardGeneralCardAccount(@Param("PackageCardAccountID") Integer PackageCardAccountID);

    List<TimeCardAccountOutputForm> transerPackageCardTimeCardAccount(@Param("PackageCardAccountID") Integer PackageCardAccountID);
    List<TimeCardAccountOutputForm> refundPackageCardTimeCardAccount(@Param("PackageCardAccountID") Integer PackageCardAccountID);

    List<SavingCardAccountForm> transerPackageCardSavingCardAccount(@Param("PackageCardAccountID") Integer PackageCardAccountID);
    List<SavingCardAccountForm> refundPackageCardSavingCardAccount(@Param("PackageCardAccountID") Integer PackageCardAccountID);

    void updatePackageCardAccountPrice(@Param("ID") Integer ID, @Param("TotalAmount") BigDecimal TotalAmount, @Param("PayAmount") BigDecimal PayAmount, @Param("SavingCardDeductionAmount") BigDecimal SavingCardDeductionAmount, @Param("SavingCardDeductionLargessAmount") BigDecimal SavingCardDeductionLargessAmount);

    List<CustomerPackageCardAccountOutputForm> customerPackageCardAccount(@Param("CustomerID") String CustomerID, @Param("Active") Boolean Active, @Param("Name") String Name, @Param("ID") Integer ID);

    List<CustomerPackageCardProductAccountOutputForm> custPackageCardProductAccount(@Param("PackageCardAccountID") Integer PackageCardAccountID);

    List<CustomerPackageCardProjectAccountOutputForm> custPackageCardProjectAccount(@Param("PackageCardAccountID") Integer PackageCardAccountID);

    List<CustomerGeneralCardAccountOutputForm> custPackageCardGeneralCardAccount(@Param("PackageCardAccountID") Integer PackageCardAccountID);

    List<CustomerTimeCardAccountOutputForm> custPackageCardTimeCardAccount(@Param("PackageCardAccountID") Integer PackageCardAccountID);

    List<CustomerSavingCardAccountForm> custPackageCardSavingCardAccount(@Param("PackageCardAccountID") Integer PackageCardAccountID);

    List<MicromallPackageCardAccountOutputForm> micromallPackageCardAccount(@Param("CustomerID") Integer CustomerID, @Param("Active") Boolean Active, @Param("Name") String Name);

    CustomerPackageCardAccountSumOutputForm customerPackageCardAccountSum(@Param("CustomerID") String CustomerID, @Param("Active") Boolean Active, @Param("Name") String Name, @Param("ID") Integer ID);

    void updatePackageCardRemark(@Param("SaleBillGoodID") Integer SaleBillGoodID, @Param("Remark") String Remark);

    void updatePackageCardBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    void updateArrearPackageCardAccountTotalAmount(@Param("SaleBillPackageCardID") Integer SaleBillPackageCardID);

    void updatePackageCardAccountArrear(PackageCardAccountEntity packageCardAccountEntity);
}
