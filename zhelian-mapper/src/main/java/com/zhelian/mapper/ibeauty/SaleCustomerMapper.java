package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.form.output.CustomerShortInfoOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleCustomerMapper {


    List<CustomerShortInfoOutputForm> saleCustomer(@Param("Name") String Name, @Param("EntityID") Integer EntityID);

    List<CustomerShortInfoOutputForm> saleAllCustomer(@Param("Name") String Name);

}
