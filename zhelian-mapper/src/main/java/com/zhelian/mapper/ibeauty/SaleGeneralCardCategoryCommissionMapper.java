package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SaleGeneralCardCategoryCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleGeneralCardCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.output.SaleGeneralCardCategoryCommissionOutputForm;
import com.zhelian.model.ibeauty.form.output.SaleGeneralCardCategoryOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleGeneralCardCategoryCommissionMapper {

    List<SaleGeneralCardCategoryCommissionOutputForm> allSaleGeneralCardCategoryCommission(@Param("EntityID") Integer EntityID);

    void updateSaleGeneralCardCommissionScheme(SaleGeneralCardCommissionSchemeEntity saleGeneralCardCommissionSchemeEntity);

    void insertSaleGeneralCardCategoryCommissionEntity(List<SaleGeneralCardCategoryCommissionEntity> list);

    SaleGeneralCardCategoryOutputForm saleGeneralCardCategoryCommission(@Param("EntityID") Integer EntityID);

    void deleteSaleGeneralCardCategoryCommission(Integer EntityID);
}
