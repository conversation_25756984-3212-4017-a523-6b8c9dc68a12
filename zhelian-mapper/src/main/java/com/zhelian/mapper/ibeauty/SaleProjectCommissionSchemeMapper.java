package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SaleProjectCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.output.SaleProductCommissionSchemeOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleProjectCommissionSchemeMapper {

    List<SaleProductCommissionSchemeOutputForm> saleProjectCommissionSchemeList(@Param("Name") String Name);

    void createSaleProjectCommissionScheme(SaleProjectCommissionSchemeEntity saleProjectCommissionSchemeEntity);

    Integer getSaleProjectCommissionScheme(@Param("EntityID") Integer EntityID);

    void deleteSaleProjectCommissionScheme(@Param("EntityID") Integer EntityID);

}
