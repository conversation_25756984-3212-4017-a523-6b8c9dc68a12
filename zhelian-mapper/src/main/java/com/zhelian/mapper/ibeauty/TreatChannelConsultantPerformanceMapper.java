package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.service.TreatChannelEmployeePerformanceGoodsEntity;
import com.zhelian.model.ibeauty.service.TreatChannelEmployeePerformancePackageCardGoodsEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TreatChannelConsultantPerformanceMapper {


    //创建渠道非套餐卡业绩
    void createTreatConsultantChannelPerformanceProduct(List<TreatChannelEmployeePerformanceGoodsEntity> list);

    void createTreatConsultantChannelPerformanceProject(List<TreatChannelEmployeePerformanceGoodsEntity> list);

    void createTreatConsultantChannelPerformanceGeneralCard(List<TreatChannelEmployeePerformanceGoodsEntity> list);

    void createTreatConsultantChannelPerformanceTimeCard(List<TreatChannelEmployeePerformanceGoodsEntity> list);

    void createTreatConsultantChannelPerformanceSavingCard(List<TreatChannelEmployeePerformanceGoodsEntity> list);

    //创建门店套餐卡业绩
    void createTreatConsultantChannelPerformancePackageCardProduct(List<TreatChannelEmployeePerformancePackageCardGoodsEntity> list);

    void createTreatConsultantChannelPerformancePackageCardProject(List<TreatChannelEmployeePerformancePackageCardGoodsEntity> list);

    void createTreatConsultantChannelPerformancePackageCardGeneralCard(List<TreatChannelEmployeePerformancePackageCardGoodsEntity> list);

    void createTreatConsultantChannelPerformancePackageCardTimeCard(List<TreatChannelEmployeePerformancePackageCardGoodsEntity> list);

    void createTreatConsultantChannelPerformancePackageCardSavingCard(List<TreatChannelEmployeePerformancePackageCardGoodsEntity> list);


    void deleteTreatConsultantChannelPerformanceProduct(String ID);

    void deleteTreatConsultantChannelPerformanceProject(String ID);

    void deleteTreatConsultantChannelPerformanceGeneralCard(String ID);

    void deleteTreatConsultantChannelPerformanceTimeCard(String ID);

    void deleteTreatConsultantChannelPerformanceSavingCard(String ID);

    void deleteTreatConsultantChannelPerformancePackageCardProduct(String ID);

    void deleteTreatConsultantChannelPerformancePackageCardProject(String ID);

    void deleteTreatConsultantChannelPerformancePackageCardGeneralCard(String ID);

    void deleteTreatConsultantChannelPerformancePackageCardTimeCard(String ID);

    void deleteTreatConsultantChannelPerformancePackageCardSavingCard(String ID);
}
