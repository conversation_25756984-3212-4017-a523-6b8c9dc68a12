package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.*;
import com.zhelian.model.ibeauty.form.output.*;
import com.zhelian.model.ibeauty.service.SaleBillArrearGoodsAccountEntity;
import com.zhelian.model.ibeauty.service.SaleBillRefundGoodsAccountEntity;
import com.zhelian.model.miniprogram.form.output.SignatureOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;


@Repository
public interface SaleBillMapper {

    void createSaleBill(SaleBillEntity saleBillEntity);

    void createSaleBillProduct(SaleBillProductEntity saleBillProductEntity);

    void createSaleBillProject(SaleBillProjectEntity saleBillProjectEntity);

    void createSaleBillGeneralCard(SaleBillGeneralCardEntity saleBillGeneralCardEntity);

    void createSaleBillTimeCard(SaleBillTimeCardEntity saleBillTimeCardEntity);

    void createSaleBillSavingCard(SaleBillSavingCardEntity saleBillSavingCardEntity);

    void createSaleBillPackageCard(SaleBillPackageCardEntity saleBillPackageCardEntity);

    void createSaleBillPackageCardProduct(SaleBillPackageCardProductEntity saleBillPackageCardProductEntity);

    void createSaleBillPackageCardProject(SaleBillPackageCardProjectEntity saleBillPackageCardProjectEntity);

    void createSaleBillPackageCardGeneralCard(SaleBillPackageCardGeneralCardEntity saleBillPackageCardGeneralCardEntity);

    void createSaleBillPackageCardTimeCard(SaleBillPackageCardTimeCardEntity saleBillPackageCardTimeCardEntity);

    void createSaleBillPackageCardSavingCard(SaleBillPackageCardSavingCardEntity saleBillPackageCardSavingCardEntity);

    List<SaleBillListOutputForm> saleBillList(@Param("EntityID") Integer EntityID, @Param("Name") String Name, @Param("SaleBillID") String SaleBillID, @Param("StartDate") String StartDate,
                                              @Param("EndDate") String EndDate, @Param("BillStatus") String BillStatus, @Param("BillType") String BillType, @Param("Channel") String Channel, @Param("IsNoHaveHandler") Boolean IsNoHaveHandler);

    SaleBillInfoOutputForm saleBillInfo(@Param("SaleBillID") String SaleBillID);

    int countArrearBillCantCancel(@Param("SaleBillID") String SaleBillID);

    int countSaleBillCantCancel(@Param("SaleBillID") String SaleBillID);

    List<SaleBillProductInfoOutputForm> saleBillProductInfo(@Param("SaleBillID") String SaleBillID);

    List<SaleBillProjectInfoOutputForm> saleBillProjectInfo(@Param("SaleBillID") String SaleBillID);

    List<SaleBillGeneralCardInfoOutputForm> saleBillGeneralCardInfo(@Param("SaleBillID") String SaleBillID);

    List<SaleBillTimeCardInfoOutputForm> saleBillTimeCardInfo(@Param("SaleBillID") String SaleBillID);

    List<SaleBillSavingCardInfoOutputForm> saleBillSavingCardInfo(@Param("SaleBillID") String SaleBillID);

    List<SaleBillPackageCardInfoOutputForm> saleBillPackageCardInfo(@Param("SaleBillID") String SaleBillID);

    List<SaleBillPackageCardProductOutputForm> saleBillPackageCardProduct(@Param("SaleBillID") String SaleBillID);

    List<SaleBillPackageCardProjectOutputForm> saleBillPackageCardProject(@Param("SaleBillID") String SaleBillID);

    List<SaleBillPackageCardGeneralCardOutputForm> saleBillPackageCardGeneralCard(@Param("SaleBillID") String SaleBillID);

    List<SaleBillPackageCardTimeCardOutputForm> saleBillPackageCardTimeCard(@Param("SaleBillID") String SaleBillID);

    List<SaleBillPackageCardSavingCardOutputForm> saleBillPackageCardSavingCard(@Param("SaleBillID") String SaleBillID);

    List<SaleBillPayInfoOutputForm> getSaleBillPay(@Param("SaleBillID") String SaleBillID);

    List<SaleBillPaySavingCardDeductionInfoOutputForm> getSaleBillPaySavingCardDeduction(@Param("SaleBillID") String SaleBillID);

    List<SaleBillRepayBillInfoOutputForm> getRepayBill(@Param("SaleBillID") String SaleBillID);

    SaleBillEntity getSaleBill(@Param("SaleBillID") String SaleBillID);

    String getTreatBillIDBySaleBillID(@Param("SaleBillID") String SaleBillID);

    Boolean getBillStockIntegrityStatus(@Param("SaleBillID") String SaleBillID);

    void updateSaleBillStatus(@Param("SaleBillID") String SaleBillID, @Param("BillStatus") String BillStatus);

    List<SaleBillPackageCardEmployeePerformanceCommissionOutputForm> getSaleBillPackageCard(@Param("SaleBillID") String SaleBillID);

    void updateRemark(@Param("SaleBillID") String SaleBillID, @Param("Remark") String Remark);

    List<SaleBillArrearGoodsAccountEntity> getSaleBillArrearProduct(@Param("SaleBillID") String SaleBillID, @Param("ID") Integer ID);

    List<SaleBillArrearGoodsAccountEntity> getSaleBillArrearProject(@Param("SaleBillID") String SaleBillID, @Param("ID") Integer ID);

    List<SaleBillArrearGoodsAccountEntity> getSaleBillArrearGeneralCard(@Param("SaleBillID") String SaleBillID, @Param("ID") Integer ID);

    List<SaleBillArrearGoodsAccountEntity> getSaleBillArrearTimeCard(@Param("SaleBillID") String SaleBillID, @Param("ID") Integer ID);

    List<SaleBillArrearGoodsAccountEntity> getSaleBillArrearSavingCard(@Param("SaleBillID") String SaleBillID, @Param("ID") Integer ID);

    List<SaleBillArrearGoodsAccountEntity> getSaleBillArrearPackageCardProduct(@Param("SaleBillID") String SaleBillID, @Param("ID") Integer ID);

    List<SaleBillArrearGoodsAccountEntity> getSaleBillArrearPackageCardProject(@Param("SaleBillID") String SaleBillID, @Param("ID") Integer ID);

    List<SaleBillArrearGoodsAccountEntity> getSaleBillArrearPackageCardGeneralCard(@Param("SaleBillID") String SaleBillID, @Param("ID") Integer ID);

    List<SaleBillArrearGoodsAccountEntity> getSaleBillArrearPackageCardTimeCard(@Param("SaleBillID") String SaleBillID, @Param("ID") Integer ID);

    List<SaleBillArrearGoodsAccountEntity> getSaleBillArrearPackageCardSavingCard(@Param("SaleBillID") String SaleBillID, @Param("ID") Integer ID);

    List<SaleBillRefundGoodsAccountEntity> getSaleBillRefundProduct(String SaleBillID);

    List<SaleBillRefundGoodsAccountEntity> getSaleBillRefundProject(String SaleBillID);

    List<SaleBillRefundGoodsAccountEntity> getSaleBillRefundGeneralCard(String SaleBillID);

    List<SaleBillRefundGoodsAccountEntity> getSaleBillRefundTimeCard(String SaleBillID);

    List<SaleBillRefundGoodsAccountEntity> getSaleBillRefundSavingCard(String SaleBillID);

    //快速开单绑定顾客开始
    void updateSaleBillCustomerIDBySaleBillID(@Param("SaleBillID") String SaleBillID, @Param("CustomerID") Integer CustomerID);

    void updateCustomerProductAccountBySaleBillID(@Param("SaleBillID") String SaleBillID, @Param("CustomerID") Integer CustomerID);

    void updateCustomerProjectAccountBySaleBillID(@Param("SaleBillID") String SaleBillID, @Param("CustomerID") Integer CustomerID);

    void updateTreatBillCustomerIDBySaleBillIDAndProductAccount(@Param("SaleBillID") String SaleBillID, @Param("CustomerID") Integer CustomerID);

    void updateTreatBillCustomerIDBySaleBillIDAndProjectAccount(@Param("SaleBillID") String SaleBillID, @Param("CustomerID") Integer CustomerID);
    //快速开单绑定顾客结束

    List<SaleBillListOutputForm> miniSaleBillList(@Param("EntityID") Integer EntityID, @Param("Name") String Name, @Param("StartDate") String StartDate,
                                                  @Param("EndDate") String EndDate, @Param("BillStatus") String BillStatus, @Param("BillType") String BillType, @Param("Channel") String Channel, @Param("IsNoHaveHandler") Boolean IsNoHaveHandler);

    ReceiptConfigEntity getReceiptConfig(Integer EntityID);

    void addSaleBillAttachment(SaleBillAttachmentEntity saleBillAttachmentEntity);

    void addTreatBillAttachment(TreatBillAttachmentEntity treatBillAttachmentEntity);

    void deleteSaleBillAttachment(@Param("ID") Integer ID);

    void deleteTreatBillAttachment(@Param("ID") Integer ID);

    String getSaleBillAttachmentByID(@Param("ID") Integer ID);

    String getTreatBillAttachmentByID(@Param("ID") Integer ID);

    List<SaleBillAttachmentForm> getSaleBillAttachment(@Param("SaleBillID") String SaleBillID);

    List<TreatBillAttachmentForm> getTreatBillAttachment(@Param("TreatBillID") String TreatBillID);

    void createSaleBillCancelProcess(SaleBillCancelProcessEntity saleBillCancelProcessEntity);

    void addSignature(SaleBillSignatureEntity saleBillSignatureEntity);

    SignatureOutputForm getSaleBillSignatureByID(@Param("ID") String ID);

    void deleteSaleBillSignature(@Param("ID") String ID);

    void updateSaleBillEntity(SaleBillEntity saleBillEntity);

    CustomerEntityEntity getCustomerEntity(@Param("ID") Integer ID);

    Integer getCustomerEntityID(@Param("ID") Integer ID, @Param("EntityID") Integer EntityID);

    List<SaleBillProductEntity> getSaleProductByBillID(@Param("BillID") String BillID);

    List<SaleBillProjectEntity> getSaleProjectByBillID(@Param("BillID") String BillID);

    List<SaleBillGeneralCardEntity> getSaleGeneralCardByBillID(@Param("BillID") String BillID);

    List<SaleBillTimeCardEntity> getSaleTimeCardByBillID(@Param("BillID") String BillID);

    List<SaleBillSavingCardEntity> getSaleSavingCardByBillID(@Param("BillID") String BillID);

    List<SaleBillPackageCardEntity> getPackageCardByBillID(@Param("BillID") String BillID);

    List<SaleBillPackageCardProjectOutputForm> getSaleBillPackageCardProject(@Param("SaleBillID") String SaleBillID, @Param("SaleBillPackageCardID") Integer SaleBillPackageCardID);

    List<SaleBillPackageCardProductOutputForm> getSaleBillPackageCardProduct(@Param("SaleBillID") String SaleBillID, @Param("SaleBillPackageCardID") Integer SaleBillPackageCardID);

    List<SaleBillPackageCardGeneralCardOutputForm> getSaleBillPackageCardGeneralCard(@Param("SaleBillID") String SaleBillID, @Param("SaleBillPackageCardID") Integer SaleBillPackageCardID);

    List<SaleBillPackageCardTimeCardOutputForm> getSaleBillPackageCardTimeCard(@Param("SaleBillID") String SaleBillID, @Param("SaleBillPackageCardID") Integer SaleBillPackageCardID);

    List<SaleBillPackageCardSavingCardOutputForm> getSaleBillPackageCardSavingCard(@Param("SaleBillID") String SaleBillID, @Param("SaleBillPackageCardID") Integer SaleBillPackageCardID);

    int countRechargeBillCantCancel(@Param("BillID") String BillID);

    List<SaleBillSavingCardEntity> getSaleBillSavingCard(@Param("BillID") String BillID);

    Boolean getSavingCardIntegrityStatus(@Param("BillID") String BillID);

    void updateSaleBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    void updatePayMethod(@Param("ID") Integer ID, @Param("PayMethodID") Integer PayMethodID);

    void updateSaleBill(SaleBillEntity saleBillEntity);

    void deleteSaleBillProduct(@Param("BillID") String BillID);

    void deleteSaleBillProject(@Param("BillID") String BillID);

    void deleteSaleBillGeneralCard(@Param("BillID") String BillID);

    void deleteSaleBillTimeCard(@Param("BillID") String BillID);

    void deleteSaleBillSavingCard(@Param("BillID") String BillID);

    void deleteSaleBillPackageCard(@Param("BillID") String BillID);

    void deleteSaleBillPackageCardProduct(@Param("BillID") String BillID);

    void deleteSaleBillPackageCardProject(@Param("BillID") String BillID);

    void deleteSaleBillPackageCardGeneralCard(@Param("BillID") String BillID);

    void deleteSaleBillPackageCardTimeCard(@Param("BillID") String BillID);

    void deleteSaleBillPackageCardSavingCard(@Param("BillID") String BillID);

    void deleteSaleBill(@Param("BillID") String BillID);

    void createSaleBillPending(SaleBillPendingEntity saleBillPendingEntity);

    String getSaleBillPending(@Param("BillID") String BillID);

    void updateSaleBillPending(SaleBillPendingEntity saleBillPendingEntity);

    void deleteSaleBillPending(@Param("BillID") String BillID);

    Integer countArrearCount(@Param("BillID") String BillID);

    Integer getCustomerBillQuantityExcludeCurrentBill(@Param("SaleBillID") String SaleBillID, @Param("CustomerID") Integer CustomerID, @Param("EntityID") Integer EntityID);

    SaleBillPayEntity getSaleBillPayEntity(@Param("BillID") String BillID);
    SaleBillPayEntity getSaleBillPayEntityByID(@Param("ID") Integer ID);

    BigDecimal getCustomerAggregateAmount(Integer CustomerID);

    SaleBillGoodPayInfoOutputForm saleBillProductPayInfo(@Param("BillID") String BillID);

    SaleBillGoodPayInfoOutputForm saleBillProjectPayInfo(@Param("BillID") String BillID);

    SaleBillGoodPayInfoOutputForm saleBillGeneralCardPayInfo(@Param("BillID") String BillID);

    SaleBillGoodPayInfoOutputForm saleBillTimeCardPayInfo(@Param("BillID") String BillID);

    SaleBillGoodPayInfoOutputForm saleBillSavingCardPayInfo(@Param("BillID") String BillID);

    List<FastSaleBillGoodInfoOutputForm> getFastSaleBillProduct(@Param("BillID") String BillID);

    List<FastSaleBillGoodInfoOutputForm> getFastSaleBillProject(@Param("BillID") String BillID);

    WXSaleBillInfoOutputForm getWXSaleBill(@Param("SaleBillID") String SaleBillID);

    WXRefundSaleBillInfoOutputForm getWXRefundSaleBill(@Param("SaleBillID") String SaleBillID);

    WXCancelSaleBillInfoOutputForm getWXCancelSaleBill(@Param("SaleBillID") String SaleBillID);

    List<SaleBillListOutputForm> pendingList(@Param("EntityID") Integer EntityID, @Param("Name") String Name, @Param("BillStatus") String BillStatus, @Param("BillType") String BillType);

    SaleBillCancelProcessEntity getSaleBillCancel(@Param("SaleBillID") String SaleBillID);
    List<SaleBillEntityPerformancePackageCardOutputForm> getSaleBillEntityPackageCard(@Param("SaleBillID") String SaleBillID);

    List<SaleBillPayArrearGoodsAccountEntity> getSaleBillPayArrearProduct(@Param("SaleBillID") String SaleBillID);

    List<SaleBillPayArrearGoodsAccountEntity> getSaleBillPayArrearProject(@Param("SaleBillID") String SaleBillID);

    List<SaleBillPayArrearGoodsAccountEntity> getSaleBillPayArrearGeneralCard(@Param("SaleBillID") String SaleBillID);

    List<SaleBillPayArrearGoodsAccountEntity> getSaleBillPayArrearTimeCard(@Param("SaleBillID") String SaleBillID);

    List<SaleBillPayArrearGoodsAccountEntity> getSaleBillPayArrearPackageCardProduct(@Param("SaleBillID") String SaleBillID);

    List<SaleBillPayArrearGoodsAccountEntity> getSaleBillPayArrearPackageCardProject(@Param("SaleBillID") String SaleBillID);

    List<SaleBillPayArrearGoodsAccountEntity> getSaleBillPayArrearPackageCardGeneralCard(@Param("SaleBillID") String SaleBillID);

    List<SaleBillPayArrearGoodsAccountEntity> getSaleBillPayArrearPackageCardTimeCard(@Param("SaleBillID") String SaleBillID);

    SaleBillEmployeeDiscountOutForm getEmployeeDiscount(@Param("EntityID") Integer EntityID, @Param("EmployeeID") String EmployeeID);
}
