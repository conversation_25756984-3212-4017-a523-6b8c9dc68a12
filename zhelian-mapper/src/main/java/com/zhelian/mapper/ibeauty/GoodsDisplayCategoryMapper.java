package com.zhelian.mapper.ibeauty;


import com.zhelian.model.pad.entity.GoodsDisplayCategoryEntity;
import com.zhelian.model.pad.form.output.GoodsDisplayCategoryOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;


@Repository
public interface GoodsDisplayCategoryMapper {

    List<GoodsDisplayCategoryOutputForm> goodsDisplayList(@Param("Name") String Name, @Param("Active") Boolean Active);

    String getMemo(@Param("ID") Integer ID);

    Integer getMaxSequence();

    GoodsDisplayCategoryEntity getGoodsDisplayCategoryEntity(Integer moveID);

    void createGoodsDisplayCategory(GoodsDisplayCategoryEntity goodsDisplayCategoryEntity);

    void updateGoodsDisplayCategory(GoodsDisplayCategoryEntity goodsDisplayCategoryEntity);

    LinkedList<Integer> getASCList(@Param("ID") int ID, @Param("ParentID") int ParentID);

    void updateSequence(List<GoodsDisplayCategoryEntity> list);

    Integer childActiveCount(@Param("ID") Integer ID);

    Integer goodsDisplayByCategoryCount(@Param("ID") Integer ID);

    List<GoodsDisplayCategoryOutputForm> validGoodsDisplayList();
}
