package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SaleProjectSchemeHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleProjectSchemeHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.output.SaleProjectSchemeHandlerCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleProjectSchemeHandlerCommissionMapper {

    List<SaleProjectSchemeHandlerCommissionOutputForm> allSaleProjectSchemeHandlerCommission(@Param("EntityID") Integer EntityID);

    void deleteSaleProjectSchemeHandler(@Param("EntityID") Integer EntityID);

    void deleteSaleProjectSchemeHandlerJobType(@Param("EntityID") Integer EntityID);

    void updateSaleProjectSchemeHandlerCommission(List<SaleProjectSchemeHandlerCommissionEntity> list);

    void insertSaleProjectSchemeHandlerJobTypeCommission(List<SaleProjectSchemeHandlerJobTypeCommissionEntity> list);
}
