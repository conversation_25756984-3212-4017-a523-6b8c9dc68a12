package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.AdditionalPaymentsEntity;
import com.zhelian.model.ibeauty.form.output.AdditionalSpendingOrIncomeOrPaymentsCategoryOutputForm;
import com.zhelian.model.ibeauty.form.output.SpendingCategoryOrIncomeOrPaymentsOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;


@Repository
public interface AdditionalPaymentsMapper {


    List<SpendingCategoryOrIncomeOrPaymentsOutputForm> additionalPaymentsList(@Param("Name") String Name, @Param("Active") Boolean Active);

    LinkedList<Integer> getIDOrderBySequenceASC(int ID);

    Integer getMaxSequence();

    void createAdditionalPayments(AdditionalPaymentsEntity additionalPaymentsEntity);

    void updateAdditionalPayments(AdditionalPaymentsEntity additionalPaymentsEntity);

    void updateSequence(List<AdditionalPaymentsEntity> list);

    List<AdditionalSpendingOrIncomeOrPaymentsCategoryOutputForm> getAdditionalPayments();
}
