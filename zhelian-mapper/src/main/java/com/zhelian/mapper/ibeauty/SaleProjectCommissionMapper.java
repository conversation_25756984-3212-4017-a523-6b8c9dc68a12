package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SaleProjectCommissionEntity;
import com.zhelian.model.ibeauty.form.output.SaleProjectCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface SaleProjectCommissionMapper {

    List<SaleProjectCommissionOutputForm> allSaleProjectCommission(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertSaleProjectCommission(List<SaleProjectCommissionEntity> list);

    void deleteSaleProjectCommission(Map<String, Object> map);

    void deleteSaleProjectCommissionByEntity(@Param("EntityID") Integer EntityID);

    void deleteSaleProjectCommissionByEntityAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);
}
