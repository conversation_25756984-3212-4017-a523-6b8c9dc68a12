package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.form.output.SaleGeneralCardCategoryHandlerCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SalePackageCardGeneralCardCategoryHandlerCommissionMapper {

    List<SaleGeneralCardCategoryHandlerCommissionOutputForm> allSalePackageCardGeneralCardCategoryHandlerCommission(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertSalePackageCardGeneralCardCategoryHandlerCommission(List list);

    void insertSalePackageCardGeneralCardCategoryHandlerJobTypeCommission(List list);

    void deleteSalePackageCardGeneralCardCategoryHandler(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void deleteSalePackageCardGeneralCardCategoryHandlerJobType(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void deleteSalePackageCardGeneralCardCategoryHandlerJobTypeCommission(Integer EntityID);

    void deleteSalePackageCardGeneralCardCategoryHandlerCommission(Integer EntityID);
}
