package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.TreatSavingCardProjectCommissionEntity;
import com.zhelian.model.ibeauty.form.output.TreatSavingCardProjectCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface TreatSavingCardProjectCommissionMapper {

    List<TreatSavingCardProjectCommissionOutputForm> allTreatSavingCardProjectCommission(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertTreatSavingProjectCommission(List<TreatSavingCardProjectCommissionEntity> list);

    void deleteTreatSavingCardProjectCommission(Map<String, Object> map);

    void deleteTreatSavingCardProjectCommissionByEntity(Integer EntityID);

    void deleteTreatSavingCardProjectCommissionByEntityAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);
}
