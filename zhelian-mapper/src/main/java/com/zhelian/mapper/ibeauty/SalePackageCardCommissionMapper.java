package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SalePackageCardCommissionEntity;
import com.zhelian.model.ibeauty.form.output.SalePackageCardCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface SalePackageCardCommissionMapper {

    List<SalePackageCardCommissionOutputForm> allSalePackageCardCommission(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertSalePackageCardCommission(List<SalePackageCardCommissionEntity> list);

    void deleteSalePackageCardCommission(Map<String, Object> map);

    void deleteSalePackageCardCommissionByEntityID(Integer EntityID);

    void deleteSalePackageCardCommissionByEntityIDAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);
}
