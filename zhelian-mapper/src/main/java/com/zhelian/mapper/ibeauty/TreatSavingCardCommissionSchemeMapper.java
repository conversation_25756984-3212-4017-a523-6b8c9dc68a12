package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.TreatSavingCardCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.output.TreatSavingCardProjectCommissionSchemeOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatSavingCardCommissionSchemeMapper {

    List<TreatSavingCardProjectCommissionSchemeOutputForm> treatSavingCardCommissionSchemeList(@Param("Name") String Name);

    void createTreatSavingCardCommissionScheme(TreatSavingCardCommissionSchemeEntity treatSavingCardCommissionSchemeEntity);

    Integer getTreatSavingCardCommissionScheme(@Param("EntityID") Integer EntityID);

    void deleteTreatSavingCardCommissionScheme(@Param("EntityID") Integer EntityID);


}
