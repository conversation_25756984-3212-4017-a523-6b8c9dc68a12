package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.AdditionalSpendingCategoryEntity;
import com.zhelian.model.ibeauty.form.output.AdditionalSpendingOrIncomeOrPaymentsCategoryOutputForm;
import com.zhelian.model.ibeauty.form.output.SpendingCategoryOrIncomeOrPaymentsOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;


@Repository
public interface AdditionalSpendingCategoryMapper {


    List<SpendingCategoryOrIncomeOrPaymentsOutputForm> additionalSpendingCategoryList(@Param("Name") String Name, @Param("Active") Boolean Active);

    LinkedList<Integer> getIDOrderBySequenceASC(int ID);

    Integer getMaxSequence();

    void createAdditionalSpendingCategory(AdditionalSpendingCategoryEntity additionalSpendingCategoryEntity);

    void updateAdditionalSpendingCategory(AdditionalSpendingCategoryEntity additionalSpendingCategoryEntity);

    void updateSequence(List<AdditionalSpendingCategoryEntity> list);

    List<AdditionalSpendingOrIncomeOrPaymentsCategoryOutputForm> getAdditionalSpendingCategory();

}
