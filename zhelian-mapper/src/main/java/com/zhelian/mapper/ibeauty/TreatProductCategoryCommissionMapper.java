package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.TreatProductCategoryCommissionEntity;
import com.zhelian.model.ibeauty.entity.TreatProductCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.output.TreatProductCategoryCommissionOutputForm;
import com.zhelian.model.ibeauty.form.output.TreatProductCategoryOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatProductCategoryCommissionMapper {

    TreatProductCategoryOutputForm treatProductCategoryCommission(@Param("EntityID") Integer EntityID);

    List<TreatProductCategoryCommissionOutputForm> allTreatProductCategoryCommission(@Param("EntityID") Integer EntityID);

    void updateTreatProductCommissionScheme(TreatProductCommissionSchemeEntity treatProductCommissionSchemeEntity);

    void insertTreatProductCategoryCommissionEntity(List<TreatProductCategoryCommissionEntity> list);

    void deleteTreatProductCategoryCommissionEntity(@Param("EntityID") Integer EntityID);
}
