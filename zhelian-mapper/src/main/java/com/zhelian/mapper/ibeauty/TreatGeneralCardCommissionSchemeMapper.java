package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.TreatGeneralCardProjectCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.output.TreatProjectCommissionSchemeOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatGeneralCardCommissionSchemeMapper {

    List<TreatProjectCommissionSchemeOutputForm> treatGeneralCardCommissionSchemeList(@Param("Name") String Name);

    void createTreatGeneralCardCommissionScheme(TreatGeneralCardProjectCommissionSchemeEntity treatGeneralCardProjectCommissionSchemeEntity);

    Integer getTreatGeneralCardCommissionScheme(@Param("EntityID") Integer EntityID);

    void deleteTreatGeneralCardCommissionScheme(@Param("EntityID") Integer EntityID);

}
