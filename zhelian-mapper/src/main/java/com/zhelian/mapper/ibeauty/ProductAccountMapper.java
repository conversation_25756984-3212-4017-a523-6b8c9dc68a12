package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.ProductAccountEntity;
import com.zhelian.model.ibeauty.entity.ProductAccountExcendEntity;
import com.zhelian.model.ibeauty.form.input.TreatProductAccountForm;
import com.zhelian.model.ibeauty.form.output.CustomerProductAccountOutputForm;
import com.zhelian.model.ibeauty.form.output.CustomerProductAccountSumOutputForm;
import com.zhelian.model.ibeauty.form.output.ProductAccountOutputForm;
import com.zhelian.model.ibeauty.form.output.TranserProductAccountOutputForm;
import com.zhelian.model.micromall.form.output.MicromallProductAccountOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProductAccountMapper {

    void createProductAccount(List<ProductAccountEntity> list);

    //产品账户列表
    List<ProductAccountOutputForm> getProductAccount(TreatProductAccountForm form);

    //根据账户ID 查询产品账户
    ProductAccountExcendEntity getProductAccountByAccountID(Integer AccountID);

    void updateProductAccountBalance(ProductAccountEntity productAccountEntity);

    void updateProductAccountActiveFalse(@Param("SaleBillID") String SaleBillID);

    void updatePackageCardProductAccountActiveFalse(@Param("SaleBillID") String SaleBillID);

    List<ProductAccountOutputForm> arrearProductAccount(@Param("CustomerID") String CustomerID, @Param("Name") String Name, @Param("EntityID") Integer EntityID);

    void updateArrearProductAccount(ProductAccountEntity productAccountEntity);

    List<ProductAccountOutputForm> getTranserProductAccount(@Param("CustomerID") String CustomerID, @Param("Name") String Name);

    List<ProductAccountOutputForm> getRefundProductAccount(@Param("CustomerID") String CustomerID, @Param("Name") String Name);

    void createTranserProductAccount(ProductAccountEntity productAccountEntity);

    TranserProductAccountOutputForm getProductAccountEntity(@Param("ID") Integer ID);

    List<CustomerProductAccountOutputForm> customerProductAccount(@Param("CustomerID") String CustomerID, @Param("Active") Boolean Active, @Param("Name") String Name);

    List<ProductAccountOutputForm> customerMiniprogramProductAccount(@Param("CustomerID") String CustomerID, @Param("Active") Boolean Active, @Param("Name") String Name);

    void updateCustomerProductAccount(ProductAccountEntity productAccountEntity);

    List<ProductAccountEntity> getProductAccountBySaleBillID(String SaleBillID);

    List<MicromallProductAccountOutputForm> customerMicromallProductAccount(@Param("CustomerID") Integer CustomerID, @Param("Active") Boolean Active, @Param("Name") String Name);

    CustomerProductAccountSumOutputForm customerProductAccountSum(@Param("CustomerID") String CustomerID, @Param("Active") Boolean Active, @Param("Name") String Name);

    void updateProductRemark(@Param("SaleBillGoodID") Integer SaleBillGoodID, @Param("Remark") String Remark);

    void updatePackageCardProductRemark(@Param("SaleBillGoodID") Integer SaleBillGoodID, @Param("Remark") String Remark);

    void updateTransferProductAccountActiveFalse(@Param("ID") String ID);

    void updateProductBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    void updatePackageCardProductBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    ProductAccountExcendEntity getProductAccountBySaleBillProductID(@Param("SaleBillProductID") Integer SaleBillProductID);

    void updateProductAccount(ProductAccountEntity productAccountEntity);

    ProductAccountExcendEntity getProductAccountBySaleBillPackageCardProductID(@Param("SaleBillPackageCardProductID") Integer SaleBillPackageCardProductID);

    void updateProductAccountArrear(ProductAccountEntity productAccountEntity);
}
