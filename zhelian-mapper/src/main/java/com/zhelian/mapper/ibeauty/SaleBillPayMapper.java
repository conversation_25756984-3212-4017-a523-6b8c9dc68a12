package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.SaleBillPayEntity;
import com.zhelian.model.ibeauty.entity.SaleBillPayGoodsEntity;
import com.zhelian.model.ibeauty.entity.SaleBillPaySavingCardDeductionEntity;
import com.zhelian.model.ibeauty.entity.SaleBillPaySavingCardDeductionGoodsEntity;
import com.zhelian.model.ibeauty.form.output.RefundBillPayGoodsForm;
import com.zhelian.model.ibeauty.form.output.RefundBillPaySavingCardGoodsForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SaleBillPayMapper {

    void createSaleBillPay(SaleBillPayEntity entity);

    void createSaleBillPaySavingCardDeduction(List<SaleBillPaySavingCardDeductionEntity> list);

    //现金支付方式
    void createSaleBillPayProduct(List<SaleBillPayGoodsEntity> list);

    void createSaleBillPayProject(List<SaleBillPayGoodsEntity> list);

    void createSaleBillPaySavingCard(List<SaleBillPayGoodsEntity> list);

    void createSaleBillPayGeneralCard(List<SaleBillPayGoodsEntity> list);

    void createSaleBillPayTimeCard(List<SaleBillPayGoodsEntity> list);

    void createSaleBillPayPackageCard(List<SaleBillPayGoodsEntity> list);


    //储值卡抵扣支付方式
    void createSaleBillPaySavingCardDeductionProduct(List<SaleBillPaySavingCardDeductionGoodsEntity> list);

    void createSaleBillPaySavingCardDeductionProject(List<SaleBillPaySavingCardDeductionGoodsEntity> list);

    void createSaleBillPaySavingCardDeductionGeneralCard(List<SaleBillPaySavingCardDeductionGoodsEntity> list);

    void createSaleBillPaySavingCardDeductionTimeCard(List<SaleBillPaySavingCardDeductionGoodsEntity> list);

    void createSaleBillPaySavingCardDeductionPackageCard(List<SaleBillPaySavingCardDeductionGoodsEntity> list);


    //套餐卡储值卡抵扣支付方式
    void createSaleBillPaySavingCardDeductionPackageCardProduct(List<SaleBillPaySavingCardDeductionGoodsEntity> list);

    void createSaleBillPaySavingCardDeductionPackageCardProject(List<SaleBillPaySavingCardDeductionGoodsEntity> list);

    void createSaleBillPaySavingCardDeductionPackageCardGeneralCard(List<SaleBillPaySavingCardDeductionGoodsEntity> list);

    void createSaleBillPaySavingCardDeductionPackageCardTimeCard(List<SaleBillPaySavingCardDeductionGoodsEntity> list);

    //套餐卡明细 现金支付方式
    void createSaleBillPayPackageCardProduct(List<SaleBillPayGoodsEntity> list);

    void createSaleBillPayPackageCardProject(List<SaleBillPayGoodsEntity> list);

    void createSaleBillPayPackageCardGeneralCard(List<SaleBillPayGoodsEntity> list);

    void createSaleBillPayPackageCardTimeCard(List<SaleBillPayGoodsEntity> list);

    void createSaleBillPayPackageCardSavingCard(List<SaleBillPayGoodsEntity> list);

    List<RefundBillPayGoodsForm> getSaleBillPayProduct(@Param("SaleBillProductID") Integer SaleBillProductID,@Param("ProductAccountID") Integer ProductAccountID);

    List<RefundBillPayGoodsForm> getSaleBillPayProject(@Param("SaleBillProjectID") Integer SaleBillProjectID,@Param("ProjectAccountID") Integer ProjectAccountID);

    List<RefundBillPayGoodsForm> getSaleBillPayGeneralCard(@Param("SaleBillGeneralCardID") Integer SaleBillGeneralCardID,@Param("GeneralCardAccountID") Integer GeneralCardAccountID);

    List<RefundBillPayGoodsForm> getSaleBillPayTimeCard(@Param("SaleBillTimeCardID") Integer SaleBillTimeCardID,@Param("TimeCardAccountID") Integer TimeCardAccountID);

    List<RefundBillPayGoodsForm> getSaleBillPaySavingCard(@Param("SaleBillSavingCardID") Integer SaleBillSavingCardID,@Param("SavingCardAccountID") Integer SavingCardAccountID);

    List<RefundBillPayGoodsForm> getSaleBillPayPackageCardProduct(@Param("SaleBillPackageCardProductID") Integer SaleBillPackageCardProductID,@Param("ProductAccountID") Integer ProductAccountID);

    List<RefundBillPayGoodsForm> getSaleBillPayPackageCardProject(@Param("SaleBillPackageCardProjectID") Integer SaleBillPackageCardProjectID,@Param("ProjectAccountID") Integer ProjectAccountID);

    List<RefundBillPayGoodsForm> getSaleBillPayPackageCardGeneralCard(@Param("SaleBillPackageCardGeneralCardID") Integer SaleBillPackageCardGeneralCardID,@Param("GeneralCardAccountID") Integer GeneralCardAccountID);

    List<RefundBillPayGoodsForm> getSaleBillPayPackageCardTimeCard(@Param("SaleBillPackageCardTimeCardID") Integer SaleBillPackageCardTimeCardID,@Param("TimeCardAccountID") Integer TimeCardAccountID);

    List<RefundBillPayGoodsForm> getSaleBillPayPackageCardSavingCard(@Param("SaleBillPackageCardSavingCardID") Integer SaleBillPackageCardSavingCardID,@Param("SavingCardAccountID") Integer SavingCardAccountID);

    List<RefundBillPaySavingCardGoodsForm> getSaleBillPaySavingCardProduct(@Param("SaleBillProductID") Integer SaleBillProductID,@Param("AccountID") Integer AccountID);

    List<RefundBillPaySavingCardGoodsForm> getSaleBillPaySavingCardProductTransfer(@Param("AccountID") Integer AccountID);

    List<RefundBillPaySavingCardGoodsForm> getSaleBillPaySavingCardProject(@Param("SaleBillProjectID") Integer SaleBillProjectID,@Param("AccountID") Integer AccountID);

    List<RefundBillPaySavingCardGoodsForm> getSaleBillPaySavingCardGeneralCard(@Param("SaleBillGeneralCardID") Integer SaleBillGeneralCardID,@Param("AccountID") Integer AccountID);

    List<RefundBillPaySavingCardGoodsForm> getSaleBillPaySavingCardTimeCard(@Param("SaleBillTimeCardID") Integer SaleBillTimeCardID,@Param("AccountID") Integer AccountID);

    List<RefundBillPaySavingCardGoodsForm> getSaleBillPayPackageCardSavingCardProduct(@Param("SaleBillPackageCardProductID") Integer SaleBillProductID,@Param("AccountID") Integer AccountID);

    List<RefundBillPaySavingCardGoodsForm> getSaleBillPayPackageCardSavingCardProject(@Param("SaleBillPackageCardProjectID") Integer SaleBillProjectID,@Param("AccountID") Integer AccountID);

    List<RefundBillPaySavingCardGoodsForm> getSaleBillPayPackageCardSavingCardGeneralCard(@Param("SaleBillPackageCardGeneralCardID") Integer SaleBillGeneralCardID,@Param("AccountID") Integer AccountID);

    List<RefundBillPaySavingCardGoodsForm> getSaleBillPayPackageCardSavingCardTimeCard(@Param("SaleBillPackageCardTimeCardID") Integer SaleBillTimeCardID,@Param("AccountID") Integer AccountID);

    void updateProductPayMethod(@Param("ID") Integer ID, @Param("PayMethodID") Integer PayMethodID);

    void updateProjectPayMethod(@Param("ID") Integer ID, @Param("PayMethodID") Integer PayMethodID);

    void updateGeneralCardPayMethod(@Param("ID") Integer ID, @Param("PayMethodID") Integer PayMethodID);

    void updateTimeCardPayMethod(@Param("ID") Integer ID, @Param("PayMethodID") Integer PayMethodID);

    void updateSavingCardPayMethod(@Param("ID") Integer ID, @Param("PayMethodID") Integer PayMethodID);

    void updatePackageCardPayMethod(@Param("ID") Integer ID, @Param("PayMethodID") Integer PayMethodID);

    void updatePackageCardProductPayMethod(@Param("ID") Integer ID, @Param("PayMethodID") Integer PayMethodID);

    void updatePackageCardProjectPayMethod(@Param("ID") Integer ID, @Param("PayMethodID") Integer PayMethodID);

    void updatePackageCardGeneralCardPayMethod(@Param("ID") Integer ID, @Param("PayMethodID") Integer PayMethodID);

    void updatePackageCardTimeCardPayMethod(@Param("ID") Integer ID, @Param("PayMethodID") Integer PayMethodID);

    void updatePackageCardSavingCardPayMethod(@Param("ID") Integer ID, @Param("PayMethodID") Integer PayMethodID);

    void deleteSaleBillPaySavingCardDeductionProduct(@Param("BillID") String BillID);

    void deleteSaleBillPaySavingCardDeductionProject(@Param("BillID") String BillID);

    void deleteSaleBillPaySavingCardDeductionGeneralCard(@Param("BillID") String BillID);

    void deleteSaleBillPaySavingCardDeductionTimeCard(@Param("BillID") String BillID);

    void deleteSaleBillPaySavingCardDeductionPackageCardProduct(@Param("BillID") String BillID);

    void deleteSaleBillPaySavingCardDeductionPackageCardProject(@Param("BillID") String BillID);

    void deleteSaleBillPaySavingCardDeductionPackageCardGeneralCard(@Param("BillID") String BillID);

    void deleteSaleBillPaySavingCardDeductionPackageCardTimeCard(@Param("BillID") String BillID);

    void deleteSaleBillPaySavingCardDeduction(@Param("BillID") String BillID);
    List<SaleBillPayEntity> getSaleBillPayByBillID(@Param("BillID") String BillID);


}
