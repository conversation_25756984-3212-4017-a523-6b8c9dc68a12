package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.TreatProjectCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.output.TreatProjectCommissionSchemeOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatProjectCommissionSchemeMapper {

    List<TreatProjectCommissionSchemeOutputForm> treatProjectCommissionSchemeList(@Param("Name") String Name);

    void createTreatProjectCommissionScheme(TreatProjectCommissionSchemeEntity TreatProjectCommissionSchemeEntity);

    Integer getTreatProjectCommissionScheme(@Param("EntityID") Integer EntityID);

    void deleteTreatProjectCommissionScheme(@Param("EntityID") Integer EntityID);

    void deleteTreatProjectCategoryCommission(@Param("EntityID") Integer EntityID);

    void deleteTreatProjectCategoryHandlerCommission(@Param("EntityID") Integer EntityID);

    void deleteTreatProjectCategoryHandlerJobTypeCommission(@Param("EntityID") Integer EntityID);

    void deleteTreatProjectCommission(@Param("EntityID") Integer EntityID);

    void deleteTreatProjectHandlerCommission(@Param("EntityID") Integer EntityID);

    void deleteTreatProjectHandlerJobTypeCommission(@Param("EntityID") Integer EntityID);
}
