package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.form.output.SalePackageCardCategoryHandlerCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SalePackageCardCategoryHandlerCommissionMapper {

    List<SalePackageCardCategoryHandlerCommissionOutputForm> allSalePackageCardCategoryHandlerCommission(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertSalePackageCardCategoryHandlerCommission(List list);

    void insertSalePackageCardCategoryHandlerJobTypeCommission(List list);

    void deleteSalePackageCardCategoryHandler(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void deleteSalePackageCardCategoryHandlerJobType(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void deleteSalePackageCardCategoryHandlerJobTypeCommission(Integer EntityID);

    void deleteSalePackageCardCategoryHandlerCommission(Integer entityID);
}
