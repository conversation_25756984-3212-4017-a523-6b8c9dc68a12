package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.form.output.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CustomerSavingCardAccountMapper {

    List<CustomerSavingCardAccountOutputForm> getTreatBill(@Param("ID") String ID);

    List<CustomerSavingCardRefundAccountOutputForm> getRefundBill(@Param("ID") String ID);

    List<CustomerSavingCardRefundAccountOutputForm> getRefundTreatBill(@Param("ID") String ID);

    List<CustomerSavingCardTransferAccountOutputForm> getTransferBill(@Param("ID") String ID);

    List<CustomerRepaymentAccountOutputForm> getRepayment(@Param("ID") String ID);

    List<CustomerRepaymentAccountOutputForm> getPackageCardRepayment(@Param("ID") String ID);

    List<AccountArrearAmountProcessOutputForm> getArrearAmountProcess(@Param("ID") String ID);

    List<CustomerSavingCardDeductionAccountOutputForm> savingCardDeduction(@Param("ID") String ID);

    List<CustomerSavingCardRechargeOutputForm> rechargeBill(@Param("ID") String ID);
}
