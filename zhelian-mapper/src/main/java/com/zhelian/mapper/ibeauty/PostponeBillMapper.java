package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.*;
import com.zhelian.model.ibeauty.form.input.PostponeBillInfoQueryForm;
import com.zhelian.model.ibeauty.form.output.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface PostponeBillMapper {

    void createPostponeBill(PostponeBillEntity postponeBillEntity);

    void createPostponeProcess(PostponeProcessEntity postponeProcessEntity);

    void createPostponeBillGeneralCard(List<PostponeBillGeneralCardAccountEntity> list);

    void createPostponeBillTimeCard(List<PostponeBillTimeCardAccountEntity> list);

    void createPostponeBillSavingCard(List<PostponeBillSavingCardAccountEntity> list);

    void createPostponeBillPackageCard(List<PostponeBillPackageCardAccountEntity> list);

    List<PostponeBillOutputForm> postponeBillList(@Param("EntityID") Integer EntityID, @Param("PostponeBillID") String PostponeBillID, @Param("Name") String Name, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("ApprovalStatus") String ApprovalStatus, @Param("ShowAll") Boolean ShowAll);

    PostponeBillInfoOutputForm postponeBillInfo(PostponeBillInfoQueryForm form);

    List<PostponeBillInfoGeneralCardOutputForm> postponeBillInfoGeneralCard(@Param("ID") String ID);

    List<PostponeBillInfoTimeCardOutputForm> postponeBillInfoTimeCard(@Param("ID") String ID);

    List<PostponeBillInfoSavingCardOutputForm> postponeBillInfoSavingCard(@Param("ID") String ID);

    List<PostponeBillInfoPackageCardOutputForm> postponeBillInfoPackageCard(@Param("ID") String ID);

    void updatePostponeBillStatus(@Param("ID") String ID, @Param("ApprovalStatus") String ApprovalStatus);

    List<PostponeBillGeneralCardAccountEntity> getPostponeBillGeneralCardAccount(@Param("ID") String ID);

    List<PostponeBillTimeCardAccountEntity> getPostponeBillTimeCardAccount(@Param("ID") String ID);

    List<PostponeBillSavingCardAccountEntity> getPostponeBillSavingCardAccount(@Param("ID") String ID);

    List<PostponeBillPackageCardAccountEntity> getPostponeBillPackageCardAccount(@Param("ID") String ID);

    PostponeBillEntity getPostponeBill(@Param("ID") String ID);

    void deletePostponeGeneralCardAccount(@Param("ID") String ID);

    void deletePostponeTimeCardAccount(@Param("ID") String ID);

    void deletePostponeSavingCardAccount(@Param("ID") String ID);

    void deletePostponePackageCardAccount(@Param("ID") String ID);

    void updatePostponeBill(PostponeBillEntity postponeBillEntity);

    List<PostponeBillOutputForm> miniProgramPostponeBill(@Param("EntityID") Integer EntityID, @Param("Name") String Name, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("ApprovalStatus") String ApprovalStatus, @Param("ShowAll") Boolean ShowAll);

    WXPostponeBillIInfoOutputForm getWXPostponeBill(String BillID);
}
