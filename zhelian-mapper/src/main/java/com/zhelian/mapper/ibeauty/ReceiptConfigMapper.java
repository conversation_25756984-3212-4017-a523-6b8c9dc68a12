package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.ReceiptConfigEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;


@Repository
public interface ReceiptConfigMapper {

    ReceiptConfigEntity receiptConfigAll(@Param("EntityID") Integer EntityID);

    void createReceiptConfig(ReceiptConfigEntity receiptConfigEntity);

    void updateReceiptConfig(ReceiptConfigEntity receiptConfigEntity);
}
