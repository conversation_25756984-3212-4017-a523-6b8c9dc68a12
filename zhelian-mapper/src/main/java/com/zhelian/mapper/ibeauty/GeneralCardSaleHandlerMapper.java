package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.GeneralCardSaleHandlerEntity;
import com.zhelian.model.ibeauty.entity.GeneralCardSaleHandlerJobTypeEntity;
import com.zhelian.model.ibeauty.form.output.SaleHandlerOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;


@Repository
public interface GeneralCardSaleHandlerMapper {


    List<SaleHandlerOutputForm> allGeneralCardSaleHandler(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("Active") Boolean Active);

    Integer getMaxSequence();

    void createGeneralCardSaleHandler(GeneralCardSaleHandlerEntity generalCardSaleHandlerEntity);

    void insertGeneralCardSaleHandlerJobType(List<GeneralCardSaleHandlerJobTypeEntity> list);

    void updateGeneralCardSaleHandler(GeneralCardSaleHandlerEntity generalCardSaleHandlerEntity);

    void deleteSaleHandlerJobType(@Param("ID") Integer ID);

    LinkedList<Integer> getIDOrderBySequenceASC(Integer ID);

    void updateSequence(List<GeneralCardSaleHandlerEntity> list);
}
