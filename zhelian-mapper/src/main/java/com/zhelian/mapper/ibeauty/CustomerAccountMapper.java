package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.*;
import org.springframework.stereotype.Repository;

@Repository
public interface CustomerAccountMapper {

    void createProjectAccountArrearAmountProcess(ProjectAccountArrearAmountProcessEntity projectAccountArrearAmountProcessEntity);

    void createProductAccountArrearAmountProcess(ProductAccountArrearAmountProcessEntity productAccountArrearAmountProcessEntity);

    void createGeneralCardAccountArrearAmountProcess(GeneralCardAccountArrearAmountProcessEntity generalCardAccountArrearAmountProcessEntity);

    void createTimeCardAccountArrearAmountProcess(TimeCardAccountArrearAmountProcessEntity timeCardAccountArrearAmountProcessEntity);

    void createSavingCardAccountArrearAmountProcess(SavingCardAccountArrearAmountProcessEntity savingCardAccountArrearAmountProcessEntity);
}
