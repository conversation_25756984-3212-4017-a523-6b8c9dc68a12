package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SaleTimeCardCategoryCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleTimeCardCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.output.SaleTimeCardCategoryCommissionOutputForm;
import com.zhelian.model.ibeauty.form.output.SaleTimeCardCategoryOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleTimeCardCategoryCommissionMapper {

    List<SaleTimeCardCategoryCommissionOutputForm> allSaleTimeCardCategoryCommission(@Param("EntityID") Integer EntityID);

    void updateSaleTimeCardCommissionScheme(SaleTimeCardCommissionSchemeEntity saleTimeCardCommissionSchemeEntity);

    void insertSaleTimeCardCategoryCommissionEntity(List<SaleTimeCardCategoryCommissionEntity> list);

    SaleTimeCardCategoryOutputForm saleTimeCardCategoryCommission(@Param("EntityID") Integer EntityID);

    void deleteSaleTimeCardCategoryCommission(Integer EntityID);
}
