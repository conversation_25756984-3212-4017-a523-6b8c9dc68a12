package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.form.output.AllSaleHandlerOutputForm;
import com.zhelian.model.ibeauty.form.output.SaleAllHandlerOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleHandlerMapper {

    List<AllSaleHandlerOutputForm> productSaleHandler(@Param("EntityID") Integer EntityID);

    List<AllSaleHandlerOutputForm> projectSaleHandler(@Param("EntityID") Integer EntityID);

    List<AllSaleHandlerOutputForm> generalCardSaleHandler(@Param("EntityID") Integer EntityID);

    List<AllSaleHandlerOutputForm> timeCardSaleHandler(@Param("EntityID") Integer EntityID);

    List<AllSaleHandlerOutputForm> savingCardSaleHandler(@Param("EntityID") Integer EntityID);

    List<AllSaleHandlerOutputForm> packageCardSaleHandler(@Param("EntityID") Integer EntityID);

    List<SaleAllHandlerOutputForm> allHandler(@Param("EntityID") Integer EntityID,@Param("GoodTypes") List<String> GoodTypes);
}
