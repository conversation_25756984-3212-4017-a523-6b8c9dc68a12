package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.PackageCardCategoryEntity;
import com.zhelian.model.ibeauty.form.output.PackageCardCategoryOutputForm;
import com.zhelian.model.report.form.output.GoodsCategoryForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;

@Repository
public interface PackageCardCategoryMapper {
    List<PackageCardCategoryOutputForm> packageCardCategoryAll(@Param("Name") String Name, @Param("Active") Boolean Active);

    void createPackageCardCategory(PackageCardCategoryEntity entity);

    void updatePackageCardCategory(PackageCardCategoryEntity entity);

    LinkedList<Integer> packageCardCategoryAscList(Integer id);

    void updateSequence(List<PackageCardCategoryEntity> list);

    Integer getMaxSequence();

    int packageCardByCategoryCount(int ID);

    List<GoodsCategoryForm> packageCardGoodsCategoryAll(@Param("Name") String Name, @Param("Active") Boolean Active);
}
