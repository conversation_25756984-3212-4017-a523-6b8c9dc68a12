package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SaleProductCategoryCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleProductCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.output.SaleProductCategoryCommissionOutputForm;
import com.zhelian.model.ibeauty.form.output.SaleProductCategoryOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleProductCategoryCommissionMapper {

    List<SaleProductCategoryCommissionOutputForm> allSaleProductCategoryCommission(@Param("EntityID") Integer EntityID);

    void updateSaleProductCommissionScheme(SaleProductCommissionSchemeEntity saleProductCommissionSchemeEntity);

    void insertSaleProductCategoryCommissionEntity(List<SaleProductCategoryCommissionEntity> list);

    SaleProductCategoryOutputForm saleProductCategoryCommission(@Param("EntityID") Integer EntityID);

    void deleteSaleProductCategoryCommissionEntity(@Param("EntityID") Integer EntityID);
}
