package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.AppointmentServicerEntity;
import com.zhelian.model.ibeauty.entity.AppointmentServicerJobTypeEntity;
import com.zhelian.model.ibeauty.form.input.AppointmentServicerQueryForm;
import com.zhelian.model.ibeauty.form.output.AppointmentServicerDetailOutputForm;
import com.zhelian.model.ibeauty.form.output.AppointmentServicerOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;

@Repository
public interface AppointmentServicerMapper {

    List<AppointmentServicerOutputForm> all(AppointmentServicerQueryForm form);

    AppointmentServicerDetailOutputForm detail(AppointmentServicerQueryForm form);

    Integer getCount();

    void addAppointmentServicer(AppointmentServicerEntity appointmentServicerEntity);

    void addAppointmentServicerJobType(List<AppointmentServicerJobTypeEntity> list);

    void addAppointmentServicerEntity(@Param("ID") Integer ID, @Param("list") List<Integer> list);

    void updateAppointmentServicer(AppointmentServicerEntity appointmentServicerEntity);

    void deleteAppointmentServicerJobType(@Param("ID") Integer ID);

    void deleteAppointmentServicerEntity(@Param("ID") Integer ID);

    LinkedList<Integer> getASCList(@Param("ID") Integer ID);

    void updateSequence(@Param("list") List<AppointmentServicerEntity> list);

    List<AppointmentServicerOutputForm> appointmentServicerByEntity(@Param("EntityID") Integer EntityID);

    AppointmentServicerEntity getAppointmentServicerEntity(@Param("ID") Integer ID);
}
