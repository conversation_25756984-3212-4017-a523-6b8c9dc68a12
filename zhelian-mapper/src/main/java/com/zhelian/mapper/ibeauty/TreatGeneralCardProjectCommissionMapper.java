package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.TreatGeneralCardProjectCommissionEntity;
import com.zhelian.model.ibeauty.form.output.TreatCardCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatGeneralCardProjectCommissionMapper {

    List<TreatCardCommissionOutputForm> allTreatGeneralCardProjectCommission(@Param("EntityID") Integer EntityID, @Param("GeneralCardID") Integer GeneralCardID);

    void insertTreatGeneralCardProjectCommission(List<TreatGeneralCardProjectCommissionEntity> list);

    void deleteTreatGeneralCardProjectCommissionByEntityIDAndCard(@Param("EntityID") Integer EntityID, @Param("GeneralCardID") Integer GeneralCardID);

    void deleteTreatGeneralCardProjectCommissionByEntityID(@Param("EntityID") Integer EntityID);
}
