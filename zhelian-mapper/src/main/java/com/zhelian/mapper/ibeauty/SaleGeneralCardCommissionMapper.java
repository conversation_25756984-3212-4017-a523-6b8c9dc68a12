package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SaleGeneralCardCommissionEntity;
import com.zhelian.model.ibeauty.form.output.SaleGeneralCardCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface SaleGeneralCardCommissionMapper {

    List<SaleGeneralCardCommissionOutputForm> allSaleGeneralCardCommission(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertSaleGeneralCardCommission(List<SaleGeneralCardCommissionEntity> list);

    void deleteSaleGeneralCardCommission(Map<String, Object> map);

    void deleteSaleGeneralCardCommissionByEntityID(Integer EntityID);

    void deleteSaleGeneralCardCommissionByEntityIDAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);
}
