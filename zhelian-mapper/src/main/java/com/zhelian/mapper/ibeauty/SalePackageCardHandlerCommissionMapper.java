package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.form.output.SalePackageCardHandlerCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SalePackageCardHandlerCommissionMapper {

    List<SalePackageCardHandlerCommissionOutputForm> allSalePackageCardHandlerCommission(@Param("EntityID") Integer EntityID, @Param("PackageCardID") Integer PackageCardID);

    void deleteSalePackageCardHandler(@Param("EntityID") Integer EntityID, @Param("PackageCardID") Integer PackageCardID);

    void deleteSalePackageCardHandlerJobType(@Param("EntityID") Integer EntityID, @Param("PackageCardID") Integer PackageCardID);

    void insertSalePackageCardHandlerCommission(List list);

    void insertSalePackageCardHandlerJobTypeCommission(List list);

    void deleteSalePackageCardHandlerJobTypeCommission(Integer EntityID);

    void deleteSalePackageCardHandlerCommission(Integer entityID);
}
