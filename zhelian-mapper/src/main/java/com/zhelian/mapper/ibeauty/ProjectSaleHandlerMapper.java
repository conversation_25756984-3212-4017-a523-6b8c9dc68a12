package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.ProjectSaleHandlerEntity;
import com.zhelian.model.ibeauty.entity.ProjectSaleHandlerJobTypeEntity;
import com.zhelian.model.ibeauty.form.output.SaleHandlerOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;


@Repository
public interface ProjectSaleHandlerMapper {


    List<SaleHandlerOutputForm> allProjectSaleHandler(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("Active") Boolean Active);

    Integer getMaxSequence();

    void createProjectSaleHandler(ProjectSaleHandlerEntity projectSaleHandlerEntity);

    void insertProjectSaleHandlerJobType(List<ProjectSaleHandlerJobTypeEntity> list);

    void updateProjectSaleHandler(ProjectSaleHandlerEntity projectSaleHandlerEntity);

    void deleteSaleHandlerJobType(@Param("ID") Integer ID);

    LinkedList<Integer> getIDOrderBySequenceASC(Integer ID);

    void updateSequence(List<ProjectSaleHandlerEntity> list);
}
