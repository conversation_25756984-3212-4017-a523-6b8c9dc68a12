package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SaleSavingCardCategoryCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleSavingCardCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.output.SaleSavingCardCategoryCommissionOutputForm;
import com.zhelian.model.ibeauty.form.output.SaleSavingCardCategoryOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleSavingCardCategoryCommissionMapper {

    SaleSavingCardCategoryOutputForm saleSavingCardCategoryCommission(@Param("EntityID") Integer EntityID);

    List<SaleSavingCardCategoryCommissionOutputForm> allSaleSavingCardCategoryCommission(@Param("EntityID") Integer EntityID);

    void updateSaleSavingCardCommissionScheme(SaleSavingCardCommissionSchemeEntity saleSavingCardCommissionSchemeEntity);

    void insertSaleSavingCardCategoryCommissionEntity(List<SaleSavingCardCategoryCommissionEntity> list);

    void deleteSaleSavingCardCategoryCommission(@Param("EntityID") Integer EntityID);
}
