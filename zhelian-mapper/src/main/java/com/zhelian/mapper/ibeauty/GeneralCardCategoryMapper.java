package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.GeneralCardCategoryEntity;
import com.zhelian.model.ibeauty.form.output.GeneralCardCategoryOutputForm;
import com.zhelian.model.report.form.output.GoodsCategoryForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;

@Repository
public interface GeneralCardCategoryMapper {
    List<GeneralCardCategoryOutputForm> generalCardCategoryAll(@Param("Name") String Name, @Param("Active") Boolean Active);

    void createGeneralCardCategory(GeneralCardCategoryEntity entity);

    void updateGeneralCardCategory(GeneralCardCategoryEntity entity);

    LinkedList<Integer> generalCardCategoryAscList(Integer id);

    void updateGeneralCardCategorySequence(List<GeneralCardCategoryEntity> list);

    Integer getGeneralCardCategoryMaxSequence();

    Integer generalCardByCategoryCount(Integer ID);

    List<GoodsCategoryForm> generalCardGoodsCategoryAll(@Param("Name") String Name, @Param("Active") Boolean Active);
}
