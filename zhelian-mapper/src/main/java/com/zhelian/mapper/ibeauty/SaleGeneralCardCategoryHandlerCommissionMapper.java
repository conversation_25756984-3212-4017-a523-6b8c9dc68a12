package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.form.output.SaleGeneralCardCategoryHandlerCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleGeneralCardCategoryHandlerCommissionMapper {

    List<SaleGeneralCardCategoryHandlerCommissionOutputForm> allSaleGeneralCardCategoryHandlerCommission(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertSaleGeneralCardCategoryHandlerCommission(List list);

    void insertSaleGeneralCardCategoryHandlerJobTypeCommission(List list);

    void deleteSaleGeneralCardCategoryHandler(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void deleteSaleGeneralCardCategoryHandlerJobType(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void deleteSaleGeneralCardCategoryHandlerJobTypeCommission(Integer EntityID);

    void deleteSaleGeneralCardCategoryHandlerCommission(Integer EntityID);
}
