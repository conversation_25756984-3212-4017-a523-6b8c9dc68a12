package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.*;
import com.zhelian.model.ibeauty.form.input.RefundTreatBillInfoQueryForm;
import com.zhelian.model.ibeauty.form.output.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface RefundTreatBillMapper {

    List<RefundTreatBillOutputForm> getTreatBill(@Param("CustomerID") Integer CustomerID);

    List<RefundTreatBillProductOutputForm> getTreatBillProduct(@Param("CustomerID") Integer CustomerID, @Param("Name") String Name);

    List<RefundTreatBillProjectOutputForm> getTreatBillProject(@Param("CustomerID") Integer CustomerID, @Param("Name") String Name);

    List<RefundTreatBillGeneralCardOutputForm> getTreatBillGeneralCard(@Param("CustomerID") Integer CustomerID, @Param("Name") String Name);

    List<RefundTreatBillTimeCardOutputForm> getTreatBillTimeCard(@Param("CustomerID") Integer CustomerID, @Param("Name") String Name);

    List<RefundTreatBillSavingCardOutputForm> getTreatBillSavingCard(@Param("CustomerID") Integer CustomerID, @Param("Name") String Name);

    List<RefundTreatBillPackageCardOutputForm> getTreatBillPackageCard(@Param("CustomerID") Integer CustomerID, @Param("Name") String Name);

    List<RefundTreatBillProductExtendOutputForm> getTreatBillPackageCardProduct(@Param("CustomerID") Integer CustomerID);

    List<RefundTreatBillProjectExtendOutputForm> getTreatBillPackageCardProject(@Param("CustomerID") Integer CustomerID);

    List<RefundTreatBillGeneralCardExtendOutputForm> getTreatBillPackageCardGeneralCard(@Param("CustomerID") Integer CustomerID);

    List<RefundTreatBillTimeCardExtendOutputForm> getTreatBillPackageCardTimeCard(@Param("CustomerID") Integer CustomerID);

    List<RefundTreatBillSavingCardExtendOutputForm> getTreatBillPackageCardSavingCard(@Param("CustomerID") Integer CustomerID);

    void createRefundTreatBill(RefundTreatBillEntity refundTreatBillEntity);

    void createRefundTreatBillProcess(RefundTreatBillProcessEntity refundTreatBillProcessEntity);

    void createRefundTreatBillProduct(RefundTreatBillProductEntity refundTreatBillProductEntity);

    void createRefundTreatBillProject(RefundTreatBillProjectEntity refundTreatBillProjectEntity);

    void createRefundTreatBillGeneralCard(RefundTreatBillGeneralCardEntity refundTreatBillGeneralCardEntity);

    void createRefundTreatBillTimeCard(RefundTreatBillTimeCardEntity refundTreatBillTimeCardEntity);

    void createRefundTreatBillSavingCard(RefundTreatBillSavingCardEntity refundTreatBillSavingCardEntity);

    List<RefundTreatBillListOutputForm> refundTreatBillList(@Param("EntityID") Integer EntityID, @Param("ID") String ID, @Param("Name") String Name, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("ApprovalStatus") String ApprovalStatus, @Param("ShowAll") Boolean ShowAll);

    List<RefundTreatBillListOutputForm> refundApprovalTreatBillList(@Param("EmployeeID") String EmployeeID, @Param("EntityID") Integer EntityID, @Param("ID") String ID, @Param("Name") String Name, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("ApprovalStatus") String ApprovalStatus, @Param("ShowAll") Boolean ShowAll);

    RefundTreatBillInfoOutputForm refundTreatBillInfo(RefundTreatBillInfoQueryForm form);

    List<RefundTreatBillInfoProductOutputForm> refundTreatBillInfoProduct(@Param("ID") String ID);

    List<RefundTreatBillInfoProjectOutputForm> refundTreatBillInfoProject(@Param("ID") String ID);

    List<RefundTreatBillInfoGeneralCardOutputForm> refundTreatBillInfoGeneralCard(@Param("ID") String ID);

    List<RefundTreatBillInfoTimeCardOutputForm> refundTreatBillInfoTimeCard(@Param("ID") String ID);

    List<RefundTreatBillInfoSavingCardOutputForm> refundTreatBillInfoSavingCard(@Param("ID") String ID);

    List<RefundTreatBillInfoPackageCardOutputForm> refundTreatBillInfoPackageCard(@Param("ID") String ID);

    List<RefundTreatBillInfoProductExtendOutputForm> refundTreatBillPackageCardProductInfo(@Param("ID") String ID);

    List<RefundTreatBillInfoProjectExtendOutputForm> refundTreatBillPackageCardProjectInfo(@Param("ID") String ID);

    List<RefundTreatBillInfoGeneralCardExtendOutputForm> refundTreatBillPackageCardGeneralCardInfo(@Param("ID") String ID);

    List<RefundTreatBillInfoTimeCardExtendOutputForm> refundTreatBillPackageCardTimeCardInfo(@Param("ID") String ID);

    List<RefundTreatBillInfoSavingCardExtendOutputForm> refundTreatBillPackageCardSavingCardInfo(@Param("ID") String ID);

    void updateRefundTreatBillStatus(@Param("ID") String ID, @Param("ApprovalStatus") String ApprovalStatus);

    void updateRefundTreatBill(RefundTreatBillEntity refundTreatBillEntity);

    RefundTreatBillEntity getRefundTreatBill(@Param("ID") String ID);

    List<RefundTreatBillProductEntity> getRefundTreatBillProduct(@Param("ID") String ID);

    List<RefundTreatBillProjectEntity> getRefundTreatBillProject(@Param("ID") String ID);

    List<RefundTreatBillGeneralCardEntity> getRefundTreatBillGeneralCard(@Param("ID") String ID);

    List<RefundTreatBillTimeCardEntity> getRefundTreatBillTimeCard(@Param("ID") String ID);

    List<RefundTreatBillSavingCardEntity> getRefundTreatBillSavingCard(@Param("ID") String ID);

    RefundTreatBillGoodsOutputForm getRefundProductCount(@Param("AccountID") Integer AccountID, @Param("TreatBillProductID") Integer TreatBillProductID);

    RefundTreatBillGoodsOutputForm getRefundProjectCount(@Param("AccountID") Integer AccountID, @Param("TreatBillProjectID") Integer TreatBillProjectID);

    RefundTreatBillGoodsOutputForm getRefundGeneralCardCount(@Param("AccountID") Integer AccountID, @Param("TreatBillGeneralCardID") Integer TreatBillGeneralCardID);

    RefundTreatBillGoodsOutputForm getRefundTimeCardCount(@Param("AccountID") Integer AccountID, @Param("TreatBillTimeCardID") Integer TreatBillTimeCardID);

    RefundTreatBillGoodsOutputForm getRefundSavingCardCount(@Param("AccountID") Integer AccountID, @Param("TreatBillSavingCardID") Integer TreatBillSavingCardID);

    List<RefundTreatBillHandlerProductEntity> getRefundTreatBillHandlerProduct(@Param("ID") Integer ID);

    List<RefundTreatBillHandlerProjectEntity> getRefundTreatBillHandlerProject(@Param("ID") Integer ID);

    List<RefundTreatBillHandlerGeneralCardEntity> getRefundTreatBillHandlerGeneralCard(@Param("ID") Integer ID);

    List<RefundTreatBillHandlerTimeCardEntity> getRefundTreatBillHandlerTimeCard(@Param("ID") Integer ID);

    List<RefundTreatBillHandlerSavingCardEntity> getRefundTreatBillHandlerSavingCard(@Param("ID") Integer ID);


    void createRefundTreatBillHandlerProduct(List<RefundTreatBillHandlerProductEntity> list);

    void createRefundTreatBillHandlerProject(List<RefundTreatBillHandlerProjectEntity> list);

    void createRefundTreatBillHandlerGeneralCard(List<RefundTreatBillHandlerGeneralCardEntity> list);

    void createRefundTreatBillHandlerTimeCard(List<RefundTreatBillHandlerTimeCardEntity> list);

    void createRefundTreatBillHandlerSavingCard(List<RefundTreatBillHandlerSavingCardEntity> list);
}
