package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SaleTimeCardCommissionEntity;
import com.zhelian.model.ibeauty.form.output.SaleTimeCardCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface SaleTimeCardCommissionMapper {

    List<SaleTimeCardCommissionOutputForm> allSaleTimeCardCommission(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertSaleTimeCardCommission(List<SaleTimeCardCommissionEntity> list);

    void deleteSaleTimeCardCommission(Map<String, Object> map);

    void deleteSaleTimeCardCommissionByEntityID(Integer EntityID);

    void deleteSaleTimeCardCommissionByEntityIDAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);
}
