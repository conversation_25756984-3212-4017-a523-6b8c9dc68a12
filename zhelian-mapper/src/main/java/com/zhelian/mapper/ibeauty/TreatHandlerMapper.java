package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.form.output.AllTreatHandlerOutputForm;
import com.zhelian.model.ibeauty.form.output.TreatAllHandlerOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface TreatHandlerMapper {

    List<AllTreatHandlerOutputForm> projectTreatHandler(@Param("EntityID") Integer EntityID);

    List<AllTreatHandlerOutputForm> productTreatHandler(@Param("EntityID") Integer EntityID);

    List<AllTreatHandlerOutputForm> savingCardTreatHandler(@Param("EntityID") Integer EntityID);

    List<TreatAllHandlerOutputForm> allHandler(@Param("EntityID") Integer EntityID,@Param("GoodTypes") List<String> GoodTypes);
}
