package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.TreatTimeCardCommissionEntity;
import com.zhelian.model.ibeauty.form.output.TreatCardCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatTimeCardCommissionMapper {

    List<TreatCardCommissionOutputForm> allTreatTimeCardCommission(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertTreatTimeCardCommission(List<TreatTimeCardCommissionEntity> list);

    void deleteTreatTimeCardCommissionByEntityIDAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void deleteTreatTimeCardCommissionByEntityID(@Param("EntityID") Integer EntityID);
}
