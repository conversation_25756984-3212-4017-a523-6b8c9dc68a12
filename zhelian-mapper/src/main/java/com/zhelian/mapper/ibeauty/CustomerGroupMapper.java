package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.CustomerGroupEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CustomerGroupMapper {

    //新增会员分群信息
    void addCustomerGroup(CustomerGroupEntity group);

    //编辑会员基本信息
    void updateCustomerGroup(CustomerGroupEntity group);

    //删除会员基本信息
    void delCustomerGroup(String groupName);

    //会员分群信息查询
    List<CustomerGroupEntity> getGroupList(String groupName);

}
