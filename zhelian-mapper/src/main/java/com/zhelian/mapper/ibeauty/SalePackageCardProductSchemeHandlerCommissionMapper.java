package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SalePackageCardProductSchemeHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SalePackageCardProductSchemeHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.output.SaleProductSchemeHandlerCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SalePackageCardProductSchemeHandlerCommissionMapper {

    List<SaleProductSchemeHandlerCommissionOutputForm> allSalePackageCardProductSchemeHandlerCommission(@Param("EntityID") Integer EntityID);

    void deleteSalePackageCardProductSchemeHandler(@Param("EntityID") Integer EntityID);

    void deleteSalePackageCardProductSchemeHandlerJobType(@Param("EntityID") Integer EntityID);

    void insertSalePackageCardProductSchemeHandlerCommission(List<SalePackageCardProductSchemeHandlerCommissionEntity> list);

    void insertSalePackageCardProductSchemeHandlerJobTypeCommission(List<SalePackageCardProductSchemeHandlerJobTypeCommissionEntity> list);
}
