package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.SavingCardCategoryEntity;
import com.zhelian.model.ibeauty.form.output.SavingCardCategoryOutputForm;
import com.zhelian.model.report.form.output.GoodsCategoryForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;


@Repository
public interface SavingCardCategoryMapper {


    List<SavingCardCategoryOutputForm> savingCardCategoryList(@Param("Name") String Name, @Param("Active") Boolean Active);

    LinkedList<Integer> getASCList(int ID);

    Integer getMaxSequence();

    void createSavingCardCategory(SavingCardCategoryEntity savingCardCategoryEntity);

    void updateSavingCardCategory(SavingCardCategoryEntity savingCardCategoryEntity);

    void updateSequence(List<SavingCardCategoryEntity> list);

    Integer savingCardByCategoryCount(Integer ID);

    List<GoodsCategoryForm> savingCardGoodsCategoryAll(@Param("Name") String Name, @Param("Active") Boolean Active);
}
