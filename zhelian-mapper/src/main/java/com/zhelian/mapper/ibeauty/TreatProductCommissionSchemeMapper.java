package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.TreatProductCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.output.TreatProductCommissionSchemeOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatProductCommissionSchemeMapper {

    List<TreatProductCommissionSchemeOutputForm> treatProductCommissionSchemeList(@Param("Name") String Name);

    void createTreatProductCommissionScheme(TreatProductCommissionSchemeEntity TreatProductCommissionSchemeEntity);

    Integer getTreatProductCommissionScheme(@Param("EntityID") Integer EntityID);

    void deleteTreatProductCommissionScheme(@Param("EntityID") Integer EntityID);

    void deleteTreatProductCategoryCommission(@Param("EntityID") Integer EntityID);

    void deleteTreatProductCategoryHandlerCommission(@Param("EntityID") Integer EntityID);

    void deleteTreatProductCategoryHandlerJobTypeCommission(@Param("EntityID") Integer EntityID);

    void deleteTreatProductCommission(@Param("EntityID") Integer EntityID);

    void deleteTreatProductHandlerCommission(@Param("EntityID") Integer EntityID);

    void deleteTreatProductHandlerJobTypeCommission(@Param("EntityID") Integer EntityID);
}
