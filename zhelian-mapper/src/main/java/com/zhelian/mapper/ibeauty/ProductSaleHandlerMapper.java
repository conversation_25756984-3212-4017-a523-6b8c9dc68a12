package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.ProductSaleHandlerEntity;
import com.zhelian.model.ibeauty.entity.ProductSaleHandlerJobTypeEntity;
import com.zhelian.model.ibeauty.form.output.SaleHandlerOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;


@Repository
public interface ProductSaleHandlerMapper {


    List<SaleHandlerOutputForm> allProductSaleHandler(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("Active") Boolean Active);

    Integer getMaxSequence();

    void createProductSaleHandler(ProductSaleHandlerEntity productSaleHandlerEntity);

    void insertProductSaleHandlerJobType(List<ProductSaleHandlerJobTypeEntity> list);

    void updateProductSaleHandler(ProductSaleHandlerEntity productSaleHandlerEntity);

    void deleteSaleHandlerJobType(@Param("ID") Integer ID);

    LinkedList<Integer> getIDOrderBySequenceASC(Integer ID);

    void updateSequence(List<ProductSaleHandlerEntity> list);
}
