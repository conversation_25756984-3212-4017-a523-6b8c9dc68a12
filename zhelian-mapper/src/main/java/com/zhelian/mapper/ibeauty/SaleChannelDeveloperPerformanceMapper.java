package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.service.SaleChannelEmployeePerformanceGoodsEntity;
import com.zhelian.model.ibeauty.service.SaleChannelEmployeePerformancePackageCardGoodsEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SaleChannelDeveloperPerformanceMapper {


    //创建渠道非套餐卡业绩
    void createSaleDeveloperChannelPerformanceProduct(List<SaleChannelEmployeePerformanceGoodsEntity> list);

    void createSaleDeveloperChannelPerformanceProject(List<SaleChannelEmployeePerformanceGoodsEntity> list);

    void createSaleDeveloperChannelPerformanceGeneralCard(List<SaleChannelEmployeePerformanceGoodsEntity> list);

    void createSaleDeveloperChannelPerformanceTimeCard(List<SaleChannelEmployeePerformanceGoodsEntity> list);

    void createSaleDeveloperChannelPerformanceSavingCard(List<SaleChannelEmployeePerformanceGoodsEntity> list);

    //创建门店套餐卡业绩
    void createSaleDeveloperChannelPerformancePackageCardProduct(List<SaleChannelEmployeePerformancePackageCardGoodsEntity> list);

    void createSaleDeveloperChannelPerformancePackageCardProject(List<SaleChannelEmployeePerformancePackageCardGoodsEntity> list);

    void createSaleDeveloperChannelPerformancePackageCardGeneralCard(List<SaleChannelEmployeePerformancePackageCardGoodsEntity> list);

    void createSaleDeveloperChannelPerformancePackageCardTimeCard(List<SaleChannelEmployeePerformancePackageCardGoodsEntity> list);

    void createSaleDeveloperChannelPerformancePackageCardSavingCard(List<SaleChannelEmployeePerformancePackageCardGoodsEntity> list);


    void deleteSaleDeveloperChannelPerformanceProduct(String ID);

    void deleteSaleDeveloperChannelPerformanceProject(String ID);

    void deleteSaleDeveloperChannelPerformanceGeneralCard(String ID);

    void deleteSaleDeveloperChannelPerformanceTimeCard(String ID);

    void deleteSaleDeveloperChannelPerformanceSavingCard(String ID);

    void deleteSaleDeveloperChannelPerformancePackageCardProduct(String ID);

    void deleteSaleDeveloperChannelPerformancePackageCardProject(String ID);

    void deleteSaleDeveloperChannelPerformancePackageCardGeneralCard(String ID);

    void deleteSaleDeveloperChannelPerformancePackageCardTimeCard(String ID);

    void deleteSaleDeveloperChannelPerformancePackageCardSavingCard(String ID);
}
