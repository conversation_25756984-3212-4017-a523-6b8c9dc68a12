package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.GeneralCardAccountEntity;
import com.zhelian.model.ibeauty.entity.GeneralCardAccountExcendEntity;
import com.zhelian.model.ibeauty.form.input.TreatGeneralCardAccountForm;
import com.zhelian.model.ibeauty.form.output.*;
import com.zhelian.model.micromall.form.output.ApplyEntityOutputForm;
import com.zhelian.model.micromall.form.output.MicromallGeneralCardAccountOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface GeneralCardAccountMapper {

    void createGeneralCardAccount(List<GeneralCardAccountEntity> list);

    //通用次卡账户列表
    List<GeneralCardAccountOutputForm> generalCardAccount(TreatGeneralCardAccountForm form);

    //通用次卡消耗适用项目
    List<GeneralCardAccountProjectOutputForm> generalCardAccountProject(@Param("GeneralCardID") Integer GeneralCardID, @Param("Name") String Name, @Param("GeneralCardAccountID") Integer GeneralCardAccountID, @Param("EntityID") Integer EntityID);

    List<SaleGoodsParentCategoryOutputForm> generalCardAccountProjectCategory(@Param("GeneralCardID") Integer GeneralCardID, @Param("Name") String Name, @Param("EntityID") Integer EntityID);

    List<GeneralCardAccountProjectChildDetailOutputForm> generalCardAccountProjectByCategory(@Param("GeneralCardID") Integer GeneralCardID, @Param("Name") String Name, @Param("GeneralCardAccountID") Integer GeneralCardAccountID, @Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    List<GeneralCardHistoricalDataOutputForm> generalCardHistoricalData(@Param("GeneralCardID") Integer GeneralCardID, @Param("Name") String Name, @Param("GeneralCardAccountID") Integer GeneralCardAccountID, @Param("EntityID") Integer EntityID);

    List<GeneralCardAccountProjectChildDetailOutputForm> generalCardProject(@Param("GeneralCardID") Integer GeneralCardID, @Param("Name") String Name);

    //根据账户ID 查询账户
    GeneralCardAccountExcendEntity getGeneralCardAccountByAccountID(Integer AccountID);

    void updateGeneralCardAccountBalance(GeneralCardAccountEntity generalCardAccountEntity);

    void updateGeneralCardAccountActiveFalse(@Param("SaleBillID") String SaleBillID);

    void updatePackageCardGeneralCardAccountActiveFalse(@Param("SaleBillID") String SaleBillID);

    List<GeneralCardAccountOutputForm> arrearGeneralCardAccount(@Param("CustomerID") String CustomerID, @Param("Name") String Name, @Param("EntityID") Integer EntityID);

    void updateArrearGeneralCardAccount(GeneralCardAccountEntity generalCardAccountEntity);

    List<GeneralCardAccountOutputForm> getPostPoneGeneralCardAccount(@Param("CustomerID") String CustomerID, @Param("Name") String Name);

    void updateGeneralCardAccountValidDate(GeneralCardAccountEntity generalCardAccountEntity);

    List<GeneralCardAccountOutputForm> getTranserGeneralCardAccount(@Param("CustomerID") String CustomerID, @Param("Name") String Name);

    List<GeneralCardAccountOutputForm> getRefundGeneralCardAccount(@Param("CustomerID") String CustomerID, @Param("Name") String Name);

    void createTranserGeneralCardAccount(GeneralCardAccountEntity generalCardAccountEntity);

    TranserGeneralCardAccountOutputForm getGeneralCardAccountEntity(@Param("ID") Integer ID);

    List<CustomerGeneralCardAccountOutputForm> customerGeneralCardAccount(@Param("CustomerID") String CustomerID, @Param("Active") Boolean Active, @Param("Name") String Name);

    List<GeneralCardAccountOutputForm> customerMiniprogramGeneralCardAccount(@Param("CustomerID") String CustomerID, @Param("Active") Boolean Active, @Param("Name") String Name);

    void updateCustomerGeneralCardAccount(GeneralCardAccountEntity generalCardAccountEntity);

    Integer getGeneralCardProjectAccount(@Param("AccountID") Integer AccountID, @Param("ProjectID") Integer ProjectID);

    Integer getGeneralCardProjectLimitConsumeTimes(@Param("AccountID") Integer AccountID, @Param("ProjectID") Integer ProjectID);

    List<MicromallGeneralCardAccountOutputForm> customerMicromallGeneralCardAccount(@Param("CustomerID") Integer CustomerID, @Param("Active") Boolean Active, @Param("Name") String Name);

    List<Integer> getGeneralCardEntityByID(@Param("ID") Integer ID);

    List<ApplyEntityOutputForm> customerMicromallGeneralCardAccountEntity(@Param("ID") Integer ID, @Param("Name") String Name, @Param("CityCode") String CityCode, @Param("Longitude") String Longitude, @Param("Latitude") String Latitude);

    CustomerGeneralCardAccountSumOutputForm customerGeneralCardAccountSum(@Param("CustomerID") String CustomerID, @Param("Active") Boolean Active, @Param("Name") String Name);

    void updateTransferGeneralCardAccountActiveFalse(@Param("ID") String ID);

    void updateGeneralCardBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    void updatePackageCardGeneralCardBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    List<Integer> getConsumeProjectCount(Integer ID);

    GeneralCardAccountExcendEntity getGeneralCardAccountBySaleBillGeneralCardID(@Param("SaleBillGeneralCardID") Integer SaleBillGeneralCardID);

    void updateGeneralCardAccount(GeneralCardAccountEntity generalCardAccountEntity);

    GeneralCardAccountExcendEntity getGeneralCardAccountBySaleBillPackageCardGeneralCardID(@Param("SaleBillPackageCardGeneralCardID") Integer SaleBillPackageCardGeneralCardID);

    void updateGeneralCardAccountArrear(GeneralCardAccountEntity accountEntity);
}
