package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.TreatProductHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.TreatProductHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.output.TreatProductHandlerCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatProductHandlerCommissionMapper {

    List<TreatProductHandlerCommissionOutputForm> allTreatProductHandlerCommission(@Param("EntityID") Integer EntityID, @Param("ProductID") Integer ProductID);

    void deleteTreatProductHandler(@Param("EntityID") Integer EntityID, @Param("ProductID") Integer ProductID);

    void deleteTreatProductHandlerJobType(@Param("EntityID") Integer EntityID, @Param("ProductID") Integer ProductID);

    void updateTreatProductHandlerCommission(List<TreatProductHandlerCommissionEntity> list);

    void insertTreatProductHandlerJobTypeCommission(List<TreatProductHandlerJobTypeCommissionEntity> list);

    void deleteTreatProductHandlerCommissionByEntity(Integer EntityID);

    void deleteTreatProductHandlerJobTypeCommissionByEntity(Integer EntityID);
}
