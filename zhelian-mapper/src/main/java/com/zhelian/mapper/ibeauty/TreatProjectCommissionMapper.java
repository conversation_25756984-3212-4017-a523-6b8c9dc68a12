package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.TreatProjectCommissionEntity;
import com.zhelian.model.ibeauty.form.output.TreatProjectCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface TreatProjectCommissionMapper {

    List<TreatProjectCommissionOutputForm> allTreatProjectCommission(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertTreatProjectCommission(List<TreatProjectCommissionEntity> list);

    void deleteTreatProjectCommission(Map<String, Object> map);

    void deleteTreatProjectCommissionByEntity(Integer EntityID);

    void deleteTreatProjectCommissionByEntityAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);
}
