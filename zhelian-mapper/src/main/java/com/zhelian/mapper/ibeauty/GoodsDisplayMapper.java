package com.zhelian.mapper.ibeauty;


import com.zhelian.model.pad.entity.GoodsDisplayEntity;
import com.zhelian.model.pad.form.output.GoodsDisplayChildDetailOutputForm;
import com.zhelian.model.pad.form.output.GoodsDisplayDetailOutputForm;
import com.zhelian.model.pad.form.output.GoodsDisplayOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface GoodsDisplayMapper {

    void createGoodsDisplay(GoodsDisplayEntity goodsDisplayEntity);

    void updateGoodsDisplay(GoodsDisplayEntity goodsDisplayEntity);

    GoodsDisplayEntity getGoodsDisplay(@Param("ID") Integer ID);

    void deleteImageByID(@Param("ID") Integer ID);

    void deleteGoods(@Param("ID") Integer ID);


    List<GoodsDisplayOutputForm> allGoodsDisplay(@Param("Name") String Name);

    String getMemo(@Param("ID") Integer ID);

    List<GoodsDisplayDetailOutputForm> getGoods();

    List<GoodsDisplayChildDetailOutputForm> getChildGoods();

    List<GoodsDisplayDetailOutputForm> getTopGoodsList();


}
