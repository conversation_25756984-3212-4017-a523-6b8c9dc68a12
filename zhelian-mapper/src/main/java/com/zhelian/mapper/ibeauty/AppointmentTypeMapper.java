package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.AppointmentTypeEntity;
import com.zhelian.model.ibeauty.form.input.AppointmentTypeQueryForm;
import com.zhelian.model.ibeauty.form.output.AppointmentTypeOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;

@Repository
public interface AppointmentTypeMapper {

    List<AppointmentTypeOutputForm> all(AppointmentTypeQueryForm form);

    Integer getAppointmentTypeCount();

    void create(AppointmentTypeEntity appointmentTypeEntity);

    void update(AppointmentTypeEntity appointmentTypeEntity);

    LinkedList<Integer> getASCList(@Param("ID") Integer ID);

    void updateSequence(List<AppointmentTypeEntity> list);
}
