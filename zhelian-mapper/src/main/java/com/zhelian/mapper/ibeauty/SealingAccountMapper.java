package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.SealingAccountEntity;
import com.zhelian.model.ibeauty.entity.SealingAccountEntityEntity;
import com.zhelian.model.ibeauty.entity.SealingAccountJobTypeEntity;
import com.zhelian.model.ibeauty.entity.TimeCardCategoryEntity;
import com.zhelian.model.ibeauty.form.output.SealingAccountOutputForm;
import com.zhelian.model.ibeauty.form.output.SealingAccountPermissionEntityOutputForm;
import com.zhelian.model.ibeauty.form.output.TimeCardCategoryOutputForm;
import com.zhelian.model.khs.entity.EntityEntity;
import com.zhelian.model.report.form.output.GoodsCategoryForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;


@Repository
public interface SealingAccountMapper {

    List<SealingAccountOutputForm> sealingAccountList(@Param("EntityID") Integer EntityID,@Param("Name") String Name);

    void createSealingAccount(SealingAccountEntity sealingAccountEntity);

    void createSealingAccountEntityEntity(List<SealingAccountEntityEntity> list);

    void createSealingAccountJobTypeEntity(List<SealingAccountJobTypeEntity> list);

    void updateSealingAccount(SealingAccountEntity sealingAccountEntity);

    void deleteSealingAccountEntity(Integer ID);

    void deleteSealingAccountJobTypeEntity(Integer ID);

    void deleteSealingAccount(Integer ID);

    List<Integer> getSealingAccountEntity();

    List<Integer> getSealingAccountEntityRemoveSelf(Integer ID);

    List<SealingAccountPermissionEntityOutputForm> entityPermission();
}
