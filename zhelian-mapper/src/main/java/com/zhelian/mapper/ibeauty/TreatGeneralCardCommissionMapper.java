package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.TreatGeneralCardCommissionEntity;
import com.zhelian.model.ibeauty.form.output.TreatCardCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatGeneralCardCommissionMapper {

    List<TreatCardCommissionOutputForm> allTreatGeneralCardCommission(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertTreatGeneralCardCommission(List<TreatGeneralCardCommissionEntity> list);

    void deleteTreatGeneralCardCommissionByEntityIDAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void deleteTreatGeneralCardCommissionByEntityID(@Param("EntityID") Integer EntityID);
}
