package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.service.SaleChannelPerformanceGoodsEntity;
import com.zhelian.model.ibeauty.service.SaleChannelPerformancePackageCardGoodsEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SaleChannelPerformanceMapper {


    //创建渠道非套餐卡业绩
    void createSaleChannelPerformanceProduct(List<SaleChannelPerformanceGoodsEntity> list);

    void createSaleChannelPerformanceProject(List<SaleChannelPerformanceGoodsEntity> list);

    void createSaleChannelPerformanceGeneralCard(List<SaleChannelPerformanceGoodsEntity> list);

    void createSaleChannelPerformanceTimeCard(List<SaleChannelPerformanceGoodsEntity> list);

    void createSaleChannelPerformanceSavingCard(List<SaleChannelPerformanceGoodsEntity> list);

    //创建门店套餐卡业绩
    void createSaleChannelPerformancePackageCardProduct(List<SaleChannelPerformancePackageCardGoodsEntity> list);

    void createSaleChannelPerformancePackageCardProject(List<SaleChannelPerformancePackageCardGoodsEntity> list);

    void createSaleChannelPerformancePackageCardGeneralCard(List<SaleChannelPerformancePackageCardGoodsEntity> list);

    void createSaleChannelPerformancePackageCardTimeCard(List<SaleChannelPerformancePackageCardGoodsEntity> list);

    void createSaleChannelPerformancePackageCardSavingCard(List<SaleChannelPerformancePackageCardGoodsEntity> list);


    void deleteSaleChannelPerformanceProduct(String ID);

    void deleteSaleChannelPerformanceProject(String ID);

    void deleteSaleChannelPerformanceGeneralCard(String ID);

    void deleteSaleChannelPerformanceTimeCard(String ID);

    void deleteSaleChannelPerformanceSavingCard(String ID);

    void deleteSaleChannelPerformancePackageCardProduct(String ID);

    void deleteSaleChannelPerformancePackageCardProject(String ID);

    void deleteSaleChannelPerformancePackageCardGeneralCard(String ID);

    void deleteSaleChannelPerformancePackageCardTimeCard(String ID);

    void deleteSaleChannelPerformancePackageCardSavingCard(String ID);
}
