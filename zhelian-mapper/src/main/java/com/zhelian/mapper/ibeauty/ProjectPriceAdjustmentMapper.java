package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.ProjectPriceAdjustmentEntity;
import com.zhelian.model.ibeauty.form.output.ProjectPriceAdjustmentOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface ProjectPriceAdjustmentMapper {
    List<ProjectPriceAdjustmentOutputForm> projectPriceAdjustmentList(@Param("ProjectName") String ProjectName, @Param("EntityName") String EntityName);

    Integer projectPriceAdjustmentCount(@Param("EntityID") Integer EntityID, @Param("ProjectID") Integer ProjectID);

    void insertProjectPriceAdjustmentEntity(ProjectPriceAdjustmentEntity projectPriceAdjustmentEntity);

    void updateProjectPriceAdjustmentEntity(ProjectPriceAdjustmentEntity projectPriceAdjustmentEntity);

    void deleteProjectPriceAdjustmentEntity(@Param("EntityID") Integer EntityID, @Param("ProjectID") Integer ProjectID);
}
