package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.AdditionalSpendingAttachmentEntity;
import com.zhelian.model.ibeauty.entity.AdditionalSpendingEntity;
import com.zhelian.model.ibeauty.form.output.AdditionalSpendingOutputEasyExcelForm;
import com.zhelian.model.ibeauty.form.output.AdditionalSpendingOutputForm;
import com.zhelian.model.ibeauty.form.output.AdditionalSpendingSumOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AdditionalSpendingMapper {

    List<AdditionalSpendingOutputForm> additionalSpendingList(@Param("EmployeeID") String EmployeeID, @Param("Payer") String Payer, @Param("EntityID") Integer EntityID, @Param("SpendingCategoryID") Integer SpendingCategoryID, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate);

    void insertAdditionalSpending(AdditionalSpendingEntity additionalSpendingEntity);

    void updateAdditionalSpending(AdditionalSpendingEntity additionalSpendingEntity);

    void deleteAdditionalSpendingByID(@Param("ID") Integer ID);

    AdditionalSpendingSumOutputForm additionalSpendingSum(@Param("EmployeeID") String EmployeeID, @Param("Payer") String Payer, @Param("EntityID") Integer EntityID, @Param("SpendingCategoryID") Integer SpendingCategoryID, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate);

    void addAttachment(@Param("AdditionalSpendingID") Integer AdditionalSpendingID, @Param("AttachmentURL") String AttachmentURL, @Param("AttachmentType") Integer AttachmentType);

    void deleteAttachment(@Param("ID") Integer ID);

    List<AdditionalSpendingAttachmentEntity> getAttachment(@Param("ID") Integer ID);

    List<Object> additionalSpendingExcel(@Param("EmployeeID") String EmployeeID, @Param("Payer") String Payer, @Param("EntityID") Integer EntityID, @Param("SpendingCategoryID") Integer SpendingCategoryID, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate);

    List<AdditionalSpendingOutputEasyExcelForm> additionalSpendingEasyExcel(@Param("EmployeeID") String EmployeeID, @Param("Payer") String Payer, @Param("EntityID") Integer EntityID, @Param("SpendingCategoryID") Integer SpendingCategoryID, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate);
    AdditionalSpendingEntity getAdditionalSpendingEntity(@Param("ID") Integer ID);

}
