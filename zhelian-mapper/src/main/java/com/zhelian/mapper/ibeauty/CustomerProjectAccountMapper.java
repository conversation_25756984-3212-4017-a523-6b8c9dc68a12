package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.form.output.AccountOutputForm;
import com.zhelian.model.ibeauty.form.output.CustomerAccountTreatBillOutputForm;
import com.zhelian.model.ibeauty.form.output.CustomerTransferAccountOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CustomerProjectAccountMapper {

    List<CustomerAccountTreatBillOutputForm> getTreatBill(@Param("ID") String ID);

    List<AccountOutputForm> getRefundBill(@Param("ID") String ID);

    List<AccountOutputForm> getRefundTreatBill(@Param("ID") String ID);

    List<CustomerTransferAccountOutputForm> getTransferBill(@Param("ID") String ID);

    List<AccountOutputForm> getRepayment(@Param("ID") String ID);

    List<AccountOutputForm> getPackageCardRepayment(@Param("ID") String ID);

    List<AccountOutputForm> getArrearAmountProcess(@Param("ID") String ID);
}
