package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.EmployeeLoginEntityEntity;
import com.zhelian.model.ibeauty.entity.EmployeeLoginEntityExtendEntity;
import com.zhelian.model.miniprogram.entity.EmployeeLoginEntityMiniProgramEntity;
import org.springframework.stereotype.Repository;


@Repository
public interface EmployeeLoginEntityMapper {

    EmployeeLoginEntityExtendEntity getEmployeeLoginEntityByEmployeeID(String EmployeeID);

    void deleteEmployeeLoginEntityByEmployeeID(String EmployeeID);

    void addEmployeeLoginEntity(EmployeeLoginEntityEntity employeeLoginEntityEntity);

    void deleteEmployeeLoginEntityMiniprogramByEmployeeID(String EmployeeID);

    void addEmployeeLoginEntityMiniprogram(EmployeeLoginEntityMiniProgramEntity employeeLoginEntityMiniProgramEntity);
}
