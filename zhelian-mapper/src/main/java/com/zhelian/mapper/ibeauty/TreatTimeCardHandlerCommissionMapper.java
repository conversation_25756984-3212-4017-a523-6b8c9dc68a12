package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.form.output.TreatCardHandlerCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatTimeCardHandlerCommissionMapper {

    List<TreatCardHandlerCommissionOutputForm> allTreatTimeCardHandlerCommission(@Param("EntityID") Integer EntityID, @Param("TimeCardID") Integer TimeCardID);

    void deleteTreatTimeCardHandler(@Param("EntityID") Integer EntityID, @Param("TimeCardID") Integer TimeCardID);

    void deleteTreatTimeCardHandlerJobType(@Param("EntityID") Integer EntityID, @Param("TimeCardID") Integer TimeCardID);

    void insertTreatTimeCardHandlerCommission(List list);

    void insertTreatTimeCardHandlerJobTypeCommission(List list);

    void deleteTreatTimeCardHandlerJobTypeCommissionByEntityID(Integer EntityID);

    void deleteTreatTimeCardHandlerCommissionByEntityID(Integer EntityID);
}
