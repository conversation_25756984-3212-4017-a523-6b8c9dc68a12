package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SaleProjectHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleProjectHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.output.SaleProjectHandlerCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleProjectHandlerCommissionMapper {

    List<SaleProjectHandlerCommissionOutputForm> allSaleProjectHandlerCommission(@Param("EntityID") Integer EntityID, @Param("ProjectID") Integer ProjectID);

    void deleteSaleProjectHandler(@Param("EntityID") Integer EntityID, @Param("ProjectID") Integer ProjectID);

    void deleteSaleProjectHandlerJobType(@Param("EntityID") Integer EntityID, @Param("ProjectID") Integer ProjectID);

    void updateSaleProjectHandlerCommission(List<SaleProjectHandlerCommissionEntity> list);

    void insertSaleProjectHandlerJobTypeCommission(List<SaleProjectHandlerJobTypeCommissionEntity> list);

    void deleteSaleProjectHandlerCommissionByEntity(@Param("EntityID") Integer EntityID);

    void deleteSaleProjectHandlerJobTypeCommissionByEntity(@Param("EntityID") Integer EntityID);
}
