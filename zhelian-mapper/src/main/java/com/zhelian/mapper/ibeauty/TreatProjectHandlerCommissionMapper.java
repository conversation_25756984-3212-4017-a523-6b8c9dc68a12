package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.TreatProjectHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.TreatProjectHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.output.TreatProjectHandlerCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatProjectHandlerCommissionMapper {

    List<TreatProjectHandlerCommissionOutputForm> allTreatProjectHandlerCommission(@Param("EntityID") Integer EntityID, @Param("ProjectID") Integer ProjectID);

    void deleteTreatProjectHandler(@Param("EntityID") Integer EntityID, @Param("ProjectID") Integer ProjectID);

    void deleteTreatProjectHandlerJobType(@Param("EntityID") Integer EntityID, @Param("ProjectID") Integer ProjectID);

    void updateTreatProjectHandlerCommission(List<TreatProjectHandlerCommissionEntity> list);

    void insertTreatProjectHandlerJobTypeCommission(List<TreatProjectHandlerJobTypeCommissionEntity> list);

    void deleteTreatProjectHandlerCommissionByEntity(Integer EntityID);

    void deleteTreatProjectHandlerJobTypeCommissionByEntity(Integer EntityID);
}
