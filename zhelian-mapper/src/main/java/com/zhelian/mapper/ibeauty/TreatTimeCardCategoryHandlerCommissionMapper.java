package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.form.output.TreatCardCategoryHandlerCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatTimeCardCategoryHandlerCommissionMapper {

    List<TreatCardCategoryHandlerCommissionOutputForm> allTreatTimeCardCategoryHandlerCommission(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertTreatTimeCardCategoryHandlerCommission(List list);

    void insertTreatTimeCardCategoryHandlerJobTypeCommission(List list);

    void deleteTreatTimeCardCategoryHandler(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void deleteTreatTimeCardCategoryHandlerJobType(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void deleteTreatTimeCardCategoryHandlerByEntityID(@Param("EntityID") Integer EntityID);

    void deleteTreatTimeCardCategoryHandlerJobTypeByEntityID(@Param("EntityID") Integer EntityID);
}
