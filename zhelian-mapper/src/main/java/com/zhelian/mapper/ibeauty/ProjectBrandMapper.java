package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.ProjectBrandEntity;
import com.zhelian.model.ibeauty.form.output.GoodsBrandOutputForm;
import com.zhelian.model.ibeauty.form.output.ProjectBrandOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;

@Repository
public interface ProjectBrandMapper {
    List<ProjectBrandOutputForm> projectBrandList(@Param("Name") String Name, @Param("Active") Boolean Active);

    void createProjectBrand(ProjectBrandEntity entity);

    void updateProjectBrand(ProjectBrandEntity entity);

    LinkedList<Integer> getASCList(Integer id);

    void updateSequence(List<ProjectBrandEntity> list);

    Integer getMaxSequence();

    Integer projectByBrandCount(Integer ID);

    List<GoodsBrandOutputForm> projectGoodsBrandList(@Param("Name") String Name, @Param("Active") Boolean Active);
}
