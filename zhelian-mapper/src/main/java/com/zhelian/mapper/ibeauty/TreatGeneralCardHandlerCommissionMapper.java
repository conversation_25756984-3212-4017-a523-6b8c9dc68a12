package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.form.output.TreatCardHandlerCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatGeneralCardHandlerCommissionMapper {

    List<TreatCardHandlerCommissionOutputForm> allTreatGeneralCardHandlerCommission(@Param("EntityID") Integer EntityID, @Param("GeneralCardID") Integer GeneralCardID);

    void deleteTreatGeneralCardHandler(@Param("EntityID") Integer EntityID, @Param("GeneralCardID") Integer GeneralCardID);

    void deleteTreatGeneralCardHandlerJobType(@Param("EntityID") Integer EntityID, @Param("GeneralCardID") Integer GeneralCardID);

    void insertTreatGeneralCardHandlerCommission(List list);

    void insertTreatGeneralCardHandlerJobTypeCommission(List list);

    void deleteTreatGeneralCardHandlerCommissionByEntityID(Integer EntityID);

    void deleteTreatGeneralCardHandlerJobTypeCommissionByEntityID(Integer EntityID);

}
