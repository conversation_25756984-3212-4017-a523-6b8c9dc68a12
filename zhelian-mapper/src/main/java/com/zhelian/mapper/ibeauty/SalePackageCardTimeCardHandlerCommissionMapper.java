package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.form.output.SaleTimeCardHandlerCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SalePackageCardTimeCardHandlerCommissionMapper {

    List<SaleTimeCardHandlerCommissionOutputForm> allSalePackageCardTimeCardHandlerCommission(@Param("EntityID") Integer EntityID, @Param("TimeCardID") Integer TimeCardID);

    void deleteSalePackageCardTimeCardHandler(@Param("EntityID") Integer EntityID, @Param("TimeCardID") Integer TimeCardID);

    void deleteSalePackageCardTimeCardHandlerJobType(@Param("EntityID") Integer EntityID, @Param("TimeCardID") Integer TimeCardID);

    void insertSalePackageCardTimeCardHandlerCommission(List list);

    void insertSalePackageCardTimeCardHandlerJobTypeCommission(List list);

    void deleteSalePackageCardTimeCardHandlerJobTypeCommission(Integer EntityID);

    void deleteSalePackageCardTimeCardHandlerCommission(Integer entityID);
}
