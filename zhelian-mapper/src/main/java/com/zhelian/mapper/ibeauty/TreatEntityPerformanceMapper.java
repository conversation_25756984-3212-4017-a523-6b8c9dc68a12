package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.*;
import com.zhelian.model.ibeauty.form.output.*;
import com.zhelian.model.ibeauty.service.TreatPerformanceEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatEntityPerformanceMapper {

    //获取门店业绩比例
    TreatPerformanceEntity getTreatProductEntityPerformance(@Param("EntityID") int EntityID, @Param("GoodsID") int GoodsID);

    TreatPerformanceEntity getTreatProjectEntityPerformance(@Param("EntityID") int EntityID, @Param("GoodsID") int GoodsID);

    TreatPerformanceEntity getTreatGeneralCardEntityPerformance(@Param("EntityID") int EntityID, @Param("GoodsID") int GoodsID, @Param("GeneralCardID") int GeneralCardID);

    TreatPerformanceEntity getTreatTimeCardEntityPerformance(@Param("EntityID") int EntityID, @Param("GoodsID") int GoodsID, @Param("TimeCardID") int TimeCardID);

    TreatPerformanceEntity getTreatSavingCardEntityPerformance(@Param("EntityID") int EntityID, @Param("GoodsID") int GoodsID);

    void createTreatEntityPerformanceProduct(TreatEntityPerformanceProductEntity treatEntityPerformanceProductEntity);

    void createTreatEntityPerformanceProject(TreatEntityPerformanceProjectEntity treatEntityPerformanceProjectEntity);

    void createTreatEntityPerformanceSavingCard(TreatEntityPerformanceSavingCardEntity treatEntityPerformanceSavingCardEntity);

    void createTreatEntityPerformanceGeneralCard(TreatEntityPerformanceGeneralCardEntity treatEntityPerformanceGeneralCardEntity);

    void createTreatEntityPerformanceTimeCard(TreatEntityPerformanceTimeCardEntity treatEntityPerformanceTimeCardEntity);

    void createTreatEntityPerformancePackageCardProduct(TreatEntityPerformancePackageCardProductEntity treatEntityPerformancePackageCardProductEntity);

    void createTreatEntityPerformancePackageCardProject(TreatEntityPerformancePackageCardProjectEntity treatEntityPerformancePackageCardProjectEntity);

    void createTreatEntityPerformancePackageCardSavingCard(TreatEntityPerformancePackageCardSavingCardEntity treatEntityPerformancePackageCardSavingCardEntity);

    void createTreatEntityPerformancePackageCardGeneralCard(TreatEntityPerformancePackageCardGeneralCardEntity treatEntityPerformancePackageCardGeneralCardEntity);

    void createTreatEntityPerformancePackageCardTimeCard(TreatEntityPerformancePackageCardTimeCardEntity treatEntityPerformancePackageCardTimeCardEntity);

    //删除门店非套餐卡业绩和提佣
    void deleteTreatEntityPerformanceCommissionProduct(@Param("TreatBillID") String TreatBillID);

    void deleteTreatEntityPerformanceCommissionProject(@Param("TreatBillID") String TreatBillID);

    void deleteTreatEntityPerformanceCommissionGeneralCard(@Param("TreatBillID") String TreatBillID);

    void deleteTreatEntityPerformanceCommissionTimeCard(@Param("TreatBillID") String TreatBillID);

    void deleteTreatEntityPerformanceCommissionSavingCard(@Param("TreatBillID") String TreatBillID);

    //删除门店套餐卡业绩和提佣
    void deleteTreatEntityPerformanceCommissionPackageCardProduct(@Param("TreatBillID") String TreatBillID);

    void deleteTreatEntityPerformanceCommissionPackageCardProject(@Param("TreatBillID") String TreatBillID);

    void deleteTreatEntityPerformanceCommissionPackageCardSavingCard(@Param("TreatBillID") String TreatBillID);

    void deleteTreatEntityPerformanceCommissionPackageCardGeneralCard(@Param("TreatBillID") String TreatBillID);

    void deleteTreatEntityPerformanceCommissionPackageCardTimeCard(@Param("TreatBillID") String TreatBillID);

    void updateTreatEntityPerformanceProductBillDate(@Param("TreatBillID") String TreatBillID, @Param("BillDate") String BillDate);

    void updateTreatEntityPerformanceProjectBillDate(@Param("TreatBillID") String TreatBillID, @Param("BillDate") String BillDate);

    void updateTreatEntityPerformanceGeneralCardBillDate(@Param("TreatBillID") String TreatBillID, @Param("BillDate") String BillDate);

    void updateTreatEntityPerformanceTimeCardBillDate(@Param("TreatBillID") String TreatBillID, @Param("BillDate") String BillDate);

    void updateTreatEntityPerformanceSavingCardBillDate(@Param("TreatBillID") String TreatBillID, @Param("BillDate") String BillDate);

    void updateTreatEntityPerformancePackageCardProductBillDate(@Param("TreatBillID") String TreatBillID, @Param("BillDate") String BillDate);

    void updateTreatEntityPerformancePackageCardProjectBillDate(@Param("TreatBillID") String TreatBillID, @Param("BillDate") String BillDate);

    void updateTreatEntityPerformancePackageCardGeneralCardBillDate(@Param("TreatBillID") String TreatBillID, @Param("BillDate") String BillDate);

    void updateTreatEntityPerformancePackageCardTimeCardBillDate(@Param("TreatBillID") String TreatBillID, @Param("BillDate") String BillDate);

    void updateTreatEntityPerformancePackageCardSavingCardBillDate(@Param("TreatBillID") String TreatBillID, @Param("BillDate") String BillDate);



    List<TreatBillProductEntityPerformanceOutputForm> getTreatBillProductEntityPerformance(@Param("BillID") String BillID);

    List<TreatBillProjectEntityPerformanceOutputForm> getTreatBillProjectEntityPerformance(@Param("BillID") String BillID);

    List<TreatBillGeneralCardEntityPerformanceOutputForm> getTreatBillGeneralCardEntityPerformance(@Param("BillID") String BillID);

    List<TreatBillTimeCardEntityPerformanceOutputForm> getTreatBillTimeCardEntityPerformance(@Param("BillID") String BillID);

    List<TreatBillSavingCardEntityPerformanceOutputForm> getTreatBillSavingCardEntityPerformance(@Param("BillID") String BillID);
    List<TreatBillPackageCardEntityPerformanceOutputForm> getTreatBillPackageCardEntityPerformance(@Param("BillID") String BillID);

    List<TreatBillProductEntityPerformanceOutputForm> getTreatBillPackageCardProductEntityPerformance(@Param("BillID") String BillID);

    List<TreatBillProjectEntityPerformanceOutputForm> getTreatBillPackageCardProjectEntityPerformance(@Param("BillID") String BillID);

    List<TreatBillGeneralCardEntityPerformanceOutputForm> getTreatBillPackageCardGeneralCardEntityPerformance(@Param("BillID") String BillID);

    List<TreatBillTimeCardEntityPerformanceOutputForm> getTreatBillPackageCardTimeCardEntityPerformance(@Param("BillID") String BillID);

    List<TreatBillSavingCardEntityPerformanceOutputForm> getTreatBillPackageCardSavingCardEntityPerformance(@Param("BillID") String BillID);


    void createTreatEntityPerformanceProductList(List<TreatEntityPerformanceProductEntity> list);

    void createTreatEntityPerformanceProjectList(List<TreatEntityPerformanceProjectEntity> list);

    void createTreatEntityPerformanceSavingCardList(List<TreatEntityPerformanceSavingCardEntity> list);

    void createTreatEntityPerformanceGeneralCardList(List<TreatEntityPerformanceGeneralCardEntity> list);

    void createTreatEntityPerformanceTimeCardList(List<TreatEntityPerformanceTimeCardEntity> list);

    void createTreatEntityPerformancePackageCardProductList(List<TreatEntityPerformancePackageCardProductEntity> list);

    void createTreatEntityPerformancePackageCardProjectList(List<TreatEntityPerformancePackageCardProjectEntity> list);

    void createTreatEntityPerformancePackageCardSavingCardList(List<TreatEntityPerformancePackageCardSavingCardEntity> list);

    void createTreatEntityPerformancePackageCardGeneralCardList(List<TreatEntityPerformancePackageCardGeneralCardEntity> list);

    void createTreatEntityPerformancePackageCardTimeCardList(List<TreatEntityPerformancePackageCardTimeCardEntity> list);

}