package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.TreatProductSchemeHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.TreatProductSchemeHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.output.TreatProductHandlerCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatProductSchemeHandlerCommissionMapper {

    List<TreatProductHandlerCommissionOutputForm> allTreatProductSchemeHandlerCommission(@Param("EntityID") Integer EntityID);

    void deleteTreatProductSchemeHandler(@Param("EntityID") Integer EntityID);

    void deleteTreatProductSchemeHandlerJobType(@Param("EntityID") Integer EntityID);

    void updateTreatProductSchemeHandlerCommission(List<TreatProductSchemeHandlerCommissionEntity> list);

    void insertTreatProductSchemeHandlerJobTypeCommission(List<TreatProductSchemeHandlerJobTypeCommissionEntity> list);
}
