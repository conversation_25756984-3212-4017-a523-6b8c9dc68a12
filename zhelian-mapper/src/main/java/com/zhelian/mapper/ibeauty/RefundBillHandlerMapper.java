package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface RefundBillHandlerMapper {

    void createRefundBillHandlerProduct(List<RefundBillHandlerProductEntity> list);

    void createRefundBillHandlerProject(List<RefundBillHandlerProjectEntity> list);

    void createRefundBillHandlerGeneralCard(List<RefundBillHandlerGeneralCardEntity> list);

    void createRefundBillHandlerTimeCard(List<RefundBillHandlerTimeCardEntity> list);

    void createRefundBillHandlerSavingCard(List<RefundBillHandlerSavingCardEntity> list);

    void createRefundBillHandlerPackageCard(List<RefundBillHandlerPackageCardEntity> list);

    List<RefundBillHandlerProductEntity> getRefundBillHandlerProduct(@Param("ID") Integer ID);

    List<RefundBillHandlerProjectEntity> getRefundBillHandlerProject(@Param("ID") Integer ID);

    List<RefundBillHandlerGeneralCardEntity> getRefundBillHandlerGeneralCard(@Param("ID") Integer ID);

    List<RefundBillHandlerTimeCardEntity> getRefundBillHandlerTimeCard(@Param("ID") Integer ID);

    List<RefundBillHandlerSavingCardEntity> getRefundBillHandlerSavingCard(@Param("ID") Integer ID);

    List<RefundBillHandlerPackageCardEntity> getRefundBillHandlerPackageCard(@Param("ID") Integer ID);
}
