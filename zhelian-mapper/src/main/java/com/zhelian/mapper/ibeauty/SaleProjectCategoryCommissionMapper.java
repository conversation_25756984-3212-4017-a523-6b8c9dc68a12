package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SaleProjectCategoryCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleProjectCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.output.SaleProjectCategoryOutputForm;
import com.zhelian.model.ibeauty.form.output.SaleProjectChildCategoryCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleProjectCategoryCommissionMapper {

    SaleProjectCategoryOutputForm saleProjectCategoryCommission(@Param("EntityID") Integer EntityID);

    List<SaleProjectChildCategoryCommissionOutputForm> allSaleProjectCategoryCommission(@Param("EntityID") Integer EntityID);

    void updateSaleProjectCommissionScheme(SaleProjectCommissionSchemeEntity saleProjectCommissionSchemeEntity);

    void insertSaleProjectCategoryCommissionEntity(List<SaleProjectCategoryCommissionEntity> list);


    void deleteSaleProjectCategoryCommissionEntity(@Param("EntityID") Integer EntityID);
}
