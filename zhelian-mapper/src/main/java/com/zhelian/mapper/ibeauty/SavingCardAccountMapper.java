package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SavingCardAccountEntity;
import com.zhelian.model.ibeauty.entity.SavingCardAccountExtendEntity;
import com.zhelian.model.ibeauty.form.input.TreatSavingCardAccountForm;
import com.zhelian.model.ibeauty.form.output.*;
import com.zhelian.model.micromall.form.output.ApplyEntityOutputForm;
import com.zhelian.model.micromall.form.output.MicromallSavingCardAccountOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;


@Repository
public interface SavingCardAccountMapper {

    List<AllSavingCardAccountOutputForm> allSavingCardAccount(@Param("CustomerID") String CustomerID);

    List<AllSavingCardAccountOutputForm> allSomeGoodsSavingCardAccount(@Param("CustomerID") String CustomerID);

    List<SavingCardAccountOutputForm> savingCardProductAccount(@Param("CustomerID") String CustomerID, @Param("ProductID") Integer ProductID);

    List<SavingCardAccountOutputForm> savingCardProjectAccount(@Param("CustomerID") String CustomerID, @Param("ProjectID") Integer ProjectID);

    List<SavingCardAccountOutputForm> savingCardGeneralCardAccount(@Param("CustomerID") String CustomerID, @Param("GeneralCardID") Integer GeneralCardID);

    List<SavingCardAccountOutputForm> savingCardTimeCardAccount(@Param("CustomerID") String CustomerID, @Param("TimeCardID") Integer TimeCardID);

    List<SavingCardAccountOutputForm> savingCardPackageCardAccount(@Param("CustomerID") String CustomerID, @Param("PackageCardID") Integer PackageCardID);

    SavingCardAccountExtendEntity getSavingCardAccountByID(Integer ID);

    void updateSavingCardAccountList(List<SavingCardAccountExtendEntity> list);

    void updateSavingCardAccountValidDate(SavingCardAccountEntity savingCardAccountEntity);

    void createSavingCardAccount(List<SavingCardAccountEntity> list);

    //储值卡账户列表
    List<SavingCardAccountForm> getSavingCardAccount(TreatSavingCardAccountForm form);

    //查询储值卡适用分类下的项目
    List<SavingCardProjectOutputForm> getSavingCardProjectList(@Param("SavingCardID") Integer SavingCardID, @Param("Type") Integer Type, @Param("Name") String Name, @Param("EntityID") Integer EntityID);

    List<SavingCardProjectChildDetailOutputForm> getSavingCardProjectByCategory(@Param("SavingCardID") Integer SavingCardID, @Param("Type") Integer Type, @Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    TreatSavingCardAccountProjectCustomerDiscountOutForm getSavingCardAccountProjectCustomerDiscount(@Param("ProjectID") Integer ProjectID, @Param("CustomerID") Integer CustomerID);

    List<SaleGoodsParentCategoryOutputForm> savingCardAccountProjectCategory(@Param("SavingCardID") Integer SavingCardID, @Param("Type") Integer Type, @Param("Name") String Name, @Param("EntityID") Integer EntityID);

    List<SavingCardProjectOutputForm> getSavingCardAllProjectList(@Param("Name") String Name, @Param("EntityID") Integer EntityID);

    List<SavingCardProjectChildDetailOutputForm> getSavingCardAllProjectByCategory(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    List<SaleGoodsParentCategoryOutputForm> savingCardAccountAllProjectCategory(@Param("Name") String Name, @Param("EntityID") Integer EntityID);

    void updateSavingCardAccountBalance(SavingCardAccountEntity savingCardAccountEntity);

    void updateSavingCardAccountActiveFalse(@Param("SaleBillID") String SaleBillID);

    void updatePackageCardSavingCardAccountActiveFalse(@Param("SaleBillID") String SaleBillID);

    List<SavingCardAccountForm> arrearSavingCardAccount(@Param("CustomerID") String CustomerID, @Param("Name") String Name, @Param("EntityID") Integer EntityID);

    void updateArrearSavingCardAccount(SavingCardAccountEntity savingCardAccountEntity);

    List<SavingCardAccountForm> getPostPoneSavingCardAccount(@Param("CustomerID") String CustomerID, @Param("Name") String Name);

    void updateSavingCardValidDate(SavingCardAccountEntity savingCardAccountEntity);

    List<SavingCardAccountForm> getTranserSavingCardAccount(@Param("CustomerID") String CustomerID, @Param("Name") String Name);

    List<SavingCardAccountForm> getRefundSavingCardAccount(@Param("CustomerID") String CustomerID, @Param("Name") String Name);

    void createTranserSavingCardAccount(SavingCardAccountEntity savingCardAccountEntity);

    TranserSavingCardAccountOutputForm getSavingCardAccountEntity(@Param("ID") Integer ID);

    void updateSavingCardAccountEntityBalance(@Param("ID") Integer ID, @Param("Amount") BigDecimal Amount, @Param("LargessAmount") BigDecimal LargessAmount);

    List<CustomerSavingCardAccountForm> customerSavingCardAccount(@Param("CustomerID") String CustomerID, @Param("Active") Boolean Active, @Param("Name") String Name);

    List<SavingCardAccountForm> customerMiniprogramSavingCardAccount(@Param("CustomerID") String CustomerID, @Param("Active") Boolean Active, @Param("Name") String Name);

    List<MicromallSavingCardAccountOutputForm> customerMicromallSavingCardAccount(@Param("CustomerID") Integer CustomerID, @Param("Active") Boolean Active, @Param("Name") String Name);

    List<ApplyEntityOutputForm> customerMicromallSavingCardAccountEntity(@Param("ID") Integer ID, @Param("Name") String Name, @Param("CityCode") String CityCode, @Param("Longitude") String Longitude, @Param("Latitude") String Latitude);

    CustomerSavingCardAccountSumOutputForm customerSavingCardAccountSum(@Param("CustomerID") String CustomerID, @Param("Active") Boolean Active, @Param("Name") String Name);

    List<SavingCardAccountForm> rechargeSavingCardAccount(@Param("CustomerID") String CustomerID, @Param("Name") String Name);

    void updateSavingCardRemark(@Param("SaleBillGoodID") Integer SaleBillGoodID, @Param("Remark") String Remark);

    void updatePackageCardSavingCardRemark(@Param("SaleBillGoodID") Integer SaleBillGoodID, @Param("Remark") String Remark);

    void updateTransferSavingCardAccountActiveFalse(@Param("ID") String ID);

    void updateSavingCardBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    void updatePackageCardSavingCardBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);
}
