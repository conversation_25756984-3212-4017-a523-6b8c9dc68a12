package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SaleGeneralCardCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.output.SaleGeneralCardCommissionSchemeOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleGeneralCardCommissionSchemeMapper {

    List<SaleGeneralCardCommissionSchemeOutputForm> saleGeneralCardCommissionSchemeList(@Param("Name") String Name);

    void createSaleGeneralCardCommissionScheme(SaleGeneralCardCommissionSchemeEntity saleGeneralCardCommissionSchemeEntity);

    void deleteSaleGeneralCardCommissionScheme(@Param("EntityID") Integer EntityID);

    Integer findSaleGeneralCardCommissionSchemeByEntityID(@Param("EntityID") Integer EntityID);
}
