package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.*;
import com.zhelian.model.ibeauty.form.input.ProjectConsumableCategoryQueryForm;
import com.zhelian.model.ibeauty.form.input.ProjectConsumableCategoryUpdateForm;
import com.zhelian.model.ibeauty.form.input.ProjectConsumableTemplateQueryForm;
import com.zhelian.model.ibeauty.form.output.CategoryAndProjectOutputForm;
import com.zhelian.model.ibeauty.form.output.ProjectConsumableTemplateDetailOutputForm;
import com.zhelian.model.ibeauty.form.output.ProjectConsumableTemplateOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProjectConsumableTemplateMapper {

    List<ProjectConsumableTemplateOutputForm> getProjectConsumableTemplate(ProjectConsumableTemplateQueryForm form);

    void addProjectConsumableTemplate(ProjectConsumableTemplateEntity projectConsumableTemplateEntity);

    void addProjectConsumableTemplateProduct(List<ProjectConsumableTemplateProductEntity> productAddList);

    void addProjectConsumableTemplateProject(List<ProjectConsumableTemplateProjectEntity> projectAddList);

    void addProjectConsumableTemplateProjectCategory(List<ProjectConsumableTemplateProjectCategoryEntity> projectCategoryAddList);

    void updateProjectConsumableTemplate(ProjectConsumableTemplateEntity projectConsumableTemplateEntity);

    void deleteProjectConsumableTemplateProduct(@Param("ID") Integer ID);

    void deleteProjectConsumableTemplateProject(@Param("ID") Integer ID);

    void deleteProjectConsumableTemplateProjectCategory(@Param("ID") Integer ID);

    List<ProjectConsumableCategoryEntity> getProjectConsumableCategory(ProjectConsumableCategoryQueryForm form);

    Integer getProjectConsumableCategoryCount(@Param("ParentID") Integer ParentID);

    void addProjectConsumableCategory(ProjectConsumableCategoryEntity form);

    void updateProjectConsumableCategory(ProjectConsumableCategoryUpdateForm form);

    Integer getProjectConsumableTemplateCountByCategory(@Param("CategoryID") Integer CategoryID);

    List<CategoryAndProjectOutputForm> findCategoryAndProject();

    ProjectConsumableTemplateDetailOutputForm getProjectConsumableTemplateDetail(@Param("ID") Integer ID);
}
