package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.GeneralCardEntity;
import com.zhelian.model.ibeauty.entity.GeneralCardProjectEntity;
import com.zhelian.model.ibeauty.entity.GeneralCardSaleEntity;
import com.zhelian.model.ibeauty.entity.TbGeneralCardImageEntity;
import com.zhelian.model.ibeauty.form.input.GeneralCardQueryEntityForm;
import com.zhelian.model.ibeauty.form.input.GoodsImageUrlForm;
import com.zhelian.model.ibeauty.form.output.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;

@Repository
public interface GeneralCardMapper {
    List<GeneralCardOutputForm> generalCardList(@Param("Name") String Name, @Param("GeneralCardCategoryID") Integer GeneralCardCategoryID, @Param("IsAllowSell") Boolean IsAllowSell);

    GeneralCardDetailsOutputForm getGeneralCardDetails(@Param("ID") int ID);

    Integer[] generalCardSaleEntity(GeneralCardQueryEntityForm form);

    Integer[] generalCardConsumeEntity(GeneralCardQueryEntityForm form);

    Integer[] generalCardModifyPriceEntity(GeneralCardQueryEntityForm form);

    List<GeneralCardProjectOutputForm> generalCardProject(GeneralCardQueryEntityForm form);

    void addGeneralCard(GeneralCardEntity entity);

    void addGeneralCardProject(List<GeneralCardProjectEntity> generalCardProject);

    void addGeneralCardConsume(List<GeneralCardSaleEntity> generalCardConsumeEntity);

    void addGeneralCardModifyPrice(List<GeneralCardSaleEntity> generalCardConsumeEntity);

    void addGeneralCardSale(List<GeneralCardSaleEntity> generalCardSaleEntity);

    void updateGeneralCard(GeneralCardEntity generalCardEntity);

    void deleteGeneralCardProject(Integer ID);

    void deleteGeneralCardConsume(Integer ID);

    void deleteGeneralCardSale(Integer ID);

    void deleteGeneralCardModify(Integer ID);

    List<CategoryAndProjectOutputForm> findCategoryAndProject();

    List<AllGeneralCardOutputForm> allGeneralCard();

    GeneralCardEntity getGeneralCardByID(Integer ID);

    List<GoodsImageUrlForm> getGeneralCardImageList(@Param("ID") Integer ID);

    void deleteGeneralCardImageByID(@Param("ID") Integer ID);

    void insertGeneralCardImageURL(@Param("GeneralCardID") Integer GeneralCardID, @Param("ImageURL") String ImageURL, @Param("Sequence") Integer Sequence);

    LinkedList<Integer> getASCList(@Param("ID") int ID, @Param("GeneralCardID") int GeneralCardID);

    void updateSequence(List<TbGeneralCardImageEntity> list);

    void updateGeneralCardRemark(@Param("SaleBillGoodID") Integer SaleBillGoodID, @Param("Remark") String Remark);

    void updatePackageCardGeneralCardRemark(@Param("SaleBillGoodID") Integer SaleBillGoodID, @Param("Remark") String Remark);

    void deleteBitchSaleEntity(Integer[] array);

    void deleteBitchConsumeEntity(Integer[] array);
}
