package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.TreatProjectCategoryCommissionEntity;
import com.zhelian.model.ibeauty.entity.TreatProjectCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.output.TreatProjectCategoryOutputForm;
import com.zhelian.model.ibeauty.form.output.TreatProjectChildCategoryCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatProjectCategoryCommissionMapper {

    TreatProjectCategoryOutputForm treatProjectCategoryCommission(@Param("EntityID") Integer EntityID);

    List<TreatProjectChildCategoryCommissionOutputForm> allTreatProjectCategoryCommission(@Param("EntityID") Integer EntityID);

    void updateTreatProjectCommissionScheme(TreatProjectCommissionSchemeEntity treatProjectCommissionSchemeEntity);

    void insertTreatProjectCategoryCommissionEntity(List<TreatProjectCategoryCommissionEntity> list);

    void deleteTreatProjectCategoryCommissionEntity(@Param("EntityID") Integer EntityID);
}
