package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.ProjectCategoryEntity;
import com.zhelian.model.ibeauty.form.output.ProjectCategoryOutputForm;
import com.zhelian.model.ibeauty.form.output.TopProjectCategoryOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;


@Repository
public interface ProjectCategoryMapper {


    List<ProjectCategoryOutputForm> projectCategoryList(@Param("Name") String Name, @Param("Active") Boolean Active);

    List<ProjectCategoryOutputForm> getValidProjectCategoryValid();

    List<TopProjectCategoryOutputForm> getTopProjectList(@Param("Active") Boolean Active);

    Integer getMaxSequence();

    void createProjectCategory(ProjectCategoryEntity projectCategoryEntity);

    void updateProjectCategory(ProjectCategoryEntity projectCategoryEntity);

    LinkedList<Integer> getASCList(@Param("ID") int ID, @Param("ParentID") int ParentID);

    void updateSequence(List<ProjectCategoryEntity> list);

    ProjectCategoryEntity getProjectCategoryEntity(Integer moveID);

    Integer childActiveCount(@Param("ID") Integer ID);

    Integer projectByCategoryCount(@Param("ID") Integer ID);
}
