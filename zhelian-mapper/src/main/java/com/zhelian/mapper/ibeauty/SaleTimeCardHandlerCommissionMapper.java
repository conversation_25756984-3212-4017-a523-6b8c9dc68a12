package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.form.output.SaleTimeCardHandlerCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleTimeCardHandlerCommissionMapper {

    List<SaleTimeCardHandlerCommissionOutputForm> allSaleTimeCardHandlerCommission(@Param("EntityID") Integer EntityID, @Param("TimeCardID") Integer TimeCardID);

    void deleteSaleTimeCardHandler(@Param("EntityID") Integer EntityID, @Param("TimeCardID") Integer TimeCardID);

    void deleteSaleTimeCardHandlerJobType(@Param("EntityID") Integer EntityID, @Param("TimeCardID") Integer TimeCardID);

    void insertSaleTimeCardHandlerCommission(List list);

    void insertSaleTimeCardHandlerJobTypeCommission(List list);

    void deleteSaleTimeCardHandlerJobTypeCommission(Integer EntityID);

    void deleteSaleTimeCardHandlerCommission(Integer entityID);
}
