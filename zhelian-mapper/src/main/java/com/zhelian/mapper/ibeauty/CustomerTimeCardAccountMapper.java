package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.form.output.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CustomerTimeCardAccountMapper {

    List<AccountOutputForm> getTreatBill(@Param("ID") String ID);

    List<CustomerTimeCardAccountRefundBillOutputForm> getRefundBill(@Param("ID") String ID);

    List<CustomerAccountTreatBillOutputForm> getRefundTreatBill(@Param("ID") String ID);

    List<CustomerTransferAccountOutputForm> getTransferBill(@Param("ID") String ID);

    List<CustomerRepaymentAccountOutputForm> getRepayment(@Param("ID") String ID);

    List<CustomerRepaymentAccountOutputForm> getPackageCardRepayment(@Param("ID") String ID);

    List<AccountOutputForm> getArrearAmountProcess(@Param("ID") String ID);
}
