package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.TreatSavingCardHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.TreatSavingCardHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.output.TreatSavingCardProjectHandlerCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatSavingCardHandlerCommissionMapper {

    List<TreatSavingCardProjectHandlerCommissionOutputForm> allTreatSavingCardHandlerCommission(@Param("EntityID") Integer EntityID, @Param("SavingCardID") Integer SavingCardID);

    void deleteTreatSavingCardHandler(@Param("EntityID") Integer EntityID, @Param("SavingCardID") Integer SavingCardID);

    void deleteTreatSavingCardHandlerJobType(@Param("EntityID") Integer EntityID, @Param("SavingCardID") Integer SavingCardID);

    void innsertTreatSavingCardHandlerCommission(List<TreatSavingCardHandlerCommissionEntity> list);

    void insertTreatSavingCardHandlerJobTypeCommission(List<TreatSavingCardHandlerJobTypeCommissionEntity> list);

    void deleteTreatHandlerCommissionByEntity(Integer EntityID);

    void deleteTreatHandlerJobTypeCommissionByEntity(Integer EntityID);
}
