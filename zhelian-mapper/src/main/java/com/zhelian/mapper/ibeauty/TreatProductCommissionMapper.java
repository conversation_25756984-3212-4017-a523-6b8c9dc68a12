package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.TreatProductCommissionEntity;
import com.zhelian.model.ibeauty.form.output.TreatProductCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface TreatProductCommissionMapper {

    List<TreatProductCommissionOutputForm> allTreatProductCommission(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertTreatProductCommission(List<TreatProductCommissionEntity> list);

    void deleteTreatProductCommission(Map<String, Object> map);

    void deleteTreatProductCommissionByEntity(Integer EntityID);

    void deleteTreatProductCommissionByEntityAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);
}
