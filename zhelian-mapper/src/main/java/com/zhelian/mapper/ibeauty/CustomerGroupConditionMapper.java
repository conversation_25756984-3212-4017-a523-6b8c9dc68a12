package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.CustomerGroupConditionEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Description:
 * @author: zhanglongyang
 * @date: 2021/05/17
 * @time: 16:33
 */
@Repository
public interface CustomerGroupConditionMapper {

    //分群条件增加
    void addGroupCondition(CustomerGroupConditionEntity customerGroupCondition);

    //分群条件删除
    void delGroupCondition(CustomerGroupConditionEntity customerGroupCondition);

    //分群条件查询避免重复添加
    List<CustomerGroupConditionEntity> getGroupCondition(CustomerGroupConditionEntity customerGroupCondition);

    //查询获取一个分区类型下的条件
    List<CustomerGroupConditionEntity> getManyGroupCondition(Integer Cg_Id);

    //根据条件id进行修改其中规则数值
    void updateGroupCondition(CustomerGroupConditionEntity groupCondition);

}
