package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.form.input.AppointmentProjectQueryForm;
import com.zhelian.model.ibeauty.form.output.AppointmentProjectOutputForm;
import com.zhelian.model.ibeauty.form.output.CategoryAndProjectOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AppointmentProjectMapper {


    List<AppointmentProjectOutputForm> all(AppointmentProjectQueryForm form);

    void delete();

    void add(@Param("list") List<Integer> list);

    List<CategoryAndProjectOutputForm> findCategoryAndProject();
}
