package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.form.output.*;
import com.zhelian.model.ibeauty.service.SaleEntityPerformanceGoodsEntity;
import com.zhelian.model.ibeauty.service.SaleEntityPerformancePackageCardGoodsEntity;
import com.zhelian.model.ibeauty.service.SalePerformanceEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SaleEntityPerformanceMapper {

    //获取门店业绩比例
    SalePerformanceEntity getSaleProductPerformance(@Param("EntityID") int EntityID, @Param("GoodsID") int GoodsID);

    SalePerformanceEntity getSaleProjectPerformance(@Param("EntityID") int EntityID, @Param("GoodsID") int GoodsID);

    SalePerformanceEntity getSaleGeneralCardPerformance(@Param("EntityID") int EntityID, @Param("GoodsID") int GoodsID);

    SalePerformanceEntity getSaleTimeCardPerformance(@Param("EntityID") int EntityID, @Param("GoodsID") int GoodsID);

    SalePerformanceEntity getSaleSavingCardPerformance(@Param("EntityID") int EntityID, @Param("GoodsID") int GoodsID);

    //创建门店非套餐卡业绩
    void createSaleEntityPerformanceProduct(List<SaleEntityPerformanceGoodsEntity> list);

    void createSaleEntityPerformanceProject(List<SaleEntityPerformanceGoodsEntity> list);

    void createSaleEntityPerformanceGeneralCard(List<SaleEntityPerformanceGoodsEntity> list);

    void createSaleEntityPerformanceTimeCard(List<SaleEntityPerformanceGoodsEntity> list);

    void createSaleEntityPerformanceSavingCard(List<SaleEntityPerformanceGoodsEntity> list);

    //创建门店套餐卡业绩
    void createSaleEntityPerformancePackageCardProduct(List<SaleEntityPerformancePackageCardGoodsEntity> list);

    void createSaleEntityPerformancePackageCardProject(List<SaleEntityPerformancePackageCardGoodsEntity> list);

    void createSaleEntityPerformancePackageCardSavingCard(List<SaleEntityPerformancePackageCardGoodsEntity> list);

    void createSaleEntityPerformancePackageCardGeneralCard(List<SaleEntityPerformancePackageCardGoodsEntity> list);

    void createSaleEntityPerformancePackageCardTimeCard(List<SaleEntityPerformancePackageCardGoodsEntity> list);


    //删除门店非套餐卡业绩和提佣
    void deleteSaleEntityPerformanceCommissionProduct(@Param("SaleBillID") String SaleBillID);

    void deleteSaleEntityPerformanceCommissionProject(@Param("SaleBillID") String SaleBillID);

    void deleteSaleEntityPerformanceCommissionGeneralCard(@Param("SaleBillID") String SaleBillID);

    void deleteSaleEntityPerformanceCommissionTimeCard(@Param("SaleBillID") String SaleBillID);

    void deleteSaleEntityPerformanceCommissionSavingCard(@Param("SaleBillID") String SaleBillID);


    //删除门店套餐卡业绩和提佣
    void deleteSaleEntityPerformanceCommissionPackageCardProduct(@Param("SaleBillID") String SaleBillID);

    void deleteSaleEntityPerformanceCommissionPackageCardProject(@Param("SaleBillID") String SaleBillID);

    void deleteSaleEntityPerformanceCommissionPackageCardSavingCard(@Param("SaleBillID") String SaleBillID);

    void deleteSaleEntityPerformanceCommissionPackageCardGeneralCard(@Param("SaleBillID") String SaleBillID);

    void deleteSaleEntityPerformanceCommissionPackageCardTimeCard(@Param("SaleBillID") String SaleBillID);

    //更改时间
    void updateSaleEntityPerformanceProductBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    void updateSaleEntityPerformanceProjectBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    void updateSaleEntityPerformanceGeneralCardBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    void updateSaleEntityPerformanceTimeCardBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    void updateSaleEntityPerformanceSavingCardBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    void updateSaleEntityPerformancePackageCardProductBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    void updateSaleEntityPerformancePackageCardProjectBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    void updateSaleEntityPerformancePackageCardSavingCardBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    void updateSaleEntityPerformancePackageCardGeneralCardBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    void updateSaleEntityPerformancePackageCardTimeCardBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    List<SaleBillEntityPerformanceProductOutputForm> saleBillProductEntityPerformance(String SaleBillID);
    List<SaleBillEntityPerformanceProjectOutputForm> saleBillProjectEntityPerformance(String SaleBillID);
    List<SaleBillEntityPerformanceGeneralCardOutputForm> saleBillGeneralCardEntityPerformance(String SaleBillID);
    List<SaleBillEntityPerformanceTimeCardOutputForm> saleBillTimeCardEntityPerformance(String SaleBillID);
    List<SaleBillEntityPerformanceSavingCardOutputForm> saleBillSavingCardEntityPerformance(String SaleBillID);

    List<SaleBillEntityPerformancePackageCardProductListOutputForm> saleBillPackageCardProductEntityPerformance(String SaleBillID);
    List<SaleBillEntityPerformancePackageCardProjectListOutputForm> saleBillPackageCardProjectEntityPerformance(String SaleBillID);
    List<SaleBillEntityPerformancePackageCardGeneralCardListOutputForm> saleBillPackageCardGeneralCardEntityPerformance(String SaleBillID);
    List<SaleBillEntityPerformancePackageCardTimeCardListOutputForm> saleBillPackageCardTimeCardEntityPerformance(String SaleBillID);
    List<SaleBillEntityPerformancePackageCardSavingCardListOutputForm> saleBillPackageCardSavingCardEntityPerformance(String SaleBillID);


}
