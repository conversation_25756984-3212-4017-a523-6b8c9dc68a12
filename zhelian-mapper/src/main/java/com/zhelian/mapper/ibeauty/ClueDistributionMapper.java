package com.zhelian.mapper.ibeauty;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.entity.ClueDistributionConfigEntity;
import com.zhelian.model.ibeauty.entity.ClueDistributionEmployeeEntity;
import com.zhelian.model.ibeauty.entity.ClueDistributionEntity;
import com.zhelian.model.ibeauty.entity.ClueDistributionLogEntity;
import com.zhelian.model.ibeauty.form.output.ClueDistributionDetailOutForm;
import com.zhelian.model.ibeauty.form.output.ClueDistributionEmployeeConfigAllOutForm;
import com.zhelian.model.ibeauty.form.output.ClueDistributionOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ClueDistributionMapper {

    ClueDistributionEmployeeConfigAllOutForm employeeConfigAll();

    void deleteEmployeeConfig(@Param("EmployeeID") String EmployeeID);

    void addEmployeeConfig(@Param("EmployeeID") String EmployeeID);

    List<ClueDistributionOutForm> list(@Param("ProvinceCode") String ProvinceCode, @Param("CityCode") String CityCode, @Param("Active") Boolean active);

    List<ClueDistributionDetailOutForm> detail(@Param("ID") Integer ID);

    ClueDistributionEntity getClueDistributionByID(@Param("ID") Integer ID);

    ClueDistributionEntity getClueDistributionByProvinceCodeAndCityCode(@Param("ProvinceCode") String ProvinceCode, @Param("CityCode") String CityCode);

    void addClueDistribution(ClueDistributionEntity entity);

    void addClueDistributionEmployee(List<ClueDistributionEmployeeEntity> list);

    void updateClueDistribution(ClueDistributionEntity entity);

    void deleteClueDistributionEmployeeByID(@Param("ID") Integer ID);

    String getProvinceCode(@Param("Name") String Name);

    String getCityCode(@Param("Name") String Name,@Param("ProvinceCode") String ProvinceCode);

    String getAreaCode(@Param("Name") String Name,@Param("CityCode") String CityCode);

    ClueDistributionEmployeeEntity getClueDistribution(@Param("ProvinceCode") String ProvinceCode, @Param("CityCode") String CityCode);

    String getClueDistributionEmployeeConfig();

    Integer getEmployeeEntity(@Param("EmployeeID") String EmployeeID);

    void updateClueDistributionEmployeeAddDistributionNum(@Param("ID") Integer ID,@Param("EmployeeID") String EmployeeID);

    void addClueDistributionLogEntity(ClueDistributionLogEntity entity);

    List<ClueDistributionConfigEntity> getClueDistributionConfigEntity();

    void updateClueDistributionConfigEntity(ClueDistributionConfigEntity entity);

    ClueDistributionLogEntity getClueDistributionLogByClueID(@Param("ClueId") String ClueId);
}
