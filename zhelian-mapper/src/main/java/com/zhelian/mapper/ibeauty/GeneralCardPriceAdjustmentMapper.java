package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.GeneralCardPriceAdjustmentEntity;
import com.zhelian.model.ibeauty.form.output.GeneralCardPriceAdjustmentOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface GeneralCardPriceAdjustmentMapper {
    List<GeneralCardPriceAdjustmentOutputForm> generalCardPriceAdjustmentList(@Param("GeneralCardName") String GeneralCardName, @Param("EntityName") String EntityName);

    Integer generalCardPriceAdjustmentCount(@Param("EntityID") Integer EntityID, @Param("GeneralCardID") Integer GeneralCardID);

    void insertGeneralCardPriceAdjustmentEntity(GeneralCardPriceAdjustmentEntity GeneralCardPriceAdjustmentEntity);

    void updateGeneralCardPriceAdjustmentEntity(GeneralCardPriceAdjustmentEntity GeneralCardPriceAdjustmentEntity);

    void deleteGeneralCardPriceAdjustmentEntity(@Param("EntityID") Integer EntityID, @Param("GeneralCardID") Integer GeneralCardID);
}
