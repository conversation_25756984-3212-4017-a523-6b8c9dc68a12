package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.AdditionalIncomeAttachmentEntity;
import com.zhelian.model.ibeauty.entity.AdditionalIncomeEntity;
import com.zhelian.model.ibeauty.form.output.AdditionalIncomeOutputEasyExcelForm;
import com.zhelian.model.ibeauty.form.output.AdditionalIncomeOutputForm;
import com.zhelian.model.ibeauty.form.output.AdditionalIncomeSumOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AdditionalIncomeMapper {

    List<AdditionalIncomeOutputForm> additionalIncomeList(@Param("EmployeeID") String EmployeeID, @Param("IncomeSource") String IncomeSource, @Param("EntityID") Integer EntityID, @Param("IncomeCategoryID") Integer IncomeCategoryID, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate);

    void insertAdditionalIncome(AdditionalIncomeEntity additionalIncomeEntity);

    void updateAdditionalIncome(AdditionalIncomeEntity additionalIncomeEntity);

    void deleteAdditionalIncomeByID(@Param("ID") Integer ID);

    AdditionalIncomeSumOutputForm additionalIncomeSum(@Param("EmployeeID") String EmployeeID, @Param("IncomeSource") String IncomeSource, @Param("EntityID") Integer EntityID, @Param("IncomeCategoryID") Integer IncomeCategoryID, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate);

    void addAttachment(@Param("AdditionalIncomeID") Integer AdditionalIncomeID, @Param("AttachmentURL") String AttachmentURL, @Param("AttachmentType") Integer AttachmentType);

    void deleteAttachment(@Param("ID") Integer ID);

    String getAttachmentByID(@Param("ID") Integer ID);

    List<AdditionalIncomeAttachmentEntity> getAttachment(@Param("ID") Integer ID);

    List<Object> excelAdditionalIncomeList(@Param("EmployeeID") String EmployeeID, @Param("IncomeSource") String IncomeSource, @Param("EntityID") Integer EntityID, @Param("IncomeCategoryID") Integer IncomeCategoryID, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate);

    List<AdditionalIncomeOutputEasyExcelForm> easyExcelAdditionalIncomeList(@Param("EmployeeID") String EmployeeID, @Param("IncomeSource") String IncomeSource, @Param("EntityID") Integer EntityID, @Param("IncomeCategoryID") Integer IncomeCategoryID, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate);

    AdditionalIncomeEntity getAdditionalIncome(Integer ID);
}
