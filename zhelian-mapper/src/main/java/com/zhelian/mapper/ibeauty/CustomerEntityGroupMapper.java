package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.CustomerEntity;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * @Description: 获取当前门店会员分群信息
 * @author: zhanglongyang
 * @date: 2021/05/19
 * @time: 14:40
 */
@Repository
public interface CustomerEntityGroupMapper {

    //查询该门店下的会员分群信息
    List<Map<String, Object>> getCustomerEntityGroup(Map map);

    //获取当前门店下的会员数
    List<CustomerEntity> getAllCustomer(Integer ID);

    //获取所有的分群类型下的分区规则
    List<Map<String, Object>> getRule();
    //会员等级(有区间)

    //最近无销售

    //最近有销售

    //销售金额(有区间)

    //销售客单价(有区间)

    //销售次数(有区间)

    //最近无消耗

    //最近有消耗

    //消耗金额(有区间)

    //消耗客单价(有区间)

    //消耗次数(有区间)


}
