package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SalePackageCardCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.output.SalePackageCardCommissionSchemeOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SalePackageCardCommissionSchemeMapper {

    List<SalePackageCardCommissionSchemeOutputForm> salePackageCardCommissionSchemeList(@Param("Name") String Name);

    void createSalePackageCardCommissionScheme(SalePackageCardCommissionSchemeEntity salePackageCardCommissionSchemeEntity);

    void deleteSalePackageCardCommissionScheme(@Param("EntityID") Integer EntityID);

    Integer findSalePackageCardCommissionSchemeByEntityID(@Param("EntityID") Integer EntityID);
}
