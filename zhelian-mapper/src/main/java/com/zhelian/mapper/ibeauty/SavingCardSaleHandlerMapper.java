package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SavingCardSaleHandlerEntity;
import com.zhelian.model.ibeauty.entity.SavingCardSaleHandlerJobTypeEntity;
import com.zhelian.model.ibeauty.form.output.SaleHandlerOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;


@Repository
public interface SavingCardSaleHandlerMapper {


    List<SaleHandlerOutputForm> allSavingCardSaleHandler(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("Active") Boolean Active);

    Integer getMaxSequence();

    void createSavingCardSaleHandler(SavingCardSaleHandlerEntity savingCardSaleHandlerEntity);

    void insertSavingCardSaleHandlerJobType(List<SavingCardSaleHandlerJobTypeEntity> list);

    void updateSavingCardSaleHandler(SavingCardSaleHandlerEntity savingCardSaleHandlerEntity);

    void deleteSaleHandlerJobType(@Param("ID") Integer ID);

    LinkedList<Integer> getIDOrderBySequenceASC(Integer ID);

    void updateSequence(List<SavingCardSaleHandlerEntity> list);
}
