package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SaleProductCommissionEntity;
import com.zhelian.model.ibeauty.form.output.SaleProductCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface SaleProductCommissionMapper {

    List<SaleProductCommissionOutputForm> allSaleProductCommission(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertSaleProductCommission(List<SaleProductCommissionEntity> list);

    void deleteSaleProductCommission(Map<String, Object> map);

    void deleteSaleProductCommissionByEntityID(@Param("EntityID") Integer EntityID);

    void deleteSaleProductCommissionByEntityIDAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);
}
