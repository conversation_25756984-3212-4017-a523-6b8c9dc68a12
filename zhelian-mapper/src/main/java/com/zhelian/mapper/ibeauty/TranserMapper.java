package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.*;
import com.zhelian.model.ibeauty.form.output.*;
import com.zhelian.model.miniprogram.form.output.MiniprogramTranserBillOutputForm;
import com.zhelian.model.miniprogram.form.output.SignatureOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TranserMapper {
    void createTransferBill(TransferBillEntity transferBillEntity);

    void insertTranserProduct(List<TransferBillProductAccountEntity> list);

    void insertTranserProject(List<TransferBillProjectAccountEntity> list);

    void insertTranserGeneralCard(List<TransferBillGeneralCardAccountEntity> list);

    void insertTranserTimeCard(List<TransferBillTimeCardAccountEntity> list);

    void insertTranserSavingCard(List<TransferBillSavingCardAccountEntity> list);

    List<TranserBillOutputForm> getTranserBill(@Param("EntityID") Integer EntityID, @Param("TranserBillID") String TranserBillID, @Param("TransferOutCustomerID") String TransferOutCustomerID, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("BillStatus") String BillStatus);

    TranserBillInfoOutputForm getTranserBillInfo(@Param("ID") String ID);

    List<TranserBillProductOutputForm> getTranserBillProductInfo(@Param("ID") String ID);

    List<TranserBillProjectOutputForm> getTranserBillProjectInfo(@Param("ID") String ID);

    List<TranserBillGeneralCardOutputForm> getTranserBillGeneralCardInfo(@Param("ID") String ID);

    List<TranserBillTimeCardOutputForm> getTranserBillTimeCardInfo(@Param("ID") String ID);

    List<TranserBillSavingCardOutputForm> getTranserBillSavingCardInfo(@Param("ID") String ID);

    List<TranserBillPackageCardOutputForm> getTranserBillPackageCardInfo(@Param("ID") String ID);

    List<TranserBillProductOutputForm> getTranserBillPackageCardProductInfo(@Param("ID") String ID);

    List<TranserBillProjectOutputForm> getTranserBillPackageCardProjectInfo(@Param("ID") String ID);

    List<TranserBillGeneralCardOutputForm> getTranserBillPackageCardGeneralCardInfo(@Param("ID") String ID);

    List<TranserBillTimeCardOutputForm> getTranserBillPackageCardTimeCardInfo(@Param("ID") String ID);

    List<TranserBillSavingCardOutputForm> getTranserBillPackageCardSavingCardInfo(@Param("ID") String ID);

    List<MiniprogramTranserBillOutputForm> miniProgramTranserList(@Param("EntityID") Integer EntityID, @Param("Name") String Name, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("BillStatus") String BillStatus);

    void addTransferBillAttachment(TransferBillAttachmentEntity transferBillAttachmentEntity);

    String getTransferBillAttachmentByID(@Param("ID") String ID);

    void deleteTransferBillAttachment(@Param("ID") String ID);

    List<TransferBillAttachmentForm> getTransferBillAttachment(@Param("TransferBillID") String TransferBillID);

    SignatureOutputForm getTransferBillSignatureByID(@Param("TransferBillID") String TransferBillID);

    void deleteTransferBillSignature(@Param("TransferBillID") String TransferBillID);

    void addSignature(TransferBillSignatureEntity transferBillSignatureEntity);

    Boolean getBillStockIntegrityStatus(@Param("ID") String ID);

    List<TransferBillProductAccountEntity> getTranserBillProduct(@Param("ID") String ID);

    List<TransferBillProjectAccountEntity> getTranserBillProject(@Param("ID") String ID);

    List<TransferBillGeneralCardAccountEntity> getTranserBillGeneralCard(@Param("ID") String ID);

    List<TransferBillTimeCardAccountEntity> getTranserBillTimeCard(@Param("ID") String ID);

    List<TransferBillSavingCardAccountEntity> getTranserBillSavingCard(@Param("ID") String ID);

    void updateTransferBillStatus(@Param("ID") String ID, @Param("BillStatus") String BillStatus);

    void createTransferBillCancelProcess(TransferBillCancelProcessEntity transferBillCancelProcessEntity);


    WXTransBillInfoOutputForm getWXTranserBillInfo(String ID);
}
