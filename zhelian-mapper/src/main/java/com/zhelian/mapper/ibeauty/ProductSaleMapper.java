package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.TbProductEntity;
import com.zhelian.model.ibeauty.entity.TbProductImageEntity;
import com.zhelian.model.ibeauty.entity.TbProductSaleEntity;
import com.zhelian.model.ibeauty.form.input.GoodsImageUrlForm;
import com.zhelian.model.ibeauty.form.output.AllProductOutputForm;
import com.zhelian.model.ibeauty.form.output.ProductDetailsOutputForm;
import com.zhelian.model.ibeauty.form.output.ProductOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;


@Repository
public interface ProductSaleMapper {
    List<ProductOutputForm> productList(@Param("Name") String Name, @Param("PCategoryID") Integer PCategoryID, @Param("IsAllowSell") Boolean IsAllowSell, @Param("ProductBrandID") Integer ProductBrandID);

    Integer[] productSaleEntity(@Param("ID") Integer ID);

    Integer[] productModifyPriceEntity(@Param("ID") Integer ID);

    void insertProduct(TbProductEntity tbProductEntity);

    void deleteProduct(Integer ID);

    void updateSaleEntity(List<TbProductSaleEntity> list);

    void deleteSaleEntity(Integer ID);

    List<AllProductOutputForm> allProduct();

    List<GoodsImageUrlForm> getProductImageList(@Param("ID") Integer ID);

    void deleteProductImageByID(@Param("ID") Integer ID);

    void insertProductImageURL(@Param("ProductID") Integer ProductID, @Param("ImageURL") String ImageURL, @Param("Sequence") Integer Sequence);

    LinkedList<Integer> getASCList(@Param("ID") int ID, @Param("ProductID") int ProductID);

    void updateSequence(List<TbProductImageEntity> list);

    ProductDetailsOutputForm getProductDetails(@Param("ID") int ID);

    void deleteBitchSaleEntity(Integer[] array);

    void deleteModifyPriceEntity(@Param("ID") int ID);

    void updateModifyPriceEntity(List<TbProductSaleEntity> list);
}
