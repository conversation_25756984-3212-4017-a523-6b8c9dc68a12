package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.TreatProductCategoryHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.TreatProductCategoryHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.output.TreatProductCategoryHandlerCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatProductCategoryHandlerCommissionMapper {

    List<TreatProductCategoryHandlerCommissionOutputForm> allTreatProductCategoryHandlerCommission(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void updateTreatProductCategoryHandlerCommission(List<TreatProductCategoryHandlerCommissionEntity> list);

    void insertTreatProductCategoryHandlerJobTypeCommission(List<TreatProductCategoryHandlerJobTypeCommissionEntity> list);

    void deleteTreatProductCategoryHandler(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void deleteTreatProductCategoryHandlerJobType(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void deleteTreatProductCategoryHandlerCommissionByEntity(@Param("EntityID") Integer EntityID);

    void deleteTreatProductCategoryHandlerJobTypeCommissionByEntity(@Param("EntityID") Integer EntityID);
}
