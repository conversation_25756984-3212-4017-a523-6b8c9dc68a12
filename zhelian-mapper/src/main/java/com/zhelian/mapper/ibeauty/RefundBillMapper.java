package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.*;
import com.zhelian.model.ibeauty.form.input.RefundBillInfoQueryForm;
import com.zhelian.model.ibeauty.form.output.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;


@Repository
public interface RefundBillMapper {

    void createRefundBill(RefundSaleBillEntity refundBillEntity);

    void createRefundBillProcess(RefundBillProcessEntity refundBillProcessEntity);

    void createRefundBillProduct(RefundBillProductEntity refundBillProductEntity);

    void createRefundBillProject(RefundBillProjectEntity refundBillProjectEntity);

    void createRefundBillGeneralCard(RefundBillGeneralCardEntity refundBillGeneralCardEntity);

    void createRefundBillTimeCard(RefundBillTimeCardEntity refundBillTimeCardEntity);

    void createRefundBillSavingCard(RefundBillSavingCardEntity refundBillSavingCardEntity);

    void createRefundBillPackageCard(RefundBillPackageCardEntity refundBillPackageCardEntity);

    List<RefundBillOutputForm> refundBillList(@Param("EntityID") Integer EntityID, @Param("ID") String ID, @Param("Name") String Name, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("ApprovalStatus") String ApprovalStatus, @Param("ShowAll") Boolean ShowAll);

    List<RefundBillOutputForm> refundApprovalBillList(@Param("EmployeeID") String EmployeeID, @Param("EntityID") Integer EntityID, @Param("ID") String ID, @Param("Name") String Name, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("ApprovalStatus") String ApprovalStatus, @Param("ShowAll") Boolean ShowAll);

    RefundBillInfoOutputForm refundBillInfo(RefundBillInfoQueryForm form);

    List<RefundBillInfoProductOutputForm> refundBillInfoProduct(@Param("ID") String ID);

    List<RefundBillInfoProjectOutputForm> refundBillInfoProject(@Param("ID") String ID);

    List<RefundBillInfoGeneralCardOutputForm> refundBillInfoGeneralCard(@Param("ID") String ID);

    List<RefundBillInfoTimeCardOutputForm> refundBillInfoTimeCard(@Param("ID") String ID);

    List<RefundBillInfoSavingCardOutputForm> refundBillInfoSavingCard(@Param("ID") String ID);

    List<RefundBillInfoPackageCardOutputForm> refundBillInfoPackageCard(@Param("ID") String ID);

    List<RefundBillInfoProductOutputForm> refundBillPackageCardProductInfo(@Param("ID") String ID);

    List<RefundBillInfoProjectOutputForm> refundBillPackageCardProjectInfo(@Param("ID") String ID);

    List<RefundBillInfoGeneralCardOutputForm> refundBillPackageCardGeneralCardInfo(@Param("ID") String ID);

    List<RefundBillInfoTimeCardOutputForm> refundBillPackageCardTimeCardInfo(@Param("ID") String ID);

    List<RefundBillInfoSavingCardOutputForm> refundBillPackageCardSavingCardInfo(@Param("ID") String ID);

    List<RefundBillInfoPayMethodOutputForm> refundBillInfoPayMethod(@Param("ID") String ID);

    List<RefundBillInfoPaySavingCardDeductionOutputForm> refundBillInfoSavingCardDeduction(@Param("ID") String ID);

    void updateRefundBillStatus(@Param("ID") String ID, @Param("ApprovalStatus") String ApprovalStatus);

    RefundSaleBillEntity getRefundBill(@Param("ID") String ID);

    void createRefundBillPay(List<RefundBillPayEntity> list);

    void createRefundBillPaySavingCardDeduction(List<RefundBillPaySavingCardDeductionEntity> list);

    List<RefundBillProductEntity> getRefundBillProduct(@Param("ID") String ID);

    List<RefundBillProjectEntity> getRefundBillProject(@Param("ID") String ID);

    List<RefundBillGeneralCardEntity> getRefundBillGeneralCard(@Param("ID") String ID);

    List<RefundBillTimeCardEntity> getRefundBillTimeCard(@Param("ID") String ID);

    List<RefundBillSavingCardEntity> getRefundBillSavingCard(@Param("ID") String ID);

    List<RefundBillPackageCardEntity> getRefundBillPackageCard(@Param("ID") String ID);

    List<RefundBillPayEntity> getRefundBillPayMethod(@Param("ID") String ID);

    List<RefundBillPaySavingCardDeductionEntity> getRefundBillSavingCardDeduction(@Param("ID") String ID);

    List<RefundBillProductEntity> getRefundBillProductByPackageCardID(@Param("ID") String ID, @Param("PackageCardID") Integer PackageCardID);

    List<RefundBillProjectEntity> getRefundBillProjectByPackageCardID(@Param("ID") String ID, @Param("PackageCardID") Integer PackageCardID);

    List<RefundBillGeneralCardEntity> getRefundBillGeneralCardByPackageCardID(@Param("ID") String ID, @Param("PackageCardID") Integer PackageCardID);

    List<RefundBillTimeCardEntity> getRefundBillTimeCardByPackageCardID(@Param("ID") String ID, @Param("PackageCardID") Integer PackageCardID);

    List<RefundBillSavingCardEntity> getRefundBillSavingCardByPackageCardID(@Param("ID") String ID, @Param("PackageCardID") Integer PackageCardID);

    void updateRefundBillSaleBill(@Param("ID") String ID, @Param("SaleBillID") String SaleBillID);

    BigDecimal getProductPricePreferentialAmountTotal(@Param("ID") Integer ID);

    BigDecimal getProjectPricePreferentialAmountTotal(@Param("ID") Integer ID);

    BigDecimal getGeneralCardPricePreferentialAmountTotal(@Param("ID") Integer ID);

    void updatePayMethod(@Param("ID") Integer ID, @Param("PayMethodID") Integer PayMethodID);
}
