package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.SavingCardEntity;
import com.zhelian.model.ibeauty.entity.SavingCardRechargeRulesEntity;
import com.zhelian.model.ibeauty.entity.SavingCardSaleEntity;
import com.zhelian.model.ibeauty.entity.TbSavingCardImageEntity;
import com.zhelian.model.ibeauty.form.input.GoodsImageUrlForm;
import com.zhelian.model.ibeauty.form.input.SavingCardQueryEntityForm;
import com.zhelian.model.ibeauty.form.output.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;

@Repository
public interface SavingCardMapper {
    List<SavingCardOutputForm> savingCardList(@Param("Name") String Name, @Param("SavingCardCategoryID") Integer SavingCardCategoryID, @Param("IsAllowSell") Boolean IsAllowSell);

    SavingCardDetailsOutputForm getSavingCardDetails(@Param("ID") int ID);

    Integer[] savingCardSaleEntity(SavingCardQueryEntityForm form);

    Integer[] savingCardConsumeEntity(SavingCardQueryEntityForm form);

    void addSavingCard(SavingCardEntity entity);

    void addSavingCardConsumeEntity(List<SavingCardSaleEntity> savingCardConsumeEntity);

    void addSavingCardSaleEntity(List<SavingCardSaleEntity> savingCardSaleEntity);

    void updateSavingCard(SavingCardEntity savingCardEntity);

    void deleteSavingCardConsumeEntity(Integer ID);

    void deleteSavingCardSaleEntity(Integer ID);

    void addSavingCardProduct(List list);

    void addSavingCardProject(List list);

    void addSavingCardGeneralCard(List list);

    void addSavingCardTimeCard(List list);

    void addSavingCardPackageCard(List list);

    void addSavingCardProductCategory(List list);

    void addSavingCardProjectCategory(List list);

    void addSavingCardGeneralCardCategory(List list);

    void addSavingCardTimeCardCategory(List list);

    void addSavingCardPackageCardCategory(List list);

    List<GoodsCategoryOutputForm> product();

    List<GoodsCategoryOutputForm> project();

    List<GoodsCategoryOutputForm> generalCard();

    List<GoodsCategoryOutputForm> timeCard();

    List<GoodsCategoryOutputForm> packageCard();

    List<SavingCardGoodsOutputForm> savingCardProduct(@Param("ID") Integer ID, @Param("IsLargess") Boolean IsLargess);

    List<SavingCardGoodsOutputForm> savingCardProject(@Param("ID") Integer ID, @Param("IsLargess") Boolean IsLargess);

    List<SavingCardGoodsOutputForm> savingCardGeneralCard(@Param("ID") Integer ID, @Param("IsLargess") Boolean IsLargess);

    List<SavingCardGoodsOutputForm> savingCardTimeCard(@Param("ID") Integer ID, @Param("IsLargess") Boolean IsLargess);

    List<SavingCardPackageCardGoodOutputForm> savingCardPackageCard(@Param("ID") Integer ID, @Param("IsLargess") Boolean IsLargess);

    List<SavingCardGoodsCategoryOutputForm> savingCardProductCategory(@Param("ID") Integer ID, @Param("IsLargess") Boolean IsLargess);

    List<SavingCardGoodsCategoryOutputForm> savingCardProjectCategory(@Param("ID") Integer ID, @Param("IsLargess") Boolean IsLargess);

    List<SavingCardGoodsCategoryOutputForm> savingCardGeneralCardCategory(@Param("ID") Integer ID, @Param("IsLargess") Boolean IsLargess);

    List<SavingCardGoodsCategoryOutputForm> savingCardTimeCardCategory(@Param("ID") Integer ID, @Param("IsLargess") Boolean IsLargess);

    List<SavingCardGoodsCategoryOutputForm> savingCardPackageCardCategory(@Param("ID") Integer ID, @Param("IsLargess") Boolean IsLargess);


    void deleteSavingCardProduct(Integer ID);

    void deleteSavingCardProductCategory(Integer ID);

    void deleteSavingCardProject(Integer ID);

    void deleteSavingCardProjectCategory(Integer ID);

    void deleteSavingCardGeneralCard(Integer ID);

    void deleteSavingCardGeneralCardCategory(Integer ID);

    void deleteSavingCardPackageCard(Integer ID);

    void deleteSavingCardPackageCardCategory(Integer ID);

    void deleteSavingCardTimeCard(Integer ID);

    void deleteSavingCardTimeCardCategory(Integer ID);

    SavingCardEntity getSavingCardByID(Integer ID);

    void createSavingCardRechargeRules(List<SavingCardRechargeRulesEntity> savingCardRechargeRules);

    void deleteSavingCardRechargeRules(Integer ID);

    List<SavingCardRechargeRulesEntity> savingCardRechargeRules(Integer ID);

    List<GoodsImageUrlForm> getSavingCardImageList(@Param("ID") Integer ID);

    void deleteSavingCardImageByID(@Param("ID") Integer ID);

    void insertSavingCardImageURL(@Param("SavingCardID") Integer SavingCardID, @Param("ImageURL") String ImageURL, @Param("Sequence") Integer Sequence);

    LinkedList<Integer> getASCList(@Param("ID") int ID, @Param("TimeCardID") int TimeCardID);

    void updateSequence(List<TbSavingCardImageEntity> list);

    void deleteBitchSaleEntity(Integer[] array);

    void deleteBitchConsumeEntity(Integer[] array);
}
