package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.PhoneCallBackCodeEntity;
import com.zhelian.model.ibeauty.entity.PhoneCallBackConfigEntity;
import com.zhelian.model.ibeauty.entity.PhoneCallBackLogEntity;
import com.zhelian.model.ibeauty.form.input.PhoneCallBackLogReportForm;
import com.zhelian.model.ibeauty.form.input.PhoneCallBackLogStatisticsForm;
import com.zhelian.model.ibeauty.form.output.PhoneCallBackLogOutputForm;
import com.zhelian.model.ibeauty.form.output.PhoneCallBackLogReportOutputForm;
import com.zhelian.model.ibeauty.form.output.PhoneCallBackLogStatisticsOutputForm;
import com.zhelian.model.ibeauty.form.output.PhoneCallBackOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Repository
public interface PhoneCallBackMapper {

    List<PhoneCallBackOutputForm> list(@Param("Name") String Name, @Param("Phone") String Phone);

    PhoneCallBackConfigEntity getPhoneCallBackConfigEntityByEmployeeID(@Param("EmployeeID") String EmployeeID);

    void add(PhoneCallBackConfigEntity entity);

    void addPhoneCallBackCodeEntity(PhoneCallBackCodeEntity entity);

    void delete(@Param("ID") Integer ID);

    void addPhoneCallBackLog(PhoneCallBackLogEntity entity);

    PhoneCallBackLogEntity getPhoneCallBackLogEntity(@Param("CallId") String CallId);

    List<PhoneCallBackLogEntity> getPhoneCallBackLogEntityAll();

    void updatePhoneCallBackLog(PhoneCallBackLogEntity entity);

    List<PhoneCallBackLogOutputForm> callBackLog(@Param("CustomerID") Integer CustomerID);

    PhoneCallBackCodeEntity getPhoneCallBackCodeEntity(@Param("PhoneNumber") String PhoneNumber);

    List<PhoneCallBackLogReportOutputForm> callBackLogReport(PhoneCallBackLogReportForm form);

    List<PhoneCallBackLogStatisticsOutputForm> callBackLogStatisticsReport(PhoneCallBackLogStatisticsForm form);

    Integer getCustomerSourceCount(@Param("employeeId") String employeeId, @Param("customerSourceId") Integer customerSourceId
            , @Param("startDate") String startDate, @Param("endDate") String endDate);

    PhoneCallBackLogStatisticsOutputForm callBackLogStatisticsReportTotal(PhoneCallBackLogStatisticsForm form);
    Integer getCustomerSourceTotalCount(@Param("employeeName") String employeeName, @Param("customerSourceId") Integer customerSourceId
            , @Param("startDate") String startDate, @Param("endDate") String endDate, @Param("jobIds") List<Integer> jobIds
            , @Param("entityIds") List<Integer> entityIds);

    List<PhoneCallBackConfigEntity> getPhoneCallBackConfigEntityByEcpId(@Param("ecpId") String ecpId);
}
