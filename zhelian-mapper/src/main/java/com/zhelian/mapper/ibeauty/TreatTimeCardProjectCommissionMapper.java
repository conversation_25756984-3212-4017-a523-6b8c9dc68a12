package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.TreatTimeCardProjectCommissionEntity;
import com.zhelian.model.ibeauty.form.output.TreatCardCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatTimeCardProjectCommissionMapper {

    List<TreatCardCommissionOutputForm> allTreatTimeCardProjectCommission(@Param("EntityID") Integer EntityID, @Param("TimeCardID") Integer TimeCardID);

    void insertTreatTimeCardProjectCommission(List<TreatTimeCardProjectCommissionEntity> list);

    void deleteTreatTimeCardProjectCommissionByEntityIDAndCard(@Param("EntityID") Integer EntityID, @Param("TimeCardID") Integer TimeCardID);

    void deleteTreatTimeCardProjectCommissionByEntityID(@Param("EntityID") Integer EntityID);
}
