package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SaleSavingCardCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.output.SaleSavingCardCommissionSchemeOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleSavingCardCommissionSchemeMapper {

    List<SaleSavingCardCommissionSchemeOutputForm> saleSavingCardCommissionSchemeList(@Param("Name") String Name);

    void createSaleSavingCardCommissionScheme(SaleSavingCardCommissionSchemeEntity saleProductCommissionSchemeEntity);

    void deleteSaleSavingCardCommissionScheme(@Param("EntityID") Integer EntityID);

    Integer getSaleSavingCardCommissionScheme(@Param("EntityID") Integer EntityID);

}
