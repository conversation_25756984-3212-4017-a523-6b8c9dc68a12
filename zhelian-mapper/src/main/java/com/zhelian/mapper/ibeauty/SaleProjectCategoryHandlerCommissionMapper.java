package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SaleProjectCategoryHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleProjectCategoryHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.output.SaleProjectCategoryHandlerCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleProjectCategoryHandlerCommissionMapper {

    List<SaleProjectCategoryHandlerCommissionOutputForm> allSaleProjectCategoryHandlerCommission(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void updateSaleProjectCategoryHandlerCommission(List<SaleProjectCategoryHandlerCommissionEntity> list);

    void insertSaleProjectCategoryHandlerJobTypeCommission(List<SaleProjectCategoryHandlerJobTypeCommissionEntity> list);

    void deleteSaleProjectCategoryHandler(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void deleteSaleProjectCategoryHandlerJobType(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void deleteSaleProjectCategoryHandlerCommissionByEntity(@Param("EntityID") Integer EntityID);

    void deleteSaleProjectCategoryHandlerJobTypeCommissionByEntity(@Param("EntityID") Integer EntityID);
}
