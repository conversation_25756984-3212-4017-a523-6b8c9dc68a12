package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.TreatSavingCardCommissionEntity;
import com.zhelian.model.ibeauty.form.output.TreatSavingCardCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface TreatSavingCardCommissionMapper {

    List<TreatSavingCardCommissionOutputForm> allTreatSavingCardCommission(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertTreatSavingCommission(List<TreatSavingCardCommissionEntity> list);

    void deleteTreatSavingCardCommission(Map<String, Object> map);

    void deleteTreatSavingCardCommissionByEntity(Integer EntityID);

    void deleteTreatSavingCardCommissionByEntityAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);
}
