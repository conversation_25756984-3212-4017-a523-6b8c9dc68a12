package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SaleSavingCardCommissionEntity;
import com.zhelian.model.ibeauty.form.output.SaleSavingCardCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface SaleSavingCardCommissionMapper {

    List<SaleSavingCardCommissionOutputForm> allSaleSavingCardCommission(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertSaleSavingCardCommission(List<SaleSavingCardCommissionEntity> list);

    void deleteSaleSavingCardCommission(Map<String, Object> map);

    void deleteSaleSavingCardCommissionByEntity(Integer EntityID);

    void deleteSaleSavingCardCommissionByEntityAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);
}
