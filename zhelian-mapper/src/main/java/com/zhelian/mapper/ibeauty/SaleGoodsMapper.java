package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.form.output.SaleTimeCardCustomerDiscountOutForm;
import com.zhelian.model.ibeauty.form.output.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleGoodsMapper {

    List<SaleGoodAllProductOutputForm> allSaleGoodProduct(@Param("Name") String Name, @Param("EntityID") Integer EntityID);

    List<SaleGoodAllProjectOutputForm> allSaleGoodProject(@Param("Name") String Name, @Param("EntityID") Integer EntityID);

    List<SaleGoodAllGeneralCardOutputForm> allSaleGoodGeneralCard(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("BillDate") String BillDate);

    List<SaleGoodAllTimeCardOutputForm> allSaleGoodTimeCard(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("BillDate") String BillDate);

    List<SaleGoodAllSavingCardOutputForm> allSaleGoodSavingCard(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("BillDate") String BillDate);

    List<SaleGoodAllPackageCardOutputForm> allSaleGoodPackageCard(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("BillDate") String BillDate);

    List<SaleGoodsPackageCardDetailOutputForm> saleGoodsPackageCardProduct(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("BillDate") String BillDate);

    List<SaleGoodsPackageCardDetailOutputForm> saleGoodsPackageCardProject(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("BillDate") String BillDate);

    List<SaleGoodsPackageCardDetailOutputForm> saleGoodsPackageCardGeneralCard(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("BillDate") String BillDate);

    List<SaleGoodsPackageCardDetailOutputForm> saleGoodsPackageCardTimeCard(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("BillDate") String BillDate);

    List<SaleGoodsPackageCardDetailOutputForm> saleGoodsPackageCardSavingCard(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("BillDate") String BillDate);

    SaleProjectCustomerDiscountOutForm getProjectCustomerDiscount(@Param("ProjectID") Integer ProjectID, @Param("CustomerID") Integer CustomerID);

    List<SaleGoodsProjectCategoryChildOutputForm> saleGoodsProject(@Param("Name") String Name, @Param("EntityID") Integer EntityID);

    List<SaleGoodsProjectCategoryOutputForm> getTopProjectList(List<Integer> list);

    SaleProductCustomerDiscountOutForm getProductCustomerDiscount(@Param("ProductID") Integer ProductID, @Param("CustomerID") Integer CustomerID);

    List<SaleGoodsProductCategoryChildOutputForm> saleGoodsProduct(@Param("Name") String Name, @Param("EntityID") Integer EntityID);

    SaleGeneralCardCustomerDiscountOutForm getGeneralCardCustomerDiscount(@Param("GeneralCardID") Integer GeneralCardID, @Param("CustomerID") Integer CustomerID);

    List<SaleGoodsGeneralCardCategoryOutputForm> saleGoodsGeneralCard(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("BillDate") String BillDate);

    SaleTimeCardCustomerDiscountOutForm getTimeCardCustomerDiscount(@Param("TimeCardID") Integer TimeCardID, @Param("CustomerID") Integer CustomerID);

    List<SaleGoodsTimeCardCategoryOutputForm> saleGoodsTimeCard(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("BillDate") String BillDate);

    List<SaleGoodsSavingCardCategoryOutputForm> saleGoodsSavingCard(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("BillDate") String BillDate);

    SalePackageCardCustomerDiscountOutForm getPackageCardCustomerDiscount(@Param("PackageCardID") Integer PackageCardID, @Param("CustomerID") Integer CustomerID);

    List<SaleGoodsPackageCardCategoryOutputForm> saleGoodsPackageCardCategory(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("BillDate") String BillDate);

    List<SaleGoodsPackageCardDetailOutputForm> saleGoodsPackageCardDetail(@Param("Name") String Name, @Param("EntityID") Integer EntityID);

    List<SaleGoodsProductCategoryOutputForm> getTopProductList(List<Integer> list);

    List<SaleGoodsParentCategoryOutputForm> allSaleGoodsProjectCategory(@Param("EntityID") Integer EntityID, @Param("Name") String Name);

    List<SaleGoodAllProjectOutputForm> projectByCategory(@Param("EntityID") Integer EntityID, @Param("Name") String Name, @Param("CategoryID") Integer CategoryID);

    List<SaleGoodsCategoryChildOutputForm> allSaleGoodsSavingCardCategory(@Param("EntityID") Integer EntityID, @Param("Name") String Name, @Param("BillDate") String BillDate);

    List<SaleGoodSavingCardOutputForm> savingCardByCategory(@Param("EntityID") Integer EntityID, @Param("Name") String Name, @Param("CategoryID") Integer CategoryID, @Param("BillDate") String BillDate);

    List<SaleGoodsCategoryChildOutputForm> allSaleGoodsTimeCardCategory(@Param("EntityID") Integer EntityID, @Param("Name") String Name, @Param("BillDate") String BillDate);

    List<SaleGoodTimeCardOutputForm> timeCardByCategory(@Param("EntityID") Integer EntityID, @Param("Name") String Name, @Param("CategoryID") Integer CategoryID, @Param("BillDate") String BillDate);

    List<SaleGoodsTypeOutputForm> getGoodsType(@Param("EntityID") Integer EntityID, @Param("Name") String Name, @Param("BillDate") String BillDate);

    List<SaleGoodsTypeOutputForm> getFastGoodsType(@Param("EntityID") Integer EntityID, @Param("Name") String Name);

    List<SaleGoodsParentCategoryOutputForm> saleGoodsProductCategory(@Param("EntityID") Integer EntityID, @Param("Name") String Name);

    List<SaleGoodAllProductOutputForm> saleGoodsProductByCategory(@Param("EntityID") Integer EntityID, @Param("Name") String Name, @Param("CategoryID") Integer CategoryID);

    List<SaleGoodsCategoryChildOutputForm> saleGoodsGeneralCardCategory(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("BillDate") String BillDate);

    List<SaleGoodGeneralCardOutputForm> saleGoodsGeneralCardByCategory(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("BillDate") String BillDate, @Param("CategoryID") Integer CategoryID);

    List<SaleGoodsCategoryChildOutputForm> saleGoodsPackageCardOnlyCategory(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("BillDate") String BillDate);

    List<SaleGoodPackageCardOutputForm> saleGoodsPackageCardByCategory(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("BillDate") String BillDate, @Param("CategoryID") Integer CategoryID);

    List<SaleGoodsParentCategoryOutputForm> generalCardProjectCategory(@Param("Name") String Name, @Param("GoodsID") Integer GoodsID);

    List<SaleGoodCardProjectOutputForm> generalCardProjectByCategory(@Param("Name") String Name, @Param("GoodsID") Integer GoodsID, @Param("CategoryID") Integer CategoryID);

    List<SaleGoodsParentCategoryOutputForm> timeCardProjectCategory(@Param("Name") String Name, @Param("GoodsID") Integer GoodsID);

    List<SaleGoodCardProjectOutputForm> timeCardProjectByCategory(@Param("Name") String Name, @Param("GoodsID") Integer GoodsID, @Param("CategoryID") Integer CategoryID);

    List<SaleGoodsParentCategoryOutputForm> savingCardAllProjectCategory(@Param("Name") String Name);

    List<SaleGoodsParentCategoryOutputForm> savingCardProjectCategory(@Param("Name") String Name, @Param("GoodsID") Integer GoodsID, @Param("IsLargess") Boolean IsLargess);

    List<SaleGoodCardProjectOutputForm> savingCardAllProjectByCategory(@Param("Name") String Name, @Param("CategoryID") Integer CategoryID);

    List<SaleGoodCardProjectOutputForm> savingCardProjectByCategory(@Param("Name") String Name, @Param("GoodsID") Integer GoodsID, @Param("CategoryID") Integer CategoryID, @Param("IsLargess") Boolean IsLargess);

    List<SaleGoodsParentCategoryOutputForm> packageCardProductCategory(@Param("Name") String Name, @Param("GoodsID") Integer GoodsID, @Param("IsLargess") Boolean IsLargess);

    List<SaleGoodsParentCategoryOutputForm> packageCardProjectCategory(@Param("Name") String Name, @Param("GoodsID") Integer GoodsID, @Param("IsLargess") Boolean IsLargess);

    List<SaleGoodsCategoryChildOutputForm> packageCardGeneralCardCategory(@Param("Name") String Name, @Param("GoodsID") Integer GoodsID, @Param("IsLargess") Boolean IsLargess);

    List<SaleGoodsCategoryChildOutputForm> packageCardTimeCardCategory(@Param("Name") String Name, @Param("GoodsID") Integer GoodsID, @Param("IsLargess") Boolean IsLargess);

    List<SaleGoodsCategoryChildOutputForm> packageCardSavingCardCategory(@Param("Name") String Name, @Param("GoodsID") Integer GoodsID, @Param("IsLargess") Boolean IsLargess);

    List<SaleGoodCardProjectOutputForm> packageCardProductByCategory(@Param("Name") String Name, @Param("GoodsID") Integer GoodsID, @Param("CategoryID") Integer CategoryID, @Param("IsLargess") Boolean IsLargess);

    List<SaleGoodCardProjectOutputForm> packageCardProjectByCategory(@Param("Name") String Name, @Param("GoodsID") Integer GoodsID, @Param("CategoryID") Integer CategoryID, @Param("IsLargess") Boolean IsLargess);

    List<SaleGoodCardProjectOutputForm> packageCardGeneralCardByCategory(@Param("Name") String Name, @Param("GoodsID") Integer GoodsID, @Param("CategoryID") Integer CategoryID, @Param("IsLargess") Boolean IsLargess);

    List<SaleGoodCardProjectOutputForm> packageCardTimeCardByCategory(@Param("Name") String Name, @Param("GoodsID") Integer GoodsID, @Param("CategoryID") Integer CategoryID, @Param("IsLargess") Boolean IsLargess);

    List<SaleGoodCardProjectOutputForm> packageCardSavingCardByCategory(@Param("Name") String Name, @Param("GoodsID") Integer GoodsID, @Param("CategoryID") Integer CategoryID, @Param("IsLargess") Boolean IsLargess);

}
