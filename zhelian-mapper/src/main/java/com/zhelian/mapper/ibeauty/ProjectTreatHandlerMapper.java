package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.ProjectTreatHandlerEntity;
import com.zhelian.model.ibeauty.entity.ProjectTreatHandlerJobTypeEntity;
import com.zhelian.model.ibeauty.form.output.SaleHandlerOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;


@Repository
public interface ProjectTreatHandlerMapper {


    List<SaleHandlerOutputForm> allProjectTreatHandler(@Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("Active") Boolean Active);

    Integer getMaxSequence();

    void createProjectTreatHandler(ProjectTreatHandlerEntity projectTreatHandlerEntity);

    void insertHandlerJobType(List<ProjectTreatHandlerJobTypeEntity> list);

    void updateProjectTreatHandler(ProjectTreatHandlerEntity projectTreatHandlerEntity);

    void deleteTreatHandlerJobType(@Param("ID") Integer ID);

    LinkedList<Integer> getIDOrderBySequenceASC(Integer ID);

    void updateSequence(List<ProjectTreatHandlerEntity> list);
}
