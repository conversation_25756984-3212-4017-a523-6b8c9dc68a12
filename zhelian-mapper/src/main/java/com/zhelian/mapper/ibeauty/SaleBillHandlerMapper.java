package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.*;
import com.zhelian.model.ibeauty.form.output.HandlerOrEmployeeNameOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SaleBillHandlerMapper {

    void createSaleBillHandlerProduct(List<SaleBillHandlerProductEntity> list);

    void createSaleBillHandlerProject(List<SaleBillHandlerProjectEntity> list);

    void createSaleBillHandlerGeneralCard(List<SaleBillHandlerGeneralCardEntity> list);

    void createSaleBillHandlerTimeCard(List<SaleBillHandlerTimeCardEntity> list);

    void createSaleBillHandlerSavingCard(List<SaleBillHandlerSavingCardEntity> list);

    void createSaleBillHandlerPackageCard(List<SaleBillHandlerPackageCardEntity> list);

    void deleteProductHandler(@Param("SaleBillID") String SaleBillID);

    void deleteProjectHandler(@Param("SaleBillID") String SaleBillID);

    void deleteGeneralCardHandler(@Param("SaleBillID") String SaleBillID);

    void deleteTimeCardHandler(@Param("SaleBillID") String SaleBillID);

    void deleteSavingCardHandler(@Param("SaleBillID") String SaleBillID);

    void deletePackageCardHandler(@Param("SaleBillID") String SaleBillID);

    List<HandlerOrEmployeeNameOutputForm> getSaleProductHandlerName(Integer EntityID);

    List<HandlerOrEmployeeNameOutputForm> getSaleProjectHandlerName(Integer EntityID);

    List<HandlerOrEmployeeNameOutputForm> getSaleGeneralCardHandlerName(Integer EntityID);

    List<HandlerOrEmployeeNameOutputForm> getSaleTimeCardHandlerName(Integer EntityID);

    List<HandlerOrEmployeeNameOutputForm> getSaleSavingCardHandlerName(Integer EntityID);

    List<HandlerOrEmployeeNameOutputForm> getSalePackageCardHandlerName(Integer EntityID);

    List<HandlerOrEmployeeNameOutputForm> getSaleEmployeeName(List<String> list);
}
