package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.ProductPriceAdjustmentEntity;
import com.zhelian.model.ibeauty.form.output.ProductPriceAdjustmentOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface ProductPriceAdjustmentMapper {
    List<ProductPriceAdjustmentOutputForm> productPriceAdjustmentList(@Param("ProductName") String ProductName, @Param("EntityName") String EntityName);

    Integer productPriceAdjustmentCount(@Param("EntityID") Integer EntityID, @Param("ProductID") Integer ProductID);

    void insertProductPriceAdjustmentEntity(ProductPriceAdjustmentEntity productPriceAdjustmentEntity);

    void updateProductPriceAdjustmentEntity(ProductPriceAdjustmentEntity productPriceAdjustmentEntity);

    void deleteProductPriceAdjustmentEntity(@Param("EntityID") Integer EntityID, @Param("ProductID") Integer ProductID);
}
