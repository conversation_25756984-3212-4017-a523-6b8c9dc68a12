package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SaleSavingCardSchemeHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleSavingCardSchemeHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.output.SaleSavingCardSchemeHandlerCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleSavingCardSchemeHandlerCommissionMapper {

    List<SaleSavingCardSchemeHandlerCommissionOutputForm> allSaleSavingCardSchemeHandlerCommission(@Param("EntityID") Integer EntityID);

    void deleteSaleSavingCardSchemeHandler(@Param("EntityID") Integer EntityID);

    void deleteSaleSavingCardSchemeHandlerJobType(@Param("EntityID") Integer EntityID);

    void updateSaleSavingCardSchemeHandlerCommission(List<SaleSavingCardSchemeHandlerCommissionEntity> list);

    void insertSaleSavingCardSchemeHandlerJobTypeCommission(List<SaleSavingCardSchemeHandlerJobTypeCommissionEntity> list);
}
