package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.*;
import com.zhelian.model.ibeauty.service.TreatCommissionEntity;
import com.zhelian.model.ibeauty.service.TreatPerformanceEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatEmployeePerformanceCommissionMapper {

    //获取销售提佣
    TreatCommissionEntity getTreatProductCommission(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("TreatHandlerID") int TreatHandlerID, @Param("GoodsID") int GoodsID);

    TreatCommissionEntity getTreatProjectCommission(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("TreatHandlerID") int TreatHandlerID, @Param("GoodsID") int GoodsID);

    TreatCommissionEntity getTreatSavingCardCommission(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("TreatHandlerID") int TreatHandlerID, @Param("GoodsID") int GoodsID, @Param("SavingCardID") int SavingCardID);

    TreatCommissionEntity getTreatGeneralCardCommission(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("TreatHandlerID") int TreatHandlerID, @Param("GoodsID") int GoodsID, @Param("GeneralCardID") int GeneralCardID);

    TreatCommissionEntity getTreatTimeCardCommission(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("TreatHandlerID") int TreatHandlerID, @Param("GoodsID") int GoodsID, @Param("TimeCardID") int TimeCardID);


    //获取销售提佣
    TreatPerformanceEntity getTreatProductPerformance(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("TreatHandlerID") int TreatHandlerID, @Param("GoodsID") int GoodsID);

    TreatPerformanceEntity getTreatProjectPerformance(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("TreatHandlerID") int TreatHandlerID, @Param("GoodsID") int GoodsID);

    TreatPerformanceEntity getTreatSavingCardPerformance(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("TreatHandlerID") int TreatHandlerID, @Param("GoodsID") int GoodsID, @Param("SavingCardID") int SavingCardID);

    TreatPerformanceEntity getTreatGeneralCardPerformance(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("TreatHandlerID") int TreatHandlerID, @Param("GoodsID") int GoodsID, @Param("GeneralCardID") int GeneralCardID);

    TreatPerformanceEntity getTreatTimeCardPerformance(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("TreatHandlerID") int TreatHandlerID, @Param("GoodsID") int GoodsID, @Param("TimeCardID") int TimeCardID);

    void createTreatEmployeePerformanceCommissionProduct(TreatEmployeePerformanceCommissionProductEntity treatEmployeePerformanceCommissionProductEntity);

    void createTreatEmployeePerformanceCommissionProject(TreatEmployeePerformanceCommissionProjectEntity treatEmployeePerformanceCommissionProjectEntity);

    void createTreatEmployeePerformanceCommissionSavingCard(TreatEmployeePerformanceCommissionSavingCardEntity treatEmployeePerformanceCommissionSavingCardEntity);

    void createTreatEmployeePerformanceCommissionGeneralCard(TreatEmployeePerformanceCommissionGeneralCardEntity treatEmployeePerformanceCommissionGeneralCardEntity);

    void createTreatEmployeePerformanceCommissionTimeCard(TreatEmployeePerformanceCommissionTimeCardEntity treatEmployeePerformanceCommissionTimeCardEntity);

    void createTreatEmployeePerformanceCommissionPackageCardProduct(TreatEmployeePerformanceCommissionPackageCardProductEntity treatEmployeePerformanceCommissionPackageCardProductEntity);

    void createTreatEmployeePerformanceCommissionPackageCardProject(TreatEmployeePerformanceCommissionPackageCardProjectEntity treatEmployeePerformanceCommissionPackageCardProjectEntity);

    void createTreatEmployeePerformanceCommissionPackageCardSavingCard(TreatEmployeePerformanceCommissionPackageCardSavingCardEntity treatEmployeePerformanceCommissionPackageCardSavingCardEntity);

    void createTreatEmployeePerformanceCommissionPackageCardGeneralCard(TreatEmployeePerformanceCommissionPackageCardGeneralCardEntity treatEmployeePerformanceCommissionPackageCardGeneralCardEntity);

    void createTreatEmployeePerformanceCommissionPackageCardTimeCard(TreatEmployeePerformanceCommissionPackageCardTimeCardEntity treatEmployeePerformanceCommissionPackageCardTimeCardEntity);

    void deleteTreatEmployeePerformanceCommissionProduct(@Param("ID") String ID);

    void deleteTreatEmployeePerformanceCommissionProject(@Param("ID") String ID);

    void deleteTreatEmployeePerformanceCommissionGeneralCard(@Param("ID") String ID);

    void deleteTreatEmployeePerformanceCommissionTimeCard(@Param("ID") String ID);

    void deleteTreatEmployeePerformanceCommissionSavingCard(@Param("ID") String ID);

    void deleteTreatEmployeePerformanceCommissionPackageCardProduct(@Param("ID") String ID);

    void deleteTreatEmployeePerformanceCommissionPackageCardProject(@Param("ID") String ID);

    void deleteTreatEmployeePerformanceCommissionPackageCardGeneralCard(@Param("ID") String ID);

    void deleteTreatEmployeePerformanceCommissionPackageCardTimeCard(@Param("ID") String ID);

    void deleteTreatEmployeePerformanceCommissionPackageCardSavingCard(@Param("ID") String ID);

    void createTreatEmployeePerformanceCommissionProductList(List<TreatEmployeePerformanceCommissionProductEntity> list);

    void createTreatEmployeePerformanceCommissionProjectList(List<TreatEmployeePerformanceCommissionProjectEntity> list);

    void createTreatEmployeePerformanceCommissionGeneralCardList(List<TreatEmployeePerformanceCommissionGeneralCardEntity> list);

    void createTreatEmployeePerformanceCommissionTimeCardList(List<TreatEmployeePerformanceCommissionTimeCardEntity> list);

    void createTreatEmployeePerformanceCommissionSavingCardList(List<TreatEmployeePerformanceCommissionSavingCardEntity> list);

    void createTreatEmployeePerformanceCommissionPackageCardProductList(List<TreatEmployeePerformanceCommissionPackageCardProductEntity> list);

    void createTreatEmployeePerformanceCommissionPackageCardProjectList(List<TreatEmployeePerformanceCommissionPackageCardProjectEntity> list);

    void createTreatEmployeePerformanceCommissionPackageCardGeneralCardList(List<TreatEmployeePerformanceCommissionPackageCardGeneralCardEntity> list);

    void createTreatEmployeePerformanceCommissionPackageCardTimeCardList(List<TreatEmployeePerformanceCommissionPackageCardTimeCardEntity> list);

    void createTreatEmployeePerformanceCommissionPackageCardSavingCardList(List<TreatEmployeePerformanceCommissionPackageCardSavingCardEntity> list);

    void updateTreatEmployeePerformanceProductBillDate(@Param("TreatBillID") String TreatBillID, @Param("BillDate") String BillDate);

    void updateTreatEmployeePerformanceProjectBillDate(@Param("TreatBillID") String TreatBillID, @Param("BillDate") String BillDate);

    void updateTreatEmployeePerformanceGeneralCardBillDate(@Param("TreatBillID") String TreatBillID, @Param("BillDate") String BillDate);

    void updateTreatEmployeePerformanceTimeCardBillDate(@Param("TreatBillID") String TreatBillID, @Param("BillDate") String BillDate);

    void updateTreatEmployeePerformanceSavingCardBillDate(@Param("TreatBillID") String TreatBillID, @Param("BillDate") String BillDate);

    void updateTreatEmployeePerformancePackageCardProductBillDate(@Param("TreatBillID") String TreatBillID, @Param("BillDate") String BillDate);

    void updateTreatEmployeePerformancePackageCardProjectBillDate(@Param("TreatBillID") String TreatBillID, @Param("BillDate") String BillDate);

    void updateTreatEmployeePerformancePackageCardSavingCardBillDate(@Param("TreatBillID") String TreatBillID, @Param("BillDate") String BillDate);

    void updateTreatEmployeePerformancePackageCardGeneralCardBillDate(@Param("TreatBillID") String TreatBillID, @Param("BillDate") String BillDate);

    void updateTreatEmployeePerformancePackageCardTimeCardBillDate(@Param("TreatBillID") String TreatBillID, @Param("BillDate") String BillDate);
}