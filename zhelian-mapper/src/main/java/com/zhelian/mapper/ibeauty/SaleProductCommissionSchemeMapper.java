package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SaleProductCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.output.SaleProductCommissionSchemeOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleProductCommissionSchemeMapper {

    List<SaleProductCommissionSchemeOutputForm> saleProductCommissionSchemeList(@Param("Name") String Name);

    void createSaleProductCommissionScheme(SaleProductCommissionSchemeEntity saleProductCommissionSchemeEntity);

    void deleteSaleProductCommissionScheme(@Param("EntityID") Integer EntityID);

    Integer getSaleProductCommissionScheme(@Param("EntityID") Integer EntityID);

}
