package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.TimeCardAccountEntity;
import com.zhelian.model.ibeauty.entity.TimeCardAccountExtendEntity;
import com.zhelian.model.ibeauty.form.input.TreatTimeCardAccountForm;
import com.zhelian.model.ibeauty.form.output.*;
import com.zhelian.model.micromall.form.output.ApplyEntityOutputForm;
import com.zhelian.model.micromall.form.output.MicromallTimeCardAccountOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TimeCardAccountMapper {

    void createTimeCardAccount(List<TimeCardAccountEntity> list);

    //根据账户ID 查询账户
    TimeCardAccountEntity getTimeCardAccountByAccountID(Integer AccountID);

    void updateTimeCardAccount(TimeCardAccountEntity timeCardAccountEntity);

    //时效卡账户列表
    List<TimeCardAccountOutputForm> timeCardAccount(TreatTimeCardAccountForm form);

    //时效卡消耗适用项目
    List<TimeCardAccountProjectOutputForm> timeCardAccountProject(@Param("TimeCardID") Integer TimeCardID, @Param("Name") String Name, @Param("EntityID") Integer EntityID);

    List<SaleGoodsParentCategoryOutputForm> timeCardAccountProjectCategory(@Param("TimeCardID") Integer TimeCardID, @Param("Name") String Name, @Param("EntityID") Integer EntityID);

    List<TimeCardAccountProjectChildDetailOutputForm> timeCardAccountProjectByCategory(@Param("TimeCardID") Integer TimeCardID, @Param("Name") String Name, @Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);


    List<TimeCardAccountProjectChildDetailOutputForm> customerMicromallTimeCardAccountProject(@Param("TimeCardID") Integer TimeCardID, @Param("Name") String Name);

    void updateTimeCardAccountActiveFalse(@Param("SaleBillID") String SaleBillID);

    void updatePackageCardTimeCardAccountActiveFalse(@Param("SaleBillID") String SaleBillID);

    List<TimeCardAccountOutputForm> arrearTimeCardAccount(@Param("CustomerID") String CustomerID, @Param("Name") String Name, @Param("BillDate") String BillDate, @Param("EntityID") Integer EntityID);

    void updateArrearTimeCardAccount(TimeCardAccountEntity timeCardAccountEntity);

    List<TimeCardAccountOutputForm> getPostPoneTimeCardAccount(@Param("CustomerID") String CustomerID, @Param("Name") String Name);

    void updateTimeCardAccountValidDate(TimeCardAccountEntity timeCardAccountEntity);

    List<TimeCardAccountOutputForm> getTranserTimeCardAccount(@Param("CustomerID") String CustomerID, @Param("Name") String Name);

    List<TimeCardAccountOutputForm> getRefundTimeCardAccount(@Param("CustomerID") String CustomerID, @Param("Name") String Name);

    void createTranserTimeCardAccount(TimeCardAccountEntity timeCardAccountEntity);

    void updateTimeCardAccountEntity(TimeCardAccountEntity timeCardAccountEntity);

    List<CustomerTimeCardAccountOutputForm> customerTimeCardAccount(@Param("CustomerID") String CustomerID, @Param("Active") Boolean Active, @Param("Name") String Name);

    List<TimeCardAccountOutputForm> customerMiniprogramTimeCardAccount(@Param("CustomerID") String CustomerID, @Param("Active") Boolean Active, @Param("Name") String Name);

    TimeCardAccountExtendEntity getTimeCardAccountExtend(@Param("AccountID") Integer AccountID, @Param("BillDate") String BillDate);

    List<MicromallTimeCardAccountOutputForm> customerMicromallTimeCardAccount(@Param("CustomerID") Integer CustomerID, @Param("Active") Boolean Active, @Param("Name") String Name);

    List<ApplyEntityOutputForm> customerMicromallTimeCardAccountEntity(@Param("ID") Integer ID, @Param("Name") String Name, @Param("CityCode") String CityCode, @Param("Longitude") String Longitude, @Param("Latitude") String Latitude);

    CustomerTimeCardAccountSumOutputForm customerTimeCardAccountSum(@Param("CustomerID") String CustomerID, @Param("Active") Boolean Active, @Param("Name") String Name);

    void updateTransferTimeCardAccountActiveFalse(@Param("ID") String ID);

    void updateTimeCardBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    void updatePackageCardTimeCardBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    TimeCardAccountEntity getTimeCardAccountBySaleBillTimeCardID(@Param("SaleBillTimeCardID") Integer SaleBillTimeCardID);

    void updateTimeCardAccountPerformance(TimeCardAccountEntity timeCardAccountEntity);

    TimeCardAccountEntity getTimeCardAccountBySaleBillPackageCardTimeCardID(@Param("SaleBillPackageCardTimeCardID") Integer SaleBillPackageCardTimeCardID);

    void updateTimeCardAccountArrear(TimeCardAccountEntity accountEntity);
}
