package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.form.output.SaleGeneralCardHandlerCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleGeneralCardHandlerCommissionMapper {

    List<SaleGeneralCardHandlerCommissionOutputForm> allSaleGeneralCardHandlerCommission(@Param("EntityID") Integer EntityID, @Param("GeneralCardID") Integer GeneralCardID);

    void deleteSaleGeneralCardHandler(@Param("EntityID") Integer EntityID, @Param("GeneralCardID") Integer GeneralCardID);

    void deleteSaleGeneralCardHandlerJobType(@Param("EntityID") Integer EntityID, @Param("GeneralCardID") Integer GeneralCardID);

    void insertSaleGeneralCardHandlerCommission(List list);

    void insertSaleGeneralCardHandlerJobTypeCommission(List list);

    void deleteSaleGeneralCardHandlerJobTypeCommission(Integer EntityID);

    void deleteSaleGeneralCardHandlerCommission(Integer EntityID);
}
