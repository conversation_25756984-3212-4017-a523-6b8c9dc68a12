package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.TreatTimeCardCategoryCommissionEntity;
import com.zhelian.model.ibeauty.entity.TreatTimeCardProjectCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.output.TreatGeneralCardCategoryCommissionOutputForm;
import com.zhelian.model.ibeauty.form.output.TreatGeneralCardCategoryOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatTimeCardCategoryCommissionMapper {

    TreatGeneralCardCategoryOutputForm treatTimeCardProjectCategoryCommission(@Param("EntityID") Integer EntityID);

    List<TreatGeneralCardCategoryCommissionOutputForm> allSaleTimeCardCategoryCommission(@Param("EntityID") Integer EntityID);

    void updateTreatTimeCardCommissionScheme(TreatTimeCardProjectCommissionSchemeEntity treatTimeCardProjectCommissionSchemeEntity);

    void deleteTreatTimeCardCategoryCommission(@Param("EntityID") Integer EntityID);


    void insertTreatTimeCardCategoryCommissionEntity(List<TreatTimeCardCategoryCommissionEntity> list);
}
