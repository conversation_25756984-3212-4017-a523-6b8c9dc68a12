package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.TreatChannelPerformanceGoodEntity;
import com.zhelian.model.ibeauty.entity.TreatChannelPerformancePackageCardGoodEntity;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatChannelPerformanceMapper {


    void createTreatChannelPerformanceProduct(List<TreatChannelPerformanceGoodEntity> list);

    void createTreatChannelPerformanceProject(List<TreatChannelPerformanceGoodEntity> list);

    void createTreatChannelPerformanceSavingCard(List<TreatChannelPerformanceGoodEntity> list);

    void createTreatChannelPerformanceGeneralCard(List<TreatChannelPerformanceGoodEntity> list);

    void createTreatChannelPerformanceTimeCard(List<TreatChannelPerformanceGoodEntity> list);

    void createTreatChannelPerformancePackageCardProduct(List<TreatChannelPerformancePackageCardGoodEntity> list);

    void createTreatChannelPerformancePackageCardProject(List<TreatChannelPerformancePackageCardGoodEntity> list);

    void createTreatChannelPerformancePackageCardSavingCard(List<TreatChannelPerformancePackageCardGoodEntity> list);

    void createTreatChannelPerformancePackageCardGeneralCard(List<TreatChannelPerformancePackageCardGoodEntity> list);

    void createTreatChannelPerformancePackageCardTimeCard(List<TreatChannelPerformancePackageCardGoodEntity> list);


    void deleteTreatChannelPerformanceProduct(String ID);

    void deleteTreatChannelPerformanceProject(String ID);

    void deleteTreatChannelPerformanceSavingCard(String ID);

    void deleteTreatChannelPerformanceGeneralCard(String ID);

    void deleteTreatChannelPerformanceTimeCard(String ID);

    void deleteTreatChannelPerformancePackageCardProduct(String ID);

    void deleteTreatChannelPerformancePackageCardProject(String ID);

    void deleteTreatChannelPerformancePackageCardSavingCard(String ID);

    void deleteTreatChannelPerformancePackageCardGeneralCard(String ID);

    void deleteTreatChannelPerformancePackageCardTimeCard(String ID);


}