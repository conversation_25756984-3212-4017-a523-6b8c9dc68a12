package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.TimeCardCategoryEntity;
import com.zhelian.model.ibeauty.form.output.TimeCardCategoryOutputForm;
import com.zhelian.model.report.form.output.GoodsCategoryForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;


@Repository
public interface TimeCardCategoryMapper {

    List<TimeCardCategoryOutputForm> timeCardCategoryList(@Param("Name") String Name, @Param("Active") Boolean Active);

    Integer getMaxSequence();

    void createTimeCardCategory(TimeCardCategoryEntity timeCardCategoryEntity);

    void updateTimeCardCategory(TimeCardCategoryEntity timeCardCategoryEntity);

    LinkedList<Integer> getASCList(Integer ID);

    void updateSequence(List<TimeCardCategoryEntity> list);

    Integer timeCardByCategoryCount(Integer ID);

    List<GoodsCategoryForm> timeCardGoodsCategoryAll(@Param("Name") String Name, @Param("Active") Boolean Active);
}
