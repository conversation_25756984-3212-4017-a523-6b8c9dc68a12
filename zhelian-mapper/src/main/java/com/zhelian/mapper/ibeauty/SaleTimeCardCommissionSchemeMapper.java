package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SaleTimeCardCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.output.SaleTimeCardCommissionSchemeOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleTimeCardCommissionSchemeMapper {

    List<SaleTimeCardCommissionSchemeOutputForm> saleTimeCardCommissionSchemeList(@Param("Name") String Name);

    void createSaleTimeCardCommissionScheme(SaleTimeCardCommissionSchemeEntity saleTimeCardCommissionSchemeEntity);

    void deleteSaleTimeCardCommissionScheme(@Param("EntityID") Integer EntityID);

    Integer findSaleTimeCardCommissionSchemeByEntityID(@Param("EntityID") Integer EntityID);
}
