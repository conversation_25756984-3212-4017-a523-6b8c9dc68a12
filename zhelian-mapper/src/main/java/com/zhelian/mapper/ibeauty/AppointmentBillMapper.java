package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.*;
import com.zhelian.model.ibeauty.form.output.*;
import com.zhelian.model.miniprogram.form.output.AppointmentBillRemainOutputForm;
import com.zhelian.model.miniprogram.form.output.ChannelVisitAppointmentOutputForm;
import com.zhelian.model.miniprogram.form.output.MiniAppointmentBillOutputForm;
import com.zhelian.model.miniprogram.form.output.MiniAppointmentCountOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AppointmentBillMapper {

    List<AppointmentBillAppointmentOutputForm> employeeUndistributedScheduleAppointment(@Param("EntityID") Integer EntityID, @Param("AppointmentDate") String AppointmentDate);

    List<AppointmentBillEmpOutputForm> employeeScheduleAppointment(@Param("EntityID") Integer EntityID, @Param("AppointmentDate") String AppointmentDate, @Param("ServicerID") Integer ServicerID);

    void createAppointmentBill(AppointmentBillEntity appointmentBillEntity);

    void createAppointmentBillProcess(AppointmentBillProcessEntity appointmentBillProcessEntity);

    void createAppointmentBillProject(List<AppointmentBillProjectEntity> list);

    AppointmentBillEntity getAppointmentBill(@Param("ID") String ID);

    void updateAppointmentBill(AppointmentBillEntity appointmentBillEntity);

    void deleteAppointmentBill(@Param("ID") String ID);

    void createAppointmentBillEmployee(AppointmentBillEmployeeEntity appointmentBillEmployeeEntity);

    void createAppointmentBillEmployeeList(List<AppointmentBillEmployeeEntity> list);

    AppointmentBillEmployeeEntity getAppointmentBillEmployee(@Param("ID") String ID);

    void updateAppointmentBillEmployee(AppointmentBillEmployeeEntity appointmentBillEmployeeEntity);

    List<AppointmentBillOutputForm> appointmentBillList(@Param("EntityID") Integer EntityID, @Param("AppointmentBillID") String AppointmentBillID, @Param("Name") String Name, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("Type") String Type, @Param("Status") String Status, @Param("CustomerID") Integer CustomerID, @Param("CreatedBy") String CreatedBy, @Param("CreatedOnStartDate") String CreatedOnStartDate, @Param("CreatedOnEndDate") String CreatedOnEndDate, @Param("ServicerID") Integer ServicerID, @Param("ServicerEmployeeID") String ServicerEmployeeID, @Param("Channel") String Channel, @Param("AppointmentTypeID") Integer AppointmentTypeID, @Param("CustomerSourceID") Integer CustomerSourceID, @Param("CustomerLevelID") Integer CustomerLevelID,@Param("CustomerChannel") String CustomerChannel);

    AppointmentBillInfoOutputForm appointmentBillInfo(@Param("ID") String ID);

    List<MiniAppointmentBillOutputForm> appointmentBillInfoList(@Param("EntityID") Integer EntityID, @Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("AppointmentDate") String AppointmentDate, @Param("Status") String Status, @Param("CustomerID") Integer CustomerID, @Param("AppointmentTypeID") Integer AppointmentTypeID, @Param("ServicerID") Integer ServicerID, @Param("ServicerEmployeeID") String ServicerEmployeeID, @Param("Channel") String Channel, @Param("CreatedBy") String CreatedBy, @Param("CreatedOnStartDate") String CreatedOnStartDate, @Param("CreatedOnEndDate") String CreatedOnEndDate, @Param("CustomerSourceID") Integer CustomerSourceID);

    void createAppointmentBillAudio(List<AppointmentBillAudioEntity> list);

    void deleteAppointmentBillAudio(@Param("ID") String ID);

    List<MiniAppointmentCountOutputForm> getUnToShopCustomerCount(@Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("EntityID") Integer EntityID, @Param("EmployeeID") String EmployeeID);

    List<AppointmentBillRemainOutputForm> appointmentBillRemainList(@Param("EntityID") Integer EntityID, @Param("AppointmentDate") String AppointmentDate);

    List<MiniAppointmentBillOutputForm> customerAppointmentBill(@Param("CustomerID") Integer CustomerID, @Param("Status") String Status, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("AppointmentTypeID") Integer AppointmentTypeID, @Param("ServicerID") Integer ServicerID, @Param("ServicerEmployeeID") String ServicerEmployeeID, @Param("EmployeeName") String EmployeeName);


    void deleteAppointmentBillProject(@Param("ID") String ID);

    List<AppointmentBillEntity> getCustomerAppointment(@Param("EntityID") Integer EntityID, @Param("BillDate") String BillDate, @Param("CustomerID") Integer CustomerID);

    void updateAppointmentBillStatus(@Param("EntityID") Integer EntityID, @Param("BillDate") String BillDate, @Param("CustomerID") Integer CustomerID);

    List<WXAppointmentBillInfoOutputForm> getWXAppointmentBill(String ID);

    List<WXAppointmentBillRemindInfoOutputForm> getWXAppointmentBillRemind();

    List<AppointmentBillOutputExcelForm> excel(@Param("EntityID") Integer EntityID, @Param("AppointmentBillID") String AppointmentBillID, @Param("Name") String Name, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("Type") String Type, @Param("Status") String Status, @Param("CustomerID") Integer CustomerID, @Param("CreatedBy") String CreatedBy, @Param("CreatedOnStartDate") String CreatedOnStartDate, @Param("CreatedOnEndDate") String CreatedOnEndDate, @Param("ServicerID") Integer ServicerID, @Param("ServicerEmployeeID") String ServicerEmployeeID, @Param("Channel") String Channel, @Param("AppointmentTypeID") Integer AppointmentTypeID, @Param("CustomerSourceID") Integer CustomerSourceID, @Param("CustomerLevelID") Integer CustomerLevelID,@Param("CustomerChannel") String CustomerChannel);

    void updateAppointmentBillIsSendRemindMessage(List<String> list);

    List<ChannelVisitAppointmentOutputForm> getChannelAppointmentBill(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("Status") String Status, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("EntityID") Integer EntityID, @Param("ChannelID") String ChannelID, @Param("ReceptionEmployeeID") String ReceptionEmployeeID);

    List<AppointmentBillServicerInfoOutputExtendForm> getAppointmentBillServicer(Integer EntityID);

    List<AppointmentBillServicerInfoOutputForm> getAppointmentBillServicerEmployee(@Param("EntityID") Integer EntityID, @Param("AppointmentDate") String AppointmentDate, @Param("CanChooseRestEmployee") Boolean CanChooseRestEmployee);

    List<AppointmentBillServicerInfoOutputForm> getAppointmentBillServicerEmployeeAll(@Param("EntityID") Integer EntityID);

    void deleteAppointmentBillServicer(String ID);

    List<AppointmentBillProcessOutputForm> appointmentBillProcess(String AppointmentBillID);

    List<CustomerShortInfoOutputExtendForm> customerList(@Param("Name") String Name, @Param("EntityID") Integer EntityID);

    void deleteAppointmentBillProcess(@Param("ID") String ID);

    List<AppointmentCustomerBillOutputForm> getCustomerAppointmentAll(@Param("CustomerID") Integer CustomerID, @Param("EntityID") Integer EntityID, @Param("StartDate") String StartDate);

    AppointmentCustomerNumberOutputForm getCustomerAppointmentNumber(@Param("CustomerID") Integer CustomerID, @Param("EntityID") Integer EntityID, @Param("StartDate") String StartDate);

}
