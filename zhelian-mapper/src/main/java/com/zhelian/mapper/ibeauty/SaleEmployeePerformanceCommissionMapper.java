package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.form.output.*;
import com.zhelian.model.ibeauty.service.SaleCommissionEntity;
import com.zhelian.model.ibeauty.service.SaleEmployeePerformanceCommissionGoodsEntity;
import com.zhelian.model.ibeauty.service.SaleEmployeePerformanceCommissionPackageCardGoodsEntity;
import com.zhelian.model.ibeauty.service.SalePerformanceEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SaleEmployeePerformanceCommissionMapper {


    //获取销售业绩
    SalePerformanceEntity getSaleProductPerformance(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("SaleHandlerID") int SaleHandlerID, @Param("GoodsID") int GoodsID);

    SalePerformanceEntity getSaleProjectPerformance(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("SaleHandlerID") int SaleHandlerID, @Param("GoodsID") int GoodsID);

    SalePerformanceEntity getSaleGeneralCardPerformance(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("SaleHandlerID") int SaleHandlerID, @Param("GoodsID") int GoodsID);

    SalePerformanceEntity getSaleTimeCardPerformance(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("SaleHandlerID") int SaleHandlerID, @Param("GoodsID") int GoodsID);

    SalePerformanceEntity getSaleSavingCardPerformance(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("SaleHandlerID") int SaleHandlerID, @Param("GoodsID") int GoodsID);

    //获取套餐卡销售业绩
    SalePerformanceEntity getSalePackageCardProductPerformance(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("SaleHandlerID") int SaleHandlerID, @Param("GoodsID") int GoodsID);

    SalePerformanceEntity getSalePackageCardProjectPerformance(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("SaleHandlerID") int SaleHandlerID, @Param("GoodsID") int GoodsID);

    SalePerformanceEntity getSalePackageCardGeneralCardPerformance(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("SaleHandlerID") int SaleHandlerID, @Param("GoodsID") int GoodsID);

    SalePerformanceEntity getSalePackageCardTimeCardPerformance(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("SaleHandlerID") int SaleHandlerID, @Param("GoodsID") int GoodsID);

    SalePerformanceEntity getSalePackageCardSavingCardPerformance(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("SaleHandlerID") int SaleHandlerID, @Param("GoodsID") int GoodsID);


    //获取销售提佣
    SaleCommissionEntity getSaleProductCommission(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("SaleHandlerID") int SaleHandlerID, @Param("GoodsID") int GoodsID);

    SaleCommissionEntity getSaleProjectCommission(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("SaleHandlerID") int SaleHandlerID, @Param("GoodsID") int GoodsID);

    SaleCommissionEntity getSaleGeneralCardCommission(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("SaleHandlerID") int SaleHandlerID, @Param("GoodsID") int GoodsID);

    SaleCommissionEntity getSaleTimeCardCommission(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("SaleHandlerID") int SaleHandlerID, @Param("GoodsID") int GoodsID);

    SaleCommissionEntity getSaleSavingCardCommission(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("SaleHandlerID") int SaleHandlerID, @Param("GoodsID") int GoodsID);

    //获取套餐卡销售提佣
    SaleCommissionEntity getSalePackageCardProductCommission(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("SaleHandlerID") int SaleHandlerID, @Param("GoodsID") int GoodsID);

    SaleCommissionEntity getSalePackageCardProjectCommission(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("SaleHandlerID") int SaleHandlerID, @Param("GoodsID") int GoodsID);

    SaleCommissionEntity getSalePackageCardGeneralCardCommission(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("SaleHandlerID") int SaleHandlerID, @Param("GoodsID") int GoodsID);

    SaleCommissionEntity getSalePackageCardTimeCardCommission(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("SaleHandlerID") int SaleHandlerID, @Param("GoodsID") int GoodsID);

    SaleCommissionEntity getSalePackageCardSavingCardCommission(@Param("EntityID") int EntityID, @Param("EmployeeID") String EmployeeID, @Param("SaleHandlerID") int SaleHandlerID, @Param("GoodsID") int GoodsID);

    //创建员工非套餐卡业绩和提佣
    void createSaleEmployeePerformanceCommissionProduct(List<SaleEmployeePerformanceCommissionGoodsEntity> list);

    void createSaleEmployeePerformanceCommissionProject(List<SaleEmployeePerformanceCommissionGoodsEntity> list);

    void createSaleEmployeePerformanceCommissionGeneralCard(List<SaleEmployeePerformanceCommissionGoodsEntity> list);

    void createSaleEmployeePerformanceCommissionTimeCard(List<SaleEmployeePerformanceCommissionGoodsEntity> list);

    void createSaleEmployeePerformanceCommissionSavingCard(List<SaleEmployeePerformanceCommissionGoodsEntity> list);

    //创建员工套餐卡业绩和提佣
    void createSaleEmployeePerformanceCommissionPackageCardProduct(List<SaleEmployeePerformanceCommissionPackageCardGoodsEntity> list);

    void createSaleEmployeePerformanceCommissionPackageCardProject(List<SaleEmployeePerformanceCommissionPackageCardGoodsEntity> list);

    void createSaleEmployeePerformanceCommissionPackageCardSavingCard(List<SaleEmployeePerformanceCommissionPackageCardGoodsEntity> list);

    void createSaleEmployeePerformanceCommissionPackageCardGeneralCard(List<SaleEmployeePerformanceCommissionPackageCardGoodsEntity> list);

    void createSaleEmployeePerformanceCommissionPackageCardTimeCard(List<SaleEmployeePerformanceCommissionPackageCardGoodsEntity> list);

    //删除员工非套餐卡业绩和提佣
    void deleteSaleEmployeePerformanceCommissionProduct(@Param("SaleBillID") String SaleBillID);

    void deleteSaleEmployeePerformanceCommissionProject(@Param("SaleBillID") String SaleBillID);

    void deleteSaleEmployeePerformanceCommissionGeneralCard(@Param("SaleBillID") String SaleBillID);

    void deleteSaleEmployeePerformanceCommissionTimeCard(@Param("SaleBillID") String SaleBillID);

    void deleteSaleEmployeePerformanceCommissionSavingCard(@Param("SaleBillID") String SaleBillID);


    //删除员工套餐卡业绩和提佣
    void deleteSaleEmployeePerformanceCommissionPackageCardProduct(@Param("SaleBillID") String SaleBillID);

    void deleteSaleEmployeePerformanceCommissionPackageCardProject(@Param("SaleBillID") String SaleBillID);

    void deleteSaleEmployeePerformanceCommissionPackageCardSavingCard(@Param("SaleBillID") String SaleBillID);

    void deleteSaleEmployeePerformanceCommissionPackageCardGeneralCard(@Param("SaleBillID") String SaleBillID);

    void deleteSaleEmployeePerformanceCommissionPackageCardTimeCard(@Param("SaleBillID") String SaleBillID);

    List<SaleBillProductOutputForm> saleBillProductEmployeePerformanceCommission(@Param("SaleBillID") String SaleBillID);

    List<SaleBillProjectOutputForm> saleBillProjectEmployeePerformanceCommission(@Param("SaleBillID") String SaleBillID);

    List<SaleBillGeneralCardOutputForm> saleBillGeneralCardEmployeePerformanceCommission(@Param("SaleBillID") String SaleBillID);

    List<SaleBillTimeCardOutputForm> saleBillTimeCardEmployeePerformanceCommission(@Param("SaleBillID") String SaleBillID);

    List<SaleBillSavingCardOutputForm> saleBillSavingCardEmployeePerformanceCommission(@Param("SaleBillID") String SaleBillID);

    List<SaleBillPackageCardProductListOutputForm> saleBillPackageCardProductEmployeePerformanceCommission(@Param("SaleBillID") String SaleBillID);

    List<SaleBillPackageCardProjectListOutputForm> saleBillPackageCardProjectEmployeePerformanceCommission(@Param("SaleBillID") String SaleBillID);

    List<SaleBillPackageCardGeneralCardListOutputForm> saleBillPackageCardGeneralCardEmployeePerformanceCommission(@Param("SaleBillID") String SaleBillID);

    List<SaleBillPackageCardTimeCardListOutputForm> saleBillPackageCardTimeCardEmployeePerformanceCommission(@Param("SaleBillID") String SaleBillID);

    List<SaleBillPackageCardSavingCardListOutputForm> saleBillPackageCardSavingCardEmployeePerformanceCommission(@Param("SaleBillID") String SaleBillID);

    void updateSaleEmployeePerformanceProductBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    void updateSaleEmployeePerformanceProjectBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    void updateSaleEmployeePerformanceGeneralCardBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    void updateSaleEmployeePerformanceTimeCardBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    void updateSaleEmployeePerformanceSavingCardBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    void updateSaleEmployeePerformancePackageCardProductBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    void updateSaleEmployeePerformancePackageCardProjectBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    void updateSaleEmployeePerformancePackageCardSavingCardBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    void updateSaleEmployeePerformancePackageCardGeneralCardBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    void updateSaleEmployeePerformancePackageCardTimeCardBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);
}
