package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.TreatSavingCardCategoryCommissionEntity;
import com.zhelian.model.ibeauty.entity.TreatSavingCardCommissionSchemeEntity;
import com.zhelian.model.ibeauty.form.output.TreatSavingCardCategoryOutputForm;
import com.zhelian.model.ibeauty.form.output.TreatSavingCardChildCategoryCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatSavingCardCategoryCommissionMapper {

    TreatSavingCardCategoryOutputForm treatSavingCardCategoryCommission(@Param("EntityID") Integer EntityID);

    List<TreatSavingCardChildCategoryCommissionOutputForm> allTreatSavingCardCategoryCommission(@Param("EntityID") Integer EntityID);

    void updateTreatSavingCardCommissionScheme(TreatSavingCardCommissionSchemeEntity treatSavingCardCommissionSchemeEntity);

    void deleteTreatSavingCardCategoryCommissionEntity(@Param("EntityID") Integer EntityID);

    void insertTreatSavingCardCategoryCommissionEntity(List<TreatSavingCardCategoryCommissionEntity> list);
}
