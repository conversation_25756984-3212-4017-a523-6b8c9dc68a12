package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.ProjectAccountEntity;
import com.zhelian.model.ibeauty.entity.ProjectAccountExcendEntity;
import com.zhelian.model.ibeauty.form.input.TreatProjectAccountForm;
import com.zhelian.model.ibeauty.form.output.CustomerProjectAccountOutputForm;
import com.zhelian.model.ibeauty.form.output.CustomerProjectAccountSumOutputForm;
import com.zhelian.model.ibeauty.form.output.ProjectAccountOutputForm;
import com.zhelian.model.ibeauty.form.output.TranserProjectAccountOutputForm;
import com.zhelian.model.micromall.form.output.ApplyEntityOutputForm;
import com.zhelian.model.micromall.form.output.MicromallProjectAccountOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProjectAccountMapper {

    void createProjectAccount(List<ProjectAccountEntity> list);

    //项目账户列表
    List<ProjectAccountOutputForm> getProjectAccount(TreatProjectAccountForm form);

    //根据账户ID 查询账户
    ProjectAccountExcendEntity getProjectAccountByAccountID(Integer AccountID);

    void updateProjectAccountBalance(ProjectAccountEntity projectAccountEntity);

    void updateProjectAccountActiveFalse(@Param("SaleBillID") String SaleBillID);

    void updatePackageCardProjectAccountActiveFalse(@Param("SaleBillID") String SaleBillID);

    List<ProjectAccountOutputForm> arrearProjectAccount(@Param("CustomerID") String CustomerID, @Param("Name") String Name, @Param("EntityID") Integer EntityID);

    void updateArrearProjectAccount(ProjectAccountEntity projectAccountEntity);

    List<ProjectAccountOutputForm> getTranserProjectAccount(@Param("CustomerID") String CustomerID, @Param("Name") String Name);

    List<ProjectAccountOutputForm> getRefundProjectAccount(@Param("CustomerID") String CustomerID, @Param("Name") String Name);

    void createTranserProjectAccount(ProjectAccountEntity projectAccountEntity);

    TranserProjectAccountOutputForm getProjectAccountEntity(@Param("ID") Integer ID);

    List<CustomerProjectAccountOutputForm> customerProjectAccount(@Param("CustomerID") String CustomerID, @Param("Active") Boolean Active, @Param("Name") String Name);

    List<ProjectAccountOutputForm> customerMiniprogramProjectAccount(@Param("CustomerID") String CustomerID, @Param("Active") Boolean Active, @Param("Name") String Name);

    void updateCustomerProjectAccount(ProjectAccountEntity projectAccountEntity);

    List<ProjectAccountEntity> getProjectAccountBySaleBillID(String SaleBillID);

    List<MicromallProjectAccountOutputForm> customerMicromallProjectAccount(@Param("CustomerID") Integer CustomerID, @Param("Active") Boolean Active, @Param("Name") String Name);

    List<ApplyEntityOutputForm> customerMicromallProjectAccountEntity(@Param("ID") Integer ID, @Param("Name") String Name, @Param("CityCode") String CityCode, @Param("Longitude") String Longitude, @Param("Latitude") String Latitude);

    CustomerProjectAccountSumOutputForm customerProjectAccountSum(@Param("CustomerID") String CustomerID, @Param("Active") Boolean Active, @Param("Name") String Name);

    void updateProjectRemark(@Param("SaleBillGoodID") Integer SaleBillGoodID, @Param("Remark") String Remark);

    void updatePackageCardProjectRemark(@Param("SaleBillGoodID") Integer SaleBillGoodID, @Param("Remark") String Remark);

    void updateTransferProjectAccountActiveFalse(@Param("ID") String ID);

    void updateProjectBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    void updatePackageCardProjectBillDate(@Param("SaleBillID") String SaleBillID, @Param("BillDate") String BillDate);

    ProjectAccountExcendEntity getProjectAccountBySaleBillProjectID(@Param("SaleBillProjectID") Integer SaleBillProjectID);

    void updateProjectAccount(ProjectAccountEntity projectAccountEntity);

    ProjectAccountExcendEntity getProjectAccountBySaleBillPackageCardProjectID(@Param("SaleBillPackageCardProjectID") Integer SaleBillPackageCardProjectID);

    void updateProjectAccountArrear(ProjectAccountEntity accountEntity);
}
