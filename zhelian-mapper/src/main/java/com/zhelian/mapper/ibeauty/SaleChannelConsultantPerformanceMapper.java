package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.service.SaleChannelEmployeePerformanceGoodsEntity;
import com.zhelian.model.ibeauty.service.SaleChannelEmployeePerformancePackageCardGoodsEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SaleChannelConsultantPerformanceMapper {


    //创建渠道非套餐卡业绩
    void createSaleConsultantChannelPerformanceProduct(List<SaleChannelEmployeePerformanceGoodsEntity> list);

    void createSaleConsultantChannelPerformanceProject(List<SaleChannelEmployeePerformanceGoodsEntity> list);

    void createSaleConsultantChannelPerformanceGeneralCard(List<SaleChannelEmployeePerformanceGoodsEntity> list);

    void createSaleConsultantChannelPerformanceTimeCard(List<SaleChannelEmployeePerformanceGoodsEntity> list);

    void createSaleConsultantChannelPerformanceSavingCard(List<SaleChannelEmployeePerformanceGoodsEntity> list);

    //创建门店套餐卡业绩
    void createSaleConsultantChannelPerformancePackageCardProduct(List<SaleChannelEmployeePerformancePackageCardGoodsEntity> list);

    void createSaleConsultantChannelPerformancePackageCardProject(List<SaleChannelEmployeePerformancePackageCardGoodsEntity> list);

    void createSaleConsultantChannelPerformancePackageCardGeneralCard(List<SaleChannelEmployeePerformancePackageCardGoodsEntity> list);

    void createSaleConsultantChannelPerformancePackageCardTimeCard(List<SaleChannelEmployeePerformancePackageCardGoodsEntity> list);

    void createSaleConsultantChannelPerformancePackageCardSavingCard(List<SaleChannelEmployeePerformancePackageCardGoodsEntity> list);


    void deleteSaleConsultantChannelPerformanceProduct(String ID);

    void deleteSaleConsultantChannelPerformanceProject(String ID);

    void deleteSaleConsultantChannelPerformanceGeneralCard(String ID);

    void deleteSaleConsultantChannelPerformanceTimeCard(String ID);

    void deleteSaleConsultantChannelPerformanceSavingCard(String ID);

    void deleteSaleConsultantChannelPerformancePackageCardProduct(String ID);

    void deleteSaleConsultantChannelPerformancePackageCardProject(String ID);

    void deleteSaleConsultantChannelPerformancePackageCardGeneralCard(String ID);

    void deleteSaleConsultantChannelPerformancePackageCardTimeCard(String ID);

    void deleteSaleConsultantChannelPerformancePackageCardSavingCard(String ID);
}
