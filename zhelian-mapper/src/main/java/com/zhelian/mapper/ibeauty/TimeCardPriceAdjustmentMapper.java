package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.TimeCardPriceAdjustmentEntity;
import com.zhelian.model.ibeauty.form.output.TimeCardPriceAdjustmentOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TimeCardPriceAdjustmentMapper {
    List<TimeCardPriceAdjustmentOutputForm> timeCardPriceAdjustmentList(@Param("TimeCardName") String TimeCardName, @Param("EntityName") String EntityName);

    Integer timeCardPriceAdjustmentCount(@Param("EntityID") Integer EntityID, @Param("TimeCardID") Integer TimeCardID);

    void insertTimeCardPriceAdjustmentEntity(TimeCardPriceAdjustmentEntity TimeCardPriceAdjustmentEntity);

    void updateTimeCardPriceAdjustmentEntity(TimeCardPriceAdjustmentEntity TimeCardPriceAdjustmentEntity);

    void deleteTimeCardPriceAdjustmentEntity(@Param("EntityID") Integer EntityID, @Param("TimeCardID") Integer TimeCardID);
}
