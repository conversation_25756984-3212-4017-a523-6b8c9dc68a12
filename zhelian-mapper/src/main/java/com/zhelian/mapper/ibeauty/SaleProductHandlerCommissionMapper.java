package com.zhelian.mapper.ibeauty;


import com.zhelian.model.ibeauty.entity.SaleProductHandlerCommissionEntity;
import com.zhelian.model.ibeauty.entity.SaleProductHandlerJobTypeCommissionEntity;
import com.zhelian.model.ibeauty.form.output.SaleProductHandlerCommissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleProductHandlerCommissionMapper {

    List<SaleProductHandlerCommissionOutputForm> allSaleProductHandlerCommission(@Param("EntityID") Integer EntityID, @Param("ProductID") Integer ProductID);

    void deleteSaleProductHandler(@Param("EntityID") Integer EntityID, @Param("ProductID") Integer ProductID);

    void deleteSaleProductHandlerJobType(@Param("EntityID") Integer EntityID, @Param("ProductID") Integer ProductID);

    void updateSaleProductHandlerCommission(List<SaleProductHandlerCommissionEntity> list);

    void insertSaleProductHandlerJobTypeCommission(List<SaleProductHandlerJobTypeCommissionEntity> list);

    void deleteSaleProductHandlerCommissionByEntityID(@Param("EntityID") Integer EntityID);

    void deleteSaleProductHandlerJobTypeCommissionByEntityID(@Param("EntityID") Integer EntityID);
}
