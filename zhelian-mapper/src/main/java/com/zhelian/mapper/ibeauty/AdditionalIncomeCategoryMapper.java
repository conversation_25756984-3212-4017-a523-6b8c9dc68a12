package com.zhelian.mapper.ibeauty;

import com.zhelian.model.ibeauty.entity.AdditionalIncomeCategoryEntity;
import com.zhelian.model.ibeauty.form.output.AdditionalSpendingOrIncomeOrPaymentsCategoryOutputForm;
import com.zhelian.model.ibeauty.form.output.SpendingCategoryOrIncomeOrPaymentsOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;


@Repository
public interface AdditionalIncomeCategoryMapper {


    List<SpendingCategoryOrIncomeOrPaymentsOutputForm> additionalIncomeCategoryList(@Param("Name") String Name, @Param("Active") Boolean Active);

    LinkedList<Integer> getIDOrderBySequenceASC(int ID);

    Integer getMaxSequence();

    void createAdditionalIncomeCategory(AdditionalIncomeCategoryEntity additionalIncomeCategoryEntity);

    void updateAdditionalIncomeCategory(AdditionalIncomeCategoryEntity additionalIncomeCategoryEntity);

    void updateSequence(List<AdditionalIncomeCategoryEntity> list);

    List<AdditionalSpendingOrIncomeOrPaymentsCategoryOutputForm> getAdditionalIncomeCategory();

}
