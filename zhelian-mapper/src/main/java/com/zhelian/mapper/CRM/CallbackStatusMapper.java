package com.zhelian.mapper.CRM;

import com.zhelian.model.CRM.entity.CallbackStatusEntity;
import com.zhelian.model.ibeauty.form.output.PayMethodOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;


@Repository
public interface CallbackStatusMapper {

    List<PayMethodOutputForm> callbackStatusList(@Param("Name") String Name, @Param("Active") Boolean Active);

    LinkedList<Integer> getIDOrderBySequenceASC(int ID);

    Integer getMaxSequence();

    void createCallbackStatus(CallbackStatusEntity callbackStatusEntity);

    void updateCallbackStatus(CallbackStatusEntity callbackStatusEntity);

    void updateSequence(List<CallbackStatusEntity> list);


}
