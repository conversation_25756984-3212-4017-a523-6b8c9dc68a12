package com.zhelian.mapper.CRM;

import com.zhelian.model.CRM.entity.DiagnosisRecordAttachmentEntity;
import com.zhelian.model.CRM.entity.DiagnosisRecordEntity;
import com.zhelian.model.CRM.form.output.DiagnosisExcelOutputForm;
import com.zhelian.model.CRM.form.output.DiagnosisOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface DiagnosisMapper {

    List<DiagnosisOutputForm> diagnosisList(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("IsDiagnosis") Boolean IsDiagnosis);


    List<DiagnosisOutputForm> allDiagnosisList(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("IsDiagnosis") Boolean IsDiagnosis, @Param("EntityID") Integer EntityID, @Param("CustomerLevelID") Integer CustomerLevelID, @Param("ChannelName") String ChannelName);

    DiagnosisRecordEntity getDiagnosisRecordEntity(Integer DiagnosisID);

    void createDiagnosisRecordAttachmentEntity(List<DiagnosisRecordAttachmentEntity> list);

    List<DiagnosisExcelOutputForm> excelDisPlayPhone(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("IsDiagnosis") Boolean IsDiagnosis, @Param("EntityID") Integer EntityID, @Param("CustomerLevelID") Integer CustomerLevelID, @Param("ChannelName") String ChannelName);

    List<DiagnosisExcelOutputForm> excelNoDisPlayPhone(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("IsDiagnosis") Boolean IsDiagnosis, @Param("EntityID") Integer EntityID, @Param("CustomerLevelID") Integer CustomerLevelID, @Param("ChannelName") String ChannelName);
}
