package com.zhelian.mapper.CRM;

import com.zhelian.model.CRM.entity.CallbackRecordEntity;
import com.zhelian.model.CRM.entity.CallbackRecordProjectAttachmentEntity;
import com.zhelian.model.CRM.entity.CallbackRecordProjectEntity;
import com.zhelian.model.CRM.form.output.*;
import com.zhelian.model.khs.entity.EntityEntity;
import com.zhelian.model.yingxiaoyun.form.output.YXYCallBackOutListProjectPutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface CallbackMapper {

    List<CallbackOutputForm> getCallbackList(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("IsCallback") Boolean IsCallback, @Param("CallbackMethodID") Integer CallbackMethodID, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("CallbackStatusID") Integer CallbackStatusID);

    List<CallbackOutputForm> allCallbackList(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("IsCallback") Boolean IsCallback, @Param("CallbackMethodID") Integer CallbackMethodID, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("CallbackEmployeeID") String CallbackEmployeeID, @Param("CallbackEntityID") Integer CallbackEntityID, @Param("IsShowOwnCallback") Boolean IsShowOwnCallback, @Param("CallbackStatusID") Integer CallbackStatusID, @Param("CustomerLevelID") Integer CustomerLevelID, @Param("ChannelName") String ChannelName);

    void createCallback(CallbackRecordEntity callbackRecordEntity);

    void createCallbackProject(CallbackRecordProjectEntity callbackRecordProjectEntity);


    List<CallbackTreatBillProjectOutputForm> getTreatBillByCallbackProject(@Param("ID") Integer ID, @Param("BillID") String BillID);

    List<EntityEntity> allEntity();

    List<String> getCustomerServicer(@Param("CustomerID") Integer CustomerID);

    List<String> getCustomerServicerEmployee(@Param("ServicerID") Integer ServicerID, @Param("CustomerID") Integer CustomerID);


    CallbackRecordEntity getCallbackRecord(Integer CallbackRecordID);

    void updateCallbackRecord(CallbackRecordEntity callbackRecordEntity);

    CallbackRecordProjectEntity getCallbackRecordProject(Integer CallbackRecordProjectID);

    void updateCallbackRecordProject(CallbackRecordProjectEntity callbackRecordProjectEntity);

    void insertCallbackProjectAttachment(List<CallbackRecordProjectAttachmentEntity> list);

    CallbackRecordDetailOutputForm callbackRecordDetail(Integer CallbackRecordID);

    List<YXYCallBackOutListProjectPutForm> yxyCallbackRecordDetail(Integer CallbackRecordID);

    void deleteCallbackRecordProjectAttachment(Integer CallbackRecordID);

    List<CallbackRecordProjectAttachmentEntity> getCallbackRecordProjectAttachment(Integer CallbackRecordID);

    CallbackRecordEntity getCallbackRecordByDateAndCallback(@Param("CustomerID") Integer CustomerID, @Param("PlannedOn") String PlannedOn, @Param("EmployeeID") String EmployeeID);

    Integer getCallbackRecordProjectByRecordIDAndBillIDAndProjectIDAndCalbackCycle(@Param("RecordID") Integer RecordID, @Param("BillID") String BillID, @Param("ProjectID") Integer ProjectID, @Param("CallbackCycle") Integer CallbackCycle);

    void deleteCallbackRecord(Integer CallbackRecordID);

    void deleteCallbackRecordProject(Integer CallbackRecordID);

    List<CallbackRecordEntity> getCallbackRecordList(Integer[] array);

    List<String> getTreatBillEmployee(@Param("ProjectTreatHandlerID") Integer ProjectTreatHandlerID, @Param("SavingCardProjectTreatHandlerID") Integer SavingCardProjectTreatHandlerID, @Param("ID") Integer ID);

    List<CallbackExcelOutputForm> excelDisPlayPhone(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("IsCallback") Boolean IsCallback, @Param("CallbackMethodID") Integer CallbackMethodID, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("CallbackEmployeeID") String CallbackEmployeeID, @Param("CallbackEntityID") Integer CallbackEntityID, @Param("IsShowOwnCallback") Boolean IsShowOwnCallback, @Param("CallbackStatusID") Integer CallbackStatusID, @Param("CustomerLevelID") Integer CustomerLevelID, @Param("ChannelName") String ChannelName);

    List<CallbackExcelOutputForm> excelNoDisPlayPhone(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("IsCallback") Boolean IsCallback, @Param("CallbackMethodID") Integer CallbackMethodID, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("CallbackEmployeeID") String CallbackEmployeeID, @Param("CallbackEntityID") Integer CallbackEntityID, @Param("IsShowOwnCallback") Boolean IsShowOwnCallback, @Param("CallbackStatusID") Integer CallbackStatusID, @Param("CustomerLevelID") Integer CustomerLevelID, @Param("ChannelName") String ChannelName);

}
