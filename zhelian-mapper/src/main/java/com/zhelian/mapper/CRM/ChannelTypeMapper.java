package com.zhelian.mapper.CRM;

import com.zhelian.model.CRM.entity.ChannelTypeEntity;
import com.zhelian.model.CRM.form.input.ChannelTypeQueryForm;
import com.zhelian.model.CRM.form.output.ChannelTypeOutputForm;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;

@Repository
public interface ChannelTypeMapper {

    List<ChannelTypeOutputForm> getPageList(ChannelTypeQueryForm form);

    void create(ChannelTypeEntity entity);

    void updateChannelType(ChannelTypeEntity entity);

    Integer getMaxSequence();

    LinkedList<Integer> getIDOrderBySequenceASC(int ID);

    void updateSequence(List<ChannelTypeEntity> list);

    List<ChannelTypeOutputForm> all(ChannelTypeQueryForm form);

    ChannelTypeEntity getChannelType(Integer ID);

    Integer channelByChannelTypeCount(Integer ID);
}
