package com.zhelian.mapper.CRM;

import com.zhelian.model.ibeauty.entity.CustomerReduceConfigEntity;
import com.zhelian.model.ibeauty.form.output.CustomerReduceConfigOutputForm;
import org.springframework.stereotype.Repository;

@Repository
public interface CustomerReduceConfigMapper {

    CustomerReduceConfigOutputForm getCustomerReduceConfig();

    CustomerReduceConfigEntity getCustomerReduceConfigEntity();

    void updateCustomerReduceConfig(CustomerReduceConfigEntity customerReduceConfig);
}
