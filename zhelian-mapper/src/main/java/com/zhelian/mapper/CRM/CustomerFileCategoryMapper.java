package com.zhelian.mapper.CRM;

import com.zhelian.model.ibeauty.entity.CustomerFileCategoryEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;


@Repository
public interface CustomerFileCategoryMapper {


    List<CustomerFileCategoryEntity> allCustomerFileCategory(@Param("Name") String Name, @Param("Active") Boolean Active);

    LinkedList<Integer> getIDOrderBySequenceASC(int ID);

    Integer getMaxSequence();

    void createCustomerFileCategory(CustomerFileCategoryEntity customerFileCategoryEntity);

    void updateCustomerFileCategory(CustomerFileCategoryEntity customerFileCategoryEntity);

    void updateSequence(List<CustomerFileCategoryEntity> list);

}
