package com.zhelian.mapper.CRM;

import com.zhelian.model.CRM.entity.CustomerPriceGeneralCardCategoryEntity;
import com.zhelian.model.CRM.entity.CustomerPriceGeneralCardEntity;
import com.zhelian.model.CRM.entity.CustomerPriceGeneralCardSchemeEntity;
import com.zhelian.model.CRM.form.output.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CustomerLevelGeneralCardDiscountConfigMapper {

    List<CustomerLevelGeneralCardCategoryDiscountOutputForm> customerLevelDiscountAll();

    void deleteCustomerLevelDiscountConfig();

    void createCustomerLevelDiscountConfig(List<CustomerPriceGeneralCardSchemeEntity> list);

    List<CustomerLevelGeneralCardCategoryDiscountConfigOutputForm> customerLevelCategoryDiscountAll(@Param("Name") String Name);

    List<CustomerLevelDiscountPriceGeneralCardCategoryOutputForm> category();

    List<Integer> selectedCategory();

    void deleteCustomerLevelCategoryDiscountConfigByGeneralCardCategoryID(@Param("GeneralCardCategoryID") Integer GeneralCardCategoryID);

    void createCustomerLevelCategoryDiscountConfig(List<CustomerPriceGeneralCardCategoryEntity> list);

    List<CustomerLevelDiscountPriceGeneralCardAllOutputForm> generalCard(@Param("Name") String Name);

    List<Integer> selectedGeneralCard();

    List<CustomerLevelDiscountPriceGeneralCardConfigAllOutputForm> customerLevelGeneralCardDiscountPriceAll(@Param("Name") String Name);

    void createCustomerLevelGeneralCardDiscountPriceConfig(List<CustomerPriceGeneralCardEntity> list);

    void deleteCustomerLevelCategoryDiscountConfigByGeneralCardID(@Param("GeneralCardID") Integer GeneralCardID);
}
