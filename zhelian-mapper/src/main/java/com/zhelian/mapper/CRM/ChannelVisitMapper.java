package com.zhelian.mapper.CRM;

import com.zhelian.model.CRM.entity.*;
import com.zhelian.model.CRM.form.output.ChannelVisitChannelOutputForm;
import com.zhelian.model.CRM.form.output.ChannelVisitDetailOutputForm;
import com.zhelian.model.CRM.form.output.ChannelVisitOutputForm;
import com.zhelian.model.miniprogram.form.output.ChannelVisitReportOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface ChannelVisitMapper {

    void createChannelVisit(ChannelVisitEntity entity);

    ChannelVisitEntity getChannelVisit(Integer ID);

    void createChannelVisitAssistor(List<ChannelVisitAssistorEntity> list);

    List<ChannelVisitOutputForm> channelVisitList(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("VisitType") String VisitType, @Param("Assistor") String Assistor, @Param("VisitStatus") String VisitStatus, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate);

    List<ChannelVisitOutputForm> subordinatesList(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("VisitType") String VisitType, @Param("Assistor") String Assistor, @Param("VisitStatus") String VisitStatus, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate);

    void createChannelVisitRecordAttachment(List<ChannelVisitRecordAttachmentEntity> attachment);

    void createChannelVisitSignInAttachment(List<ChannelVisitSignInAttachmentEntity> attachment);

    void createChannelVisitSignOutAttachment(List<ChannelVisitSignOutAttachmentEntity> attachment);

    void updateChannelVisit(ChannelVisitEntity channelVisit);

    ChannelVisitDetailOutputForm channelVisitDetail(Integer ID);

    String getRecentVisitDate(Integer ID);

    List<ChannelVisitChannelOutputForm> getChannelVisitChannel(String Name);

    List<ChannelVisitReportOutForm> report(@Param("EmployeeID") String EmployeeID, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("Name") String Name);
}
