package com.zhelian.mapper.CRM;

import com.zhelian.model.CRM.entity.FollowUpStatusEntity;
import com.zhelian.model.ibeauty.form.output.PayMethodOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;


@Repository
public interface FollowUpStatusMapper {

    List<PayMethodOutputForm> followUpStatusList(@Param("Name") String Name, @Param("Active") Boolean Active);

    LinkedList<Integer> getIDOrderBySequenceASC(int ID);

    Integer getMaxSequence();

    void createFollowUpStatus(FollowUpStatusEntity followUpStatusEntity);

    void updateFollowUpStatus(FollowUpStatusEntity followUpStatusEntity);

    void updateSequence(List<FollowUpStatusEntity> list);


}
