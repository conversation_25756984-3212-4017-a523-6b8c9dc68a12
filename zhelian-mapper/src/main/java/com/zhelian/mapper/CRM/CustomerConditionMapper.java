package com.zhelian.mapper.CRM;

import com.zhelian.model.ibeauty.entity.CustomerConditionEntity;
import com.zhelian.model.ibeauty.entity.CustomerConditionGoodsEntity;
import com.zhelian.model.ibeauty.entity.CustomerConditionGoodsTypeEntity;
import com.zhelian.model.ibeauty.form.output.CustomerConditionGoodsOutputForm;
import com.zhelian.model.ibeauty.form.output.CustomerConditionOutputForm;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface CustomerConditionMapper {


    CustomerConditionOutputForm allCustomerCondition();

    String[] allCustomerConditionGoodsType();

    List<CustomerConditionGoodsOutputForm> allCustomerConditionGoods();

    void createCustomerCondition(CustomerConditionEntity customerConditionEntity);

    CustomerConditionEntity getCustomerCondition();

    void updateCustomerCondition(CustomerConditionEntity customerConditionEntity);

    void deleteCustomerConditionGoodsTypeEntity();

    void deleteCustomerConditionGoodsEntity();

    void createCustomerConditionGoodsType(List<CustomerConditionGoodsTypeEntity> list);

    void createCustomerConditionGoods(List<CustomerConditionGoodsEntity> list);

}
