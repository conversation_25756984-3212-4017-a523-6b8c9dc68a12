package com.zhelian.mapper.CRM;

import com.zhelian.model.CRM.entity.ArtSpeakCategoryEntity;
import com.zhelian.model.ibeauty.form.output.TopProjectCategoryOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;

@Repository
public interface ArtSpeakCategoryMapper {

    List<ArtSpeakCategoryEntity> artSpeakCategoryList(@Param("Name") String Name, @Param("Active") Boolean Active);

    List<ArtSpeakCategoryEntity> getValidArtSpeakCategoryValid();

    List<TopProjectCategoryOutputForm> getTopArtSpeakList();

    void addArtSpeakCategory(ArtSpeakCategoryEntity form);

    void updateArtSpeakCategory(ArtSpeakCategoryEntity form);

    LinkedList<Integer> getASCList(@Param("ID") int ID, @Param("ParentID") int ParentID);

    void updateSequence(List<ArtSpeakCategoryEntity> list);

    Integer getMaxSequence();

    Integer artSpeakByCategoryCount(Integer ID);

    ArtSpeakCategoryEntity getArtSpeakCategoryEntity(Integer ID);

    Integer childActiveCount(@Param("ID") Integer ID);

}
