package com.zhelian.mapper.CRM;

import com.zhelian.model.CRM.entity.*;
import com.zhelian.model.CRM.form.input.ChannelEmployeeAddForm;
import com.zhelian.model.CRM.form.input.ChannelParentAddForm;
import com.zhelian.model.CRM.form.input.ChannelQueryForm;
import com.zhelian.model.CRM.form.output.*;
import com.zhelian.model.ibeauty.form.output.ChannelApprovalOutputForm;
import com.zhelian.model.miniprogram.form.output.ChannelDataOutputForm;
import com.zhelian.model.miniprogram.form.output.ChannelDetailOutputExtendForm;
import com.zhelian.model.miniprogram.form.output.ChannelDetailOutputForm;
import com.zhelian.model.miniprogram.form.output.EmployeeOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ChannelMapper {

    List<ChannelOutputForm> getPageList(ChannelQueryForm form);

    List<ChannelOutputExcelForm> excel(ChannelQueryForm form);

    void create(ChannelEntity entity);

    void createIntroducer(IntroducerEntity entity);

    void updateChannel(ChannelEntity entity);

    Integer channelSequenceByParentID(Integer ParentID);

    void deleteIntroducerByID(@Param("ID") Integer ID);

    void deleteContractByID(@Param("ID") Integer ID);

    void deleteContractAttachmentByID(@Param("ID") Integer ID);

    void deleteCompanyByID(@Param("ID") Integer ID);

    ChannelEntity channelInfo(Integer ID);

    List<ChannelEntity> channelListByParentID(@Param("ParentID") Integer ParentID, @Param("ID") Integer ID, @Param("Active") Boolean Active);

    void updateChannelSequence(List<ChannelEntity> list);

    List<ChannelEntity> channelList(ChannelQueryForm form);

    void updateChannelParent(List<ChannelParentAddForm> list);

    void updateChannelEmployee(List<ChannelEmployeeAddForm> list);

    List<EmployeeOutputForm> empList();

    void insertIntroducer(List<IntroducerEntity> list);

    List<ChannelEntity> channerlListByParentID(Integer ID);


    List<ChannelParentOutputForm> getChannelParents(Integer ID);

    List<Integer> getIntroducer(Integer channelID);

    List<ChannelInfoOutputForm> all(@Param("Name") String Name,@Param("Active") Boolean Active);

    List<ChannelInfoOutputForm> customerInfo(@Param("Name") String Name,@Param("CustomerID") Integer CustomerID);

    List<ChannelDataOutputForm> miniSelfList(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("ApprovalStatus") String ApprovalStatus, @Param("ChannelTypeID") Integer ChannelTypeID, @Param("ChannelLevelID") Integer ChannelLevelID, @Param("ParentID") Integer ParentID, @Param("DeveloperID") Integer DeveloperID, @Param("ConsultantID") Integer ConsultantID);

    List<ChannelDataOutputForm> miniAllList(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("ApprovalStatus") String ApprovalStatus, @Param("BehalfEmployeeID") String BehalfEmployeeID, @Param("EntityID") Integer EntityID, @Param("IsShow") Boolean IsShow, @Param("ChannelTypeID") Integer ChannelTypeID, @Param("ChannelLevelID") Integer ChannelLevelID, @Param("ParentID") Integer ParentID, @Param("DeveloperID") Integer DeveloperID, @Param("ConsultantID") Integer ConsultantID);

    List<ChannelApprovalOutputForm> approvalList(@Param("ChannelTypeID") Integer ChannelTypeID, @Param("ChannelLevelID") Integer ChannelLevelID, @Param("BehalfEmployeeID") String BehalfEmployeeID, @Param("ParentID") Integer ParentID, @Param("Name") String Name, @Param("ApprovalStatus") String ApprovalStatus, @Param("DeveloperID") Integer DeveloperID, @Param("ConsultantID") Integer ConsultantID);

    ChannelDetailOutputForm detail(Integer ID);

    ChannelDetailOutputExtendForm approvalDetail(Integer ID);

    void createChannelApprovalProcess(ChannelApprovalProcessEntity channelApprovalProcessEntity);

    ChannelEntity getChannelEntity(Integer ID);

    void createChannelContract(ChannelContractEntity channelContractEntity);

    void insertChannelCompany(List<ChannelCompanyEntity> list);

    void insertChannelContractAttachment(List<ChannelContractAttachmentEntity> list);

    void insertChannelDeveloper(List<ChannelDeveloperEntity> list);

    void insertChannelConsultant(List<ChannelConsultantEntity> list);

    List<ChannelInfoOutputForm> currentChannel(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("IsShow") Boolean IsShow,@Param("Active") Boolean Active);

    List<ChannelEmployeePerformanceRateOutputForm> channelDeveloper(Integer ChannelID);

    List<ChannelEmployeePerformanceRateOutputForm> channelConsultant(Integer ChannelID);

    void deleteDeveloperByID(@Param("ID") Integer ID);

    void deleteConsultantByID(@Param("ID") Integer ID);

    void deleteBatchConsultant(Integer[] array);

    void deleteBatchDeveloper(Integer[] array);

    List<ChannelDeveloperEntity> getChannelDeveloperByEmployeeID(@Param("EmployeeID") String EmployeeID);

    void deleteChannelDeveloperByEmployeeIDAndChannelID(@Param("EmployeeID") String EmployeeID, @Param("ChannelID") Integer ChannelID);

    void deleteChannelDeveloperByEmployeeID(@Param("EmployeeID") String EmployeeID);

    void insertTransferChannelDeveloper(List<ChannelDeveloperEntity> list);

    List<ChannelConsultantEntity> getChannelConsultantByEmployeeID(@Param("EmployeeID") String EmployeeID);

    void deleteChannelConsultantByEmployeeIDAndChannelID(@Param("EmployeeID") String EmployeeID, @Param("ChannelID") Integer ChannelID);

    void deleteChannelConsultantByEmployeeID(@Param("EmployeeID") String EmployeeID);

    void insertTransferChannelConsultant(List<ChannelConsultantEntity> list);
}
