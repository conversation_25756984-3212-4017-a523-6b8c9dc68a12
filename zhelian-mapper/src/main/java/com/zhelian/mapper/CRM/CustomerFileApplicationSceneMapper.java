package com.zhelian.mapper.CRM;

import com.zhelian.model.CRM.form.output.CustomerFileApplicationSceneOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CustomerFileApplicationSceneMapper {


    List<CustomerFileApplicationSceneOutputForm> customerFileApplicationScene();

    void updateStatus(@Param("Code") String Code, @Param("IsRequired") Boolean IsRequired);

    CustomerFileApplicationSceneOutputForm getCustomerFileApplicationScene(String Code);
}
