package com.zhelian.mapper.CRM;

import com.zhelian.model.ibeauty.entity.CustomerSourceEntity;
import com.zhelian.model.ibeauty.form.output.CustomerSourceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;


@Repository
public interface CustomerSourceMapper {


    List<CustomerSourceOutputForm> allCustomerSource(@Param("Name") String Name, @Param("Active") Boolean Active);

    Integer getMaxSequence();

    void createCustomerSource(CustomerSourceEntity CustomerSourceEntity);

    void updateCustomerSource(CustomerSourceEntity CustomerSourceEntity);

    LinkedList<Integer> getASCList(@Param("ID") int ID, @Param("ParentID") int ParentID);

    void updateSequence(List<CustomerSourceEntity> list);

    CustomerSourceEntity getCustomerSourceEntity(Integer moveID);

    Integer childActiveCount(Integer ID);

    Integer customerByCustomerSourceCount(Integer ID);

    List<CustomerSourceOutputForm> getCustomerSource();
}
