package com.zhelian.mapper.CRM;


import com.zhelian.model.ibeauty.entity.CustomerBasicFileEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;


@Repository
public interface CustomerBasicFileMapper {


    void createCustomerBasicFile(CustomerBasicFileEntity customerBasicFileEntity);

    Integer getMaxSequence();

    void updateCustomerBasicFile(CustomerBasicFileEntity customerBasicFileEntity);

    void deleteCustomerBasicFile(@Param("ID") Integer ID);

    LinkedList<Integer> getIDOrderBySequenceASC(int ID);

    void updateSequence(List<CustomerBasicFileEntity> list);

    List<CustomerBasicFileEntity> allCustomerBasicFile(@Param("Name") String Name, @Param("Type") Integer Type);

    void deleteCustomerBasicFileInfo(@Param("ID") Integer ID);
}
