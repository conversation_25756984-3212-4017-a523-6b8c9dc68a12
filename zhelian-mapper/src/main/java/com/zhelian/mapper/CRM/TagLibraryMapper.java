package com.zhelian.mapper.CRM;


import com.zhelian.model.ibeauty.entity.TagLibraryEntity;
import com.zhelian.model.ibeauty.form.output.TagLibraryOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TagLibraryMapper {

    List<TagLibraryOutputForm> allTagLibrary(@Param("Name") String Name);

    Integer countTagLibrary(@Param("Name") String Name);

    Integer countExceptByID(@Param("Name") String Name, @Param("ID") Integer ID);

    void createTagLibrary(TagLibraryEntity tagLibraryEntity);

    void updateTagLibrary(TagLibraryEntity tagLibraryEntity);

    void deleteTagLibrary(@Param("ID") Integer ID);

    void deleteCustomerTag(@Param("ID") Integer ID);

    void deleteNursingLogTag(@Param("ID") Integer ID);

    void deleteProjectLogTag(@Param("ID") Integer ID);

    List<TagLibraryEntity> allTagLibraryByName(@Param("Name") String Name);

    TagLibraryEntity getTagLibraryByLabel(@Param("Name") String Name);
}
