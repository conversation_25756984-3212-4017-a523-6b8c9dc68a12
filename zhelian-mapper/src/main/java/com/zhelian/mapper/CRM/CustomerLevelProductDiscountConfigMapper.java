package com.zhelian.mapper.CRM;

import com.zhelian.model.CRM.entity.CustomerPriceProductCategoryEntity;
import com.zhelian.model.CRM.entity.CustomerPriceProductEntity;
import com.zhelian.model.CRM.entity.CustomerPriceProductSchemeEntity;
import com.zhelian.model.CRM.form.output.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CustomerLevelProductDiscountConfigMapper {

    List<CustomerLevelProductCategoryDiscountOutputForm> customerLevelDiscountAll();

    void deleteCustomerLevelDiscountConfig();

    void createCustomerLevelDiscountConfig(List<CustomerPriceProductSchemeEntity> list);

    List<CustomerLevelProductCategoryDiscountConfigOutputForm> customerLevelCategoryDiscountAll(@Param("Name") String Name);

    List<CustomerLevelDiscountPriceGoodsCategoryOutputForm> category();

    List<Integer> selectedCategory();

    void deleteCustomerLevelCategoryDiscountConfigByProductCategoryID(@Param("ProductCategoryID") Integer ProductCategoryID);

    void createCustomerLevelCategoryDiscountConfig(List<CustomerPriceProductCategoryEntity> list);

    List<CustomerLevelDiscountPriceProductAllOutputForm> product(@Param("Name") String Name);

    List<Integer> selectedProduct();
    
    List<CustomerLevelDiscountPriceProductConfigAllOutputForm> customerLevelProductDiscountPriceAll(@Param("Name") String Name);

    void createCustomerLevelProductDiscountPriceConfig(List<CustomerPriceProductEntity> list);

    void deleteCustomerLevelCategoryDiscountConfigByProductID(@Param("ProductID") Integer ProductID);
}
