package com.zhelian.mapper.CRM;

import com.zhelian.model.ibeauty.entity.CustomerEntity;
import com.zhelian.model.ibeauty.entity.CustomerLevelEntity;
import com.zhelian.model.ibeauty.entity.CustomerLevelValidityEntity;
import com.zhelian.model.ibeauty.form.output.CustomerLevelOutputForm;
import com.zhelian.model.ibeauty.form.output.MemberCustomerOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface CustomerLevelMapper {


    List<CustomerLevelOutputForm> allCustomerLevel();

    void createCustomerLevel(CustomerLevelEntity customerLevelEntity);

    void updateCustomerLevel(CustomerLevelEntity customerLevelEntity);

    //获取用户等级
    List<CustomerLevelEntity> getAllLevel(CustomerLevelEntity form);

    CustomerLevelEntity getCustomerLevel(Integer ID);

    void deleteCustomerLevel(Integer ID);

    Integer getCustomerLevelByArray(List<Integer> list);

    void insertCustomerLevelValidity(CustomerLevelValidityEntity customerLevelValidityEntity);

    void updateCustomerLevelValidity(CustomerLevelValidityEntity customerLevelValidityEntity);

    CustomerLevelValidityEntity getCustomerLevelValidity(int CustomerID);

    void deleteCustomerLevelValidity(int CustomerID);

    void deleteAllCustomerLevelValidity();

    //升一级
    CustomerLevelEntity getUpOneLevelByID(Integer ID);

    //降一级
    CustomerLevelEntity getDownOneLevelByID(Integer ID);

    void updateCustomerLevelSetting(@Param("Status") Boolean Status);

    List<MemberCustomerOutputForm> getCustomerByValidityMode();

    void deleteCustomerValid(List<Integer> list);

    void insertCustomerValid(List<CustomerLevelValidityEntity> list);
}
