package com.zhelian.mapper.CRM;

import com.zhelian.model.ibeauty.entity.CustomerConsultantJobTypeEntity;
import com.zhelian.model.ibeauty.form.output.CustomerConsultantJobTypeOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CustomerConsultantJobTypeMapper {
    List<CustomerConsultantJobTypeOutputForm> allCustomerConsultantJobType();

    void updateCustomerConsultantJobTypeSale(List<CustomerConsultantJobTypeEntity> list);

    void deleteCustomerConsultantJobTypeSale(@Param("JobTypeID") Integer JobTypeID);

    void deleteAllCustomerConsultantJobTypeSale();
}
