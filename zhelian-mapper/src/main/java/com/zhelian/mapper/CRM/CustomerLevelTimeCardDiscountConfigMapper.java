package com.zhelian.mapper.CRM;

import com.zhelian.model.CRM.entity.CustomerPriceTimeCardCategoryEntity;
import com.zhelian.model.CRM.entity.CustomerPriceTimeCardEntity;
import com.zhelian.model.CRM.entity.CustomerPriceTimeCardSchemeEntity;
import com.zhelian.model.CRM.form.output.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CustomerLevelTimeCardDiscountConfigMapper {

    List<CustomerLevelTimeCardCategoryDiscountOutputForm> customerLevelDiscountAll();

    void deleteCustomerLevelDiscountConfig();

    void createCustomerLevelDiscountConfig(List<CustomerPriceTimeCardSchemeEntity> list);

    List<CustomerLevelTimeCardCategoryDiscountConfigOutputForm> customerLevelCategoryDiscountAll(@Param("Name") String Name);

    List<CustomerLevelDiscountPriceTimeCardCategoryOutputForm> category();

    List<Integer> selectedCategory();

    void deleteCustomerLevelCategoryDiscountConfigByTimeCardCategoryID(@Param("TimeCardCategoryID") Integer TimeCardCategoryID);
    
    void createCustomerLevelCategoryDiscountConfig(List<CustomerPriceTimeCardCategoryEntity> list);

    List<CustomerLevelDiscountPriceTimeCardAllOutputForm> timeCard(@Param("Name") String Name);

    List<Integer> selectedTimeCard();

    List<CustomerLevelDiscountPriceTimeCardConfigAllOutputForm> customerLevelTimeCardDiscountPriceAll(@Param("Name") String Name);

    void createCustomerLevelTimeCardDiscountPriceConfig(List<CustomerPriceTimeCardEntity> list);

    void deleteCustomerLevelCategoryDiscountConfigByTimeCardID(@Param("TimeCardID") Integer TimeCardID);
}
