package com.zhelian.mapper.CRM;

import com.zhelian.model.CRM.entity.CustomerPriceProjectCategoryEntity;
import com.zhelian.model.CRM.entity.CustomerPriceProjectEntity;
import com.zhelian.model.CRM.entity.CustomerPriceProjectSchemeEntity;
import com.zhelian.model.CRM.form.output.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CustomerLevelProjectDiscountConfigMapper {

    List<CustomerLevelProjectCategoryDiscountOutputForm> customerLevelDiscountAll();

    void deleteCustomerLevelDiscountConfig();

    void createCustomerLevelDiscountConfig(List<CustomerPriceProjectSchemeEntity> list);

    List<CustomerLevelProjectCategoryDiscountConfigOutputForm> customerLevelCategoryDiscountAll(@Param("Name") String Name);

    List<CustomerLevelDiscountPriceGoodsCategoryOutputForm> category();

    List<Integer> selectedCategory();

    void deleteCustomerLevelCategoryDiscountConfigByProjectCategoryID(@Param("ProjectCategoryID") Integer ProjectCategoryID);

    void createCustomerLevelCategoryDiscountConfig(List<CustomerPriceProjectCategoryEntity> list);

    List<CustomerLevelDiscountPriceProjectAllOutputForm> project(@Param("Name") String Name);

    List<Integer> selectedProject();
    
    List<CustomerLevelDiscountPriceProjectConfigAllOutputForm> customerLevelProjectDiscountPriceAll(@Param("Name") String Name);

    void createCustomerLevelProjectDiscountPriceConfig(List<CustomerPriceProjectEntity> list);

    void deleteCustomerLevelCategoryDiscountConfigByProjectID(@Param("ProjectID") Integer ProjectID);
}
