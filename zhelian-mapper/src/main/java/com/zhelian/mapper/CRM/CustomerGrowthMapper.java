package com.zhelian.mapper.CRM;

import com.zhelian.model.ibeauty.entity.CustomerGrowthConfigEntity;
import com.zhelian.model.ibeauty.entity.CustomerGrowthEntity;
import com.zhelian.model.ibeauty.entity.CustomerLevelEntity;
import com.zhelian.model.ibeauty.entity.CustomerLevelSettingEntity;
import com.zhelian.model.ibeauty.form.output.CustomerGrowthOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;


@Repository
public interface CustomerGrowthMapper {


    List<CustomerGrowthOutputForm> allCustomerGrowth();


    void updateCustomerGrowth(List<CustomerGrowthConfigEntity> list);

    Boolean getCustomerGrowthSetting();


    void insertCustomerGrowth(CustomerGrowthEntity customerGrowthEntity);

    void createCustomerGrowth(List<CustomerGrowthEntity> list);

    BigDecimal getCustomerGrowth(@Param("CustomerID") int CustomerID);

    Integer getCustomerLevelByGrowthTotal(BigDecimal customerGrowthTotal);

    CustomerLevelEntity getCustomerLevelEntityByGrowthTotal(BigDecimal customerGrowthTotal);

    CustomerLevelSettingEntity status();

    void updateStatus(Boolean Status);

    BigDecimal getCustomerLevelByValidityOn(@Param("CustomerID") int CustomerID, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate);


    BigDecimal getCustomerLevelByValidity(@Param("CustomerID") int CustomerID, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate);

}
