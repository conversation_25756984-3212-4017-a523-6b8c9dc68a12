package com.zhelian.mapper.CRM;

import com.zhelian.model.CRM.entity.FollowUpMethodEntity;
import com.zhelian.model.ibeauty.form.output.PayMethodOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;


@Repository
public interface FollowUpMethodMapper {

    List<PayMethodOutputForm> payMethodList(@Param("Name") String Name, @Param("Active") Boolean Active);

    LinkedList<Integer> getIDOrderBySequenceASC(int ID);

    Integer getMaxSequence();

    void createPayMethod(FollowUpMethodEntity payMethodEntity);

    void updatePayMethod(FollowUpMethodEntity payMethodEntity);

    void updateSequence(List<FollowUpMethodEntity> list);


}
