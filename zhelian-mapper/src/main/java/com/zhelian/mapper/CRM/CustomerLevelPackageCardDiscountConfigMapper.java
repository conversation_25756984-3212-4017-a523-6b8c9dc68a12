package com.zhelian.mapper.CRM;

import com.zhelian.model.CRM.entity.CustomerPricePackageCardCategoryEntity;
import com.zhelian.model.CRM.entity.CustomerPricePackageCardEntity;
import com.zhelian.model.CRM.entity.CustomerPricePackageCardSchemeEntity;
import com.zhelian.model.CRM.form.output.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CustomerLevelPackageCardDiscountConfigMapper {

    List<CustomerLevelPackageCardCategoryDiscountOutputForm> customerLevelDiscountAll();

    void deleteCustomerLevelDiscountConfig();

    void createCustomerLevelDiscountConfig(List<CustomerPricePackageCardSchemeEntity> list);

    List<CustomerLevelPackageCardCategoryDiscountConfigOutputForm> customerLevelCategoryDiscountAll(@Param("Name") String Name);

    List<CustomerLevelDiscountPricePackageCardCategoryOutputForm> category();

    List<Integer> selectedCategory();
    
    void deleteCustomerLevelCategoryDiscountConfigByPackageCardCategoryID(@Param("PackageCardCategoryID") Integer PackageCardCategoryID);

    void createCustomerLevelCategoryDiscountConfig(List<CustomerPricePackageCardCategoryEntity> list);

    List<CustomerLevelDiscountPricePackageCardAllOutputForm> packageCard(@Param("Name") String Name);

    List<Integer> selectedPackageCard();

    List<CustomerLevelDiscountPricePackageCardConfigAllOutputForm> customerLevelPackageCardDiscountPriceAll(@Param("Name") String Name);

    void createCustomerLevelPackageCardDiscountPriceConfig(List<CustomerPricePackageCardEntity> list);

    void deleteCustomerLevelCategoryDiscountConfigByPackageCardID(@Param("PackageCardID") Integer PackageCardID);
}
