package com.zhelian.mapper.CRM;

import com.zhelian.model.CRM.entity.FollowUpRuleEntity;
import com.zhelian.model.CRM.entity.FollowUpRuleEntityEntity;
import com.zhelian.model.CRM.entity.FollowUpRuleFilterConditionEntity;
import com.zhelian.model.CRM.form.input.FollowUpConditionQueryForm;
import com.zhelian.model.CRM.form.output.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;


@Repository
public interface FollowUpRuleMapper {

    List<FollowUpRuleOutForm> followUpRuleList(@Param("Name") String Name, @Param("Active") Boolean Active);

    FollowUpRuleEntity getCreateFollowUpRule(Integer ID);

    void createFollowUpRule(FollowUpRuleEntity followUpRuleEntity);

    void updateFollowUpRule(FollowUpRuleEntity followUpRuleEntity);

    void createFollowUpRuleEntity(List<FollowUpRuleEntityEntity> list);

    void createFollowUpRuleCondition(List<FollowUpRuleFilterConditionEntity> list);

    List<FollowUpRuleAllConditionOutForm> getCondition();

    void deleteFollowUpRuleEntity(Integer ID);

    void deleteFollowUpRuleCondition(Integer ID);

    FollowUpRuleDetailOutputForm detail(Integer ID);

    List<FollowUpRuleConditionDetailOutputForm> getConditionDetail(Integer ID);

    List<Integer> getEntityDetail(Integer ID);

    List<FollowUpRuleDetailOutputExtendForm> allFollowUpRule();

    List<Integer> getCustomerByFollowUpCondition(FollowUpConditionQueryForm form);

    void updateFollowUpRuleLastExecutionDate(@Param("ID") Integer ID, @Param("LastExecutionDate") Date LastExecutionDate);

    Integer getFollowUpRuleInfo();
}
