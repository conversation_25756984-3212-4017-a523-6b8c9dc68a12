package com.zhelian.mapper.CRM;

import com.zhelian.model.CRM.entity.CustomerMedicalRecordDetailEntity;
import com.zhelian.model.CRM.entity.CustomerMedicalRecordEntity;
import com.zhelian.model.CRM.form.output.CustomerMedicalRecordDetailAllOutputForm;
import com.zhelian.model.CRM.form.output.CustomerMedicalRecordEmployeeOutputForm;
import com.zhelian.model.CRM.form.output.CustomerMedicalRecordOutputForm;
import com.zhelian.model.CRM.form.output.CustomerMedicalRecordTemplateAllOutputForm;
import com.zhelian.model.khs.entity.DepartmentEntity;
import com.zhelian.model.khs.entity.MedicalRecordCategoryEntity;
import com.zhelian.model.khs.entity.MedicalRecordTemplateEntity;
import com.zhelian.model.miniprogram.form.output.EmployeeOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CustomerMedicalRecordMapper {

    List<CustomerMedicalRecordEmployeeOutputForm> getDoctor(@Param("EntityID")Integer EntityID);

    List<DepartmentEntity> getDepartment(@Param("EntityID")Integer EntityID);

    List<MedicalRecordCategoryEntity> getMedicalRecordCategory();

    List<CustomerMedicalRecordOutputForm> customerMedicalRecordList(@Param("CustomerID") String CustomerID);

    CustomerMedicalRecordOutputForm customerMedicalRecordByID(@Param("ID") String ID);

    void createCustomerMedicalRecordEntity(CustomerMedicalRecordEntity entity);

    CustomerMedicalRecordEntity getCustomerMedicalRecordEntity(@Param("ID") String ID);

    void updateCustomerMedicalRecordEntity(CustomerMedicalRecordEntity entity);

    void deleteCustomerMedicalRecord(@Param("ID") String ID);

    void deleteCustomerMedicalRecordDetail(@Param("ID") String ID);

    CustomerMedicalRecordDetailAllOutputForm getCustomerMedicalRecordDetail(@Param("ID") String ID,@Param("CategoryID") Integer CategoryID);

    void insertCustomerMedicalRecordDetail(List<CustomerMedicalRecordDetailEntity> list);

    CustomerMedicalRecordDetailEntity getCustomerMedicalRecordDetailEntity(@Param("ID") Integer ID);

    void updateCustomerMedicalRecordDetailEntity(CustomerMedicalRecordDetailEntity entity);

    List<CustomerMedicalRecordTemplateAllOutputForm> getMedicalRecordTemplate(@Param("CategoryID") Integer CategoryID);

    MedicalRecordTemplateEntity getMedicalRecordTemplateDetail(@Param("ID") Integer ID);

    void deleteCustomerMedicalRecordDetailEntity(@Param("ID") Integer ID);
}
