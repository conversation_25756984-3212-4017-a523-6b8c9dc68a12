package com.zhelian.mapper.CRM;

import com.zhelian.model.CRM.entity.ArtSpeakEntity;
import com.zhelian.model.CRM.form.input.ArtSpeakAddOrUpdateForm;
import com.zhelian.model.CRM.form.output.ArtSpeakCategoryChildOutputForm;
import com.zhelian.model.CRM.form.output.ArtSpeakCategoryOutputForm;
import com.zhelian.model.CRM.form.output.ArtSpeakOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ArtSpeakMapper {


    List<ArtSpeakOutputForm> artSpeakList(@Param("Name") String Name, @Param("ArtSpeakCategoryID") Integer ArtSpeakCategoryID);

    void insertArtSpeak(ArtSpeakEntity artSpeakEntity);

    void updateArtSpeak(ArtSpeakEntity artSpeakEntity);

    int getArtSpeakName(ArtSpeakAddOrUpdateForm artSpeakAddOrUpdateForm);

    List<ArtSpeakCategoryChildOutputForm> getArtSpeakParentList(@Param("Name") String Name);

    List<ArtSpeakCategoryOutputForm> getTopArtSpeakList(List<Integer> list);

    void deleteArtSpeakByID(@Param("ID") Integer ID);
}
