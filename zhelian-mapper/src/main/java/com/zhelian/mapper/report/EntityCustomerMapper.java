package com.zhelian.mapper.report;


import com.zhelian.model.report.form.output.EntityCustomerDetailOutputExcelForm;
import com.zhelian.model.report.form.output.EntityCustomerDetailOutputForm;
import com.zhelian.model.report.form.output.EntityCustomerLevelDetailOutputForm;
import com.zhelian.model.report.form.output.EntityCustomerSumOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface EntityCustomerMapper {

    EntityCustomerSumOutputForm getEntityCustomerStatementSum(@Param("EmployeeID") String EmployeeID, @Param("EntityID") Integer EntityID);

    List<EntityCustomerDetailOutputForm> getEntityCustomerStatement(@Param("EmployeeID") String EmployeeID, @Param("EntityID") Integer EntityID);

    List<EntityCustomerLevelDetailOutputForm> getEntityCustomerStatementSumLevel(@Param("EmployeeID") String EmployeeID, @Param("EntityID") Integer EntityID);

    List<EntityCustomerDetailOutputExcelForm> getEntityCustomerStatementExcel(@Param("EmployeeID") String EmployeeID, @Param("EntityID") Integer EntityID);

}
