package com.zhelian.mapper.report;

import com.zhelian.model.report.form.output.EmployeeSaleAndTreatDetailStatementExcelForm;
import com.zhelian.model.report.form.output.EmployeeSaleAndTreatDetailStatementForm;
import com.zhelian.model.report.form.output.EmployeeSaleAndTreatDetailSumStatementForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EmployeeSaleAndTreatDetailMapper {

    List<EmployeeSaleAndTreatDetailStatementForm> getEmployeeSaleAndTreatDetail(@Param("EmployeeID") String EmployeeID, @Param("EntityID") Integer EntityID, @Param("BuyEntityID") Integer BuyEntityID, @Param("Name") String Name, @Param("StartTime") String StartTime, @Param("EndTime") String EndTime, @Param("JobID") Integer JobID);

    EmployeeSaleAndTreatDetailSumStatementForm getEmployeeSaleAndTreatDetailSum(@Param("EmployeeID") String EmployeeID, @Param("EntityID") Integer EntityID, @Param("BuyEntityID") Integer BuyEntityID, @Param("Name") String Name, @Param("StartTime") String StartTime, @Param("EndTime") String EndTime, @Param("JobID") Integer JobID);

    List<EmployeeSaleAndTreatDetailStatementExcelForm> getEmployeeSaleAndTreatDetailExcel(@Param("EmployeeID") String EmployeeID, @Param("EntityID") Integer EntityID, @Param("BuyEntityID") Integer BuyEntityID, @Param("Name") String Name, @Param("StartTime") String StartTime, @Param("EndTime") String EndTime, @Param("JobID") Integer JobID);

}
