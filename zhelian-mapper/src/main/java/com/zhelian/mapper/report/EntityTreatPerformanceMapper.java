package com.zhelian.mapper.report;

import com.zhelian.model.report.form.output.EntityTreatPerformanceDetailOutputEasyExcelForm;
import com.zhelian.model.report.form.output.EntityTreatPerformanceDetailOutputForm;
import com.zhelian.model.report.form.output.EntityTreatPerformanceSumOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EntityTreatPerformanceMapper {
    List<EntityTreatPerformanceDetailOutputForm> getEntityTreatPerformance(@Param("EmployeeID") String EmployeeID, @Param("EntityID") Integer EntityID, @Param("TreatCardTypeName") String TreatCardTypeName, @Param("CustomerName") String CustomerName, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("IsLargess") Boolean IsLargess, @Param("BillType") String BillType, @Param("CategoryID") Integer CategoryID, @Param("GoodsTypeName") String GoodsTypeName, @Param("BillID") String BillID, @Param("CustomerLevelID") Integer CustomerLevelID);

    EntityTreatPerformanceSumOutputForm getEntityTreatPerformanceSum(@Param("EmployeeID") String EmployeeID, @Param("EntityID") Integer EntityID, @Param("TreatCardTypeName") String TreatCardTypeName, @Param("CustomerName") String CustomerName, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("IsLargess") Boolean IsLargess, @Param("BillType") String BillType, @Param("CategoryID") Integer CategoryID, @Param("GoodsTypeName") String GoodsTypeName, @Param("BillID") String BillID, @Param("CustomerLevelID") Integer CustomerLevelID);

    List<EntityTreatPerformanceDetailOutputEasyExcelForm> getEntityTreatPerformanceEasyExcel(@Param("EmployeeID") String EmployeeID, @Param("EntityID") Integer EntityID, @Param("TreatCardTypeName") String TreatCardTypeName, @Param("CustomerName") String CustomerName, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("IsLargess") Boolean IsLargess, @Param("BillType") String BillType, @Param("CategoryID") Integer CategoryID, @Param("GoodsTypeName") String GoodsTypeName,  @Param("BillID") String BillID, @Param("CustomerLevelID") Integer CustomerLevelID);

}
