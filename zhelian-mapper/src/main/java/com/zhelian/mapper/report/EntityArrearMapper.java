package com.zhelian.mapper.report;

import com.zhelian.model.report.form.output.EntityArrearDetailOutputEasyExcelForm;
import com.zhelian.model.report.form.output.EntityArrearDetailOutputForm;
import com.zhelian.model.report.form.output.EntityArrearSumOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EntityArrearMapper {
    List<EntityArrearDetailOutputForm> getEntityArrearStatement(@Param("EmployeeID") String EmployeeID, @Param("EntityID") Integer EntityID);

    List<Object> getEntityArrearStatementExcel(@Param("EmployeeID") String EmployeeID, @Param("EntityID") Integer EntityID);

    EntityArrearSumOutputForm getEntityArrearStatementSum(@Param("EmployeeID") String EmployeeID, @Param("EntityID") Integer EntityID);

    List<EntityArrearDetailOutputEasyExcelForm> getEntityArrearStatementEasyExcel(@Param("EmployeeID") String EmployeeID, @Param("EntityID") Integer EntityID);
}
