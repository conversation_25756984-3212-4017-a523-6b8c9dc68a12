package com.zhelian.mapper.report;

import com.zhelian.model.report.form.output.ChannelSalePerformanceDetailStatementEasyExcelForm;
import com.zhelian.model.report.form.output.ChannelSalePerformanceDetailStatementForm;
import com.zhelian.model.report.form.output.ChannelSalePerformanceSumStatementForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ChannelSalePerformanceDetailMapper {
    List<ChannelSalePerformanceDetailStatementForm> getChannelSalePerformanceDetail(@Param("EntityID") Integer EntityID, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("GoodsTypeName") String GoodsTypeName, @Param("GoodsName") String GoodsName, @Param("ChannelName") String ChannelName, @Param("BillID") String BillID, @Param("ChannelTypeID") Integer ChannelTypeID,@Param("CustomerName") String CustomerName);

    ChannelSalePerformanceSumStatementForm getChannelSalePerformanceSum(@Param("EntityID") Integer EntityID, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("GoodsTypeName") String GoodsTypeName, @Param("GoodsName") String GoodsName, @Param("ChannelName") String ChannelName, @Param("BillID") String BillID, @Param("ChannelTypeID") Integer ChannelTypeID,@Param("CustomerName") String CustomerName);

    List<Object> getExcelChannelSalePerformanceDetail(@Param("EntityID") Integer EntityID, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("GoodsTypeName") String GoodsTypeName, @Param("GoodsName") String GoodsName, @Param("ChannelName") String ChannelName, @Param("BillID") String BillID, @Param("ChannelTypeID") Integer ChannelTypeID,@Param("CustomerName") String CustomerName);

    List<ChannelSalePerformanceDetailStatementEasyExcelForm> getEasyExcelChannelSalePerformanceDetail(@Param("EntityID") Integer EntityID, @Param("StartDate") String StartDate, @Param("EndDate") String EndDate, @Param("GoodsTypeName") String GoodsTypeName, @Param("GoodsName") String GoodsName, @Param("ChannelName") String ChannelName, @Param("BillID") String BillID, @Param("ChannelTypeID") Integer ChannelTypeID,@Param("CustomerName") String CustomerName);
}
