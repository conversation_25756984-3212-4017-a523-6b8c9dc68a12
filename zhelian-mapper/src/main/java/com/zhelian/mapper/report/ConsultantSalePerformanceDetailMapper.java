package com.zhelian.mapper.report;

import com.zhelian.model.report.form.input.ConsultantPerformanceDetailForm;
import com.zhelian.model.report.form.output.ConsultantSalePerformanceDetailStatementEasyExcelForm;
import com.zhelian.model.report.form.output.ConsultantSalePerformanceDetailStatementForm;
import com.zhelian.model.report.form.output.ConsultantSalePerformanceSumStatementForm;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ConsultantSalePerformanceDetailMapper {
    List<ConsultantSalePerformanceDetailStatementForm> getConsultantSalePerformanceDetail(ConsultantPerformanceDetailForm form);

    ConsultantSalePerformanceSumStatementForm getConsultantSalePerformanceSum(ConsultantPerformanceDetailForm form);

    List<Object> getExcelConsultantSalePerformanceDetail(ConsultantPerformanceDetailForm form);

    List<ConsultantSalePerformanceDetailStatementEasyExcelForm> getEasyExcelConsultantSalePerformanceDetail(ConsultantPerformanceDetailForm form);
}
