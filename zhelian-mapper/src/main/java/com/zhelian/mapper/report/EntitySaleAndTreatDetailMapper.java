package com.zhelian.mapper.report;

import com.zhelian.model.report.form.output.EntitySaleAndTreatDetailStatementForm;
import com.zhelian.model.report.form.output.EntitySaleAndTreatDetailSumStatementForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EntitySaleAndTreatDetailMapper {

    List<EntitySaleAndTreatDetailStatementForm> getEntitySaleAndTreatDetail(@Param("EmployeeID") String EmployeeID, @Param("EntityID") Integer EntityID, @Param("StartTime") String StartTime, @Param("EndTime") String EndTime);

    EntitySaleAndTreatDetailSumStatementForm getEntitySaleAndTreatDetailSum(@Param("EmployeeID") String EmployeeID, @Param("EntityID") Integer EntityID, @Param("StartTime") String StartTime, @Param("EndTime") String EndTime);

    List<Object> getEntitySaleAndTreatDetailExcel(@Param("EmployeeID") String EmployeeID, @Param("EntityID") Integer EntityID, @Param("StartTime") String StartTime, @Param("EndTime") String EndTime);

}
