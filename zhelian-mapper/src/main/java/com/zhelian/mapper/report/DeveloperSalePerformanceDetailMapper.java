package com.zhelian.mapper.report;

import com.zhelian.model.report.form.input.DeveloperPerformanceDetailForm;
import com.zhelian.model.report.form.output.DeveloperSalePerformanceDetailStatementEasyExcelForm;
import com.zhelian.model.report.form.output.DeveloperSalePerformanceDetailStatementForm;
import com.zhelian.model.report.form.output.DeveloperSalePerformanceSumStatementForm;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DeveloperSalePerformanceDetailMapper {
    List<DeveloperSalePerformanceDetailStatementForm> getDeveloperSalePerformanceDetail(DeveloperPerformanceDetailForm form);

    DeveloperSalePerformanceSumStatementForm getDeveloperSalePerformanceSum(DeveloperPerformanceDetailForm form);

    List<Object> getExcelDeveloperSalePerformanceDetail(DeveloperPerformanceDetailForm form);

    List<DeveloperSalePerformanceDetailStatementEasyExcelForm> getEasyExcelDeveloperSalePerformanceDetail(DeveloperPerformanceDetailForm form);
}
