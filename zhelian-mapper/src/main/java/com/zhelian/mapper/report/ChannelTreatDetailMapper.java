package com.zhelian.mapper.report;

import com.zhelian.model.report.form.output.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ChannelTreatDetailMapper {
    List<ChannelTreatDetailStatementForm> getChannelTreatDetail(@Param("EmployeeID") String employeeID, @Param("EntityID") Integer entityID, @Param("StartDate") String startDate, @Param("EndDate") String endDate, @Param("TreatCardTypeName") String treatCardTypeName, @Param("GoodName") String goodName, @Param("CustomerName") String CustomerName, @Param("BelongEntityID") Integer BelongEntityID, @Param("BuyEntityID") Integer BuyEntityID, @Param("CustomerLevelID") Integer CustomerLevelID, @Param("CustomerSourceID") Integer CustomerSourceID, @Param("Channel") String Channel, @Param("IntroducerName") String IntroducerName, @Param("ChannelName") String ChannelName);

    ChannelTreatDetailSumStatementForm getChannelTreatDetailSum(@Param("EmployeeID") String employeeID, @Param("EntityID") Integer entityID, @Param("StartDate") String startDate, @Param("EndDate") String endDate, @Param("TreatCardTypeName") String treatCardTypeName, @Param("GoodName") String goodName, @Param("CustomerName") String CustomerName, @Param("BelongEntityID") Integer BelongEntityID, @Param("BuyEntityID") Integer BuyEntityID, @Param("CustomerLevelID") Integer CustomerLevelID, @Param("CustomerSourceID") Integer CustomerSourceID, @Param("Channel") String Channel, @Param("IntroducerName") String IntroducerName, @Param("ChannelName") String ChannelName);

    List<ChannelTreatDetailStatementEasyExcelForm> getEasyExcelChannelTreatDetail(@Param("EmployeeID") String employeeID, @Param("EntityID") Integer entityID, @Param("StartDate") String startDate, @Param("EndDate") String endDate, @Param("TreatCardTypeName") String treatCardTypeName, @Param("GoodName") String goodName, @Param("CustomerName") String CustomerName, @Param("BelongEntityID") Integer BelongEntityID, @Param("BuyEntityID") Integer BuyEntityID, @Param("CustomerLevelID") Integer CustomerLevelID, @Param("CustomerSourceID") Integer CustomerSourceID, @Param("Channel") String Channel, @Param("IntroducerName") String IntroducerName, @Param("ChannelName") String ChannelName);

    List<ChannelTreatRefundDetailStatementForm> getChannelTreatRefundDetail(@Param("EmployeeID") String employeeID, @Param("EntityID") Integer entityID, @Param("StartDate") String startDate, @Param("EndDate") String endDate, @Param("TreatCardTypeName") String treatCardTypeName, @Param("GoodName") String goodName, @Param("CustomerName") String CustomerName, @Param("BelongEntityID") Integer BelongEntityID, @Param("BuyEntityID") Integer BuyEntityID, @Param("CustomerLevelID") Integer CustomerLevelID, @Param("CustomerSourceID") Integer CustomerSourceID, @Param("Channel") String Channel, @Param("IntroducerName") String IntroducerName, @Param("ChannelName") String ChannelName);

    ChannelTreatRefundDetailSumStatementForm getChannelTreatRefundDetailSum(@Param("EmployeeID") String employeeID, @Param("EntityID") Integer entityID, @Param("StartDate") String startDate, @Param("EndDate") String endDate, @Param("TreatCardTypeName") String treatCardTypeName, @Param("GoodName") String goodName, @Param("CustomerName") String CustomerName, @Param("BelongEntityID") Integer BelongEntityID, @Param("BuyEntityID") Integer BuyEntityID, @Param("CustomerLevelID") Integer CustomerLevelID, @Param("CustomerSourceID") Integer CustomerSourceID, @Param("Channel") String Channel, @Param("IntroducerName") String IntroducerName, @Param("ChannelName") String ChannelName);

    List<ChannelTreatRefundDetailStatementEasyExcelForm> getEasyExcelChannelTreatRefundDetail(@Param("EmployeeID") String employeeID, @Param("EntityID") Integer entityID, @Param("StartDate") String startDate, @Param("EndDate") String endDate, @Param("TreatCardTypeName") String treatCardTypeName, @Param("GoodName") String goodName, @Param("CustomerName") String CustomerName, @Param("BelongEntityID") Integer BelongEntityID, @Param("BuyEntityID") Integer BuyEntityID, @Param("CustomerLevelID") Integer CustomerLevelID, @Param("CustomerSourceID") Integer CustomerSourceID, @Param("Channel") String Channel, @Param("IntroducerName") String IntroducerName, @Param("ChannelName") String ChannelName);

}
