package com.zhelian.mapper.report;

import com.zhelian.model.report.form.output.EntityTreatGoodsDetailEasyExcelStatementForm;
import com.zhelian.model.report.form.output.EntityTreatGoodsDetailStatementForm;
import com.zhelian.model.report.form.output.EntityTreatGoodsDetailSumStatementForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EntityTreatGoodsDetailMapper {

    List<EntityTreatGoodsDetailStatementForm> getEntityTreatGoodsDetail(@Param("EmployeeID") String EmployeeID, @Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID, @Param("Name") String Name, @Param("StartTime") String StartTime, @Param("EndTime") String EndTime, @Param("BrandID") Integer BrandID);

    EntityTreatGoodsDetailSumStatementForm getEntityTreatGoodsDetailSum(@Param("EmployeeID") String EmployeeID, @Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID, @Param("Name") String Name, @Param("StartTime") String StartTime, @Param("EndTime") String EndTime, @Param("BrandID") Integer BrandID);

    List<Object> getEntityTreatGoodsDetailExcel(@Param("EmployeeID") String EmployeeID, @Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID, @Param("Name") String Name, @Param("StartTime") String StartTime, @Param("EndTime") String EndTime, @Param("BrandID") Integer BrandID);

    List<EntityTreatGoodsDetailEasyExcelStatementForm> getEntityTreatGoodsDetailEasyExcel(@Param("EmployeeID") String EmployeeID, @Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID, @Param("Name") String Name, @Param("StartTime") String StartTime, @Param("EndTime") String EndTime, @Param("BrandID") Integer BrandID);
}
