package com.zhelian.mapper.report;

import com.zhelian.model.report.form.output.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CustomerStatementMapper {

    List<CustomerDynamicSaleAndDynamicConsumeDetailStatementForm> getCustomerDynamicSaleAndDynamicConsume(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("LevelID") Integer LevelID, @Param("SourceID") Integer SourceID, @Param("StartCreatedOn") String StartCreatedOn, @Param("EndCreatedOn") String EndCreatedOn, @Param("EntityID") Integer EntityID, @Param("StartTime") String StartTime, @Param("EndTime") String EndTime);


    CustomerDynamicSaleAndDynamicConsumeSumStatementForm getCustomerDynamicSaleAndDynamicConsumeSum(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("LevelID") Integer LevelID, @Param("SourceID") Integer SourceID, @Param("StartCreatedOn") String StartCreatedOn, @Param("EndCreatedOn") String EndCreatedOn, @Param("EntityID") Integer EntityID, @Param("StartTime") String StartTime, @Param("EndTime") String EndTime);


    List<Object> getCustomerDynamicSaleAndDynamicConsumeExcel(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("LevelID") Integer LevelID, @Param("SourceID") Integer SourceID, @Param("StartCreatedOn") String StartCreatedOn, @Param("EndCreatedOn") String EndCreatedOn, @Param("EntityID") Integer EntityID, @Param("StartTime") String StartTime, @Param("EndTime") String EndTime);

    List<CustomerDynamicSaleAndDynamicConsumeDetailStatementEasyExcelForm> getCustomerDynamicSaleAndDynamicConsumeEasyExcel(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("LevelID") Integer LevelID, @Param("SourceID") Integer SourceID, @Param("StartCreatedOn") String StartCreatedOn, @Param("EndCreatedOn") String EndCreatedOn, @Param("EntityID") Integer EntityID, @Param("StartTime") String StartTime, @Param("EndTime") String EndTime);

    List<CustomerAccountStatisticsDetailStatementForm> getCustomerAccountStatistics(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("BuyEntityID") Integer BuyEntityID, @Param("EntityID") Integer EntityID, @Param("LevelID") Integer LevelID, @Param("GoodsTypeName") String GoodsTypeName, @Param("GoodsName") String GoodsName, @Param("PackageCardID") Integer PackageCardID, @Param("CategoryName") String CategoryName, @Param("BrandName") String BrandName, @Param("IsLargess") Boolean IsLargess, @Param("IsOverdue") Boolean IsOverdue, @Param("IsShowUp") Boolean IsShowUp,@Param("CustomerSourceID") Integer CustomerSourceID);

    CustomerAccountStatisticsSumStatementForm getCustomerAccountStatisticsSum(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("BuyEntityID") Integer BuyEntityID, @Param("EntityID") Integer EntityID, @Param("LevelID") Integer LevelID, @Param("GoodsTypeName") String GoodsTypeName, @Param("GoodsName") String GoodsName, @Param("PackageCardID") Integer PackageCardID, @Param("CategoryName") String CategoryName, @Param("BrandName") String BrandName, @Param("IsLargess") Boolean IsLargess, @Param("IsOverdue") Boolean IsOverdue, @Param("IsShowUp") Boolean IsShowUp,@Param("CustomerSourceID") Integer CustomerSourceID);

    List<CustomerAccountStatisticsDetailStatementEasyExcelForm> getCustomerAccountStatisticsEasyExcel(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("BuyEntityID") Integer BuyEntityID, @Param("EntityID") Integer EntityID, @Param("LevelID") Integer LevelID, @Param("GoodsTypeName") String GoodsTypeName, @Param("GoodsName") String GoodsName, @Param("PackageCardID") Integer PackageCardID, @Param("CategoryName") String CategoryName, @Param("BrandName") String BrandName, @Param("IsLargess") Boolean IsLargess, @Param("IsOverdue") Boolean IsOverdue, @Param("IsShowUp") Boolean IsShowUp,@Param("CustomerSourceID") Integer CustomerSourceID);

    List<CustomerAccountStatisticsDetailStatementEasyExcelForm> getCustomerAccountStatisticsEasyExcelDisPlayPhone(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("BuyEntityID") Integer BuyEntityID, @Param("EntityID") Integer EntityID, @Param("LevelID") Integer LevelID, @Param("GoodsTypeName") String GoodsTypeName, @Param("GoodsName") String GoodsName, @Param("PackageCardID") Integer PackageCardID, @Param("CategoryName") String CategoryName, @Param("BrandName") String BrandName, @Param("IsLargess") Boolean IsLargess, @Param("IsOverdue") Boolean IsOverdue, @Param("IsShowUp") Boolean IsShowUp,@Param("CustomerSourceID") Integer CustomerSourceID);

    List<CustomerArrearsDetailStatementForm> getCustomerArrear(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("BuyEntityID") Integer BuyEntityID, @Param("EntityID") Integer EntityID, @Param("StartTime") String StartTime, @Param("EndTime") String EndTime, @Param("GoodsTypeName") String GoodsTypeName, @Param("GoodsName") String GoodsName, @Param("CategoryName") String CategoryName);

    CustomerArrearsSumStatementForm getCustomerArrearSum(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("BuyEntityID") Integer BuyEntityID, @Param("EntityID") Integer EntityID, @Param("StartTime") String StartTime, @Param("EndTime") String EndTime, @Param("GoodsTypeName") String GoodsTypeName, @Param("GoodsName") String GoodsName, @Param("CategoryName") String CategoryName);

    List<Object> getCustomerArrearExcel(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("BuyEntityID") Integer BuyEntityID, @Param("EntityID") Integer EntityID, @Param("StartTime") String StartTime, @Param("EndTime") String EndTime, @Param("GoodsTypeName") String GoodsTypeName, @Param("GoodsName") String GoodsName, @Param("CategoryName") String CategoryName);

    List<CustomerArrearsDetailStatementEasyExcelForm> getCustomerArrearEasyExcel(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("BuyEntityID") Integer BuyEntityID, @Param("EntityID") Integer EntityID, @Param("StartTime") String StartTime, @Param("EndTime") String EndTime, @Param("GoodsTypeName") String GoodsTypeName, @Param("GoodsName") String GoodsName, @Param("CategoryName") String CategoryName);

    List<CustomerArrearsDetailStatementEasyExcelForm> getCustomerArrearEasyExcelDisPlayPhone(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name, @Param("BuyEntityID") Integer BuyEntityID, @Param("EntityID") Integer EntityID, @Param("StartTime") String StartTime, @Param("EndTime") String EndTime, @Param("GoodsTypeName") String GoodsTypeName, @Param("GoodsName") String GoodsName, @Param("CategoryName") String CategoryName);
}
