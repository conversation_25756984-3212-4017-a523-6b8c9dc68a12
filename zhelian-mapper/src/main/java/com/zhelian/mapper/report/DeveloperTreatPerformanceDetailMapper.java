package com.zhelian.mapper.report;

import com.zhelian.model.report.form.input.DeveloperTreatPerformanceDetailForm;
import com.zhelian.model.report.form.output.DeveloperTreatPerformanceDetailStatementEasyExcelForm;
import com.zhelian.model.report.form.output.DeveloperTreatPerformanceDetailStatementForm;
import com.zhelian.model.report.form.output.DeveloperTreatPerformanceSumStatementForm;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DeveloperTreatPerformanceDetailMapper {

    List<DeveloperTreatPerformanceDetailStatementForm> getDeveloperTreatPerformanceDetail(DeveloperTreatPerformanceDetailForm form);

    DeveloperTreatPerformanceSumStatementForm getDeveloperTreatPerformanceSum(DeveloperTreatPerformanceDetailForm form);

    List<Object> getExcelDeveloperTreatPerformanceDetail(DeveloperTreatPerformanceDetailForm form);

    List<DeveloperTreatPerformanceDetailStatementEasyExcelForm> getEasyExcelDeveloperTreatPerformanceDetail(DeveloperTreatPerformanceDetailForm form);
}
