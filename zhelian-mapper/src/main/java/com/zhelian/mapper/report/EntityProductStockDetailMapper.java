package com.zhelian.mapper.report;

import com.zhelian.model.report.form.output.EntityProductStockDetailStatementForm;
import com.zhelian.model.report.form.output.EntityProductStockDetailSumStatementForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EntityProductStockDetailMapper {

    List<EntityProductStockDetailStatementForm> getEntityProductStockDetail(@Param("EmployeeID") String EmployeeID, @Param("EntityID") Integer EntityID, @Param("Name") String Name, @Param("StartTime") String StartTime, @Param("EndTime") String EndTime, @Param("ParentCategoryID") Integer ParentCategoryID, @Param("CategoryID") Integer CategoryID, @Param("BrandID") Integer BrandID);

    EntityProductStockDetailSumStatementForm getEntityProductStockDetailSum(@Param("EmployeeID") String EmployeeID, @Param("EntityID") Integer EntityID, @Param("Name") String Name, @Param("StartTime") String StartTime, @Param("EndTime") String EndTime, @Param("ParentCategoryID") Integer ParentCategoryID, @Param("CategoryID") Integer CategoryID, @Param("BrandID") Integer BrandID);

    List<Object> getEntityProductStockDetailExcel(@Param("EmployeeID") String EmployeeID, @Param("EntityID") Integer EntityID, @Param("Name") String Name, @Param("StartTime") String StartTime, @Param("EndTime") String EndTime, @Param("ParentCategoryID") Integer ParentCategoryID, @Param("CategoryID") Integer CategoryID, @Param("BrandID") Integer BrandID);

}
