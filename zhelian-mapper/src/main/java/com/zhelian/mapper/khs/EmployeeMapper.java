package com.zhelian.mapper.khs;

import com.zhelian.model.khs.entity.EmployeeEntity;
import com.zhelian.model.khs.entity.EmployeeEntityEntity;
import com.zhelian.model.khs.entity.EmployeeExtendEntity;
import com.zhelian.model.khs.form.input.EmployeeAddForm;
import com.zhelian.model.khs.form.input.EmployeeJobAddForm;
import com.zhelian.model.khs.form.input.EmployeeQueryForm;
import com.zhelian.model.khs.form.output.BelongEmployeeListForm;
import com.zhelian.model.khs.form.output.EmployeeExportOutputForm;
import com.zhelian.model.khs.form.output.EmployeeListForm;
import com.zhelian.model.miniprogram.form.output.EmployeeOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EmployeeMapper {

    EmployeeEntity getEmployeeByID(String employeeID);

    EmployeeExtendEntity getEmployeeExtendByID(String employeeID);

    List<EmployeeExtendEntity> getEmpList(EmployeeQueryForm form);

    void addEmp(EmployeeEntity employeeEntity);

    void updateEmp(EmployeeAddForm form);

    void updateEmployeeJobList(EmployeeJobAddForm form);

    void updateEmpPassWord(@Param("NewPassword") String newPassword, @Param("UserID") String userID);

    List<EmployeeEntity> getEmployeeByRoleID(Integer roleID);

    List<EmployeeOutputForm> empList(Integer EntityID);

    List<EmployeeExportOutputForm> getExcelEmpList(EmployeeQueryForm form);

    List<EmployeeEntity> getEntityEmployeeList(Integer EntityID);

    List<EmployeeEntity> getAllEmployee();

    List<EmployeeEntity> entityEmployeeByJobType(Integer EntityID);

    List<EmployeeOutputForm> allEmployee(String SearchKey);

    Integer getEmployeePermissionByEntityID(@Param("EntityID") Integer EntityID, @Param("EmployeeID") String EmployeeID);

    EmployeeEntity getEmployee(String ID);

    void updateEmployee(EmployeeEntity employeeEntity);

    List<EmployeeListForm> getEmployeePermissionEntityEmployee(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name);

    List<BelongEmployeeListForm> belongCurrentEntity(@Param("EntityID") Integer EntityID, @Param("SearchKey") String SearchKey);

    List<BelongEmployeeListForm> belongCurrentNoPrimaryEntity(@Param("EntityID") Integer EntityID, @Param("SearchKey") String SearchKey);

    void deleteBelongEntity(@Param("EntityID") Integer EntityID,@Param("EmployeeID") String EmployeeID);

    List<BelongEmployeeListForm> belongCurrentNoEntity(@Param("EntityID") Integer EntityID, @Param("SearchKey") String SearchKey);

    void createBelongEntity(EmployeeEntityEntity employeeEntityEntity);

    EmployeeEntityEntity getEmployeeEntityEntity(@Param("EntityID") Integer EntityID, @Param("EmployeeID") String EmployeeID);

    List<EmployeeOutputForm> listAllActiveEmployees(@Param("Name") String name);
}
