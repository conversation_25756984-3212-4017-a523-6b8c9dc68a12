package com.zhelian.mapper.khs;

import com.zhelian.model.khs.entity.JobTypeEntity;
import com.zhelian.model.khs.form.input.JobTypeAddForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface JobTypeMapper {

    List<JobTypeEntity> getJobType(@Param("jobTypeName") String jobTypeName, @Param("active") Boolean active);

    void addJobType(JobTypeAddForm jobTypeAddForm);

    JobTypeEntity getJobTypeByID(Integer ID);

    int countJobTypeByName(String Name);

    void updateJobType(JobTypeEntity jobTypeEntity);

}
