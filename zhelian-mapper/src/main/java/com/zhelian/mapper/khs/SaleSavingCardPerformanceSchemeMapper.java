package com.zhelian.mapper.khs;


import com.zhelian.model.ibeauty.form.output.SaleSavingCardCommissionSchemeOutputForm;
import com.zhelian.model.khs.entity.SaleSavingCardPerformanceSchemeEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleSavingCardPerformanceSchemeMapper {

    List<SaleSavingCardCommissionSchemeOutputForm> saleSavingCardPerformanceSchemeList(@Param("Name") String Name);

    void createSaleSavingCardPerformanceScheme(SaleSavingCardPerformanceSchemeEntity saleProductPerformanceSchemeEntity);

    void deleteSaleSavingCardPerformanceScheme(@Param("EntityID") Integer EntityID);

    Integer getSaleSavingCardPerformanceScheme(@Param("EntityID") Integer EntityID);

}
