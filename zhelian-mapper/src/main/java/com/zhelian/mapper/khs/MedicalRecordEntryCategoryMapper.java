package com.zhelian.mapper.khs;

import com.zhelian.model.khs.entity.MedicalRecordEntryCategoryEntity;
import com.zhelian.model.khs.form.output.MedicalRecordEntryCategoryOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;

@Repository
public interface MedicalRecordEntryCategoryMapper {

    List<MedicalRecordEntryCategoryOutForm> list();

    Integer getMaxSequence();

    void create(MedicalRecordEntryCategoryEntity entity);

    void update(MedicalRecordEntryCategoryEntity entity);

    void delete(@Param("ID") Integer ID);

    List<MedicalRecordEntryCategoryEntity> medicalRecordEntryCategoryEntityListByID(@Param("ID") Integer ID);

    void updateSequence(List<MedicalRecordEntryCategoryEntity> list);

    MedicalRecordEntryCategoryEntity getMedicalRecordEntryCategoryEntityByID(@Param("ID") Integer ID);
}
