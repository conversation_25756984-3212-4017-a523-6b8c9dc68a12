package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.SaleProjectHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SaleProjectHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleGoodCategoryHandlerPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleProjectHandlerPerformanceMapper {

    List<SaleGoodCategoryHandlerPerformanceOutputForm> allSaleProjectHandlerPerformance(@Param("EntityID") Integer EntityID, @Param("ProjectID") Integer ProjectID);

    void deleteSaleProjectHandler(@Param("EntityID") Integer EntityID, @Param("ProjectID") Integer ProjectID);

    void deleteSaleProjectHandlerJobType(@Param("EntityID") Integer EntityID, @Param("ProjectID") Integer ProjectID);

    void updateSaleProjectHandlerPerformance(List<SaleProjectHandlerPerformanceEntity> list);

    void insertSaleProjectHandlerJobTypePerformance(List<SaleProjectHandlerJobTypePerformanceEntity> list);

    void deleteSaleProjectHandlerPerformanceByEntity(@Param("EntityID") Integer EntityID);

    void deleteSaleProjectHandlerJobTypePerformanceByEntity(@Param("EntityID") Integer EntityID);
}
