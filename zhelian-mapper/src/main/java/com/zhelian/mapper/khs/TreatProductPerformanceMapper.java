package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TBTreatProductPerformanceEntity;
import com.zhelian.model.khs.form.output.TreatProductPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface TreatProductPerformanceMapper {

    List<TreatProductPerformanceOutputForm> allTreatProductPerformance(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertTreatProductPerformance(List<TBTreatProductPerformanceEntity> list);

    void deleteTreatProductPerformance(Map<String, Object> map);

    void deleteTreatProductPerformanceByEntity(Integer EntityID);

    void deleteTreatProductPerformanceByEntityAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);
}
