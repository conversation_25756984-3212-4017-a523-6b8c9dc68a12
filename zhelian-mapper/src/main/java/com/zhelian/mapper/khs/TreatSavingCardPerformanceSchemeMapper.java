package com.zhelian.mapper.khs;


import com.zhelian.model.ibeauty.form.output.TreatSavingCardProjectCommissionSchemeOutputForm;
import com.zhelian.model.khs.entity.TreatSavingCardPerformanceSchemeEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatSavingCardPerformanceSchemeMapper {

    List<TreatSavingCardProjectCommissionSchemeOutputForm> treatSavingCardPerformanceSchemeList(@Param("Name") String Name);

    void createTreatSavingCardPerformanceScheme(TreatSavingCardPerformanceSchemeEntity treatSavingCardPerformanceSchemeEntity);

    Integer getTreatSavingCardPerformanceScheme(@Param("EntityID") Integer EntityID);

    void deleteTreatSavingCardPerformanceScheme(@Param("EntityID") Integer EntityID);


}
