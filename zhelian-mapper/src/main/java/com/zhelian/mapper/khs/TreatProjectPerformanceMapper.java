package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TBTreatProjectPerformanceEntity;
import com.zhelian.model.khs.form.output.TreatProjectPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface TreatProjectPerformanceMapper {

    List<TreatProjectPerformanceOutputForm> allTreatProjectPerformance(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertTreatProjectPerformance(List<TBTreatProjectPerformanceEntity> list);

    void deleteTreatProjectPerformance(Map<String, Object> map);

    void deleteTreatProjectPerformanceByEntity(Integer EntityID);

    void deleteTreatProjectPerformanceByEntityAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);
}
