package com.zhelian.mapper.khs;


import com.zhelian.model.ibeauty.form.output.SaleGeneralCardCommissionSchemeOutputForm;
import com.zhelian.model.khs.entity.SaleGeneralCardPerformanceSchemeEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleGeneralCardPerformanceSchemeMapper {

    List<SaleGeneralCardCommissionSchemeOutputForm> saleGeneralCardPerformanceSchemeList(@Param("Name") String Name);

    void createSaleGeneralCardPerformanceScheme(SaleGeneralCardPerformanceSchemeEntity saleGeneralCardPerformanceSchemeEntity);

    void deleteSaleGeneralCardPerformanceScheme(@Param("EntityID") Integer EntityID);

    Integer findSaleGeneralCardPerformanceSchemeByEntityID(@Param("EntityID") Integer EntityID);
}
