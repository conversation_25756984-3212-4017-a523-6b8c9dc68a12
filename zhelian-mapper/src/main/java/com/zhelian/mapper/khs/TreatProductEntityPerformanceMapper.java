package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TreatGoodEntityPerformanceEntity;
import com.zhelian.model.khs.form.output.TreatGoodPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface TreatProductEntityPerformanceMapper {

    List<TreatGoodPerformanceOutputForm> allTreatProductEntityPerformance(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertTreatProductEntityPerformance(List<TreatGoodEntityPerformanceEntity> list);

    void deleteTreatProductEntityPerformance(Map<String, Object> map);

    void deleteTreatProductEntityPerformanceByEntity(Integer EntityID);

    void deleteTreatProductEntityPerformanceByEntityAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);
}
