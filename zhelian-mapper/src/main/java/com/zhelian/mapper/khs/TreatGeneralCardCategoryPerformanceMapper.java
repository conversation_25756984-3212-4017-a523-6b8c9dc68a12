package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TreatGeneralCardCategoryPerformanceEntity;
import com.zhelian.model.khs.entity.TreatGeneralCardPerformanceSchemeEntity;
import com.zhelian.model.khs.form.output.TreatCardGoodCategoryOutputForm;
import com.zhelian.model.khs.form.output.TreatGoodCategoryPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatGeneralCardCategoryPerformanceMapper {

    TreatCardGoodCategoryOutputForm treatGeneralCardProjectCategoryPerformance(@Param("EntityID") Integer EntityID);

    List<TreatGoodCategoryPerformanceOutputForm> allSaleGeneralCardCategoryPerformance(@Param("EntityID") Integer EntityID);

    void updateTreatGeneralCardPerformanceScheme(TreatGeneralCardPerformanceSchemeEntity treatGeneralCardProjectPerformanceSchemeEntity);

    void deleteTreatGeneralCardCategoryPerformance(@Param("EntityID") Integer EntityID);


    void insertTreatGeneralCardCategoryPerformanceEntity(List<TreatGeneralCardCategoryPerformanceEntity> list);
}
