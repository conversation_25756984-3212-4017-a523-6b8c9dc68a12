package com.zhelian.mapper.khs;

import com.zhelian.model.khs.entity.SaleProductPerformanceEntity;
import com.zhelian.model.khs.form.output.PerformanceSchemeGoodOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ChannelEmployeePerformanceSchemeProductMapper {

    List<PerformanceSchemeGoodOutForm> getAllPerformanceSchemeProduct(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);

    void insertSaleProductPerformance(List<SaleProductPerformanceEntity> list);

    void deleteSaleProductPerformanceByPerformanceSchemeIDAndCategory(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);

}
