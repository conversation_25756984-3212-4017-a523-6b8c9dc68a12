package com.zhelian.mapper.khs;


import com.zhelian.model.ibeauty.form.output.SaleTimeCardCommissionSchemeOutputForm;
import com.zhelian.model.khs.entity.SaleTimeCardPerformanceSchemeEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleTimeCardPerformanceSchemeMapper {

    List<SaleTimeCardCommissionSchemeOutputForm> saleTimeCardPerformanceSchemeList(@Param("Name") String Name);

    void createSaleTimeCardPerformanceScheme(SaleTimeCardPerformanceSchemeEntity saleTimeCardPerformanceSchemeEntity);

    void deleteSaleTimeCardPerformanceScheme(@Param("EntityID") Integer EntityID);

    Integer findSaleTimeCardPerformanceSchemeByEntityID(@Param("EntityID") Integer EntityID);
}
