package com.zhelian.mapper.khs;

import com.zhelian.model.khs.entity.*;
import com.zhelian.model.khs.form.output.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ChannelEmployeePerformanceSchemeMapper {

    List<ChannelEmployeePerformanceSchemeEntity> getPerformanceScheme(@Param("Name") String Name, @Param("Active") Boolean Active);

    void createPerformanceScheme(ChannelEmployeePerformanceSchemeEntity performanceSchemeEntity);

    void createSaleProductCategoryPerformanceEntity(List<SaleProductCategoryPerformanceEntity> list);

    void createSaleProjectCategoryPerformanceEntity(List<SaleProjectCategoryPerformanceEntity> projectList);

    void createSaleGeneralCardCategoryPerformanceEntity(List<SaleGeneralCardCategoryPerformanceEntity> generalCardList);

    void createSaleTimeCardCategoryPerformanceEntity(List<SaleTimeCardCategoryPerformanceEntity> timeCardList);

    void createSaleSavingCardCategoryPerformanceEntity(List<SaleSavingCardCategoryPerformanceEntity> savingCardList);

    void createTreatProductCategoryPerformanceEntity(List<TreatProductCategoryPerformanceEntity> treatProductList);

    void createTreatProjectCategoryPerformanceEntity(List<TreatProjectCategoryPerformanceEntity> treatProjectList);

    void createTreatGeneralCardCategoryPerformanceEntity(List<TreatChannelCardCategoryPerformanceEntity> list);

    void createTreatTimeCardCategoryPerformanceEntity(List<TreatChannelCardCategoryPerformanceEntity> list);

    void createTreatSavingCardCategoryPerformanceEntity(List<TreatChannelCardCategoryPerformanceEntity> list);

    void updatePerformanceScheme(ChannelEmployeePerformanceSchemeEntity performanceSchemeEntity);

    void deleteProductCategory(@Param("ID") Integer ID);

    void deleteProjectCategory(@Param("ID") Integer ID);

    void deleteGeneralCardCategory(@Param("ID") Integer ID);

    void deleteTimeCardCategory(@Param("ID") Integer ID);

    void deleteSavingCardCategory(@Param("ID") Integer ID);

    void deleteTreatProductCategory(@Param("ID") Integer ID);

    void deleteTreatProjectCategory(@Param("ID") Integer ID);

    void deleteTreatGeneralCardCategory(@Param("ID") Integer ID);

    void deleteTreatTimeCardCategory(@Param("ID") Integer ID);

    void deleteTreatSavingCardCategory(@Param("ID") Integer ID);

    List<PerformanceSchemeGoodCategoryOutForm> getPerformanceSchemeProduct(@Param("ID") Integer ID);

    List<PerformanceSchemeGoodCategoryOutForm> getPerformanceSchemeProject(@Param("ID") Integer ID);

    List<PerformanceSchemeGoodCardCategoryOutForm> getPerformanceSchemeGeneralCard(@Param("ID") Integer ID);

    List<PerformanceSchemeGoodCardCategoryOutForm> getPerformanceSchemeTimeCard(@Param("ID") Integer ID);

    List<PerformanceSchemeSavingCardCategoryOutForm> getPerformanceSchemeSavingCard(@Param("ID") Integer ID);

    List<PerformanceSchemeTreatGoodCategoryOutForm> getPerformanceSchemeTreatProduct(@Param("ID") Integer ID);

    List<PerformanceSchemeTreatGoodCategoryOutForm> getPerformanceSchemeTreatProject(@Param("ID") Integer ID);

    Integer getPerformanceSchemeCount(@Param("ID") Integer ID);

    List<ChannelPerformanceSchemeTreaCardCategoryOutForm> getPerformanceSchemeTreatGeneralCard(Integer ID);

    List<ChannelPerformanceSchemeTreaCardCategoryOutForm> getPerformanceSchemeTreatTimeCard(Integer ID);

    List<ChannelPerformanceSchemeTreaSavingCardCategoryOutForm> getPerformanceSchemeTreatSavingCard(Integer ID);
}
