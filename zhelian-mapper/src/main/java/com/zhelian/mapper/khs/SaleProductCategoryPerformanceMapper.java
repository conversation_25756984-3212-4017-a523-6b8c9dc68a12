package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.SaleProductPerformanceSchemeEntity;
import com.zhelian.model.khs.entity.TBSaleProductCategoryPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleGoodCategoryPerformanceOutputForm;
import com.zhelian.model.khs.form.output.SaleGoodCategorySchemeOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleProductCategoryPerformanceMapper {

    List<SaleGoodCategoryPerformanceOutputForm> allSaleProductCategoryPerformance(@Param("EntityID") Integer EntityID);

    void updateSaleProductPerformanceScheme(SaleProductPerformanceSchemeEntity saleProductPerformanceSchemeEntity);

    void insertSaleProductCategoryPerformanceEntity(List<TBSaleProductCategoryPerformanceEntity> list);

    SaleGoodCategorySchemeOutputForm saleProductCategoryPerformance(@Param("EntityID") Integer EntityID);

    void deleteSaleProductCategoryPerformanceEntity(@Param("EntityID") Integer EntityID);
}
