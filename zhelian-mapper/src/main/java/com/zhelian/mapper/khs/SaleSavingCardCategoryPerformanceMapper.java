package com.zhelian.mapper.khs;


import com.zhelian.model.ibeauty.form.output.SaleSavingCardCategoryCommissionOutputForm;
import com.zhelian.model.ibeauty.form.output.SaleSavingCardCategoryOutputForm;
import com.zhelian.model.khs.entity.SaleSavingCardPerformanceSchemeEntity;
import com.zhelian.model.khs.entity.TBSaleSavingCardCategoryPerformanceEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleSavingCardCategoryPerformanceMapper {

    SaleSavingCardCategoryOutputForm saleSavingCardCategoryPerformance(@Param("EntityID") Integer EntityID);

    List<SaleSavingCardCategoryCommissionOutputForm> allSaleSavingCardCategoryPerformance(@Param("EntityID") Integer EntityID);

    void updateSaleSavingCardPerformanceScheme(SaleSavingCardPerformanceSchemeEntity saleSavingCardPerformanceSchemeEntity);

    void insertSaleSavingCardCategoryPerformanceEntity(List<TBSaleSavingCardCategoryPerformanceEntity> list);

    void deleteSaleSavingCardCategoryPerformance(@Param("EntityID") Integer EntityID);
}
