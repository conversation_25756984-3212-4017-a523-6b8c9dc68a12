package com.zhelian.mapper.khs;


import com.zhelian.model.khs.form.output.SaleGoodCategoryHandlerPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleTimeCardHandlerPerformanceMapper {

    List<SaleGoodCategoryHandlerPerformanceOutputForm> allSaleTimeCardHandlerPerformance(@Param("EntityID") Integer EntityID, @Param("TimeCardID") Integer TimeCardID);

    void deleteSaleTimeCardHandler(@Param("EntityID") Integer EntityID, @Param("TimeCardID") Integer TimeCardID);

    void deleteSaleTimeCardHandlerJobType(@Param("EntityID") Integer EntityID, @Param("TimeCardID") Integer TimeCardID);

    void insertSaleTimeCardHandlerPerformance(List list);

    void insertSaleTimeCardHandlerJobTypePerformance(List list);

    void deleteSaleTimeCardHandlerJobTypePerformance(Integer EntityID);

    void deleteSaleTimeCardHandlerPerformance(Integer entityID);
}
