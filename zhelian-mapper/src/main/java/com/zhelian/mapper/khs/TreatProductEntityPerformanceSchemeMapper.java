package com.zhelian.mapper.khs;


import com.zhelian.model.ibeauty.form.output.TreatProductCommissionSchemeOutputForm;
import com.zhelian.model.khs.entity.TreatGoodEntityPerformanceSchemeEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatProductEntityPerformanceSchemeMapper {

    List<TreatProductCommissionSchemeOutputForm> treatProductEntityPerformanceSchemeList(@Param("Name") String Name);

    void createTreatProductEntityPerformanceScheme(TreatGoodEntityPerformanceSchemeEntity TreatProductEntityEntityPerformanceSchemeEntity);

    Integer getTreatProductEntityPerformanceScheme(@Param("EntityID") Integer EntityID);

    void deleteTreatProductEntityPerformanceScheme(@Param("EntityID") Integer EntityID);


}
