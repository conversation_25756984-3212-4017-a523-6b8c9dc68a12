package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.SaleTimeCardPerformanceSchemeEntity;
import com.zhelian.model.khs.entity.TBSaleTimeCardCategoryPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleCardGoodCategoryOutputForm;
import com.zhelian.model.khs.form.output.SaleGoodCategoryEntityPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleTimeCardCategoryPerformanceMapper {

    List<SaleGoodCategoryEntityPerformanceOutputForm> allSaleTimeCardCategoryPerformance(@Param("EntityID") Integer EntityID);

    void updateSaleTimeCardPerformanceScheme(SaleTimeCardPerformanceSchemeEntity saleTimeCardPerformanceSchemeEntity);

    void insertSaleTimeCardCategoryPerformanceEntity(List<TBSaleTimeCardCategoryPerformanceEntity> list);

    SaleCardGoodCategoryOutputForm saleTimeCardCategoryPerformance(@Param("EntityID") Integer EntityID);

    void deleteSaleTimeCardCategoryPerformance(Integer EntityID);
}
