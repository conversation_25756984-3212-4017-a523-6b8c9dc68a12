package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TBSaleSavingCardPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleSavingCardPerformacneExtendOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface SaleSavingCardPerformanceMapper {

    List<SaleSavingCardPerformacneExtendOutputForm> allSaleSavingCardPerformance(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertSaleSavingCardPerformance(List<TBSaleSavingCardPerformanceEntity> list);

    void deleteSaleSavingCardPerformance(Map<String, Object> map);

    void deleteSaleSavingCardPerformanceByEntity(Integer EntityID);

    void deleteSaleSavingCardPerformanceByEntityAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);
}
