package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.ChannelEmployeeSalarySettlementIntervalEntity;
import com.zhelian.model.khs.form.output.ChannelEmployeeSalarySheetOutExcelForm;
import com.zhelian.model.khs.form.output.ChannelEmployeeSalarySheetOutForm;
import com.zhelian.model.khs.form.output.SalaryChannelEmployeePerformanceCommissionOutForm;
import com.zhelian.model.khs.form.output.SalarySettlementIntervalOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface ChannelEmployeeSalarySheetMapper {

    void createSettlementInterval(ChannelEmployeeSalarySettlementIntervalEntity channelEmployeeSalarySettlementIntervalEntity);

    List<SalarySettlementIntervalOutForm> allSettlementInterval();

    List<ChannelEmployeeSalarySheetOutForm> all(@Param("Name") String Name, @Param("SalarySettlementIntervalID") Integer SalarySettlementIntervalID, @Param("JobID") Integer JobID, @Param("EntityID") Integer EntityID, @Param("IsHideZero") Boolean IsHideZero);

    List<ChannelEmployeeSalarySheetOutExcelForm> excel(@Param("Name") String Name, @Param("SalarySettlementIntervalID") Integer SalarySettlementIntervalID, @Param("JobID") Integer JobID, @Param("EntityID") Integer EntityID, @Param("IsHideZero") Boolean IsHideZero);

    void settlement(@Param("ID") Integer ID);

    List<SalaryChannelEmployeePerformanceCommissionOutForm> employeeCommission(@Param("EmployeeID") String EmployeeID, @Param("SalarySettlementIntervalID") Integer SalarySettlementIntervalID);

    List<SalaryChannelEmployeePerformanceCommissionOutForm> teamCommission(@Param("EmployeeID") String EmployeeID, @Param("SalarySettlementIntervalID") Integer SalarySettlementIntervalID);
}
