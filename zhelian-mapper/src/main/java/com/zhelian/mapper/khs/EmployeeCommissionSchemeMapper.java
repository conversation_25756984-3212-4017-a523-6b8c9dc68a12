package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.EmployeeCommissionEntity;
import com.zhelian.model.khs.entity.EmployeeCommissionEntityEntity;
import com.zhelian.model.khs.entity.EmployeeCommissionGoodSaleHandlerJobTypeEntity;
import com.zhelian.model.khs.entity.EmployeeCommissionSchemeEntity;
import com.zhelian.model.khs.form.output.EmployeeCommissionHandlerOutForm;
import com.zhelian.model.khs.form.output.EmployeeCommissionOutForm;
import com.zhelian.model.khs.form.output.EmployeeCommissionSchemeOutForm;
import com.zhelian.model.khs.form.output.EmployeeEntityOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface EmployeeCommissionSchemeMapper {


    List<EmployeeCommissionSchemeOutForm> getEmployeeCommissionScheme(@Param("Name") String Name, @Param("PerformanceEvaluationSchemeID") Integer PerformanceEvaluationSchemeID, @Param("PerformanceCalculationSchemeID") Integer PerformanceCalculationSchemeID, @Param("Active") Boolean Active);

    void createEmployeeCommissionScheme(EmployeeCommissionSchemeEntity employeeCommissionSchemeEntity);

    void createEmployeeCommission(List<EmployeeCommissionEntity> list);

    void createEmployeeCommissionEntities(List<EmployeeCommissionEntityEntity> list);

    void createEmployeeCommissionProductHandler(List<EmployeeCommissionGoodSaleHandlerJobTypeEntity> list);

    void createEmployeeCommissionProjectHandler(List<EmployeeCommissionGoodSaleHandlerJobTypeEntity> list);

    void createEmployeeCommissionGeneralCardHandler(List<EmployeeCommissionGoodSaleHandlerJobTypeEntity> list);

    void createEmployeeCommissionTimeCardHandler(List<EmployeeCommissionGoodSaleHandlerJobTypeEntity> list);

    void createEmployeeCommissionSavingCardHandler(List<EmployeeCommissionGoodSaleHandlerJobTypeEntity> list);

    void createEmployeeCommissionPackageCardHandler(List<EmployeeCommissionGoodSaleHandlerJobTypeEntity> list);

    void createEmployeeCommissionTreatProductHandler(List<EmployeeCommissionGoodSaleHandlerJobTypeEntity> list);

    void createEmployeeCommissionTreatProjectHandler(List<EmployeeCommissionGoodSaleHandlerJobTypeEntity> list);

    void createEmployeeCommissionTreatSavingCardHandler(List<EmployeeCommissionGoodSaleHandlerJobTypeEntity> list);

    void updateEmployeeCommissionScheme(EmployeeCommissionSchemeEntity employeeCommissionSchemeEntity);

    void deleteEmployeeCommission(@Param("ID") Integer ID);

    void deleteEmployeeCommissionEntity(@Param("ID") Integer ID);

    void deleteEmployeeCommissionProductHandler(@Param("ID") Integer ID);

    void deleteEmployeeCommissionProjectHandler(@Param("ID") Integer ID);

    void deleteEmployeeCommissionGeneralCardHandler(@Param("ID") Integer ID);

    void deleteEmployeeCommissionTimeCardHandler(@Param("ID") Integer ID);

    void deleteEmployeeCommissionSavingCardHandler(@Param("ID") Integer ID);

    void deleteEmployeeCommissionPackageCardHandler(@Param("ID") Integer ID);

    void deleteEmployeeCommissionTreatProductHandler(@Param("ID") Integer ID);

    void deleteEmployeeCommissionTreatProjectHandler(@Param("ID") Integer ID);

    void deleteEmployeeCommissionTreatSavingCardHandler(@Param("ID") Integer ID);

    List<EmployeeEntityOutForm> getSaleProductHandler();

    List<EmployeeEntityOutForm> getSaleProjectHandler();

    List<EmployeeEntityOutForm> getSaleGeneralCardHandler();

    List<EmployeeEntityOutForm> getSaleTimeCardHandler();

    List<EmployeeEntityOutForm> getSaleSavingCardHandler();

    List<EmployeeEntityOutForm> getSalePackageCardHandler();

    List<EmployeeEntityOutForm> getTreatProductHandler();

    List<EmployeeEntityOutForm> getTreatProjectHandler();

    List<EmployeeEntityOutForm> getTreatSavingCardHandler();

    List<EmployeeCommissionOutForm> getEmployeeCommission(@Param("ID") Integer ID);

    Integer[] getEmployeeCommissionEntity(@Param("ID") Integer ID);

    List<EmployeeCommissionHandlerOutForm> getEmployeeCommissionProductHandler(@Param("ID") Integer ID);

    List<EmployeeCommissionHandlerOutForm> getEmployeeCommissionProjectHandler(@Param("ID") Integer ID);

    List<EmployeeCommissionHandlerOutForm> getEmployeeCommissionGeneralCardHandler(@Param("ID") Integer ID);

    List<EmployeeCommissionHandlerOutForm> getEmployeeCommissionTimeCardHandler(@Param("ID") Integer ID);

    List<EmployeeCommissionHandlerOutForm> getEmployeeCommissionSavingCardHandler(@Param("ID") Integer ID);

    List<EmployeeCommissionHandlerOutForm> getEmployeeCommissionPackageCardHandler(@Param("ID") Integer ID);

    List<EmployeeCommissionHandlerOutForm> getEmployeeCommissionTreatProductHandler(@Param("ID") Integer ID);

    List<EmployeeCommissionHandlerOutForm> getEmployeeCommissionTreatProjectHandler(@Param("ID") Integer ID);

    List<EmployeeCommissionHandlerOutForm> getEmployeeCommissionTreatSavingCardHandler(@Param("ID") Integer ID);
}
