package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.SaleGoodCategoryEntityPerformanceEntity;
import com.zhelian.model.khs.entity.SaleGoodEntityPerformanceSchemeEntity;
import com.zhelian.model.khs.form.output.SaleSavingCardCategoryEntityPerformanceOutputForm;
import com.zhelian.model.khs.form.output.SaleSavingCardCategoryOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleSavingCardCategoryEntityPerformanceMapper {

    List<SaleSavingCardCategoryEntityPerformanceOutputForm> allSaleSavingCardCategoryEntityPerformance(@Param("EntityID") Integer EntityID);

    void updateSaleSavingCardEntityPerformanceScheme(SaleGoodEntityPerformanceSchemeEntity saleSavingCardEntityPerformanceSchemeEntity);

    void insertSaleSavingCardCategoryEntityPerformanceEntity(List<SaleGoodCategoryEntityPerformanceEntity> list);

    SaleSavingCardCategoryOutputForm saleSavingCardCategoryEntityPerformance(@Param("EntityID") Integer EntityID);

    void deleteSaleSavingCardCategoryEntityPerformanceEntity(@Param("EntityID") Integer EntityID);
}
