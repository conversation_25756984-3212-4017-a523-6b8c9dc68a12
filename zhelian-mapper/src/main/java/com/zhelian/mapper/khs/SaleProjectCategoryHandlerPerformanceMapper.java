package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.SaleProjectCategoryHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SaleProjectCategoryHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleGoodCategoryHandlerPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleProjectCategoryHandlerPerformanceMapper {

    List<SaleGoodCategoryHandlerPerformanceOutputForm> allSaleProjectCategoryHandlerPerformance(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void updateSaleProjectCategoryHandlerPerformance(List<SaleProjectCategoryHandlerPerformanceEntity> list);

    void insertSaleProjectCategoryHandlerJobTypePerformance(List<SaleProjectCategoryHandlerJobTypePerformanceEntity> list);

    void deleteSaleProjectCategoryHandler(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void deleteSaleProjectCategoryHandlerJobType(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void deleteSaleProjectCategoryHandlerPerformanceByEntity(@Param("EntityID") Integer EntityID);

    void deleteSaleProjectCategoryHandlerJobTypePerformanceByEntity(@Param("EntityID") Integer EntityID);
}
