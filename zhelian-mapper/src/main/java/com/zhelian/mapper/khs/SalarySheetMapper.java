package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.SalarySettlementIntervalEntity;
import com.zhelian.model.khs.form.input.SalarySheetQueryForm;
import com.zhelian.model.khs.form.output.*;
import com.zhelian.model.report.form.output.EmployeeSalePerformanceCommissionDetailStatementExtendForm;
import com.zhelian.model.report.form.output.EmployeeSalePerformanceCommissionSumStatementExtendForm;
import com.zhelian.model.report.form.output.EmployeeTreatPerformanceCommissionDetailStatementExtendForm;
import com.zhelian.model.report.form.output.EmployeeTreatPerformanceCommissionSumStatementExtendForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SalarySheetMapper {

    List<EmployeeSalePerformanceCommissionDetailStatementExtendForm> getEmployeeSalePerformanceCommissionDetail(@Param("EmployeeID") String employeeID, @Param("StartTime") String StartTime, @Param("EndTime") String EndTime);

    EmployeeSalePerformanceCommissionSumStatementExtendForm getEmployeeSalePerformanceCommissionSum(@Param("EmployeeID") String employeeID, @Param("StartTime") String StartTime, @Param("EndTime") String EndTime);

    List<EmployeeTreatPerformanceCommissionDetailStatementExtendForm> getEmployeeTreatPerformanceCommissionDetail(@Param("EmployeeID") String employeeID, @Param("StartTime") String StartTime, @Param("EndTime") String EndTime);

    EmployeeTreatPerformanceCommissionSumStatementExtendForm getEmployeeTreatPerformanceCommissionSum(@Param("EmployeeID") String employeeID, @Param("StartTime") String StartTime, @Param("EndTime") String EndTime);

    List<SalarySheetOutForm> getSalary(@Param("Name") String Name, @Param("SalarySettlementIntervalID") Integer SalarySettlementIntervalID, @Param("JobID") Integer JobID, @Param("EntityID") Integer EntityID, @Param("IsHideZero") Boolean IsHideZero);

    void createSettlementInterval(SalarySettlementIntervalEntity salarySettlementIntervalEntity);

    List<SalarySettlementIntervalOutForm> allSettlementInterval();

    List<SalaryEmployeePerformanceCommissionOutForm> employeeCommission(@Param("EmployeeID") String employeeID, @Param("SalarySettlementIntervalID") Integer salarySettlementIntervalID);

    List<SalaryTeamPerformanceCommissionOutForm> teamCommission(@Param("EmployeeID") String employeeID, @Param("SalarySettlementIntervalID") Integer salarySettlementIntervalID);

    List<SalaryEntityPerformanceCommissionOutForm> entityCommission(@Param("EmployeeID") String employeeID, @Param("SalarySettlementIntervalID") Integer salarySettlementIntervalID);

    SalarySettlementIntervalOutForm getSalarySettlementIntervalEntity(@Param("ID") Integer ID);

    void settlement(@Param("ID") Integer ID);

    List<Object> excelSalary(SalarySheetQueryForm form);

    List<SalarySheetEasyExcelOutForm> easyExcelSalary(SalarySheetQueryForm form);

    void updateSalarySettlementInterval(@Param("ID") Integer ID, @Param("IsInSettlement")Boolean IsInSettlement);
}
