package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.*;
import com.zhelian.model.khs.form.output.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PerformanceSchemeMapper {

    List<PerformanceSchemeEntity> getPerformanceScheme(@Param("Name") String Name, @Param("Active") Boolean Active);

    void createPerformanceScheme(PerformanceSchemeEntity performanceSchemeEntity);

    void createSalePackageCardCategoryPerformanceEntity(List<SalePackageCardCategoryPerformanceEntity> list);

    void createSaleProductCategoryPerformanceEntity(List<SaleProductCategoryPerformanceEntity> list);

    void createSaleProjectCategoryPerformanceEntity(List<SaleProjectCategoryPerformanceEntity> projectList);

    void createSaleGeneralCardCategoryPerformanceEntity(List<SaleGeneralCardCategoryPerformanceEntity> generalCardList);

    void createSaleTimeCardCategoryPerformanceEntity(List<SaleTimeCardCategoryPerformanceEntity> timeCardList);

    void createSaleSavingCardCategoryPerformanceEntity(List<SaleSavingCardCategoryPerformanceEntity> savingCardList);

    void createTreatProductCategoryPerformanceEntity(List<TreatProductCategoryPerformanceEntity> treatProductList);

    void createTreatProjectCategoryPerformanceEntity(List<TreatProjectCategoryPerformanceEntity> treatProjectList);

    void createTreatGeneralCardCategoryPerformanceEntity(List<TreatGoodCategoryPerformanceEntity> treatProjectList);

    void createTreatTimeCardCategoryPerformanceEntity(List<TreatGoodCategoryPerformanceEntity> treatProjectList);

    void createTreatSavingCardCategoryPerformanceEntity(List<TreatGoodCategoryPerformanceEntity> treatProjectList);

    void updatePerformanceScheme(PerformanceSchemeEntity performanceSchemeEntity);

    void deleteProductCategory(@Param("ID") Integer ID);

    void deleteProjectCategory(@Param("ID") Integer ID);

    void deleteGeneralCardCategory(@Param("ID") Integer ID);

    void deleteTimeCardCategory(@Param("ID") Integer ID);

    void deleteSavingCardCategory(@Param("ID") Integer ID);
    void deletePackageCardCategory(@Param("ID") Integer ID);

    void deleteTreatProductCategory(@Param("ID") Integer ID);

    void deleteTreatProjectCategory(@Param("ID") Integer ID);

    void deleteTreatGeneralCardCategory(@Param("ID") Integer ID);

    void deleteTreatSavingCardCategory(@Param("ID") Integer ID);

    void deleteTreatTimeCardCategory(@Param("ID") Integer ID);

    List<PerformanceSchemeGoodCategoryOutForm> getPerformanceSchemeProduct(@Param("ID") Integer ID);

    List<PerformanceSchemeGoodCategoryOutForm> getPerformanceSchemeProject(@Param("ID") Integer ID);

    List<PerformanceSchemeGoodCardCategoryOutForm> getPerformanceSchemeGeneralCard(@Param("ID") Integer ID);

    List<PerformanceSchemeGoodCardCategoryOutForm> getPerformanceSchemeTimeCard(@Param("ID") Integer ID);

    List<PerformanceSchemeSavingCardCategoryOutForm> getPerformanceSchemeSavingCard(@Param("ID") Integer ID);
    List<PerformanceSchemeGoodCardCategoryOutForm> getPerformanceSchemePackageCard(@Param("ID") Integer ID);

    List<PerformanceSchemeTreatGoodCategoryOutForm> getPerformanceSchemeTreatProduct(@Param("ID") Integer ID);

    List<PerformanceSchemeTreatGoodCategoryOutForm> getPerformanceSchemeTreatProject(@Param("ID") Integer ID);

    List<PerformanceSchemeTreatGoodCategoryOutForm> getPerformanceSchemeTreatGeneralCard(@Param("ID") Integer ID);

    List<PerformanceSchemeTreatGoodCategoryOutForm> getPerformanceSchemeTreatTimeCard(@Param("ID") Integer ID);

    List<PerformanceSchemeTreatGoodCategoryOutForm> getPerformanceSchemeTreatSavingCard(@Param("ID") Integer ID);

    Integer getPerformanceSchemeCount(@Param("ID") Integer ID);

    List<PerformanceSchemeGoodPackageCardCategoryOutForm> getProductPerformancePackageCardCategory(@Param("ID") Integer ID);
    List<PerformanceSchemeGoodPackageCardCategoryOutForm> getProjectPerformancePackageCardCategory(@Param("ID") Integer ID);
    List<PerformanceSchemeGoodPackageCardCategoryOutForm> getGeneralCardPerformancePackageCardCategory(@Param("ID") Integer ID);
    List<PerformanceSchemeGoodPackageCardCategoryOutForm> getTimeCardPerformancePackageCardCategory(@Param("ID") Integer ID);
    List<PerformanceSchemeGoodPackageCardCategoryOutForm> getSavingCardPerformancePackageCardCategory(@Param("ID") Integer ID);

    void deleteProductPackageCardCategory(Integer ID);

    void deleteProjectPackageCardCategory(Integer ID);

    void deleteGeneralCardPackageCardCategory(Integer ID);

    void deleteTimeCardPackageCardCategory(Integer ID);

    void deleteSavingCardPackageCardCategory(Integer ID);

    void deleteProductPackageCard(Integer ID);

    void deleteProjectPackageCard(Integer ID);

    void deleteGeneralCardPackageCard(Integer ID);

    void deleteTimeCardPackageCard(Integer ID);

    void deleteSavingCardPackageCard(Integer ID);

    void createProductPerformancePackageCardCategoryEntity(List<SaleGoodPerformancePackageCardCategoryEntity> list);
    void createProjectPerformancePackageCardCategoryEntity(List<SaleGoodPerformancePackageCardCategoryEntity> list);
    void createGeneralCardPerformancePackageCardCategoryEntity(List<SaleGoodPerformancePackageCardCategoryEntity> list);
    void createTimeCardPerformancePackageCardCategoryEntity(List<SaleGoodPerformancePackageCardCategoryEntity> list);
    void createSavingCardPerformancePackageCardCategoryEntity(List<SaleGoodPerformancePackageCardCategoryEntity> list);

    void createProductPerformancePackageCardEntity(List<SaleGoodPerformancePackageCardEntity> list);
    void createProjectPerformancePackageCardEntity(List<SaleGoodPerformancePackageCardEntity> list);
    void createGeneralCardPerformancePackageCardEntity(List<SaleGoodPerformancePackageCardEntity> list);
    void createTimeCardPerformancePackageCardEntity(List<SaleGoodPerformancePackageCardEntity> list);
    void createSavingCardPerformancePackageCardEntity(List<SaleGoodPerformancePackageCardEntity> list);

}
