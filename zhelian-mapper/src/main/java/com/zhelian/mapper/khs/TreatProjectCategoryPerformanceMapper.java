package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TBTreatProjectCategoryPerformanceEntity;
import com.zhelian.model.khs.entity.TreatProjectPerformanceSchemeEntity;
import com.zhelian.model.khs.form.output.TreatGoodCategoryOutputForm;
import com.zhelian.model.khs.form.output.TreatGoodCategoryPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatProjectCategoryPerformanceMapper {

    TreatGoodCategoryOutputForm treatProjectCategoryPerformance(@Param("EntityID") Integer EntityID);

    List<TreatGoodCategoryPerformanceOutputForm> allTreatProjectCategoryPerformance(@Param("EntityID") Integer EntityID);

    void updateTreatProjectPerformanceScheme(TreatProjectPerformanceSchemeEntity treatProjectPerformanceSchemeEntity);

    void insertTreatProjectCategoryPerformanceEntity(List<TBTreatProjectCategoryPerformanceEntity> list);

    void deleteTreatProjectCategoryPerformanceEntity(@Param("EntityID") Integer EntityID);
}
