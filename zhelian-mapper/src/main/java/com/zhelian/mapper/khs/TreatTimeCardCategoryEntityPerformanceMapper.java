package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TreatGoodCategoryEntityPerformanceEntity;
import com.zhelian.model.khs.entity.TreatGoodEntityPerformanceSchemeEntity;
import com.zhelian.model.khs.form.output.TreatCardGoodCategoryOutputForm;
import com.zhelian.model.khs.form.output.TreatGoodCategoryPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatTimeCardCategoryEntityPerformanceMapper {

    TreatCardGoodCategoryOutputForm treatTimeCardCategoryEntityPerformance(@Param("EntityID") Integer EntityID);

    List<TreatGoodCategoryPerformanceOutputForm> allTreatTimeCardCategoryEntityPerformance(@Param("EntityID") Integer EntityID);

    void updateTreatTimeCardEntityPerformanceScheme(TreatGoodEntityPerformanceSchemeEntity treatGoodEntityPerformanceSchemeEntity);

    void insertTreatTimeCardCategoryEntityPerformanceEntity(List<TreatGoodCategoryEntityPerformanceEntity> list);

    void deleteTreatTimeCardCategoryEntityPerformanceEntity(@Param("EntityID") Integer EntityID);
}
