package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.SaleSavingCardPerformanceEntity;
import com.zhelian.model.khs.form.output.PerformanceSchemeSavingCarOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface PerformanceSchemeSavingCardMapper {

    List<PerformanceSchemeSavingCarOutForm> getAllPerformanceSchemeSavingCard(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);

    void deleteSaleSavingCardPerformance(Map<String, Object> map);

    void insertSaleSavingCardPerformance(List<SaleSavingCardPerformanceEntity> list);

    void deleteSaleSavingCardPerformanceByPerformanceSchemeIDAndCategory(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);
}
