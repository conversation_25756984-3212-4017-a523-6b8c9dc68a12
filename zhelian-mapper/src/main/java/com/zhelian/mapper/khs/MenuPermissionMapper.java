package com.zhelian.mapper.khs;

import com.zhelian.model.khs.entity.MenuPermissionEntity;
import com.zhelian.model.khs.form.output.EmployeeMenuPermissionOutputForm;
import com.zhelian.model.khs.form.output.EmployeeMimiProgramMenuPermissionOutputForm;
import com.zhelian.model.khs.form.output.MenuPermissionOutputForm;
import com.zhelian.model.khs.form.output.RouterOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MenuPermissionMapper {
    List<MenuPermissionOutputForm> getMenuPermissionByType(String type);

    List<EmployeeMenuPermissionOutputForm> getEmployeeMenuPermissionByType(@Param("Type") String type, @Param("EmployeeID") String EmployeeID);

    List<EmployeeMimiProgramMenuPermissionOutputForm> getEmployeeMiniprogramMenuPermissionByType(@Param("Type") String type, @Param("EmployeeID") String EmployeeID);

    List<MenuPermissionEntity> getTopMenuByEmployeeIDAndMenuType(@Param("EmployeeID") String EmployeeID, @Param("MenuType") String MenuType);

    List<RouterOutputForm> getEmployeeRouterByByEmployeeID(@Param("Type") String type, @Param("EmployeeID") String EmployeeID);
}
