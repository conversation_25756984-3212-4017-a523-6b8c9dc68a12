package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TreatGoodPerformanceEntity;
import com.zhelian.model.khs.form.output.PerformanceSchemeTreatSavingCardGoodOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PerformanceSchemeTreatSavingCardMapper {

    List<PerformanceSchemeTreatSavingCardGoodOutForm> getAllPerformanceSchemeSavingCard(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);

    void insertTreatSavingCardPerformance(List<TreatGoodPerformanceEntity> list);

    void deleteTreatSavingCardPerformanceByPerformanceSchemeIDAndCategory(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);
}
