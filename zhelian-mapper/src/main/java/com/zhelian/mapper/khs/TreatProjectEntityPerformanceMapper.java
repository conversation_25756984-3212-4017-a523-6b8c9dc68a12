package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TreatGoodEntityPerformanceEntity;
import com.zhelian.model.khs.form.output.TreatGoodPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface TreatProjectEntityPerformanceMapper {

    List<TreatGoodPerformanceOutputForm> allTreatProjectEntityPerformance(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertTreatProjectEntityPerformance(List<TreatGoodEntityPerformanceEntity> list);

    void deleteTreatProjectEntityPerformance(Map<String, Object> map);

    void deleteTreatProjectEntityPerformanceByEntity(Integer EntityID);

    void deleteTreatProjectEntityPerformanceByEntityAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);
}
