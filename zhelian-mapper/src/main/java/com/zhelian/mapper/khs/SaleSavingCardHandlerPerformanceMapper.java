package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.SaleSavingCardHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SaleSavingCardHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleSavingCardCategoryHandlerPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleSavingCardHandlerPerformanceMapper {

    List<SaleSavingCardCategoryHandlerPerformanceOutputForm> allSaleSavingCardHandlerPerformance(@Param("EntityID") Integer EntityID, @Param("SavingCardID") Integer SavingCardID);

    void deleteSaleSavingCardHandler(@Param("EntityID") Integer EntityID, @Param("SavingCardID") Integer SavingCardID);

    void deleteSaleSavingCardHandlerJobType(@Param("EntityID") Integer EntityID, @Param("SavingCardID") Integer SavingCardID);

    void updateSaleSavingCardHandlerPerformance(List<SaleSavingCardHandlerPerformanceEntity> list);

    void insertSaleSavingCardHandlerJobTypePerformance(List<SaleSavingCardHandlerJobTypePerformanceEntity> list);

    void deleteSaleSavingCardHandlerPerformanceByEntity(@Param("EntityID") Integer EntityID);

    void deleteSaleSavingCardHandlerJobTypePerformanceByEntity(@Param("EntityID") Integer EntityID);
}
