package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TreatTimeCardPerformanceEntity;
import com.zhelian.model.khs.form.output.TreatCardPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatTimeCardPerformanceMapper {

    List<TreatCardPerformanceOutputForm> allTreatTimeCardPerformance(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertTreatTimeCardPerformance(List<TreatTimeCardPerformanceEntity> list);

    void deleteTreatTimeCardPerformanceByEntityIDAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void deleteTreatTimeCardPerformanceByEntityID(@Param("EntityID") Integer EntityID);
}
