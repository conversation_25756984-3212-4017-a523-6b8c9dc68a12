package com.zhelian.mapper.khs;


import com.zhelian.model.ibeauty.form.output.TreatProductCommissionSchemeOutputForm;
import com.zhelian.model.khs.entity.TreatGoodEntityPerformanceSchemeEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatGeneralCardEntityPerformanceSchemeMapper {

    List<TreatProductCommissionSchemeOutputForm> treatGeneralCardEntityPerformanceSchemeList(@Param("Name") String Name);

    void createTreatGeneralCardEntityPerformanceScheme(TreatGoodEntityPerformanceSchemeEntity TreatGeneralCardEntityEntityPerformanceSchemeEntity);

    Integer getTreatGeneralCardEntityPerformanceScheme(@Param("EntityID") Integer EntityID);

    void deleteTreatGeneralCardEntityPerformanceScheme(@Param("EntityID") Integer EntityID);


}
