package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.SaleProjectPerformanceEntity;
import com.zhelian.model.khs.form.output.PerformanceSchemeGoodOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ChannelPerformanceSchemeProjectMapper {

    List<PerformanceSchemeGoodOutForm> getAllPerformanceSchemeProject(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);

    void insertSaleProjectPerformance(List<SaleProjectPerformanceEntity> list);

    void deleteSaleProjectPerformanceByPerformanceSchemeIDAndCategory(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);
}
