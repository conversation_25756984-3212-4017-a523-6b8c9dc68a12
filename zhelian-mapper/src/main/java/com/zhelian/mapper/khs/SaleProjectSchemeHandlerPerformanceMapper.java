package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.SaleProjectSchemeHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SaleProjectSchemeHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleGoodCategoryHandlerPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleProjectSchemeHandlerPerformanceMapper {

    List<SaleGoodCategoryHandlerPerformanceOutputForm> allSaleProjectSchemeHandlerPerformance(@Param("EntityID") Integer EntityID);

    void deleteSaleProjectSchemeHandler(@Param("EntityID") Integer EntityID);

    void deleteSaleProjectSchemeHandlerJobType(@Param("EntityID") Integer EntityID);

    void updateSaleProjectSchemeHandlerPerformance(List<SaleProjectSchemeHandlerPerformanceEntity> list);

    void insertSaleProjectSchemeHandlerJobTypePerformance(List<SaleProjectSchemeHandlerJobTypePerformanceEntity> list);
}
