package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TreatGoodProjectEntityPerformanceEntity;
import com.zhelian.model.khs.form.output.TreatGoodPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatTimeCardProjectEntityPerformanceMapper {

    List<TreatGoodPerformanceOutputForm> allTreatTimeCardProjectEntityPerformance(@Param("EntityID") Integer EntityID, @Param("TimeCardID") Integer TimeCardID);

    void insertTreatTimeCardProjectEntityPerformance(List<TreatGoodProjectEntityPerformanceEntity> list);

    void deleteTreatTimeCardProjectEntityPerformanceByEntityIDAndCard(@Param("EntityID") Integer EntityID, @Param("TimeCardID") Integer TimeCardID);

    void deleteTreatTimeCardProjectEntityPerformanceByEntityID(@Param("EntityID") Integer EntityID);
}
