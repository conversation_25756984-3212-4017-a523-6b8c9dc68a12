package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TreatProjectHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.TreatProjectHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.TreatGoodCategoryHandlerPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatProjectHandlerPerformanceMapper {

    List<TreatGoodCategoryHandlerPerformanceOutputForm> allTreatProjectHandlerPerformance(@Param("EntityID") Integer EntityID, @Param("ProjectID") Integer ProjectID);

    void deleteTreatProjectHandler(@Param("EntityID") Integer EntityID, @Param("ProjectID") Integer ProjectID);

    void deleteTreatProjectHandlerJobType(@Param("EntityID") Integer EntityID, @Param("ProjectID") Integer ProjectID);

    void updateTreatProjectHandlerPerformance(List<TreatProjectHandlerPerformanceEntity> list);

    void insertTreatProjectHandlerJobTypePerformance(List<TreatProjectHandlerJobTypePerformanceEntity> list);

    void deleteTreatProjectHandlerPerformanceByEntity(Integer EntityID);

    void deleteTreatProjectHandlerJobTypePerformanceByEntity(Integer EntityID);
}
