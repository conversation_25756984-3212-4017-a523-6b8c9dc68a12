package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.SaleGoodPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleGoodPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface SaleProductEntityPerformanceMapper {

    List<SaleGoodPerformanceOutputForm> allSaleProductEntityPerformance(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertSaleProductEntityPerformance(List<SaleGoodPerformanceEntity> list);

    void deleteSaleProductEntityPerformance(Map<String, Object> map);

    void deleteSaleProductEntityPerformanceByEntityID(@Param("EntityID") Integer EntityID);

    void deleteSaleProductEntityPerformanceByEntityIDAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);
}
