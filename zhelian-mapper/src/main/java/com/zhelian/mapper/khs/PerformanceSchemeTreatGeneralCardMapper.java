package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TreatGoodPerformanceEntity;
import com.zhelian.model.khs.form.output.PerformanceSchemeTreatGoodOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PerformanceSchemeTreatGeneralCardMapper {

    List<PerformanceSchemeTreatGoodOutForm> getAllPerformanceSchemeGeneralCard(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);

    void insertTreatGeneralCardPerformance(List<TreatGoodPerformanceEntity> list);

    void deleteTreatGeneralCardPerformanceByPerformanceSchemeIDAndCategory(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);
}
