package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.EntityCommissionEmployeeEntity;
import com.zhelian.model.khs.entity.EntityCommissionEmployeeEntityEntity;
import com.zhelian.model.khs.entity.EntityCommissionEntity;
import com.zhelian.model.khs.entity.EntityCommissionSchemeEntity;
import com.zhelian.model.khs.form.output.EmployeeCommissionOutForm;
import com.zhelian.model.khs.form.output.EntityCalculateEmployeeOutForm;
import com.zhelian.model.khs.form.output.EntityCommissionSchemeOutForm;
import com.zhelian.model.khs.form.output.EntityEmployeeOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface EntityCommissionSchemeMapper {


    List<EntityCommissionSchemeOutForm> getEntityCommissionScheme(@Param("Name") String Name, @Param("PerformanceEvaluationSchemeID") Integer PerformanceEvaluationSchemeID, @Param("PerformanceCalculationSchemeID") Integer PerformanceCalculationSchemeID, @Param("Active") Boolean Active);


    void createEntityCommissionScheme(EntityCommissionSchemeEntity entityCommissionSchemeEntity);

    void createEntityCommissionEmployee(EntityCommissionEmployeeEntity employeeCommissionEntity);

    void createEnitityCommission(List<EntityCommissionEntity> list);

    void createEmployeeCommissionEntity(List<EntityCommissionEmployeeEntityEntity> list);

    void updateEntityCommissionScheme(EntityCommissionSchemeEntity entityCommissionSchemeEntity);

    void deleteCommission(@Param("ID") Integer ID);

    void deleteCommissionEmployee(@Param("ID") Integer ID);

    void deleteCommissionEmployeeEntity(@Param("ID") Integer ID);

    List<EmployeeCommissionOutForm> getEntityCommission(@Param("ID") Integer ID);

    List<EntityEmployeeOutForm> allEmployee(@Param("Name") String Name, @Param("JobID") Integer JobID, @Param("State") Boolean State);

    List<EntityCalculateEmployeeOutForm> employee(@Param("ID") Integer ID);

    List<Integer> employeeCalculateEntity(@Param("ID") Integer ID);
}
