package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TreatGeneralCardPerformanceEntity;
import com.zhelian.model.khs.form.output.TreatCardPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatGeneralCardPerformanceMapper {

    List<TreatCardPerformanceOutputForm> allTreatGeneralCardPerformance(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertTreatGeneralCardPerformance(List<TreatGeneralCardPerformanceEntity> list);

    void deleteTreatGeneralCardPerformanceByEntityIDAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void deleteTreatGeneralCardPerformanceByEntityID(@Param("EntityID") Integer EntityID);
}
