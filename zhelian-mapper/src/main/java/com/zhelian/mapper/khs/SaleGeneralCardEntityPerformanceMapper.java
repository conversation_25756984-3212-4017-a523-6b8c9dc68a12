package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.SaleGoodPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleGoodPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface SaleGeneralCardEntityPerformanceMapper {

    List<SaleGoodPerformanceOutputForm> allSaleGeneralCardEntityPerformance(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertSaleGeneralCardEntityPerformance(List<SaleGoodPerformanceEntity> list);

    void deleteSaleGeneralCardEntityPerformance(Map<String, Object> map);

    void deleteSaleGeneralCardEntityPerformanceByEntityID(@Param("EntityID") Integer EntityID);

    void deleteSaleGeneralCardEntityPerformanceByEntityIDAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);
}
