package com.zhelian.mapper.khs;

import com.zhelian.model.khs.entity.TreatChannelGoodPerformanceEntity;
import com.zhelian.model.khs.form.output.PerformanceSchemeTreatGoodOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ChannelEmployeePerformanceSchemeTreatGeneralCardMapper {

    void insertTreatChannelGeneralCardPerformance(List<TreatChannelGoodPerformanceEntity> list);

    List<PerformanceSchemeTreatGoodOutForm> getAllPerformanceSchemeGeneralCard(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);

    void deleteTreatGeneralCardPerformanceByPerformanceSchemeIDAndCategory(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);

}
