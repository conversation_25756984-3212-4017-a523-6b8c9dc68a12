package com.zhelian.mapper.khs;


import com.zhelian.model.ibeauty.form.output.TreatProjectCommissionSchemeOutputForm;
import com.zhelian.model.khs.entity.TreatTimeCardPerformanceSchemeEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatTimeCardPerformanceSchemeMapper {

    List<TreatProjectCommissionSchemeOutputForm> treatTimeCardPerformanceSchemeList(@Param("Name") String Name);

    void createTreatTimeCardPerformanceScheme(TreatTimeCardPerformanceSchemeEntity treatTimeCardProjectPerformanceSchemeEntity);

    Integer getTreatTimeCardPerformanceScheme(@Param("EntityID") Integer EntityID);

    void deleteTreatTimeCardPerformanceScheme(@Param("EntityID") Integer EntityID);

}
