package com.zhelian.mapper.khs;

import com.zhelian.model.ibeauty.form.output.JobTypeOutputForm;
import com.zhelian.model.khs.entity.ChannelEmployeeCommissionEntity;
import com.zhelian.model.khs.entity.ChannelEmployeeCommissionRangeEntity;
import com.zhelian.model.khs.entity.ChannelEmployeeCommissionSchemeEntity;
import com.zhelian.model.khs.form.output.ChannelCommissionSchemeOutForm;
import com.zhelian.model.khs.form.output.EmployeeCommissionOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ChannelEmployeeCommissionSchemeMapper {

    List<ChannelCommissionSchemeOutForm> getChannelEmployeeCommissionScheme(@Param("Name") String Name, @Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("Active") Boolean Active);

    void createChannelEmployeeCommissionScheme(ChannelEmployeeCommissionSchemeEntity channelCommissionSchemeEntity);

    void createChannelEmployeeCommission(List<ChannelEmployeeCommissionEntity> list);

    void createChannelEmployeeCommissionEntities(List<ChannelEmployeeCommissionRangeEntity> list);

    void updateChannelEmployeeCommissionScheme(ChannelEmployeeCommissionSchemeEntity channelCommissionSchemeEntity);

    void deleteChannelEmployeeCommission(@Param("ID") Integer ID);

    void deleteChannelEmployeeCommissionEntity(@Param("ID") Integer ID);

    List<EmployeeCommissionOutForm> getChannelEmployeeCommission(@Param("ID") Integer ID);

    List<JobTypeOutputForm> getChannelEmployeeCommissionJobTypeRange(@Param("ID") Integer ID);
}
