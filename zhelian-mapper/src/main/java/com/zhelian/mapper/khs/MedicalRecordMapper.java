package com.zhelian.mapper.khs;

import com.zhelian.model.khs.entity.MedicalRecordCatalogEntity;
import com.zhelian.model.khs.entity.MedicalRecordTemplateEntity;
import com.zhelian.model.khs.form.output.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MedicalRecordMapper {

    List<MedicalRecordOutForm> list(@Param("Name") String Name, @Param("CategoryID") Integer CategoryID);

    Integer getCatalogMaxSequence();

    void createCatalog(MedicalRecordCatalogEntity entity);

    List<MedicalRecordTemplateEntity> getMedicalRecordTemplateByCatalogID(@Param("CatalogID") Integer CatalogID);

    void updateCatalog(MedicalRecordCatalogEntity entity);

    void deleteCatalog(@Param("ID") Integer ID);

    MedicalRecordCatalogEntity getMedicalRecordCatalogByID(@Param("ID") Integer ID);

    List<MedicalRecordCatalogEntity> medicalRecordCatalogEntityListByIDAndCategoryID(@Param("CategoryID") Integer CategoryID, @Param("ID") Integer ID);

    void updateCatalogSequence(List<MedicalRecordCatalogEntity> list);

    Integer getTemplateMaxSequence();

    MedicalRecordTemplateOutForm getTemplate(@Param("ID") Integer ID);

    void createTemplate(MedicalRecordTemplateEntity entity);

    void updateTemplate(MedicalRecordTemplateEntity entity);

    void deleteTemplate(@Param("ID") Integer ID);

    MedicalRecordTemplateEntity getMedicalRecordTemplateByID(@Param("ID") Integer ID);

    List<MedicalRecordTemplateEntity> medicalRecordTemplateEntityListByIDAndCatalogID(@Param("CatalogID") Integer CatalogID, @Param("ID") Integer ID);

    void updateTemplateSequence(List<MedicalRecordTemplateEntity> list);

    List<MedicalRecordCategoryAllOutForm> categoryAll();

    List<MedicalRecordLabelContentAllOutForm> labelContentAll(@Param("CategoryID") Integer CategoryID);
}
