package com.zhelian.mapper.khs;

import com.zhelian.model.khs.entity.MedicalRecordEntryContentEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface MedicalRecordEntryContentMapper {

    void deleteByLabelID(@Param("LabelID") Integer LabelID);

    void create(MedicalRecordEntryContentEntity entity);

    void update(MedicalRecordEntryContentEntity entity);

    void delete(@Param("ID") Integer ID);
}
