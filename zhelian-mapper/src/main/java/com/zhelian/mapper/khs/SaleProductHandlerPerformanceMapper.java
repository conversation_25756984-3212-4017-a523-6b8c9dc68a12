package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.SaleProductHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SaleProductHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleGoodCategoryHandlerPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleProductHandlerPerformanceMapper {

    List<SaleGoodCategoryHandlerPerformanceOutputForm> allSaleProductHandlerPerformance(@Param("EntityID") Integer EntityID, @Param("ProductID") Integer ProductID);

    void deleteSaleProductHandler(@Param("EntityID") Integer EntityID, @Param("ProductID") Integer ProductID);

    void deleteSaleProductHandlerJobType(@Param("EntityID") Integer EntityID, @Param("ProductID") Integer ProductID);

    void updateSaleProductHandlerPerformance(List<SaleProductHandlerPerformanceEntity> list);

    void insertSaleProductHandlerJobTypePerformance(List<SaleProductHandlerJobTypePerformanceEntity> list);

    void deleteSaleProductHandlerPerformanceByEntityID(@Param("EntityID") Integer EntityID);

    void deleteSaleProductHandlerJobTypePerformanceByEntityID(@Param("EntityID") Integer EntityID);
}
