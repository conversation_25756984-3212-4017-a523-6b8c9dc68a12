package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.SaleGoodPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleGoodPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface SaleProjectEntityPerformanceMapper {

    List<SaleGoodPerformanceOutputForm> allSaleProjectEntityPerformance(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertSaleProjectEntityPerformance(List<SaleGoodPerformanceEntity> list);

    void deleteSaleProjectEntityPerformance(Map<String, Object> map);

    void deleteSaleProjectEntityPerformanceByEntityID(@Param("EntityID") Integer EntityID);

    void deleteSaleProjectEntityPerformanceByEntityIDAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);
}
