package com.zhelian.mapper.khs;


import com.zhelian.model.ibeauty.form.output.SaleProductCommissionSchemeOutputForm;
import com.zhelian.model.khs.entity.SaleProductPerformanceSchemeEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleProductPerformanceSchemeMapper {

    List<SaleProductCommissionSchemeOutputForm> saleProductPerformanceSchemeList(@Param("Name") String Name);

    void createSaleProductPerformanceScheme(SaleProductPerformanceSchemeEntity saleProductPerformanceSchemeEntity);

    void deleteSaleProductPerformanceScheme(@Param("EntityID") Integer EntityID);

    Integer getSaleProductPerformanceScheme(@Param("EntityID") Integer EntityID);

}
