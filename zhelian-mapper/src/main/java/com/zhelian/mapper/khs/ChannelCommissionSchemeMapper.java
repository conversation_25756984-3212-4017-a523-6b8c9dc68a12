package com.zhelian.mapper.khs;

import com.zhelian.model.khs.entity.ChannelCommissionEntity;
import com.zhelian.model.khs.entity.ChannelCommissionRangeEntity;
import com.zhelian.model.khs.entity.ChannelCommissionSchemeEntity;
import com.zhelian.model.khs.form.output.ChannelCommissionSchemeOutForm;
import com.zhelian.model.khs.form.output.EmployeeCommissionOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface ChannelCommissionSchemeMapper {


    List<ChannelCommissionSchemeOutForm> getChannelCommissionScheme(@Param("Name") String Name, @Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("Active") Boolean Active);

    void createChannelCommissionScheme(ChannelCommissionSchemeEntity channelCommissionSchemeEntity);

    void createChannelCommission(List<ChannelCommissionEntity> list);

    void createChannelCommissionEntities(List<ChannelCommissionRangeEntity> list);

    void updateChannelCommissionScheme(ChannelCommissionSchemeEntity channelCommissionSchemeEntity);

    void deleteChannelCommission(@Param("ID") Integer ID);

    void deleteChannelCommissionEntity(@Param("ID") Integer ID);

    List<EmployeeCommissionOutForm> getChannelCommission(@Param("ID") Integer ID);

    Integer[] getChannelCommissionRange(@Param("ID") Integer ID);

}
