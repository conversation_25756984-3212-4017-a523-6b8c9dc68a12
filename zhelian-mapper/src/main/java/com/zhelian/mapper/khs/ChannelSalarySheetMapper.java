package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.ChannelSalarySettlementIntervalEntity;
import com.zhelian.model.khs.form.output.ChannelSalarySheetDetailOutForm;
import com.zhelian.model.khs.form.output.ChannelSalarySheetOutExcelForm;
import com.zhelian.model.khs.form.output.ChannelSalarySheetOutForm;
import com.zhelian.model.khs.form.output.SalarySettlementIntervalOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface ChannelSalarySheetMapper {

    void createSettlementInterval(ChannelSalarySettlementIntervalEntity channelSalarySettlementIntervalEntity);

    List<SalarySettlementIntervalOutForm> allSettlementInterval();

    List<ChannelSalarySheetOutForm> all(@Param("Name") String Name, @Param("ChannelSalarySettlementIntervalID") Integer ChannelSalarySettlementIntervalID, @Param("ParentID") Integer ParentID);

    List<ChannelSalarySheetOutExcelForm> excelChannelSalary(@Param("Name") String Name, @Param("ChannelSalarySettlementIntervalID") Integer ChannelSalarySettlementIntervalID, @Param("ParentID") Integer ParentID);

    List<ChannelSalarySheetDetailOutForm> detail(@Param("ChannelID") Integer ChannelID, @Param("ChannelSalarySettlementIntervalID") Integer ChannelSalarySettlementIntervalID);

    void settlement(@Param("ID") Integer ID);
}
