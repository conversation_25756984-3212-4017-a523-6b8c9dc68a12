package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TBSaleTimeCardPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleTimeCardPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface SaleTimeCardPerformanceMapper {

    List<SaleTimeCardPerformanceOutputForm> allSaleTimeCardPerformance(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertSaleTimeCardPerformance(List<TBSaleTimeCardPerformanceEntity> list);

    void deleteSaleTimeCardPerformance(Map<String, Object> map);

    void deleteSaleTimeCardPerformanceByEntityID(Integer EntityID);

    void deleteSaleTimeCardPerformanceByEntityIDAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);
}
