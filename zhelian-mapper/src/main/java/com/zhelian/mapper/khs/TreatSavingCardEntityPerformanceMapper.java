package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TreatGoodEntityPerformanceEntity;
import com.zhelian.model.khs.form.output.TreatSavingCardPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface TreatSavingCardEntityPerformanceMapper {

    List<TreatSavingCardPerformanceOutputForm> allTreatSavingCardEntityPerformance(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertTreatSavingCardEntityPerformance(List<TreatGoodEntityPerformanceEntity> list);

    void deleteTreatSavingCardEntityPerformance(Map<String, Object> map);

    void deleteTreatSavingCardEntityPerformanceByEntity(Integer EntityID);

    void deleteTreatSavingCardEntityPerformanceByEntityAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);
}
