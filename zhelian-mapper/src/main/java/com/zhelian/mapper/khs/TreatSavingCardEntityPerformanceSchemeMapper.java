package com.zhelian.mapper.khs;


import com.zhelian.model.ibeauty.form.output.TreatProductCommissionSchemeOutputForm;
import com.zhelian.model.khs.entity.TreatGoodEntityPerformanceSchemeEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatSavingCardEntityPerformanceSchemeMapper {

    List<TreatProductCommissionSchemeOutputForm> treatSavingCardEntityPerformanceSchemeList(@Param("Name") String Name);

    void createTreatSavingCardEntityPerformanceScheme(TreatGoodEntityPerformanceSchemeEntity TreatSavingCardEntityEntityPerformanceSchemeEntity);

    Integer getTreatSavingCardEntityPerformanceScheme(@Param("EntityID") Integer EntityID);

    void deleteTreatSavingCardEntityPerformanceScheme(@Param("EntityID") Integer EntityID);


}
