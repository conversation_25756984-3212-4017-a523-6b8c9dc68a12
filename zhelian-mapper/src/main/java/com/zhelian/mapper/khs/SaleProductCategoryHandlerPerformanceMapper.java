package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.SaleProductCategoryHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SaleProductCategoryHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleGoodCategoryHandlerPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleProductCategoryHandlerPerformanceMapper {

    List<SaleGoodCategoryHandlerPerformanceOutputForm> allSaleProductCategoryHandlerPerformance(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void updateSaleProductCategoryHandlerPerformance(List<SaleProductCategoryHandlerPerformanceEntity> list);

    void insertSaleProductCategoryHandlerJobTypePerformance(List<SaleProductCategoryHandlerJobTypePerformanceEntity> list);

    void deleteSaleProductCategoryHandler(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void deleteSaleProductCategoryHandlerJobType(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void deleteSaleProductCategoryHandlerPerformanceByEntityID(@Param("EntityID") Integer EntityID);

    void deleteSaleProductCategoryHandlerJobTypePerformanceByEntityID(@Param("EntityID") Integer EntityID);
}
