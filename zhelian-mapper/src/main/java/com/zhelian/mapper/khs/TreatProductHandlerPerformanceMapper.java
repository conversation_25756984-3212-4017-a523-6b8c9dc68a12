package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TreatProductHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.TreatProductHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.TreatGoodCategoryHandlerPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatProductHandlerPerformanceMapper {

    List<TreatGoodCategoryHandlerPerformanceOutputForm> allTreatProductHandlerPerformance(@Param("EntityID") Integer EntityID, @Param("ProductID") Integer ProductID);

    void deleteTreatProductHandler(@Param("EntityID") Integer EntityID, @Param("ProductID") Integer ProductID);

    void deleteTreatProductHandlerJobType(@Param("EntityID") Integer EntityID, @Param("ProductID") Integer ProductID);

    void updateTreatProductHandlerPerformance(List<TreatProductHandlerPerformanceEntity> list);

    void insertTreatProductHandlerJobTypePerformance(List<TreatProductHandlerJobTypePerformanceEntity> list);

    void deleteTreatProductHandlerPerformanceByEntity(Integer EntityID);

    void deleteTreatProductHandlerJobTypePerformanceByEntity(Integer EntityID);
}
