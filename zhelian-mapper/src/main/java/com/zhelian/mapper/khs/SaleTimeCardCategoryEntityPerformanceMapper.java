package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.SaleGoodCategoryEntityPerformanceEntity;
import com.zhelian.model.khs.entity.SaleGoodEntityPerformanceSchemeEntity;
import com.zhelian.model.khs.form.output.SaleCardGoodCategoryOutputForm;
import com.zhelian.model.khs.form.output.SaleGoodCategoryEntityPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleTimeCardCategoryEntityPerformanceMapper {

    List<SaleGoodCategoryEntityPerformanceOutputForm> allSaleTimeCardCategoryEntityPerformance(@Param("EntityID") Integer EntityID);

    void updateSaleTimeCardEntityPerformanceScheme(SaleGoodEntityPerformanceSchemeEntity saleTimeCardEntityPerformanceSchemeEntity);

    void insertSaleTimeCardCategoryEntityPerformanceEntity(List<SaleGoodCategoryEntityPerformanceEntity> list);

    SaleCardGoodCategoryOutputForm saleTimeCardCategoryEntityPerformance(@Param("EntityID") Integer EntityID);

    void deleteSaleTimeCardCategoryEntityPerformanceEntity(@Param("EntityID") Integer EntityID);
}
