package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TreatProjectPerformanceEntity;
import com.zhelian.model.khs.form.output.PerformanceSchemeTreatGoodOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface PerformanceSchemeTreatProjectMapper {

    List<PerformanceSchemeTreatGoodOutForm> getAllPerformanceSchemeProject(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);

    void deleteTreatProjectPerformance(Map<String, Object> map);

    void insertTreatProjectPerformance(List<TreatProjectPerformanceEntity> list);

    void deleteTreatProjectPerformanceByPerformanceSchemeIDAndCategory(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);
}
