package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TreatGoodCategoryEntityPerformanceEntity;
import com.zhelian.model.khs.entity.TreatGoodEntityPerformanceSchemeEntity;
import com.zhelian.model.khs.form.output.TreatGoodCategoryOutputForm;
import com.zhelian.model.khs.form.output.TreatGoodCategoryPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatProductCategoryEntityPerformanceMapper {

    TreatGoodCategoryOutputForm treatProductCategoryEntityPerformance(@Param("EntityID") Integer EntityID);

    List<TreatGoodCategoryPerformanceOutputForm> allTreatProductCategoryEntityPerformance(@Param("EntityID") Integer EntityID);

    void updateTreatProductEntityPerformanceScheme(TreatGoodEntityPerformanceSchemeEntity treatGoodEntityPerformanceSchemeEntity);

    void insertTreatProductCategoryEntityPerformanceEntity(List<TreatGoodCategoryEntityPerformanceEntity> list);

    void deleteTreatProductCategoryEntityPerformanceEntity(@Param("EntityID") Integer EntityID);
}
