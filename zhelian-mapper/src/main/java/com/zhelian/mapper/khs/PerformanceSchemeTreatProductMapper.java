package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TreatProductPerformanceEntity;
import com.zhelian.model.khs.form.output.PerformanceSchemeTreatGoodOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface PerformanceSchemeTreatProductMapper {

    List<PerformanceSchemeTreatGoodOutForm> getAllPerformanceSchemeProduct(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);

    void deleteTreatProductPerformance(Map<String, Object> map);

    void insertTreatProductPerformance(List<TreatProductPerformanceEntity> list);

    void deleteTreatProductPerformanceByPerformanceSchemeIDAndCategory(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);
}
