package com.zhelian.mapper.khs;


import com.zhelian.model.khs.form.output.TreatGoodCategoryHandlerPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatTimeCardHandlerPerformanceMapper {

    List<TreatGoodCategoryHandlerPerformanceOutputForm> allTreatTimeCardHandlerPerformance(@Param("EntityID") Integer EntityID, @Param("TimeCardID") Integer TimeCardID);

    void deleteTreatTimeCardHandler(@Param("EntityID") Integer EntityID, @Param("TimeCardID") Integer TimeCardID);

    void deleteTreatTimeCardHandlerJobType(@Param("EntityID") Integer EntityID, @Param("TimeCardID") Integer TimeCardID);

    void insertTreatTimeCardHandlerPerformance(List list);

    void insertTreatTimeCardHandlerJobTypePerformance(List list);

    void deleteTreatTimeCardHandlerJobTypePerformanceByEntityID(Integer EntityID);

    void deleteTreatTimeCardHandlerPerformanceByEntityID(Integer EntityID);
}
