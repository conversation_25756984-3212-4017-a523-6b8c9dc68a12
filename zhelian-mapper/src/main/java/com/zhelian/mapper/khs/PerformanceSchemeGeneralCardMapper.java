package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.SaleGeneralCardPerformanceEntity;
import com.zhelian.model.khs.form.output.PerformanceSchemeGoodOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface PerformanceSchemeGeneralCardMapper {

    List<PerformanceSchemeGoodOutForm> getAllPerformanceSchemeGeneralCard(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);

    void deleteSaleGeneralCardPerformance(Map<String, Object> map);

    void insertSaleGeneralCardPerformance(List<SaleGeneralCardPerformanceEntity> list);

    void deleteSaleGeneralCardPerformanceByPerformanceSchemeIDAndCategory(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);
}
