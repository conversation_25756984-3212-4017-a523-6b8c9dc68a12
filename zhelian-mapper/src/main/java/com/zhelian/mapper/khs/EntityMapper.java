package com.zhelian.mapper.khs;

import com.alibaba.fastjson2.JSONObject;
import com.zhelian.model.ibeauty.entity.TMEntityPhotoEntity;
import com.zhelian.model.khs.entity.EntityEntity;
import com.zhelian.model.khs.entity.TMEntityEntity;
import com.zhelian.model.khs.form.input.EntityQueryForm;
import com.zhelian.model.khs.form.input.EntityUpdateForm;
import com.zhelian.model.khs.form.output.EntityListForm;
import com.zhelian.model.khs.form.output.OnlineEntityOutputForm;
import com.zhelian.model.khs.form.output.StoreListExcelForm;
import com.zhelian.model.khs.form.output.StoreListForm;
import com.zhelian.model.psi.form.output.EntityPrepayOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;

@Repository
public interface EntityMapper {

    List<EntityEntity> entityList(EntityQueryForm form);

    List<EntityPrepayOutputForm> warehouseList(String EmployeeID);

    List<EntityEntity> entityListByParentID(@Param("ParentID") Integer ParentID, @Param("ID") Integer ID, @Param("Active") Boolean Active);

    EntityEntity entityInfo(Integer ID);

    EntityEntity entityDetail(Integer ID);

    Integer entitySequenceByParentID(Integer ParentID);

    int addEntity(EntityEntity form);

    int addTMEntity(TMEntityEntity entityEntity);

    void updateEntity(EntityUpdateForm form);

    void updateTMEntity(TMEntityEntity form);

    int getIsStoreNum();

    void updateEntitySequence(List<EntityEntity> list);

    List<StoreListForm> getStoreList(@Param("EmployeeID") String EmployeeID);

    OnlineEntityOutputForm onlineEntityList(@Param("EntityID") Integer EntityID);

    void deleteEntityBusinessDateByID(@Param("ID") Integer ID);

    void insertEntityBusinessDate(List<JSONObject> list);

    void deleteEntityPhotoURLByID(@Param("ID") Integer ID);

    void insertEntityPhotoURL(List<JSONObject> list);

    TMEntityEntity tmEntityInfo(Integer EntityID);

    LinkedList<Integer> getASCList(@Param("ID") int ID, @Param("EntityID") int EntityID);

    void updateSequence(List<TMEntityPhotoEntity> list);

    Integer getEntityListOrDerByID(Integer ParentID);

    Integer getEntityListOrDerByID();

    List<StoreListForm> allEntity();

    List<StoreListForm> allWarehouse();

    List<StoreListExcelForm> excelEntity(@Param("Name") String Name);

    List<String> getEmployeeLoginEntityByEntity(Integer ID);

    List<EntityListForm> getEntityList(@Param("EmployeeID") String EmployeeID);

    List<StoreListForm> all(String Name);

    List<StoreListForm> getEmployeePermissionEntity(@Param("EmployeeID") String EmployeeID, @Param("Name") String Name);
}
