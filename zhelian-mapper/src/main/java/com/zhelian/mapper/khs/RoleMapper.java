package com.zhelian.mapper.khs;

import com.zhelian.model.khs.entity.RoleEntity;
import com.zhelian.model.khs.entity.RoleMenuPermissionEntity;
import com.zhelian.model.khs.form.input.RoleMenuPermissionQueryForm;
import com.zhelian.model.khs.form.input.RoleUpdateForm;
import com.zhelian.model.khs.form.output.RoleMenuPermissionOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RoleMapper {

    List<RoleEntity> roleList(@Param("RoleName") String RoleName);

    void addRole(RoleEntity form);

    void updateRole(RoleUpdateForm form);

    void deleteRoleMenuPermissionByID(Integer ID);

    void insertRoleMenuPermissionList(List<RoleMenuPermissionEntity> list);

    List<RoleMenuPermissionOutputForm> getRoleMenuPermissionByType(RoleMenuPermissionQueryForm form);

}
