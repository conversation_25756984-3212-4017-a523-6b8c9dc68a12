package com.zhelian.mapper.khs;


import com.zhelian.model.ibeauty.form.output.SaleProductCommissionSchemeOutputForm;
import com.zhelian.model.khs.entity.SaleProjectPerformanceSchemeEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleProjectPerformanceSchemeMapper {

    List<SaleProductCommissionSchemeOutputForm> saleProjectPerformanceSchemeList(@Param("Name") String Name);

    void createSaleProjectPerformanceScheme(SaleProjectPerformanceSchemeEntity saleProjectPerformanceSchemeEntity);

    Integer getSaleProjectPerformanceScheme(@Param("EntityID") Integer EntityID);

    void deleteSaleProjectPerformanceScheme(@Param("EntityID") Integer EntityID);

}
