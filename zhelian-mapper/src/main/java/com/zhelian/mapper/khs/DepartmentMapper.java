package com.zhelian.mapper.khs;

import com.zhelian.model.khs.entity.DepartmentEntity;
import com.zhelian.model.khs.entity.DepartmentEntityEntity;
import com.zhelian.model.khs.form.output.DepartmentOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;

@Repository
public interface DepartmentMapper {

    List<DepartmentOutForm> all(@Param("Name") String Name,@Param("Active") Boolean Active);

    Integer getMaxSequence();

    void create(DepartmentEntity entity);

    void saveEntity(List<DepartmentEntityEntity> list);

    void update(DepartmentEntity entity);

    void deleteEntity(@Param("ID") Integer ID);

    LinkedList<Integer> getASCList(@Param("ID") Integer ID);

    void updateSequence(List<DepartmentEntity> list);
}
