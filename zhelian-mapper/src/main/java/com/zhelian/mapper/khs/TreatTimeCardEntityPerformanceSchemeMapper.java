package com.zhelian.mapper.khs;


import com.zhelian.model.ibeauty.form.output.TreatProductCommissionSchemeOutputForm;
import com.zhelian.model.khs.entity.TreatGoodEntityPerformanceSchemeEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatTimeCardEntityPerformanceSchemeMapper {

    List<TreatProductCommissionSchemeOutputForm> treatTimeCardEntityPerformanceSchemeList(@Param("Name") String Name);

    void createTreatTimeCardEntityPerformanceScheme(TreatGoodEntityPerformanceSchemeEntity TreatTimeCardEntityEntityPerformanceSchemeEntity);

    Integer getTreatTimeCardEntityPerformanceScheme(@Param("EntityID") Integer EntityID);

    void deleteTreatTimeCardEntityPerformanceScheme(@Param("EntityID") Integer EntityID);


}
