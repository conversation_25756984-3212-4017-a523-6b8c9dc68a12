package com.zhelian.mapper.khs;

import com.zhelian.model.khs.entity.TBSaleGeneralCardPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleGeneralCardPerfromanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface SaleGeneralCardPerformanceMapper {

    List<SaleGeneralCardPerfromanceOutputForm> allSaleGeneralCardPerformance(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertSaleGeneralCardPerformance(List<TBSaleGeneralCardPerformanceEntity> list);

    void deleteSaleGeneralCardPerformance(Map<String, Object> map);

    void deleteSaleGeneralCardPerformanceByEntityID(Integer EntityID);

    void deleteSaleGeneralCardPerformanceByEntityIDAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);
}
