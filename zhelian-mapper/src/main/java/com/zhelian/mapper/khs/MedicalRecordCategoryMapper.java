package com.zhelian.mapper.khs;

import com.zhelian.model.khs.entity.MedicalRecordCategoryEntity;
import com.zhelian.model.khs.entity.MedicalRecordTemplateEntity;
import com.zhelian.model.khs.form.output.MedicalRecordCategoryOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;

@Repository
public interface MedicalRecordCategoryMapper {

    List<MedicalRecordCategoryOutForm> list(@Param("Name") String Name,@Param("Active") Boolean Active);

    Integer getMaxSequence();

    void create(MedicalRecordCategoryEntity entity);

    List<MedicalRecordTemplateEntity> getMedicalRecordTemplateByCategoryID(@Param("CategoryID") Integer CategoryID);

    void update(MedicalRecordCategoryEntity entity);

    LinkedList<Integer> getASCList(@Param("ID") Integer ID);

    void updateSequence(List<MedicalRecordCategoryEntity> list);

    MedicalRecordCategoryEntity getMedicalRecordCategoryByID(@Param("ID") Integer ID);
}
