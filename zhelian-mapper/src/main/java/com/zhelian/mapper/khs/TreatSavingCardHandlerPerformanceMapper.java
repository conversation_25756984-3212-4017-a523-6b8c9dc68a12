package com.zhelian.mapper.khs;


import com.zhelian.model.ibeauty.form.output.TreatSavingCardProjectHandlerCommissionOutputForm;
import com.zhelian.model.khs.entity.TreatSavingCardHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.TreatSavingCardHandlerPerformanceEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatSavingCardHandlerPerformanceMapper {

    List<TreatSavingCardProjectHandlerCommissionOutputForm> allTreatSavingCardHandlerPerformance(@Param("EntityID") Integer EntityID, @Param("SavingCardID") Integer SavingCardID);

    void deleteTreatSavingCardHandler(@Param("EntityID") Integer EntityID, @Param("SavingCardID") Integer SavingCardID);

    void deleteTreatSavingCardHandlerJobType(@Param("EntityID") Integer EntityID, @Param("SavingCardID") Integer SavingCardID);

    void innsertTreatSavingCardHandlerPerformance(List<TreatSavingCardHandlerPerformanceEntity> list);

    void insertTreatSavingCardHandlerJobTypePerformance(List<TreatSavingCardHandlerJobTypePerformanceEntity> list);

    void deleteTreatHandlerPerformanceByEntity(Integer EntityID);

    void deleteTreatHandlerJobTypePerformanceByEntity(Integer EntityID);
}
