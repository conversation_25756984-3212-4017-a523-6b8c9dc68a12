package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.SaleGoodPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleGoodPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface SaleTimeCardEntityPerformanceMapper {

    List<SaleGoodPerformanceOutputForm> allSaleTimeCardEntityPerformance(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertSaleTimeCardEntityPerformance(List<SaleGoodPerformanceEntity> list);

    void deleteSaleTimeCardEntityPerformance(Map<String, Object> map);

    void deleteSaleTimeCardEntityPerformanceByEntityID(@Param("EntityID") Integer EntityID);

    void deleteSaleTimeCardEntityPerformanceByEntityIDAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);
}
