package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TreatGeneralCardSchemeHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.TreatGeneralCardSchemeHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.TreatGoodCategoryHandlerPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatGeneralCardSchemeHandlerPerformanceMapper {

    List<TreatGoodCategoryHandlerPerformanceOutputForm> allTreatGeneralCardSchemeHandlerPerformance(@Param("EntityID") Integer EntityID);

    void deleteTreatGeneralCardSchemeHandler(@Param("EntityID") Integer EntityID);

    void deleteTreatGeneralCardSchemeHandlerJobType(@Param("EntityID") Integer EntityID);

    void updateTreatGeneralCardSchemeHandlerPerformance(List<TreatGeneralCardSchemeHandlerPerformanceEntity> list);

    void insertTreatGeneralCardSchemeHandlerJobTypePerformance(List<TreatGeneralCardSchemeHandlerJobTypePerformanceEntity> list);
}
