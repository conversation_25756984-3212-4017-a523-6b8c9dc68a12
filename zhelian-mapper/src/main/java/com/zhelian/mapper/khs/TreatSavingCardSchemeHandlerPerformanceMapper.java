package com.zhelian.mapper.khs;

import com.zhelian.model.ibeauty.form.output.TreatSavingCardProjectHandlerCommissionOutputForm;
import com.zhelian.model.khs.entity.TreatSavingCardSchemeHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.TreatSavingCardSchemeHandlerPerformanceEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatSavingCardSchemeHandlerPerformanceMapper {

    List<TreatSavingCardProjectHandlerCommissionOutputForm> allTreatSavingCardSchemeHandlerPerformance(@Param("EntityID") Integer EntityID);

    void deleteTreatSavingCardSchemeHandler(@Param("EntityID") Integer EntityID);

    void deleteTreatSavingCardSchemeHandlerJobType(@Param("EntityID") Integer EntityID);

    void insertTreatSavingCardSchemeHandlerPerformance(List<TreatSavingCardSchemeHandlerPerformanceEntity> list);

    void insertTreatSavingCardSchemeHandlerJobTypePerformance(List<TreatSavingCardSchemeHandlerJobTypePerformanceEntity> list);
}
