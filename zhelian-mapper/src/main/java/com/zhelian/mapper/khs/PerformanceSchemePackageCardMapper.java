package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.SalePackageCardPerformanceEntity;
import com.zhelian.model.khs.form.output.PerformanceSchemeGoodOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PerformanceSchemePackageCardMapper {

    List<PerformanceSchemeGoodOutForm> getAllPerformanceSchemePackageCard(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);

    void insertSalePackageCardPerformance(List<SalePackageCardPerformanceEntity> list);

    void deleteSalePackageCardPerformanceByPerformanceSchemeIDAndCategory(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);
}
