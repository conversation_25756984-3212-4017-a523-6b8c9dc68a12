package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TBSaleProductPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleProductPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface SaleProductPerformanceMapper {

    List<SaleProductPerformanceOutputForm> allSaleProductPerformance(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertSaleProductPerformance(List<TBSaleProductPerformanceEntity> list);

    void deleteSaleProductPerformance(Map<String, Object> map);

    void deleteSaleProductPerformanceByEntityID(@Param("EntityID") Integer EntityID);

    void deleteSaleProductPerformanceByEntityIDAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);
}
