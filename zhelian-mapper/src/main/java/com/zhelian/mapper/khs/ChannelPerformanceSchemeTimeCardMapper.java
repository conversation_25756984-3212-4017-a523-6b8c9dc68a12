package com.zhelian.mapper.khs;

import com.zhelian.model.khs.entity.SaleTimeCardPerformanceEntity;
import com.zhelian.model.khs.form.output.PerformanceSchemeGoodOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ChannelPerformanceSchemeTimeCardMapper {

    List<PerformanceSchemeGoodOutForm> getAllPerformanceSchemeTimeCard(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);

    void insertSaleTimeCardPerformance(List<SaleTimeCardPerformanceEntity> list);

    void deleteSaleTimeCardPerformanceByPerformanceSchemeIDAndCategory(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);
}
