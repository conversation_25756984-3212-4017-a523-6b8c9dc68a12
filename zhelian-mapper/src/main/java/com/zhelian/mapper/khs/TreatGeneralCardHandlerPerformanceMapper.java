package com.zhelian.mapper.khs;


import com.zhelian.model.khs.form.output.TreatGoodCategoryHandlerPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatGeneralCardHandlerPerformanceMapper {

    List<TreatGoodCategoryHandlerPerformanceOutputForm> allTreatGeneralCardHandlerPerformance(@Param("EntityID") Integer EntityID, @Param("GeneralCardID") Integer GeneralCardID);

    void deleteTreatGeneralCardHandler(@Param("EntityID") Integer EntityID, @Param("GeneralCardID") Integer GeneralCardID);

    void deleteTreatGeneralCardHandlerJobType(@Param("EntityID") Integer EntityID, @Param("GeneralCardID") Integer GeneralCardID);

    void insertTreatGeneralCardHandlerPerformance(List list);

    void insertTreatGeneralCardHandlerJobTypePerformance(List list);

    void deleteTreatGeneralCardHandlerPerformanceByEntityID(Integer EntityID);

    void deleteTreatGeneralCardHandlerJobTypePerformanceByEntityID(Integer EntityID);

}
