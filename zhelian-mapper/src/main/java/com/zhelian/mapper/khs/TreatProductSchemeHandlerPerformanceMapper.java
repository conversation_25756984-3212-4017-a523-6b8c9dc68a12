package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TreatProductSchemeHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.TreatProductSchemeHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.TreatGoodCategoryHandlerPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatProductSchemeHandlerPerformanceMapper {

    List<TreatGoodCategoryHandlerPerformanceOutputForm> allTreatProductSchemeHandlerPerformance(@Param("EntityID") Integer EntityID);

    void deleteTreatProductSchemeHandler(@Param("EntityID") Integer EntityID);

    void deleteTreatProductSchemeHandlerJobType(@Param("EntityID") Integer EntityID);

    void updateTreatProductSchemeHandlerPerformance(List<TreatProductSchemeHandlerPerformanceEntity> list);

    void insertTreatProductSchemeHandlerJobTypePerformance(List<TreatProductSchemeHandlerJobTypePerformanceEntity> list);
}
