package com.zhelian.mapper.khs;


import com.zhelian.model.ibeauty.form.output.TreatSavingCardProjectCategoryHandlerCommissionOutputForm;
import com.zhelian.model.khs.entity.TreatSavingCardCategoryHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.TreatSavingCardCategoryHandlerPerformanceEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatSavingCardCategoryHandlerPerformanceMapper {

    List<TreatSavingCardProjectCategoryHandlerCommissionOutputForm> allTreatSavingCardCategoryHandlerPerformance(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void deleteTreatSavingCardCategoryHandler(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void deleteTreatSavingCardCategoryHandlerJobType(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void innsertTreatSavingCardCategoryHandlerPerformance(List<TreatSavingCardCategoryHandlerPerformanceEntity> list);

    void insertTreatSavingCardCategoryHandlerJobTypePerformance(List<TreatSavingCardCategoryHandlerJobTypePerformanceEntity> list);


    void deleteTreatCategoryHandlerPerformanceByEntity(@Param("EntityID") Integer EntityID);

    void deleteTreatCategoryHandlerJobTypePerformanceByEntity(@Param("EntityID") Integer EntityID);
}
