package com.zhelian.mapper.khs;


import com.zhelian.model.ibeauty.form.output.TreatProductCommissionSchemeOutputForm;
import com.zhelian.model.khs.entity.TreatProductPerformanceSchemeEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatProductPerformanceSchemeMapper {

    List<TreatProductCommissionSchemeOutputForm> treatProductPerformanceSchemeList(@Param("Name") String Name);

    void createTreatProductPerformanceScheme(TreatProductPerformanceSchemeEntity TreatProductPerformanceSchemeEntity);

    Integer getTreatProductPerformanceScheme(@Param("EntityID") Integer EntityID);

    void deleteTreatProductPerformanceScheme(@Param("EntityID") Integer EntityID);

    void deleteTreatProductCategoryPerformance(@Param("EntityID") Integer EntityID);

    void deleteTreatProductCategoryHandlerPerformance(@Param("EntityID") Integer EntityID);

    void deleteTreatProductCategoryHandlerJobTypePerformance(@Param("EntityID") Integer EntityID);

    void deleteTreatProductPerformance(@Param("EntityID") Integer EntityID);

    void deleteTreatProductHandlerPerformance(@Param("EntityID") Integer EntityID);

    void deleteTreatProductHandlerJobTypePerformance(@Param("EntityID") Integer EntityID);
}
