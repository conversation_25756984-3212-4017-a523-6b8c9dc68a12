package com.zhelian.mapper.khs;


import com.zhelian.model.ibeauty.form.output.TreatProjectCommissionSchemeOutputForm;
import com.zhelian.model.khs.entity.TreatProjectPerformanceSchemeEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatProjectPerformanceSchemeMapper {

    List<TreatProjectCommissionSchemeOutputForm> treatProjectPerformanceSchemeList(@Param("Name") String Name);

    void createTreatProjectPerformanceScheme(TreatProjectPerformanceSchemeEntity TreatProjectPerformanceSchemeEntity);

    Integer getTreatProjectPerformanceScheme(@Param("EntityID") Integer EntityID);

    void deleteTreatProjectPerformanceScheme(@Param("EntityID") Integer EntityID);

    void deleteTreatProjectCategoryPerformance(@Param("EntityID") Integer EntityID);

    void deleteTreatProjectCategoryHandlerPerformance(@Param("EntityID") Integer EntityID);

    void deleteTreatProjectCategoryHandlerJobTypePerformance(@Param("EntityID") Integer EntityID);

    void deleteTreatProjectPerformance(@Param("EntityID") Integer EntityID);

    void deleteTreatProjectHandlerPerformance(@Param("EntityID") Integer EntityID);

    void deleteTreatProjectHandlerJobTypePerformance(@Param("EntityID") Integer EntityID);
}
