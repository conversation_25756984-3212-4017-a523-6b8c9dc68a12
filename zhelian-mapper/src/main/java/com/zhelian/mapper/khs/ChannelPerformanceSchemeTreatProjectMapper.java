package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TreatChannelGoodPerformanceEntity;
import com.zhelian.model.khs.form.output.PerformanceSchemeTreatGoodOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ChannelPerformanceSchemeTreatProjectMapper {

    List<PerformanceSchemeTreatGoodOutForm> getAllPerformanceSchemeProject(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);

    void insertTreatProjectPerformance(List<TreatChannelGoodPerformanceEntity> list);

    void deleteTreatProjectPerformanceByPerformanceSchemeIDAndCategory(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);

}
