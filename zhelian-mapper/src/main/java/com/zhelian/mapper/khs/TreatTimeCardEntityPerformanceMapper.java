package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TreatGoodEntityPerformanceEntity;
import com.zhelian.model.khs.form.output.TreatGoodPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface TreatTimeCardEntityPerformanceMapper {

    List<TreatGoodPerformanceOutputForm> allTreatTimeCardEntityPerformance(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertTreatTimeCardEntityPerformance(List<TreatGoodEntityPerformanceEntity> list);

    void deleteTreatTimeCardEntityPerformance(Map<String, Object> map);

    void deleteTreatTimeCardEntityPerformanceByEntity(Integer EntityID);

    void deleteTreatTimeCardEntityPerformanceByEntityAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);
}
