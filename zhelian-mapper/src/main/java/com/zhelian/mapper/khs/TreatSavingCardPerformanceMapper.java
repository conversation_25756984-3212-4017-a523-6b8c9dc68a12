package com.zhelian.mapper.khs;

import com.zhelian.model.ibeauty.form.output.TreatSavingCardCommissionOutputForm;
import com.zhelian.model.khs.entity.TreatSavingCardPerformanceEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface TreatSavingCardPerformanceMapper {

    List<TreatSavingCardCommissionOutputForm> allTreatSavingCardPerformance(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertTreatSavingPerformance(List<TreatSavingCardPerformanceEntity> list);

    void deleteTreatSavingCardPerformance(Map<String, Object> map);

    void deleteTreatSavingCardPerformanceByEntity(Integer EntityID);

    void deleteTreatSavingCardPerformanceByEntityAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);
}
