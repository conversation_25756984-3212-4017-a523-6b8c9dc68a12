package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TBSaleProjectPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleProjectPerformacneOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface SaleProjectPerformanceMapper {

    List<SaleProjectPerformacneOutputForm> allSaleProjectPerformance(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);

    void insertSaleProjectPerformance(List<TBSaleProjectPerformanceEntity> list);

    void deleteSaleProjectPerformance(Map<String, Object> map);

    void deleteSaleProjectPerformanceByEntity(@Param("EntityID") Integer EntityID);

    void deleteSaleProjectPerformanceByEntityAndCategory(@Param("EntityID") Integer EntityID, @Param("CategoryID") Integer CategoryID);
}
