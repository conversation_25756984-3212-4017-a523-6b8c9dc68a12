package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.SaleProjectPerformanceSchemeEntity;
import com.zhelian.model.khs.entity.TBSaleProjectCategoryPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleGoodCategoryOutputForm;
import com.zhelian.model.khs.form.output.SaleGoodCategoryPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleProjectCategoryPerformanceMapper {

    SaleGoodCategoryOutputForm saleProjectCategoryPerformance(@Param("EntityID") Integer EntityID);

    List<SaleGoodCategoryPerformanceOutputForm> allSaleProjectCategoryPerformance(@Param("EntityID") Integer EntityID);

    void updateSaleProjectPerformanceScheme(SaleProjectPerformanceSchemeEntity saleProjectPerformanceSchemeEntity);

    void insertSaleProjectCategoryPerformanceEntity(List<TBSaleProjectCategoryPerformanceEntity> list);


    void deleteSaleProjectCategoryPerformanceEntity(@Param("EntityID") Integer EntityID);
}
