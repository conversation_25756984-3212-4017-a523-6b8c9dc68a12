package com.zhelian.mapper.khs;

import com.zhelian.model.khs.entity.EmployeeEntityPermissionEntity;
import com.zhelian.model.khs.entity.EmployeeEntityPermissionExtendEntity;
import com.zhelian.model.khs.entity.EntityEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EmployeeEntityPermissionMapper {

    Integer[] employeeEntityPermission(String EmployeeID);

    List<EntityEntity> entityPermission(String EmployeeID);

    void deleteEmployeeEntityPermissionByEmployeeList(List<String> list);

    void insertEmployeeEntityPermissionByList(List<EmployeeEntityPermissionEntity> list);

    List<EmployeeEntityPermissionExtendEntity> getEmployeeEntityPermissionByEmployeeID(String EmployeeID);

}
