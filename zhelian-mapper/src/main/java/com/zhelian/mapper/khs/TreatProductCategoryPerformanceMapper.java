package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.TBTreatProductCategoryPerformanceEntity;
import com.zhelian.model.khs.entity.TreatProductPerformanceSchemeEntity;
import com.zhelian.model.khs.form.output.TreatGoodCategoryOutputForm;
import com.zhelian.model.khs.form.output.TreatGoodCategoryPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatProductCategoryPerformanceMapper {

    TreatGoodCategoryOutputForm treatProductCategoryPerformance(@Param("EntityID") Integer EntityID);

    List<TreatGoodCategoryPerformanceOutputForm> allTreatProductCategoryPerformance(@Param("EntityID") Integer EntityID);

    void updateTreatProductPerformanceScheme(TreatProductPerformanceSchemeEntity treatProductPerformanceSchemeEntity);

    void insertTreatProductCategoryPerformanceEntity(List<TBTreatProductCategoryPerformanceEntity> list);

    void deleteTreatProductCategoryPerformanceEntity(@Param("EntityID") Integer EntityID);
}
