package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.SaleDiscountEntity;
import com.zhelian.model.khs.entity.SaleDiscountEntityEntity;
import com.zhelian.model.khs.form.output.EmployeeJobSaleDiscountOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EmployeeJobSaleDiscountMapper {

    List<EmployeeJobSaleDiscountOutForm> list(@Param("JobTypeName") String JobTypeName);

    void createSaleDiscount(SaleDiscountEntity entity);

    void createSaleDiscountEntity(List<SaleDiscountEntityEntity> list);

    SaleDiscountEntity getSaleDiscountByID(@Param("ID") Integer ID);

    void updateSaleDiscount(SaleDiscountEntity entity);

    void deleteSaleDiscountEntityBySaleDiscountID(@Param("ID") Integer ID);

    void deleteSaleDiscountByID(@Param("ID") Integer ID);
}
