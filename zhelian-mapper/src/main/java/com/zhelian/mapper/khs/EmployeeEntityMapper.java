package com.zhelian.mapper.khs;

import com.zhelian.model.khs.entity.EmployeeEntityEntity;
import com.zhelian.model.miniprogram.form.output.EmployeeDetailOutputForm;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EmployeeEntityMapper {

    void insertEmployeeEntityList(List<EmployeeEntityEntity> List);

    void deleteEmployeeEntityByEmployeeID(String EmployeeID);

    void deleteEmployeeEntityByEmployeeList(List<String> EmpList);

    EmployeeDetailOutputForm getEmployeeDetail(String EmployeeID);
}
