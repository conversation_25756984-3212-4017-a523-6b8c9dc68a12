package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.SaleSavingCardSchemeHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SaleSavingCardSchemeHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleSavingCardCategoryHandlerPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SaleSavingCardSchemeHandlerPerformanceMapper {

    List<SaleSavingCardCategoryHandlerPerformanceOutputForm> allSaleSavingCardSchemeHandlerPerformance(@Param("EntityID") Integer EntityID);

    void deleteSaleSavingCardSchemeHandler(@Param("EntityID") Integer EntityID);

    void deleteSaleSavingCardSchemeHandlerJobType(@Param("EntityID") Integer EntityID);

    void updateSaleSavingCardSchemeHandlerPerformance(List<SaleSavingCardSchemeHandlerPerformanceEntity> list);

    void insertSaleSavingCardSchemeHandlerJobTypePerformance(List<SaleSavingCardSchemeHandlerJobTypePerformanceEntity> list);
}
