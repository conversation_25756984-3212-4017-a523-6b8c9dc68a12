package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.SaleProjectPerformanceEntity;
import com.zhelian.model.khs.form.output.PerformanceSchemeGoodOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface PerformanceSchemeProjectMapper {

    List<PerformanceSchemeGoodOutForm> getAllPerformanceSchemeProject(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);

    void deleteSaleProjectPerformance(Map<String, Object> map);

    void insertSaleProjectPerformance(List<SaleProjectPerformanceEntity> list);

    void deleteSaleProjectPerformanceByPerformanceSchemeIDAndCategory(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);
}
