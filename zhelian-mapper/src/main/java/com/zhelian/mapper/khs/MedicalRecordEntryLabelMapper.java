package com.zhelian.mapper.khs;

import com.zhelian.model.khs.entity.MedicalRecordEntryLabelEntity;
import com.zhelian.model.khs.form.output.MedicalRecordEntryLabelOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;

@Repository
public interface MedicalRecordEntryLabelMapper {

    List<MedicalRecordEntryLabelEntity> getMedicalRecordEntryLabelEntityByCategoryID(@Param("CategoryID") Integer CategoryID);

    List<MedicalRecordEntryLabelOutForm> list(@Param("Name") String Name, @Param("CategoryID") Integer CategoryID);

    Integer getMaxSequence();

    void create(MedicalRecordEntryLabelEntity entity);

    void update(MedicalRecordEntryLabelEntity entity);

    void delete(@Param("ID") Integer ID);

    LinkedList<Integer> getASCList(@Param("ID") Integer ID, @Param("CategoryID") Integer CategoryID);

    void updateSequence(List<MedicalRecordEntryLabelEntity> list);
}
