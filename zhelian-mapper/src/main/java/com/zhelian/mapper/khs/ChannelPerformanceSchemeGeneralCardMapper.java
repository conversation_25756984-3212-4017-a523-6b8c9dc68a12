package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.SaleGeneralCardPerformanceEntity;
import com.zhelian.model.khs.form.output.PerformanceSchemeGoodOutForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ChannelPerformanceSchemeGeneralCardMapper {

    List<PerformanceSchemeGoodOutForm> getAllPerformanceSchemeGeneralCard(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);

    void insertSaleGeneralCardPerformance(List<SaleGeneralCardPerformanceEntity> list);

    void deleteSaleGeneralCardPerformanceByPerformanceSchemeIDAndCategory(@Param("PerformanceSchemeID") Integer PerformanceSchemeID, @Param("CategoryID") Integer CategoryID);
}
