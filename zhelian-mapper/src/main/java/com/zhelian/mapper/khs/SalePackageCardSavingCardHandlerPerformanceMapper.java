package com.zhelian.mapper.khs;


import com.zhelian.model.khs.entity.SalePackageCardSavingCardHandlerJobTypePerformanceEntity;
import com.zhelian.model.khs.entity.SalePackageCardSavingCardHandlerPerformanceEntity;
import com.zhelian.model.khs.form.output.SaleSavingCardCategoryHandlerPerformanceOutputForm;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SalePackageCardSavingCardHandlerPerformanceMapper {

    List<SaleSavingCardCategoryHandlerPerformanceOutputForm> allSalePackageCardSavingCardHandlerPerformance(@Param("EntityID") Integer EntityID, @Param("SavingCardID") Integer SavingCardID);

    void deleteSalePackageCardSavingCardHandler(@Param("EntityID") Integer EntityID, @Param("SavingCardID") Integer SavingCardID);

    void deleteSalePackageCardSavingCardHandlerJobType(@Param("EntityID") Integer EntityID, @Param("SavingCardID") Integer SavingCardID);

    void updateSalePackageCardSavingCardHandlerPerformance(List<SalePackageCardSavingCardHandlerPerformanceEntity> list);

    void insertSalePackageCardSavingCardHandlerJobTypePerformance(List<SalePackageCardSavingCardHandlerJobTypePerformanceEntity> list);

    void deleteSalePackageCardSavingCardHandlerPerformanceByEntity(@Param("EntityID") Integer EntityID);

    void deleteSalePackageCardSavingCardHandlerJobTypePerformanceByEntity(@Param("EntityID") Integer EntityID);
}
