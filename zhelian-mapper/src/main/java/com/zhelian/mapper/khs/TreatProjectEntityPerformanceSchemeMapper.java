package com.zhelian.mapper.khs;


import com.zhelian.model.ibeauty.form.output.TreatProjectCommissionSchemeOutputForm;
import com.zhelian.model.khs.entity.TreatGoodEntityPerformanceSchemeEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TreatProjectEntityPerformanceSchemeMapper {

    List<TreatProjectCommissionSchemeOutputForm> treatProjectEntityPerformanceSchemeList(@Param("Name") String Name);

    void createTreatProjectEntityPerformanceScheme(TreatGoodEntityPerformanceSchemeEntity TreatProjectEntityEntityPerformanceSchemeEntity);

    Integer getTreatProjectEntityPerformanceScheme(@Param("EntityID") Integer EntityID);

    void deleteTreatProjectEntityPerformanceScheme(@Param("EntityID") Integer EntityID);


}
