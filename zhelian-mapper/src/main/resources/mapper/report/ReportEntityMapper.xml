<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.report.ReportEntityMapper">
    <select id="getStoreEntityListByEmployeeID" parameterType="String"
            resultType="com.zhelian.model.report.form.output.ReportEntityForm">
        SELECT E.ID, E.EntityName
        FROM TK_Entity E WITH (NOLOCK)
        WHERE E.IsStore = 1
          AND E.Active = 1
          AND EXISTS (
            SELECT *
            FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
            WHERE EEP.EmployeeID = #{EmployeeID}
          AND E.ID = EEP.EntityID
            )
        ORDER BY E.EntityName
    </select>
</mapper>