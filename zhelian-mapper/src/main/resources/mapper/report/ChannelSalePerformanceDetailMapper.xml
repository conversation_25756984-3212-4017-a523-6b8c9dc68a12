<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.report.ChannelSalePerformanceDetailMapper">
    <select id="getChannelSalePerformanceDetail"
            resultType="com.zhelian.model.report.form.output.ChannelSalePerformanceDetailStatementForm">
        SELECT SaleBillID,
               CONVERT(varchar(100), BillDate, 120) AS BillDate,
               CASE BillType
               WHEN '10' THEN '销售订单'
               WHEN '20' THEN '退款订单'
               WHEN '30' THEN '补欠款单'
               WHEN '40' THEN '充值订单' END AS BillType,
               EntityName,
               CustomerName,
               PhoneNumber,
               Code,
               ChannelName,
               ChannelType,
               GoodsTypeName,
               CategoryName,
               GoodName,
               PayPerformance,
               SavingCardPerformance,
               SavingCardLargessPerformance
        FROM (
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.Entity<PERSON>ame,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '项目' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformanceProject SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON SEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '项目' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformancePackageCardProject SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON SEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID

        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '储值卡' AS GoodsTypeName,
        SCC.Name AS CategoryName,
        SC.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        0 AS SavingCardPerformance,
        0 AS SavingCardLargessPerformance
        FROM TB_SaleChannelPerformanceSavingCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SEPC.SavingCardID = SC.ID
        LEFT JOIN TB_SavingCardCategory SCC WITH(NOLOCK) ON SC.SavingCardCategoryID = SCC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND SC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '储值卡' AS GoodsTypeName,
        SCC.Name AS CategoryName,
        SC.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        0 AS SavingCardPerformance,
        0 AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformancePackageCardSavingCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SEPC.SavingCardID = SC.ID
        LEFT JOIN TB_SavingCardCategory SCC WITH(NOLOCK) ON SC.SavingCardCategoryID = SCC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND SC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '时效卡' AS GoodsTypeName,
        TCC.Name AS CategoryName,
        TC.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformanceTimeCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON SEPC.TimeCardID = TC.ID
        LEFT JOIN TB_TimeCardCategory TCC WITH(NOLOCK) ON TC.TimeCardCategoryID = TCC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND TC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '时效卡' AS GoodsTypeName,
        TCC.Name AS CategoryName,
        TC.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformancePackageCardTimeCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON SEPC.TimeCardID = TC.ID
        LEFT JOIN TB_TimeCardCategory TCC WITH(NOLOCK) ON TC.TimeCardCategoryID = TCC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND TC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '通用次卡' AS GoodsTypeName,
        GCC.Name AS CategoryName,
        GC.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformanceGeneralCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON SEPC.GeneralCardID = GC.ID
        LEFT JOIN TB_GeneralCardCategory GCC WITH(NOLOCK) ON GC.GeneralCardCategoryID = GCC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND GC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '通用次卡' AS GoodsTypeName,
        GCC.Name AS CategoryName,
        GC.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformancePackageCardGeneralCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON SEPC.GeneralCardID = GC.ID
        LEFT JOIN TB_GeneralCardCategory GCC WITH(NOLOCK) ON GC.GeneralCardCategoryID = GCC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND GC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '产品' AS GoodsTypeName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformanceProduct SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON SEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON P.PCategoryID = PC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '产品' AS GoodsTypeName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformancePackageCardProduct SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON SEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON P.PCategoryID = PC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        ) T
        <where>
            <if test="GoodsTypeName != null and GoodsTypeName != '' ">
                GoodsTypeName = #{GoodsTypeName}
            </if>
        </where>
        ORDER BY BillDate DESC
    </select>
    <select id="getChannelSalePerformanceSum"
            resultType="com.zhelian.model.report.form.output.ChannelSalePerformanceSumStatementForm">
        SELECT SUM(PayPerformance) AS PayPerformance,SUM(SavingCardPerformance) AS
        SavingCardPerformance,SUM(SavingCardLargessPerformance) AS SavingCardLargessPerformance
        FROM (
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '项目' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformanceProject SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON SEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '项目' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformancePackageCardProject SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON SEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID

        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '储值卡' AS GoodsTypeName,
        SCC.Name AS CategoryName,
        SC.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        0 AS SavingCardPerformance,
        0 AS SavingCardLargessPerformance
        FROM TB_SaleChannelPerformanceSavingCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SEPC.SavingCardID = SC.ID
        LEFT JOIN TB_SavingCardCategory SCC WITH(NOLOCK) ON SC.SavingCardCategoryID = SCC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND SC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '储值卡' AS GoodsTypeName,
        SCC.Name AS CategoryName,
        SC.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        0 AS SavingCardPerformance,
        0 AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformancePackageCardSavingCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SEPC.SavingCardID = SC.ID
        LEFT JOIN TB_SavingCardCategory SCC WITH(NOLOCK) ON SC.SavingCardCategoryID = SCC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND SC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '时效卡' AS GoodsTypeName,
        TCC.Name AS CategoryName,
        TC.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformanceTimeCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON SEPC.TimeCardID = TC.ID
        LEFT JOIN TB_TimeCardCategory TCC WITH(NOLOCK) ON TC.TimeCardCategoryID = TCC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND TC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '时效卡' AS GoodsTypeName,
        TCC.Name AS CategoryName,
        TC.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformancePackageCardTimeCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON SEPC.TimeCardID = TC.ID
        LEFT JOIN TB_TimeCardCategory TCC WITH(NOLOCK) ON TC.TimeCardCategoryID = TCC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND TC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '通用次卡' AS GoodsTypeName,
        GCC.Name AS CategoryName,
        GC.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformanceGeneralCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON SEPC.GeneralCardID = GC.ID
        LEFT JOIN TB_GeneralCardCategory GCC WITH(NOLOCK) ON GC.GeneralCardCategoryID = GCC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND GC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '通用次卡' AS GoodsTypeName,
        GCC.Name AS CategoryName,
        GC.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformancePackageCardGeneralCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON SEPC.GeneralCardID = GC.ID
        LEFT JOIN TB_GeneralCardCategory GCC WITH(NOLOCK) ON GC.GeneralCardCategoryID = GCC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND GC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '产品' AS GoodsTypeName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformanceProduct SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON SEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON P.PCategoryID = PC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '产品' AS GoodsTypeName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformancePackageCardProduct SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON SEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON P.PCategoryID = PC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        ) T
        <where>
            <if test="GoodsTypeName != null and GoodsTypeName != '' ">
                GoodsTypeName = #{GoodsTypeName}
            </if>
        </where>
    </select>

    <select id="getExcelChannelSalePerformanceDetail"
            resultType="com.zhelian.model.report.form.output.ChannelSalePerformanceDetailStatementExcelForm">
        SELECT SaleBillID,
               CONVERT(varchar(100), BillDate, 120) AS BillDate,
               CASE BillType
                   WHEN '10' THEN '销售订单'
                   WHEN '20' THEN '退款订单'
                   WHEN '30' THEN '补欠款单'
                   WHEN '40' THEN '充值订单' END AS BillType,
               EntityName,
               CustomerName,
               PhoneNumber,
               Code,
               ChannelName,
               ChannelType,
               GoodsTypeName,
               CategoryName,
               GoodName,
               PayPerformance,
               SavingCardPerformance,
               SavingCardLargessPerformance
        FROM (
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '项目' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformanceProject SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON SEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '项目' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformancePackageCardProject SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON SEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID

        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '储值卡' AS GoodsTypeName,
        SCC.Name AS CategoryName,
        SC.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        0 AS SavingCardPerformance,
        0 AS SavingCardLargessPerformance
        FROM TB_SaleChannelPerformanceSavingCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SEPC.SavingCardID = SC.ID
        LEFT JOIN TB_SavingCardCategory SCC WITH(NOLOCK) ON SC.SavingCardCategoryID = SCC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND SC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '储值卡' AS GoodsTypeName,
        SCC.Name AS CategoryName,
        SC.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        0 AS SavingCardPerformance,
        0 AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformancePackageCardSavingCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SEPC.SavingCardID = SC.ID
        LEFT JOIN TB_SavingCardCategory SCC WITH(NOLOCK) ON SC.SavingCardCategoryID = SCC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND SC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '时效卡' AS GoodsTypeName,
        TCC.Name AS CategoryName,
        TC.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformanceTimeCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON SEPC.TimeCardID = TC.ID
        LEFT JOIN TB_TimeCardCategory TCC WITH(NOLOCK) ON TC.TimeCardCategoryID = TCC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND TC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '时效卡' AS GoodsTypeName,
        TCC.Name AS CategoryName,
        TC.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformancePackageCardTimeCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON SEPC.TimeCardID = TC.ID
        LEFT JOIN TB_TimeCardCategory TCC WITH(NOLOCK) ON TC.TimeCardCategoryID = TCC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND TC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '通用次卡' AS GoodsTypeName,
        GCC.Name AS CategoryName,
        GC.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformanceGeneralCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON SEPC.GeneralCardID = GC.ID
        LEFT JOIN TB_GeneralCardCategory GCC WITH(NOLOCK) ON GC.GeneralCardCategoryID = GCC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND GC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '通用次卡' AS GoodsTypeName,
        GCC.Name AS CategoryName,
        GC.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformancePackageCardGeneralCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON SEPC.GeneralCardID = GC.ID
        LEFT JOIN TB_GeneralCardCategory GCC WITH(NOLOCK) ON GC.GeneralCardCategoryID = GCC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND GC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '产品' AS GoodsTypeName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformanceProduct SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON SEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON P.PCategoryID = PC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '产品' AS GoodsTypeName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformancePackageCardProduct SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON SEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON P.PCategoryID = PC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        ) T
        <where>
            <if test="GoodsTypeName != null and GoodsTypeName != '' ">
                GoodsTypeName = #{GoodsTypeName}
            </if>
        </where>
        ORDER BY BillDate DESC
    </select>

    <select id="getEasyExcelChannelSalePerformanceDetail"
            resultType="com.zhelian.model.report.form.output.ChannelSalePerformanceDetailStatementEasyExcelForm">
        SELECT saleBillID,
        CONVERT(varchar(100), BillDate, 120) AS billDate,
        CASE billType
        WHEN '10' THEN '销售订单'
        WHEN '20' THEN '退款订单'
        WHEN '30' THEN '补欠款单'
        WHEN '40' THEN '充值订单' END AS billType,
        entityName,
        customerName,
        phoneNumber,
        code,
        channelName,
        channelType,
        goodsTypeName,
        categoryName,
        goodName,
        payPerformance,
        savingCardPerformance,
        savingCardLargessPerformance
        FROM (
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '项目' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformanceProject SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON SEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '项目' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformancePackageCardProject SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON SEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID

        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '储值卡' AS GoodsTypeName,
        SCC.Name AS CategoryName,
        SC.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        0 AS SavingCardPerformance,
        0 AS SavingCardLargessPerformance
        FROM TB_SaleChannelPerformanceSavingCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SEPC.SavingCardID = SC.ID
        LEFT JOIN TB_SavingCardCategory SCC WITH(NOLOCK) ON SC.SavingCardCategoryID = SCC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND SC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '储值卡' AS GoodsTypeName,
        SCC.Name AS CategoryName,
        SC.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        0 AS SavingCardPerformance,
        0 AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformancePackageCardSavingCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SEPC.SavingCardID = SC.ID
        LEFT JOIN TB_SavingCardCategory SCC WITH(NOLOCK) ON SC.SavingCardCategoryID = SCC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND SC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '时效卡' AS GoodsTypeName,
        TCC.Name AS CategoryName,
        TC.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformanceTimeCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON SEPC.TimeCardID = TC.ID
        LEFT JOIN TB_TimeCardCategory TCC WITH(NOLOCK) ON TC.TimeCardCategoryID = TCC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND TC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '时效卡' AS GoodsTypeName,
        TCC.Name AS CategoryName,
        TC.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformancePackageCardTimeCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON SEPC.TimeCardID = TC.ID
        LEFT JOIN TB_TimeCardCategory TCC WITH(NOLOCK) ON TC.TimeCardCategoryID = TCC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND TC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '通用次卡' AS GoodsTypeName,
        GCC.Name AS CategoryName,
        GC.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformanceGeneralCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON SEPC.GeneralCardID = GC.ID
        LEFT JOIN TB_GeneralCardCategory GCC WITH(NOLOCK) ON GC.GeneralCardCategoryID = GCC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND GC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '通用次卡' AS GoodsTypeName,
        GCC.Name AS CategoryName,
        GC.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformancePackageCardGeneralCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON SEPC.GeneralCardID = GC.ID
        LEFT JOIN TB_GeneralCardCategory GCC WITH(NOLOCK) ON GC.GeneralCardCategoryID = GCC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND GC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '产品' AS GoodsTypeName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformanceProduct SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON SEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON P.PCategoryID = PC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        SB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '产品' AS GoodsTypeName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance

        FROM TB_SaleChannelPerformancePackageCardProduct SEPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = SB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON SEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON SEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON P.PCategoryID = PC.ID
        <where>
            <if test="EntityID != null and EntityID != null ">
                SEPC.EntityID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND SEPC.SaleBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        ) T
        <where>
            <if test="GoodsTypeName != null and GoodsTypeName != '' ">
                GoodsTypeName = #{GoodsTypeName}
            </if>
        </where>
        ORDER BY BillDate DESC
    </select>
</mapper>