<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.report.EntityIncomeStatementMapper">
    <select id="getDateIncomeStatement"
            resultType="com.zhelian.model.report.form.output.EntityDateIncomeDetailStatementForm">
        SELECT
        BillDate,
        EntityName,
        SUM(BeautyRecharge) BeautyRecharge,
        SUM(BeautyProduct) as BeautyProduct,
        SUM(BeautyProject) as BeautyProject,
        SUM(BeautyRecharge)+SUM(BeautyProduct)+SUM(BeautyProject) BeautyTotal,
        SUM(HairRecharge) as HairRecharge,
        SUM(HairProduct) as HairProduct,
        SUM(HairProject) as HairProject,
        SUM(HairRecharge)+SUM(HairProduct)+SUM(HairProject) HairTotal,
        SUM(BeautyTreatProduct) as BeautyTreatProduct,
        SUM(BeautyTreatProject) as BeautyTreatProject,
        SUM(BeautyTreatProduct)+SUM(BeautyTreatProject) BeautyTreatTotal,
        SUM(HairTreatProduct) as HairTreatProduct,
        SUM(HairTreatProject) as HairTreatProject,
        SUM(HairTreatProduct)+SUM(HairTreatProject) HairTreatTotal,
        SUM(BeautySavingCard)*0.1 BeautySavingCardRate,
        SUM(HaidSavingCard)*0.4 HaidSavingCardRate,
        SUM(BeautySavingCard) as BeautySavingCard,
        SUM(HaidSavingCard) as HaidSavingCard,
        SUM(BeautyProduct)+SUM(BeautyProject)+SUM(BeautyTreatProduct)+SUM(BeautyTreatProject)-SUM(BeautySavingCard)*0.1
        BeautyTotalMoney,
        SUM(HairProduct)+SUM(HairProject)+SUM(HairTreatProduct)+SUM(HairTreatProject)-SUM(HaidSavingCard)*0.4
        HairTotalMoney

        FROM
        (
        SELECT
        BillDate,
        EntityID,
        EntityName,
        SUM(aa.PayPerformance) BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.SaleBillGeneralCardID=tt.ID
        LEFT JOIN TB_GeneralCard tp WITH(NOLOCK) ON tt.GeneralCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.GeneralCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillTimeCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.SaleBillTimeCardID=tt.ID
        LEFT JOIN TB_TimeCard tp WITH(NOLOCK) ON tt.TimeCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.TimeCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.SaleBillSavingCardID=tt.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillPackageCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_PackageCard tp WITH(NOLOCK) ON tt.PackageCardID=tp.ID
        LEFT JOIN (
        SELECT
        SaleBillPackageCardID,
        SUM(PayPerformance)PayPerformance
        FROM
        (
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProduct WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProject WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardGeneralCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardTimeCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardSavingCard WITH(NOLOCK)
        )T GROUP BY SaleBillPackageCardID
        ) tsp ON tsp.SaleBillPackageCardID=tt.ID
        WHERE t.BillStatus='20' AND tp.PackageCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName
        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        SUM(aa.PayPerformance) as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName

        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        SUM(aa.PayPerformance) as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName


        UNION ALL

        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        SUM(aa.PayPerformance) as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.SaleBillGeneralCardID=tt.ID
        LEFT JOIN TB_GeneralCard tp WITH(NOLOCK) ON tt.GeneralCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.GeneralCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillTimeCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.SaleBillTimeCardID=tt.ID
        LEFT JOIN TB_TimeCard tp WITH(NOLOCK) ON tt.TimeCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.TimeCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.SaleBillSavingCardID=tt.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillPackageCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_PackageCard tp WITH(NOLOCK) ON tt.PackageCardID=tp.ID
        LEFT JOIN (
        SELECT
        SaleBillPackageCardID,
        SUM(PayPerformance)PayPerformance
        FROM
        (
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProduct WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProject WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardGeneralCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardTimeCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardSavingCard WITH(NOLOCK)
        )T GROUP BY SaleBillPackageCardID
        ) tsp ON tsp.SaleBillPackageCardID=tt.ID
        WHERE t.BillStatus='20' AND tp.PackageCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName
        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        SUM(aa.PayPerformance) as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName

        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        SUM(aa.PayPerformance) as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName


        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        SUM(SavingCardPerformance) as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.SavingCardPerformance*-1 ELSE tsp.SavingCardPerformance
        END),0)SavingCardPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName
        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        SUM(Performance) as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.TreatBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.TreatBillGeneralCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.TreatBillTimeCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.CardPerformance)*-1 ELSE (tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.TreatBillSavingCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName

        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        SUM(SavingCardPerformance) as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.SavingCardPerformance*-1 ELSE tsp.SavingCardPerformance
        END),0)SavingCardPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName
        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        SUM(Performance) as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.TreatBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.TreatBillGeneralCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.TreatBillTimeCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.CardPerformance*-1 ELSE tsp.CardPerformance END),0)Performance
        FROM TB_TreatBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.TreatBillSavingCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName

        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        SUM(LargessAmount) as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tt.LargessAmount*-1 ELSE tt.LargessAmount END),0)LargessAmount
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName
        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        SUM(LargessAmount) as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tt.LargessAmount*-1 ELSE tt.LargessAmount END),0)LargessAmount
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName
        )T GROUP BY BillDate,EntityID,EntityName,EntityName
        ORDER BY EntityName,BillDate
    </select>

    <select id="getDateIncomeStatementSum"
            resultType="com.zhelian.model.report.form.output.EntityIncomeSumStatementForm">
        SELECT
        SUM(BeautyRecharge) BeautyRecharge,
        SUM(BeautyProduct) as BeautyProduct,
        SUM(BeautyProject) as BeautyProject,
        SUM(BeautyRecharge)+SUM(BeautyProduct)+SUM(BeautyProject) BeautyTotal,
        SUM(HairRecharge) as HairRecharge,
        SUM(HairProduct) as HairProduct,
        SUM(HairProject) as HairProject,
        SUM(HairRecharge)+SUM(HairProduct)+SUM(HairProject) HairTotal,
        SUM(BeautyTreatProduct) as BeautyTreatProduct,
        SUM(BeautyTreatProject) as BeautyTreatProject,
        SUM(BeautyTreatProduct)+SUM(BeautyTreatProject) BeautyTreatTotal,
        SUM(HairTreatProduct) as HairTreatProduct,
        SUM(HairTreatProject) as HairTreatProject,
        SUM(HairTreatProduct)+SUM(HairTreatProject) HairTreatTotal,
        SUM(BeautySavingCard)*0.1 BeautySavingCardRate,
        SUM(HaidSavingCard)*0.4 HaidSavingCardRate,
        SUM(BeautySavingCard) as BeautySavingCard,
        SUM(HaidSavingCard) as HaidSavingCard,
        SUM(BeautyProduct)+SUM(BeautyProject)+SUM(BeautyTreatProduct)+SUM(BeautyTreatProject)-SUM(BeautySavingCard)*0.1
        BeautyTotalMoney,
        SUM(HairProduct)+SUM(HairProject)+SUM(HairTreatProduct)+SUM(HairTreatProject)-SUM(HaidSavingCard)*0.4
        HairTotalMoney
        FROM
        (
        SELECT
        BillDate,
        EntityID,
        EntityName,
        SUM(aa.PayPerformance) BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.SaleBillGeneralCardID=tt.ID
        LEFT JOIN TB_GeneralCard tp WITH(NOLOCK) ON tt.GeneralCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.GeneralCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillTimeCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.SaleBillTimeCardID=tt.ID
        LEFT JOIN TB_TimeCard tp WITH(NOLOCK) ON tt.TimeCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.TimeCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.SaleBillSavingCardID=tt.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillPackageCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_PackageCard tp WITH(NOLOCK) ON tt.PackageCardID=tp.ID
        LEFT JOIN (
        SELECT
        SaleBillPackageCardID,
        SUM(PayPerformance)PayPerformance
        FROM
        (
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProduct WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProject WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardGeneralCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardTimeCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardSavingCard WITH(NOLOCK)
        )T GROUP BY SaleBillPackageCardID
        ) tsp ON tsp.SaleBillPackageCardID=tt.ID
        WHERE t.BillStatus='20' AND tp.PackageCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName
        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        SUM(aa.PayPerformance) as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName

        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        SUM(aa.PayPerformance) as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName


        UNION ALL

        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        SUM(aa.PayPerformance) as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.SaleBillGeneralCardID=tt.ID
        LEFT JOIN TB_GeneralCard tp WITH(NOLOCK) ON tt.GeneralCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.GeneralCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillTimeCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.SaleBillTimeCardID=tt.ID
        LEFT JOIN TB_TimeCard tp WITH(NOLOCK) ON tt.TimeCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.TimeCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.SaleBillSavingCardID=tt.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillPackageCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_PackageCard tp WITH(NOLOCK) ON tt.PackageCardID=tp.ID
        LEFT JOIN (
        SELECT
        SaleBillPackageCardID,
        SUM(PayPerformance)PayPerformance
        FROM
        (
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProduct WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProject WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardGeneralCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardTimeCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardSavingCard WITH(NOLOCK)
        )T GROUP BY SaleBillPackageCardID
        ) tsp ON tsp.SaleBillPackageCardID=tt.ID
        WHERE t.BillStatus='20' AND tp.PackageCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName
        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        SUM(aa.PayPerformance) as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName

        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        SUM(aa.PayPerformance) as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName


        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        SUM(SavingCardPerformance) as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.SavingCardPerformance*-1 ELSE tsp.SavingCardPerformance
        END),0)SavingCardPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName
        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        SUM(Performance) as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.TreatBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.TreatBillGeneralCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.TreatBillTimeCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.CardPerformance)*-1 ELSE (tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.TreatBillSavingCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName

        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        SUM(SavingCardPerformance) as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.SavingCardPerformance*-1 ELSE tsp.SavingCardPerformance
        END),0)SavingCardPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName
        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        SUM(Performance) as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.TreatBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.TreatBillGeneralCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.TreatBillTimeCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.CardPerformance*-1 ELSE tsp.CardPerformance END),0)Performance
        FROM TB_TreatBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.TreatBillSavingCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName

        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        SUM(LargessAmount) as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tt.LargessAmount*-1 ELSE tt.LargessAmount END),0)LargessAmount
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName
        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        SUM(LargessAmount) as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tt.LargessAmount*-1 ELSE tt.LargessAmount END),0)LargessAmount
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName
        )T
    </select>

    <select id="getDateIncomeStatementExcel"
            resultType="com.zhelian.model.report.form.output.EntityDateIncomeDetailStatementExcelForm">
        SELECT
        BillDate billDate,
        EntityName entityName,
        SUM(BeautyRecharge) beautyRecharge,
        SUM(BeautyProduct) as beautyProduct,
        SUM(BeautyProject) as beautyProject,
        SUM(BeautyRecharge)+SUM(BeautyProduct)+SUM(BeautyProject) beautyTotal,
        SUM(HairRecharge) as hairRecharge,
        SUM(HairProduct) as hairProduct,
        SUM(HairProject) as hairProject,
        SUM(HairRecharge)+SUM(HairProduct)+SUM(HairProject) hairTotal,
        SUM(BeautyTreatProduct) as beautyTreatProduct,
        SUM(BeautyTreatProject) as beautyTreatProject,
        SUM(BeautyTreatProduct)+SUM(BeautyTreatProject) beautyTreatTotal,
        SUM(HairTreatProduct) as hairTreatProduct,
        SUM(HairTreatProject) as hairTreatProject,
        SUM(HairTreatProduct)+SUM(HairTreatProject) hairTreatTotal,
        SUM(BeautySavingCard)*0.1 beautySavingCardRate,
        SUM(HaidSavingCard)*0.4 haidSavingCardRate,
        SUM(BeautySavingCard) as beautySavingCard,
        SUM(HaidSavingCard) as haidSavingCard,
        SUM(BeautyProduct)+SUM(BeautyProject)+SUM(BeautyTreatProduct)+SUM(BeautyTreatProject)-SUM(BeautySavingCard)*0.1
        beautyTotalMoney,
        SUM(HairProduct)+SUM(HairProject)+SUM(HairTreatProduct)+SUM(HairTreatProject)-SUM(HaidSavingCard)*0.4
        hairTotalMoney

        FROM
        (
        SELECT
        BillDate,
        EntityID,
        EntityName,
        SUM(aa.PayPerformance) BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.SaleBillGeneralCardID=tt.ID
        LEFT JOIN TB_GeneralCard tp WITH(NOLOCK) ON tt.GeneralCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.GeneralCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillTimeCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.SaleBillTimeCardID=tt.ID
        LEFT JOIN TB_TimeCard tp WITH(NOLOCK) ON tt.TimeCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.TimeCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.SaleBillSavingCardID=tt.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillPackageCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_PackageCard tp WITH(NOLOCK) ON tt.PackageCardID=tp.ID
        LEFT JOIN (
        SELECT
        SaleBillPackageCardID,
        SUM(PayPerformance)PayPerformance
        FROM
        (
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProduct WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProject WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardGeneralCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardTimeCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardSavingCard WITH(NOLOCK)
        )T GROUP BY SaleBillPackageCardID
        ) tsp ON tsp.SaleBillPackageCardID=tt.ID
        WHERE t.BillStatus='20' AND tp.PackageCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName
        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        SUM(aa.PayPerformance) as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName

        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        SUM(aa.PayPerformance) as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName


        UNION ALL

        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        SUM(aa.PayPerformance) as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.SaleBillGeneralCardID=tt.ID
        LEFT JOIN TB_GeneralCard tp WITH(NOLOCK) ON tt.GeneralCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.GeneralCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillTimeCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.SaleBillTimeCardID=tt.ID
        LEFT JOIN TB_TimeCard tp WITH(NOLOCK) ON tt.TimeCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.TimeCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.SaleBillSavingCardID=tt.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillPackageCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_PackageCard tp WITH(NOLOCK) ON tt.PackageCardID=tp.ID
        LEFT JOIN (
        SELECT
        SaleBillPackageCardID,
        SUM(PayPerformance)PayPerformance
        FROM
        (
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProduct WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProject WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardGeneralCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardTimeCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardSavingCard WITH(NOLOCK)
        )T GROUP BY SaleBillPackageCardID
        ) tsp ON tsp.SaleBillPackageCardID=tt.ID
        WHERE t.BillStatus='20' AND tp.PackageCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName
        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        SUM(aa.PayPerformance) as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName

        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        SUM(aa.PayPerformance) as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName


        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        SUM(SavingCardPerformance) as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.SavingCardPerformance*-1 ELSE tsp.SavingCardPerformance
        END),0)SavingCardPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName
        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        SUM(Performance) as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.TreatBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.TreatBillGeneralCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.TreatBillTimeCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.CardPerformance)*-1 ELSE (tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.TreatBillSavingCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName

        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        SUM(SavingCardPerformance) as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.SavingCardPerformance*-1 ELSE tsp.SavingCardPerformance
        END),0)SavingCardPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName
        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        SUM(Performance) as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.TreatBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.TreatBillGeneralCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.TreatBillTimeCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.CardPerformance*-1 ELSE tsp.CardPerformance END),0)Performance
        FROM TB_TreatBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.TreatBillSavingCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName

        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        SUM(LargessAmount) as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tt.LargessAmount*-1 ELSE tt.LargessAmount END),0)LargessAmount
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName
        UNION ALL
        SELECT
        BillDate,
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        SUM(LargessAmount) as HaidSavingCard
        FROM
        (
        SELECT
        CONVERT(VARCHAR(10),t.BillDate,120) BillDate,
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tt.LargessAmount*-1 ELSE tt.LargessAmount END),0)LargessAmount
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY BillDate,EntityID,EntityName
        )T GROUP BY BillDate,EntityID,EntityName,EntityName
        ORDER BY EntityName,BillDate
    </select>

    <select id="getEntityIncomeStatement"
            resultType="com.zhelian.model.report.form.output.EntityIncomeDetailStatementForm">
        SELECT
        EntityName,
        SUM(BeautyRecharge) BeautyRecharge,
        SUM(BeautyProduct) as BeautyProduct,
        SUM(BeautyProject) as BeautyProject,
        SUM(BeautyRecharge)+SUM(BeautyProduct)+SUM(BeautyProject) BeautyTotal,
        SUM(HairRecharge) as HairRecharge,
        SUM(HairProduct) as HairProduct,
        SUM(HairProject) as HairProject,
        SUM(HairRecharge)+SUM(HairProduct)+SUM(HairProject) HairTotal,
        SUM(BeautyTreatProduct) as BeautyTreatProduct,
        SUM(BeautyTreatProject) as BeautyTreatProject,
        SUM(BeautyTreatProduct)+SUM(BeautyTreatProject) BeautyTreatTotal,
        SUM(HairTreatProduct) as HairTreatProduct,
        SUM(HairTreatProject) as HairTreatProject,
        SUM(HairTreatProduct)+SUM(HairTreatProject) HairTreatTotal,
        SUM(BeautySavingCard)*0.1 BeautySavingCardRate,
        SUM(HaidSavingCard)*0.4 HaidSavingCardRate,
        SUM(BeautySavingCard) as BeautySavingCard,
        SUM(HaidSavingCard) as HaidSavingCard,
        SUM(BeautyProduct)+SUM(BeautyProject)+SUM(BeautyTreatProduct)+SUM(BeautyTreatProject)-SUM(BeautySavingCard)*0.1
        BeautyTotalMoney,
        SUM(HairProduct)+SUM(HairProject)+SUM(HairTreatProduct)+SUM(HairTreatProject)-SUM(HaidSavingCard)*0.4
        HairTotalMoney

        FROM
        (
        SELECT
        EntityID,
        EntityName,
        SUM(aa.PayPerformance) BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.SaleBillGeneralCardID=tt.ID
        LEFT JOIN TB_GeneralCard tp WITH(NOLOCK) ON tt.GeneralCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.GeneralCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillTimeCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.SaleBillTimeCardID=tt.ID
        LEFT JOIN TB_TimeCard tp WITH(NOLOCK) ON tt.TimeCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.TimeCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.SaleBillSavingCardID=tt.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillPackageCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_PackageCard tp WITH(NOLOCK) ON tt.PackageCardID=tp.ID
        LEFT JOIN (
        SELECT
        SaleBillPackageCardID,
        SUM(PayPerformance)PayPerformance
        FROM
        (
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProduct WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProject WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardGeneralCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardTimeCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardSavingCard WITH(NOLOCK)
        )T GROUP BY SaleBillPackageCardID
        ) tsp ON tsp.SaleBillPackageCardID=tt.ID
        WHERE t.BillStatus='20' AND tp.PackageCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        SUM(aa.PayPerformance) as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName

        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        SUM(aa.PayPerformance) as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName


        UNION ALL

        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        SUM(aa.PayPerformance) as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.SaleBillGeneralCardID=tt.ID
        LEFT JOIN TB_GeneralCard tp WITH(NOLOCK) ON tt.GeneralCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.GeneralCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillTimeCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.SaleBillTimeCardID=tt.ID
        LEFT JOIN TB_TimeCard tp WITH(NOLOCK) ON tt.TimeCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.TimeCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.SaleBillSavingCardID=tt.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillPackageCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_PackageCard tp WITH(NOLOCK) ON tt.PackageCardID=tp.ID
        LEFT JOIN (
        SELECT
        SaleBillPackageCardID,
        SUM(PayPerformance)PayPerformance
        FROM
        (
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProduct WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProject WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardGeneralCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardTimeCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardSavingCard WITH(NOLOCK)
        )T GROUP BY SaleBillPackageCardID
        ) tsp ON tsp.SaleBillPackageCardID=tt.ID
        WHERE t.BillStatus='20' AND tp.PackageCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        SUM(aa.PayPerformance) as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName

        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        SUM(aa.PayPerformance) as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName


        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        SUM(SavingCardPerformance) as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.SavingCardPerformance*-1 ELSE tsp.SavingCardPerformance
        END),0)SavingCardPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        SUM(Performance) as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.TreatBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.TreatBillGeneralCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.TreatBillTimeCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.CardPerformance)*-1 ELSE (tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.TreatBillSavingCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName

        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        SUM(SavingCardPerformance) as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.SavingCardPerformance*-1 ELSE tsp.SavingCardPerformance
        END),0)SavingCardPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        SUM(Performance) as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.TreatBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.TreatBillGeneralCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.TreatBillTimeCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.CardPerformance)*-1 ELSE (tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.TreatBillSavingCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName

        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        SUM(LargessAmount) as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tt.LargessAmount*-1 ELSE tt.LargessAmount END),0)LargessAmount
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        SUM(LargessAmount) as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tt.LargessAmount*-1 ELSE tt.LargessAmount END),0)LargessAmount
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        )T GROUP BY EntityID,EntityName,EntityName
        ORDER BY EntityName
    </select>

    <select id="getEntityIncomeStatementSum"
            resultType="com.zhelian.model.report.form.output.EntityIncomeSumStatementForm">
        SELECT
        SUM(BeautyRecharge) BeautyRecharge,
        SUM(BeautyProduct) as BeautyProduct,
        SUM(BeautyProject) as BeautyProject,
        SUM(BeautyRecharge)+SUM(BeautyProduct)+SUM(BeautyProject) BeautyTotal,
        SUM(HairRecharge) as HairRecharge,
        SUM(HairProduct) as HairProduct,
        SUM(HairProject) as HairProject,
        SUM(HairRecharge)+SUM(HairProduct)+SUM(HairProject) HairTotal,
        SUM(BeautyTreatProduct) as BeautyTreatProduct,
        SUM(BeautyTreatProject) as BeautyTreatProject,
        SUM(BeautyTreatProduct)+SUM(BeautyTreatProject) BeautyTreatTotal,
        SUM(HairTreatProduct) as HairTreatProduct,
        SUM(HairTreatProject) as HairTreatProject,
        SUM(HairTreatProduct)+SUM(HairTreatProject) HairTreatTotal,
        SUM(BeautySavingCard)*0.1 BeautySavingCardRate,
        SUM(HaidSavingCard)*0.4 HaidSavingCardRate,
        SUM(BeautySavingCard) as BeautySavingCard,
        SUM(HaidSavingCard) as HaidSavingCard,
        SUM(BeautyProduct)+SUM(BeautyProject)+SUM(BeautyTreatProduct)+SUM(BeautyTreatProject)-SUM(BeautySavingCard)*0.1
        BeautyTotalMoney,
        SUM(HairProduct)+SUM(HairProject)+SUM(HairTreatProduct)+SUM(HairTreatProject)-SUM(HaidSavingCard)*0.4
        HairTotalMoney
        FROM
        (
        SELECT
        EntityID,
        EntityName,
        SUM(aa.PayPerformance) BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.SaleBillGeneralCardID=tt.ID
        LEFT JOIN TB_GeneralCard tp WITH(NOLOCK) ON tt.GeneralCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.GeneralCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillTimeCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.SaleBillTimeCardID=tt.ID
        LEFT JOIN TB_TimeCard tp WITH(NOLOCK) ON tt.TimeCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.TimeCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.SaleBillSavingCardID=tt.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillPackageCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_PackageCard tp WITH(NOLOCK) ON tt.PackageCardID=tp.ID
        LEFT JOIN (
        SELECT
        SaleBillPackageCardID,
        SUM(PayPerformance)PayPerformance
        FROM
        (
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProduct WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProject WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardGeneralCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardTimeCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardSavingCard WITH(NOLOCK)
        )T GROUP BY SaleBillPackageCardID
        ) tsp ON tsp.SaleBillPackageCardID=tt.ID
        WHERE t.BillStatus='20' AND tp.PackageCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        SUM(aa.PayPerformance) as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName

        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        SUM(aa.PayPerformance) as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName


        UNION ALL

        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        SUM(aa.PayPerformance) as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.SaleBillGeneralCardID=tt.ID
        LEFT JOIN TB_GeneralCard tp WITH(NOLOCK) ON tt.GeneralCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.GeneralCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillTimeCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.SaleBillTimeCardID=tt.ID
        LEFT JOIN TB_TimeCard tp WITH(NOLOCK) ON tt.TimeCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.TimeCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.SaleBillSavingCardID=tt.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillPackageCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_PackageCard tp WITH(NOLOCK) ON tt.PackageCardID=tp.ID
        LEFT JOIN (
        SELECT
        SaleBillPackageCardID,
        SUM(PayPerformance)PayPerformance
        FROM
        (
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProduct WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProject WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardGeneralCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardTimeCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardSavingCard WITH(NOLOCK)
        )T GROUP BY SaleBillPackageCardID
        ) tsp ON tsp.SaleBillPackageCardID=tt.ID
        WHERE t.BillStatus='20' AND tp.PackageCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        SUM(aa.PayPerformance) as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName

        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        SUM(aa.PayPerformance) as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName


        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        SUM(SavingCardPerformance) as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.SavingCardPerformance*-1 ELSE tsp.SavingCardPerformance
        END),0)SavingCardPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        SUM(Performance) as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.TreatBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.TreatBillGeneralCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.TreatBillTimeCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.CardPerformance*-1 ELSE tsp.CardPerformance END),0)Performance
        FROM TB_TreatBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.TreatBillSavingCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName

        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        SUM(SavingCardPerformance) as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.SavingCardPerformance*-1 ELSE tsp.SavingCardPerformance
        END),0)SavingCardPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        SUM(Performance) as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.TreatBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.TreatBillGeneralCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.TreatBillTimeCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.CardPerformance*-1 ELSE tsp.CardPerformance END),0)Performance
        FROM TB_TreatBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.TreatBillSavingCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName

        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        SUM(LargessAmount) as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tt.LargessAmount*-1 ELSE tt.LargessAmount END),0)LargessAmount
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        SUM(LargessAmount) as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tt.LargessAmount*-1 ELSE tt.LargessAmount END),0)LargessAmount
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        )T
    </select>

    <select id="getEntityIncomeStatementExcel"
            resultType="com.zhelian.model.report.form.output.EntityIncomeDetailStatementExcelForm">
        SELECT
        EntityName entityName,
        SUM(BeautyRecharge) beautyRecharge,
        SUM(BeautyProduct) as beautyProduct,
        SUM(BeautyProject) as beautyProject,
        SUM(BeautyRecharge)+SUM(BeautyProduct)+SUM(BeautyProject) beautyTotal,
        SUM(HairRecharge) as hairRecharge,
        SUM(HairProduct) as hairProduct,
        SUM(HairProject) as hairProject,
        SUM(HairRecharge)+SUM(HairProduct)+SUM(HairProject) hairTotal,
        SUM(BeautyTreatProduct) as beautyTreatProduct,
        SUM(BeautyTreatProject) as beautyTreatProject,
        SUM(BeautyTreatProduct)+SUM(BeautyTreatProject) beautyTreatTotal,
        SUM(HairTreatProduct) as hairTreatProduct,
        SUM(HairTreatProject) as hairTreatProject,
        SUM(HairTreatProduct)+SUM(HairTreatProject) hairTreatTotal,
        SUM(BeautySavingCard)*0.1 beautySavingCardRate,
        SUM(HaidSavingCard)*0.4 haidSavingCardRate,
        SUM(BeautySavingCard) as beautySavingCard,
        SUM(HaidSavingCard) as haidSavingCard,
        SUM(BeautyProduct)+SUM(BeautyProject)+SUM(BeautyTreatProduct)+SUM(BeautyTreatProject)-SUM(BeautySavingCard)*0.1
        beautyTotalMoney,
        SUM(HairProduct)+SUM(HairProject)+SUM(HairTreatProduct)+SUM(HairTreatProject)-SUM(HaidSavingCard)*0.4
        hairTotalMoney

        FROM
        (
        SELECT
        EntityID,
        EntityName,
        SUM(aa.PayPerformance) BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.SaleBillGeneralCardID=tt.ID
        LEFT JOIN TB_GeneralCard tp WITH(NOLOCK) ON tt.GeneralCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.GeneralCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillTimeCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.SaleBillTimeCardID=tt.ID
        LEFT JOIN TB_TimeCard tp WITH(NOLOCK) ON tt.TimeCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.TimeCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.SaleBillSavingCardID=tt.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillPackageCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_PackageCard tp WITH(NOLOCK) ON tt.PackageCardID=tp.ID
        LEFT JOIN (
        SELECT
        SaleBillPackageCardID,
        SUM(PayPerformance)PayPerformance
        FROM
        (
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProduct WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProject WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardGeneralCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardTimeCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardSavingCard WITH(NOLOCK)
        )T GROUP BY SaleBillPackageCardID
        ) tsp ON tsp.SaleBillPackageCardID=tt.ID
        WHERE t.BillStatus='20' AND tp.PackageCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        SUM(aa.PayPerformance) as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName

        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        SUM(aa.PayPerformance) as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName


        UNION ALL

        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        SUM(aa.PayPerformance) as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.SaleBillGeneralCardID=tt.ID
        LEFT JOIN TB_GeneralCard tp WITH(NOLOCK) ON tt.GeneralCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.GeneralCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillTimeCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.SaleBillTimeCardID=tt.ID
        LEFT JOIN TB_TimeCard tp WITH(NOLOCK) ON tt.TimeCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.TimeCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.SaleBillSavingCardID=tt.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillPackageCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_PackageCard tp WITH(NOLOCK) ON tt.PackageCardID=tp.ID
        LEFT JOIN (
        SELECT
        SaleBillPackageCardID,
        SUM(PayPerformance)PayPerformance
        FROM
        (
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProduct WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProject WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardGeneralCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardTimeCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardSavingCard WITH(NOLOCK)
        )T GROUP BY SaleBillPackageCardID
        ) tsp ON tsp.SaleBillPackageCardID=tt.ID
        WHERE t.BillStatus='20' AND tp.PackageCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        SUM(aa.PayPerformance) as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName

        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        SUM(aa.PayPerformance) as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName


        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        SUM(SavingCardPerformance) as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.SavingCardPerformance*-1 ELSE tsp.SavingCardPerformance
        END),0)SavingCardPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        SUM(Performance) as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.TreatBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.TreatBillGeneralCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.TreatBillTimeCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.CardPerformance*-1 ELSE tsp.CardPerformance END),0)Performance
        FROM TB_TreatBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.TreatBillSavingCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName

        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        SUM(SavingCardPerformance) as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.SavingCardPerformance*-1 ELSE tsp.SavingCardPerformance
        END),0)SavingCardPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        SUM(Performance) as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.TreatBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.TreatBillGeneralCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.TreatBillTimeCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.CardPerformance*-1 ELSE tsp.CardPerformance END),0)Performance
        FROM TB_TreatBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.TreatBillSavingCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName

        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        SUM(LargessAmount) as BeautySavingCard,
        0 as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tt.LargessAmount*-1 ELSE tt.LargessAmount END),0)LargessAmount
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        SUM(LargessAmount) as HaidSavingCard
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tt.LargessAmount*-1 ELSE tt.LargessAmount END),0)LargessAmount
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null and EntityID.length>0">
                AND t.EntityID IN
                <foreach collection="EntityID" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        )T GROUP BY EntityID,EntityName,EntityName
        ORDER BY EntityName
    </select>

    <select id="getIncomeStatement"
            resultType="com.zhelian.model.report.form.output.IncomeDetailStatementForm">
        SELECT
        EntityName,
        SUM(BeautyRecharge)+SUM(BeautyProduct)+SUM(BeautyProject) BeautyTotal,
        SUM(BeautyTreatProduct)+SUM(BeautyTreatProject) BeautyTreatTotal,
        CAST(SUM(BeautyProduct)+SUM(BeautyProject)+SUM(BeautyTreatProduct)+SUM(BeautyTreatProject)-SUM(BeautySavingCard)*0.1
        as decimal(38, 2)) BeautyTotalMoney,
        SUM(HairRecharge)+SUM(HairProduct)+SUM(HairProject) HairTotal,
        SUM(HairTreatProduct)+SUM(HairTreatProject) HairTreatTotal,
        CAST(SUM(HairProduct)+SUM(HairProject)+SUM(HairTreatProduct)+SUM(HairTreatProject)-SUM(HaidSavingCard)*0.4 as
        decimal(38, 2)) HairTotalMoney,
        SUM(BeautyRecharge)+SUM(BeautyProduct)+SUM(BeautyProject)+SUM(BeautyTreatProduct)+SUM(BeautyTreatProject)+SUM(BeautyProduct)+SUM(BeautyProject)+SUM(BeautyTreatProduct)+SUM(BeautyTreatProject)-SUM(BeautySavingCard)*0.1
        +SUM(HairRecharge)+SUM(HairProduct)+SUM(HairProject)+SUM(HairTreatProduct)+SUM(HairTreatProject)+SUM(HairProduct)+SUM(HairProject)+SUM(HairTreatProduct)+SUM(HairTreatProject)-SUM(HaidSavingCard)*0.4
        TotalPerformance,
        CASE WHEN ISNULL(SUM(EntityCustomerCount),0)=0 then 0 ELSE
        CAST(SUM(TreatProjectHeadCount)*1.0/SUM(EntityCustomerCount) as decimal(38, 2)) END ToShopRate,
        CASE WHEN ISNULL(SUM(EntityCustomerCount),0)=0 then 0 ELSE
        CAST(SUM(SecondBuyCustomerCount)*1.0/SUM(EntityCustomerCount) as decimal(38, 2)) END AgainBuyRate,
        CASE WHEN ISNULL(SUM(NoSaleCustomerCount),0)=0 then 0 ELSE
        CAST(SUM(FirstSaleCustomerCount)*1.0/SUM(NoSaleCustomerCount) as decimal(38, 2)) END ConversionRate
        FROM
        (
        SELECT
        EntityID,
        EntityName,
        SUM(aa.PayPerformance) BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.SaleBillGeneralCardID=tt.ID
        LEFT JOIN TB_GeneralCard tp WITH(NOLOCK) ON tt.GeneralCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.GeneralCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillTimeCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.SaleBillTimeCardID=tt.ID
        LEFT JOIN TB_TimeCard tp WITH(NOLOCK) ON tt.TimeCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.TimeCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.SaleBillSavingCardID=tt.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillPackageCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_PackageCard tp WITH(NOLOCK) ON tt.PackageCardID=tp.ID
        LEFT JOIN (
        SELECT
        SaleBillPackageCardID,
        SUM(PayPerformance)PayPerformance
        FROM
        (
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProduct WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProject WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardGeneralCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardTimeCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardSavingCard WITH(NOLOCK)
        )T GROUP BY SaleBillPackageCardID
        ) tsp ON tsp.SaleBillPackageCardID=tt.ID
        WHERE t.BillStatus='20' AND tp.PackageCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        SUM(aa.PayPerformance) as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        SUM(aa.PayPerformance) as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        SUM(aa.PayPerformance) as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.SaleBillGeneralCardID=tt.ID
        LEFT JOIN TB_GeneralCard tp WITH(NOLOCK) ON tt.GeneralCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.GeneralCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillTimeCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.SaleBillTimeCardID=tt.ID
        LEFT JOIN TB_TimeCard tp WITH(NOLOCK) ON tt.TimeCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.TimeCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.SaleBillSavingCardID=tt.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillPackageCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_PackageCard tp WITH(NOLOCK) ON tt.PackageCardID=tp.ID
        LEFT JOIN (
        SELECT
        SaleBillPackageCardID,
        SUM(PayPerformance)PayPerformance
        FROM
        (
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProduct WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProject WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardGeneralCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardTimeCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardSavingCard WITH(NOLOCK)
        )T GROUP BY SaleBillPackageCardID
        ) tsp ON tsp.SaleBillPackageCardID=tt.ID
        WHERE t.BillStatus='20' AND tp.PackageCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        SUM(aa.PayPerformance) as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        SUM(aa.PayPerformance) as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        SUM(SavingCardPerformance) as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.SavingCardPerformance*-1 ELSE tsp.SavingCardPerformance
        END),0)SavingCardPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        SUM(Performance) as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.TreatBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.TreatBillGeneralCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.TreatBillTimeCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.CardPerformance*-1 ELSE tsp.CardPerformance END),0)Performance
        FROM TB_TreatBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.TreatBillSavingCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        SUM(SavingCardPerformance) as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.SavingCardPerformance*-1 ELSE tsp.SavingCardPerformance
        END),0)SavingCardPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        SUM(Performance) as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.TreatBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.TreatBillGeneralCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.TreatBillTimeCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.CardPerformance*-1 ELSE tsp.CardPerformance END),0)Performance
        FROM TB_TreatBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.TreatBillSavingCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        SUM(LargessAmount) as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tt.LargessAmount*-1 ELSE tt.LargessAmount END),0)LargessAmount
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        SUM(LargessAmount) as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tt.LargessAmount*-1 ELSE tt.LargessAmount END),0)LargessAmount
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        Count(*) as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        DISTINCT
        t.EntityID,
        tee.EntityName,
        t.CustomerID
        FROM TB_TreatBill t WITH(NOLOCK)
        LEFT JOIN TK_Entity tee WITH(NOLOCK) ON tee.ID=t.EntityID
        WHERE t.BillStatus='20' AND t.BillType!='20' AND t.CustomerID IS NOT NULL AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        
        AND EXISTS
        (
        SELECT 1 FROM (
        SELECT TreatBillID FROM TB_TreatBillProject ttt
        UNION
        SELECT TreatBillID FROM TB_TreatBillGeneralCard ttt
        UNION
        SELECT TreatBillID FROM TB_TreatBillTimeCard ttt
        UNION
        SELECT TreatBillID FROM TB_TreatBillSavingCard ttt
        )aa WHERE aa.TreatBillID=t.ID
        )
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tee.EntityName,
        t.CustomerID
        FROM TB_TreatBill t WITH(NOLOCK)
        LEFT JOIN TK_Entity tee WITH(NOLOCK) ON tee.ID=t.EntityID
        WHERE t.BillStatus='20' AND t.BillType!='20' AND t.CustomerID IS NULL AND t.BillDate BETWEEN #{StartTime} AND
        DATEADD(DAY,1,#{EndTime})
        AND EXISTS
        (
        SELECT 1 FROM TB_TreatBillProject ttt WHERE ttt.TreatBillID=t.ID
        )
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        tc.EntityID,
        tee.EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        Count(*) as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        TB_Customer t WITH(NOLOCK)
        LEFT JOIN TB_CustomerEntity tc WITH(NOLOCK) ON t.ID=tc.CustomerID AND tc.IsBelongEntity=1
        LEFT JOIN TK_Entity tee WITH(NOLOCK) ON tee.ID=tc.EntityID
        GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        Count(*) as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tee.EntityName,
        t.CustomerID,
        Count(*) count
        FROM TB_SaleBill t WITH(NOLOCK)
        LEFT JOIN TK_Entity tee WITH(NOLOCK) ON tee.ID=t.EntityID
        WHERE t.BillStatus='20' AND t.BillType!='20' AND t.CustomerID IS NOT NULL
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        GROUP BY t.EntityID,t.CustomerID,tee.EntityName
        )aa
        WHERE count>1
        GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        Count(*) as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (SELECT t.*,row_number() over (partition by t.CustomerID order by t.BillDate ) rn
        FROM (
        SELECT tsm.BillDate,tsm.CustomerID,tsm.EntityID,tee.EntityName FROM TB_SaleBill tsm WITH(NOLOCK)
        LEFT JOIN TK_Entity tee ON tee.ID=tsm.EntityID
        WHERE tsm.BillStatus='20' AND tsm.BillType!='20' AND tsm.CustomerID IS NOT NULL
        <choose>
            <when test="EntityID!=null">
                AND tsm.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tsm.EntityID
                )
            </otherwise>
        </choose>
        GROUP BY tsm.CustomerID,tsm.BillDate,tsm.EntityID,tee.EntityName
        )t
        ) t
        WHERE rn=1 AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        Count(*) as NoSaleCustomerCount
        FROM
        TB_Customer t WITH(NOLOCK)
        LEFT JOIN TB_CustomerEntity tc WITH(NOLOCK) ON t.ID=tc.CustomerID AND tc.IsBelongEntity=1
        LEFT JOIN TK_Entity tee WITH(NOLOCK) ON tee.ID=tc.EntityID
        WHERE NOT EXISTS
        (
        SELECT 1 FROM TB_SaleBill tt WITH(NOLOCK) WHERE tt.BillStatus='20' AND tt.BillType!='20' AND t.ID=tt.CustomerID
        )
        GROUP BY EntityID,EntityName
        )T
        <where>
            <if test="EntityID!=null and EntityID!=''">
                T.EntityID=#{EntityID}
            </if>
        </where>
        GROUP BY EntityID,EntityName,EntityName
        ORDER BY EntityName
    </select>

    <select id="getIncomeStatementSum"
            resultType="com.zhelian.model.report.form.output.IncomeSumStatementForm">
        SELECT
        SUM(BeautyRecharge)+SUM(BeautyProduct)+SUM(BeautyProject) BeautyTotal,
        SUM(BeautyTreatProduct)+SUM(BeautyTreatProject) BeautyTreatTotal,
        CAST(SUM(BeautyProduct)+SUM(BeautyProject)+SUM(BeautyTreatProduct)+SUM(BeautyTreatProject)-SUM(BeautySavingCard)*0.1
        as decimal(38, 2)) BeautyTotalMoney,
        SUM(HairRecharge)+SUM(HairProduct)+SUM(HairProject) HairTotal,
        SUM(HairTreatProduct)+SUM(HairTreatProject) HairTreatTotal,
        CAST(SUM(HairProduct)+SUM(HairProject)+SUM(HairTreatProduct)+SUM(HairTreatProject)-SUM(HaidSavingCard)*0.4 as
        decimal(38, 2)) HairTotalMoney,
        SUM(BeautyRecharge)+SUM(BeautyProduct)+SUM(BeautyProject)+SUM(BeautyTreatProduct)+SUM(BeautyTreatProject)+SUM(BeautyProduct)+SUM(BeautyProject)+SUM(BeautyTreatProduct)+SUM(BeautyTreatProject)-SUM(BeautySavingCard)*0.1
        +SUM(HairRecharge)+SUM(HairProduct)+SUM(HairProject)+SUM(HairTreatProduct)+SUM(HairTreatProject)+SUM(HairProduct)+SUM(HairProject)+SUM(HairTreatProduct)+SUM(HairTreatProject)-SUM(HaidSavingCard)*0.4
        TotalPerformance
        FROM
        (
        SELECT
        EntityID,
        EntityName,
        SUM(aa.PayPerformance) BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.SaleBillGeneralCardID=tt.ID
        LEFT JOIN TB_GeneralCard tp WITH(NOLOCK) ON tt.GeneralCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.GeneralCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillTimeCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.SaleBillTimeCardID=tt.ID
        LEFT JOIN TB_TimeCard tp WITH(NOLOCK) ON tt.TimeCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.TimeCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.SaleBillSavingCardID=tt.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillPackageCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_PackageCard tp WITH(NOLOCK) ON tt.PackageCardID=tp.ID
        LEFT JOIN (
        SELECT
        SaleBillPackageCardID,
        SUM(PayPerformance)PayPerformance
        FROM
        (
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProduct WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProject WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardGeneralCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardTimeCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardSavingCard WITH(NOLOCK)
        )T GROUP BY SaleBillPackageCardID
        ) tsp ON tsp.SaleBillPackageCardID=tt.ID
        WHERE t.BillStatus='20' AND tp.PackageCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        SUM(aa.PayPerformance) as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        SUM(aa.PayPerformance) as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        SUM(aa.PayPerformance) as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.SaleBillGeneralCardID=tt.ID
        LEFT JOIN TB_GeneralCard tp WITH(NOLOCK) ON tt.GeneralCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.GeneralCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillTimeCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.SaleBillTimeCardID=tt.ID
        LEFT JOIN TB_TimeCard tp WITH(NOLOCK) ON tt.TimeCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.TimeCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.SaleBillSavingCardID=tt.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillPackageCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_PackageCard tp WITH(NOLOCK) ON tt.PackageCardID=tp.ID
        LEFT JOIN (
        SELECT
        SaleBillPackageCardID,
        SUM(PayPerformance)PayPerformance
        FROM
        (
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProduct WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProject WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardGeneralCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardTimeCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardSavingCard WITH(NOLOCK)
        )T GROUP BY SaleBillPackageCardID
        ) tsp ON tsp.SaleBillPackageCardID=tt.ID
        WHERE t.BillStatus='20' AND tp.PackageCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        SUM(aa.PayPerformance) as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        SUM(aa.PayPerformance) as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        SUM(SavingCardPerformance) as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.SavingCardPerformance*-1 ELSE tsp.SavingCardPerformance
        END),0)SavingCardPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        SUM(Performance) as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.TreatBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.TreatBillGeneralCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.TreatBillTimeCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.CardPerformance*-1 ELSE tsp.CardPerformance END),0)Performance
        FROM TB_TreatBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.TreatBillSavingCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        SUM(SavingCardPerformance) as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.SavingCardPerformance*-1 ELSE tsp.SavingCardPerformance
        END),0)SavingCardPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        SUM(Performance) as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.TreatBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.TreatBillGeneralCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.TreatBillTimeCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.CardPerformance*-1 ELSE tsp.CardPerformance END),0)Performance
        FROM TB_TreatBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.TreatBillSavingCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        SUM(LargessAmount) as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tt.LargessAmount*-1 ELSE tt.LargessAmount END),0)LargessAmount
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        SUM(LargessAmount) as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tt.LargessAmount*-1 ELSE tt.LargessAmount END),0)LargessAmount
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        Count(*) as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        DISTINCT
        t.EntityID,
        tee.EntityName,
        t.CustomerID
        FROM TB_TreatBill t WITH(NOLOCK)
        LEFT JOIN TK_Entity tee WITH(NOLOCK) ON tee.ID=t.EntityID
        WHERE t.BillStatus='20' AND t.BillType!='20' AND t.CustomerID IS NOT NULL AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        AND EXISTS
        (
        SELECT 1 FROM (
        SELECT TreatBillID FROM TB_TreatBillProject ttt
        UNION
        SELECT TreatBillID FROM TB_TreatBillGeneralCard ttt
        UNION
        SELECT TreatBillID FROM TB_TreatBillTimeCard ttt
        UNION
        SELECT TreatBillID FROM TB_TreatBillSavingCard ttt
        )aa WHERE aa.TreatBillID=t.ID
        )
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tee.EntityName,
        t.CustomerID
        FROM TB_TreatBill t WITH(NOLOCK)
        LEFT JOIN TK_Entity tee WITH(NOLOCK) ON tee.ID=t.EntityID
        WHERE t.BillStatus='20' AND t.BillType!='20' AND t.CustomerID IS NULL AND t.BillDate BETWEEN #{StartTime} AND
        DATEADD(DAY,1,#{EndTime})
        AND EXISTS
        (
        SELECT 1 FROM TB_TreatBillProject ttt WHERE ttt.TreatBillID=t.ID
        )
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        tc.EntityID,
        tee.EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        Count(*) as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        TB_Customer t WITH(NOLOCK)
        LEFT JOIN TB_CustomerEntity tc WITH(NOLOCK) ON t.ID=tc.CustomerID AND tc.IsBelongEntity=1
        LEFT JOIN TK_Entity tee WITH(NOLOCK) ON tee.ID=tc.EntityID
        GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        Count(*) as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tee.EntityName,
        t.CustomerID,
        Count(*) count
        FROM TB_SaleBill t WITH(NOLOCK)
        LEFT JOIN TK_Entity tee WITH(NOLOCK) ON tee.ID=t.EntityID
        WHERE t.BillStatus='20' AND t.BillType!='20' AND t.CustomerID IS NOT NULL AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        GROUP BY t.EntityID,t.CustomerID,tee.EntityName
        )aa
        WHERE count>1
        GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        Count(*) as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (SELECT t.*,row_number() over (partition by t.CustomerID order by t.BillDate ) rn
        FROM (
        SELECT tsm.BillDate,tsm.CustomerID,tsm.EntityID,tee.EntityName FROM TB_SaleBill tsm WITH(NOLOCK)
        LEFT JOIN TK_Entity tee ON tee.ID=tsm.EntityID
        WHERE tsm.BillStatus='20' AND tsm.BillType!='20' AND tsm.CustomerID IS NOT NULL
        <choose>
            <when test="EntityID!=null">
                AND tsm.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tsm.EntityID
                )
            </otherwise>
        </choose>
        GROUP BY tsm.CustomerID,tsm.BillDate,tsm.EntityID,tee.EntityName
        )t
        ) t
        WHERE rn=1 AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        Count(*) as NoSaleCustomerCount
        FROM
        TB_Customer t WITH(NOLOCK)
        LEFT JOIN TB_CustomerEntity tc WITH(NOLOCK) ON t.ID=tc.CustomerID AND tc.IsBelongEntity=1
        LEFT JOIN TK_Entity tee WITH(NOLOCK) ON tee.ID=tc.EntityID
        WHERE NOT EXISTS
        (
        SELECT 1 FROM TB_SaleBill tt WHERE tt.BillStatus='20' AND tt.BillType!='20' AND t.ID=tt.CustomerID
        )
        GROUP BY EntityID,EntityName
        )T
        <where>
            <if test="EntityID!=null and EntityID!=''">
                T.EntityID=#{EntityID}
            </if>
        </where>
    </select>

    <select id="getIncomeStatementExcel"
            resultType="com.zhelian.model.report.form.output.IncomeDetailStatementExcelForm">
        SELECT
        EntityName entityName,
        SUM(BeautyRecharge)+SUM(BeautyProduct)+SUM(BeautyProject) beautyTotal,
        SUM(BeautyTreatProduct)+SUM(BeautyTreatProject) beautyTreatTotal,
        CAST(SUM(BeautyProduct)+SUM(BeautyProject)+SUM(BeautyTreatProduct)+SUM(BeautyTreatProject)-SUM(BeautySavingCard)*0.1
        as decimal(38, 2)) beautyTotalMoney,
        SUM(HairRecharge)+SUM(HairProduct)+SUM(HairProject) hairTotal,
        SUM(HairTreatProduct)+SUM(HairTreatProject) hairTreatTotal,
        CAST(SUM(HairProduct)+SUM(HairProject)+SUM(HairTreatProduct)+SUM(HairTreatProject)-SUM(HaidSavingCard)*0.4 as
        decimal(38, 2)) hairTotalMoney,
        SUM(BeautyRecharge)+SUM(BeautyProduct)+SUM(BeautyProject)+SUM(BeautyTreatProduct)+SUM(BeautyTreatProject)+SUM(BeautyProduct)+SUM(BeautyProject)+SUM(BeautyTreatProduct)+SUM(BeautyTreatProject)-SUM(BeautySavingCard)*0.1
        +SUM(HairRecharge)+SUM(HairProduct)+SUM(HairProject)+SUM(HairTreatProduct)+SUM(HairTreatProject)+SUM(HairProduct)+SUM(HairProject)+SUM(HairTreatProduct)+SUM(HairTreatProject)-SUM(HaidSavingCard)*0.4
        totalPerformance,
        CASE WHEN ISNULL(SUM(EntityCustomerCount),0)=0 then 0 ELSE
        CAST(SUM(TreatProjectHeadCount)*1.0/SUM(EntityCustomerCount) as decimal(38, 2)) END toShopRate,
        CASE WHEN ISNULL(SUM(EntityCustomerCount),0)=0 then 0 ELSE
        CAST(SUM(SecondBuyCustomerCount)*1.0/SUM(EntityCustomerCount) as decimal(38, 2)) END againBuyRate,
        CASE WHEN ISNULL(SUM(NoSaleCustomerCount),0)=0 then 0 ELSE
        CAST(SUM(FirstSaleCustomerCount)*1.0/SUM(NoSaleCustomerCount) as decimal(38, 2)) END conversionRate
        FROM
        (
        SELECT
        EntityID,
        EntityName,
        SUM(aa.PayPerformance) BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.SaleBillGeneralCardID=tt.ID
        LEFT JOIN TB_GeneralCard tp WITH(NOLOCK) ON tt.GeneralCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.GeneralCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillTimeCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.SaleBillTimeCardID=tt.ID
        LEFT JOIN TB_TimeCard tp WITH(NOLOCK) ON tt.TimeCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.TimeCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TB_SaleEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.SaleBillSavingCardID=tt.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillPackageCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_PackageCard tp WITH(NOLOCK) ON tt.PackageCardID=tp.ID
        LEFT JOIN (
        SELECT
        SaleBillPackageCardID,
        SUM(PayPerformance)PayPerformance
        FROM
        (
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProduct WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProject WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardGeneralCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardTimeCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardSavingCard WITH(NOLOCK)
        )T GROUP BY SaleBillPackageCardID
        ) tsp ON tsp.SaleBillPackageCardID=tt.ID
        WHERE t.BillStatus='20' AND tp.PackageCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        SUM(aa.PayPerformance) as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        SUM(aa.PayPerformance) as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        SUM(aa.PayPerformance) as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.SaleBillGeneralCardID=tt.ID
        LEFT JOIN TB_GeneralCard tp WITH(NOLOCK) ON tt.GeneralCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.GeneralCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillTimeCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.SaleBillTimeCardID=tt.ID
        LEFT JOIN TB_TimeCard tp WITH(NOLOCK) ON tt.TimeCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.TimeCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.SaleBillSavingCardID=tt.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL((CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillPackageCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_PackageCard tp WITH(NOLOCK) ON tt.PackageCardID=tp.ID
        LEFT JOIN (
        SELECT
        SaleBillPackageCardID,
        SUM(PayPerformance)PayPerformance
        FROM
        (
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProduct WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardProject WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardGeneralCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardTimeCard WITH(NOLOCK)
        UNION ALL
        SELECT SaleBillPackageCardID,PayPerformance FROM TB_SaleEntityPerformancePackageCardSavingCard WITH(NOLOCK)
        )T GROUP BY SaleBillPackageCardID
        ) tsp ON tsp.SaleBillPackageCardID=tt.ID
        WHERE t.BillStatus='20' AND tp.PackageCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        SUM(aa.PayPerformance) as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        SUM(aa.PayPerformance) as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.PayPerformance*-1 ELSE tsp.PayPerformance END),0)PayPerformance
        FROM TB_SaleBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.SaleBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        SUM(SavingCardPerformance) as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.SavingCardPerformance*-1 ELSE tsp.SavingCardPerformance
        END),0)SavingCardPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        SUM(Performance) as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.TreatBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.TreatBillGeneralCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.TreatBillTimeCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.CardPerformance*-1 ELSE tsp.CardPerformance END),0)Performance
        FROM TB_TreatBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.TreatBillSavingCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        SUM(SavingCardPerformance) as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.SavingCardPerformance*-1 ELSE tsp.SavingCardPerformance
        END),0)SavingCardPerformance
        FROM TB_SaleBillProduct tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SaleEntityPerformanceProduct tsp WITH(NOLOCK) ON tsp.SaleBillProductID=tt.ID
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON tt.ProductID=tp.ID
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tp.PCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        SUM(Performance) as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceProject tsp WITH(NOLOCK) ON tsp.TreatBillProjectID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND t.IsFastBill!=1 AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceGeneralCard tsp WITH(NOLOCK) ON tsp.TreatBillGeneralCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN (tsp.PayPerformance+tsp.CardPerformance)*-1 ELSE
        (tsp.PayPerformance+tsp.CardPerformance) END),0)Performance
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceTimeCard tsp WITH(NOLOCK) ON tsp.TreatBillTimeCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL

        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tsp.CardPerformance*-1 ELSE tsp.CardPerformance END),0)Performance
        FROM TB_TreatBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t WITH(NOLOCK) ON t.ID=tt.TreatBillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_TreatEntityPerformanceSavingCard tsp WITH(NOLOCK) ON tsp.TreatBillSavingCardID=tt.ID
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON tt.ProjectID=tp.ID
        LEFT JOIN TB_ProjectCategory tpc WITH(NOLOCK) ON tp.ProjectCategoryID=tpc.ID
        WHERE t.BillStatus='20' AND tpc.ParentID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        SUM(LargessAmount) as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tt.LargessAmount*-1 ELSE tt.LargessAmount END),0)LargessAmount
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=2
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        SUM(LargessAmount) as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tey.EntityName,
        ISNULL( (CASE WHEN t.BillType='20' THEN tt.LargessAmount*-1 ELSE tt.LargessAmount END),0)LargessAmount
        FROM TB_SaleBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID=tt.BillID
        LEFT JOIN TK_Entity tey WITH(NOLOCK) ON t.EntityID=tey.ID
        LEFT JOIN TB_SavingCard tp WITH(NOLOCK) ON tt.SavingCardID=tp.ID
        WHERE t.BillStatus='20' AND tp.SavingCardCategoryID=1
        AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        Count(*) as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        DISTINCT
        t.EntityID,
        tee.EntityName,
        t.CustomerID
        FROM TB_TreatBill t WITH(NOLOCK)
        LEFT JOIN TK_Entity tee WITH(NOLOCK) ON tee.ID=t.EntityID
        WHERE t.BillStatus='20' AND t.BillType!='20' AND t.CustomerID IS NOT NULL AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t.EntityID,
        tee.EntityName,
        t.CustomerID
        FROM TB_TreatBill t
        LEFT JOIN TK_Entity tee ON tee.ID=t.EntityID
        WHERE t.BillStatus='20' AND t.BillType!='20' AND t.CustomerID IS NULL AND t.BillDate BETWEEN #{StartTime} AND
        DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        )aa GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        tc.EntityID,
        tee.EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        Count(*) as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        TB_Customer t WITH(NOLOCK)
        LEFT JOIN TB_CustomerEntity tc WITH(NOLOCK) ON t.ID=tc.CustomerID AND tc.IsBelongEntity=1
        LEFT JOIN TK_Entity tee WITH(NOLOCK) ON tee.ID=tc.EntityID
        GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        Count(*) as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (
        SELECT
        t.EntityID,
        tee.EntityName,
        t.CustomerID,
        Count(*) count
        FROM TB_SaleBill t WITH(NOLOCK)
        LEFT JOIN TK_Entity tee WITH(NOLOCK) ON tee.ID=t.EntityID
        WHERE t.BillStatus='20' AND t.BillType!='20' AND t.CustomerID IS NOT NULL AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        GROUP BY t.EntityID,t.CustomerID,tee.EntityName
        )aa
        WHERE count>1
        GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        Count(*) as FirstSaleCustomerCount,
        0 as NoSaleCustomerCount
        FROM
        (SELECT t.*,row_number() over (partition by t.CustomerID order by t.BillDate ) rn
        FROM (
        SELECT tsm.BillDate,tsm.CustomerID,tsm.EntityID,tee.EntityName FROM TB_SaleBill tsm WITH(NOLOCK)
        LEFT JOIN TK_Entity tee WITH(NOLOCK) ON tee.ID=tsm.EntityID
        WHERE tsm.BillStatus='20' AND tsm.BillType!='20' AND tsm.CustomerID IS NOT NULL
        <choose>
            <when test="EntityID!=null">
                AND tsm.EntityID =#{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tsm.EntityID
                )
            </otherwise>
        </choose>
        GROUP BY tsm.CustomerID,tsm.BillDate,tsm.EntityID,tee.EntityName
        )t
        ) t
        WHERE rn=1 AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY EntityID,EntityName
        UNION ALL
        SELECT
        EntityID,
        EntityName,
        0 BeautyRecharge,
        0 as BeautyProduct,
        0 as BeautyProject,
        0 as HairRecharge,
        0 as HairProduct,
        0 as HairProject,
        0 as BeautyTreatProduct,
        0 as BeautyTreatProject,
        0 as HairTreatProduct,
        0 as HairTreatProject,
        0 as BeautySavingCard,
        0 as HaidSavingCard,
        0 as TreatProjectHeadCount,
        0 as EntityCustomerCount,
        0 as SecondBuyCustomerCount,
        0 as FirstSaleCustomerCount,
        Count(*) as NoSaleCustomerCount
        FROM
        TB_Customer t WITH(NOLOCK)
        LEFT JOIN TB_CustomerEntity tc WITH(NOLOCK) ON t.ID=tc.CustomerID AND tc.IsBelongEntity=1
        LEFT JOIN TK_Entity tee WITH(NOLOCK) ON tee.ID=tc.EntityID
        WHERE NOT EXISTS
        (
        SELECT 1 FROM TB_SaleBill tt WITH(NOLOCK) WHERE tt.BillStatus='20' AND tt.BillType!='20' AND t.ID=tt.CustomerID
        )
        GROUP BY EntityID,EntityName
        )T
        <where>
            <if test="EntityID!=null and EntityID!=''">
                T.EntityID=#{EntityID}
            </if>
        </where>
        GROUP BY EntityID,EntityName,EntityName
        ORDER BY EntityName
    </select>
</mapper>