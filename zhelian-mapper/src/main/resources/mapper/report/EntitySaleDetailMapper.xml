<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.report.EntitySaleDetailMapper">
    <select id="getEntitySaleDetail" parameterType="String"
            resultType="com.zhelian.model.report.form.output.EntitySaleDetailStatementForm">
        SELECT BillID,CONVERT(varchar(100), BillDate, 120) AS
        BillDate,EntityName,CustomerCode,CustomerName,CustomerPhoneNumber,CreatedOn,CustomerLevelName,CustomerSourceName,IntroducerName,CustomerEntityName,EmployeeName,GoodsTypeName,CategoryName,GoodName,Price,Quantity,IsLargess,PreferentialAmount,TotalAmount,LargessAmount,ArrearAmount,PayAmount,SavingCardDeductionAmount,LargessSavingCardDeductionAmount,Channel,ChannelName,
        DetailsRemark,Remark
        FROM (
        SELECT
        SBP.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '项目' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        SBP.Price,
        SBP.Quantity,
        SBP.IsLargess,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SBP.IsLargess = 1 THEN 0 ELSE SBP.TotalAmount END AS TotalAmount,
        SBP.ArrearAmount,
        CASE WHEN SBP.IsLargess = 1 THEN SBP.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProject SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProjectID =
        SBP.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) LargessSavingCardDeductionAmount,
        (SELECT Top 1 Remark FROM TB_ProjectAccount tpa WITH (NOLOCK) WHERE tpa.SaleBillProjectID = SBP.ID) DetailsRemark,
        SB.Remark
        FROM TB_SaleBillProject SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON SBP.ProjectID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCC WITH(NOLOCK) ON TCC.CustomerID = C.ID AND TCC.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCC.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '10' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCC.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <if test="IsLargess != null ">
            and SBP.IsLargess =#{IsLargess}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT SBSC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '储值卡' AS GoodsTypeName,
        SCC.Name AS CategoryName,
        SC.Name AS GoodName,
        SBSC.Amount AS Price,
        SBSC.Quantity,
        0 AS IsLargess,
        (SBSC.TotalAmount-(SBSC.Amount*SBSC.Quantity)) AS PreferentialAmount,
        SBSC.TotalAmount,
        SBSC.ArrearAmount,
        SBSC.LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCard SBPSC WITH(NOLOCK) WHERE SBPSC.SaleBillSavingCardID
        = SBSC.ID) AS PayAmount,
        0 AS SavingCardDeductionAmount,
        0 AS LargessSavingCardDeductionAmount,
        (SELECT Top 1 Remark FROM TB_SavingCardAccount tpa WITH (NOLOCK) WHERE tpa.SaleBillSavingCardID = SBSC.ID) DetailsRemark,
        SB.Remark
        FROM TB_SaleBillSavingCard SBSC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBSC.BillID = SB.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SBSC.SavingCardID = SC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCC WITH(NOLOCK) ON TCC.CustomerID = C.ID AND TCC.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCC.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_SavingCardCategory SCC WITH(NOLOCK) ON SC.SavingCardCategoryID = SCC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE (SB.BillType = '10' OR SB.BillType = '40' ) AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND SC.SavingCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND SC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCC.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBTC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '时效卡' AS GoodsTypeName,
        TCCY.Name AS CategoryName,
        TC.Name AS GoodName,
        SBTC.Price,
        SBTC.Quantity,
        SBTC.IsLargess,
        (SBTC.MemberPreferentialAmount+SBTC.PricePreferentialAmount+SBTC.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SBTC.IsLargess = 1 THEN 0 ELSE SBTC.TotalAmount END AS TotalAmount,
        SBTC.ArrearAmount,
        CASE WHEN SBTC.IsLargess = 1 THEN SBTC.TotalAmount ELSE 0 END AS LargesAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayTimeCard SBPTC WITH(NOLOCK) WHERE SBPTC.SaleBillTimeCardID =
        SBTC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBTC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBTC.ID ) LargessSavingCardDeductionAmount,
        (SELECT Top 1 Remark FROM TB_TimeCardAccount tpa WITH (NOLOCK) WHERE tpa.SaleBillTimeCardID = SBTC.ID) DetailsRemark,
        SB.Remark
        FROM TB_SaleBillTimeCard SBTC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBTC.BillID = SB.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON SBTC.TimeCardID = TC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCC WITH(NOLOCK) ON TCC.CustomerID = C.ID AND TCC.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCC.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_TimeCardCategory TCCY WITH(NOLOCK) ON TCCY.ID = TC.TimeCardCategoryID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '10' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND TC.TimeCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND TC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCC.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <if test="IsLargess != null ">
            and SBTC.IsLargess =#{IsLargess}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBGC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '通用次卡' AS GoodsTypeName,
        GCC.Name AS CategoryName,
        GC.Name AS GoodName,
        SBGC.Price,
        SBGC.Quantity,
        SBGC.IsLargess,
        (SBGC.MemberPreferentialAmount+SBGC.PricePreferentialAmount+SBGC.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SBGC.IsLargess = 1 THEN 0 ELSE SBGC.TotalAmount END AS TotalAmount,
        SBGC.ArrearAmount,
        CASE WHEN SBGC.IsLargess = 1 THEN SBGC.TotalAmount ELSE 0 END AS LargesAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayGeneralCard SBPTC WITH(NOLOCK) WHERE
        SBPTC.SaleBillGeneralCardID = SBGC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillGeneralCardID = SBGC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK)
        WHERE SBPSCDTC.SaleBillGeneralCardID = SBGC.ID ) LargessSavingCardDeductionAmount,
        (SELECT Top 1 Remark FROM TB_GeneralCardAccount tpa WITH (NOLOCK) WHERE tpa.SaleBillGeneralCardID = SBGC.ID) DetailsRemark,
        SB.Remark
        FROM TB_SaleBillGeneralCard SBGC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBGC.BillID = SB.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON SBGC.GeneralCardID = GC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCC WITH(NOLOCK) ON TCC.CustomerID = C.ID AND TCC.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCC.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_GeneralCardCategory GCC WITH(NOLOCK) ON GCC.ID = GC.GeneralCardCategoryID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '10' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND GC.GeneralCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND GC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCC.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <if test="IsLargess != null ">
            and SBGC.IsLargess =#{IsLargess}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBPC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '套餐卡' AS GoodsTypeName,
        PCC.Name AS CategoryName,
        PC.Name AS GoodName,
        SBPC.Price,
        SBPC.Quantity,
        SBPC.IsLargess,
        (SBPC.MemberPreferentialAmount+SBPC.PricePreferentialAmount+SBPC.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SBPC.IsLargess = 1 THEN 0 ELSE SBPC.TotalAmount END AS TotalAmount,
        SBPC.ArrearAmount,
        CASE WHEN SBPC.IsLargess = 1 THEN SBPC.TotalAmount ELSE 0 END AS LargesAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCard SBPPC WITH(NOLOCK) WHERE
        SBPPC.SaleBillPackageCardID = SBPC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCard SBPSCDPC WITH(NOLOCK) WHERE
        SBPSCDPC.SaleBillPackageCardID = SBPC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCard SBPSCDPC WITH(NOLOCK)
        WHERE SBPSCDPC.SaleBillPackageCardID = SBPC.ID ) LargessSavingCardDeductionAmount,
        (SELECT Top 1 Remark FROM TB_PackageCardAccount tpa WITH (NOLOCK) WHERE tpa.SaleBillPackageCardID = SBPC.ID) DetailsRemark,
        SB.Remark
        FROM TB_SaleBillPackageCard SBPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBPC.BillID = SB.ID
        LEFT JOIN TB_PackageCard PC WITH(NOLOCK) ON SBPC.PackageCardID = PC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCC WITH(NOLOCK) ON TCC.CustomerID = C.ID AND TCC.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCC.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_PackageCardCategory PCC WITH(NOLOCK) ON PCC.ID = PC.PackageCardCategoryID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '10' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND PC.PackageCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND PC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCC.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <if test="IsLargess != null ">
            and SBPC.IsLargess =#{IsLargess}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBP.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '产品' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        SBP.Price,
        SBP.Quantity,
        SBP.IsLargess,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SBP.IsLargess = 1 THEN 0 ELSE SBP.TotalAmount END AS TotalAmount,
        SBP.ArrearAmount,
        CASE WHEN SBP.IsLargess = 1 THEN SBP.TotalAmount ELSE 0 END AS LargesAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProduct SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProductID =
        SBP.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) LargessSavingCardDeductionAmount,
        (SELECT Top 1 Remark FROM TB_ProductAccount tpa WITH (NOLOCK) WHERE tpa.SaleBillProductID = SBP.ID) DetailsRemark,
        SB.Remark
        FROM TB_SaleBillProduct SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON SBP.ProductID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCC WITH(NOLOCK) ON TCC.CustomerID = C.ID AND TCC.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCC.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TE_ProductCategory SubPC WITH(NOLOCK) ON P.PCategoryID = SubPC .ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON SubPC .ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '10' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND (P.PCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCC.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <if test="IsLargess != null ">
            and SBP.IsLargess =#{IsLargess}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        ) T
        <where>
            <if test="GoodsTypeName != null and GoodsTypeName != '' ">
                GoodsTypeName = #{GoodsTypeName}
            </if>
        </where>
        ORDER BY BillDate DESC
    </select>
    <select id="getEntitySaleDetailSum" parameterType="String"
            resultType="com.zhelian.model.report.form.output.EntitySaleDetailSumStatementForm">
        SELECT SUM(PreferentialAmount) AS PreferentialAmount,SUM(TotalAmount) AS TotalAmount,SUM(LargessAmount) AS
        LargessAmount,SUM(ArrearAmount) AS
        ArrearAmount,SUM(PayAmount) AS PayAmount,SUM(SavingCardDeductionAmount) AS
        SavingCardDeductionAmount,SUM(LargessSavingCardDeductionAmount) AS LargessSavingCardDeductionAmount
        FROM (
        SELECT
        '项目' AS GoodsTypeName,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SBP.IsLargess = 1 THEN 0 ELSE SBP.TotalAmount END AS TotalAmount,
        SBP.ArrearAmount,
        CASE WHEN SBP.IsLargess = 1 THEN SBP.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProject SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProjectID =
        SBP.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) LargessSavingCardDeductionAmount
        FROM TB_SaleBillProject SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCC WITH(NOLOCK) ON TCC.CustomerID = C.ID AND TCC.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCC.EntityID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON SBP.ProjectID = P.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        WHERE SB.BillType = '10' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCC.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <if test="IsLargess != null ">
            and SBP.IsLargess =#{IsLargess}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        '储值卡' AS GoodsTypeName,
        (SBSC.TotalAmount-(SBSC.Amount*SBSC.Quantity)) AS PreferentialAmount,
        SBSC.TotalAmount,
        SBSC.ArrearAmount,
        SBSC.LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCard SBPSC WITH(NOLOCK) WHERE SBPSC.SaleBillSavingCardID
        = SBSC.ID) AS PayAmount,
        0 AS SavingCardDeductionAmount,
        0 AS LargessSavingCardDeductionAmount
        FROM TB_SaleBillSavingCard SBSC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBSC.BillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCC WITH(NOLOCK) ON TCC.CustomerID = C.ID AND TCC.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCC.EntityID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SBSC.SavingCardID = SC.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        LEFT JOIN TB_SavingCardCategory SCC WITH(NOLOCK) ON SC.SavingCardCategoryID = SCC.ID
        WHERE (SB.BillType = '10' OR SB.BillType = '40' ) AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND SC.SavingCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND SC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCC.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        '时效卡' AS GoodsTypeName,
        (SBTC.MemberPreferentialAmount+SBTC.PricePreferentialAmount+SBTC.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SBTC.IsLargess = 1 THEN 0 ELSE SBTC.TotalAmount END AS TotalAmount,
        SBTC.ArrearAmount,
        CASE WHEN SBTC.IsLargess = 1 THEN SBTC.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayTimeCard SBPTC WITH(NOLOCK) WHERE SBPTC.SaleBillTimeCardID =
        SBTC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBTC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBTC.ID ) LargessSavingCardDeductionAmount
        FROM TB_SaleBillTimeCard SBTC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBTC.BillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCC WITH(NOLOCK) ON TCC.CustomerID = C.ID AND TCC.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCC.EntityID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON SBTC.TimeCardID = TC.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        LEFT JOIN TB_TimeCardCategory TCCY WITH(NOLOCK) ON TCCY.ID = TC.TimeCardCategoryID
        WHERE SB.BillType = '10' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND TC.TimeCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND TC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCC.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <if test="IsLargess != null ">
            and SBTC.IsLargess =#{IsLargess}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        '通用次卡' AS GoodsTypeName,
        (SBGC.MemberPreferentialAmount+SBGC.PricePreferentialAmount+SBGC.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SBGC.IsLargess = 1 THEN 0 ELSE SBGC.TotalAmount END AS TotalAmount,
        SBGC.ArrearAmount,
        CASE WHEN SBGC.IsLargess = 1 THEN SBGC.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayGeneralCard SBPTC WITH(NOLOCK) WHERE
        SBPTC.SaleBillGeneralCardID = SBGC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillGeneralCardID = SBGC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK)
        WHERE SBPSCDTC.SaleBillGeneralCardID = SBGC.ID ) LargessSavingCardDeductionAmount
        FROM TB_SaleBillGeneralCard SBGC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBGC.BillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCC WITH(NOLOCK) ON TCC.CustomerID = C.ID AND TCC.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCC.EntityID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON SBGC.GeneralCardID = GC.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        LEFT JOIN TB_GeneralCardCategory GCC WITH(NOLOCK) ON GCC.ID = GC.GeneralCardCategoryID
        WHERE SB.BillType = '10' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND GC.GeneralCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND GC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCC.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <if test="IsLargess != null ">
            and SBGC.IsLargess =#{IsLargess}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        '套餐卡' AS GoodsTypeName,
        (SBPC.MemberPreferentialAmount+SBPC.PricePreferentialAmount+SBPC.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SBPC.IsLargess = 1 THEN 0 ELSE SBPC.TotalAmount END AS TotalAmount,
        SBPC.ArrearAmount,
        CASE WHEN SBPC.IsLargess = 1 THEN SBPC.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCard SBPPC WITH(NOLOCK) WHERE
        SBPPC.SaleBillPackageCardID = SBPC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCard SBPSCDPC WITH(NOLOCK) WHERE
        SBPSCDPC.SaleBillPackageCardID = SBPC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCard SBPSCDPC WITH(NOLOCK)
        WHERE SBPSCDPC.SaleBillPackageCardID = SBPC.ID ) LargessSavingCardDeductionAmount
        FROM TB_SaleBillPackageCard SBPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBPC.BillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCC WITH(NOLOCK) ON TCC.CustomerID = C.ID AND TCC.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCC.EntityID
        LEFT JOIN TB_PackageCard PC WITH(NOLOCK) ON SBPC.PackageCardID = PC.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        LEFT JOIN TB_PackageCardCategory PCC WITH(NOLOCK) ON PCC.ID = PC.PackageCardCategoryID
        WHERE SB.BillType = '10' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND PC.PackageCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND PC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCC.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <if test="IsLargess != null ">
            and SBPC.IsLargess =#{IsLargess}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        '产品' AS GoodsTypeName,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SBP.IsLargess = 1 THEN 0 ELSE SBP.TotalAmount END AS TotalAmount,
        SBP.ArrearAmount,
        CASE WHEN SBP.IsLargess = 1 THEN SBP.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProduct SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProductID =
        SBP.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) LargessSavingCardDeductionAmount
        FROM TB_SaleBillProduct SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCC WITH(NOLOCK) ON TCC.CustomerID = C.ID AND TCC.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCC.EntityID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON SBP.ProductID = P.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        LEFT JOIN TE_ProductCategory SubPC WITH(NOLOCK) ON P.PCategoryID = SubPC .ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON SubPC .ParentID = PC.ID
        WHERE SB.BillType = '10' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND (P.PCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCC.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <if test="IsLargess != null ">
            and SBP.IsLargess =#{IsLargess}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        ) T
        <where>
            <if test="GoodsTypeName != null and GoodsTypeName != '' ">
                GoodsTypeName = #{GoodsTypeName}
            </if>
        </where>
    </select>
    <select id="getEasyExcelEntitySaleDetail" parameterType="String"
            resultType="com.zhelian.model.report.form.output.EntitySaleDetailStatementEasyExcelForm">
        SELECT BillID as billID,CONVERT(varchar(100), BillDate, 120) AS
        billDate,EntityName as entityName,CustomerName as customerName,CustomerCode as customerCode,REPLACE(CustomerPhoneNumber , SUBSTRING (CustomerPhoneNumber,4,4), '****') customerPhoneNumber,CreatedOn as createdOn,
        CustomerLevelName as customerLevelName,CustomerSourceName as customerSourceName,IntroducerName,
        CustomerEntityName as customerEntityName,EmployeeName as employeeName,GoodsTypeName as goodsTypeName,CategoryName as categoryName,GoodName as goodName,Price as price,Quantity as quantity,Largess as largess,PreferentialAmount as preferentialAmount,TotalAmount as totalAmount,LargessAmount as largessAmount,ArrearAmount as arrearAmount,PayAmount as payAmount,SavingCardDeductionAmount as savingCardDeductionAmount,LargessSavingCardDeductionAmount as largessSavingCardDeductionAmount,
        Channel as channel,ChannelName as channelName,
        DetailsRemark as detailsRemark,Remark as remark
        FROM (
        SELECT
        SBP.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '项目' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        SBP.Price,
        SBP.Quantity,
        CASE WHEN SBP.IsLargess=1 THEN '是'
        ELSE '否'
        END Largess,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SBP.IsLargess = 1 THEN 0 ELSE SBP.TotalAmount END AS TotalAmount,
        SBP.ArrearAmount,
        CASE WHEN SBP.IsLargess = 1 THEN SBP.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProject SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProjectID =
        SBP.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) LargessSavingCardDeductionAmount,
        (SELECT Top 1 Remark FROM TB_ProjectAccount tpa WITH (NOLOCK) WHERE tpa.SaleBillProjectID = SBP.ID) DetailsRemark,
        SB.Remark
        FROM TB_SaleBillProject SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON SBP.ProjectID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCC WITH(NOLOCK) ON TCC.CustomerID = C.ID AND TCC.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCC.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '10' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCC.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <if test="IsLargess != null ">
            and SBP.IsLargess =#{IsLargess}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT SBSC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '储值卡' AS GoodsTypeName,
        SCC.Name AS CategoryName,
        SC.Name AS GoodName,
        SBSC.Amount AS Price,
        SBSC.Quantity,
        '否' AS Largess,
        (SBSC.TotalAmount-(SBSC.Amount*SBSC.Quantity)) AS PreferentialAmount,
        SBSC.TotalAmount,
        SBSC.ArrearAmount,
        SBSC.LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCard SBPSC WITH(NOLOCK) WHERE SBPSC.SaleBillSavingCardID
        = SBSC.ID) AS PayAmount,
        0 AS SavingCardDeductionAmount,
        0 AS LargessSavingCardDeductionAmount,
        (SELECT Top 1 Remark FROM TB_SavingCardAccount tpa WITH (NOLOCK) WHERE tpa.SaleBillSavingCardID = SBSC.ID) DetailsRemark,
        SB.Remark
        FROM TB_SaleBillSavingCard SBSC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBSC.BillID = SB.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SBSC.SavingCardID = SC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCC WITH(NOLOCK) ON TCC.CustomerID = C.ID AND TCC.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCC.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_SavingCardCategory SCC WITH(NOLOCK) ON SC.SavingCardCategoryID = SCC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE (SB.BillType = '10' OR SB.BillType = '40' ) AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND SC.SavingCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND SC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCC.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBTC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '时效卡' AS GoodsTypeName,
        TCC.Name AS CategoryName,
        TC.Name AS GoodName,
        SBTC.Price,
        SBTC.Quantity,
        CASE WHEN SBTC.IsLargess=1 THEN '是'
        ELSE '否'
        END Largess,
        (SBTC.MemberPreferentialAmount+SBTC.PricePreferentialAmount+SBTC.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SBTC.IsLargess = 1 THEN 0 ELSE SBTC.TotalAmount END AS TotalAmount,
        SBTC.ArrearAmount,
        CASE WHEN SBTC.IsLargess = 1 THEN SBTC.TotalAmount ELSE 0 END AS LargesAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayTimeCard SBPTC WITH(NOLOCK) WHERE SBPTC.SaleBillTimeCardID =
        SBTC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBTC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBTC.ID ) LargessSavingCardDeductionAmount,
        (SELECT Top 1 Remark FROM TB_TimeCardAccount tpa WITH (NOLOCK) WHERE tpa.SaleBillTimeCardID = SBTC.ID) DetailsRemark,
        SB.Remark
        FROM TB_SaleBillTimeCard SBTC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBTC.BillID = SB.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON SBTC.TimeCardID = TC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_TimeCardCategory TCC WITH(NOLOCK) ON TCC.ID = TC.TimeCardCategoryID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '10' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND TC.TimeCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND TC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>

        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <if test="IsLargess != null ">
            and SBTC.IsLargess =#{IsLargess}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBGC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '通用次卡' AS GoodsTypeName,
        GCC.Name AS CategoryName,
        GC.Name AS GoodName,
        SBGC.Price,
        SBGC.Quantity,
        CASE WHEN SBGC.IsLargess=1 THEN '是'
        ELSE '否'
        END Largess,
        (SBGC.MemberPreferentialAmount+SBGC.PricePreferentialAmount+SBGC.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SBGC.IsLargess = 1 THEN 0 ELSE SBGC.TotalAmount END AS TotalAmount,
        SBGC.ArrearAmount,
        CASE WHEN SBGC.IsLargess = 1 THEN SBGC.TotalAmount ELSE 0 END AS TotalAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayGeneralCard SBPTC WITH(NOLOCK) WHERE
        SBPTC.SaleBillGeneralCardID = SBGC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillGeneralCardID = SBGC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK)
        WHERE SBPSCDTC.SaleBillGeneralCardID = SBGC.ID ) LargessSavingCardDeductionAmount,
        (SELECT Top 1 Remark FROM TB_GeneralCardAccount tpa WITH (NOLOCK) WHERE tpa.SaleBillGeneralCardID = SBGC.ID) DetailsRemark,
        SB.Remark
        FROM TB_SaleBillGeneralCard SBGC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBGC.BillID = SB.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON SBGC.GeneralCardID = GC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCC WITH(NOLOCK) ON TCC.CustomerID = C.ID AND TCC.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCC.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_GeneralCardCategory GCC WITH(NOLOCK) ON GCC.ID = GC.GeneralCardCategoryID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '10' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND GC.GeneralCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND GC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCC.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <if test="IsLargess != null ">
            and SBGC.IsLargess =#{IsLargess}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBPC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '套餐卡' AS GoodsTypeName,
        PCC.Name AS CategoryName,
        PC.Name AS GoodName,
        SBPC.Price,
        SBPC.Quantity,
        CASE WHEN SBPC.IsLargess=1 THEN '是'
        ELSE '否'
        END Largess,
        (SBPC.MemberPreferentialAmount+SBPC.PricePreferentialAmount+SBPC.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SBPC.IsLargess = 1 THEN 0 ELSE SBPC.TotalAmount END AS TotalAmount,
        SBPC.ArrearAmount,
        CASE WHEN SBPC.IsLargess = 1 THEN SBPC.TotalAmount ELSE 0 END AS TotalAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCard SBPPC WITH(NOLOCK) WHERE
        SBPPC.SaleBillPackageCardID = SBPC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCard SBPSCDPC WITH(NOLOCK) WHERE
        SBPSCDPC.SaleBillPackageCardID = SBPC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCard SBPSCDPC WITH(NOLOCK)
        WHERE SBPSCDPC.SaleBillPackageCardID = SBPC.ID ) LargessSavingCardDeductionAmount,
        (SELECT Top 1 Remark FROM TB_PackageCardAccount tpa WITH (NOLOCK) WHERE tpa.SaleBillPackageCardID = SBPC.ID) DetailsRemark,
        SB.Remark
        FROM TB_SaleBillPackageCard SBPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBPC.BillID = SB.ID
        LEFT JOIN TB_PackageCard PC WITH(NOLOCK) ON SBPC.PackageCardID = PC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCC WITH(NOLOCK) ON TCC.CustomerID = C.ID AND TCC.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCC.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_PackageCardCategory PCC WITH(NOLOCK) ON PCC.ID = PC.PackageCardCategoryID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '10' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND PC.PackageCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND PC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCC.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <if test="IsLargess != null ">
            and SBPC.IsLargess =#{IsLargess}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBP.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '产品' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        SBP.Price,
        SBP.Quantity,
        CASE WHEN SBP.IsLargess=1 THEN '是'
        ELSE '否'
        END Largess,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SBP.IsLargess = 1 THEN 0 ELSE SBP.TotalAmount END AS TotalAmount,
        SBP.ArrearAmount,
        CASE WHEN SBP.IsLargess = 1 THEN SBP.TotalAmount ELSE 0 END AS TotalAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProduct SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProductID =
        SBP.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) LargessSavingCardDeductionAmount,
        (SELECT Top 1 Remark FROM TB_ProductAccount tpa WITH (NOLOCK) WHERE tpa.SaleBillProductID = SBP.ID) DetailsRemark,
        SB.Remark
        FROM TB_SaleBillProduct SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON SBP.ProductID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCC WITH(NOLOCK) ON TCC.CustomerID = C.ID AND TCC.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCC.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TE_ProductCategory SubPC WITH(NOLOCK) ON P.PCategoryID = SubPC .ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON SubPC .ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '10' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND (P.PCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCC.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <if test="IsLargess != null ">
            and SBP.IsLargess =#{IsLargess}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        ) T
        <where>
            <if test="GoodsTypeName != null and GoodsTypeName != '' ">
                GoodsTypeName = #{GoodsTypeName}
            </if>
        </where>
        ORDER BY BillDate DESC
    </select>
    <select id="getEasyExcelDisPlayPhoneEntitySaleDetail" parameterType="String"
            resultType="com.zhelian.model.report.form.output.EntitySaleDetailStatementEasyExcelForm">
        SELECT BillID as billID,CONVERT(varchar(100), BillDate, 120) AS
        billDate,EntityName as entityName,CustomerName as customerName,CustomerCode as customerCode,CustomerPhoneNumber customerPhoneNumber,CreatedOn as createdOn,
        CustomerLevelName as customerLevelName,CustomerSourceName as customerSourceName,IntroducerName,
        CustomerEntityName as customerEntityName,EmployeeName as employeeName,GoodsTypeName as goodsTypeName,CategoryName as categoryName,GoodName as goodName,Price as price,Quantity as quantity,Largess as largess,PreferentialAmount as preferentialAmount,TotalAmount as totalAmount,LargessAmount as largessAmount,ArrearAmount as arrearAmount,PayAmount as payAmount,SavingCardDeductionAmount as savingCardDeductionAmount,LargessSavingCardDeductionAmount as largessSavingCardDeductionAmount,
        Channel as channel,ChannelName as channelName,
        DetailsRemark as detailsRemark,Remark as remark
        FROM (
        SELECT
        SBP.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '项目' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        SBP.Price,
        SBP.Quantity,
        CASE WHEN SBP.IsLargess=1 THEN '是'
        ELSE '否'
        END Largess,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SBP.IsLargess = 1 THEN 0 ELSE SBP.TotalAmount END AS TotalAmount,
        SBP.ArrearAmount,
        CASE WHEN SBP.IsLargess = 1 THEN SBP.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProject SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProjectID =
        SBP.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) LargessSavingCardDeductionAmount,
        (SELECT Top 1 Remark FROM TB_ProjectAccount tpa WITH (NOLOCK) WHERE tpa.SaleBillProjectID = SBP.ID) DetailsRemark,
        SB.Remark
        FROM TB_SaleBillProject SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON SBP.ProjectID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCC WITH(NOLOCK) ON TCC.CustomerID = C.ID AND TCC.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCC.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '10' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCC.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <if test="IsLargess != null ">
            and SBP.IsLargess =#{IsLargess}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT SBSC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '储值卡' AS GoodsTypeName,
        SCC.Name AS CategoryName,
        SC.Name AS GoodName,
        SBSC.Amount AS Price,
        SBSC.Quantity,
        '否' AS Largess,
        (SBSC.TotalAmount-(SBSC.Amount*SBSC.Quantity)) AS PreferentialAmount,
        SBSC.TotalAmount,
        SBSC.ArrearAmount,
        SBSC.LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCard SBPSC WITH(NOLOCK) WHERE SBPSC.SaleBillSavingCardID
        = SBSC.ID) AS PayAmount,
        0 AS SavingCardDeductionAmount,
        0 AS LargessSavingCardDeductionAmount,
        (SELECT Top 1 Remark FROM TB_SavingCardAccount tpa WITH (NOLOCK) WHERE tpa.SaleBillSavingCardID = SBSC.ID) DetailsRemark,
        SB.Remark
        FROM TB_SaleBillSavingCard SBSC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBSC.BillID = SB.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SBSC.SavingCardID = SC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCC WITH(NOLOCK) ON TCC.CustomerID = C.ID AND TCC.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCC.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_SavingCardCategory SCC WITH(NOLOCK) ON SC.SavingCardCategoryID = SCC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE (SB.BillType = '10' OR SB.BillType = '40' ) AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND SC.SavingCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND SC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCC.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBTC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '时效卡' AS GoodsTypeName,
        TCC.Name AS CategoryName,
        TC.Name AS GoodName,
        SBTC.Price,
        SBTC.Quantity,
        CASE WHEN SBTC.IsLargess=1 THEN '是'
        ELSE '否'
        END Largess,
        (SBTC.MemberPreferentialAmount+SBTC.PricePreferentialAmount+SBTC.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SBTC.IsLargess = 1 THEN 0 ELSE SBTC.TotalAmount END AS TotalAmount,
        SBTC.ArrearAmount,
        CASE WHEN SBTC.IsLargess = 1 THEN SBTC.TotalAmount ELSE 0 END AS LargesAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayTimeCard SBPTC WITH(NOLOCK) WHERE SBPTC.SaleBillTimeCardID =
        SBTC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBTC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBTC.ID ) LargessSavingCardDeductionAmount,
        (SELECT Top 1 Remark FROM TB_TimeCardAccount tpa WITH (NOLOCK) WHERE tpa.SaleBillTimeCardID = SBTC.ID) DetailsRemark,
        SB.Remark
        FROM TB_SaleBillTimeCard SBTC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBTC.BillID = SB.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON SBTC.TimeCardID = TC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_TimeCardCategory TCC WITH(NOLOCK) ON TCC.ID = TC.TimeCardCategoryID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '10' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND TC.TimeCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND TC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>

        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <if test="IsLargess != null ">
            and SBTC.IsLargess =#{IsLargess}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBGC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '通用次卡' AS GoodsTypeName,
        GCC.Name AS CategoryName,
        GC.Name AS GoodName,
        SBGC.Price,
        SBGC.Quantity,
        CASE WHEN SBGC.IsLargess=1 THEN '是'
        ELSE '否'
        END Largess,
        (SBGC.MemberPreferentialAmount+SBGC.PricePreferentialAmount+SBGC.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SBGC.IsLargess = 1 THEN 0 ELSE SBGC.TotalAmount END AS TotalAmount,
        SBGC.ArrearAmount,
        CASE WHEN SBGC.IsLargess = 1 THEN SBGC.TotalAmount ELSE 0 END AS TotalAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayGeneralCard SBPTC WITH(NOLOCK) WHERE
        SBPTC.SaleBillGeneralCardID = SBGC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillGeneralCardID = SBGC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK)
        WHERE SBPSCDTC.SaleBillGeneralCardID = SBGC.ID ) LargessSavingCardDeductionAmount,
        (SELECT Top 1 Remark FROM TB_GeneralCardAccount tpa WITH (NOLOCK) WHERE tpa.SaleBillGeneralCardID = SBGC.ID) DetailsRemark,
        SB.Remark
        FROM TB_SaleBillGeneralCard SBGC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBGC.BillID = SB.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON SBGC.GeneralCardID = GC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCC WITH(NOLOCK) ON TCC.CustomerID = C.ID AND TCC.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCC.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_GeneralCardCategory GCC WITH(NOLOCK) ON GCC.ID = GC.GeneralCardCategoryID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '10' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND GC.GeneralCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND GC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCC.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <if test="IsLargess != null ">
            and SBGC.IsLargess =#{IsLargess}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBPC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '套餐卡' AS GoodsTypeName,
        PCC.Name AS CategoryName,
        PC.Name AS GoodName,
        SBPC.Price,
        SBPC.Quantity,
        CASE WHEN SBPC.IsLargess=1 THEN '是'
        ELSE '否'
        END Largess,
        (SBPC.MemberPreferentialAmount+SBPC.PricePreferentialAmount+SBPC.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SBPC.IsLargess = 1 THEN 0 ELSE SBPC.TotalAmount END AS TotalAmount,
        SBPC.ArrearAmount,
        CASE WHEN SBPC.IsLargess = 1 THEN SBPC.TotalAmount ELSE 0 END AS TotalAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCard SBPPC WITH(NOLOCK) WHERE
        SBPPC.SaleBillPackageCardID = SBPC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCard SBPSCDPC WITH(NOLOCK) WHERE
        SBPSCDPC.SaleBillPackageCardID = SBPC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCard SBPSCDPC WITH(NOLOCK)
        WHERE SBPSCDPC.SaleBillPackageCardID = SBPC.ID ) LargessSavingCardDeductionAmount,
        (SELECT Top 1 Remark FROM TB_PackageCardAccount tpa WITH (NOLOCK) WHERE tpa.SaleBillPackageCardID = SBPC.ID) DetailsRemark,
        SB.Remark
        FROM TB_SaleBillPackageCard SBPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBPC.BillID = SB.ID
        LEFT JOIN TB_PackageCard PC WITH(NOLOCK) ON SBPC.PackageCardID = PC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCC WITH(NOLOCK) ON TCC.CustomerID = C.ID AND TCC.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCC.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_PackageCardCategory PCC WITH(NOLOCK) ON PCC.ID = PC.PackageCardCategoryID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '10' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND PC.PackageCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND PC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCC.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <if test="IsLargess != null ">
            and SBPC.IsLargess =#{IsLargess}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBP.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '产品' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        SBP.Price,
        SBP.Quantity,
        CASE WHEN SBP.IsLargess=1 THEN '是'
        ELSE '否'
        END Largess,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SBP.IsLargess = 1 THEN 0 ELSE SBP.TotalAmount END AS TotalAmount,
        SBP.ArrearAmount,
        CASE WHEN SBP.IsLargess = 1 THEN SBP.TotalAmount ELSE 0 END AS TotalAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProduct SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProductID =
        SBP.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) LargessSavingCardDeductionAmount,
        (SELECT Top 1 Remark FROM TB_ProductAccount tpa WITH (NOLOCK) WHERE tpa.SaleBillProductID = SBP.ID) DetailsRemark,
        SB.Remark
        FROM TB_SaleBillProduct SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON SBP.ProductID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCC WITH(NOLOCK) ON TCC.CustomerID = C.ID AND TCC.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCC.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TE_ProductCategory SubPC WITH(NOLOCK) ON P.PCategoryID = SubPC .ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON SubPC .ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '10' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND (P.PCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCC.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <if test="IsLargess != null ">
            and SBP.IsLargess =#{IsLargess}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        ) T
        <where>
            <if test="GoodsTypeName != null and GoodsTypeName != '' ">
                GoodsTypeName = #{GoodsTypeName}
            </if>
        </where>
        ORDER BY BillDate DESC
    </select>

    <select id="getEntitySaleArrearDetail" parameterType="String"
            resultType="com.zhelian.model.report.form.output.EntitySaleArrearDetailStatementForm">
        SELECT BillID,CONVERT(varchar(100), BillDate, 120) AS
        BillDate,EntityName,CustomerName,CustomerCode,CustomerPhoneNumber,CreatedOn,CustomerLevelName,CustomerSourceName,IntroducerName,CustomerEntityName,EmployeeName,GoodsTypeName,CategoryName,GoodName,BuyAmount,PayAmount,SavingCardDeductionAmount,LargessSavingCardDeductionAmount,Channel,ChannelName,Remark
        FROM (
        SELECT
        SBP.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '项目' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        PA.TotalAmount AS BuyAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProject SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProjectID =
        SBP.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillProject SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON SBP.ProjectID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_ProjectAccount PA WITH(NOLOCK) ON SBP.ProjectAccountID = PA.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '30' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT SBSC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '储值卡' AS GoodsTypeName,
        SCC.Name AS CategoryName,
        SC.Name AS GoodName,
        SCA.Amount AS BuyAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCard SBPSC WITH(NOLOCK) WHERE SBPSC.SaleBillSavingCardID
        = SBSC.ID) AS PayAmount,
        0 AS SavingCardDeductionAmount,
        0 AS LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillSavingCard SBSC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBSC.BillID = SB.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SBSC.SavingCardID = SC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_SavingCardCategory SCC WITH(NOLOCK) ON SC.SavingCardCategoryID = SCC.ID
        LEFT JOIN TB_SavingCardAccount SCA WITH(NOLOCK) ON SBSC.SavingCardAccountID = SCA.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '30' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND SC.SavingCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND SC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBTC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '时效卡' AS GoodsTypeName,
        TCC.Name AS CategoryName,
        TC.Name AS GoodName,
        TCA.TotalAmount AS BuyAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayTimeCard SBPTC WITH(NOLOCK) WHERE SBPTC.SaleBillTimeCardID =
        SBTC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBTC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBTC.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillTimeCard SBTC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBTC.BillID = SB.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON SBTC.TimeCardID = TC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_TimeCardCategory TCC WITH(NOLOCK) ON TCC.ID = TC.TimeCardCategoryID
        LEFT JOIN TB_TimeCardAccount TCA WITH(NOLOCK) ON SBTC.TimeCardAccountID = TCA.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '30' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>

        <if test=" CategoryID != null ">
            AND TC.TimeCardCategoryID=#{CategoryID}
        </if>

        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND TC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBGC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '通用次卡' AS GoodsTypeName,
        GCC.Name AS CategoryName,
        GC.Name AS GoodName,
        GCA.TotalAmount AS BuyAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayGeneralCard SBPTC WITH(NOLOCK) WHERE
        SBPTC.SaleBillGeneralCardID = SBGC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillGeneralCardID = SBGC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK)
        WHERE SBPSCDTC.SaleBillGeneralCardID = SBGC.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillGeneralCard SBGC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBGC.BillID = SB.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON SBGC.GeneralCardID = GC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_GeneralCardCategory GCC WITH(NOLOCK) ON GCC.ID = GC.GeneralCardCategoryID
        LEFT JOIN TB_GeneralCardAccount GCA WITH(NOLOCK) ON SBGC.GeneralCardAccountID = GCA.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '30' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND GC.GeneralCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND GC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBPC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '套餐卡' AS GoodsTypeName,
        PCC.Name AS CategoryName,
        PC.Name AS GoodName,
        PCA.TotalAmount AS BuyAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCard SBPPC WITH(NOLOCK) WHERE
        SBPPC.SaleBillPackageCardID = SBPC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCard SBPSCDPC WITH(NOLOCK) WHERE
        SBPSCDPC.SaleBillPackageCardID = SBPC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCard SBPSCDPC WITH(NOLOCK)
        WHERE SBPSCDPC.SaleBillPackageCardID = SBPC.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillPackageCard SBPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBPC.BillID = SB.ID
        LEFT JOIN TB_PackageCard PC WITH(NOLOCK) ON SBPC.PackageCardID = PC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_PackageCardCategory PCC WITH(NOLOCK) ON PCC.ID = PC.PackageCardCategoryID
        LEFT JOIN TB_PackageCardAccount PCA WITH(NOLOCK) ON SBPC.PackageCardAccountID = PCA.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '30' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND PC.PackageCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND PC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBP.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '产品' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        PA.TotalAmount AS BuyAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProduct SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProductID =
        SBP.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillProduct SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON SBP.ProductID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TE_ProductCategory SubPC WITH(NOLOCK) ON P.PCategoryID = SubPC .ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON SubPC .ParentID = PC.ID
        LEFT JOIN TB_ProductAccount PA WITH(NOLOCK) ON SBP.ProductAccountID = PA.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '30' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND (P.PCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        ) T
        <where>
            <if test="GoodsTypeName != null and GoodsTypeName != '' ">
                GoodsTypeName = #{GoodsTypeName}
            </if>
        </where>
        ORDER BY BillDate DESC
    </select>
    <select id="getEntitySaleArrearDetailSum" parameterType="String"
            resultType="com.zhelian.model.report.form.output.EntitySaleArrearDetailSumStatementForm">
        SELECT SUM(PayAmount) AS PayAmount,SUM(SavingCardDeductionAmount) AS SavingCardDeductionAmount,SUM(LargessSavingCardDeductionAmount) AS LargessSavingCardDeductionAmount
        FROM (
        SELECT
        '项目' AS GoodsTypeName,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProject SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProjectID =
        SBP.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) LargessSavingCardDeductionAmount
        FROM TB_SaleBillProject SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON SBP.ProjectID = P.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        WHERE SB.BillType = '30' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        '储值卡' AS GoodsTypeName,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCard SBPSC WITH(NOLOCK) WHERE SBPSC.SaleBillSavingCardID
        = SBSC.ID) AS PayAmount,
        0 AS SavingCardDeductionAmount,
        0 AS LargessSavingCardDeductionAmount
        FROM TB_SaleBillSavingCard SBSC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBSC.BillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SBSC.SavingCardID = SC.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        LEFT JOIN TB_SavingCardCategory SCC WITH(NOLOCK) ON SC.SavingCardCategoryID = SCC.ID
        WHERE SB.BillType = '30' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND SC.SavingCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND SC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        '时效卡' AS GoodsTypeName,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayTimeCard SBPTC WITH(NOLOCK) WHERE SBPTC.SaleBillTimeCardID =
        SBTC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBTC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBTC.ID ) LargessSavingCardDeductionAmount
        FROM TB_SaleBillTimeCard SBTC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBTC.BillID = SB.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON SBTC.TimeCardID = TC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        LEFT JOIN TB_TimeCardCategory TCC WITH(NOLOCK) ON TCC.ID = TC.TimeCardCategoryID
        WHERE SB.BillType = '30' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND TC.TimeCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND TC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        '通用次卡' AS GoodsTypeName,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayGeneralCard SBPTC WITH(NOLOCK) WHERE
        SBPTC.SaleBillGeneralCardID = SBGC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillGeneralCardID = SBGC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK)
        WHERE SBPSCDTC.SaleBillGeneralCardID = SBGC.ID ) LargessSavingCardDeductionAmount
        FROM TB_SaleBillGeneralCard SBGC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBGC.BillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON SBGC.GeneralCardID = GC.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        LEFT JOIN TB_GeneralCardCategory GCC WITH(NOLOCK) ON GCC.ID = GC.GeneralCardCategoryID
        WHERE SB.BillType = '30' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND GC.GeneralCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND GC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        '套餐卡' AS GoodsTypeName,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCard SBPPC WITH(NOLOCK) WHERE
        SBPPC.SaleBillPackageCardID = SBPC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCard SBPSCDPC WITH(NOLOCK) WHERE
        SBPSCDPC.SaleBillPackageCardID = SBPC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCard SBPSCDPC WITH(NOLOCK)
        WHERE SBPSCDPC.SaleBillPackageCardID = SBPC.ID ) LargessSavingCardDeductionAmount
        FROM TB_SaleBillPackageCard SBPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBPC.BillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TB_PackageCard PC WITH(NOLOCK) ON SBPC.PackageCardID = PC.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        LEFT JOIN TB_PackageCardCategory PCC WITH(NOLOCK) ON PCC.ID = PC.PackageCardCategoryID
        WHERE SB.BillType = '30' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND PC.PackageCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND PC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        '产品' AS GoodsTypeName,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProduct SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProductID =
        SBP.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) LargessSavingCardDeductionAmount
        FROM TB_SaleBillProduct SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON SBP.ProductID = P.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        LEFT JOIN TE_ProductCategory SubPC WITH(NOLOCK) ON P.PCategoryID = SubPC .ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON SubPC .ParentID = PC.ID
        WHERE SB.BillType = '30' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND (P.PCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        ) T
        <where>
            <if test="GoodsTypeName != null and GoodsTypeName != '' ">
                GoodsTypeName = #{GoodsTypeName}
            </if>
        </where>
    </select>

    <select id="getEasyExcelEntitySaleArrearDetail" parameterType="String"
            resultType="com.zhelian.model.report.form.output.EntitySaleArrearDetailStatementEasyExcelForm">
        SELECT BillID as billID,CONVERT(varchar(100), BillDate, 120) AS
        billDate,EntityName as entityName,CustomerName as customerName,CustomerCode as customerCode,REPLACE(CustomerPhoneNumber , SUBSTRING (CustomerPhoneNumber,4,4), '****') customerPhoneNumber,CreatedOn as createdOn,
        CustomerLevelName as customerLevelName,CustomerSourceName as customerSourceName,IntroducerName,
        CustomerEntityName as customerEntityName,EmployeeName as employeeName,GoodsTypeName as goodsTypeName,CategoryName as categoryName,GoodName as goodName,BuyAmount as buyAmount,
        PayAmount as payAmount,SavingCardDeductionAmount as savingCardDeductionAmount,LargessSavingCardDeductionAmount as largessSavingCardDeductionAmount,Channel as channel,ChannelName as channelName,Remark as remark
        FROM (
        SELECT
        SBP.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '项目' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        PA.TotalAmount AS BuyAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProject SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProjectID =
        SBP.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillProject SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON SBP.ProjectID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_ProjectAccount PA WITH(NOLOCK) ON SBP.ProjectAccountID = PA.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '30' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT SBSC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '储值卡' AS GoodsTypeName,
        SCC.Name AS CategoryName,
        SC.Name AS GoodName,
        SCA.Amount AS BuyAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCard SBPSC WITH(NOLOCK) WHERE SBPSC.SaleBillSavingCardID
        = SBSC.ID) AS PayAmount,
        0 AS SavingCardDeductionAmount,
        0 AS LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillSavingCard SBSC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBSC.BillID = SB.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SBSC.SavingCardID = SC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_SavingCardCategory SCC WITH(NOLOCK) ON SC.SavingCardCategoryID = SCC.ID
        LEFT JOIN TB_SavingCardAccount SCA WITH(NOLOCK) ON SBSC.SavingCardAccountID = SCA.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '30' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND SC.SavingCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND SC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBTC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '时效卡' AS GoodsTypeName,
        TCC.Name AS CategoryName,
        TC.Name AS GoodName,
        TCA.TotalAmount AS BuyAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayTimeCard SBPTC WITH(NOLOCK) WHERE SBPTC.SaleBillTimeCardID =
        SBTC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBTC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBTC.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillTimeCard SBTC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBTC.BillID = SB.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON SBTC.TimeCardID = TC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_TimeCardCategory TCC WITH(NOLOCK) ON TCC.ID = TC.TimeCardCategoryID
        LEFT JOIN TB_TimeCardAccount TCA WITH(NOLOCK) ON SBTC.TimeCardAccountID = TCA.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '30' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>

        <if test=" CategoryID != null ">
            AND TC.TimeCardCategoryID=#{CategoryID}
        </if>

        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND TC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBGC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '通用次卡' AS GoodsTypeName,
        GCC.Name AS CategoryName,
        GC.Name AS GoodName,
        GCA.TotalAmount AS BuyAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayGeneralCard SBPTC WITH(NOLOCK) WHERE
        SBPTC.SaleBillGeneralCardID = SBGC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillGeneralCardID = SBGC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK)
        WHERE SBPSCDTC.SaleBillGeneralCardID = SBGC.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillGeneralCard SBGC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBGC.BillID = SB.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON SBGC.GeneralCardID = GC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_GeneralCardCategory GCC WITH(NOLOCK) ON GCC.ID = GC.GeneralCardCategoryID
        LEFT JOIN TB_GeneralCardAccount GCA WITH(NOLOCK) ON SBGC.GeneralCardAccountID = GCA.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '30' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND GC.GeneralCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND GC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBPC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '套餐卡' AS GoodsTypeName,
        PCC.Name AS CategoryName,
        PC.Name AS GoodName,
        PCA.TotalAmount AS BuyAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCard SBPPC WITH(NOLOCK) WHERE
        SBPPC.SaleBillPackageCardID = SBPC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCard SBPSCDPC WITH(NOLOCK) WHERE
        SBPSCDPC.SaleBillPackageCardID = SBPC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCard SBPSCDPC WITH(NOLOCK)
        WHERE SBPSCDPC.SaleBillPackageCardID = SBPC.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillPackageCard SBPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBPC.BillID = SB.ID
        LEFT JOIN TB_PackageCard PC WITH(NOLOCK) ON SBPC.PackageCardID = PC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_PackageCardCategory PCC WITH(NOLOCK) ON PCC.ID = PC.PackageCardCategoryID
        LEFT JOIN TB_PackageCardAccount PCA WITH(NOLOCK) ON SBPC.PackageCardAccountID = PCA.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '30' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND PC.PackageCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND PC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBP.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '产品' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        PA.TotalAmount AS BuyAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProduct SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProductID =
        SBP.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillProduct SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON SBP.ProductID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TE_ProductCategory SubPC WITH(NOLOCK) ON P.PCategoryID = SubPC .ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON SubPC .ParentID = PC.ID
        LEFT JOIN TB_ProductAccount PA WITH(NOLOCK) ON SBP.ProductAccountID = PA.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '30' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND (P.PCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        ) T
        <where>
            <if test="GoodsTypeName != null and GoodsTypeName != '' ">
                GoodsTypeName = #{GoodsTypeName}
            </if>
        </where>
        ORDER BY BillDate DESC
    </select>
    <select id="getEasyExcelDisPlayPhoneEntitySaleArrearDetail" parameterType="String"
            resultType="com.zhelian.model.report.form.output.EntitySaleArrearDetailStatementEasyExcelForm">
        SELECT BillID as billID,CONVERT(varchar(100), BillDate, 120) AS
        billDate,EntityName as entityName,CustomerName as customerName,CustomerCode as customerCode,CustomerPhoneNumber customerPhoneNumber,CreatedOn as createdOn,
        CustomerLevelName as customerLevelName,CustomerSourceName as customerSourceName,IntroducerName,
        CustomerEntityName as customerEntityName,EmployeeName as employeeName,GoodsTypeName as goodsTypeName,CategoryName as categoryName,GoodName as goodName,BuyAmount as buyAmount,
        PayAmount as payAmount,SavingCardDeductionAmount as savingCardDeductionAmount,LargessSavingCardDeductionAmount as largessSavingCardDeductionAmount,Channel as channel,ChannelName as channelName,Remark as remark
        FROM (
        SELECT
        SBP.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '项目' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        PA.TotalAmount AS BuyAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProject SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProjectID =
        SBP.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillProject SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON SBP.ProjectID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_ProjectAccount PA WITH(NOLOCK) ON SBP.ProjectAccountID = PA.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '30' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT SBSC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '储值卡' AS GoodsTypeName,
        SCC.Name AS CategoryName,
        SC.Name AS GoodName,
        SCA.Amount AS BuyAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCard SBPSC WITH(NOLOCK) WHERE SBPSC.SaleBillSavingCardID
        = SBSC.ID) AS PayAmount,
        0 AS SavingCardDeductionAmount,
        0 AS LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillSavingCard SBSC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBSC.BillID = SB.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SBSC.SavingCardID = SC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_SavingCardCategory SCC WITH(NOLOCK) ON SC.SavingCardCategoryID = SCC.ID
        LEFT JOIN TB_SavingCardAccount SCA WITH(NOLOCK) ON SBSC.SavingCardAccountID = SCA.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '30' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND SC.SavingCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND SC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBTC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '时效卡' AS GoodsTypeName,
        TCC.Name AS CategoryName,
        TC.Name AS GoodName,
        TCA.TotalAmount AS BuyAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayTimeCard SBPTC WITH(NOLOCK) WHERE SBPTC.SaleBillTimeCardID =
        SBTC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBTC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBTC.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillTimeCard SBTC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBTC.BillID = SB.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON SBTC.TimeCardID = TC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_TimeCardCategory TCC WITH(NOLOCK) ON TCC.ID = TC.TimeCardCategoryID
        LEFT JOIN TB_TimeCardAccount TCA WITH(NOLOCK) ON SBTC.TimeCardAccountID = TCA.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '30' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>

        <if test=" CategoryID != null ">
            AND TC.TimeCardCategoryID=#{CategoryID}
        </if>

        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND TC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBGC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '通用次卡' AS GoodsTypeName,
        GCC.Name AS CategoryName,
        GC.Name AS GoodName,
        GCA.TotalAmount AS BuyAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayGeneralCard SBPTC WITH(NOLOCK) WHERE
        SBPTC.SaleBillGeneralCardID = SBGC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillGeneralCardID = SBGC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK)
        WHERE SBPSCDTC.SaleBillGeneralCardID = SBGC.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillGeneralCard SBGC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBGC.BillID = SB.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON SBGC.GeneralCardID = GC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_GeneralCardCategory GCC WITH(NOLOCK) ON GCC.ID = GC.GeneralCardCategoryID
        LEFT JOIN TB_GeneralCardAccount GCA WITH(NOLOCK) ON SBGC.GeneralCardAccountID = GCA.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '30' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND GC.GeneralCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND GC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBPC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '套餐卡' AS GoodsTypeName,
        PCC.Name AS CategoryName,
        PC.Name AS GoodName,
        PCA.TotalAmount AS BuyAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCard SBPPC WITH(NOLOCK) WHERE
        SBPPC.SaleBillPackageCardID = SBPC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCard SBPSCDPC WITH(NOLOCK) WHERE
        SBPSCDPC.SaleBillPackageCardID = SBPC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCard SBPSCDPC WITH(NOLOCK)
        WHERE SBPSCDPC.SaleBillPackageCardID = SBPC.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillPackageCard SBPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBPC.BillID = SB.ID
        LEFT JOIN TB_PackageCard PC WITH(NOLOCK) ON SBPC.PackageCardID = PC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_PackageCardCategory PCC WITH(NOLOCK) ON PCC.ID = PC.PackageCardCategoryID
        LEFT JOIN TB_PackageCardAccount PCA WITH(NOLOCK) ON SBPC.PackageCardAccountID = PCA.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '30' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND PC.PackageCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND PC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBP.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '产品' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        PA.TotalAmount AS BuyAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProduct SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProductID =
        SBP.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillProduct SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON SBP.ProductID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TE_ProductCategory SubPC WITH(NOLOCK) ON P.PCategoryID = SubPC .ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON SubPC .ParentID = PC.ID
        LEFT JOIN TB_ProductAccount PA WITH(NOLOCK) ON SBP.ProductAccountID = PA.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '30' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND (P.PCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        ) T
        <where>
            <if test="GoodsTypeName != null and GoodsTypeName != '' ">
                GoodsTypeName = #{GoodsTypeName}
            </if>
        </where>
        ORDER BY BillDate DESC
    </select>

    <select id="getEntitySaleRefundDetail" parameterType="String"
            resultType="com.zhelian.model.report.form.output.EntitySaleRefundDetailStatementForm">
        SELECT BillID,CONVERT(varchar(100), BillDate, 120) AS
        BillDate,EntityName,CustomerName,CustomerCode,CustomerPhoneNumber,CreatedOn,CustomerLevelName,CustomerSourceName,IntroducerName,CustomerEntityName,EmployeeName,GoodsTypeName,CategoryName,GoodName,Quantity,IsLargess,OriginAmount,TotalAmount,LargessAmount,PayAmount,SavingCardDeductionAmount,LargessSavingCardDeductionAmount,Channel,ChannelName,Remark
        FROM (
        SELECT
        SBP.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '项目' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        SBP.Quantity,
        SBP.IsLargess,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount + SBP.TotalAmount) AS OriginAmount,
        CASE WHEN SBP.IsLargess = 1 THEN 0 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SBP.IsLargess = 1 THEN SBP.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProject SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProjectID =
        SBP.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillProject SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON SBP.ProjectID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '20' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT SBSC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '储值卡' AS GoodsTypeName,
        SCC.Name AS CategoryName,
        SC.Name AS GoodName,
        SBSC.Quantity,
        0 AS IsLargess,
        SBSC.TotalAmount AS OriginAmount,
        SBSC.TotalAmount,
        SBSC.LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCard SBPSC WITH(NOLOCK) WHERE SBPSC.SaleBillSavingCardID
        = SBSC.ID) AS PayAmount,
        0 AS SavingCardDeductionAmount,
        0 AS LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillSavingCard SBSC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBSC.BillID = SB.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SBSC.SavingCardID = SC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_SavingCardCategory SCC WITH(NOLOCK) ON SC.SavingCardCategoryID = SCC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '20' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND SC.SavingCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND SC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBTC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '时效卡' AS GoodsTypeName,
        TCC.Name AS CategoryName,
        TC.Name AS GoodName,
        SBTC.Quantity,
        SBTC.IsLargess,
        (SBTC.MemberPreferentialAmount+SBTC.PricePreferentialAmount+SBTC.TotalAmount) AS OriginAmount,
        CASE WHEN SBTC.IsLargess = 1 THEN 0 ELSE SBTC.TotalAmount END AS TotalAmount,
        CASE WHEN SBTC.IsLargess = 1 THEN SBTC.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayTimeCard SBPTC WITH(NOLOCK) WHERE SBPTC.SaleBillTimeCardID =
        SBTC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBTC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBTC.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillTimeCard SBTC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBTC.BillID = SB.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON SBTC.TimeCardID = TC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_TimeCardCategory TCC WITH(NOLOCK) ON TCC.ID = TC.TimeCardCategoryID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '20' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>

        <if test=" CategoryID != null ">
            AND TC.TimeCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND TC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBGC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '通用次卡' AS GoodsTypeName,
        GCC.Name AS CategoryName,
        GC.Name AS GoodName,
        SBGC.Quantity,
        SBGC.IsLargess,
        (SBGC.MemberPreferentialAmount+SBGC.PricePreferentialAmount+SBGC.TotalAmount) AS OriginAmount,
        CASE WHEN SBGC.IsLargess = 1 THEN 0 ELSE SBGC.TotalAmount END AS TotalAmount,
        CASE WHEN SBGC.IsLargess = 1 THEN SBGC.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayGeneralCard SBPTC WITH(NOLOCK) WHERE
        SBPTC.SaleBillGeneralCardID = SBGC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillGeneralCardID = SBGC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK)
        WHERE SBPSCDTC.SaleBillGeneralCardID = SBGC.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillGeneralCard SBGC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBGC.BillID = SB.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON SBGC.GeneralCardID = GC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_GeneralCardCategory GCC WITH(NOLOCK) ON GCC.ID = GC.GeneralCardCategoryID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '20' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND GC.GeneralCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND GC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBPC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '套餐卡' AS GoodsTypeName,
        PCC.Name AS CategoryName,
        PC.Name AS GoodName,
        SBPC.Quantity,
        SBPC.IsLargess,
        (SBPC.MemberPreferentialAmount+SBPC.PricePreferentialAmount+SBPC.TotalAmount)AS OriginAmount,
        CASE WHEN SBPC.IsLargess = 1 THEN 0 ELSE SBPC.TotalAmount END AS TotalAmount,
        CASE WHEN SBPC.IsLargess = 1 THEN SBPC.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCard SBPPC WITH(NOLOCK) WHERE
        SBPPC.SaleBillPackageCardID = SBPC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCard SBPSCDPC WITH(NOLOCK) WHERE
        SBPSCDPC.SaleBillPackageCardID = SBPC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCard SBPSCDPC WITH(NOLOCK)
        WHERE SBPSCDPC.SaleBillPackageCardID = SBPC.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillPackageCard SBPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBPC.BillID = SB.ID
        LEFT JOIN TB_PackageCard PC WITH(NOLOCK) ON SBPC.PackageCardID = PC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_PackageCardCategory PCC WITH(NOLOCK) ON PCC.ID = PC.PackageCardCategoryID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '20' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND PC.PackageCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND PC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBP.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '产品' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        SBP.Quantity,
        SBP.IsLargess,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.TotalAmount) AS OriginAmount,
        CASE WHEN SBP.IsLargess = 1 THEN 0 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SBP.IsLargess = 1 THEN SBP.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProduct SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProductID =
        SBP.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillProduct SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON SBP.ProductID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TE_ProductCategory SubPC WITH(NOLOCK) ON P.PCategoryID = SubPC .ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON SubPC .ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '20' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND (P.PCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        ) T
        <where>
            <if test="GoodsTypeName != null and GoodsTypeName != '' ">
                GoodsTypeName = #{GoodsTypeName}
            </if>
        </where>
        ORDER BY BillDate DESC

    </select>
    <select id="getEntitySaleRefundDetailSum" parameterType="String"
            resultType="com.zhelian.model.report.form.output.EntitySaleRefundDetailSumStatementForm">
        SELECT SUM(OriginAmount) AS OriginAmount,SUM(TotalAmount) AS TotalAmount,SUM(LargessAmount) AS
        LargessAmount,SUM(PayAmount) AS PayAmount,SUM(SavingCardDeductionAmount) AS
        SavingCardDeductionAmount,SUM(LargessSavingCardDeductionAmount) AS LargessSavingCardDeductionAmount
        FROM (
        SELECT
        '项目' AS GoodsTypeName,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount + SBP.TotalAmount) AS OriginAmount,
        CASE WHEN SBP.IsLargess = 1 THEN 0 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SBP.IsLargess = 1 THEN SBP.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProject SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProjectID =
        SBP.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) LargessSavingCardDeductionAmount
        FROM TB_SaleBillProject SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON SBP.ProjectID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '20' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        '储值卡' AS GoodsTypeName,
        SBSC.TotalAmount AS OriginAmount,
        SBSC.TotalAmount,
        SBSC.LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCard SBPSC WITH(NOLOCK) WHERE SBPSC.SaleBillSavingCardID
        = SBSC.ID) AS PayAmount,
        0 AS SavingCardDeductionAmount,
        0 AS LargessSavingCardDeductionAmount
        FROM TB_SaleBillSavingCard SBSC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBSC.BillID = SB.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SBSC.SavingCardID = SC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_SavingCardCategory SCC WITH(NOLOCK) ON SC.SavingCardCategoryID = SCC.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '20' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND SC.SavingCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND SC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        '时效卡' AS GoodsTypeName,
        (SBTC.MemberPreferentialAmount+SBTC.PricePreferentialAmount+SBTC.TotalAmount) AS OriginAmount,
        CASE WHEN SBTC.IsLargess = 1 THEN 0 ELSE SBTC.TotalAmount END AS TotalAmount,
        CASE WHEN SBTC.IsLargess = 1 THEN SBTC.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayTimeCard SBPTC WITH(NOLOCK) WHERE SBPTC.SaleBillTimeCardID =
        SBTC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBTC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBTC.ID ) LargessSavingCardDeductionAmount
        FROM TB_SaleBillTimeCard SBTC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBTC.BillID = SB.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON SBTC.TimeCardID = TC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_TimeCardCategory TCC WITH(NOLOCK) ON TCC.ID = TC.TimeCardCategoryID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '20' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND TC.TimeCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND TC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        '通用次卡' AS GoodsTypeName,
        (SBGC.MemberPreferentialAmount+SBGC.PricePreferentialAmount+SBGC.TotalAmount) AS OriginAmount,
        CASE WHEN SBGC.IsLargess = 1 THEN 0 ELSE SBGC.TotalAmount END AS TotalAmount,
        CASE WHEN SBGC.IsLargess = 1 THEN SBGC.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayGeneralCard SBPTC WITH(NOLOCK) WHERE
        SBPTC.SaleBillGeneralCardID = SBGC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillGeneralCardID = SBGC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK)
        WHERE SBPSCDTC.SaleBillGeneralCardID = SBGC.ID ) LargessSavingCardDeductionAmount
        FROM TB_SaleBillGeneralCard SBGC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBGC.BillID = SB.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON SBGC.GeneralCardID = GC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_GeneralCardCategory GCC WITH(NOLOCK) ON GCC.ID = GC.GeneralCardCategoryID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '20' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND GC.GeneralCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND GC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        '套餐卡' AS GoodsTypeName,
        (SBPC.MemberPreferentialAmount+SBPC.PricePreferentialAmount+SBPC.TotalAmount) AS OriginAmount,
        CASE WHEN SBPC.IsLargess = 1 THEN 0 ELSE SBPC.TotalAmount END AS TotalAmount,
        CASE WHEN SBPC.IsLargess = 1 THEN SBPC.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCard SBPPC WITH(NOLOCK) WHERE
        SBPPC.SaleBillPackageCardID = SBPC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCard SBPSCDPC WITH(NOLOCK) WHERE
        SBPSCDPC.SaleBillPackageCardID = SBPC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCard SBPSCDPC WITH(NOLOCK)
        WHERE SBPSCDPC.SaleBillPackageCardID = SBPC.ID ) LargessSavingCardDeductionAmount
        FROM TB_SaleBillPackageCard SBPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBPC.BillID = SB.ID
        LEFT JOIN TB_PackageCard PC WITH(NOLOCK) ON SBPC.PackageCardID = PC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_PackageCardCategory PCC WITH(NOLOCK) ON PCC.ID = PC.PackageCardCategoryID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '20' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND PC.PackageCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND PC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        '产品' AS GoodsTypeName,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.TotalAmount) AS OriginAmount,
        CASE WHEN SBP.IsLargess = 1 THEN 0 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SBP.IsLargess = 1 THEN SBP.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProduct SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProductID =
        SBP.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) LargessSavingCardDeductionAmount
        FROM TB_SaleBillProduct SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON SBP.ProductID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TE_ProductCategory SubPC WITH(NOLOCK) ON P.PCategoryID = SubPC .ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON SubPC .ParentID = PC.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '20' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND (P.PCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        ) T
        <where>
            <if test="GoodsTypeName != null and GoodsTypeName != '' ">
                GoodsTypeName = #{GoodsTypeName}
            </if>
        </where>
    </select>

    <select id="getEasyExcelEntitySaleRefundDetail" parameterType="String"
            resultType="com.zhelian.model.report.form.output.EntitySaleRefundDetailStatementEasyExcelForm">
        SELECT BillID as billID,CONVERT(varchar(100), BillDate, 120) AS
        billDate,EntityName as entityName,CustomerName as customerName,CustomerCode as customerCode,REPLACE(CustomerPhoneNumber , SUBSTRING (CustomerPhoneNumber,4,4), '****') customerPhoneNumber,CreatedOn as createdOn,
        CustomerLevelName as customerLevelName,CustomerSourceName as customerSourceName,IntroducerName,
        CustomerEntityName as customerEntityName,EmployeeName as employeeName,GoodsTypeName as goodsTypeName,CategoryName as categoryName,GoodName as goodName,Quantity as quantity,IsLargess as isLargess,OriginAmount as originAmount,TotalAmount as totalAmount,LargessAmount as largessAmount,PayAmount as payAmount,SavingCardDeductionAmount as savingCardDeductionAmount,LargessSavingCardDeductionAmount as largessSavingCardDeductionAmount,Channel as channel,ChannelName as channelName,Remark as remark
        FROM (
        SELECT
        SBP.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '项目' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        SBP.Quantity,
        CASE WHEN SBP.IsLargess=1 THEN '是'
        ELSE '否'
        END IsLargess,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount + SBP.TotalAmount) AS OriginAmount,
        CASE WHEN SBP.IsLargess = 1 THEN 0 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SBP.IsLargess = 1 THEN SBP.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProject SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProjectID =
        SBP.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillProject SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON SBP.ProjectID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '20' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT SBSC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '储值卡' AS GoodsTypeName,
        SCC.Name AS CategoryName,
        SC.Name AS GoodName,
        SBSC.Quantity,
        '否' AS IsLargess,
        SBSC.TotalAmount AS OriginAmount,
        SBSC.TotalAmount,
        SBSC.LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCard SBPSC WITH(NOLOCK) WHERE SBPSC.SaleBillSavingCardID
        = SBSC.ID) AS PayAmount,
        0 AS SavingCardDeductionAmount,
        0 AS LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillSavingCard SBSC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBSC.BillID = SB.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SBSC.SavingCardID = SC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_SavingCardCategory SCC WITH(NOLOCK) ON SC.SavingCardCategoryID = SCC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '20' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND SC.SavingCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND SC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBTC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '时效卡' AS GoodsTypeName,
        TCC.Name AS CategoryName,
        TC.Name AS GoodName,
        SBTC.Quantity,
        CASE WHEN SBTC.IsLargess=1 THEN '是'
        ELSE '否'
        END IsLargess,
        (SBTC.MemberPreferentialAmount+SBTC.PricePreferentialAmount+SBTC.TotalAmount) AS OriginAmount,
        CASE WHEN SBTC.IsLargess = 1 THEN 0 ELSE SBTC.TotalAmount END AS TotalAmount,
        CASE WHEN SBTC.IsLargess = 1 THEN SBTC.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayTimeCard SBPTC WITH(NOLOCK) WHERE SBPTC.SaleBillTimeCardID =
        SBTC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBTC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBTC.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillTimeCard SBTC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBTC.BillID = SB.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON SBTC.TimeCardID = TC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_TimeCardCategory TCC WITH(NOLOCK) ON TCC.ID = TC.TimeCardCategoryID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '20' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND TC.TimeCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND TC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBGC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '通用次卡' AS GoodsTypeName,
        GCC.Name AS CategoryName,
        GC.Name AS GoodName,
        SBGC.Quantity,
        CASE WHEN SBGC.IsLargess=1 THEN '是'
        ELSE '否'
        END IsLargess,
        (SBGC.MemberPreferentialAmount+SBGC.PricePreferentialAmount+SBGC.TotalAmount) AS OriginAmount,
        CASE WHEN SBGC.IsLargess = 1 THEN 0 ELSE SBGC.TotalAmount END AS TotalAmount,
        CASE WHEN SBGC.IsLargess = 1 THEN SBGC.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayGeneralCard SBPTC WITH(NOLOCK) WHERE
        SBPTC.SaleBillGeneralCardID = SBGC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillGeneralCardID = SBGC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK)
        WHERE SBPSCDTC.SaleBillGeneralCardID = SBGC.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillGeneralCard SBGC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBGC.BillID = SB.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON SBGC.GeneralCardID = GC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_GeneralCardCategory GCC WITH(NOLOCK) ON GCC.ID = GC.GeneralCardCategoryID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '20' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND GC.GeneralCardCategoryID=#{CategoryID}
        </if>

        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND GC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBPC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '套餐卡' AS GoodsTypeName,
        PCC.Name AS CategoryName,
        PC.Name AS GoodName,
        SBPC.Quantity,
        CASE WHEN SBPC.IsLargess=1 THEN '是'
        ELSE '否'
        END IsLargess,
        (SBPC.MemberPreferentialAmount+SBPC.PricePreferentialAmount+SBPC.TotalAmount)AS OriginAmount,
        CASE WHEN SBPC.IsLargess = 1 THEN 0 ELSE SBPC.TotalAmount END AS TotalAmount,
        CASE WHEN SBPC.IsLargess = 1 THEN SBPC.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCard SBPPC WITH(NOLOCK) WHERE
        SBPPC.SaleBillPackageCardID = SBPC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCard SBPSCDPC WITH(NOLOCK) WHERE
        SBPSCDPC.SaleBillPackageCardID = SBPC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCard SBPSCDPC WITH(NOLOCK)
        WHERE SBPSCDPC.SaleBillPackageCardID = SBPC.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillPackageCard SBPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBPC.BillID = SB.ID
        LEFT JOIN TB_PackageCard PC WITH(NOLOCK) ON SBPC.PackageCardID = PC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_PackageCardCategory PCC WITH(NOLOCK) ON PCC.ID = PC.PackageCardCategoryID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '20' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND PC.PackageCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND PC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBP.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '产品' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        SBP.Quantity,
        CASE WHEN SBP.IsLargess=1 THEN '是'
        ELSE '否'
        END IsLargess,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.TotalAmount) AS OriginAmount,
        CASE WHEN SBP.IsLargess = 1 THEN 0 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SBP.IsLargess = 1 THEN SBP.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProduct SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProductID =
        SBP.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillProduct SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON SBP.ProductID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TE_ProductCategory SubPC WITH(NOLOCK) ON P.PCategoryID = SubPC .ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON SubPC .ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '20' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND (P.PCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        ) T
        <where>
            <if test="GoodsTypeName != null and GoodsTypeName != '' ">
                GoodsTypeName = #{GoodsTypeName}
            </if>
        </where>
        ORDER BY BillDate DESC

    </select>
    <select id="getEasyExcelDisPlayPhoneEntitySaleRefundDetail" parameterType="String"
            resultType="com.zhelian.model.report.form.output.EntitySaleRefundDetailStatementEasyExcelForm">
        SELECT BillID as billID,CONVERT(varchar(100), BillDate, 120) AS
        billDate,EntityName as entityName,CustomerName as customerName,CustomerCode as customerCode,CustomerPhoneNumber customerPhoneNumber,CreatedOn as createdOn,
        CustomerLevelName as customerLevelName,CustomerSourceName as customerSourceName,IntroducerName,
        CustomerEntityName as customerEntityName,EmployeeName as employeeName,GoodsTypeName as goodsTypeName,CategoryName as categoryName,GoodName as goodName,Quantity as quantity,IsLargess as isLargess,OriginAmount as originAmount,TotalAmount as totalAmount,LargessAmount as largessAmount,PayAmount as payAmount,SavingCardDeductionAmount as savingCardDeductionAmount,LargessSavingCardDeductionAmount as largessSavingCardDeductionAmount,Channel as channel,ChannelName as channelName,Remark as remark
        FROM (
        SELECT
        SBP.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '项目' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        SBP.Quantity,
        CASE WHEN SBP.IsLargess=1 THEN '是'
        ELSE '否'
        END IsLargess,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount + SBP.TotalAmount) AS OriginAmount,
        CASE WHEN SBP.IsLargess = 1 THEN 0 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SBP.IsLargess = 1 THEN SBP.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProject SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProjectID =
        SBP.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillProject SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON SBP.ProjectID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '20' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT SBSC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '储值卡' AS GoodsTypeName,
        SCC.Name AS CategoryName,
        SC.Name AS GoodName,
        SBSC.Quantity,
        '否' AS IsLargess,
        SBSC.TotalAmount AS OriginAmount,
        SBSC.TotalAmount,
        SBSC.LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCard SBPSC WITH(NOLOCK) WHERE SBPSC.SaleBillSavingCardID
        = SBSC.ID) AS PayAmount,
        0 AS SavingCardDeductionAmount,
        0 AS LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillSavingCard SBSC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBSC.BillID = SB.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SBSC.SavingCardID = SC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_SavingCardCategory SCC WITH(NOLOCK) ON SC.SavingCardCategoryID = SCC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '20' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND SC.SavingCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND SC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBTC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '时效卡' AS GoodsTypeName,
        TCC.Name AS CategoryName,
        TC.Name AS GoodName,
        SBTC.Quantity,
        CASE WHEN SBTC.IsLargess=1 THEN '是'
        ELSE '否'
        END IsLargess,
        (SBTC.MemberPreferentialAmount+SBTC.PricePreferentialAmount+SBTC.TotalAmount) AS OriginAmount,
        CASE WHEN SBTC.IsLargess = 1 THEN 0 ELSE SBTC.TotalAmount END AS TotalAmount,
        CASE WHEN SBTC.IsLargess = 1 THEN SBTC.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayTimeCard SBPTC WITH(NOLOCK) WHERE SBPTC.SaleBillTimeCardID =
        SBTC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBTC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBTC.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillTimeCard SBTC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBTC.BillID = SB.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON SBTC.TimeCardID = TC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_TimeCardCategory TCC WITH(NOLOCK) ON TCC.ID = TC.TimeCardCategoryID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '20' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND TC.TimeCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND TC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBGC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '通用次卡' AS GoodsTypeName,
        GCC.Name AS CategoryName,
        GC.Name AS GoodName,
        SBGC.Quantity,
        CASE WHEN SBGC.IsLargess=1 THEN '是'
        ELSE '否'
        END IsLargess,
        (SBGC.MemberPreferentialAmount+SBGC.PricePreferentialAmount+SBGC.TotalAmount) AS OriginAmount,
        CASE WHEN SBGC.IsLargess = 1 THEN 0 ELSE SBGC.TotalAmount END AS TotalAmount,
        CASE WHEN SBGC.IsLargess = 1 THEN SBGC.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayGeneralCard SBPTC WITH(NOLOCK) WHERE
        SBPTC.SaleBillGeneralCardID = SBGC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillGeneralCardID = SBGC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK)
        WHERE SBPSCDTC.SaleBillGeneralCardID = SBGC.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillGeneralCard SBGC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBGC.BillID = SB.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON SBGC.GeneralCardID = GC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_GeneralCardCategory GCC WITH(NOLOCK) ON GCC.ID = GC.GeneralCardCategoryID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '20' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND GC.GeneralCardCategoryID=#{CategoryID}
        </if>

        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND GC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBPC.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '套餐卡' AS GoodsTypeName,
        PCC.Name AS CategoryName,
        PC.Name AS GoodName,
        SBPC.Quantity,
        CASE WHEN SBPC.IsLargess=1 THEN '是'
        ELSE '否'
        END IsLargess,
        (SBPC.MemberPreferentialAmount+SBPC.PricePreferentialAmount+SBPC.TotalAmount)AS OriginAmount,
        CASE WHEN SBPC.IsLargess = 1 THEN 0 ELSE SBPC.TotalAmount END AS TotalAmount,
        CASE WHEN SBPC.IsLargess = 1 THEN SBPC.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCard SBPPC WITH(NOLOCK) WHERE
        SBPPC.SaleBillPackageCardID = SBPC.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCard SBPSCDPC WITH(NOLOCK) WHERE
        SBPSCDPC.SaleBillPackageCardID = SBPC.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCard SBPSCDPC WITH(NOLOCK)
        WHERE SBPSCDPC.SaleBillPackageCardID = SBPC.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillPackageCard SBPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBPC.BillID = SB.ID
        LEFT JOIN TB_PackageCard PC WITH(NOLOCK) ON SBPC.PackageCardID = PC.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TB_PackageCardCategory PCC WITH(NOLOCK) ON PCC.ID = PC.PackageCardCategoryID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '20' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND PC.PackageCardCategoryID=#{CategoryID}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND PC.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        SBP.BillID,
        SB.BillDate,
        Ent.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        CASE WHEN SB.Channel ='PC' THEN 'pc'
        WHEN SB.Channel ='Miniprogram' THEN '小程序'
        WHEN SB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tccc.Name ChannelName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        CONVERT(VARCHAR(19),C.CreatedOn,120) AS CreatedOn,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        Intro.Name AS IntroducerName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        EMP.Name AS EmployeeName,
        '产品' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        SBP.Quantity,
        CASE WHEN SBP.IsLargess=1 THEN '是'
        ELSE '否'
        END IsLargess,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.TotalAmount) AS OriginAmount,
        CASE WHEN SBP.IsLargess = 1 THEN 0 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SBP.IsLargess = 1 THEN SBP.TotalAmount ELSE 0 END AS LargessAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProduct SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProductID =
        SBP.ID) AS PayAmount,
        (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) SavingCardDeductionAmount,
        (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) LargessSavingCardDeductionAmount,SB.Remark
        FROM TB_SaleBillProduct SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON SBP.ProductID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON SB.CustomerID = C.ID
        LEFT JOIN TB_Channel tccc WITH(NOLOCK) ON tccc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON SB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SB.EmployeeID = Emp.ID
        LEFT JOIN TE_ProductCategory SubPC WITH(NOLOCK) ON P.PCategoryID = SubPC .ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON SubPC .ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        LEFT JOIN TB_Customer Intro WITH(NOLOCK) ON C.Introducer = Intro.ID
        WHERE SB.BillType = '20' AND SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <if test=" CategoryID != null ">
            AND (P.PCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="CreateStartDate != null and CreateEndDate != null ">
            AND C.CreatedOn BETWEEN #{CreateStartDate} AND DATEADD(DAY,1,#{CreateEndDate})
        </if>
        <if test="Channel != null and Channel != ''">
            AND SB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( Intro.Name like '%'+ #{IntroducerName} +'%' or Intro.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            Intro.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tccc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND SB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        ) T
        <where>
            <if test="GoodsTypeName != null and GoodsTypeName != '' ">
                GoodsTypeName = #{GoodsTypeName}
            </if>
        </where>
        ORDER BY BillDate DESC

    </select>
</mapper>