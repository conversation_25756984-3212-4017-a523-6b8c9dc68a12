<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.report.ConsultantTreatPerformanceDetailMapper">
    <select id="getConsultantTreatPerformanceDetail" resultType="com.zhelian.model.report.form.output.ConsultantTreatPerformanceDetailStatementForm">
        SELECT
        TreatBillID,
        CONVERT(varchar(100), BillDate, 120) AS BillDate,
        CASE BillType
        WHEN '10' THEN '消耗单'
        WHEN '20' THEN '消耗退单' END AS BillType,
        EntityName,
        CustomerName,
        PhoneNumber,
        Code,
        ChannelName,
        ChannelType,
        TreatCardTypeName,
        CardName,
        CategoryName,
        GoodName,
        EmployeeName,
        JobName,
        PayPerformance,
        CardPerformance,
        CardLargessPerformance,
        LargessPerformance
        FROM (
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '项目卡' AS TreatCardTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformanceProject TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_TreatBillProject TBP WITH(NOLOCK) ON TEPC.TreatBillProjectID = TBP.ID
        LEFT JOIN TB_ProjectAccount PA WITH(NOLOCK) ON TBP.ProjectAccountID = PA.ID
        LEFT JOIN TB_Project PAP WITH(NOLOCK) ON PA.ProjectID = PAP.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '项目卡' AS TreatCardTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformancePackageCardProject TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_TreatBillProject TBP WITH(NOLOCK) ON TEPC.TreatBillProjectID = TBP.ID
        LEFT JOIN TB_ProjectAccount PA WITH(NOLOCK) ON TBP.ProjectAccountID = PA.ID
        LEFT JOIN TB_Project PAP WITH(NOLOCK) ON PA.ProjectID = PAP.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '储值卡' AS TreatCardTypeName,
        SC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        0 AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END as CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END as CardLargessPerformance,
        0 AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformanceSavingCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON TEPC.SavingCardID = SC.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND SC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '储值卡' AS TreatCardTypeName,
        SC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        0 AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END as CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END as CardLargessPerformance,
        0 AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformancePackageCardSavingCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON TEPC.SavingCardID = SC.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND SC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '时效卡' AS TreatCardTypeName,
        TC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformanceTimeCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON TEPC.TimeCardID = TC.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND TC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '时效卡' AS TreatCardTypeName,
        TC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformancePackageCardTimeCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON TEPC.TimeCardID = TC.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND TC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '通用次卡' AS TreatCardTypeName,
        GC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformanceGeneralCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON TEPC.GeneralCardID = GC.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND GC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '通用次卡' AS TreatCardTypeName,
        GC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformancePackageCardGeneralCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON TEPC.GeneralCardID = GC.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND GC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '产品卡' AS TreatCardTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformanceProduct TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON TEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON P.PCategoryID = PC.ID
        LEFT JOIN TB_TreatBillProduct TBP WITH(NOLOCK) ON TEPC.TreatBillProductID = TBP.ID
        LEFT JOIN TB_ProductAccount PA WITH(NOLOCK) ON TBP.ProductAccountID = PA.ID
        LEFT JOIN TE_Product PAP WITH(NOLOCK) ON PA.ProductID = PAP.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '产品卡' AS TreatCardTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformancePackageCardProduct TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON TEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON P.PCategoryID = PC.ID
        LEFT JOIN TB_TreatBillProduct TBP WITH(NOLOCK) ON TEPC.TreatBillProductID = TBP.ID
        LEFT JOIN TB_ProductAccount PA WITH(NOLOCK) ON TBP.ProductAccountID = PA.ID
        LEFT JOIN TE_Product PAP WITH(NOLOCK) ON PA.ProductID = PAP.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        ) T
        <where>
            <if test="TreatCardTypeName != null and TreatCardTypeName != '' ">
                TreatCardTypeName = #{TreatCardTypeName}
            </if>
        </where>
        ORDER BY BillDate DESC
    </select>

    <select id="getConsultantTreatPerformanceSum" resultType="com.zhelian.model.report.form.output.ConsultantTreatPerformanceSumStatementForm">
        SELECT SUM(PayPerformance) AS PayPerformance,
        SUM(CardPerformance) AS CardPerformance,
        SUM(CardLargessPerformance) AS CardLargessPerformance,
        SUM(LargessPerformance) AS LargessPerformance
        FROM (
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '项目卡' AS TreatCardTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformanceProject TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_TreatBillProject TBP WITH(NOLOCK) ON TEPC.TreatBillProjectID = TBP.ID
        LEFT JOIN TB_ProjectAccount PA WITH(NOLOCK) ON TBP.ProjectAccountID = PA.ID
        LEFT JOIN TB_Project PAP WITH(NOLOCK) ON PA.ProjectID = PAP.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '项目卡' AS TreatCardTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformancePackageCardProject TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_TreatBillProject TBP WITH(NOLOCK) ON TEPC.TreatBillProjectID = TBP.ID
        LEFT JOIN TB_ProjectAccount PA WITH(NOLOCK) ON TBP.ProjectAccountID = PA.ID
        LEFT JOIN TB_Project PAP WITH(NOLOCK) ON PA.ProjectID = PAP.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '储值卡' AS TreatCardTypeName,
        SC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        0 AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END as CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END as CardLargessPerformance,
        0 AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformanceSavingCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON TEPC.SavingCardID = SC.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND SC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '储值卡' AS TreatCardTypeName,
        SC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        0 AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END as CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END as CardLargessPerformance,
        0 AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformancePackageCardSavingCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON TEPC.SavingCardID = SC.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND SC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '时效卡' AS TreatCardTypeName,
        TC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformanceTimeCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON TEPC.TimeCardID = TC.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND TC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '时效卡' AS TreatCardTypeName,
        TC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformancePackageCardTimeCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON TEPC.TimeCardID = TC.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND TC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '通用次卡' AS TreatCardTypeName,
        GC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformanceGeneralCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON TEPC.GeneralCardID = GC.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND GC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '通用次卡' AS TreatCardTypeName,
        GC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformancePackageCardGeneralCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON TEPC.GeneralCardID = GC.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND GC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '产品卡' AS TreatCardTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformanceProduct TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON TEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON P.PCategoryID = PC.ID
        LEFT JOIN TB_TreatBillProduct TBP WITH(NOLOCK) ON TEPC.TreatBillProductID = TBP.ID
        LEFT JOIN TB_ProductAccount PA WITH(NOLOCK) ON TBP.ProductAccountID = PA.ID
        LEFT JOIN TE_Product PAP WITH(NOLOCK) ON PA.ProductID = PAP.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '产品卡' AS TreatCardTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformancePackageCardProduct TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON TEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON P.PCategoryID = PC.ID
        LEFT JOIN TB_TreatBillProduct TBP WITH(NOLOCK) ON TEPC.TreatBillProductID = TBP.ID
        LEFT JOIN TB_ProductAccount PA WITH(NOLOCK) ON TBP.ProductAccountID = PA.ID
        LEFT JOIN TE_Product PAP WITH(NOLOCK) ON PA.ProductID = PAP.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        ) T
        <where>
            <if test="TreatCardTypeName != null and TreatCardTypeName != '' ">
                TreatCardTypeName = #{TreatCardTypeName}
            </if>
        </where>
    </select>

    <select id="getExcelConsultantTreatPerformanceDetail" resultType="com.zhelian.model.report.form.output.ConsultantTreatPerformanceDetailStatementExcelForm">
        SELECT
        TreatBillID,
        CONVERT(varchar(100), BillDate, 120) AS BillDate,
        CASE BillType
        WHEN '10' THEN '消耗单'
        WHEN '20' THEN '消耗退单' END AS BillType,
        EntityName,
        CustomerName,
        PhoneNumber,
        Code,
        ChannelName,
        ChannelType,
        TreatCardTypeName,
        CardName,
        CategoryName,
        GoodName,
        EmployeeName,
        JobName,
        PayPerformance,
        CardPerformance,
        CardLargessPerformance,
        LargessPerformance
        FROM (
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '项目卡' AS TreatCardTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformanceProject TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_TreatBillProject TBP WITH(NOLOCK) ON TEPC.TreatBillProjectID = TBP.ID
        LEFT JOIN TB_ProjectAccount PA WITH(NOLOCK) ON TBP.ProjectAccountID = PA.ID
        LEFT JOIN TB_Project PAP WITH(NOLOCK) ON PA.ProjectID = PAP.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '项目卡' AS TreatCardTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformancePackageCardProject TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_TreatBillProject TBP WITH(NOLOCK) ON TEPC.TreatBillProjectID = TBP.ID
        LEFT JOIN TB_ProjectAccount PA WITH(NOLOCK) ON TBP.ProjectAccountID = PA.ID
        LEFT JOIN TB_Project PAP WITH(NOLOCK) ON PA.ProjectID = PAP.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '储值卡' AS TreatCardTypeName,
        SC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        0 AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END as CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END as CardLargessPerformance,
        0 AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformanceSavingCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON TEPC.SavingCardID = SC.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND SC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '储值卡' AS TreatCardTypeName,
        SC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        0 AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END as CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END as CardLargessPerformance,
        0 AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformancePackageCardSavingCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON TEPC.SavingCardID = SC.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND SC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '时效卡' AS TreatCardTypeName,
        TC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformanceTimeCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON TEPC.TimeCardID = TC.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND TC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '时效卡' AS TreatCardTypeName,
        TC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformancePackageCardTimeCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON TEPC.TimeCardID = TC.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND TC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '通用次卡' AS TreatCardTypeName,
        GC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformanceGeneralCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON TEPC.GeneralCardID = GC.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND GC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '通用次卡' AS TreatCardTypeName,
        GC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformancePackageCardGeneralCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON TEPC.GeneralCardID = GC.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND GC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '产品卡' AS TreatCardTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformanceProduct TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON TEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON P.PCategoryID = PC.ID
        LEFT JOIN TB_TreatBillProduct TBP WITH(NOLOCK) ON TEPC.TreatBillProductID = TBP.ID
        LEFT JOIN TB_ProductAccount PA WITH(NOLOCK) ON TBP.ProductAccountID = PA.ID
        LEFT JOIN TE_Product PAP WITH(NOLOCK) ON PA.ProductID = PAP.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '产品卡' AS TreatCardTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformancePackageCardProduct TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON TEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON P.PCategoryID = PC.ID
        LEFT JOIN TB_TreatBillProduct TBP WITH(NOLOCK) ON TEPC.TreatBillProductID = TBP.ID
        LEFT JOIN TB_ProductAccount PA WITH(NOLOCK) ON TBP.ProductAccountID = PA.ID
        LEFT JOIN TE_Product PAP WITH(NOLOCK) ON PA.ProductID = PAP.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        ) T
        <where>
            <if test="TreatCardTypeName != null and TreatCardTypeName != '' ">
                TreatCardTypeName = #{TreatCardTypeName}
            </if>
        </where>
        ORDER BY BillDate DESC
    </select>

    <select id="getEasyExcelConsultantTreatPerformanceDetail" resultType="com.zhelian.model.report.form.output.ConsultantTreatPerformanceDetailStatementEasyExcelForm">
        SELECT
        treatBillID,
        CONVERT(varchar(100), BillDate, 120) AS billDate,
        CASE BillType
        WHEN '10' THEN '消耗单'
        WHEN '20' THEN '消耗退单' END AS billType,
        entityName,
        customerName,
        phoneNumber,
        code,
        channelName,
        channelType,
        treatCardTypeName,
        cardName,
        categoryName,
        goodName,
        employeeName,
        jobName,
        payPerformance,
        cardPerformance,
        cardLargessPerformance,
        LargessPerformance
        FROM (
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '项目卡' AS TreatCardTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformanceProject TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_TreatBillProject TBP WITH(NOLOCK) ON TEPC.TreatBillProjectID = TBP.ID
        LEFT JOIN TB_ProjectAccount PA WITH(NOLOCK) ON TBP.ProjectAccountID = PA.ID
        LEFT JOIN TB_Project PAP WITH(NOLOCK) ON PA.ProjectID = PAP.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '项目卡' AS TreatCardTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformancePackageCardProject TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_TreatBillProject TBP WITH(NOLOCK) ON TEPC.TreatBillProjectID = TBP.ID
        LEFT JOIN TB_ProjectAccount PA WITH(NOLOCK) ON TBP.ProjectAccountID = PA.ID
        LEFT JOIN TB_Project PAP WITH(NOLOCK) ON PA.ProjectID = PAP.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '储值卡' AS TreatCardTypeName,
        SC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        0 AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END as CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END as CardLargessPerformance,
        0 AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformanceSavingCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON TEPC.SavingCardID = SC.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND SC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '储值卡' AS TreatCardTypeName,
        SC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        0 AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END as CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END as CardLargessPerformance,
        0 AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformancePackageCardSavingCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON TEPC.SavingCardID = SC.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND SC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '时效卡' AS TreatCardTypeName,
        TC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformanceTimeCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON TEPC.TimeCardID = TC.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND TC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '时效卡' AS TreatCardTypeName,
        TC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformancePackageCardTimeCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON TEPC.TimeCardID = TC.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND TC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '通用次卡' AS TreatCardTypeName,
        GC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformanceGeneralCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON TEPC.GeneralCardID = GC.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND GC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '通用次卡' AS TreatCardTypeName,
        GC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformancePackageCardGeneralCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON TEPC.GeneralCardID = GC.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND GC.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '产品卡' AS TreatCardTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformanceProduct TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON TEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON P.PCategoryID = PC.ID
        LEFT JOIN TB_TreatBillProduct TBP WITH(NOLOCK) ON TEPC.TreatBillProductID = TBP.ID
        LEFT JOIN TB_ProductAccount PA WITH(NOLOCK) ON TBP.ProductAccountID = PA.ID
        LEFT JOIN TE_Product PAP WITH(NOLOCK) ON PA.ProductID = PAP.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        TB.BillType,
        E.EntityName,
        C.Name AS CustomerName,REPLACE(C.PhoneNumber , SUBSTRING (C.PhoneNumber,4,4), '****') PhoneNumber,C.Code,
        TCL.Name AS ChannelName,
        TCLT.Name AS ChannelType,
        '产品卡' AS TreatCardTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TE.Name AS EmployeeName,
        JT.JobName,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS LargessPerformance
        FROM TB_TreatChannelConsultantPerformancePackageCardProduct TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON C.ID = TB.CustomerID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TB_Channel TCL WITH(NOLOCK) ON TEPC.ChannelID = TCL.ID
        LEFT JOIN TB_ChannelType TCLT WITH(NOLOCK) ON TCL.ChannelTypeID = TCLT.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON TEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON P.PCategoryID = PC.ID
        LEFT JOIN TB_TreatBillProduct TBP WITH(NOLOCK) ON TEPC.TreatBillProductID = TBP.ID
        LEFT JOIN TB_ProductAccount PA WITH(NOLOCK) ON TBP.ProductAccountID = PA.ID
        LEFT JOIN TE_Product PAP WITH(NOLOCK) ON PA.ProductID = PAP.ID
        LEFT JOIN TK_Employee TE WITH(NOLOCK) ON TE.ID = TEPC.EmployeeID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON JT.ID = TE.JobID
        <where>
            <if test="EntityID != null and EntityID != null ">
                TEPC.EntityID = #{EntityID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND P.Name like concat('%',#{GoodsName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( C.Name like '%'+ #{CustomerName} +'%' or C.PhoneNumber like '%'+ #{CustomerName} +'%' or
                C.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND TCL.Name like concat('%',#{ChannelName},'%')
            </if>
            <if test="EmployeeID != null and EmployeeID != ''">
                AND TE.ID = #{EmployeeID}
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID like concat('%',#{BillID},'%')
            </if>
            <if test="ChannelTypeID != null and ChannelTypeID != ''">
                AND TCL.ChannelTypeID = #{ChannelTypeID}
            </if>
        </where>
        ) T
        <where>
            <if test="TreatCardTypeName != null and TreatCardTypeName != '' ">
                TreatCardTypeName = #{TreatCardTypeName}
            </if>
        </where>
        ORDER BY BillDate DESC
    </select>
</mapper>