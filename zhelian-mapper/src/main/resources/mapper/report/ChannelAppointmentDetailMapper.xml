<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.report.ChannelAppointmentDetailMapper">

    <resultMap id="appointmentBillListMap" type="com.zhelian.model.report.form.output.ChannelAppointmentDetailOutputForm">
        <id column="ID" property="ID" jdbcType="VARCHAR"/>
        <result column="CustomerID" property="CustomerID" jdbcType="INTEGER"/>
        <result column="CustomerName" property="CustomerName" jdbcType="VARCHAR"/>
        <result column="Code" property="Code" jdbcType="VARCHAR"/>
        <result column="PhoneNumber" property="PhoneNumber" jdbcType="VARCHAR"/>
        <result column="AppointmentDate" property="AppointmentDate" jdbcType="VARCHAR"/>
        <result column="EntityName" property="EntityName" jdbcType="VARCHAR"/>
        <result column="Period" property="Period" jdbcType="VARCHAR"/>
        <result column="Remark" property="Remark" jdbcType="VARCHAR"/>
        <result column="Status" property="Status" jdbcType="VARCHAR"/>
        <result column="TimeOut" property="TimeOut" jdbcType="VARCHAR"/>
        <result column="CreatedBy" property="CreatedBy" jdbcType="VARCHAR"/>
        <result column="CreatedOn" property="CreatedOn" jdbcType="VARCHAR"/>
        <result column="Channel" property="Channel" jdbcType="VARCHAR"/>
        <result column="LevelName" property="LevelName" jdbcType="VARCHAR"/>
        <result column="CustomerSourceName" property="CustomerSourceName" jdbcType="VARCHAR"/>
        <result column="AppointmentTypeName" property="AppointmentTypeName" jdbcType="VARCHAR"/>
        <result column="ArrivalDate" property="ArrivalDate" jdbcType="VARCHAR"/>
        <result column="ChannelName" property="ChannelName" jdbcType="VARCHAR"/>
        <collection property="Servicer" select="getAppointmentBillListServicer" column="{ID=ID}" ofType="com.zhelian.model.report.form.output.ChannelAppointmentDetailServicerInfoOutputForm"/>
    </resultMap>

    <select id="list" resultMap="appointmentBillListMap">
        SELECT
        tab.ID,
        tab.CustomerID,
        tc.Name CustomerName,
        tc.Code,
        tc.PhoneNumber,
        CONVERT(VARCHAR(19),tab.AppointmentDate,120) AppointmentDate,
        te.EntityName,
        tab.Period,
        tab.Remark,
        tab.Status,
        CASE WHEN tab.Status =10 AND DATEDIFF(Minute,tab.AppointmentDate,GETDATE()) > 0 THEN
        DATEDIFF(Minute,tab.AppointmentDate,GETDATE())
        ELSE 0 END TimeOut,
        teec.Name CreatedBy,
        CONVERT(VARCHAR(19),tab.CreatedOn,120) CreatedOn,
        CASE WHEN tab.Channel ='PC' THEN 'pc'
        WHEN tab.Channel ='Miniprogram' THEN '小程序'
        WHEN tab.Channel ='MicroMall' THEN '商城'  END Channel,
        tat.Name AppointmentTypeName,
        tcs.Name CustomerSourceName,
        CONVERT(VARCHAR (19), tps.CreatedOn, 120) ArrivalDate,
        tt.Name ChannelName,
        tcl.Name LevelName

        FROM TB_AppointmentBill tab WITH(NOLOCK)
        LEFT JOIN TK_Entity te WITH(NOLOCK) ON te.ID = tab.EntityID
        LEFT JOIN TB_AppointmentType tat WITH(NOLOCK) ON tat.ID=tab.AppointmentTypeID
        LEFT JOIN TB_Customer tc WITH(NOLOCK) ON tc.ID=tab.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN TK_Employee teec WITH(NOLOCK) ON teec.ID=tab.CreatedBy
        LEFT JOIN TB_Channel tt WITH(NOLOCK) ON tt.ID=tc.ChannelID
        LEFT JOIN TB_AppointmentBillProcess tps WITH (NOLOCK) ON tps.AppointmentBillID=tab.ID AND tps.ActionType='20'
        <where>
            EXISTS (
            SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tt.ID and tbcc.EmployeeID = #{EmployeeID}
            UNION ALL
            SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tt.ID and tbcc.EmployeeID = #{EmployeeID}
            )
            <if test="EntityID!=null">
                AND tab.EntityID=#{EntityID}
            </if>
            <if test="Name != null and Name != ''">
                AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+
                #{Name} +'%')
            </if>
            <if test="AppointmentBillID != null and AppointmentBillID != ''">
                AND tab.ID = #{AppointmentBillID}
            </if>
            <if test="StartDate != null and StartDate != ''and EndDate != null and EndDate != ''">
                AND tab.AppointmentDate >= #{StartDate} AND tab.AppointmentDate &lt; DATEADD(DAY,1,#{EndDate})
            </if>
            <if test="Status != null and Status != ''">
                AND tab.Status = #{Status}
            </if>
            <if test="CustomerID != null and CustomerID != ''">
                AND tab.CustomerID = #{CustomerID}
            </if>
            <if test="CustomerSourceID != null and CustomerSourceID != ''">
                AND tc.CustomerSourceID = #{CustomerSourceID}
            </if>
            <if test="CreatedBy != null and CreatedBy != ''">
                AND teec.Name LIKE '%' + #{CreatedBy} + '%'
            </if>
            <if test="CreatedOnStartDate != null and CreatedOnStartDate != '' and CreatedOnEndDate != null and CreatedOnEndDate != ''">
                AND tab.CreatedOn >= #{CreatedOnStartDate} AND tab.CreatedOn &lt; DATEADD(DAY,1,#{CreatedOnEndDate})
            </if>
            <if test="ServicerID != null and ServicerID != ''">
                and exists (
                SELECT * FROM TB_AppointmentBillEmployee tt
                WHERE tt.AppointmentServicerID=#{ServicerID} AND tt.AppointmentBillID=tab.ID
                )
            </if>
            <if test="ServicerEmployeeID != null and ServicerEmployeeID != ''">
                and exists (
                SELECT * FROM TB_AppointmentBillEmployee tt
                WHERE tt.EmployeeID=#{ServicerEmployeeID} AND tt.AppointmentBillID=tab.ID
                )
            </if>
            <if test="ServicerID != null and ServicerID != '' and ServicerEmployeeID != null and ServicerEmployeeID != ''">
                and exists (
                SELECT * FROM TB_AppointmentBillEmployee tt
                WHERE tt.AppointmentServicerID=#{ServicerID} AND tt.EmployeeID=#{ServicerEmployeeID} AND tt.AppointmentBillID=tab.ID
                )
            </if>
            <if test="Channel != null and Channel != ''">
                AND tab.Channel = #{Channel}
            </if>
            <if test="AppointmentTypeID != null and AppointmentTypeID != ''">
                AND tab.AppointmentTypeID = #{AppointmentTypeID}
            </if>
            <if test="CustomerChannel != null and CustomerChannel != ''">
                AND tt.Name like '%'+ #{CustomerChannel} +'%'
            </if>
            <if test="CustomerLevelID != null and CustomerLevelID != null ">
                AND tc.CustomerLevelID=#{CustomerLevelID}
            </if>
        </where>
        ORDER BY tab.AppointmentDate DESC,tab.ID ASC
    </select>

    <resultMap id="appointmentBillListServicerMap" type="com.zhelian.model.report.form.output.ChannelAppointmentDetailServicerInfoOutputForm">
        <id column="ServicerID" property="ServicerID" jdbcType="INTEGER"/>
        <result column="ServicerName" property="ServicerName" jdbcType="VARCHAR"/>
        <collection property="Employee"  ofType="com.zhelian.model.report.form.output.ChannelAppointmentDetailServicerEmpOutputForm">
            <id column="EmployeeID" property="EmployeeID" jdbcType="VARCHAR"/>
            <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <select id="getAppointmentBillListServicer" resultMap="appointmentBillListServicerMap">
        SELECT
            a2.ID ServicerID,
            a2.Name ServicerName,
            a3.ID EmployeeID,
            a3.Name EmployeeName
        FROM
            TB_AppointmentBillEmployee a1 WITH(NOLOCK)
        LEFT JOIN TB_AppointmentServicer a2 WITH(NOLOCK) ON  a2.ID=a1.AppointmentServicerID
        LEFT JOIN TK_Employee a3 WITH(NOLOCK) ON a1.EmployeeID=a3.ID
        WHERE   a1.AppointmentBillID=#{ID}
        ORDER BY Sequence DESC
    </select>

    <select id="excel" resultType="com.zhelian.model.report.form.output.ChannelAppointmentDetailOutputExcelForm">
        SELECT
        tc.name customerName,
        tc.code,
        ISNULL(REPLACE(tc.PhoneNumber , SUBSTRING (tc.PhoneNumber,4,4), '****'),'') phoneNumber,
        CASE WHEN tab.Status ='10' THEN '未到店' WHEN tab.Status ='20' THEN '已到店'
        ELSE '已取消' END status,
        CONVERT(VARCHAR(19),tab.AppointmentDate,120) appointmentDate,
        tab.period,
        teec.Name createdBy,
        tct.Name appointmentTypeName,
        te.EntityName as entityName,
        CONVERT(VARCHAR(19),tab.CreatedOn,120) createdOn,
        CASE WHEN tab.Channel ='PC' THEN 'pc'
        WHEN tab.Channel ='Miniprogram' THEN '小程序'
        WHEN tab.Channel ='MicroMall' THEN '商城'  END channel,
        CONVERT(VARCHAR (19), tps.CreatedOn, 120) arrivalDate,
        servicer = stuff(
        (
        SELECT ',' + t.Name
        FROM
        (
        SELECT tp.Name+':'+tee.Name  Name FROM TB_AppointmentBillEmployee t WITH (NOLOCK)
        LEFT JOIN TB_AppointmentServicer tp WITH (NOLOCK) ON t.AppointmentServicerID=tp.ID
        LEFT JOIN TK_Employee tee WITH (NOLOCK) ON t.EmployeeID =tee.ID
        WHERE t.AppointmentBillID =
        tab.ID ) t FOR xml path('') ), 1, 1, ''),
        isnull(cha.Name,'') as ChannelName,
        tab.Remark,
        PorjectName =ISNULL(stuff(
        (
        SELECT ','+T.NAME FROM(
        SELECT PP.Name FROM TB_AppointmentBillProject ABP
        LEFT JOIN TB_Project  PP ON ABP.ProjectID = PP.ID
        WHERE ABP.AppointmentBillID = tab.ID) T FOR xml path('') ), 1, 1, ''),'')
        FROM TB_AppointmentBill tab WITH(NOLOCK)
        LEFT JOIN TK_Entity te WITH(NOLOCK) ON te.ID = tab.EntityID
        LEFT JOIN TB_Customer tc WITH(NOLOCK) ON tc.ID=tab.CustomerID
        LEFT JOIN TB_AppointmentType tct WITH(NOLOCK) ON tct.ID=tab.AppointmentTypeID
        LEFT JOIN TK_Employee teec WITH(NOLOCK) ON teec.ID=tab.CreatedBy
        LEFT JOIN TB_AppointmentBillProcess tps WITH (NOLOCK) ON tps.AppointmentBillID=tab.ID AND tps.ActionType='20'
        LEFT JOIN TB_Channel cha on tc.ChannelID = cha.ID
        <where>
            EXISTS (
            SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = cha.ID and tbcc.EmployeeID = #{EmployeeID}
            UNION ALL
            SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = cha.ID and tbcc.EmployeeID = #{EmployeeID}
            )
            <if test="EntityID!=null">
                AND tab.EntityID=#{EntityID}
            </if>
            <if test="Name != null and Name != ''">
                AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+
                #{Name} +'%')
            </if>
            <if test="AppointmentBillID != null and AppointmentBillID != ''">
                AND tab.ID = #{AppointmentBillID}
            </if>
            <if test="StartDate != null and StartDate != ''and EndDate != null and EndDate != ''">
                AND tab.AppointmentDate >= #{StartDate} AND tab.AppointmentDate &lt; DATEADD(DAY,1,#{EndDate})
            </if>
            <if test="Status != null and Status != ''">
                AND tab.Status = #{Status}
            </if>
            <if test="CustomerID != null and CustomerID != ''">
                AND tab.CustomerID = #{CustomerID}
            </if>
            <if test="CreatedBy != null and CreatedBy != ''">
                AND teec.Name LIKE '%' + #{CreatedBy} + '%'
            </if>
            <if test="CreatedOnStartDate != null and CreatedOnStartDate != '' and CreatedOnEndDate != null and CreatedOnEndDate != ''">
                AND tab.CreatedOn >= #{CreatedOnStartDate} AND tab.CreatedOn &lt; DATEADD(DAY,1,#{CreatedOnEndDate})
            </if>
            <if test="ServicerID != null and ServicerID != ''">
                and exists (
                SELECT * FROM TB_AppointmentBillEmployee tt
                WHERE tt.AppointmentServicerID=#{ServicerID} AND tt.AppointmentBillID=tab.ID
                )
            </if>
            <if test="ServicerEmployeeID != null and ServicerEmployeeID != ''">
                and exists (
                SELECT * FROM TB_AppointmentBillEmployee tt
                WHERE tt.EmployeeID=#{ServicerEmployeeID} AND tt.AppointmentBillID=tab.ID
                )
            </if>
            <if test="ServicerID != null and ServicerID != '' and ServicerEmployeeID != null and ServicerEmployeeID != ''">
                and exists (
                SELECT * FROM TB_AppointmentBillEmployee tt
                WHERE tt.AppointmentServicerID=#{ServicerID} AND tt.EmployeeID=#{ServicerEmployeeID} AND tt.AppointmentBillID=tab.ID
                )
            </if>
            <if test="Channel != null and Channel != ''">
                AND tab.Channel = #{Channel}
            </if>
            <if test="AppointmentTypeID != null and AppointmentTypeID != ''">
                AND tab.AppointmentTypeID = #{AppointmentTypeID}
            </if>
            <if test="CustomerChannel != null and CustomerChannel != ''">
                AND cha.Name like '%'+ #{CustomerChannel} +'%'
            </if>
            <if test="CustomerLevelID != null and CustomerLevelID != null ">
                AND tc.CustomerLevelID=#{CustomerLevelID}
            </if>
        </where>
        ORDER BY tab.AppointmentDate DESC,tab.ID ASC
    </select>
</mapper>