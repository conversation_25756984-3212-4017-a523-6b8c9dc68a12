<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.report.EmployeeSalePerformanceCommissionDetailMapper">
    <select id="getEmployeeSalePerformanceCommissionDetail"
            resultType="com.zhelian.model.report.form.output.EmployeeSalePerformanceCommissionDetailStatementForm">
        SELECT SaleBillID,CONVERT(varchar(100), BillDate, 120) AS
        BillDate,
        SaleHandlerName,
        EntityName,
        EmployeeID,
        EmployeeName,
        EmployeeEntityName,
        JobName,
        ISNULL(CustomerName,'散客')CustomerName,
        Code,
        PhoneNumber,
        CustomerEntityName,
        CustomerLevelName,
        CustomerSourceName,
        ChannelName,
        GoodsTypeName,
        CategoryName,
        GoodName,
        Price,
        Quantity,
        PreferentialAmount,
        TotalAmount,
        ArrearAmount,
        PayAmount,
        SavingCardDeductionAmount,
        LargessSavingCardDeductionAmount,
        Scale,
        PayPerformance,
        PayCommission,
        SavingCardPerformance,
        SavingCardCommission,
        SavingCardLargessPerformance,
        SavingCardLargessCardCommission,
        SpecialBenefitCommission
        FROM (
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TCSE.Name CustomerSourceName,
        cha.Name AS ChannelName,
        '项目' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        SBP.Price,
        SBP.Quantity,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProject SBPP WITH(NOLOCK)
        WHERE SBPP.SaleBillProjectID =SBP.ID) *-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProject SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProjectID
        =SBP.ID) END AS PayAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProject
        SBPSCDP WITH(NOLOCK) WHERE SBPSCDP.SaleBillProjectID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) END AS SavingCardDeductionAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(LargessAmount),0) FROM
        TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE SBPSCDP.SaleBillProjectID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK)
        WHERE SBPSCDP.SaleBillProjectID = SBP.ID ) END AS LargessSavingCardDeductionAmount,
        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission)*-1 ELSE
        (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission) END AS SavingCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance,
        CASE WHEN SB.BillType = '20' THEN
        (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission)*-1
        ELSE (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission) END AS
        SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionProject SEPC WITH(NOLOCK)
        LEFT JOIN TB_ProjectSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.ProjectSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerProject TSBP WITH(NOLOCK) ON TSBP.SaleBillProjectID = SEPC.SaleBillProjectID AND
        TSBP.ProjectSaleHandlerID =SEPC.ProjectSaleHandlerID AND TSBP.EmployeeID =SEPC.EmployeeID
        LEFT JOIN TB_SaleBillProject SBP WITH(NOLOCK) ON SEPC.SaleBillProjectID = SBP.ID
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON SEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        LEFT JOIN TB_CustomerSource TCSE WITH(NOLOCK) ON TCR.CustomerSourceID = TCSE.ID
        LEFT JOIN TB_Channel cha WITH(NOLOCK) on TCR.ChannelID = cha.ID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="GoodsName != null and GoodsName != ''">
               AND ( P.Name like '%'+ #{GoodsName} +'%' or P.Alias like '%'+ #{GoodsName} +'%'
                OR P.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR P.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR P.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR P.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TCSE.Name CustomerSourceName,
        cha.Name AS ChannelName,
        '项目' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        SBP.PackagePrice Price,
        SBP.Quantity*SBP.PackageQuantity Quantity,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardProject SBPP
        WITH(NOLOCK) WHERE SBPP.SaleBillPackageCardProjectID =SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardProject SBPP WITH(NOLOCK) WHERE
        SBPP.SaleBillPackageCardProjectID =SBP.ID)end AS PayAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM
        TB_SaleBillPaySavingCardDeductionPackageCardProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillPackageCardProjectID = SBP.ID )
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCardProject SBPSCDP WITH(NOLOCK)
        WHERE SBPSCDP.SaleBillPackageCardProjectID = SBP.ID ) END AS SavingCardDeductionAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(LargessAmount),0) FROM
        TB_SaleBillPaySavingCardDeductionPackageCardProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillPackageCardProjectID = SBP.ID )
        ELSE (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCardProject SBPSCDP
        WITH(NOLOCK) WHERE SBPSCDP.SaleBillPackageCardProjectID = SBP.ID ) END AS LargessSavingCardDeductionAmount,
        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission)*-1 ELSE
        (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission) END AS SavingCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance,
        CASE WHEN SB.BillType = '20' THEN
        (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission)*-1
        ELSE (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission) END AS
        SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionPackageCardProject SEPC WITH(NOLOCK)
        LEFT JOIN TB_PackageCardSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.PackageCardSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerPackageCard TSBP WITH(NOLOCK) ON TSBP.SaleBillPackageCardID =
        SEPC.SaleBillPackageCardID AND TSBP.PackageCardSaleHandlerID =SEPC.PackageCardSaleHandlerID AND TSBP.EmployeeID
        =SEPC.EmployeeID
        LEFT JOIN TB_SaleBillPackageCardProject SBP WITH(NOLOCK) ON SEPC.SaleBillPackageCardProjectID = SBP.ID

        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON SEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        LEFT JOIN TB_CustomerSource TCSE WITH(NOLOCK) ON TCR.CustomerSourceID = TCSE.ID
        LEFT JOIN TB_Channel cha WITH(NOLOCK) on TCR.ChannelID = cha.ID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="GoodsName != null and GoodsName != ''">
              AND  ( P.Name like '%'+ #{GoodsName} +'%' or P.Alias like '%'+ #{GoodsName} +'%'
                OR P.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR P.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR P.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR P.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TCSE.Name CustomerSourceName,
        cha.Name AS ChannelName,
        '储值卡' AS GoodsTypeName,
        SCC.Name AS CategoryName,
        SC.Name AS GoodName,
        SBP.Amount AS Price,
        SBP.Quantity,
        CASE WHEN SBP.Amount!=0 THEN (SBP.TotalAmount-(SBP.Amount*SBP.Quantity)) ELSE 0 END PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCard SBPSC WITH(NOLOCK)
        WHERE SBPSC.SaleBillSavingCardID= SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCard SBPSC WITH(NOLOCK) WHERE
        SBPSC.SaleBillSavingCardID= SBP.ID) END AS PayAmount,
        0 AS SavingCardDeductionAmount,
        0 AS LargessSavingCardDeductionAmount,
        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        0 AS SavingCardPerformance,
        0 AS SavingCardCommission,
        0 AS SavingCardLargessPerformance,
        0 AS SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionSavingCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SavingCardSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.SavingCardSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerSavingCard TSBP WITH(NOLOCK) ON TSBP.SaleBillSavingCardID =
        SEPC.SaleBillSavingCardID AND TSBP.SavingCardSaleHandlerID =SEPC.SavingCardSaleHandlerID AND TSBP.EmployeeID
        =SEPC.EmployeeID
        LEFT JOIN TB_SaleBillSavingCard SBP WITH(NOLOCK) ON SEPC.SaleBillSavingCardID = SBP.ID
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SEPC.SavingCardID = SC.ID
        LEFT JOIN TB_SavingCardCategory SCC WITH(NOLOCK) ON SC.SavingCardCategoryID = SCC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        LEFT JOIN TB_CustomerSource TCSE WITH(NOLOCK) ON TCR.CustomerSourceID = TCSE.ID
        LEFT JOIN TB_Channel cha WITH(NOLOCK) on TCR.ChannelID = cha.ID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND SC.SavingCardCategoryID=#{CategoryID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
              AND  ( SC.Name like '%'+ #{GoodsName} +'%' or SC.Alias like '%'+ #{GoodsName} +'%'
                OR SC.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR SC.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR SC.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR SC.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TCSE.Name CustomerSourceName,
        cha.Name AS ChannelName,
        '储值卡' AS GoodsTypeName,
        SCC.Name AS CategoryName,
        SC.Name AS GoodName,
        SBP.Amount AS Price,
        SBP.Quantity*SBP.PackageQuantity Quantity,
        CASE WHEN SBP.Amount!=0 THEN (SBP.TotalAmount-(SBP.Amount*SBP.Quantity*SBP.PackageQuantity)) ELSE 0 END
        PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardSavingCard SBPSC
        WITH(NOLOCK) WHERE SBPSC.SaleBillPackageCardSavingCardID= SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardSavingCard SBPSC WITH(NOLOCK) WHERE
        SBPSC.SaleBillPackageCardSavingCardID= SBP.ID)END AS PayAmount,
        0 AS SavingCardDeductionAmount,
        0 AS LargessSavingCardDeductionAmount,
        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        0 AS SavingCardPerformance,
        0 AS SavingCardCommission,
        0 AS SavingCardLargessPerformance,
        0 AS SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionPackageCardSavingCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_PackageCardSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.PackageCardSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerPackageCard TSBP WITH(NOLOCK) ON TSBP.SaleBillPackageCardID =
        SEPC.SaleBillPackageCardID AND TSBP.PackageCardSaleHandlerID =SEPC.PackageCardSaleHandlerID AND TSBP.EmployeeID
        =SEPC.EmployeeID
        LEFT JOIN TB_SaleBillPackageCardSavingCard SBP WITH(NOLOCK) ON SEPC.SaleBillPackageCardSavingCardID = SBP.ID

        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SEPC.SavingCardID = SC.ID
        LEFT JOIN TB_SavingCardCategory SCC WITH(NOLOCK) ON SC.SavingCardCategoryID = SCC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        LEFT JOIN TB_CustomerSource TCSE WITH(NOLOCK) ON TCR.CustomerSourceID = TCSE.ID
        LEFT JOIN TB_Channel cha WITH(NOLOCK) on TCR.ChannelID = cha.ID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND SC.SavingCardCategoryID=#{CategoryID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
              AND  ( SC.Name like '%'+ #{GoodsName} +'%' or SC.Alias like '%'+ #{GoodsName} +'%'
                OR SC.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR SC.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR SC.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR SC.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TCSE.Name CustomerSourceName,
        cha.Name AS ChannelName,
        '时效卡' AS GoodsTypeName,
        TCC.Name AS CategoryName,
        TC.Name AS GoodName,
        SBP.Price,
        SBP.Quantity,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayTimeCard SBPTC WITH(NOLOCK)
        WHERE SBPTC.SaleBillTimeCardID =SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayTimeCard SBPTC WITH(NOLOCK) WHERE SBPTC.SaleBillTimeCardID
        =SBP.ID) END AS PayAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard
        SBPSCDTC WITH(NOLOCK) WHERE SBPSCDTC.SaleBillTimeCardID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBP.ID ) END AS SavingCardDeductionAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(LargessAmount),0) FROM
        TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE SBPSCDTC.SaleBillTimeCardID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK)
        WHERE SBPSCDTC.SaleBillTimeCardID = SBP.ID ) END AS LargessSavingCardDeductionAmount,
        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission)*-1 ELSE
        (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission) END AS SavingCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance,
        CASE WHEN SB.BillType = '20' THEN
        (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission)*-1
        ELSE (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission) END AS
        SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionTimeCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_TimeCardSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.TimeCardSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerTimeCard TSBP WITH(NOLOCK) ON TSBP.SaleBillTimeCardID = SEPC.SaleBillTimeCardID AND
        TSBP.TimeCardSaleHandlerID =SEPC.TimeCardSaleHandlerID AND TSBP.EmployeeID =SEPC.EmployeeID

        LEFT JOIN TB_SaleBillTimeCard SBP WITH(NOLOCK) ON SEPC.SaleBillTimeCardID = SBP.ID
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON SEPC.TimeCardID = TC.ID
        LEFT JOIN TB_TimeCardCategory TCC WITH(NOLOCK) ON TC.TimeCardCategoryID = TCC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        LEFT JOIN TB_CustomerSource TCSE WITH(NOLOCK) ON TCR.CustomerSourceID = TCSE.ID
        LEFT JOIN TB_Channel cha WITH(NOLOCK) on TCR.ChannelID = cha.ID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND TC.TimeCardCategoryID=#{CategoryID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND ( TC.Name like '%'+ #{GoodsName} +'%' or TC.Alias like '%'+ #{GoodsName} +'%'
                OR TC.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR TC.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR TC.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR TC.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TCSE.Name CustomerSourceName,
        cha.Name AS ChannelName,
        '时效卡' AS GoodsTypeName,
        TCC.Name AS CategoryName,
        TC.Name AS GoodName,
        SBP.PackagePrice Price,
        SBP.Quantity*SBP.PackageQuantity Quantity,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardTimeCard SBPTC
        WITH(NOLOCK) WHERE SBPTC.SaleBillPackageCardTimeCardID = SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardTimeCard SBPTC WITH(NOLOCK) WHERE
        SBPTC.SaleBillPackageCardTimeCardID = SBP.ID)END AS PayAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM
        TB_SaleBillPaySavingCardDeductionPackageCardTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillPackageCardTimeCardID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCardTimeCard SBPSCDTC
        WITH(NOLOCK) WHERE SBPSCDTC.SaleBillPackageCardTimeCardID = SBP.ID ) end as SavingCardDeductionAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(LargessAmount),0) FROM
        TB_SaleBillPaySavingCardDeductionPackageCardTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillPackageCardTimeCardID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCardTimeCard SBPSCDTC
        WITH(NOLOCK) WHERE SBPSCDTC.SaleBillPackageCardTimeCardID = SBP.ID ) end as LargessSavingCardDeductionAmount,
        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission)*-1 ELSE
        (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission) END AS SavingCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance,
        CASE WHEN SB.BillType = '20' THEN
        (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission)*-1
        ELSE (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission) END AS
        SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionPackageCardTimeCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_PackageCardSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.PackageCardSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerPackageCard TSBP WITH(NOLOCK) ON TSBP.SaleBillPackageCardID =
        SEPC.SaleBillPackageCardID AND TSBP.PackageCardSaleHandlerID =SEPC.PackageCardSaleHandlerID AND TSBP.EmployeeID
        =SEPC.EmployeeID
        LEFT JOIN TB_SaleBillPackageCardTimeCard SBP WITH(NOLOCK) ON SEPC.SaleBillPackageCardTimeCardID = SBP.ID

        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON SEPC.TimeCardID = TC.ID
        LEFT JOIN TB_TimeCardCategory TCC WITH(NOLOCK) ON TC.TimeCardCategoryID = TCC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        LEFT JOIN TB_CustomerSource TCSE WITH(NOLOCK) ON TCR.CustomerSourceID = TCSE.ID
        LEFT JOIN TB_Channel cha WITH(NOLOCK) on TCR.ChannelID = cha.ID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND TC.TimeCardCategoryID=#{CategoryID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND ( TC.Name like '%'+ #{GoodsName} +'%' or TC.Alias like '%'+ #{GoodsName} +'%'
                OR TC.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR TC.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR TC.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR TC.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TCSE.Name CustomerSourceName,
        cha.Name AS ChannelName,
        '通用次卡' AS GoodsTypeName,
        GCC.Name AS CategoryName,
        GC.Name AS GoodName,
        SBP.Price,
        SBP.Quantity,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayGeneralCard SBPTC
        WITH(NOLOCK) WHERE SBPTC.SaleBillGeneralCardID = SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayGeneralCard SBPTC WITH(NOLOCK) WHERE
        SBPTC.SaleBillGeneralCardID = SBP.ID)END AS PayAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM
        TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK) WHERE SBPSCDTC.SaleBillGeneralCardID = SBP.ID
        )*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillGeneralCardID = SBP.ID ) END AS SavingCardDeductionAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(LargessAmount),0) FROM
        TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK) WHERE SBPSCDTC.SaleBillGeneralCardID = SBP.ID
        )*-1
        ELSE (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC
        WITH(NOLOCK) WHERE SBPSCDTC.SaleBillGeneralCardID = SBP.ID ) END AS LargessSavingCardDeductionAmount,
        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission)*-1 ELSE
        (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission) END AS SavingCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance,
        CASE WHEN SB.BillType = '20' THEN
        (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission)*-1
        ELSE (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission) END AS
        SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionGeneralCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_GeneralCardSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.GeneralCardSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerGeneralCard TSBP WITH(NOLOCK) ON TSBP.SaleBillGeneralCardID =
        SEPC.SaleBillGeneralCardID AND TSBP.GeneralCardSaleHandlerID =SEPC.GeneralCardSaleHandlerID AND TSBP.EmployeeID
        =SEPC.EmployeeID

        LEFT JOIN TB_SaleBillGeneralCard SBP WITH(NOLOCK) ON SEPC.SaleBillGeneralCardID = SBP.ID
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON SEPC.GeneralCardID = GC.ID
        LEFT JOIN TB_GeneralCardCategory GCC WITH(NOLOCK) ON GC.GeneralCardCategoryID = GCC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        LEFT JOIN TB_CustomerSource TCSE WITH(NOLOCK) ON TCR.CustomerSourceID = TCSE.ID
        LEFT JOIN TB_Channel cha WITH(NOLOCK) on TCR.ChannelID = cha.ID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND GC.GeneralCardCategoryID=#{CategoryID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND ( GC.Name like '%'+ #{GoodsName} +'%' or GC.Alias like '%'+ #{GoodsName} +'%'
                OR GC.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR GC.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR GC.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR GC.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TCSE.Name CustomerSourceName,
        cha.Name AS ChannelName,
        '通用次卡' AS GoodsTypeName,
        GCC.Name AS CategoryName,
        GC.Name AS GoodName,
        SBP.PackagePrice Price,
        SBP.Quantity*SBP.PackageQuantity Quantity,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardGeneralCard SBPTC
        WITH(NOLOCK) WHERE SBPTC.SaleBillPackageCardGeneralCardID = SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardGeneralCard SBPTC WITH(NOLOCK) WHERE
        SBPTC.SaleBillPackageCardGeneralCardID = SBP.ID) END AS PayAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM
        TB_SaleBillPaySavingCardDeductionPackageCardGeneralCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillPackageCardGeneralCardID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCardGeneralCard SBPSCDTC
        WITH(NOLOCK) WHERE SBPSCDTC.SaleBillPackageCardGeneralCardID = SBP.ID ) END AS SavingCardDeductionAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(LargessAmount),0) FROM
        TB_SaleBillPaySavingCardDeductionPackageCardGeneralCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillPackageCardGeneralCardID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCardGeneralCard SBPSCDTC
        WITH(NOLOCK) WHERE SBPSCDTC.SaleBillPackageCardGeneralCardID = SBP.ID ) END AS LargessSavingCardDeductionAmount,
        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission)*-1 ELSE
        (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission) END AS SavingCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance,
        CASE WHEN SB.BillType = '20' THEN
        (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission)*-1
        ELSE (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission) END AS
        SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionPackageCardGeneralCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_PackageCardSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.PackageCardSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerPackageCard TSBP WITH(NOLOCK) ON TSBP.SaleBillPackageCardID =
        SEPC.SaleBillPackageCardID AND TSBP.PackageCardSaleHandlerID =SEPC.PackageCardSaleHandlerID AND TSBP.EmployeeID
        =SEPC.EmployeeID
        LEFT JOIN TB_SaleBillPackageCardGeneralCard SBP WITH(NOLOCK) ON SEPC.SaleBillPackageCardGeneralCardID = SBP.ID
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON SEPC.GeneralCardID = GC.ID
        LEFT JOIN TB_GeneralCardCategory GCC WITH(NOLOCK) ON GC.GeneralCardCategoryID = GCC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        LEFT JOIN TB_CustomerSource TCSE WITH(NOLOCK) ON TCR.CustomerSourceID = TCSE.ID
        LEFT JOIN TB_Channel cha WITH(NOLOCK) on TCR.ChannelID = cha.ID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND GC.GeneralCardCategoryID=#{CategoryID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND ( GC.Name like '%'+ #{GoodsName} +'%' or GC.Alias like '%'+ #{GoodsName} +'%'
                OR GC.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR GC.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR GC.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR GC.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TCSE.Name CustomerSourceName,
        cha.Name AS ChannelName,
        '产品' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        SBP.Price,
        SBP.Quantity,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProduct SBPP WITH(NOLOCK)
        WHERE SBPP.SaleBillProductID = SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProduct SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProductID =
        SBP.ID) END AS PayAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProduct
        SBPSCDP WITH(NOLOCK) WHERE SBPSCDP.SaleBillProductID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) END AS SavingCardDeductionAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(LargessAmount),0) FROM
        TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE SBPSCDP.SaleBillProductID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK)
        WHERE SBPSCDP.SaleBillProductID = SBP.ID ) END AS LargessSavingCardDeductionAmount,
        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission)*-1 ELSE
        (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission) END AS SavingCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance,
        CASE WHEN SB.BillType = '20' THEN
        (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission)*-1
        ELSE (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission) END AS
        SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionProduct SEPC WITH(NOLOCK)
        LEFT JOIN TB_ProductSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.ProductSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerProduct TSBP WITH(NOLOCK) ON TSBP.SaleBillProductID = SEPC.SaleBillProductID AND
        TSBP.ProductSaleHandlerID =SEPC.ProductSaleHandlerID AND TSBP.EmployeeID =SEPC.EmployeeID
        LEFT JOIN TB_SaleBillProduct SBP WITH(NOLOCK) ON SEPC.SaleBillProductID = SBP.ID

        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON SEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory SubPC WITH(NOLOCK) ON P.PCategoryID = SubPC .ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON SubPC .ParentID = PC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        LEFT JOIN TB_CustomerSource TCSE WITH(NOLOCK) ON TCR.CustomerSourceID = TCSE.ID
        LEFT JOIN TB_Channel cha WITH(NOLOCK) on TCR.ChannelID = cha.ID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>

            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND (P.PCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND ( P.Name like '%'+ #{GoodsName} +'%' or P.Alias like '%'+ #{GoodsName} +'%'
                OR P.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR P.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR P.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR P.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TCSE.Name CustomerSourceName,
        cha.Name AS ChannelName,
        '产品' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        SBP.PackagePrice Price,
        SBP.Quantity*SBP.PackageQuantity Quantity,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardProduct SBPP
        WITH(NOLOCK) WHERE SBPP.SaleBillPackageCardProductID =SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardProduct SBPP WITH(NOLOCK) WHERE
        SBPP.SaleBillPackageCardProductID =SBP.ID) END AS PayAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM
        TB_SaleBillPaySavingCardDeductionPackageCardProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillPackageCardProductID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCardProduct SBPSCDP WITH(NOLOCK)
        WHERE SBPSCDP.SaleBillPackageCardProductID = SBP.ID ) END AS SavingCardDeductionAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(LargessAmount),0) FROM
        TB_SaleBillPaySavingCardDeductionPackageCardProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillPackageCardProductID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCardProduct SBPSCDP
        WITH(NOLOCK) WHERE SBPSCDP.SaleBillPackageCardProductID = SBP.ID ) END AS LargessSavingCardDeductionAmount,
        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission)*-1 ELSE
        (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission) END AS SavingCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance,
        CASE WHEN SB.BillType = '20' THEN
        (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission)*-1
        ELSE (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission) END AS
        SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionPackageCardProduct SEPC WITH(NOLOCK)
        LEFT JOIN TB_PackageCardSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.PackageCardSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerPackageCard TSBP WITH(NOLOCK) ON TSBP.SaleBillPackageCardID =
        SEPC.SaleBillPackageCardID AND TSBP.PackageCardSaleHandlerID =SEPC.PackageCardSaleHandlerID AND TSBP.EmployeeID
        =SEPC.EmployeeID
        LEFT JOIN TB_SaleBillPackageCardProduct SBP WITH(NOLOCK) ON SEPC.SaleBillPackageCardProductID = SBP.ID
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON SEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory SubPC WITH(NOLOCK) ON P.PCategoryID = SubPC .ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON SubPC .ParentID = PC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        LEFT JOIN TB_CustomerSource TCSE WITH(NOLOCK) ON TCR.CustomerSourceID = TCSE.ID
        LEFT JOIN TB_Channel cha WITH(NOLOCK) on TCR.ChannelID = cha.ID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>

            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND (P.PCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND ( P.Name like '%'+ #{GoodsName} +'%' or P.Alias like '%'+ #{GoodsName} +'%'
                OR P.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR P.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR P.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR P.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        ) T
        <where>
            <if test="BillID != null and BillID != '' ">
                AND SaleBillID = #{BillID}
            </if>
            <if test="GoodsTypeName != null and GoodsTypeName != '' ">
                AND GoodsTypeName = #{GoodsTypeName}
            </if>
        </where>
        ORDER BY BillDate DESC
    </select>
    <select id="getEmployeeSalePerformanceCommissionSum"
            resultType="com.zhelian.model.report.form.output.EmployeeSalePerformanceCommissionSumStatementForm">
        SELECT

        SUM(TotalAmount) AS TotalAmount,
        SUM(ArrearAmount) AS ArrearAmount,
        SUM(PayAmount) AS PayAmount,
        SUM(SavingCardDeductionAmount) AS SavingCardDeductionAmount,
        SUM(LargessSavingCardDeductionAmount) AS LargessSavingCardDeductionAmount,
        SUM(PayPerformance) AS PayPerformance,
        SUM(PayCommission) AS PayCommission,
        SUM(SavingCardPerformance) AS SavingCardPerformance,
        SUM(SavingCardCommission) AS SavingCardCommission,
        SUM(SavingCardLargessPerformance) AS SavingCardLargessPerformance,
        SUM(SavingCardLargessCardCommission) AS SavingCardLargessCardCommission,
        SUM(SpecialBenefitCommission) AS SpecialBenefitCommission
        FROM (
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        '项目' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        SBP.Price,
        SBP.Quantity,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProject SBPP WITH(NOLOCK)
        WHERE SBPP.SaleBillProjectID =SBP.ID) *-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProject SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProjectID
        =SBP.ID) END AS PayAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProject
        SBPSCDP WITH(NOLOCK) WHERE SBPSCDP.SaleBillProjectID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) END AS SavingCardDeductionAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(LargessAmount),0) FROM
        TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE SBPSCDP.SaleBillProjectID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK)
        WHERE SBPSCDP.SaleBillProjectID = SBP.ID ) END AS LargessSavingCardDeductionAmount,

        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission)*-1 ELSE
        (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission) END AS SavingCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance,
        CASE WHEN SB.BillType = '20' THEN
        (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission)*-1
        ELSE (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission) END AS
        SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionProject SEPC WITH(NOLOCK)
        LEFT JOIN TB_ProjectSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.ProjectSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerProject TSBP WITH(NOLOCK) ON TSBP.SaleBillProjectID = SEPC.SaleBillProjectID AND
        TSBP.ProjectSaleHandlerID =SEPC.ProjectSaleHandlerID AND TSBP.EmployeeID =SEPC.EmployeeID
        LEFT JOIN TB_SaleBillProject SBP WITH(NOLOCK) ON SEPC.SaleBillProjectID = SBP.ID
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON SEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>

            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND  ( P.Name like '%'+ #{GoodsName} +'%' or P.Alias like '%'+ #{GoodsName} +'%'
                OR P.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR P.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR P.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR P.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        '项目' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        SBP.PackagePrice Price,
        SBP.Quantity*SBP.PackageQuantity Quantity,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardProject SBPP
        WITH(NOLOCK) WHERE SBPP.SaleBillPackageCardProjectID =SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardProject SBPP WITH(NOLOCK) WHERE
        SBPP.SaleBillPackageCardProjectID =SBP.ID)end AS PayAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM
        TB_SaleBillPaySavingCardDeductionPackageCardProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillPackageCardProjectID = SBP.ID )
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCardProject SBPSCDP WITH(NOLOCK)
        WHERE SBPSCDP.SaleBillPackageCardProjectID = SBP.ID ) END AS SavingCardDeductionAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(LargessAmount),0) FROM
        TB_SaleBillPaySavingCardDeductionPackageCardProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillPackageCardProjectID = SBP.ID )
        ELSE (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCardProject SBPSCDP
        WITH(NOLOCK) WHERE SBPSCDP.SaleBillPackageCardProjectID = SBP.ID ) END AS LargessSavingCardDeductionAmount,

        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission)*-1 ELSE
        (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission) END AS SavingCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance,
        CASE WHEN SB.BillType = '20' THEN
        (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission)*-1
        ELSE (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission) END AS
        SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionPackageCardProject SEPC WITH(NOLOCK)
        LEFT JOIN TB_PackageCardSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.PackageCardSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerPackageCard TSBP WITH(NOLOCK) ON TSBP.SaleBillPackageCardID =
        SEPC.SaleBillPackageCardID AND TSBP.PackageCardSaleHandlerID =SEPC.PackageCardSaleHandlerID AND TSBP.EmployeeID
        =SEPC.EmployeeID
        LEFT JOIN TB_SaleBillPackageCardProject SBP WITH(NOLOCK) ON SEPC.SaleBillPackageCardProjectID = SBP.ID

        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON SEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>

            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND ( P.Name like '%'+ #{GoodsName} +'%' or P.Alias like '%'+ #{GoodsName} +'%'
                OR P.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR P.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR P.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR P.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        '储值卡' AS GoodsTypeName,
        SCC.Name AS CategoryName,
        SC.Name AS GoodName,
        SBP.Amount AS Price,
        SBP.Quantity,
        CASE WHEN SBP.Amount!=0 THEN (SBP.TotalAmount-(SBP.Amount*SBP.Quantity)) ELSE 0 END PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCard SBPSC WITH(NOLOCK)
        WHERE SBPSC.SaleBillSavingCardID= SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCard SBPSC WITH(NOLOCK) WHERE
        SBPSC.SaleBillSavingCardID= SBP.ID) END AS PayAmount,

        0 AS SavingCardDeductionAmount,
        0 AS LargessSavingCardDeductionAmount,
        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        0 AS SavingCardPerformance,
        0 AS SavingCardCommission,
        0 AS SavingCardLargessPerformance,
        0 AS SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionSavingCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SavingCardSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.SavingCardSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerSavingCard TSBP WITH(NOLOCK) ON TSBP.SaleBillSavingCardID =
        SEPC.SaleBillSavingCardID AND TSBP.SavingCardSaleHandlerID =SEPC.SavingCardSaleHandlerID AND TSBP.EmployeeID
        =SEPC.EmployeeID
        LEFT JOIN TB_SaleBillSavingCard SBP WITH(NOLOCK) ON SEPC.SaleBillSavingCardID = SBP.ID
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SEPC.SavingCardID = SC.ID
        LEFT JOIN TB_SavingCardCategory SCC WITH(NOLOCK) ON SC.SavingCardCategoryID = SCC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>

            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND SC.SavingCardCategoryID=#{CategoryID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND ( SC.Name like '%'+ #{GoodsName} +'%' or SC.Alias like '%'+ #{GoodsName} +'%'
                OR SC.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR SC.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR SC.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR SC.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        '储值卡' AS GoodsTypeName,
        SCC.Name AS CategoryName,
        SC.Name AS GoodName,
        SBP.Amount AS Price,
        SBP.Quantity*SBP.PackageQuantity Quantity,
        CASE WHEN SBP.Amount!=0 THEN (SBP.TotalAmount-(SBP.Amount*SBP.Quantity*SBP.PackageQuantity)) ELSE 0 END
        PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardSavingCard SBPSC
        WITH(NOLOCK) WHERE SBPSC.SaleBillPackageCardSavingCardID= SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardSavingCard SBPSC WITH(NOLOCK) WHERE
        SBPSC.SaleBillPackageCardSavingCardID= SBP.ID)END AS PayAmount,

        0 AS SavingCardDeductionAmount,
        0 AS LargessSavingCardDeductionAmount,
        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        0 AS SavingCardPerformance,
        0 AS SavingCardCommission,
        0 AS SavingCardLargessPerformance,
        0 AS SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionPackageCardSavingCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_PackageCardSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.PackageCardSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerPackageCard TSBP WITH(NOLOCK) ON TSBP.SaleBillPackageCardID =
        SEPC.SaleBillPackageCardID AND TSBP.PackageCardSaleHandlerID =SEPC.PackageCardSaleHandlerID AND TSBP.EmployeeID
        =SEPC.EmployeeID
        LEFT JOIN TB_SaleBillPackageCardSavingCard SBP WITH(NOLOCK) ON SEPC.SaleBillPackageCardSavingCardID = SBP.ID

        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SEPC.SavingCardID = SC.ID
        LEFT JOIN TB_SavingCardCategory SCC WITH(NOLOCK) ON SC.SavingCardCategoryID = SCC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>

            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND SC.SavingCardCategoryID=#{CategoryID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND  ( SC.Name like '%'+ #{GoodsName} +'%' or SC.Alias like '%'+ #{GoodsName} +'%'
                OR SC.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR SC.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR SC.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR SC.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        '时效卡' AS GoodsTypeName,
        TCC.Name AS CategoryName,
        TC.Name AS GoodName,
        SBP.Price,
        SBP.Quantity,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayTimeCard SBPTC WITH(NOLOCK)
        WHERE SBPTC.SaleBillTimeCardID =SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayTimeCard SBPTC WITH(NOLOCK) WHERE SBPTC.SaleBillTimeCardID
        =SBP.ID) END AS PayAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard
        SBPSCDTC WITH(NOLOCK) WHERE SBPSCDTC.SaleBillTimeCardID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBP.ID ) END AS SavingCardDeductionAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(LargessAmount),0) FROM
        TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE SBPSCDTC.SaleBillTimeCardID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK)
        WHERE SBPSCDTC.SaleBillTimeCardID = SBP.ID ) END AS LargessSavingCardDeductionAmount,

        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission)*-1 ELSE
        (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission) END AS SavingCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance,
        CASE WHEN SB.BillType = '20' THEN
        (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission)*-1
        ELSE (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission) END AS
        SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionTimeCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_TimeCardSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.TimeCardSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerTimeCard TSBP WITH(NOLOCK) ON TSBP.SaleBillTimeCardID = SEPC.SaleBillTimeCardID AND
        TSBP.TimeCardSaleHandlerID =SEPC.TimeCardSaleHandlerID AND TSBP.EmployeeID =SEPC.EmployeeID

        LEFT JOIN TB_SaleBillTimeCard SBP WITH(NOLOCK) ON SEPC.SaleBillTimeCardID = SBP.ID
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON SEPC.TimeCardID = TC.ID
        LEFT JOIN TB_TimeCardCategory TCC WITH(NOLOCK) ON TC.TimeCardCategoryID = TCC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>

            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND TC.TimeCardCategoryID=#{CategoryID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND ( TC.Name like '%'+ #{GoodsName} +'%' or TC.Alias like '%'+ #{GoodsName} +'%'
                OR TC.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR TC.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR TC.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR TC.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        '时效卡' AS GoodsTypeName,
        TCC.Name AS CategoryName,
        TC.Name AS GoodName,
        SBP.PackagePrice Price,
        SBP.Quantity*SBP.PackageQuantity Quantity,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardTimeCard SBPTC
        WITH(NOLOCK) WHERE SBPTC.SaleBillPackageCardTimeCardID = SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardTimeCard SBPTC WITH(NOLOCK) WHERE
        SBPTC.SaleBillPackageCardTimeCardID = SBP.ID)END AS PayAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM
        TB_SaleBillPaySavingCardDeductionPackageCardTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillPackageCardTimeCardID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCardTimeCard SBPSCDTC
        WITH(NOLOCK) WHERE SBPSCDTC.SaleBillPackageCardTimeCardID = SBP.ID ) end as SavingCardDeductionAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(LargessAmount),0) FROM
        TB_SaleBillPaySavingCardDeductionPackageCardTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillPackageCardTimeCardID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCardTimeCard SBPSCDTC
        WITH(NOLOCK) WHERE SBPSCDTC.SaleBillPackageCardTimeCardID = SBP.ID ) end as LargessSavingCardDeductionAmount,

        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission)*-1 ELSE
        (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission) END AS SavingCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance,
        CASE WHEN SB.BillType = '20' THEN
        (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission)*-1
        ELSE (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission) END AS
        SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionPackageCardTimeCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_PackageCardSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.PackageCardSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerPackageCard TSBP WITH(NOLOCK) ON TSBP.SaleBillPackageCardID =
        SEPC.SaleBillPackageCardID AND TSBP.PackageCardSaleHandlerID =SEPC.PackageCardSaleHandlerID AND TSBP.EmployeeID
        =SEPC.EmployeeID
        LEFT JOIN TB_SaleBillPackageCardTimeCard SBP WITH(NOLOCK) ON SEPC.SaleBillPackageCardTimeCardID = SBP.ID

        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON SEPC.TimeCardID = TC.ID
        LEFT JOIN TB_TimeCardCategory TCC WITH(NOLOCK) ON TC.TimeCardCategoryID = TCC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>

            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND TC.TimeCardCategoryID=#{CategoryID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND ( TC.Name like '%'+ #{GoodsName} +'%' or TC.Alias like '%'+ #{GoodsName} +'%'
                OR TC.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR TC.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR TC.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR TC.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        '通用次卡' AS GoodsTypeName,
        GCC.Name AS CategoryName,
        GC.Name AS GoodName,
        SBP.Price,
        SBP.Quantity,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayGeneralCard SBPTC
        WITH(NOLOCK) WHERE SBPTC.SaleBillGeneralCardID = SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayGeneralCard SBPTC WITH(NOLOCK) WHERE
        SBPTC.SaleBillGeneralCardID = SBP.ID)END AS PayAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM
        TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK) WHERE SBPSCDTC.SaleBillGeneralCardID = SBP.ID
        )*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillGeneralCardID = SBP.ID ) END AS SavingCardDeductionAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(LargessAmount),0) FROM
        TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK) WHERE SBPSCDTC.SaleBillGeneralCardID = SBP.ID
        )*-1
        ELSE (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC
        WITH(NOLOCK) WHERE SBPSCDTC.SaleBillGeneralCardID = SBP.ID ) END AS LargessSavingCardDeductionAmount,

        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission)*-1 ELSE
        (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission) END AS SavingCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance,
        CASE WHEN SB.BillType = '20' THEN
        (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission)*-1
        ELSE (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission) END AS
        SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionGeneralCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_GeneralCardSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.GeneralCardSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerGeneralCard TSBP WITH(NOLOCK) ON TSBP.SaleBillGeneralCardID =
        SEPC.SaleBillGeneralCardID AND TSBP.GeneralCardSaleHandlerID =SEPC.GeneralCardSaleHandlerID AND TSBP.EmployeeID
        =SEPC.EmployeeID

        LEFT JOIN TB_SaleBillGeneralCard SBP WITH(NOLOCK) ON SEPC.SaleBillGeneralCardID = SBP.ID
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON SEPC.GeneralCardID = GC.ID
        LEFT JOIN TB_GeneralCardCategory GCC WITH(NOLOCK) ON GC.GeneralCardCategoryID = GCC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>

            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND GC.GeneralCardCategoryID=#{CategoryID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND  ( GC.Name like '%'+ #{GoodsName} +'%' or GC.Alias like '%'+ #{GoodsName} +'%'
                OR GC.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR GC.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR GC.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR GC.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        '通用次卡' AS GoodsTypeName,
        GCC.Name AS CategoryName,
        GC.Name AS GoodName,
        SBP.PackagePrice Price,
        SBP.Quantity*SBP.PackageQuantity Quantity,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardGeneralCard SBPTC
        WITH(NOLOCK) WHERE SBPTC.SaleBillPackageCardGeneralCardID = SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardGeneralCard SBPTC WITH(NOLOCK) WHERE
        SBPTC.SaleBillPackageCardGeneralCardID = SBP.ID) END AS PayAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM
        TB_SaleBillPaySavingCardDeductionPackageCardGeneralCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillPackageCardGeneralCardID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCardGeneralCard SBPSCDTC
        WITH(NOLOCK) WHERE SBPSCDTC.SaleBillPackageCardGeneralCardID = SBP.ID ) END AS SavingCardDeductionAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(LargessAmount),0) FROM
        TB_SaleBillPaySavingCardDeductionPackageCardGeneralCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillPackageCardGeneralCardID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCardGeneralCard SBPSCDTC
        WITH(NOLOCK) WHERE SBPSCDTC.SaleBillPackageCardGeneralCardID = SBP.ID ) END AS LargessSavingCardDeductionAmount,

        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission)*-1 ELSE
        (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission) END AS SavingCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance,
        CASE WHEN SB.BillType = '20' THEN
        (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission)*-1
        ELSE (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission) END AS
        SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionPackageCardGeneralCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_PackageCardSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.PackageCardSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerPackageCard TSBP WITH(NOLOCK) ON TSBP.SaleBillPackageCardID =
        SEPC.SaleBillPackageCardID AND TSBP.PackageCardSaleHandlerID =SEPC.PackageCardSaleHandlerID AND TSBP.EmployeeID
        =SEPC.EmployeeID
        LEFT JOIN TB_SaleBillPackageCardGeneralCard SBP WITH(NOLOCK) ON SEPC.SaleBillPackageCardGeneralCardID = SBP.ID
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON SEPC.GeneralCardID = GC.ID
        LEFT JOIN TB_GeneralCardCategory GCC WITH(NOLOCK) ON GC.GeneralCardCategoryID = GCC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>

            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND GC.GeneralCardCategoryID=#{CategoryID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND  ( GC.Name like '%'+ #{GoodsName} +'%' or GC.Alias like '%'+ #{GoodsName} +'%'
                OR GC.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR GC.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR GC.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR GC.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        '产品' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        SBP.Price,
        SBP.Quantity,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProduct SBPP WITH(NOLOCK)
        WHERE SBPP.SaleBillProductID = SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProduct SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProductID =
        SBP.ID) END AS PayAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProduct
        SBPSCDP WITH(NOLOCK) WHERE SBPSCDP.SaleBillProductID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) END AS SavingCardDeductionAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(LargessAmount),0) FROM
        TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE SBPSCDP.SaleBillProductID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK)
        WHERE SBPSCDP.SaleBillProductID = SBP.ID ) END AS LargessSavingCardDeductionAmount,

        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission)*-1 ELSE
        (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission) END AS SavingCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance,
        CASE WHEN SB.BillType = '20' THEN
        (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission)*-1
        ELSE (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission) END AS
        SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionProduct SEPC WITH(NOLOCK)
        LEFT JOIN TB_ProductSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.ProductSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerProduct TSBP WITH(NOLOCK) ON TSBP.SaleBillProductID = SEPC.SaleBillProductID AND
        TSBP.ProductSaleHandlerID =SEPC.ProductSaleHandlerID AND TSBP.EmployeeID =SEPC.EmployeeID
        LEFT JOIN TB_SaleBillProduct SBP WITH(NOLOCK) ON SEPC.SaleBillProductID = SBP.ID

        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON SEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory SubPC WITH(NOLOCK) ON P.PCategoryID = SubPC .ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON SubPC .ParentID = PC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>

            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND (P.PCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND ( P.Name like '%'+ #{GoodsName} +'%' or P.Alias like '%'+ #{GoodsName} +'%'
                OR P.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR P.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR P.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR P.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>

        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        '产品' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        SBP.PackagePrice Price,
        SBP.Quantity*SBP.PackageQuantity Quantity,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardProduct SBPP
        WITH(NOLOCK) WHERE SBPP.SaleBillPackageCardProductID =SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardProduct SBPP WITH(NOLOCK) WHERE
        SBPP.SaleBillPackageCardProductID =SBP.ID) END AS PayAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM
        TB_SaleBillPaySavingCardDeductionPackageCardProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillPackageCardProductID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCardProduct SBPSCDP WITH(NOLOCK)
        WHERE SBPSCDP.SaleBillPackageCardProductID = SBP.ID ) END AS SavingCardDeductionAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(LargessAmount),0) FROM
        TB_SaleBillPaySavingCardDeductionPackageCardProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillPackageCardProductID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCardProduct SBPSCDP
        WITH(NOLOCK) WHERE SBPSCDP.SaleBillPackageCardProductID = SBP.ID ) END AS LargessSavingCardDeductionAmount,

        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission)*-1 ELSE
        (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission) END AS SavingCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance,
        CASE WHEN SB.BillType = '20' THEN
        (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission)*-1
        ELSE (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission) END AS
        SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionPackageCardProduct SEPC WITH(NOLOCK)
        LEFT JOIN TB_PackageCardSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.PackageCardSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerPackageCard TSBP WITH(NOLOCK) ON TSBP.SaleBillPackageCardID =
        SEPC.SaleBillPackageCardID AND TSBP.PackageCardSaleHandlerID =SEPC.PackageCardSaleHandlerID AND TSBP.EmployeeID
        =SEPC.EmployeeID
        LEFT JOIN TB_SaleBillPackageCardProduct SBP WITH(NOLOCK) ON SEPC.SaleBillPackageCardProductID = SBP.ID
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON SEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory SubPC WITH(NOLOCK) ON P.PCategoryID = SubPC .ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON SubPC .ParentID = PC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>

            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND (P.PCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
        </where>
        <if test="GoodsName != null and GoodsName != ''">
            AND ( P.Name like '%'+ #{GoodsName} +'%' or P.Alias like '%'+ #{GoodsName} +'%'
            OR P.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR P.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
            OR P.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR P.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
        </if>
        <if test="CustomerLevelID != null ">
            AND TCR.CustomerLevelID = #{CustomerLevelID}
        </if>

        ) T
        <where>
            <if test="BillID != null and BillID != '' ">
                AND SaleBillID = #{BillID}
            </if>
            <if test="GoodsTypeName != null and GoodsTypeName != '' ">
                AND GoodsTypeName = #{GoodsTypeName}
            </if>
        </where>
    </select>

    <select id="getExcelEmployeeSalePerformanceCommissionDetail"
            resultType="com.zhelian.model.report.form.output.EmployeeSalePerformanceCommissionDetailStatementExcelForm">
        SELECT
        SaleBillID as saleBillID,CONVERT(varchar(100), BillDate, 120) AS
        billDate,
        SaleHandlerName as saleHandlerName,
        EntityName as entityName,
        EmployeeID as employeeID,
        EmployeeName as employeeName,
        EmployeeEntityName as employeeEntityName,
        JobName as jobName,
        ISNULL(CustomerName,'散客')customerName,
        Code as code,
        REPLACE(PhoneNumber , SUBSTRING (PhoneNumber,4,4), '****') phoneNumber,
        CustomerEntityName as customerEntityName,
        CustomerLevelName as customerLevelName,
        CustomerSourceName as customerSourceName,
        ChannelName as channelName,
        GoodsTypeName as goodsTypeName,
        CategoryName as categoryName,
        GoodName as goodName,
        Price as price,
        Quantity as quantity,
        PreferentialAmount as preferentialAmount,
        TotalAmount as totalAmount,
        ArrearAmount as arrearAmount,
        PayAmount as payAmount,
        SavingCardDeductionAmount as savingCardDeductionAmount,
        LargessSavingCardDeductionAmount as largessSavingCardDeductionAmount,
        CONVERT(VARCHAR(8),Scale*100)+'%' scale,
        PayPerformance as payPerformance,
        PayCommission as payCommission,
        SavingCardPerformance as savingCardPerformance,
        SavingCardCommission as savingCardCommission,
        SavingCardLargessPerformance as savingCardLargessPerformance,
        SavingCardLargessCardCommission as savingCardLargessCardCommission,
        SpecialBenefitCommission as specialBenefitCommission
        FROM (
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TCSE.Name CustomerSourceName,
        cha.Name AS ChannelName,
        '项目' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        SBP.Price,
        SBP.Quantity,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProject SBPP WITH(NOLOCK)
        WHERE SBPP.SaleBillProjectID =SBP.ID) *-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProject SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProjectID
        =SBP.ID) END AS PayAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProject
        SBPSCDP WITH(NOLOCK) WHERE SBPSCDP.SaleBillProjectID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProjectID = SBP.ID ) END AS SavingCardDeductionAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(LargessAmount),0) FROM
        TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) WHERE SBPSCDP.SaleBillProjectID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK)
        WHERE SBPSCDP.SaleBillProjectID = SBP.ID ) END AS LargessSavingCardDeductionAmount,

        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission)*-1 ELSE
        (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission) END AS SavingCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance,
        CASE WHEN SB.BillType = '20' THEN
        (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission)*-1
        ELSE (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission) END AS
        SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionProject SEPC WITH(NOLOCK)
        LEFT JOIN TB_ProjectSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.ProjectSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerProject TSBP WITH(NOLOCK) ON TSBP.SaleBillProjectID = SEPC.SaleBillProjectID AND
        TSBP.ProjectSaleHandlerID =SEPC.ProjectSaleHandlerID AND TSBP.EmployeeID =SEPC.EmployeeID
        LEFT JOIN TB_SaleBillProject SBP WITH(NOLOCK) ON SEPC.SaleBillProjectID = SBP.ID
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON SEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        LEFT JOIN TB_CustomerSource TCSE WITH(NOLOCK) ON TCR.CustomerSourceID = TCSE.ID
        LEFT JOIN TB_Channel cha WITH(NOLOCK) on TCR.ChannelID = cha.ID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>

            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND ( P.Name like '%'+ #{GoodsName} +'%' or P.Alias like '%'+ #{GoodsName} +'%'
                OR P.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR P.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR P.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR P.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>

        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TCSE.Name CustomerSourceName,
        cha.Name AS ChannelName,
        '项目' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        SBP.PackagePrice Price,
        SBP.Quantity*SBP.PackageQuantity Quantity,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardProject SBPP
        WITH(NOLOCK) WHERE SBPP.SaleBillPackageCardProjectID =SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardProject SBPP WITH(NOLOCK) WHERE
        SBPP.SaleBillPackageCardProjectID =SBP.ID)end AS PayAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM
        TB_SaleBillPaySavingCardDeductionPackageCardProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillPackageCardProjectID = SBP.ID )
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCardProject SBPSCDP WITH(NOLOCK)
        WHERE SBPSCDP.SaleBillPackageCardProjectID = SBP.ID ) END AS SavingCardDeductionAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(LargessAmount),0) FROM
        TB_SaleBillPaySavingCardDeductionPackageCardProject SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillPackageCardProjectID = SBP.ID )
        ELSE (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCardProject SBPSCDP
        WITH(NOLOCK) WHERE SBPSCDP.SaleBillPackageCardProjectID = SBP.ID ) END AS LargessSavingCardDeductionAmount,

        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission)*-1 ELSE
        (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission) END AS SavingCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance,
        CASE WHEN SB.BillType = '20' THEN
        (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission)*-1
        ELSE (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission) END AS
        SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionPackageCardProject SEPC WITH(NOLOCK)
        LEFT JOIN TB_PackageCardSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.PackageCardSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerPackageCard TSBP WITH(NOLOCK) ON TSBP.SaleBillPackageCardID =
        SEPC.SaleBillPackageCardID AND TSBP.PackageCardSaleHandlerID =SEPC.PackageCardSaleHandlerID AND TSBP.EmployeeID
        =SEPC.EmployeeID
        LEFT JOIN TB_SaleBillPackageCardProject SBP WITH(NOLOCK) ON SEPC.SaleBillPackageCardProjectID = SBP.ID

        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON SEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        LEFT JOIN TB_CustomerSource TCSE WITH(NOLOCK) ON TCR.CustomerSourceID = TCSE.ID
        LEFT JOIN TB_Channel cha WITH(NOLOCK) on TCR.ChannelID = cha.ID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>

            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND ( P.Name like '%'+ #{GoodsName} +'%' or P.Alias like '%'+ #{GoodsName} +'%'
                OR P.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR P.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR P.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR P.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>

        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TCSE.Name CustomerSourceName,
        cha.Name AS ChannelName,
        '储值卡' AS GoodsTypeName,
        SCC.Name AS CategoryName,
        SC.Name AS GoodName,
        SBP.Amount AS Price,
        SBP.Quantity,
        CASE WHEN SBP.Amount!=0 THEN (SBP.TotalAmount-(SBP.Amount*SBP.Quantity)) ELSE 0 END PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCard SBPSC WITH(NOLOCK)
        WHERE SBPSC.SaleBillSavingCardID= SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCard SBPSC WITH(NOLOCK) WHERE
        SBPSC.SaleBillSavingCardID= SBP.ID) END AS PayAmount,
        0 AS SavingCardDeductionAmount,
        0 AS LargessSavingCardDeductionAmount,
        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        0 AS SavingCardPerformance,
        0 AS SavingCardCommission,
        0 AS SavingCardLargessPerformance,
        0 AS SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionSavingCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_SavingCardSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.SavingCardSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerSavingCard TSBP WITH(NOLOCK) ON TSBP.SaleBillSavingCardID =
        SEPC.SaleBillSavingCardID AND TSBP.SavingCardSaleHandlerID =SEPC.SavingCardSaleHandlerID AND TSBP.EmployeeID
        =SEPC.EmployeeID
        LEFT JOIN TB_SaleBillSavingCard SBP WITH(NOLOCK) ON SEPC.SaleBillSavingCardID = SBP.ID
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SEPC.SavingCardID = SC.ID
        LEFT JOIN TB_SavingCardCategory SCC WITH(NOLOCK) ON SC.SavingCardCategoryID = SCC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        LEFT JOIN TB_CustomerSource TCSE WITH(NOLOCK) ON TCR.CustomerSourceID = TCSE.ID
        LEFT JOIN TB_Channel cha WITH(NOLOCK) on TCR.ChannelID = cha.ID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>

            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND SC.SavingCardCategoryID=#{CategoryID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND  ( SC.Name like '%'+ #{GoodsName} +'%' or SC.Alias like '%'+ #{GoodsName} +'%'
                OR SC.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR SC.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR SC.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR SC.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TCSE.Name CustomerSourceName,
        cha.Name AS ChannelName,
        '储值卡' AS GoodsTypeName,
        SCC.Name AS CategoryName,
        SC.Name AS GoodName,
        SBP.Amount AS Price,
        SBP.Quantity*SBP.PackageQuantity Quantity,
        CASE WHEN SBP.Amount!=0 THEN (SBP.TotalAmount-(SBP.Amount*SBP.Quantity*SBP.PackageQuantity)) ELSE 0 END
        PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardSavingCard SBPSC
        WITH(NOLOCK) WHERE SBPSC.SaleBillPackageCardSavingCardID= SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardSavingCard SBPSC WITH(NOLOCK) WHERE
        SBPSC.SaleBillPackageCardSavingCardID= SBP.ID)END AS PayAmount,

        0 AS SavingCardDeductionAmount,
        0 AS LargessSavingCardDeductionAmount,
        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        0 AS SavingCardPerformance,
        0 AS SavingCardCommission,
        0 AS SavingCardLargessPerformance,
        0 AS SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionPackageCardSavingCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_PackageCardSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.PackageCardSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerPackageCard TSBP WITH(NOLOCK) ON TSBP.SaleBillPackageCardID =
        SEPC.SaleBillPackageCardID AND TSBP.PackageCardSaleHandlerID =SEPC.PackageCardSaleHandlerID AND TSBP.EmployeeID
        =SEPC.EmployeeID
        LEFT JOIN TB_SaleBillPackageCardSavingCard SBP WITH(NOLOCK) ON SEPC.SaleBillPackageCardSavingCardID = SBP.ID

        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SEPC.SavingCardID = SC.ID
        LEFT JOIN TB_SavingCardCategory SCC WITH(NOLOCK) ON SC.SavingCardCategoryID = SCC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        LEFT JOIN TB_CustomerSource TCSE WITH(NOLOCK) ON TCR.CustomerSourceID = TCSE.ID
        LEFT JOIN TB_Channel cha WITH(NOLOCK) on TCR.ChannelID = cha.ID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>

            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND SC.SavingCardCategoryID=#{CategoryID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND  ( SC.Name like '%'+ #{GoodsName} +'%' or SC.Alias like '%'+ #{GoodsName} +'%'
                OR SC.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR SC.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR SC.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR SC.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TCSE.Name CustomerSourceName,
        cha.Name AS ChannelName,
        '时效卡' AS GoodsTypeName,
        TCC.Name AS CategoryName,
        TC.Name AS GoodName,
        SBP.Price,
        SBP.Quantity,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayTimeCard SBPTC WITH(NOLOCK)
        WHERE SBPTC.SaleBillTimeCardID =SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayTimeCard SBPTC WITH(NOLOCK) WHERE SBPTC.SaleBillTimeCardID
        =SBP.ID) END AS PayAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard
        SBPSCDTC WITH(NOLOCK) WHERE SBPSCDTC.SaleBillTimeCardID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillTimeCardID = SBP.ID ) END AS SavingCardDeductionAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(LargessAmount),0) FROM
        TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) WHERE SBPSCDTC.SaleBillTimeCardID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK)
        WHERE SBPSCDTC.SaleBillTimeCardID = SBP.ID ) END AS LargessSavingCardDeductionAmount,

        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission)*-1 ELSE
        (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission) END AS SavingCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance,
        CASE WHEN SB.BillType = '20' THEN
        (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission)*-1
        ELSE (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission) END AS
        SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionTimeCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_TimeCardSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.TimeCardSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerTimeCard TSBP WITH(NOLOCK) ON TSBP.SaleBillTimeCardID = SEPC.SaleBillTimeCardID AND
        TSBP.TimeCardSaleHandlerID =SEPC.TimeCardSaleHandlerID AND TSBP.EmployeeID =SEPC.EmployeeID

        LEFT JOIN TB_SaleBillTimeCard SBP WITH(NOLOCK) ON SEPC.SaleBillTimeCardID = SBP.ID
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON SEPC.TimeCardID = TC.ID
        LEFT JOIN TB_TimeCardCategory TCC WITH(NOLOCK) ON TC.TimeCardCategoryID = TCC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        LEFT JOIN TB_CustomerSource TCSE WITH(NOLOCK) ON TCR.CustomerSourceID = TCSE.ID
        LEFT JOIN TB_Channel cha WITH(NOLOCK) on TCR.ChannelID = cha.ID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>

            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND TC.TimeCardCategoryID=#{CategoryID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND ( TC.Name like '%'+ #{GoodsName} +'%' or TC.Alias like '%'+ #{GoodsName} +'%'
                OR TC.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR TC.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR TC.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR TC.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TCSE.Name CustomerSourceName,
        cha.Name AS ChannelName,
        '时效卡' AS GoodsTypeName,
        TCC.Name AS CategoryName,
        TC.Name AS GoodName,
        SBP.PackagePrice Price,
        SBP.Quantity*SBP.PackageQuantity Quantity,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardTimeCard SBPTC
        WITH(NOLOCK) WHERE SBPTC.SaleBillPackageCardTimeCardID = SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardTimeCard SBPTC WITH(NOLOCK) WHERE
        SBPTC.SaleBillPackageCardTimeCardID = SBP.ID)END AS PayAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM
        TB_SaleBillPaySavingCardDeductionPackageCardTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillPackageCardTimeCardID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCardTimeCard SBPSCDTC
        WITH(NOLOCK) WHERE SBPSCDTC.SaleBillPackageCardTimeCardID = SBP.ID ) end as SavingCardDeductionAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(LargessAmount),0) FROM
        TB_SaleBillPaySavingCardDeductionPackageCardTimeCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillPackageCardTimeCardID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCardTimeCard SBPSCDTC
        WITH(NOLOCK) WHERE SBPSCDTC.SaleBillPackageCardTimeCardID = SBP.ID ) end as LargessSavingCardDeductionAmount,

        TSBP.Scale,

        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission)*-1 ELSE
        (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission) END AS SavingCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance,
        CASE WHEN SB.BillType = '20' THEN
        (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission)*-1
        ELSE (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission) END AS
        SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionPackageCardTimeCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_PackageCardSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.PackageCardSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerPackageCard TSBP WITH(NOLOCK) ON TSBP.SaleBillPackageCardID =
        SEPC.SaleBillPackageCardID AND TSBP.PackageCardSaleHandlerID =SEPC.PackageCardSaleHandlerID AND TSBP.EmployeeID
        =SEPC.EmployeeID
        LEFT JOIN TB_SaleBillPackageCardTimeCard SBP WITH(NOLOCK) ON SEPC.SaleBillPackageCardTimeCardID = SBP.ID

        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON SEPC.TimeCardID = TC.ID
        LEFT JOIN TB_TimeCardCategory TCC WITH(NOLOCK) ON TC.TimeCardCategoryID = TCC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        LEFT JOIN TB_CustomerSource TCSE WITH(NOLOCK) ON TCR.CustomerSourceID = TCSE.ID
        LEFT JOIN TB_Channel cha WITH(NOLOCK) on TCR.ChannelID = cha.ID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>

            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND TC.TimeCardCategoryID=#{CategoryID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND ( TC.Name like '%'+ #{GoodsName} +'%' or TC.Alias like '%'+ #{GoodsName} +'%'
                OR TC.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR TC.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR TC.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR TC.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TCSE.Name CustomerSourceName,
        cha.Name AS ChannelName,
        '通用次卡' AS GoodsTypeName,
        GCC.Name AS CategoryName,
        GC.Name AS GoodName,
        SBP.Price,
        SBP.Quantity,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayGeneralCard SBPTC
        WITH(NOLOCK) WHERE SBPTC.SaleBillGeneralCardID = SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayGeneralCard SBPTC WITH(NOLOCK) WHERE
        SBPTC.SaleBillGeneralCardID = SBP.ID)END AS PayAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM
        TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK) WHERE SBPSCDTC.SaleBillGeneralCardID = SBP.ID
        )*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillGeneralCardID = SBP.ID ) END AS SavingCardDeductionAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(LargessAmount),0) FROM
        TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC WITH(NOLOCK) WHERE SBPSCDTC.SaleBillGeneralCardID = SBP.ID
        )*-1
        ELSE (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDTC
        WITH(NOLOCK) WHERE SBPSCDTC.SaleBillGeneralCardID = SBP.ID ) END AS LargessSavingCardDeductionAmount,

        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission)*-1 ELSE
        (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission) END AS SavingCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance,
        CASE WHEN SB.BillType = '20' THEN
        (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission)*-1
        ELSE (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission) END AS
        SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionGeneralCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_GeneralCardSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.GeneralCardSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerGeneralCard TSBP WITH(NOLOCK) ON TSBP.SaleBillGeneralCardID =
        SEPC.SaleBillGeneralCardID AND TSBP.GeneralCardSaleHandlerID =SEPC.GeneralCardSaleHandlerID AND TSBP.EmployeeID
        =SEPC.EmployeeID

        LEFT JOIN TB_SaleBillGeneralCard SBP WITH(NOLOCK) ON SEPC.SaleBillGeneralCardID = SBP.ID
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON SEPC.GeneralCardID = GC.ID
        LEFT JOIN TB_GeneralCardCategory GCC WITH(NOLOCK) ON GC.GeneralCardCategoryID = GCC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        LEFT JOIN TB_CustomerSource TCSE WITH(NOLOCK) ON TCR.CustomerSourceID = TCSE.ID
        LEFT JOIN TB_Channel cha WITH(NOLOCK) on TCR.ChannelID = cha.ID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>

            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND  GC.GeneralCardCategoryID =#{CategoryID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND ( GC.Name like '%'+ #{GoodsName} +'%' or GC.Alias like '%'+ #{GoodsName} +'%'
            OR GC.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR GC.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
            OR GC.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR GC.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
        </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TCSE.Name CustomerSourceName,
        cha.Name AS ChannelName,
        '通用次卡' AS GoodsTypeName,
        GCC.Name AS CategoryName,
        GC.Name AS GoodName,
        SBP.PackagePrice Price,
        SBP.Quantity*SBP.PackageQuantity Quantity,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardGeneralCard SBPTC
        WITH(NOLOCK) WHERE SBPTC.SaleBillPackageCardGeneralCardID = SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardGeneralCard SBPTC WITH(NOLOCK) WHERE
        SBPTC.SaleBillPackageCardGeneralCardID = SBP.ID) END AS PayAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM
        TB_SaleBillPaySavingCardDeductionPackageCardGeneralCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillPackageCardGeneralCardID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCardGeneralCard SBPSCDTC
        WITH(NOLOCK) WHERE SBPSCDTC.SaleBillPackageCardGeneralCardID = SBP.ID ) END AS SavingCardDeductionAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(LargessAmount),0) FROM
        TB_SaleBillPaySavingCardDeductionPackageCardGeneralCard SBPSCDTC WITH(NOLOCK) WHERE
        SBPSCDTC.SaleBillPackageCardGeneralCardID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCardGeneralCard SBPSCDTC
        WITH(NOLOCK) WHERE SBPSCDTC.SaleBillPackageCardGeneralCardID = SBP.ID ) END AS LargessSavingCardDeductionAmount,

        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission)*-1 ELSE
        (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission) END AS SavingCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance,
        CASE WHEN SB.BillType = '20' THEN
        (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission)*-1
        ELSE (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission) END AS
        SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionPackageCardGeneralCard SEPC WITH(NOLOCK)
        LEFT JOIN TB_PackageCardSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.PackageCardSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerPackageCard TSBP WITH(NOLOCK) ON TSBP.SaleBillPackageCardID =
        SEPC.SaleBillPackageCardID AND TSBP.PackageCardSaleHandlerID =SEPC.PackageCardSaleHandlerID AND TSBP.EmployeeID
        =SEPC.EmployeeID
        LEFT JOIN TB_SaleBillPackageCardGeneralCard SBP WITH(NOLOCK) ON SEPC.SaleBillPackageCardGeneralCardID = SBP.ID
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON SEPC.GeneralCardID = GC.ID
        LEFT JOIN TB_GeneralCardCategory GCC WITH(NOLOCK) ON GC.GeneralCardCategoryID = GCC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        LEFT JOIN TB_CustomerSource TCSE WITH(NOLOCK) ON TCR.CustomerSourceID = TCSE.ID
        LEFT JOIN TB_Channel cha WITH(NOLOCK) on TCR.ChannelID = cha.ID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>

            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND  GC.GeneralCardCategoryID =#{CategoryID}
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND ( GC.Name like '%'+ #{GoodsName} +'%' or GC.Alias like '%'+ #{GoodsName} +'%'
                OR GC.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR GC.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR GC.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR GC.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TCSE.Name CustomerSourceName,
        cha.Name AS ChannelName,
        '产品' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        SBP.Price,
        SBP.Quantity,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProduct SBPP WITH(NOLOCK)
        WHERE SBPP.SaleBillProductID = SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayProduct SBPP WITH(NOLOCK) WHERE SBPP.SaleBillProductID =
        SBP.ID) END AS PayAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProduct
        SBPSCDP WITH(NOLOCK) WHERE SBPSCDP.SaleBillProductID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillProductID = SBP.ID ) END AS SavingCardDeductionAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(LargessAmount),0) FROM
        TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) WHERE SBPSCDP.SaleBillProductID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK)
        WHERE SBPSCDP.SaleBillProductID = SBP.ID ) END AS LargessSavingCardDeductionAmount,

        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission)*-1 ELSE
        (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission) END AS SavingCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance,
        CASE WHEN SB.BillType = '20' THEN
        (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission)*-1
        ELSE (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission) END AS
        SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionProduct SEPC WITH(NOLOCK)
        LEFT JOIN TB_ProductSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.ProductSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerProduct TSBP WITH(NOLOCK) ON TSBP.SaleBillProductID = SEPC.SaleBillProductID AND
        TSBP.ProductSaleHandlerID =SEPC.ProductSaleHandlerID AND TSBP.EmployeeID =SEPC.EmployeeID
        LEFT JOIN TB_SaleBillProduct SBP WITH(NOLOCK) ON SEPC.SaleBillProductID = SBP.ID

        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON SEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory SubPC WITH(NOLOCK) ON P.PCategoryID = SubPC .ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON SubPC .ParentID = PC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        LEFT JOIN TB_CustomerSource TCSE WITH(NOLOCK) ON TCR.CustomerSourceID = TCSE.ID
        LEFT JOIN TB_Channel cha WITH(NOLOCK) on TCR.ChannelID = cha.ID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>

            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND (P.PCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND  ( P.Name like '%'+ #{GoodsName} +'%' or P.Alias like '%'+ #{GoodsName} +'%'
                OR P.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR P.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR P.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR P.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        SEPC.SaleBillID,
        SEPC.BillDate,
        PSH.Name AS SaleHandlerName,
        E.EntityName,
        Emp.ID AS EmployeeID,
        Emp.Name AS EmployeeName,
        EE.EntityName AS EmployeeEntityName,
        JT.JobName,
        TCR.Name CustomerName,
        CEE.EntityName AS CustomerEntityName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TCSE.Name CustomerSourceName,
        cha.Name AS ChannelName,
        '产品' AS GoodsTypeName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        SBP.PackagePrice Price,
        SBP.Quantity*SBP.PackageQuantity Quantity,
        (SBP.MemberPreferentialAmount+SBP.PricePreferentialAmount+SBP.CardPreferentialAmount)*-1 AS PreferentialAmount,
        CASE WHEN SB.BillType = '20' THEN SBP.TotalAmount*-1 ELSE SBP.TotalAmount END AS TotalAmount,
        CASE WHEN SB.BillType = '20' THEN 0 ELSE SBP.ArrearAmount END AS ArrearAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardProduct SBPP
        WITH(NOLOCK) WHERE SBPP.SaleBillPackageCardProductID =SBP.ID)*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPayPackageCardProduct SBPP WITH(NOLOCK) WHERE
        SBPP.SaleBillPackageCardProductID =SBP.ID) END AS PayAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(Amount),0) FROM
        TB_SaleBillPaySavingCardDeductionPackageCardProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillPackageCardProductID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(Amount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCardProduct SBPSCDP WITH(NOLOCK)
        WHERE SBPSCDP.SaleBillPackageCardProductID = SBP.ID ) END AS SavingCardDeductionAmount,
        CASE WHEN SB.BillType = '20' THEN (SELECT ISNULL(SUM(LargessAmount),0) FROM
        TB_SaleBillPaySavingCardDeductionPackageCardProduct SBPSCDP WITH(NOLOCK) WHERE
        SBPSCDP.SaleBillPackageCardProductID = SBP.ID )*-1
        ELSE (SELECT ISNULL(SUM(LargessAmount),0) FROM TB_SaleBillPaySavingCardDeductionPackageCardProduct SBPSCDP
        WITH(NOLOCK) WHERE SBPSCDP.SaleBillPackageCardProductID = SBP.ID ) END AS LargessSavingCardDeductionAmount,

        TSBP.Scale,
        CASE WHEN SB.BillType = '20' THEN SEPC.PayPerformance*-1 ELSE SEPC.PayPerformance END AS PayPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.PayRateCommission+SEPC.PayFixedCommission)*-1 ELSE
        (SEPC.PayRateCommission+SEPC.PayFixedCommission) END AS PayCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardPerformance*-1 ELSE SEPC.SavingCardPerformance END AS
        SavingCardPerformance,
        CASE WHEN SB.BillType = '20' THEN (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission)*-1 ELSE
        (SEPC.SavingCardRateCommission+SEPC.SavingCardFixedCommission) END AS SavingCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SavingCardLargessPerformance*-1 ELSE SEPC.SavingCardLargessPerformance
        END
        AS SavingCardLargessPerformance,
        CASE WHEN SB.BillType = '20' THEN
        (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission)*-1
        ELSE (SEPC.SavingCardLargessRateCommission+SEPC.SavingCardLargessFixedCommission) END AS
        SavingCardLargessCardCommission,
        CASE WHEN SB.BillType = '20' THEN SEPC.SpecialBenefitCommission *-1 ELSE SEPC.SpecialBenefitCommission END AS
        SpecialBenefitCommission
        FROM TB_SaleEmployeePerformanceCommissionPackageCardProduct SEPC WITH(NOLOCK)
        LEFT JOIN TB_PackageCardSaleHandler PSH WITH(NOLOCK) ON PSH.ID = SEPC.PackageCardSaleHandlerID
        LEFT JOIN TB_SaleBillHandlerPackageCard TSBP WITH(NOLOCK) ON TSBP.SaleBillPackageCardID =
        SEPC.SaleBillPackageCardID AND TSBP.PackageCardSaleHandlerID =SEPC.PackageCardSaleHandlerID AND TSBP.EmployeeID
        =SEPC.EmployeeID
        LEFT JOIN TB_SaleBillPackageCardProduct SBP WITH(NOLOCK) ON SEPC.SaleBillPackageCardProductID = SBP.ID
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SEPC.SaleBillID = SB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON SEPC.EntityID = E.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON SEPC.EmployeeID = Emp.ID
        LEFT JOIN TK_EmployeeEntity Eme WITH(NOLOCK) ON Eme.EmployeeID = Emp.ID AND Eme.IsPrimaryEntity = 1
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON EE.ID = Eme.EntityID
        LEFT JOIN TK_JobType JT WITH(NOLOCK) ON EMP.JobID = JT.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON SEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory SubPC WITH(NOLOCK) ON P.PCategoryID = SubPC .ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON SubPC .ParentID = PC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON SB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerEntity TCRE WITH(NOLOCK) ON TCRE.CustomerID = TCR.ID AND TCRE.IsBelongEntity = 1
        LEFT JOIN TK_Entity CEE WITH(NOLOCK) ON CEE.ID = TCRE.EntityID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        LEFT JOIN TB_CustomerSource TCSE WITH(NOLOCK) ON TCR.CustomerSourceID = TCSE.ID
        LEFT JOIN TB_Channel cha WITH(NOLOCK) on TCR.ChannelID = cha.ID
        <where>
            <choose>
                <when test="EntityID!=null">
                    Eme.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = Eme.EntityID
                    )
                </otherwise>
            </choose>

            <if test="StartDate != null and EndDate != null ">
                AND SEPC.BillDate &gt;= #{StartDate} AND SEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND Emp.Name like concat('%',#{EmployeeName},'%')
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="JobID != null and JobID != ''">
                AND Emp.JobID =#{JobID}
            </if>
            <if test=" CategoryID != null ">
                AND (P.PCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="GoodsName != null and GoodsName != ''">
                AND ( P.Name like '%'+ #{GoodsName} +'%' or P.Alias like '%'+ #{GoodsName} +'%'
                OR P.NameFullPinYin LIKE '%'+ #{GoodsName} +'%' OR P.NameAbbrPinYin LIKE '%'+ #{GoodsName} +'%'
                OR P.AliasFullPinYin LIKE '%'+ #{GoodsName} +'%' OR P.AliasAbbrPinYin LIKE '%'+ #{GoodsName} +'%')
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>


        ) T
        <where>
            <if test="BillID != null and BillID != '' ">
                AND SaleBillID = #{BillID}
            </if>
            <if test="GoodsTypeName != null and GoodsTypeName != '' ">
                AND GoodsTypeName = #{GoodsTypeName}
            </if>
        </where>
        ORDER BY BillDate DESC
    </select>

    <select id="getEmployeeSalePerformanceCommissionStatisticsSum"
            resultType="com.zhelian.model.report.form.output.EmployeeSalePerformanceCommissionStatisticsSumStatementForm">
        SELECT

            ISNULL(SUM(SalePayPerformance),0) SalePayPerformance,
            ISNULL(SUM(SalePayCommission),0) SalePayCommission,
            ISNULL(SUM(SaleSavingCardPerformance),0) SaleSavingCardPerformance,
            ISNULL(SUM(SaleSavingCardCommission),0) SaleSavingCardCommission,
            ISNULL(SUM(SaleSavingCardLargessPerformance),0) SaleSavingCardLargessPerformance,
            ISNULL(SUM(SaleSavingCardLargessCommission),0) SaleSavingCardLargessCommission,
            ISNULL(SUM(SaleSpecialBenefitCommission),0) SaleSpecialBenefitCommission,
            ISNULL(SUM(ArrearPayPerformance),0) ArrearPayPerformance,
            ISNULL(SUM(ArrearPayCommission),0)ArrearPayCommission,
            ISNULL(SUM(ArrearSavingCardPerformance),0) ArrearSavingCardPerformance,
            ISNULL(SUM(ArrearSavingCardCommission),0) ArrearSavingCardCommission,
            ISNULL(SUM(ArrearSavingCardLargessPerformance),0) ArrearSavingCardLargessPerformance,
            ISNULL(SUM(ArrearSavingCardLargessCommission),0) ArrearSavingCardLargessCommission,
            ISNULL(SUM(ArrearSpecialBenefitCommission),0) ArrearSpecialBenefitCommission,

            ISNULL(SUM(RefundSalePayPerformance),0) RefundSalePayPerformance,
            ISNULL(SUM(RefundSalePayCommission),0) RefundSalePayCommission,
            ISNULL(SUM(RefundSaleSavingCardPerformance),0) RefundSaleSavingCardPerformance,
            ISNULL(SUM(RefundSaleSavingCardCommission),0) RefundSaleSavingCardCommission,
            ISNULL(SUM(RefundSaleSavingCardLargessPerformance),0) RefundSaleSavingCardLargessPerformance,
            ISNULL(SUM(RefundSaleSavingCardLargessCommission),0) RefundSaleSavingCardLargessCommission,
            ISNULL(SUM(RefundSaleSpecialBenefitCommission),0) RefundSaleSpecialBenefitCommission,
            ISNULL(SUM(SalePayPerformance),0)+ ISNULL(SUM(ArrearPayPerformance),0)-ISNULL(SUM(RefundSalePayPerformance),0) TotalPayPerformance,
            ISNULL(SUM(SalePayCommission), 0)+ ISNULL(SUM(ArrearPayCommission),0)-ISNULL(SUM(RefundSalePayCommission),0) TotalPayCommission,
            ISNULL(SUM(SaleSavingCardPerformance),0)+ISNULL(SUM(ArrearSavingCardPerformance),0)-ISNULL(SUM(RefundSaleSavingCardPerformance),0) TotalSavingCardPerformance,
            ISNULL(SUM(SaleSavingCardCommission),0)+ISNULL(SUM(ArrearSavingCardCommission),0)-ISNULL(SUM(RefundSaleSavingCardCommission),0) TotalSavingCardCommission,
            ISNULL(SUM(SaleSavingCardLargessPerformance),0)+ISNULL(SUM(ArrearSavingCardLargessPerformance),0)-ISNULL(SUM(RefundSaleSavingCardLargessPerformance),0) TotalSavingCardLargessPerformance,
            ISNULL(SUM(SaleSavingCardLargessCommission),0)+ISNULL(SUM(ArrearSavingCardLargessCommission),0)-ISNULL(SUM(RefundSaleSavingCardLargessCommission),0) TotalSaleSavingCardLargessCommission,
            ISNULL(SUM(SaleSpecialBenefitCommission),0)+ ISNULL(SUM(ArrearSpecialBenefitCommission),0)-ISNULL(SUM(RefundSaleSpecialBenefitCommission),0) TotalSpecialBenefitCommission
            FROM
            (
                SELECT
                    tsp.EmployeeID,
                    te.Name EmployeeName,
                    tjt.JobName ,
                    ty.EntityName,
                    CASE WHEN t.BillType ='10' THEN PayPerformance  END SalePayPerformance,
                    CASE WHEN t.BillType ='10' THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                    CASE WHEN t.BillType ='10' THEN SavingCardPerformance  END SaleSavingCardPerformance,
                    CASE WHEN t.BillType ='10' THEN SavingCardRateCommission+SavingCardFixedCommission  END SaleSavingCardCommission,
                    CASE WHEN t.BillType ='10' THEN SavingCardLargessPerformance  END SaleSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='10' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END SaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='10' THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                    CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                    CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                    CASE WHEN t.BillType ='30' THEN SavingCardPerformance  END ArrearSavingCardPerformance,
                    CASE WHEN t.BillType ='30' THEN SavingCardRateCommission+SavingCardFixedCommission  END ArrearSavingCardCommission,
                    CASE WHEN t.BillType ='30' THEN SavingCardLargessPerformance  END ArrearSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='30' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END ArrearSavingCardLargessCommission,
                    CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                    CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                    CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                    CASE WHEN t.BillType ='20'THEN SavingCardPerformance  END RefundSaleSavingCardPerformance,
                    CASE WHEN t.BillType ='20'THEN SavingCardRateCommission+SavingCardFixedCommission  END RefundSaleSavingCardCommission,
                    CASE WHEN t.BillType ='20' THEN SavingCardLargessPerformance  END RefundSaleSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='20'THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END RefundSaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionProduct tsp WITH(NOLOCK)
                LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                    LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                    LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                    LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                    LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                <where>
            t.BillStatus='20'  AND  EXISTS (
            SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
            WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
            )
            <if test="EntityID != null ">
                 AND   ty.ID = #{EntityID}
            </if>
            <if test="StartDate != null and EndDate != null ">
                AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND te.Name like concat('%',#{EmployeeName},'%')
            </if>

            <if test="JobID != null and JobID != ''">
                AND te.JobID =#{JobID}
            </if>
        </where>
                UNION ALL
                SELECT
                    tsp.EmployeeID,
                    te.Name EmployeeName,
                    tjt.JobName ,
                    ty.EntityName ,
                    CASE WHEN t.BillType ='10' THEN PayPerformance  END SalePayPerformance,
                    CASE WHEN t.BillType ='10' THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                    CASE WHEN t.BillType ='10' THEN SavingCardPerformance  END SaleSavingCardPerformance,
                    CASE WHEN t.BillType ='10' THEN SavingCardRateCommission+SavingCardFixedCommission  END SaleSavingCardCommission,
                    CASE WHEN t.BillType ='10' THEN SavingCardLargessPerformance  END SaleSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='10' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END SaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='10' THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                    CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                    CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                    CASE WHEN t.BillType ='30' THEN SavingCardPerformance  END ArrearSavingCardPerformance,
                    CASE WHEN t.BillType ='30' THEN SavingCardRateCommission+SavingCardFixedCommission  END ArrearSavingCardCommission,
                    CASE WHEN t.BillType ='30' THEN SavingCardLargessPerformance  END ArrearSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='30' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END ArrearSavingCardLargessCommission,
                    CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                    CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                    CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                    CASE WHEN t.BillType ='20'THEN SavingCardPerformance  END RefundSaleSavingCardPerformance,
                    CASE WHEN t.BillType ='20'THEN SavingCardRateCommission+SavingCardFixedCommission  END RefundSaleSavingCardCommission,
                    CASE WHEN t.BillType ='20' THEN SavingCardLargessPerformance  END RefundSaleSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='20'THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END RefundSaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionProject   tsp WITH(NOLOCK)
                    LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                    LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                    LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                    LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                    LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
               <where>
            t.BillStatus='20'  AND  EXISTS (
            SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
            WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
            )
                   <if test="EntityID != null ">
                       AND   ty.ID = #{EntityID}
                   </if>
            <if test="StartDate != null and EndDate != null ">
                AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND te.Name like concat('%',#{EmployeeName},'%')
            </if>

            <if test="JobID != null and JobID != ''">
                AND te.JobID =#{JobID}
            </if>
        </where>

                UNION ALL
                SELECT
                    tsp.EmployeeID,
                    te.Name EmployeeName,
                    tjt.JobName ,
                    ty.EntityName ,
                    CASE WHEN t.BillType ='10' THEN PayPerformance  END SalePayPerformance,
                    CASE WHEN t.BillType ='10' THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                    CASE WHEN t.BillType ='10' THEN SavingCardPerformance  END SaleSavingCardPerformance,
                    CASE WHEN t.BillType ='10' THEN SavingCardRateCommission+SavingCardFixedCommission  END SaleSavingCardCommission,
                    CASE WHEN t.BillType ='10' THEN SavingCardLargessPerformance  END SaleSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='10' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END SaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='10' THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                    CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                    CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                    CASE WHEN t.BillType ='30' THEN SavingCardPerformance  END ArrearSavingCardPerformance,
                    CASE WHEN t.BillType ='30' THEN SavingCardRateCommission+SavingCardFixedCommission  END ArrearSavingCardCommission,
                    CASE WHEN t.BillType ='30' THEN SavingCardLargessPerformance  END ArrearSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='30' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END ArrearSavingCardLargessCommission,
                    CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                    CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                    CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                    CASE WHEN t.BillType ='20'THEN SavingCardPerformance  END RefundSaleSavingCardPerformance,
                    CASE WHEN t.BillType ='20'THEN SavingCardRateCommission+SavingCardFixedCommission  END RefundSaleSavingCardCommission,
                    CASE WHEN t.BillType ='20' THEN SavingCardLargessPerformance  END RefundSaleSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='20'THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END RefundSaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionGeneralCard   tsp WITH(NOLOCK)
                    LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                    LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                    LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                    LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                    LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                <where>
            t.BillStatus='20'  AND  EXISTS (
            SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
            WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
            )
                    <if test="EntityID != null ">
                        AND   ty.ID = #{EntityID}
                    </if>
            <if test="StartDate != null and EndDate != null ">
                AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND te.Name like concat('%',#{EmployeeName},'%')
            </if>

            <if test="JobID != null and JobID != ''">
                AND te.JobID =#{JobID}
            </if>
        </where>
                UNION ALL
                SELECT
                    tsp.EmployeeID,
                    te.Name EmployeeName,
                    tjt.JobName ,
                    ty.EntityName ,
                    CASE WHEN t.BillType ='10' THEN PayPerformance  END SalePayPerformance,
                    CASE WHEN t.BillType ='10' THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                    CASE WHEN t.BillType ='10' THEN SavingCardPerformance  END SaleSavingCardPerformance,
                    CASE WHEN t.BillType ='10' THEN SavingCardRateCommission+SavingCardFixedCommission  END SaleSavingCardCommission,
                    CASE WHEN t.BillType ='10' THEN SavingCardLargessPerformance  END SaleSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='10' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END SaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='10' THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                    CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                    CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                    CASE WHEN t.BillType ='30' THEN SavingCardPerformance  END ArrearSavingCardPerformance,
                    CASE WHEN t.BillType ='30' THEN SavingCardRateCommission+SavingCardFixedCommission  END ArrearSavingCardCommission,
                    CASE WHEN t.BillType ='30' THEN SavingCardLargessPerformance  END ArrearSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='30' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END ArrearSavingCardLargessCommission,
                    CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                    CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                    CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                    CASE WHEN t.BillType ='20'THEN SavingCardPerformance  END RefundSaleSavingCardPerformance,
                    CASE WHEN t.BillType ='20'THEN SavingCardRateCommission+SavingCardFixedCommission  END RefundSaleSavingCardCommission,
                    CASE WHEN t.BillType ='20' THEN SavingCardLargessPerformance  END RefundSaleSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='20'THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END RefundSaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionTimeCard   tsp WITH(NOLOCK)
                    LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                    LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                    LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                    LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                    LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                <where>
            t.BillStatus='20'  AND  EXISTS (
            SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
            WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
            )
                    <if test="EntityID != null ">
                        AND   ty.ID = #{EntityID}
                    </if>
            <if test="StartDate != null and EndDate != null ">
                AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND te.Name like concat('%',#{EmployeeName},'%')
            </if>

            <if test="JobID != null and JobID != ''">
                AND te.JobID =#{JobID}
            </if>
        </where>

                UNION ALL
                SELECT
                    tsp.EmployeeID,
                    te.Name EmployeeName,
                    tjt.JobName ,
                    ty.EntityName ,
                    CASE WHEN t.BillType in ('10','40') THEN PayPerformance  END SalePayPerformance,
                    CASE WHEN t.BillType in ('10','40') THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                    0 SaleSavingCardPerformance,
                    0 SaleSavingCardCommission,
                    0 SaleSavingCardLargessPerformance,
                    0 SaleSavingCardLargessCommission,
                    CASE WHEN t.BillType in ('10','40') THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                    CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                    CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                    0 ArrearSavingCardPerformance,
                    0 ArrearSavingCardCommission,
                    0 ArrearSavingCardLargessPerformance,
                    0 ArrearSavingCardLargessCommission,
                    CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                    CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                    CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                    0 RefundSaleSavingCardPerformance,
                    0RefundSaleSavingCardCommission,
                    0RefundSaleSavingCardLargessPerformance,
                    0 RefundSaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionSavingCard   tsp WITH(NOLOCK)
                    LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                    LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                    LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                    LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                    LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                 <where>
            t.BillStatus='20'  AND  EXISTS (
            SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
            WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
            )
                     <if test="EntityID != null ">
                         AND   ty.ID = #{EntityID}
                     </if>
            <if test="StartDate != null and EndDate != null ">
                AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND te.Name like concat('%',#{EmployeeName},'%')
            </if>

            <if test="JobID != null and JobID != ''">
                AND te.JobID =#{JobID}
            </if>
        </where>
                UNION ALL
                SELECT
                tsp.EmployeeID,
                te.Name EmployeeName,
                tjt.JobName ,
                ty.EntityName,
                CASE WHEN t.BillType ='10' THEN PayPerformance  END SalePayPerformance,
                CASE WHEN t.BillType ='10' THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                CASE WHEN t.BillType ='10' THEN SavingCardPerformance  END SaleSavingCardPerformance,
                CASE WHEN t.BillType ='10' THEN SavingCardRateCommission+SavingCardFixedCommission  END SaleSavingCardCommission,
                CASE WHEN t.BillType ='10' THEN SavingCardLargessPerformance  END SaleSavingCardLargessPerformance,
                CASE WHEN t.BillType ='10' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END SaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='10' THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                CASE WHEN t.BillType ='30' THEN SavingCardPerformance  END ArrearSavingCardPerformance,
                CASE WHEN t.BillType ='30' THEN SavingCardRateCommission+SavingCardFixedCommission  END ArrearSavingCardCommission,
                CASE WHEN t.BillType ='30' THEN SavingCardLargessPerformance  END ArrearSavingCardLargessPerformance,
                CASE WHEN t.BillType ='30' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END ArrearSavingCardLargessCommission,
                CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                CASE WHEN t.BillType ='20'THEN SavingCardPerformance  END RefundSaleSavingCardPerformance,
                CASE WHEN t.BillType ='20'THEN SavingCardRateCommission+SavingCardFixedCommission  END RefundSaleSavingCardCommission,
                CASE WHEN t.BillType ='20' THEN SavingCardLargessPerformance  END RefundSaleSavingCardLargessPerformance,
                CASE WHEN t.BillType ='20'THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END RefundSaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionPackageCardProduct tsp WITH(NOLOCK)
                LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                <where>
                    t.BillStatus='20'  AND  EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
                    )
                    <if test="EntityID != null ">
                        AND   ty.ID = #{EntityID}
                    </if>
                    <if test="StartDate != null and EndDate != null ">
                        AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
                    </if>
                    <if test="EmployeeName != null and EmployeeName != ''">
                        AND te.Name like concat('%',#{EmployeeName},'%')
                    </if>

                    <if test="JobID != null and JobID != ''">
                        AND te.JobID =#{JobID}
                    </if>
                </where>
                UNION ALL
                SELECT
                tsp.EmployeeID,
                te.Name EmployeeName,
                tjt.JobName ,
                ty.EntityName ,
                CASE WHEN t.BillType ='10' THEN PayPerformance  END SalePayPerformance,
                CASE WHEN t.BillType ='10' THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                CASE WHEN t.BillType ='10' THEN SavingCardPerformance  END SaleSavingCardPerformance,
                CASE WHEN t.BillType ='10' THEN SavingCardRateCommission+SavingCardFixedCommission  END SaleSavingCardCommission,
                CASE WHEN t.BillType ='10' THEN SavingCardLargessPerformance  END SaleSavingCardLargessPerformance,
                CASE WHEN t.BillType ='10' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END SaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='10' THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                CASE WHEN t.BillType ='30' THEN SavingCardPerformance  END ArrearSavingCardPerformance,
                CASE WHEN t.BillType ='30' THEN SavingCardRateCommission+SavingCardFixedCommission  END ArrearSavingCardCommission,
                CASE WHEN t.BillType ='30' THEN SavingCardLargessPerformance  END ArrearSavingCardLargessPerformance,
                CASE WHEN t.BillType ='30' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END ArrearSavingCardLargessCommission,
                CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                CASE WHEN t.BillType ='20'THEN SavingCardPerformance  END RefundSaleSavingCardPerformance,
                CASE WHEN t.BillType ='20'THEN SavingCardRateCommission+SavingCardFixedCommission  END RefundSaleSavingCardCommission,
                CASE WHEN t.BillType ='20' THEN SavingCardLargessPerformance  END RefundSaleSavingCardLargessPerformance,
                CASE WHEN t.BillType ='20'THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END RefundSaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionPackageCardProject   tsp WITH(NOLOCK)
                LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                <where>
                    t.BillStatus='20'  AND  EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
                    )
                    <if test="EntityID != null ">
                        AND   ty.ID = #{EntityID}
                    </if>
                    <if test="StartDate != null and EndDate != null ">
                        AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
                    </if>
                    <if test="EmployeeName != null and EmployeeName != ''">
                        AND te.Name like concat('%',#{EmployeeName},'%')
                    </if>

                    <if test="JobID != null and JobID != ''">
                        AND te.JobID =#{JobID}
                    </if>
                </where>

                UNION ALL
                SELECT
                tsp.EmployeeID,
                te.Name EmployeeName,
                tjt.JobName ,
                ty.EntityName ,
                CASE WHEN t.BillType ='10' THEN PayPerformance  END SalePayPerformance,
                CASE WHEN t.BillType ='10' THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                CASE WHEN t.BillType ='10' THEN SavingCardPerformance  END SaleSavingCardPerformance,
                CASE WHEN t.BillType ='10' THEN SavingCardRateCommission+SavingCardFixedCommission  END SaleSavingCardCommission,
                CASE WHEN t.BillType ='10' THEN SavingCardLargessPerformance  END SaleSavingCardLargessPerformance,
                CASE WHEN t.BillType ='10' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END SaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='10' THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                CASE WHEN t.BillType ='30' THEN SavingCardPerformance  END ArrearSavingCardPerformance,
                CASE WHEN t.BillType ='30' THEN SavingCardRateCommission+SavingCardFixedCommission  END ArrearSavingCardCommission,
                CASE WHEN t.BillType ='30' THEN SavingCardLargessPerformance  END ArrearSavingCardLargessPerformance,
                CASE WHEN t.BillType ='30' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END ArrearSavingCardLargessCommission,
                CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                CASE WHEN t.BillType ='20'THEN SavingCardPerformance  END RefundSaleSavingCardPerformance,
                CASE WHEN t.BillType ='20'THEN SavingCardRateCommission+SavingCardFixedCommission  END RefundSaleSavingCardCommission,
                CASE WHEN t.BillType ='20' THEN SavingCardLargessPerformance  END RefundSaleSavingCardLargessPerformance,
                CASE WHEN t.BillType ='20'THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END RefundSaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionPackageCardGeneralCard   tsp WITH(NOLOCK)
                LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                <where>
                    t.BillStatus='20'  AND  EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
                    )
                    <if test="EntityID != null ">
                        AND   ty.ID = #{EntityID}
                    </if>
                    <if test="StartDate != null and EndDate != null ">
                        AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
                    </if>
                    <if test="EmployeeName != null and EmployeeName != ''">
                        AND te.Name like concat('%',#{EmployeeName},'%')
                    </if>

                    <if test="JobID != null and JobID != ''">
                        AND te.JobID =#{JobID}
                    </if>
                </where>
                UNION ALL
                SELECT
                tsp.EmployeeID,
                te.Name EmployeeName,
                tjt.JobName ,
                ty.EntityName ,
                CASE WHEN t.BillType ='10' THEN PayPerformance  END SalePayPerformance,
                CASE WHEN t.BillType ='10' THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                CASE WHEN t.BillType ='10' THEN SavingCardPerformance  END SaleSavingCardPerformance,
                CASE WHEN t.BillType ='10' THEN SavingCardRateCommission+SavingCardFixedCommission  END SaleSavingCardCommission,
                CASE WHEN t.BillType ='10' THEN SavingCardLargessPerformance  END SaleSavingCardLargessPerformance,
                CASE WHEN t.BillType ='10' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END SaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='10' THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                CASE WHEN t.BillType ='30' THEN SavingCardPerformance  END ArrearSavingCardPerformance,
                CASE WHEN t.BillType ='30' THEN SavingCardRateCommission+SavingCardFixedCommission  END ArrearSavingCardCommission,
                CASE WHEN t.BillType ='30' THEN SavingCardLargessPerformance  END ArrearSavingCardLargessPerformance,
                CASE WHEN t.BillType ='30' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END ArrearSavingCardLargessCommission,
                CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                CASE WHEN t.BillType ='20'THEN SavingCardPerformance  END RefundSaleSavingCardPerformance,
                CASE WHEN t.BillType ='20'THEN SavingCardRateCommission+SavingCardFixedCommission  END RefundSaleSavingCardCommission,
                CASE WHEN t.BillType ='20' THEN SavingCardLargessPerformance  END RefundSaleSavingCardLargessPerformance,
                CASE WHEN t.BillType ='20'THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END RefundSaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionPackageCardTimeCard   tsp WITH(NOLOCK)
                LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                <where>
                    t.BillStatus='20'  AND  EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
                    )
                    <if test="EntityID != null ">
                        AND   ty.ID = #{EntityID}
                    </if>
                    <if test="StartDate != null and EndDate != null ">
                        AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
                    </if>
                    <if test="EmployeeName != null and EmployeeName != ''">
                        AND te.Name like concat('%',#{EmployeeName},'%')
                    </if>

                    <if test="JobID != null and JobID != ''">
                        AND te.JobID =#{JobID}
                    </if>
                </where>

                UNION ALL
                SELECT
                tsp.EmployeeID,
                te.Name EmployeeName,
                tjt.JobName ,
                ty.EntityName ,
                CASE WHEN t.BillType in ('10','40') THEN PayPerformance  END SalePayPerformance,
                CASE WHEN t.BillType in ('10','40') THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                0 SaleSavingCardPerformance,
                0 SaleSavingCardCommission,
                0 SaleSavingCardLargessPerformance,
                0 SaleSavingCardLargessCommission,
                CASE WHEN t.BillType in ('10','40') THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                0 ArrearSavingCardPerformance,
                0 ArrearSavingCardCommission,
                0 ArrearSavingCardLargessPerformance,
                0 ArrearSavingCardLargessCommission,
                CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                0 RefundSaleSavingCardPerformance,
                0RefundSaleSavingCardCommission,
                0RefundSaleSavingCardLargessPerformance,
                0 RefundSaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionPackageCardSavingCard   tsp WITH(NOLOCK)
                LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                <where>
                    t.BillStatus='20'  AND  EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
                    )
                    <if test="EntityID != null ">
                        AND   ty.ID = #{EntityID}
                    </if>
                    <if test="StartDate != null and EndDate != null ">
                        AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
                    </if>
                    <if test="EmployeeName != null and EmployeeName != ''">
                        AND te.Name like concat('%',#{EmployeeName},'%')
                    </if>

                    <if test="JobID != null and JobID != ''">
                        AND te.JobID =#{JobID}
                    </if>
                </where>
            )T

    </select>

    <select id="getEmployeeSalePerformanceCommissionStatistics"
            resultType="com.zhelian.model.report.form.output.EmployeeSalePerformanceCommissionDetailStatisticsStatementForm">
        SELECT
            T.EmployeeID,
            T.EmployeeName,
            T.JobName ,
            T.EntityName,
            ISNULL(SUM(SalePayPerformance),0) SalePayPerformance,
            ISNULL(SUM(SalePayCommission),0) SalePayCommission,
            ISNULL(SUM(SaleSavingCardPerformance),0) SaleSavingCardPerformance,
            ISNULL(SUM(SaleSavingCardCommission),0) SaleSavingCardCommission,
            ISNULL(SUM(SaleSavingCardLargessPerformance),0) SaleSavingCardLargessPerformance,
            ISNULL(SUM(SaleSavingCardLargessCommission),0) SaleSavingCardLargessCommission,
            ISNULL(SUM(SaleSpecialBenefitCommission),0) SaleSpecialBenefitCommission,
            ISNULL(SUM(ArrearPayPerformance),0) ArrearPayPerformance,
            ISNULL(SUM(ArrearPayCommission),0)ArrearPayCommission,
            ISNULL(SUM(ArrearSavingCardPerformance),0) ArrearSavingCardPerformance,
            ISNULL(SUM(ArrearSavingCardCommission),0)ArrearSavingCardCommission,
            ISNULL(SUM(ArrearSavingCardLargessPerformance),0) ArrearSavingCardLargessPerformance,
            ISNULL(SUM(ArrearSavingCardLargessCommission),0)ArrearSavingCardLargessCommission,
            ISNULL(SUM(ArrearSpecialBenefitCommission),0) ArrearSpecialBenefitCommission,

            ISNULL(SUM(RefundSalePayPerformance),0) RefundSalePayPerformance,
            ISNULL(SUM(RefundSalePayCommission),0) RefundSalePayCommission,
            ISNULL(SUM(RefundSaleSavingCardPerformance),0) RefundSaleSavingCardPerformance,
            ISNULL(SUM(RefundSaleSavingCardCommission),0) RefundSaleSavingCardCommission,
            ISNULL(SUM(RefundSaleSavingCardLargessPerformance),0) RefundSaleSavingCardLargessPerformance,
            ISNULL(SUM(RefundSaleSavingCardLargessCommission),0) RefundSaleSavingCardLargessCommission,
            ISNULL(SUM(RefundSaleSpecialBenefitCommission),0) RefundSaleSpecialBenefitCommission,
            ISNULL(SUM(SalePayPerformance),0)+ ISNULL(SUM(ArrearPayPerformance),0)-ISNULL(SUM(RefundSalePayPerformance),0) TotalPayPerformance,
            ISNULL(SUM(SalePayCommission), 0)+ ISNULL(SUM(ArrearPayCommission),0)-ISNULL(SUM(RefundSalePayCommission),0) TotalPayCommission,
            ISNULL(SUM(SaleSavingCardPerformance),0)+ISNULL(SUM(ArrearSavingCardPerformance),0)-ISNULL(SUM(RefundSaleSavingCardPerformance),0) TotalSavingCardPerformance,
            ISNULL(SUM(SaleSavingCardCommission),0)+ISNULL(SUM(ArrearSavingCardCommission),0)-ISNULL(SUM(RefundSaleSavingCardCommission),0) TotalSavingCardCommission,
            ISNULL(SUM(SaleSavingCardLargessPerformance),0)+ISNULL(SUM(ArrearSavingCardLargessPerformance),0)-ISNULL(SUM(RefundSaleSavingCardLargessPerformance),0) TotalSavingCardLargessPerformance,
            ISNULL(SUM(SaleSavingCardLargessCommission),0)+ISNULL(SUM(ArrearSavingCardLargessCommission),0)-ISNULL(SUM(RefundSaleSavingCardLargessCommission),0) TotalSaleSavingCardLargessCommission,
            ISNULL(SUM(SaleSpecialBenefitCommission),0)+ ISNULL(SUM(ArrearSpecialBenefitCommission),0)-ISNULL(SUM(RefundSaleSpecialBenefitCommission),0) TotalSpecialBenefitCommission
            FROM
            (
                SELECT
                    tsp.EmployeeID,
                    te.Name EmployeeName,
                    tjt.JobName ,
                    ty.EntityName,
                    CASE WHEN t.BillType ='10' THEN PayPerformance  END SalePayPerformance,
                    CASE WHEN t.BillType ='10' THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                    CASE WHEN t.BillType ='10' THEN SavingCardPerformance  END SaleSavingCardPerformance,
                    CASE WHEN t.BillType ='10' THEN SavingCardRateCommission+SavingCardFixedCommission  END SaleSavingCardCommission,
                    CASE WHEN t.BillType ='10' THEN SavingCardLargessPerformance  END SaleSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='10' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END SaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='10' THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                    CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                    CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                    CASE WHEN t.BillType ='30' THEN SavingCardPerformance  END ArrearSavingCardPerformance,
                    CASE WHEN t.BillType ='30' THEN SavingCardRateCommission+SavingCardFixedCommission  END ArrearSavingCardCommission,
                    CASE WHEN t.BillType ='30' THEN SavingCardLargessPerformance  END ArrearSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='30' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END ArrearSavingCardLargessCommission,
                    CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                    CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                    CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                    CASE WHEN t.BillType ='20'THEN SavingCardPerformance  END RefundSaleSavingCardPerformance,
                    CASE WHEN t.BillType ='20'THEN SavingCardRateCommission+SavingCardFixedCommission  END RefundSaleSavingCardCommission,
                    CASE WHEN t.BillType ='20' THEN SavingCardLargessPerformance  END RefundSaleSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='20'THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END RefundSaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionProduct tsp WITH(NOLOCK)
                LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                    LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                    LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                    LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                    LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                <where>
            t.BillStatus='20'  AND  EXISTS (
            SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
            WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
            )
                    <if test="EntityID != null ">
                        AND   ty.ID = #{EntityID}
                    </if>
            <if test="StartDate != null and EndDate != null ">
                AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND te.Name like concat('%',#{EmployeeName},'%')
            </if>

            <if test="JobID != null and JobID != ''">
                AND te.JobID =#{JobID}
            </if>
        </where>
                UNION ALL
                SELECT
                    tsp.EmployeeID,
                    te.Name EmployeeName,
                    tjt.JobName ,
                    ty.EntityName ,
                    CASE WHEN t.BillType ='10' THEN PayPerformance  END SalePayPerformance,
                    CASE WHEN t.BillType ='10' THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                    CASE WHEN t.BillType ='10' THEN SavingCardPerformance  END SaleSavingCardPerformance,
                    CASE WHEN t.BillType ='10' THEN SavingCardRateCommission+SavingCardFixedCommission  END SaleSavingCardCommission,
                    CASE WHEN t.BillType ='10' THEN SavingCardLargessPerformance  END SaleSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='10' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END SaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='10' THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                    CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                    CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                    CASE WHEN t.BillType ='30' THEN SavingCardPerformance  END ArrearSavingCardPerformance,
                    CASE WHEN t.BillType ='30' THEN SavingCardRateCommission+SavingCardFixedCommission  END ArrearSavingCardCommission,
                    CASE WHEN t.BillType ='30' THEN SavingCardLargessPerformance  END ArrearSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='30' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END ArrearSavingCardLargessCommission,
                    CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                    CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                    CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                    CASE WHEN t.BillType ='20'THEN SavingCardPerformance  END RefundSaleSavingCardPerformance,
                    CASE WHEN t.BillType ='20'THEN SavingCardRateCommission+SavingCardFixedCommission  END RefundSaleSavingCardCommission,
                    CASE WHEN t.BillType ='20' THEN SavingCardLargessPerformance  END RefundSaleSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='20'THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END RefundSaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionProject   tsp WITH(NOLOCK)
                    LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                    LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                    LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                    LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                    LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
               <where>
            t.BillStatus='20'  AND  EXISTS (
            SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
            WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
            )
                   <if test="EntityID != null ">
                       AND   ty.ID = #{EntityID}
                   </if>
            <if test="StartDate != null and EndDate != null ">
                AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND te.Name like concat('%',#{EmployeeName},'%')
            </if>

            <if test="JobID != null and JobID != ''">
                AND te.JobID =#{JobID}
            </if>
        </where>

                UNION ALL
                SELECT
                    tsp.EmployeeID,
                    te.Name EmployeeName,
                    tjt.JobName ,
                    ty.EntityName ,
                    CASE WHEN t.BillType ='10' THEN PayPerformance  END SalePayPerformance,
                    CASE WHEN t.BillType ='10' THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                    CASE WHEN t.BillType ='10' THEN SavingCardPerformance  END SaleSavingCardPerformance,
                    CASE WHEN t.BillType ='10' THEN SavingCardRateCommission+SavingCardFixedCommission  END SaleSavingCardCommission,
                    CASE WHEN t.BillType ='10' THEN SavingCardLargessPerformance  END SaleSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='10' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END SaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='10' THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                    CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                    CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                    CASE WHEN t.BillType ='30' THEN SavingCardPerformance  END ArrearSavingCardPerformance,
                    CASE WHEN t.BillType ='30' THEN SavingCardRateCommission+SavingCardFixedCommission  END ArrearSavingCardCommission,
                    CASE WHEN t.BillType ='30' THEN SavingCardLargessPerformance  END ArrearSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='30' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END ArrearSavingCardLargessCommission,
                    CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                    CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                    CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                    CASE WHEN t.BillType ='20'THEN SavingCardPerformance  END RefundSaleSavingCardPerformance,
                    CASE WHEN t.BillType ='20'THEN SavingCardRateCommission+SavingCardFixedCommission  END RefundSaleSavingCardCommission,
                    CASE WHEN t.BillType ='20' THEN SavingCardLargessPerformance  END RefundSaleSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='20'THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END RefundSaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionGeneralCard   tsp WITH(NOLOCK)
                    LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                    LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                    LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                    LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                    LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                <where>
            t.BillStatus='20'  AND  EXISTS (
            SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
            WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
            )
                    <if test="EntityID != null ">
                        AND   ty.ID = #{EntityID}
                    </if>
            <if test="StartDate != null and EndDate != null ">
                AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND te.Name like concat('%',#{EmployeeName},'%')
            </if>

            <if test="JobID != null and JobID != ''">
                AND te.JobID =#{JobID}
            </if>
        </where>
                UNION ALL
                SELECT
                    tsp.EmployeeID,
                    te.Name EmployeeName,
                    tjt.JobName ,
                    ty.EntityName ,
                    CASE WHEN t.BillType ='10' THEN PayPerformance  END SalePayPerformance,
                    CASE WHEN t.BillType ='10' THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                    CASE WHEN t.BillType ='10' THEN SavingCardPerformance  END SaleSavingCardPerformance,
                    CASE WHEN t.BillType ='10' THEN SavingCardRateCommission+SavingCardFixedCommission  END SaleSavingCardCommission,
                    CASE WHEN t.BillType ='10' THEN SavingCardLargessPerformance  END SaleSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='10' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END SaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='10' THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                    CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                    CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                    CASE WHEN t.BillType ='30' THEN SavingCardPerformance  END ArrearSavingCardPerformance,
                    CASE WHEN t.BillType ='30' THEN SavingCardRateCommission+SavingCardFixedCommission  END ArrearSavingCardCommission,
                    CASE WHEN t.BillType ='30' THEN SavingCardLargessPerformance  END ArrearSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='30' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END ArrearSavingCardLargessCommission,
                    CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                    CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                    CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                    CASE WHEN t.BillType ='20'THEN SavingCardPerformance  END RefundSaleSavingCardPerformance,
                    CASE WHEN t.BillType ='20'THEN SavingCardRateCommission+SavingCardFixedCommission  END RefundSaleSavingCardCommission,
                    CASE WHEN t.BillType ='20' THEN SavingCardLargessPerformance  END RefundSaleSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='20'THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END RefundSaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionTimeCard   tsp WITH(NOLOCK)
                    LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                    LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                    LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                    LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                    LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                <where>
            t.BillStatus='20'  AND  EXISTS (
            SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
            WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
            )
                    <if test="EntityID != null ">
                        AND   ty.ID = #{EntityID}
                    </if>
            <if test="StartDate != null and EndDate != null ">
                AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND te.Name like concat('%',#{EmployeeName},'%')
            </if>

            <if test="JobID != null and JobID != ''">
                AND te.JobID =#{JobID}
            </if>
        </where>

                UNION ALL
                SELECT
                    tsp.EmployeeID,
                    te.Name EmployeeName,
                    tjt.JobName ,
                    ty.EntityName ,
                    CASE WHEN t.BillType in ('10','40') THEN PayPerformance  END SalePayPerformance,
                    CASE WHEN t.BillType in ('10','40') THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                    0 SaleSavingCardPerformance,
                    0 SaleSavingCardCommission,
                    0 SaleSavingCardLargessPerformance,
                    0 SaleSavingCardLargessCommission,
                    CASE WHEN t.BillType in ('10','40') THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                    CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                    CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                    0 ArrearSavingCardPerformance,
                    0 ArrearSavingCardCommission,
                    0 ArrearSavingCardLargessPerformance,
                    0 ArrearSavingCardLargessCommission,
                    CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                    CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                    CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                    0 RefundSaleSavingCardPerformance,
                    0RefundSaleSavingCardCommission,
                    0RefundSaleSavingCardLargessPerformance,
                    0 RefundSaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionSavingCard   tsp WITH(NOLOCK)
                    LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                    LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                    LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                    LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                    LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                 <where>
            t.BillStatus='20'  AND  EXISTS (
            SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
            WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
            )
                     <if test="EntityID != null ">
                         AND   ty.ID = #{EntityID}
                     </if>
            <if test="StartDate != null and EndDate != null ">
                AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND te.Name like concat('%',#{EmployeeName},'%')
            </if>

            <if test="JobID != null and JobID != ''">
                AND te.JobID =#{JobID}
            </if>
        </where>
                UNION ALL
                SELECT
                tsp.EmployeeID,
                te.Name EmployeeName,
                tjt.JobName ,
                ty.EntityName,
                CASE WHEN t.BillType ='10' THEN PayPerformance  END SalePayPerformance,
                CASE WHEN t.BillType ='10' THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                CASE WHEN t.BillType ='10' THEN SavingCardPerformance  END SaleSavingCardPerformance,
                CASE WHEN t.BillType ='10' THEN SavingCardRateCommission+SavingCardFixedCommission  END SaleSavingCardCommission,
                CASE WHEN t.BillType ='10' THEN SavingCardLargessPerformance  END SaleSavingCardLargessPerformance,
                CASE WHEN t.BillType ='10' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END SaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='10' THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                CASE WHEN t.BillType ='30' THEN SavingCardPerformance  END ArrearSavingCardPerformance,
                CASE WHEN t.BillType ='30' THEN SavingCardRateCommission+SavingCardFixedCommission  END ArrearSavingCardCommission,
                CASE WHEN t.BillType ='30' THEN SavingCardLargessPerformance  END ArrearSavingCardLargessPerformance,
                CASE WHEN t.BillType ='30' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END ArrearSavingCardLargessCommission,
                CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                CASE WHEN t.BillType ='20'THEN SavingCardPerformance  END RefundSaleSavingCardPerformance,
                CASE WHEN t.BillType ='20'THEN SavingCardRateCommission+SavingCardFixedCommission  END RefundSaleSavingCardCommission,
                CASE WHEN t.BillType ='20' THEN SavingCardLargessPerformance  END RefundSaleSavingCardLargessPerformance,
                CASE WHEN t.BillType ='20'THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END RefundSaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionPackageCardProduct tsp WITH(NOLOCK)
                LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                <where>
                    t.BillStatus='20'  AND  EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
                    )
                    <if test="EntityID != null ">
                        AND   ty.ID = #{EntityID}
                    </if>
                    <if test="StartDate != null and EndDate != null ">
                        AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
                    </if>
                    <if test="EmployeeName != null and EmployeeName != ''">
                        AND te.Name like concat('%',#{EmployeeName},'%')
                    </if>

                    <if test="JobID != null and JobID != ''">
                        AND te.JobID =#{JobID}
                    </if>
                </where>
                UNION ALL
                SELECT
                tsp.EmployeeID,
                te.Name EmployeeName,
                tjt.JobName ,
                ty.EntityName ,
                CASE WHEN t.BillType ='10' THEN PayPerformance  END SalePayPerformance,
                CASE WHEN t.BillType ='10' THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                CASE WHEN t.BillType ='10' THEN SavingCardPerformance  END SaleSavingCardPerformance,
                CASE WHEN t.BillType ='10' THEN SavingCardRateCommission+SavingCardFixedCommission  END SaleSavingCardCommission,
                CASE WHEN t.BillType ='10' THEN SavingCardLargessPerformance  END SaleSavingCardLargessPerformance,
                CASE WHEN t.BillType ='10' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END SaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='10' THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                CASE WHEN t.BillType ='30' THEN SavingCardPerformance  END ArrearSavingCardPerformance,
                CASE WHEN t.BillType ='30' THEN SavingCardRateCommission+SavingCardFixedCommission  END ArrearSavingCardCommission,
                CASE WHEN t.BillType ='30' THEN SavingCardLargessPerformance  END ArrearSavingCardLargessPerformance,
                CASE WHEN t.BillType ='30' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END ArrearSavingCardLargessCommission,
                CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                CASE WHEN t.BillType ='20'THEN SavingCardPerformance  END RefundSaleSavingCardPerformance,
                CASE WHEN t.BillType ='20'THEN SavingCardRateCommission+SavingCardFixedCommission  END RefundSaleSavingCardCommission,
                CASE WHEN t.BillType ='20' THEN SavingCardLargessPerformance  END RefundSaleSavingCardLargessPerformance,
                CASE WHEN t.BillType ='20'THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END RefundSaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionPackageCardProject   tsp WITH(NOLOCK)
                LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                <where>
                    t.BillStatus='20'  AND  EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
                    )
                    <if test="EntityID != null ">
                        AND   ty.ID = #{EntityID}
                    </if>
                    <if test="StartDate != null and EndDate != null ">
                        AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
                    </if>
                    <if test="EmployeeName != null and EmployeeName != ''">
                        AND te.Name like concat('%',#{EmployeeName},'%')
                    </if>

                    <if test="JobID != null and JobID != ''">
                        AND te.JobID =#{JobID}
                    </if>
                </where>

                UNION ALL
                SELECT
                tsp.EmployeeID,
                te.Name EmployeeName,
                tjt.JobName ,
                ty.EntityName ,
                CASE WHEN t.BillType ='10' THEN PayPerformance  END SalePayPerformance,
                CASE WHEN t.BillType ='10' THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                CASE WHEN t.BillType ='10' THEN SavingCardPerformance  END SaleSavingCardPerformance,
                CASE WHEN t.BillType ='10' THEN SavingCardRateCommission+SavingCardFixedCommission  END SaleSavingCardCommission,
                CASE WHEN t.BillType ='10' THEN SavingCardLargessPerformance  END SaleSavingCardLargessPerformance,
                CASE WHEN t.BillType ='10' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END SaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='10' THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                CASE WHEN t.BillType ='30' THEN SavingCardPerformance  END ArrearSavingCardPerformance,
                CASE WHEN t.BillType ='30' THEN SavingCardRateCommission+SavingCardFixedCommission  END ArrearSavingCardCommission,
                CASE WHEN t.BillType ='30' THEN SavingCardLargessPerformance  END ArrearSavingCardLargessPerformance,
                CASE WHEN t.BillType ='30' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END ArrearSavingCardLargessCommission,
                CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                CASE WHEN t.BillType ='20'THEN SavingCardPerformance  END RefundSaleSavingCardPerformance,
                CASE WHEN t.BillType ='20'THEN SavingCardRateCommission+SavingCardFixedCommission  END RefundSaleSavingCardCommission,
                CASE WHEN t.BillType ='20' THEN SavingCardLargessPerformance  END RefundSaleSavingCardLargessPerformance,
                CASE WHEN t.BillType ='20'THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END RefundSaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionPackageCardGeneralCard   tsp WITH(NOLOCK)
                LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                <where>
                    t.BillStatus='20'  AND  EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
                    )
                    <if test="EntityID != null ">
                        AND   ty.ID = #{EntityID}
                    </if>
                    <if test="StartDate != null and EndDate != null ">
                        AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
                    </if>
                    <if test="EmployeeName != null and EmployeeName != ''">
                        AND te.Name like concat('%',#{EmployeeName},'%')
                    </if>

                    <if test="JobID != null and JobID != ''">
                        AND te.JobID =#{JobID}
                    </if>
                </where>
                UNION ALL
                SELECT
                tsp.EmployeeID,
                te.Name EmployeeName,
                tjt.JobName ,
                ty.EntityName ,
                CASE WHEN t.BillType ='10' THEN PayPerformance  END SalePayPerformance,
                CASE WHEN t.BillType ='10' THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                CASE WHEN t.BillType ='10' THEN SavingCardPerformance  END SaleSavingCardPerformance,
                CASE WHEN t.BillType ='10' THEN SavingCardRateCommission+SavingCardFixedCommission  END SaleSavingCardCommission,
                CASE WHEN t.BillType ='10' THEN SavingCardLargessPerformance  END SaleSavingCardLargessPerformance,
                CASE WHEN t.BillType ='10' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END SaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='10' THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                CASE WHEN t.BillType ='30' THEN SavingCardPerformance  END ArrearSavingCardPerformance,
                CASE WHEN t.BillType ='30' THEN SavingCardRateCommission+SavingCardFixedCommission  END ArrearSavingCardCommission,
                CASE WHEN t.BillType ='30' THEN SavingCardLargessPerformance  END ArrearSavingCardLargessPerformance,
                CASE WHEN t.BillType ='30' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END ArrearSavingCardLargessCommission,
                CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                CASE WHEN t.BillType ='20'THEN SavingCardPerformance  END RefundSaleSavingCardPerformance,
                CASE WHEN t.BillType ='20'THEN SavingCardRateCommission+SavingCardFixedCommission  END RefundSaleSavingCardCommission,
                CASE WHEN t.BillType ='20' THEN SavingCardLargessPerformance  END RefundSaleSavingCardLargessPerformance,
                CASE WHEN t.BillType ='20'THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END RefundSaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionPackageCardTimeCard   tsp WITH(NOLOCK)
                LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                <where>
                    t.BillStatus='20'  AND  EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
                    )
                    <if test="EntityID != null ">
                        AND   ty.ID = #{EntityID}
                    </if>
                    <if test="StartDate != null and EndDate != null ">
                        AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
                    </if>
                    <if test="EmployeeName != null and EmployeeName != ''">
                        AND te.Name like concat('%',#{EmployeeName},'%')
                    </if>

                    <if test="JobID != null and JobID != ''">
                        AND te.JobID =#{JobID}
                    </if>
                </where>

                UNION ALL
                SELECT
                tsp.EmployeeID,
                te.Name EmployeeName,
                tjt.JobName ,
                ty.EntityName ,
                CASE WHEN t.BillType in ('10','40') THEN PayPerformance  END SalePayPerformance,
                CASE WHEN t.BillType in ('10','40') THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                0 SaleSavingCardPerformance,
                0 SaleSavingCardCommission,
                0 SaleSavingCardLargessPerformance,
                0 SaleSavingCardLargessCommission,
                CASE WHEN t.BillType in ('10','40') THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                0 ArrearSavingCardPerformance,
                0 ArrearSavingCardCommission,
                0 ArrearSavingCardLargessPerformance,
                0 ArrearSavingCardLargessCommission,
                CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                0 RefundSaleSavingCardPerformance,
                0RefundSaleSavingCardCommission,
                0RefundSaleSavingCardLargessPerformance,
                0 RefundSaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionPackageCardSavingCard   tsp WITH(NOLOCK)
                LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                <where>
                    t.BillStatus='20'  AND  EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
                    )
                    <if test="EntityID != null ">
                        AND   ty.ID = #{EntityID}
                    </if>
                    <if test="StartDate != null and EndDate != null ">
                        AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
                    </if>
                    <if test="EmployeeName != null and EmployeeName != ''">
                        AND te.Name like concat('%',#{EmployeeName},'%')
                    </if>

                    <if test="JobID != null and JobID != ''">
                        AND te.JobID =#{JobID}
                    </if>
                </where>
            )T
        GROUP BY
        T.EmployeeID,
        T.EmployeeName,
        T.JobName ,
        T.EntityName
        ORDER BY T.EmployeeName
    </select>

    <select id="getExcelEmployeeSalePerformanceCommissionStatistics"
            resultType="com.zhelian.model.report.form.output.EmployeeSalePerformanceCommissionDetailStatisticsStatementExcelForm">
        SELECT
            T.EmployeeID employeeID,
            T.EmployeeName employeeName,
            T.JobName jobName,
            T.EntityName entityName,
            ISNULL(SUM(SalePayPerformance),0) salePayPerformance,
            ISNULL(SUM(SalePayCommission),0) salePayCommission,
            ISNULL(SUM(SaleSavingCardPerformance),0) saleSavingCardPerformance,
            ISNULL(SUM(SaleSavingCardCommission),0) saleSavingCardCommission,
            ISNULL(SUM(SaleSavingCardLargessPerformance),0) saleSavingCardLargessPerformance,
            ISNULL(SUM(SaleSavingCardLargessCommission),0) saleSavingCardLargessCommission,
            ISNULL(SUM(SaleSpecialBenefitCommission),0) saleSpecialBenefitCommission,
            ISNULL(SUM(ArrearPayPerformance),0) arrearPayPerformance,
            ISNULL(SUM(ArrearPayCommission),0)arrearPayCommission,
            ISNULL(SUM(ArrearSavingCardPerformance),0) arrearSavingCardPerformance,
            ISNULL(SUM(ArrearSavingCardCommission),0) arrearSavingCardCommission,
            ISNULL(SUM(ArrearSavingCardLargessPerformance),0) arrearSavingCardLargessPerformance,
            ISNULL(SUM(ArrearSavingCardLargessCommission),0) arrearSavingCardLargessCommission,
            ISNULL(SUM(ArrearSpecialBenefitCommission),0) arrearSpecialBenefitCommission,

            ISNULL(SUM(RefundSalePayPerformance),0) refundSalePayPerformance,
            ISNULL(SUM(RefundSalePayCommission),0) refundSalePayCommission,
            ISNULL(SUM(RefundSaleSavingCardPerformance),0) refundSaleSavingCardPerformance,
            ISNULL(SUM(RefundSaleSavingCardCommission),0) refundSaleSavingCardCommission,
            ISNULL(SUM(RefundSaleSavingCardLargessPerformance),0) refundSaleSavingCardLargessPerformance,
            ISNULL(SUM(RefundSaleSavingCardLargessCommission),0) refundSaleSavingCardLargessCommission,
            ISNULL(SUM(RefundSaleSpecialBenefitCommission),0) refundSaleSpecialBenefitCommission,
            ISNULL(SUM(SalePayPerformance),0)+ ISNULL(SUM(ArrearPayPerformance),0)-ISNULL(SUM(RefundSalePayPerformance),0) totalPayPerformance,
            ISNULL(SUM(SalePayCommission), 0)+ ISNULL(SUM(ArrearPayCommission),0)-ISNULL(SUM(RefundSalePayCommission),0) totalPayCommission,
            ISNULL(SUM(SaleSavingCardPerformance),0)+ISNULL(SUM(ArrearSavingCardPerformance),0)-ISNULL(SUM(RefundSaleSavingCardPerformance),0) totalSavingCardPerformance,
            ISNULL(SUM(SaleSavingCardCommission),0)+ISNULL(SUM(ArrearSavingCardCommission),0)-ISNULL(SUM(RefundSaleSavingCardCommission),0) totalSavingCardCommission,
            ISNULL(SUM(SaleSavingCardLargessPerformance),0)+ISNULL(SUM(ArrearSavingCardLargessPerformance),0)-ISNULL(SUM(RefundSaleSavingCardLargessPerformance),0) totalSavingCardLargessPerformance,
            ISNULL(SUM(SaleSavingCardLargessCommission),0)+ISNULL(SUM(ArrearSavingCardLargessCommission),0)-ISNULL(SUM(RefundSaleSavingCardLargessCommission),0) totalSaleSavingCardLargessCommission,
            ISNULL(SUM(SaleSpecialBenefitCommission),0)+ ISNULL(SUM(ArrearSpecialBenefitCommission),0)-ISNULL(SUM(RefundSaleSpecialBenefitCommission),0) totalSpecialBenefitCommission
            FROM
            (
                SELECT
                    tsp.EmployeeID,
                    te.Name EmployeeName,
                    tjt.JobName ,
                    ty.EntityName,
                    CASE WHEN t.BillType ='10' THEN PayPerformance  END SalePayPerformance,
                    CASE WHEN t.BillType ='10' THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                    CASE WHEN t.BillType ='10' THEN SavingCardPerformance  END SaleSavingCardPerformance,
                    CASE WHEN t.BillType ='10' THEN SavingCardRateCommission+SavingCardFixedCommission  END SaleSavingCardCommission,
                    CASE WHEN t.BillType ='10' THEN SavingCardLargessPerformance  END SaleSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='10' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END SaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='10' THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                    CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                    CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                    CASE WHEN t.BillType ='30' THEN SavingCardPerformance  END ArrearSavingCardPerformance,
                    CASE WHEN t.BillType ='30' THEN SavingCardRateCommission+SavingCardFixedCommission  END ArrearSavingCardCommission,
                    CASE WHEN t.BillType ='30' THEN SavingCardLargessPerformance  END ArrearSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='30' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END ArrearSavingCardLargessCommission,
                    CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                    CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                    CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                    CASE WHEN t.BillType ='20'THEN SavingCardPerformance  END RefundSaleSavingCardPerformance,
                    CASE WHEN t.BillType ='20'THEN SavingCardRateCommission+SavingCardFixedCommission  END RefundSaleSavingCardCommission,
                    CASE WHEN t.BillType ='20' THEN SavingCardLargessPerformance  END RefundSaleSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='20'THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END RefundSaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionProduct tsp WITH(NOLOCK)
                LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                    LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                    LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                    LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                    LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                <where>
            t.BillStatus='20'  AND  EXISTS (
            SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
            WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
            )
                    <if test="EntityID != null ">
                        AND   ty.ID = #{EntityID}
                    </if>
            <if test="StartDate != null and EndDate != null ">
                AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND te.Name like concat('%',#{EmployeeName},'%')
            </if>

            <if test="JobID != null and JobID != ''">
                AND te.JobID =#{JobID}
            </if>
        </where>
                UNION ALL
                SELECT
                    tsp.EmployeeID,
                    te.Name EmployeeName,
                    tjt.JobName ,
                    ty.EntityName ,
                    CASE WHEN t.BillType ='10' THEN PayPerformance  END SalePayPerformance,
                    CASE WHEN t.BillType ='10' THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                    CASE WHEN t.BillType ='10' THEN SavingCardPerformance  END SaleSavingCardPerformance,
                    CASE WHEN t.BillType ='10' THEN SavingCardRateCommission+SavingCardFixedCommission  END SaleSavingCardCommission,
                    CASE WHEN t.BillType ='10' THEN SavingCardLargessPerformance  END SaleSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='10' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END SaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='10' THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                    CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                    CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                    CASE WHEN t.BillType ='30' THEN SavingCardPerformance  END ArrearSavingCardPerformance,
                    CASE WHEN t.BillType ='30' THEN SavingCardRateCommission+SavingCardFixedCommission  END ArrearSavingCardCommission,
                    CASE WHEN t.BillType ='30' THEN SavingCardLargessPerformance  END ArrearSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='30' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END ArrearSavingCardLargessCommission,
                    CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                    CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                    CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                    CASE WHEN t.BillType ='20'THEN SavingCardPerformance  END RefundSaleSavingCardPerformance,
                    CASE WHEN t.BillType ='20'THEN SavingCardRateCommission+SavingCardFixedCommission  END RefundSaleSavingCardCommission,
                    CASE WHEN t.BillType ='20' THEN SavingCardLargessPerformance  END RefundSaleSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='20'THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END RefundSaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionProject   tsp WITH(NOLOCK)
                    LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                    LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                    LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                    LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                    LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
               <where>
            t.BillStatus='20'  AND  EXISTS (
            SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
            WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
            )
                   <if test="EntityID != null ">
                       AND   ty.ID = #{EntityID}
                   </if>
            <if test="StartDate != null and EndDate != null ">
                AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND te.Name like concat('%',#{EmployeeName},'%')
            </if>

            <if test="JobID != null and JobID != ''">
                AND te.JobID =#{JobID}
            </if>
        </where>

                UNION ALL
                SELECT
                    tsp.EmployeeID,
                    te.Name EmployeeName,
                    tjt.JobName ,
                    ty.EntityName ,
                    CASE WHEN t.BillType ='10' THEN PayPerformance  END SalePayPerformance,
                    CASE WHEN t.BillType ='10' THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                    CASE WHEN t.BillType ='10' THEN SavingCardPerformance  END SaleSavingCardPerformance,
                    CASE WHEN t.BillType ='10' THEN SavingCardRateCommission+SavingCardFixedCommission  END SaleSavingCardCommission,
                    CASE WHEN t.BillType ='10' THEN SavingCardLargessPerformance  END SaleSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='10' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END SaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='10' THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                    CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                    CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                    CASE WHEN t.BillType ='30' THEN SavingCardPerformance  END ArrearSavingCardPerformance,
                    CASE WHEN t.BillType ='30' THEN SavingCardRateCommission+SavingCardFixedCommission  END ArrearSavingCardCommission,
                    CASE WHEN t.BillType ='30' THEN SavingCardLargessPerformance  END ArrearSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='30' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END ArrearSavingCardLargessCommission,
                    CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                    CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                    CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                    CASE WHEN t.BillType ='20'THEN SavingCardPerformance  END RefundSaleSavingCardPerformance,
                    CASE WHEN t.BillType ='20'THEN SavingCardRateCommission+SavingCardFixedCommission  END RefundSaleSavingCardCommission,
                    CASE WHEN t.BillType ='20' THEN SavingCardLargessPerformance  END RefundSaleSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='20'THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END RefundSaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionGeneralCard   tsp WITH(NOLOCK)
                    LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                    LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                    LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                    LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                    LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                <where>
            t.BillStatus='20'  AND  EXISTS (
            SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
            WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
            )
                    <if test="EntityID != null ">
                        AND   ty.ID = #{EntityID}
                    </if>
            <if test="StartDate != null and EndDate != null ">
                AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND te.Name like concat('%',#{EmployeeName},'%')
            </if>

            <if test="JobID != null and JobID != ''">
                AND te.JobID =#{JobID}
            </if>
        </where>
                UNION ALL
                SELECT
                    tsp.EmployeeID,
                    te.Name EmployeeName,
                    tjt.JobName ,
                    ty.EntityName ,
                    CASE WHEN t.BillType ='10' THEN PayPerformance  END SalePayPerformance,
                    CASE WHEN t.BillType ='10' THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                    CASE WHEN t.BillType ='10' THEN SavingCardPerformance  END SaleSavingCardPerformance,
                    CASE WHEN t.BillType ='10' THEN SavingCardRateCommission+SavingCardFixedCommission  END SaleSavingCardCommission,
                    CASE WHEN t.BillType ='10' THEN SavingCardLargessPerformance  END SaleSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='10' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END SaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='10' THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                    CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                    CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                    CASE WHEN t.BillType ='30' THEN SavingCardPerformance  END ArrearSavingCardPerformance,
                    CASE WHEN t.BillType ='30' THEN SavingCardRateCommission+SavingCardFixedCommission  END ArrearSavingCardCommission,
                    CASE WHEN t.BillType ='30' THEN SavingCardLargessPerformance  END ArrearSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='30' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END ArrearSavingCardLargessCommission,
                    CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                    CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                    CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                    CASE WHEN t.BillType ='20'THEN SavingCardPerformance  END RefundSaleSavingCardPerformance,
                    CASE WHEN t.BillType ='20'THEN SavingCardRateCommission+SavingCardFixedCommission  END RefundSaleSavingCardCommission,
                    CASE WHEN t.BillType ='20' THEN SavingCardLargessPerformance  END RefundSaleSavingCardLargessPerformance,
                    CASE WHEN t.BillType ='20'THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END RefundSaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionTimeCard   tsp WITH(NOLOCK)
                    LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                    LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                    LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                    LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                    LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                <where>
            t.BillStatus='20'  AND  EXISTS (
            SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
            WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
            )
                    <if test="EntityID != null ">
                        AND   ty.ID = #{EntityID}
                    </if>
            <if test="StartDate != null and EndDate != null ">
                AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND te.Name like concat('%',#{EmployeeName},'%')
            </if>

            <if test="JobID != null and JobID != ''">
                AND te.JobID =#{JobID}
            </if>
        </where>

                UNION ALL
                SELECT
                    tsp.EmployeeID,
                    te.Name EmployeeName,
                    tjt.JobName ,
                    ty.EntityName ,
                    CASE WHEN t.BillType in ('10','40') THEN PayPerformance  END SalePayPerformance,
                    CASE WHEN t.BillType in ('10','40') THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                    0 SaleSavingCardPerformance,
                    0 SaleSavingCardCommission,
                    0 SaleSavingCardLargessPerformance,
                    0 SaleSavingCardLargessCommission,
                    CASE WHEN t.BillType in ('10','40') THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                    CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                    CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                    0 ArrearSavingCardPerformance,
                    0 ArrearSavingCardCommission,
                    0 ArrearSavingCardLargessPerformance,
                    0 ArrearSavingCardLargessCommission,
                    CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                    CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                    CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                    0 RefundSaleSavingCardPerformance,
                    0RefundSaleSavingCardCommission,
                    0RefundSaleSavingCardLargessPerformance,
                    0 RefundSaleSavingCardLargessCommission,
                    CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionSavingCard   tsp WITH(NOLOCK)
                    LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                    LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                    LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                    LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                    LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                 <where>
            t.BillStatus='20'  AND  EXISTS (
            SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
            WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
            )
                     <if test="EntityID != null ">
                         AND   ty.ID = #{EntityID}
                     </if>
            <if test="StartDate != null and EndDate != null ">
                AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
            </if>
            <if test="EmployeeName != null and EmployeeName != ''">
                AND te.Name like concat('%',#{EmployeeName},'%')
            </if>

            <if test="JobID != null and JobID != ''">
                AND te.JobID =#{JobID}
            </if>
        </where>
                UNION ALL
                SELECT
                tsp.EmployeeID,
                te.Name EmployeeName,
                tjt.JobName ,
                ty.EntityName,
                CASE WHEN t.BillType ='10' THEN PayPerformance  END SalePayPerformance,
                CASE WHEN t.BillType ='10' THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                CASE WHEN t.BillType ='10' THEN SavingCardPerformance  END SaleSavingCardPerformance,
                CASE WHEN t.BillType ='10' THEN SavingCardRateCommission+SavingCardFixedCommission  END SaleSavingCardCommission,
                CASE WHEN t.BillType ='10' THEN SavingCardLargessPerformance  END SaleSavingCardLargessPerformance,
                CASE WHEN t.BillType ='10' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END SaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='10' THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                CASE WHEN t.BillType ='30' THEN SavingCardPerformance  END ArrearSavingCardPerformance,
                CASE WHEN t.BillType ='30' THEN SavingCardRateCommission+SavingCardFixedCommission  END ArrearSavingCardCommission,
                CASE WHEN t.BillType ='30' THEN SavingCardLargessPerformance  END ArrearSavingCardLargessPerformance,
                CASE WHEN t.BillType ='30' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END ArrearSavingCardLargessCommission,
                CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                CASE WHEN t.BillType ='20'THEN SavingCardPerformance  END RefundSaleSavingCardPerformance,
                CASE WHEN t.BillType ='20'THEN SavingCardRateCommission+SavingCardFixedCommission  END RefundSaleSavingCardCommission,
                CASE WHEN t.BillType ='20' THEN SavingCardLargessPerformance  END RefundSaleSavingCardLargessPerformance,
                CASE WHEN t.BillType ='20'THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END RefundSaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionPackageCardProduct tsp WITH(NOLOCK)
                LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                <where>
                    t.BillStatus='20'  AND  EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
                    )
                    <if test="EntityID != null ">
                        AND   ty.ID = #{EntityID}
                    </if>
                    <if test="StartDate != null and EndDate != null ">
                        AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
                    </if>
                    <if test="EmployeeName != null and EmployeeName != ''">
                        AND te.Name like concat('%',#{EmployeeName},'%')
                    </if>

                    <if test="JobID != null and JobID != ''">
                        AND te.JobID =#{JobID}
                    </if>
                </where>
                UNION ALL
                SELECT
                tsp.EmployeeID,
                te.Name EmployeeName,
                tjt.JobName ,
                ty.EntityName ,
                CASE WHEN t.BillType ='10' THEN PayPerformance  END SalePayPerformance,
                CASE WHEN t.BillType ='10' THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                CASE WHEN t.BillType ='10' THEN SavingCardPerformance  END SaleSavingCardPerformance,
                CASE WHEN t.BillType ='10' THEN SavingCardRateCommission+SavingCardFixedCommission  END SaleSavingCardCommission,
                CASE WHEN t.BillType ='10' THEN SavingCardLargessPerformance  END SaleSavingCardLargessPerformance,
                CASE WHEN t.BillType ='10' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END SaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='10' THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                CASE WHEN t.BillType ='30' THEN SavingCardPerformance  END ArrearSavingCardPerformance,
                CASE WHEN t.BillType ='30' THEN SavingCardRateCommission+SavingCardFixedCommission  END ArrearSavingCardCommission,
                CASE WHEN t.BillType ='30' THEN SavingCardLargessPerformance  END ArrearSavingCardLargessPerformance,
                CASE WHEN t.BillType ='30' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END ArrearSavingCardLargessCommission,
                CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                CASE WHEN t.BillType ='20'THEN SavingCardPerformance  END RefundSaleSavingCardPerformance,
                CASE WHEN t.BillType ='20'THEN SavingCardRateCommission+SavingCardFixedCommission  END RefundSaleSavingCardCommission,
                CASE WHEN t.BillType ='20' THEN SavingCardLargessPerformance  END RefundSaleSavingCardLargessPerformance,
                CASE WHEN t.BillType ='20'THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END RefundSaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionPackageCardProject   tsp WITH(NOLOCK)
                LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                <where>
                    t.BillStatus='20'  AND  EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
                    )
                    <if test="EntityID != null ">
                        AND   ty.ID = #{EntityID}
                    </if>
                    <if test="StartDate != null and EndDate != null ">
                        AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
                    </if>
                    <if test="EmployeeName != null and EmployeeName != ''">
                        AND te.Name like concat('%',#{EmployeeName},'%')
                    </if>

                    <if test="JobID != null and JobID != ''">
                        AND te.JobID =#{JobID}
                    </if>
                </where>

                UNION ALL
                SELECT
                tsp.EmployeeID,
                te.Name EmployeeName,
                tjt.JobName ,
                ty.EntityName ,
                CASE WHEN t.BillType ='10' THEN PayPerformance  END SalePayPerformance,
                CASE WHEN t.BillType ='10' THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                CASE WHEN t.BillType ='10' THEN SavingCardPerformance  END SaleSavingCardPerformance,
                CASE WHEN t.BillType ='10' THEN SavingCardRateCommission+SavingCardFixedCommission  END SaleSavingCardCommission,
                CASE WHEN t.BillType ='10' THEN SavingCardLargessPerformance  END SaleSavingCardLargessPerformance,
                CASE WHEN t.BillType ='10' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END SaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='10' THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                CASE WHEN t.BillType ='30' THEN SavingCardPerformance  END ArrearSavingCardPerformance,
                CASE WHEN t.BillType ='30' THEN SavingCardRateCommission+SavingCardFixedCommission  END ArrearSavingCardCommission,
                CASE WHEN t.BillType ='30' THEN SavingCardLargessPerformance  END ArrearSavingCardLargessPerformance,
                CASE WHEN t.BillType ='30' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END ArrearSavingCardLargessCommission,
                CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                CASE WHEN t.BillType ='20'THEN SavingCardPerformance  END RefundSaleSavingCardPerformance,
                CASE WHEN t.BillType ='20'THEN SavingCardRateCommission+SavingCardFixedCommission  END RefundSaleSavingCardCommission,
                CASE WHEN t.BillType ='20' THEN SavingCardLargessPerformance  END RefundSaleSavingCardLargessPerformance,
                CASE WHEN t.BillType ='20'THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END RefundSaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionPackageCardGeneralCard   tsp WITH(NOLOCK)
                LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                <where>
                    t.BillStatus='20'  AND  EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
                    )
                    <if test="EntityID != null ">
                        AND   ty.ID = #{EntityID}
                    </if>
                    <if test="StartDate != null and EndDate != null ">
                        AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
                    </if>
                    <if test="EmployeeName != null and EmployeeName != ''">
                        AND te.Name like concat('%',#{EmployeeName},'%')
                    </if>

                    <if test="JobID != null and JobID != ''">
                        AND te.JobID =#{JobID}
                    </if>
                </where>
                UNION ALL
                SELECT
                tsp.EmployeeID,
                te.Name EmployeeName,
                tjt.JobName ,
                ty.EntityName ,
                CASE WHEN t.BillType ='10' THEN PayPerformance  END SalePayPerformance,
                CASE WHEN t.BillType ='10' THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                CASE WHEN t.BillType ='10' THEN SavingCardPerformance  END SaleSavingCardPerformance,
                CASE WHEN t.BillType ='10' THEN SavingCardRateCommission+SavingCardFixedCommission  END SaleSavingCardCommission,
                CASE WHEN t.BillType ='10' THEN SavingCardLargessPerformance  END SaleSavingCardLargessPerformance,
                CASE WHEN t.BillType ='10' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END SaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='10' THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                CASE WHEN t.BillType ='30' THEN SavingCardPerformance  END ArrearSavingCardPerformance,
                CASE WHEN t.BillType ='30' THEN SavingCardRateCommission+SavingCardFixedCommission  END ArrearSavingCardCommission,
                CASE WHEN t.BillType ='30' THEN SavingCardLargessPerformance  END ArrearSavingCardLargessPerformance,
                CASE WHEN t.BillType ='30' THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END ArrearSavingCardLargessCommission,
                CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                CASE WHEN t.BillType ='20'THEN SavingCardPerformance  END RefundSaleSavingCardPerformance,
                CASE WHEN t.BillType ='20'THEN SavingCardRateCommission+SavingCardFixedCommission  END RefundSaleSavingCardCommission,
                CASE WHEN t.BillType ='20' THEN SavingCardLargessPerformance  END RefundSaleSavingCardLargessPerformance,
                CASE WHEN t.BillType ='20'THEN SavingCardLargessRateCommission+SavingCardLargessFixedCommission  END RefundSaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionPackageCardTimeCard   tsp WITH(NOLOCK)
                LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                <where>
                    t.BillStatus='20'  AND  EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
                    )
                    <if test="EntityID != null ">
                        AND   ty.ID = #{EntityID}
                    </if>
                    <if test="StartDate != null and EndDate != null ">
                        AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
                    </if>
                    <if test="EmployeeName != null and EmployeeName != ''">
                        AND te.Name like concat('%',#{EmployeeName},'%')
                    </if>

                    <if test="JobID != null and JobID != ''">
                        AND te.JobID =#{JobID}
                    </if>
                </where>

                UNION ALL
                SELECT
                tsp.EmployeeID,
                te.Name EmployeeName,
                tjt.JobName ,
                ty.EntityName ,
                CASE WHEN t.BillType in ('10','40') THEN PayPerformance  END SalePayPerformance,
                CASE WHEN t.BillType in ('10','40') THEN PayRateCommission+PayFixedCommission  END SalePayCommission,
                0 SaleSavingCardPerformance,
                0 SaleSavingCardCommission,
                0 SaleSavingCardLargessPerformance,
                0 SaleSavingCardLargessCommission,
                CASE WHEN t.BillType in ('10','40') THEN SpecialBenefitCommission  END SaleSpecialBenefitCommission,

                CASE WHEN t.BillType ='30' THEN PayPerformance  END ArrearPayPerformance,
                CASE WHEN t.BillType ='30' THEN PayRateCommission+PayFixedCommission  END ArrearPayCommission,
                0 ArrearSavingCardPerformance,
                0 ArrearSavingCardCommission,
                0 ArrearSavingCardLargessPerformance,
                0 ArrearSavingCardLargessCommission,
                CASE WHEN t.BillType ='30' THEN SpecialBenefitCommission  END ArrearSpecialBenefitCommission,

                CASE WHEN t.BillType ='20' THEN PayPerformance  END RefundSalePayPerformance,
                CASE WHEN t.BillType ='20' THEN PayRateCommission+PayFixedCommission  END RefundSalePayCommission,
                0 RefundSaleSavingCardPerformance,
                0RefundSaleSavingCardCommission,
                0RefundSaleSavingCardLargessPerformance,
                0 RefundSaleSavingCardLargessCommission,
                CASE WHEN t.BillType ='20'THEN SpecialBenefitCommission  END RefundSaleSpecialBenefitCommission

                FROM TB_SaleEmployeePerformanceCommissionPackageCardSavingCard   tsp WITH(NOLOCK)
                LEFT JOIN TB_SaleBill t ON tsp.SaleBillID=t.ID
                LEFT JOIN TK_Employee te  ON te.ID=tsp.EmployeeID
                LEFT JOIN TK_JobType tjt  ON tjt.ID=te.JobID
                LEFT JOIN TK_EmployeeEntity tee  ON tee.EmployeeID=te.ID AND tee.IsPrimaryEntity = 1
                LEFT JOIN TK_Entity ty   ON ty.ID=tee.EntityID
                <where>
                    t.BillStatus='20'  AND  EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
                    )
                    <if test="EntityID != null ">
                        AND   ty.ID = #{EntityID}
                    </if>
                    <if test="StartDate != null and EndDate != null ">
                        AND tsp.BillDate &gt;= #{StartDate} AND tsp.BillDate &lt; #{EndDate}
                    </if>
                    <if test="EmployeeName != null and EmployeeName != ''">
                        AND te.Name like concat('%',#{EmployeeName},'%')
                    </if>

                    <if test="JobID != null and JobID != ''">
                        AND te.JobID =#{JobID}
                    </if>
                </where>
            )T
        GROUP BY
        T.EmployeeID,
        T.EmployeeName,
        T.JobName ,
        T.EntityName
        ORDER BY T.EmployeeName
    </select>
</mapper>