<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.report.EmployeePassengerFlowMapper">


    <select id="getEmployeePassengerFlowStatement"
            resultType="com.zhelian.model.report.form.output.EmployeePassengerFlowDetailStatementForm">
        SELECT
        t.ID,
        t.Name,
        tt.EntityName,
        tj.JobName,
        CAST(ISNULL(SUM(aa.EmployeePassengerFlow), 0) as decimal(38, 2)) AS EmployeePassengerFlow,
        CAST(ISNULL(SUM(aa.EmployeeSalePassengerFlow), 0) as decimal(38, 2)) AS EmployeeSalePassengerFlow,
        CAST(ISNULL(SUM(aa.EmployeeTreatPassengerFlow), 0) as decimal(38, 2)) AS EmployeeTreatPassengerFlow
        FROM
        (
        SELECT
        EmployeeID,
        EmployeeSalePassengerFlow EmployeePassengerFlow,
        0 EmployeeSalePassengerFlow,
        0 EmployeeTreatPassengerFlow
        FROM dbo.GetEmployeePassengerFlow(#{StartTime},DATEADD(DAY,1,#{EndTime}))


        UNION ALL
        SELECT
        EmployeeID,
        0 EmployeePassengerFlow,
        EmployeeSalePassengerFlow ,
        0 EmployeeTreatPassengerFlow
        FROM dbo.GetEmployeeSalePassengerFlow(#{StartTime},DATEADD(DAY,1,#{EndTime}))

        UNION ALL
        SELECT
        EmployeeID,
        0 EmployeePassengerFlow,
        0 EmployeeSalePassengerFlow,
        EmployeeTreatPassengerFlow
        FROM dbo.GetEmployeeTreatPassengerFlow(#{StartTime},DATEADD(DAY,1,#{EndTime}))

        )aa
        LEFT JOIN TK_Employee t WITH(NOLOCK) ON aa.EmployeeID=t.ID
        LEFT JOIN TK_EmployeeEntity tee WITH(NOLOCK) ON t.ID=tee.EmployeeID
        LEFT JOIN TK_Entity tt WITH(NOLOCK) ON tt.ID=tee.EntityID
        LEFT JOIN TK_JobType tj WITH(NOLOCK) ON t.JobID=tj.ID
        <where>
            tee.IsPrimaryEntity=1
            <if test="Name != null and Name != ''">
                AND ( t.ID LIKE '%'+ #{Name} +'%' OR t.Name LIKE '%'+ #{Name} +'%' )
            </if>
            <if test="Jobs != null and Jobs.size()>0">
                AND t.JobID IN
                <foreach collection="Jobs" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <choose>
                <when test="EntityID!=null">
                    AND tee.EntityID = #{EntityID}
                </when>
                <otherwise>
                    AND EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
                    )
                </otherwise>
            </choose>
        </where>
        GROUP BY
        aa.EmployeeID,
        t.ID,
        t.Name,
        tt.EntityName,
        tj.JobName
        ORDER BY SUM(aa.EmployeePassengerFlow) desc,tt.EntityName,t.Name
    </select>


    <select id="getEmployeePassengerFlowStatementSum"
            resultType="com.zhelian.model.report.form.output.EmployeePassengerFlowSumStatementForm">
        SELECT
        ISNULL(SUM(T.EmployeePassengerFlow),0)EmployeePassengerFlow,
        ISNULL(SUM(T.EmployeeSalePassengerFlow),0)EmployeeSalePassengerFlow,
        ISNULL(SUM(T.EmployeeTreatPassengerFlow),0)EmployeeTreatPassengerFlow
        FROM
        (
        SELECT
        t.ID,
        t.Name,
        tt.EntityName,
        tj.JobName,
        CAST(ISNULL(SUM(aa.EmployeePassengerFlow), 0) as decimal(38, 2)) AS EmployeePassengerFlow,
        CAST(ISNULL(SUM(aa.EmployeeSalePassengerFlow), 0) as decimal(38, 2)) AS EmployeeSalePassengerFlow,
        CAST(ISNULL(SUM(aa.EmployeeTreatPassengerFlow), 0) as decimal(38, 2)) AS EmployeeTreatPassengerFlow
        FROM
        (
        SELECT
        EmployeeID,
        EmployeeSalePassengerFlow EmployeePassengerFlow,
        0 EmployeeSalePassengerFlow,
        0 EmployeeTreatPassengerFlow
        FROM dbo.GetEmployeePassengerFlow(#{StartTime},DATEADD(DAY,1,#{EndTime}))


        UNION ALL
        SELECT
        EmployeeID,
        0 EmployeePassengerFlow,
        EmployeeSalePassengerFlow ,
        0 EmployeeTreatPassengerFlow
        FROM dbo.GetEmployeeSalePassengerFlow(#{StartTime},DATEADD(DAY,1,#{EndTime}))

        UNION ALL
        SELECT
        EmployeeID,
        0 EmployeePassengerFlow,
        0 EmployeeSalePassengerFlow,
        EmployeeTreatPassengerFlow
        FROM dbo.GetEmployeeTreatPassengerFlow(#{StartTime},DATEADD(DAY,1,#{EndTime}))
        )aa
        LEFT JOIN TK_Employee t WITH(NOLOCK) ON aa.EmployeeID=t.ID
        LEFT JOIN TK_EmployeeEntity tee WITH(NOLOCK) ON t.ID=tee.EmployeeID
        LEFT JOIN TK_Entity tt WITH(NOLOCK) ON tt.ID=tee.EntityID
        LEFT JOIN TK_JobType tj WITH(NOLOCK) ON t.JobID=tj.ID
        <where>
            tee.IsPrimaryEntity=1
            <if test="Name != null and Name != ''">
                AND ( t.ID LIKE '%'+ #{Name} +'%' OR t.Name LIKE '%'+ #{Name} +'%' )
            </if>
            <if test="Jobs != null and Jobs.size()>0">
                AND t.JobID IN
                <foreach collection="Jobs" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <choose>
                <when test="EntityID!=null">
                    AND tee.EntityID = #{EntityID}
                </when>
                <otherwise>
                    AND EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
                    )
                </otherwise>
            </choose>
        </where>
        GROUP BY
        aa.EmployeeID,
        t.ID,
        t.Name,
        tt.EntityName,
        tj.JobName
        )T
    </select>

    <select id="getEmployeePassengerFlowStatementExcel"
            resultType="com.zhelian.model.report.form.output.EmployeePassengerFlowDetailStatementExcelForm">
        SELECT
        t.ID,
        t.Name,
        tt.EntityName,
        tj.JobName,
        CAST(ISNULL(SUM(aa.EmployeePassengerFlow), 0) as decimal(38, 2)) AS EmployeePassengerFlow,
        CAST(ISNULL(SUM(aa.EmployeeSalePassengerFlow), 0) as decimal(38, 2)) AS EmployeeSalePassengerFlow,
        CAST(ISNULL(SUM(aa.EmployeeTreatPassengerFlow), 0) as decimal(38, 2)) AS EmployeeTreatPassengerFlow
        FROM
        (
        SELECT
        EmployeeID,
        EmployeeSalePassengerFlow EmployeePassengerFlow,
        0 EmployeeSalePassengerFlow,
        0 EmployeeTreatPassengerFlow
        FROM dbo.GetEmployeePassengerFlow(#{StartTime},DATEADD(DAY,1,#{EndTime}))


        UNION ALL
        SELECT
        EmployeeID,
        0 EmployeePassengerFlow,
        EmployeeSalePassengerFlow ,
        0 EmployeeTreatPassengerFlow
        FROM dbo.GetEmployeeSalePassengerFlow(#{StartTime},DATEADD(DAY,1,#{EndTime}))

        UNION ALL
        SELECT
        EmployeeID,
        0 EmployeePassengerFlow,
        0 EmployeeSalePassengerFlow ,
        EmployeeTreatPassengerFlow
        FROM dbo.GetEmployeeTreatPassengerFlow(#{StartTime},DATEADD(DAY,1,#{EndTime}))
        )aa
        LEFT JOIN TK_Employee t WITH(NOLOCK) ON aa.EmployeeID=t.ID
        LEFT JOIN TK_EmployeeEntity tee WITH(NOLOCK) ON t.ID=tee.EmployeeID
        LEFT JOIN TK_Entity tt WITH(NOLOCK) ON tt.ID=tee.EntityID
        LEFT JOIN TK_JobType tj WITH(NOLOCK) ON t.JobID=tj.ID
        <where>
            tee.IsPrimaryEntity=1
            <if test="Name != null and Name != ''">
                AND ( t.ID LIKE '%'+ #{Name} +'%' OR t.Name LIKE '%'+ #{Name} +'%' )
            </if>
            <if test="Jobs != null and Jobs.size()>0">
                AND t.JobID IN
                <foreach collection="Jobs" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <choose>
                <when test="EntityID!=null">
                    AND tee.EntityID = #{EntityID}
                </when>
                <otherwise>
                    AND EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tee.EntityID
                    )
                </otherwise>
            </choose>
        </where>
        GROUP BY
        t.ID,
        aa.EmployeeID,
        t.Name,
        tt.EntityName,
        tj.JobName
        ORDER BY SUM(aa.EmployeePassengerFlow) desc,tt.EntityName,t.Name
    </select>
</mapper>