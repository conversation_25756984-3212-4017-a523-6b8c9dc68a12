<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.report.ChannelTreatDetailMapper">
    <select id="getChannelTreatDetail" parameterType="String"
            resultType="com.zhelian.model.report.form.output.ChannelTreatDetailStatementForm">
        SELECT
        TreatBillID,BillDate,EntityName,CustomerName,CustomerCode,CustomerPhoneNumber,CustomerLevelName,CustomerSourceName,CustomerEntityName,BuyEntityName,EmployeeName,TreatCardTypeName,CardName,CategoryName,GoodName,Price,Quantity,IsLargess,PreferentialAmount,TotalAmount,TreatPayAmount,TreatCardAmount,TreatCardLargessAmount,TreatLargessAmount,AccountRemark,Channel,IntroducerName,ChannelName
        FROM (
        SELECT
        TBP.TreatBillID,
        TB.BillDate,
        ENT.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        ISNULL(TEY.EntityName,'') AS BuyEntityName,
        EMP.Name AS EmployeeName,
        '项目卡' AS TreatCardTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TBP.Price,
        TBP.Quantity,
        TBP.IsLargess,
        TBP.CardPreferentialAmount*-1 AS PreferentialAmount,
        TBP.TotalAmount,
        TBP.PayAmount AS TreatPayAmount,
        TBP.CardDeductionAmount AS TreatCardAmount,
        TBP.LargessCardDeductionAmount AS TreatCardLargessAmount,
        CASE TBP.IsLargess WHEN 0 THEN 0 ELSE TBP.TotalAmount END AS TreatLargessAmount,
        PA.Remark AccountRemark,
        CASE WHEN TB.Channel ='PC' THEN 'pc'
        WHEN TB.Channel ='Miniprogram' THEN '小程序'
        WHEN TB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tci.Name IntroducerName,
        tcc.Name ChannelName
        FROM TB_TreatBillProject TBP WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBP.TreatBillID = TB.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TBP.ProjectID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON TB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON TB.EmployeeID = Emp.ID
        LEFT JOIN TB_ProjectAccount PA WITH(NOLOCK) ON TBP.ProjectAccountID = PA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = PA.EntityID
        LEFT JOIN TB_Project PAP WITH(NOLOCK) ON PA.ProjectID = PAP.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
                LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='10'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND PA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        TBSC.TreatBillID,
        TB.BillDate,
        ENT.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        ISNULL(TEY.EntityName,'') AS BuyEntityName,
        EMP.Name AS EmployeeName,
        '储值卡' AS TreatCardTypeName,
        SC.Name AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TBSC.Price,
        TBSC.Quantity,
        0 AS IsLargess,
        (TBSC.PricePreferentialAmount + TBSC.MemberPreferentialAmount + TBSC.CardPreferentialAmount)*-1 AS PreferentialAmount,
        TBSC.TotalAmount,
        0 AS TreatPayAmount,
        TBSC.CardTreatAmount AS TreatCardAmount,
        TBSC.LargessCardTreatAmount AS TreatCardLargessAmount,
        0 AS TreatLargessAmount,
        SCA.Remark AccountRemark,
        CASE WHEN TB.Channel ='PC' THEN 'pc'
        WHEN TB.Channel ='Miniprogram' THEN '小程序'
        WHEN TB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tci.Name IntroducerName,
        tcc.Name ChannelName
        FROM TB_TreatBillSavingCard TBSC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBSC.TreatBillID = TB.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TBSC.ProjectID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON TB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON TB.EmployeeID = Emp.ID
        LEFT JOIN TB_SavingCardAccount SCA WITH(NOLOCK) ON TBSC.SavingCardAccountID = SCA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = SCA.EntityID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SCA.SavingCardID = SC.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
                LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='10'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND SCA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        TBTC.TreatBillID,
        TB.BillDate,
        ENT.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        ISNULL(TEY.EntityName,'') AS BuyEntityName,
        EMP.Name AS EmployeeName,
        '时效卡' AS TreatCardTypeName,
        TC.Name AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TBTC.Price,
        TBTC.Quantity,
        TBTC.IsLargess,
        TBTC.CardPreferentialAmount*-1 AS PreferentialAmount,
        TBTC.TotalAmount,
        TBTC.PayAmount AS TreatPayAmount,
        TBTC.CardDeductionAmount AS TreatCardAmount,
        TBTC.LargessCardDeductionAmount AS TreatCardLargessAmount,
        CASE TBTC.IsLargess WHEN 0 THEN 0 ELSE TBTC.TotalAmount END AS TreatLargessAmount,
        TCA.Remark AccountRemark,
        CASE WHEN TB.Channel ='PC' THEN 'pc'
        WHEN TB.Channel ='Miniprogram' THEN '小程序'
        WHEN TB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tci.Name IntroducerName,
        tcc.Name ChannelName
        FROM TB_TreatBillTimeCard TBTC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBTC.TreatBillID = TB.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TBTC.ProjectID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON TB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON TB.EmployeeID = Emp.ID
        LEFT JOIN TB_TimeCardAccount TCA WITH(NOLOCK) ON TBTC.TimeCardAccountID = TCA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = TCA.EntityID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON TCA.TimeCardID = TC.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
                LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='10'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND TCA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        TBGC.TreatBillID,
        TB.BillDate,
        ENT.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        ISNULL(TEY.EntityName,'') AS BuyEntityName,
        EMP.Name AS EmployeeName,
        '通用次卡' AS TreatCardTypeName,
        GC.Name AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TBGC.Price,
        TBGC.Quantity,
        TBGC.IsLargess,
        TBGC.CardPreferentialAmount*-1 AS PreferentialAmount,
        TBGC.TotalAmount,
        TBGC.PayAmount AS TreatPayAmount,
        TBGC.CardDeductionAmount AS TreatCardAmount,
        TBGC.LargessCardDeductionAmount AS TreatCardLargessAmount,
        CASE TBGC.IsLargess WHEN 0 THEN 0 ELSE TBGC.TotalAmount END AS TreatLargessAmount,
        GCA.Remark AccountRemark,
        CASE WHEN TB.Channel ='PC' THEN 'pc'
        WHEN TB.Channel ='Miniprogram' THEN '小程序'
        WHEN TB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tci.Name IntroducerName,
        tcc.Name ChannelName
        FROM TB_TreatBillGeneralCard TBGC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBGC.TreatBillID = TB.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TBGC.ProjectID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON TB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON TB.EmployeeID = Emp.ID
        LEFT JOIN TB_GeneralCardAccount GCA WITH(NOLOCK) ON TBGC.GeneralCardAccountID = GCA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = GCA.EntityID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON GCA.GeneralCardID = GC.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
                LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='10'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND GCA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        TBP.TreatBillID,
        TB.BillDate,
        ENT.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        ISNULL(TEY.EntityName,'') AS BuyEntityName,
        EMP.Name AS EmployeeName,
        '产品卡' AS TreatCardTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TBP.Price,
        TBP.Quantity,
        TBP.IsLargess,
        TBP.CardPreferentialAmount*-1 AS PreferentialAmount,
        TBP.TotalAmount,
        TBP.PayAmount AS TreatPayAmount,
        TBP.CardDeductionAmount AS TreatCardAmount,
        TBP.LargessCardDeductionAmount AS TreatCardLargessAmount,
        CASE TBP.IsLargess WHEN 0 THEN 0 ELSE TBP.TotalAmount END AS TreatLargessAmount,
        PA.Remark AccountRemark,
        CASE WHEN TB.Channel ='PC' THEN 'pc'
        WHEN TB.Channel ='Miniprogram' THEN '小程序'
        WHEN TB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tci.Name IntroducerName,
        tcc.Name ChannelName
        FROM TB_TreatBillProduct TBP WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBP.TreatBillID = TB.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON TBP.ProductID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON TB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON TB.EmployeeID = Emp.ID
        LEFT JOIN TB_ProductAccount PA WITH(NOLOCK) ON TBP.ProductAccountID = PA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = PA.EntityID
        LEFT JOIN TE_Product PAP WITH(NOLOCK) ON PA.ProductID = PAP.ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON PC.ID = P.PCategoryID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
                LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='10'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND PA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        ) T
        <where>
            <if test="TreatCardTypeName != null and TreatCardTypeName != '' ">
                TreatCardTypeName = #{TreatCardTypeName}
            </if>
        </where>
        ORDER BY BillDate DESC
    </select>
    <select id="getChannelTreatDetailSum" parameterType="String"
            resultType="com.zhelian.model.report.form.output.ChannelTreatDetailSumStatementForm">
        SELECT
        SUM(PreferentialAmount) AS PreferentialAmount,SUM(TotalAmount) AS TotalAmount,SUM(TreatPayAmount) AS
        TreatPayAmount,SUM(TreatCardAmount) AS TreatCardAmount,SUM(TreatCardLargessAmount) AS
        TreatCardLargessAmount,SUM(TreatLargessAmount) AS TreatLargessAmount
        FROM (
        SELECT
        '项目卡' AS TreatCardTypeName,
        TBP.CardPreferentialAmount*-1 AS PreferentialAmount,
        TBP.TotalAmount,
        TBP.PayAmount AS TreatPayAmount,
        TBP.CardDeductionAmount AS TreatCardAmount,
        TBP.LargessCardDeductionAmount AS TreatCardLargessAmount,
        CASE TBP.IsLargess WHEN 0 THEN 0 ELSE TBP.TotalAmount END AS TreatLargessAmount

        FROM TB_TreatBillProject TBP WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBP.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TBP.ProjectID = P.ID
        LEFT JOIN TB_ProjectAccount PA WITH(NOLOCK) ON TBP.ProjectAccountID = PA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = PA.EntityID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='10'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND PA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        '储值卡' AS TreatCardTypeName,
        (TBSC.PricePreferentialAmount + TBSC.MemberPreferentialAmount + TBSC.CardPreferentialAmount)*-1 AS PreferentialAmount,
        TBSC.TotalAmount,
        0 AS TreatPayAmount,
        TBSC.CardTreatAmount AS TreatCardAmount,
        TBSC.LargessCardTreatAmount AS TreatCardLargessAmount,
        0 AS TreatLargessAmount
        FROM TB_TreatBillSavingCard TBSC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBSC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TBSC.ProjectID = P.ID
        LEFT JOIN TB_SavingCardAccount SCA WITH(NOLOCK) ON TBSC.SavingCardAccountID = SCA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = SCA.EntityID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='10'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND SCA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        '时效卡' AS TreatCardTypeName,
        TBTC.CardPreferentialAmount*-1 AS PreferentialAmount,
        TBTC.TotalAmount,
        TBTC.PayAmount AS TreatPayAmount,
        TBTC.CardDeductionAmount AS TreatCardAmount,
        TBTC.LargessCardDeductionAmount AS TreatCardLargessAmount,
        CASE TBTC.IsLargess WHEN 0 THEN 0 ELSE TBTC.TotalAmount END AS TreatLargessAmount
        FROM TB_TreatBillTimeCard TBTC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBTC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TBTC.ProjectID = P.ID
        LEFT JOIN TB_TimeCardAccount TCA WITH(NOLOCK) ON TBTC.TimeCardAccountID = TCA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = TCA.EntityID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='10'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND TCA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        '通用次卡' AS TreatCardTypeName,
        TBGC.CardPreferentialAmount*-1 AS PreferentialAmount,
        TBGC.TotalAmount,
        TBGC.PayAmount AS TreatPayAmount,
        TBGC.CardDeductionAmount AS TreatCardAmount,
        TBGC.LargessCardDeductionAmount AS TreatCardLargessAmount,
        CASE TBGC.IsLargess WHEN 0 THEN 0 ELSE TBGC.TotalAmount END AS TreatLargessAmount
        FROM TB_TreatBillGeneralCard TBGC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBGC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TBGC.ProjectID = P.ID
        LEFT JOIN TB_GeneralCardAccount GCA WITH(NOLOCK) ON TBGC.GeneralCardAccountID = GCA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = GCA.EntityID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='10'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND GCA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        '产品卡' AS TreatCardTypeName,
        TBP.CardPreferentialAmount*-1 AS PreferentialAmount,
        TBP.TotalAmount,
        TBP.PayAmount AS TreatPayAmount,
        TBP.CardDeductionAmount AS TreatCardAmount,
        TBP.LargessCardDeductionAmount AS TreatCardLargessAmount,
        CASE TBP.IsLargess WHEN 0 THEN 0 ELSE TBP.TotalAmount END AS TreatLargessAmount
        FROM TB_TreatBillProduct TBP WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBP.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON TBP.ProductID = P.ID
        LEFT JOIN TB_ProductAccount PA WITH(NOLOCK) ON TBP.ProductAccountID = PA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = PA.EntityID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='10'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND PA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        ) T
        <where>
            <if test="TreatCardTypeName != null and TreatCardTypeName != '' ">
                TreatCardTypeName = #{TreatCardTypeName}
            </if>
        </where>
    </select>

    <select id="getEasyExcelChannelTreatDetail" parameterType="String"
            resultType="com.zhelian.model.report.form.output.ChannelTreatDetailStatementEasyExcelForm">
        SELECT
        TreatBillID as treatBillID,CONVERT(varchar(100), BillDate, 120) AS
        billDate,EntityName as entityName,CustomerName as customerName,CustomerCode as customerCode,REPLACE(CustomerPhoneNumber , SUBSTRING (CustomerPhoneNumber,4,4), '****') customerPhoneNumber,
        CustomerLevelName as customerLevelName,CustomerSourceName as customerSourceName,CustomerEntityName as CustomerEntityName,BuyEntityName as buyEntityName,EmployeeName as employeeName,TreatCardTypeName as treatCardTypeName,CardName as cardName,CategoryName as categoryName,GoodName as goodName,Price as price,Quantity as quantity,CASE
        WHEN IsLargess!=0 THEN '是' else '否' end
        isLargess,PreferentialAmount as preferentialAmount,TotalAmount as totalAmount,TreatPayAmount as treatPayAmount,TreatCardAmount as treatCardAmount,TreatCardLargessAmount as treatCardLargessAmount,TreatLargessAmount as treatLargessAmount,AccountRemark as accountRemark,Channel as channel,IntroducerName as introducerName,ChannelName as channelName
        FROM (
        SELECT
        TBP.TreatBillID,
        TB.BillDate,
        ENT.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        ISNULL(TEY.EntityName,'') AS BuyEntityName,
        EMP.Name AS EmployeeName,
        '项目卡' AS TreatCardTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TBP.Price,
        TBP.Quantity,
        TBP.IsLargess,
        TBP.CardPreferentialAmount*-1 AS PreferentialAmount,
        TBP.TotalAmount,
        TBP.PayAmount AS TreatPayAmount,
        TBP.CardDeductionAmount AS TreatCardAmount,
        TBP.LargessCardDeductionAmount AS TreatCardLargessAmount,
        CASE TBP.IsLargess WHEN 0 THEN 0 ELSE TBP.TotalAmount END AS TreatLargessAmount,
        PA.Remark AccountRemark,
        CASE WHEN TB.Channel ='PC' THEN 'pc'
        WHEN TB.Channel ='Miniprogram' THEN '小程序'
        WHEN TB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tci.Name IntroducerName,
        tcc.Name ChannelName
        FROM TB_TreatBillProject TBP WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBP.TreatBillID = TB.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TBP.ProjectID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON TB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON TB.EmployeeID = Emp.ID
        LEFT JOIN TB_ProjectAccount PA WITH(NOLOCK) ON TBP.ProjectAccountID = PA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = PA.EntityID
        LEFT JOIN TB_Project PAP WITH(NOLOCK) ON PA.ProjectID = PAP.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
                LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='10'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND PA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        TBSC.TreatBillID,
        TB.BillDate,
        ENT.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        ISNULL(TEY.EntityName,'') AS BuyEntityName,
        EMP.Name AS EmployeeName,
        '储值卡' AS TreatCardTypeName,
        SC.Name AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TBSC.Price,
        TBSC.Quantity,
        0 AS IsLargess,
        (TBSC.PricePreferentialAmount + TBSC.MemberPreferentialAmount + TBSC.CardPreferentialAmount)*-1 AS PreferentialAmount,
        TBSC.TotalAmount,
        0 AS TreatPayAmount,
        TBSC.CardTreatAmount AS TreatCardAmount,
        TBSC.LargessCardTreatAmount AS TreatCardLargessAmount,
        0 AS TreatLargessAmount,
        SCA.Remark AccountRemark,
        CASE WHEN TB.Channel ='PC' THEN 'pc'
        WHEN TB.Channel ='Miniprogram' THEN '小程序'
        WHEN TB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tci.Name IntroducerName,
        tcc.Name ChannelName
        FROM TB_TreatBillSavingCard TBSC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBSC.TreatBillID = TB.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TBSC.ProjectID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON TB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON TB.EmployeeID = Emp.ID
        LEFT JOIN TB_SavingCardAccount SCA WITH(NOLOCK) ON TBSC.SavingCardAccountID = SCA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = SCA.EntityID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SCA.SavingCardID = SC.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
                LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='10'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND SCA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        TBTC.TreatBillID,
        TB.BillDate,
        ENT.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        ISNULL(TEY.EntityName,'') AS BuyEntityName,
        EMP.Name AS EmployeeName,
        '时效卡' AS TreatCardTypeName,
        TC.Name AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TBTC.Price,
        TBTC.Quantity,
        TBTC.IsLargess,
        TBTC.CardPreferentialAmount*-1 AS PreferentialAmount,
        TBTC.TotalAmount,
        TBTC.PayAmount AS TreatPayAmount,
        TBTC.CardDeductionAmount AS TreatCardAmount,
        TBTC.LargessCardDeductionAmount AS TreatCardLargessAmount,
        CASE TBTC.IsLargess WHEN 0 THEN 0 ELSE TBTC.TotalAmount END AS TreatLargessAmount,
        TCA.Remark AccountRemark,
        CASE WHEN TB.Channel ='PC' THEN 'pc'
        WHEN TB.Channel ='Miniprogram' THEN '小程序'
        WHEN TB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tci.Name IntroducerName,
        tcc.Name ChannelName
        FROM TB_TreatBillTimeCard TBTC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBTC.TreatBillID = TB.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TBTC.ProjectID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON TB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON TB.EmployeeID = Emp.ID
        LEFT JOIN TB_TimeCardAccount TCA WITH(NOLOCK) ON TBTC.TimeCardAccountID = TCA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = TCA.EntityID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON TCA.TimeCardID = TC.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
                LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='10'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND TCA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        TBGC.TreatBillID,
        TB.BillDate,
        ENT.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        ISNULL(TEY.EntityName,'') AS BuyEntityName,
        EMP.Name AS EmployeeName,
        '通用次卡' AS TreatCardTypeName,
        GC.Name AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TBGC.Price,
        TBGC.Quantity,
        TBGC.IsLargess,
        TBGC.CardPreferentialAmount*-1 AS PreferentialAmount,
        TBGC.TotalAmount,
        TBGC.PayAmount AS TreatPayAmount,
        TBGC.CardDeductionAmount AS TreatCardAmount,
        TBGC.LargessCardDeductionAmount AS TreatCardLargessAmount,
        CASE TBGC.IsLargess WHEN 0 THEN 0 ELSE TBGC.TotalAmount END AS TreatLargessAmount,
        GCA.Remark AccountRemark,
        CASE WHEN TB.Channel ='PC' THEN 'pc'
        WHEN TB.Channel ='Miniprogram' THEN '小程序'
        WHEN TB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tci.Name IntroducerName,
        tcc.Name ChannelName
        FROM TB_TreatBillGeneralCard TBGC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBGC.TreatBillID = TB.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TBGC.ProjectID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON TB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON TB.EmployeeID = Emp.ID
        LEFT JOIN TB_GeneralCardAccount GCA WITH(NOLOCK) ON TBGC.GeneralCardAccountID = GCA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = GCA.EntityID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON GCA.GeneralCardID = GC.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
                LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='10'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND GCA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        TBP.TreatBillID,
        TB.BillDate,
        ENT.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        ISNULL(TEY.EntityName,'') AS BuyEntityName,
        EMP.Name AS EmployeeName,
        '产品卡' AS TreatCardTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TBP.Price,
        TBP.Quantity,
        TBP.IsLargess,
        TBP.CardPreferentialAmount*-1 AS PreferentialAmount,
        TBP.TotalAmount,
        TBP.PayAmount AS TreatPayAmount,
        TBP.CardDeductionAmount AS TreatCardAmount,
        TBP.LargessCardDeductionAmount AS TreatCardLargessAmount,
        CASE TBP.IsLargess WHEN 0 THEN 0 ELSE TBP.TotalAmount END AS TreatLargessAmount,
        PA.Remark AccountRemark,
        CASE WHEN TB.Channel ='PC' THEN 'pc'
        WHEN TB.Channel ='Miniprogram' THEN '小程序'
        WHEN TB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tci.Name IntroducerName,
        tcc.Name ChannelName
        FROM TB_TreatBillProduct TBP WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBP.TreatBillID = TB.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON TBP.ProductID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON TB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON TB.EmployeeID = Emp.ID
        LEFT JOIN TB_ProductAccount PA WITH(NOLOCK) ON TBP.ProductAccountID = PA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = PA.EntityID
        LEFT JOIN TE_Product PAP WITH(NOLOCK) ON PA.ProductID = PAP.ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON PC.ID = P.PCategoryID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
                LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='10'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND PA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        ) T
        <where>
            <if test="TreatCardTypeName != null and TreatCardTypeName != '' ">
                TreatCardTypeName = #{TreatCardTypeName}
            </if>
        </where>
        ORDER BY BillDate DESC
    </select>

    <select id="getChannelTreatRefundDetail" parameterType="String"
            resultType="com.zhelian.model.report.form.output.ChannelTreatRefundDetailStatementForm">
        SELECT
        TreatBillID,CONVERT(varchar(100), BillDate, 120) AS
        BillDate,EntityName,CustomerName,CustomerCode,CustomerPhoneNumber,CustomerLevelName,CustomerSourceName,CustomerEntityName,BuyEntityName,EmployeeName,TreatCardTypeName,CardName,CategoryName,GoodName,Quantity,IsLargess,TotalAmount,TreatPayAmount,TreatCardAmount,TreatCardLargessAmount,TreatLargessAmount,AccountRemark,Channel,IntroducerName,ChannelName
        FROM (
        SELECT
        TBP.TreatBillID,
        TB.BillDate,
        ENT.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        ISNULL(TEY.EntityName,'') AS BuyEntityName,
        EMP.Name AS EmployeeName,
        '项目卡' AS TreatCardTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TBP.Quantity,
        TBP.IsLargess,
        TBP.TotalAmount,
        TBP.PayAmount AS TreatPayAmount,
        TBP.CardDeductionAmount AS TreatCardAmount,
        TBP.LargessCardDeductionAmount AS TreatCardLargessAmount,
        CASE TBP.IsLargess WHEN 0 THEN 0 ELSE TBP.TotalAmount END AS TreatLargessAmount,
        PA.Remark AccountRemark,
        CASE WHEN TB.Channel ='PC' THEN 'pc'
        WHEN TB.Channel ='Miniprogram' THEN '小程序'
        WHEN TB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tci.Name IntroducerName,
        tcc.Name ChannelName
        FROM TB_TreatBillProject TBP WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBP.TreatBillID = TB.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TBP.ProjectID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON TB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON TB.EmployeeID = Emp.ID
        LEFT JOIN TB_ProjectAccount PA WITH(NOLOCK) ON TBP.ProjectAccountID = PA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = PA.EntityID
        LEFT JOIN TB_Project PAP WITH(NOLOCK) ON PA.ProjectID = PAP.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
                LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='20'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND PA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        TBSC.TreatBillID,
        TB.BillDate,
        ENT.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        ISNULL(TEY.EntityName,'') AS BuyEntityName,
        EMP.Name AS EmployeeName,
        '储值卡' AS TreatCardTypeName,
        SC.Name AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TBSC.Quantity,
        0 AS IsLargess,
        TBSC.TotalAmount,
        0 AS TreatPayAmount,
        TBSC.CardTreatAmount AS TreatCardAmount,
        TBSC.LargessCardTreatAmount AS TreatCardLargessAmount,
        0 AS TreatLargessAmount,
        SCA.Remark AccountRemark,
        CASE WHEN TB.Channel ='PC' THEN 'pc'
        WHEN TB.Channel ='Miniprogram' THEN '小程序'
        WHEN TB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tci.Name IntroducerName,
        tcc.Name ChannelName
        FROM TB_TreatBillSavingCard TBSC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBSC.TreatBillID = TB.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TBSC.ProjectID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON TB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON TB.EmployeeID = Emp.ID
        LEFT JOIN TB_SavingCardAccount SCA WITH(NOLOCK) ON TBSC.SavingCardAccountID = SCA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = SCA.EntityID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SCA.SavingCardID = SC.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
                LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='20'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND SCA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        TBTC.TreatBillID,
        TB.BillDate,
        ENT.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        ISNULL(TEY.EntityName,'') AS BuyEntityName,
        EMP.Name AS EmployeeName,
        '时效卡' AS TreatCardTypeName,
        TC.Name AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TBTC.Quantity,
        TBTC.IsLargess,
        TBTC.TotalAmount,
        TBTC.PayAmount AS TreatPayAmount,
        TBTC.CardDeductionAmount AS TreatCardAmount,
        TBTC.LargessCardDeductionAmount AS TreatCardLargessAmount,
        CASE TBTC.IsLargess WHEN 0 THEN 0 ELSE TBTC.TotalAmount END AS TreatLargessAmount,
        TCA.Remark AccountRemark,
        CASE WHEN TB.Channel ='PC' THEN 'pc'
        WHEN TB.Channel ='Miniprogram' THEN '小程序'
        WHEN TB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tci.Name IntroducerName,
        tcc.Name ChannelName
        FROM TB_TreatBillTimeCard TBTC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBTC.TreatBillID = TB.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TBTC.ProjectID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON TB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON TB.EmployeeID = Emp.ID
        LEFT JOIN TB_TimeCardAccount TCA WITH(NOLOCK) ON TBTC.TimeCardAccountID = TCA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = TCA.EntityID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON TCA.TimeCardID = TC.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
                LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='20'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND TCA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        TBGC.TreatBillID,
        TB.BillDate,
        ENT.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        ISNULL(TEY.EntityName,'') AS BuyEntityName,
        EMP.Name AS EmployeeName,
        '通用次卡' AS TreatCardTypeName,
        GC.Name AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TBGC.Quantity,
        TBGC.IsLargess,
        TBGC.TotalAmount,
        TBGC.PayAmount AS TreatPayAmount,
        TBGC.CardDeductionAmount AS TreatCardAmount,
        TBGC.LargessCardDeductionAmount AS TreatCardLargessAmount,
        CASE TBGC.IsLargess WHEN 0 THEN 0 ELSE TBGC.TotalAmount END AS TreatLargessAmount,
        GCA.Remark AccountRemark,
        CASE WHEN TB.Channel ='PC' THEN 'pc'
        WHEN TB.Channel ='Miniprogram' THEN '小程序'
        WHEN TB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tci.Name IntroducerName,
        tcc.Name ChannelName
        FROM TB_TreatBillGeneralCard TBGC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBGC.TreatBillID = TB.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TBGC.ProjectID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON TB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON TB.EmployeeID = Emp.ID
        LEFT JOIN TB_GeneralCardAccount GCA WITH(NOLOCK) ON TBGC.GeneralCardAccountID = GCA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = GCA.EntityID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON GCA.GeneralCardID = GC.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
                LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='20'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND GCA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        TBP.TreatBillID,
        TB.BillDate,
        ENT.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        ISNULL(TEY.EntityName,'') AS BuyEntityName,
        EMP.Name AS EmployeeName,
        '产品卡' AS TreatCardTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TBP.Quantity,
        TBP.IsLargess,
        TBP.TotalAmount,
        TBP.PayAmount AS TreatPayAmount,
        TBP.CardDeductionAmount AS TreatCardAmount,
        TBP.LargessCardDeductionAmount AS TreatCardLargessAmount,
        CASE TBP.IsLargess WHEN 0 THEN 0 ELSE TBP.TotalAmount END AS TreatLargessAmount,
        PA.Remark AccountRemark,
        CASE WHEN TB.Channel ='PC' THEN 'pc'
        WHEN TB.Channel ='Miniprogram' THEN '小程序'
        WHEN TB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tci.Name IntroducerName,
        tcc.Name ChannelName
        FROM TB_TreatBillProduct TBP WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBP.TreatBillID = TB.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON TBP.ProductID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON TB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON TB.EmployeeID = Emp.ID
        LEFT JOIN TB_ProductAccount PA WITH(NOLOCK) ON TBP.ProductAccountID = PA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = PA.EntityID
        LEFT JOIN TE_Product PAP WITH(NOLOCK) ON PA.ProductID = PAP.ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON PC.ID = P.PCategoryID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
                LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='20'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND PA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        ) T
        <where>
            <if test="TreatCardTypeName != null and TreatCardTypeName != '' ">
                TreatCardTypeName = #{TreatCardTypeName}
            </if>
        </where>
        ORDER BY BillDate DESC
    </select>
    <select id="getChannelTreatRefundDetailSum" parameterType="String"
            resultType="com.zhelian.model.report.form.output.ChannelTreatRefundDetailSumStatementForm">
        SELECT
        SUM(TotalAmount) AS TotalAmount,SUM(TreatPayAmount) AS TreatPayAmount,SUM(TreatCardAmount) AS
        TreatCardAmount,SUM(TreatCardLargessAmount) AS TreatCardLargessAmount,SUM(TreatLargessAmount) AS
        TreatLargessAmount
        FROM (
        SELECT
        '项目卡' AS TreatCardTypeName,
        TBP.TotalAmount,
        TBP.PayAmount AS TreatPayAmount,
        TBP.CardDeductionAmount AS TreatCardAmount,
        TBP.LargessCardDeductionAmount AS TreatCardLargessAmount,
        CASE TBP.IsLargess WHEN 0 THEN 0 ELSE TBP.TotalAmount END AS TreatLargessAmount
        FROM TB_TreatBillProject TBP WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBP.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TBP.ProjectID = P.ID
        LEFT JOIN TB_ProjectAccount PA WITH(NOLOCK) ON TBP.ProjectAccountID = PA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = PA.EntityID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='20'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND PA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        '储值卡' AS TreatCardTypeName,
        TBSC.TotalAmount,
        0 AS TreatPayAmount,
        TBSC.CardTreatAmount AS TreatCardAmount,
        TBSC.LargessCardTreatAmount AS TreatCardLargessAmount,
        0 AS TreatLargessAmount
        FROM TB_TreatBillSavingCard TBSC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBSC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TBSC.ProjectID = P.ID
        LEFT JOIN TB_SavingCardAccount SCA WITH(NOLOCK) ON TBSC.SavingCardAccountID = SCA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = SCA.EntityID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='20'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND SCA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        '时效卡' AS TreatCardTypeName,
        TBTC.TotalAmount,
        TBTC.PayAmount AS TreatPayAmount,
        TBTC.CardDeductionAmount AS TreatCardAmount,
        TBTC.LargessCardDeductionAmount AS TreatCardLargessAmount,
        CASE TBTC.IsLargess WHEN 0 THEN 0 ELSE TBTC.TotalAmount END AS TreatLargessAmount
        FROM TB_TreatBillTimeCard TBTC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBTC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TBTC.ProjectID = P.ID
        LEFT JOIN TB_TimeCardAccount TCA WITH(NOLOCK) ON TBTC.TimeCardAccountID = TCA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = TCA.EntityID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='20'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND TCA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        '通用次卡' AS TreatCardTypeName,
        TBGC.TotalAmount,
        TBGC.PayAmount AS TreatPayAmount,
        TBGC.CardDeductionAmount AS TreatCardAmount,
        TBGC.LargessCardDeductionAmount AS TreatCardLargessAmount,
        CASE TBGC.IsLargess WHEN 0 THEN 0 ELSE TBGC.TotalAmount END AS TreatLargessAmount
        FROM TB_TreatBillGeneralCard TBGC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBGC.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TBGC.ProjectID = P.ID
        LEFT JOIN TB_GeneralCardAccount GCA WITH(NOLOCK) ON TBGC.GeneralCardAccountID = GCA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = GCA.EntityID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='20'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND GCA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        '产品卡' AS TreatCardTypeName,
        TBP.TotalAmount,
        TBP.PayAmount AS TreatPayAmount,
        TBP.CardDeductionAmount AS TreatCardAmount,
        TBP.LargessCardDeductionAmount AS TreatCardLargessAmount,
        CASE TBP.IsLargess WHEN 0 THEN 0 ELSE TBP.TotalAmount END AS TreatLargessAmount
        FROM TB_TreatBillProduct TBP WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBP.TreatBillID = TB.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON TBP.ProductID = P.ID
        LEFT JOIN TB_ProductAccount PA WITH(NOLOCK) ON TBP.ProductAccountID = PA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = PA.EntityID
        LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='20'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND PA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        ) T
        <where>
            <if test="TreatCardTypeName != null and TreatCardTypeName != '' ">
                TreatCardTypeName = #{TreatCardTypeName}
            </if>
        </where>
    </select>

    <select id="getEasyExcelChannelTreatRefundDetail" parameterType="String"
            resultType="com.zhelian.model.report.form.output.ChannelTreatRefundDetailStatementEasyExcelForm">
        SELECT
        TreatBillID as treatBillID,CONVERT(varchar(100), BillDate, 120) AS
        billDate,EntityName as entityName,BuyEntityName as buyEntityName,CustomerName as customerName,CustomerCode as customerCode,REPLACE(CustomerPhoneNumber , SUBSTRING (CustomerPhoneNumber,4,4), '****') customerPhoneNumber,
        CustomerLevelName as customerLevelName,CustomerSourceName as customerSourceName,CustomerEntityName as customerEntityName,EmployeeName as employeeName,TreatCardTypeName as treatCardTypeName,CardName as cardName,CategoryName as categoryName,GoodName as goodName,Quantity as Quantity,IsLargess as isLargess,TotalAmount as totalAmount,TreatPayAmount as treatPayAmount,TreatCardAmount as treatCardAmount,TreatCardLargessAmount as treatCardLargessAmount,TreatLargessAmount as treatLargessAmount,AccountRemark as accountRemark,Channel as channel,IntroducerName as introducerName,ChannelName as channelName
        FROM (
        SELECT
        TBP.TreatBillID,
        TB.BillDate,
        ENT.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        ISNULL(TEY.EntityName,'') AS BuyEntityName,
        EMP.Name AS EmployeeName,
        '项目卡' AS TreatCardTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TBP.Quantity,
        CASE WHEN TBP.IsLargess=1 THEN '是'
        ELSE '否'
        END IsLargess,
        TBP.TotalAmount,
        TBP.PayAmount AS TreatPayAmount,
        TBP.CardDeductionAmount AS TreatCardAmount,
        TBP.LargessCardDeductionAmount AS TreatCardLargessAmount,
        CASE TBP.IsLargess WHEN 0 THEN 0 ELSE TBP.TotalAmount END AS TreatLargessAmount,
        PA.Remark AccountRemark,
        CASE WHEN TB.Channel ='PC' THEN 'pc'
        WHEN TB.Channel ='Miniprogram' THEN '小程序'
        WHEN TB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tci.Name IntroducerName,
        tcc.Name ChannelName
        FROM TB_TreatBillProject TBP WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBP.TreatBillID = TB.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TBP.ProjectID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON TB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON TB.EmployeeID = Emp.ID
        LEFT JOIN TB_ProjectAccount PA WITH(NOLOCK) ON TBP.ProjectAccountID = PA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = PA.EntityID
        LEFT JOIN TB_Project PAP WITH(NOLOCK) ON PA.ProjectID = PAP.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
                LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='20'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND PA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        TBSC.TreatBillID,
        TB.BillDate,
        ENT.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        ISNULL(TEY.EntityName,'') AS BuyEntityName,
        EMP.Name AS EmployeeName,
        '储值卡' AS TreatCardTypeName,
        SC.Name AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TBSC.Quantity,
        '否' AS IsLargess,
        TBSC.TotalAmount,
        0 AS TreatPayAmount,
        TBSC.CardTreatAmount AS TreatCardAmount,
        TBSC.LargessCardTreatAmount AS TreatCardLargessAmount,
        0 AS TreatLargessAmount,
        SCA.Remark AccountRemark,
        CASE WHEN TB.Channel ='PC' THEN 'pc'
        WHEN TB.Channel ='Miniprogram' THEN '小程序'
        WHEN TB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tci.Name IntroducerName,
        tcc.Name ChannelName
        FROM TB_TreatBillSavingCard TBSC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBSC.TreatBillID = TB.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TBSC.ProjectID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON TB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON TB.EmployeeID = Emp.ID
        LEFT JOIN TB_SavingCardAccount SCA WITH(NOLOCK) ON TBSC.SavingCardAccountID = SCA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = SCA.EntityID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON SCA.SavingCardID = SC.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
                LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='20'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND SCA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        TBTC.TreatBillID,
        TB.BillDate,
        ENT.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        ISNULL(TEY.EntityName,'') AS BuyEntityName,
        EMP.Name AS EmployeeName,
        '时效卡' AS TreatCardTypeName,
        TC.Name AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TBTC.Quantity,
        CASE WHEN TBTC.IsLargess=1 THEN '是'
        ELSE '否'
        END IsLargess,
        TBTC.TotalAmount,
        TBTC.PayAmount AS TreatPayAmount,
        TBTC.CardDeductionAmount AS TreatCardAmount,
        TBTC.LargessCardDeductionAmount AS TreatCardLargessAmount,
        CASE TBTC.IsLargess WHEN 0 THEN 0 ELSE TBTC.TotalAmount END AS TreatLargessAmount,
        TCA.Remark AccountRemark,
        CASE WHEN TB.Channel ='PC' THEN 'pc'
        WHEN TB.Channel ='Miniprogram' THEN '小程序'
        WHEN TB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tci.Name IntroducerName,
        tcc.Name ChannelName
        FROM TB_TreatBillTimeCard TBTC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBTC.TreatBillID = TB.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TBTC.ProjectID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON TB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON TB.EmployeeID = Emp.ID
        LEFT JOIN TB_TimeCardAccount TCA WITH(NOLOCK) ON TBTC.TimeCardAccountID = TCA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = TCA.EntityID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON TCA.TimeCardID = TC.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
                LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='20'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND TCA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        TBGC.TreatBillID,
        TB.BillDate,
        ENT.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        ISNULL(TEY.EntityName,'') AS BuyEntityName,
        EMP.Name AS EmployeeName,
        '通用次卡' AS TreatCardTypeName,
        GC.Name AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TBGC.Quantity,
        CASE WHEN TBGC.IsLargess=1 THEN '是'
        ELSE '否'
        END IsLargess,
        TBGC.TotalAmount,
        TBGC.PayAmount AS TreatPayAmount,
        TBGC.CardDeductionAmount AS TreatCardAmount,
        TBGC.LargessCardDeductionAmount AS TreatCardLargessAmount,
        CASE TBGC.IsLargess WHEN 0 THEN 0 ELSE TBGC.TotalAmount END AS TreatLargessAmount,
        GCA.Remark AccountRemark,
        CASE WHEN TB.Channel ='PC' THEN 'pc'
        WHEN TB.Channel ='Miniprogram' THEN '小程序'
        WHEN TB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tci.Name IntroducerName,
        tcc.Name ChannelName
        FROM TB_TreatBillGeneralCard TBGC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBGC.TreatBillID = TB.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TBGC.ProjectID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON TB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON TB.EmployeeID = Emp.ID
        LEFT JOIN TB_GeneralCardAccount GCA WITH(NOLOCK) ON TBGC.GeneralCardAccountID = GCA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = GCA.EntityID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON GCA.GeneralCardID = GC.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
                LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='20'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND GCA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        TBP.TreatBillID,
        TB.BillDate,
        ENT.EntityName,
        ISNULL(C.Name,'散客') AS CustomerName,
        ISNULL(C.Code,'') AS CustomerCode,
        ISNULL(C.PhoneNumber,'') AS CustomerPhoneNumber,
        TCL.Name CustomerLevelName,
        TCE.Name CustomerSourceName,
        ISNULL(TEE.EntityName,'') AS CustomerEntityName,
        ISNULL(TEY.EntityName,'') AS BuyEntityName,
        EMP.Name AS EmployeeName,
        '产品卡' AS TreatCardTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TBP.Quantity,
        CASE WHEN TBP.IsLargess=1 THEN '是'
        ELSE '否'
        END IsLargess,
        TBP.TotalAmount,
        TBP.PayAmount AS TreatPayAmount,
        TBP.CardDeductionAmount AS TreatCardAmount,
        TBP.LargessCardDeductionAmount AS TreatCardLargessAmount,
        CASE TBP.IsLargess WHEN 0 THEN 0 ELSE TBP.TotalAmount END AS TreatLargessAmount,
        PA.Remark AccountRemark,
        CASE WHEN TB.Channel ='PC' THEN 'pc'
        WHEN TB.Channel ='Miniprogram' THEN '小程序'
        WHEN TB.Channel ='MicroMall' THEN '商城'
        END Channel,
        tci.Name IntroducerName,
        tcc.Name ChannelName
        FROM TB_TreatBillProduct TBP WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBP.TreatBillID = TB.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON TBP.ProductID = P.ID
        LEFT JOIN TB_Customer C WITH(NOLOCK) ON TB.CustomerID = C.ID
        LEFT JOIN TB_Customer tci WITH(NOLOCK) ON tci.ID =C.Introducer
        LEFT JOIN TB_Channel tcc WITH(NOLOCK) ON tcc.ID =C.ChannelID
        LEFT JOIN TB_CustomerEntity TCCE WITH(NOLOCK) ON TCCE.CustomerID = C.ID AND TCCE.IsBelongEntity=1
        LEFT JOIN TK_Entity TEE WITH(NOLOCK) ON TEE.ID = TCCE.EntityID
        LEFT JOIN TK_Entity Ent WITH(NOLOCK) ON TB.EntityID = Ent.ID
        LEFT JOIN TK_Employee Emp WITH(NOLOCK) ON TB.EmployeeID = Emp.ID
        LEFT JOIN TB_ProductAccount PA WITH(NOLOCK) ON TBP.ProductAccountID = PA.ID
        LEFT JOIN TK_Entity TEY WITH(NOLOCK) ON TEY.ID = PA.EntityID
        LEFT JOIN TE_Product PAP WITH(NOLOCK) ON PA.ProductID = PAP.ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON PC.ID = P.PCategoryID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON C.CustomerLevelID = TCL.ID
                LEFT JOIN TB_CustomerSource TCE WITH(NOLOCK) ON C.CustomerSourceID = TCE.ID
        LEFT JOIN TB_CustomerSource TCET WITH(NOLOCK) ON TCET.ID = TCE.ParentID
        WHERE TB.BillStatus = '20' AND TB.BillType ='20'
        and EXISTS (
        SELECT 1 FROM TB_ChannelConsultant tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        UNION ALL
        SELECT 1 FROM TB_ChannelDeveloper tbcc WITH(NOLOCK) where tbcc.ChannelID = tcc.ID and tbcc.EmployeeID = #{EmployeeID}
        )
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <if test="CustomerLevelID != null and CustomerLevelID != null ">
            AND C.CustomerLevelID=#{CustomerLevelID}
        </if>
        <if test="CustomerSourceID != null and CustomerSourceID != null ">
            AND (C.CustomerSourceID=#{CustomerSourceID} OR TCET.ID=#{CustomerSourceID})
        </if>
        <if test="Channel != null and Channel != ''">
            AND TB.Channel LIKE '%'+ #{Channel} +'%'
        </if>
        <if test="IntroducerName != null and IntroducerName != ''">
            AND ( tci.Name like '%'+ #{IntroducerName} +'%' or tci.PhoneNumber like '%'+ #{IntroducerName} +'%' or
            tci.Code like '%'+ #{IntroducerName} +'%')
        </if>
        <if test="ChannelName != null and ChannelName != ''">
            AND tcc.Name like '%'+ #{ChannelName} +'%'
        </if>
        <if test="GoodName != null and GoodName != ''">
            AND P.Name like concat('%',#{GoodName},'%')
        </if>
        <if test="BelongEntityID != null and BelongEntityID != ''">
            AND TCCE.EntityID=#{BelongEntityID}
        </if>
        <if test="BuyEntityID != null and BuyEntityID != ''">
            AND PA.EntityID=#{BuyEntityID}
        </if>
        <if test="CustomerName != null and CustomerName != ''">
            <choose>
                <when test="CustomerName =='散客'">
                    AND TB.CustomerID is null
                </when>
                <otherwise>
                    AND ( C.Name LIKE '%'+ #{CustomerName} +'%' OR C.PhoneNumber LIKE '%'+ #{CustomerName} +'%' OR
                    C.Code LIKE '%'+ #{CustomerName} +'%')
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        ) T
        <where>
            <if test="TreatCardTypeName != null and TreatCardTypeName != '' ">
                TreatCardTypeName = #{TreatCardTypeName}
            </if>
        </where>
        ORDER BY BillDate DESC
    </select>
</mapper>