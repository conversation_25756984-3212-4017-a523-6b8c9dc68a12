<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http//:mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.report.EntityTradingMapper">


    <select id="getTradingStatementSum"
            resultType="com.zhelian.model.report.form.output.EntityTradingSumOutputForm">
        SELECT
        ISNULL(SUM(SalePayAmount),0) AS SalePayAmount,
        ISNULL(SUM(SaleSavingCardDeductionAmount),0) AS SaleSavingCardDeductionAmount,
        ISNULL(SUM(SaleLargessSavingCardDeductionAmount),0) AS SaleLargessSavingCardDeductionAmount,
        ISNULL(SUM(SaleLargessAmount),0) AS SaleLargessAmount,
        ISNULL(SUM(ArrearPayAmount),0) AS ArrearPayAmount,
        ISNULL(SUM(ArrearSavingCardDeductionAmount),0) AS ArrearSavingCardDeductionAmount,
        ISNULL(SUM(ArrearLargessSavingCardDeductionAmount),0) AS ArrearLargessSavingCardDeductionAmount,

        ISNULL(SUM(RefundPayAmount),0) AS RefundPayAmount,
        ISNULL(SUM(RefundSavingCardDeductionAmount),0) AS RefundSavingCardDeductionAmount,
        ISNULL(SUM(RefundLargessSavingCardDeductionAmount),0) AS RefundLargessSavingCardDeductionAmount,
        ISNULL(SUM(RefundLargessAmount),0) AS RefundLargessAmount,

        ISNULL(SUM(TreatPayAmount),0) AS TreatPayAmount,
        ISNULL(SUM(TreatCardDeductionAmount),0) AS TreatCardDeductionAmount,
        ISNULL(SUM(TreatLargesCardDeductionAmount),0) AS TreatLargesCardDeductionAmount,
        ISNULL(SUM(TreatLargessAmount),0) AS TreatLargessAmount,

        ISNULL(SUM(RefundTreatPayAmount),0) AS RefundTreatPayAmount,
        ISNULL(SUM(RefundTreatCardDeductionAmount),0) AS RefundTreatCardDeductionAmount,
        ISNULL(SUM(RefundTreatLargesCardDeductionAmount),0) AS RefundTreatLargesCardDeductionAmount,
        ISNULL(SUM(RefundTreatLargessAmount),0) AS RefundTreatLargessAmount

        FROM (
        SELECT
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPP.Amount END,0)),0) AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        0 SaleLargessAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPP.Amount END,0)),0) AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPP.Amount END,0)),0) AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        0 AS RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillProject SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TB_SaleBillPayProject SBPP WITH(NOLOCK) ON SBP.ID = SBPP.SaleBillProjectID
        WHERE SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT

        0 SalePayAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPSCDP.Amount END,0)),0) AS SaleSavingCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPSCDP.LargessAmount END,0)),0) AS
        SaleLargessSavingCardDeductionAmount,
        0 SaleLargessAmount,
        0 AS ArrearPayAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPSCDP.Amount END,0)),0)  AS ArrearSavingCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPSCDP.LargessAmount END,0)),0) AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPSCDP.Amount END,0)),0) AS
        RefundSavingCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPSCDP.LargessAmount END,0)),0) AS
        RefundLargessSavingCardDeductionAmount,
        0 AS RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillProject SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) ON SBP.ID = SBPSCDP.SaleBillProjectID
        WHERE SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        0 AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        CASE WHEN SB.BillType !='20' THEN SBP.TotalAmount ELSE 0 END AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        CASE WHEN SB.BillType ='20' THEN SBP.Quantity *SBP.Price ELSE 0 END AS RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillProject SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        WHERE SB.BillStatus = '20' AND SBP.IsLargess=1
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        ISNULL(SUM(ISNULL(CASE WHEN (SB.BillType = '10' OR SB.BillType = '40') THEN SBPSC.Amount END,0)),0) AS
        SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPSC.Amount END,0)),0) AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPSC.Amount END,0)),0) AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        0 AS RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillSavingCard SBSC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBSC.BillID = SB.ID
        LEFT JOIN TB_SaleBillPaySavingCard SBPSC WITH(NOLOCK) ON SBSC.ID = SBPSC.SaleBillSavingCardID
        WHERE SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        0 AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN (SB.BillType = '10' OR SB.BillType = '40') THEN SBSC.LargessAmount END,0)),0) AS
        SaleLargessAmount,
        0 AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '20' THEN SBSC.LargessAmount END,0)),0) RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillSavingCard SBSC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBSC.BillID = SB.ID
        WHERE SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPTC.Amount END,0)),0) AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPTC.Amount END,0)),0) AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPTC.Amount END,0)),0) AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        0 RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillTimeCard SBTC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBTC.BillID = SB.ID
        LEFT JOIN TB_SaleBillPayTimeCard SBPTC WITH(NOLOCK) ON SBTC.ID = SBPTC.SaleBillTimeCardID
        WHERE SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        0 AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        CASE WHEN SB.BillType !='20' THEN SBTC.TotalAmount ELSE 0 END AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        CASE WHEN SB.BillType ='20' THEN SBTC.Quantity *SBTC.Price ELSE 0 END AS RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillTimeCard SBTC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBTC.BillID = SB.ID
        WHERE SB.BillStatus = '20' AND SBTC.IsLargess=1
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        0 AS SalePayAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPSCDTC.Amount END,0)),0) AS SaleSavingCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPSCDTC.LargessAmount END,0)),0) AS
        SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPSCDTC.Amount END,0)),0)  AS ArrearSavingCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPSCDTC.LargessAmount END,0)),0) AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPSCDTC.Amount END,0)),0) AS
        RefundSavingCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPSCDTC.LargessAmount END,0)),0) AS
        RefundLargessSavingCardDeductionAmount,
        0 AS RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillTimeCard SBTC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBTC.BillID = SB.ID
        LEFT JOIN TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) ON SBTC.ID =
        SBPSCDTC.SaleBillTimeCardID
        WHERE SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPGC.Amount END,0)),0) AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPGC.Amount END,0)),0) AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPGC.Amount END,0)),0) AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        0 RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillGeneralCard SBGC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBGC.BillID = SB.ID
        LEFT JOIN TB_SaleBillPayGeneralCard SBPGC WITH(NOLOCK) ON SBGC.ID = SBPGC.SaleBillGeneralCardID
        WHERE SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        0 AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        CASE WHEN SB.BillType !='20' THEN SBGC.TotalAmount ELSE 0 END AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        CASE WHEN SB.BillType ='20' THEN SBGC.CardTimes / TGA.CardTimes *SBGC.Price ELSE 0 END AS RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillGeneralCard SBGC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBGC.BillID = SB.ID
        LEFT JOIN TB_GeneralCardAccount TGA WITH(NOLOCK) ON TGA.ID = SBGC.GeneralCardAccountID
        WHERE SB.BillStatus = '20' AND SBGC.IsLargess=1
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT

        0 AS SalePayAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPSCDGC.Amount END,0)),0) AS SaleSavingCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPSCDGC.LargessAmount END,0)),0) AS
        SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPSCDGC.Amount END,0)),0)  AS ArrearSavingCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPSCDGC.LargessAmount END,0)),0) AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPSCDGC.Amount END,0)),0) AS
        RefundSavingCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPSCDGC.LargessAmount END,0)),0) AS
        RefundLargessSavingCardDeductionAmount,
        0 RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillGeneralCard SBGC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBGC.BillID = SB.ID
        LEFT JOIN TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDGC WITH(NOLOCK) ON SBGC.ID =
        SBPSCDGC.SaleBillGeneralCardID
        WHERE SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT

        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPPC.Amount END,0)),0) AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPPC.Amount END,0)),0) AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPPC.Amount END,0)),0) AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        0 RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillPackageCard SBPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBPC.BillID = SB.ID
        LEFT JOIN TB_SaleBillPayPackageCard SBPPC WITH(NOLOCK) ON SBPC.ID = SBPPC.SaleBillPackageCardID
        WHERE SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT

        0 AS SalePayAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPSCDPC.Amount END,0)),0) AS SaleSavingCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPSCDPC.LargessAmount END,0)),0) AS
        SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPSCDPC.Amount END,0)),0)  AS ArrearSavingCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPSCDPC.LargessAmount END,0)),0) AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPSCDPC.Amount END,0)),0) AS
        RefundSavingCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPSCDPC.LargessAmount END,0)),0) AS
        RefundLargessSavingCardDeductionAmount,
        0 RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillPackageCard SBPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBPC.BillID = SB.ID
        LEFT JOIN TB_SaleBillPaySavingCardDeductionPackageCard SBPSCDPC WITH(NOLOCK) ON SBPC.ID =
        SBPSCDPC.SaleBillPackageCardID
        WHERE SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT

        0 AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        CASE WHEN SB.BillType !='20' THEN SBPC.TotalAmount ELSE 0 END AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        CASE WHEN SB.BillType ='20' THEN SBPC.TotalAmount ELSE 0 END AS RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillPackageCard SBPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBPC.BillID = SB.ID
        WHERE SB.BillStatus = '20' AND SBPC.IsLargess=1
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT

        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPP.Amount END,0)),0) AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPP.Amount END,0)),0) AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPP.Amount END,0)),0) AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        0 RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillProduct SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TB_SaleBillPayProduct SBPP WITH(NOLOCK) ON SBP.ID = SBPP.SaleBillProductID
        WHERE SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT

        0 AS SalePayAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPSCDP.Amount END,0)),0) AS SaleSavingCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPSCDP.LargessAmount END,0)),0) AS
        SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPSCDP.Amount END,0)),0)  AS ArrearSavingCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPSCDP.LargessAmount END,0)),0) AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPSCDP.Amount END,0)),0) AS
        RefundSavingCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPSCDP.LargessAmount END,0)),0) AS
        RefundLargessSavingCardDeductionAmount,
        0 RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillProduct SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) ON SBP.ID = SBPSCDP.SaleBillProductID
        WHERE SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL
        SELECT

        0 AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        CASE WHEN SB.BillType !='20' THEN SBP.TotalAmount ELSE 0 END AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        CASE WHEN SB.BillType ='20' THEN SBP.Quantity *SBP.Price ELSE 0 END AS RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillProduct SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        WHERE SB.BillStatus = '20' AND SBP.IsLargess=1
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL
        SELECT
        0 AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        0 AS RefundLargessAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '10' THEN TBP.PayAmount END,0)),0) AS
        TreatPayAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '10' THEN TBP.CardDeductionAmount END,0)),0) AS
        TreatCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '10' THEN TBP.LargessCardDeductionAmount END,0)),0) AS
        TreatLargesCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '10' AND TBP.IsLargess =1 THEN TBP.TotalAmount END,0)),0) AS
        TreatLargessAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '20' THEN TBP.PayAmount END,0)),0) AS
        RefundTreatPayAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '20' THEN TBP.CardDeductionAmount END,0)),0) AS
        RefundTreatCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '20' THEN TBP.LargessCardDeductionAmount END,0)),0) AS
        RefundTreatLargesCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '20' AND TBP.IsLargess =1 THEN TBP.TotalAmount END,0)),0) AS
        RefundTreatLargessAmount
        FROM TB_TreatBillProject TBP WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBP.TreatBillID = TB.ID
        WHERE TB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        0 AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        0 AS RefundLargessAmount,
        0 AS TreatPayAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '10' THEN TBSD.CardTreatAmount END,0)),0) AS TreatCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '10' THEN TBSD.LargessCardTreatAmount END,0)),0) AS
        TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '20' THEN TBSD.CardTreatAmount END,0)),0) AS
        RefundTreatCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '20' THEN TBSD.LargessCardTreatAmount END,0)),0) AS
        RefundTreatLargesCardDeductionAmount,
        0 AS
        RefundTreatLargessAmount
        FROM TB_TreatBillSavingCard TBSD WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBSD.TreatBillID = TB.ID
        WHERE TB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        0 AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        0 AS RefundLargessAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '10' THEN TBTC.PayAmount END,0)),0) AS
        TreatPayAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '10' THEN TBTC.CardDeductionAmount END,0)),0) AS
        TreatCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '10' THEN TBTC.LargessCardDeductionAmount END,0)),0) AS
        TreatLargesCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '10' AND TBTC.IsLargess =1 THEN TBTC.TotalAmount END,0)),0) AS
        TreatLargessAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '20' THEN TBTC.PayAmount END,0)),0) AS
        RefundTreatPayAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '20' THEN TBTC.CardDeductionAmount END,0)),0) AS
        RefundTreatCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '20' THEN TBTC.LargessCardDeductionAmount END,0)),0) AS
        RefundTreatLargesCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '20' AND TBTC.IsLargess =1 THEN TBTC.TotalAmount END,0)),0) AS
        RefundTreatLargessAmount

        FROM TB_TreatBillTimeCard TBTC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBTC.TreatBillID = TB.ID
        WHERE TB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        0 AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        0 AS RefundLargessAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '10' THEN TBGC.PayAmount END,0)),0) AS
        TreatPayAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '10' THEN TBGC.CardDeductionAmount END,0)),0) AS
        TreatCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '10' THEN TBGC.LargessCardDeductionAmount END,0)),0) AS
        TreatLargesCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '10' AND TBGC.IsLargess =1 THEN TBGC.TotalAmount END,0)),0) AS
        TreatLargessAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '20' THEN TBGC.PayAmount END,0)),0) AS
        RefundTreatPayAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '20' THEN TBGC.CardDeductionAmount END,0)),0) AS
        RefundTreatCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '20' THEN TBGC.LargessCardDeductionAmount END,0)),0) AS
        RefundTreatLargesCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '20' AND TBGC.IsLargess =1 THEN TBGC.TotalAmount END,0)),0) AS
        RefundTreatLargessAmount
        FROM TB_TreatBillGeneralCard TBGC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBGC.TreatBillID = TB.ID
        WHERE TB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        0 AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        0 AS RefundLargessAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '10' THEN TBP.PayAmount END,0)),0) AS
        TreatPayAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '10' THEN TBP.CardDeductionAmount END,0)),0) AS
        TreatCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '10' THEN TBP.LargessCardDeductionAmount END,0)),0) AS
        TreatLargesCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '10' AND TBP.IsLargess =1 THEN TBP.TotalAmount END,0)),0) AS
        TreatLargessAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '20' THEN TBP.PayAmount END,0)),0) AS
        RefundTreatPayAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '20' THEN TBP.CardDeductionAmount END,0)),0) AS
        RefundTreatCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '20' THEN TBP.LargessCardDeductionAmount END,0)),0) AS
        RefundTreatLargesCardDeductionAmount,
        ISNULL(SUM(ISNULL(CASE WHEN TB.BillType = '20' AND TBP.IsLargess =1 THEN TBP.TotalAmount END,0)),0) AS
        RefundTreatLargessAmount
        FROM TB_TreatBillProduct TBP WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBP.TreatBillID = TB.ID
        WHERE TB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>

        ) T
    </select>

    <select id="getTradingStatementSumPayMethod"
            resultType="com.zhelian.model.report.form.output.EntityTradingPayMethodDetailOutputForm">
        SELECT
        0 AS ID,
        0 Sequence,
        '总金额' Type,
        ISNULL(SUM(tp.Amount),0)Amount
        FROM TB_SaleBillPay tp WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t ON t.ID=tp.SaleBillID
        WHERE t.BillStatus='20' AND t.BillType!='20'
        <if test="StartDate != null and EndDate != null ">
            AND t.BillDate &gt;= #{StartDate} AND t.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        tsbp.PayMethodID AS ID,
        tpm.Sequence,
        tpm.Name Type,
        ISNULL(SUM(tsbp.Amount),0)Amount
        FROM TB_SaleBillPay tsbp WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID = tsbp .SaleBillID
        LEFT JOIN TB_PayMethod tpm WITH(NOLOCK) ON tpm.ID = tsbp.PayMethodID
        WHERE t.BillStatus='20' AND t.BillType!='20'
        <if test="StartDate != null and EndDate != null ">
            AND t.BillDate &gt;= #{StartDate} AND t.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND t.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = t.EntityID
                )
            </otherwise>
        </choose>
        GROUP BY tsbp.PayMethodID,tpm.Name,tpm.Sequence
        ORDER BY Sequence
    </select>


    <resultMap id="TradingMap"
               type="com.zhelian.model.report.form.output.EntityTradingDetailOutputForm">
        <id column="EntityID" property="EntityID" jdbcType="INTEGER"/>
        <result column="EntityName" property="EntityName" jdbcType="VARCHAR"/>
        <result column="StartDate" property="StartDate" jdbcType="VARCHAR"/>
        <result column="EndDate" property="EndDate" jdbcType="VARCHAR"/>
        <result column="SalePayAmount" property="SalePayAmount" jdbcType="DECIMAL"/>
        <result column="SaleSavingCardDeductionAmount" property="SaleSavingCardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="SaleLargessSavingCardDeductionAmount" property="SaleLargessSavingCardDeductionAmount"
                jdbcType="DECIMAL"/>
        <result column="SaleLargessAmount" property="SaleLargessAmount" jdbcType="DECIMAL"/>
        <result column="ArrearPayAmount" property="ArrearPayAmount" jdbcType="DECIMAL"/>
        <result column="ArrearSavingCardDeductionAmount" property="ArrearSavingCardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="ArrearLargessSavingCardDeductionAmount" property="ArrearLargessSavingCardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="RefundPayAmount" property="RefundPayAmount" jdbcType="DECIMAL"/>
        <result column="RefundSavingCardDeductionAmount" property="RefundSavingCardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="RefundLargessSavingCardDeductionAmount" property="RefundLargessSavingCardDeductionAmount"
                jdbcType="DECIMAL"/>
        <result column="RefundLargessAmount" property="RefundLargessAmount" jdbcType="DECIMAL"/>
        <result column="TreatPayAmount" property="TreatPayAmount" jdbcType="DECIMAL"/>
        <result column="TreatCardDeductionAmount" property="TreatCardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="TreatLargesCardDeductionAmount" property="TreatLargesCardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="TreatLargessAmount" property="TreatLargessAmount" jdbcType="DECIMAL"/>
        <result column="RefundTreatPayAmount" property="RefundTreatPayAmount" jdbcType="DECIMAL"/>
        <result column="RefundTreatCardDeductionAmount" property="RefundTreatCardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="RefundTreatLargesCardDeductionAmount" property="RefundTreatLargesCardDeductionAmount"
                jdbcType="DECIMAL"/>
        <result column="RefundTreatLargessAmount" property="RefundTreatLargessAmount" jdbcType="DECIMAL"/>
        <collection property="PayMethod"
                    ofType="com.zhelian.model.report.form.output.EntityTradingPayMethodDetailOutputForm"
                    select="getPayMethodList" column="{EntityID=EntityID,StartDate=StartDate,EndDate=EndDate}">
        </collection>
    </resultMap>

    <select id="getTradingStatement" resultMap="TradingMap">
        SELECT
        #{StartDate} StartDate,
        #{EndDate} EndDate,
        te.ID AS EntityID,
        te.EntityName,
        ISNULL(SUM(SalePayAmount),0) AS SalePayAmount,
        ISNULL(SUM(SaleSavingCardDeductionAmount),0) AS SaleSavingCardDeductionAmount,
        ISNULL(SUM(SaleLargessSavingCardDeductionAmount),0) AS SaleLargessSavingCardDeductionAmount,
        ISNULL(SUM(SaleLargessAmount),0) AS SaleLargessAmount,
        ISNULL(SUM(ArrearPayAmount),0) AS ArrearPayAmount,
        ISNULL(SUM(ArrearSavingCardDeductionAmount),0) AS ArrearSavingCardDeductionAmount,
        ISNULL(SUM(ArrearLargessSavingCardDeductionAmount),0) AS ArrearLargessSavingCardDeductionAmount,

        ISNULL(SUM(RefundPayAmount),0) AS RefundPayAmount,
        ISNULL(SUM(RefundSavingCardDeductionAmount),0) AS RefundSavingCardDeductionAmount,
        ISNULL(SUM(RefundLargessSavingCardDeductionAmount),0) AS RefundLargessSavingCardDeductionAmount,
        ISNULL(SUM(RefundLargessAmount),0) AS RefundLargessAmount,

        ISNULL(SUM(TreatPayAmount),0) AS TreatPayAmount,
        ISNULL(SUM(TreatCardDeductionAmount),0) AS TreatCardDeductionAmount,
        ISNULL(SUM(TreatLargesCardDeductionAmount),0) AS TreatLargesCardDeductionAmount,
        ISNULL(SUM(TreatLargessAmount),0) AS TreatLargessAmount,

        ISNULL(SUM(RefundTreatPayAmount),0) AS RefundTreatPayAmount,
        ISNULL(SUM(RefundTreatCardDeductionAmount),0) AS RefundTreatCardDeductionAmount,
        ISNULL(SUM(RefundTreatLargesCardDeductionAmount),0) AS RefundTreatLargesCardDeductionAmount,
        ISNULL(SUM(RefundTreatLargessAmount),0) AS RefundTreatLargessAmount
        FROM TK_Entity te WITH(NOLOCK)
        LEFT JOIN (
        SELECT EntityID,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPP.Amount END,0),0) AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        0 SaleLargessAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPP.Amount END,0),0) AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPP.Amount END,0),0) AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        0 AS RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillProject SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TB_SaleBillPayProject SBPP WITH(NOLOCK) ON SBP.ID = SBPP.SaleBillProjectID
        WHERE SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT EntityID, 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT EntityID,

        0 SalePayAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPSCDP.Amount END,0),0) AS SaleSavingCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPSCDP.LargessAmount END,0),0) AS
        SaleLargessSavingCardDeductionAmount,
        0 SaleLargessAmount,
        0 AS ArrearPayAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPSCDP.Amount END,0),0) AS ArrearSavingCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPSCDP.LargessAmount END,0),0) AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPSCDP.Amount END,0),0) AS RefundSavingCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPSCDP.LargessAmount END,0),0) AS
        RefundLargessSavingCardDeductionAmount,
        0 AS RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillProject SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TB_SaleBillPaySavingCardDeductionProject SBPSCDP WITH(NOLOCK) ON SBP.ID = SBPSCDP.SaleBillProjectID
        WHERE SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT EntityID, 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT EntityID,
        0 AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        CASE WHEN SB.BillType !='20' THEN SBP.TotalAmount ELSE 0 END AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        CASE WHEN SB.BillType ='20' THEN SBP.Quantity *SBP.Price ELSE 0 END AS RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillProject SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        WHERE SB.BillStatus = '20' AND SBP.IsLargess=1
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT EntityID, 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT EntityID,
        ISNULL(ISNULL(CASE WHEN (SB.BillType = '10' OR SB.BillType = '40') THEN SBPSC.Amount END,0),0) AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPSC.Amount END,0),0) AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPSC.Amount END,0),0) AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        0 AS RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillSavingCard SBSC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBSC.BillID = SB.ID
        LEFT JOIN TB_SaleBillPaySavingCard SBPSC WITH(NOLOCK) ON SBSC.ID = SBPSC.SaleBillSavingCardID
        WHERE SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT EntityID, 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT EntityID,
        0 AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN (SB.BillType = '10' OR SB.BillType = '40') THEN SBSC.LargessAmount END,0),0) AS
        SaleLargessAmount,
        0 AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '20' THEN SBSC.LargessAmount END,0),0) RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillSavingCard SBSC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBSC.BillID = SB.ID
        WHERE SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT EntityID, 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT EntityID,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPTC.Amount END,0),0) AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPTC.Amount END,0),0) AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPTC.Amount END,0),0) AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        0 RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillTimeCard SBTC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBTC.BillID = SB.ID
        LEFT JOIN TB_SaleBillPayTimeCard SBPTC WITH(NOLOCK) ON SBTC.ID = SBPTC.SaleBillTimeCardID
        WHERE SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT EntityID, 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT EntityID,
        0 AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        CASE WHEN SB.BillType !='20' THEN SBTC.TotalAmount ELSE 0 END AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        CASE WHEN SB.BillType ='20' THEN SBTC.Quantity *SBTC.Price ELSE 0 END AS RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillTimeCard SBTC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBTC.BillID = SB.ID
        WHERE SB.BillStatus = '20' AND SBTC.IsLargess=1
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT EntityID, 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT EntityID,
        0 AS SalePayAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPSCDTC.Amount END,0),0) AS SaleSavingCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPSCDTC.LargessAmount END,0),0) AS
        SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPSCDTC.Amount END,0),0) AS ArrearSavingCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPSCDTC.LargessAmount END,0),0) AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPSCDTC.Amount END,0),0) AS RefundSavingCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPSCDTC.LargessAmount END,0),0) AS
        RefundLargessSavingCardDeductionAmount,
        0 AS RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillTimeCard SBTC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBTC.BillID = SB.ID
        LEFT JOIN TB_SaleBillPaySavingCardDeductionTimeCard SBPSCDTC WITH(NOLOCK) ON SBTC.ID =
        SBPSCDTC.SaleBillTimeCardID
        WHERE SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT EntityID, 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT EntityID,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPGC.Amount END,0),0) AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPGC.Amount END,0),0) AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPGC.Amount END,0),0) AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        0 RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillGeneralCard SBGC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBGC.BillID = SB.ID
        LEFT JOIN TB_SaleBillPayGeneralCard SBPGC WITH(NOLOCK) ON SBGC.ID = SBPGC.SaleBillGeneralCardID
        WHERE SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT EntityID, 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT SB.EntityID,
        0 AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        CASE WHEN SB.BillType !='20' THEN SBGC.TotalAmount ELSE 0 END AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        CASE WHEN SB.BillType ='20' THEN SBGC.CardTimes / TGA.CardTimes *SBGC.Price ELSE 0 END AS RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillGeneralCard SBGC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBGC.BillID = SB.ID
        LEFT JOIN TB_GeneralCardAccount TGA WITH(NOLOCK) ON TGA.ID = SBGC.GeneralCardAccountID
        WHERE SB.BillStatus = '20' AND SBGC.IsLargess=1
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT EntityID, 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT EntityID,

        0 AS SalePayAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPSCDGC.Amount END,0),0) AS SaleSavingCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPSCDGC.LargessAmount END,0),0) AS
        SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPSCDGC.Amount END,0),0) AS ArrearSavingCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPSCDGC.LargessAmount END,0),0) AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPSCDGC.Amount END,0),0) AS RefundSavingCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPSCDGC.LargessAmount END,0),0) AS
        RefundLargessSavingCardDeductionAmount,
        0 RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillGeneralCard SBGC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBGC.BillID = SB.ID
        LEFT JOIN TB_SaleBillPaySavingCardDeductionGeneralCard SBPSCDGC WITH(NOLOCK) ON SBGC.ID =
        SBPSCDGC.SaleBillGeneralCardID
        WHERE SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT EntityID, 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT EntityID,

        ISNULL(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPPC.Amount END,0),0) AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPPC.Amount END,0),0) AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPPC.Amount END,0),0) AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        0 RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillPackageCard SBPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBPC.BillID = SB.ID
        LEFT JOIN TB_SaleBillPayPackageCard SBPPC WITH(NOLOCK) ON SBPC.ID = SBPPC.SaleBillPackageCardID
        WHERE SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT EntityID, 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT EntityID,

        0 AS SalePayAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPSCDPC.Amount END,0),0) AS SaleSavingCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPSCDPC.LargessAmount END,0),0) AS
        SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPSCDPC.Amount END,0),0) AS ArrearSavingCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPSCDPC.LargessAmount END,0),0) AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPSCDPC.Amount END,0),0) AS RefundSavingCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPSCDPC.LargessAmount END,0),0) AS
        RefundLargessSavingCardDeductionAmount,
        0 RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillPackageCard SBPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBPC.BillID = SB.ID
        LEFT JOIN TB_SaleBillPaySavingCardDeductionPackageCard SBPSCDPC WITH(NOLOCK) ON SBPC.ID =
        SBPSCDPC.SaleBillPackageCardID
        WHERE SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT EntityID, 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT EntityID,

        0 AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        CASE WHEN SB.BillType !='20' THEN SBPC.TotalAmount ELSE 0 END AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        CASE WHEN SB.BillType ='20' THEN SBPC.TotalAmount ELSE 0 END AS RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillPackageCard SBPC WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBPC.BillID = SB.ID
        WHERE SB.BillStatus = '20' AND SBPC.IsLargess=1
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT EntityID, 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT EntityID,

        ISNULL(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPP.Amount END,0),0) AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPP.Amount END,0),0) AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPP.Amount END,0),0) AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        0 RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillProduct SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TB_SaleBillPayProduct SBPP WITH(NOLOCK) ON SBP.ID = SBPP.SaleBillProductID
        WHERE SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT EntityID, 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT EntityID,

        0 AS SalePayAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPSCDP.Amount END,0),0) AS SaleSavingCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '10' THEN SBPSCDP.LargessAmount END,0),0) AS
        SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPSCDP.Amount END,0),0) AS ArrearSavingCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '30' THEN SBPSCDP.LargessAmount END,0),0) AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPSCDP.Amount END,0),0) AS RefundSavingCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN SB.BillType = '20' THEN SBPSCDP.LargessAmount END,0),0) AS
        RefundLargessSavingCardDeductionAmount,
        0 RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillProduct SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        LEFT JOIN TB_SaleBillPaySavingCardDeductionProduct SBPSCDP WITH(NOLOCK) ON SBP.ID = SBPSCDP.SaleBillProductID
        WHERE SB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT EntityID, 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL
        SELECT EntityID,

        0 AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        CASE WHEN SB.BillType !='20' THEN SBP.TotalAmount ELSE 0 END AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        CASE WHEN SB.BillType ='20' THEN SBP.Quantity *SBP.Price ELSE 0 END AS RefundLargessAmount,
        0 AS TreatPayAmount,
        0 AS TreatCardDeductionAmount,
        0 AS TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        0 AS RefundTreatCardDeductionAmount,
        0 AS RefundTreatLargesCardDeductionAmount,
        0 AS RefundTreatLargessAmount
        FROM TB_SaleBillProduct SBP WITH(NOLOCK)
        LEFT JOIN TB_SaleBill SB WITH(NOLOCK) ON SBP.BillID = SB.ID
        WHERE SB.BillStatus = '20' AND SBP.IsLargess=1
        <if test="StartDate != null and EndDate != null ">
            AND SB.BillDate &gt;= #{StartDate} AND SB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND SB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT EntityID, 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = SB.EntityID
                )
            </otherwise>
        </choose>

        UNION ALL
        SELECT EntityID,
        0 AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        0 AS RefundLargessAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '10' THEN TBP.PayAmount END,0),0) AS
        TreatPayAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '10' THEN TBP.CardDeductionAmount END,0),0) AS
        TreatCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '10' THEN TBP.LargessCardDeductionAmount END,0),0) AS
        TreatLargesCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '10' AND TBP.IsLargess =1 THEN TBP.TotalAmount END,0),0) AS
        TreatLargessAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '20' THEN TBP.PayAmount END,0),0) AS
        RefundTreatPayAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '20' THEN TBP.CardDeductionAmount END,0),0) AS
        RefundTreatCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '20' THEN TBP.LargessCardDeductionAmount END,0),0) AS
        RefundTreatLargesCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '20' AND TBP.IsLargess =1 THEN TBP.TotalAmount END,0),0) AS
        RefundTreatLargessAmount
        FROM TB_TreatBillProject TBP WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBP.TreatBillID = TB.ID
        WHERE TB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT EntityID, 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT EntityID,
        0 AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        0 AS RefundLargessAmount,
        0 AS TreatPayAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '10' THEN TBSD.CardTreatAmount END,0),0) AS TreatCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '10' THEN TBSD.LargessCardTreatAmount END,0),0) AS
        TreatLargesCardDeductionAmount,
        0 AS TreatLargessAmount,
        0 AS RefundTreatPayAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '20' THEN TBSD.CardTreatAmount END,0),0) AS
        RefundTreatCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '20' THEN TBSD.LargessCardTreatAmount END,0),0) AS
        RefundTreatLargesCardDeductionAmount,
        0 AS
        RefundTreatLargessAmount
        FROM TB_TreatBillSavingCard TBSD WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBSD.TreatBillID = TB.ID
        WHERE TB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT EntityID, 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT EntityID,
        0 AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        0 AS RefundLargessAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '10' THEN TBTC.PayAmount END,0),0) AS
        TreatPayAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '10' THEN TBTC.CardDeductionAmount END,0),0) AS
        TreatCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '10' THEN TBTC.LargessCardDeductionAmount END,0),0) AS
        TreatLargesCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '10' AND TBTC.IsLargess =1 THEN TBTC.TotalAmount END,0),0) AS
        TreatLargessAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '20' THEN TBTC.PayAmount END,0),0) AS
        RefundTreatPayAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '20' THEN TBTC.CardDeductionAmount END,0),0) AS
        RefundTreatCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '20' THEN TBTC.LargessCardDeductionAmount END,0),0) AS
        RefundTreatLargesCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '20' AND TBTC.IsLargess =1 THEN TBTC.TotalAmount END,0),0) AS
        RefundTreatLargessAmount

        FROM TB_TreatBillTimeCard TBTC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBTC.TreatBillID = TB.ID
        WHERE TB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT EntityID, 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT EntityID,
        0 AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        0 AS RefundLargessAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '10' THEN TBGC.PayAmount END,0),0) AS
        TreatPayAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '10' THEN TBGC.CardDeductionAmount END,0),0) AS
        TreatCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '10' THEN TBGC.LargessCardDeductionAmount END,0),0) AS
        TreatLargesCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '10' AND TBGC.IsLargess =1 THEN TBGC.TotalAmount END,0),0) AS
        TreatLargessAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '20' THEN TBGC.PayAmount END,0),0) AS
        RefundTreatPayAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '20' THEN TBGC.CardDeductionAmount END,0),0) AS
        RefundTreatCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '20' THEN TBGC.LargessCardDeductionAmount END,0),0) AS
        RefundTreatLargesCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '20' AND TBGC.IsLargess =1 THEN TBGC.TotalAmount END,0),0) AS
        RefundTreatLargessAmount
        FROM TB_TreatBillGeneralCard TBGC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBGC.TreatBillID = TB.ID
        WHERE TB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT EntityID, 1 FROM TB_EmployeeEntityPermission EEP WITH (NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        UNION ALL
        SELECT EntityID,
        0 AS SalePayAmount,
        0 AS SaleSavingCardDeductionAmount,
        0 AS SaleLargessSavingCardDeductionAmount,
        0 AS SaleLargessAmount,
        0 AS ArrearPayAmount,
        0 AS ArrearSavingCardDeductionAmount,
        0 AS ArrearLargessSavingCardDeductionAmount,
        0 AS RefundPayAmount,
        0 AS RefundSavingCardDeductionAmount,
        0 AS RefundLargessSavingCardDeductionAmount,
        0 AS RefundLargessAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '10' THEN TBP.PayAmount END,0),0) AS
        TreatPayAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '10' THEN TBP.CardDeductionAmount END,0),0) AS
        TreatCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '10' THEN TBP.LargessCardDeductionAmount END,0),0) AS
        TreatLargesCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '10' AND TBP.IsLargess =1 THEN TBP.TotalAmount END,0),0) AS
        TreatLargessAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '20' THEN TBP.PayAmount END,0),0) AS
        RefundTreatPayAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '20' THEN TBP.CardDeductionAmount END,0),0) AS
        RefundTreatCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '20' THEN TBP.LargessCardDeductionAmount END,0),0) AS
        RefundTreatLargesCardDeductionAmount,
        ISNULL(ISNULL(CASE WHEN TB.BillType = '20' AND TBP.IsLargess =1 THEN TBP.TotalAmount END,0),0) AS
        RefundTreatLargessAmount
        FROM TB_TreatBillProduct TBP WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TBP.TreatBillID = TB.ID
        WHERE TB.BillStatus = '20'
        <if test="StartDate != null and EndDate != null ">
            AND TB.BillDate &gt;= #{StartDate} AND TB.BillDate &lt; #{EndDate}
        </if>
        <choose>
            <when test="EntityID!=null">
                AND TB.EntityID = #{EntityID}
            </when>
            <otherwise>
                AND EXISTS (
                SELECT EntityID, 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TB.EntityID
                )
            </otherwise>
        </choose>
        ) T ON te.ID=T.EntityID
        <where>
            te.IsStore = 1 AND te.Active=1
            <choose>
                <when test="EntityID!=null">
                    AND te.ID = #{EntityID}
                </when>
                <otherwise>
                    AND EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = te.ID
                    )
                </otherwise>
            </choose>
        </where>

        GROUP BY te.ID,te.EntityName
        ORDER BY te.EntityName
    </select>

    <select id="getPayMethodList"
            resultType="com.zhelian.model.report.form.output.EntityTradingPayMethodDetailOutputForm">
        SELECT ID,Type,Amount FROM (
        SELECT
        0 AS ID,
        0 Sequence,
        '总金额' Type,
        ISNULL(SUM(tp.Amount),0)Amount
        FROM TB_SaleBillPay tp WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t ON t.ID=tp.SaleBillID
        WHERE t.BillStatus='20' AND t.BillType!='20' AND t.EntityID = #{EntityID}
        <if test="StartDate != null and EndDate != null ">
            AND t.BillDate &gt;= #{StartDate} AND t.BillDate &lt; #{EndDate}
        </if>
        UNION ALL
        SELECT
        tsbp.PayMethodID AS ID,
        tpm.Sequence,
        tpm.Name Type,
        ISNULL(SUM(tsbp.Amount),0)Amount
        FROM TB_SaleBillPay tsbp WITH(NOLOCK)
        LEFT JOIN TB_SaleBill t WITH(NOLOCK) ON t.ID = tsbp .SaleBillID
        LEFT JOIN TB_PayMethod tpm WITH(NOLOCK) ON tpm.ID = tsbp.PayMethodID
        WHERE t.BillStatus='20' AND t.BillType!='20' AND t.EntityID = #{EntityID}
        <if test="StartDate != null and EndDate != null ">
            AND t.BillDate &gt;= #{StartDate} AND t.BillDate &lt; #{EndDate}
        </if>
        GROUP BY tsbp.PayMethodID,tpm.Name,tpm.Sequence
        )T ORDER BY Sequence
    </select>

</mapper>