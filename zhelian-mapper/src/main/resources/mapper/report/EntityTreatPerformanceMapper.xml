<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http//:mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.report.EntityTreatPerformanceMapper">


    <select id="getEntityTreatPerformance"
            resultType="com.zhelian.model.report.form.output.EntityTreatPerformanceDetailOutputForm">
        SELECT TreatBillID,CONVERT(varchar(100), BillDate, 120) AS
        BillDate,BillType,EntityName,BillEntityName,ISNULL(CustomerName,'散客')CustomerName,Code,PhoneNumber,CustomerLevelName,TreatCardTypeName,CardName,IsLargess,CategoryName,GoodName,Price,Quantity,CardPreferentialAmount
        PreferentialAmount,TotalAmount,PayPerformance,CardPerformance,CardLargessPerformance,LargessPerformance
        FROM (
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        EE.EntityName BillEntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TBPT.IsLargess,
        '项目卡' AS TreatCardTypeName,'项目' AS GoodsTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE TBPT.CardPreferentialAmount*-1 END AS CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS
        CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS
        LargessPerformance

        FROM TB_TreatEntityPerformanceProject TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_TreatBillProject TBPT WITH(NOLOCK) ON TEPC.TreatBillProjectID = TBPT.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_TreatBillProject TBP WITH(NOLOCK) ON TEPC.TreatBillProjectID = TBP.ID
        LEFT JOIN TB_ProjectAccount PA WITH(NOLOCK) ON TBP.ProjectAccountID = PA.ID
        LEFT JOIN TB_Project PAP WITH(NOLOCK) ON PA.ProjectID = PAP.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null ">
                AND TBPT.IsLargess=#{IsLargess}
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        EE.EntityName BillEntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TBPT.IsLargess,
        '项目卡' AS TreatCardTypeName,'项目' AS GoodsTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE TBPT.CardPreferentialAmount*-1 END AS CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS
        CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS
        LargessPerformance

        FROM TB_TreatEntityPerformancePackageCardProject TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_TreatBillProject TBPT WITH(NOLOCK) ON TEPC.TreatBillProjectID = TBPT.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_TreatBillProject TBP WITH(NOLOCK) ON TEPC.TreatBillProjectID = TBP.ID
        LEFT JOIN TB_ProjectAccount PA WITH(NOLOCK) ON TBP.ProjectAccountID = PA.ID
        LEFT JOIN TB_Project PAP WITH(NOLOCK) ON PA.ProjectID = PAP.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null ">
                AND TBPT.IsLargess=#{IsLargess}
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        EE.EntityName BillEntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        0 IsLargess,
        '储值卡' AS TreatCardTypeName,'项目' AS GoodsTypeName,
        SC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE (TBPT.PricePreferentialAmount+TBPT.MemberPreferentialAmount+TBPT.CardPreferentialAmount)*-1 END AS
        CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        0 AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END as CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END as
        CardLargessPerformance,
        0 AS LargessPerformance

        FROM TB_TreatEntityPerformanceSavingCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_TreatBillSavingCard TBPT WITH(NOLOCK) ON TEPC.TreatBillSavingCardID = TBPT.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON TEPC.SavingCardID = SC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null and IsLargess == true ">
                AND 1=0
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        EE.EntityName BillEntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        0 IsLargess,
        '储值卡' AS TreatCardTypeName,'项目' AS GoodsTypeName,
        SC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE (TBPT.PricePreferentialAmount+TBPT.MemberPreferentialAmount+TBPT.CardPreferentialAmount)*-1 END AS
        CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        0 AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END as CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END as
        CardLargessPerformance,
        0 AS LargessPerformance

        FROM TB_TreatEntityPerformancePackageCardSavingCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_TreatBillSavingCard TBPT WITH(NOLOCK) ON TEPC.TreatBillSavingCardID = TBPT.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON TEPC.SavingCardID = SC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null and IsLargess == true ">
                AND 1=0
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        EE.EntityName BillEntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TBPT.IsLargess,
        '时效卡' AS TreatCardTypeName,'项目' AS GoodsTypeName,
        TC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE TBPT.CardPreferentialAmount*-1 END AS CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS
        CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS
        LargessPerformance
        FROM TB_TreatEntityPerformanceTimeCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBillTimeCard TBPT WITH(NOLOCK) ON TEPC.TreatBillTimeCardID = TBPT.ID
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON TEPC.TimeCardID = TC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null ">
                AND TBPT.IsLargess=#{IsLargess}
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        EE.EntityName BillEntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TBPT.IsLargess,
        '时效卡' AS TreatCardTypeName,'项目' AS GoodsTypeName,
        TC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE TBPT.CardPreferentialAmount*-1 END AS CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS
        CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS
        LargessPerformance
        FROM TB_TreatEntityPerformancePackageCardTimeCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBillTimeCard TBPT WITH(NOLOCK) ON TEPC.TreatBillTimeCardID = TBPT.ID
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON TEPC.TimeCardID = TC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null ">
                AND TBPT.IsLargess=#{IsLargess}
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        EE.EntityName BillEntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TBPT.IsLargess,
        '通用次卡' AS TreatCardTypeName,'项目' AS GoodsTypeName,
        GC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE TBPT.CardPreferentialAmount*-1 END AS CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS
        CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS
        LargessPerformance


        FROM TB_TreatEntityPerformanceGeneralCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBillGeneralCard TBPT WITH(NOLOCK) ON TEPC.TreatBillGeneralCardID = TBPT.ID
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON TEPC.GeneralCardID = GC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null ">
                AND TBPT.IsLargess=#{IsLargess}
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        EE.EntityName BillEntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TBPT.IsLargess,
        '通用次卡' AS TreatCardTypeName,'项目' AS GoodsTypeName,
        GC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE TBPT.CardPreferentialAmount*-1 END AS CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS
        CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS
        LargessPerformance


        FROM TB_TreatEntityPerformancePackageCardGeneralCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBillGeneralCard TBPT WITH(NOLOCK) ON TEPC.TreatBillGeneralCardID = TBPT.ID
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON TEPC.GeneralCardID = GC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null ">
                AND TBPT.IsLargess=#{IsLargess}
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        EE.EntityName BillEntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TBPT.IsLargess,
        '产品卡' AS TreatCardTypeName,'产品' AS GoodsTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE TBPT.CardPreferentialAmount*-1 END AS CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS
        CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS
        LargessPerformance


        FROM TB_TreatEntityPerformanceProduct TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBillProduct TBPT WITH(NOLOCK) ON TEPC.TreatBillProductID = TBPT.ID
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON TEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory SubPC WITH(NOLOCK) ON P.PCategoryID = SubPC.ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_TreatBillProduct TBP WITH(NOLOCK) ON TEPC.TreatBillProductID = TBP.ID
        LEFT JOIN TB_ProductAccount PA WITH(NOLOCK) ON TBP.ProductAccountID = PA.ID
        LEFT JOIN TE_Product PAP WITH(NOLOCK) ON PA.ProductID = PAP.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null ">
                AND TBPT.IsLargess=#{IsLargess}
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.PCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        EE.EntityName BillEntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TBPT.IsLargess,
        '产品卡' AS TreatCardTypeName,'产品' AS GoodsTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE TBPT.CardPreferentialAmount*-1 END AS CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS
        CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS
        LargessPerformance

        FROM TB_TreatEntityPerformancePackageCardProduct TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBillProduct TBPT WITH(NOLOCK) ON TEPC.TreatBillProductID = TBPT.ID
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON TEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory SubPC WITH(NOLOCK) ON P.PCategoryID = SubPC.ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_TreatBillProduct TBP WITH(NOLOCK) ON TEPC.TreatBillProductID = TBP.ID
        LEFT JOIN TB_ProductAccount PA WITH(NOLOCK) ON TBP.ProductAccountID = PA.ID
        LEFT JOIN TE_Product PAP WITH(NOLOCK) ON PA.ProductID = PAP.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null ">
                AND TBPT.IsLargess=#{IsLargess}
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.PCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        ) T
        <where>
            <if test="TreatCardTypeName != null and TreatCardTypeName != '' ">
                and TreatCardTypeName = #{TreatCardTypeName}
            </if>
            <if test="GoodsTypeName != null and GoodsTypeName != '' ">
                and GoodsTypeName = #{GoodsTypeName}
            </if>
        </where>
        ORDER BY BillDate DESC
    </select>
    <select id="getEntityTreatPerformanceSum"
            resultType="com.zhelian.model.report.form.output.EntityTreatPerformanceSumOutputForm">
        SELECT
        ISNULL(SUM(CardPreferentialAmount),0) PreferentialAmount,
        ISNULL(SUM(TotalAmount),0) TotalAmount,
        ISNULL(SUM(PayPerformance),0) PayPerformance,
        ISNULL(SUM(CardPerformance),0) CardPerformance,
        ISNULL(SUM(CardLargessPerformance),0) CardLargessPerformance,
        ISNULL(SUM(LargessPerformance),0) LargessPerformance
        FROM (
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TBPT.IsLargess,
        '项目卡' AS TreatCardTypeName,'项目' AS GoodsTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE TBPT.CardPreferentialAmount*-1 END AS CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS
        CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS
        LargessPerformance

        FROM TB_TreatEntityPerformanceProject TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_TreatBillProject TBPT WITH(NOLOCK) ON TEPC.TreatBillProjectID = TBPT.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_TreatBillProject TBP WITH(NOLOCK) ON TEPC.TreatBillProjectID = TBP.ID
        LEFT JOIN TB_ProjectAccount PA WITH(NOLOCK) ON TBP.ProjectAccountID = PA.ID
        LEFT JOIN TB_Project PAP WITH(NOLOCK) ON PA.ProjectID = PAP.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null ">
                AND TBPT.IsLargess=#{IsLargess}
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TBPT.IsLargess,
        '项目卡' AS TreatCardTypeName,'项目' AS GoodsTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE TBPT.CardPreferentialAmount*-1 END AS CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS
        CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS
        LargessPerformance

        FROM TB_TreatEntityPerformancePackageCardProject TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_TreatBillProject TBPT WITH(NOLOCK) ON TEPC.TreatBillProjectID = TBPT.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_TreatBillProject TBP WITH(NOLOCK) ON TEPC.TreatBillProjectID = TBP.ID
        LEFT JOIN TB_ProjectAccount PA WITH(NOLOCK) ON TBP.ProjectAccountID = PA.ID
        LEFT JOIN TB_Project PAP WITH(NOLOCK) ON PA.ProjectID = PAP.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null ">
                AND TBPT.IsLargess=#{IsLargess}
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        0 IsLargess,
        '储值卡' AS TreatCardTypeName,'项目' AS GoodsTypeName,
        SC.Name AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE (TBPT.PricePreferentialAmount+TBPT.MemberPreferentialAmount+TBPT.CardPreferentialAmount)*-1 END AS
        CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        0 AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END as CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END as
        CardLargessPerformance,
        0 AS LargessPerformance

        FROM TB_TreatEntityPerformanceSavingCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_TreatBillSavingCard TBPT WITH(NOLOCK) ON TEPC.TreatBillSavingCardID = TBPT.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON TEPC.SavingCardID = SC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null and IsLargess == true ">
                AND 1=0
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        0 IsLargess,
        '储值卡' AS TreatCardTypeName,'项目' AS GoodsTypeName,
        SC.Name AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE (TBPT.PricePreferentialAmount+TBPT.MemberPreferentialAmount+TBPT.CardPreferentialAmount)*-1 END AS
        CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        0 AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END as CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END as
        CardLargessPerformance,
        0 AS LargessPerformance

        FROM TB_TreatEntityPerformancePackageCardSavingCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_TreatBillSavingCard TBPT WITH(NOLOCK) ON TEPC.TreatBillSavingCardID = TBPT.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON TEPC.SavingCardID = SC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null and IsLargess == true ">
                AND 1=0
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TBPT.IsLargess,
        '时效卡' AS TreatCardTypeName,'项目' AS GoodsTypeName,
        TC.Name AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE TBPT.CardPreferentialAmount*-1 END AS CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS
        CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS
        LargessPerformance
        FROM TB_TreatEntityPerformanceTimeCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBillTimeCard TBPT WITH(NOLOCK) ON TEPC.TreatBillTimeCardID = TBPT.ID
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON TEPC.TimeCardID = TC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null ">
                AND TBPT.IsLargess=#{IsLargess}
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TBPT.IsLargess,
        '时效卡' AS TreatCardTypeName,'项目' AS GoodsTypeName,
        TC.Name AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE TBPT.CardPreferentialAmount*-1 END AS CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS
        CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS
        LargessPerformance
        FROM TB_TreatEntityPerformancePackageCardTimeCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBillTimeCard TBPT WITH(NOLOCK) ON TEPC.TreatBillTimeCardID = TBPT.ID
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON TEPC.TimeCardID = TC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null ">
                AND TBPT.IsLargess=#{IsLargess}
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TBPT.IsLargess,
        '通用次卡' AS TreatCardTypeName,'项目' AS GoodsTypeName,
        GC.Name AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE TBPT.CardPreferentialAmount*-1 END AS CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS
        CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS
        LargessPerformance


        FROM TB_TreatEntityPerformanceGeneralCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBillGeneralCard TBPT WITH(NOLOCK) ON TEPC.TreatBillGeneralCardID = TBPT.ID
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON TEPC.GeneralCardID = GC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null ">
                AND TBPT.IsLargess=#{IsLargess}
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TBPT.IsLargess,
        '通用次卡' AS TreatCardTypeName,'项目' AS GoodsTypeName,
        GC.Name AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE TBPT.CardPreferentialAmount*-1 END AS CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS
        CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS
        LargessPerformance


        FROM TB_TreatEntityPerformancePackageCardGeneralCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBillGeneralCard TBPT WITH(NOLOCK) ON TEPC.TreatBillGeneralCardID = TBPT.ID
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON TEPC.GeneralCardID = GC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null ">
                AND TBPT.IsLargess=#{IsLargess}
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TBPT.IsLargess,
        '产品卡' AS TreatCardTypeName,'产品' AS GoodsTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE TBPT.CardPreferentialAmount*-1 END AS CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS
        CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS
        LargessPerformance


        FROM TB_TreatEntityPerformanceProduct TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBillProduct TBPT WITH(NOLOCK) ON TEPC.TreatBillProductID = TBPT.ID
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON TEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory SubPC WITH(NOLOCK) ON P.PCategoryID = SubPC.ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_TreatBillProduct TBP WITH(NOLOCK) ON TEPC.TreatBillProductID = TBP.ID
        LEFT JOIN TB_ProductAccount PA WITH(NOLOCK) ON TBP.ProductAccountID = PA.ID
        LEFT JOIN TE_Product PAP WITH(NOLOCK) ON PA.ProductID = PAP.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null ">
                AND TBPT.IsLargess=#{IsLargess}
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.PCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TBPT.IsLargess,
        '产品卡' AS TreatCardTypeName,'产品' AS GoodsTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        PC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE TBPT.CardPreferentialAmount*-1 END AS CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS
        CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS
        LargessPerformance

        FROM TB_TreatEntityPerformancePackageCardProduct TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBillProduct TBPT WITH(NOLOCK) ON TEPC.TreatBillProductID = TBPT.ID
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON TEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory SubPC WITH(NOLOCK) ON P.PCategoryID = SubPC.ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_TreatBillProduct TBP WITH(NOLOCK) ON TEPC.TreatBillProductID = TBP.ID
        LEFT JOIN TB_ProductAccount PA WITH(NOLOCK) ON TBP.ProductAccountID = PA.ID
        LEFT JOIN TE_Product PAP WITH(NOLOCK) ON PA.ProductID = PAP.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null ">
                AND TBPT.IsLargess=#{IsLargess}
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.PCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        ) T
        <where>
            <if test="TreatCardTypeName != null and TreatCardTypeName != '' ">
                AND TreatCardTypeName = #{TreatCardTypeName}
            </if>
            <if test="GoodsTypeName != null and GoodsTypeName != '' ">
                AND GoodsTypeName = #{GoodsTypeName}
            </if>
        </where>

    </select>
    <select id="getEntityTreatPerformanceEasyExcel"
            resultType="com.zhelian.model.report.form.output.EntityTreatPerformanceDetailOutputEasyExcelForm">
        SELECT TreatBillID as treatBillID,CONVERT(varchar(100), BillDate, 120) AS billDate,
        CASE WHEN BillType= '10' THEN '消耗单' WHEN BillType= '20' THEN '消耗退单' END billType,
        EntityName as entityName,BillEntityName as billEntityName,ISNULL(CustomerName,'散客')customerName,Code,REPLACE(PhoneNumber , SUBSTRING (PhoneNumber,4,4), '****') phoneNumber,CustomerLevelName as customerLevelName,TreatCardTypeName treatCardTypeName,CardName as cardName,CASE WHEN
        IsLargess!=1 THEN '否' ELSE '是' END isLargess,CategoryName as categoryName,GoodName as goodName,Price as price,Quantity as quantity,CardPreferentialAmount
         as preferentialAmount,TotalAmount as totalAmount,PayPerformance as payPerformance,CardPerformance as cardPerformance,CardLargessPerformance as cardLargessPerformance,LargessPerformance as largessPerformance
        FROM (
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        EE.EntityName BillEntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TBPT.IsLargess,
        '项目卡' AS TreatCardTypeName,'项目' AS GoodsTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE TBPT.CardPreferentialAmount*-1 END AS CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS
        CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS
        LargessPerformance

        FROM TB_TreatEntityPerformanceProject TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_TreatBillProject TBPT WITH(NOLOCK) ON TEPC.TreatBillProjectID = TBPT.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_TreatBillProject TBP WITH(NOLOCK) ON TEPC.TreatBillProjectID = TBP.ID
        LEFT JOIN TB_ProjectAccount PA WITH(NOLOCK) ON TBP.ProjectAccountID = PA.ID
        LEFT JOIN TB_Project PAP WITH(NOLOCK) ON PA.ProjectID = PAP.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>
            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null ">
                AND TBPT.IsLargess=#{IsLargess}
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        EE.EntityName BillEntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TBPT.IsLargess,
        '项目卡' AS TreatCardTypeName,'项目' AS GoodsTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE TBPT.CardPreferentialAmount*-1 END AS CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS
        CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS
        LargessPerformance

        FROM TB_TreatEntityPerformancePackageCardProject TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_TreatBillProject TBPT WITH(NOLOCK) ON TEPC.TreatBillProjectID = TBPT.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_TreatBillProject TBP WITH(NOLOCK) ON TEPC.TreatBillProjectID = TBP.ID
        LEFT JOIN TB_ProjectAccount PA WITH(NOLOCK) ON TBP.ProjectAccountID = PA.ID
        LEFT JOIN TB_Project PAP WITH(NOLOCK) ON PA.ProjectID = PAP.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null ">
                AND TBPT.IsLargess=#{IsLargess}
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        EE.EntityName BillEntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        0 IsLargess,
        '储值卡' AS TreatCardTypeName,'项目' AS GoodsTypeName,
        SC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE (TBPT.PricePreferentialAmount+TBPT.MemberPreferentialAmount+TBPT.CardPreferentialAmount)*-1 END AS
        CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        0 AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END as CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END as
        CardLargessPerformance,
        0 AS LargessPerformance

        FROM TB_TreatEntityPerformanceSavingCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_TreatBillSavingCard TBPT WITH(NOLOCK) ON TEPC.TreatBillSavingCardID = TBPT.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON TEPC.SavingCardID = SC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null and IsLargess == true ">
                AND 1=0
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        EE.EntityName BillEntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        0 IsLargess,
        '储值卡' AS TreatCardTypeName,'项目' AS GoodsTypeName,
        SC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE (TBPT.PricePreferentialAmount+TBPT.MemberPreferentialAmount+TBPT.CardPreferentialAmount)*-1 END AS
        CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        0 AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END as CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END as
        CardLargessPerformance,
        0 AS LargessPerformance

        FROM TB_TreatEntityPerformancePackageCardSavingCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TB_TreatBillSavingCard TBPT WITH(NOLOCK) ON TEPC.TreatBillSavingCardID = TBPT.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_SavingCard SC WITH(NOLOCK) ON TEPC.SavingCardID = SC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null and IsLargess == true ">
                AND 1=0
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        EE.EntityName BillEntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TBPT.IsLargess,
        '时效卡' AS TreatCardTypeName,'项目' AS GoodsTypeName,
        TC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE TBPT.CardPreferentialAmount*-1 END AS CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS
        CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS
        LargessPerformance
        FROM TB_TreatEntityPerformanceTimeCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBillTimeCard TBPT WITH(NOLOCK) ON TEPC.TreatBillTimeCardID = TBPT.ID
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON TEPC.TimeCardID = TC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null ">
                AND TBPT.IsLargess=#{IsLargess}
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        EE.EntityName BillEntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TBPT.IsLargess,
        '时效卡' AS TreatCardTypeName,'项目' AS GoodsTypeName,
        TC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE TBPT.CardPreferentialAmount*-1 END AS CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS
        CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS
        LargessPerformance
        FROM TB_TreatEntityPerformancePackageCardTimeCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBillTimeCard TBPT WITH(NOLOCK) ON TEPC.TreatBillTimeCardID = TBPT.ID
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_TimeCard TC WITH(NOLOCK) ON TEPC.TimeCardID = TC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null ">
                AND TBPT.IsLargess=#{IsLargess}
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        EE.EntityName BillEntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TBPT.IsLargess,
        '通用次卡' AS TreatCardTypeName,'项目' AS GoodsTypeName,
        GC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE TBPT.CardPreferentialAmount*-1 END AS CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS
        CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS
        LargessPerformance


        FROM TB_TreatEntityPerformanceGeneralCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBillGeneralCard TBPT WITH(NOLOCK) ON TEPC.TreatBillGeneralCardID = TBPT.ID
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON TEPC.GeneralCardID = GC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null ">
                AND TBPT.IsLargess=#{IsLargess}
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        EE.EntityName BillEntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TBPT.IsLargess,
        '通用次卡' AS TreatCardTypeName,'项目' AS GoodsTypeName,
        GC.Name AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE TBPT.CardPreferentialAmount*-1 END AS CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS
        CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS
        LargessPerformance


        FROM TB_TreatEntityPerformancePackageCardGeneralCard TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBillGeneralCard TBPT WITH(NOLOCK) ON TEPC.TreatBillGeneralCardID = TBPT.ID
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TB_Project P WITH(NOLOCK) ON TEPC.ProjectID = P.ID
        LEFT JOIN TB_ProjectCategory SubPC WITH(NOLOCK) ON P.ProjectCategoryID = SubPC.ID
        LEFT JOIN TB_ProjectCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_GeneralCard GC WITH(NOLOCK) ON TEPC.GeneralCardID = GC.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null ">
                AND TBPT.IsLargess=#{IsLargess}
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.ProjectCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        EE.EntityName BillEntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TBPT.IsLargess,
        '产品卡' AS TreatCardTypeName,'产品' AS GoodsTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE TBPT.CardPreferentialAmount*-1 END AS CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS
        CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS
        LargessPerformance


        FROM TB_TreatEntityPerformanceProduct TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBillProduct TBPT WITH(NOLOCK) ON TEPC.TreatBillProductID = TBPT.ID
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON TEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory SubPC WITH(NOLOCK) ON P.PCategoryID = SubPC.ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_TreatBillProduct TBP WITH(NOLOCK) ON TEPC.TreatBillProductID = TBP.ID
        LEFT JOIN TB_ProductAccount PA WITH(NOLOCK) ON TBP.ProductAccountID = PA.ID
        LEFT JOIN TE_Product PAP WITH(NOLOCK) ON PA.ProductID = PAP.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null ">
                AND TBPT.IsLargess=#{IsLargess}
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.PCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        UNION ALL
        SELECT
        TEPC.TreatBillID,
        TEPC.BillDate,
        E.EntityName,
        EE.EntityName BillEntityName,
        TCR.Name CustomerName,
        TCR.Code,
        TCR.PhoneNumber,
        TCL.Name CustomerLevelName,
        TBPT.IsLargess,
        '产品卡' AS TreatCardTypeName,'产品' AS GoodsTypeName,
        (PAP.Name + ' ' + CONVERT(nvarchar(200), PA.Quantity)+ '次卡') AS CardName,
        SubPC.Name AS CategoryName,
        P.Name AS GoodName,
        TB.BillType,
        TBPT.Price,
        TBPT.Quantity,
        CASE WHEN TB.BillType = '20' THEN 0 ELSE TBPT.CardPreferentialAmount*-1 END AS CardPreferentialAmount,
        CASE WHEN TB.BillType = '20' THEN TBPT.TotalAmount*-1 ELSE TBPT.TotalAmount END AS TotalAmount,
        CASE WHEN TB.BillType = '20' THEN TEPC.PayPerformance*-1 ELSE TEPC.PayPerformance END AS PayPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardPerformance*-1 ELSE TEPC.CardPerformance END AS CardPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.CardLargessPerformance*-1 ELSE TEPC.CardLargessPerformance END AS
        CardLargessPerformance,
        CASE WHEN TB.BillType = '20' THEN TEPC.LargessPerformance*-1 ELSE TEPC.LargessPerformance END AS
        LargessPerformance

        FROM TB_TreatEntityPerformancePackageCardProduct TEPC WITH(NOLOCK)
        LEFT JOIN TB_TreatBillProduct TBPT WITH(NOLOCK) ON TEPC.TreatBillProductID = TBPT.ID
        LEFT JOIN TB_TreatBill TB WITH(NOLOCK) ON TEPC.TreatBillID = TB.ID
        LEFT JOIN TK_Entity E WITH(NOLOCK) ON TEPC.EntityID = E.ID
        LEFT JOIN TK_Entity EE WITH(NOLOCK) ON TB.EntityID = EE.ID
        LEFT JOIN TE_Product P WITH(NOLOCK) ON TEPC.ProductID = P.ID
        LEFT JOIN TE_ProductCategory SubPC WITH(NOLOCK) ON P.PCategoryID = SubPC.ID
        LEFT JOIN TE_ProductCategory PC WITH(NOLOCK) ON SubPC.ParentID = PC.ID
        LEFT JOIN TB_TreatBillProduct TBP WITH(NOLOCK) ON TEPC.TreatBillProductID = TBP.ID
        LEFT JOIN TB_ProductAccount PA WITH(NOLOCK) ON TBP.ProductAccountID = PA.ID
        LEFT JOIN TE_Product PAP WITH(NOLOCK) ON PA.ProductID = PAP.ID
        LEFT JOIN TB_Customer TCR WITH(NOLOCK) ON TB.CustomerID = TCR.ID
        LEFT JOIN TB_CustomerLevel TCL WITH(NOLOCK) ON TCL.ID = TCR.CustomerLevelID
        <where>
            <choose>
                <when test="EntityID!=null">
                    TEPC.EntityID = #{EntityID}
                </when>
                <otherwise>
                    EXISTS (
                    SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
                    WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = TEPC.EntityID
                    )
                </otherwise>
            </choose>
            <if test="StartDate != null and EndDate != null ">
                AND TEPC.BillDate &gt;= #{StartDate} AND TEPC.BillDate &lt; #{EndDate}
            </if>

            <if test="CustomerName != null and CustomerName != ''">
                AND ( TCR.Name like '%'+ #{CustomerName} +'%' or TCR.PhoneNumber like '%'+ #{CustomerName} +'%' or
                TCR.Code like '%'+ #{CustomerName} +'%')
            </if>
            <if test="BillID != null and BillID != ''">
                AND TEPC.TreatBillID =#{BillID}
            </if>
            <if test="IsLargess != null ">
                AND TBPT.IsLargess=#{IsLargess}
            </if>
            <if test="BillType != null and BillType != ''">
                AND TB.BillType=#{BillType}
            </if>
            <if test=" CategoryID != null ">
                AND (P.PCategoryID=#{CategoryID} OR SubPC.ParentID=#{CategoryID})
            </if>
            <if test="CustomerLevelID != null ">
                AND TCR.CustomerLevelID = #{CustomerLevelID}
            </if>
        </where>
        ) T
        <where>
            <if test="TreatCardTypeName != null and TreatCardTypeName != '' ">
                AND TreatCardTypeName = #{TreatCardTypeName}
            </if>
            <if test="GoodsTypeName != null and GoodsTypeName != '' ">
                AND GoodsTypeName = #{GoodsTypeName}
            </if>
        </where>
        ORDER BY BillDate DESC
    </select>
</mapper>