<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.report.CustomerStatementMapper">
    <select id="getCustomerDynamicSaleAndDynamicConsume"
            resultType="com.zhelian.model.report.form.output.CustomerDynamicSaleAndDynamicConsumeDetailStatementForm">
        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        tcl.Name LevelName,
        tcs.Name SourceName,
        tc.Birthday,
        CONVERT(VARCHAR(19),tc.CreatedOn,120) CreatedOn,
        te.EntityName,
        T.TotalSaleQuantity,
        T.TotalSaleAmount,
        CASE WHEN T.TotalSaleQuantity=0 THEN 0 ELSE ISNULL(CAST(T.TotalSaleAmount/T.TotalSaleQuantity as decimal(38,
        2)),0) END TotalSalePrice,
        T.CurrentSaleQuantity,
        T.Current<PERSON>,
        CASE WHEN T.CurrentSaleAmount=0 THEN 0 ELSE ISNULL(CAST(T.CurrentSaleQuantity/T.CurrentSaleAmount as decimal(38,
        2)),0) END CurrentSalePrice,
        T.CurrentConsumeProjectCount,
        T.CurrentConsumeProjectQuantity,
        T.CurrentConsumeProjectAmount,
        CASE WHEN T.CurrentConsumeProjectQuantity=0 THEN 0 ELSE
        ISNULL(CAST(T.CurrentConsumeProjectAmount/T.CurrentConsumeProjectQuantity as decimal(38, 2)),0) END
        CurrentConsumeProjectPrice,
        (SELECT TOP 1 CONVERT(VARCHAR(10),ts.BillDate,120) BillDate FROM TB_SaleBill ts WITH(NOLOCK) WHERE
        ts.BillStatus='20' AND ts.BillType!='20' AND ts.CustomerID =tc.ID AND ts.PayAmount>0 ORDER BY ts.BillDate
        DESC)LastSaleBillDate,
        ISNULL(DATEDIFF(DAY, (SELECT TOP 1 CONVERT(VARCHAR(10),ts.BillDate,120) BillDate FROM TB_SaleBill ts
        WITH(NOLOCK) WHERE ts.BillStatus='20' AND ts.BillType!='20' AND ts.CustomerID =tc.ID AND ts.PayAmount>0 ORDER BY
        ts.BillDate DESC), GETDATE()),0) UnSaleDay,
        (SELECT TOP 1 BillDate FROM
        (
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillProject ts WITH(NOLOCK) JOIN
        TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        UNION ALL
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillGeneralCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        UNION ALL
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillTimeCard ts WITH(NOLOCK) JOIN
        TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        UNION ALL
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillSavingCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        )T where T.CustomerID=tc.ID
        ORDER BY BillDate DESC)LastConsumeBillDate,
        ISNULL(DATEDIFF(DAY, (SELECT TOP 1 BillDate FROM
        (
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillProject ts WITH(NOLOCK) JOIN
        TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        UNION ALL
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillGeneralCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        UNION ALL
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillTimeCard ts WITH(NOLOCK) JOIN
        TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        UNION ALL
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillSavingCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        )T where T.CustomerID =tc.ID
        ORDER BY BillDate DESC), GETDATE()),0) UnConsumeDay,
        T.ToShopQuantity
        FROM
        TB_Customer tc WITH(NOLOCK)
        LEFT JOIN TB_CustomerEntity tce WITH(NOLOCK) ON tce.CustomerID=tc.ID AND tce.IsBelongEntity=1
        LEFT JOIN TK_Entity te WITH(NOLOCK) ON te.ID=tce.EntityID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN
        (
        SELECT
        T.CustomerID,
        SUM(TotalSaleQuantity) TotalSaleQuantity,
        SUM(TotalSaleAmount) TotalSaleAmount,
        SUM(CurrentSaleQuantity) CurrentSaleQuantity,
        SUM(CurrentSaleAmount) CurrentSaleAmount,
        SUM(CurrentConsumeProjectCount) CurrentConsumeProjectCount,
        SUM(CurrentConsumeProjectQuantity) CurrentConsumeProjectQuantity,
        SUM(CurrentConsumeProjectAmount) CurrentConsumeProjectAmount,
        SUM(ToShopQuantity) ToShopQuantity
        FROM
        (
        SELECT
        T.CustomerID,
        Count(*) TotalSaleQuantity,
        0 TotalSaleAmount,
        0 CurrentSaleQuantity,
        0 CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        0 ToShopQuantity
        FROM(
        SELECT
        DISTINCT
        ts.CustomerID,
        CONVERT(VARCHAR(10),ts.BillDate,120) BillDate
        FROM
        TB_SaleBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND ts.BillType!='20' AND ts.PayAmount>0  AND ts.CustomerID
        IS NOT NULL
        )T
        GROUP BY T.CustomerID
        UNION ALL
        SELECT
        ts.CustomerID,
        0 TotalSaleQuantity,
        ISNULL(SUM(ISNULL(CASE WHEN ts.BillType = '20' THEN ts.PayAmount*-1 ELSE ts.PayAmount END,0)),0)
        TotalSaleAmount,
        0 CurrentSaleQuantity,
        0 CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        0 ToShopQuantity
        FROM
        TB_SaleBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND ts.PayAmount>0  AND ts.CustomerID
        IS NOT NULL
        GROUP BY ts.CustomerID
        UNION ALL

        SELECT
        T.CustomerID,
        0 TotalSaleQuantity,
        0 TotalSaleAmount,
        Count(*) CurrentSaleQuantity,
        0 CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        0 ToShopQuantity
        FROM(
        SELECT
        DISTINCT
        ts.CustomerID,
        CONVERT(VARCHAR(10),ts.BillDate,120) BillDate
        FROM
        TB_SaleBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND ts.BillType!='20' AND ts.PayAmount>0  AND ts.BillDate >= #{StartTime} AND ts.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        )T
        GROUP BY T.CustomerID
        UNION ALL
        SELECT
        ts.CustomerID,
        0 TotalSaleQuantity,
        0 TotalSaleAmount,
        0 CurrentSaleQuantity,
        ISNULL(SUM(ISNULL(CASE WHEN ts.BillType = '20' THEN ts.PayAmount*-1 ELSE ts.PayAmount END,0)),0)
        CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        0 ToShopQuantity
        FROM
        TB_SaleBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20'  AND ts.PayAmount>0  AND ts.BillDate >= #{StartTime} AND ts.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY ts.CustomerID
        UNION ALL
        SELECT
        T.CustomerID,
        0 TotalSaleQuantity,
        0 TotalSaleAmount,
        0 CurrentSaleQuantity,
        0 CurrentSaleAmount,
        Count(*) CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        0 ToShopQuantity

        FROM(
        SELECT
        DISTINCT
        ts.CustomerID,
        CONVERT(VARCHAR(10),ts.BillDate,120) BillDate
        FROM
        TB_TreatBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND ts.BillType!='20'
        AND ts.BillDate >= #{StartTime} AND ts.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        AND EXISTS
        (
        SELECT 1 FROM
        (
        SELECT TreatBillID FROM TB_TreatBillProject
        UNION
        SELECT TreatBillID FROM TB_TreatBillGeneralCard
        UNION
        SELECT TreatBillID FROM TB_TreatBillTimeCard
        UNION
        SELECT TreatBillID FROM TB_TreatBillSavingCard
        )T WHERE ts.ID=T.TreatBillID
        )
        )T
        GROUP BY T.CustomerID
        UNION ALL
        SELECT
        T.CustomerID,
        0 TotalSaleQuantity,
        0 TotalSaleAmount,
        0 CurrentSaleQuantity,
        0 CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        SUM(Amount) CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        0 ToShopQuantity
        FROM
        (
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.Quantity)*-1 ELSE ts.Quantity END,0)),0) AS Amount
        FROM
        TB_TreatBillProject ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID
        UNION ALL
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.Quantity)*-1 ELSE ts.Quantity END,0)),0) AS Amount
        FROM
        TB_TreatBillGeneralCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID
        UNION ALL
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.Quantity)*-1 ELSE ts.Quantity END,0)),0) AS Amount
        FROM
        TB_TreatBillTimeCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID
        UNION ALL
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.Quantity)*-1 ELSE ts.Quantity END,0)),0) AS Amount
        FROM
        TB_TreatBillSavingCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID

        )T
        GROUP BY T.CustomerID
        UNION ALL
        SELECT
        T.CustomerID,
        0 TotalSaleQuantity,
        0 TotalSaleAmount,
        0 CurrentSaleQuantity,
        0 CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        SUM(Amount) CurrentConsumeProjectAmount,
        0 ToShopQuantity
        FROM
        (
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.PayAmount+ts.CardDeductionAmount)*-1 ELSE
        ts.PayAmount+ts.CardDeductionAmount END,0)),0) AS Amount
        FROM
        TB_TreatBillProject ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID
        UNION ALL
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.PayAmount+ts.CardDeductionAmount)*-1 ELSE
        ts.PayAmount+ts.CardDeductionAmount END,0)),0) AS Amount
        FROM
        TB_TreatBillGeneralCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID
        UNION ALL
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.PayAmount+ts.CardDeductionAmount)*-1 ELSE
        ts.PayAmount+ts.CardDeductionAmount END,0)),0) AS Amount
        FROM
        TB_TreatBillTimeCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID
        UNION ALL
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.CardTreatAmount)*-1 ELSE ts.CardTreatAmount END,0)),0) AS
        Amount
        FROM
        TB_TreatBillSavingCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID

        )T
        GROUP BY T.CustomerID
        UNION ALL
        SELECT
        T.CustomerID,
        0 TotalSaleQuantity,
        0 TotalSaleAmount,
        0 CurrentSaleQuantity,
        0 CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        Count(*) ToShopQuantity

        FROM
        (
        SELECT
        ts.CustomerID,
        CONVERT(VARCHAR(10),ts.BillDate,120) BillDate
        FROM
        TB_SaleBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND ts.BillType!='20' and ts.BillDate &gt;=#{StartTime} AND ts.BillDate &lt; DATEADD(DAY, 1, #{EndTime})
        UNION
        SELECT
        ts.CustomerID,
        CONVERT(VARCHAR(10),ts.BillDate,120) BillDate
        FROM
        TB_TreatBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND ts.BillType!='20' and ts.BillDate &gt;=#{StartTime} AND ts.BillDate &lt; DATEADD(DAY, 1, #{EndTime})

        )T
        GROUP BY T.CustomerID
        )T
        GROUP BY T.CustomerID
        )T ON T.CustomerID =tc.ID
        <where>
            EXISTS (
            SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
            WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tce.EntityID
            ) AND EXISTS(
            SELECT 1 FROM (
            SELECT CustomerID FROM TB_SaleBill t WHERE t.BillStatus='20' AND t.BillType!='20' AND t.CustomerID IS NOT
            NULL
            UNION ALL
            SELECT CustomerID FROM TB_TreatBill t WHERE t.BillStatus='20' AND t.BillType!='20' AND t.CustomerID IS NOT
            NULL
            )ttt WHERE ttt.CustomerID=tc.ID
            )
            <if test="Name != null and Name != ''">
                AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+
                #{Name} +'%')
            </if>
            <if test="LevelID != null and LevelID != ''">
                AND tc.CustomerLevelID =#{LevelID}
            </if>
            <if test="SourceID != null and SourceID != ''">
                AND tc.CustomerSourceID =#{SourceID}
            </if>
            <if test="StartCreatedOn != null and StartCreatedOn != '' and EndCreatedOn != null and EndCreatedOn != ''">
                AND tc.CreatedOn &gt;= #{StartCreatedOn} AND tc.CreatedOn &lt; DATEADD(DAY,1,#{EndCreatedOn})
            </if>
            <if test="EntityID != null and EntityID != ''">
                AND tce.EntityID=#{EntityID}
            </if>
        </where>
        ORDER BY tc.Name
    </select>

    <select id="getCustomerDynamicSaleAndDynamicConsumeSum"
            resultType="com.zhelian.model.report.form.output.CustomerDynamicSaleAndDynamicConsumeSumStatementForm">
        SELECT
        ISNULL(SUM(TotalSaleQuantity), 0) TotalSaleQuantity,
        ISNULL(SUM(TotalSaleAmount), 0) TotalSaleAmount,
        ISNULL(SUM(TotalSalePrice), 0) TotalSalePrice,
        ISNULL(SUM(CurrentSaleQuantity), 0) CurrentSaleQuantity,
        ISNULL(SUM(CurrentSaleAmount), 0) CurrentSaleAmount,
        ISNULL(SUM(CurrentSalePrice), 0) CurrentSalePrice,
        ISNULL(SUM(CurrentConsumeProjectCount), 0) CurrentConsumeProjectCount,
        ISNULL(SUM(CurrentConsumeProjectQuantity), 0) CurrentConsumeProjectQuantity,
        ISNULL(SUM(CurrentConsumeProjectAmount), 0) CurrentConsumeProjectAmount,
        ISNULL(SUM(CurrentConsumeProjectPrice), 0) CurrentConsumeProjectPrice,
        ISNULL(SUM(UnSaleDay), 0) UnSaleDay,
        ISNULL(SUM(UnConsumeDay), 0) UnConsumeDay,
        ISNULL(SUM(ToShopQuantity), 0) ToShopQuantity

        FROM
        (
        SELECT
        T.TotalSaleQuantity,
        T.TotalSaleAmount,
        CASE WHEN T.TotalSaleQuantity=0 THEN 0 ELSE ISNULL(CAST(T.TotalSaleAmount/T.TotalSaleQuantity as decimal(38,
        2)),0) END TotalSalePrice,
        T.CurrentSaleQuantity,
        T.CurrentSaleAmount,
        CASE WHEN T.CurrentSaleAmount=0 THEN 0 ELSE ISNULL(CAST(T.CurrentSaleQuantity/T.CurrentSaleAmount as decimal(38,
        2)),0) END CurrentSalePrice,
        T.CurrentConsumeProjectCount,
        T.CurrentConsumeProjectQuantity,
        T.CurrentConsumeProjectAmount,
        CASE WHEN T.CurrentConsumeProjectQuantity=0 THEN 0 ELSE
        ISNULL(CAST(T.CurrentConsumeProjectAmount/T.CurrentConsumeProjectQuantity as decimal(38, 2)),0) END
        CurrentConsumeProjectPrice,
        ISNULL(DATEDIFF(DAY, (SELECT TOP 1 CONVERT(VARCHAR(10),ts.BillDate,120) BillDate FROM TB_SaleBill ts
        WITH(NOLOCK) WHERE ts.BillStatus='20' AND ts.BillType!='20' AND ts.CustomerID =tc.ID AND ts.PayAmount>0 ORDER BY
        ts.BillDate DESC), GETDATE()),0) UnSaleDay,
        ISNULL(DATEDIFF(DAY, (SELECT TOP 1 BillDate FROM
        (
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillProject ts WITH(NOLOCK) JOIN
        TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        UNION ALL
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillGeneralCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        UNION ALL
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillTimeCard ts WITH(NOLOCK) JOIN
        TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        UNION ALL
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillSavingCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        )T  where T.CustomerID =tc.ID
        ORDER BY BillDate DESC), GETDATE()),0) UnConsumeDay,
        T.ToShopQuantity
        FROM
        TB_Customer tc WITH(NOLOCK)
        LEFT JOIN TB_CustomerEntity tce WITH(NOLOCK) ON tce.CustomerID=tc.ID AND tce.IsBelongEntity=1
        LEFT JOIN TK_Entity te WITH(NOLOCK) ON te.ID=tce.EntityID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN
        (
        SELECT
        T.CustomerID,
        SUM(TotalSaleQuantity) TotalSaleQuantity,
        SUM(TotalSaleAmount) TotalSaleAmount,
        SUM(CurrentSaleQuantity) CurrentSaleQuantity,
        SUM(CurrentSaleAmount) CurrentSaleAmount,
        SUM(CurrentConsumeProjectCount) CurrentConsumeProjectCount,
        SUM(CurrentConsumeProjectQuantity) CurrentConsumeProjectQuantity,
        SUM(CurrentConsumeProjectAmount) CurrentConsumeProjectAmount,
        SUM(ToShopQuantity) ToShopQuantity
        FROM
        (
        SELECT
        T.CustomerID,
        Count(*) TotalSaleQuantity,
        0 TotalSaleAmount,
        0 CurrentSaleQuantity,
        0 CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        0 ToShopQuantity
        FROM(
        SELECT
        DISTINCT
        ts.CustomerID,
        CONVERT(VARCHAR(10),ts.BillDate,120) BillDate
        FROM
        TB_SaleBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND ts.BillType!='20' AND ts.PayAmount>0  AND ts.CustomerID
        IS NOT NULL
        )T
        GROUP BY T.CustomerID
        UNION ALL
        SELECT
        ts.CustomerID,
        0 TotalSaleQuantity,
        ISNULL(SUM(ISNULL(CASE WHEN ts.BillType = '20' THEN ts.PayAmount*-1 ELSE ts.PayAmount END,0)),0)
        TotalSaleAmount,
        0 CurrentSaleQuantity,
        0 CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        0 ToShopQuantity
        FROM
        TB_SaleBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND ts.PayAmount>0
        GROUP BY ts.CustomerID
        UNION ALL

        SELECT
        T.CustomerID,
        0 TotalSaleQuantity,
        0 TotalSaleAmount,
        Count(*) CurrentSaleQuantity,
        0 CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        0 ToShopQuantity
        FROM(
        SELECT
        DISTINCT
        ts.CustomerID,
        CONVERT(VARCHAR(10),ts.BillDate,120) BillDate
        FROM
        TB_SaleBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND ts.BillType!='20' AND ts.PayAmount>0  AND ts.BillDate >= #{StartTime} AND ts.BillDate &lt; DATEADD(DAY,1,#{EndTime})

        )T
        GROUP BY T.CustomerID
        UNION ALL
        SELECT
        ts.CustomerID,
        0 TotalSaleQuantity,
        0 TotalSaleAmount,
        0 CurrentSaleQuantity,
        ISNULL(SUM(ISNULL(CASE WHEN ts.BillType = '20' THEN ts.PayAmount*-1 ELSE ts.PayAmount END,0)),0)
        CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        0 ToShopQuantity
        FROM
        TB_SaleBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND ts.BillDate >= #{StartTime} AND ts.BillDate &lt; DATEADD(DAY,1,#{EndTime}) AND ts.PayAmount>0
        GROUP BY ts.CustomerID
        UNION ALL
        SELECT
        T.CustomerID,
        0 TotalSaleQuantity,
        0 TotalSaleAmount,
        0 CurrentSaleQuantity,
        0 CurrentSaleAmount,
        Count(*) CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        0 ToShopQuantity

        FROM(
        SELECT
        DISTINCT
        ts.CustomerID,
        CONVERT(VARCHAR(10),ts.BillDate,120) BillDate
        FROM
        TB_TreatBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND ts.BillType!='20'
        AND ts.BillDate >= #{StartTime} AND ts.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        AND EXISTS
        (
        SELECT 1 FROM
        (
        SELECT TreatBillID FROM TB_TreatBillProject
        UNION
        SELECT TreatBillID FROM TB_TreatBillGeneralCard
        UNION
        SELECT TreatBillID FROM TB_TreatBillTimeCard
        UNION
        SELECT TreatBillID FROM TB_TreatBillSavingCard
        )T WHERE ts.ID=T.TreatBillID
        )
        )T
        GROUP BY T.CustomerID
        UNION ALL
        SELECT
        T.CustomerID,
        0 TotalSaleQuantity,
        0 TotalSaleAmount,
        0 CurrentSaleQuantity,
        0 CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        SUM(Amount) CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        0 ToShopQuantity
        FROM
        (
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.Quantity)*-1 ELSE ts.Quantity END,0)),0) AS Amount
        FROM
        TB_TreatBillProject ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID
        UNION ALL
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.Quantity)*-1 ELSE ts.Quantity END,0)),0) AS Amount
        FROM
        TB_TreatBillGeneralCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID
        UNION ALL
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.Quantity)*-1 ELSE ts.Quantity END,0)),0) AS Amount
        FROM
        TB_TreatBillTimeCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID
        UNION ALL
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.Quantity)*-1 ELSE ts.Quantity END,0)),0) AS Amount
        FROM
        TB_TreatBillSavingCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID

        )T
        GROUP BY T.CustomerID
        UNION ALL
        SELECT
        T.CustomerID,
        0 TotalSaleQuantity,
        0 TotalSaleAmount,
        0 CurrentSaleQuantity,
        0 CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        SUM(Amount) CurrentConsumeProjectAmount,
        0 ToShopQuantity
        FROM
        (
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.PayAmount+ts.CardDeductionAmount)*-1 ELSE
        ts.PayAmount+ts.CardDeductionAmount END,0)),0) AS Amount
        FROM
        TB_TreatBillProject ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID
        UNION ALL
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.PayAmount+ts.CardDeductionAmount)*-1 ELSE
        ts.PayAmount+ts.CardDeductionAmount END,0)),0) AS Amount
        FROM
        TB_TreatBillGeneralCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID
        UNION ALL
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.PayAmount+ts.CardDeductionAmount)*-1 ELSE
        ts.PayAmount+ts.CardDeductionAmount END,0)),0) AS Amount
        FROM
        TB_TreatBillTimeCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID
        UNION ALL
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.CardTreatAmount)*-1 ELSE ts.CardTreatAmount END,0)),0) AS
        Amount
        FROM
        TB_TreatBillSavingCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID

        )T
        GROUP BY T.CustomerID
        UNION ALL
        SELECT
        T.CustomerID,
        0 TotalSaleQuantity,
        0 TotalSaleAmount,
        0 CurrentSaleQuantity,
        0 CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        Count(*) ToShopQuantity

        FROM
        (
        SELECT
        ts.CustomerID,
        CONVERT(VARCHAR(10),ts.BillDate,120) BillDate
        FROM
        TB_SaleBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND ts.BillType!='20'  and ts.BillDate &gt;=#{StartTime} AND ts.BillDate &lt; DATEADD(DAY, 1, #{EndTime})
        UNION
        SELECT
        ts.CustomerID,
        CONVERT(VARCHAR(10),ts.BillDate,120) BillDate
        FROM
        TB_TreatBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND ts.BillType!='20'  and ts.BillDate &gt;=#{StartTime} AND ts.BillDate &lt; DATEADD(DAY, 1, #{EndTime})

        )T
        GROUP BY T.CustomerID
        )T
        GROUP BY T.CustomerID
        )T ON T.CustomerID =tc.ID
        <where>
            EXISTS (
            SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
            WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tce.EntityID
            )
            <if test="Name != null and Name != ''">
                AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+
                #{Name} +'%')
            </if>
            <if test="LevelID != null and LevelID != ''">
                AND tc.CustomerLevelID =#{LevelID}
            </if>
            <if test="SourceID != null and SourceID != ''">
                AND tc.CustomerSourceID =#{SourceID}
            </if>
            <if test="StartCreatedOn != null and StartCreatedOn != '' and EndCreatedOn != null and EndCreatedOn != ''">
                and tc.CreatedOn &gt;=#{StartCreatedOn} AND  tc.CreatedOn &lt; DATEADD(DAY,1,#{EndCreatedOn})
            </if>
            <if test="EntityID != null and EntityID != ''">
                AND tce.EntityID=#{EntityID}
            </if>
        </where>
        )T
    </select>

    <select id="getCustomerDynamicSaleAndDynamicConsumeExcel"
            resultType="com.zhelian.model.report.form.output.CustomerDynamicSaleAndDynamicConsumeDetailStatementExcelForm">
        SELECT
        tc.Name,
        ISNULL(tc.Code,'') Code,
        ISNULL(REPLACE(tc.PhoneNumber , SUBSTRING (tc.PhoneNumber,4,4), '****') ,'') PhoneNumber,
        ISNULL(tcl.Name,'') LevelName,
        ISNULL(tcs.Name,'') SourceName,
        ISNULL(tc.Birthday,'') Birthday,
        CONVERT(VARCHAR(19),tc.CreatedOn,120) CreatedOn,
        te.EntityName,
        ISNULL(T.TotalSaleQuantity,0)TotalSaleQuantity,
        ISNULL(T.TotalSaleAmount,0)TotalSaleAmount,
        CASE WHEN T.TotalSaleQuantity=0 THEN 0 ELSE ISNULL(CAST(T.TotalSaleAmount/T.TotalSaleQuantity as decimal(38,
        2)),0) END TotalSalePrice,
        ISNULL(T.CurrentSaleQuantity,0)CurrentSaleQuantity,
        ISNULL(T.CurrentSaleAmount,0)CurrentSaleAmount,
        CASE WHEN T.CurrentSaleAmount=0 THEN 0 ELSE ISNULL(CAST(T.CurrentSaleQuantity/T.CurrentSaleAmount as decimal(38,
        2)),0) END CurrentSalePrice,
        ISNULL(T.CurrentConsumeProjectCount,0)CurrentConsumeProjectCount,
        ISNULL(T.CurrentConsumeProjectQuantity,0)CurrentConsumeProjectQuantity,
        ISNULL(T.CurrentConsumeProjectAmount,0)CurrentConsumeProjectAmount,
        CASE WHEN T.CurrentConsumeProjectQuantity=0 THEN 0 ELSE
        ISNULL(CAST(T.CurrentConsumeProjectAmount/T.CurrentConsumeProjectQuantity as decimal(38, 2)),0) END
        CurrentConsumeProjectPrice,
        ISNULL((SELECT TOP 1 CONVERT(VARCHAR(10),ts.BillDate,120) BillDate FROM TB_SaleBill ts WITH(NOLOCK) WHERE
        ts.BillStatus='20' AND ts.BillType!='20' AND ts.CustomerID =tc.ID AND ts.PayAmount>0 ORDER BY ts.BillDate
        DESC),'')LastSaleBillDate,
        ISNULL(DATEDIFF(DAY, (SELECT TOP 1 CONVERT(VARCHAR(10),ts.BillDate,120) BillDate FROM TB_SaleBill ts
        WITH(NOLOCK) WHERE ts.BillStatus='20' AND ts.BillType!='20' AND ts.CustomerID =tc.ID AND ts.PayAmount>0 ORDER BY
        ts.BillDate DESC), GETDATE()),0) UnSaleDay,
        ISNULL(( SELECT TOP 1 BillDate FROM
        (
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillProject ts WITH(NOLOCK) JOIN
        TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        UNION ALL
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillGeneralCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        UNION ALL
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillTimeCard ts WITH(NOLOCK) JOIN
        TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        UNION ALL
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillSavingCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        )T where T.CustomerID=tc.ID
        ORDER BY BillDate DESC),'')LastConsumeBillDate,
        ISNULL(DATEDIFF(DAY, (SELECT TOP 1 BillDate FROM
        (
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillProject ts WITH(NOLOCK) JOIN
        TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        UNION ALL
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillGeneralCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        UNION ALL
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillTimeCard ts WITH(NOLOCK) JOIN
        TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        UNION ALL
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillSavingCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        )T where T.CustomerID=tc.ID
        ORDER BY BillDate DESC), GETDATE()),0) UnConsumeDay,
        ISNULL(T.ToShopQuantity,0)ToShopQuantity

        FROM
        TB_Customer tc WITH(NOLOCK)
        LEFT JOIN TB_CustomerEntity tce WITH(NOLOCK) ON tce.CustomerID=tc.ID AND tce.IsBelongEntity=1
        LEFT JOIN TK_Entity te WITH(NOLOCK) ON te.ID=tce.EntityID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN
        (
        SELECT
        T.CustomerID,
        SUM(TotalSaleQuantity) TotalSaleQuantity,
        SUM(TotalSaleAmount) TotalSaleAmount,
        SUM(CurrentSaleQuantity) CurrentSaleQuantity,
        SUM(CurrentSaleAmount) CurrentSaleAmount,
        SUM(CurrentConsumeProjectCount) CurrentConsumeProjectCount,
        SUM(CurrentConsumeProjectQuantity) CurrentConsumeProjectQuantity,
        SUM(CurrentConsumeProjectAmount) CurrentConsumeProjectAmount,
        SUM(ToShopQuantity) ToShopQuantity
        FROM
        (
        SELECT
        T.CustomerID,
        Count(*) TotalSaleQuantity,
        0 TotalSaleAmount,
        0 CurrentSaleQuantity,
        0 CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        0 ToShopQuantity
        FROM(
        SELECT
        DISTINCT
        ts.CustomerID,
        CONVERT(VARCHAR(10),ts.BillDate,120) BillDate
        FROM
        TB_SaleBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND ts.BillType!='20' AND ts.PayAmount>0  AND ts.CustomerID
        IS NOT NULL
        )T
        GROUP BY T.CustomerID
        UNION ALL
        SELECT
        ts.CustomerID,
        0 TotalSaleQuantity,
        ISNULL(SUM(ISNULL(CASE WHEN ts.BillType = '20' THEN ts.PayAmount*-1 ELSE ts.PayAmount END,0)),0)
        TotalSaleAmount,
        0 CurrentSaleQuantity,
        0 CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        0 ToShopQuantity
        FROM
        TB_SaleBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND ts.PayAmount>0  AND ts.CustomerID
        IS NOT NULL
        GROUP BY ts.CustomerID
        UNION ALL

        SELECT
        T.CustomerID,
        0 TotalSaleQuantity,
        0 TotalSaleAmount,
        Count(*) CurrentSaleQuantity,
        0 CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        0 ToShopQuantity
        FROM(
        SELECT
        DISTINCT
        ts.CustomerID,
        CONVERT(VARCHAR(10),ts.BillDate,120) BillDate
        FROM
        TB_SaleBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND ts.BillType!='20' AND ts.PayAmount>0  AND ts.BillDate >= #{StartTime} AND ts.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        )T
        GROUP BY T.CustomerID
        UNION ALL
        SELECT
        ts.CustomerID,
        0 TotalSaleQuantity,
        0 TotalSaleAmount,
        0 CurrentSaleQuantity,
        ISNULL(SUM(ISNULL(CASE WHEN ts.BillType = '20' THEN ts.PayAmount*-1 ELSE ts.PayAmount END,0)),0)
        CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        0 ToShopQuantity
        FROM
        TB_SaleBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND  ts.PayAmount>0 AND ts.BillDate >= #{StartTime} AND ts.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY ts.CustomerID
        UNION ALL
        SELECT
        T.CustomerID,
        0 TotalSaleQuantity,
        0 TotalSaleAmount,
        0 CurrentSaleQuantity,
        0 CurrentSaleAmount,
        Count(*) CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        0 ToShopQuantity

        FROM(
        SELECT
        DISTINCT
        ts.CustomerID,
        CONVERT(VARCHAR(10),ts.BillDate,120) BillDate
        FROM
        TB_TreatBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND ts.BillType!='20'
        AND ts.BillDate >= #{StartTime} AND ts.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        AND EXISTS
        (
        SELECT 1 FROM
        (
        SELECT TreatBillID FROM TB_TreatBillProject
        UNION
        SELECT TreatBillID FROM TB_TreatBillGeneralCard
        UNION
        SELECT TreatBillID FROM TB_TreatBillTimeCard
        UNION
        SELECT TreatBillID FROM TB_TreatBillSavingCard
        )T WHERE ts.ID=T.TreatBillID
        )
        )T
        GROUP BY T.CustomerID
        UNION ALL
        SELECT
        T.CustomerID,
        0 TotalSaleQuantity,
        0 TotalSaleAmount,
        0 CurrentSaleQuantity,
        0 CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        SUM(Amount) CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        0 ToShopQuantity
        FROM
        (
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.Quantity)*-1 ELSE ts.Quantity END,0)),0) AS Amount
        FROM
        TB_TreatBillProject ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID
        UNION ALL
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.Quantity)*-1 ELSE ts.Quantity END,0)),0) AS Amount
        FROM
        TB_TreatBillGeneralCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID
        UNION ALL
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.Quantity)*-1 ELSE ts.Quantity END,0)),0) AS Amount
        FROM
        TB_TreatBillTimeCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID
        UNION ALL
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.Quantity)*-1 ELSE ts.Quantity END,0)),0) AS Amount
        FROM
        TB_TreatBillSavingCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID

        )T
        GROUP BY T.CustomerID
        UNION ALL
        SELECT
        T.CustomerID,
        0 TotalSaleQuantity,
        0 TotalSaleAmount,
        0 CurrentSaleQuantity,
        0 CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        SUM(Amount) CurrentConsumeProjectAmount,
        0 ToShopQuantity
        FROM
        (
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.PayAmount+ts.CardDeductionAmount)*-1 ELSE
        ts.PayAmount+ts.CardDeductionAmount END,0)),0) AS Amount
        FROM
        TB_TreatBillProject ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID
        UNION ALL
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.PayAmount+ts.CardDeductionAmount)*-1 ELSE
        ts.PayAmount+ts.CardDeductionAmount END,0)),0) AS Amount
        FROM
        TB_TreatBillGeneralCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID
        UNION ALL
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.PayAmount+ts.CardDeductionAmount)*-1 ELSE
        ts.PayAmount+ts.CardDeductionAmount END,0)),0) AS Amount
        FROM
        TB_TreatBillTimeCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID
        UNION ALL
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.CardTreatAmount)*-1 ELSE ts.CardTreatAmount END,0)),0) AS
        Amount
        FROM
        TB_TreatBillSavingCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID

        )T
        GROUP BY T.CustomerID
        UNION ALL
        SELECT
        T.CustomerID,
        0 TotalSaleQuantity,
        0 TotalSaleAmount,
        0 CurrentSaleQuantity,
        0 CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        Count(*) ToShopQuantity

        FROM
        (
        SELECT
        ts.CustomerID,
        CONVERT(VARCHAR(10),ts.BillDate,120) BillDate
        FROM
        TB_SaleBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND ts.BillType!='20'  and ts.BillDate &gt;=#{StartTime} AND ts.BillDate &lt; DATEADD(DAY, 1, #{EndTime})
        UNION
        SELECT
        ts.CustomerID,
        CONVERT(VARCHAR(10),ts.BillDate,120) BillDate
        FROM
        TB_TreatBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND ts.BillType!='20' and ts.BillDate &gt;=#{StartTime} AND ts.BillDate &lt; DATEADD(DAY, 1, #{EndTime})

        )T
        GROUP BY T.CustomerID
        )T
        GROUP BY T.CustomerID
        )T ON T.CustomerID =tc.ID
        <where>
            EXISTS (
            SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
            WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tce.EntityID
            )
            <if test="Name != null and Name != ''">
                AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+
                #{Name} +'%')
            </if>
            <if test="LevelID != null and LevelID != ''">
                AND tc.CustomerLevelID =#{LevelID}
            </if>
            <if test="SourceID != null and SourceID != ''">
                AND tc.CustomerSourceID =#{SourceID}
            </if>
            <if test="StartCreatedOn != null and StartCreatedOn != '' and EndCreatedOn != null and EndCreatedOn != ''">
                and tc.CreatedOn &gt;=#{StartCreatedOn} AND  tc.CreatedOn &lt; DATEADD(DAY,1,#{EndCreatedOn})
            </if>
            <if test="EntityID != null and EntityID != ''">
                AND tce.EntityID=#{EntityID}
            </if>
        </where>
        ORDER BY tc.Name
    </select>

    <select id="getCustomerDynamicSaleAndDynamicConsumeEasyExcel"
            resultType="com.zhelian.model.report.form.output.CustomerDynamicSaleAndDynamicConsumeDetailStatementEasyExcelForm">
        SELECT
        tc.name,
        ISNULL(tc.Code,'') code,
        ISNULL(REPLACE(tc.PhoneNumber , SUBSTRING (tc.PhoneNumber,4,4), '****') ,'') phoneNumber,
        ISNULL(tcl.Name,'') levelName,
        ISNULL(tcs.Name,'') sourceName,
        ISNULL(tc.Birthday,'') birthday,
        CONVERT(VARCHAR(19),tc.CreatedOn,120) createdOn,
        te.entityName,
        ISNULL(T.TotalSaleQuantity,0)totalSaleQuantity,
        ISNULL(T.TotalSaleAmount,0)totalSaleAmount,
        CASE WHEN T.TotalSaleQuantity=0 THEN 0 ELSE ISNULL(CAST(T.TotalSaleAmount/T.TotalSaleQuantity as decimal(38,
        2)),0) END totalSalePrice,
        ISNULL(T.CurrentSaleQuantity,0)currentSaleQuantity,
        ISNULL(T.CurrentSaleAmount,0)currentSaleAmount,
        CASE WHEN T.CurrentSaleAmount=0 THEN 0 ELSE ISNULL(CAST(T.CurrentSaleQuantity/T.CurrentSaleAmount as decimal(38,
        2)),0) END currentSalePrice,
        ISNULL(T.CurrentConsumeProjectCount,0)currentConsumeProjectCount,
        ISNULL(T.CurrentConsumeProjectQuantity,0)currentConsumeProjectQuantity,
        ISNULL(T.CurrentConsumeProjectAmount,0)currentConsumeProjectAmount,
        CASE WHEN T.CurrentConsumeProjectQuantity=0 THEN 0 ELSE
        ISNULL(CAST(T.CurrentConsumeProjectAmount/T.CurrentConsumeProjectQuantity as decimal(38, 2)),0) END
        currentConsumeProjectPrice,
        ISNULL((SELECT TOP 1 CONVERT(VARCHAR(10),ts.BillDate,120) BillDate FROM TB_SaleBill ts WITH(NOLOCK) WHERE
        ts.BillStatus='20' AND ts.BillType!='20' AND ts.CustomerID =tc.ID AND ts.PayAmount>0 ORDER BY ts.BillDate
        DESC),'')lastSaleBillDate,
        ISNULL(DATEDIFF(DAY, (SELECT TOP 1 CONVERT(VARCHAR(10),ts.BillDate,120) BillDate FROM TB_SaleBill ts
        WITH(NOLOCK) WHERE ts.BillStatus='20' AND ts.BillType!='20' AND ts.CustomerID =tc.ID AND ts.PayAmount>0 ORDER BY
        ts.BillDate DESC), GETDATE()),0) unSaleDay,
        ISNULL(( SELECT TOP 1 BillDate FROM
        (
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillProject ts WITH(NOLOCK) JOIN
        TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        UNION ALL
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillGeneralCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        UNION ALL
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillTimeCard ts WITH(NOLOCK) JOIN
        TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        UNION ALL
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillSavingCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        )T where T.CustomerID=tc.ID
        ORDER BY BillDate DESC),'')lastConsumeBillDate,
        ISNULL(DATEDIFF(DAY, (SELECT TOP 1 BillDate FROM
        (
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillProject ts WITH(NOLOCK) JOIN
        TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        UNION ALL
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillGeneralCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        UNION ALL
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillTimeCard ts WITH(NOLOCK) JOIN
        TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        UNION ALL
        SELECT t.CustomerID,CONVERT(VARCHAR(10),t.BillDate,120) BillDate FROM TB_TreatBillSavingCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID WHERE t.BillStatus='20' AND t.BillType!='20'
        )T where T.CustomerID=tc.ID
        ORDER BY BillDate DESC), GETDATE()),0) unConsumeDay,
        ISNULL(T.ToShopQuantity,0)toShopQuantity

        FROM
        TB_Customer tc WITH(NOLOCK)
        LEFT JOIN TB_CustomerEntity tce WITH(NOLOCK) ON tce.CustomerID=tc.ID AND tce.IsBelongEntity=1
        LEFT JOIN TK_Entity te WITH(NOLOCK) ON te.ID=tce.EntityID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN
        (
        SELECT
        T.CustomerID,
        SUM(TotalSaleQuantity) TotalSaleQuantity,
        SUM(TotalSaleAmount) TotalSaleAmount,
        SUM(CurrentSaleQuantity) CurrentSaleQuantity,
        SUM(CurrentSaleAmount) CurrentSaleAmount,
        SUM(CurrentConsumeProjectCount) CurrentConsumeProjectCount,
        SUM(CurrentConsumeProjectQuantity) CurrentConsumeProjectQuantity,
        SUM(CurrentConsumeProjectAmount) CurrentConsumeProjectAmount,
        SUM(ToShopQuantity) ToShopQuantity
        FROM
        (
        SELECT
        T.CustomerID,
        Count(*) TotalSaleQuantity,
        0 TotalSaleAmount,
        0 CurrentSaleQuantity,
        0 CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        0 ToShopQuantity
        FROM(
        SELECT
        DISTINCT
        ts.CustomerID,
        CONVERT(VARCHAR(10),ts.BillDate,120) BillDate
        FROM
        TB_SaleBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND ts.BillType!='20' AND ts.PayAmount>0  AND ts.CustomerID
        IS NOT NULL
        )T
        GROUP BY T.CustomerID
        UNION ALL
        SELECT
        ts.CustomerID,
        0 TotalSaleQuantity,
        ISNULL(SUM(ISNULL(CASE WHEN ts.BillType = '20' THEN ts.PayAmount*-1 ELSE ts.PayAmount END,0)),0)
        TotalSaleAmount,
        0 CurrentSaleQuantity,
        0 CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        0 ToShopQuantity
        FROM
        TB_SaleBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND ts.PayAmount>0  AND ts.CustomerID
        IS NOT NULL
        GROUP BY ts.CustomerID
        UNION ALL

        SELECT
        T.CustomerID,
        0 TotalSaleQuantity,
        0 TotalSaleAmount,
        Count(*) CurrentSaleQuantity,
        0 CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        0 ToShopQuantity
        FROM(
        SELECT
        DISTINCT
        ts.CustomerID,
        CONVERT(VARCHAR(10),ts.BillDate,120) BillDate
        FROM
        TB_SaleBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND ts.BillType!='20' AND ts.PayAmount>0  AND ts.BillDate >= #{StartTime} AND ts.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        )T
        GROUP BY T.CustomerID
        UNION ALL
        SELECT
        ts.CustomerID,
        0 TotalSaleQuantity,
        0 TotalSaleAmount,
        0 CurrentSaleQuantity,
        ISNULL(SUM(ISNULL(CASE WHEN ts.BillType = '20' THEN ts.PayAmount*-1 ELSE ts.PayAmount END,0)),0)
        CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        0 ToShopQuantity
        FROM
        TB_SaleBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND  ts.PayAmount>0 AND ts.BillDate >= #{StartTime} AND ts.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY ts.CustomerID
        UNION ALL
        SELECT
        T.CustomerID,
        0 TotalSaleQuantity,
        0 TotalSaleAmount,
        0 CurrentSaleQuantity,
        0 CurrentSaleAmount,
        Count(*) CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        0 ToShopQuantity

        FROM(
        SELECT
        DISTINCT
        ts.CustomerID,
        CONVERT(VARCHAR(10),ts.BillDate,120) BillDate
        FROM
        TB_TreatBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND ts.BillType!='20'
        AND ts.BillDate >= #{StartTime} AND ts.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        AND EXISTS
        (
        SELECT 1 FROM
        (
        SELECT TreatBillID FROM TB_TreatBillProject
        UNION
        SELECT TreatBillID FROM TB_TreatBillGeneralCard
        UNION
        SELECT TreatBillID FROM TB_TreatBillTimeCard
        UNION
        SELECT TreatBillID FROM TB_TreatBillSavingCard
        )T WHERE ts.ID=T.TreatBillID
        )
        )T
        GROUP BY T.CustomerID
        UNION ALL
        SELECT
        T.CustomerID,
        0 TotalSaleQuantity,
        0 TotalSaleAmount,
        0 CurrentSaleQuantity,
        0 CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        SUM(Amount) CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        0 ToShopQuantity
        FROM
        (
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.Quantity)*-1 ELSE ts.Quantity END,0)),0) AS Amount
        FROM
        TB_TreatBillProject ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID
        UNION ALL
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.Quantity)*-1 ELSE ts.Quantity END,0)),0) AS Amount
        FROM
        TB_TreatBillGeneralCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID
        UNION ALL
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.Quantity)*-1 ELSE ts.Quantity END,0)),0) AS Amount
        FROM
        TB_TreatBillTimeCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID
        UNION ALL
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.Quantity)*-1 ELSE ts.Quantity END,0)),0) AS Amount
        FROM
        TB_TreatBillSavingCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID

        )T
        GROUP BY T.CustomerID
        UNION ALL
        SELECT
        T.CustomerID,
        0 TotalSaleQuantity,
        0 TotalSaleAmount,
        0 CurrentSaleQuantity,
        0 CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        SUM(Amount) CurrentConsumeProjectAmount,
        0 ToShopQuantity
        FROM
        (
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.PayAmount+ts.CardDeductionAmount)*-1 ELSE
        ts.PayAmount+ts.CardDeductionAmount END,0)),0) AS Amount
        FROM
        TB_TreatBillProject ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID
        UNION ALL
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.PayAmount+ts.CardDeductionAmount)*-1 ELSE
        ts.PayAmount+ts.CardDeductionAmount END,0)),0) AS Amount
        FROM
        TB_TreatBillGeneralCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID
        UNION ALL
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.PayAmount+ts.CardDeductionAmount)*-1 ELSE
        ts.PayAmount+ts.CardDeductionAmount END,0)),0) AS Amount
        FROM
        TB_TreatBillTimeCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID
        UNION ALL
        SELECT
        t.CustomerID,
        ISNULL(SUM(ISNULL(CASE WHEN t.BillType = '20' THEN (ts.CardTreatAmount)*-1 ELSE ts.CardTreatAmount END,0)),0) AS
        Amount
        FROM
        TB_TreatBillSavingCard ts WITH(NOLOCK)
        JOIN TB_TreatBill t ON t.ID=ts.TreatBillID
        WHERE t.BillStatus='20' AND t.BillDate >= #{StartTime} AND t.BillDate &lt; DATEADD(DAY,1,#{EndTime})
        GROUP BY t.CustomerID

        )T
        GROUP BY T.CustomerID
        UNION ALL
        SELECT
        T.CustomerID,
        0 TotalSaleQuantity,
        0 TotalSaleAmount,
        0 CurrentSaleQuantity,
        0 CurrentSaleAmount,
        0 CurrentConsumeProjectCount,
        0 CurrentConsumeProjectQuantity,
        0 CurrentConsumeProjectAmount,
        Count(*) ToShopQuantity

        FROM
        (
        SELECT
        ts.CustomerID,
        CONVERT(VARCHAR(10),ts.BillDate,120) BillDate
        FROM
        TB_SaleBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND ts.BillType!='20'  and ts.BillDate &gt;=#{StartTime} AND ts.BillDate &lt; DATEADD(DAY, 1, #{EndTime})
        UNION
        SELECT
        ts.CustomerID,
        CONVERT(VARCHAR(10),ts.BillDate,120) BillDate
        FROM
        TB_TreatBill ts WITH(NOLOCK)
        WHERE ts.BillStatus='20' AND ts.BillType!='20' and ts.BillDate &gt;=#{StartTime} AND ts.BillDate &lt; DATEADD(DAY, 1, #{EndTime})

        )T
        GROUP BY T.CustomerID
        )T
        GROUP BY T.CustomerID
        )T ON T.CustomerID =tc.ID
        <where>
            EXISTS (
            SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
            WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tce.EntityID
            )
            <if test="Name != null and Name != ''">
                AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+
                #{Name} +'%')
            </if>
            <if test="LevelID != null and LevelID != ''">
                AND tc.CustomerLevelID =#{LevelID}
            </if>
            <if test="SourceID != null and SourceID != ''">
                AND tc.CustomerSourceID =#{SourceID}
            </if>
            <if test="StartCreatedOn != null and StartCreatedOn != '' and EndCreatedOn != null and EndCreatedOn != ''">
                and tc.CreatedOn &gt;=#{StartCreatedOn} AND  tc.CreatedOn &lt; DATEADD(DAY,1,#{EndCreatedOn})
            </if>
            <if test="EntityID != null and EntityID != ''">
                AND tce.EntityID=#{EntityID}
            </if>
        </where>
        ORDER BY tc.Name
    </select>

    <select id="getCustomerAccountStatistics"
            resultType="com.zhelian.model.report.form.output.CustomerAccountStatisticsDetailStatementForm">
        SELECT
        T.Name,
        T.Code,
        T.PhoneNumber,
        T.CustomerSourceName,
        T.BuyEntityName,
        T.EntityName,
        T.LevelName,
        T.GoodsTypeName,
        T.PackageCardName,
        T.CategoryName,
        T.GoodsName,
        T.BrandName,
        T.IsLargess,
        T.Quantity,
        T.TotalAmount,
        T.Balance,
        T.BalanceAmount,
        T.PayAmount,
        T.LargessAmount,
        T.ArrearAmount,
        T.BuyDate,
        T.ValidDate,
        T.IsOverdue,
        T.LargessMoney
        FROM
        (
        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        tcs.Name CustomerSourceName,
        tpa.EntityID BuyEntityID,
        yy.EntityName BuyEntityName,
        y.EntityName,
        tcl.Name LevelName,
        '产品' GoodsTypeName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        teb.Name BrandName,
        tpa.IsLargess,
        tpa.Quantity,
        tpa.TotalAmount,
        tpa.Balance,
        tpa.PayPerformanceBalanceAmount+tpa.CardLargessPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount+tpa.LargessPerformanceBalanceAmount
        BalanceAmount,
        tpa.PayPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount PayAmount,
        tpa.CardLargessPerformanceBalanceAmount LargessAmount,
        tpa.ArrearAmount,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        '' ValidDate,
        null IsOverdue,
        case when tpa.IsLargess=1 then tpa.LargessPerformanceBalanceAmount else 0 end LargessMoney
        FROM TB_ProductAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TK_Entity yy ON yy.ID=tpa.EntityID
        LEFT JOIN TE_Product t ON t.ID=tpa.ProductID
        LEFT JOIN TE_ProductCategory tepc ON tepc.ID=t.PCategoryID
        LEFT JOIN TE_ProductBrand teb ON teb.ID=t.ProductBrandID
        LEFT JOIN TB_SaleBillProduct tsb ON tsb.ID=tpa.SaleBillProductID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardProduct spc ON spc.ID=tpa.SaleBillPackageCardProductID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE
        EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        ) AND tpa.Active=1
        <if test="Name != null and Name != ''">
            and ( tc.Name like '%'+ #{Name} +'%' or tc.PhoneNumber like '%'+ #{Name} +'%' or tc.Code like '%'+ #{Name}
            +'%')
        </if>
        <if test="LevelID != null and LevelID != ''">
            and tc.CustomerLevelID =#{LevelID}
        </if>
        <if test="EntityID != null and EntityID != ''">
            and tec.EntityID =#{EntityID}
        </if>
        <if test="PackageCardID != null and PackageCardID != ''">
            and pc.ID =#{PackageCardID}
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            and t.Name like '%'+ #{GoodsName} +'%'
        </if>
        <if test="IsShowUp != null ">
            and tpa.Balance > 0
        </if>
        <if test="CustomerSourceID != null ">
            and tc.CustomerSourceID = #{CustomerSourceID}
        </if>
        UNION ALL

        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        tcs.Name CustomerSourceName,
        tpa.EntityID BuyEntityID,
        yy.EntityName BuyEntityName,
        y.EntityName,
        tcl.Name LevelName,
        '项目' GoodsTypeName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        teb.Name BrandName,
        tpa.IsLargess,
        tpa.Quantity,
        tpa.TotalAmount,
        tpa.Balance,
        tpa.PayPerformanceBalanceAmount+tpa.CardLargessPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount+tpa.LargessPerformanceBalanceAmount
        BalanceAmount,
        tpa.PayPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount PayAmount,
        tpa.CardLargessPerformanceBalanceAmount LargessAmount,
        tpa.ArrearAmount,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        '' ValidDate,
        null IsOverdue,
        case when tpa.IsLargess=1 then tpa.LargessPerformanceBalanceAmount else 0 end LargessMoney
        FROM TB_ProjectAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TK_Entity yy ON yy.ID=tpa.EntityID
        LEFT JOIN TB_Project t ON t.ID=tpa.ProjectID
        LEFT JOIN TB_ProjectCategory tepc ON tepc.ID=t.ProjectCategoryID
        LEFT JOIN TB_ProjectBrand teb ON teb.ID=t.ProjectBrandID
        LEFT JOIN TB_SaleBillProject tsb ON tsb.ID=tpa.SaleBillProjectID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardProject spc ON spc.ID=tpa.SaleBillPackageCardProjectID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE
        EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        ) AND tpa.Active=1
        <if test="Name != null and Name != ''">
            and ( tc.Name like '%'+ #{Name} +'%' or tc.PhoneNumber like '%'+ #{Name} +'%' or tc.Code like '%'+ #{Name}
            +'%')
        </if>
        <if test="LevelID != null and LevelID != ''">
            and tc.CustomerLevelID =#{LevelID}
        </if>
        <if test="EntityID != null and EntityID != ''">
            and tec.EntityID =#{EntityID}
        </if>
        <if test="PackageCardID != null and PackageCardID != ''">
            and pc.ID =#{PackageCardID}
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            and t.Name like '%'+ #{GoodsName} +'%'
        </if>
        <if test="IsShowUp != null ">
            and tpa.Balance > 0
        </if>
        <if test="CustomerSourceID != null ">
            and tc.CustomerSourceID = #{CustomerSourceID}
        </if>
        UNION ALL

        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        tcs.Name CustomerSourceName,
        tpa.EntityID BuyEntityID,
        yy.EntityName BuyEntityName,
        y.EntityName,
        tcl.Name LevelName,
        '通用次卡' GoodsTypeName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        '' BrandName,
        tpa.IsLargess,
        tpa.CardTimes Quantity,
        tpa.TotalAmount,
        tpa.Balance,
        tpa.PayPerformanceBalanceAmount+tpa.CardLargessPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount+tpa.LargessPerformanceBalanceAmount
        BalanceAmount,
        tpa.PayPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount PayAmount,
        tpa.CardLargessPerformanceBalanceAmount LargessAmount,
        tpa.ArrearAmount,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE
        WHEN tpa.ValidType = 3 THEN '永久有效'
        ELSE CONVERT(VARCHAR(10),tpa.ValidDate,120)
        END ValidDate,
        CASE
        WHEN CONVERT(VARCHAR(10),tpa.ValidDate,120)&lt;CONVERT(VARCHAR(10),GETDATE(),120) THEN 1
        ELSE 0
        END IsOverdue,
        case when tpa.IsLargess=1 then tpa.LargessPerformanceBalanceAmount else 0 end LargessMoney

        FROM TB_GeneralCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TK_Entity yy ON yy.ID=tpa.EntityID
        LEFT JOIN TB_GeneralCard t ON t.ID=tpa.GeneralCardID
        LEFT JOIN TB_GeneralCardCategory tepc ON tepc.ID=t.GeneralCardCategoryID
        LEFT JOIN TB_SaleBillGeneralCard tsb ON tsb.ID=tpa.SaleBillGeneralCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardGeneralCard spc ON spc.ID=tpa.SaleBillPackageCardGeneralCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE
        EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        ) AND tpa.Active=1
        <if test="Name != null and Name != ''">
            and ( tc.Name like '%'+ #{Name} +'%' or tc.PhoneNumber like '%'+ #{Name} +'%' or tc.Code like '%'+ #{Name}
            +'%')
        </if>
        <if test="LevelID != null and LevelID != ''">
            and tc.CustomerLevelID =#{LevelID}
        </if>
        <if test="EntityID != null and EntityID != ''">
            and tec.EntityID =#{EntityID}
        </if>
        <if test="PackageCardID != null and PackageCardID != ''">
            and pc.ID =#{PackageCardID}
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            and t.Name like '%'+ #{GoodsName} +'%'
        </if>
        <if test="IsShowUp != null ">
            and tpa.Balance > 0
        </if>
        <if test="CustomerSourceID != null ">
            and tc.CustomerSourceID = #{CustomerSourceID}
        </if>
        UNION ALL

        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        tcs.Name CustomerSourceName,
        tpa.EntityID BuyEntityID,
        yy.EntityName BuyEntityName,
        y.EntityName,
        tcl.Name LevelName,
        '时效卡' GoodsTypeName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        '' BrandName,
        tpa.IsLargess,
        1 Quantity,
        tpa.TotalAmount,
        1 Balance,
        tpa.PayPerformanceBalanceAmount+tpa.CardLargessPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount+tpa.LargessPerformanceBalanceAmount
        BalanceAmount,
        tpa.PayPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount PayAmount,
        tpa.CardLargessPerformanceBalanceAmount LargessAmount,
        tpa.ArrearAmount,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE
        WHEN tpa.ValidType = 3 THEN '永久有效'
        ELSE CONVERT(VARCHAR(10),tpa.ValidDate,120)
        END ValidDate,
        CASE
        WHEN CONVERT(VARCHAR(10),tpa.ValidDate,120)&lt;CONVERT(VARCHAR(10),GETDATE(),120) THEN 1
        ELSE 0
        END IsOverdue,
        case when tpa.IsLargess=1 then tpa.LargessPerformanceBalanceAmount else 0 end LargessMoney
        FROM TB_TimeCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TK_Entity yy ON yy.ID=tpa.EntityID
        LEFT JOIN TB_TimeCard t ON t.ID=tpa.TimeCardID
        LEFT JOIN TB_TimeCardCategory tepc ON tepc.ID=t.TimeCardCategoryID
        LEFT JOIN TB_SaleBillTimeCard tsb ON tsb.ID=tpa.SaleBillTimeCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardTimeCard spc ON spc.ID=tpa.SaleBillPackageCardTimeCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE
        EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        ) AND tpa.Active=1
        <if test="Name != null and Name != ''">
            and ( tc.Name like '%'+ #{Name} +'%' or tc.PhoneNumber like '%'+ #{Name} +'%' or tc.Code like '%'+ #{Name}
            +'%')
        </if>
        <if test="LevelID != null and LevelID != ''">
            and tc.CustomerLevelID =#{LevelID}
        </if>
        <if test="EntityID != null and EntityID != ''">
            and tec.EntityID =#{EntityID}
        </if>
        <if test="PackageCardID != null and PackageCardID != ''">
            and pc.ID =#{PackageCardID}
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            and t.Name like '%'+ #{GoodsName} +'%'
        </if>
        <if test="CustomerSourceID != null ">
            and tc.CustomerSourceID = #{CustomerSourceID}
        </if>
        UNION ALL

        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        tcs.Name CustomerSourceName,
        tpa.EntityID BuyEntityID,
        yy.EntityName BuyEntityName,
        y.EntityName,
        tcl.Name LevelName,
        '储值卡' GoodsTypeName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        '' BrandName,
        CASE WHEN tpa.Amount=0 then 1
        ELSE 0
        END IsLargess,
        1 Quantity,
        tpa.Amount+tpa.LargessAmount TotalAmount,
        1 Balance,
        tpa.Balance+tpa.LargessBalance BalanceAmount,
        tpa.Balance PayAmount,
        tpa.LargessBalance LargessAmount,
        tpa.ArrearAmount,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE
        WHEN tpa.ValidType = 3 THEN '永久有效'
        ELSE CONVERT(VARCHAR(10),tpa.ValidDate,120)
        END ValidDate,
        CASE
        WHEN CONVERT(VARCHAR(10),tpa.ValidDate,120)&lt;CONVERT(VARCHAR(10),GETDATE(),120) THEN 1
        ELSE 0
        END IsOverdue,
        0 LargessMoney
        FROM TB_SavingCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TK_Entity yy ON yy.ID=tpa.EntityID
        LEFT JOIN TB_SavingCard t ON t.ID=tpa.SavingCardID
        LEFT JOIN TB_SavingCardCategory tepc ON tepc.ID=t.SavingCardCategoryID
        LEFT JOIN TB_SaleBillSavingCard tsb ON tsb.ID=tpa.SaleBillSavingCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardSavingCard spc ON spc.ID=tpa.SaleBillPackageCardSavingCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE
        EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        ) AND tpa.Active=1
        <if test="Name != null and Name != ''">
            and ( tc.Name like '%'+ #{Name} +'%' or tc.PhoneNumber like '%'+ #{Name} +'%' or tc.Code like '%'+ #{Name}
            +'%')
        </if>
        <if test="LevelID != null and LevelID != ''">
            and tc.CustomerLevelID =#{LevelID}
        </if>
        <if test="EntityID != null and EntityID != ''">
            and tec.EntityID =#{EntityID}
        </if>
        <if test="PackageCardID != null and PackageCardID != ''">
            and pc.ID =#{PackageCardID}
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            and t.Name like '%'+ #{GoodsName} +'%'
        </if>
        <if test="IsShowUp != null ">
            and (tpa.Balance > 0 OR tpa.LargessBalance>0)
        </if>
        <if test="CustomerSourceID != null ">
            and tc.CustomerSourceID = #{CustomerSourceID}
        </if>

        )T
        <where>
            T.Name IS NOT NULL
            <if test="BuyEntityID != null and BuyEntityID != ''">
                and T.BuyEntityID =#{BuyEntityID}
            </if>
            <if test="GoodsTypeName != null and GoodsTypeName != ''">
                and T.GoodsTypeName =#{GoodsTypeName}
            </if>
            <if test="CategoryName != null and CategoryName != ''">
                and T.CategoryName LIKE '%'+ #{CategoryName} +'%'
            </if>
            <if test="BrandName != null and BrandName != ''">
                and T.BrandName LIKE '%'+ #{BrandName} +'%'
            </if>
            <if test="IsLargess != null">
                and T.IsLargess =#{IsLargess}
            </if>
            <if test="IsOverdue != null">
                and T.IsOverdue =#{IsOverdue}
            </if>
        </where>
        ORDER BY T.Name ,GoodsTypeName
    </select>

    <select id="getCustomerAccountStatisticsSum"
            resultType="com.zhelian.model.report.form.output.CustomerAccountStatisticsSumStatementForm">
        SELECT ISNULL(SUM(Quantity), 0)      Quantity,
        ISNULL(SUM(TotalAmount), 0)   TotalAmount,
        ISNULL(SUM(Balance), 0)       Balance,
        ISNULL(SUM(BalanceAmount), 0) BalanceAmount,
        ISNULL(SUM(PayAmount), 0)     PayAmount,
        ISNULL(SUM(LargessAmount), 0) LargessAmount,
        ISNULL(SUM(ArrearAmount), 0)  ArrearAmount,
        ISNULL(SUM(LargessMoney), 0)  LargessMoney
        FROM (
        SELECT
        T.Name,
        T.Code,
        T.PhoneNumber,
        T.CustomerSourceName,
        T.BuyEntityName,
        T.EntityName,
        T.LevelName,
        T.GoodsTypeName,
        T.PackageCardName,
        T.CategoryName,
        T.GoodsName,
        T.BrandName,
        T.IsLargess,
        T.Quantity,
        T.TotalAmount,
        T.Balance,
        T.BalanceAmount,
        T.PayAmount,
        T.LargessAmount,
        T.ArrearAmount,
        T.BuyDate,
        T.ValidDate,
        T.IsOverdue,
        T.LargessMoney
        FROM
        (
        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        tcs.Name CustomerSourceName,
        tpa.EntityID BuyEntityID,
        yy.EntityName BuyEntityName,
        y.EntityName,
        tcl.Name LevelName,
        '产品' GoodsTypeName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        teb.Name BrandName,
        tpa.IsLargess,
        tpa.Quantity,
        tpa.TotalAmount,
        tpa.Balance,
        tpa.PayPerformanceBalanceAmount+tpa.CardLargessPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount+tpa.LargessPerformanceBalanceAmount
        BalanceAmount,
        tpa.PayPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount PayAmount,
        tpa.CardLargessPerformanceBalanceAmount LargessAmount,
        tpa.ArrearAmount,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        '' ValidDate,
        null IsOverdue,
        case when tpa.IsLargess=1 then tpa.LargessPerformanceBalanceAmount else 0 end LargessMoney
        FROM TB_ProductAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TK_Entity yy ON yy.ID=tpa.EntityID
        LEFT JOIN TE_Product t ON t.ID=tpa.ProductID
        LEFT JOIN TE_ProductCategory tepc ON tepc.ID=t.PCategoryID
        LEFT JOIN TE_ProductBrand teb ON teb.ID=t.ProductBrandID
        LEFT JOIN TB_SaleBillProduct tsb ON tsb.ID=tpa.SaleBillProductID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardProduct spc ON spc.ID=tpa.SaleBillPackageCardProductID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE
        EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        ) AND tpa.Active=1
        <if test="Name != null and Name != ''">
            and ( tc.Name like '%'+ #{Name} +'%' or tc.PhoneNumber like '%'+ #{Name} +'%' or tc.Code like '%'+ #{Name}
            +'%')
        </if>
        <if test="LevelID != null and LevelID != ''">
            and tc.CustomerLevelID =#{LevelID}
        </if>
        <if test="EntityID != null and EntityID != ''">
            and tec.EntityID =#{EntityID}
        </if>
        <if test="PackageCardID != null and PackageCardID != ''">
            and pc.ID =#{PackageCardID}
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            and t.Name like '%'+ #{GoodsName} +'%'
        </if>
        <if test="IsShowUp != null ">
            and tpa.Balance > 0
        </if>
        <if test="CustomerSourceID != null ">
            and tc.CustomerSourceID = #{CustomerSourceID}
        </if>
        UNION ALL

        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        tcs.Name CustomerSourceName,
        tpa.EntityID BuyEntityID,
        yy.EntityName BuyEntityName,
        y.EntityName,
        tcl.Name LevelName,
        '项目' GoodsTypeName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        teb.Name BrandName,
        tpa.IsLargess,
        tpa.Quantity,
        tpa.TotalAmount,
        tpa.Balance,
        tpa.PayPerformanceBalanceAmount+tpa.CardLargessPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount+tpa.LargessPerformanceBalanceAmount
        BalanceAmount,
        tpa.PayPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount PayAmount,
        tpa.CardLargessPerformanceBalanceAmount LargessAmount,
        tpa.ArrearAmount,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        '' ValidDate,
        null IsOverdue,
        case when tpa.IsLargess=1 then tpa.LargessPerformanceBalanceAmount else 0 end LargessMoney
        FROM TB_ProjectAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TK_Entity yy ON yy.ID=tpa.EntityID
        LEFT JOIN TB_Project t ON t.ID=tpa.ProjectID
        LEFT JOIN TB_ProjectCategory tepc ON tepc.ID=t.ProjectCategoryID
        LEFT JOIN TB_ProjectBrand teb ON teb.ID=t.ProjectBrandID
        LEFT JOIN TB_SaleBillProject tsb ON tsb.ID=tpa.SaleBillProjectID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardProject spc ON spc.ID=tpa.SaleBillPackageCardProjectID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE
        EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        ) AND tpa.Active=1
        <if test="Name != null and Name != ''">
            and ( tc.Name like '%'+ #{Name} +'%' or tc.PhoneNumber like '%'+ #{Name} +'%' or tc.Code like '%'+ #{Name}
            +'%')
        </if>
        <if test="LevelID != null and LevelID != ''">
            and tc.CustomerLevelID =#{LevelID}
        </if>
        <if test="EntityID != null and EntityID != ''">
            and tec.EntityID =#{EntityID}
        </if>
        <if test="PackageCardID != null and PackageCardID != ''">
            and pc.ID =#{PackageCardID}
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            and t.Name like '%'+ #{GoodsName} +'%'
        </if>
        <if test="IsShowUp != null ">
            and tpa.Balance > 0
        </if>
        <if test="CustomerSourceID != null ">
            and tc.CustomerSourceID = #{CustomerSourceID}
        </if>
        UNION ALL

        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        tcs.Name CustomerSourceName,
        tpa.EntityID BuyEntityID,
        yy.EntityName BuyEntityName,
        y.EntityName,
        tcl.Name LevelName,
        '通用次卡' GoodsTypeName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        '' BrandName,
        tpa.IsLargess,
        tpa.CardTimes Quantity,
        tpa.TotalAmount,
        tpa.Balance,
        tpa.PayPerformanceBalanceAmount+tpa.CardLargessPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount+tpa.LargessPerformanceBalanceAmount
        BalanceAmount,
        tpa.PayPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount PayAmount,
        tpa.CardLargessPerformanceBalanceAmount LargessAmount,
        tpa.ArrearAmount,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE
        WHEN tpa.ValidType = 3 THEN '永久有效'
        ELSE CONVERT(VARCHAR(10),tpa.ValidDate,120)
        END ValidDate,
        CASE
        WHEN CONVERT(VARCHAR(10),tpa.ValidDate,120)&lt;CONVERT(VARCHAR(10),GETDATE(),120) THEN 1
        ELSE 0
        END IsOverdue,
        case when tpa.IsLargess=1 then tpa.LargessPerformanceBalanceAmount else 0 end LargessMoney

        FROM TB_GeneralCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TK_Entity yy ON yy.ID=tpa.EntityID
        LEFT JOIN TB_GeneralCard t ON t.ID=tpa.GeneralCardID
        LEFT JOIN TB_GeneralCardCategory tepc ON tepc.ID=t.GeneralCardCategoryID
        LEFT JOIN TB_SaleBillGeneralCard tsb ON tsb.ID=tpa.SaleBillGeneralCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardGeneralCard spc ON spc.ID=tpa.SaleBillPackageCardGeneralCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE
        EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        ) AND tpa.Active=1
        <if test="Name != null and Name != ''">
            and ( tc.Name like '%'+ #{Name} +'%' or tc.PhoneNumber like '%'+ #{Name} +'%' or tc.Code like '%'+ #{Name}
            +'%')
        </if>
        <if test="LevelID != null and LevelID != ''">
            and tc.CustomerLevelID =#{LevelID}
        </if>
        <if test="EntityID != null and EntityID != ''">
            and tec.EntityID =#{EntityID}
        </if>
        <if test="PackageCardID != null and PackageCardID != ''">
            and pc.ID =#{PackageCardID}
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            and t.Name like '%'+ #{GoodsName} +'%'
        </if>
        <if test="IsShowUp != null ">
            and tpa.Balance > 0
        </if>
        <if test="CustomerSourceID != null ">
            and tc.CustomerSourceID = #{CustomerSourceID}
        </if>
        UNION ALL

        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        tcs.Name CustomerSourceName,
        tpa.EntityID BuyEntityID,
        yy.EntityName BuyEntityName,
        y.EntityName,
        tcl.Name LevelName,
        '时效卡' GoodsTypeName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        '' BrandName,
        tpa.IsLargess,
        1 Quantity,
        tpa.TotalAmount,
        1 Balance,
        tpa.PayPerformanceBalanceAmount+tpa.CardLargessPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount+tpa.LargessPerformanceBalanceAmount
        BalanceAmount,
        tpa.PayPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount PayAmount,
        tpa.CardLargessPerformanceBalanceAmount LargessAmount,
        tpa.ArrearAmount,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE
        WHEN tpa.ValidType = 3 THEN '永久有效'
        ELSE CONVERT(VARCHAR(10),tpa.ValidDate,120)
        END ValidDate,
        CASE
        WHEN CONVERT(VARCHAR(10),tpa.ValidDate,120)&lt;CONVERT(VARCHAR(10),GETDATE(),120) THEN 1
        ELSE 0
        END IsOverdue,
        case when tpa.IsLargess=1 then tpa.LargessPerformanceBalanceAmount else 0 end LargessMoney
        FROM TB_TimeCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TK_Entity yy ON yy.ID=tpa.EntityID
        LEFT JOIN TB_TimeCard t ON t.ID=tpa.TimeCardID
        LEFT JOIN TB_TimeCardCategory tepc ON tepc.ID=t.TimeCardCategoryID
        LEFT JOIN TB_SaleBillTimeCard tsb ON tsb.ID=tpa.SaleBillTimeCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardTimeCard spc ON spc.ID=tpa.SaleBillPackageCardTimeCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE
        EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        ) AND tpa.Active=1
        <if test="Name != null and Name != ''">
            and ( tc.Name like '%'+ #{Name} +'%' or tc.PhoneNumber like '%'+ #{Name} +'%' or tc.Code like '%'+ #{Name}
            +'%')
        </if>
        <if test="LevelID != null and LevelID != ''">
            and tc.CustomerLevelID =#{LevelID}
        </if>
        <if test="EntityID != null and EntityID != ''">
            and tec.EntityID =#{EntityID}
        </if>
        <if test="PackageCardID != null and PackageCardID != ''">
            and pc.ID =#{PackageCardID}
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            and t.Name like '%'+ #{GoodsName} +'%'
        </if>
        <if test="CustomerSourceID != null ">
            and tc.CustomerSourceID = #{CustomerSourceID}
        </if>
        UNION ALL

        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        tcs.Name CustomerSourceName,
        tpa.EntityID BuyEntityID,
        yy.EntityName BuyEntityName,
        y.EntityName,
        tcl.Name LevelName,
        '储值卡' GoodsTypeName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        '' BrandName,
        CASE WHEN tpa.Amount=0 then 1
        ELSE 0
        END IsLargess,
        1 Quantity,
        tpa.Amount+tpa.LargessAmount TotalAmount,
        1 Balance,
        tpa.Balance+tpa.LargessBalance BalanceAmount,
        tpa.Balance PayAmount,
        tpa.LargessBalance LargessAmount,
        tpa.ArrearAmount,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE
        WHEN tpa.ValidType = 3 THEN '永久有效'
        ELSE CONVERT(VARCHAR(10),tpa.ValidDate,120)
        END ValidDate,
        CASE
        WHEN CONVERT(VARCHAR(10),tpa.ValidDate,120)&lt;CONVERT(VARCHAR(10),GETDATE(),120) THEN 1
        ELSE 0
        END IsOverdue,
        0 LargessMoney
        FROM TB_SavingCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TK_Entity yy ON yy.ID=tpa.EntityID
        LEFT JOIN TB_SavingCard t ON t.ID=tpa.SavingCardID
        LEFT JOIN TB_SavingCardCategory tepc ON tepc.ID=t.SavingCardCategoryID
        LEFT JOIN TB_SaleBillSavingCard tsb ON tsb.ID=tpa.SaleBillSavingCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardSavingCard spc ON spc.ID=tpa.SaleBillPackageCardSavingCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE
        EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        ) AND tpa.Active=1
        <if test="Name != null and Name != ''">
            and ( tc.Name like '%'+ #{Name} +'%' or tc.PhoneNumber like '%'+ #{Name} +'%' or tc.Code like '%'+ #{Name}
            +'%')
        </if>
        <if test="LevelID != null and LevelID != ''">
            and tc.CustomerLevelID =#{LevelID}
        </if>
        <if test="EntityID != null and EntityID != ''">
            and tec.EntityID =#{EntityID}
        </if>
        <if test="PackageCardID != null and PackageCardID != ''">
            and pc.ID =#{PackageCardID}
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            and t.Name like '%'+ #{GoodsName} +'%'
        </if>
        <if test="IsShowUp != null ">
            and (tpa.Balance > 0 OR tpa.LargessBalance>0)
        </if>
        <if test="CustomerSourceID != null ">
            and tc.CustomerSourceID = #{CustomerSourceID}
        </if>

        )T
        <where>
            T.Name IS NOT NULL
            <if test="BuyEntityID != null and BuyEntityID != ''">
                and T.BuyEntityID =#{BuyEntityID}
            </if>
            <if test="GoodsTypeName != null and GoodsTypeName != ''">
                and T.GoodsTypeName =#{GoodsTypeName}
            </if>
            <if test="CategoryName != null and CategoryName != ''">
                and T.CategoryName LIKE '%'+ #{CategoryName} +'%'
            </if>
            <if test="BrandName != null and BrandName != ''">
                and T.BrandName LIKE '%'+ #{BrandName} +'%'
            </if>
            <if test="IsLargess != null and IsLargess != ''">
                and T.IsLargess =#{IsLargess}
            </if>
            <if test="IsOverdue != null">
                and T.IsOverdue =#{IsOverdue}
            </if>
        </where>
        )T
    </select>

    <select id="getCustomerAccountStatisticsEasyExcel"
            resultType="com.zhelian.model.report.form.output.CustomerAccountStatisticsDetailStatementEasyExcelForm">
        SELECT
        T.name,
        ISNULL(T.Code,'') code,
        ISNULL(REPLACE(T.PhoneNumber , SUBSTRING (T.PhoneNumber,4,4), '****'),'') phoneNumber,
        ISNULL(T.CustomerSourceName,'') customerSourceName,
        ISNULL(T.BuyEntityName,'') buyEntityName,
        ISNULL(T.EntityName,'') entityName,
        ISNULL(T.LevelName,'') levelName,
        ISNULL(T.GoodsTypeName,'') goodsTypeName,
        ISNULL(T.PackageCardName,'') packageCardName,
        ISNULL(T.CategoryName,'') categoryName,
        ISNULL(T.GoodsName,'') goodsName,
        ISNULL(T.BrandName,'') brandName,
        CASE WHEN T.IsLargess=1 THEN '是' ELSE '否' END isLargess,
        T.quantity,
        T.totalAmount,
        T.balance,
        T.balanceAmount,
        T.payAmount,
        T.largessAmount,
        T.arrearAmount,
        T.buyDate,
        T.validDate,
        CASE WHEN T.IsOverdue=1 THEN '是' ELSE '否' END isOverdue,
        T.largessMoney
        FROM
        (
        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        tcs.Name CustomerSourceName,
        tpa.EntityID BuyEntityID,
        yy.EntityName BuyEntityName,
        y.EntityName,
        tcl.Name LevelName,
        '产品' GoodsTypeName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        teb.Name BrandName,
        tpa.IsLargess,
        tpa.Quantity,
        tpa.TotalAmount,
        tpa.Balance,
        tpa.PayPerformanceBalanceAmount+tpa.CardLargessPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount+tpa.LargessPerformanceBalanceAmount
        BalanceAmount,
        tpa.PayPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount PayAmount,
        tpa.CardLargessPerformanceBalanceAmount LargessAmount,
        tpa.ArrearAmount,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        '' ValidDate,
        null IsOverdue,
        case when tpa.IsLargess=1 then tpa.LargessPerformanceBalanceAmount else 0 end LargessMoney
        FROM TB_ProductAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TK_Entity yy ON yy.ID=tpa.EntityID
        LEFT JOIN TE_Product t ON t.ID=tpa.ProductID
        LEFT JOIN TE_ProductCategory tepc ON tepc.ID=t.PCategoryID
        LEFT JOIN TE_ProductBrand teb ON teb.ID=t.ProductBrandID
        LEFT JOIN TB_SaleBillProduct tsb ON tsb.ID=tpa.SaleBillProductID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardProduct spc ON spc.ID=tpa.SaleBillPackageCardProductID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE
        EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        ) AND tpa.Active=1
        <if test="Name != null and Name != ''">
            and ( tc.Name like '%'+ #{Name} +'%' or tc.PhoneNumber like '%'+ #{Name} +'%' or tc.Code like '%'+ #{Name}
            +'%')
        </if>
        <if test="LevelID != null and LevelID != ''">
            and tc.CustomerLevelID =#{LevelID}
        </if>
        <if test="EntityID != null and EntityID != ''">
            and tec.EntityID =#{EntityID}
        </if>
        <if test="PackageCardID != null and PackageCardID != ''">
            and pc.ID =#{PackageCardID}
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            and t.Name like '%'+ #{GoodsName} +'%'
        </if>
        <if test="IsShowUp != null ">
            and tpa.Balance > 0
        </if>
        <if test="CustomerSourceID != null ">
            and tc.CustomerSourceID = #{CustomerSourceID}
        </if>
        UNION ALL

        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        tcs.Name CustomerSourceName,
        tpa.EntityID BuyEntityID,
        yy.EntityName BuyEntityName,
        y.EntityName,
        tcl.Name LevelName,
        '项目' GoodsTypeName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        teb.Name BrandName,
        tpa.IsLargess,
        tpa.Quantity,
        tpa.TotalAmount,
        tpa.Balance,
        tpa.PayPerformanceBalanceAmount+tpa.CardLargessPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount+tpa.LargessPerformanceBalanceAmount
        BalanceAmount,
        tpa.PayPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount PayAmount,
        tpa.CardLargessPerformanceBalanceAmount LargessAmount,
        tpa.ArrearAmount,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        '' ValidDate,
        null IsOverdue,
        case when tpa.IsLargess=1 then tpa.LargessPerformanceBalanceAmount else 0 end LargessMoney
        FROM TB_ProjectAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TK_Entity yy ON yy.ID=tpa.EntityID
        LEFT JOIN TB_Project t ON t.ID=tpa.ProjectID
        LEFT JOIN TB_ProjectCategory tepc ON tepc.ID=t.ProjectCategoryID
        LEFT JOIN TB_ProjectBrand teb ON teb.ID=t.ProjectBrandID
        LEFT JOIN TB_SaleBillProject tsb ON tsb.ID=tpa.SaleBillProjectID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardProject spc ON spc.ID=tpa.SaleBillPackageCardProjectID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE
        EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        ) AND tpa.Active=1
        <if test="Name != null and Name != ''">
            and ( tc.Name like '%'+ #{Name} +'%' or tc.PhoneNumber like '%'+ #{Name} +'%' or tc.Code like '%'+ #{Name}
            +'%')
        </if>
        <if test="LevelID != null and LevelID != ''">
            and tc.CustomerLevelID =#{LevelID}
        </if>
        <if test="EntityID != null and EntityID != ''">
            and tec.EntityID =#{EntityID}
        </if>
        <if test="PackageCardID != null and PackageCardID != ''">
            and pc.ID =#{PackageCardID}
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            and t.Name like '%'+ #{GoodsName} +'%'
        </if>
        <if test="IsShowUp != null ">
            and tpa.Balance > 0
        </if>
        <if test="CustomerSourceID != null ">
            and tc.CustomerSourceID = #{CustomerSourceID}
        </if>
        UNION ALL

        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        tcs.Name CustomerSourceName,
        tpa.EntityID BuyEntityID,
        yy.EntityName BuyEntityName,
        y.EntityName,
        tcl.Name LevelName,
        '通用次卡' GoodsTypeName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        '' BrandName,
        tpa.IsLargess,
        tpa.CardTimes Quantity,
        tpa.TotalAmount,
        tpa.Balance,
        tpa.PayPerformanceBalanceAmount+tpa.CardLargessPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount+tpa.LargessPerformanceBalanceAmount
        BalanceAmount,
        tpa.PayPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount PayAmount,
        tpa.CardLargessPerformanceBalanceAmount LargessAmount,
        tpa.ArrearAmount,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE
        WHEN tpa.ValidType = 3 THEN '永久有效'
        ELSE CONVERT(VARCHAR(10),tpa.ValidDate,120)
        END ValidDate,
        CASE
        WHEN CONVERT(VARCHAR(10),tpa.ValidDate,120)&lt;CONVERT(VARCHAR(10),GETDATE(),120) THEN 1
        ELSE 0
        END IsOverdue,
        case when tpa.IsLargess=1 then tpa.LargessPerformanceBalanceAmount else 0 end LargessMoney

        FROM TB_GeneralCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TK_Entity yy ON yy.ID=tpa.EntityID
        LEFT JOIN TB_GeneralCard t ON t.ID=tpa.GeneralCardID
        LEFT JOIN TB_GeneralCardCategory tepc ON tepc.ID=t.GeneralCardCategoryID
        LEFT JOIN TB_SaleBillGeneralCard tsb ON tsb.ID=tpa.SaleBillGeneralCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardGeneralCard spc ON spc.ID=tpa.SaleBillPackageCardGeneralCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE
        EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        ) AND tpa.Active=1
        <if test="Name != null and Name != ''">
            and ( tc.Name like '%'+ #{Name} +'%' or tc.PhoneNumber like '%'+ #{Name} +'%' or tc.Code like '%'+ #{Name}
            +'%')
        </if>
        <if test="LevelID != null and LevelID != ''">
            and tc.CustomerLevelID =#{LevelID}
        </if>
        <if test="EntityID != null and EntityID != ''">
            and tec.EntityID =#{EntityID}
        </if>
        <if test="PackageCardID != null and PackageCardID != ''">
            and pc.ID =#{PackageCardID}
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            and t.Name like '%'+ #{GoodsName} +'%'
        </if>
        <if test="IsShowUp != null ">
            and tpa.Balance > 0
        </if>
        <if test="CustomerSourceID != null ">
            and tc.CustomerSourceID = #{CustomerSourceID}
        </if>
        UNION ALL

        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        tcs.Name CustomerSourceName,
        tpa.EntityID BuyEntityID,
        yy.EntityName BuyEntityName,
        y.EntityName,
        tcl.Name LevelName,
        '时效卡' GoodsTypeName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        '' BrandName,
        tpa.IsLargess,
        1 Quantity,
        tpa.TotalAmount,
        1 Balance,
        tpa.PayPerformanceBalanceAmount+tpa.CardLargessPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount+tpa.LargessPerformanceBalanceAmount
        BalanceAmount,
        tpa.PayPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount PayAmount,
        tpa.CardLargessPerformanceBalanceAmount LargessAmount,
        tpa.ArrearAmount,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE
        WHEN tpa.ValidType = 3 THEN '永久有效'
        ELSE CONVERT(VARCHAR(10),tpa.ValidDate,120)
        END ValidDate,
        CASE
        WHEN CONVERT(VARCHAR(10),tpa.ValidDate,120)&lt;CONVERT(VARCHAR(10),GETDATE(),120) THEN 1
        ELSE 0
        END IsOverdue,
        case when tpa.IsLargess=1 then tpa.LargessPerformanceBalanceAmount else 0 end LargessMoney
        FROM TB_TimeCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TK_Entity yy ON yy.ID=tpa.EntityID
        LEFT JOIN TB_TimeCard t ON t.ID=tpa.TimeCardID
        LEFT JOIN TB_TimeCardCategory tepc ON tepc.ID=t.TimeCardCategoryID
        LEFT JOIN TB_SaleBillTimeCard tsb ON tsb.ID=tpa.SaleBillTimeCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardTimeCard spc ON spc.ID=tpa.SaleBillPackageCardTimeCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE
        EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        ) AND tpa.Active=1
        <if test="Name != null and Name != ''">
            and ( tc.Name like '%'+ #{Name} +'%' or tc.PhoneNumber like '%'+ #{Name} +'%' or tc.Code like '%'+ #{Name}
            +'%')
        </if>
        <if test="LevelID != null and LevelID != ''">
            and tc.CustomerLevelID =#{LevelID}
        </if>
        <if test="EntityID != null and EntityID != ''">
            and tec.EntityID =#{EntityID}
        </if>
        <if test="PackageCardID != null and PackageCardID != ''">
            and pc.ID =#{PackageCardID}
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            and t.Name like '%'+ #{GoodsName} +'%'
        </if>
        <if test="CustomerSourceID != null ">
            and tc.CustomerSourceID = #{CustomerSourceID}
        </if>
        UNION ALL

        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        tcs.Name CustomerSourceName,
        tpa.EntityID BuyEntityID,
        yy.EntityName BuyEntityName,
        y.EntityName,
        tcl.Name LevelName,
        '储值卡' GoodsTypeName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        '' BrandName,
        CASE WHEN tpa.Amount=0 then 1
        ELSE 0
        END IsLargess,
        1 Quantity,
        tpa.Amount+tpa.LargessAmount TotalAmount,
        1 Balance,
        tpa.Balance+tpa.LargessBalance BalanceAmount,
        tpa.Balance PayAmount,
        tpa.LargessBalance LargessAmount,
        tpa.ArrearAmount,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE
        WHEN tpa.ValidType = 3 THEN '永久有效'
        ELSE CONVERT(VARCHAR(10),tpa.ValidDate,120)
        END ValidDate,
        CASE
        WHEN CONVERT(VARCHAR(10),tpa.ValidDate,120)&lt;CONVERT(VARCHAR(10),GETDATE(),120) THEN 1
        ELSE 0
        END IsOverdue,
        0 LargessMoney
        FROM TB_SavingCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TK_Entity yy ON yy.ID=tpa.EntityID
        LEFT JOIN TB_SavingCard t ON t.ID=tpa.SavingCardID
        LEFT JOIN TB_SavingCardCategory tepc ON tepc.ID=t.SavingCardCategoryID
        LEFT JOIN TB_SaleBillSavingCard tsb ON tsb.ID=tpa.SaleBillSavingCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardSavingCard spc ON spc.ID=tpa.SaleBillPackageCardSavingCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE
        EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        ) AND tpa.Active=1
        <if test="Name != null and Name != ''">
            and ( tc.Name like '%'+ #{Name} +'%' or tc.PhoneNumber like '%'+ #{Name} +'%' or tc.Code like '%'+ #{Name}
            +'%')
        </if>
        <if test="LevelID != null and LevelID != ''">
            and tc.CustomerLevelID =#{LevelID}
        </if>
        <if test="EntityID != null and EntityID != ''">
            and tec.EntityID =#{EntityID}
        </if>
        <if test="PackageCardID != null and PackageCardID != ''">
            and pc.ID =#{PackageCardID}
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            and t.Name like '%'+ #{GoodsName} +'%'
        </if>
        <if test="IsShowUp != null ">
            and (tpa.Balance > 0 OR tpa.LargessBalance>0)
        </if>
        <if test="CustomerSourceID != null ">
            and tc.CustomerSourceID = #{CustomerSourceID}
        </if>
        )T
        <where>
            T.Name IS NOT NULL
            <if test="BuyEntityID != null and BuyEntityID != ''">
                and T.BuyEntityID =#{BuyEntityID}
            </if>
            <if test="GoodsTypeName != null and GoodsTypeName != ''">
                and T.GoodsTypeName =#{GoodsTypeName}
            </if>
            <if test="CategoryName != null and CategoryName != ''">
                and T.CategoryName LIKE '%'+ #{CategoryName} +'%'
            </if>
            <if test="BrandName != null and BrandName != ''">
                and T.BrandName LIKE '%'+ #{BrandName} +'%'
            </if>
            <if test="IsLargess != null and IsLargess != ''">
                and T.IsLargess =#{IsLargess}
            </if>
            <if test="IsOverdue != null">
                and T.IsOverdue =#{IsOverdue}
            </if>
        </where>
        ORDER BY T.Name ,GoodsTypeName
    </select>

    <select id="getCustomerAccountStatisticsEasyExcelDisPlayPhone"
            resultType="com.zhelian.model.report.form.output.CustomerAccountStatisticsDetailStatementEasyExcelForm">
        SELECT
        T.name,
        ISNULL(T.Code,'') code,
        ISNULL(T.PhoneNumber,'') phoneNumber,
        ISNULL(T.CustomerSourceName,'') customerSourceName,
        ISNULL(T.BuyEntityName,'') buyEntityName,
        ISNULL(T.EntityName,'') entityName,
        ISNULL(T.LevelName,'') levelName,
        ISNULL(T.GoodsTypeName,'') goodsTypeName,
        ISNULL(T.PackageCardName,'') packageCardName,
        ISNULL(T.CategoryName,'') categoryName,
        ISNULL(T.GoodsName,'') goodsName,
        ISNULL(T.BrandName,'') brandName,
        CASE WHEN T.IsLargess=1 THEN '是' ELSE '否' END isLargess,
        T.quantity,
        T.totalAmount,
        T.balance,
        T.balanceAmount,
        T.payAmount,
        T.largessAmount,
        T.arrearAmount,
        T.buyDate,
        T.validDate,
        CASE WHEN T.IsOverdue=1 THEN '是' ELSE '否' END isOverdue,
        T.largessMoney
        FROM
        (
        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        tcs.Name CustomerSourceName,
        tpa.EntityID BuyEntityID,
        yy.EntityName BuyEntityName,
        y.EntityName,
        tcl.Name LevelName,
        '产品' GoodsTypeName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        teb.Name BrandName,
        tpa.IsLargess,
        tpa.Quantity,
        tpa.TotalAmount,
        tpa.Balance,
        tpa.PayPerformanceBalanceAmount+tpa.CardLargessPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount+tpa.LargessPerformanceBalanceAmount
        BalanceAmount,
        tpa.PayPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount PayAmount,
        tpa.CardLargessPerformanceBalanceAmount LargessAmount,
        tpa.ArrearAmount,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        '' ValidDate,
        null IsOverdue,
        case when tpa.IsLargess=1 then tpa.LargessPerformanceBalanceAmount else 0 end LargessMoney
        FROM TB_ProductAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TK_Entity yy ON yy.ID=tpa.EntityID
        LEFT JOIN TE_Product t ON t.ID=tpa.ProductID
        LEFT JOIN TE_ProductCategory tepc ON tepc.ID=t.PCategoryID
        LEFT JOIN TE_ProductBrand teb ON teb.ID=t.ProductBrandID
        LEFT JOIN TB_SaleBillProduct tsb ON tsb.ID=tpa.SaleBillProductID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardProduct spc ON spc.ID=tpa.SaleBillPackageCardProductID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE
        EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        ) AND tpa.Active=1
        <if test="Name != null and Name != ''">
            and ( tc.Name like '%'+ #{Name} +'%' or tc.PhoneNumber like '%'+ #{Name} +'%' or tc.Code like '%'+ #{Name}
            +'%')
        </if>
        <if test="LevelID != null and LevelID != ''">
            and tc.CustomerLevelID =#{LevelID}
        </if>
        <if test="EntityID != null and EntityID != ''">
            and tec.EntityID =#{EntityID}
        </if>
        <if test="PackageCardID != null and PackageCardID != ''">
            and pc.ID =#{PackageCardID}
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            and t.Name like '%'+ #{GoodsName} +'%'
        </if>
        <if test="IsShowUp != null ">
            and tpa.Balance > 0
        </if>
        <if test="CustomerSourceID != null ">
            and tc.CustomerSourceID = #{CustomerSourceID}
        </if>
        UNION ALL

        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        tcs.Name CustomerSourceName,
        tpa.EntityID BuyEntityID,
        yy.EntityName BuyEntityName,
        y.EntityName,
        tcl.Name LevelName,
        '项目' GoodsTypeName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        teb.Name BrandName,
        tpa.IsLargess,
        tpa.Quantity,
        tpa.TotalAmount,
        tpa.Balance,
        tpa.PayPerformanceBalanceAmount+tpa.CardLargessPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount+tpa.LargessPerformanceBalanceAmount
        BalanceAmount,
        tpa.PayPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount PayAmount,
        tpa.CardLargessPerformanceBalanceAmount LargessAmount,
        tpa.ArrearAmount,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        '' ValidDate,
        null IsOverdue,
        case when tpa.IsLargess=1 then tpa.LargessPerformanceBalanceAmount else 0 end LargessMoney
        FROM TB_ProjectAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TK_Entity yy ON yy.ID=tpa.EntityID
        LEFT JOIN TB_Project t ON t.ID=tpa.ProjectID
        LEFT JOIN TB_ProjectCategory tepc ON tepc.ID=t.ProjectCategoryID
        LEFT JOIN TB_ProjectBrand teb ON teb.ID=t.ProjectBrandID
        LEFT JOIN TB_SaleBillProject tsb ON tsb.ID=tpa.SaleBillProjectID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardProject spc ON spc.ID=tpa.SaleBillPackageCardProjectID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE
        EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        ) AND tpa.Active=1
        <if test="Name != null and Name != ''">
            and ( tc.Name like '%'+ #{Name} +'%' or tc.PhoneNumber like '%'+ #{Name} +'%' or tc.Code like '%'+ #{Name}
            +'%')
        </if>
        <if test="LevelID != null and LevelID != ''">
            and tc.CustomerLevelID =#{LevelID}
        </if>
        <if test="EntityID != null and EntityID != ''">
            and tec.EntityID =#{EntityID}
        </if>
        <if test="PackageCardID != null and PackageCardID != ''">
            and pc.ID =#{PackageCardID}
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            and t.Name like '%'+ #{GoodsName} +'%'
        </if>
        <if test="IsShowUp != null ">
            and tpa.Balance > 0
        </if>
        <if test="CustomerSourceID != null ">
            and tc.CustomerSourceID = #{CustomerSourceID}
        </if>
        UNION ALL

        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        tcs.Name CustomerSourceName,
        tpa.EntityID BuyEntityID,
        yy.EntityName BuyEntityName,
        y.EntityName,
        tcl.Name LevelName,
        '通用次卡' GoodsTypeName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        '' BrandName,
        tpa.IsLargess,
        tpa.CardTimes Quantity,
        tpa.TotalAmount,
        tpa.Balance,
        tpa.PayPerformanceBalanceAmount+tpa.CardLargessPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount+tpa.LargessPerformanceBalanceAmount
        BalanceAmount,
        tpa.PayPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount PayAmount,
        tpa.CardLargessPerformanceBalanceAmount LargessAmount,
        tpa.ArrearAmount,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE
        WHEN tpa.ValidType = 3 THEN '永久有效'
        ELSE CONVERT(VARCHAR(10),tpa.ValidDate,120)
        END ValidDate,
        CASE
        WHEN CONVERT(VARCHAR(10),tpa.ValidDate,120)&lt;CONVERT(VARCHAR(10),GETDATE(),120) THEN 1
        ELSE 0
        END IsOverdue,
        case when tpa.IsLargess=1 then tpa.LargessPerformanceBalanceAmount else 0 end LargessMoney

        FROM TB_GeneralCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TK_Entity yy ON yy.ID=tpa.EntityID
        LEFT JOIN TB_GeneralCard t ON t.ID=tpa.GeneralCardID
        LEFT JOIN TB_GeneralCardCategory tepc ON tepc.ID=t.GeneralCardCategoryID
        LEFT JOIN TB_SaleBillGeneralCard tsb ON tsb.ID=tpa.SaleBillGeneralCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardGeneralCard spc ON spc.ID=tpa.SaleBillPackageCardGeneralCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE
        EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        ) AND tpa.Active=1
        <if test="Name != null and Name != ''">
            and ( tc.Name like '%'+ #{Name} +'%' or tc.PhoneNumber like '%'+ #{Name} +'%' or tc.Code like '%'+ #{Name}
            +'%')
        </if>
        <if test="LevelID != null and LevelID != ''">
            and tc.CustomerLevelID =#{LevelID}
        </if>
        <if test="EntityID != null and EntityID != ''">
            and tec.EntityID =#{EntityID}
        </if>
        <if test="PackageCardID != null and PackageCardID != ''">
            and pc.ID =#{PackageCardID}
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            and t.Name like '%'+ #{GoodsName} +'%'
        </if>
        <if test="IsShowUp != null ">
            and tpa.Balance > 0
        </if>
        <if test="CustomerSourceID != null ">
            and tc.CustomerSourceID = #{CustomerSourceID}
        </if>
        UNION ALL

        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        tcs.Name CustomerSourceName,
        tpa.EntityID BuyEntityID,
        yy.EntityName BuyEntityName,
        y.EntityName,
        tcl.Name LevelName,
        '时效卡' GoodsTypeName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        '' BrandName,
        tpa.IsLargess,
        1 Quantity,
        tpa.TotalAmount,
        1 Balance,
        tpa.PayPerformanceBalanceAmount+tpa.CardLargessPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount+tpa.LargessPerformanceBalanceAmount
        BalanceAmount,
        tpa.PayPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount PayAmount,
        tpa.CardLargessPerformanceBalanceAmount LargessAmount,
        tpa.ArrearAmount,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE
        WHEN tpa.ValidType = 3 THEN '永久有效'
        ELSE CONVERT(VARCHAR(10),tpa.ValidDate,120)
        END ValidDate,
        CASE
        WHEN CONVERT(VARCHAR(10),tpa.ValidDate,120)&lt;CONVERT(VARCHAR(10),GETDATE(),120) THEN 1
        ELSE 0
        END IsOverdue,
        case when tpa.IsLargess=1 then tpa.LargessPerformanceBalanceAmount else 0 end LargessMoney
        FROM TB_TimeCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TK_Entity yy ON yy.ID=tpa.EntityID
        LEFT JOIN TB_TimeCard t ON t.ID=tpa.TimeCardID
        LEFT JOIN TB_TimeCardCategory tepc ON tepc.ID=t.TimeCardCategoryID
        LEFT JOIN TB_SaleBillTimeCard tsb ON tsb.ID=tpa.SaleBillTimeCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardTimeCard spc ON spc.ID=tpa.SaleBillPackageCardTimeCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE
        EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        ) AND tpa.Active=1
        <if test="Name != null and Name != ''">
            and ( tc.Name like '%'+ #{Name} +'%' or tc.PhoneNumber like '%'+ #{Name} +'%' or tc.Code like '%'+ #{Name}
            +'%')
        </if>
        <if test="LevelID != null and LevelID != ''">
            and tc.CustomerLevelID =#{LevelID}
        </if>
        <if test="EntityID != null and EntityID != ''">
            and tec.EntityID =#{EntityID}
        </if>
        <if test="PackageCardID != null and PackageCardID != ''">
            and pc.ID =#{PackageCardID}
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            and t.Name like '%'+ #{GoodsName} +'%'
        </if>
        <if test="CustomerSourceID != null ">
            and tc.CustomerSourceID = #{CustomerSourceID}
        </if>
        UNION ALL

        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        tcs.Name CustomerSourceName,
        tpa.EntityID BuyEntityID,
        yy.EntityName BuyEntityName,
        y.EntityName,
        tcl.Name LevelName,
        '储值卡' GoodsTypeName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        '' BrandName,
        CASE WHEN tpa.Amount=0 then 1
        ELSE 0
        END IsLargess,
        1 Quantity,
        tpa.Amount+tpa.LargessAmount TotalAmount,
        1 Balance,
        tpa.Balance+tpa.LargessBalance BalanceAmount,
        tpa.Balance PayAmount,
        tpa.LargessBalance LargessAmount,
        tpa.ArrearAmount,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE
        WHEN tpa.ValidType = 3 THEN '永久有效'
        ELSE CONVERT(VARCHAR(10),tpa.ValidDate,120)
        END ValidDate,
        CASE
        WHEN CONVERT(VARCHAR(10),tpa.ValidDate,120)&lt;CONVERT(VARCHAR(10),GETDATE(),120) THEN 1
        ELSE 0
        END IsOverdue,
        0 LargessMoney
        FROM TB_SavingCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TK_Entity yy ON yy.ID=tpa.EntityID
        LEFT JOIN TB_SavingCard t ON t.ID=tpa.SavingCardID
        LEFT JOIN TB_SavingCardCategory tepc ON tepc.ID=t.SavingCardCategoryID
        LEFT JOIN TB_SaleBillSavingCard tsb ON tsb.ID=tpa.SaleBillSavingCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardSavingCard spc ON spc.ID=tpa.SaleBillPackageCardSavingCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE
        EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP WITH(NOLOCK)
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        ) AND tpa.Active=1
        <if test="Name != null and Name != ''">
            and ( tc.Name like '%'+ #{Name} +'%' or tc.PhoneNumber like '%'+ #{Name} +'%' or tc.Code like '%'+ #{Name}
            +'%')
        </if>
        <if test="LevelID != null and LevelID != ''">
            and tc.CustomerLevelID =#{LevelID}
        </if>
        <if test="EntityID != null and EntityID != ''">
            and tec.EntityID =#{EntityID}
        </if>
        <if test="PackageCardID != null and PackageCardID != ''">
            and pc.ID =#{PackageCardID}
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            and t.Name like '%'+ #{GoodsName} +'%'
        </if>
        <if test="IsShowUp != null ">
            and (tpa.Balance > 0 OR tpa.LargessBalance>0)
        </if>
        <if test="CustomerSourceID != null ">
            and tc.CustomerSourceID = #{CustomerSourceID}
        </if>
        )T
        <where>
            T.Name IS NOT NULL
            <if test="BuyEntityID != null and BuyEntityID != ''">
                and T.BuyEntityID =#{BuyEntityID}
            </if>
            <if test="GoodsTypeName != null and GoodsTypeName != ''">
                and T.GoodsTypeName =#{GoodsTypeName}
            </if>
            <if test="CategoryName != null and CategoryName != ''">
                and T.CategoryName LIKE '%'+ #{CategoryName} +'%'
            </if>
            <if test="BrandName != null and BrandName != ''">
                and T.BrandName LIKE '%'+ #{BrandName} +'%'
            </if>
            <if test="IsLargess != null and IsLargess != ''">
                and T.IsLargess =#{IsLargess}
            </if>
            <if test="IsOverdue != null">
                and T.IsOverdue =#{IsOverdue}
            </if>
        </where>
        ORDER BY T.Name ,GoodsTypeName
    </select>

    <select id="getCustomerArrear"
            resultType="com.zhelian.model.report.form.output.CustomerArrearsDetailStatementForm">
        SELECT
        T.Name,
        ISNULL(T.Code,'') Code,
        ISNULL(T.PhoneNumber,'') PhoneNumber,
        ISNULL(T.EntityName,'') EntityName,
        ISNULL(T.LevelName,'') LevelName,
        ISNULL(T.BillID,'') BillID,
        ISNULL(T.BuyDate,'') BuyDate,
        ISNULL(T.BuyEntityName,'') BuyEntityName,
        ISNULL(T.EmployeeName,'') EmployeeName,
        ISNULL(T.GoodsTypeName,'') GoodsTypeName,
        ISNULL(T.CategoryName,'') CategoryName,
        ISNULL(T.GoodsName,'') GoodsName,
        T.PackageCardName,
        T.TotalAmount,
        T.PayAmount,
        T.SavingCardDeductionAmount,
        T.SavingCardDeductionLargessAmount,
        T.FillingMoney+ T.ArrearAmount OriginMoney,
        T.FillingMoney,
        T.ArrearAmount
        FROM
        (
        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        y.EntityName,
        tcl.Name LevelName,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN ts.ID
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN tts.ID
        END BillID,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN ts.EntityID
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN tts.EntityID
        END BuyEntityID,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN tey.EntityName
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN tety.EntityName
        END BuyEntityName,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN toe.Name
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN toye.Name
        END EmployeeName,
        '产品' GoodsTypeName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tpa.TotalAmount,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN
        tsb.TotalAmount-tsb.ArrearAmount-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN
        spc.TotalAmount-spc.ArrearAmount-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        END PayAmount,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        END SavingCardDeductionAmount,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        END SavingCardDeductionLargessAmount,
        ISNULL((
        SELECT SUM(ts.TotalAmount) FROM TB_SaleBillProduct ts
        JOIN TB_SaleBill t ON t.ID=ts.BillID
        WHERE t.BillType='30' AND t.BillStatus='20' AND ts.ProductAccountID=tpa.ID
        ),0)FillingMoney,
        tpa.ArrearAmount

        FROM TB_ProductAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TE_Product t ON t.ID=tpa.ProductID
        LEFT JOIN TE_ProductCategory tepc ON tepc.ID=t.PCategoryID
        LEFT JOIN TB_SaleBillProduct tsb ON tsb.ID=tpa.SaleBillProductID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Employee toe ON toe.ID=ts.EmployeeID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardProduct spc ON spc.ID=tpa.SaleBillPackageCardProductID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Employee toye ON toye.ID=tts.EmployeeID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE tpa.ArrearAmount>0 AND tpa.Active=1
        AND EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        )
        <if test="Name != null and Name != ''">
            AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+ #{Name}
            +'%')
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            AND t.Name LIKE '%'+ #{GoodsName} +'%'
        </if>
        <if test="EntityID != null and EntityID != ''">
            AND tec.EntityID=#{EntityID}
        </if>
        UNION ALL
        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        y.EntityName,
        tcl.Name LevelName,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN ts.ID
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN tts.ID
        END BillID,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN tey.ID
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN tety.ID
        END BuyEntityID,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN tey.EntityName
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN tety.EntityName
        END BuyEntityName,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN toe.Name
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN toye.Name
        END EmployeeName,
        '项目' GoodsTypeName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tpa.TotalAmount,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN
        tsb.TotalAmount-tsb.ArrearAmount-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN
        spc.TotalAmount-spc.ArrearAmount-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        END PayAmount,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        END SavingCardDeductionAmount,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        END SavingCardDeductionLargessAmount,
        ISNULL((
        SELECT SUM(ts.TotalAmount) FROM TB_SaleBillProject ts
        JOIN TB_SaleBill t ON t.ID=ts.BillID
        WHERE t.BillType='30' AND t.BillStatus='20' AND ts.ProjectAccountID=tpa.ID
        ),0)FillingMoney,
        tpa.ArrearAmount

        FROM TB_ProjectAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TB_Project t ON t.ID=tpa.ProjectID
        LEFT JOIN TB_ProjectCategory tepc ON tepc.ID=t.ProjectCategoryID
        LEFT JOIN TB_SaleBillProject tsb ON tsb.ID=tpa.SaleBillProjectID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Employee toe ON toe.ID=ts.EmployeeID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardProject spc ON spc.ID=tpa.SaleBillPackageCardProjectID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Employee toye ON toye.ID=tts.EmployeeID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE tpa.ArrearAmount>0 AND tpa.Active=1
        AND EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        )
        <if test="Name != null and Name != ''">
            AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+ #{Name}
            +'%')
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            AND t.Name LIKE '%'+ #{GoodsName} +'%'
        </if>
        <if test="EntityID != null and EntityID != ''">
            AND tec.EntityID=#{EntityID}
        </if>
        UNION ALL
        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        y.EntityName,
        tcl.Name LevelName,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN ts.ID
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN tts.ID
        END BillID,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN tey.ID
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN tety.ID
        END BuyEntityID,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN tey.EntityName
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN tety.EntityName
        END BuyEntityName,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN toe.Name
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN toye.Name
        END EmployeeName,
        '通用次卡' GoodsTypeName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tpa.TotalAmount,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN CAST(tsb.TotalAmount/tsb.Quantity as decimal(38,
        2))-CAST(tsb.ArrearAmount/tsb.Quantity as decimal(38,
        2))-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN CAST(spc.TotalAmount/spc.Quantity as decimal(38,
        2))-CAST(spc.ArrearAmount/spc.Quantity as decimal(38,
        2))-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        END PayAmount,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        END SavingCardDeductionAmount,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        END SavingCardDeductionLargessAmount,
        ISNULL((
        SELECT SUM(ts.TotalAmount) FROM TB_SaleBillGeneralCard ts
        JOIN TB_SaleBill t ON t.ID=ts.BillID
        WHERE t.BillType='30' AND t.BillStatus='20' AND ts.GeneralCardAccountID=tpa.ID
        ),0)FillingMoney,
        tpa.ArrearAmount

        FROM TB_GeneralCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TB_GeneralCard t ON t.ID=tpa.GeneralCardID
        LEFT JOIN TB_GeneralCardCategory tepc ON tepc.ID=t.GeneralCardCategoryID
        LEFT JOIN TB_SaleBillGeneralCard tsb ON tsb.ID=tpa.SaleBillGeneralCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Employee toe ON toe.ID=ts.EmployeeID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardGeneralCard spc ON spc.ID=tpa.SaleBillPackageCardGeneralCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Employee toye ON toye.ID=tts.EmployeeID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE tpa.ArrearAmount>0 AND tpa.Active=1
        AND EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        )
        <if test="Name != null and Name != ''">
            AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+ #{Name}
            +'%')
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            AND t.Name LIKE '%'+ #{GoodsName} +'%'
        </if>
        <if test="EntityID != null and EntityID != ''">
            AND tec.EntityID=#{EntityID}
        </if>
        UNION ALL
        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        y.EntityName,
        tcl.Name LevelName,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN ts.ID
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN tts.ID
        END BillID,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN tey.ID
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN tety.ID
        END BuyEntityID,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN tey.EntityName
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN tety.EntityName
        END BuyEntityName,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN toe.Name
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN toye.Name
        END EmployeeName,
        '时效卡' GoodsTypeName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tpa.TotalAmount,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN CAST(tsb.TotalAmount/tsb.Quantity as decimal(38,
        2))-CAST(tsb.ArrearAmount/tsb.Quantity as decimal(38,
        2))-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN CAST(spc.TotalAmount/spc.Quantity as decimal(38,
        2))-CAST(spc.ArrearAmount/spc.Quantity as decimal(38,
        2))-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        END PayAmount,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        END SavingCardDeductionAmount,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        END SavingCardDeductionLargessAmount,
        ISNULL((
        SELECT SUM(ts.TotalAmount) FROM TB_SaleBillTimeCard ts
        JOIN TB_SaleBill t ON t.ID=ts.BillID
        WHERE t.BillType='30' AND t.BillStatus='20' AND ts.TimeCardAccountID=tpa.ID
        ),0)FillingMoney,
        tpa.ArrearAmount

        FROM TB_TimeCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TB_TimeCard t ON t.ID=tpa.TimeCardID
        LEFT JOIN TB_TimeCardCategory tepc ON tepc.ID=t.TimeCardCategoryID
        LEFT JOIN TB_SaleBillTimeCard tsb ON tsb.ID=tpa.SaleBillTimeCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Employee toe ON toe.ID=ts.EmployeeID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardTimeCard spc ON spc.ID=tpa.SaleBillPackageCardTimeCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Employee toye ON toye.ID=tts.EmployeeID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE tpa.ArrearAmount>0 AND tpa.Active=1
        AND EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        )
        <if test="Name != null and Name != ''">
            AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+ #{Name}
            +'%')
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            AND t.Name LIKE '%'+ #{GoodsName} +'%'
        </if>
        <if test="EntityID != null and EntityID != ''">
            AND tec.EntityID=#{EntityID}
        </if>
        UNION ALL
        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        y.EntityName,
        tcl.Name LevelName,
        CASE WHEN tpa.SaleBillSavingCardID IS NOT NULL THEN ts.ID
        WHEN tpa.SaleBillPackageCardSavingCardID IS NOT NULL THEN tts.ID
        END BillID,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE WHEN tpa.SaleBillSavingCardID IS NOT NULL THEN tey.ID
        WHEN tpa.SaleBillPackageCardSavingCardID IS NOT NULL THEN tety.ID
        END BuyEntityID,
        CASE WHEN tpa.SaleBillSavingCardID IS NOT NULL THEN tey.EntityName
        WHEN tpa.SaleBillPackageCardSavingCardID IS NOT NULL THEN tety.EntityName
        END BuyEntityName,
        CASE WHEN tpa.SaleBillSavingCardID IS NOT NULL THEN toe.Name
        WHEN tpa.SaleBillPackageCardSavingCardID IS NOT NULL THEN toye.Name
        END EmployeeName,
        '储值卡' GoodsTypeName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tpa.Amount TotalAmount,
        CASE WHEN tpa.SaleBillSavingCardID IS NOT NULL THEN CAST(tsb.TotalAmount/tsb.Quantity as decimal(38,
        2))-CAST(tsb.ArrearAmount/tsb.Quantity as decimal(38, 2))
        WHEN tpa.SaleBillPackageCardSavingCardID IS NOT NULL THEN CAST(spc.TotalAmount/spc.Quantity as decimal(38,
        2))-CAST(spc.ArrearAmount/spc.Quantity as decimal(38, 2))
        END PayAmount,
        0 SavingCardDeductionAmount,
        0 SavingCardDeductionLargessAmount,
        ISNULL((
        SELECT SUM(ts.TotalAmount) FROM TB_SaleBillSavingCard ts
        JOIN TB_SaleBill t ON t.ID=ts.BillID
        WHERE t.BillType='30' AND t.BillStatus='20' AND ts.SavingCardAccountID=tpa.ID
        ),0)FillingMoney,
        tpa.ArrearAmount

        FROM TB_SavingCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TB_SavingCard t ON t.ID=tpa.SavingCardID
        LEFT JOIN TB_SavingCardCategory tepc ON tepc.ID=t.SavingCardCategoryID
        LEFT JOIN TB_SaleBillSavingCard tsb ON tsb.ID=tpa.SaleBillSavingCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Employee toe ON toe.ID=ts.EmployeeID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardSavingCard spc ON spc.ID=tpa.SaleBillPackageCardSavingCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Employee toye ON toye.ID=tts.EmployeeID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE tpa.ArrearAmount>0 AND tpa.Active=1
        AND EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        )
        <if test="Name != null and Name != ''">
            AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+ #{Name}
            +'%')
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            AND t.Name LIKE '%'+ #{GoodsName} +'%'
        </if>
        <if test="EntityID != null and EntityID != ''">
            AND tec.EntityID=#{EntityID}
        </if>
        )T
        <where>
            <if test="BuyEntityID != null and BuyEntityID != ''">
                and T.BuyEntityID =#{BuyEntityID}
            </if>
            <if test="GoodsTypeName != null and GoodsTypeName != ''">
                and T.GoodsTypeName =#{GoodsTypeName}
            </if>
            <if test="CategoryName != null and CategoryName != ''">
                and T.CategoryName LIKE '%'+ #{CategoryName} +'%'
            </if>
            <if test="StartTime!=null and StartTime!='' and EndTime!=null and EndTime!=''">
                AND  BuyDate >= #{StartTime} AND BuyDate &lt; DATEADD(DAY,1,#{EndTime})
            </if>
        </where>
        ORDER BY T.Name ,GoodsTypeName
    </select>

    <select id="getCustomerArrearSum"
            resultType="com.zhelian.model.report.form.output.CustomerArrearsSumStatementForm">
        SELECT
        ISNULL(SUM(T.TotalAmount),0) TotalAmount,
        ISNULL(SUM(T.PayAmount),0) PayAmount,
        ISNULL(SUM(T.SavingCardDeductionAmount),0) SavingCardDeductionAmount,
        ISNULL(SUM(T.SavingCardDeductionLargessAmount),0) SavingCardDeductionLargessAmount,
        ISNULL(SUM(T.FillingMoney),0) + ISNULL(SUM(T.ArrearAmount),0) OriginMoney,
        ISNULL(SUM(T.FillingMoney),0) FillingMoney,
        ISNULL(SUM(T.ArrearAmount),0)ArrearAmount

        FROM
        (
        SELECT
        tc.Name,
        tc.PhoneNumber,
        y.EntityName,
        tcl.Name LevelName,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN ts.ID
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN tts.ID
        END BillID,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN ts.EntityID
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN tts.EntityID
        END BuyEntityID,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN tey.EntityName
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN tety.EntityName
        END BuyEntityName,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN toe.Name
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN toye.Name
        END EmployeeName,
        '产品' GoodsTypeName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tpa.TotalAmount,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN
        tsb.TotalAmount-tsb.ArrearAmount-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN
        spc.TotalAmount-spc.ArrearAmount-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        END PayAmount,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        END SavingCardDeductionAmount,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        END SavingCardDeductionLargessAmount,
        ISNULL((
        SELECT SUM(ts.TotalAmount) FROM TB_SaleBillProduct ts
        JOIN TB_SaleBill t ON t.ID=ts.BillID
        WHERE t.BillType='30' AND t.BillStatus='20' AND ts.ProductAccountID=tpa.ID
        ),0)FillingMoney,
        tpa.ArrearAmount

        FROM TB_ProductAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TE_Product t ON t.ID=tpa.ProductID
        LEFT JOIN TE_ProductCategory tepc ON tepc.ID=t.PCategoryID
        LEFT JOIN TB_SaleBillProduct tsb ON tsb.ID=tpa.SaleBillProductID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Employee toe ON toe.ID=ts.EmployeeID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardProduct spc ON spc.ID=tpa.SaleBillPackageCardProductID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Employee toye ON toye.ID=tts.EmployeeID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE tpa.ArrearAmount>0 AND tpa.Active=1
        AND EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        )
        <if test="Name != null and Name != ''">
            AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+ #{Name}
            +'%')
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            AND t.Name LIKE '%'+ #{GoodsName} +'%'
        </if>
        <if test="EntityID != null and EntityID != ''">
            AND tec.EntityID=#{EntityID}
        </if>
        UNION ALL
        SELECT
        tc.Name,

        tc.PhoneNumber,
        y.EntityName,
        tcl.Name LevelName,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN ts.ID
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN tts.ID
        END BillID,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN tey.ID
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN tety.ID
        END BuyEntityID,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN tey.EntityName
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN tety.EntityName
        END BuyEntityName,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN toe.Name
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN toye.Name
        END EmployeeName,
        '项目' GoodsTypeName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tpa.TotalAmount,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN
        tsb.TotalAmount-tsb.ArrearAmount-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN
        spc.TotalAmount-spc.ArrearAmount-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        END PayAmount,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        END SavingCardDeductionAmount,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        END SavingCardDeductionLargessAmount,
        ISNULL((
        SELECT SUM(ts.TotalAmount) FROM TB_SaleBillProject ts
        JOIN TB_SaleBill t ON t.ID=ts.BillID
        WHERE t.BillType='30' AND t.BillStatus='20' AND ts.ProjectAccountID=tpa.ID
        ),0)FillingMoney,
        tpa.ArrearAmount

        FROM TB_ProjectAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TB_Project t ON t.ID=tpa.ProjectID
        LEFT JOIN TB_ProjectCategory tepc ON tepc.ID=t.ProjectCategoryID
        LEFT JOIN TB_SaleBillProject tsb ON tsb.ID=tpa.SaleBillProjectID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Employee toe ON toe.ID=ts.EmployeeID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardProject spc ON spc.ID=tpa.SaleBillPackageCardProjectID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Employee toye ON toye.ID=tts.EmployeeID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE tpa.ArrearAmount>0 AND tpa.Active=1
        AND EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        )
        <if test="Name != null and Name != ''">
            AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+ #{Name}
            +'%')
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            AND t.Name LIKE '%'+ #{GoodsName} +'%'
        </if>
        <if test="EntityID != null and EntityID != ''">
            AND tec.EntityID=#{EntityID}
        </if>
        UNION ALL
        SELECT
        tc.Name,

        tc.PhoneNumber,
        y.EntityName,
        tcl.Name LevelName,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN ts.ID
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN tts.ID
        END BillID,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN tey.ID
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN tety.ID
        END BuyEntityID,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN tey.EntityName
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN tety.EntityName
        END BuyEntityName,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN toe.Name
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN toye.Name
        END EmployeeName,
        '通用次卡' GoodsTypeName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tpa.TotalAmount,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN CAST(tsb.TotalAmount/tsb.Quantity as decimal(38,
        2))-CAST(tsb.ArrearAmount/tsb.Quantity as decimal(38,
        2))-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN CAST(spc.TotalAmount/spc.Quantity as decimal(38,
        2))-CAST(spc.ArrearAmount/spc.Quantity as decimal(38,
        2))-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        END PayAmount,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        END SavingCardDeductionAmount,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        END SavingCardDeductionLargessAmount,
        ISNULL((
        SELECT SUM(ts.TotalAmount) FROM TB_SaleBillGeneralCard ts
        JOIN TB_SaleBill t ON t.ID=ts.BillID
        WHERE t.BillType='30' AND t.BillStatus='20' AND ts.GeneralCardAccountID=tpa.ID
        ),0)FillingMoney,
        tpa.ArrearAmount

        FROM TB_GeneralCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TB_GeneralCard t ON t.ID=tpa.GeneralCardID
        LEFT JOIN TB_GeneralCardCategory tepc ON tepc.ID=t.GeneralCardCategoryID
        LEFT JOIN TB_SaleBillGeneralCard tsb ON tsb.ID=tpa.SaleBillGeneralCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Employee toe ON toe.ID=ts.EmployeeID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardGeneralCard spc ON spc.ID=tpa.SaleBillPackageCardGeneralCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Employee toye ON toye.ID=tts.EmployeeID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE tpa.ArrearAmount>0 AND tpa.Active=1
        AND EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        )
        <if test="Name != null and Name != ''">
            AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+ #{Name}
            +'%')
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            AND t.Name LIKE '%'+ #{GoodsName} +'%'
        </if>
        <if test="EntityID != null and EntityID != ''">
            AND tec.EntityID=#{EntityID}
        </if>
        UNION ALL
        SELECT
        tc.Name,

        tc.PhoneNumber,
        y.EntityName,
        tcl.Name LevelName,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN ts.ID
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN tts.ID
        END BillID,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN tey.ID
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN tety.ID
        END BuyEntityID,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN tey.EntityName
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN tety.EntityName
        END BuyEntityName,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN toe.Name
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN toye.Name
        END EmployeeName,
        '时效卡' GoodsTypeName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tpa.TotalAmount,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN CAST(tsb.TotalAmount/tsb.Quantity as decimal(38,
        2))-CAST(tsb.ArrearAmount/tsb.Quantity as decimal(38,
        2))-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN CAST(spc.TotalAmount/spc.Quantity as decimal(38,
        2))-CAST(spc.ArrearAmount/spc.Quantity as decimal(38,
        2))-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        END PayAmount,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        END SavingCardDeductionAmount,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        END SavingCardDeductionLargessAmount,
        ISNULL((
        SELECT SUM(ts.TotalAmount) FROM TB_SaleBillTimeCard ts
        JOIN TB_SaleBill t ON t.ID=ts.BillID
        WHERE t.BillType='30' AND t.BillStatus='20' AND ts.TimeCardAccountID=tpa.ID
        ),0)FillingMoney,
        tpa.ArrearAmount

        FROM TB_TimeCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TB_TimeCard t ON t.ID=tpa.TimeCardID
        LEFT JOIN TB_TimeCardCategory tepc ON tepc.ID=t.TimeCardCategoryID
        LEFT JOIN TB_SaleBillTimeCard tsb ON tsb.ID=tpa.SaleBillTimeCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Employee toe ON toe.ID=ts.EmployeeID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardTimeCard spc ON spc.ID=tpa.SaleBillPackageCardTimeCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Employee toye ON toye.ID=tts.EmployeeID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE tpa.ArrearAmount>0 AND tpa.Active=1
        AND EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        )
        <if test="Name != null and Name != ''">
            AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+ #{Name}
            +'%')
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            AND t.Name LIKE '%'+ #{GoodsName} +'%'
        </if>
        <if test="EntityID != null and EntityID != ''">
            AND tec.EntityID=#{EntityID}
        </if>
        UNION ALL
        SELECT
        tc.Name,

        tc.PhoneNumber,
        y.EntityName,
        tcl.Name LevelName,
        CASE WHEN tpa.SaleBillSavingCardID IS NOT NULL THEN ts.ID
        WHEN tpa.SaleBillPackageCardSavingCardID IS NOT NULL THEN tts.ID
        END BillID,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE WHEN tpa.SaleBillSavingCardID IS NOT NULL THEN tey.ID
        WHEN tpa.SaleBillPackageCardSavingCardID IS NOT NULL THEN tety.ID
        END BuyEntityID,
        CASE WHEN tpa.SaleBillSavingCardID IS NOT NULL THEN tey.EntityName
        WHEN tpa.SaleBillPackageCardSavingCardID IS NOT NULL THEN tety.EntityName
        END BuyEntityName,
        CASE WHEN tpa.SaleBillSavingCardID IS NOT NULL THEN toe.Name
        WHEN tpa.SaleBillPackageCardSavingCardID IS NOT NULL THEN toye.Name
        END EmployeeName,
        '储值卡' GoodsTypeName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tpa.Amount TotalAmount,
        CASE WHEN tpa.SaleBillSavingCardID IS NOT NULL THEN CAST(tsb.TotalAmount/tsb.Quantity as decimal(38,
        2))-CAST(tsb.ArrearAmount/tsb.Quantity as decimal(38, 2))
        WHEN tpa.SaleBillPackageCardSavingCardID IS NOT NULL THEN CAST(spc.TotalAmount/spc.Quantity as decimal(38,
        2))-CAST(spc.ArrearAmount/spc.Quantity as decimal(38, 2))
        END PayAmount,
        0 SavingCardDeductionAmount,
        0 SavingCardDeductionLargessAmount,
        ISNULL((
        SELECT SUM(ts.TotalAmount) FROM TB_SaleBillSavingCard ts
        JOIN TB_SaleBill t ON t.ID=ts.BillID
        WHERE t.BillType='30' AND t.BillStatus='20' AND ts.SavingCardAccountID=tpa.ID
        ),0)FillingMoney,
        tpa.ArrearAmount

        FROM TB_SavingCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TB_SavingCard t ON t.ID=tpa.SavingCardID
        LEFT JOIN TB_SavingCardCategory tepc ON tepc.ID=t.SavingCardCategoryID
        LEFT JOIN TB_SaleBillSavingCard tsb ON tsb.ID=tpa.SaleBillSavingCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Employee toe ON toe.ID=ts.EmployeeID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardSavingCard spc ON spc.ID=tpa.SaleBillPackageCardSavingCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Employee toye ON toye.ID=tts.EmployeeID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE tpa.ArrearAmount>0 AND tpa.Active=1
        AND EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        )
        <if test="Name != null and Name != ''">
            AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+ #{Name}
            +'%')
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            AND t.Name LIKE '%'+ #{GoodsName} +'%'
        </if>
        <if test="EntityID != null and EntityID != ''">
            AND tec.EntityID=#{EntityID}
        </if>
        )T
        <where>
            <if test="BuyEntityID != null and BuyEntityID != ''">
                and T.BuyEntityID =#{BuyEntityID}
            </if>
            <if test="GoodsTypeName != null and GoodsTypeName != ''">
                and T.GoodsTypeName =#{GoodsTypeName}
            </if>
            <if test="CategoryName != null and CategoryName != ''">
                and T.CategoryName LIKE '%'+ #{CategoryName} +'%'
            </if>
            <if test="StartTime!=null and StartTime!='' and EndTime!=null and EndTime!=''">
                AND  BuyDate >= #{StartTime} AND BuyDate &lt; DATEADD(DAY,1,#{EndTime})
            </if>
        </where>

    </select>

    <select id="getCustomerArrearExcel"
            resultType="com.zhelian.model.report.form.output.CustomerArrearsDetailStatementExcelForm">
        SELECT
        T.Name,
        ISNULL(T.Code,'') Code,
        ISNULL(REPLACE(T.PhoneNumber , SUBSTRING (T.PhoneNumber,4,4), '****'),'') PhoneNumber,
        ISNULL(T.EntityName,'') EntityName,
        ISNULL(T.LevelName,'') LevelName,
        ISNULL(T.BillID,'') BillID,
        ISNULL(T.BuyDate,'') BuyDate,
        ISNULL(T.BuyEntityName,'') BuyEntityName,
        ISNULL(T.EmployeeName,'') EmployeeName,
        ISNULL(T.GoodsTypeName,'') GoodsTypeName,
        ISNULL(T.CategoryName,'') CategoryName,
        ISNULL(T.GoodsName,'') GoodsName,
        ISNULL(T.PackageCardName,'') PackageCardName,
        ISNULL(T.TotalAmount,0) TotalAmount,
        ISNULL(T.PayAmount,0) PayAmount,
        ISNULL(T.SavingCardDeductionAmount,0) SavingCardDeductionAmount,
        ISNULL(T.SavingCardDeductionLargessAmount,0) SavingCardDeductionLargessAmount,
        ISNULL(T.FillingMoney,0)+ISNULL(T.ArrearAmount,0) OriginMoney,
        ISNULL(T.FillingMoney,0)FillingMoney,
        ISNULL(T.ArrearAmount,0)ArrearAmount
        FROM
        (
        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        y.EntityName,
        tcl.Name LevelName,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN ts.ID
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN tts.ID
        END BillID,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN ts.EntityID
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN tts.EntityID
        END BuyEntityID,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN tey.EntityName
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN tety.EntityName
        END BuyEntityName,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN toe.Name
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN toye.Name
        END EmployeeName,
        '产品' GoodsTypeName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tpa.TotalAmount,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN
        tsb.TotalAmount-tsb.ArrearAmount-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN
        spc.TotalAmount-spc.ArrearAmount-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        END PayAmount,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        END SavingCardDeductionAmount,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        END SavingCardDeductionLargessAmount,
        ISNULL((
        SELECT SUM(ts.TotalAmount) FROM TB_SaleBillProduct ts
        JOIN TB_SaleBill t ON t.ID=ts.BillID
        WHERE t.BillType='30' AND t.BillStatus='20' AND ts.ProductAccountID=tpa.ID
        ),0)FillingMoney,
        tpa.ArrearAmount

        FROM TB_ProductAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TE_Product t ON t.ID=tpa.ProductID
        LEFT JOIN TE_ProductCategory tepc ON tepc.ID=t.PCategoryID
        LEFT JOIN TB_SaleBillProduct tsb ON tsb.ID=tpa.SaleBillProductID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Employee toe ON toe.ID=ts.EmployeeID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardProduct spc ON spc.ID=tpa.SaleBillPackageCardProductID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Employee toye ON toye.ID=tts.EmployeeID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE tpa.ArrearAmount>0 AND tpa.Active=1
        AND EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        )
        <if test="Name != null and Name != ''">
            AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+ #{Name}
            +'%')
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            AND t.Name LIKE '%'+ #{GoodsName} +'%'
        </if>
        <if test="EntityID != null and EntityID != ''">
            AND tec.EntityID=#{EntityID}
        </if>
        UNION ALL
        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        y.EntityName,
        tcl.Name LevelName,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN ts.ID
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN tts.ID
        END BillID,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN tey.ID
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN tety.ID
        END BuyEntityID,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN tey.EntityName
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN tety.EntityName
        END BuyEntityName,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN toe.Name
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN toye.Name
        END EmployeeName,
        '项目' GoodsTypeName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tpa.TotalAmount,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN
        tsb.TotalAmount-tsb.ArrearAmount-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN
        spc.TotalAmount-spc.ArrearAmount-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        END PayAmount,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        END SavingCardDeductionAmount,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        END SavingCardDeductionLargessAmount,
        ISNULL((
        SELECT SUM(ts.TotalAmount) FROM TB_SaleBillProject ts
        JOIN TB_SaleBill t ON t.ID=ts.BillID
        WHERE t.BillType='30' AND t.BillStatus='20' AND ts.ProjectAccountID=tpa.ID
        ),0)FillingMoney,
        tpa.ArrearAmount

        FROM TB_ProjectAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TB_Project t ON t.ID=tpa.ProjectID
        LEFT JOIN TB_ProjectCategory tepc ON tepc.ID=t.ProjectCategoryID
        LEFT JOIN TB_SaleBillProject tsb ON tsb.ID=tpa.SaleBillProjectID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Employee toe ON toe.ID=ts.EmployeeID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardProject spc ON spc.ID=tpa.SaleBillPackageCardProjectID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Employee toye ON toye.ID=tts.EmployeeID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE tpa.ArrearAmount>0 AND tpa.Active=1
        AND EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        )
        <if test="Name != null and Name != ''">
            AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+ #{Name}
            +'%')
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            AND t.Name LIKE '%'+ #{GoodsName} +'%'
        </if>
        <if test="EntityID != null and EntityID != ''">
            AND tec.EntityID=#{EntityID}
        </if>
        UNION ALL
        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        y.EntityName,
        tcl.Name LevelName,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN ts.ID
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN tts.ID
        END BillID,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN tey.ID
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN tety.ID
        END BuyEntityID,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN tey.EntityName
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN tety.EntityName
        END BuyEntityName,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN toe.Name
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN toye.Name
        END EmployeeName,
        '通用次卡' GoodsTypeName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tpa.TotalAmount,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN CAST(tsb.TotalAmount/tsb.Quantity as decimal(38,
        2))-CAST(tsb.ArrearAmount/tsb.Quantity as decimal(38,
        2))-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN CAST(spc.TotalAmount/spc.Quantity as decimal(38,
        2))-CAST(spc.ArrearAmount/spc.Quantity as decimal(38,
        2))-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        END PayAmount,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        END SavingCardDeductionAmount,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        END SavingCardDeductionLargessAmount,
        ISNULL((
        SELECT SUM(ts.TotalAmount) FROM TB_SaleBillGeneralCard ts
        JOIN TB_SaleBill t ON t.ID=ts.BillID
        WHERE t.BillType='30' AND t.BillStatus='20' AND ts.GeneralCardAccountID=tpa.ID
        ),0)FillingMoney,
        tpa.ArrearAmount

        FROM TB_GeneralCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TB_GeneralCard t ON t.ID=tpa.GeneralCardID
        LEFT JOIN TB_GeneralCardCategory tepc ON tepc.ID=t.GeneralCardCategoryID
        LEFT JOIN TB_SaleBillGeneralCard tsb ON tsb.ID=tpa.SaleBillGeneralCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Employee toe ON toe.ID=ts.EmployeeID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardGeneralCard spc ON spc.ID=tpa.SaleBillPackageCardGeneralCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Employee toye ON toye.ID=tts.EmployeeID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE tpa.ArrearAmount>0 AND tpa.Active=1
        AND EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        )
        <if test="Name != null and Name != ''">
            AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+ #{Name}
            +'%')
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            AND t.Name LIKE '%'+ #{GoodsName} +'%'
        </if>
        <if test="EntityID != null and EntityID != ''">
            AND tec.EntityID=#{EntityID}
        </if>
        UNION ALL
        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        y.EntityName,
        tcl.Name LevelName,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN ts.ID
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN tts.ID
        END BillID,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN tey.ID
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN tety.ID
        END BuyEntityID,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN tey.EntityName
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN tety.EntityName
        END BuyEntityName,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN toe.Name
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN toye.Name
        END EmployeeName,
        '时效卡' GoodsTypeName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tpa.TotalAmount,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN CAST(tsb.TotalAmount/tsb.Quantity as decimal(38,
        2))-CAST(tsb.ArrearAmount/tsb.Quantity as decimal(38,
        2))-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN CAST(spc.TotalAmount/spc.Quantity as decimal(38,
        2))-CAST(spc.ArrearAmount/spc.Quantity as decimal(38,
        2))-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        END PayAmount,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        END SavingCardDeductionAmount,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        END SavingCardDeductionLargessAmount,
        ISNULL((
        SELECT SUM(ts.TotalAmount) FROM TB_SaleBillTimeCard ts
        JOIN TB_SaleBill t ON t.ID=ts.BillID
        WHERE t.BillType='30' AND t.BillStatus='20' AND ts.TimeCardAccountID=tpa.ID
        ),0)FillingMoney,
        tpa.ArrearAmount

        FROM TB_TimeCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TB_TimeCard t ON t.ID=tpa.TimeCardID
        LEFT JOIN TB_TimeCardCategory tepc ON tepc.ID=t.TimeCardCategoryID
        LEFT JOIN TB_SaleBillTimeCard tsb ON tsb.ID=tpa.SaleBillTimeCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Employee toe ON toe.ID=ts.EmployeeID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardTimeCard spc ON spc.ID=tpa.SaleBillPackageCardTimeCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Employee toye ON toye.ID=tts.EmployeeID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE tpa.ArrearAmount>0 AND tpa.Active=1
        AND EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        )
        <if test="Name != null and Name != ''">
            AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+ #{Name}
            +'%')
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            AND t.Name LIKE '%'+ #{GoodsName} +'%'
        </if>
        <if test="EntityID != null and EntityID != ''">
            AND tec.EntityID=#{EntityID}
        </if>
        UNION ALL
        SELECT
        tc.Name,
        tc.Code,

        tc.PhoneNumber,
        y.EntityName,
        tcl.Name LevelName,
        CASE WHEN tpa.SaleBillSavingCardID IS NOT NULL THEN ts.ID
        WHEN tpa.SaleBillPackageCardSavingCardID IS NOT NULL THEN tts.ID
        END BillID,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE WHEN tpa.SaleBillSavingCardID IS NOT NULL THEN tey.ID
        WHEN tpa.SaleBillPackageCardSavingCardID IS NOT NULL THEN tety.ID
        END BuyEntityID,
        CASE WHEN tpa.SaleBillSavingCardID IS NOT NULL THEN tey.EntityName
        WHEN tpa.SaleBillPackageCardSavingCardID IS NOT NULL THEN tety.EntityName
        END BuyEntityName,
        CASE WHEN tpa.SaleBillSavingCardID IS NOT NULL THEN toe.Name
        WHEN tpa.SaleBillPackageCardSavingCardID IS NOT NULL THEN toye.Name
        END EmployeeName,
        '储值卡' GoodsTypeName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tpa.Amount TotalAmount,
        CASE WHEN tpa.SaleBillSavingCardID IS NOT NULL THEN CAST(tsb.TotalAmount/tsb.Quantity as decimal(38,
        2))-CAST(tsb.ArrearAmount/tsb.Quantity as decimal(38, 2))
        WHEN tpa.SaleBillPackageCardSavingCardID IS NOT NULL THEN CAST(spc.TotalAmount/spc.Quantity as decimal(38,
        2))-CAST(spc.ArrearAmount/spc.Quantity as decimal(38, 2))
        END PayAmount,
        0 SavingCardDeductionAmount,
        0 SavingCardDeductionLargessAmount,
        ISNULL((
        SELECT SUM(ts.TotalAmount) FROM TB_SaleBillSavingCard ts
        JOIN TB_SaleBill t ON t.ID=ts.BillID
        WHERE t.BillType='30' AND t.BillStatus='20' AND ts.SavingCardAccountID=tpa.ID
        ),0)FillingMoney,
        tpa.ArrearAmount

        FROM TB_SavingCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TB_SavingCard t ON t.ID=tpa.SavingCardID
        LEFT JOIN TB_SavingCardCategory tepc ON tepc.ID=t.SavingCardCategoryID
        LEFT JOIN TB_SaleBillSavingCard tsb ON tsb.ID=tpa.SaleBillSavingCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Employee toe ON toe.ID=ts.EmployeeID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardSavingCard spc ON spc.ID=tpa.SaleBillPackageCardSavingCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Employee toye ON toye.ID=tts.EmployeeID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE tpa.ArrearAmount>0 AND tpa.Active=1
        AND EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        )
        <if test="Name != null and Name != ''">
            AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+ #{Name}
            +'%')
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            AND t.Name LIKE '%'+ #{GoodsName} +'%'
        </if>
        <if test="EntityID != null and EntityID != ''">
            AND tec.EntityID=#{EntityID}
        </if>
        )T
        <where>
            <if test="BuyEntityID != null and BuyEntityID != ''">
                and T.BuyEntityID =#{BuyEntityID}
            </if>
            <if test="GoodsTypeName != null and GoodsTypeName != ''">
                and T.GoodsTypeName =#{GoodsTypeName}
            </if>
            <if test="CategoryName != null and CategoryName != ''">
                and T.CategoryName LIKE '%'+ #{CategoryName} +'%'
            </if>
            <if test="StartTime!=null and StartTime!='' and EndTime!=null and EndTime!=''">
                AND  BuyDate >= #{StartTime} AND BuyDate &lt; DATEADD(DAY,1,#{EndTime})
            </if>
        </where>
        ORDER BY T.Name ,GoodsTypeName
    </select>

    <select id="getCustomerArrearEasyExcel"
            resultType="com.zhelian.model.report.form.output.CustomerArrearsDetailStatementEasyExcelForm">
        SELECT
        T.name,
        ISNULL(T.Code,'') code,
        ISNULL(REPLACE(T.PhoneNumber , SUBSTRING (T.PhoneNumber,4,4), '****'),'') phoneNumber,
        ISNULL(T.EntityName,'') entityName,
        ISNULL(T.LevelName,'') levelName,
        ISNULL(T.BillID,'') billID,
        ISNULL(T.BuyDate,'') buyDate,
        ISNULL(T.BuyEntityName,'') buyEntityName,
        ISNULL(T.EmployeeName,'') employeeName,
        ISNULL(T.GoodsTypeName,'') goodsTypeName,
        ISNULL(T.CategoryName,'') categoryName,
        ISNULL(T.GoodsName,'') goodsName,
        ISNULL(T.PackageCardName,'') packageCardName,
        ISNULL(T.TotalAmount,0) totalAmount,
        ISNULL(T.PayAmount,0) payAmount,
        ISNULL(T.SavingCardDeductionAmount,0) savingCardDeductionAmount,
        ISNULL(T.SavingCardDeductionLargessAmount,0) savingCardDeductionLargessAmount,
        ISNULL(T.FillingMoney,0)+ISNULL(T.ArrearAmount,0) originMoney,
        ISNULL(T.FillingMoney,0)fillingMoney,
        ISNULL(T.ArrearAmount,0)arrearAmount
        FROM
        (
        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        y.EntityName,
        tcl.Name LevelName,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN ts.ID
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN tts.ID
        END BillID,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN ts.EntityID
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN tts.EntityID
        END BuyEntityID,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN tey.EntityName
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN tety.EntityName
        END BuyEntityName,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN toe.Name
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN toye.Name
        END EmployeeName,
        '产品' GoodsTypeName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tpa.TotalAmount,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN
        tsb.TotalAmount-tsb.ArrearAmount-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN
        spc.TotalAmount-spc.ArrearAmount-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        END PayAmount,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        END SavingCardDeductionAmount,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        END SavingCardDeductionLargessAmount,
        ISNULL((
        SELECT SUM(ts.TotalAmount) FROM TB_SaleBillProduct ts
        JOIN TB_SaleBill t ON t.ID=ts.BillID
        WHERE t.BillType='30' AND t.BillStatus='20' AND ts.ProductAccountID=tpa.ID
        ),0)FillingMoney,
        tpa.ArrearAmount

        FROM TB_ProductAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TE_Product t ON t.ID=tpa.ProductID
        LEFT JOIN TE_ProductCategory tepc ON tepc.ID=t.PCategoryID
        LEFT JOIN TB_SaleBillProduct tsb ON tsb.ID=tpa.SaleBillProductID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Employee toe ON toe.ID=ts.EmployeeID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardProduct spc ON spc.ID=tpa.SaleBillPackageCardProductID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Employee toye ON toye.ID=tts.EmployeeID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE tpa.ArrearAmount>0 AND tpa.Active=1
        AND EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        )
        <if test="Name != null and Name != ''">
            AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+ #{Name}
            +'%')
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            AND t.Name LIKE '%'+ #{GoodsName} +'%'
        </if>
        <if test="EntityID != null and EntityID != ''">
            AND tec.EntityID=#{EntityID}
        </if>
        UNION ALL
        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        y.EntityName,
        tcl.Name LevelName,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN ts.ID
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN tts.ID
        END BillID,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN tey.ID
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN tety.ID
        END BuyEntityID,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN tey.EntityName
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN tety.EntityName
        END BuyEntityName,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN toe.Name
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN toye.Name
        END EmployeeName,
        '项目' GoodsTypeName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tpa.TotalAmount,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN
        tsb.TotalAmount-tsb.ArrearAmount-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN
        spc.TotalAmount-spc.ArrearAmount-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        END PayAmount,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        END SavingCardDeductionAmount,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        END SavingCardDeductionLargessAmount,
        ISNULL((
        SELECT SUM(ts.TotalAmount) FROM TB_SaleBillProject ts
        JOIN TB_SaleBill t ON t.ID=ts.BillID
        WHERE t.BillType='30' AND t.BillStatus='20' AND ts.ProjectAccountID=tpa.ID
        ),0)FillingMoney,
        tpa.ArrearAmount

        FROM TB_ProjectAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TB_Project t ON t.ID=tpa.ProjectID
        LEFT JOIN TB_ProjectCategory tepc ON tepc.ID=t.ProjectCategoryID
        LEFT JOIN TB_SaleBillProject tsb ON tsb.ID=tpa.SaleBillProjectID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Employee toe ON toe.ID=ts.EmployeeID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardProject spc ON spc.ID=tpa.SaleBillPackageCardProjectID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Employee toye ON toye.ID=tts.EmployeeID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE tpa.ArrearAmount>0 AND tpa.Active=1
        AND EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        )
        <if test="Name != null and Name != ''">
            AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+ #{Name}
            +'%')
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            AND t.Name LIKE '%'+ #{GoodsName} +'%'
        </if>
        <if test="EntityID != null and EntityID != ''">
            AND tec.EntityID=#{EntityID}
        </if>
        UNION ALL
        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        y.EntityName,
        tcl.Name LevelName,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN ts.ID
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN tts.ID
        END BillID,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN tey.ID
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN tety.ID
        END BuyEntityID,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN tey.EntityName
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN tety.EntityName
        END BuyEntityName,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN toe.Name
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN toye.Name
        END EmployeeName,
        '通用次卡' GoodsTypeName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tpa.TotalAmount,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN CAST(tsb.TotalAmount/tsb.Quantity as decimal(38,
        2))-CAST(tsb.ArrearAmount/tsb.Quantity as decimal(38,
        2))-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN CAST(spc.TotalAmount/spc.Quantity as decimal(38,
        2))-CAST(spc.ArrearAmount/spc.Quantity as decimal(38,
        2))-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        END PayAmount,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        END SavingCardDeductionAmount,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        END SavingCardDeductionLargessAmount,
        ISNULL((
        SELECT SUM(ts.TotalAmount) FROM TB_SaleBillGeneralCard ts
        JOIN TB_SaleBill t ON t.ID=ts.BillID
        WHERE t.BillType='30' AND t.BillStatus='20' AND ts.GeneralCardAccountID=tpa.ID
        ),0)FillingMoney,
        tpa.ArrearAmount

        FROM TB_GeneralCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TB_GeneralCard t ON t.ID=tpa.GeneralCardID
        LEFT JOIN TB_GeneralCardCategory tepc ON tepc.ID=t.GeneralCardCategoryID
        LEFT JOIN TB_SaleBillGeneralCard tsb ON tsb.ID=tpa.SaleBillGeneralCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Employee toe ON toe.ID=ts.EmployeeID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardGeneralCard spc ON spc.ID=tpa.SaleBillPackageCardGeneralCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Employee toye ON toye.ID=tts.EmployeeID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE tpa.ArrearAmount>0 AND tpa.Active=1
        AND EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        )
        <if test="Name != null and Name != ''">
            AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+ #{Name}
            +'%')
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            AND t.Name LIKE '%'+ #{GoodsName} +'%'
        </if>
        <if test="EntityID != null and EntityID != ''">
            AND tec.EntityID=#{EntityID}
        </if>
        UNION ALL
        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        y.EntityName,
        tcl.Name LevelName,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN ts.ID
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN tts.ID
        END BillID,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN tey.ID
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN tety.ID
        END BuyEntityID,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN tey.EntityName
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN tety.EntityName
        END BuyEntityName,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN toe.Name
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN toye.Name
        END EmployeeName,
        '时效卡' GoodsTypeName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tpa.TotalAmount,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN CAST(tsb.TotalAmount/tsb.Quantity as decimal(38,
        2))-CAST(tsb.ArrearAmount/tsb.Quantity as decimal(38,
        2))-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN CAST(spc.TotalAmount/spc.Quantity as decimal(38,
        2))-CAST(spc.ArrearAmount/spc.Quantity as decimal(38,
        2))-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        END PayAmount,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        END SavingCardDeductionAmount,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        END SavingCardDeductionLargessAmount,
        ISNULL((
        SELECT SUM(ts.TotalAmount) FROM TB_SaleBillTimeCard ts
        JOIN TB_SaleBill t ON t.ID=ts.BillID
        WHERE t.BillType='30' AND t.BillStatus='20' AND ts.TimeCardAccountID=tpa.ID
        ),0)FillingMoney,
        tpa.ArrearAmount

        FROM TB_TimeCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TB_TimeCard t ON t.ID=tpa.TimeCardID
        LEFT JOIN TB_TimeCardCategory tepc ON tepc.ID=t.TimeCardCategoryID
        LEFT JOIN TB_SaleBillTimeCard tsb ON tsb.ID=tpa.SaleBillTimeCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Employee toe ON toe.ID=ts.EmployeeID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardTimeCard spc ON spc.ID=tpa.SaleBillPackageCardTimeCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Employee toye ON toye.ID=tts.EmployeeID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE tpa.ArrearAmount>0 AND tpa.Active=1
        AND EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        )
        <if test="Name != null and Name != ''">
            AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+ #{Name}
            +'%')
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            AND t.Name LIKE '%'+ #{GoodsName} +'%'
        </if>
        <if test="EntityID != null and EntityID != ''">
            AND tec.EntityID=#{EntityID}
        </if>
        UNION ALL
        SELECT
        tc.Name,
        tc.Code,

        tc.PhoneNumber,
        y.EntityName,
        tcl.Name LevelName,
        CASE WHEN tpa.SaleBillSavingCardID IS NOT NULL THEN ts.ID
        WHEN tpa.SaleBillPackageCardSavingCardID IS NOT NULL THEN tts.ID
        END BillID,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE WHEN tpa.SaleBillSavingCardID IS NOT NULL THEN tey.ID
        WHEN tpa.SaleBillPackageCardSavingCardID IS NOT NULL THEN tety.ID
        END BuyEntityID,
        CASE WHEN tpa.SaleBillSavingCardID IS NOT NULL THEN tey.EntityName
        WHEN tpa.SaleBillPackageCardSavingCardID IS NOT NULL THEN tety.EntityName
        END BuyEntityName,
        CASE WHEN tpa.SaleBillSavingCardID IS NOT NULL THEN toe.Name
        WHEN tpa.SaleBillPackageCardSavingCardID IS NOT NULL THEN toye.Name
        END EmployeeName,
        '储值卡' GoodsTypeName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tpa.Amount TotalAmount,
        CASE WHEN tpa.SaleBillSavingCardID IS NOT NULL THEN CAST(tsb.TotalAmount/tsb.Quantity as decimal(38,
        2))-CAST(tsb.ArrearAmount/tsb.Quantity as decimal(38, 2))
        WHEN tpa.SaleBillPackageCardSavingCardID IS NOT NULL THEN CAST(spc.TotalAmount/spc.Quantity as decimal(38,
        2))-CAST(spc.ArrearAmount/spc.Quantity as decimal(38, 2))
        END PayAmount,
        0 SavingCardDeductionAmount,
        0 SavingCardDeductionLargessAmount,
        ISNULL((
        SELECT SUM(ts.TotalAmount) FROM TB_SaleBillSavingCard ts
        JOIN TB_SaleBill t ON t.ID=ts.BillID
        WHERE t.BillType='30' AND t.BillStatus='20' AND ts.SavingCardAccountID=tpa.ID
        ),0)FillingMoney,
        tpa.ArrearAmount

        FROM TB_SavingCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TB_SavingCard t ON t.ID=tpa.SavingCardID
        LEFT JOIN TB_SavingCardCategory tepc ON tepc.ID=t.SavingCardCategoryID
        LEFT JOIN TB_SaleBillSavingCard tsb ON tsb.ID=tpa.SaleBillSavingCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Employee toe ON toe.ID=ts.EmployeeID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardSavingCard spc ON spc.ID=tpa.SaleBillPackageCardSavingCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Employee toye ON toye.ID=tts.EmployeeID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE tpa.ArrearAmount>0 AND tpa.Active=1
        AND EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        )
        <if test="Name != null and Name != ''">
            AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+ #{Name}
            +'%')
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            AND t.Name LIKE '%'+ #{GoodsName} +'%'
        </if>
        <if test="EntityID != null and EntityID != ''">
            AND tec.EntityID=#{EntityID}
        </if>
        )T
        <where>
            <if test="BuyEntityID != null and BuyEntityID != ''">
                and T.BuyEntityID =#{BuyEntityID}
            </if>
            <if test="GoodsTypeName != null and GoodsTypeName != ''">
                and T.GoodsTypeName =#{GoodsTypeName}
            </if>
            <if test="CategoryName != null and CategoryName != ''">
                and T.CategoryName LIKE '%'+ #{CategoryName} +'%'
            </if>
            <if test="StartTime!=null and StartTime!='' and EndTime!=null and EndTime!=''">
                AND  BuyDate >= #{StartTime} AND BuyDate &lt; DATEADD(DAY,1,#{EndTime})
            </if>
        </where>
        ORDER BY T.Name ,GoodsTypeName
    </select>

    <select id="getCustomerArrearEasyExcelDisPlayPhone"
            resultType="com.zhelian.model.report.form.output.CustomerArrearsDetailStatementEasyExcelForm">
        SELECT
        T.name,
        ISNULL(T.Code,'') code,
        ISNULL(T.PhoneNumber,'') phoneNumber,
        ISNULL(T.EntityName,'') entityName,
        ISNULL(T.LevelName,'') levelName,
        ISNULL(T.BillID,'') billID,
        ISNULL(T.BuyDate,'') buyDate,
        ISNULL(T.BuyEntityName,'') buyEntityName,
        ISNULL(T.EmployeeName,'') employeeName,
        ISNULL(T.GoodsTypeName,'') goodsTypeName,
        ISNULL(T.CategoryName,'') categoryName,
        ISNULL(T.GoodsName,'') goodsName,
        ISNULL(T.PackageCardName,'') packageCardName,
        ISNULL(T.TotalAmount,0) totalAmount,
        ISNULL(T.PayAmount,0) payAmount,
        ISNULL(T.SavingCardDeductionAmount,0) savingCardDeductionAmount,
        ISNULL(T.SavingCardDeductionLargessAmount,0) savingCardDeductionLargessAmount,
        ISNULL(T.FillingMoney,0)+ISNULL(T.ArrearAmount,0) originMoney,
        ISNULL(T.FillingMoney,0)fillingMoney,
        ISNULL(T.ArrearAmount,0)arrearAmount
        FROM
        (
        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        y.EntityName,
        tcl.Name LevelName,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN ts.ID
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN tts.ID
        END BillID,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN ts.EntityID
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN tts.EntityID
        END BuyEntityID,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN tey.EntityName
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN tety.EntityName
        END BuyEntityName,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN toe.Name
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN toye.Name
        END EmployeeName,
        '产品' GoodsTypeName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tpa.TotalAmount,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN
        tsb.TotalAmount-tsb.ArrearAmount-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN
        spc.TotalAmount-spc.ArrearAmount-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        END PayAmount,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        END SavingCardDeductionAmount,
        CASE WHEN tpa.SaleBillProductID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardProductID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        END SavingCardDeductionLargessAmount,
        ISNULL((
        SELECT SUM(ts.TotalAmount) FROM TB_SaleBillProduct ts
        JOIN TB_SaleBill t ON t.ID=ts.BillID
        WHERE t.BillType='30' AND t.BillStatus='20' AND ts.ProductAccountID=tpa.ID
        ),0)FillingMoney,
        tpa.ArrearAmount

        FROM TB_ProductAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TE_Product t ON t.ID=tpa.ProductID
        LEFT JOIN TE_ProductCategory tepc ON tepc.ID=t.PCategoryID
        LEFT JOIN TB_SaleBillProduct tsb ON tsb.ID=tpa.SaleBillProductID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Employee toe ON toe.ID=ts.EmployeeID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardProduct spc ON spc.ID=tpa.SaleBillPackageCardProductID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Employee toye ON toye.ID=tts.EmployeeID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE tpa.ArrearAmount>0 AND tpa.Active=1
        AND EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        )
        <if test="Name != null and Name != ''">
            AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+ #{Name}
            +'%')
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            AND t.Name LIKE '%'+ #{GoodsName} +'%'
        </if>
        <if test="EntityID != null and EntityID != ''">
            AND tec.EntityID=#{EntityID}
        </if>
        UNION ALL
        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        y.EntityName,
        tcl.Name LevelName,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN ts.ID
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN tts.ID
        END BillID,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN tey.ID
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN tety.ID
        END BuyEntityID,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN tey.EntityName
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN tety.EntityName
        END BuyEntityName,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN toe.Name
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN toye.Name
        END EmployeeName,
        '项目' GoodsTypeName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tpa.TotalAmount,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN
        tsb.TotalAmount-tsb.ArrearAmount-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN
        spc.TotalAmount-spc.ArrearAmount-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        END PayAmount,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        END SavingCardDeductionAmount,
        CASE WHEN tpa.SaleBillProjectID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardProjectID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        END SavingCardDeductionLargessAmount,
        ISNULL((
        SELECT SUM(ts.TotalAmount) FROM TB_SaleBillProject ts
        JOIN TB_SaleBill t ON t.ID=ts.BillID
        WHERE t.BillType='30' AND t.BillStatus='20' AND ts.ProjectAccountID=tpa.ID
        ),0)FillingMoney,
        tpa.ArrearAmount

        FROM TB_ProjectAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TB_Project t ON t.ID=tpa.ProjectID
        LEFT JOIN TB_ProjectCategory tepc ON tepc.ID=t.ProjectCategoryID
        LEFT JOIN TB_SaleBillProject tsb ON tsb.ID=tpa.SaleBillProjectID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Employee toe ON toe.ID=ts.EmployeeID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardProject spc ON spc.ID=tpa.SaleBillPackageCardProjectID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Employee toye ON toye.ID=tts.EmployeeID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE tpa.ArrearAmount>0 AND tpa.Active=1
        AND EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        )
        <if test="Name != null and Name != ''">
            AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+ #{Name}
            +'%')
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            AND t.Name LIKE '%'+ #{GoodsName} +'%'
        </if>
        <if test="EntityID != null and EntityID != ''">
            AND tec.EntityID=#{EntityID}
        </if>
        UNION ALL
        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        y.EntityName,
        tcl.Name LevelName,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN ts.ID
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN tts.ID
        END BillID,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN tey.ID
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN tety.ID
        END BuyEntityID,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN tey.EntityName
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN tety.EntityName
        END BuyEntityName,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN toe.Name
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN toye.Name
        END EmployeeName,
        '通用次卡' GoodsTypeName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tpa.TotalAmount,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN CAST(tsb.TotalAmount/tsb.Quantity as decimal(38,
        2))-CAST(tsb.ArrearAmount/tsb.Quantity as decimal(38,
        2))-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN CAST(spc.TotalAmount/spc.Quantity as decimal(38,
        2))-CAST(spc.ArrearAmount/spc.Quantity as decimal(38,
        2))-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        END PayAmount,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        END SavingCardDeductionAmount,
        CASE WHEN tpa.SaleBillGeneralCardID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardGeneralCardID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        END SavingCardDeductionLargessAmount,
        ISNULL((
        SELECT SUM(ts.TotalAmount) FROM TB_SaleBillGeneralCard ts
        JOIN TB_SaleBill t ON t.ID=ts.BillID
        WHERE t.BillType='30' AND t.BillStatus='20' AND ts.GeneralCardAccountID=tpa.ID
        ),0)FillingMoney,
        tpa.ArrearAmount

        FROM TB_GeneralCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TB_GeneralCard t ON t.ID=tpa.GeneralCardID
        LEFT JOIN TB_GeneralCardCategory tepc ON tepc.ID=t.GeneralCardCategoryID
        LEFT JOIN TB_SaleBillGeneralCard tsb ON tsb.ID=tpa.SaleBillGeneralCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Employee toe ON toe.ID=ts.EmployeeID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardGeneralCard spc ON spc.ID=tpa.SaleBillPackageCardGeneralCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Employee toye ON toye.ID=tts.EmployeeID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE tpa.ArrearAmount>0 AND tpa.Active=1
        AND EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        )
        <if test="Name != null and Name != ''">
            AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+ #{Name}
            +'%')
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            AND t.Name LIKE '%'+ #{GoodsName} +'%'
        </if>
        <if test="EntityID != null and EntityID != ''">
            AND tec.EntityID=#{EntityID}
        </if>
        UNION ALL
        SELECT
        tc.Name,
        tc.Code,
        tc.PhoneNumber,
        y.EntityName,
        tcl.Name LevelName,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN ts.ID
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN tts.ID
        END BillID,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN tey.ID
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN tety.ID
        END BuyEntityID,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN tey.EntityName
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN tety.EntityName
        END BuyEntityName,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN toe.Name
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN toye.Name
        END EmployeeName,
        '时效卡' GoodsTypeName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tpa.TotalAmount,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN CAST(tsb.TotalAmount/tsb.Quantity as decimal(38,
        2))-CAST(tsb.ArrearAmount/tsb.Quantity as decimal(38,
        2))-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN CAST(spc.TotalAmount/spc.Quantity as decimal(38,
        2))-CAST(spc.ArrearAmount/spc.Quantity as decimal(38,
        2))-tpa.SavingCardDeductionAmount-tpa.SavingCardDeductionLargessAmount
        END PayAmount,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN tpa.SavingCardDeductionAmount
        END SavingCardDeductionAmount,
        CASE WHEN tpa.SaleBillTimeCardID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        WHEN tpa.SaleBillPackageCardTimeCardID IS NOT NULL THEN tpa.SavingCardDeductionLargessAmount
        END SavingCardDeductionLargessAmount,
        ISNULL((
        SELECT SUM(ts.TotalAmount) FROM TB_SaleBillTimeCard ts
        JOIN TB_SaleBill t ON t.ID=ts.BillID
        WHERE t.BillType='30' AND t.BillStatus='20' AND ts.TimeCardAccountID=tpa.ID
        ),0)FillingMoney,
        tpa.ArrearAmount

        FROM TB_TimeCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TB_TimeCard t ON t.ID=tpa.TimeCardID
        LEFT JOIN TB_TimeCardCategory tepc ON tepc.ID=t.TimeCardCategoryID
        LEFT JOIN TB_SaleBillTimeCard tsb ON tsb.ID=tpa.SaleBillTimeCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Employee toe ON toe.ID=ts.EmployeeID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardTimeCard spc ON spc.ID=tpa.SaleBillPackageCardTimeCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Employee toye ON toye.ID=tts.EmployeeID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE tpa.ArrearAmount>0 AND tpa.Active=1
        AND EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        )
        <if test="Name != null and Name != ''">
            AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+ #{Name}
            +'%')
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            AND t.Name LIKE '%'+ #{GoodsName} +'%'
        </if>
        <if test="EntityID != null and EntityID != ''">
            AND tec.EntityID=#{EntityID}
        </if>
        UNION ALL
        SELECT
        tc.Name,
        tc.Code,

        tc.PhoneNumber,
        y.EntityName,
        tcl.Name LevelName,
        CASE WHEN tpa.SaleBillSavingCardID IS NOT NULL THEN ts.ID
        WHEN tpa.SaleBillPackageCardSavingCardID IS NOT NULL THEN tts.ID
        END BillID,
        CONVERT(VARCHAR(19),tpa.BuyDate,120) BuyDate,
        CASE WHEN tpa.SaleBillSavingCardID IS NOT NULL THEN tey.ID
        WHEN tpa.SaleBillPackageCardSavingCardID IS NOT NULL THEN tety.ID
        END BuyEntityID,
        CASE WHEN tpa.SaleBillSavingCardID IS NOT NULL THEN tey.EntityName
        WHEN tpa.SaleBillPackageCardSavingCardID IS NOT NULL THEN tety.EntityName
        END BuyEntityName,
        CASE WHEN tpa.SaleBillSavingCardID IS NOT NULL THEN toe.Name
        WHEN tpa.SaleBillPackageCardSavingCardID IS NOT NULL THEN toye.Name
        END EmployeeName,
        '储值卡' GoodsTypeName,
        tepc.Name CategoryName,
        t.Name GoodsName,
        pca.PackageCardID,
        pc.Name PackageCardName,
        tpa.Amount TotalAmount,
        CASE WHEN tpa.SaleBillSavingCardID IS NOT NULL THEN CAST(tsb.TotalAmount/tsb.Quantity as decimal(38,
        2))-CAST(tsb.ArrearAmount/tsb.Quantity as decimal(38, 2))
        WHEN tpa.SaleBillPackageCardSavingCardID IS NOT NULL THEN CAST(spc.TotalAmount/spc.Quantity as decimal(38,
        2))-CAST(spc.ArrearAmount/spc.Quantity as decimal(38, 2))
        END PayAmount,
        0 SavingCardDeductionAmount,
        0 SavingCardDeductionLargessAmount,
        ISNULL((
        SELECT SUM(ts.TotalAmount) FROM TB_SaleBillSavingCard ts
        JOIN TB_SaleBill t ON t.ID=ts.BillID
        WHERE t.BillType='30' AND t.BillStatus='20' AND ts.SavingCardAccountID=tpa.ID
        ),0)FillingMoney,
        tpa.ArrearAmount

        FROM TB_SavingCardAccount tpa WITH(NOLOCK)
        JOIN TB_Customer tc ON tc.ID=tpa.CustomerID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tec ON tec.CustomerID=tc.ID AND tec.IsBelongEntity=1
        LEFT JOIN TK_Entity y ON y.ID=tec.EntityID
        LEFT JOIN TB_SavingCard t ON t.ID=tpa.SavingCardID
        LEFT JOIN TB_SavingCardCategory tepc ON tepc.ID=t.SavingCardCategoryID
        LEFT JOIN TB_SaleBillSavingCard tsb ON tsb.ID=tpa.SaleBillSavingCardID
        LEFT JOIN TB_SaleBill ts ON ts.ID=tsb.BillID
        LEFT JOIN TK_Employee toe ON toe.ID=ts.EmployeeID
        LEFT JOIN TK_Entity tey ON tey.ID=ts.EntityID
        LEFT JOIN TB_SaleBillPackageCardSavingCard spc ON spc.ID=tpa.SaleBillPackageCardSavingCardID
        LEFT JOIN TB_SaleBillPackageCard sp ON sp.ID=spc.SaleBillPackageCardID
        LEFT JOIN TB_SaleBill tts ON tts.ID=sp.BillID
        LEFT JOIN TK_Employee toye ON toye.ID=tts.EmployeeID
        LEFT JOIN TK_Entity tety ON tety.ID=tts.EntityID
        LEFT JOIN TB_PackageCardAccount pca ON pca.ID=tpa.PackageCardAccountID
        LEFT JOIN TB_PackageCard pc ON pc.ID=pca.PackageCardID
        WHERE tpa.ArrearAmount>0 AND tpa.Active=1
        AND EXISTS (
        SELECT 1 FROM TB_EmployeeEntityPermission EEP
        WHERE EEP.EmployeeID = #{EmployeeID} AND EEP.EntityID = tec.EntityID
        )
        <if test="Name != null and Name != ''">
            AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE '%'+ #{Name}
            +'%')
        </if>
        <if test="GoodsName != null and GoodsName != ''">
            AND t.Name LIKE '%'+ #{GoodsName} +'%'
        </if>
        <if test="EntityID != null and EntityID != ''">
            AND tec.EntityID=#{EntityID}
        </if>
        )T
        <where>
            <if test="BuyEntityID != null and BuyEntityID != ''">
                and T.BuyEntityID =#{BuyEntityID}
            </if>
            <if test="GoodsTypeName != null and GoodsTypeName != ''">
                and T.GoodsTypeName =#{GoodsTypeName}
            </if>
            <if test="CategoryName != null and CategoryName != ''">
                and T.CategoryName LIKE '%'+ #{CategoryName} +'%'
            </if>
            <if test="StartTime!=null and StartTime!='' and EndTime!=null and EndTime!=''">
                AND  BuyDate >= #{StartTime} AND BuyDate &lt; DATEADD(DAY,1,#{EndTime})
            </if>
        </where>
        ORDER BY T.Name ,GoodsTypeName
    </select>
</mapper>