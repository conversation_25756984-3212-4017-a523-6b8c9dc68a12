<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.ibeauty.CustomerSavingCardAccountMapper">

    <select id="getTreatBill" resultType="com.zhelian.model.ibeauty.form.output.CustomerSavingCardAccountOutputForm">
        SELECT tt.TreatBillID                          BillID,
               CONVERT(VARCHAR (19), tb.BillDate, 120) BillDate,
               SUM(tt.TotalAmount)                     Amount,
               SUM(tt.CardTreatAmount)                 CardTreatAmount,
               SUM(tt.LargessCardTreatAmount)          LargessCardTreatAmount,
               tee.Name                                EmployeeName,
               te.EntityName
        FROM TB_TreatBill tb WITH(NOLOCK)
			INNER JOIN TB_TreatBillSavingCard tt
        WITH (NOLOCK)
        ON tt.TreatBillID=tb.ID
            LEFT JOIN TK_Entity te
        WITH (NOLOCK)
        ON te.ID=tb.EntityID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tb.EmployeeID
        WHERE tt.SavingCardAccountID=#{ID} AND tb.BillType='10' AND tb.BillStatus='20'
        GROUP BY tt.TreatBillID, tb.BillDate, tee.Name, te.EntityName
        ORDER BY tb.BillDate DESC
    </select>

    <select id="getRefundBill"
            resultType="com.zhelian.model.ibeauty.form.output.CustomerSavingCardRefundAccountOutputForm">
        SELECT tt.ID                                    BillID,
               CONVERT(VARCHAR (19), tt.CreatedOn, 120) BillDate,
               tee.Name                                 EmployeeName,
               tb.Amount + tb.LargessAmount             TotalAmount,
               tb.Amount,
               tb.LargessAmount,
               te.EntityName
        FROM TB_RefundSaleBill tt WITH(NOLOCK)
            INNER JOIN TB_RefundSaleBillSavingCard tb
        WITH (NOLOCK)
        ON tt.ID=tb.RefundBillID
            LEFT JOIN TK_Entity te
        WITH (NOLOCK)
        ON te.ID=tt.EntityID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tt.CreatedBy
        WHERE tb.SavingCardAccountID=#{ID} AND tt.ApprovalStatus='30'
        ORDER BY tt.CreatedOn DESC
    </select>
    <select id="getRefundTreatBill"
            resultType="com.zhelian.model.ibeauty.form.output.CustomerSavingCardRefundAccountOutputForm">
        SELECT tt.ID                                    BillID,
               CONVERT(VARCHAR (19), tt.CreatedOn, 120) BillDate,
               SUM(tb.TotalAmount)                      TotalAmount,
               SUM(tb.Amount)                           Amount,
               SUM(tb.LargessAmount)                    LargessAmount,
               tee.Name                                 EmployeeName,
               te.EntityName
        FROM TB_RefundTreatBill tt WITH(NOLOCK)
            INNER JOIN TB_RefundTreatBillSavingCard tb
        WITH (NOLOCK)
        ON tt.ID=tb.RefundTreatBillID
            LEFT JOIN TK_Entity te
        WITH (NOLOCK)
        ON te.ID=tt.EntityID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tt.CreatedBy
        WHERE tb.SavingCardAccountID=#{ID} AND tt.ApprovalStatus='30'
        GROUP BY tt.ID, tt.CreatedOn, tee.Name, te.EntityName
        ORDER BY tt.CreatedOn DESC
    </select>
    <select id="getTransferBill"
            resultType="com.zhelian.model.ibeauty.form.output.CustomerSavingCardTransferAccountOutputForm">
        SELECT tt.ID                                    BillID,
               CONVERT(VARCHAR (19), tt.CreatedOn, 120) BillDate,
               tb.Amount,
               tb.LargessAmount,
               tb.Amount + tb.LargessAmount             TotalAmount,
               tee.Name                                 EmployeeName,
               te.EntityName,
               CASE
                   WHEN tb.TransferOutSavingCardAccountID = #{ID} THEN 10
                   WHEN tb.TransferInSavingCardAccountID = #{ID} THEN 20
                   END                                  TransferType
        FROM TB_TransferBill tt WITH(NOLOCK)
            INNER JOIN TB_TransferBillSavingCardAccount tb
        WITH (NOLOCK)
        ON tt.ID=tb.TransferBillID
            LEFT JOIN TK_Entity te
        WITH (NOLOCK)
        ON te.ID=tt.EntityID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tt.CreatedBy
        WHERE tb.TransferOutSavingCardAccountID=#{ID}
           OR tb.TransferInSavingCardAccountID=#{ID}
        ORDER BY tt.CreatedOn DESC
    </select>
    <select id="getRepayment" resultType="com.zhelian.model.ibeauty.form.output.CustomerRepaymentAccountOutputForm">
        SELECT tt.ID                                    BillID,
               CONVERT(VARCHAR (19), tt.CreatedOn, 120) BillDate,
               tb.Amount,
               tee.Name                                 EmployeeName,
               te.EntityName
        FROM TB_SaleBill tt WITH(NOLOCK)
            INNER JOIN TB_SaleBillSavingCard tb
        WITH (NOLOCK)
        ON tt.ID=tb.BillID
            LEFT JOIN TK_Entity te
        WITH (NOLOCK)
        ON te.ID=tt.EntityID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tt.EmployeeID
        WHERE tb.SavingCardAccountID=#{ID} AND tt.BillType='30' AND tt.BillStatus='20'
        ORDER BY tt.CreatedOn DESC
    </select>

    <select id="getPackageCardRepayment"
            resultType="com.zhelian.model.ibeauty.form.output.CustomerRepaymentAccountOutputForm">
        SELECT tt.ID                                    BillID,
               CONVERT(VARCHAR (19), tt.CreatedOn, 120) BillDate,
               tb.TotalAmount                           Amount,
               tee.Name                                 EmployeeName,
               te.EntityName
        FROM TB_SaleBill tt WITH(NOLOCK)
            INNER JOIN TB_SaleBillPackageCard tsp
        WITH (NOLOCK)
        ON tt.ID=tsp.BillID
            INNER JOIN TB_SaleBillPackageCardSavingCard tb
        WITH (NOLOCK)
        ON tsp.ID=tb.SaleBillPackageCardID
            LEFT JOIN TK_Entity te
        WITH (NOLOCK)
        ON te.ID=tt.EntityID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tt.EmployeeID
        WHERE tb.SavingCardAccountID=#{ID} AND tt.BillType='30' AND tt.BillStatus='20'
        ORDER BY tt.CreatedOn DESC
    </select>

    <select id="getArrearAmountProcess"
            resultType="com.zhelian.model.ibeauty.form.output.AccountArrearAmountProcessOutputForm">
        SELECT tt.ID,
               CONVERT(VARCHAR (19), tt.CreatedOn, 120) BillDate,
               tt.Amount,
               tee.Name                                 EmployeeName,
               te.EntityName
        FROM TB_SavingCardAccountArrearAmountProcess tt WITH(NOLOCK)
            LEFT JOIN  TK_Entity te
        WITH (NOLOCK)
        ON te.ID=tt.EntityID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tt.CreatedBy
        WHERE tt.SavingCardAccountID=#{ID}
        ORDER BY tt.CreatedOn DESC
    </select>

    <select id="savingCardDeduction"
            resultType="com.zhelian.model.ibeauty.form.output.CustomerSavingCardDeductionAccountOutputForm">
        SELECT ts.ID                                    BillID,
               CONVERT(VARCHAR (19), ts.CreatedOn, 120) BillDate,
               CASE
                   WHEN ts.BillType = '20' then 20
                   ELSE 10
                   END                                  DeductionType,
               tsp.TotalAmount,
               tsp.Amount,
               tsp.LargessAmount,
               tee.Name                                 EmployeeName,
               te.EntityName
        FROM TB_SaleBillPaySavingCardDeduction tsp WITH(NOLOCK)
                INNER JOIN TB_SaleBill ts
        WITH (NOLOCK)
        ON tsp.SaleBillID=ts.ID
            LEFT JOIN TK_Entity te
        WITH (NOLOCK)
        ON te.ID=ts.EntityID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=ts.EmployeeID
        WHERE ts.BillStatus='20' AND tsp.SavingCardAccountID=#{ID}
        ORDER BY ts.BillDate DESC
    </select>

    <select id="rechargeBill" resultType="com.zhelian.model.ibeauty.form.output.CustomerSavingCardRechargeOutputForm">
              SELECT
                  t.ID BillID,
                  tt.Amount,
                  tt.LargessAmount,
                  CONVERT (VARCHAR (19), t.BillDate, 120) BillDate,
                  te.EntityName,
                  tee.Name EmployeeName
              FROM TB_SaleBillSavingCard tt
              WITH (NOLOCK)
                  LEFT JOIN TB_SaleBill t
              WITH (NOLOCK)
              ON tt.BillID=t.ID
                  LEFT JOIN TK_Entity te
              WITH (NOLOCK)
              ON te.ID=t.EntityID
                  LEFT JOIN TK_Employee tee
              WITH (NOLOCK)
              ON tee.ID=t.EmployeeID
              WHERE t.BillStatus='20' AND t.BillType='40' AND tt.SavingCardAccountID=#{ID}
        ORDER BY BillDate
    </select>
</mapper>