<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.ibeauty.SaleEmployeePerformanceCommissionMapper">

    <select id="getSaleProductPerformance" resultType="com.zhelian.model.ibeauty.service.SalePerformanceEntity">
        SELECT *
        FROM dbo.GetSaleProductPerformance(#{EntityID}, #{EmployeeID}, #{SaleHandlerID}, #{GoodsID})
    </select>
    <select id="getSaleProjectPerformance" resultType="com.zhelian.model.ibeauty.service.SalePerformanceEntity">
        SELECT *
        FROM dbo.GetSaleProjectPerformance(#{EntityID}, #{EmployeeID}, #{SaleHandlerID}, #{GoodsID})
    </select>
    <select id="getSaleGeneralCardPerformance" resultType="com.zhelian.model.ibeauty.service.SalePerformanceEntity">
        SELECT *
        FROM dbo.GetSaleGeneralCardPerformance(#{EntityID}, #{EmployeeID}, #{SaleHandlerID}, #{GoodsID})
    </select>
    <select id="getSaleTimeCardPerformance" resultType="com.zhelian.model.ibeauty.service.SalePerformanceEntity">
        SELECT *
        FROM dbo.GetSaleTimeCardPerformance(#{EntityID}, #{EmployeeID}, #{SaleHandlerID}, #{GoodsID})
    </select>
    <select id="getSaleSavingCardPerformance" resultType="com.zhelian.model.ibeauty.service.SalePerformanceEntity">
        SELECT *
        FROM dbo.GetSaleSavingCardPerformance(#{EntityID}, #{EmployeeID}, #{SaleHandlerID}, #{GoodsID})
    </select>

    <select id="getSalePackageCardProductPerformance"
            resultType="com.zhelian.model.ibeauty.service.SalePerformanceEntity">
        SELECT *
        FROM dbo.GetSalePackageCardProductPerformance(#{EntityID}, #{EmployeeID}, #{SaleHandlerID}, #{GoodsID})
    </select>
    <select id="getSalePackageCardProjectPerformance"
            resultType="com.zhelian.model.ibeauty.service.SalePerformanceEntity">
        SELECT *
        FROM dbo.GetSalePackageCardProjectPerformance(#{EntityID}, #{EmployeeID}, #{SaleHandlerID}, #{GoodsID})
    </select>
    <select id="getSalePackageCardGeneralCardPerformance"
            resultType="com.zhelian.model.ibeauty.service.SalePerformanceEntity">
        SELECT *
        FROM dbo.GetSalePackageCardGeneralCardPerformance(#{EntityID}, #{EmployeeID}, #{SaleHandlerID}, #{GoodsID})
    </select>
    <select id="getSalePackageCardTimeCardPerformance"
            resultType="com.zhelian.model.ibeauty.service.SalePerformanceEntity">
        SELECT *
        FROM dbo.GetSalePackageCardTimeCardPerformance(#{EntityID}, #{EmployeeID}, #{SaleHandlerID}, #{GoodsID})
    </select>
    <select id="getSalePackageCardSavingCardPerformance"
            resultType="com.zhelian.model.ibeauty.service.SalePerformanceEntity">
        SELECT *
        FROM dbo.GetSalePackageCardSavingCardPerformance(#{EntityID}, #{EmployeeID}, #{SaleHandlerID}, #{GoodsID})
    </select>

    <select id="getSaleProductCommission" resultType="com.zhelian.model.ibeauty.service.SaleCommissionEntity">
        SELECT *
        FROM dbo.GetSaleProductCommission(#{EntityID}, #{EmployeeID}, #{SaleHandlerID}, #{GoodsID})
    </select>
    <select id="getSaleProjectCommission" resultType="com.zhelian.model.ibeauty.service.SaleCommissionEntity">
        SELECT *
        FROM dbo.GetSaleProjectCommission(#{EntityID}, #{EmployeeID}, #{SaleHandlerID}, #{GoodsID})
    </select>
    <select id="getSaleGeneralCardCommission" resultType="com.zhelian.model.ibeauty.service.SaleCommissionEntity">
        SELECT *
        FROM dbo.GetSaleGeneralCardCommission(#{EntityID}, #{EmployeeID}, #{SaleHandlerID}, #{GoodsID})
    </select>
    <select id="getSaleTimeCardCommission" resultType="com.zhelian.model.ibeauty.service.SaleCommissionEntity">
        SELECT *
        FROM dbo.GetSaleTimeCardCommission(#{EntityID}, #{EmployeeID}, #{SaleHandlerID}, #{GoodsID})
    </select>
    <select id="getSaleSavingCardCommission" resultType="com.zhelian.model.ibeauty.service.SaleCommissionEntity">
        SELECT *
        FROM dbo.GetSaleSavingCardCommission(#{EntityID}, #{EmployeeID}, #{SaleHandlerID}, #{GoodsID})
    </select>

    <select id="getSalePackageCardProductCommission"
            resultType="com.zhelian.model.ibeauty.service.SaleCommissionEntity">
        SELECT *
        FROM dbo.GetSalePackageCardProductCommission(#{EntityID}, #{EmployeeID}, #{SaleHandlerID}, #{GoodsID})
    </select>
    <select id="getSalePackageCardProjectCommission"
            resultType="com.zhelian.model.ibeauty.service.SaleCommissionEntity">
        SELECT *
        FROM dbo.GetSalePackageCardProjectCommission(#{EntityID}, #{EmployeeID}, #{SaleHandlerID}, #{GoodsID})
    </select>
    <select id="getSalePackageCardGeneralCardCommission"
            resultType="com.zhelian.model.ibeauty.service.SaleCommissionEntity">
        SELECT *
        FROM dbo.GetSalePackageCardGeneralCardCommission(#{EntityID}, #{EmployeeID}, #{SaleHandlerID}, #{GoodsID})
    </select>
    <select id="getSalePackageCardTimeCardCommission"
            resultType="com.zhelian.model.ibeauty.service.SaleCommissionEntity">
        SELECT *
        FROM dbo.GetSalePackageCardTimeCardCommission(#{EntityID}, #{EmployeeID}, #{SaleHandlerID}, #{GoodsID})
    </select>
    <select id="getSalePackageCardSavingCardCommission"
            resultType="com.zhelian.model.ibeauty.service.SaleCommissionEntity">
        SELECT *
        FROM dbo.GetSalePackageCardSavingCardCommission(#{EntityID}, #{EmployeeID}, #{SaleHandlerID}, #{GoodsID})
    </select>

    <insert id="createSaleEmployeePerformanceCommissionProduct" parameterType="java.util.List">
        INSERT INTO dbo.TB_SaleEmployeePerformanceCommissionProduct (
        EntityID,
        SaleBillID,
        SaleBillProductID,
        ProductID,
        BillDate,
        ProductSaleHandlerID,
        EmployeeID,
        PayPerformance,
        PayRateCommission,
        PayFixedCommission,
        SavingCardPerformance,
        SavingCardRateCommission,
        SavingCardFixedCommission,
        SavingCardLargessPerformance,
        SavingCardLargessRateCommission,
        SavingCardLargessFixedCommission,
        SpecialBenefitCommission
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.EntityID},
            #{item.SaleBillID},#{item.SaleBillGoodsID},#{item.GoodsID},#{item.BillDate},#{item.SaleHandlerID},
            #{item.EmployeeID},#{item.PayPerformance},#{item.PayRateCommission},#{item.PayFixedCommission},#{item.SavingCardPerformance},
            #{item.SavingCardRateCommission},#{item.SavingCardFixedCommission},#{item.SavingCardLargessPerformance},#{item.SavingCardLargessRateCommission},
            #{item.SavingCardLargessFixedCommission},#{item.SpecialBenefitCommission})
        </foreach>
    </insert>

    <insert id="createSaleEmployeePerformanceCommissionProject" parameterType="java.util.List">
        INSERT INTO dbo.TB_SaleEmployeePerformanceCommissionProject (
        EntityID,
        SaleBillID,
        SaleBillProjectID,
        ProjectID,
        BillDate,
        ProjectSaleHandlerID,
        EmployeeID,
        PayPerformance,
        PayRateCommission,
        PayFixedCommission,
        SavingCardPerformance,
        SavingCardRateCommission,
        SavingCardFixedCommission,
        SavingCardLargessPerformance,
        SavingCardLargessRateCommission,
        SavingCardLargessFixedCommission,
        SpecialBenefitCommission
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.EntityID},
            #{item.SaleBillID},#{item.SaleBillGoodsID},#{item.GoodsID},#{item.BillDate},#{item.SaleHandlerID},
            #{item.EmployeeID},#{item.PayPerformance},#{item.PayRateCommission},#{item.PayFixedCommission},#{item.SavingCardPerformance},
            #{item.SavingCardRateCommission},#{item.SavingCardFixedCommission},#{item.SavingCardLargessPerformance},#{item.SavingCardLargessRateCommission},
            #{item.SavingCardLargessFixedCommission},#{item.SpecialBenefitCommission})
        </foreach>
    </insert>

    <insert id="createSaleEmployeePerformanceCommissionGeneralCard" parameterType="java.util.List">
        INSERT INTO dbo.TB_SaleEmployeePerformanceCommissionGeneralCard (
        EntityID,
        SaleBillID,
        SaleBillGeneralCardID,
        GeneralCardID,
        BillDate,
        GeneralCardSaleHandlerID,
        EmployeeID,
        PayPerformance,
        PayRateCommission,
        PayFixedCommission,
        SavingCardPerformance,
        SavingCardRateCommission,
        SavingCardFixedCommission,
        SavingCardLargessPerformance,
        SavingCardLargessRateCommission,
        SavingCardLargessFixedCommission,
        SpecialBenefitCommission
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.EntityID},
            #{item.SaleBillID},#{item.SaleBillGoodsID},#{item.GoodsID},#{item.BillDate},#{item.SaleHandlerID},
            #{item.EmployeeID},#{item.PayPerformance},#{item.PayRateCommission},#{item.PayFixedCommission},#{item.SavingCardPerformance},
            #{item.SavingCardRateCommission},#{item.SavingCardFixedCommission},#{item.SavingCardLargessPerformance},#{item.SavingCardLargessRateCommission},
            #{item.SavingCardLargessFixedCommission},#{item.SpecialBenefitCommission})
        </foreach>
    </insert>

    <insert id="createSaleEmployeePerformanceCommissionTimeCard" parameterType="java.util.List">
        INSERT INTO dbo.TB_SaleEmployeePerformanceCommissionTimeCard (
        EntityID,
        SaleBillID,
        SaleBillTimeCardID,
        TimeCardID,
        BillDate,
        TimeCardSaleHandlerID,
        EmployeeID,
        PayPerformance,
        PayRateCommission,
        PayFixedCommission,
        SavingCardPerformance,
        SavingCardRateCommission,
        SavingCardFixedCommission,
        SavingCardLargessPerformance,
        SavingCardLargessRateCommission,
        SavingCardLargessFixedCommission,
        SpecialBenefitCommission
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.EntityID},
            #{item.SaleBillID},#{item.SaleBillGoodsID},#{item.GoodsID},#{item.BillDate},#{item.SaleHandlerID},
            #{item.EmployeeID},#{item.PayPerformance},#{item.PayRateCommission},#{item.PayFixedCommission},#{item.SavingCardPerformance},
            #{item.SavingCardRateCommission},#{item.SavingCardFixedCommission},#{item.SavingCardLargessPerformance},#{item.SavingCardLargessRateCommission},
            #{item.SavingCardLargessFixedCommission},#{item.SpecialBenefitCommission})
        </foreach>
    </insert>

    <insert id="createSaleEmployeePerformanceCommissionSavingCard" parameterType="java.util.List">
        INSERT INTO dbo.TB_SaleEmployeePerformanceCommissionSavingCard (
        EntityID,
        SaleBillID,
        SaleBillSavingCardID,
        SavingCardID,
        BillDate,
        SavingCardSaleHandlerID,
        EmployeeID,
        PayPerformance,
        PayRateCommission,
        PayFixedCommission,
        SpecialBenefitCommission
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.EntityID},
            #{item.SaleBillID},#{item.SaleBillGoodsID},#{item.GoodsID},#{item.BillDate},#{item.SaleHandlerID},
            #{item.EmployeeID},#{item.PayPerformance},#{item.PayRateCommission},#{item.PayFixedCommission},
            #{item.SpecialBenefitCommission})
        </foreach>
    </insert>

    <insert id="createSaleEmployeePerformanceCommissionPackageCardProduct" parameterType="java.util.List">
        INSERT INTO dbo.TB_SaleEmployeePerformanceCommissionPackageCardProduct (
        EntityID,
        SaleBillID,
        SaleBillPackageCardID,
        SaleBillPackageCardProductID,
        PackageCardID,
        ProductID,
        BillDate,
        PackageCardSaleHandlerID,
        EmployeeID,
        PayPerformance,
        PayRateCommission,
        PayFixedCommission,
        SavingCardPerformance,
        SavingCardRateCommission,
        SavingCardFixedCommission,
        SavingCardLargessPerformance,
        SavingCardLargessRateCommission,
        SavingCardLargessFixedCommission,
        SpecialBenefitCommission
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.EntityID},
            #{item.SaleBillID},#{item.SaleBillPackageCardID},#{item.SaleBillGoodsID},#{item.PackageCardID},#{item.GoodsID},#{item.BillDate},#{item.SaleHandlerID},
            #{item.EmployeeID},#{item.PayPerformance},#{item.PayRateCommission},#{item.PayFixedCommission},#{item.SavingCardPerformance},
            #{item.SavingCardRateCommission},#{item.SavingCardFixedCommission},#{item.SavingCardLargessPerformance},#{item.SavingCardLargessRateCommission},
            #{item.SavingCardLargessFixedCommission},#{item.SpecialBenefitCommission})
        </foreach>
    </insert>

    <insert id="createSaleEmployeePerformanceCommissionPackageCardProject" parameterType="java.util.List">
        INSERT INTO dbo.TB_SaleEmployeePerformanceCommissionPackageCardProject (
        EntityID,
        SaleBillID,
        SaleBillPackageCardID,
        SaleBillPackageCardProjectID,
        PackageCardID,
        ProjectID,
        BillDate,
        PackageCardSaleHandlerID,
        EmployeeID,
        PayPerformance,
        PayRateCommission,
        PayFixedCommission,
        SavingCardPerformance,
        SavingCardRateCommission,
        SavingCardFixedCommission,
        SavingCardLargessPerformance,
        SavingCardLargessRateCommission,
        SavingCardLargessFixedCommission,
        SpecialBenefitCommission
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.EntityID},
            #{item.SaleBillID},#{item.SaleBillPackageCardID},#{item.SaleBillGoodsID},#{item.PackageCardID},#{item.GoodsID},#{item.BillDate},#{item.SaleHandlerID},
            #{item.EmployeeID},#{item.PayPerformance},#{item.PayRateCommission},#{item.PayFixedCommission},#{item.SavingCardPerformance},
            #{item.SavingCardRateCommission},#{item.SavingCardFixedCommission},#{item.SavingCardLargessPerformance},#{item.SavingCardLargessRateCommission},
            #{item.SavingCardLargessFixedCommission},#{item.SpecialBenefitCommission})
        </foreach>
    </insert>

    <insert id="createSaleEmployeePerformanceCommissionPackageCardGeneralCard" parameterType="java.util.List">
        INSERT INTO dbo.TB_SaleEmployeePerformanceCommissionPackageCardGeneralCard (
        EntityID,
        SaleBillID,
        SaleBillPackageCardID,
        SaleBillPackageCardGeneralCardID,
        PackageCardID,
        GeneralCardID,
        BillDate,
        PackageCardSaleHandlerID,
        EmployeeID,
        PayPerformance,
        PayRateCommission,
        PayFixedCommission,
        SavingCardPerformance,
        SavingCardRateCommission,
        SavingCardFixedCommission,
        SavingCardLargessPerformance,
        SavingCardLargessRateCommission,
        SavingCardLargessFixedCommission,
        SpecialBenefitCommission
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.EntityID},
            #{item.SaleBillID},#{item.SaleBillPackageCardID},#{item.SaleBillGoodsID},#{item.PackageCardID},#{item.GoodsID},#{item.BillDate},#{item.SaleHandlerID},
            #{item.EmployeeID},#{item.PayPerformance},#{item.PayRateCommission},#{item.PayFixedCommission},#{item.SavingCardPerformance},
            #{item.SavingCardRateCommission},#{item.SavingCardFixedCommission},#{item.SavingCardLargessPerformance},#{item.SavingCardLargessRateCommission},
            #{item.SavingCardLargessFixedCommission},#{item.SpecialBenefitCommission})
        </foreach>
    </insert>

    <insert id="createSaleEmployeePerformanceCommissionPackageCardTimeCard" parameterType="java.util.List">
        INSERT INTO dbo.TB_SaleEmployeePerformanceCommissionPackageCardTimeCard (
        EntityID,
        SaleBillID,
        SaleBillPackageCardID,
        SaleBillPackageCardTimeCardID,
        PackageCardID,
        TimeCardID,
        BillDate,
        PackageCardSaleHandlerID,
        EmployeeID,
        PayPerformance,
        PayRateCommission,
        PayFixedCommission,
        SavingCardPerformance,
        SavingCardRateCommission,
        SavingCardFixedCommission,
        SavingCardLargessPerformance,
        SavingCardLargessRateCommission,
        SavingCardLargessFixedCommission,
        SpecialBenefitCommission
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.EntityID},
            #{item.SaleBillID},#{item.SaleBillPackageCardID},#{item.SaleBillGoodsID},#{item.PackageCardID},#{item.GoodsID},#{item.BillDate},#{item.SaleHandlerID},
            #{item.EmployeeID},#{item.PayPerformance},#{item.PayRateCommission},#{item.PayFixedCommission},#{item.SavingCardPerformance},
            #{item.SavingCardRateCommission},#{item.SavingCardFixedCommission},#{item.SavingCardLargessPerformance},#{item.SavingCardLargessRateCommission},
            #{item.SavingCardLargessFixedCommission},#{item.SpecialBenefitCommission})
        </foreach>
    </insert>

    <insert id="createSaleEmployeePerformanceCommissionPackageCardSavingCard" parameterType="java.util.List">
        INSERT INTO dbo.TB_SaleEmployeePerformanceCommissionPackageCardSavingCard (
        EntityID,
        SaleBillID,
        SaleBillPackageCardID,
        SaleBillPackageCardSavingCardID,
        PackageCardID,
        SavingCardID,
        BillDate,
        PackageCardSaleHandlerID,
        EmployeeID,
        PayPerformance,
        PayRateCommission,
        PayFixedCommission,
        SpecialBenefitCommission
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.EntityID},
            #{item.SaleBillID},#{item.SaleBillPackageCardID},#{item.SaleBillGoodsID},#{item.PackageCardID},#{item.GoodsID},#{item.BillDate},#{item.SaleHandlerID},
            #{item.EmployeeID},#{item.PayPerformance},#{item.PayRateCommission},#{item.PayFixedCommission},
            #{item.SpecialBenefitCommission})
        </foreach>
    </insert>


    <delete id="deleteSaleEmployeePerformanceCommissionProduct">
        DELETE
        FROM TB_SaleEmployeePerformanceCommissionProduct
        WHERE SaleBillID = #{SaleBillID}
    </delete>
    <delete id="deleteSaleEmployeePerformanceCommissionProject">
        DELETE
        FROM TB_SaleEmployeePerformanceCommissionProject
        WHERE SaleBillID = #{SaleBillID}
    </delete>
    <delete id="deleteSaleEmployeePerformanceCommissionGeneralCard">
        DELETE
        FROM TB_SaleEmployeePerformanceCommissionGeneralCard
        WHERE SaleBillID = #{SaleBillID}
    </delete>
    <delete id="deleteSaleEmployeePerformanceCommissionTimeCard">
        DELETE
        FROM TB_SaleEmployeePerformanceCommissionTimeCard
        WHERE SaleBillID = #{SaleBillID}
    </delete>
    <delete id="deleteSaleEmployeePerformanceCommissionSavingCard">
        DELETE
        FROM TB_SaleEmployeePerformanceCommissionSavingCard
        WHERE SaleBillID = #{SaleBillID}
    </delete>


    <delete id="deleteSaleEmployeePerformanceCommissionPackageCardProduct">
        DELETE
        FROM TB_SaleEmployeePerformanceCommissionPackageCardProduct
        WHERE SaleBillID = #{SaleBillID}
    </delete>
    <delete id="deleteSaleEmployeePerformanceCommissionPackageCardProject">
        DELETE
        FROM TB_SaleEmployeePerformanceCommissionPackageCardProject
        WHERE SaleBillID = #{SaleBillID}
    </delete>
    <delete id="deleteSaleEmployeePerformanceCommissionPackageCardGeneralCard">
        DELETE
        FROM TB_SaleEmployeePerformanceCommissionPackageCardGeneralCard
        WHERE SaleBillID = #{SaleBillID}
    </delete>
    <delete id="deleteSaleEmployeePerformanceCommissionPackageCardTimeCard">
        DELETE
        FROM TB_SaleEmployeePerformanceCommissionPackageCardTimeCard
        WHERE SaleBillID = #{SaleBillID}
    </delete>
    <delete id="deleteSaleEmployeePerformanceCommissionPackageCardSavingCard">
        DELETE
        FROM TB_SaleEmployeePerformanceCommissionPackageCardSavingCard
        WHERE SaleBillID = #{SaleBillID}
    </delete>


    <resultMap id="Product" type="com.zhelian.model.ibeauty.form.output.SaleBillProductOutputForm">
        <id column="SaleBillProductID" property="SaleBillProductID" jdbcType="INTEGER"/>
        <result column="ProductID" property="ProductID" jdbcType="INTEGER"/>
        <result column="ProductName" property="ProductName" jdbcType="VARCHAR"/>
        <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
        <result column="Price" property="Price" jdbcType="DECIMAL"/>
        <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
        <result column="AccountTotalAmount" property="AccountTotalAmount" jdbcType="DECIMAL"/>
        <result column="AccountQuantity" property="AccountQuantity" jdbcType="INTEGER"/>
        <result column="ArrearAmount" property="ArrearAmount" jdbcType="DECIMAL"/>
        <result column="PricePreferentialAmount" property="PricePreferentialAmount" jdbcType="DECIMAL"/>
        <result column="MemberPreferentialAmount" property="MemberPreferentialAmount" jdbcType="DECIMAL"/>
        <result column="CardPreferentialAmount" property="CardPreferentialAmount" jdbcType="DECIMAL"/>
        <result column="PreferentialTotalAmount" property="PreferentialTotalAmount" jdbcType="DECIMAL"/>
        <result column="PayAmount" property="PayAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionTotalAmount" property="CardDeductionTotalAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionAmount" property="CardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionLargessAmount" property="CardDeductionLargessAmount" jdbcType="DECIMAL"/>
        <result column="IsLargess" property="IsLargess" jdbcType="BIT"/>
        <collection property="SaleBillHandler"
                    ofType="com.zhelian.model.ibeauty.form.output.SaleBillHandlerListOutputForm">
            <id column="SaleHandlerID" property="SaleHandlerID" jdbcType="INTEGER"/>
            <result column="SaleHandlerName" property="SaleHandlerName" jdbcType="VARCHAR"/>
            <collection property="Employee"
                        ofType="com.zhelian.model.ibeauty.form.output.SaleBillGoodsEmployeePerformanceCommissionOutputForm">
                <result column="EmployeeID" property="EmployeeID" jdbcType="INTEGER"/>
                <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
                <result column="Scale" property="Scale" jdbcType="DECIMAL"/>
                <result column="PayPerformance" property="PayPerformance" jdbcType="DECIMAL"/>
                <result column="PayRateCommission" property="PayRateCommission" jdbcType="DECIMAL"/>
                <result column="PayFixedCommission" property="PayFixedCommission" jdbcType="DECIMAL"/>
                <result column="SavingCardPerformance" property="SavingCardPerformance" jdbcType="DECIMAL"/>
                <result column="SavingCardRateCommission" property="SavingCardRateCommission" jdbcType="DECIMAL"/>
                <result column="SavingCardFixedCommission" property="SavingCardFixedCommission" jdbcType="DECIMAL"/>
                <result column="SavingCardLargessPerformance" property="SavingCardLargessPerformance"
                        jdbcType="DECIMAL"/>
                <result column="SavingCardLargessRateCommission" property="SavingCardLargessRateCommission"
                        jdbcType="DECIMAL"/>
                <result column="SavingCardLargessFixedCommission" property="SavingCardLargessFixedCommission"
                        jdbcType="DECIMAL"/>
                <result column="SpecialBenefitCommission" property="SpecialBenefitCommission" jdbcType="DECIMAL"/>
                <result column="IsCalculatePassengerFlow" property="IsCalculatePassengerFlow" jdbcType="BOOLEAN"/>
            </collection>

        </collection>
    </resultMap>
    <resultMap id="Project" type="com.zhelian.model.ibeauty.form.output.SaleBillProjectOutputForm">
        <id column="SaleBillProjectID" property="SaleBillProjectID" jdbcType="INTEGER"/>
        <result column="ProjectID" property="ProjectID" jdbcType="INTEGER"/>
        <result column="ProjectName" property="ProjectName" jdbcType="VARCHAR"/>
        <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
        <result column="AccountQuantity" property="AccountQuantity" jdbcType="INTEGER"/>
        <result column="Price" property="Price" jdbcType="DECIMAL"/>
        <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
        <result column="AccountTotalAmount" property="AccountTotalAmount" jdbcType="DECIMAL"/>
        <result column="AccountQuantity" property="AccountQuantity" jdbcType="INTEGER"/>
        <result column="ArrearAmount" property="ArrearAmount" jdbcType="DECIMAL"/>
        <result column="PricePreferentialAmount" property="PricePreferentialAmount" jdbcType="DECIMAL"/>
        <result column="MemberPreferentialAmount" property="MemberPreferentialAmount" jdbcType="DECIMAL"/>
        <result column="CardPreferentialAmount" property="CardPreferentialAmount" jdbcType="DECIMAL"/>
        <result column="PreferentialTotalAmount" property="PreferentialTotalAmount" jdbcType="DECIMAL"/>
        <result column="PayAmount" property="PayAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionTotalAmount" property="CardDeductionTotalAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionAmount" property="CardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionLargessAmount" property="CardDeductionLargessAmount" jdbcType="DECIMAL"/>
        <result column="IsLargess" property="IsLargess" jdbcType="BIT"/>
        <collection property="SaleBillHandler"
                    ofType="com.zhelian.model.ibeauty.form.output.SaleBillHandlerListOutputForm">
            <id column="SaleHandlerID" property="SaleHandlerID" jdbcType="INTEGER"/>
            <result column="SaleHandlerName" property="SaleHandlerName" jdbcType="VARCHAR"/>
            <collection property="Employee"
                        ofType="com.zhelian.model.ibeauty.form.output.SaleBillGoodsEmployeePerformanceCommissionOutputForm">
                <result column="EmployeeID" property="EmployeeID" jdbcType="INTEGER"/>
                <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
                <result column="Scale" property="Scale" jdbcType="DECIMAL"/>
                <result column="PayPerformance" property="PayPerformance" jdbcType="DECIMAL"/>
                <result column="PayRateCommission" property="PayRateCommission" jdbcType="DECIMAL"/>
                <result column="PayFixedCommission" property="PayFixedCommission" jdbcType="DECIMAL"/>
                <result column="SavingCardPerformance" property="SavingCardPerformance" jdbcType="DECIMAL"/>
                <result column="SavingCardRateCommission" property="SavingCardRateCommission" jdbcType="DECIMAL"/>
                <result column="SavingCardFixedCommission" property="SavingCardFixedCommission" jdbcType="DECIMAL"/>
                <result column="SavingCardLargessPerformance" property="SavingCardLargessPerformance"
                        jdbcType="DECIMAL"/>
                <result column="SavingCardLargessRateCommission" property="SavingCardLargessRateCommission"
                        jdbcType="DECIMAL"/>
                <result column="SavingCardLargessFixedCommission" property="SavingCardLargessFixedCommission"
                        jdbcType="DECIMAL"/>
                <result column="SpecialBenefitCommission" property="SpecialBenefitCommission" jdbcType="DECIMAL"/>
                <result column="IsCalculatePassengerFlow" property="IsCalculatePassengerFlow" jdbcType="BOOLEAN"/>
            </collection>

        </collection>
    </resultMap>
    <resultMap id="GeneralCard" type="com.zhelian.model.ibeauty.form.output.SaleBillGeneralCardOutputForm">
        <id column="SaleBillGeneralCardID" property="SaleBillGeneralCardID" jdbcType="INTEGER"/>
        <result column="GeneralCardID" property="GeneralCardID" jdbcType="INTEGER"/>
        <result column="GeneralCardName" property="GeneralCardName" jdbcType="VARCHAR"/>
        <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
        <result column="CardTimes" property="CardTimes" jdbcType="INTEGER"/>
        <result column="AccountCardTimes" property="AccountCardTimes" jdbcType="INTEGER"/>
        <result column="Price" property="Price" jdbcType="DECIMAL"/>
        <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
        <result column="AccountTotalAmount" property="AccountTotalAmount" jdbcType="DECIMAL"/>
        <result column="AccountCardTimes" property="AccountCardTimes" jdbcType="DECIMAL"/>
        <result column="ArrearAmount" property="ArrearAmount" jdbcType="DECIMAL"/>
        <result column="PricePreferentialAmount" property="PricePreferentialAmount" jdbcType="DECIMAL"/>
        <result column="MemberPreferentialAmount" property="MemberPreferentialAmount" jdbcType="DECIMAL"/>
        <result column="CardPreferentialAmount" property="CardPreferentialAmount" jdbcType="DECIMAL"/>
        <result column="PreferentialTotalAmount" property="PreferentialTotalAmount" jdbcType="DECIMAL"/>
        <result column="PayAmount" property="PayAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionTotalAmount" property="CardDeductionTotalAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionAmount" property="CardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionLargessAmount" property="CardDeductionLargessAmount" jdbcType="DECIMAL"/>
        <result column="IsLargess" property="IsLargess" jdbcType="BIT"/>
        <collection property="SaleBillHandler"
                    ofType="com.zhelian.model.ibeauty.form.output.SaleBillHandlerListOutputForm">
            <id column="SaleHandlerID" property="SaleHandlerID" jdbcType="INTEGER"/>
            <result column="SaleHandlerName" property="SaleHandlerName" jdbcType="VARCHAR"/>
            <collection property="Employee"
                        ofType="com.zhelian.model.ibeauty.form.output.SaleBillGoodsEmployeePerformanceCommissionOutputForm">
                <result column="EmployeeID" property="EmployeeID" jdbcType="INTEGER"/>
                <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
                <result column="Scale" property="Scale" jdbcType="DECIMAL"/>
                <result column="PayPerformance" property="PayPerformance" jdbcType="DECIMAL"/>
                <result column="PayRateCommission" property="PayRateCommission" jdbcType="DECIMAL"/>
                <result column="PayFixedCommission" property="PayFixedCommission" jdbcType="DECIMAL"/>
                <result column="SavingCardPerformance" property="SavingCardPerformance" jdbcType="DECIMAL"/>
                <result column="SavingCardRateCommission" property="SavingCardRateCommission" jdbcType="DECIMAL"/>
                <result column="SavingCardFixedCommission" property="SavingCardFixedCommission" jdbcType="DECIMAL"/>
                <result column="SavingCardLargessPerformance" property="SavingCardLargessPerformance"
                        jdbcType="DECIMAL"/>
                <result column="SavingCardLargessRateCommission" property="SavingCardLargessRateCommission"
                        jdbcType="DECIMAL"/>
                <result column="SavingCardLargessFixedCommission" property="SavingCardLargessFixedCommission"
                        jdbcType="DECIMAL"/>
                <result column="SpecialBenefitCommission" property="SpecialBenefitCommission" jdbcType="DECIMAL"/>
                <result column="IsCalculatePassengerFlow" property="IsCalculatePassengerFlow" jdbcType="BOOLEAN"/>
            </collection>
        </collection>
    </resultMap>
    <resultMap id="TimeCard" type="com.zhelian.model.ibeauty.form.output.SaleBillTimeCardOutputForm">
        <id column="SaleBillTimeCardID" property="SaleBillTimeCardID" jdbcType="INTEGER"/>
        <result column="TimeCardID" property="TimeCardID" jdbcType="INTEGER"/>
        <result column="TimeCardName" property="TimeCardName" jdbcType="VARCHAR"/>
        <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
        <result column="Price" property="Price" jdbcType="DECIMAL"/>
        <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
        <result column="AccountTotalAmount" property="AccountTotalAmount" jdbcType="DECIMAL"/>
        <result column="ArrearAmount" property="ArrearAmount" jdbcType="DECIMAL"/>
        <result column="PricePreferentialAmount" property="PricePreferentialAmount" jdbcType="DECIMAL"/>
        <result column="MemberPreferentialAmount" property="MemberPreferentialAmount" jdbcType="DECIMAL"/>
        <result column="CardPreferentialAmount" property="CardPreferentialAmount" jdbcType="DECIMAL"/>
        <result column="PreferentialTotalAmount" property="PreferentialTotalAmount" jdbcType="DECIMAL"/>
        <result column="PayAmount" property="PayAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionTotalAmount" property="CardDeductionTotalAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionAmount" property="CardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionLargessAmount" property="CardDeductionLargessAmount" jdbcType="DECIMAL"/>
        <result column="IsLargess" property="IsLargess" jdbcType="BIT"/>
        <collection property="SaleBillHandler"
                    ofType="com.zhelian.model.ibeauty.form.output.SaleBillHandlerListOutputForm">
            <id column="SaleHandlerID" property="SaleHandlerID" jdbcType="INTEGER"/>
            <result column="SaleHandlerName" property="SaleHandlerName" jdbcType="VARCHAR"/>
            <collection property="Employee"
                        ofType="com.zhelian.model.ibeauty.form.output.SaleBillGoodsEmployeePerformanceCommissionOutputForm">
                <result column="EmployeeID" property="EmployeeID" jdbcType="INTEGER"/>
                <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
                <result column="Scale" property="Scale" jdbcType="DECIMAL"/>
                <result column="PayPerformance" property="PayPerformance" jdbcType="DECIMAL"/>
                <result column="PayRateCommission" property="PayRateCommission" jdbcType="DECIMAL"/>
                <result column="PayFixedCommission" property="PayFixedCommission" jdbcType="DECIMAL"/>
                <result column="SavingCardPerformance" property="SavingCardPerformance" jdbcType="DECIMAL"/>
                <result column="SavingCardRateCommission" property="SavingCardRateCommission" jdbcType="DECIMAL"/>
                <result column="SavingCardFixedCommission" property="SavingCardFixedCommission" jdbcType="DECIMAL"/>
                <result column="SavingCardLargessPerformance" property="SavingCardLargessPerformance"
                        jdbcType="DECIMAL"/>
                <result column="SavingCardLargessRateCommission" property="SavingCardLargessRateCommission"
                        jdbcType="DECIMAL"/>
                <result column="SavingCardLargessFixedCommission" property="SavingCardLargessFixedCommission"
                        jdbcType="DECIMAL"/>
                <result column="SpecialBenefitCommission" property="SpecialBenefitCommission" jdbcType="DECIMAL"/>
                <result column="IsCalculatePassengerFlow" property="IsCalculatePassengerFlow" jdbcType="BOOLEAN"/>
            </collection>

        </collection>
    </resultMap>
    <resultMap id="SavingCard" type="com.zhelian.model.ibeauty.form.output.SaleBillSavingCardOutputForm">
        <id column="SaleBillSavingCardID" property="SaleBillSavingCardID" jdbcType="INTEGER"/>
        <result column="SavingCardID" property="SavingCardID" jdbcType="INTEGER"/>
        <result column="SavingCardName" property="SavingCardName" jdbcType="VARCHAR"/>
        <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
        <result column="Price" property="Price" jdbcType="DECIMAL"/>
        <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
        <result column="AccountTotalAmount" property="AccountTotalAmount" jdbcType="DECIMAL"/>
        <result column="AccountLargessAmount" property="AccountLargessAmount" jdbcType="DECIMAL"/>
        <result column="ArrearAmount" property="ArrearAmount" jdbcType="DECIMAL"/>
        <result column="Amount" property="Amount" jdbcType="DECIMAL"/>
        <result column="LargessAmount" property="LargessAmount" jdbcType="DECIMAL"/>
        <result column="PayAmount" property="PayAmount" jdbcType="DECIMAL"/>
        <result column="Price" property="Price" jdbcType="DECIMAL"/>
        <result column="LargessPrice" property="LargessPrice" jdbcType="DECIMAL"/>
        <collection property="SaleBillHandler"
                    ofType="com.zhelian.model.ibeauty.form.output.SaleBillSavingCardHandlerListOutputForm">
            <id column="SaleHandlerID" property="SaleHandlerID" jdbcType="INTEGER"/>
            <result column="SaleHandlerName" property="SaleHandlerName" jdbcType="VARCHAR"/>
            <collection property="Employee"
                        ofType="com.zhelian.model.ibeauty.form.output.SaleBillSavingCardEmployeePerformanceCommissionOutputForm">
                <result column="EmployeeID" property="EmployeeID" jdbcType="INTEGER"/>
                <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
                <result column="Scale" property="Scale" jdbcType="DECIMAL"/>
                <result column="PayPerformance" property="PayPerformance" jdbcType="DECIMAL"/>
                <result column="PayRateCommission" property="PayRateCommission" jdbcType="DECIMAL"/>
                <result column="PayFixedCommission" property="PayFixedCommission" jdbcType="DECIMAL"/>
                <result column="SpecialBenefitCommission" property="SpecialBenefitCommission" jdbcType="DECIMAL"/>
                <result column="IsCalculatePassengerFlow" property="IsCalculatePassengerFlow" jdbcType="BOOLEAN"/>
            </collection>

        </collection>
    </resultMap>


    <select id="saleBillProductEmployeePerformanceCommission" resultMap="Product">
        SELECT tsp.ID                                                   SaleBillProductID,
               tsp.ProductID,
               te.Name                                                  ProductName,
               te.Alias,
               tsp.Quantity,
               tsp.Price,
               tsp.TotalAmount,
               tsp.ArrearAmount,
               tsp.PricePreferentialAmount,
               tsp.MemberPreferentialAmount,
               tsp.CardPreferentialAmount,
               tsp.MemberPreferentialAmount + tsp.PricePreferentialAmount + tsp.CardPreferentialAmount PreferentialTotalAmount,
               (SELECT ISNULL(SUM(Amount), 0) FROM TB_SaleBillPayProduct
        WITH (NOLOCK)
        WHERE SaleBillProductID=tsp.ID) PayAmount
            , (
        SELECT ISNULL(SUM (Amount), 0)
        FROM TB_SaleBillPaySavingCardDeductionProduct
        WITH (NOLOCK)
        WHERE SaleBillProductID=tsp.ID) CardDeductionAmount
            , (
        SELECT ISNULL(SUM (LargessAmount), 0)
        FROM TB_SaleBillPaySavingCardDeductionProduct
        WITH (NOLOCK)
        WHERE SaleBillProductID=tsp.ID) CardDeductionLargessAmount
            , (
        SELECT ISNULL(SUM (TotalAmount), 0)
        FROM TB_SaleBillPaySavingCardDeductionProduct
        WITH (NOLOCK)
        WHERE SaleBillProductID=tsp.ID) CardDeductionTotalAmount
            , tsp.IsLargess
            , tsh.ProductSaleHandlerID SaleHandlerID
            , tp.Name SaleHandlerName
            , tsh.EmployeeID
            , tsh.IsCalculatePassengerFlow
            , tee.Name EmployeeName
            , tsh.Scale*100 Scale
            , tsep.PayPerformance
            , tsep.PayRateCommission
            , tsep.PayFixedCommission
            , tsep.SavingCardPerformance
            , tsep.SavingCardRateCommission
            , tsep.SavingCardFixedCommission
            , tsep.SavingCardLargessPerformance
            , tsep.SavingCardLargessRateCommission
            , tsep.SavingCardLargessFixedCommission
            , tsep.SpecialBenefitCommission
            , CASE WHEN pac.IsLargess = 1 THEN 0
            ELSE pac.TotalAmount
        END AccountTotalAmount
        ,
            pac.Quantity AS AccountQuantity
            FROM  TB_SaleBillProduct tsp WITH(NOLOCK)
            LEFT JOIN  TE_Product te WITH(NOLOCK)  ON  te.ID=tsp.ProductID
            LEFT JOIN TB_SaleEntityPerformanceProduct tse WITH(NOLOCK) ON  tse.SaleBillID=tsp.BillID  AND tse.SaleBillProductID=tsp.ID
            AND tse.ProductID=tsp.ProductID
            LEFT JOIN  TB_SaleBillHandlerProduct tsh WITH(NOLOCK)  ON  tsh.SaleBillProductID=tsp.ID
            LEFT JOIN TB_SaleEmployeePerformanceCommissionProduct tsep WITH(NOLOCK) ON tsh.SaleBillProductID=tsep.SaleBillProductID
            AND tsh.ProductSaleHandlerID=tsep.ProductSaleHandlerID AND tsh.EmployeeID=tsep.EmployeeID
            LEFT JOIN TB_ProductSaleHandler tp WITH(NOLOCK)  ON  tp.ID=tsh.ProductSaleHandlerID
            LEFT JOIN TK_Employee tee WITH(NOLOCK)  ON tee.ID=tsh.EmployeeID
            LEFT JOIN TB_ProductAccount pac WITH(NOLOCK)  ON pac.ID=tsp.ProductAccountID
            WHERE tsp.BillID=
        #{SaleBillID}
        ORDER
        BY
        tp
        .
        Sequence
        desc
    </select>

    <select id="saleBillProjectEmployeePerformanceCommission" resultMap="Project">
        SELECT tsp.ID                                                   SaleBillProjectID,
               tsp.ProjectID,
               te.Name                                                  ProjectName,
               te.Alias,
               tsp.Quantity,
               tsp.Price,
               tsp.TotalAmount,
               tsp.ArrearAmount,
               tsp.PricePreferentialAmount,
               tsp.MemberPreferentialAmount,
               tsp.CardPreferentialAmount,
               tsp.MemberPreferentialAmount + tsp.PricePreferentialAmount + tsp.CardPreferentialAmount PreferentialTotalAmount,
               (SELECT ISNULL(SUM(Amount), 0) FROM TB_SaleBillPayProject
        WITH (NOLOCK)
        WHERE SaleBillProjectID=tsp.ID) PayAmount
            , (
        SELECT ISNULL(SUM (Amount), 0)
        FROM TB_SaleBillPaySavingCardDeductionProject
        WITH (NOLOCK)
        WHERE SaleBillProjectID=tsp.ID) CardDeductionAmount
            , (
        SELECT ISNULL(SUM (LargessAmount), 0)
        FROM TB_SaleBillPaySavingCardDeductionProject
        WITH (NOLOCK)
        WHERE SaleBillProjectID=tsp.ID) CardDeductionLargessAmount
            , (
        SELECT ISNULL(SUM (TotalAmount), 0)
        FROM TB_SaleBillPaySavingCardDeductionProject
        WITH (NOLOCK)
        WHERE SaleBillProjectID=tsp.ID) CardDeductionTotalAmount
            , tsp.IsLargess
            , tp.ID SaleHandlerID
            , tp.Name SaleHandlerName
            , tsh.EmployeeID
            , tsh.IsCalculatePassengerFlow
            , tee.Name EmployeeName
            , tsh.Scale*100 Scale
            , tsep.PayPerformance
            , tsep.PayRateCommission
            , tsep.PayFixedCommission
            , tsep.SavingCardPerformance
            , tsep.SavingCardRateCommission
            , tsep.SavingCardFixedCommission
            , tsep.SavingCardLargessPerformance
            , tsep.SavingCardLargessRateCommission
            , tsep.SavingCardLargessFixedCommission
            , tsep.SpecialBenefitCommission
            , CASE WHEN pac.IsLargess = 1 THEN 0
            ELSE pac.TotalAmount
        END AccountTotalAmount
        ,
            pac.Quantity AS AccountQuantity
            FROM  TB_SaleBillProject tsp WITH(NOLOCK)
            LEFT JOIN  TB_Project te WITH(NOLOCK)  ON  te.ID=tsp.ProjectID
            LEFT JOIN TB_SaleEntityPerformanceProject tse WITH(NOLOCK) ON  tse.SaleBillID=tsp.BillID  AND tse.SaleBillProjectID=tsp.ID
            AND tse.ProjectID=tsp.ProjectID
            LEFT JOIN  TB_SaleBillHandlerProject tsh WITH(NOLOCK)  ON  tsh.SaleBillProjectID=tsp.ID
            LEFT JOIN TB_SaleEmployeePerformanceCommissionProject tsep WITH(NOLOCK) ON tsh.SaleBillProjectID=tsep.SaleBillProjectID
            AND tsh.ProjectSaleHandlerID=tsep.ProjectSaleHandlerID AND tsh.EmployeeID=tsep.EmployeeID
            LEFT JOIN TB_ProjectSaleHandler tp WITH(NOLOCK)  ON  tp.ID=tsh.ProjectSaleHandlerID
            LEFT JOIN TK_Employee tee WITH(NOLOCK)  ON tee.ID=tsh.EmployeeID
            LEFT JOIN TB_ProjectAccount pac WITH(NOLOCK)  ON pac.ID=tsp.ProjectAccountID
            WHERE tsp.BillID=
        #{SaleBillID}
        ORDER
        BY
        tp
        .
        Sequence
        desc
    </select>

    <select id="saleBillGeneralCardEmployeePerformanceCommission" resultMap="GeneralCard">
        SELECT tsp.ID                                                   SaleBillGeneralCardID,
               tsp.GeneralCardID,
               te.Name                                                  GeneralCardName,
               te.Alias,
               tsp.Quantity,
               tsp.CardTimes,
               tsp.Price,
               tsp.TotalAmount,
               tsp.ArrearAmount,
               tsp.PricePreferentialAmount,
               tsp.MemberPreferentialAmount,
               tsp.CardPreferentialAmount,
               tsp.MemberPreferentialAmount + tsp.PricePreferentialAmount + tsp.CardPreferentialAmount PreferentialTotalAmount,
               (SELECT ISNULL(SUM(Amount), 0) FROM TB_SaleBillPayGeneralCard
        WITH (NOLOCK)
        WHERE SaleBillGeneralCardID=tsp.ID) PayAmount
            , (
        SELECT ISNULL(SUM (Amount), 0)
        FROM TB_SaleBillPaySavingCardDeductionGeneralCard
        WITH (NOLOCK)
        WHERE SaleBillGeneralCardID=tsp.ID) CardDeductionAmount
            , (
        SELECT ISNULL(SUM (LargessAmount), 0)
        FROM TB_SaleBillPaySavingCardDeductionGeneralCard
        WITH (NOLOCK)
        WHERE SaleBillGeneralCardID=tsp.ID) CardDeductionLargessAmount
            , (
        SELECT ISNULL(SUM (TotalAmount), 0)
        FROM TB_SaleBillPaySavingCardDeductionGeneralCard
        WITH (NOLOCK)
        WHERE SaleBillGeneralCardID=tsp.ID) CardDeductionTotalAmount
            , tsp.IsLargess
            , tsh.GeneralCardSaleHandlerID SaleHandlerID
            , tp.Name SaleHandlerName
            , tsh.EmployeeID
            , tsh.IsCalculatePassengerFlow
            , tee.Name EmployeeName
            , tsh.Scale*100 Scale
            , tsep.PayPerformance
            , tsep.PayRateCommission
            , tsep.PayFixedCommission
            , tsep.SavingCardPerformance
            , tsep.SavingCardRateCommission
            , tsep.SavingCardFixedCommission
            , tsep.SavingCardLargessPerformance
            , tsep.SavingCardLargessRateCommission
            , tsep.SavingCardLargessFixedCommission
            , tsep.SpecialBenefitCommission
            , CASE WHEN pac.IsLargess = 1 THEN 0
            ELSE pac.TotalAmount
        END AccountTotalAmount
        ,
            pac.CardTimes AS AccountCardTimes
            FROM  TB_SaleBillGeneralCard tsp WITH(NOLOCK)
            LEFT JOIN  TB_GeneralCard te WITH(NOLOCK)  ON  te.ID=tsp.GeneralCardID
            LEFT JOIN TB_SaleEntityPerformanceGeneralCard tse WITH(NOLOCK) ON  tse.SaleBillID=tsp.BillID  AND tse.SaleBillGeneralCardID=tsp.ID
            AND tse.GeneralCardID=tsp.GeneralCardID
            LEFT JOIN  TB_SaleBillHandlerGeneralCard tsh WITH(NOLOCK)  ON  tsh.SaleBillGeneralCardID=tsp.ID
            LEFT JOIN TB_SaleEmployeePerformanceCommissionGeneralCard tsep WITH(NOLOCK) ON tsh.SaleBillGeneralCardID=tsep.SaleBillGeneralCardID
            AND tsh.GeneralCardSaleHandlerID=tsep.GeneralCardSaleHandlerID AND tsh.EmployeeID=tsep.EmployeeID
            LEFT JOIN TB_GeneralCardSaleHandler tp WITH(NOLOCK)  ON  tp.ID=tsh.GeneralCardSaleHandlerID
            LEFT JOIN TK_Employee tee WITH(NOLOCK)  ON tee.ID=tsh.EmployeeID
            LEFT JOIN TB_GeneralCardAccount pac WITH(NOLOCK)  ON pac.ID=tsp.GeneralCardAccountID
            WHERE tsp.BillID=
        #{SaleBillID}
        ORDER
        BY
        tp
        .
        Sequence
        desc
    </select>

    <select id="saleBillTimeCardEmployeePerformanceCommission" resultMap="TimeCard">
        SELECT tsp.ID                                                   SaleBillTimeCardID,
               tsp.TimeCardID,
               te.Name                                                  TimeCardName,
               te.Alias,
               tsp.Quantity,
               tsp.Price,
               tsp.TotalAmount,
               tsp.ArrearAmount,
               tsp.PricePreferentialAmount,
               tsp.MemberPreferentialAmount,
               tsp.CardPreferentialAmount,
               tsp.MemberPreferentialAmount + tsp.PricePreferentialAmount + tsp.CardPreferentialAmount PreferentialTotalAmount,
               (SELECT ISNULL(SUM(Amount), 0) FROM TB_SaleBillPayTimeCard
        WITH (NOLOCK)
        WHERE SaleBillTimeCardID=tsp.ID) PayAmount
            , (
        SELECT ISNULL(SUM (Amount), 0)
        FROM TB_SaleBillPaySavingCardDeductionTimeCard
        WITH (NOLOCK)
        WHERE SaleBillTimeCardID=tsp.ID) CardDeductionAmount
            , (
        SELECT ISNULL(SUM (LargessAmount), 0)
        FROM TB_SaleBillPaySavingCardDeductionTimeCard
        WITH (NOLOCK)
        WHERE SaleBillTimeCardID=tsp.ID) CardDeductionLargessAmount
            , (
        SELECT ISNULL(SUM (TotalAmount), 0)
        FROM TB_SaleBillPaySavingCardDeductionTimeCard
        WITH (NOLOCK)
        WHERE SaleBillTimeCardID=tsp.ID) CardDeductionTotalAmount
            , tsp.IsLargess
            , tsh.TimeCardSaleHandlerID SaleHandlerID
            , tp.Name SaleHandlerName
            , tsh.EmployeeID
            , tsh.IsCalculatePassengerFlow
            , tee.Name EmployeeName
            , tsh.Scale*100 Scale
            , tsep.PayPerformance
            , tsep.PayRateCommission
            , tsep.PayFixedCommission
            , tsep.SavingCardPerformance
            , tsep.SavingCardRateCommission
            , tsep.SavingCardFixedCommission
            , tsep.SavingCardLargessPerformance
            , tsep.SavingCardLargessRateCommission
            , tsep.SavingCardLargessFixedCommission
            , tsep.SpecialBenefitCommission
            , CASE WHEN pac.IsLargess = 1 THEN 0
            ELSE pac.TotalAmount
        END AccountTotalAmount
        FROM  TB_SaleBillTimeCard tsp WITH(NOLOCK)
            LEFT JOIN  TB_TimeCard te WITH(NOLOCK)  ON  te.ID=tsp.TimeCardID
            LEFT JOIN TB_SaleEntityPerformanceTimeCard tse WITH(NOLOCK) ON  tse.SaleBillID=tsp.BillID  AND tse.SaleBillTimeCardID=tsp.ID
            AND tse.TimeCardID=tsp.TimeCardID
            LEFT JOIN  TB_SaleBillHandlerTimeCard tsh WITH(NOLOCK)  ON  tsh.SaleBillTimeCardID=tsp.ID
            LEFT JOIN TB_SaleEmployeePerformanceCommissionTimeCard tsep WITH(NOLOCK) ON tsh.SaleBillTimeCardID=tsep.SaleBillTimeCardID
            AND tsh.TimeCardSaleHandlerID=tsep.TimeCardSaleHandlerID AND tsh.EmployeeID=tsep.EmployeeID
            LEFT JOIN TB_TimeCardSaleHandler tp WITH(NOLOCK)  ON  tp.ID=tsh.TimeCardSaleHandlerID
            LEFT JOIN TK_Employee tee WITH(NOLOCK)  ON tee.ID=tsh.EmployeeID
            LEFT JOIN TB_TimeCardAccount pac WITH(NOLOCK)  ON pac.ID=tsp.TimeCardAccountID
            WHERE tsp.BillID=
        #{SaleBillID}
        ORDER
        BY
        tp
        .
        Sequence
        desc
    </select>

    <select id="saleBillSavingCardEmployeePerformanceCommission" resultMap="SavingCard">
        SELECT tsp.ID                                                      SaleBillSavingCardID,
               tsp.SavingCardID,
               te.Name                                                     SavingCardName,
               te.Alias,
               tsp.Quantity,
               tsp.TotalAmount,
               tsp.ArrearAmount,
               tsp.Amount,
               tsp.LargessAmount,
               tsh.SavingCardSaleHandlerID                                 SaleHandlerID,
               tp.Name                                                     SaleHandlerName,
               tsh.EmployeeID,
               tsh.IsCalculatePassengerFlow,
               tee.Name                                                    EmployeeName,
               tsh.Scale * 100                                             Scale,
               tsep.PayPerformance,
               tsep.PayRateCommission,
               tsep.PayFixedCommission,
               tsep.SpecialBenefitCommission,
               tsp.Amount                                                  Price,
               CAST(tsp.LargessAmount / tsp.Quantity as decimal(38, 2)) AS LargessPrice,
               tsp.TotalAmount - tsp.ArrearAmount                          PayAmount,
               pac.Amount                                                  AccountTotalAmount,
               pac.LargessAmount                                        AS AccountLargessAmount
        FROM TB_SaleBillSavingCard tsp WITH(NOLOCK)
        LEFT JOIN  TB_SavingCard te
        WITH (NOLOCK)
        ON te.ID=tsp.SavingCardID
            LEFT JOIN TB_SaleEntityPerformanceSavingCard tse
        WITH (NOLOCK)
        ON tse.SaleBillID=tsp.BillID AND tse.SaleBillSavingCardID=tsp.ID
            AND tse.SavingCardID=tsp.SavingCardID
            LEFT JOIN TB_SaleBillHandlerSavingCard tsh
        WITH (NOLOCK)
        ON tsh.SaleBillSavingCardID=tsp.ID
            LEFT JOIN TB_SaleEmployeePerformanceCommissionSavingCard tsep
        WITH (NOLOCK)
        ON tsh.SaleBillSavingCardID=tsep.SaleBillSavingCardID
            AND tsh.SavingCardSaleHandlerID=tsep.SavingCardSaleHandlerID AND tsh.EmployeeID=tsep.EmployeeID
            LEFT JOIN TB_SavingCardSaleHandler tp
        WITH (NOLOCK)
        ON tp.ID=tsh.SavingCardSaleHandlerID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tsh.EmployeeID
            LEFT JOIN TB_SavingCardAccount pac
        WITH (NOLOCK)
        ON pac.ID=tsp.SavingCardAccountID
        WHERE tsp.BillID=#{SaleBillID}
        ORDER BY tp.Sequence desc
    </select>

    <resultMap id="PackageCardProduct"
               type="com.zhelian.model.ibeauty.form.output.SaleBillPackageCardProductListOutputForm">
        <id column="SaleBillPackageCardProductID" property="SaleBillPackageCardProductID" jdbcType="INTEGER"/>
        <id column="SaleBillPackageCardID" property="SaleBillPackageCardID" jdbcType="INTEGER"/>
        <result column="ProductID" property="ProductID" jdbcType="INTEGER"/>
        <result column="ProductName" property="ProductName" jdbcType="VARCHAR"/>
        <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
        <result column="Price" property="Price" jdbcType="DECIMAL"/>
        <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
        <result column="AccountTotalAmount" property="AccountTotalAmount" jdbcType="DECIMAL"/>
        <result column="AccountQuantity" property="AccountQuantity" jdbcType="INTEGER"/>
        <result column="ArrearAmount" property="ArrearAmount" jdbcType="DECIMAL"/>
        <result column="PricePreferentialAmount" property="PricePreferentialAmount" jdbcType="DECIMAL"/>
        <result column="MemberPreferentialAmount" property="MemberPreferentialAmount" jdbcType="DECIMAL"/>
        <result column="CardPreferentialAmount" property="CardPreferentialAmount" jdbcType="DECIMAL"/>
        <result column="PreferentialTotalAmount" property="PreferentialTotalAmount" jdbcType="DECIMAL"/>
        <result column="PayAmount" property="PayAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionTotalAmount" property="CardDeductionTotalAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionAmount" property="CardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionLargessAmount" property="CardDeductionLargessAmount" jdbcType="DECIMAL"/>
        <result column="IsLargess" property="IsLargess" jdbcType="BIT"/>
        <collection property="SaleBillHandler"
                    ofType="com.zhelian.model.ibeauty.form.output.SaleBillHandlerListOutputForm">
            <id column="SaleHandlerID" property="SaleHandlerID" jdbcType="INTEGER"/>
            <result column="SaleHandlerName" property="SaleHandlerName" jdbcType="VARCHAR"/>
            <collection property="Employee"
                        ofType="com.zhelian.model.ibeauty.form.output.SaleBillGoodsEmployeePerformanceCommissionOutputForm">
                <result column="EmployeeID" property="EmployeeID" jdbcType="INTEGER"/>
                <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
                <result column="Scale" property="Scale" jdbcType="DECIMAL"/>
                <result column="PayPerformance" property="PayPerformance" jdbcType="DECIMAL"/>
                <result column="PayRateCommission" property="PayRateCommission" jdbcType="DECIMAL"/>
                <result column="PayFixedCommission" property="PayFixedCommission" jdbcType="DECIMAL"/>
                <result column="SavingCardPerformance" property="SavingCardPerformance" jdbcType="DECIMAL"/>
                <result column="SavingCardRateCommission" property="SavingCardRateCommission" jdbcType="DECIMAL"/>
                <result column="SavingCardFixedCommission" property="SavingCardFixedCommission" jdbcType="DECIMAL"/>
                <result column="SavingCardLargessPerformance" property="SavingCardLargessPerformance"
                        jdbcType="DECIMAL"/>
                <result column="SavingCardLargessRateCommission" property="SavingCardLargessRateCommission"
                        jdbcType="DECIMAL"/>
                <result column="SavingCardLargessFixedCommission" property="SavingCardLargessFixedCommission"
                        jdbcType="DECIMAL"/>
                <result column="SpecialBenefitCommission" property="SpecialBenefitCommission" jdbcType="DECIMAL"/>
                <result column="IsCalculatePassengerFlow" property="IsCalculatePassengerFlow" jdbcType="BOOLEAN"/>
            </collection>

        </collection>
    </resultMap>
    <resultMap id="PackageCardProject"
               type="com.zhelian.model.ibeauty.form.output.SaleBillPackageCardProjectListOutputForm">
        <id column="SaleBillPackageCardProjectID" property="SaleBillPackageCardProjectID" jdbcType="INTEGER"/>
        <id column="SaleBillPackageCardID" property="SaleBillPackageCardID" jdbcType="INTEGER"/>
        <result column="ProjectID" property="ProjectID" jdbcType="INTEGER"/>
        <result column="ProjectName" property="ProjectName" jdbcType="VARCHAR"/>
        <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
        <result column="Price" property="Price" jdbcType="DECIMAL"/>
        <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
        <result column="AccountTotalAmount" property="AccountTotalAmount" jdbcType="DECIMAL"/>
        <result column="AccountQuantity" property="AccountQuantity" jdbcType="INTEGER"/>
        <result column="ArrearAmount" property="ArrearAmount" jdbcType="DECIMAL"/>
        <result column="PricePreferentialAmount" property="PricePreferentialAmount" jdbcType="DECIMAL"/>
        <result column="MemberPreferentialAmount" property="MemberPreferentialAmount" jdbcType="DECIMAL"/>
        <result column="CardPreferentialAmount" property="CardPreferentialAmount" jdbcType="DECIMAL"/>
        <result column="PreferentialTotalAmount" property="PreferentialTotalAmount" jdbcType="DECIMAL"/>
        <result column="PayAmount" property="PayAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionTotalAmount" property="CardDeductionTotalAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionAmount" property="CardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionLargessAmount" property="CardDeductionLargessAmount" jdbcType="DECIMAL"/>
        <result column="IsLargess" property="IsLargess" jdbcType="BIT"/>
        <collection property="SaleBillHandler"
                    ofType="com.zhelian.model.ibeauty.form.output.SaleBillHandlerListOutputForm">
            <id column="SaleHandlerID" property="SaleHandlerID" jdbcType="INTEGER"/>
            <result column="SaleHandlerName" property="SaleHandlerName" jdbcType="VARCHAR"/>
            <collection property="Employee"
                        ofType="com.zhelian.model.ibeauty.form.output.SaleBillGoodsEmployeePerformanceCommissionOutputForm">
                <result column="EmployeeID" property="EmployeeID" jdbcType="INTEGER"/>
                <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
                <result column="Scale" property="Scale" jdbcType="DECIMAL"/>
                <result column="PayPerformance" property="PayPerformance" jdbcType="DECIMAL"/>
                <result column="PayRateCommission" property="PayRateCommission" jdbcType="DECIMAL"/>
                <result column="PayFixedCommission" property="PayFixedCommission" jdbcType="DECIMAL"/>
                <result column="SavingCardPerformance" property="SavingCardPerformance" jdbcType="DECIMAL"/>
                <result column="SavingCardRateCommission" property="SavingCardRateCommission" jdbcType="DECIMAL"/>
                <result column="SavingCardFixedCommission" property="SavingCardFixedCommission" jdbcType="DECIMAL"/>
                <result column="SavingCardLargessPerformance" property="SavingCardLargessPerformance"
                        jdbcType="DECIMAL"/>
                <result column="SavingCardLargessRateCommission" property="SavingCardLargessRateCommission"
                        jdbcType="DECIMAL"/>
                <result column="SavingCardLargessFixedCommission" property="SavingCardLargessFixedCommission"
                        jdbcType="DECIMAL"/>
                <result column="SpecialBenefitCommission" property="SpecialBenefitCommission" jdbcType="DECIMAL"/>
                <result column="IsCalculatePassengerFlow" property="IsCalculatePassengerFlow" jdbcType="BOOLEAN"/>
            </collection>

        </collection>
    </resultMap>
    <resultMap id="PackageCardGeneralCard"
               type="com.zhelian.model.ibeauty.form.output.SaleBillPackageCardGeneralCardListOutputForm">
        <id column="SaleBillPackageCardGeneralCardID" property="SaleBillPackageCardGeneralCardID" jdbcType="INTEGER"/>
        <id column="SaleBillPackageCardID" property="SaleBillPackageCardID" jdbcType="INTEGER"/>
        <result column="GeneralCardID" property="GeneralCardID" jdbcType="INTEGER"/>
        <result column="GeneralCardName" property="GeneralCardName" jdbcType="VARCHAR"/>
        <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
        <result column="CardTimes" property="CardTimes" jdbcType="INTEGER"/>
        <result column="Price" property="Price" jdbcType="DECIMAL"/>
        <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
        <result column="AccountTotalAmount" property="AccountTotalAmount" jdbcType="DECIMAL"/>
        <result column="AccountCardTimes" property="AccountCardTimes" jdbcType="DECIMAL"/>
        <result column="ArrearAmount" property="ArrearAmount" jdbcType="DECIMAL"/>
        <result column="PricePreferentialAmount" property="PricePreferentialAmount" jdbcType="DECIMAL"/>
        <result column="MemberPreferentialAmount" property="MemberPreferentialAmount" jdbcType="DECIMAL"/>
        <result column="CardPreferentialAmount" property="CardPreferentialAmount" jdbcType="DECIMAL"/>
        <result column="PreferentialTotalAmount" property="PreferentialTotalAmount" jdbcType="DECIMAL"/>
        <result column="PayAmount" property="PayAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionTotalAmount" property="CardDeductionTotalAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionAmount" property="CardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionLargessAmount" property="CardDeductionLargessAmount" jdbcType="DECIMAL"/>
        <result column="IsLargess" property="IsLargess" jdbcType="BIT"/>
        <collection property="SaleBillHandler"
                    ofType="com.zhelian.model.ibeauty.form.output.SaleBillHandlerListOutputForm">
            <id column="SaleHandlerID" property="SaleHandlerID" jdbcType="INTEGER"/>
            <result column="SaleHandlerName" property="SaleHandlerName" jdbcType="VARCHAR"/>
            <collection property="Employee"
                        ofType="com.zhelian.model.ibeauty.form.output.SaleBillGoodsEmployeePerformanceCommissionOutputForm">
                <result column="EmployeeID" property="EmployeeID" jdbcType="INTEGER"/>
                <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
                <result column="Scale" property="Scale" jdbcType="DECIMAL"/>
                <result column="PayPerformance" property="PayPerformance" jdbcType="DECIMAL"/>
                <result column="PayRateCommission" property="PayRateCommission" jdbcType="DECIMAL"/>
                <result column="PayFixedCommission" property="PayFixedCommission" jdbcType="DECIMAL"/>
                <result column="SavingCardPerformance" property="SavingCardPerformance" jdbcType="DECIMAL"/>
                <result column="SavingCardRateCommission" property="SavingCardRateCommission" jdbcType="DECIMAL"/>
                <result column="SavingCardFixedCommission" property="SavingCardFixedCommission" jdbcType="DECIMAL"/>
                <result column="SavingCardLargessPerformance" property="SavingCardLargessPerformance"
                        jdbcType="DECIMAL"/>
                <result column="SavingCardLargessRateCommission" property="SavingCardLargessRateCommission"
                        jdbcType="DECIMAL"/>
                <result column="SavingCardLargessFixedCommission" property="SavingCardLargessFixedCommission"
                        jdbcType="DECIMAL"/>
                <result column="SpecialBenefitCommission" property="SpecialBenefitCommission" jdbcType="DECIMAL"/>
                <result column="IsCalculatePassengerFlow" property="IsCalculatePassengerFlow" jdbcType="BOOLEAN"/>
            </collection>

        </collection>
    </resultMap>
    <resultMap id="PackageCardTimeCard"
               type="com.zhelian.model.ibeauty.form.output.SaleBillPackageCardTimeCardListOutputForm">
        <id column="SaleBillPackageCardTimeCardID" property="SaleBillPackageCardTimeCardID" jdbcType="INTEGER"/>
        <id column="SaleBillPackageCardID" property="SaleBillPackageCardID" jdbcType="INTEGER"/>
        <result column="TimeCardID" property="TimeCardID" jdbcType="INTEGER"/>
        <result column="TimeCardName" property="TimeCardName" jdbcType="VARCHAR"/>
        <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
        <result column="Price" property="Price" jdbcType="DECIMAL"/>
        <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
        <result column="AccountTotalAmount" property="AccountTotalAmount" jdbcType="DECIMAL"/>
        <result column="ArrearAmount" property="ArrearAmount" jdbcType="DECIMAL"/>
        <result column="PricePreferentialAmount" property="PricePreferentialAmount" jdbcType="DECIMAL"/>
        <result column="MemberPreferentialAmount" property="MemberPreferentialAmount" jdbcType="DECIMAL"/>
        <result column="CardPreferentialAmount" property="CardPreferentialAmount" jdbcType="DECIMAL"/>
        <result column="PreferentialTotalAmount" property="PreferentialTotalAmount" jdbcType="DECIMAL"/>
        <result column="PayAmount" property="PayAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionTotalAmount" property="CardDeductionTotalAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionAmount" property="CardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionLargessAmount" property="CardDeductionLargessAmount" jdbcType="DECIMAL"/>
        <result column="IsLargess" property="IsLargess" jdbcType="BIT"/>
        <collection property="SaleBillHandler"
                    ofType="com.zhelian.model.ibeauty.form.output.SaleBillHandlerListOutputForm">
            <id column="SaleHandlerID" property="SaleHandlerID" jdbcType="INTEGER"/>
            <result column="SaleHandlerName" property="SaleHandlerName" jdbcType="VARCHAR"/>
            <collection property="Employee"
                        ofType="com.zhelian.model.ibeauty.form.output.SaleBillGoodsEmployeePerformanceCommissionOutputForm">
                <result column="EmployeeID" property="EmployeeID" jdbcType="INTEGER"/>
                <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
                <result column="Scale" property="Scale" jdbcType="DECIMAL"/>
                <result column="PayPerformance" property="PayPerformance" jdbcType="DECIMAL"/>
                <result column="PayRateCommission" property="PayRateCommission" jdbcType="DECIMAL"/>
                <result column="PayFixedCommission" property="PayFixedCommission" jdbcType="DECIMAL"/>
                <result column="SavingCardPerformance" property="SavingCardPerformance" jdbcType="DECIMAL"/>
                <result column="SavingCardRateCommission" property="SavingCardRateCommission" jdbcType="DECIMAL"/>
                <result column="SavingCardFixedCommission" property="SavingCardFixedCommission" jdbcType="DECIMAL"/>
                <result column="SavingCardLargessPerformance" property="SavingCardLargessPerformance"
                        jdbcType="DECIMAL"/>
                <result column="SavingCardLargessRateCommission" property="SavingCardLargessRateCommission"
                        jdbcType="DECIMAL"/>
                <result column="SavingCardLargessFixedCommission" property="SavingCardLargessFixedCommission"
                        jdbcType="DECIMAL"/>
                <result column="SpecialBenefitCommission" property="SpecialBenefitCommission" jdbcType="DECIMAL"/>
                <result column="IsCalculatePassengerFlow" property="IsCalculatePassengerFlow" jdbcType="BOOLEAN"/>
            </collection>

        </collection>
    </resultMap>
    <resultMap id="PackageCardSavingCard"
               type="com.zhelian.model.ibeauty.form.output.SaleBillPackageCardSavingCardListOutputForm">
        <id column="SaleBillPackageCardSavingCardID" property="SaleBillPackageCardSavingCardID" jdbcType="INTEGER"/>
        <id column="SaleBillPackageCardID" property="SaleBillPackageCardID" jdbcType="INTEGER"/>
        <result column="SavingCardID" property="SavingCardID" jdbcType="INTEGER"/>
        <result column="SavingCardName" property="SavingCardName" jdbcType="VARCHAR"/>
        <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
        <result column="Amount" property="Amount" jdbcType="DECIMAL"/>
        <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
        <result column="AccountTotalAmount" property="AccountTotalAmount" jdbcType="DECIMAL"/>
        <result column="AccountLargessAmount" property="AccountLargessAmount" jdbcType="DECIMAL"/>
        <result column="ArrearAmount" property="ArrearAmount" jdbcType="DECIMAL"/>
        <result column="LargessAmount" property="LargessAmount" jdbcType="DECIMAL"/>
        <result column="PayAmount" property="PayAmount" jdbcType="DECIMAL"/>
        <result column="Price" property="Price" jdbcType="DECIMAL"/>
        <result column="LargessPrice" property="LargessPrice" jdbcType="DECIMAL"/>
        <result column="IsLargess" property="IsLargess" jdbcType="BIT"/>
        <collection property="SaleBillHandler"
                    ofType="com.zhelian.model.ibeauty.form.output.SaleBillSavingCardHandlerListOutputForm">
            <id column="SaleHandlerID" property="SaleHandlerID" jdbcType="INTEGER"/>
            <result column="SaleHandlerName" property="SaleHandlerName" jdbcType="VARCHAR"/>
            <collection property="Employee"
                        ofType="com.zhelian.model.ibeauty.form.output.SaleBillSavingCardEmployeePerformanceCommissionOutputForm">
                <result column="EmployeeID" property="EmployeeID" jdbcType="INTEGER"/>
                <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
                <result column="Scale" property="Scale" jdbcType="DECIMAL"/>
                <result column="PayPerformance" property="PayPerformance" jdbcType="DECIMAL"/>
                <result column="PayRateCommission" property="PayRateCommission" jdbcType="DECIMAL"/>
                <result column="PayFixedCommission" property="PayFixedCommission" jdbcType="DECIMAL"/>
                <result column="SpecialBenefitCommission" property="SpecialBenefitCommission" jdbcType="DECIMAL"/>
                <result column="IsCalculatePassengerFlow" property="IsCalculatePassengerFlow" jdbcType="BOOLEAN"/>
            </collection>

        </collection>
    </resultMap>


    <select id="saleBillPackageCardProductEmployeePerformanceCommission" resultMap="PackageCardProduct">
        SELECT tsp.ID                                                   SaleBillPackageCardProductID,
               tsp.SaleBillPackageCardID,
               tsp.ProductID,
               te.Name                                                  ProductName,
               te.Alias,
               tsp.Quantity,
               tsp.PackagePrice                                         Price,
               tsp.TotalAmount,
               tsp.ArrearAmount,
               tsp.PricePreferentialAmount,
               tsp.MemberPreferentialAmount,
               tsp.CardPreferentialAmount,
               tsp.MemberPreferentialAmount + tsp.PricePreferentialAmount + tsp.CardPreferentialAmount PreferentialTotalAmount,
               (SELECT ISNULL(SUM(Amount), 0) FROM TB_SaleBillPayPackageCardProduct
        WITH (NOLOCK)
        WHERE SaleBillPackageCardProductID=tsp.ID) PayAmount
            , (
        SELECT ISNULL(SUM (Amount), 0)
        FROM TB_SaleBillPaySavingCardDeductionPackageCardProduct
        WITH (NOLOCK)
        WHERE SaleBillPackageCardProductID=tsp.ID) CardDeductionAmount
            , (
        SELECT ISNULL(SUM (LargessAmount), 0)
        FROM TB_SaleBillPaySavingCardDeductionPackageCardProduct
        WITH (NOLOCK)
        WHERE SaleBillPackageCardProductID=tsp.ID) CardDeductionLargessAmount
            , (
        SELECT ISNULL(SUM (TotalAmount), 0)
        FROM TB_SaleBillPaySavingCardDeductionPackageCardProduct
        WITH (NOLOCK)
        WHERE SaleBillPackageCardProductID=tsp.ID) CardDeductionTotalAmount
            , tsp.IsLargess
            , tsh.PackageCardSaleHandlerID SaleHandlerID
            , tp.Name SaleHandlerName
            , tsh.EmployeeID
            , tsh.IsCalculatePassengerFlow
            , tee.Name EmployeeName
            , tsh.Scale*100 Scale
            , tsep.PayPerformance
            , tsep.PayRateCommission
            , tsep.PayFixedCommission
            , tsep.SavingCardPerformance
            , tsep.SavingCardRateCommission
            , tsep.SavingCardFixedCommission
            , tsep.SavingCardLargessPerformance
            , tsep.SavingCardLargessRateCommission
            , tsep.SavingCardLargessFixedCommission
            , tsep.SpecialBenefitCommission
            , CASE WHEN pac.IsLargess = 1 THEN 0
            ELSE pac.TotalAmount
        END AccountTotalAmount
        ,
            pac.Quantity AS AccountQuantity
            FROM  TB_SaleBillPackageCardProduct tsp WITH(NOLOCK)
            INNER JOIN TB_SaleBillPackageCard tc WITH(NOLOCK) ON  tc.ID=tsp.SaleBillPackageCardID
            LEFT JOIN  TE_Product te WITH(NOLOCK)  ON  te.ID=tsp.ProductID
            LEFT JOIN TB_SaleEntityPerformancePackageCardProduct tse WITH(NOLOCK) ON  tse.SaleBillPackageCardID=tsp.SaleBillPackageCardID  AND tse.SaleBillPackageCardProductID=tsp.ID
            AND tse.ProductID=tsp.ProductID
            LEFT JOIN  TB_SaleBillHandlerPackageCard tsh WITH(NOLOCK)  ON  tsh.SaleBillPackageCardID=tsp.SaleBillPackageCardID
            LEFT JOIN TB_SaleEmployeePerformanceCommissionPackageCardProduct tsep WITH(NOLOCK) ON tsh.SaleBillPackageCardID=tsep.SaleBillPackageCardID
            AND tsh.PackageCardSaleHandlerID=tsep.PackageCardSaleHandlerID AND tsh.EmployeeID=tsep.EmployeeID AND tsp.ID=tsep.SaleBillPackageCardProductID
            LEFT JOIN TB_PackageCardSaleHandler tp WITH(NOLOCK)  ON  tp.ID=tsh.PackageCardSaleHandlerID
            LEFT JOIN TK_Employee tee WITH(NOLOCK)  ON tee.ID=tsh.EmployeeID
            LEFT JOIN TB_ProductAccount pac WITH(NOLOCK)  ON pac.ID=tsp.ProductAccountID
            WHERE tc.BillID=
        #{SaleBillID}
        ORDER
        BY
        tp
        .
        Sequence
        desc
    </select>

    <select id="saleBillPackageCardProjectEmployeePerformanceCommission" resultMap="PackageCardProject">
        SELECT tsp.ID                                                   SaleBillPackageCardProjectID,
               tsp.SaleBillPackageCardID,
               tsp.ProjectID,
               te.Name                                                  ProjectName,
               te.Alias,
               tsp.Quantity,
               tsp.PackagePrice                                         Price,
               tsp.TotalAmount,
               tsp.ArrearAmount,
               tsp.PricePreferentialAmount,
               tsp.MemberPreferentialAmount,
               tsp.CardPreferentialAmount,
               tsp.MemberPreferentialAmount + tsp.PricePreferentialAmount + tsp.CardPreferentialAmount PreferentialTotalAmount,
               (SELECT ISNULL(SUM(Amount), 0) FROM TB_SaleBillPayPackageCardProject
        WITH (NOLOCK)
        WHERE SaleBillPackageCardProjectID=tsp.ID) PayAmount
            , (
        SELECT ISNULL(SUM (Amount), 0)
        FROM TB_SaleBillPaySavingCardDeductionPackageCardProject
        WITH (NOLOCK)
        WHERE SaleBillPackageCardProjectID=tsp.ID) CardDeductionAmount
            , (
        SELECT ISNULL(SUM (LargessAmount), 0)
        FROM TB_SaleBillPaySavingCardDeductionPackageCardProject
        WITH (NOLOCK)
        WHERE SaleBillPackageCardProjectID=tsp.ID) CardDeductionLargessAmount
            , (
        SELECT ISNULL(SUM (TotalAmount), 0)
        FROM TB_SaleBillPaySavingCardDeductionPackageCardProject
        WITH (NOLOCK)
        WHERE SaleBillPackageCardProjectID=tsp.ID) CardDeductionTotalAmount
            , tsp.IsLargess
            , tsh.PackageCardSaleHandlerID SaleHandlerID
            , tp.Name SaleHandlerName
            , tsh.EmployeeID
            , tsh.IsCalculatePassengerFlow
            , tee.Name EmployeeName
            , tsh.Scale*100 Scale
            , tsep.PayPerformance
            , tsep.PayRateCommission
            , tsep.PayFixedCommission
            , tsep.SavingCardPerformance
            , tsep.SavingCardRateCommission
            , tsep.SavingCardFixedCommission
            , tsep.SavingCardLargessPerformance
            , tsep.SavingCardLargessRateCommission
            , tsep.SavingCardLargessFixedCommission
            , tsep.SpecialBenefitCommission
            , CASE WHEN pac.IsLargess = 1 THEN 0
            ELSE pac.TotalAmount
        END AccountTotalAmount
        ,
            pac.Quantity AS AccountQuantity
            FROM  TB_SaleBillPackageCardProject tsp WITH(NOLOCK)
            INNER JOIN TB_SaleBillPackageCard tc WITH(NOLOCK) ON  tc.ID=tsp.SaleBillPackageCardID
            LEFT JOIN  TB_Project te WITH(NOLOCK)  ON  te.ID=tsp.ProjectID
            LEFT JOIN TB_SaleEntityPerformancePackageCardProject tse WITH(NOLOCK) ON  tse.SaleBillPackageCardID=tsp.SaleBillPackageCardID  AND tse.SaleBillPackageCardProjectID=tsp.ID
            AND tse.ProjectID=tsp.ProjectID
            LEFT JOIN  TB_SaleBillHandlerPackageCard tsh WITH(NOLOCK)  ON  tsh.SaleBillPackageCardID=tsp.SaleBillPackageCardID
            LEFT JOIN TB_SaleEmployeePerformanceCommissionPackageCardProject tsep WITH(NOLOCK) ON tsh.SaleBillPackageCardID=tsep.SaleBillPackageCardID
            AND tsh.PackageCardSaleHandlerID=tsep.PackageCardSaleHandlerID AND tsh.EmployeeID=tsep.EmployeeID AND tsp.ID=tsep.SaleBillPackageCardProjectID
            LEFT JOIN TB_PackageCardSaleHandler tp WITH(NOLOCK)  ON  tp.ID=tsh.PackageCardSaleHandlerID
            LEFT JOIN TK_Employee tee WITH(NOLOCK)  ON tee.ID=tsh.EmployeeID
            LEFT JOIN TB_ProjectAccount pac WITH(NOLOCK)  ON pac.ID=tsp.ProjectAccountID
            WHERE tc.BillID=
        #{SaleBillID}
        ORDER
        BY
        tp
        .
        Sequence
        desc
    </select>

    <select id="saleBillPackageCardGeneralCardEmployeePerformanceCommission" resultMap="PackageCardGeneralCard">
        SELECT tsp.ID                                                   SaleBillPackageCardGeneralCardID,
               tsp.SaleBillPackageCardID,
               tsp.GeneralCardID,
               te.Name                                                  GeneralCardName,
               te.Alias,
               tsp.Quantity,
               tsp.CardTimes,
               tsp.PackagePrice                                         Price,
               tsp.TotalAmount,
               tsp.ArrearAmount,
               tsp.PricePreferentialAmount,
               tsp.MemberPreferentialAmount,
               tsp.CardPreferentialAmount,
               tsp.MemberPreferentialAmount + tsp.PricePreferentialAmount + tsp.CardPreferentialAmount PreferentialTotalAmount,
               (SELECT ISNULL(SUM(Amount), 0) FROM TB_SaleBillPayPackageCardGeneralCard
        WITH (NOLOCK)
        WHERE SaleBillPackageCardGeneralCardID=tsp.ID) PayAmount
            , (
        SELECT ISNULL(SUM (Amount), 0)
        FROM TB_SaleBillPaySavingCardDeductionPackageCardGeneralCard
        WITH (NOLOCK)
        WHERE SaleBillPackageCardGeneralCardID=tsp.ID) CardDeductionAmount
            , (
        SELECT ISNULL(SUM (LargessAmount), 0)
        FROM TB_SaleBillPaySavingCardDeductionPackageCardGeneralCard
        WITH (NOLOCK)
        WHERE SaleBillPackageCardGeneralCardID=tsp.ID) CardDeductionLargessAmount
            , (
        SELECT ISNULL(SUM (TotalAmount), 0)
        FROM TB_SaleBillPaySavingCardDeductionPackageCardGeneralCard
        WITH (NOLOCK)
        WHERE SaleBillPackageCardGeneralCardID=tsp.ID) CardDeductionTotalAmount
            , tsp.IsLargess
            , tsh.PackageCardSaleHandlerID SaleHandlerID
            , tp.Name SaleHandlerName
            , tsh.EmployeeID
            , tsh.IsCalculatePassengerFlow
            , tee.Name EmployeeName
            , tsh.Scale*100 Scale
            , tsep.PayPerformance
            , tsep.PayRateCommission
            , tsep.PayFixedCommission
            , tsep.SavingCardPerformance
            , tsep.SavingCardRateCommission
            , tsep.SavingCardFixedCommission
            , tsep.SavingCardLargessPerformance
            , tsep.SavingCardLargessRateCommission
            , tsep.SavingCardLargessFixedCommission
            , tsep.SpecialBenefitCommission
            , CASE WHEN pac.IsLargess = 1 THEN 0
            ELSE pac.TotalAmount
        END AccountTotalAmount
        ,
            pac.CardTimes AccountCardTimes
            FROM  TB_SaleBillPackageCardGeneralCard tsp WITH(NOLOCK)
            INNER JOIN TB_SaleBillPackageCard tc WITH(NOLOCK) ON  tc.ID=tsp.SaleBillPackageCardID
            LEFT JOIN  TB_GeneralCard te WITH(NOLOCK)  ON  te.ID=tsp.GeneralCardID
            LEFT JOIN TB_SaleEntityPerformancePackageCardGeneralCard tse WITH(NOLOCK) ON  tse.SaleBillPackageCardID=tsp.SaleBillPackageCardID  AND tse.SaleBillPackageCardGeneralCardID=tsp.ID
            AND tse.GeneralCardID=tsp.GeneralCardID
            LEFT JOIN  TB_SaleBillHandlerPackageCard tsh WITH(NOLOCK)  ON  tsh.SaleBillPackageCardID=tsp.SaleBillPackageCardID
            LEFT JOIN TB_SaleEmployeePerformanceCommissionPackageCardGeneralCard tsep WITH(NOLOCK) ON tsh.SaleBillPackageCardID=tsep.SaleBillPackageCardID
            AND tsh.PackageCardSaleHandlerID=tsep.PackageCardSaleHandlerID AND tsh.EmployeeID=tsep.EmployeeID AND tsp.ID=tsep.SaleBillPackageCardGeneralCardID
            LEFT JOIN TB_PackageCardSaleHandler tp WITH(NOLOCK)  ON  tp.ID=tsh.PackageCardSaleHandlerID
            LEFT JOIN TK_Employee tee WITH(NOLOCK)  ON tee.ID=tsh.EmployeeID
            LEFT JOIN TB_GeneralCardAccount pac WITH(NOLOCK)  ON pac.ID=tsp.GeneralCardAccountID
            WHERE tc.BillID=
        #{SaleBillID}
        ORDER
        BY
        tp
        .
        Sequence
        desc
    </select>

    <select id="saleBillPackageCardTimeCardEmployeePerformanceCommission" resultMap="PackageCardTimeCard">
        SELECT tsp.ID                                                   SaleBillPackageCardTimeCardID,
               tsp.SaleBillPackageCardID,
               tsp.TimeCardID,
               te.Name                                                  TimeCardName,
               te.Alias,
               tsp.Quantity,
               tsp.PackagePrice                                         Price,
               tsp.TotalAmount,
               tsp.ArrearAmount,
               tsp.PricePreferentialAmount,
               tsp.MemberPreferentialAmount,
               tsp.CardPreferentialAmount,
               tsp.MemberPreferentialAmount + tsp.PricePreferentialAmount + tsp.CardPreferentialAmount PreferentialTotalAmount,
               (SELECT ISNULL(SUM(Amount), 0) FROM TB_SaleBillPayPackageCardTimeCard
        WITH (NOLOCK)
        WHERE SaleBillPackageCardTimeCardID=tsp.ID) PayAmount
            , (
        SELECT ISNULL(SUM (Amount), 0)
        FROM TB_SaleBillPaySavingCardDeductionPackageCardTimeCard
        WITH (NOLOCK)
        WHERE SaleBillPackageCardTimeCardID=tsp.ID) CardDeductionAmount
            , (
        SELECT ISNULL(SUM (LargessAmount), 0)
        FROM TB_SaleBillPaySavingCardDeductionPackageCardTimeCard
        WITH (NOLOCK)
        WHERE SaleBillPackageCardTimeCardID=tsp.ID) CardDeductionLargessAmount
            , (
        SELECT ISNULL(SUM (TotalAmount), 0)
        FROM TB_SaleBillPaySavingCardDeductionPackageCardTimeCard
        WITH (NOLOCK)
        WHERE SaleBillPackageCardTimeCardID=tsp.ID) CardDeductionTotalAmount
            , tsp.IsLargess
            , tsh.PackageCardSaleHandlerID SaleHandlerID
            , tp.Name SaleHandlerName
            , tsh.EmployeeID
            , tsh.IsCalculatePassengerFlow
            , tee.Name EmployeeName
            , tsh.Scale*100 Scale
            , tsep.PayPerformance
            , tsep.PayRateCommission
            , tsep.PayFixedCommission
            , tsep.SavingCardPerformance
            , tsep.SavingCardRateCommission
            , tsep.SavingCardFixedCommission
            , tsep.SavingCardLargessPerformance
            , tsep.SavingCardLargessRateCommission
            , tsep.SavingCardLargessFixedCommission
            , tsep.SpecialBenefitCommission
            , CASE WHEN pac.IsLargess = 1 THEN 0
            ELSE pac.TotalAmount
        END AccountTotalAmount
        FROM  TB_SaleBillPackageCardTimeCard tsp WITH(NOLOCK)
            INNER JOIN TB_SaleBillPackageCard tc WITH(NOLOCK) ON  tc.ID=tsp.SaleBillPackageCardID
            LEFT JOIN  TB_TimeCard te WITH(NOLOCK)  ON  te.ID=tsp.TimeCardID
            LEFT JOIN TB_SaleEntityPerformancePackageCardTimeCard tse WITH(NOLOCK) ON  tse.SaleBillPackageCardID=tsp.SaleBillPackageCardID  AND tse.SaleBillPackageCardTimeCardID=tsp.ID
            AND tse.TimeCardID=tsp.TimeCardID
            LEFT JOIN  TB_SaleBillHandlerPackageCard tsh WITH(NOLOCK)  ON  tsh.SaleBillPackageCardID=tsp.SaleBillPackageCardID
            LEFT JOIN TB_SaleEmployeePerformanceCommissionPackageCardTimeCard tsep WITH(NOLOCK) ON tsh.SaleBillPackageCardID=tsep.SaleBillPackageCardID
            AND tsh.PackageCardSaleHandlerID=tsep.PackageCardSaleHandlerID AND tsh.EmployeeID=tsep.EmployeeID  AND tsp.ID=tsep.SaleBillPackageCardTimeCardID
            LEFT JOIN TB_PackageCardSaleHandler tp WITH(NOLOCK)  ON  tp.ID=tsh.PackageCardSaleHandlerID
            LEFT JOIN TK_Employee tee WITH(NOLOCK)  ON tee.ID=tsh.EmployeeID
            LEFT JOIN TB_TimeCardAccount pac WITH(NOLOCK)  ON pac.ID=tsp.TimeCardAccountID
            WHERE tc.BillID=
        #{SaleBillID}
        ORDER
        BY
        tp
        .
        Sequence
        desc
    </select>

    <select id="saleBillPackageCardSavingCardEmployeePerformanceCommission" resultMap="PackageCardSavingCard">
        SELECT tsp.ID                       SaleBillPackageCardSavingCardID,
               tsp.SaleBillPackageCardID,
               tsp.SavingCardID,
               te.Name                      SavingCardName,
               te.Alias,
               tsp.Quantity,
               tsp.Amount,
               tsp.TotalAmount,
               tsp.ArrearAmount,
               tsp.LargessAmount,
               tsp.Amount AS                Price,
               tsp.LargessAmount            LargessPrice,
               CASE
                   WHEN tc.IsLargess = 0 THEN tsp.TotalAmount - tsp.ArrearAmount
                   ELSE 0
                   END                      PayAmount,
               tsh.PackageCardSaleHandlerID SaleHandlerID,
               tp.Name                      SaleHandlerName,
               tsh.EmployeeID,
               tsh.IsCalculatePassengerFlow,
               tee.Name                     EmployeeName,
               tsh.Scale * 100              Scale,
               tsep.PayPerformance,
               tsep.PayRateCommission,
               tsep.PayFixedCommission,
               tsep.SpecialBenefitCommission,
               tsp.IsLargess,
               pac.Amount                   AccountTotalAmount,
               pac.LargessAmount            AccountLargessAmount
        FROM TB_SaleBillPackageCardSavingCard tsp WITH(NOLOCK)
            INNER JOIN TB_SaleBillPackageCard tc
        WITH (NOLOCK)
        ON tc.ID=tsp.SaleBillPackageCardID
            LEFT JOIN TB_SavingCard te
        WITH (NOLOCK)
        ON te.ID=tsp.SavingCardID
            LEFT JOIN (SELECT SaleBillPackageCardSavingCardID, SUM (Amount) Amount FROM TB_SaleBillPayPackageCardSavingCard WITH (NOLOCK) GROUP BY SaleBillPackageCardSavingCardID) ts ON ts.SaleBillPackageCardSavingCardID=tsp.ID
            LEFT JOIN TB_SaleEntityPerformancePackageCardSavingCard tse
        WITH (NOLOCK)
        ON tse.SaleBillPackageCardID=tsp.SaleBillPackageCardID AND tse.SaleBillPackageCardSavingCardID=tsp.ID
            AND tse.SavingCardID=tsp.SavingCardID
            LEFT JOIN TB_SaleBillHandlerPackageCard tsh
        WITH (NOLOCK)
        ON tsh.SaleBillPackageCardID=tsp.SaleBillPackageCardID
            LEFT JOIN TB_SaleEmployeePerformanceCommissionPackageCardSavingCard tsep
        WITH (NOLOCK)
        ON tsh.SaleBillPackageCardID=tsep.SaleBillPackageCardID
            AND tsh.PackageCardSaleHandlerID=tsep.PackageCardSaleHandlerID AND tsh.EmployeeID=tsep.EmployeeID AND tsp.ID=tsep.SaleBillPackageCardSavingCardID
            LEFT JOIN TB_PackageCardSaleHandler tp
        WITH (NOLOCK)
        ON tp.ID=tsh.PackageCardSaleHandlerID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tsh.EmployeeID
            LEFT JOIN TB_SavingCardAccount pac
        WITH (NOLOCK)
        ON pac.ID=tsp.SavingCardAccountID
        WHERE tc.BillID=#{SaleBillID}
        ORDER BY tp.Sequence desc
    </select>

    <update id="updateSaleEmployeePerformanceProductBillDate">
        UPDATE TB_SaleEmployeePerformanceCommissionProduct
        SET BillDate =#{BillDate}
        WHERE SaleBillID = #{SaleBillID}
    </update>
    <update id="updateSaleEmployeePerformanceProjectBillDate">
        UPDATE TB_SaleEmployeePerformanceCommissionProject
        SET BillDate =#{BillDate}
        WHERE SaleBillID = #{SaleBillID}
    </update>
    <update id="updateSaleEmployeePerformanceGeneralCardBillDate">
        UPDATE TB_SaleEmployeePerformanceCommissionGeneralCard
        SET BillDate =#{BillDate}
        WHERE SaleBillID = #{SaleBillID}
    </update>
    <update id="updateSaleEmployeePerformanceTimeCardBillDate">
        UPDATE TB_SaleEmployeePerformanceCommissionTimeCard
        SET BillDate =#{BillDate}
        WHERE SaleBillID = #{SaleBillID}
    </update>
    <update id="updateSaleEmployeePerformanceSavingCardBillDate">
        UPDATE TB_SaleEmployeePerformanceCommissionSavingCard
        SET BillDate =#{BillDate}
        WHERE SaleBillID = #{SaleBillID}
    </update>


    <update id="updateSaleEmployeePerformancePackageCardProductBillDate">
        UPDATE TB_SaleEmployeePerformanceCommissionPackageCardProduct
        SET BillDate =#{BillDate}
        WHERE SaleBillID = #{SaleBillID}
    </update>
    <update id="updateSaleEmployeePerformancePackageCardProjectBillDate">
        UPDATE TB_SaleEmployeePerformanceCommissionPackageCardProject
        SET BillDate =#{BillDate}
        WHERE SaleBillID = #{SaleBillID}
    </update>
    <update id="updateSaleEmployeePerformancePackageCardSavingCardBillDate">
        UPDATE TB_SaleEmployeePerformanceCommissionPackageCardGeneralCard
        SET BillDate =#{BillDate}
        WHERE SaleBillID = #{SaleBillID}
    </update>
    <update id="updateSaleEmployeePerformancePackageCardGeneralCardBillDate">
        UPDATE TB_SaleEmployeePerformanceCommissionPackageCardTimeCard
        SET BillDate =#{BillDate}
        WHERE SaleBillID = #{SaleBillID}
    </update>
    <update id="updateSaleEmployeePerformancePackageCardTimeCardBillDate">
        UPDATE TB_SaleEmployeePerformanceCommissionPackageCardSavingCard
        SET BillDate =#{BillDate}
        WHERE SaleBillID = #{SaleBillID}
    </update>
</mapper>