<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.ibeauty.SaleProductHandlerCommissionMapper">
    <resultMap id="ProductHandlerCommission"
               type="com.zhelian.model.ibeauty.form.output.SaleProductHandlerCommissionOutputForm">
        <id column="SaleHandlerID" property="ID" jdbcType="INTEGER"/>
        <result column="SaleHandlerName" property="Name" jdbcType="VARCHAR"/>
        <result column="PayRate" property="PayRate" jdbcType="DECIMAL"/>
        <result column="PayFixed" property="PayFixed" jdbcType="DECIMAL"/>
        <result column="SavingCardRate" property="SavingCardRate" jdbcType="DECIMAL"/>
        <result column="SavingCardFixed" property="SavingCardFixed" jdbcType="DECIMAL"/>
        <result column="SavingCardLargessRate" property="SavingCardLargessRate" jdbcType="DECIMAL"/>
        <result column="SavingCardLargessFixed" property="SavingCardLargessFixed" jdbcType="DECIMAL"/>
        <result column="SpecialBenefit" property="SpecialBenefit" jdbcType="DECIMAL"/>
        <collection property="HandlerJobTypeCommission"
                    ofType="com.zhelian.model.ibeauty.form.output.SaleProductHandlerJobTypeCommissionOutputForm">
            <id column="JobTypeID" property="JobTypeID" jdbcType="INTEGER"/>
            <result column="HandlerJobTypeID" property="ID" jdbcType="VARCHAR"/>
            <result column="JobName" property="Name" jdbcType="VARCHAR"/>
            <result column="JobTypePayRate" property="PayRate" jdbcType="DECIMAL"/>
            <result column="JobTypePayFixed" property="PayFixed" jdbcType="DECIMAL"/>
            <result column="JobTypeSavingCardRate" property="SavingCardRate" jdbcType="DECIMAL"/>
            <result column="JobTypeSavingCardFixed" property="SavingCardFixed" jdbcType="DECIMAL"/>
            <result column="JobTypeSavingCardLargessRate" property="SavingCardLargessRate" jdbcType="DECIMAL"/>
            <result column="JobTypeSavingCardLargessFixed" property="SavingCardLargessFixed" jdbcType="DECIMAL"/>
            <result column="JobTypeSpecialBenefit" property="SpecialBenefit" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>

    <select id="allSaleProductHandlerCommission" resultMap="ProductHandlerCommission">
        SELECT tsh.EntityID,
               tsh.ID                                                           SaleHandlerID,
               tsh.Name                                                         SaleHandlerName,
               tsp.PayRate * 100                                                PayRate,
               tsp.PayFixed,
               tsp.SavingCardRate * 100                                         SavingCardRate,
               tsp.SavingCardFixed,
               tsp.SavingCardLargessRate * 100                                  SavingCardLargessRate,
               tsp.SavingCardLargessFixed,
               tsp.SpecialBenefit,
               (cast(tsh.ID as varchar) + '-' + cast(tpj.JobTypeID as varchar)) HandlerJobTypeID,
               tpj.JobTypeID,
               tj.JobName,
               tsj.PayRate * 100                                                JobTypePayRate,
               tsj.PayFixed                                                     JobTypePayFixed,
               tsj.SavingCardRate * 100                                         JobTypeSavingCardRate,
               tsj.SavingCardFixed                                              JobTypeSavingCardFixed,
               tsj.SavingCardLargessRate * 100                                  JobTypeSavingCardLargessRate,
               tsj.SavingCardLargessFixed                                       JobTypeSavingCardLargessFixed,
               tsj.SpecialBenefit                                               JobTypeSpecialBenefit
        FROM TB_ProductSaleHandler tsh WITH(NOLOCK)
        LEFT JOIN   TB_SaleProductHandlerCommission tsp
        WITH (NOLOCK)
        ON tsp.ProductSaleHandlerID=tsh.ID AND tsh.EntityID=tsp.EntityID
            AND tsp.ProductID=#{ProductID}
            LEFT JOIN TB_ProductSaleHandlerJobType tpj
        WITH (NOLOCK)
        ON tsh.ID=tpj.ProductSaleHandlerID
            LEFT JOIN TB_SaleProductHandlerJobTypeCommission tsj
        WITH (NOLOCK)
        ON tsh.EntityID=tsj.EntityID
            AND tsj.ProductSaleHandlerID = tsh.ID AND tsj.JobTypeID=tpj.JobTypeID AND tsj.ProductID=#{ProductID}
            LEFT JOIN TK_JobType tj
        WITH (NOLOCK)
        ON tpj.JobTypeID=tj.ID
        WHERE tsh.EntityID=#{EntityID} AND tsh.Active=1
        ORDER BY tsh.Sequence DESC
    </select>


    <delete id="deleteSaleProductHandler">
        DELETE
        FROM TB_SaleProductHandlerCommission
        WHERE EntityID = #{EntityID}
          AND ProductID = #{ProductID}
    </delete>

    <delete id="deleteSaleProductHandlerJobType">
        DELETE
        FROM TB_SaleProductHandlerJobTypeCommission
        WHERE EntityID = #{EntityID}
          AND ProductID = #{ProductID}
    </delete>

    <delete id="deleteSaleProductHandlerCommissionByEntityID">
        DELETE
        FROM TB_SaleProductHandlerCommission
        WHERE EntityID = #{EntityID}
    </delete>

    <delete id="deleteSaleProductHandlerJobTypeCommissionByEntityID">
        DELETE
        FROM TB_SaleProductHandlerJobTypeCommission
        WHERE EntityID = #{EntityID}
    </delete>


    <insert id="updateSaleProductHandlerCommission" parameterType="java.util.List">
        INSERT INTO TB_SaleProductHandlerCommission (
        EntityID,
        ProductID,
        ProductSaleHandlerID,
        PayRate,
        PayFixed,
        SavingCardRate,
        SavingCardFixed,
        SavingCardLargessRate,
        SavingCardLargessFixed,
        SpecialBenefit
        ) VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.EntityID},
            #{item.ProductID},
            #{item.ProductSaleHandlerID},
            cast(#{item.PayRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.PayFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.SavingCardRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.SavingCardFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.SavingCardLargessRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.SavingCardLargessFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.SpecialBenefit,jdbcType=DECIMAL} as decimal(18,2))
            )
        </foreach>

    </insert>

    <insert id="insertSaleProductHandlerJobTypeCommission" parameterType="java.util.List">
        INSERT INTO TB_SaleProductHandlerJobTypeCommission (
        EntityID,
        ProductID,
        ProductSaleHandlerID,
        JobTypeID,
        PayRate,
        PayFixed,
        SavingCardRate,
        SavingCardFixed,
        SavingCardLargessRate,
        SavingCardLargessFixed,
        SpecialBenefit
        ) VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.EntityID},
            #{item.ProductID},
            #{item.ProductSaleHandlerID},
            #{item.JobTypeID},
            cast(#{item.PayRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.PayFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.SavingCardRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.SavingCardFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.SavingCardLargessRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.SavingCardLargessFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.SpecialBenefit,jdbcType=DECIMAL} as decimal(18,2))
            )
        </foreach>
    </insert>
</mapper>