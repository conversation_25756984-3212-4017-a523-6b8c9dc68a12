<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.ibeauty.TreatBillMapper">

    <insert id="createTreatBill" parameterType="com.zhelian.model.ibeauty.entity.TreatBillEntity">
        INSERT INTO dbo.TB_TreatBill (ID, BillDate, EntityID, CustomerID, BillType, BillStatus, EmployeeID,
            Amount, PayAmount, CardDeductionAmount, LargessCardDeductionAmount, Channel, Remark,PricePreferentialAmount,MemberPreferentialAmount, CardPreferentialAmount, IsFastBill)
        VALUES
            ( #{ID}, #{BillDate}, #{EntityID}, #{CustomerID}, #{BillType}, #{BillStatus}, #{EmployeeID}, #{Amount}, #{PayAmount},
            #{CardDeductionAmount}, #{LargessCardDeductionAmount}, #{Channel}, #{Remark}, #{PricePreferentialAmount}, #{MemberPreferentialAmount}, #{CardPreferentialAmount}, #{IsFastBill});
    </insert>

    <insert id="createTreatBillPending" parameterType="com.zhelian.model.ibeauty.entity.TreatBillPendingEntity">
        INSERT INTO dbo.TB_TreatBillPending (TreatBillID, Content)
        VALUES
            (#{TreatBillID}, #{Content}
            )
    </insert>

    <select id="getTreatBillPending" resultType="java.lang.String">
        SELECT Content
        FROM TB_TreatBillPending
        WHERE TreatBillID = #{BillID}
    </select>

    <update id="updateTreatBillPending">
        UPDATE TB_TreatBillPending
        SET Content =#{Content}
        WHERE TreatBillID = #{TreatBillID}
    </update>

    <delete id="deleteTreatBillPending">
        DELETE
        FROM TB_TreatBillPending
        WHERE TreatBillID = #{BillID}
    </delete>

    <insert id="createTreatBillProduct" parameterType="com.zhelian.model.ibeauty.entity.TreatBillProductEntity"
            useGeneratedKeys="true" keyProperty="ID" keyColumn="ID">
        INSERT INTO dbo.TB_TreatBillProduct (TreatBillID, ProductAccountID, ProductID, Quantity,
                                                  IsLargess, Price, TotalAmount, PayAmount,
                                                  CardDeductionAmount, LargessCardDeductionAmount,
                                                  PackageCardAccountID, PackageCardID, CardPreferentialAmount, Remark)
        VALUES
            (#{TreatBillID}, #{ProductAccountID}, #{ProductID}, #{Quantity}, #{IsLargess},
            #{Price}, #{TotalAmount}, #{PayAmount}, #{CardDeductionAmount}, #{LargessCardDeductionAmount}, #{PackageCardAccountID}, #{PackageCardID},
            #{CardPreferentialAmount},#{Remark})
    </insert>
    <insert id="createTreatBillProject" parameterType="com.zhelian.model.ibeauty.entity.TreatBillProjectEntity"
            useGeneratedKeys="true" keyProperty="ID" keyColumn="ID">
        INSERT INTO dbo.TB_TreatBillProject (TreatBillID, ProjectAccountID, ProjectID, Quantity,
                                                  IsLargess, Price, TotalAmount, PayAmount,
                                                  CardDeductionAmount, LargessCardDeductionAmount,
                                                  PackageCardAccountID, PackageCardID, CardPreferentialAmount, Remark)
        VALUES
            (#{TreatBillID}, #{ProjectAccountID}, #{ProjectID}, #{Quantity}, #{IsLargess},
            #{Price}, #{TotalAmount}, #{PayAmount}, #{CardDeductionAmount}, #{LargessCardDeductionAmount}, #{PackageCardAccountID}, #{PackageCardID},
            #{CardPreferentialAmount},#{Remark})
    </insert>
    <insert id="createTreatBillSavingCard" parameterType="com.zhelian.model.ibeauty.entity.TreatBillSavingCardEntity"
            useGeneratedKeys="true" keyProperty="ID" keyColumn="ID">
        INSERT INTO dbo.TB_TreatBillSavingCard (TreatBillID, SavingCardAccountID, ProjectID, Price,
                                                     Quantity, PricePreferentialAmount,MemberPreferentialAmount, CardPreferentialAmount,
                                                     TotalAmount, CardTreatAmount, LargessCardTreatAmount,
                                                     PackageCardAccountID, PackageCardID, Remark)
        VALUES
            (#{TreatBillID}, #{SavingCardAccountID}, #{ProjectID}, #{Price}, #{Quantity}, #{PricePreferentialAmount}, #{MemberPreferentialAmount}, #{CardPreferentialAmount},
            #{TotalAmount}, #{CardTreatAmount}, #{LargessCardTreatAmount}, #{PackageCardAccountID}, #{PackageCardID},#{Remark}
            )
    </insert>
    <insert id="createTreatBillGeneralCard" parameterType="com.zhelian.model.ibeauty.entity.TreatBillGeneralCardEntity"
            useGeneratedKeys="true" keyProperty="ID" keyColumn="ID">
        INSERT INTO dbo.TB_TreatBillGeneralCard (TreatBillID, GeneralCardAccountID, ProjectID, Quantity,
            CardTreatTimes, IsLargess, Price, TotalAmount, PayAmount, CardDeductionAmount,
            LargessCardDeductionAmount, PackageCardAccountID, PackageCardID, CardPreferentialAmount, Remark)
        VALUES
            (#{TreatBillID}, #{GeneralCardAccountID}, #{ProjectID}, #{Quantity}, #{CardTreatTimes}, #{IsLargess},
            #{Price}, #{TotalAmount}, #{PayAmount}, #{CardDeductionAmount}, #{LargessCardDeductionAmount}, #{PackageCardAccountID}, #{PackageCardID},
            #{CardPreferentialAmount},#{Remark})
    </insert>
    <insert id="createTreatBillTimeCard" parameterType="com.zhelian.model.ibeauty.entity.TreatBillTimeCardEntity"
            useGeneratedKeys="true" keyProperty="ID" keyColumn="ID">
        INSERT INTO dbo.TB_TreatBillTimeCard (TreatBillID, TimeCardAccountID, ProjectID, Quantity,
                                                   IsLargess, Price, TotalAmount, PayAmount,
                                                   CardDeductionAmount, LargessCardDeductionAmount,
                                                   PackageCardAccountID, PackageCardID, CardPreferentialAmount, Remark)
        VALUES
            (#{TreatBillID}, #{TimeCardAccountID}, #{ProjectID}, #{Quantity}, #{IsLargess},
            #{Price}, #{TotalAmount}, #{PayAmount}, #{CardDeductionAmount}, #{LargessCardDeductionAmount}, #{PackageCardAccountID}, #{PackageCardID},
            #{CardPreferentialAmount},#{Remark})
    </insert>

    <insert id="createTreatBillHandlerProduct"
            parameterType="com.zhelian.model.ibeauty.entity.TreatBillHandlerProductEntity">
        INSERT INTO dbo.TB_TreatBillHandlerProduct (TreatBillProductID, ProductTreatHandlerID, EmployeeID,
                                                         Scale,IsCalculatePassengerFlow)
        VALUES
            (#{TreatBillProductID}, #{ProductTreatHandlerID}, #{EmployeeID}, #{Scale}, #{IsCalculatePassengerFlow})
    </insert>
    <insert id="createTreatBillHandlerProject"
            parameterType="com.zhelian.model.ibeauty.entity.TreatBillHandlerProjectEntity">
        INSERT INTO dbo.TB_TreatBillHandlerProject (TreatBillProjectID, ProjectTreatHandlerID, EmployeeID,
                                                         Scale,IsCalculatePassengerFlow)
        VALUES
            (#{TreatBillProjectID}, #{ProjectTreatHandlerID}, #{EmployeeID}, #{Scale}, #{IsCalculatePassengerFlow})
    </insert>
    <insert id="createTreatBillHandlerSavingCard"
            parameterType="com.zhelian.model.ibeauty.entity.TreatBillHandlerSavingCardEntity">
        INSERT INTO dbo.TB_TreatBillHandlerSavingCard (TreatBillSavingCardID, SavingCardTreatHandlerID,
                                                            EmployeeID, Scale,IsCalculatePassengerFlow)
        VALUES
            (#{TreatBillSavingCardID}, #{SavingCardTreatHandlerID}, #{EmployeeID}, #{Scale}, #{IsCalculatePassengerFlow})
    </insert>
    <insert id="createTreatBillHandlerGeneralCard"
            parameterType="com.zhelian.model.ibeauty.entity.TreatBillHandlerGeneralCardEntity">
        INSERT INTO dbo.TB_TreatBillHandlerGeneralCard (TreatBillGeneralCardID, GeneralCardTreatHandlerID,
                                                             EmployeeID, Scale,IsCalculatePassengerFlow)
        VALUES
            (#{TreatBillGeneralCardID}, #{GeneralCardTreatHandlerID}, #{EmployeeID}, #{Scale}, #{IsCalculatePassengerFlow})
    </insert>
    <insert id="createTreatBillHandlerTimeCard"
            parameterType="com.zhelian.model.ibeauty.entity.TreatBillHandlerTimeCardEntity">
        INSERT INTO dbo.TB_TreatBillHandlerTimeCard (TreatBillTimeCardID, TimeCardTreatHandlerID,
                                                          EmployeeID, Scale,IsCalculatePassengerFlow)
        VALUES
            (#{TreatBillTimeCardID}, #{TimeCardTreatHandlerID}, #{EmployeeID}, #{Scale}, #{IsCalculatePassengerFlow})
    </insert>

    <resultMap id="treatBillListMap" type="com.zhelian.model.ibeauty.form.output.TreatBillOutputForm">
        <id column="ID" property="ID" jdbcType="VARCHAR"/>
        <result column="CustomerID" property="CustomerID" jdbcType="INTEGER"/>
        <result column="BillDate" property="BillDate" jdbcType="VARCHAR"/>
        <result column="Name" property="Name" jdbcType="VARCHAR"/>
        <result column="Code" property="Code" jdbcType="VARCHAR"/>
        <result column="PhoneNumber" property="PhoneNumber" jdbcType="VARCHAR"/>
        <result column="EntityName" property="EntityName" jdbcType="VARCHAR"/>
        <result column="Amount" property="Amount" jdbcType="DECIMAL"/>
        <result column="BillStatus" property="BillStatus" jdbcType="VARCHAR"/>
        <result column="BillType" property="BillType" jdbcType="VARCHAR"/>
        <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
        <result column="CreatedOn" property="CreatedOn" jdbcType="VARCHAR"/>
        <result column="Channel" property="Channel" jdbcType="VARCHAR"/>
        <result column="Remark" property="Remark" jdbcType="VARCHAR"/>
        <collection property="treatBillDetailList" column="{ID=ID}" select="getTreatBillDetail"
                    ofType="com.zhelian.model.ibeauty.form.output.TreatBillDetailOutputForm"/>
    </resultMap>

    <select id="treatBillList" resultMap="treatBillListMap">
        SELECT
        tsb.ID,
        tsb.CustomerID,
        CONVERT(VARCHAR (19),tsb.BillDate,120) BillDate,
        CONVERT(VARCHAR (19),tsb.CreatedOn,120) CreatedOn,
        case when tsb.CustomerID is null then '散客'
        else tc.Name end Name,
        tc.Code,
        tc.PhoneNumber as PhoneNumber,
        tsb.Amount,
        tsb.BillStatus,
        tsb.BillType,
        te.Name EmployeeName,
        CASE WHEN tsb.Channel ='PC' THEN 'pc'
        WHEN tsb.Channel ='Miniprogram' THEN '小程序'
        END Channel,
        tsb.Remark
        FROM
        TB_TreatBill tsb WITH(NOLOCK)
        LEFT JOIN TB_Customer tc WITH(NOLOCK) ON tc.ID=tsb.CustomerID
        LEFT JOIN TK_Employee te WITH(NOLOCK) ON te.ID=tsb.EmployeeID
        <where>
            tsb.EntityID=#{EntityID}
            <if test="Name != null and Name != ''">
                <choose>
                    <when test="Name =='散客'">
                        AND tsb.CustomerID is null
                    </when>
                    <otherwise>
                        AND ( tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%' OR tc.Code LIKE
                        '%'+ #{Name} +'%')
                    </otherwise>
                </choose>
            </if>
            <if test="BillID != null and BillID != ''">
                AND tsb.ID = #{BillID}
            </if>
            <if test="StartDate != null and StartDate != ''and EndDate != null and EndDate != ''">
                AND tsb.BillDate >= #{StartDate} AND tsb.BillDate &lt; DATEADD(DAY,1,#{EndDate})
            </if>
            <if test="BillStatus != null and BillStatus != ''">
                AND tsb.BillStatus = #{BillStatus}
            </if>
            <if test="BillType != null and BillType != ''">
                AND tsb.BillType = #{BillType}
            </if>
            <if test="Channel != null and Channel != ''">
                AND tsb.Channel LIKE '%'+ #{Channel} +'%'
            </if>
            <if test="IsNoHaveHandler != null and IsNoHaveHandler ==true">
                AND EXISTS (
                SELECT 1 FROM
                (
                SELECT
                tcp.TreatBillID
                FROM
                TB_TreatBillProduct tcp WITH(NOLOCK)
                WHERE NOT EXISTS (SELECT 1 FROM TB_TreatBillHandlerProduct tp WITH(NOLOCK) WHERE TCP.ID =
                TP.TreatBillProductID )
                UNION ALL
                SELECT
                tcp.TreatBillID
                FROM
                TB_TreatBillProject tcp WITH(NOLOCK)
                WHERE NOT EXISTS (SELECT 1 FROM TB_TreatBillHandlerProject tp WITH(NOLOCK) WHERE TCP.ID =
                TP.TreatBillProjectID )
                UNION ALL
                SELECT
                tcp.TreatBillID
                FROM
                TB_TreatBillGeneralCard tcp WITH(NOLOCK)
                WHERE NOT EXISTS (SELECT 1 FROM TB_TreatBillHandlerGeneralCard tp WITH(NOLOCK) WHERE TCP.ID =
                TP.TreatBillGeneralCardID )
                UNION ALL
                SELECT
                tcp.TreatBillID
                FROM
                TB_TreatBillTimeCard tcp WITH(NOLOCK)
                WHERE NOT EXISTS (SELECT 1 FROM TB_TreatBillHandlerTimeCard tp WITH(NOLOCK) WHERE TCP.ID =
                TP.TreatBillTimeCardID )
                UNION ALL
                SELECT
                tcp.TreatBillID
                FROM
                TB_TreatBillSavingCard tcp WITH(NOLOCK)
                WHERE NOT EXISTS (SELECT 1 FROM TB_TreatBillHandlerSavingCard tp WITH(NOLOCK) WHERE TCP.ID =
                TP.TreatBillSavingCardID )
                )aa WHERE aa.TreatBillID=tsb.ID
                )
            </if>
        </where>
        ORDER BY tsb.CreatedOn DESC
    </select>

    <resultMap id="miniTreatBillListMap" type="com.zhelian.model.ibeauty.form.output.TreatBillOutputForm">
        <id column="ID" property="ID" jdbcType="VARCHAR"/>
        <result column="CustomerID" property="CustomerID" jdbcType="INTEGER"/>
        <result column="BillDate" property="BillDate" jdbcType="VARCHAR"/>
        <result column="Name" property="Name" jdbcType="VARCHAR"/>
        <result column="Code" property="Code" jdbcType="VARCHAR"/>
        <result column="PhoneNumber" property="PhoneNumber" jdbcType="VARCHAR"/>
        <result column="EntityName" property="EntityName" jdbcType="VARCHAR"/>
        <result column="Amount" property="Amount" jdbcType="DECIMAL"/>
        <result column="BillStatus" property="BillStatus" jdbcType="VARCHAR"/>
        <result column="BillType" property="BillType" jdbcType="VARCHAR"/>
        <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
        <result column="CreatedOn" property="CreatedOn" jdbcType="VARCHAR"/>
        <result column="Channel" property="Channel" jdbcType="VARCHAR"/>
        <result column="Remark" property="Remark" jdbcType="VARCHAR"/>
        <collection property="treatBillDetailList" column="{ID=ID}" select="getTreatBillDetail"
                    ofType="com.zhelian.model.ibeauty.form.output.TreatBillDetailOutputForm"/>
    </resultMap>

    <select id="getTreatBillDetail" resultType="com.zhelian.model.ibeauty.form.output.TreatBillDetailOutputForm">
        SELECT
            tp.Name GoodsName,
            ttbp.Quantity,
            ttbp.TotalAmount
        FROM
            TB_TreatBillProduct ttbp WITH(NOLOCK)
        LEFT JOIN TE_Product tp WITH(NOLOCK) ON ttbp.ProductID = tp.ID
        WHERE ttbp.TreatBillID = '${ID}'
        UNION ALL
        SELECT
            tp.Name GoodsName,
            ttbp.Quantity,
            ttbp.TotalAmount
        FROM
            TB_TreatBillGeneralCard ttbp WITH(NOLOCK)
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON ttbp.ProjectID = tp.ID
        WHERE ttbp.TreatBillID = '${ID}'
        UNION ALL
        SELECT
            tp.Name GoodsName,
            ttbp.Quantity,
            ttbp.TotalAmount
        FROM
            TB_TreatBillProject ttbp WITH(NOLOCK)
LEFT JOIN TB_Project tp WITH(NOLOCK) ON ttbp.ProjectID = tp.ID
        WHERE ttbp.TreatBillID = '${ID}'
        UNION ALL
        SELECT
            tp.Name GoodsName,
            ttbp.Quantity,
            ttbp.TotalAmount
        FROM
            TB_TreatBillSavingCard ttbp WITH(NOLOCK)
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON ttbp.ProjectID = tp.ID
        WHERE ttbp.TreatBillID = '${ID}'
        UNION ALL
        SELECT
            tp.Name GoodsName,
            ttbp.Quantity,
            ttbp.TotalAmount
        FROM
            TB_TreatBillTimeCard ttbp WITH(NOLOCK)
        LEFT JOIN TB_Project tp WITH(NOLOCK) ON ttbp.ProjectID = tp.ID
        WHERE ttbp.TreatBillID = '${ID}'
    </select>

    <select id="miniTreatBillList" resultMap="miniTreatBillListMap">
        SELECT
        tsb.ID,
        tsb.CustomerID,
        CONVERT(VARCHAR (19),tsb.BillDate,120) BillDate,
        case when tsb.CustomerID is null then '散客'
        else tc.Name end Name,
        tc.Code,
        tc.PhoneNumber as PhoneNumber,
        tsb.Amount,
        tsb.BillStatus,
        tsb.BillType,
        te.Name EmployeeName,
        tsb.Remark,
        tccl.Name Channel
        FROM
        TB_TreatBill tsb WITH(NOLOCK)
        LEFT JOIN TB_Customer tc WITH(NOLOCK) ON tc.ID=tsb.CustomerID
        LEFT JOIN TK_Employee te WITH(NOLOCK) ON te.ID=tsb.EmployeeID
        LEFT JOIN TB_Channel tccl WITH(NOLOCK) ON tccl.ID=tc.ChannelID
        <where>
            tsb.EntityID=#{EntityID}
            <if test="Name != null and Name != ''">
                <choose>
                    <when test="Name =='散客'">
                        AND tsb.CustomerID is null
                    </when>
                    <otherwise>
                        AND (tsb.ID = #{Name} OR tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%'
                        OR tc.Code LIKE '%'+ #{Name} +'%')
                    </otherwise>
                </choose>
            </if>
            <if test="StartDate != null and StartDate != ''and EndDate != null and EndDate != ''">
                AND tsb.BillDate >= #{StartDate} AND tsb.BillDate &lt; DATEADD(DAY,1,#{EndDate})
            </if>
            <if test="BillStatus != null and BillStatus != ''">
                AND tsb.BillStatus = #{BillStatus}
            </if>
            <if test="BillType != null and BillType != ''">
                AND tsb.BillType = #{BillType}
            </if>
            <if test="Channel != null and Channel != ''">
                AND tsb.Channel LIKE '%'+ #{Channel} +'%'
            </if>
            <if test="IsNoHaveHandler != null and IsNoHaveHandler ==true">
                AND NOT EXISTS (
                SELECT 1 FROM
                (
                SELECT
                tcp.TreatBillID
                FROM
                TB_TreatBillProduct tcp
                INNER JOIN
                TB_TreatBillHandlerProduct  tp WITH(NOLOCK) ON tp.TreatBillProductID=tcp.ID
                UNION ALL
                SELECT
                tcp.TreatBillID
                FROM
                TB_TreatBillProject tcp
                INNER JOIN
                TB_TreatBillHandlerProject  tp WITH(NOLOCK) ON tp.TreatBillProjectID=tcp.ID
                UNION ALL
                SELECT
                tcp.TreatBillID
                FROM
                TB_TreatBillGeneralCard tcp
                INNER JOIN
                TB_TreatBillHandlerGeneralCard  tp WITH(NOLOCK) ON tp.TreatBillGeneralCardID=tcp.ID
                UNION ALL
                SELECT
                tcp.TreatBillID
                FROM
                TB_TreatBillTimeCard tcp
                INNER JOIN
                TB_TreatBillHandlerTimeCard  tp WITH(NOLOCK) ON tp.TreatBillTimeCardID=tcp.ID
                UNION ALL
                SELECT
                tcp.TreatBillID
                FROM
                TB_TreatBillSavingCard tcp
                INNER JOIN
                TB_TreatBillHandlerSavingCard  tp WITH(NOLOCK) ON tp.TreatBillSavingCardID=tcp.ID

                )aa WHERE aa.TreatBillID=tsb.ID
                )
            </if>
        </where>
        ORDER by tsb.BillDate DESC
    </select>

    <select id="treatBillInfo" resultType="com.zhelian.model.ibeauty.form.output.TreatBillInfoOutputForm">
        SELECT tsb.ID,
               tsb.CustomerID,
               CONVERT(VARCHAR (19), tsb.BillDate, 120)                                              BillDate,
               case
                   when tsb.CustomerID is null then '散客'
                   else tc.Name end                                                                  Name,
               tc.Code,
               tc.PhoneNumber as                                                                     PhoneNumber,
               tsb.PricePreferentialAmount,
               tsb.MemberPreferentialAmount,
               tsb.CardPreferentialAmount,
               tsb.Amount,
               tsb.PayAmount,
               tsb.CardDeductionAmount,
               tsb.LargessCardDeductionAmount,
               tsb.Amount - tsb.PayAmount - tsb.CardDeductionAmount - tsb.LargessCardDeductionAmount LargessAmount,
               tsb.BillStatus,
               tsb.BillType,
               te.Name                                                                               EmployeeName,
               tsb.EntityID,
               tee.EntityName,
               ISNULL(pe.Name, '') + ISNULL(ce.Name, '') + ISNULL(ae.Name, '') +
               ISNULL(tee.AddressDetail, '')                                                         AddressDetail,
               CONVERT(VARCHAR (19), tsb.CreatedOn, 120)                                             CreatedOn,
               tsb.Remark,
               CASE
                   WHEN tsb.BillStatus = '20' and tsb.CustomerID is not null and dbo.getRefundTreatCount(tsb.ID) = 0
                       THEN 1
                   ELSE 0 END                                                                        Cancel,
               tep.Name                                                                              CancelCreatedBy,
               tsc.Remark                                                                            CancelRemark,
               CONVERT(VARCHAR (19), tsc.CreatedOn, 120)                                             CancelCreatedOn,
               tccl.Name Channel
        FROM TB_TreatBill tsb WITH(NOLOCK)
        LEFT JOIN  TB_Customer  tc
        WITH (NOLOCK)
        ON tc.ID=tsb.CustomerID
        LEFT JOIN TB_Channel tccl WITH(NOLOCK) ON tccl.ID=tc.ChannelID
            LEFT JOIN TK_Employee te
        WITH (NOLOCK)
        ON te.ID=tsb.EmployeeID
            LEFT JOIN TK_Entity tee
        WITH (NOLOCK)
        ON tee.ID=tsb.EntityID
            LEFT JOIN TS_Province pe
        WITH (NOLOCK)
        ON pe.Code=tee.ProvinceCode
            LEFT JOIN TS_City ce
        WITH (NOLOCK)
        ON ce.Code=tee.CityCode
            LEFT JOIN TS_Area ae
        WITH (NOLOCK)
        ON ae.Code=tee.AreaCode
            LEFT JOIN TB_TreatBillCancelProcess tsc
        WITH (NOLOCK)
        ON tsc.BillID=tsb.ID
            LEFT JOIN TK_Employee tep
        WITH (NOLOCK)
        ON tep.ID=tsc.CreatedBy
        WHERE tsb.ID=#{BillID}
    </select>

    <resultMap id="TreatBillProduct" type="com.zhelian.model.ibeauty.form.output.TreatBillProductInfoOutputForm">
        <id column="TreatBillProductID" property="TreatBillProductID" jdbcType="INTEGER"/>
        <result column="ProductAccountID" property="ProductAccountID" jdbcType="INTEGER"/>
        <result column="PackageCardAccountID" property="PackageCardAccountID" jdbcType="INTEGER"/>
        <result column="ProductID" property="ProductID" jdbcType="INTEGER"/>
        <result column="ProductName" property="ProductName" jdbcType="VARCHAR"/>
        <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        <result column="Price" property="Price" jdbcType="DECIMAL"/>
        <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
        <result column="CardPreferentialAmount" property="CardPreferentialAmount" jdbcType="DECIMAL"/>
        <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
        <result column="IsLargess" property="IsLargess" jdbcType="BIT"/>
        <result column="Amount" property="Amount" jdbcType="DECIMAL"/>
        <result column="PayAmount" property="PayAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionAmount" property="CardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="LargessCardDeductionAmount" property="LargessCardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="LargessAmount" property="LargessAmount" jdbcType="DECIMAL"/>
        <result column="Remark" property="Remark" jdbcType="VARCHAR"/>
        <collection property="TreatBillHandler"
                    ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerOutputForm">
            <id column="TreatHandlerID" property="TreatHandlerID" jdbcType="INTEGER"/>
            <result column="TreatHandlerName" property="TreatHandlerName" jdbcType="VARCHAR"/>
            <collection property="Employee"
                        ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerEmployeeOutputForm">
                <result column="EmployeeID" property="EmployeeID" jdbcType="INTEGER"/>
                <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
                <result column="Scale" property="Scale" jdbcType="DECIMAL"/>
            </collection>
        </collection>
    </resultMap>
    <resultMap id="TreatBillProject" type="com.zhelian.model.ibeauty.form.output.TreatBillProjectInfoOutputForm">
        <id column="TreatBillProjectID" property="TreatBillProjectID" jdbcType="INTEGER"/>
        <result column="ProjectAccountID" property="ProjectAccountID" jdbcType="INTEGER"/>
        <result column="ProjectID" property="ProjectID" jdbcType="INTEGER"/>
        <result column="ProjectName" property="ProjectName" jdbcType="VARCHAR"/>
        <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        <result column="Price" property="Price" jdbcType="DECIMAL"/>
        <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
        <result column="CardPreferentialAmount" property="CardPreferentialAmount" jdbcType="DECIMAL"/>
        <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
        <result column="Amount" property="Amount" jdbcType="DECIMAL"/>
        <result column="PayAmount" property="PayAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionAmount" property="CardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="LargessCardDeductionAmount" property="LargessCardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="LargessAmount" property="LargessAmount" jdbcType="DECIMAL"/>
        <result column="IsLargess" property="IsLargess" jdbcType="BIT"/>
        <result column="Remark" property="Remark" jdbcType="VARCHAR"/>
        <collection property="TreatBillHandler"
                    ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerOutputForm">
            <id column="TreatHandlerID" property="TreatHandlerID" jdbcType="INTEGER"/>
            <result column="TreatHandlerName" property="TreatHandlerName" jdbcType="VARCHAR"/>
            <collection property="Employee"
                        ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerEmployeeOutputForm">
                <result column="EmployeeID" property="EmployeeID" jdbcType="INTEGER"/>
                <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
                <result column="Scale" property="Scale" jdbcType="DECIMAL"/>
            </collection>
        </collection>
        <collection property="Consumable" select="getProjectConsumableList" column="{TreatBillProjectID=TreatBillProjectID}"
                    ofType="com.zhelian.model.ibeauty.form.output.TreatBillConsumableOutputForm">
            <result column="ProductName" property="ProductName" jdbcType="VARCHAR"/>
            <result column="Specification" property="Specification" jdbcType="VARCHAR"/>
            <result column="BrandName" property="BrandName" jdbcType="VARCHAR"/>
            <result column="EntityName" property="EntityName" jdbcType="VARCHAR"/>
            <result column="UnitName" property="UnitName" jdbcType="VARCHAR"/>
            <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
        </collection>
    </resultMap>
    <resultMap id="TreatBillGeneralCard"
               type="com.zhelian.model.ibeauty.form.output.TreatBillGeneralCardInfoOutputForm">
        <id column="GeneralCardAccountID" property="GeneralCardAccountID" jdbcType="INTEGER"/>
        <result column="GeneralCardID" property="GeneralCardID" jdbcType="INTEGER"/>
        <result column="GeneralCardName" property="GeneralCardName" jdbcType="VARCHAR"/>
        <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        <result column="IsLargess" property="IsLargess" jdbcType="BIT"/>
        <collection property="Project"
                    ofType="com.zhelian.model.ibeauty.form.output.TreatBillGeneralCardConsumeProjectInfoOutputForm">
            <id column="TreatBillGeneralCardID" property="TreatBillGeneralCardID" jdbcType="INTEGER"/>
            <result column="ProjectID" property="ProjectID" jdbcType="INTEGER"/>
            <result column="ProjectName" property="ProjectName" jdbcType="VARCHAR"/>
            <result column="ProjectAlias" property="Alias" jdbcType="VARCHAR"/>
            <result column="Price" property="Price" jdbcType="DECIMAL"/>
            <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
            <result column="CardPreferentialAmount" property="CardPreferentialAmount" jdbcType="DECIMAL"/>
            <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
            <result column="Amount" property="Amount" jdbcType="DECIMAL"/>
            <result column="PayAmount" property="PayAmount" jdbcType="DECIMAL"/>
            <result column="CardDeductionAmount" property="CardDeductionAmount" jdbcType="DECIMAL"/>
            <result column="LargessCardDeductionAmount" property="LargessCardDeductionAmount" jdbcType="DECIMAL"/>
            <result column="LargessAmount" property="LargessAmount" jdbcType="DECIMAL"/>
            <result column="Remark" property="Remark" jdbcType="VARCHAR"/>
            <collection property="TreatBillHandler"
                        ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerOutputForm">
                <id column="TreatHandlerID" property="TreatHandlerID" jdbcType="INTEGER"/>
                <result column="TreatHandlerName" property="TreatHandlerName" jdbcType="VARCHAR"/>
                <collection property="Employee"
                            ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerEmployeeOutputForm">
                    <result column="EmployeeID" property="EmployeeID" jdbcType="INTEGER"/>
                    <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
                    <result column="Scale" property="Scale" jdbcType="DECIMAL"/>
                </collection>
            </collection>
            <collection property="Consumable" select="getGeneralCardConsumableList" column="{TreatBillGeneralCardID=TreatBillGeneralCardID}"
                        ofType="com.zhelian.model.ibeauty.form.output.TreatBillConsumableOutputForm">
                <result column="ProductName" property="ProductName" jdbcType="VARCHAR"/>
                <result column="Specification" property="Specification" jdbcType="VARCHAR"/>
                <result column="BrandName" property="BrandName" jdbcType="VARCHAR"/>
                <result column="EntityName" property="EntityName" jdbcType="VARCHAR"/>
                <result column="UnitName" property="UnitName" jdbcType="VARCHAR"/>
                <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
            </collection>
        </collection>
    </resultMap>
    <resultMap id="TreatBillTimeCard" type="com.zhelian.model.ibeauty.form.output.TreatBillTimeCardInfoOutputForm">
        <id column="TimeCardAccountID" property="TimeCardAccountID" jdbcType="INTEGER"/>
        <result column="TimeCardID" property="TimeCardID" jdbcType="INTEGER"/>
        <result column="TimeCardName" property="TimeCardName" jdbcType="VARCHAR"/>
        <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        <result column="IsLargess" property="IsLargess" jdbcType="BIT"/>
        <collection property="Project"
                    ofType="com.zhelian.model.ibeauty.form.output.TreatBillTimeCardConsumeProjectInfoOutputForm">
            <id column="TreatBillTimeCardID" property="TreatBillTimeCardID" jdbcType="INTEGER"/>
            <result column="ProjectID" property="ProjectID" jdbcType="INTEGER"/>
            <result column="ProjectName" property="ProjectName" jdbcType="VARCHAR"/>
            <result column="ProjectAlias" property="Alias" jdbcType="VARCHAR"/>
            <result column="Price" property="Price" jdbcType="DECIMAL"/>
            <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
            <result column="CardPreferentialAmount" property="CardPreferentialAmount" jdbcType="DECIMAL"/>
            <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
            <result column="Amount" property="Amount" jdbcType="DECIMAL"/>
            <result column="PayAmount" property="PayAmount" jdbcType="DECIMAL"/>
            <result column="CardDeductionAmount" property="CardDeductionAmount" jdbcType="DECIMAL"/>
            <result column="LargessCardDeductionAmount" property="LargessCardDeductionAmount" jdbcType="DECIMAL"/>
            <result column="LargessAmount" property="LargessAmount" jdbcType="DECIMAL"/>
            <result column="Remark" property="Remark" jdbcType="VARCHAR"/>
            <collection property="TreatBillHandler"
                        ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerOutputForm">
                <id column="TreatHandlerID" property="TreatHandlerID" jdbcType="INTEGER"/>
                <result column="TreatHandlerName" property="TreatHandlerName" jdbcType="VARCHAR"/>
                <collection property="Employee"
                            ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerEmployeeOutputForm">
                    <result column="EmployeeID" property="EmployeeID" jdbcType="INTEGER"/>
                    <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
                    <result column="Scale" property="Scale" jdbcType="DECIMAL"/>
                </collection>
            </collection>
            <collection property="Consumable" select="getTimeCardConsumableList" column="{TreatBillTimeCardID=TreatBillTimeCardID}"
                        ofType="com.zhelian.model.ibeauty.form.output.TreatBillConsumableOutputForm">
                <result column="ProductName" property="ProductName" jdbcType="VARCHAR"/>
                <result column="Specification" property="Specification" jdbcType="VARCHAR"/>
                <result column="BrandName" property="BrandName" jdbcType="VARCHAR"/>
                <result column="EntityName" property="EntityName" jdbcType="VARCHAR"/>
                <result column="UnitName" property="UnitName" jdbcType="VARCHAR"/>
                <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
            </collection>
        </collection>
    </resultMap>
    <resultMap id="TreatBillSavingCard" type="com.zhelian.model.ibeauty.form.output.TreatBillSavingCardInfoOutputForm">
        <id column="SavingCardAccountID" property="SavingCardAccountID" jdbcType="INTEGER"/>
        <result column="SavingCardID" property="SavingCardID" jdbcType="INTEGER"/>
        <result column="SavingCardName" property="SavingCardName" jdbcType="VARCHAR"/>
        <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        <collection property="Project"
                    ofType="com.zhelian.model.ibeauty.form.output.TreatBillSavingCardConsumeProjectInfoOutputForm">
            <id column="TreatBillSavingCardID" property="TreatBillSavingCardID" jdbcType="INTEGER"/>
            <result column="ProjectID" property="ProjectID" jdbcType="INTEGER"/>
            <result column="ProjectName" property="ProjectName" jdbcType="VARCHAR"/>
            <result column="ProjectAlias" property="Alias" jdbcType="VARCHAR"/>
            <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
            <result column="Price" property="Price" jdbcType="DECIMAL"/>
            <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
            <result column="CardPreferentialAmount" property="CardPreferentialAmount" jdbcType="DECIMAL"/>
            <result column="PricePreferentialAmount" property="PricePreferentialAmount" jdbcType="DECIMAL"/>
            <result column="MemberPreferentialAmount" property="MemberPreferentialAmount" jdbcType="DECIMAL"/>
            <result column="Amount" property="Amount" jdbcType="DECIMAL"/>
            <result column="PayAmount" property="PayAmount" jdbcType="DECIMAL"/>
            <result column="CardDeductionAmount" property="CardDeductionAmount" jdbcType="DECIMAL"/>
            <result column="LargessCardDeductionAmount" property="LargessCardDeductionAmount" jdbcType="DECIMAL"/>
            <result column="LargessAmount" property="LargessAmount" jdbcType="DECIMAL"/>
            <result column="Remark" property="Remark" jdbcType="VARCHAR"/>
            <collection property="TreatBillHandler"
                        ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerOutputForm">
                <id column="TreatHandlerID" property="TreatHandlerID" jdbcType="INTEGER"/>
                <result column="TreatHandlerName" property="TreatHandlerName" jdbcType="VARCHAR"/>
                <collection property="Employee"
                            ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerEmployeeOutputForm">
                    <result column="EmployeeID" property="EmployeeID" jdbcType="INTEGER"/>
                    <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
                    <result column="Scale" property="Scale" jdbcType="DECIMAL"/>
                </collection>
            </collection>
            <collection property="Consumable" select="getSavingCardConsumableList" column="{TreatBillSavingCardID=TreatBillSavingCardID}"
                        ofType="com.zhelian.model.ibeauty.form.output.TreatBillConsumableOutputForm">
                <result column="ProductName" property="ProductName" jdbcType="VARCHAR"/>
                <result column="Specification" property="Specification" jdbcType="VARCHAR"/>
                <result column="BrandName" property="BrandName" jdbcType="VARCHAR"/>
                <result column="EntityName" property="EntityName" jdbcType="VARCHAR"/>
                <result column="UnitName" property="UnitName" jdbcType="VARCHAR"/>
                <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
            </collection>
        </collection>
    </resultMap>


    <select id="getProjectConsumableList" resultType="com.zhelian.model.ibeauty.form.output.TreatBillConsumableOutputForm">
        SELECT
            tp.Name ProductName,
            tp.Specification ,
            tpb.Name BrandName,
            te.EntityName ,
            tu.Name UnitName,
            t.Quantity
        FROM
            TE_InventoryTreatProjectOutboundDetail t WITH(NOLOCK)
                LEFT JOIN TE_InventoryTreatOutbound tt WITH(NOLOCK) ON t.InventoryTreatOutboundID=tt.ID
                LEFT JOIN TK_Entity te  WITH(NOLOCK) ON tt.EntityID =te.ID
                LEFT JOIN TE_Product tp WITH(NOLOCK) ON t.ProductID  =tp.ID
                LEFT JOIN TE_ProductBrand tpb WITH(NOLOCK) ON tp.ProductBrandID  =tpb.ID
                LEFT JOIN TE_Unit tu  WITH(NOLOCK)  ON t.UnitID  =tu.ID
        WHERE t .TreatBillProjectID =#{TreatBillProjectID}
    </select>
    <select id="getGeneralCardConsumableList" resultType="com.zhelian.model.ibeauty.form.output.TreatBillConsumableOutputForm">
        SELECT
            tp.Name ProductName,
            tp.Specification ,
            tpb.Name BrandName,
            te.EntityName ,
            tu.Name UnitName,
            t.Quantity
        FROM
            TE_InventoryTreatGeneralCardOutboundDetail t WITH(NOLOCK)
                LEFT JOIN TE_InventoryTreatOutbound tt WITH(NOLOCK) ON t.InventoryTreatOutboundID=tt.ID
            LEFT JOIN TK_Entity te  WITH(NOLOCK) ON tt.EntityID =te.ID
            LEFT JOIN TE_Product tp WITH(NOLOCK) ON t.ProductID  =tp.ID
            LEFT JOIN TE_ProductBrand tpb WITH(NOLOCK) ON tp.ProductBrandID  =tpb.ID
            LEFT JOIN TE_Unit tu  WITH(NOLOCK)  ON t.UnitID  =tu.ID
        WHERE t .TreatBillGeneralCardID =#{TreatBillGeneralCardID}
    </select>
    <select id="getTimeCardConsumableList" resultType="com.zhelian.model.ibeauty.form.output.TreatBillConsumableOutputForm">
        SELECT
            tp.Name ProductName,
            tp.Specification ,
            tpb.Name BrandName,
            te.EntityName ,
            tu.Name UnitName,
            t.Quantity
        FROM
            TE_InventoryTreatTimeCardOutboundDetail t WITH(NOLOCK)
                LEFT JOIN TE_InventoryTreatOutbound tt WITH(NOLOCK) ON t.InventoryTreatOutboundID=tt.ID
            LEFT JOIN TK_Entity te  WITH(NOLOCK) ON tt.EntityID =te.ID
            LEFT JOIN TE_Product tp WITH(NOLOCK) ON t.ProductID  =tp.ID
            LEFT JOIN TE_ProductBrand tpb WITH(NOLOCK) ON tp.ProductBrandID  =tpb.ID
            LEFT JOIN TE_Unit tu  WITH(NOLOCK)  ON t.UnitID  =tu.ID
        WHERE t .TreatBillTimeCardID =#{TreatBillTimeCardID}
    </select>
    <select id="getSavingCardConsumableList" resultType="com.zhelian.model.ibeauty.form.output.TreatBillConsumableOutputForm">
        SELECT
            tp.Name ProductName,
            tp.Specification ,
            tpb.Name BrandName,
            te.EntityName ,
            tu.Name UnitName,
            t.Quantity
        FROM
            TE_InventoryTreatSavingCardOutboundDetail t WITH(NOLOCK)
                LEFT JOIN TE_InventoryTreatOutbound tt WITH(NOLOCK) ON t.InventoryTreatOutboundID=tt.ID
                LEFT JOIN TK_Entity te  WITH(NOLOCK) ON tt.EntityID =te.ID
                LEFT JOIN TE_Product tp WITH(NOLOCK) ON t.ProductID  =tp.ID
                LEFT JOIN TE_ProductBrand tpb WITH(NOLOCK) ON tp.ProductBrandID  =tpb.ID
                LEFT JOIN TE_Unit tu  WITH(NOLOCK)  ON t.UnitID  =tu.ID
        WHERE t .TreatBillSavingCardID =#{TreatBillSavingCardID}
    </select>


    <select id="treatBillProductInfo" resultMap="TreatBillProduct">
        SELECT tsp.ID                                  TreatBillProductID,
               tsp.ProductAccountID,
               tsp.ProductID,
               te.Name                                 ProductName,
               te.Alias,
               tsp.Quantity,
               tsp.Price,
               tsp.CardPreferentialAmount,
               tsp.TotalAmount,
               tsp.PayAmount + tsp.CardDeductionAmount Amount,
               tsp.PayAmount,
               tsp.CardDeductionAmount,
               tsp.LargessCardDeductionAmount,
               case tsp.IsLargess
                   when 0 then 0
                   else tsp.TotalAmount
                   end
                                                       LargessAmount,
               tsp.IsLargess,
               tsh.ProductTreatHandlerID               TreatHandlerID,
               tp.Name                                 TreatHandlerName,
               tsh.EmployeeID,
               tee.Name                                EmployeeName,
               tsh.Scale * 100                         Scale,
               tsp.Remark
        FROM TB_TreatBillProduct tsp WITH(NOLOCK)
        LEFT JOIN  TE_Product te
        WITH (NOLOCK)
        ON te.ID=tsp.ProductID
            LEFT JOIN TB_TreatBillHandlerProduct tsh
        WITH (NOLOCK)
        ON tsh.TreatBillProductID=tsp.ID
            LEFT JOIN TB_ProductTreatHandler tp
        WITH (NOLOCK)
        ON tp.ID=tsh.ProductTreatHandlerID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tsh.EmployeeID
        WHERE tsp.TreatBillID=#{BillID} AND tsp.PackageCardAccountID IS NULL
        ORDER BY tsp.ID ASC, tp.Sequence DESC
    </select>
    <select id="treatBillProjectInfo" resultMap="TreatBillProject">
        SELECT tsp.ID                                  TreatBillProjectID,
               tsp.ProjectAccountID,
               tsp.ProjectID,
               tpt.Name                                ProjectName,
               tpt.Alias,
               tsp.Quantity,
               tsp.Price,
               tsp.CardPreferentialAmount,
               tsp.TotalAmount,
               tsp.PayAmount + tsp.CardDeductionAmount Amount,
               tsp.PayAmount,
               tsp.CardDeductionAmount,
               tsp.LargessCardDeductionAmount,
               case tsp.IsLargess
                   when 0 then 0
                   else tsp.TotalAmount
                   end
                                                       LargessAmount,
               tsp.IsLargess,
               tsh.ProjectTreatHandlerID               TreatHandlerID,
               tp.Name                                 TreatHandlerName,
               tsh.EmployeeID,
               tee.Name                                EmployeeName,
               tsh.Scale * 100                         Scale,
               tsp.Remark
        FROM TB_TreatBillProject tsp WITH(NOLOCK)
        LEFT JOIN  TB_Project tpt
        WITH (NOLOCK)
        ON tsp.ProjectID=tpt.ID
            LEFT JOIN TB_TreatBillHandlerProject tsh
        WITH (NOLOCK)
        ON tsh.TreatBillProjectID=tsp.ID
            LEFT JOIN TB_ProjectTreatHandler tp
        WITH (NOLOCK)
        ON tp.ID=tsh.ProjectTreatHandlerID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tsh.EmployeeID
        WHERE tsp.TreatBillID=#{BillID} AND tsp.PackageCardAccountID IS NULL
        ORDER BY tsp.ID ASC, tp.Sequence desc
    </select>
    <select id="treatBillGeneralCardInfo" resultMap="TreatBillGeneralCard">
        SELECT tsp.ID                                  TreatBillGeneralCardID,
               tsp.GeneralCardAccountID,
               tgc.GeneralCardID,
               tpt.Name                                GeneralCardName,
               tpt.Alias,
               tsp.ProjectID,
               t.Name                                  ProjectName,
               t.Alias                                 ProjectAlias,
               tsp.Quantity,
               tsp.Price,
               tsp.CardPreferentialAmount,
               tsp.TotalAmount,
               tsp.PayAmount + tsp.CardDeductionAmount Amount,
               tsp.PayAmount,
               tsp.CardDeductionAmount,
               tsp.LargessCardDeductionAmount,
               case tsp.IsLargess
                   when 0 then 0
                   else tsp.TotalAmount
                   end
                                                       LargessAmount,
               tsp.IsLargess,
               tsh.GeneralCardTreatHandlerID           TreatHandlerID,
               tp.Name                                 TreatHandlerName,
               tsh.EmployeeID,
               tee.Name                                EmployeeName,
               tsh.Scale * 100                         Scale,
               tsp.Remark
        FROM TB_TreatBillGeneralCard tsp WITH(NOLOCK)
        LEFT JOIN TB_GeneralCardAccount tgc
        WITH (NOLOCK)
        ON tgc.ID=tsp.GeneralCardAccountID
            LEFT JOIN TB_GeneralCard tpt
        WITH (NOLOCK)
        ON tgc.GeneralCardID=tpt.ID
            LEFT JOIN TB_Project t
        WITH (NOLOCK)
        ON t.ID=tsp.ProjectID
            LEFT JOIN TB_TreatBillHandlerGeneralCard tsh
        WITH (NOLOCK)
        ON tsh.TreatBillGeneralCardID=tsp.ID
            LEFT JOIN TB_ProjectTreatHandler tp
        WITH (NOLOCK)
        ON tp.ID=tsh.GeneralCardTreatHandlerID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tsh.EmployeeID
        WHERE tsp.TreatBillID=#{BillID} AND tsp.PackageCardAccountID IS NULL
        ORDER BY tsp.ID ASC, tp.Sequence desc
    </select>
    <select id="treatBillTimeCardInfo" resultMap="TreatBillTimeCard">
        SELECT tsp.ID                                  TreatBillTimeCardID,
               tsp.TimeCardAccountID,
               tgc.TimeCardID,
               tpt.Name                                TimeCardName,
               tpt.Alias,
               tsp.ProjectID,
               t.Name                                  ProjectName,
               t.Alias                                 ProjectAlias,
               tsp.Quantity,
               tsp.Price,
               tsp.CardPreferentialAmount,
               tsp.TotalAmount,
               tsp.PayAmount + tsp.CardDeductionAmount Amount,
               tsp.PayAmount,
               tsp.CardDeductionAmount,
               tsp.LargessCardDeductionAmount,
               case tsp.IsLargess
                   when 0 then 0
                   else tsp.TotalAmount
                   end
                                                       LargessAmount,
               tsp.IsLargess,
               tsh.TimeCardTreatHandlerID              TreatHandlerID,
               tp.Name                                 TreatHandlerName,
               tsh.EmployeeID,
               tee.Name                                EmployeeName,
               tsh.Scale * 100                         Scale,
               tsp.Remark
        FROM TB_TreatBillTimeCard tsp WITH(NOLOCK)
        LEFT JOIN TB_TimeCardAccount tgc
        WITH (NOLOCK)
        ON tgc.ID=tsp.TimeCardAccountID
            LEFT JOIN TB_TimeCard tpt
        WITH (NOLOCK)
        ON tgc.TimeCardID=tpt.ID
            LEFT JOIN TB_Project t
        WITH (NOLOCK)
        ON t.ID=tsp.ProjectID
            LEFT JOIN TB_TreatBillHandlerTimeCard tsh
        WITH (NOLOCK)
        ON tsh.TreatBillTimeCardID=tsp.ID
            LEFT JOIN TB_ProjectTreatHandler tp
        WITH (NOLOCK)
        ON tp.ID=tsh.TimeCardTreatHandlerID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tsh.EmployeeID
        WHERE tsp.TreatBillID=#{BillID} AND tsp.PackageCardAccountID IS NULL
        ORDER BY tsp.ID ASC, tp.Sequence desc
    </select>
    <select id="treatBillSavingCardInfo" resultMap="TreatBillSavingCard">
        SELECT tsp.ID                       TreatBillSavingCardID,
               tsp.SavingCardAccountID,
               tgc.SavingCardID,
               tpt.Name                     SavingCardName,
               tpt.Alias,
               tsp.ProjectID,
               t.Name                       ProjectName,
               t.Alias                      ProjectAlias,
               tsp.Quantity,
               t.Price,
               tsp.TotalAmount,
               tsp.CardTreatAmount          Amount,
               0                            PayAmount,
               tsp.CardTreatAmount          CardDeductionAmount,
               tsp.LargessCardTreatAmount   LargessCardDeductionAmount,
               0                            LargessAmount,
               tsp.CardPreferentialAmount,
               tsp.PricePreferentialAmount,
               tsp.MemberPreferentialAmount,
               tsh.SavingCardTreatHandlerID TreatHandlerID,
               tp.Name                      TreatHandlerName,
               tsh.EmployeeID,
               tee.Name                     EmployeeName,
               tsh.Scale * 100              Scale,
               tsp.Remark
        FROM TB_TreatBillSavingCard tsp WITH(NOLOCK)
        LEFT JOIN TB_SavingCardAccount tgc
        WITH (NOLOCK)
        ON tgc.ID=tsp.SavingCardAccountID
            LEFT JOIN TB_SavingCard tpt
        WITH (NOLOCK)
        ON tgc.SavingCardID=tpt.ID
            LEFT JOIN TB_Project t
        WITH (NOLOCK)
        ON t.ID=tsp.ProjectID
            LEFT JOIN TB_TreatBillHandlerSavingCard tsh
        WITH (NOLOCK)
        ON tsh.TreatBillSavingCardID=tsp.ID
            LEFT JOIN TB_SavingCardProjectTreatHandler tp
        WITH (NOLOCK)
        ON tp.ID=tsh.SavingCardTreatHandlerID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tsh.EmployeeID
        WHERE tsp.TreatBillID=#{BillID} AND tsp.PackageCardAccountID IS NULL
        ORDER BY tsp.ID ASC, tp.Sequence desc
    </select>
    <select id="treatBillPackageCardInfo"
            resultType="com.zhelian.model.ibeauty.form.output.TreatBillPackageCardInfoOutputForm">
        SELECT T.PackageCardAccountID,
               T.PackageCardID,
               tp.Name PackageCardName,
               tp.Alias
        FROM (SELECT PackageCardAccountID, PackageCardID
              FROM TB_TreatBillProduct WITH(NOLOCK)
              WHERE TreatBillID=#{BillID} AND PackageCardAccountID IS NOT NULL
              UNION
              SELECT PackageCardAccountID, PackageCardID
              FROM TB_TreatBillProject
              WITH (NOLOCK)
              WHERE TreatBillID=#{BillID} AND PackageCardAccountID IS NOT NULL
              UNION
              SELECT PackageCardAccountID, PackageCardID
              FROM TB_TreatBillGeneralCard
              WITH (NOLOCK)
              WHERE TreatBillID=#{BillID} AND PackageCardAccountID IS NOT NULL
              UNION
              SELECT PackageCardAccountID, PackageCardID
              FROM TB_TreatBillTimeCard
              WITH (NOLOCK)
              WHERE TreatBillID=#{BillID} AND PackageCardAccountID IS NOT NULL
              UNION
              SELECT PackageCardAccountID, PackageCardID
              FROM TB_TreatBillSavingCard
              WITH (NOLOCK)
              WHERE TreatBillID=#{BillID} AND PackageCardAccountID IS NOT NULL) T
                 LEFT JOIN TB_PackageCard tp on T.PackageCardID = tp.ID
    </select>

    <resultMap id="TreatBillPackageCardProduct"
               type="com.zhelian.model.ibeauty.form.output.TreatBillProductInfoExtendOutputForm">
        <id column="TreatBillProductID" property="TreatBillProductID" jdbcType="INTEGER"/>
        <result column="ProductAccountID" property="ProductAccountID" jdbcType="INTEGER"/>
        <result column="PackageCardAccountID" property="PackageCardAccountID" jdbcType="INTEGER"/>
        <result column="ProductID" property="ProductID" jdbcType="INTEGER"/>
        <result column="ProductName" property="ProductName" jdbcType="VARCHAR"/>
        <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        <result column="Price" property="Price" jdbcType="DECIMAL"/>
        <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
        <result column="CardPreferentialAmount" property="CardPreferentialAmount" jdbcType="DECIMAL"/>
        <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
        <result column="IsLargess" property="IsLargess" jdbcType="BIT"/>
        <result column="Amount" property="Amount" jdbcType="DECIMAL"/>
        <result column="PayAmount" property="PayAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionAmount" property="CardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="LargessCardDeductionAmount" property="LargessCardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="LargessAmount" property="LargessAmount" jdbcType="DECIMAL"/>
        <result column="Remark" property="Remark" jdbcType="VARCHAR"/>
        <collection property="TreatBillHandler"
                    ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerOutputForm">
            <id column="TreatHandlerID" property="TreatHandlerID" jdbcType="INTEGER"/>
            <result column="TreatHandlerName" property="TreatHandlerName" jdbcType="VARCHAR"/>
            <collection property="Employee"
                        ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerEmployeeOutputForm">
                <result column="EmployeeID" property="EmployeeID" jdbcType="INTEGER"/>
                <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
                <result column="Scale" property="Scale" jdbcType="DECIMAL"/>
            </collection>
        </collection>
    </resultMap>
    <resultMap id="TreatBillPackageCardProject"
               type="com.zhelian.model.ibeauty.form.output.TreatBillProjectInfoExtendOutputForm">
        <id column="TreatBillProjectID" property="TreatBillProjectID" jdbcType="INTEGER"/>
        <result column="ProjectAccountID" property="ProjectAccountID" jdbcType="INTEGER"/>
        <result column="PackageCardAccountID" property="PackageCardAccountID" jdbcType="INTEGER"/>
        <result column="ProjectID" property="ProjectID" jdbcType="INTEGER"/>
        <result column="ProjectName" property="ProjectName" jdbcType="VARCHAR"/>
        <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        <result column="Price" property="Price" jdbcType="DECIMAL"/>
        <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
        <result column="CardPreferentialAmount" property="CardPreferentialAmount" jdbcType="DECIMAL"/>
        <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
        <result column="IsLargess" property="IsLargess" jdbcType="BIT"/>
        <result column="Amount" property="Amount" jdbcType="DECIMAL"/>
        <result column="PayAmount" property="PayAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionAmount" property="CardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="LargessCardDeductionAmount" property="LargessCardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="LargessAmount" property="LargessAmount" jdbcType="DECIMAL"/>
        <result column="Remark" property="Remark" jdbcType="VARCHAR"/>
        <collection property="TreatBillHandler"
                    ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerOutputForm">
            <id column="TreatHandlerID" property="TreatHandlerID" jdbcType="INTEGER"/>
            <result column="TreatHandlerName" property="TreatHandlerName" jdbcType="VARCHAR"/>
            <collection property="Employee"
                        ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerEmployeeOutputForm">
                <result column="EmployeeID" property="EmployeeID" jdbcType="INTEGER"/>
                <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
                <result column="Scale" property="Scale" jdbcType="DECIMAL"/>
            </collection>
        </collection>
        <collection property="Consumable" select="getProjectConsumableList" column="{TreatBillProjectID=TreatBillProjectID}"
                    ofType="com.zhelian.model.ibeauty.form.output.TreatBillConsumableOutputForm">
            <result column="ProductName" property="ProductName" jdbcType="VARCHAR"/>
            <result column="Specification" property="Specification" jdbcType="VARCHAR"/>
            <result column="BrandName" property="BrandName" jdbcType="VARCHAR"/>
            <result column="EntityName" property="EntityName" jdbcType="VARCHAR"/>
            <result column="UnitName" property="UnitName" jdbcType="VARCHAR"/>
            <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
        </collection>
    </resultMap>
    <resultMap id="TreatBillPackageCardGeneralCard"
               type="com.zhelian.model.ibeauty.form.output.TreatBillGeneralCardInfoExtendOutputForm">
        <id column="GeneralCardAccountID" property="GeneralCardAccountID" jdbcType="INTEGER"/>
        <result column="PackageCardAccountID" property="PackageCardAccountID" jdbcType="INTEGER"/>
        <result column="GeneralCardID" property="GeneralCardID" jdbcType="INTEGER"/>
        <result column="GeneralCardName" property="GeneralCardName" jdbcType="VARCHAR"/>
        <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        <result column="IsLargess" property="IsLargess" jdbcType="BIT"/>
        <collection property="Project"
                    ofType="com.zhelian.model.ibeauty.form.output.TreatBillGeneralCardConsumeProjectInfoOutputForm">
            <id column="TreatBillGeneralCardID" property="TreatBillGeneralCardID" jdbcType="INTEGER"/>
            <result column="ProjectID" property="ProjectID" jdbcType="INTEGER"/>
            <result column="ProjectName" property="ProjectName" jdbcType="VARCHAR"/>
            <result column="ProjectAlias" property="Alias" jdbcType="VARCHAR"/>
            <result column="Price" property="Price" jdbcType="DECIMAL"/>
            <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
            <result column="CardPreferentialAmount" property="CardPreferentialAmount" jdbcType="DECIMAL"/>
            <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
            <result column="Amount" property="Amount" jdbcType="DECIMAL"/>
            <result column="PayAmount" property="PayAmount" jdbcType="DECIMAL"/>
            <result column="CardDeductionAmount" property="CardDeductionAmount" jdbcType="DECIMAL"/>
            <result column="LargessCardDeductionAmount" property="LargessCardDeductionAmount" jdbcType="DECIMAL"/>
            <result column="LargessAmount" property="LargessAmount" jdbcType="DECIMAL"/>
            <result column="Remark" property="Remark" jdbcType="VARCHAR"/>
            <collection property="TreatBillHandler"
                        ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerOutputForm">
                <id column="TreatHandlerID" property="TreatHandlerID" jdbcType="INTEGER"/>
                <result column="TreatHandlerName" property="TreatHandlerName" jdbcType="VARCHAR"/>
                <collection property="Employee"
                            ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerEmployeeOutputForm">
                    <result column="EmployeeID" property="EmployeeID" jdbcType="INTEGER"/>
                    <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
                    <result column="Scale" property="Scale" jdbcType="DECIMAL"/>
                </collection>
            </collection>
            <collection property="Consumable" select="getGeneralCardConsumableList" column="{TreatBillGeneralCardID=TreatBillGeneralCardID}"
                        ofType="com.zhelian.model.ibeauty.form.output.TreatBillConsumableOutputForm">
                <result column="ProductName" property="ProductName" jdbcType="VARCHAR"/>
                <result column="Specification" property="Specification" jdbcType="VARCHAR"/>
                <result column="BrandName" property="BrandName" jdbcType="VARCHAR"/>
                <result column="EntityName" property="EntityName" jdbcType="VARCHAR"/>
                <result column="UnitName" property="UnitName" jdbcType="VARCHAR"/>
                <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
            </collection>
        </collection>
    </resultMap>
    <resultMap id="TreatBillPackageCardTimeCard"
               type="com.zhelian.model.ibeauty.form.output.TreatBillTimeCardInfoExtendOutputForm">
        <id column="TimeCardAccountID" property="TimeCardAccountID" jdbcType="INTEGER"/>
        <result column="PackageCardAccountID" property="PackageCardAccountID" jdbcType="INTEGER"/>
        <result column="TimeCardID" property="TimeCardID" jdbcType="INTEGER"/>
        <result column="TimeCardName" property="TimeCardName" jdbcType="VARCHAR"/>
        <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        <result column="IsLargess" property="IsLargess" jdbcType="BIT"/>
        <collection property="Project"
                    ofType="com.zhelian.model.ibeauty.form.output.TreatBillTimeCardConsumeProjectInfoOutputForm">
            <id column="TreatBillTimeCardID" property="TreatBillTimeCardID" jdbcType="INTEGER"/>
            <result column="ProjectID" property="ProjectID" jdbcType="INTEGER"/>
            <result column="ProjectName" property="ProjectName" jdbcType="VARCHAR"/>
            <result column="ProjectAlias" property="Alias" jdbcType="VARCHAR"/>
            <result column="Price" property="Price" jdbcType="DECIMAL"/>
            <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
            <result column="CardPreferentialAmount" property="CardPreferentialAmount" jdbcType="DECIMAL"/>
            <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
            <result column="Amount" property="Amount" jdbcType="DECIMAL"/>
            <result column="PayAmount" property="PayAmount" jdbcType="DECIMAL"/>
            <result column="CardDeductionAmount" property="CardDeductionAmount" jdbcType="DECIMAL"/>
            <result column="LargessCardDeductionAmount" property="LargessCardDeductionAmount" jdbcType="DECIMAL"/>
            <result column="LargessAmount" property="LargessAmount" jdbcType="DECIMAL"/>
            <result column="Remark" property="Remark" jdbcType="VARCHAR"/>
            <collection property="TreatBillHandler"
                        ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerOutputForm">
                <id column="TreatHandlerID" property="TreatHandlerID" jdbcType="INTEGER"/>
                <result column="TreatHandlerName" property="TreatHandlerName" jdbcType="VARCHAR"/>
                <collection property="Employee"
                            ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerEmployeeOutputForm">
                    <result column="EmployeeID" property="EmployeeID" jdbcType="INTEGER"/>
                    <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
                    <result column="Scale" property="Scale" jdbcType="DECIMAL"/>
                </collection>
            </collection>
            <collection property="Consumable" select="getTimeCardConsumableList" column="{TreatBillTimeCardID=TreatBillTimeCardID}"
                        ofType="com.zhelian.model.ibeauty.form.output.TreatBillConsumableOutputForm">
                <result column="ProductName" property="ProductName" jdbcType="VARCHAR"/>
                <result column="Specification" property="Specification" jdbcType="VARCHAR"/>
                <result column="BrandName" property="BrandName" jdbcType="VARCHAR"/>
                <result column="EntityName" property="EntityName" jdbcType="VARCHAR"/>
                <result column="UnitName" property="UnitName" jdbcType="VARCHAR"/>
                <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
            </collection>
        </collection>
    </resultMap>
    <resultMap id="TreatBillPackageCardSavingCard"
               type="com.zhelian.model.ibeauty.form.output.TreatBillSavingCardInfoExtendOutputForm">
        <id column="SavingCardAccountID" property="SavingCardAccountID" jdbcType="INTEGER"/>
        <result column="PackageCardAccountID" property="PackageCardAccountID" jdbcType="INTEGER"/>
        <result column="SavingCardID" property="SavingCardID" jdbcType="INTEGER"/>
        <result column="SavingCardName" property="SavingCardName" jdbcType="VARCHAR"/>
        <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        <collection property="Project"
                    ofType="com.zhelian.model.ibeauty.form.output.TreatBillSavingCardConsumeProjectInfoOutputForm">
            <id column="TreatBillSavingCardID" property="TreatBillSavingCardID" jdbcType="INTEGER"/>
            <result column="ProjectID" property="ProjectID" jdbcType="INTEGER"/>
            <result column="ProjectName" property="ProjectName" jdbcType="VARCHAR"/>
            <result column="ProjectAlias" property="Alias" jdbcType="VARCHAR"/>
            <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
            <result column="Price" property="Price" jdbcType="DECIMAL"/>
            <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
            <result column="Amount" property="Amount" jdbcType="DECIMAL"/>
            <result column="PayAmount" property="PayAmount" jdbcType="DECIMAL"/>
            <result column="CardDeductionAmount" property="CardDeductionAmount" jdbcType="DECIMAL"/>
            <result column="LargessCardDeductionAmount" property="LargessCardDeductionAmount" jdbcType="DECIMAL"/>
            <result column="LargessAmount" property="LargessAmount" jdbcType="DECIMAL"/>
            <result column="Remark" property="Remark" jdbcType="VARCHAR"/>
            <result column="CardPreferentialAmount" property="CardPreferentialAmount" jdbcType="DECIMAL"/>
            <result column="PricePreferentialAmount" property="PricePreferentialAmount" jdbcType="DECIMAL"/>
            <result column="MemberPreferentialAmount" property="MemberPreferentialAmount" jdbcType="DECIMAL"/>
            <collection property="TreatBillHandler"
                        ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerOutputForm">
                <id column="TreatHandlerID" property="TreatHandlerID" jdbcType="INTEGER"/>
                <result column="TreatHandlerName" property="TreatHandlerName" jdbcType="VARCHAR"/>
                <collection property="Employee"
                            ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerEmployeeOutputForm">
                    <result column="EmployeeID" property="EmployeeID" jdbcType="INTEGER"/>
                    <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
                    <result column="Scale" property="Scale" jdbcType="DECIMAL"/>
                </collection>
            </collection>
            <collection property="Consumable" select="getSavingCardConsumableList" column="{TreatBillSavingCardID=TreatBillSavingCardID}"
                        ofType="com.zhelian.model.ibeauty.form.output.TreatBillConsumableOutputForm">
                <result column="ProductName" property="ProductName" jdbcType="VARCHAR"/>
                <result column="Specification" property="Specification" jdbcType="VARCHAR"/>
                <result column="BrandName" property="BrandName" jdbcType="VARCHAR"/>
                <result column="EntityName" property="EntityName" jdbcType="VARCHAR"/>
                <result column="UnitName" property="UnitName" jdbcType="VARCHAR"/>
                <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
            </collection>
        </collection>
    </resultMap>

    <select id="treatBillPackageCardProductInfo" resultMap="TreatBillPackageCardProduct">
        SELECT tsp.ID                                  TreatBillProductID,
               tsp.PackageCardAccountID,
               tsp.ProductAccountID,
               tsp.ProductID,
               te.Name                                 ProductName,
               te.Alias,
               tsp.Quantity,
               tsp.CardPreferentialAmount,
               tsp.TotalAmount,
               tsp.PayAmount + tsp.CardDeductionAmount Amount,
               tsp.PayAmount,
               tsp.CardDeductionAmount,
               tsp.LargessCardDeductionAmount,
               case tsp.IsLargess
                   when 0 then 0
                   else tsp.TotalAmount
                   end
                                                       LargessAmount,
               tsp.Price,
               tsp.IsLargess,
               tsh.ProductTreatHandlerID               TreatHandlerID,
               tp.Name                                 TreatHandlerName,
               tsh.EmployeeID,
               tee.Name                                EmployeeName,
               tsh.Scale * 100                         Scale,
               tsp.Remark
        FROM TB_TreatBillProduct tsp WITH(NOLOCK)
        LEFT JOIN  TE_Product te
        WITH (NOLOCK)
        ON te.ID=tsp.ProductID
            LEFT JOIN TB_TreatBillHandlerProduct tsh
        WITH (NOLOCK)
        ON tsh.TreatBillProductID=tsp.ID
            LEFT JOIN TB_ProductTreatHandler tp
        WITH (NOLOCK)
        ON tp.ID=tsh.ProductTreatHandlerID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tsh.EmployeeID
        WHERE tsp.TreatBillID=#{BillID} AND tsp.PackageCardAccountID IS NOT NULL
        ORDER BY tsp.ID ASC, tp.Sequence desc
    </select>
    <select id="treatBillPackageCardProjectInfo" resultMap="TreatBillPackageCardProject">
        SELECT tsp.ID                                  TreatBillProjectID,
               tsp.PackageCardAccountID,
               tsp.ProjectAccountID,
               tsp.ProjectID,
               tpt.Name                                ProjectName,
               tpt.Alias,
               tsp.Quantity,
               tsp.CardPreferentialAmount,
               tsp.TotalAmount,
               tsp.PayAmount + tsp.CardDeductionAmount Amount,
               tsp.PayAmount,
               tsp.CardDeductionAmount,
               tsp.LargessCardDeductionAmount,
               case tsp.IsLargess
                   when 0 then 0
                   else tsp.TotalAmount
                   end
                                                       LargessAmount,
               tsp.Price,
               tsp.IsLargess,
               tsh.ProjectTreatHandlerID               TreatHandlerID,
               tp.Name                                 TreatHandlerName,
               tsh.EmployeeID,
               tee.Name                                EmployeeName,
               tsh.Scale * 100                         Scale,
               tsp.Remark
        FROM TB_TreatBillProject tsp WITH(NOLOCK)
        LEFT JOIN  TB_Project tpt
        WITH (NOLOCK)
        ON tsp.ProjectID=tpt.ID
            LEFT JOIN TB_TreatBillHandlerProject tsh
        WITH (NOLOCK)
        ON tsh.TreatBillProjectID=tsp.ID
            LEFT JOIN TB_ProjectTreatHandler tp
        WITH (NOLOCK)
        ON tp.ID=tsh.ProjectTreatHandlerID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tsh.EmployeeID
        WHERE tsp.TreatBillID=#{BillID} AND tsp.PackageCardAccountID IS NOT NULL
        ORDER BY tsp.ID ASC, tp.Sequence desc
    </select>
    <select id="treatBillPackageCardGeneralCardInfo" resultMap="TreatBillPackageCardGeneralCard">
        SELECT tsp.ID                                  TreatBillGeneralCardID,
               tsp.PackageCardAccountID,
               tsp.GeneralCardAccountID,
               tgc.GeneralCardID,
               tpt.Name                                GeneralCardName,
               tpt.Alias,
               tsp.ProjectID,
               t.Name                                  ProjectName,
               t.Alias                                 ProjectAlias,
               tsp.Quantity,
               tsp.CardPreferentialAmount,
               tsp.TotalAmount,
               tsp.PayAmount + tsp.CardDeductionAmount Amount,
               tsp.PayAmount,
               tsp.CardDeductionAmount,
               tsp.LargessCardDeductionAmount,
               case tsp.IsLargess
                   when 0 then 0
                   else tsp.TotalAmount
                   end
                                                       LargessAmount,
               tsp.Price,
               tsp.IsLargess,
               tsh.GeneralCardTreatHandlerID           TreatHandlerID,
               tp.Name                                 TreatHandlerName,
               tsh.EmployeeID,
               tee.Name                                EmployeeName,
               tsh.Scale * 100                         Scale,
               tsp.Remark
        FROM TB_TreatBillGeneralCard tsp WITH(NOLOCK)
        LEFT JOIN TB_GeneralCardAccount tgc
        WITH (NOLOCK)
        ON tgc.ID=tsp.GeneralCardAccountID
            LEFT JOIN TB_GeneralCard tpt
        WITH (NOLOCK)
        ON tgc.GeneralCardID=tpt.ID
            LEFT JOIN TB_Project t
        WITH (NOLOCK)
        ON t.ID=tsp.ProjectID
            LEFT JOIN TB_TreatBillHandlerGeneralCard tsh
        WITH (NOLOCK)
        ON tsh.TreatBillGeneralCardID=tsp.ID
            LEFT JOIN TB_ProjectTreatHandler tp
        WITH (NOLOCK)
        ON tp.ID=tsh.GeneralCardTreatHandlerID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tsh.EmployeeID
        WHERE tsp.TreatBillID=#{BillID} AND tsp.PackageCardAccountID IS NOT NULL
        ORDER BY tsp.ID ASC, tp.Sequence desc
    </select>
    <select id="treatBillPackageCardTimeCardInfo" resultMap="TreatBillPackageCardTimeCard">
        SELECT tsp.ID                                  TreatBillTimeCardID,
               tsp.PackageCardAccountID,
               tsp.TimeCardAccountID,
               tgc.TimeCardID,
               tpt.Name                                TimeCardName,
               tpt.Alias,
               tsp.ProjectID,
               t.Name                                  ProjectName,
               t.Alias                                 ProjectAlias,
               tsp.Quantity,
               tsp.CardPreferentialAmount,
               tsp.TotalAmount,
               tsp.PayAmount + tsp.CardDeductionAmount Amount,
               tsp.PayAmount,
               tsp.CardDeductionAmount,
               tsp.LargessCardDeductionAmount,
               case tsp.IsLargess
                   when 0 then 0
                   else tsp.TotalAmount
                   end
                                                       LargessAmount,
               tsp.Price,
               tsp.IsLargess,
               tsh.TimeCardTreatHandlerID              TreatHandlerID,
               tp.Name                                 TreatHandlerName,
               tsh.EmployeeID,
               tee.Name                                EmployeeName,
               tsh.Scale * 100                         Scale,
               tsp.Remark
        FROM TB_TreatBillTimeCard tsp WITH(NOLOCK)
        LEFT JOIN TB_TimeCardAccount tgc
        WITH (NOLOCK)
        ON tgc.ID=tsp.TimeCardAccountID
            LEFT JOIN TB_TimeCard tpt
        WITH (NOLOCK)
        ON tgc.TimeCardID=tpt.ID
            LEFT JOIN TB_Project t
        WITH (NOLOCK)
        ON t.ID=tsp.ProjectID
            LEFT JOIN TB_TreatBillHandlerTimeCard tsh
        WITH (NOLOCK)
        ON tsh.TreatBillTimeCardID=tsp.ID
            LEFT JOIN TB_ProjectTreatHandler tp
        WITH (NOLOCK)
        ON tp.ID=tsh.TimeCardTreatHandlerID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tsh.EmployeeID
        WHERE tsp.TreatBillID=#{BillID} AND tsp.PackageCardAccountID IS NOT NULL
        ORDER BY tsp.ID ASC, tp.Sequence desc
    </select>
    <select id="treatBillPackageCardSavingCardInfo" resultMap="TreatBillPackageCardSavingCard">
        SELECT tsp.ID                       TreatBillSavingCardID,
               tsp.PackageCardAccountID,
               tsp.SavingCardAccountID,
               tgc.SavingCardID,
               tpt.Name                     SavingCardName,
               tpt.Alias,
               tsp.ProjectID,
               t.Name                       ProjectName,
               t.Alias                      ProjectAlias,
               tsp.Quantity,
               tsp.Price,
               tsp.TotalAmount,
               tsp.CardTreatAmount          Amount,
               0                            PayAmount,
               tsp.CardTreatAmount          CardDeductionAmount,
               tsp.LargessCardTreatAmount   LargessCardDeductionAmount,
               0                            LargessAmount,
               tsp.CardPreferentialAmount,
               tsp.PricePreferentialAmount,
               tsp.MemberPreferentialAmount,
               tsh.SavingCardTreatHandlerID TreatHandlerID,
               tp.Name                      TreatHandlerName,
               tsh.EmployeeID,
               tee.Name                     EmployeeName,
               tsh.Scale * 100              Scale,
               tsp.Remark
        FROM TB_TreatBillSavingCard tsp WITH(NOLOCK)
        LEFT JOIN TB_SavingCardAccount tgc
        WITH (NOLOCK)
        ON tgc.ID=tsp.SavingCardAccountID
            LEFT JOIN TB_SavingCard tpt
        WITH (NOLOCK)
        ON tgc.SavingCardID=tpt.ID
            LEFT JOIN TB_Project t
        WITH (NOLOCK)
        ON t.ID=tsp.ProjectID
            LEFT JOIN TB_TreatBillHandlerSavingCard tsh
        WITH (NOLOCK)
        ON tsh.TreatBillSavingCardID=tsp.ID
            LEFT JOIN TB_SavingCardProjectTreatHandler tp
        WITH (NOLOCK)
        ON tp.ID=tsh.SavingCardTreatHandlerID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tsh.EmployeeID
        WHERE tsp.TreatBillID=#{BillID} AND tsp.PackageCardAccountID IS NOT NULL
        ORDER BY tsp.ID ASC, tp.Sequence desc
    </select>

    <resultMap id="TreatBillProductPerformance"
               type="com.zhelian.model.ibeauty.form.output.TreatBillProductPerformanceCommissionOutputForm">
        <id column="TreatBillProductID" property="TreatBillProductID" jdbcType="INTEGER"/>
        <result column="ProductAccountID" property="ProductAccountID" jdbcType="INTEGER"/>
        <result column="PackageCardAccountID" property="PackageCardAccountID" jdbcType="INTEGER"/>
        <result column="ProductID" property="ProductID" jdbcType="INTEGER"/>
        <result column="ProductName" property="ProductName" jdbcType="VARCHAR"/>
        <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
        <result column="Price" property="Price" jdbcType="DECIMAL"/>
        <result column="CardPreferentialAmount" property="CardPreferentialAmount" jdbcType="DECIMAL"/>
        <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
        <result column="PayAmount" property="PayAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionAmount" property="CardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="LargessCardDeductionAmount" property="LargessCardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="LargessAmount" property="LargessAmount" jdbcType="DECIMAL"/>
        <result column="IsLargess" property="IsLargess" jdbcType="BIT"/>
        <collection property="TreatBillHandler"
                    ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerPerformanceCommissionOutputForm">
            <id column="TreatHandlerID" property="TreatHandlerID" jdbcType="INTEGER"/>
            <result column="TreatHandlerName" property="TreatHandlerName" jdbcType="VARCHAR"/>
            <collection property="Employee"
                        ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerEmployeePerformanceOutputForm">
                <result column="EmployeeID" property="EmployeeID" jdbcType="INTEGER"/>
                <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
                <result column="Scale" property="Scale" jdbcType="DECIMAL"/>
                <result column="PayPerformance" property="PayPerformance" jdbcType="DECIMAL"/>
                <result column="PayRateCommission" property="PayRateCommission" jdbcType="DECIMAL"/>
                <result column="PayFixedCommission" property="PayFixedCommission" jdbcType="DECIMAL"/>

                <result column="CardPerformance" property="CardPerformance" jdbcType="DECIMAL"/>
                <result column="CardRateCommission" property="CardRateCommission" jdbcType="DECIMAL"/>
                <result column="CardFixedCommission" property="CardFixedCommission" jdbcType="DECIMAL"/>

                <result column="CardLargessPerformance" property="CardLargessPerformance" jdbcType="DECIMAL"/>
                <result column="CardLargessRateCommission" property="CardLargessRateCommission" jdbcType="DECIMAL"/>
                <result column="CardLargessFixedCommission" property="CardLargessFixedCommission" jdbcType="DECIMAL"/>

                <result column="LargessPerformance" property="LargessPerformance" jdbcType="DECIMAL"/>
                <result column="LargessRateCommission" property="LargessRateCommission" jdbcType="DECIMAL"/>
                <result column="LargessFixedCommission" property="LargessFixedCommission" jdbcType="DECIMAL"/>
                <result column="SpecialBenefitCommission" property="SpecialBenefitCommission" jdbcType="DECIMAL"/>
                <result column="IsCalculatePassengerFlow" property="IsCalculatePassengerFlow" jdbcType="BOOLEAN"/>
            </collection>
        </collection>
    </resultMap>
    <resultMap id="TreatBillProjectPerformance"
               type="com.zhelian.model.ibeauty.form.output.TreatBillProjectPerformanceCommissionOutputForm">
        <id column="TreatBillProjectID" property="TreatBillProjectID" jdbcType="INTEGER"/>
        <result column="ProjectAccountID" property="ProjectAccountID" jdbcType="INTEGER"/>
        <result column="PackageCardAccountID" property="PackageCardAccountID" jdbcType="INTEGER"/>
        <result column="ProjectID" property="ProjectID" jdbcType="INTEGER"/>
        <result column="ProjectName" property="ProjectName" jdbcType="VARCHAR"/>
        <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
        <result column="Price" property="Price" jdbcType="DECIMAL"/>
        <result column="CardPreferentialAmount" property="CardPreferentialAmount" jdbcType="DECIMAL"/>
        <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
        <result column="PayAmount" property="PayAmount" jdbcType="DECIMAL"/>
        <result column="CardDeductionAmount" property="CardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="LargessCardDeductionAmount" property="LargessCardDeductionAmount" jdbcType="DECIMAL"/>
        <result column="LargessAmount" property="LargessAmount" jdbcType="DECIMAL"/>
        <result column="IsLargess" property="IsLargess" jdbcType="BIT"/>
        <collection property="TreatBillHandler"
                    ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerPerformanceCommissionOutputForm">
            <id column="TreatHandlerID" property="TreatHandlerID" jdbcType="INTEGER"/>
            <result column="TreatHandlerName" property="TreatHandlerName" jdbcType="VARCHAR"/>
            <collection property="Employee"
                        ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerEmployeePerformanceOutputForm">
                <result column="EmployeeID" property="EmployeeID" jdbcType="INTEGER"/>
                <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
                <result column="Scale" property="Scale" jdbcType="DECIMAL"/>
                <result column="PayPerformance" property="PayPerformance" jdbcType="DECIMAL"/>
                <result column="PayRateCommission" property="PayRateCommission" jdbcType="DECIMAL"/>
                <result column="PayFixedCommission" property="PayFixedCommission" jdbcType="DECIMAL"/>

                <result column="CardPerformance" property="CardPerformance" jdbcType="DECIMAL"/>
                <result column="CardRateCommission" property="CardRateCommission" jdbcType="DECIMAL"/>
                <result column="CardFixedCommission" property="CardFixedCommission" jdbcType="DECIMAL"/>

                <result column="CardLargessPerformance" property="CardLargessPerformance" jdbcType="DECIMAL"/>
                <result column="CardLargessRateCommission" property="CardLargessRateCommission" jdbcType="DECIMAL"/>
                <result column="CardLargessFixedCommission" property="CardLargessFixedCommission" jdbcType="DECIMAL"/>
                <result column="LargessPerformance" property="LargessPerformance" jdbcType="DECIMAL"/>
                <result column="LargessRateCommission" property="LargessRateCommission" jdbcType="DECIMAL"/>
                <result column="LargessFixedCommission" property="LargessFixedCommission" jdbcType="DECIMAL"/>
                <result column="SpecialBenefitCommission" property="SpecialBenefitCommission" jdbcType="DECIMAL"/>
                <result column="IsCalculatePassengerFlow" property="IsCalculatePassengerFlow" jdbcType="BOOLEAN"/>
            </collection>
        </collection>
    </resultMap>
    <resultMap id="TreatBillGeneralCardPerformance"
               type="com.zhelian.model.ibeauty.form.output.TreatBillGeneralCardPerformanceCommissionOutputForm">
        <id column="GeneralCardAccountID" property="GeneralCardAccountID" jdbcType="INTEGER"/>
        <result column="PackageCardAccountID" property="PackageCardAccountID" jdbcType="INTEGER"/>
        <result column="GeneralCardID" property="GeneralCardID" jdbcType="INTEGER"/>
        <result column="GeneralCardName" property="GeneralCardName" jdbcType="VARCHAR"/>
        <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        <result column="IsLargess" property="IsLargess" jdbcType="BIT"/>
        <collection property="Project"
                    ofType="com.zhelian.model.ibeauty.form.output.TreatBillGeneralCardConsumeProjectPerformanceCommissionOutputForm">
            <id column="TreatBillGeneralCardID" property="TreatBillGeneralCardID" jdbcType="INTEGER"/>
            <result column="ProjectID" property="ProjectID" jdbcType="INTEGER"/>
            <result column="ProjectName" property="ProjectName" jdbcType="VARCHAR"/>
            <result column="ProjectAlias" property="Alias" jdbcType="VARCHAR"/>
            <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
            <result column="Price" property="Price" jdbcType="DECIMAL"/>
            <result column="CardPreferentialAmount" property="CardPreferentialAmount" jdbcType="DECIMAL"/>
            <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
            <result column="PayAmount" property="PayAmount" jdbcType="DECIMAL"/>
            <result column="CardDeductionAmount" property="CardDeductionAmount" jdbcType="DECIMAL"/>
            <result column="LargessCardDeductionAmount" property="LargessCardDeductionAmount" jdbcType="DECIMAL"/>
            <result column="LargessAmount" property="LargessAmount" jdbcType="DECIMAL"/>
            <collection property="TreatBillHandler"
                        ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerPerformanceCommissionOutputForm">
                <id column="TreatHandlerID" property="TreatHandlerID" jdbcType="INTEGER"/>
                <result column="TreatHandlerName" property="TreatHandlerName" jdbcType="VARCHAR"/>
                <collection property="Employee"
                            ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerEmployeePerformanceOutputForm">
                    <result column="EmployeeID" property="EmployeeID" jdbcType="INTEGER"/>
                    <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
                    <result column="Scale" property="Scale" jdbcType="DECIMAL"/>
                    <result column="PayPerformance" property="PayPerformance" jdbcType="DECIMAL"/>
                    <result column="PayRateCommission" property="PayRateCommission" jdbcType="DECIMAL"/>
                    <result column="PayFixedCommission" property="PayFixedCommission" jdbcType="DECIMAL"/>

                    <result column="CardPerformance" property="CardPerformance" jdbcType="DECIMAL"/>
                    <result column="CardRateCommission" property="CardRateCommission" jdbcType="DECIMAL"/>
                    <result column="CardFixedCommission" property="CardFixedCommission" jdbcType="DECIMAL"/>

                    <result column="CardLargessPerformance" property="CardLargessPerformance" jdbcType="DECIMAL"/>
                    <result column="CardLargessRateCommission" property="CardLargessRateCommission" jdbcType="DECIMAL"/>
                    <result column="CardLargessFixedCommission" property="CardLargessFixedCommission"
                            jdbcType="DECIMAL"/>
                    <result column="LargessPerformance" property="LargessPerformance" jdbcType="DECIMAL"/>
                    <result column="LargessRateCommission" property="LargessRateCommission" jdbcType="DECIMAL"/>
                    <result column="LargessFixedCommission" property="LargessFixedCommission" jdbcType="DECIMAL"/>
                    <result column="SpecialBenefitCommission" property="SpecialBenefitCommission" jdbcType="DECIMAL"/>
                    <result column="IsCalculatePassengerFlow" property="IsCalculatePassengerFlow" jdbcType="BOOLEAN"/>
                </collection>
            </collection>
        </collection>
    </resultMap>
    <resultMap id="TreatBillTimeCardPerformance"
               type="com.zhelian.model.ibeauty.form.output.TreatBillTimeCardPerformanceCommissionOutputForm">
        <id column="TimeCardAccountID" property="TimeCardAccountID" jdbcType="INTEGER"/>
        <result column="PackageCardAccountID" property="PackageCardAccountID" jdbcType="INTEGER"/>
        <result column="TimeCardID" property="TimeCardID" jdbcType="INTEGER"/>
        <result column="TimeCardName" property="TimeCardName" jdbcType="VARCHAR"/>
        <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        <result column="IsLargess" property="IsLargess" jdbcType="BIT"/>
        <collection property="Project"
                    ofType="com.zhelian.model.ibeauty.form.output.TreatBillTimeCardConsumeProjectPerformanceCommissionOutputForm">
            <id column="TreatBillTimeCardID" property="TreatBillTimeCardID" jdbcType="INTEGER"/>
            <result column="ProjectID" property="ProjectID" jdbcType="INTEGER"/>
            <result column="ProjectName" property="ProjectName" jdbcType="VARCHAR"/>
            <result column="ProjectAlias" property="Alias" jdbcType="VARCHAR"/>
            <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
            <result column="Price" property="Price" jdbcType="DECIMAL"/>
            <result column="CardPreferentialAmount" property="CardPreferentialAmount" jdbcType="DECIMAL"/>
            <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
            <result column="PayAmount" property="PayAmount" jdbcType="DECIMAL"/>
            <result column="CardDeductionAmount" property="CardDeductionAmount" jdbcType="DECIMAL"/>
            <result column="LargessCardDeductionAmount" property="LargessCardDeductionAmount" jdbcType="DECIMAL"/>
            <result column="LargessAmount" property="LargessAmount" jdbcType="DECIMAL"/>
            <collection property="TreatBillHandler"
                        ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerPerformanceCommissionOutputForm">
                <id column="TreatHandlerID" property="TreatHandlerID" jdbcType="INTEGER"/>
                <result column="TreatHandlerName" property="TreatHandlerName" jdbcType="VARCHAR"/>
                <collection property="Employee"
                            ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerEmployeePerformanceOutputForm">
                    <result column="EmployeeID" property="EmployeeID" jdbcType="INTEGER"/>
                    <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
                    <result column="Scale" property="Scale" jdbcType="DECIMAL"/>
                    <result column="PayPerformance" property="PayPerformance" jdbcType="DECIMAL"/>
                    <result column="PayRateCommission" property="PayRateCommission" jdbcType="DECIMAL"/>
                    <result column="PayFixedCommission" property="PayFixedCommission" jdbcType="DECIMAL"/>

                    <result column="CardPerformance" property="CardPerformance" jdbcType="DECIMAL"/>
                    <result column="CardRateCommission" property="CardRateCommission" jdbcType="DECIMAL"/>
                    <result column="CardFixedCommission" property="CardFixedCommission" jdbcType="DECIMAL"/>

                    <result column="CardLargessPerformance" property="CardLargessPerformance" jdbcType="DECIMAL"/>
                    <result column="CardLargessRateCommission" property="CardLargessRateCommission" jdbcType="DECIMAL"/>
                    <result column="CardLargessFixedCommission" property="CardLargessFixedCommission"
                            jdbcType="DECIMAL"/>
                    <result column="LargessPerformance" property="LargessPerformance" jdbcType="DECIMAL"/>
                    <result column="LargessRateCommission" property="LargessRateCommission" jdbcType="DECIMAL"/>
                    <result column="LargessFixedCommission" property="LargessFixedCommission" jdbcType="DECIMAL"/>
                    <result column="SpecialBenefitCommission" property="SpecialBenefitCommission" jdbcType="DECIMAL"/>
                    <result column="IsCalculatePassengerFlow" property="IsCalculatePassengerFlow" jdbcType="BOOLEAN"/>
                </collection>
            </collection>
        </collection>
    </resultMap>
    <resultMap id="TreatBillSavingCardPerformance"
               type="com.zhelian.model.ibeauty.form.output.TreatBillSavingCardPerformanceCommissionOutputForm">
        <id column="SavingCardAccountID" property="SavingCardAccountID" jdbcType="INTEGER"/>
        <result column="PackageCardAccountID" property="PackageCardAccountID" jdbcType="INTEGER"/>
        <result column="SavingCardID" property="SavingCardID" jdbcType="INTEGER"/>
        <result column="SavingCardName" property="SavingCardName" jdbcType="VARCHAR"/>
        <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        <collection property="Project"
                    ofType="com.zhelian.model.ibeauty.form.output.TreatBillSavingCardConsumeProjectPerformanceCommissionOutputForm">
            <id column="TreatBillSavingCardID" property="TreatBillSavingCardID" jdbcType="INTEGER"/>
            <result column="ProjectID" property="ProjectID" jdbcType="INTEGER"/>
            <result column="ProjectName" property="ProjectName" jdbcType="VARCHAR"/>
            <result column="ProjectAlias" property="Alias" jdbcType="VARCHAR"/>
            <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
            <result column="Price" property="Price" jdbcType="DECIMAL"/>
            <result column="CardDeductionAmount" property="CardDeductionAmount" jdbcType="DECIMAL"/>
            <result column="LargessCardDeductionAmount" property="LargessCardDeductionAmount" jdbcType="DECIMAL"/>
            <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
            <result column="PricePreferentialAmount" property="PricePreferentialAmount" jdbcType="DECIMAL"/>
            <result column="MemberPreferentialAmount" property="MemberPreferentialAmount" jdbcType="DECIMAL"/>
            <result column="CardPreferentialAmount" property="CardPreferentialAmount" jdbcType="DECIMAL"/>
            <collection property="TreatBillHandler"
                        ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerPerformanceCommissionOutputForm">
                <id column="TreatHandlerID" property="TreatHandlerID" jdbcType="INTEGER"/>
                <result column="TreatHandlerName" property="TreatHandlerName" jdbcType="VARCHAR"/>
                <collection property="Employee"
                            ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerEmployeePerformanceOutputForm">
                    <result column="EmployeeID" property="EmployeeID" jdbcType="INTEGER"/>
                    <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
                    <result column="Scale" property="Scale" jdbcType="DECIMAL"/>
                    <result column="CardPerformance" property="CardPerformance" jdbcType="DECIMAL"/>
                    <result column="CardRateCommission" property="CardRateCommission" jdbcType="DECIMAL"/>
                    <result column="CardFixedCommission" property="CardFixedCommission" jdbcType="DECIMAL"/>

                    <result column="CardLargessPerformance" property="CardLargessPerformance" jdbcType="DECIMAL"/>
                    <result column="CardLargessRateCommission" property="CardLargessRateCommission" jdbcType="DECIMAL"/>
                    <result column="CardLargessFixedCommission" property="CardLargessFixedCommission"
                            jdbcType="DECIMAL"/>
                    <result column="SpecialBenefitCommission" property="SpecialBenefitCommission" jdbcType="DECIMAL"/>
                    <result column="IsCalculatePassengerFlow" property="IsCalculatePassengerFlow" jdbcType="BOOLEAN"/>
                </collection>
            </collection>
        </collection>
    </resultMap>

    <select id="getTreatBillProductPerformanceCommission" resultMap="TreatBillProductPerformance">
        SELECT tsp.ID                       TreatBillProductID,
               tsp.ProductAccountID,
               tsp.ProductID,
               te.Name                      ProductName,
               te.Alias,
               tsp.Quantity,
               tsp.Price,
               tsp.CardPreferentialAmount,
               tsp.TotalAmount,
               tsp.PayAmount,
               tsp.CardDeductionAmount,
               tsp.LargessCardDeductionAmount,
               case tsp.IsLargess
                   when 0 then 0
                   else tsp.TotalAmount end LargessAmount,
               tsp.IsLargess,
               tsh.ProductTreatHandlerID    TreatHandlerID,
               tp.Name                      TreatHandlerName,
               tsh.EmployeeID,
               tsh.IsCalculatePassengerFlow,
               tee.Name                     EmployeeName,
               tsh.Scale * 100              Scale,
               ttp.PayPerformance,
               ttp.PayRateCommission,
               ttp.PayFixedCommission,
               ttp.CardPerformance,
               ttp.CardRateCommission,
               ttp.CardFixedCommission,
               ttp.CardLargessPerformance,
               ttp.CardLargessRateCommission,
               ttp.CardLargessFixedCommission,
               ttp.LargessPerformance,
               ttp.LargessRateCommission,
               ttp.LargessFixedCommission,
               ttp.SpecialBenefitCommission

        FROM TB_TreatBillProduct tsp WITH(NOLOCK)
            LEFT JOIN  TE_Product te
        WITH (NOLOCK)
        ON te.ID=tsp.ProductID
            LEFT JOIN TB_TreatBillHandlerProduct tsh
        WITH (NOLOCK)
        ON tsh.TreatBillProductID=tsp.ID
            LEFT JOIN TB_TreatEntityPerformanceProduct tte
        WITH (NOLOCK)
        ON tsp.TreatBillID=tte.TreatBillID AND tsp.ID=tte.TreatBillProductID
            AND tsp.ProductID=tte.ProductID
            LEFT JOIN TB_TreatEmployeePerformanceCommissionProduct ttp
        WITH (NOLOCK)
        ON ttp.TreatBillProductID=tsh.TreatBillProductID
            AND ttp.ProductTreatHandlerID=tsh.ProductTreatHandlerID AND ttp.EmployeeID=tsh.EmployeeID
            LEFT JOIN TB_ProductTreatHandler tp
        WITH (NOLOCK)
        ON tp.ID=tsh.ProductTreatHandlerID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tsh.EmployeeID
        WHERE tsp.TreatBillID=#{BillID} AND tsp.PackageCardAccountID IS NULL
        ORDER BY tsp.ID ASC, tp.Sequence desc
    </select>
    <select id="getTreatBillProjectPerformanceCommission" resultMap="TreatBillProjectPerformance">
        SELECT tsp.ID                       TreatBillProjectID,
               tsp.ProjectAccountID,
               tsp.ProjectID,
               te.Name                      ProjectName,
               te.Alias,
               tsp.Quantity,
               tsp.Price,
               tsp.CardPreferentialAmount,
               tsp.TotalAmount,
               tsp.PayAmount,
               tsp.CardDeductionAmount,
               tsp.LargessCardDeductionAmount,
               case tsp.IsLargess
                   when 0 then 0
                   else tsp.TotalAmount end LargessAmount,
               tsp.IsLargess,
               tsh.ProjectTreatHandlerID    TreatHandlerID,
               tp.Name                      TreatHandlerName,
               tsh.EmployeeID,
               tsh.IsCalculatePassengerFlow,
               tee.Name                     EmployeeName,
               tsh.Scale * 100              Scale,
               ttp.PayPerformance,
               ttp.PayRateCommission,
               ttp.PayFixedCommission,
               ttp.CardPerformance,
               ttp.CardRateCommission,
               ttp.CardFixedCommission,
               ttp.CardLargessPerformance,
               ttp.CardLargessRateCommission,
               ttp.CardLargessFixedCommission,
               ttp.LargessPerformance,
               ttp.LargessRateCommission,
               ttp.LargessFixedCommission,
               ttp.SpecialBenefitCommission
        FROM TB_TreatBillProject tsp WITH(NOLOCK)
            LEFT JOIN  TB_Project te
        WITH (NOLOCK)
        ON te.ID=tsp.ProjectID
            LEFT JOIN TB_TreatBillHandlerProject tsh
        WITH (NOLOCK)
        ON tsh.TreatBillProjectID=tsp.ID
            LEFT JOIN TB_TreatEntityPerformanceProject tte
        WITH (NOLOCK)
        ON tsp.TreatBillID=tte.TreatBillID AND tsp.ID=tte.TreatBillProjectID
            AND tsp.ProjectID=tte.ProjectID
            LEFT JOIN TB_TreatEmployeePerformanceCommissionProject ttp
        WITH (NOLOCK)
        ON ttp.TreatBillProjectID=tsh.TreatBillProjectID
            AND ttp.ProjectTreatHandlerID=tsh.ProjectTreatHandlerID AND ttp.EmployeeID=tsh.EmployeeID
            LEFT JOIN TB_ProjectTreatHandler tp
        WITH (NOLOCK)
        ON tp.ID=tsh.ProjectTreatHandlerID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tsh.EmployeeID
        WHERE tsp.TreatBillID=#{BillID} AND tsp.PackageCardAccountID IS NULL
        ORDER BY tsp.ID ASC, tp.Sequence desc
    </select>
    <select id="getTreatBillGeneralCardPerformanceCommission" resultMap="TreatBillGeneralCardPerformance">
        SELECT tsp.ID                        TreatBillGeneralCardID,
               tsp.GeneralCardAccountID,
               tpt.GeneralCardID,
               te.Name                       GeneralCardName,
               te.Alias,
               tsp.ProjectID,
               tj.Name                       ProjectName,
               tj.Alias                      ProjectAlias,
               tsp.Quantity,
               tsp.Price,
               tsp.CardPreferentialAmount,
               tsp.TotalAmount,
               tsp.PayAmount,
               tsp.CardDeductionAmount,
               tsp.LargessCardDeductionAmount,
               case tsp.IsLargess
                   when 0 then 0
                   else tsp.TotalAmount end  LargessAmount,
               tsp.IsLargess,
               tsh.GeneralCardTreatHandlerID TreatHandlerID,
               tp.Name                       TreatHandlerName,
               tsh.EmployeeID,
               tsh.IsCalculatePassengerFlow,
               tee.Name                      EmployeeName,
               tsh.Scale * 100               Scale,
               ttp.PayPerformance,
               ttp.PayRateCommission,
               ttp.PayFixedCommission,
               ttp.CardPerformance,
               ttp.CardRateCommission,
               ttp.CardFixedCommission,
               ttp.CardLargessPerformance,
               ttp.CardLargessRateCommission,
               ttp.CardLargessFixedCommission,
               ttp.LargessPerformance,
               ttp.LargessRateCommission,
               ttp.LargessFixedCommission,
               ttp.SpecialBenefitCommission
        FROM TB_TreatBillGeneralCard tsp WITH(NOLOCK)
            LEFT JOIN TB_GeneralCardAccount tpt
        WITH (NOLOCK)
        ON tpt.ID=tsp.GeneralCardAccountID
            LEFT JOIN TB_GeneralCard te
        WITH (NOLOCK)
        ON te.ID=tpt.GeneralCardID
            LEFT JOIN TB_Project tj
        WITH (NOLOCK)
        on tj.ID=tsp.ProjectID
            LEFT JOIN TB_TreatBillHandlerGeneralCard tsh
        WITH (NOLOCK)
        ON tsh.TreatBillGeneralCardID=tsp.ID
            LEFT JOIN TB_TreatEntityPerformanceGeneralCard tte
        WITH (NOLOCK)
        ON tsp.TreatBillID=tte.TreatBillID AND tsp.ID=tte.TreatBillGeneralCardID
            AND tsp.ProjectID=tte.ProjectID
            LEFT JOIN TB_TreatEmployeePerformanceCommissionGeneralCard ttp
        WITH (NOLOCK)
        on ttp.TreatBillGeneralCardID=tsh.TreatBillGeneralCardID
            and ttp.GeneralCardTreatHandlerID=tsh.GeneralCardTreatHandlerID and ttp.EmployeeID=tsh.EmployeeID
            LEFT JOIN TB_ProjectTreatHandler tp
        WITH (NOLOCK)
        ON tp.ID=tsh.GeneralCardTreatHandlerID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tsh.EmployeeID
        WHERE tsp.TreatBillID=#{BillID} AND tsp.PackageCardAccountID IS NULL
        ORDER BY tsp.ID ASC, tp.Sequence desc
    </select>
    <select id="getTreatBillTimeCardPerformanceCommission" resultMap="TreatBillTimeCardPerformance">
        SELECT tsp.ID                       TreatBillTimeCardID,
               tsp.TimeCardAccountID,
               tpt.TimeCardID,
               te.Name                      TimeCardName,
               te.Alias,
               tsp.ProjectID,
               tj.Name                      ProjectName,
               tj.Alias                     ProjectAlias,
               tsp.Quantity,
               tsp.Price,
               tsp.CardPreferentialAmount,
               tsp.TotalAmount,
               tsp.PayAmount,
               tsp.CardDeductionAmount,
               tsp.LargessCardDeductionAmount,
               case tsp.IsLargess
                   when 0 then 0
                   else tsp.TotalAmount end LargessAmount,
               tsp.IsLargess,
               tsh.TimeCardTreatHandlerID   TreatHandlerID,
               tp.Name                      TreatHandlerName,
               tsh.EmployeeID,
               tsh.IsCalculatePassengerFlow,
               tee.Name                     EmployeeName,
               tsh.Scale * 100              Scale,
               ttp.PayPerformance,
               ttp.PayRateCommission,
               ttp.PayFixedCommission,
               ttp.CardPerformance,
               ttp.CardRateCommission,
               ttp.CardFixedCommission,
               ttp.CardLargessPerformance,
               ttp.CardLargessRateCommission,
               ttp.CardLargessFixedCommission,
               ttp.LargessPerformance,
               ttp.LargessRateCommission,
               ttp.LargessFixedCommission,
               ttp.SpecialBenefitCommission
        FROM TB_TreatBillTimeCard tsp WITH(NOLOCK)
            LEFT JOIN TB_TimeCardAccount tpt
        WITH (NOLOCK)
        ON tpt.ID=tsp.TimeCardAccountID
            LEFT JOIN TB_TimeCard te
        WITH (NOLOCK)
        ON te.ID=tpt.TimeCardID
            LEFT JOIN TB_Project tj
        WITH (NOLOCK)
        on tj.ID=tsp.ProjectID
            LEFT JOIN TB_TreatBillHandlerTimeCard tsh
        WITH (NOLOCK)
        ON tsh.TreatBillTimeCardID=tsp.ID
            LEFT JOIN TB_TreatEntityPerformanceTimeCard tte
        WITH (NOLOCK)
        ON tsp.TreatBillID=tte.TreatBillID AND tsp.ID=tte.TreatBillTimeCardID
            AND tsp.ProjectID=tte.ProjectID
            LEFT JOIN TB_TreatEmployeePerformanceCommissionTimeCard ttp
        WITH (NOLOCK)
        ON ttp.TreatBillTimeCardID=tsh.TreatBillTimeCardID
            AND ttp.TimeCardTreatHandlerID=tsh.TimeCardTreatHandlerID AND ttp.EmployeeID=tsh.EmployeeID
            LEFT JOIN TB_ProjectTreatHandler tp
        WITH (NOLOCK)
        ON tp.ID=tsh.TimeCardTreatHandlerID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tsh.EmployeeID
        WHERE tsp.TreatBillID=#{BillID} AND tsp.PackageCardAccountID IS NULL
        ORDER BY tsp.ID ASC, tp.Sequence desc
    </select>
    <select id="getTreatBillSavingCardPerformanceCommission" resultMap="TreatBillSavingCardPerformance">
        SELECT tsp.ID                       TreatBillSavingCardID,
               tsp.SavingCardAccountID,
               tpt.SavingCardID,
               te.Name                      SavingCardName,
               te.Alias,
               tsp.ProjectID,
               tj.Name                      ProjectName,
               tj.Alias                     ProjectAlias,
               tsp.Quantity,
               tsp.Price,
               tsp.CardTreatAmount          CardDeductionAmount,
               tsp.LargessCardTreatAmount   LargessCardDeductionAmount,
               tsp.TotalAmount,
               tsp.PricePreferentialAmount,
               tsp.MemberPreferentialAmount,
               tsp.CardPreferentialAmount,
               tsh.SavingCardTreatHandlerID TreatHandlerID,
               tp.Name                      TreatHandlerName,
               tsh.EmployeeID,
               tsh.IsCalculatePassengerFlow,
               tee.Name                     EmployeeName,
               tsh.Scale * 100              Scale,
               ttp.CardPerformance,
               ttp.CardRateCommission,
               ttp.CardFixedCommission,
               ttp.CardLargessPerformance,
               ttp.CardLargessRateCommission,
               ttp.CardLargessFixedCommission,
               ttp.SpecialBenefitCommission
        FROM TB_TreatBillSavingCard tsp WITH(NOLOCK)
            LEFT JOIN TB_SavingCardAccount tpt
        WITH (NOLOCK)
        ON tpt.ID=tsp.SavingCardAccountID
            LEFT JOIN TB_SavingCard te
        WITH (NOLOCK)
        ON te.ID=tpt.SavingCardID
            LEFT JOIN TB_Project tj
        WITH (NOLOCK)
        on tj.ID=tsp.ProjectID
            LEFT JOIN TB_TreatBillHandlerSavingCard tsh
        WITH (NOLOCK)
        ON tsh.TreatBillSavingCardID=tsp.ID
            LEFT JOIN TB_TreatEntityPerformanceSavingCard tte
        WITH (NOLOCK)
        ON tsp.TreatBillID=tte.TreatBillID AND tsp.ID=tte.TreatBillSavingCardID
            AND tsp.ProjectID=tte.ProjectID
            LEFT JOIN TB_TreatEmployeePerformanceCommissionSavingCard ttp
        WITH (NOLOCK)
        ON ttp.TreatBillSavingCardID=tsh.TreatBillSavingCardID
            AND ttp.SavingCardTreatHandlerID=tsh.SavingCardTreatHandlerID AND ttp.EmployeeID=tsh.EmployeeID
            LEFT JOIN TB_SavingCardProjectTreatHandler tp
        WITH (NOLOCK)
        ON tp.ID=tsh.SavingCardTreatHandlerID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tsh.EmployeeID
        WHERE tsp.TreatBillID=#{BillID} AND tsp.PackageCardAccountID IS NULL
        ORDER BY tsp.ID ASC, tp.Sequence desc
    </select>

    <select id="getTreatBillPackageCardPerformanceCommission"
            resultType="com.zhelian.model.ibeauty.form.output.TreatBillPackageCardPerformanceCommissionOutputForm">
        SELECT T.PackageCardAccountID,
               T.PackageCardID,
               tp.Name PackageCardName,
               tp.Alias
        FROM (SELECT PackageCardAccountID, PackageCardID
              FROM TB_TreatBillProduct WITH(NOLOCK)
              WHERE TreatBillID=#{BillID} AND PackageCardAccountID IS NOT NULL
              UNION
              SELECT PackageCardAccountID, PackageCardID
              FROM TB_TreatBillProject
              WITH (NOLOCK)
              WHERE TreatBillID=#{BillID} AND PackageCardAccountID IS NOT NULL
              UNION
              SELECT PackageCardAccountID, PackageCardID
              FROM TB_TreatBillGeneralCard
              WITH (NOLOCK)
              WHERE TreatBillID=#{BillID} AND PackageCardAccountID IS NOT NULL
              UNION
              SELECT PackageCardAccountID, PackageCardID
              FROM TB_TreatBillTimeCard
              WITH (NOLOCK)
              WHERE TreatBillID=#{BillID} AND PackageCardAccountID IS NOT NULL
              UNION
              SELECT PackageCardAccountID, PackageCardID
              FROM TB_TreatBillSavingCard
              WITH (NOLOCK)
              WHERE TreatBillID=#{BillID} AND PackageCardAccountID IS NOT NULL) T
                 LEFT JOIN TB_PackageCard tp on T.PackageCardID = tp.ID
    </select>
    <select id="getTreatBillPackageCardProductPerformanceCommission" resultMap="TreatBillProductPerformance">
        SELECT tsp.ID                       TreatBillProductID,
               tsp.ProductAccountID,
               tsp.PackageCardAccountID,
               tsp.ProductID,
               te.Name                      ProductName,
               te.Alias,
               tsp.Quantity,
               tsp.Price,
               tsp.CardPreferentialAmount,
               tsp.TotalAmount,
               tsp.PayAmount,
               tsp.CardDeductionAmount,
               tsp.LargessCardDeductionAmount,
               case tsp.IsLargess
                   when 0 then 0
                   else tsp.TotalAmount end LargessAmount,
               tsp.IsLargess,
               tsh.IsCalculatePassengerFlow,
               tsh.ProductTreatHandlerID    TreatHandlerID,
               tp.Name                      TreatHandlerName,
               tsh.EmployeeID,
               tee.Name                     EmployeeName,
               tsh.Scale * 100              Scale,
               ttp.PayPerformance,
               ttp.PayRateCommission,
               ttp.PayFixedCommission,
               ttp.CardPerformance,
               ttp.CardRateCommission,
               ttp.CardFixedCommission,
               ttp.CardLargessPerformance,
               ttp.CardLargessRateCommission,
               ttp.CardLargessFixedCommission,
               ttp.LargessPerformance,
               ttp.LargessRateCommission,
               ttp.LargessFixedCommission,
               ttp.SpecialBenefitCommission
        FROM TB_TreatBillProduct tsp WITH(NOLOCK)
        LEFT JOIN  TE_Product te
        WITH (NOLOCK)
        ON te.ID=tsp.ProductID
            LEFT JOIN TB_TreatBillHandlerProduct tsh
        WITH (NOLOCK)
        ON tsh.TreatBillProductID=tsp.ID
            LEFT JOIN TB_TreatEmployeePerformanceCommissionPackageCardProduct ttp
        WITH (NOLOCK)
        ON ttp.TreatBillProductID=tsh.TreatBillProductID
            AND ttp.ProductTreatHandlerID=tsh.ProductTreatHandlerID AND ttp.EmployeeID=tsh.EmployeeID
            LEFT JOIN TB_ProductTreatHandler tp
        WITH (NOLOCK)
        ON tp.ID=tsh.ProductTreatHandlerID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tsh.EmployeeID
        WHERE tsp.TreatBillID=#{BillID} AND tsp.PackageCardAccountID IS NOT NULL
        ORDER BY tsp.ID ASC, tp.Sequence desc
    </select>
    <select id="getTreatBillPackageCardProjectPerformanceCommission" resultMap="TreatBillProjectPerformance">
        SELECT tsp.ID                       TreatBillProjectID,
               tsp.ProjectAccountID,
               tsp.PackageCardAccountID,
               tsp.ProjectID,
               te.Name                      ProjectName,
               te.Alias,
               tsp.Quantity,
               tsp.Price,
               tsp.CardPreferentialAmount,
               tsp.TotalAmount,
               tsp.PayAmount,
               tsp.CardDeductionAmount,
               tsp.LargessCardDeductionAmount,
               case tsp.IsLargess
                   when 0 then 0
                   else tsp.TotalAmount end LargessAmount,
               tsp.IsLargess,
               tsh.IsCalculatePassengerFlow,
               tsh.ProjectTreatHandlerID    TreatHandlerID,
               tp.Name                      TreatHandlerName,
               tsh.EmployeeID,
               tee.Name                     EmployeeName,
               tsh.Scale * 100              Scale,
               ttp.PayPerformance,
               ttp.PayRateCommission,
               ttp.PayFixedCommission,
               ttp.CardPerformance,
               ttp.CardRateCommission,
               ttp.CardFixedCommission,
               ttp.CardLargessPerformance,
               ttp.CardLargessRateCommission,
               ttp.CardLargessFixedCommission,
               ttp.LargessPerformance,
               ttp.LargessRateCommission,
               ttp.LargessFixedCommission,
               ttp.SpecialBenefitCommission
        FROM TB_TreatBillProject tsp WITH(NOLOCK)
        LEFT JOIN  TB_Project te
        WITH (NOLOCK)
        ON te.ID=tsp.ProjectID
            LEFT JOIN TB_TreatBillHandlerProject tsh
        WITH (NOLOCK)
        ON tsh.TreatBillProjectID=tsp.ID
            LEFT JOIN TB_TreatEmployeePerformanceCommissionPackageCardProject ttp
        WITH (NOLOCK)
        ON ttp.TreatBillProjectID=tsh.TreatBillProjectID
            AND ttp.ProjectTreatHandlerID=tsh.ProjectTreatHandlerID AND ttp.EmployeeID=tsh.EmployeeID
            LEFT JOIN TB_ProjectTreatHandler tp
        WITH (NOLOCK)
        ON tp.ID=tsh.ProjectTreatHandlerID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tsh.EmployeeID
        WHERE tsp.TreatBillID=#{BillID} AND tsp.PackageCardAccountID IS NOT NULL
        ORDER BY tsp.ID ASC, tp.Sequence desc
    </select>
    <select id="getTreatBillPackageCardGeneralCardPerformanceCommission" resultMap="TreatBillGeneralCardPerformance">
        SELECT tsp.ID                        TreatBillGeneralCardID,
               tsp.GeneralCardAccountID,
               tsp.PackageCardAccountID,
               tpt.GeneralCardID,
               te.Name                       GeneralCardName,
               te.Alias,
               tsp.ProjectID,
               tj.Name                       ProjectName,
               tj.Alias                      ProjectAlias,
               tsp.Quantity,
               tsp.Price,
               tsp.CardPreferentialAmount,
               tsp.TotalAmount,
               tsp.PayAmount,
               tsp.CardDeductionAmount,
               tsp.LargessCardDeductionAmount,
               case tsp.IsLargess
                   when 0 then 0
                   else tsp.TotalAmount end  LargessAmount,
               tsp.IsLargess,
               tsh.IsCalculatePassengerFlow,
               tsh.GeneralCardTreatHandlerID TreatHandlerID,
               tp.Name                       TreatHandlerName,
               tsh.EmployeeID,
               tee.Name                      EmployeeName,
               tsh.Scale * 100               Scale,
               ttp.PayPerformance,
               ttp.PayRateCommission,
               ttp.PayFixedCommission,
               ttp.CardPerformance,
               ttp.CardRateCommission,
               ttp.CardFixedCommission,
               ttp.CardLargessPerformance,
               ttp.CardLargessRateCommission,
               ttp.CardLargessFixedCommission,
               ttp.LargessPerformance,
               ttp.LargessRateCommission,
               ttp.LargessFixedCommission,
               ttp.SpecialBenefitCommission
        FROM TB_TreatBillGeneralCard tsp WITH(NOLOCK)
        LEFT JOIN TB_GeneralCardAccount tpt
        WITH (NOLOCK)
        ON tpt.ID=tsp.GeneralCardAccountID
            LEFT JOIN TB_GeneralCard te
        WITH (NOLOCK)
        ON te.ID=tpt.GeneralCardID
            LEFT JOIN TB_Project tj
        WITH (NOLOCK)
        on tj.ID=tsp.ProjectID
            LEFT JOIN TB_TreatBillHandlerGeneralCard tsh
        WITH (NOLOCK)
        ON tsh.TreatBillGeneralCardID=tsp.ID
            LEFT JOIN TB_TreatEmployeePerformanceCommissionPackageCardGeneralCard ttp
        WITH (NOLOCK)
        on ttp.TreatBillGeneralCardID=tsh.TreatBillGeneralCardID
            and ttp.GeneralCardTreatHandlerID=tsh.GeneralCardTreatHandlerID and ttp.EmployeeID=tsh.EmployeeID
            LEFT JOIN TB_ProjectTreatHandler tp
        WITH (NOLOCK)
        ON tp.ID=tsh.GeneralCardTreatHandlerID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tsh.EmployeeID
        WHERE tsp.TreatBillID=#{BillID} AND tsp.PackageCardAccountID IS NOT NULL
        ORDER BY tsp.ID ASC, tp.Sequence desc
    </select>
    <select id="getTreatBillPackageCardTimeCardPerformanceCommission" resultMap="TreatBillTimeCardPerformance">
        SELECT tsp.ID                       TreatBillTimeCardID,
               tsp.TimeCardAccountID,
               tsp.PackageCardAccountID,
               tpt.TimeCardID,
               te.Name                      TimeCardName,
               te.Alias,
               tsp.ProjectID,
               tj.Name                      ProjectName,
               tj.Alias                     ProjectAlias,
               tsp.Quantity,
               tsp.Price,
               tsp.CardPreferentialAmount,
               tsp.TotalAmount,
               tsp.PayAmount,
               tsp.CardDeductionAmount,
               tsp.LargessCardDeductionAmount,
               case tsp.IsLargess
                   when 0 then 0
                   else tsp.TotalAmount end LargessAmount,
               tsp.IsLargess,
               tsh.IsCalculatePassengerFlow,
               tsh.TimeCardTreatHandlerID   TreatHandlerID,
               tp.Name                      TreatHandlerName,
               tsh.EmployeeID,
               tee.Name                     EmployeeName,
               tsh.Scale * 100              Scale,
               ttp.PayPerformance,
               ttp.PayRateCommission,
               ttp.PayFixedCommission,
               ttp.CardPerformance,
               ttp.CardRateCommission,
               ttp.CardFixedCommission,
               ttp.CardLargessPerformance,
               ttp.CardLargessRateCommission,
               ttp.CardLargessFixedCommission,
               ttp.LargessPerformance,
               ttp.LargessRateCommission,
               ttp.LargessFixedCommission,
               ttp.SpecialBenefitCommission
        FROM TB_TreatBillTimeCard tsp WITH(NOLOCK)
        LEFT JOIN TB_TimeCardAccount tpt
        WITH (NOLOCK)
        ON tpt.ID=tsp.TimeCardAccountID
            LEFT JOIN TB_TimeCard te
        WITH (NOLOCK)
        ON te.ID=tpt.TimeCardID
            LEFT JOIN TB_Project tj
        WITH (NOLOCK)
        on tj.ID=tsp.ProjectID
            LEFT JOIN TB_TreatBillHandlerTimeCard tsh
        WITH (NOLOCK)
        ON tsh.TreatBillTimeCardID=tsp.ID
            LEFT JOIN TB_TreatEmployeePerformanceCommissionPackageCardTimeCard ttp
        WITH (NOLOCK)
        ON ttp.TreatBillTimeCardID=tsh.TreatBillTimeCardID
            AND ttp.TimeCardTreatHandlerID=tsh.TimeCardTreatHandlerID AND ttp.EmployeeID=tsh.EmployeeID
            LEFT JOIN TB_ProjectTreatHandler tp
        WITH (NOLOCK)
        ON tp.ID=tsh.TimeCardTreatHandlerID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tsh.EmployeeID
        WHERE tsp.TreatBillID=#{BillID} AND tsp.PackageCardAccountID IS NOT NULL
        ORDER BY tsp.ID ASC, tp.Sequence desc
    </select>
    <select id="getTreatBillPackageCardSavingCardPerformanceCommission" resultMap="TreatBillSavingCardPerformance">
        SELECT tsp.ID                       TreatBillSavingCardID,
               tsp.SavingCardAccountID,
               tsp.PackageCardAccountID,
               tpt.SavingCardID,
               te.Name                      SavingCardName,
               te.Alias,
               tsp.ProjectID,
               tj.Name                      ProjectName,
               tj.Alias                     ProjectAlias,
               tsp.Quantity,
               tsp.Price,
               tsp.CardTreatAmount          CardDeductionAmount,
               tsp.LargessCardTreatAmount   LargessCardDeductionAmount,
               tsp.TotalAmount,
               tsp.PricePreferentialAmount,
               tsp.MemberPreferentialAmount,
               tsp.CardPreferentialAmount,
               tsh.IsCalculatePassengerFlow,
               tsh.SavingCardTreatHandlerID TreatHandlerID,
               tp.Name                      TreatHandlerName,
               tsh.EmployeeID,
               tee.Name                     EmployeeName,
               tsh.Scale * 100              Scale,
               ttp.CardPerformance,
               ttp.CardRateCommission,
               ttp.CardFixedCommission,
               ttp.CardLargessPerformance,
               ttp.CardLargessRateCommission,
               ttp.CardLargessFixedCommission,
               ttp.SpecialBenefitCommission
        FROM TB_TreatBillSavingCard tsp WITH(NOLOCK)
        LEFT JOIN TB_SavingCardAccount tpt
        WITH (NOLOCK)
        ON tpt.ID=tsp.SavingCardAccountID
            LEFT JOIN TB_SavingCard te
        WITH (NOLOCK)
        ON te.ID=tpt.SavingCardID
            LEFT JOIN TB_Project tj
        WITH (NOLOCK)
        on tj.ID=tsp.ProjectID
            LEFT JOIN TB_TreatBillHandlerSavingCard tsh
        WITH (NOLOCK)
        ON tsh.TreatBillSavingCardID=tsp.ID
            LEFT JOIN TB_TreatEmployeePerformanceCommissionPackageCardSavingCard ttp
        WITH (NOLOCK)
        ON ttp.TreatBillSavingCardID=tsh.TreatBillSavingCardID
            AND ttp.SavingCardTreatHandlerID=tsh.SavingCardTreatHandlerID AND ttp.EmployeeID=tsh.EmployeeID
            LEFT JOIN TB_SavingCardProjectTreatHandler tp
        WITH (NOLOCK)
        ON tp.ID=tsh.SavingCardTreatHandlerID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tsh.EmployeeID
        WHERE tsp.TreatBillID=#{BillID} AND tsp.PackageCardAccountID IS NOT NULL
        ORDER BY tsp.ID ASC, tp.Sequence desc
    </select>

    <select id="deleteProductHandler">
        DELETE
        TB_TreatBillHandlerProduct  FROM   TB_TreatBillHandlerProduct tsb
        INNER JOIN TB_TreatBillProduct  ts  ON  ts.ID=tsb.TreatBillProductID
        WHERE ts.TreatBillID=
        #{ID}
    </select>
    <select id="deleteProjectHandler">
        DELETE
        TB_TreatBillHandlerProject  FROM   TB_TreatBillHandlerProject tsb
        INNER JOIN TB_TreatBillProject  ts  ON  ts.ID=tsb.TreatBillProjectID
        WHERE ts.TreatBillID=
        #{ID}
    </select>
    <select id="deleteGeneralCardHandler">
        DELETE
        TB_TreatBillHandlerGeneralCard  FROM   TB_TreatBillHandlerGeneralCard tsb
        INNER JOIN TB_TreatBillGeneralCard  ts  ON  ts.ID=tsb.TreatBillGeneralCardID
        WHERE ts.TreatBillID=
        #{ID}
    </select>
    <select id="deleteTimeCardHandler">
        DELETE
        TB_TreatBillHandlerTimeCard  FROM   TB_TreatBillHandlerTimeCard tsb
        INNER JOIN TB_TreatBillTimeCard  ts  ON  ts.ID=tsb.TreatBillTimeCardID
        WHERE ts.TreatBillID=
        #{ID}
    </select>
    <select id="deleteSavingCardHandler">
        DELETE
        TB_TreatBillHandlerSavingCard  FROM   TB_TreatBillHandlerSavingCard tsb
        INNER JOIN TB_TreatBillSavingCard  ts  ON  ts.ID=tsb.TreatBillSavingCardID
        WHERE ts.TreatBillID=
        #{ID}
    </select>

    <select id="getTreatBill" resultType="com.zhelian.model.ibeauty.entity.TreatBillEntity">
        SELECT *
        FROM TB_TreatBill tsb WITH(NOLOCK)
        WHERE tsb.ID=#{ID}
    </select>

    <insert id="createTreatBillHandlerProductList" parameterType="java.util.List">
        INSERT INTO dbo.TB_TreatBillHandlerProduct ( TreatBillProductID, ProductTreatHandlerID, EmployeeID,
        Scale,IsCalculatePassengerFlow )
        VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.TreatBillProductID},#{item.ProductTreatHandlerID},#{item.EmployeeID},#{item.Scale},#{item.IsCalculatePassengerFlow})
        </foreach>
    </insert>
    <insert id="createTreatBillHandlerProjectList" parameterType="java.util.List">
        INSERT INTO dbo.TB_TreatBillHandlerProject ( TreatBillProjectID, ProjectTreatHandlerID, EmployeeID,
        Scale,IsCalculatePassengerFlow )
        VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.TreatBillProjectID},#{item.ProjectTreatHandlerID},#{item.EmployeeID},#{item.Scale},#{item.IsCalculatePassengerFlow})
        </foreach>
    </insert>
    <insert id="createTreatBillHandlerGeneralCardList" parameterType="java.util.List">
        INSERT INTO dbo.TB_TreatBillHandlerGeneralCard ( TreatBillGeneralCardID, GeneralCardTreatHandlerID,
        EmployeeID, Scale ,IsCalculatePassengerFlow)
        VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.TreatBillGeneralCardID},#{item.GeneralCardTreatHandlerID},#{item.EmployeeID},#{item.Scale},#{item.IsCalculatePassengerFlow})
        </foreach>
    </insert>
    <insert id="createTreatBillHandlerTimeCardList" parameterType="java.util.List">
        INSERT INTO dbo.TB_TreatBillHandlerTimeCard ( TreatBillTimeCardID, TimeCardTreatHandlerID, EmployeeID,
        Scale ,IsCalculatePassengerFlow)
        VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.TreatBillTimeCardID},#{item.TimeCardTreatHandlerID},#{item.EmployeeID},#{item.Scale},#{item.IsCalculatePassengerFlow})
        </foreach>
    </insert>
    <insert id="createTreatBillHandlerSavingCardList" parameterType="java.util.List">
        INSERT INTO dbo.TB_TreatBillHandlerSavingCard ( TreatBillSavingCardID, SavingCardTreatHandlerID,
        EmployeeID, Scale,IsCalculatePassengerFlow )
        VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.TreatBillSavingCardID},#{item.SavingCardTreatHandlerID},#{item.EmployeeID},#{item.Scale},#{item.IsCalculatePassengerFlow})
        </foreach>
    </insert>

    <update id="updateTreatBillStatus">
        UPDATE TB_TreatBill
        SET BillStatus=#{BillStatus}
        WHERE ID = #{ID}
    </update>

    <select id="getTreatBillProduct" resultType="com.zhelian.model.ibeauty.form.output.AccountInfoOutputForm">
        SELECT tt.ProductAccountID AccountID,
               tt.PayAmount,
               tt.Quantity,
               tt.CardDeductionAmount,
               tt.LargessCardDeductionAmount,
               tp.Balance,
               tp.PayPerformanceBalanceAmount,
               tp.CardPerformanceBalanceAmount,
               tp.CardLargessPerformanceBalanceAmount,
               tt.IsLargess,
               tt.TotalAmount,
               ttb.BillType
        FROM TB_TreatBillProduct tt WITH(NOLOCK)
        JOIN  TB_TreatBill ttb
        WITH (NOLOCK)
        ON ttb.ID=tt.TreatBillID
            LEFT JOIN TB_ProductAccount tp ON tt.ProductAccountID=tp.ID
        WHERE tt.TreatBillID=#{ID}
    </select>
    <select id="getTreatBillProject" resultType="com.zhelian.model.ibeauty.form.output.AccountInfoOutputForm">
        SELECT tt.ProjectAccountID AccountID,
               tt.PayAmount,
               tt.Quantity,
               tt.CardDeductionAmount,
               tt.LargessCardDeductionAmount,
               tp.Balance,
               tp.PayPerformanceBalanceAmount,
               tp.CardPerformanceBalanceAmount,
               tp.CardLargessPerformanceBalanceAmount,
               tt.IsLargess,
               tt.TotalAmount,
               ttb.BillType,
               tt.ID TreatBillGoodID
        FROM TB_TreatBillProject tt WITH(NOLOCK)
            JOIN  TB_TreatBill ttb
        WITH (NOLOCK)
        ON ttb.ID=tt.TreatBillID
            LEFT JOIN TB_ProjectAccount tp ON tt.ProjectAccountID=tp.ID
        WHERE tt.TreatBillID=#{ID}
    </select>
    <select id="getTreatBillGeneralCard" resultType="com.zhelian.model.ibeauty.form.output.AccountInfoOutputForm">
        SELECT tt.GeneralCardAccountID AccountID,
               tt.PayAmount,
               tt.CardTreatTimes       Quantity,
               tt.CardDeductionAmount,
               tt.LargessCardDeductionAmount,
               tp.Balance,
               tp.ValidDate,
               tp.PayPerformanceBalanceAmount,
               tp.CardPerformanceBalanceAmount,
               tp.CardLargessPerformanceBalanceAmount,
               tt.IsLargess,
               tt.TotalAmount,
               ttb.BillType,
               tt.ID TreatBillGoodID
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
            JOIN  TB_TreatBill ttb
        WITH (NOLOCK)
        ON ttb.ID=tt.TreatBillID
            LEFT JOIN TB_GeneralCardAccount tp ON tt.GeneralCardAccountID=tp.ID
        WHERE tt.TreatBillID=#{ID}
    </select>
    <select id="getTreatBillTimeCard" resultType="com.zhelian.model.ibeauty.form.output.AccountInfoOutputForm">
        SELECT tt.TimeCardAccountID  AccountID,
               tt.PayAmount,
               tt.Quantity,
               tp.ValidDate,
               tt.CardDeductionAmount,
               tt.LargessCardDeductionAmount,
               tp.PerformanceBalance Balance,
               tp.PayPerformanceBalanceAmount,
               tp.CardPerformanceBalanceAmount,
               tp.CardLargessPerformanceBalanceAmount,
               tp.LargessPerformanceBalanceAmount,
               tt.Price,
               tt.IsLargess,
               tt.TotalAmount,
               ttb.BillType,
               tt.ID TreatBillGoodID
        FROM TB_TreatBillTimeCard tt WITH(NOLOCK)
            JOIN  TB_TreatBill ttb
        WITH (NOLOCK)
        ON ttb.ID=tt.TreatBillID
            LEFT JOIN TB_TimeCardAccount tp ON tt.TimeCardAccountID=tp.ID
        WHERE tt.TreatBillID=#{ID}
    </select>
    <select id="getTreatBillSavingCard" resultType="com.zhelian.model.ibeauty.form.output.AccountInfoOutputForm">
        SELECT tt.SavingCardAccountID AccountID,
               tt.CardTreatAmount,
               tt.LargessCardTreatAmount,
               tp.ValidDate,
               tp.Balance,
               tp.LargessBalance,
               ttb.BillType,
               tt.ID TreatBillGoodID
        FROM TB_TreatBillSavingCard tt WITH(NOLOCK)
            JOIN  TB_TreatBill ttb
        WITH (NOLOCK)
        ON ttb.ID=tt.TreatBillID
            LEFT JOIN TB_SavingCardAccount tp ON tt.SavingCardAccountID=tp.ID
        WHERE tt.TreatBillID=#{ID}
    </select>

    <update id="updateRemark">
        UPDATE TB_TreatBill
        SET Remark=#{Remark}
        WHERE ID = #{BillID}
    </update>

    <select id="getTreatEmployeeName" parameterType="java.util.List"
            resultType="com.zhelian.model.ibeauty.form.output.HandlerOrEmployeeNameOutputForm">
        SELECT ID EmployeeID,Name FROM TK_Employee
        WHERE ID IN
        (
        <foreach collection="list" item="item" index="index"
                 separator=",">
            #{item}
        </foreach>
        )
    </select>

    <select id="getTreatProductHandlerName"
            resultType="com.zhelian.model.ibeauty.form.output.HandlerOrEmployeeNameOutputForm">
        SELECT ID,
               Name
        FROM TB_ProductTreatHandler tsh
        WHERE tsh.Active = 1
          AND tsh.EntityID = (SELECT TOP
                                  1 PSH.EntityID
                              FROM TB_ProductTreatHandler PSH
        WITH ( NOLOCK )
            INNER JOIN dbo.GetParentEntities (#{EntityID}) E
        ON PSH.EntityID = E.ID
        WHERE
            PSH.Active= 1
        ORDER BY
            E.Depth
            )
            AND tsh.ID IN (
        SELECT
            tsj.ProductTreatHandlerID
        FROM
            TB_ProductTreatHandlerJobType tsj
        WITH ( NOLOCK )
            LEFT JOIN TK_JobType tj
        WITH ( NOLOCK )
        ON tj.ID= tsj.JobTypeID
            LEFT JOIN TK_Employee tee
        WITH ( NOLOCK )
        ON tsj.JobTypeID= tee.JobID
            LEFT JOIN TK_EmployeeEntity tk
        WITH ( NOLOCK )
        ON tee.ID= tk.EmployeeID
        WHERE
            tk.EntityID= #{EntityID}
          AND tee.State= 1
            )
        ORDER BY
            tsh.Sequence DESC, tsh.ID ASC
    </select>

    <select id="getTreatProjectHandlerName"
            resultType="com.zhelian.model.ibeauty.form.output.HandlerOrEmployeeNameOutputForm">
        SELECT ID,
               Name
        FROM TB_ProjectTreatHandler tsh
        WHERE tsh.Active = 1
          AND tsh.EntityID = (SELECT TOP
                                  1 PSH.EntityID
                              FROM TB_ProjectTreatHandler PSH
        WITH ( NOLOCK )
            INNER JOIN dbo.GetParentEntities (#{EntityID}) E
        ON PSH.EntityID = E.ID
        WHERE
            PSH.Active= 1
        ORDER BY
            E.Depth
            )
            AND tsh.ID IN (
        SELECT
            tsj.ProjectTreatHandlerID
        FROM
            TB_ProjectTreatHandlerJobType tsj
        WITH ( NOLOCK )
            LEFT JOIN TK_JobType tj
        WITH ( NOLOCK )
        ON tj.ID= tsj.JobTypeID
            LEFT JOIN TK_Employee tee
        WITH ( NOLOCK )
        ON tsj.JobTypeID= tee.JobID
            LEFT JOIN TK_EmployeeEntity tk
        WITH ( NOLOCK )
        ON tee.ID= tk.EmployeeID
        WHERE
            tk.EntityID= #{EntityID}
          AND tee.State= 1
            )
        ORDER BY
            tsh.Sequence DESC, tsh.ID ASC
    </select>

    <select id="getTreatSavingCardHandlerName"
            resultType="com.zhelian.model.ibeauty.form.output.HandlerOrEmployeeNameOutputForm">
        SELECT ID,
               Name
        FROM TB_SavingCardProjectTreatHandler tsh
        WHERE tsh.Active = 1
          AND tsh.EntityID = (SELECT TOP
                                  1 PSH.EntityID
                              FROM TB_SavingCardProjectTreatHandler PSH
        WITH ( NOLOCK )
            INNER JOIN dbo.GetParentEntities (#{EntityID}) E
        ON PSH.EntityID = E.ID
        WHERE
            PSH.Active= 1
        ORDER BY
            E.Depth
            )
            AND tsh.ID IN (
        SELECT
            tsj.SavingCardProjectTreatHandlerID
        FROM
            TB_SavingCardProjectTreatHandlerJobType tsj
        WITH ( NOLOCK )
            LEFT JOIN TK_JobType tj
        WITH ( NOLOCK )
        ON tj.ID= tsj.JobTypeID
            LEFT JOIN TK_Employee tee
        WITH ( NOLOCK )
        ON tsj.JobTypeID= tee.JobID
            LEFT JOIN TK_EmployeeEntity tk
        WITH ( NOLOCK )
        ON tee.ID= tk.EmployeeID
        WHERE
            tk.EntityID= #{EntityID}
          AND tee.State= 1
            )
        ORDER BY
            tsh.Sequence DESC, tsh.ID ASC
    </select>

    <select id="getTreatBillProductEntity" resultType="com.zhelian.model.ibeauty.entity.TreatBillProductEntity">
        SELECT *
        FROM TB_TreatBillProduct WITH(NOLOCK)
        WHERE ID=#{ID}
    </select>
    <select id="getTreatBillProjectEntity" resultType="com.zhelian.model.ibeauty.entity.TreatBillProjectEntity">
        SELECT *
        FROM TB_TreatBillProject WITH(NOLOCK)
        WHERE ID=#{ID}
    </select>
    <select id="getTreatBillGeneralCardEntity" resultType="com.zhelian.model.ibeauty.entity.TreatBillGeneralCardEntity">
        SELECT *
        FROM TB_TreatBillGeneralCard WITH(NOLOCK)
        WHERE ID=#{ID}
    </select>
    <select id="getTreatBillTimeCardEntity" resultType="com.zhelian.model.ibeauty.entity.TreatBillTimeCardEntity">
        SELECT *
        FROM TB_TreatBillTimeCard WITH(NOLOCK)
        WHERE ID=#{ID}
    </select>
    <select id="getTreatBillSavingCardEntity" resultType="com.zhelian.model.ibeauty.entity.TreatBillSavingCardEntity">
        SELECT *
        FROM TB_TreatBillSavingCard WITH(NOLOCK)
        WHERE ID=#{ID}
    </select>

    <insert id="createTreatBillCancelProcess"
            parameterType="com.zhelian.model.ibeauty.entity.TreatBillCancelProcessEntity">
        INSERT INTO
            dbo.TB_TreatBillCancelProcess
        (BillID,
            CreatedBy,
        Remark)
        VALUES
            (
            #{BillID},
            #{CreatedBy},
            #{Remark}
            )
    </insert>

    <insert id="addSignature" parameterType="com.zhelian.model.ibeauty.entity.TreatBillSignatureEntity">
        INSERT INTO
            dbo.TB_TreatBillSignature
        (TreatBillID,
        ImageURL)
        VALUES
            (
            #{TreatBillID},
            #{ImageURL}
            )
    </insert>

    <select id="getTreatBillSignatureByID" resultType="com.zhelian.model.miniprogram.form.output.SignatureOutputForm">
        SELECT CONVERT(VARCHAR (19), CreatedOn, 120) CreatedOn, ImageURL
        FROM TB_TreatBillSignature WITH(NOLOCK)
        WHERE TreatBillID = #{ID}
    </select>

    <delete id="deleteTreatBillSignature">
        DELETE
        FROM TB_TreatBillSignature
        WHERE TreatBillID = #{ID}
    </delete>
    <update id="updateTreatBillDate">
        UPDATE TB_TreatBill
        SET BillDate=#{BillDate}
        WHERE ID = #{TreatBillID}
    </update>

    <update id="updateTreatBill" parameterType="com.zhelian.model.ibeauty.entity.TreatBillEntity">
        UPDATE TB_TreatBill
        SET BillDate=#{BillDate},
            EntityID=#{EntityID},
            CustomerID=#{CustomerID},
            BillType=#{BillType},
            BillStatus=#{BillStatus},
            EmployeeID=#{EmployeeID},
            PricePreferentialAmount=#{PricePreferentialAmount},
            MemberPreferentialAmount=#{MemberPreferentialAmount},
            CardPreferentialAmount=#{CardPreferentialAmount},
            Amount=#{Amount},
            PayAmount=#{PayAmount},
            CardDeductionAmount=#{CardDeductionAmount},
            LargessCardDeductionAmount=#{LargessCardDeductionAmount},
            Channel=#{Channel},
            Remark=#{Remark}
        WHERE ID = #{ID}
    </update>

    <delete id="deleteProduct">
        DELETE
        FROM TB_TreatBillProduct
        WHERE TreatBillID = #{ID}
    </delete>

    <delete id="deleteProject">
        DELETE
        FROM TB_TreatBillProject
        WHERE TreatBillID = #{ID}
    </delete>

    <delete id="deleteGeneralCard">
        DELETE
        FROM TB_TreatBillGeneralCard
        WHERE TreatBillID = #{ID}
    </delete>

    <delete id="deleteTimeCard">
        DELETE
        FROM TB_TreatBillTimeCard
        WHERE TreatBillID = #{ID}
    </delete>

    <delete id="deleteSavingCard">
        DELETE
        FROM TB_TreatBillSavingCard
        WHERE TreatBillID = #{ID}
    </delete>

    <delete id="deleteTreatBill">
        DELETE
        FROM TB_TreatBill
        WHERE ID = #{ID}
    </delete>

    <select id="treatPendingOrderBillInfo"
            resultType="com.zhelian.model.ibeauty.form.output.TreatPendingOrderInfoOutputForm">
        SELECT tsb.ID,
               tsb.CustomerID,
               CONVERT(VARCHAR (19), tsb.BillDate, 120) BillDate,
               case
                   when tsb.CustomerID is null then '散客'
                   else tc.Name end                     Name,
               tc.Code,
               tsb.Remark,
               tc.PhoneNumber as                        PhoneNumber
        FROM TB_TreatBill tsb WITH(NOLOCK)
        LEFT JOIN  TB_Customer  tc
        WITH (NOLOCK)
        ON tc.ID=tsb.CustomerID
        WHERE tsb.ID=#{BillID}
    </select>

    <select id="getGeneralCardID" resultType="java.lang.Integer">
        SELECT tpa.GeneralCardID
        FROM TB_TreatBillGeneralCard t WITH(NOLOCK)
        LEFT JOIN TB_GeneralCardAccount tpa
        WITH (NOLOCK)
        ON tpa.ID=t.GeneralCardAccountID
        WHERE t.ID=#{TreatBillGoodsID}
    </select>

    <select id="getTimeCardID" resultType="java.lang.Integer">
        SELECT tpa.TimeCardID
        FROM TB_TreatBillTimeCard t WITH(NOLOCK)
        LEFT JOIN TB_TimeCardAccount tpa
        WITH (NOLOCK)
        ON tpa.ID=t.TimeCardAccountID
        WHERE t.ID=#{TreatBillGoodsID}
    </select>

    <select id="getSavingCardID" resultType="java.lang.Integer">
        SELECT tpa.SavingCardID
        FROM TB_TreatBillSavingCard t WITH(NOLOCK)
        LEFT JOIN TB_SavingCardAccount tpa
        WITH (NOLOCK)
        ON tpa.ID=t.SavingCardAccountID
        WHERE t.ID=#{TreatBillGoodsID}
    </select>

    <select id="treatBillProductPayInfo"
            resultType="com.zhelian.model.ibeauty.form.output.SaleBillGoodPayInfoOutputForm">
        SELECT SUM(SBP.PayAmount)                  PayAmount,
               SUM(SBP.CardDeductionAmount)        SavingCardDeductionAmount,
               SUM(SBP.LargessCardDeductionAmount) LargessSavingCardDeductionAmount
        FROM TB_TreatBillProduct SBP WITH(NOLOCK)
        LEFT JOIN TB_TreatBill SB
        WITH (NOLOCK)
        ON SBP.TreatBillID = SB.ID
        WHERE SB.ID=#{BillID}
    </select>

    <select id="treatBillProjectPayInfo"
            resultType="com.zhelian.model.ibeauty.form.output.SaleBillGoodPayInfoOutputForm">
        SELECT SUM(SBP.PayAmount)                  PayAmount,
               SUM(SBP.CardDeductionAmount)        SavingCardDeductionAmount,
               SUM(SBP.LargessCardDeductionAmount) LargessSavingCardDeductionAmount
        FROM TB_TreatBillProject SBP WITH(NOLOCK)
        LEFT JOIN TB_TreatBill SB
        WITH (NOLOCK)
        ON SBP.TreatBillID = SB.ID
        WHERE SB.ID=#{BillID}
    </select>

    <select id="treatBillGeneralCardPayInfo"
            resultType="com.zhelian.model.ibeauty.form.output.SaleBillGoodPayInfoOutputForm">
        SELECT SUM(SBP.PayAmount)                  PayAmount,
               SUM(SBP.CardDeductionAmount)        SavingCardDeductionAmount,
               SUM(SBP.LargessCardDeductionAmount) LargessSavingCardDeductionAmount
        FROM TB_TreatBillGeneralCard SBP WITH(NOLOCK)
        LEFT JOIN TB_TreatBill SB
        WITH (NOLOCK)
        ON SBP.TreatBillID = SB.ID
        WHERE SB.ID=#{BillID}
    </select>

    <select id="treatBillTimeCardPayInfo"
            resultType="com.zhelian.model.ibeauty.form.output.SaleBillGoodPayInfoOutputForm">
        SELECT SUM(SBP.PayAmount)                  PayAmount,
               SUM(SBP.CardDeductionAmount)        SavingCardDeductionAmount,
               SUM(SBP.LargessCardDeductionAmount) LargessSavingCardDeductionAmount
        FROM TB_TreatBillTimeCard SBP WITH(NOLOCK)
        LEFT JOIN TB_TreatBill SB
        WITH (NOLOCK)
        ON SBP.TreatBillID = SB.ID
        WHERE SB.ID=#{BillID}
    </select>

    <select id="treatBillSavingCardPayInfo"
            resultType="com.zhelian.model.ibeauty.form.output.SaleBillGoodPayInfoOutputForm">
        SELECT SUM(SBP.CardTreatAmount)        SavingCardDeductionAmount,
               SUM(SBP.LargessCardTreatAmount) LargessSavingCardDeductionAmount
        FROM TB_TreatBillSavingCard SBP WITH(NOLOCK)
        LEFT JOIN TB_TreatBill SB
        WITH (NOLOCK)
        ON SBP.TreatBillID = SB.ID
        WHERE SB.ID=#{BillID}
    </select>

    <select id="getCustomerAggregateAmount" resultType="decimal">
        SELECT ISNULL(SUM(CASE
                              WHEN BillType = '20' THEN (PayAmount + CardDeductionAmount) * -1
                              ELSE PayAmount + CardDeductionAmount END), 0) Amount
        FROM TB_TreatBill WITH(NOLOCK)
        WHERE CustomerID=#{CustomerID} AND BillStatus='20'
    </select>

    <select id="getYXYTreatBillProject"
            resultType="com.zhelian.model.yingxiaoyun.form.output.YXYTreatBillProjectListOutPutForm">
        SELECT Name          ProjectName,
               Sum(Quantity) Quantity
        FROM (SELECT ProjectID, Name, Quantity
              FROM TB_TreatBillProject t WITH(NOLOCK)
                LEFT JOIN TB_Project tp
              WITH (NOLOCK)
              ON t.ProjectID=tp.ID
              WHERE t.TreatBillID =#{ID}
              UNION ALL
              SELECT ProjectID, Name, Quantity
              FROM TB_TreatBillGeneralCard t
              WITH (NOLOCK)
                  LEFT JOIN TB_Project tp
              WITH (NOLOCK)
              ON t.ProjectID=tp.ID
              WHERE t.TreatBillID =#{ID}
              UNION ALL
              SELECT ProjectID, Name, Quantity
              FROM TB_TreatBillTimeCard t
              WITH (NOLOCK)
                  LEFT JOIN TB_Project tp
              WITH (NOLOCK)
              ON t.ProjectID=tp.ID
              WHERE t.TreatBillID =#{ID}
              UNION ALL
              SELECT ProjectID, Name, Quantity
              FROM TB_TreatBillSavingCard t
              WITH (NOLOCK)
                  LEFT JOIN TB_Project tp
              WITH (NOLOCK)
              ON t.ProjectID=tp.ID
              WHERE t.TreatBillID =#{ID}) T
        GROUP BY ProjectID, Name
        ORDER BY Name
    </select>

    <select id="getWXTreatBill" resultType="com.zhelian.model.ibeauty.form.output.WXTreatBillInfoOutputForm">
        SELECT ty.EntityName,
               tt.ID,
               t.OffiaccountOpenID,
               CONVERT(VARCHAR (19), tt.BillDate, 120) BillDate,
               td.Name TreatMessage,
               td.Quantity
        FROM TB_TreatBill tt
        WITH (NOLOCK)
            LEFT JOIN TB_Customer t
        WITH (NOLOCK)
        ON t.ID = tt.CustomerID
            LEFT JOIN TK_Employee te
        WITH (NOLOCK)
        ON te.ID = tt.EmployeeID
            LEFT JOIN TK_Entity ty
        WITH (NOLOCK)
        ON ty.ID = tt.EntityID
            LEFT JOIN (
            SELECT t.TreatBillID,t.Name,SUM(Quantity) Quantity
            FROM
            (
            SELECT t.TreatBillID, tp.Name,Quantity FROM TB_TreatBillProduct t WITH (NOLOCK)
            LEFT JOIN TE_Product tp WITH (NOLOCK) ON t.ProductID=tp.ID
            UNION ALL
            SELECT t.TreatBillID, tp.Name,Quantity FROM TB_TreatBillProject t WITH (NOLOCK)
            LEFT JOIN TB_Project tp WITH (NOLOCK) ON t.ProjectID=tp.ID
            UNION ALL
            SELECT t.TreatBillID, tp.Name,Quantity FROM TB_TreatBillGeneralCard t WITH (NOLOCK)
            LEFT JOIN TB_Project tp WITH (NOLOCK) ON t.ProjectID=tp.ID
            UNION ALL SELECT t.TreatBillID, tp.Name,Quantity FROM TB_TreatBillTimeCard t WITH (NOLOCK)
            LEFT JOIN TB_Project tp WITH (NOLOCK) ON t.ProjectID=tp.ID
            UNION ALL SELECT t.TreatBillID, tp.Name,Quantity FROM TB_TreatBillSavingCard t WITH (NOLOCK)
            LEFT JOIN TB_Project tp WITH (NOLOCK) ON t.ProjectID=tp.ID
            )
            t GROUP BY t.TreatBillID,t.Name
            ) td ON td.TreatBillID = tt.ID
        WHERE tt.ID=#{ID}
    </select>

    <select id="getWXRefundTreatBill" resultType="com.zhelian.model.ibeauty.form.output.WXRefundTreatBillInfoOutputForm">
        SELECT ty.EntityName,
               tt.ID,
               CONVERT(VARCHAR(10),tt.BillDate,120) BillDate,
               t.OffiaccountOpenID,
               td.Name TreatMessage,
               td.Quantity
        FROM TB_TreatBill tt WITH (NOLOCK)
        LEFT JOIN TB_Customer t WITH (NOLOCK) ON t.ID = tt.CustomerID
            LEFT JOIN TK_Entity ty WITH (NOLOCK) ON ty.ID = tt.EntityID
            LEFT JOIN (
            SELECT t.TreatBillID,t.Name,SUM(Quantity) Quantity
            FROM
            (
            SELECT t.TreatBillID, tp.Name,Quantity FROM TB_TreatBillProduct t WITH (NOLOCK)
            LEFT JOIN TE_Product tp WITH (NOLOCK) ON t.ProductID=tp.ID
            UNION ALL
            SELECT t.TreatBillID, tp.Name,Quantity FROM TB_TreatBillProject t WITH (NOLOCK)
            LEFT JOIN TB_Project tp WITH (NOLOCK) ON t.ProjectID=tp.ID
            UNION ALL
            SELECT t.TreatBillID, tp.Name,Quantity FROM TB_TreatBillGeneralCard t WITH (NOLOCK)
            LEFT JOIN TB_Project tp WITH (NOLOCK) ON t.ProjectID=tp.ID
            UNION ALL SELECT t.TreatBillID, tp.Name,Quantity FROM TB_TreatBillTimeCard t WITH (NOLOCK)
            LEFT JOIN TB_Project tp WITH (NOLOCK) ON t.ProjectID=tp.ID
            UNION ALL SELECT t.TreatBillID, tp.Name,Quantity FROM TB_TreatBillSavingCard t WITH (NOLOCK)
            LEFT JOIN TB_Project tp WITH (NOLOCK) ON t.ProjectID=tp.ID
            )
            t GROUP BY t.TreatBillID,t.Name
            ) td ON td.TreatBillID = tt.ID
        WHERE tt.ID=#{ID}
    </select>

    <select id="getWXCancelTreatBill"
            resultType="com.zhelian.model.ibeauty.form.output.WXCancelTreatBillInfoOutputForm">
        SELECT tt.ID,
               ty.EntityName,
               CONVERT(VARCHAR (19), TT.BillDate, 120) BillDate,
               t.Name + '(手机：' + t.PhoneNumber + ')'   Name,
               t.OffiaccountOpenID,
               td.Name TreatMessage,
               td.Quantity
        FROM TB_TreatBill tt
        WITH (NOLOCK)
            LEFT JOIN TB_Customer t
        WITH (NOLOCK)
        ON t.ID = tt.CustomerID
            LEFT JOIN TK_Employee te
        WITH (NOLOCK)
        ON te.ID = tt.EmployeeID
            LEFT JOIN TK_Entity ty
        WITH (NOLOCK)
        ON ty.ID = tt.EntityID
            LEFT JOIN (
            SELECT t.TreatBillID,t.Name,SUM(Quantity) Quantity
            FROM
            (
            SELECT t.TreatBillID, tp.Name,Quantity FROM TB_TreatBillProduct t WITH (NOLOCK)
            LEFT JOIN TE_Product tp WITH (NOLOCK) ON t.ProductID=tp.ID
            UNION ALL
            SELECT t.TreatBillID, tp.Name,Quantity FROM TB_TreatBillProject t WITH (NOLOCK)
            LEFT JOIN TB_Project tp WITH (NOLOCK) ON t.ProjectID=tp.ID
            UNION ALL
            SELECT t.TreatBillID, tp.Name,Quantity FROM TB_TreatBillGeneralCard t WITH (NOLOCK)
            LEFT JOIN TB_Project tp WITH (NOLOCK) ON t.ProjectID=tp.ID
            UNION ALL SELECT t.TreatBillID, tp.Name,Quantity FROM TB_TreatBillTimeCard t WITH (NOLOCK)
            LEFT JOIN TB_Project tp WITH (NOLOCK) ON t.ProjectID=tp.ID
            UNION ALL SELECT t.TreatBillID, tp.Name,Quantity FROM TB_TreatBillSavingCard t WITH (NOLOCK)
            LEFT JOIN TB_Project tp WITH (NOLOCK) ON t.ProjectID=tp.ID
            )
            t GROUP BY t.TreatBillID,t.Name
            ) td ON td.TreatBillID = tt.ID
        WHERE tt.ID=#{ID}
    </select>

    <select id="pendingList" resultType="com.zhelian.model.ibeauty.form.output.TreatBillOutputForm">
        SELECT
        tsb.ID,
        tsb.CustomerID,
        CONVERT(VARCHAR (19),tsb.BillDate,120) BillDate,
        CONVERT(VARCHAR (19),tsb.CreatedOn,120) CreatedOn,
        case when tsb.CustomerID is null then '散客'
        else tc.Name end Name,
        tc.Code,
        tc.PhoneNumber as PhoneNumber,
        tsb.Amount,
        tsb.BillStatus,
        tsb.BillType,
        te.Name EmployeeName
        FROM
        TB_TreatBill tsb WITH(NOLOCK)
        LEFT JOIN TB_Customer tc WITH(NOLOCK) ON tc.ID=tsb.CustomerID
        LEFT JOIN TK_Employee te WITH(NOLOCK) ON te.ID=tsb.EmployeeID
        <where>
            tsb.EntityID=#{EntityID}
            <if test="Name != null and Name != ''">
                <choose>
                    <when test="Name =='散客'">
                        AND tsb.CustomerID is null
                    </when>
                    <otherwise>
                        AND (tsb.ID = #{Name} OR tc.Name LIKE '%'+ #{Name} +'%' OR tc.PhoneNumber LIKE '%'+ #{Name} +'%'
                        OR tc.Code LIKE '%'+ #{Name} +'%')
                    </otherwise>
                </choose>
            </if>

            <if test="BillStatus != null and BillStatus != ''">
                AND tsb.BillStatus = #{BillStatus}
            </if>

        </where>
        ORDER BY tsb.BillDate DESC
    </select>

    <select id="getProjectConsumableTemplate" resultType="com.zhelian.model.ibeauty.form.output.TreatBillProjectConsumableTemplateProductOutputForm">
        SELECT
            tp.ID ProductID,
            tp.Name ProductName,
            tp.Specification,
            tpb.Name BrandName,
            a1.UnitID,
            a2.Name UnitName
        FROM
            TB_ProjectConsumableTemplateProduct tc
                LEFT JOIN TE_Product tp On tp.ID =tc.ProductID
                LEFT JOIN TE_ProductBrand tpb ON tpb.ID=tp.ProductBrandID
                LEFT JOIN TE_ProductBarCode a1 WITH(NOLOCK) on tc.ProductID=a1.ProductID AND a1.IsMinimumUnit=1
            LEFT JOIN TE_Unit a2 WITH(NOLOCK) ON a1.UnitID=a2.ID
        WHERE tc.ProjectConsumableTemplateID=#{ProjectConsumableTemplateID}
        order by tp.Name
    </select>


    <resultMap id="ProductStock"
               type="com.zhelian.model.ibeauty.form.output.EntityProductStockForm">
        <id column="ProductID" property="ProductID" jdbcType="INTEGER"/>
        <collection property="Entity"
                    ofType="com.zhelian.model.ibeauty.form.output.TreatBillProjectConsumableTemplateProductStockOutputForm">
            <id column="EntityID" property="EntityID" jdbcType="INTEGER"/>
            <result column="EntityName" property="EntityName" jdbcType="VARCHAR"/>
            <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
        </collection>
    </resultMap>

    <select id="getEntityStock" resultMap="ProductStock">

        SELECT ts.ProductID,ty.ID EntityID,ty.EntityName,ts.Quantity  FROM TB_EmployeeEntityPermission teep WITH(NOLOCK)
        JOIN GetSubsidiaryEntities(#{EntityID}) y on y.ID=teep .EntityID
        LEFT JOIN TK_Entity ty WITH(NOLOCK) ON y.ID=ty.ID
        LEFT JOIN TE_Stock ts WITH(NOLOCK) oN ts.EntityID =ty.ID
        WHERE ty.Active=1 AND ty.IsStore=0
        order by ty.Name
    </select>


    <resultMap id="Template"
               type="com.zhelian.model.ibeauty.form.output.TreatBillTemplateOutputForm">
        <id column="ID" property="ID" jdbcType="INTEGER"/>
        <id column="Name" property="Name" jdbcType="VARCHAR"/>
        <collection property="Product"
                    ofType="com.zhelian.model.ibeauty.form.output.TreatBillTemplateProductOutputForm">
            <id column="ProductID" property="ProductID" jdbcType="INTEGER"/>
            <result column="ProductName" property="ProductName" jdbcType="VARCHAR"/>
            <result column="BrandName" property="BrandName" jdbcType="VARCHAR"/>
            <result column="Specification" property="Specification" jdbcType="VARCHAR"/>
            <result column="MinimumUnitQuantity" property="MinimumUnitQuantity" jdbcType="INTEGER"/>
            <result column="MinimumUnitID" property="MinimumUnitID" jdbcType="INTEGER"/>
            <result column="MinimumUnitName" property="MinimumUnitName" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <select id="templateByProject" resultMap="Template">
        SELECT
            tc.ID ,
            tc.Name ,
            tp.ID ProductID,
            tp.Name ProductName,
            tp.Specification,
            tpb.Name BrandName,
            t.MinimumUnitQuantity,
            a1.UnitID MinimumUnitID,
            a2.Name MinimumUnitName
        FROM
            TB_ProjectConsumableTemplate tc WITH(NOLOCK)
                LEFT JOIN TB_ProjectConsumableTemplateProduct t WITH(NOLOCK) On t.ProjectConsumableTemplateID  =tc.ID
            LEFT JOIN TE_Product tp WITH(NOLOCK) On tp.ID =t.ProductID
            LEFT JOIN TE_ProductBrand tpb WITH(NOLOCK) ON tpb.ID=tp.ProductBrandID
            LEFT JOIN TE_ProductBarCode a1 WITH(NOLOCK) on t.ProductID=a1.ProductID AND a1.IsMinimumUnit=1
            LEFT JOIN TE_Unit a2 WITH(NOLOCK) ON a1.UnitID=a2.ID
        WHERE tc.Active=1 AND EXISTS (
            SELECT 1 FROM
            TB_ProjectConsumableTemplateProject tpctp WITH(NOLOCK)
          WHERE tpctp.ProjectConsumableTemplateID =tc.ID and tpctp.ProjectID =#{ProjectID}
            )OR EXISTS
            ( SELECT 1 FROM
            TB_ProjectConsumableTemplateProjectCategory tpcty WITH(NOLOCK)
            LEFT JOIN TB_ProjectCategory tpc  WITH(NOLOCK) ON tpc.ID=tpcty.ProjectCategoryID
            LEFT JOIN TB_Project tt  WITH(NOLOCK) ON tt.ProjectCategoryID=tpcty.ProjectCategoryID
            WHERE tpc.ParentID!=0 AND tpcty.ProjectConsumableTemplateID =tc.ID and tt.ID =#{ProjectID}
            )OR EXISTS
            ( SELECT 1 FROM
            TB_ProjectConsumableTemplateProjectCategory tpcty WITH(NOLOCK)
            LEFT JOIN TB_ProjectCategory tpc  WITH(NOLOCK) ON tpc.ParentID=tpcty.ProjectCategoryID
            LEFT JOIN TB_Project tt  WITH(NOLOCK) ON tt.ProjectCategoryID=tpc.ID
            WHERE  tpcty.ProjectConsumableTemplateID =tc.ID and tt.ID =#{ProjectID}
            )
    </select>

    <select id="product" resultType="com.zhelian.model.ibeauty.form.output.TreatBillTemplateAllProductOutputForm">
        SELECT
            a1.ID ProductID,
            a1.Name ProductName,
            a1.Specification,
            a5.Name BrandName,
            a3.UnitID MinimumUnitID,
            a4.Name MinimumUnitName
        FROM
            TE_Product a1 WITH(NOLOCK)
        LEFT JOIN TE_ProductCategory a2 WITH(NOLOCK) on a1.PCategoryID=a2.ID
            LEFT JOIN TE_ProductBarCode a3 WITH(NOLOCK) on a1.ID=a3.ProductID AND a3.IsMinimumUnit=1
            LEFT JOIN TE_Unit a4 WITH(NOLOCK) ON a3.UnitID=a4.ID
            LEFT JOIN TE_ProductBrand a5 WITH(NOLOCK) ON a1.ProductBrandID=a5.ID
        <where>
            a1.Active = 1
            <if test="Name != null and Name != ''">
                and (a1.Name LIKE '%'+ #{Name} +'%' OR a1.NameFullPinYin LIKE '%'+ #{Name} +'%'
                OR a1.NameAbbrPinYin LIKE '%'+ #{Name} +'%' OR a1.Alias LIKE '%'+ #{Name} +'%'
                OR a1.AliasFullPinYin LIKE '%'+ #{Name} +'%' OR a1.AliasAbbrPinYin LIKE '%'+ #{Name} +'%'
                )
            </if>
        </where>
        ORDER BY a1.Name
    </select>


    <select id="productEntity" resultType="com.zhelian.model.ibeauty.form.output.TreatBillProjectConsumableTemplateProductStockOutputForm">
        SELECT ty.ID EntityID,ty.EntityName  FROM TB_EmployeeEntityPermission teep WITH(NOLOCK)
        JOIN GetSubsidiaryEntities(#{EntityID}) y on y.ID=teep.EntityID
            LEFT JOIN TK_Entity ty WITH(NOLOCK) ON y.ID=ty.ID
        WHERE ty.Active=1 AND ty.IsWarehouse=1 AND ty.ID!=#{EntityID} AND teep.EmployeeID =#{EmployeeID}
        order by ty.EntityName
    </select>

    <select id="getTreatProjectOutboundDetail" resultType="com.zhelian.model.ibeauty.form.output.TreatGoodOutboundDetailOutputForm">
        SELECT
            ProductID,
            UnitID,
            Quantity,
            MinimumUnitID ,
            MinimumUnitQuantity ,
            t.EntityID
        FROM
            TE_InventoryTreatProjectOutboundDetail td WITH(NOLOCK)
                LEFT JOIN TE_InventoryTreatOutbound t WITH(NOLOCK) ON t.ID =td.InventoryTreatOutboundID
        WHERE td.TreatBillProjectID=#{TreatBillGoodID}
    </select>

    <select id="getTreatGeneralCardOutboundDetail" resultType="com.zhelian.model.ibeauty.form.output.TreatGoodOutboundDetailOutputForm">
        SELECT
            ProductID,
            UnitID,
            Quantity,
            MinimumUnitID ,
            MinimumUnitQuantity ,
            t.EntityID
        FROM
            TE_InventoryTreatGeneralCardOutboundDetail td WITH(NOLOCK)
                LEFT JOIN TE_InventoryTreatOutbound t WITH(NOLOCK) ON t.ID =td.InventoryTreatOutboundID
        WHERE td.TreatBillGeneralCardID=#{TreatBillGoodID}
    </select>

    <select id="getTreatTimeCardOutboundDetail" resultType="com.zhelian.model.ibeauty.form.output.TreatGoodOutboundDetailOutputForm">
        SELECT
            ProductID,
            UnitID,
            Quantity,
            MinimumUnitID ,
            MinimumUnitQuantity ,
            t.EntityID
        FROM
            TE_InventoryTreatTimeCardOutboundDetail td WITH(NOLOCK)
                LEFT JOIN TE_InventoryTreatOutbound t WITH(NOLOCK) ON t.ID =td.InventoryTreatOutboundID
        WHERE td.TreatBillTimeCardID=#{TreatBillGoodID}
    </select>

    <select id="getTreatSavingCardOutboundDetail" resultType="com.zhelian.model.ibeauty.form.output.TreatGoodOutboundDetailOutputForm">
        SELECT
            ProductID,
            UnitID,
            Quantity,
            MinimumUnitID ,
            MinimumUnitQuantity ,
            t.EntityID
        FROM
            TE_InventoryTreatSavingCardOutboundDetail td WITH(NOLOCK)
                LEFT JOIN TE_InventoryTreatOutbound t WITH(NOLOCK) ON t.ID =td.InventoryTreatOutboundID
        WHERE td.TreatBillSavingCardID=#{TreatBillGoodID}
    </select>

    <delete id="deleteInventoryTreatOutbound">
        DELETE FROM TE_InventoryTreatOutbound WHERE TreatBillID=#{BillID}
    </delete>

    <delete id="deleteInventoryTreatProjectOutboundDetail">
        DELETE
        TE_InventoryTreatProjectOutboundDetail  FROM   TE_InventoryTreatProjectOutboundDetail tsb
        INNER JOIN TE_InventoryTreatOutbound  ts  ON  ts.ID=tsb.InventoryTreatOutboundID
        WHERE ts.TreatBillID=#{BillID}
    </delete>
    <delete id="deleteInventoryTreatGeneralCardOutboundDetail">
        DELETE
        TE_InventoryTreatGeneralCardOutboundDetail  FROM   TE_InventoryTreatGeneralCardOutboundDetail tsb
        INNER JOIN TE_InventoryTreatOutbound  ts  ON  ts.ID=tsb.InventoryTreatOutboundID
        WHERE ts.TreatBillID=#{BillID}
    </delete>
    <delete id="deleteInventoryTreatTimeCardOutboundDetail">
        DELETE
        TE_InventoryTreatTimeCardOutboundDetail  FROM   TE_InventoryTreatTimeCardOutboundDetail tsb
        INNER JOIN TE_InventoryTreatOutbound  ts  ON  ts.ID=tsb.InventoryTreatOutboundID
        WHERE ts.TreatBillID=#{BillID}
    </delete>
    <delete id="deleteInventoryTreatSavingCardOutboundDetail">
        DELETE
        TE_InventoryTreatSavingCardOutboundDetail  FROM   TE_InventoryTreatSavingCardOutboundDetail tsb
        INNER JOIN TE_InventoryTreatOutbound  ts  ON  ts.ID=tsb.InventoryTreatOutboundID
        WHERE ts.TreatBillID=#{BillID}
    </delete>

    <update id="updateProductRemark">
        UPDATE TB_TreatBillProduct
        SET Remark = #{Remark}
        WHERE ID = #{TreatBillGoodID}
    </update>

    <update id="updateProjectRemark">
        UPDATE TB_TreatBillProject
        SET Remark = #{Remark}
        WHERE ID = #{TreatBillGoodID}
    </update>

    <update id="updateGeneralCardRemark">
        UPDATE TB_TreatBillGeneralCard
        SET Remark = #{Remark}
        WHERE ID = #{TreatBillGoodID}
    </update>

    <update id="updateTimeCardRemark">
        UPDATE TB_TreatBillTimeCard
        SET Remark = #{Remark}
        WHERE ID = #{TreatBillGoodID}
    </update>

    <update id="updateSavingCardRemark">
        UPDATE TB_TreatBillSavingCard
        SET Remark = #{Remark}
        WHERE ID = #{TreatBillGoodID}
    </update>
</mapper>