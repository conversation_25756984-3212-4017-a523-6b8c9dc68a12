<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.ibeauty.TreatProductCategoryCommissionMapper">

    <select id="treatProductCategoryCommission"
            resultType="com.zhelian.model.ibeauty.form.output.TreatProductCategoryOutputForm">
        SELECT tsp.EntityID,
               ty.EntityName,
               tsp.PayRate * 100         PayRate,
               tsp.PayFixed,
               tsp.CardRate * 100        CardRate,
               tsp.CardFixed,
               tsp.CardLargessRate * 100 CardLargessRate,
               tsp.CardLargessFixed,
               tsp.LargessRate * 100     LargessRate,
               tsp.LargessFixed,
               tsp.SpecialBenefit
        FROM TB_TreatProductCommissionScheme tsp WITH(NOLOCK)
      LEFT JOIN   TK_Entity  ty
        WITH (NOLOCK)
        ON ty.ID=tsp.EntityID
        WHERE tsp.EntityID=#{EntityID}
    </select>

    <select id="allTreatProductCategoryCommission"
            resultType="com.zhelian.model.ibeauty.form.output.TreatProductCategoryCommissionOutputForm">
        SELECT tee.ID                    CategoryID,
               tee.ParentID,
               tee.Name                  CategoryName,
               tsp.PayRate * 100         PayRate,
               tsp.PayFixed,
               tsp.CardRate * 100        CardRate,
               tsp.CardFixed,
               tsp.CardLargessRate * 100 CardLargessRate,
               tsp.CardLargessFixed,
               tsp.LargessRate * 100     LargessRate,
               tsp.LargessFixed,
               tsp.SpecialBenefit
        FROM TE_ProductCategory tee WITH(NOLOCK)
       LEFT JOIN   TB_TreatProductCategoryCommission  tsp
        WITH (NOLOCK)
        ON tsp.ProductCategoryID=tee.ID AND tsp.EntityID=#{EntityID}
        WHERE tee.Active=1
        ORDER BY tee.Sequence desc
    </select>


    <update id="updateTreatProductCommissionScheme"
            parameterType="com.zhelian.model.ibeauty.entity.TreatProductCommissionSchemeEntity">
        UPDATE TB_TreatProductCommissionScheme
        SET PayRate=#{PayRate}/100
                , PayFixed=#{PayFixed}
                , CardRate=#{CardRate}/100
                , CardFixed=#{CardFixed}
                , CardLargessRate=#{CardLargessRate}/100
                , CardLargessFixed=#{CardLargessFixed}
                , LargessRate=#{LargessRate}/100
                , LargessFixed=#{LargessFixed}
                , SpecialBenefit=#{SpecialBenefit}
        WHERE EntityID=#{EntityID}
    </update>

    <delete id="deleteTreatProductCategoryCommissionEntity">
        DELETE
        FROM TB_TreatProductCategoryCommission
        WHERE EntityID = #{EntityID}
    </delete>

    <insert id="insertTreatProductCategoryCommissionEntity" parameterType="java.util.List">
        INSERT INTO TB_TreatProductCategoryCommission (
        EntityID,
        ProductCategoryID,
        PayRate,
        PayFixed,
        CardRate,
        CardFixed,
        CardLargessRate,
        CardLargessFixed,
        LargessRate,
        LargessFixed,
        SpecialBenefit
        ) VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.EntityID},
            #{item.ProductCategoryID},
            cast(#{item.PayRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.PayFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.CardRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.CardFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.CardLargessRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.CardLargessFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.LargessRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.LargessFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.SpecialBenefit,jdbcType=DECIMAL} as decimal(18,2))
            )
        </foreach>
    </insert>

</mapper>