<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.ibeauty.SalePackageCardCommissionMapper">

    <select id="allSalePackageCardCommission"
            resultType="com.zhelian.model.ibeauty.form.output.SalePackageCardCommissionOutputForm">
        SELECT tb.ID                           PackageCardID,
               tb.Name                         PackageCardName,
               tsp.PayRate * 100               PayRate,
               tsp.PayFixed,
               tsp.SavingCardRate * 100        SavingCardRate,
               tsp.SavingCardFixed,
               tsp.SavingCardLargessRate * 100 SavingCardLargessRate,
               tsp.SavingCardLargessFixed,
               tsp.SpecialBenefit
        FROM TB_PackageCard tb WITH(NOLOCK)
           LEFT JOIN TB_SalePackageCardCommission  tsp
        WITH (NOLOCK)
        ON tsp.PackageCardID=tb.ID AND tsp.EntityID=#{EntityID}
        WHERE tb.IsAllowSell=1 AND tb.PackageCardCategoryID=#{CategoryID}
    </select>

    <delete id="deleteSalePackageCardCommission">
        DELETE FROM TB_SalePackageCardCommission WHERE EntityID=#{EntityID}
        AND PackageCardID IN
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>

    <insert id="insertSalePackageCardCommission" parameterType="java.util.List">
        INSERT INTO TB_SalePackageCardCommission (
        EntityID,
        PackageCardID,
        PayRate,
        PayFixed,
        SavingCardRate,
        SavingCardFixed,
        SavingCardLargessRate,
        SavingCardLargessFixed,
        SpecialBenefit
        ) VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.EntityID},
            #{item.PackageCardID},
            cast(#{item.PayRate,jdbcType=DECIMAL} /100 as decimal(5,4)),
            cast(#{item.PayFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.SavingCardRate,jdbcType=DECIMAL} /100 as decimal(5,4)),
            cast(#{item.SavingCardFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.SavingCardLargessRate,jdbcType=DECIMAL} /100 as decimal(5,4)),
            cast(#{item.SavingCardLargessFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.SpecialBenefit,jdbcType=DECIMAL} as decimal(18,2))
            )
        </foreach>

    </insert>

    <delete id="deleteSalePackageCardCommissionByEntityID">
        DELETE
        FROM TB_SalePackageCardCommission
        WHERE EntityID = #{EntityID}
    </delete>

    <delete id="deleteSalePackageCardCommissionByEntityIDAndCategory">
        DELETE
        TB_SalePackageCardCommission FROM  TB_SalePackageCardCommission  tsp
        INNER JOIN TB_PackageCard   tp  on  tsp.PackageCardID=tp.ID
        WHERE tsp.EntityID=
        #{EntityID}
        AND
        tp
        .
        PackageCardCategoryID
        =
        #{CategoryID}

    </delete>

</mapper>