<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.ibeauty.SalePackageCardTimeCardCategoryHandlerCommissionMapper">

    <resultMap id="TimeCardCategoryHandlerCommission"
               type="com.zhelian.model.ibeauty.form.output.SaleTimeCardCategoryHandlerCommissionOutputForm">
        <id column="EntityID" property="EntityID" jdbcType="INTEGER"/>
        <id column="SaleHandlerID" property="ID" jdbcType="INTEGER"/>
        <result column="SaleHandlerName" property="Name" jdbcType="VARCHAR"/>
        <result column="PayRate" property="PayRate" jdbcType="DECIMAL"/>
        <result column="PayFixed" property="PayFixed" jdbcType="DECIMAL"/>
        <result column="SavingCardRate" property="SavingCardRate" jdbcType="DECIMAL"/>
        <result column="SavingCardFixed" property="SavingCardFixed" jdbcType="DECIMAL"/>
        <result column="SavingCardLargessRate" property="SavingCardLargessRate" jdbcType="DECIMAL"/>
        <result column="SavingCardLargessFixed" property="SavingCardLargessFixed" jdbcType="DECIMAL"/>
        <result column="SpecialBenefit" property="SpecialBenefit" jdbcType="DECIMAL"/>

        <collection property="HandlerJobTypeCommission"
                    ofType="com.zhelian.model.ibeauty.form.output.SaleTimeCardHandlerJobTypeCommissionOutputForm">
            <id column="JobTypeID" property="JobTypeID" jdbcType="INTEGER"/>
            <result column="HandlerJobTypeID" property="ID" jdbcType="VARCHAR"/>
            <result column="JobName" property="Name" jdbcType="VARCHAR"/>
            <result column="JobTypePayRate" property="PayRate" jdbcType="DECIMAL"/>
            <result column="JobTypePayFixed" property="PayFixed" jdbcType="DECIMAL"/>
            <result column="JobTypeSavingCardRate" property="SavingCardRate" jdbcType="DECIMAL"/>
            <result column="JobTypeSavingCardFixed" property="SavingCardFixed" jdbcType="DECIMAL"/>
            <result column="JobTypeSavingCardLargessRate" property="SavingCardLargessRate" jdbcType="DECIMAL"/>
            <result column="JobTypeSavingCardLargessFixed" property="SavingCardLargessFixed" jdbcType="DECIMAL"/>
            <result column="JobTypeSpecialBenefit" property="SpecialBenefit" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>
    <select id="allSalePackageCardTimeCardCategoryHandlerCommission" resultMap="TimeCardCategoryHandlerCommission">
        SELECT tsh.EntityID,
               tsh.ID                                                           SaleHandlerID,
               tsh.Name                                                         SaleHandlerName,
               tsp.PayRate * 100                                                PayRate,
               tsp.PayFixed,
               tsp.SavingCardRate * 100                                         SavingCardRate,
               tsp.SavingCardFixed,
               tsp.SavingCardLargessRate * 100                                  SavingCardLargessRate,
               tsp.SavingCardLargessFixed,
               tsp.SpecialBenefit,
               (cast(tsh.ID as varchar) + '-' + cast(tpj.JobTypeID as varchar)) HandlerJobTypeID,
               tpj.JobTypeID,
               tj.JobName,
               tsj.PayRate * 100                                                JobTypePayRate,
               tsj.PayFixed                                                     JobTypePayFixed,
               tsj.SavingCardRate * 100                                         JobTypeSavingCardRate,
               tsj.SavingCardFixed                                              JobTypeSavingCardFixed,
               tsj.SavingCardLargessRate * 100                                  JobTypeSavingCardLargessRate,
               tsj.SavingCardLargessFixed                                       JobTypeSavingCardLargessFixed,
               tsj.SpecialBenefit                                               JobTypeSpecialBenefit
        FROM TB_PackageCardSaleHandler tsh WITH(NOLOCK)
            LEFT JOIN   TB_SalePackageCardTimeCardCategoryHandlerCommission tsp
        WITH (NOLOCK)
        ON tsp.PackageCardSaleHandlerID=tsh.ID AND tsh.EntityID=tsp.EntityID
            AND tsp.TimeCardCategoryID=#{CategoryID}
            LEFT JOIN TB_PackageCardSaleHandlerJobType tpj
        WITH (NOLOCK)
        ON tsh.ID=tpj.PackageCardSaleHandlerID
            LEFT JOIN TB_SalePackageCardTimeCardCategoryHandlerJobTypeCommission tsj
        WITH (NOLOCK)
        ON tsh.EntityID=tsj.EntityID
            AND tsj.PackageCardSaleHandlerID = tsh.ID AND tsj.JobTypeID=tpj.JobTypeID AND tsj.TimeCardCategoryID=#{CategoryID}
            LEFT JOIN TK_JobType tj
        WITH (NOLOCK)
        ON tpj.JobTypeID=tj.ID
        WHERE tsh.EntityID=#{EntityID} AND tsh.Active=1
    </select>

    <delete id="deleteSalePackageCardTimeCardCategoryHandler">
        DELETE
        FROM TB_SalePackageCardTimeCardCategoryHandlerCommission
        WHERE EntityID = #{EntityID}
          AND TimeCardCategoryID = #{CategoryID}
    </delete>

    <delete id="deleteSalePackageCardTimeCardCategoryHandlerJobType">
        DELETE
        FROM TB_SalePackageCardTimeCardCategoryHandlerJobTypeCommission
        WHERE EntityID = #{EntityID}
          AND TimeCardCategoryID = #{CategoryID}
    </delete>

    <insert id="insertSalePackageCardTimeCardCategoryHandlerCommission" parameterType="java.util.List">
        INSERT INTO TB_SalePackageCardTimeCardCategoryHandlerCommission
        (EntityID
        ,TimeCardCategoryID
        ,PackageCardSaleHandlerID
        ,PayRate
        ,PayFixed
        ,SavingCardRate
        ,SavingCardFixed
        ,SavingCardLargessRate
        ,SavingCardLargessFixed
        ,SpecialBenefit)
        VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.EntityID},
            #{item.TimeCardCategoryID},
            #{item.PackageCardSaleHandlerID},
            cast(#{item.PayRate,jdbcType=DECIMAL} /100 as decimal(5,4)),
            cast(#{item.PayFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.SavingCardRate,jdbcType=DECIMAL} /100 as decimal(5,4)),
            cast(#{item.SavingCardFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.SavingCardLargessRate,jdbcType=DECIMAL} /100 as decimal(5,4)),
            cast(#{item.SavingCardLargessFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.SpecialBenefit,jdbcType=DECIMAL} as decimal(18,2))
            )
        </foreach>

    </insert>

    <insert id="insertSalePackageCardTimeCardCategoryHandlerJobTypeCommission" parameterType="java.util.List">
        INSERT INTO TB_SalePackageCardTimeCardCategoryHandlerJobTypeCommission (
        EntityID,
        TimeCardCategoryID,
        PackageCardSaleHandlerID,
        JobTypeID,
        PayRate,
        PayFixed,
        SavingCardRate,
        SavingCardFixed,
        SavingCardLargessRate,
        SavingCardLargessFixed,
        SpecialBenefit
        ) VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.EntityID},
            #{item.TimeCardCategoryID},
            #{item.PackageCardSaleHandlerID},
            #{item.JobTypeID},
            cast(#{item.PayRate,jdbcType=DECIMAL} /100 as decimal(5,4)),
            cast(#{item.PayFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.SavingCardRate,jdbcType=DECIMAL} /100 as decimal(5,4)),
            cast(#{item.SavingCardFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.SavingCardLargessRate,jdbcType=DECIMAL} /100 as decimal(5,4)),
            cast(#{item.SavingCardLargessFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.SpecialBenefit,jdbcType=DECIMAL} as decimal(18,2))
            )
        </foreach>
    </insert>

    <delete id="deleteSalePackageCardTimeCardCategoryHandlerCommission">
        DELETE
        FROM TB_SalePackageCardTimeCardCategoryHandlerCommission
        WHERE EntityID = #{EntityID}
    </delete>

    <delete id="deleteSalePackageCardTimeCardCategoryHandlerJobTypeCommission">
        DELETE
        FROM TB_SalePackageCardTimeCardCategoryHandlerJobTypeCommission
        WHERE EntityID = #{EntityID}
    </delete>
</mapper>