<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.ibeauty.ProjectAccountMapper">
    <insert id="createProjectAccount" parameterType="java.util.List">
        INSERT INTO dbo.TB_ProjectAccount (
        CustomerID,
        EntityID,
        BuyDate,
        ProjectID,
        Price,
        Quantity,
        PayAmount,
        SavingCardDeductionAmount,
        SavingCardDeductionLargessAmount,
        TotalAmount,
        ArrearAmount,
        Balance,
        IsLargess,
        SaleBillProjectID,
        PackageCardAccountID,
        SaleBillPackageCardProjectID,
        PayPerformanceBalanceAmount,
        CardPerformanceBalanceAmount,
        CardLargessPerformanceBalanceAmount,
        LargessPerformanceBalanceAmount,
        Remark,
        Active
        )
        VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.CustomerID},#{item.EntityID},#{item.BuyDate},#{item.ProjectID},#{item.Price},#{item.Quantity},
            #{item.PayAmount},#{item.SavingCardDeductionAmount},#{item.SavingCardDeductionLargessAmount},#{item.TotalAmount},#{item.ArrearAmount},#{item.Balance},#{item.IsLargess},
            #{item.SaleBillProjectID},#{item.PackageCardAccountID},#{item.SaleBillPackageCardProjectID},#{item.PayPerformanceBalanceAmount},#{item.CardPerformanceBalanceAmount},#{item.CardLargessPerformanceBalanceAmount},#{item.LargessPerformanceBalanceAmount},
            #{item.Remark},
            1)
        </foreach>
    </insert>

    <!--项目账户列表-->
    <select id="getProjectAccount" parameterType="com.zhelian.model.ibeauty.form.input.TreatProjectAccountForm"
            resultType="com.zhelian.model.ibeauty.form.output.ProjectAccountOutputForm">
        SELECT
        tpa.ID,
        tpa.ProjectID,
        p.Name,
        p.Alias,
        CASE
        WHEN tpa.TotalAmount / tpa.Quantity &lt;= 0 THEN tpa.Balance - dbo.getRefundBillProjectQuantity(tpa.ID)
        WHEN (tpa.Balance - CEILING(tpa.ArrearAmount*tpa.Quantity / tpa.TotalAmount)) &lt;= 0 THEN 0
        WHEN (tpa.Balance - CEILING(tpa.ArrearAmount*tpa.Quantity / tpa.TotalAmount)) > 0 AND (tpa.Balance -
        CEILING(tpa.ArrearAmount*tpa.Quantity /
        tpa.TotalAmount) - dbo.getRefundBillProjectQuantity(tpa.ID))>0 THEN (tpa.Balance -
        CEILING(tpa.ArrearAmount*tpa.Quantity /
        tpa.TotalAmount) - dbo.getRefundBillProjectQuantity(tpa.ID))
        ELSE 0
        END ValidBalance,
        dbo.getRefundBillProjectQuantity(tpa.ID) RefundBalance,
        tpa.Quantity,
        tpa.TotalAmount,
        tpa.ArrearAmount,
        tpa.Balance,
        CAST(tpa.TotalAmount/tpa.Quantity as decimal(38, 2)) AS Amount,
        tpa.Price,
        ISNULL(tpa.IsLargess, 0) IsLargess,
        CONVERT(VARCHAR(10),tpa.BuyDate,120) BuyDate,
        tpa.Remark
        FROM
        TB_ProjectAccount tpa WITH(NOLOCK)
        LEFT JOIN TB_Project p WITH(NOLOCK) ON p.ID = tpa.ProjectID
        WHERE tpa.Active=1
        AND tpa.CustomerID = #{CustomerID}
        AND p.IsAllowConsume = 1
        AND tpa.Balance &gt; 0 AND tpa.PackageCardAccountID IS NULL
        AND EXISTS (
        SELECT
        1
        FROM
        TB_ProjectConsumeEntity PSE WITH(NOLOCK)
        WHERE
        tpa.ProjectID = PSE.ProjectID
        AND EXISTS ( SELECT 1 FROM GetParentEntities (#{EntityID}) E WHERE E.ID = PSE.EntityID )
        )
        <if test="Name!=null and Name!=''">
            AND (p.Name LIKE '%'+#{Name}+'%' OR p.Alias LIKE '%'+#{Name}+'%'
            OR p.NameFullPinYin LIKE '%'+ #{Name} +'%' OR p.NameAbbrPinYin LIKE '%'+ #{Name} +'%'
            OR p.AliasFullPinYin LIKE '%'+ #{Name} +'%' OR p.AliasAbbrPinYin LIKE '%'+ #{Name} +'%' )
        </if>
        ORDER BY tpa.BuyDate DESC, tpa.ID
    </select>

    <select id="getProjectAccountByAccountID" resultType="com.zhelian.model.ibeauty.entity.ProjectAccountExcendEntity">
        SELECT tpa.*,
               CASE
                   WHEN tpa.TotalAmount / tpa.Quantity &lt;= 0
                       THEN tpa.Balance - dbo.getRefundBillProjectQuantity(tpa.ID)
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) &lt;= 0 THEN 0
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) > 0 AND
                        (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity /
                                               tpa.TotalAmount) - dbo.getRefundBillProjectQuantity(tpa.ID)) > 0 THEN (
                           tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity /
                                                 tpa.TotalAmount) - dbo.getRefundBillProjectQuantity(tpa.ID))
                   ELSE 0
                   END ValidBalance
        FROM TB_ProjectAccount tpa WITH(NOLOCK)
        WHERE tpa.ID = #{AccountID}
    </select>

    <update id="updateProjectAccountBalance" parameterType="com.zhelian.model.ibeauty.entity.ProjectAccountEntity">
        UPDATE TB_ProjectAccount
        <set>
            <if test="Balance != null">Balance = #{Balance},</if>
            <if test="PayPerformanceBalanceAmount != null">PayPerformanceBalanceAmount =
                #{PayPerformanceBalanceAmount},
            </if>
            <if test="CardPerformanceBalanceAmount != null">CardPerformanceBalanceAmount =
                #{CardPerformanceBalanceAmount},
            </if>
            <if test="CardLargessPerformanceBalanceAmount != null">CardLargessPerformanceBalanceAmount =
                #{CardLargessPerformanceBalanceAmount},
            </if>
            <if test="LargessPerformanceBalanceAmount != null">LargessPerformanceBalanceAmount =
                #{LargessPerformanceBalanceAmount},
            </if>
            <if test="ArrearAmount != null">ArrearAmount = #{ArrearAmount},</if>
            <if test="Active != null">Active = #{Active}</if>
        </set>
        WHERE
        ID = #{ID}
    </update>
    <update id="updateProjectAccountActiveFalse">
        UPDATE tpa
        SET tpa.Active = 0 FROM
            TB_ProjectAccount tpa
            INNER JOIN TB_SaleBillProject ts
        ON tpa.SaleBillProjectID= ts.ID
        WHERE
            ts.BillID=#{SaleBillID}
    </update>
    <update id="updatePackageCardProjectAccountActiveFalse">
        UPDATE tpa
        SET tpa.Active = 0 FROM
            TB_ProjectAccount tpa
            INNER JOIN TB_SaleBillPackageCardProject ts
        on tpa.SaleBillPackageCardProjectID=ts.ID
            INNER JOIN TB_SaleBillPackageCard tsb on ts.SaleBillPackageCardID=tsb.ID
        WHERE tsb.BillID=#{SaleBillID}
    </update>

    <!--欠款项目账户列表-->
    <select id="arrearProjectAccount" resultType="com.zhelian.model.ibeauty.form.output.ProjectAccountOutputForm">
        SELECT
        tpa.ID,
        tpa.ProjectID,
        p.Name,
        p.Alias,
        CASE
        WHEN tpa.TotalAmount / tpa.Quantity &lt;= 0 THEN tpa.Balance - dbo.getRefundBillProjectQuantity(tpa.ID)
        WHEN (tpa.Balance - CEILING(tpa.ArrearAmount*tpa.Quantity / tpa.TotalAmount)) &lt;= 0 THEN 0
        WHEN (tpa.Balance - CEILING(tpa.ArrearAmount*tpa.Quantity / tpa.TotalAmount)) > 0 AND (tpa.Balance -
        CEILING(tpa.ArrearAmount*tpa.Quantity /
        tpa.TotalAmount) - dbo.getRefundBillProjectQuantity(tpa.ID))>0 THEN (tpa.Balance -
        CEILING(tpa.ArrearAmount*tpa.Quantity /
        tpa.TotalAmount) - dbo.getRefundBillProjectQuantity(tpa.ID))
        ELSE 0
        END ValidBalance,
        dbo.getRefundBillProjectQuantity(tpa.ID) RefundBalance,
        tpa.Quantity,
        tpa.ArrearAmount,
        tpa.TotalAmount,
        tpa.Balance,
        CAST(tpa.TotalAmount/tpa.Quantity as decimal(38, 2)) AS Amount,
        tpa.Price,
        ISNULL(tpa.IsLargess, 0) IsLargess,
        CONVERT(VARCHAR(10),tpa.BuyDate,120) BuyDate
        FROM
        TB_ProjectAccount tpa WITH(NOLOCK)
        LEFT JOIN TB_Project p WITH(NOLOCK) ON p.ID = tpa.ProjectID
        WHERE tpa.Active=1
        AND (tpa.EntityID=#{EntityID}
        OR EXISTS (SELECT 1 FROM TB_CustomerEntity tce WITH(NOLOCK) WHERE tce.CustomerID=tpa.CustomerID AND tce.IsBelongEntity=1 AND tce.EntityID = #{EntityID}))
        AND tpa.CustomerID = #{CustomerID}
        AND tpa.ArrearAmount &gt; 0 AND tpa.PackageCardAccountID IS NULL
        <if test="Name!=null and Name!=''">
            AND (p.Name LIKE '%${Name}%' OR p.Alias LIKE '%${Name}%'
            OR p.NameFullPinYin LIKE '%'+ #{Name} +'%' OR p.NameAbbrPinYin LIKE '%'+ #{Name} +'%'
            OR p.AliasFullPinYin LIKE '%'+ #{Name} +'%' OR p.AliasAbbrPinYin LIKE '%'+ #{Name} +'%' )
        </if>
        ORDER BY tpa.BuyDate DESC, tpa.ID
    </select>

    <update id="updateArrearProjectAccount" parameterType="com.zhelian.model.ibeauty.entity.ProjectAccountEntity">
        UPDATE TB_ProjectAccount
        SET ArrearAmount = #{ArrearAmount},
            PayAmount = #{PayAmount},
            PayPerformanceBalanceAmount = #{PayPerformanceBalanceAmount}
        WHERE
            ID = #{ID}
    </update>

    <!--转账项目账户列表-->
    <select id="getTranserProjectAccount" resultType="com.zhelian.model.ibeauty.form.output.ProjectAccountOutputForm">
        SELECT
        tpa.ID,
        tpa.ProjectID,
        p.Name,
        p.Alias,
        CASE
        WHEN tpa.TotalAmount / tpa.Quantity &lt;= 0 THEN tpa.Balance - dbo.getRefundBillProjectQuantity(tpa.ID)
        WHEN (tpa.Balance - CEILING(tpa.ArrearAmount*tpa.Quantity / tpa.TotalAmount)) &lt;= 0 THEN 0
        WHEN (tpa.Balance - CEILING(tpa.ArrearAmount*tpa.Quantity / tpa.TotalAmount)) > 0 AND (tpa.Balance -
        CEILING(tpa.ArrearAmount*tpa.Quantity /
        tpa.TotalAmount) - dbo.getRefundBillProjectQuantity(tpa.ID))>0 THEN (tpa.Balance -
        CEILING(tpa.ArrearAmount*tpa.Quantity /
        tpa.TotalAmount) - dbo.getRefundBillProjectQuantity(tpa.ID))
        ELSE 0
        END ValidBalance,
        dbo.getRefundBillProjectQuantity(tpa.ID) RefundBalance,
        tpa.Quantity,
        tpa.ArrearAmount,
        tpa.TotalAmount,
        CAST(tpa.TotalAmount/tpa.Quantity as decimal(38, 2)) AS Amount,
        tpa.Balance,
        tpa.Price,
        ISNULL(tpa.IsLargess, 0) IsLargess,
        CONVERT(VARCHAR(10),tpa.BuyDate,120) BuyDate,
        tpa.PayPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount+tpa.CardLargessPerformanceBalanceAmount
        PerformanceBalanceAmount,
        tpa.Remark
        FROM
        TB_ProjectAccount tpa WITH(NOLOCK)
        LEFT JOIN TB_Project p WITH(NOLOCK) ON p.ID = tpa.ProjectID
        WHERE
        tpa.Active=1
        AND tpa.CustomerID = #{CustomerID}
        AND tpa.Balance &gt; 0 AND tpa.PackageCardAccountID IS NULL
        <if test="Name!=null and Name!=''">
            AND (p.Name LIKE '%${Name}%' OR p.Alias LIKE '%${Name}%'
            OR p.NameFullPinYin LIKE '%'+ #{Name} +'%' OR p.NameAbbrPinYin LIKE '%'+ #{Name} +'%'
            OR p.AliasFullPinYin LIKE '%'+ #{Name} +'%' OR p.AliasAbbrPinYin LIKE '%'+ #{Name} +'%' )
        </if>
        ORDER BY tpa.BuyDate DESC, tpa.ID
    </select>

    <select id="getRefundProjectAccount" resultType="com.zhelian.model.ibeauty.form.output.ProjectAccountOutputForm">
        SELECT
        tpa.ID,
        tpa.ProjectID,
        p.Name,
        p.Alias,
        CASE
        WHEN tpa.TotalAmount / tpa.Quantity &lt;= 0 THEN tpa.Balance - dbo.getRefundBillProjectQuantity(tpa.ID)
        WHEN (tpa.Balance - CEILING(tpa.ArrearAmount*tpa.Quantity / tpa.TotalAmount)) &lt;= 0 THEN 0
        WHEN (tpa.Balance - CEILING(tpa.ArrearAmount*tpa.Quantity / tpa.TotalAmount)) > 0 AND (tpa.Balance -
        CEILING(tpa.ArrearAmount*tpa.Quantity /
        tpa.TotalAmount) - dbo.getRefundBillProjectQuantity(tpa.ID))>0 THEN (tpa.Balance -
        CEILING(tpa.ArrearAmount*tpa.Quantity /
        tpa.TotalAmount) - dbo.getRefundBillProjectQuantity(tpa.ID))
        ELSE 0
        END ValidBalance,
        dbo.getRefundBillProjectQuantity(tpa.ID) RefundBalance,
        tpa.Quantity,
        tpa.ArrearAmount,
        tpa.TotalAmount,
        CAST(tpa.TotalAmount/tpa.Quantity as decimal(38, 2)) AS Amount,
        tpa.Balance,
        tpa.Price,
        ISNULL(tpa.IsLargess, 0) IsLargess,
        CONVERT(VARCHAR(10),tpa.BuyDate,120) BuyDate,
        tpa.PayPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount+tpa.CardLargessPerformanceBalanceAmount
        PerformanceBalanceAmount,
        tpa.Remark
        FROM
        TB_ProjectAccount tpa WITH(NOLOCK)
        LEFT JOIN TB_Project p WITH(NOLOCK) ON p.ID = tpa.ProjectID
        WHERE
        tpa.Active=1
        AND tpa.CustomerID = #{CustomerID}
        AND tpa.Channel != '20'
        AND tpa.Balance &gt; 0 AND tpa.PackageCardAccountID IS NULL
        <if test="Name!=null and Name!=''">
            AND (p.Name LIKE '%${Name}%' OR p.Alias LIKE '%${Name}%'
            OR p.NameFullPinYin LIKE '%'+ #{Name} +'%' OR p.NameAbbrPinYin LIKE '%'+ #{Name} +'%'
            OR p.AliasFullPinYin LIKE '%'+ #{Name} +'%' OR p.AliasAbbrPinYin LIKE '%'+ #{Name} +'%' )
        </if>
        ORDER BY tpa.BuyDate DESC, tpa.ID
    </select>
    <insert id="createTranserProjectAccount" parameterType="com.zhelian.model.ibeauty.entity.ProjectAccountEntity"
            useGeneratedKeys="true" keyProperty="ID">
        INSERT INTO dbo.TB_ProjectAccount (CustomerID,
            EntityID,
            BuyDate,
            ProjectID,
            Price,
            Quantity,
            PayAmount,
            SavingCardDeductionAmount,
            SavingCardDeductionLargessAmount,
            TotalAmount,
            ArrearAmount,
            Balance,
            IsLargess,
            SaleBillProjectID,
            PackageCardAccountID,
            SaleBillPackageCardProjectID,
            PayPerformanceBalanceAmount,
            CardPerformanceBalanceAmount,
            CardLargessPerformanceBalanceAmount,
            LargessPerformanceBalanceAmount,
            Channel,
        Remark)
        VALUES
            (#{CustomerID}, #{EntityID}, #{BuyDate}, #{ProjectID}, #{Price}, #{Quantity},
            #{PayAmount}, #{SavingCardDeductionAmount}, #{SavingCardDeductionLargessAmount}, #{TotalAmount}, #{ArrearAmount}, #{Balance}, #{IsLargess},
            #{SaleBillProjectID}, #{PackageCardAccountID}, #{SaleBillPackageCardProjectID}, #{PayPerformanceBalanceAmount},
            #{CardPerformanceBalanceAmount}, #{CardLargessPerformanceBalanceAmount}, #{LargessPerformanceBalanceAmount}, #{Channel}, #{Remark})
    </insert>

    <select id="getProjectAccountEntity"
            resultType="com.zhelian.model.ibeauty.form.output.TranserProjectAccountOutputForm">
        SELECT tpa.ID,
               tpa.ProjectID,
               p.Name,
               p.Alias,
               CASE
                   WHEN tpa.TotalAmount / tpa.Quantity &lt;= 0
                       THEN tpa.Balance - dbo.getRefundBillProjectQuantity(tpa.ID)
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) &lt;= 0 THEN 0
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) > 0 THEN (
                           tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity /
                                                 tpa.TotalAmount) - dbo.getRefundBillProjectQuantity(tpa.ID))
                   END                                                   ValidBalance,
               tpa.TotalAmount,
               tpa.PayAmount,
               tpa.SavingCardDeductionAmount,
               tpa.SavingCardDeductionLargessAmount,
               dbo.getRefundBillProjectQuantity(tpa.ID)                  RefundBalance,
               tpa.Quantity,
               tpa.ArrearAmount,
               tpa.Balance,
               CAST(tpa.TotalAmount / tpa.Quantity as decimal(38, 2)) AS Amount,
               tpa.Remark,
               tpa.Price,
               tpa.PayPerformanceBalanceAmount,
               tpa.CardPerformanceBalanceAmount,
               tpa.CardLargessPerformanceBalanceAmount,
               tpa.LargessPerformanceBalanceAmount,
               ISNULL(tpa.IsLargess, 0)                                  IsLargess,
               CONVERT(VARCHAR (10), tpa.BuyDate, 120)                   BuyDate
        FROM TB_ProjectAccount tpa WITH(NOLOCK)
                 LEFT JOIN TB_Project p WITH(NOLOCK)
        ON p.ID = tpa.ProjectID
        WHERE
            tpa.ID = #{ID}
    </select>


    <select id="customerProjectAccount"
            resultType="com.zhelian.model.ibeauty.form.output.CustomerProjectAccountOutputForm">
        SELECT
        tpa.ID,
        tpa.ProjectID,
        p.Name,
        p.Alias,
        tpa.TotalAmount,
        tpa.Quantity,
        tpa.ArrearAmount,
        tpa.PayAmount,
        tpa.SavingCardDeductionAmount,
        tpa.SavingCardDeductionLargessAmount,
        tpa.PayPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount BalanceAmount,
        tpa.Balance,
        ISNULL(tpa.IsLargess, 0) IsLargess,
        CONVERT(VARCHAR(10),tpa.BuyDate,120) BuyDate,
        e.EntityName,
        CASE tpa.Channel WHEN '10' THEN '购买' WHEN '20' THEN '转账' WHEN '30' THEN '存量导入' END ChannelName
        FROM
        TB_ProjectAccount tpa WITH(NOLOCK)
        LEFT JOIN TB_Project p WITH(NOLOCK) ON p.ID = tpa.ProjectID
        LEFT JOIN TK_Entity e WITH(NOLOCK) ON e.ID = tpa.EntityID
        <where>
            tpa.CustomerID = #{CustomerID}
            AND tpa.PackageCardAccountID IS NULL
            <choose>
                <when test="Active">
                    AND tpa.Active=1 AND tpa.Balance > 0
                </when>
                <otherwise>
                    AND (tpa.Active=0 OR tpa.Balance=0)
                </otherwise>
            </choose>
            <if test="Name != null and Name != ''">
                AND ( tpa.ProjectID LIKE '%'+ #{Name} +'%' OR p.Name LIKE '%'+ #{Name} +'%' OR p.Alias LIKE '%'+ #{Name}
                +'%')
            </if>
        </where>
        ORDER BY tpa.BuyDate DESC, tpa.ID
    </select>

    <select id="customerProjectAccountSum"
            resultType="com.zhelian.model.ibeauty.form.output.CustomerProjectAccountSumOutputForm">
        SELECT
        ISNULL(SUM(tpa.TotalAmount),0) TotalAmount,
        ISNULL(SUM(tpa.PayAmount),0) PayAmount,
        ISNULL(SUM(tpa.SavingCardDeductionAmount),0) SavingCardDeductionAmount,
        ISNULL(SUM(tpa.SavingCardDeductionLargessAmount),0) SavingCardDeductionLargessAmount,
        ISNULL(SUM(tpa.Quantity),0) Quantity,
        ISNULL(SUM(tpa.ArrearAmount),0) ArrearAmount,
        ISNULL(SUM(tpa.Balance),0) Balance,
        ISNULL(SUM(tpa.PayPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount),0) BalanceAmount
        FROM
        TB_ProjectAccount tpa WITH(NOLOCK)
        LEFT JOIN TB_Project p WITH(NOLOCK) ON p.ID = tpa.ProjectID
        <where>
            tpa.CustomerID = #{CustomerID}
            AND tpa.PackageCardAccountID IS NULL
            <choose>
                <when test="Active">
                    AND tpa.Active=1 AND tpa.Balance > 0
                </when>
                <otherwise>
                    AND (tpa.Active=0 OR tpa.Balance=0)
                </otherwise>
            </choose>
            <if test="Name != null and Name != ''">
                AND ( tpa.ProjectID LIKE '%'+ #{Name} +'%' OR p.Name LIKE '%'+ #{Name} +'%' OR p.Alias LIKE '%'+ #{Name}
                +'%')
            </if>
        </where>
    </select>

    <update id="updateCustomerProjectAccount" parameterType="com.zhelian.model.ibeauty.entity.ProjectAccountEntity">
        UPDATE TB_ProjectAccount
        SET Balance = #{Balance},
            ArrearAmount = #{ArrearAmount},
            PayPerformanceBalanceAmount = #{PayPerformanceBalanceAmount},
            CardPerformanceBalanceAmount = #{CardPerformanceBalanceAmount},
            CardLargessPerformanceBalanceAmount = #{CardLargessPerformanceBalanceAmount}
        WHERE
            ID = #{ID}
    </update>

    <select id="getProjectAccountBySaleBillID" resultType="com.zhelian.model.ibeauty.entity.ProjectAccountEntity">
        SELECT a1.*
        FROM TB_ProjectAccount a1 WITH(NOLOCK)
                 LEFT JOIN TB_SaleBillProject a2 WITH(NOLOCK) ON a1.SaleBillProjectID = a2.ID
                 LEFT JOIN TB_SaleBill a3 WITH(NOLOCK) ON a2.BillID = a3.ID
        WHERE a3.ID = #{SaleBillID}
    </select>

    <!--转账项目账户列表-->
    <select id="customerMiniprogramProjectAccount"
            resultType="com.zhelian.model.ibeauty.form.output.ProjectAccountOutputForm">
        SELECT
        tpa.ID,
        tpa.ProjectID,
        p.Name,
        p.Alias,
        CASE
        WHEN tpa.TotalAmount / tpa.Quantity &lt;= 0 THEN tpa.Balance - dbo.getRefundBillProjectQuantity(tpa.ID)
        WHEN (tpa.Balance - CEILING(tpa.ArrearAmount*tpa.Quantity / tpa.TotalAmount)) &lt;= 0 THEN 0
        WHEN (tpa.Balance - CEILING(tpa.ArrearAmount*tpa.Quantity / tpa.TotalAmount)) > 0 AND (tpa.Balance -
        CEILING(tpa.ArrearAmount*tpa.Quantity /
        tpa.TotalAmount) - dbo.getRefundBillProjectQuantity(tpa.ID))>0 THEN (tpa.Balance -
        CEILING(tpa.ArrearAmount*tpa.Quantity /
        tpa.TotalAmount) - dbo.getRefundBillProjectQuantity(tpa.ID))
        ELSE 0
        END ValidBalance,
        dbo.getRefundBillProjectQuantity(tpa.ID) RefundBalance,
        tpa.Quantity,
        tpa.ArrearAmount,
        tpa.TotalAmount,
        tpa.PayAmount,
        tpa.SavingCardDeductionAmount,
        tpa.SavingCardDeductionLargessAmount,
        CAST(tpa.TotalAmount/tpa.Quantity as decimal(38, 2)) AS Amount,
        tpa.Balance,
        tpa.Price,
        tpa.PayPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount BalanceAmount,
        ISNULL(tpa.IsLargess, 0) IsLargess,
        CONVERT(VARCHAR(10),tpa.BuyDate,120) BuyDate,
        tpa.PayPerformanceBalanceAmount+tpa.CardPerformanceBalanceAmount+tpa.CardLargessPerformanceBalanceAmount
        PerformanceBalanceAmount
        FROM
        TB_ProjectAccount tpa WITH(NOLOCK)
        LEFT JOIN TB_Project p WITH(NOLOCK) ON p.ID = tpa.ProjectID
        <where>
            tpa.CustomerID = #{CustomerID}
            AND tpa.PackageCardAccountID IS NULL
            <choose>
                <when test="Active">
                    AND tpa.Active=1 AND tpa.Balance > 0
                </when>
                <otherwise>
                    AND (tpa.Active=0 OR tpa.Balance=0)
                </otherwise>
            </choose>
            <if test="Name!=null and Name!=''">
                AND (p.Name LIKE '%${Name}%' OR p.Alias LIKE '%${Name}%'
                OR p.NameFullPinYin LIKE '%'+ #{Name} +'%' OR p.NameAbbrPinYin LIKE '%'+ #{Name} +'%'
                OR p.AliasFullPinYin LIKE '%'+ #{Name} +'%' OR p.AliasAbbrPinYin LIKE '%'+ #{Name} +'%' )
            </if>
        </where>
        ORDER BY tpa.BuyDate DESC, tpa.ID
    </select>

    <select id="customerMicromallProjectAccount"
            resultType="com.zhelian.model.micromall.form.output.MicromallProjectAccountOutputForm">
        SELECT
        tpa.ID AccountID,
        tpa.ProjectID,
        p.Name,
        p.Alias,
        tpa.Quantity,
        CASE
        WHEN tpa.TotalAmount / tpa.Quantity &lt;= 0 THEN tpa.Balance - dbo.getRefundBillProjectQuantity(tpa.ID)
        WHEN (tpa.Balance - CEILING(tpa.ArrearAmount*tpa.Quantity / tpa.TotalAmount)) &lt;= 0 THEN 0
        WHEN (tpa.Balance - CEILING(tpa.ArrearAmount*tpa.Quantity / tpa.TotalAmount)) > 0 THEN (tpa.Balance -
        CEILING(tpa.ArrearAmount*tpa.Quantity /
        tpa.TotalAmount) - dbo.getRefundBillProjectQuantity(tpa.ID))
        END ValidBalance,
        tpa.Balance,
        tpa.IsLargess,
        dbo.getRefundBillProjectQuantity(tpa.ID) RefundBalance,
        CAST(tpa.TotalAmount/tpa.Quantity as decimal(38, 2)) AS Amount,
        tpa.ArrearAmount,
        CONVERT(VARCHAR(10),tpa.BuyDate,120) BuyDate,
        CASE tpa.Channel WHEN '10' THEN '购买' WHEN '20' THEN '转账' WHEN '30' THEN '存量导入' WHEN '40' THEN '线上商城' END
        ChannelName
        FROM
        TB_ProjectAccount tpa WITH(NOLOCK)
        LEFT JOIN TB_Project p WITH(NOLOCK) ON p.ID = tpa.ProjectID
        LEFT JOIN TK_Entity e WITH(NOLOCK) ON e.ID = tpa.EntityID
        <where>
            tpa.CustomerID = #{CustomerID}
            AND tpa.PackageCardAccountID IS NULL
            <if test="Active != null ">
                <choose>
                    <when test="Active">
                        AND tpa.Active=1 AND tpa.Balance > 0
                    </when>
                    <otherwise>
                        AND (tpa.Active=0 OR tpa.Balance=0)
                    </otherwise>
                </choose>
            </if>
            <if test="Name != null and Name != ''">
                AND ( tpa.ProjectID LIKE '%'+ #{Name} +'%' OR p.Name LIKE '%'+ #{Name} +'%' OR p.Alias LIKE '%'+ #{Name}
                +'%')
            </if>
        </where>
        ORDER BY tpa.BuyDate DESC, tpa.ID
    </select>

    <select id="customerMicromallProjectAccountEntity" resultMap="onlineEntityListMap">
        SELECT y.ID,
               y.EntityName,
               y.AddressDetail,
               convert(varchar (8), my.BusinessStartTime) BusinessStartTime,
               convert(varchar (8), my.BusinessEndTime)   BusinessEndTime,
               y.Longitude,
               y.Latitude
        FROM TB_ProjectConsumeEntity pc WITH (NOLOCK)
            LEFT JOIN TK_Entity y
        WITH (NOLOCK)
        ON pc.EntityID = y.ID
            LEFT JOIN TM_Entity my
        WITH (NOLOCK)
        ON my.EntityID = y.ID
        WHERE
            pc.ProjectID =#{ID}
          AND y.Active=1
          AND y.IsStore=1
    </select>

    <resultMap type="com.zhelian.model.micromall.form.output.ApplyEntityOutputForm" id="onlineEntityListMap">
        <id column="ID" jdbcType="INTEGER" property="ID"/>
        <result column="EntityName" jdbcType="VARCHAR" property="EntityName"/>
        <result column="AddressDetail" jdbcType="VARCHAR" property="AddressDetail"/>
        <result column="BusinessStartTime" jdbcType="VARCHAR" property="BusinessStartTime"/>
        <result column="BusinessEndTime" jdbcType="VARCHAR" property="BusinessEndTime"/>
        <result column="Longitude" jdbcType="DECIMAL" property="Longitude"/>
        <result column="Latitude" jdbcType="DECIMAL" property="Latitude"/>
        <collection property="EntityBusinessDate"
                    ofType="com.zhelian.model.khs.form.output.EntityBusinessDateOutputForm"
                    select="getOnlineEntityBusinessDate" column="ID">
        </collection>
    </resultMap>

    <select id="getOnlineEntityBusinessDate" parameterType="Integer"
            resultType="com.zhelian.model.khs.form.output.EntityBusinessDateOutputForm">
        SELECT Data
        FROM TM_EntityBusinessDate
        WHERE EntityID = #{ID}
    </select>

    <update id="updateProjectRemark">
        UPDATE TB_ProjectAccount
        SET Remark = #{Remark}
        WHERE SaleBillProjectID = #{SaleBillGoodID}
    </update>

    <update id="updatePackageCardProjectRemark">
        UPDATE TB_ProjectAccount
        SET Remark = #{Remark}
        WHERE SaleBillPackageCardProjectID = #{SaleBillGoodID}
    </update>

    <update id="updateTransferProjectAccountActiveFalse">
        UPDATE tpa
        SET tpa.Active = 0 FROM
            TB_ProjectAccount tpa
            INNER JOIN TB_TransferBillProjectAccount ts
        ON tpa.ID= ts.TransferInProjectAccountID
        WHERE
            ts.TransferBillID=#{ID}
    </update>

    <update id="updateProjectBillDate">
        UPDATE R
        SET R.BuyDate = #{BillDate} FROM TB_ProjectAccount  R
        INNER JOIN TB_SaleBillProject P
        ON R.SaleBillProjectID = P.ID
        WHERE P.BillID = #{SaleBillID}
    </update>

    <update id="updatePackageCardProjectBillDate">
        UPDATE R
        SET R.BuyDate = #{BillDate} FROM TB_ProjectAccount  R
        INNER JOIN TB_SaleBillPackageCardProject P
        ON R.SaleBillPackageCardProjectID = P.ID
            INNER JOIN TB_SaleBillPackageCard S ON P.SaleBillPackageCardID = S.ID
        WHERE S.BillID = #{SaleBillID}
    </update>

    <select id="getProjectAccountBySaleBillProjectID" resultType="com.zhelian.model.ibeauty.entity.ProjectAccountExcendEntity">
        SELECT tpa.*,
               CASE
                   WHEN tpa.TotalAmount / tpa.Quantity &lt;= 0
                       THEN tpa.Balance - dbo.getRefundBillProjectQuantity(tpa.ID)
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) &lt;= 0 THEN 0
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) > 0 AND
                        (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity /
                                               tpa.TotalAmount) - dbo.getRefundBillProjectQuantity(tpa.ID)) > 0 THEN (
                           tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity /
                                                 tpa.TotalAmount) - dbo.getRefundBillProjectQuantity(tpa.ID))
                   ELSE 0
                   END ValidBalance
        FROM TB_ProjectAccount tpa
        WHERE
            EXISTS (
                SELECT
                    1
                FROM
                    TB_SaleBillProject tsbp WITH(NOLOCK)
                WHERE tpa.ID = tsbp.ProjectAccountID AND tsbp.ID = #{SaleBillProjectID}
            )
    </select>

    <update id="updateProjectAccount" parameterType="com.zhelian.model.ibeauty.entity.ProjectAccountEntity">
        UPDATE TB_ProjectAccount
        SET PayAmount = #{PayAmount},
            SavingCardDeductionAmount = #{SavingCardDeductionAmount},
            SavingCardDeductionLargessAmount = #{SavingCardDeductionLargessAmount},
            PayPerformanceBalanceAmount = #{PayPerformanceBalanceAmount},
            CardPerformanceBalanceAmount = #{CardPerformanceBalanceAmount},
            CardLargessPerformanceBalanceAmount = #{CardLargessPerformanceBalanceAmount}
        WHERE
            ID = #{ID}
    </update>

    <update id="updateProjectAccountArrear" parameterType="com.zhelian.model.ibeauty.entity.ProjectAccountEntity">
        UPDATE TB_ProjectAccount
        SET ArrearAmount = #{ArrearAmount},
            PayAmount = #{PayAmount},
            SavingCardDeductionAmount = #{SavingCardDeductionAmount},
            SavingCardDeductionLargessAmount = #{SavingCardDeductionLargessAmount},
            PayPerformanceBalanceAmount = #{PayPerformanceBalanceAmount},
            CardPerformanceBalanceAmount = #{CardPerformanceBalanceAmount},
            CardLargessPerformanceBalanceAmount = #{CardLargessPerformanceBalanceAmount}
        WHERE
            ID = #{ID}
    </update>

    <select id="getProjectAccountBySaleBillPackageCardProjectID" resultType="com.zhelian.model.ibeauty.entity.ProjectAccountExcendEntity">
        SELECT tpa.*,
               CASE
                   WHEN tpa.TotalAmount / tpa.Quantity &lt;= 0
                       THEN tpa.Balance - dbo.getRefundBillProjectQuantity(tpa.ID)
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) &lt;= 0 THEN 0
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) > 0 AND
                        (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity /
                                               tpa.TotalAmount) - dbo.getRefundBillProjectQuantity(tpa.ID)) > 0 THEN (
                           tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity /
                                                 tpa.TotalAmount) - dbo.getRefundBillProjectQuantity(tpa.ID))
                   ELSE 0
                   END ValidBalance
        FROM TB_ProjectAccount tpa WITH(NOLOCK)
        WHERE
            EXISTS (
                SELECT
                    1
                FROM
                    TB_SaleBillPackageCardProject tsbp WITH(NOLOCK)
                WHERE tpa.ID = tsbp.ProjectAccountID AND tsbp.ID = #{SaleBillPackageCardProjectID}
            )
    </select>
</mapper>