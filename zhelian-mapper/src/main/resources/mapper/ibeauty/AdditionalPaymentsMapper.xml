<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.ibeauty.AdditionalPaymentsMapper">
    <select id="additionalPaymentsList"
            resultType="com.zhelian.model.ibeauty.form.output.SpendingCategoryOrIncomeOrPaymentsOutputForm">
        SELECT
        p.ID,
        p.Name,
        p.Active
        FROM TB_AdditionalPayments p WITH(NOLOCK)
        WHERE 1=1
        <if test="Name != null and Name != ''">
            and p.Name like concat('%',#{Name},'%')
        </if>
        <if test="Active != null ">
            and p.Active =#{Active}
        </if>
        ORDER BY p.Sequence desc
    </select>

    <insert id="createAdditionalPayments" parameterType="com.zhelian.model.ibeauty.entity.AdditionalPaymentsEntity">
        INSERT INTO TB_AdditionalPayments
        (     Name
            , Sequence
            , Active)
        VALUES
            (#{Name}
                , #{Sequence}
                , 1)
    </insert>

    <update id="updateAdditionalPayments" parameterType="com.zhelian.model.ibeauty.entity.AdditionalPaymentsEntity">
        UPDATE TB_AdditionalPayments
        SET Name = #{Name}
                , Active = #{Active}
        WHERE ID = #{ID}
    </update>


    <select id="getMaxSequence" resultType="Integer">
        SELECT top 1 Sequence
        FROM TB_AdditionalPayments WITH ( NOLOCK )
        ORDER BY Sequence desc
    </select>

    <select id="getIDOrderBySequenceASC" resultType="Integer">
        SELECT p.ID
        FROM TB_AdditionalPayments p WITH(NOLOCK)
        WHERE p.ID!=#{ID}
        ORDER BY p.Sequence ASC
    </select>


    <update id="updateSequence" parameterType="java.util.List">
        update TB_AdditionalPayments
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="Sequence =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.Sequence!=null">
                        WHEN ID = #{item.ID} then #{item.Sequence}
                    </if>
                </foreach>
            </trim>
        </trim>
        where
        <foreach collection="list" separator="or" item="item" index="index">
            ID = #{item.ID}
        </foreach>
    </update>

    <select id="getAdditionalPayments"
            resultType="com.zhelian.model.ibeauty.form.output.AdditionalSpendingOrIncomeOrPaymentsCategoryOutputForm">
        SELECT s.ID,
               s.Name
        FROM TB_AdditionalPayments s WITH(NOLOCK)
        WHERE s.Active=1
        ORDER BY s.Sequence desc
    </select>
</mapper>