<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.ibeauty.SaleSavingCardCategoryHandlerCommissionMapper">

    <resultMap id="SavingCardCategoryHandlerCommission"
               type="com.zhelian.model.ibeauty.form.output.SaleSavingCardCategoryHandlerCommissionOutputForm">
        <id column="EntityID" property="EntityID" jdbcType="INTEGER"/>
        <id column="SaleHandlerID" property="ID" jdbcType="INTEGER"/>
        <result column="SaleHandlerName" property="Name" jdbcType="VARCHAR"/>
        <result column="PayRate" property="PayRate" jdbcType="DECIMAL"/>
        <result column="PayFixed" property="PayFixed" jdbcType="DECIMAL"/>
        <result column="SpecialBenefit" property="SpecialBenefit" jdbcType="DECIMAL"/>

        <collection property="HandlerJobTypeCommission"
                    ofType="com.zhelian.model.ibeauty.form.output.SaleSavingCardHandlerJobTypeCommissionOutputForm">
            <id column="JobTypeID" property="JobTypeID" jdbcType="INTEGER"/>
            <result column="HandlerJobTypeID" property="ID" jdbcType="VARCHAR"/>
            <result column="JobName" property="Name" jdbcType="VARCHAR"/>
            <result column="JobTypePayRate" property="PayRate" jdbcType="DECIMAL"/>
            <result column="JobTypePayFixed" property="PayFixed" jdbcType="DECIMAL"/>
            <result column="JobTypeSpecialBenefit" property="SpecialBenefit" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>
    <select id="allSaleSavingCardCategoryHandlerCommission" resultMap="SavingCardCategoryHandlerCommission">
        SELECT tsh.EntityID,
               tsh.ID                                                           SaleHandlerID,
               tsh.Name                                                         SaleHandlerName,
               tsp.PayRate * 100                                                PayRate,
               tsp.PayFixed,
               tsp.SpecialBenefit,
               (cast(tsh.ID as varchar) + '-' + cast(tpj.JobTypeID as varchar)) HandlerJobTypeID,
               tpj.JobTypeID,
               tj.JobName,
               tsj.PayRate * 100                                                JobTypePayRate,
               tsj.PayFixed                                                     JobTypePayFixed,
               tsj.SpecialBenefit                                               JobTypeSpecialBenefit
        FROM TB_SavingCardSaleHandler tsh WITH(NOLOCK)
       LEFT JOIN   TB_SaleSavingCardCategoryHandlerCommission tsp
        WITH (NOLOCK)
        ON tsp.SavingCardSaleHandlerID=tsh.ID AND tsh.EntityID=tsp.EntityID
            AND tsp.SavingCardCategoryID=#{CategoryID}
            LEFT JOIN TB_SavingCardSaleHandlerJobType tpj
        WITH (NOLOCK)
        ON tsh.ID=tpj.SavingCardSaleHandlerID
            LEFT JOIN TB_SaleSavingCardCategoryHandlerJobTypeCommission tsj
        WITH (NOLOCK)
        ON tsh.EntityID=tsj.EntityID
            AND tsj.SavingCardSaleHandlerID = tsh.ID AND tsj.JobTypeID=tpj.JobTypeID AND tsj.SavingCardCategoryID=#{CategoryID}
            LEFT JOIN TK_JobType tj
        WITH (NOLOCK)
        ON tpj.JobTypeID=tj.ID
        WHERE tsh.EntityID=#{EntityID} AND tsh.Active=1
        ORDER BY tsh.Sequence DESC
    </select>

    <delete id="deleteSaleSavingCardCategoryHandler">
        DELETE
        FROM TB_SaleSavingCardCategoryHandlerCommission
        WHERE EntityID = #{EntityID}
          AND SavingCardCategoryID = #{CategoryID}
    </delete>

    <delete id="deleteSaleSavingCardCategoryHandlerJobType">
        DELETE
        FROM TB_SaleSavingCardCategoryHandlerJobTypeCommission
        WHERE EntityID = #{EntityID}
          AND SavingCardCategoryID = #{CategoryID}
    </delete>

    <delete id="deleteSaleSavingCardCategoryHandlerCommissionByEntity">
        DELETE
        FROM TB_SaleSavingCardCategoryHandlerCommission
        WHERE EntityID = #{EntityID}
    </delete>

    <delete id="deleteSaleSavingCardCategoryHandlerJobTypeCommissionByEntity">
        DELETE
        FROM TB_SaleSavingCardCategoryHandlerJobTypeCommission
        WHERE EntityID = #{EntityID}
    </delete>

    <insert id="insertSaleSavingCardCategoryHandlerCommission" parameterType="java.util.List">
        INSERT INTO TB_SaleSavingCardCategoryHandlerCommission
        (EntityID
        ,SavingCardCategoryID
        ,SavingCardSaleHandlerID
        ,PayRate
        ,PayFixed
        ,SpecialBenefit)
        VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.EntityID},
            #{item.SavingCardCategoryID},
            #{item.SavingCardSaleHandlerID},
            cast(#{item.PayRate,jdbcType=DECIMAL} /100 as decimal(5,4)),
            cast(#{item.PayFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.SpecialBenefit,jdbcType=DECIMAL} as decimal(18,2))
            )
        </foreach>

    </insert>

    <insert id="insertSaleSavingCardCategoryHandlerJobTypeCommission" parameterType="java.util.List">
        INSERT INTO TB_SaleSavingCardCategoryHandlerJobTypeCommission (
        EntityID,
        SavingCardCategoryID,
        SavingCardSaleHandlerID,
        JobTypeID,
        PayRate,
        PayFixed,
        SpecialBenefit
        ) VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.EntityID},
            #{item.SavingCardCategoryID},
            #{item.SavingCardSaleHandlerID},
            #{item.JobTypeID},
            cast(#{item.PayRate,jdbcType=DECIMAL} /100 as decimal(5,4)),
            cast(#{item.PayFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.SpecialBenefit,jdbcType=DECIMAL} as decimal(18,2))
            )
        </foreach>
    </insert>

</mapper>