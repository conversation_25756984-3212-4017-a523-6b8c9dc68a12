<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.ibeauty.TreatGeneralCardCategoryHandlerCommissionMapper">

    <resultMap id="GeneralCardCategoryHandlerCommission"
               type="com.zhelian.model.ibeauty.form.output.TreatCardCategoryHandlerCommissionOutputForm">
        <id column="EntityID" property="EntityID" jdbcType="INTEGER"/>
        <id column="TreatHandlerID" property="ID" jdbcType="INTEGER"/>
        <result column="TreatHandlerName" property="Name" jdbcType="VARCHAR"/>
        <result column="PayRate" property="PayRate" jdbcType="DECIMAL"/>
        <result column="PayFixed" property="PayFixed" jdbcType="DECIMAL"/>
        <result column="CardRate" property="CardRate" jdbcType="DECIMAL"/>
        <result column="CardFixed" property="CardFixed" jdbcType="DECIMAL"/>
        <result column="CardLargessRate" property="CardLargessRate" jdbcType="DECIMAL"/>
        <result column="CardLargessFixed" property="CardLargessFixed" jdbcType="DECIMAL"/>
        <result column="LargessRate" property="LargessRate" jdbcType="DECIMAL"/>
        <result column="LargessFixed" property="LargessFixed" jdbcType="DECIMAL"/>
        <result column="SpecialBenefit" property="SpecialBenefit" jdbcType="DECIMAL"/>

        <collection property="HandlerJobTypeCommission"
                    ofType="com.zhelian.model.ibeauty.form.output.TreatCardHandlerJobTypeCommissionOutputForm">
            <id column="JobTypeID" property="JobTypeID" jdbcType="INTEGER"/>
            <result column="HandlerJobTypeID" property="ID" jdbcType="VARCHAR"/>
            <result column="JobName" property="Name" jdbcType="VARCHAR"/>
            <result column="JobTypePayRate" property="PayRate" jdbcType="DECIMAL"/>
            <result column="JobTypePayFixed" property="PayFixed" jdbcType="DECIMAL"/>
            <result column="JobTypeCardRate" property="CardRate" jdbcType="DECIMAL"/>
            <result column="JobTypeCardFixed" property="CardFixed" jdbcType="DECIMAL"/>
            <result column="JobTypeCardLargessRate" property="CardLargessRate" jdbcType="DECIMAL"/>
            <result column="JobTypeCardLargessFixed" property="CardLargessFixed" jdbcType="DECIMAL"/>
            <result column="JobTypeLargessRate" property="LargessRate" jdbcType="DECIMAL"/>
            <result column="JobTypeLargessFixed" property="LargessFixed" jdbcType="DECIMAL"/>
            <result column="JobTypeSpecialBenefit" property="SpecialBenefit" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>
    <select id="allTreatGeneralCardCategoryHandlerCommission" resultMap="GeneralCardCategoryHandlerCommission">
        SELECT tsh.EntityID,
               tsh.ID                                                           TreatHandlerID,
               tsh.Name                                                         TreatHandlerName,
               tsp.PayRate * 100                                                PayRate,
               tsp.PayFixed,
               tsp.CardRate * 100                                               CardRate,
               tsp.CardFixed,
               tsp.CardLargessRate * 100                                        CardLargessRate,
               tsp.CardLargessFixed,
               tsp.LargessRate * 100                                            LargessRate,
               tsp.LargessFixed,
               tsp.SpecialBenefit,
               (cast(tsh.ID as varchar) + '-' + cast(tpj.JobTypeID as varchar)) HandlerJobTypeID,
               tpj.JobTypeID,
               tj.JobName,
               tsj.PayRate * 100                                                JobTypePayRate,
               tsj.PayFixed                                                     JobTypePayFixed,
               tsj.CardRate * 100                                               JobTypeCardRate,
               tsj.CardFixed                                                    JobTypeCardFixed,
               tsj.CardLargessRate * 100                                        JobTypeCardLargessRate,
               tsj.CardLargessFixed                                             JobTypeCardLargessFixed,
               tsj.LargessRate * 100                                            JobTypeLargessRate,
               tsj.LargessFixed                                                 JobTypeLargessFixed,
               tsj.SpecialBenefit                                               JobTypeSpecialBenefit
        FROM TB_ProjectTreatHandler tsh WITH(NOLOCK)
        LEFT JOIN   TB_TreatGeneralCardCategoryHandlerCommission tsp
        WITH (NOLOCK)
        ON tsp.GeneralCardProjectTreatHandlerID=tsh.ID AND tsh.EntityID=tsp.EntityID
            AND tsp.GeneralCardCategoryID=#{CategoryID}
            LEFT JOIN TB_ProjectTreatHandlerJobType tpj
        WITH (NOLOCK)
        ON tsh.ID=tpj.ProjectTreatHandlerID
            LEFT JOIN TB_TreatGeneralCardCategoryHandlerJobTypeCommission tsj
        WITH (NOLOCK)
        ON tsh.EntityID=tsj.EntityID
            AND tsj.GeneralCardProjectTreatHandlerID = tsh.ID AND tsj.JobTypeID=tpj.JobTypeID AND tsj.GeneralCardCategoryID=#{CategoryID}
            LEFT JOIN TK_JobType tj
        WITH (NOLOCK)
        ON tpj.JobTypeID=tj.ID
        WHERE tsh.EntityID=#{EntityID} AND tsh.Active=1
        ORDER BY tsh.Sequence DESC
    </select>

    <insert id="insertTreatGeneralCardCategoryHandlerCommission" parameterType="java.util.List">
        INSERT INTO TB_TreatGeneralCardCategoryHandlerCommission
        (EntityID
        ,GeneralCardCategoryID
        ,GeneralCardProjectTreatHandlerID
        ,PayRate
        ,PayFixed
        ,CardRate
        ,CardFixed
        ,CardLargessRate
        ,CardLargessFixed
        ,LargessRate
        ,LargessFixed
        ,SpecialBenefit)
        VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.EntityID},
            #{item.GeneralCardCategoryID},
            #{item.GeneralCardTreatHandlerID},
            cast(#{item.PayRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.PayFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.CardRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.CardFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.CardLargessRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.CardLargessFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.LargessRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.LargessFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.SpecialBenefit,jdbcType=DECIMAL} as decimal(18,2))
            )
        </foreach>

    </insert>

    <insert id="insertTreatGeneralCardCategoryHandlerJobTypeCommission" parameterType="java.util.List">
        INSERT INTO TB_TreatGeneralCardCategoryHandlerJobTypeCommission (
        EntityID,
        GeneralCardCategoryID,
        GeneralCardProjectTreatHandlerID,
        JobTypeID,
        PayRate,
        PayFixed,
        CardRate,
        CardFixed,
        CardLargessRate,
        CardLargessFixed,
        LargessRate,
        LargessFixed,
        SpecialBenefit
        ) VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.EntityID},
            #{item.GeneralCardCategoryID},
            #{item.GeneralCardTreatHandlerID},
            #{item.JobTypeID},
            cast(#{item.PayRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.PayFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.CardRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.CardFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.CardLargessRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.CardLargessFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.LargessRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.LargessFixed,jdbcType=DECIMAL} as decimal(18,2)),
            cast(#{item.SpecialBenefit,jdbcType=DECIMAL} as decimal(18,2))
            )
        </foreach>
    </insert>

    <delete id="deleteTreatGeneralCardCategoryHandler">
        DELETE
        FROM TB_TreatGeneralCardCategoryHandlerCommission
        WHERE EntityID = #{EntityID}
          AND GeneralCardCategoryID = #{CategoryID}
    </delete>

    <delete id="deleteTreatGeneralCardCategoryHandlerJobType">
        DELETE
        FROM TB_TreatGeneralCardCategoryHandlerJobTypeCommission
        WHERE EntityID = #{EntityID}
          AND GeneralCardCategoryID = #{CategoryID}
    </delete>

    <delete id="deleteTreatGeneralCardCategoryHandlerByEntityID">
        DELETE
        FROM TB_TreatGeneralCardCategoryHandlerCommission
        WHERE EntityID = #{EntityID}
    </delete>

    <delete id="deleteTreatGeneralCardCategoryHandlerJobTypeByEntityID">
        DELETE
        FROM TB_TreatGeneralCardCategoryHandlerJobTypeCommission
        WHERE EntityID = #{EntityID}
    </delete>
</mapper>