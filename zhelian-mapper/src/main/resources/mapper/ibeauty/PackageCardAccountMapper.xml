<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.ibeauty.PackageCardAccountMapper">
    <insert id="createPackageCardAccount" parameterType="com.zhelian.model.ibeauty.entity.PackageCardAccountEntity"
            useGeneratedKeys="true" keyProperty="ID">
        INSERT INTO dbo.TB_PackageCardAccount (CustomerID, EntityID, BuyDate, PackageCardID, Price,
                                                    TotalAmount, ArrearAmount, IsLargess,
                                                    SaleBillPackageCardID, ValidType, ValidDay, ValidDate,
                                                    PayAmount, SavingCardDeductionAmount,
                                                    SavingCardDeductionLargessAmount,
            Active, Channel, Remark)
        VALUES
            (#{CustomerID}, #{EntityID}, #{BuyDate}, #{PackageCardID}, #{Price}, #{TotalAmount}, #{ArrearAmount}, #{IsLargess}, #{SaleBillPackageCardID}, #{ValidType}, #{ValidDay}, #{ValidDate, jdbcType = TIMESTAMP}, #{PayAmount}, #{SavingCardDeductionAmount}, #{SavingCardDeductionLargessAmount},
            1, #{Channel}, #{Remark})
    </insert>
    <update id="updatePackageCardAccountActiveFalse">
        UPDATE tpa
        SET tpa.Active = 0 FROM
            TB_PackageCardAccount tpa
            INNER JOIN TB_SaleBillPackageCard ts
        ON tpa.SaleBillPackageCardID= ts.ID
        WHERE
            ts.BillID=#{SaleBillID}
    </update>
    <!--套餐卡账户列表-->
    <select id="packageCardAccount" parameterType="com.zhelian.model.ibeauty.form.input.TreatPackageCardAccountForm"
            resultType="com.zhelian.model.ibeauty.form.output.PackageCardAccountOutputForm">
        SELECT
        w.ID,
        w.PackageCardID,
        w.TotalAmount,
        p.Name,
        p.Alias,
        CASE
        WHEN w.ValidType = 3 THEN '永久有效'
        ELSE CONVERT(VARCHAR(10),w.ValidDate,120)
        END ValidDayName,
        ISNULL(w.IsLargess, 0) IsLargess,
        CONVERT(VARCHAR(10),w.BuyDate,120) BuyDate,
        w.Price,
        w.ArrearAmount,
        w.Remark
        FROM
        (--项目
        SELECT DISTINCT a.PackageCardAccountID FROM TB_ProjectAccount a WITH(NOLOCK) WHERE a.PackageCardAccountID IS NOT NULL AND
        a.Balance>0 AND a.CustomerID = #{CustomerID} AND a.Active=1
        UNION
        --储值卡
        SELECT DISTINCT b.PackageCardAccountID FROM TB_SavingCardAccount b WITH(NOLOCK) WHERE b.PackageCardAccountID IS NOT NULL AND
        (b.Balance>0 OR b.LargessBalance>0) AND b.CustomerID = #{CustomerID} AND b.Active=1
        UNION
        --产品
        SELECT DISTINCT c.PackageCardAccountID FROM TB_ProductAccount c WITH(NOLOCK) WHERE c.Balance>0 AND c.PackageCardAccountID IS
        NOT NULL AND c.CustomerID = #{CustomerID} AND c.Active=1
        UNION
        --通用次卡
        SELECT DISTINCT d.PackageCardAccountID FROM TB_GeneralCardAccount d WITH(NOLOCK) WHERE d.Balance>0 AND d.PackageCardAccountID
        IS NOT NULL AND d.CustomerID = #{CustomerID} AND d.Active=1
        UNION
        --时效卡
        SELECT DISTINCT e.PackageCardAccountID FROM TB_TimeCardAccount e WITH(NOLOCK) WHERE e.PackageCardAccountID IS NOT NULL AND
        e.CustomerID = #{CustomerID} AND e.Active=1
        ) q
        INNER JOIN TB_PackageCardAccount w WITH(NOLOCK) ON w.ID = q.PackageCardAccountID
        LEFT JOIN TB_PackageCard p WITH(NOLOCK) ON w.PackageCardID = p.ID
        WHERE w.Active=1
        AND (w.ValidDate IS NULL OR w.ValidDate &gt;= convert(varchar(10), #{BillDate}, 120))
        <if test="Name!=null and Name!=''">
            AND (p.Name LIKE '%'+ #{Name} +'%' OR p.Alias LIKE '%'+ #{Name} +'%'
            OR p.NameFullPinYin LIKE '%'+ #{Name} +'%' OR p.NameAbbrPinYin LIKE '%'+ #{Name} +'%'
            OR p.AliasFullPinYin LIKE '%'+ #{Name} +'%' OR p.AliasAbbrPinYin LIKE '%'+ #{Name} +'%')
        </if>
        ORDER BY w.BuyDate DESC ,p.Name ASC,w.ID DESC
    </select>

    <resultMap id="AllAccountMap" type="com.zhelian.model.ibeauty.form.output.PackageCardAccountDetailsOutputForm">
        <!--项目账户-->
        <collection property="Project"
                    column="{AccountID = PackageCardAccountID,EntityID = EntityID,ValidDayName = ValidDayName}"
                    select="getPackageCardProjectAccounts">
            <result column="ID" property="ID" jdbcType="INTEGER"/>
            <result column="ProjectID" property="ProjectID" jdbcType="INTEGER"/>
            <result column="Name" property="Name" jdbcType="VARCHAR"/>
            <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
            <result column="ValidDayName" property="ValidDayName" jdbcType="VARCHAR"/>
            <result column="ValidBalance" property="ValidBalance" jdbcType="INTEGER"/>
            <result column="RefundBalance" property="RefundBalance" jdbcType="INTEGER"/>
            <result column="ArrearAmount" property="ArrearAmount" jdbcType="DECIMAL"/>
            <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
            <result column="Balance" property="Balance" jdbcType="INTEGER"/>
            <result column="Price" property="Price" jdbcType="DECIMAL"/>
            <result column="Amount" property="Amount" jdbcType="DECIMAL"/>
            <result column="IsLargess" property="IsLargess" jdbcType="BOOLEAN"/>
        </collection>
        <!--产品账户-->
        <collection property="Product" column="{AccountID = PackageCardAccountID,ValidDayName = ValidDayName}"
                    select="getPackageCardProductAccounts">
            <result column="ID" property="ID" jdbcType="INTEGER"/>
            <result column="ProjectID" property="ProjectID" jdbcType="INTEGER"/>
            <result column="Name" property="Name" jdbcType="VARCHAR"/>
            <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
            <result column="ValidDayName" property="ValidDayName" jdbcType="VARCHAR"/>
            <result column="ValidBalance" property="ValidBalance" jdbcType="INTEGER"/>
            <result column="RefundBalance" property="RefundBalance" jdbcType="INTEGER"/>
            <result column="ArrearAmount" property="ArrearAmount" jdbcType="DECIMAL"/>
            <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
            <result column="Balance" property="Balance" jdbcType="INTEGER"/>
            <result column="Price" property="Price" jdbcType="DECIMAL"/>
            <result column="Amount" property="Amount" jdbcType="DECIMAL"/>
            <result column="IsLargess" property="IsLargess" jdbcType="BOOLEAN"/>
        </collection>
        <!--储值卡账户-->
        <collection property="SavingCard"
                    column="{AccountID = PackageCardAccountID,EntityID = EntityID,ValidDayName = ValidDayName}"
                    select="getPackageCardSavingCardAccounts">
            <result column="ID" property="ID" jdbcType="VARCHAR"/>
            <result column="SavingCardID" property="SavingCardID" jdbcType="INTEGER"/>
            <result column="Name" property="Name" jdbcType="VARCHAR"/>
            <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
            <result column="ValidDayName" property="ValidDayName" jdbcType="VARCHAR"/>
            <result column="ValidBalance" property="ValidBalance" jdbcType="DECIMAL"/>
            <result column="TotalBalance" property="TotalBalance" jdbcType="DECIMAL"/>
            <result column="Balance" property="Balance" jdbcType="DECIMAL"/>
            <result column="LargessBalance" property="LargessBalance" jdbcType="DECIMAL"/>
            <result column="ArrearAmount" property="ArrearAmount" jdbcType="DECIMAL"/>
            <result column="RefundAmount" property="RefundAmount" jdbcType="DECIMAL"/>
        </collection>
        <!--通用次卡账户-->
        <collection property="GeneralCard"
                    column="{AccountID = PackageCardAccountID,EntityID = EntityID,ValidDayName = ValidDayName}"
                    select="getPackageCardGeneralCardAccounts">
            <result column="ID" property="ID" jdbcType="INTEGER"/>
            <result column="GeneralCardID" property="GeneralCardID" jdbcType="INTEGER"/>
            <result column="Name" property="Name" jdbcType="VARCHAR"/>
            <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
            <result column="ValidDayName" property="ValidDayName" jdbcType="VARCHAR"/>
            <result column="ValidBalance" property="ValidBalance" jdbcType="INTEGER"/>
            <result column="CardTimes" property="CardTimes" jdbcType="INTEGER"/>
            <result column="Balance" property="Balance" jdbcType="INTEGER"/>
            <result column="Price" property="Price" jdbcType="DECIMAL"/>
            <result column="Amount" property="Amount" jdbcType="DECIMAL"/>
            <result column="ArrearAmount" property="ArrearAmount" jdbcType="DECIMAL"/>
            <result column="RefundAmount" property="RefundAmount" jdbcType="INTEGER"/>
        </collection>
        <!--时效卡账户-->
        <collection property="TimeCard"
                    column="{AccountID = PackageCardAccountID,EntityID = EntityID,BillDate = BillDate,ValidDayName = ValidDayName}"
                    select="getPackageCardTimeCardAccounts">
            <result column="ID" property="ID" jdbcType="INTEGER"/>
            <result column="TimeCardID" property="TimeCardID" jdbcType="INTEGER"/>
            <result column="Name" property="Name" jdbcType="VARCHAR"/>
            <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
            <result column="ValidDayName" property="ValidDayName" jdbcType="VARCHAR"/>
            <result column="ConsumeCycle" property="ConsumeCycle" jdbcType="INTEGER"/>
            <result column="CycleLimitAmount" property="CycleLimitAmount" jdbcType="INTEGER"/>
            <result column="ConsumeCycleAmount" property="ConsumeCycleAmount" jdbcType="INTEGER"/>
            <result column="PerformanceTimes" property="PerformanceTimes" jdbcType="INTEGER"/>
            <result column="PerformanceBalance" property="PerformanceBalance" jdbcType="INTEGER"/>
            <result column="IsLargess" property="IsLargess" jdbcType="BOOLEAN"/>
            <result column="Price" property="Price" jdbcType="DECIMAL"/>
            <result column="ArrearAmount" property="ArrearAmount" jdbcType="DECIMAL"/>
            <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
            <result column="ConsumeAmount" property="ConsumeAmount" jdbcType="INTEGER"/>
            <result column="PerformanceBalanceAmount" property="PerformanceBalanceAmount" jdbcType="INTEGER"/>
        </collection>

    </resultMap>

    <!--套餐卡账户盒子，列出明细需要的参数-->
    <select id="packageCardAccountDetails" resultMap="AllAccountMap"
            parameterType="com.zhelian.model.ibeauty.form.input.PackageCardAccountDetailsInputForm">
        select ${PackageCardAccountID} PackageCardAccountID,
               ${EntityID}             EntityID,
               #{BillDate}             BillDate,
               #{ValidDayName}         ValidDayName
    </select>

    <!--项目账户明细-->
    <select id="getPackageCardProjectAccounts" parameterType="java.util.Map"
            resultType="com.zhelian.model.ibeauty.form.output.PackageCardProjectAccountOutputForm">
        SELECT tpa.ID,
               tpa.ProjectID,
               p.Name,
               p.Alias,
               #{ValidDayName}                                           ValidDayName,
               CASE
                   WHEN tpa.TotalAmount / tpa.Quantity &lt;= 0
                       THEN tpa.Balance - dbo.getRefundBillProjectQuantity(tpa.ID)
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) &lt;= 0 THEN 0
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) > 0 THEN (
                           tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity /
                                                 tpa.TotalAmount) - dbo.getRefundBillProjectQuantity(tpa.ID))
                   END                                                   ValidBalance,
               dbo.getRefundBillProjectQuantity(tpa.ID)                  RefundBalance,
               tpa.Quantity,
               tpa.TotalAmount,
               tpa.ArrearAmount,
               tpa.Balance,
               CAST(tpa.TotalAmount / tpa.Quantity as decimal(38, 2)) AS Amount,
               tpa.Price,
               tpa.IsLargess,
               CONVERT(VARCHAR (10), tpa.BuyDate, 120)                   BuyDate,
               tpa.Remark
        FROM TB_ProjectAccount tpa WITH(NOLOCK)
                 LEFT JOIN TB_Project p WITH(NOLOCK)
        ON p.ID = tpa.ProjectID
        WHERE tpa.Active=1
          AND tpa.Balance
         &gt; 0
          AND tpa.PackageCardAccountID = #{AccountID}
          AND EXISTS (
            SELECT
            1
            FROM
            TB_ProjectConsumeEntity PSE WITH (NOLOCK)
            WHERE
            tpa.ProjectID = PSE.ProjectID
          AND EXISTS ( SELECT 1 FROM GetParentEntities (#{EntityID}) E WHERE E.ID = PSE.EntityID )
            )
        ORDER BY tpa.IsLargess, p.Name ASC, tpa.ID DESC

    </select>

    <!--产品账户明细-->
    <select id="getPackageCardProductAccounts" parameterType="java.util.Map"
            resultType="com.zhelian.model.ibeauty.form.output.PackageCardProductAccountOutputForm">
        SELECT tpa.ID,
               tpa.ProductID,
               p.Name,
               p.Alias,
               #{ValidDayName}                                           ValidDayName,
               CASE
                   WHEN tpa.TotalAmount / tpa.Quantity &lt;= 0
                       THEN tpa.Balance - dbo.getRefundBillProductQuantity(tpa.ID)
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) &lt;= 0 THEN 0
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) > 0 THEN (
                           tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity /
                                                 tpa.TotalAmount) - dbo.getRefundBillProductQuantity(tpa.ID))
                   END                                                   ValidBalance,
               dbo.getRefundBillProductQuantity(tpa.ID)                  RefundBalance,
               tpa.Quantity,
               tpa.TotalAmount,
               tpa.ArrearAmount,
               tpa.Balance,
               CAST(tpa.TotalAmount / tpa.Quantity as decimal(38, 2)) AS Amount,
               tpa.Price,
               tpa.IsLargess,
               CONVERT(VARCHAR (10), tpa.BuyDate, 120)                   BuyDate,
               tpa.Remark
        FROM TB_ProductAccount tpa WITH(NOLOCK)
                 LEFT JOIN TE_Product p WITH(NOLOCK)
        ON p.ID = tpa.ProductID
            LEFT JOIN TB_Product bp
        WITH (NOLOCK)
        ON p.ID = bp.ProductID
        WHERE tpa.Active=1
          AND tpa.Balance
         &gt; 0
          AND tpa.PackageCardAccountID = #{AccountID}
        ORDER BY tpa.IsLargess, p.Name ASC, tpa.ID DESC
    </select>

    <!--储值卡账户明细-->
    <select id="getPackageCardSavingCardAccounts" parameterType="java.util.Map"
            resultType="com.zhelian.model.ibeauty.form.output.SavingCardAccountForm">
        SELECT T.ID,
               T.AccountID,
               T.Type,
               T.SavingCardID,
               T.Name,
               T.Alias,
               #{ValidDayName} ValidDayName,
               T.IsLargess,
               T.Balance,
               T.LargessBalance,
               T.TotalBalance,
               T.ValidBalance,
               T.TotalAmount,
               T.ArrearAmount,
               T.RefundAmount,
               T.BuyDate,
               T.createDate,
               T.Remark
        FROM (SELECT ('0' + '-' + CAST(sca.ID AS varchar)) AS       ID,
                     sca.ID                                AS       AccountID,
                     0                                     AS       Type,
                     sca.SavingCardID,
                     s.Name,
                     ISNULL(s.Alias, '')                            Alias,
                     CASE
                         WHEN sca.Amount = 0 THEN 1
                         ELSE 0
                         END                                        IsLargess,
                     sca.Balance,
                     sca.LargessBalance,
                     sca.Balance + sca.LargessBalance               TotalBalance,
                     CASE
                         WHEN sca.Amount > 0 THEN
                             CONVERT(DECIMAL (18, 2), sca.Balance - sca.ArrearAmount +
                                                      sca.LargessBalance -
                                                      sca.ArrearAmount * sca.LargessAmount / sca.Amount
                                 - dbo.getRefundBillSavingCardAmount(sca.ID) -
                                                      dbo.getRefundBillSavingCardLargessAmount(sca.ID))
                         ELSE sca.LargessBalance
                         END                                        ValidBalance,
                     sca.Amount                                     TotalAmount,
                     sca.ArrearAmount,
                     dbo.getRefundBillSavingCardTotalAmount(sca.ID) RefundAmount,
                     CONVERT(VARCHAR (10), sca.BuyDate, 120)        BuyDate,
                     sca.BuyDate                                    createDate,
                     sca.Remark
              FROM TB_SavingCardAccount sca WITH(NOLOCK)
                       LEFT JOIN TB_SavingCard s WITH(NOLOCK)
              ON sca.SavingCardID = s.ID
              WHERE sca.Active=1
                AND sca.PackageCardAccountID = #{AccountID}
                AND (sca.Balance &gt; 0 OR sca.LargessBalance  &gt; 0)
                AND s.IsSettingLargess = 0
                AND s.IsAllowConsumeDeduct = 1 --允许消耗
                AND EXISTS (
                  SELECT
                  *
                  FROM
                  TB_SavingCardConsumeEntity PSE WITH (NOLOCK)
                  WHERE
                  sca.SavingCardID = PSE.SavingCardID
                AND EXISTS ( SELECT 1 FROM GetParentEntities (#{EntityID}) E WHERE E.ID = PSE.EntityID )
                  )
              UNION ALL
              SELECT
                  ('1' + '-' + CAST (sca.ID AS varchar)) AS ID,
                  sca.ID AS AccountID,
                  1 AS Type,
                  sca.SavingCardID,
                  s.Name,
                  ISNULL(s.Alias, '') Alias,
                  0 as IsLargess,
                  sca.Balance,
                  0 LargessBalance,
                  sca.Balance TotalBalance,
                  CONVERT (DECIMAL (18, 2), sca.Balance - sca.ArrearAmount - dbo.getRefundBillSavingCardAmount(sca.ID)) ValidBalance,
                  sca.Amount TotalAmount,
                  sca.ArrearAmount,
                  dbo.getRefundBillSavingCardAmount(sca.ID) RefundAmount,
                  CONVERT (VARCHAR (10), sca.BuyDate, 120) BuyDate,
                  sca.BuyDate createDate,
                  sca.Remark
              FROM
                  TB_SavingCardAccount sca WITH(NOLOCK)
                  LEFT JOIN TB_SavingCard s
              WITH (NOLOCK)
              ON sca.SavingCardID = s.ID
              WHERE sca.Active=1
                AND sca.PackageCardAccountID = #{AccountID}
                AND sca.Balance
               &gt; 0
                AND s.IsSettingLargess = 1
                AND s.IsAllowConsumeDeduct = 1 --允许消耗
                AND EXISTS (
                  SELECT
                  *
                  FROM
                  TB_SavingCardConsumeEntity PSE
                  WHERE
                  sca.SavingCardID = PSE.SavingCardID
                AND EXISTS ( SELECT 1 FROM GetParentEntities (#{EntityID}) E WHERE E.ID = PSE.EntityID )
                  )
              UNION ALL
              SELECT
                  ('2' + '-' + CAST (sca.ID AS varchar)) AS ID,
                  sca.ID AS AccountID,
                  2 AS Type,
                  sca.SavingCardID,
                  s.Name,
                  ISNULL(s.Alias, '') Alias,
                  1 as IsLargess,
                  0 Balance,
                  sca.LargessBalance,
                  sca.LargessBalance TotalBalance,
                  CASE WHEN sca.Amount >0 THEN CONVERT (DECIMAL (18, 2), sca.LargessBalance -sca.ArrearAmount*sca.LargessAmount/sca.Amount-dbo.getRefundBillSavingCardLargessAmount(sca.ID))
                  ELSE sca.LargessBalance
                  END ValidBalance,
                  0 TotalAmount,
                  0 ArrearAmount,
                  dbo.getRefundBillSavingCardLargessAmount(sca.ID) RefundAmount,
                  CONVERT (VARCHAR (10), sca.BuyDate, 120) BuyDate,
                  sca.BuyDate createDate,
                  sca.Remark
              FROM
                  TB_SavingCardAccount sca WITH(NOLOCK)
                  LEFT JOIN TB_SavingCard s
              WITH (NOLOCK)
              ON sca.SavingCardID = s.ID
              WHERE sca.Active=1
                AND sca.PackageCardAccountID = #{AccountID}
                AND sca.LargessBalance
               &gt; 0
                AND s.IsSettingLargess = 1
                AND s.IsAllowConsumeDeduct = 1 --允许消耗
                AND EXISTS (
                  SELECT
                  *
                  FROM
                  TB_SavingCardConsumeEntity PSE WITH (NOLOCK)
                  WHERE
                  sca.SavingCardID = PSE.SavingCardID
                AND EXISTS ( SELECT 1 FROM GetParentEntities (#{EntityID}) E WHERE E.ID = PSE.EntityID )
                  )) T
        ORDER BY T.Type, T.Name, T.createDate DESC
    </select>

    <!--通用次卡账户明细-->
    <select id="getPackageCardGeneralCardAccounts" parameterType="java.util.Map"
            resultType="com.zhelian.model.ibeauty.form.output.GeneralCardAccountOutputForm">
        SELECT tpa.ID,
               tpa.GeneralCardID,
               p.Name,
               p.Alias,
               #{ValidDayName}                                            ValidDayName,
               CASE
                   WHEN tpa.TotalAmount / tpa.CardTimes &lt;= 0
                       THEN tpa.Balance - dbo.getRefundBillGeneralCardQuantity(tpa.ID)
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.CardTimes / tpa.TotalAmount)) &lt;= 0 THEN 0
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.CardTimes / tpa.TotalAmount)) > 0 THEN (
                           tpa.Balance - CEILING(tpa.ArrearAmount * tpa.CardTimes /
                                                 tpa.TotalAmount) - dbo.getRefundBillGeneralCardQuantity(tpa.ID))
                   END                                                    ValidBalance,
               tpa.CardTimes,
               dbo.getRefundBillGeneralCardQuantity(tpa.ID)               RefundAmount,
               tpa.TotalAmount,
               tpa.ArrearAmount,
               CAST(tpa.TotalAmount / tpa.CardTimes as decimal(38, 2)) AS Amount,
               tpa.Balance,
               tpa.Price,
               tpa.IsLargess,
               CONVERT(VARCHAR (10), tpa.BuyDate, 120)                    BuyDate,
               tpa.Remark,
               p.TreatProjectNumber
        FROM TB_GeneralCardAccount tpa
                 LEFT JOIN TB_GeneralCard p WITH(NOLOCK)
        ON p.ID = tpa.GeneralCardID
        WHERE tpa.Active=1
          AND tpa.PackageCardAccountID = #{AccountID}
          AND EXISTS (
            SELECT
            *
            FROM
            TB_GeneralCardConsumeEntity PSE WITH (NOLOCK)
            WHERE
            tpa.GeneralCardID = PSE.GeneralCardID
          AND EXISTS ( SELECT 1 FROM GetParentEntities (#{EntityID}) E WHERE E.ID = PSE.EntityID )
            )
          AND tpa.Balance
         &gt; 0
        ORDER BY tpa.IsLargess, p.Name, tpa.ID DESC
    </select>

    <!--时效卡账户明细-->
    <select id="getPackageCardTimeCardAccounts" parameterType="java.util.Map"
            resultType="com.zhelian.model.ibeauty.form.output.TimeCardAccountOutputForm">
        SELECT tpa.ID,
               tpa.TimeCardID,
               p.Name,
               p.Alias,
               #{ValidDayName}                                                              ValidDayName,
               CASE
                   WHEN tpa.ArrearAmount = 0 THEN tpa.PerformanceBalance
                   WHEN tpa.ArrearAmount > 0 AND tpa.PerformanceTimes > 0 THEN (tpa.PerformanceBalance -
                                                                                CEILING(tpa.ArrearAmount / (tpa.TotalAmount / tpa.PerformanceTimes)))
                   ELSE 0 END                                                               ValidBalance,
               p.ConsumeCycle,
               p.CycleLimitAmount,
               ISNULL(dbo.GetTimeCardConsumeCycleAmount(
                              (SELECT TOP 1 CONVERT (VARCHAR (10),BillDate,120) BillDate
                                  FROM TB_TreatBillTimeCard d
                                  LEFT JOIN TB_TreatBill m ON m.ID=d.TreatBillID
                                  WHERE m.BillStatus='20' and d.TimeCardAccountID =tpa.ID and m.BillType = '10'
                                  ORDER BY BillDate),p.ConsumeCycle,#{BillDate},tpa.ID), 0) ConsumeCycleAmount,
               tpa.PerformanceTimes,
               tpa.PerformanceBalance,
               tpa.TotalAmount,
               tpa.ArrearAmount,
               tpa.PayPerformanceBalanceAmount + tpa.CardPerformanceBalanceAmount +
               tpa.CardLargessPerformanceBalanceAmount                                      PerformanceBalanceAmount,
               CASE
                   WHEN tpa.PerformanceBalance = 0 THEN 0
                   ELSE CAST(tpa.TotalAmount / tpa.PerformanceTimes as decimal(38, 2))
                   END                                                                      Amount,
               tpa.Price,
               tpa.IsLargess,
               CONVERT(VARCHAR (10), tpa.BuyDate, 120)                                      BuyDate,
               tpa.TotalAmount,
               ISNULL(
                       (SELECT SUM(T.Quantity)
                        FROM (SELECT CASE
                                         WHEN
                                             a2.BillType = '10' then a1.Quantity
                                         ELSE a1.Quantity * -1
                                         END Quantity
                              from TB_TreatBillTimeCard a1
                                       JOIN TB_TreatBill a2 ON a1.TreatBillID = a2.ID
                              WHERE a1.TimeCardAccountID = tpa.ID
                                AND a2.BillStatus = '20') T), 0)                            ConsumeAmount,
               tpa.Remark
        FROM TB_TimeCardAccount tpa
                 LEFT JOIN TB_TimeCard p WITH(NOLOCK)
        ON p.ID = tpa.TimeCardID
        WHERE tpa.Active=1
          AND tpa.PackageCardAccountID = #{AccountID}
          AND EXISTS (
            SELECT
            *
            FROM
            TB_TimeCardConsumeEntity PSE WITH (NOLOCK)
            WHERE
            tpa.TimeCardID = PSE.TimeCardID
          AND EXISTS ( SELECT 1 FROM GetParentEntities (#{EntityID}) E WHERE E.ID = PSE.EntityID )
            )
          AND NOT EXISTS
            (
            SELECT * FROM TB_RefundSaleBillTimeCard trs WITH (NOLOCK)
            JOIN TB_RefundSaleBill trb WITH (NOLOCK) ON trb.ID=trs.RefundBillID
            where trs.TimeCardAccountID = tpa.ID
          AND trb.ApprovalStatus='20'
            )
        ORDER BY tpa.IsLargess, p.Name, tpa.ID DESC
    </select>

    <select id="arrearPackageCardAccount"
            resultType="com.zhelian.model.ibeauty.form.output.PackageCardAccountOutputForm">
        SELECT
        w.ID,
        w.PackageCardID,
        p.Name,
        p.Alias,
        CASE
        WHEN w.ValidType = 3 THEN '永久有效'
        ELSE CONVERT(VARCHAR(10),w.ValidDate,120)
        END ValidDayName,
        ISNULL(w.IsLargess, 0) IsLargess,
        CONVERT(VARCHAR(10),w.BuyDate,120) BuyDate,
        w.Price,
        w.ArrearAmount,
        w.TotalAmount
        FROM TB_PackageCardAccount w WITH(NOLOCK)
        LEFT JOIN TB_PackageCard p WITH(NOLOCK) ON w.PackageCardID = p.ID
        WHERE w.Active=1
        AND (EXISTS(SELECT 1 FROM TB_ProjectAccount a WITH(NOLOCK) WHERE a.PackageCardAccountID = w.ID AND
        a.ArrearAmount>0 AND a.CustomerID = #{CustomerID} AND a.Active=1)
        OR EXISTS(SELECT 1 FROM TB_SavingCardAccount b WITH(NOLOCK) WHERE b.PackageCardAccountID = w.ID AND
        b.ArrearAmount>0 AND b.CustomerID = #{CustomerID} AND b.Active=1)
        OR EXISTS(SELECT 1 FROM TB_ProductAccount c WITH(NOLOCK) WHERE c.PackageCardAccountID = w.ID AND
        c.ArrearAmount>0 AND c.CustomerID = #{CustomerID} AND c.Active=1)
        OR EXISTS(SELECT 1 FROM TB_GeneralCardAccount d WITH(NOLOCK) WHERE d.PackageCardAccountID = w.ID AND
        d.ArrearAmount>0 AND d.CustomerID = #{CustomerID} AND d.Active=1)
        OR EXISTS(SELECT 1 FROM TB_TimeCardAccount e WITH(NOLOCK) WHERE e.PackageCardAccountID = w.ID AND
        e.ArrearAmount>0 AND e.CustomerID = #{CustomerID} AND e.Active=1)
        )
        AND (w.EntityID=#{EntityID}
        OR EXISTS (SELECT 1 FROM TB_CustomerEntity tce WITH(NOLOCK) WHERE tce.CustomerID=w.CustomerID AND tce.IsBelongEntity=1 AND tce.EntityID = #{EntityID}))
        AND (w.ValidDate IS NULL OR w.ValidDate &gt;= convert(varchar(10), GETDATE(), 120))
        <if test="Name!=null and Name!=''">
            AND (p.Name LIKE '%'+ #{Name} +'%' OR p.Alias LIKE '%'+ #{Name} +'%'
            OR p.NameFullPinYin LIKE '%'+ #{Name} +'%' OR p.NameAbbrPinYin LIKE '%'+ #{Name} +'%'
            OR p.AliasFullPinYin LIKE '%'+ #{Name} +'%' OR p.AliasAbbrPinYin LIKE '%'+ #{Name} +'%')
        </if>

        ORDER BY w.ID DESC
    </select>

    <!--欠款项目账户明细-->
    <select id="arrearPackageCardProjectAccount" parameterType="java.util.Map"
            resultType="com.zhelian.model.ibeauty.form.output.PackageCardProjectAccountOutputForm">
        SELECT tpa.ID,
               tpa.ProjectID,
               p.Name,
               ISNULL(p.Alias, '')                                       Alias,
               CASE
                   WHEN tpa.TotalAmount / tpa.Quantity &lt;= 0
                       THEN tpa.Balance - dbo.getRefundBillProjectQuantity(tpa.ID)
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) &lt;= 0 THEN 0
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) > 0 THEN (
                           tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity /
                                                 tpa.TotalAmount) - dbo.getRefundBillProjectQuantity(tpa.ID))
                   END                                                   ValidBalance,
               dbo.getRefundBillProjectQuantity(tpa.ID)                  RefundBalance,
               tpa.Quantity,
               tpa.ArrearAmount,
               tpa.TotalAmount,
               tpa.Balance,
               CAST(tpa.TotalAmount / tpa.Quantity as decimal(38, 2)) AS Amount,
               tpa.Price,
               tpa.IsLargess,
               CONVERT(VARCHAR (10), tpa.BuyDate, 120)                   BuyDate
        FROM TB_ProjectAccount tpa WITH(NOLOCK)
                 LEFT JOIN TB_Project p WITH(NOLOCK)
        ON p.ID = tpa.ProjectID
        WHERE tpa.Active=1
          AND tpa.ArrearAmount
            > 0
          AND tpa.PackageCardAccountID = #{PackageCardAccountID}
        ORDER BY tpa.IsLargess, p.Name, tpa.ID DESC
    </select>

    <!--欠款产品账户明细-->
    <select id="arrearPackageCardProductAccount" parameterType="java.util.Map"
            resultType="com.zhelian.model.ibeauty.form.output.PackageCardProductAccountOutputForm">
        SELECT tpa.ID,
               tpa.ProductID,
               p.Name,
               ISNULL(p.Alias, '')                                       Alias,
               CASE
                   WHEN tpa.TotalAmount / tpa.Quantity &lt;= 0
                       THEN tpa.Balance - dbo.getRefundBillProductQuantity(tpa.ID)
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) &lt;= 0 THEN 0
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) > 0 THEN (
                           tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity /
                                                 tpa.TotalAmount) - dbo.getRefundBillProductQuantity(tpa.ID))
                   END                                                   ValidBalance,
               dbo.getRefundBillProductQuantity(tpa.ID)                  RefundBalance,
               tpa.Quantity,
               tpa.ArrearAmount,
               tpa.TotalAmount,
               tpa.Balance,
               CAST(tpa.TotalAmount / tpa.Quantity as decimal(38, 2)) AS Amount,
               tpa.Price,
               tpa.IsLargess,
               CONVERT(VARCHAR (10), tpa.BuyDate, 120)                   BuyDate
        FROM TB_ProductAccount tpa WITH(NOLOCK)
                 LEFT JOIN TE_Product p WITH(NOLOCK)
        ON p.ID = tpa.ProductID
            LEFT JOIN TB_Product bp
        WITH (NOLOCK)
        ON p.ID = bp.ProductID
        WHERE tpa.Active=1
          AND tpa.ArrearAmount
         &gt; 0
          AND tpa.PackageCardAccountID = #{PackageCardAccountID}
        ORDER BY tpa.IsLargess, p.Name, tpa.ID DESC
    </select>

    <!--欠款储值卡账户明细-->
    <select id="arrearPackageCardSavingCardAccount" parameterType="java.util.Map"
            resultType="com.zhelian.model.ibeauty.form.output.SavingCardAccountForm">
        SELECT sca.ID,
               sca.SavingCardID,
               s.Name,
               ISNULL(s.Alias, '')                                                                 Alias,
               CASE
                   WHEN sca.ValidType = 3 THEN '永久有效'
                   ELSE CONVERT(VARCHAR (10), sca.ValidDate, 120)
                   END                                                                             ValidDayName,
               sca.Balance,
               sca.LargessBalance,
               sca.Balance + sca.LargessBalance                                                    TotalBalance,
               CASE
                   WHEN sca.Amount > 0 THEN
                       CONVERT(DECIMAL (18, 2), sca.Balance - sca.ArrearAmount +
                                                sca.LargessBalance - sca.ArrearAmount * sca.LargessAmount / sca.Amount
                           - dbo.getRefundBillSavingCardAmount(sca.ID) -
                                                dbo.getRefundBillSavingCardLargessAmount(sca.ID))
                   ELSE sca.LargessBalance
                   END                                                                             ValidBalance,
               sca.Amount                                                                          TotalAmount,
               sca.ArrearAmount,
               dbo.getRefundBillSavingCardTotalAmount(sca.ID)                                      RefundAmount,
               CONVERT(VARCHAR (10), sca.BuyDate, 120)                                             BuyDate,
               CONVERT(DECIMAL (18, 2),
                       sca.Balance - sca.ArrearAmount - dbo.getRefundBillSavingCardAmount(sca.ID)) AllowRefundBalance,
               CASE
                   WHEN sca.Amount > 0 THEN
                       CONVERT(DECIMAL (18, 2), sca.LargessBalance - sca.ArrearAmount * sca.LargessAmount / sca.Amount -
                                                dbo.getRefundBillSavingCardLargessAmount(sca.ID))
                   ELSE sca.LargessBalance
                   END                                                                             AllowRefundLargessBalance,
               sca.ID                                                                              AccountID
        FROM TB_SavingCardAccount sca WITH(NOLOCK)
                 LEFT JOIN TB_SavingCard s WITH(NOLOCK)
        ON sca.SavingCardID = s.ID
        WHERE sca.Active=1
          AND sca.ArrearAmount
         &gt; 0
          AND sca.PackageCardAccountID = #{PackageCardAccountID}
        ORDER BY s.Name, sca.ID DESC
    </select>

    <!--欠款通用次卡账户明细-->
    <select id="arrearPackageCardGeneralCardAccount" parameterType="java.util.Map"
            resultType="com.zhelian.model.ibeauty.form.output.GeneralCardAccountOutputForm">
        SELECT tpa.ID,
               tpa.GeneralCardID,
               p.Name,
               ISNULL(p.Alias, '')                                        Alias,
               CASE
                   WHEN tpa.ValidType = 3 THEN '永久有效'
                   ELSE CONVERT(VARCHAR (10), tpa.ValidDate, 120)
                   END                                                    ValidDayName,
               CASE
                   WHEN tpa.TotalAmount / tpa.CardTimes &lt;= 0
                       THEN tpa.Balance - dbo.getRefundBillGeneralCardQuantity(tpa.ID)
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.CardTimes / tpa.TotalAmount)) &lt;= 0 THEN 0
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.CardTimes / tpa.TotalAmount)) > 0 THEN (
                           tpa.Balance - CEILING(tpa.ArrearAmount * tpa.CardTimes /
                                                 tpa.TotalAmount) - dbo.getRefundBillGeneralCardQuantity(tpa.ID))
                   END                                                    ValidBalance,
               tpa.CardTimes,
               dbo.getRefundBillGeneralCardQuantity(tpa.ID)               RefundAmount,
               tpa.ArrearAmount,
               tpa.TotalAmount,
               tpa.Balance,
               CAST(tpa.TotalAmount / tpa.CardTimes as decimal(38, 2)) AS Amount,
               tpa.Price,
               tpa.IsLargess,
               CONVERT(VARCHAR (10), tpa.BuyDate, 120)                    BuyDate
        FROM TB_GeneralCardAccount tpa
                 LEFT JOIN TB_GeneralCard p WITH(NOLOCK)
        ON p.ID = tpa.GeneralCardID
        WHERE tpa.Active=1
          AND tpa.PackageCardAccountID = #{PackageCardAccountID}
          AND tpa.ArrearAmount
         &gt; 0
        ORDER BY tpa.IsLargess, p.Name, tpa.ID DESC
    </select>

    <!--欠款时效卡账户明细-->
    <select id="arrearPackageCardTimeCardAccount" parameterType="java.util.Map"
            resultType="com.zhelian.model.ibeauty.form.output.TimeCardAccountOutputForm">
        SELECT tpa.ID,
               tpa.TimeCardID,
               p.Name,
               ISNULL(p.Alias, '')                                                          Alias,
               CASE
                   WHEN tpa.ValidType = 3 THEN '永久有效'
                   ELSE CONVERT(VARCHAR (10), tpa.ValidDate, 120)
                   END                                                                      ValidDayName,
               CASE
                   WHEN tpa.ArrearAmount = 0 THEN tpa.PerformanceBalance
                   WHEN tpa.ArrearAmount > 0 AND tpa.PerformanceTimes > 0 THEN (tpa.PerformanceBalance -
                                                                                CEILING(tpa.ArrearAmount / (tpa.TotalAmount / tpa.PerformanceTimes)))
                   ELSE 0 END                                                               ValidBalance,
               p.ConsumeCycle,
               p.CycleLimitAmount,
               ISNULL(dbo.GetTimeCardConsumeCycleAmount(
                              (SELECT TOP 1 CONVERT (VARCHAR (10),BillDate,120) BillDate
                                  FROM TB_TreatBillTimeCard d
                                  LEFT JOIN TB_TreatBill m ON m.ID=d.TreatBillID
                                  WHERE m.BillStatus='20' and d.TimeCardAccountID =tpa.ID and m.BillType = '10'
                                  ORDER BY BillDate),p.ConsumeCycle,#{BillDate},tpa.ID), 0) ConsumeCycleAmount,
               tpa.PerformanceTimes,
               tpa.PerformanceBalance,
               tpa.ArrearAmount,
               tpa.PayPerformanceBalanceAmount + tpa.CardPerformanceBalanceAmount +
               tpa.CardLargessPerformanceBalanceAmount                                      PerformanceBalanceAmount,
               CASE
                   WHEN tpa.PerformanceTimes = 0 THEN 0
                   ELSE CAST(tpa.TotalAmount / tpa.PerformanceTimes as decimal(38, 2))
                   END                                                                      Amount,
               tpa.Price,
               tpa.IsLargess,
               CONVERT(VARCHAR (10), tpa.BuyDate, 120)                                      BuyDate,
               tpa.TotalAmount,
               ISNULL(
                       (SELECT SUM(T.Quantity)
                        FROM (SELECT CASE
                                         WHEN
                                             a2.BillType = '10' then a1.Quantity
                                         ELSE a1.Quantity * -1
                                         END Quantity
                              from TB_TreatBillTimeCard a1
                                       JOIN TB_TreatBill a2 ON a1.TreatBillID = a2.ID
                              WHERE a1.TimeCardAccountID = tpa.ID
                                AND a2.BillStatus = '20') T), 0)                            ConsumeAmount
        FROM TB_TimeCardAccount tpa
                 LEFT JOIN TB_TimeCard p WITH(NOLOCK)
        ON p.ID = tpa.TimeCardID
        WHERE tpa.Active=1
          AND tpa.ArrearAmount
         &gt; 0
          AND tpa.PackageCardAccountID = #{PackageCardAccountID}
        ORDER BY tpa.IsLargess, p.Name, tpa.ID DESC
    </select>

    <update id="updatePackageCardAccountTotalAmount" parameterType="Integer">
        UPDATE t1
        SET t1.TotalAmount                     = t2.TotalAmount,
            t1.PayAmount                       = t2.PayAmount,
            t1.SavingCardDeductionAmount=t2.SavingCardDeductionAmount,
            t1.SavingCardDeductionLargessAmount=t2.SavingCardDeductionLargessAmount,
            t1.ArrearAmount=t2.ArrearAmount FROM TB_PackageCardAccount t1
        INNER JOIN
        (
        SELECT PackageCardAccountID,SUM(TotalAmount) TotalAmount,SUM(PayAmount) PayAmount, SUM(SavingCardDeductionAmount) SavingCardDeductionAmount, SUM(SavingCardDeductionLargessAmount) SavingCardDeductionLargessAmount,SUM(ArrearAmount) ArrearAmount  FROM (
        SELECT a1.PackageCardAccountID,a1.TotalAmount TotalAmount,a1.PayAmount,a1.SavingCardDeductionAmount,a1.SavingCardDeductionLargessAmount,a1.ArrearAmount FROM TB_GeneralCardAccount a1,TB_PackageCardAccount a2
        WHERE a1.PackageCardAccountID=a2.ID and a1.IsLargess=0 and a2.SaleBillPackageCardID=#{SaleBillPackageCardID}
            UNION ALL
            SELECT a1.PackageCardAccountID, a1.TotalAmount TotalAmount, a1.PayAmount, a1.SavingCardDeductionAmount, a1.SavingCardDeductionLargessAmount, a1.ArrearAmount FROM TB_ProductAccount a1, TB_PackageCardAccount a2
            WHERE a1.PackageCardAccountID=a2.ID and a1.IsLargess=0 and a2.SaleBillPackageCardID=#{SaleBillPackageCardID}
            UNION ALL
            SELECT a1.PackageCardAccountID, a1.TotalAmount TotalAmount, a1.PayAmount, a1.SavingCardDeductionAmount, a1.SavingCardDeductionLargessAmount, a1.ArrearAmount FROM TB_ProjectAccount a1, TB_PackageCardAccount a2
            WHERE a1.PackageCardAccountID=a2.ID and a1.IsLargess=0 and a2.SaleBillPackageCardID=#{SaleBillPackageCardID}
            UNION ALL
            SELECT a1.PackageCardAccountID, a1.TotalAmount TotalAmount, a1.PayAmount, a1.SavingCardDeductionAmount, a1.SavingCardDeductionLargessAmount, a1.ArrearAmount FROM TB_TimeCardAccount a1, TB_PackageCardAccount a2
            WHERE a1.PackageCardAccountID=a2.ID and a1.IsLargess=0 and a2.SaleBillPackageCardID=#{SaleBillPackageCardID}
            UNION ALL
            SELECT a1.PackageCardAccountID, a1.Amount TotalAmount, a1.Amount-a1.ArrearAmount PayAmount, 0 SavingCardDeductionAmount, 0 SavingCardDeductionLargessAmount, a1.ArrearAmount FROM TB_SavingCardAccount a1, TB_PackageCardAccount a2
            WHERE a1.PackageCardAccountID=a2.ID and a2.SaleBillPackageCardID=#{SaleBillPackageCardID}
            ) a GROUP BY PackageCardAccountID) t2
        on t1.ID=t2.PackageCardAccountID and t1.SaleBillPackageCardID=#{SaleBillPackageCardID}
    </update>

    <select id="getPackageCardAccount" resultType="com.zhelian.model.ibeauty.entity.PackageCardAccountEntity">
        SELECT *
        FROM TB_PackageCardAccount
        WHERE ID = #{PackageCardAccountID}
    </select>

    <select id="getPackageCardAccountValidDayName" resultType="String">
        SELECT CASE
                   WHEN ValidType = 3 THEN '永久有效'
                   ELSE CONVERT(VARCHAR (10), ValidDate, 120)
                   END ValidDayName
        FROM TB_PackageCardAccount
        WHERE ID = #{PackageCardAccountID}
    </select>

    <update id="updateArrearPackageCardAccount"
            parameterType="com.zhelian.model.ibeauty.entity.PackageCardAccountEntity">
        UPDATE TB_PackageCardAccount
        <set>
            <if test="ArrearAmount != null">ArrearAmount = #{ArrearAmount},</if>
            <if test="PayAmount != null">PayAmount = #{PayAmount},</if>
            <if test="Active != null">Active = #{Active}</if>
        </set>
        WHERE ID = #{ID}
    </update>

    <select id="getPostPonePackageCardAccount"
            resultType="com.zhelian.model.ibeauty.form.output.PackageCardAccountOutputForm">
        SELECT
        w.ID,
        w.PackageCardID,
        p.Name,
        p.Alias,
        CASE
        WHEN w.ValidType = 3 THEN '永久有效'
        ELSE CONVERT(VARCHAR(10),w.ValidDate,120)
        END ValidDayName,
        ISNULL(w.IsLargess, 0) IsLargess,
        CONVERT(VARCHAR(10),w.BuyDate,120) BuyDate,
        w.Price,
        w.ArrearAmount
        FROM
        (--项目
        SELECT DISTINCT a.PackageCardAccountID FROM TB_ProjectAccount a WITH(NOLOCK) WHERE a.PackageCardAccountID IS NOT NULL AND
        a.Balance>0 AND a.CustomerID = #{CustomerID} AND a.Active=1
        UNION
        --储值卡
        SELECT DISTINCT b.PackageCardAccountID FROM TB_SavingCardAccount b WITH(NOLOCK) WHERE b.PackageCardAccountID IS NOT NULL AND
        (b.Balance>0 OR b.LargessBalance>0) AND b.CustomerID = #{CustomerID} AND b.Active=1
        UNION
        --产品
        SELECT DISTINCT c.PackageCardAccountID FROM TB_ProductAccount c WITH(NOLOCK) WHERE c.Balance>0 AND c.PackageCardAccountID IS
        NOT NULL AND c.CustomerID = #{CustomerID} AND c.Active=1
        UNION
        --通用次卡
        SELECT DISTINCT d.PackageCardAccountID FROM TB_GeneralCardAccount d WITH(NOLOCK) WHERE d.Balance>0 AND d.PackageCardAccountID
        IS NOT NULL AND d.CustomerID = #{CustomerID} AND d.Active=1
        UNION
        --时效卡
        SELECT DISTINCT e.PackageCardAccountID FROM TB_TimeCardAccount e WITH(NOLOCK) WHERE e.PackageCardAccountID IS NOT NULL AND
        e.CustomerID = #{CustomerID} AND e.Active=1
        ) q
        INNER JOIN TB_PackageCardAccount w WITH(NOLOCK) ON w.ID = q.PackageCardAccountID
        LEFT JOIN TB_PackageCard p WITH(NOLOCK) ON w.PackageCardID = p.ID
        <where>
            w.Active=1
            <if test="Name!=null and Name!=''">
                AND (p.Name LIKE '%'+ #{Name} +'%' OR p.Alias LIKE '%'+ #{Name} +'%'
                OR p.NameFullPinYin LIKE '%'+ #{Name} +'%' OR p.NameAbbrPinYin LIKE '%'+ #{Name} +'%'
                OR p.AliasFullPinYin LIKE '%'+ #{Name} +'%' OR p.AliasAbbrPinYin LIKE '%'+ #{Name} +'%' )
            </if>
        </where>
        ORDER BY w.ID DESC
    </select>

    <update id="updatePackageCardAccountValidDate"
            parameterType="com.zhelian.model.ibeauty.entity.PackageCardAccountEntity">
        UPDATE dbo.TB_PackageCardAccount
        SET ValidType = #{ValidType},
            ValidDay = #{ValidDay},
            ValidDate = #{ValidDate}
        WHERE
            ID = #{ID}
    </update>

    <select id="getTranserPackageCardAccount"
            resultType="com.zhelian.model.ibeauty.form.output.PackageCardAccountOutputForm">
        SELECT
        w.ID,
        w.PackageCardID,
        p.Name,
        p.Alias,
        CASE
        WHEN w.ValidType = 3 THEN '永久有效'
        ELSE CONVERT(VARCHAR(10),w.ValidDate,120)
        END ValidDayName,
        ISNULL(w.IsLargess, 0) IsLargess,
        CONVERT(VARCHAR(10),w.BuyDate,120) BuyDate,
        w.Price,
        w.TotalAmount,
        w.ArrearAmount,
        CASE WHEN tmp.PackageCardAccountID IS NULL THEN 1
        ELSE 0 END IsFullRefund,
        w.Remark
        FROM
        TB_PackageCardAccount w WITH(NOLOCK)
        LEFT JOIN TB_PackageCard p WITH(NOLOCK) ON w.PackageCardID = p.ID
        LEFT JOIN (
        --项目
        SELECT a.PackageCardAccountID FROM TB_ProjectAccount a WITH(NOLOCK)
        WHERE a.PackageCardAccountID is not null AND a.Balance>0 AND a.CustomerID = #{CustomerID} AND a.Active=1
        AND NOT EXISTS (SELECT trs.ID FROM TB_RefundSaleBillProject trs WITH (NOLOCK)
        LEFT JOIN TB_RefundSaleBill trb WITH (NOLOCK) ON trb.ID=trs.RefundBillID
        WHERE trs.ProjectAccountID=a.ID AND trb.ApprovalStatus='20'
        )
        UNION
        --储值卡
        SELECT b.PackageCardAccountID FROM TB_SavingCardAccount b WITH(NOLOCK) WHERE b.PackageCardAccountID is not null
        AND (b.Balance>0 OR b.LargessBalance>0) AND b.CustomerID = #{CustomerID} AND b.Active=1
        AND NOT EXISTS (SELECT trs.ID FROM TB_RefundSaleBillSavingCard trs WITH (NOLOCK)
        LEFT JOIN TB_RefundSaleBill trb WITH (NOLOCK) ON trb.ID=trs.RefundBillID
        WHERE trs.SavingCardAccountID=b.ID AND trb.ApprovalStatus='20'
        )
        UNION
        --产品
        SELECT c.PackageCardAccountID FROM TB_ProductAccount c WITH(NOLOCK) WHERE c.Balance>0 AND c.PackageCardAccountID
        is not null AND c.CustomerID = #{CustomerID} AND c.Active=1
        AND NOT EXISTS (SELECT trs.ID FROM TB_RefundSaleBillProduct trs WITH (NOLOCK)
        LEFT JOIN TB_RefundSaleBill trb WITH (NOLOCK) ON trb.ID=trs.RefundBillID
        WHERE trs.ProductAccountID=c.ID AND trb.ApprovalStatus='20'
        )
        UNION
        --通用次卡
        SELECT d.PackageCardAccountID FROM TB_GeneralCardAccount d WITH(NOLOCK) WHERE d.Balance>0 AND
        d.PackageCardAccountID is not null AND d.CustomerID = #{CustomerID} AND d.Active=1
        AND NOT EXISTS (SELECT trs.ID FROM TB_RefundSaleBillGeneralCard trs WITH (NOLOCK)
        LEFT JOIN TB_RefundSaleBill trb WITH (NOLOCK) ON trb.ID=trs.RefundBillID
        WHERE trs.GeneralCardAccountID=d.ID AND trb.ApprovalStatus='20'
        )
        UNION
        --时效卡
        SELECT e.PackageCardAccountID FROM TB_TimeCardAccount e WITH(NOLOCK) WHERE e.PackageCardAccountID is not null
        AND e.CustomerID = #{CustomerID} AND e.Active=1
        AND NOT EXISTS (SELECT trs.ID FROM TB_RefundSaleBillTimeCard trs WITH (NOLOCK)
        LEFT JOIN TB_RefundSaleBill trb WITH (NOLOCK) ON trb.ID=trs.RefundBillID
        WHERE trs.TimeCardAccountID=e.ID AND trb.ApprovalStatus='20'
        )
        ) tmp ON tmp.PackageCardAccountID=w.ID
        <where>
            w.Active=1 AND w.CustomerID=#{CustomerID}
            AND (w.ValidDate IS NULL OR w.ValidDate &gt;= convert(varchar(10), GETDATE(),
            120))
            AND EXISTS (
            --项目
            SELECT a.PackageCardAccountID FROM TB_ProjectAccount a WITH(NOLOCK) WHERE a.PackageCardAccountID=w.ID AND
            a.Balance>0 AND a.CustomerID = #{CustomerID} AND a.Active=1
            UNION
            --储值卡
            SELECT b.PackageCardAccountID FROM TB_SavingCardAccount b WITH(NOLOCK) WHERE b.PackageCardAccountID=w.ID AND
            (b.Balance>0 OR b.LargessBalance>0) AND b.CustomerID = #{CustomerID} AND b.Active=1
            UNION
            --产品
            SELECT c.PackageCardAccountID FROM TB_ProductAccount c WITH(NOLOCK) WHERE c.Balance>0 AND
            c.PackageCardAccountID=w.ID AND c.CustomerID = #{CustomerID} AND c.Active=1
            UNION
            --通用次卡
            SELECT d.PackageCardAccountID FROM TB_GeneralCardAccount d WITH(NOLOCK) WHERE d.Balance>0 AND
            d.PackageCardAccountID=w.ID AND d.CustomerID = #{CustomerID} AND d.Active=1
            UNION
            --时效卡
            SELECT e.PackageCardAccountID FROM TB_TimeCardAccount e WITH(NOLOCK) WHERE e.PackageCardAccountID=w.ID AND
            e.CustomerID = #{CustomerID} AND e.Active=1
            )
            <if test="Name!=null and Name!=''">
                AND (p.Name LIKE '%'+ #{Name} +'%' OR p.Alias LIKE '%'+ #{Name} +'%'
                OR p.NameFullPinYin LIKE '%'+ #{Name} +'%' OR p.NameAbbrPinYin LIKE '%'+ #{Name} +'%'
                OR p.AliasFullPinYin LIKE '%'+ #{Name} +'%' OR p.AliasAbbrPinYin LIKE '%'+ #{Name} +'%' )
            </if>
        </where>
        ORDER BY w.ID DESC
    </select>

    <select id="getRefundPackageCardAccount"
            resultType="com.zhelian.model.ibeauty.form.output.PackageCardAccountOutputForm">
        SELECT
        w.ID,
        w.PackageCardID,
        p.Name,
        p.Alias,
        CASE
        WHEN w.ValidType = 3 THEN '永久有效'
        ELSE CONVERT(VARCHAR(10),w.ValidDate,120)
        END ValidDayName,
        ISNULL(w.IsLargess, 0) IsLargess,
        CONVERT(VARCHAR(10),w.BuyDate,120) BuyDate,
        w.Price,
        w.TotalAmount,
        w.ArrearAmount,
        CASE WHEN tmp.PackageCardAccountID IS NULL THEN 1
        ELSE 0 END IsFullRefund,
        w.Remark
        FROM
        TB_PackageCardAccount w WITH(NOLOCK)
        LEFT JOIN TB_PackageCard p WITH(NOLOCK) ON w.PackageCardID = p.ID
        LEFT JOIN (
        --项目
        SELECT a.PackageCardAccountID FROM TB_ProjectAccount a WITH(NOLOCK)
        WHERE a.PackageCardAccountID is not null AND a.Balance>0 AND a.CustomerID = #{CustomerID} AND a.Active=1
        AND NOT EXISTS (SELECT trs.ID FROM TB_RefundSaleBillProject trs WITH (NOLOCK)
        LEFT JOIN TB_RefundSaleBill trb WITH (NOLOCK) ON trb.ID=trs.RefundBillID
        WHERE trs.ProjectAccountID=a.ID AND trb.ApprovalStatus='20'
        )
        UNION
        --储值卡
        SELECT b.PackageCardAccountID FROM TB_SavingCardAccount b WITH(NOLOCK) WHERE b.PackageCardAccountID is not null
        AND (b.Balance>0 OR b.LargessBalance>0) AND b.CustomerID = #{CustomerID} AND b.Active=1
        AND NOT EXISTS (SELECT trs.ID FROM TB_RefundSaleBillSavingCard trs WITH (NOLOCK)
        LEFT JOIN TB_RefundSaleBill trb WITH (NOLOCK) ON trb.ID=trs.RefundBillID
        WHERE trs.SavingCardAccountID=b.ID AND trb.ApprovalStatus='20'
        )
        UNION
        --产品
        SELECT c.PackageCardAccountID FROM TB_ProductAccount c WITH(NOLOCK) WHERE c.Balance>0 AND c.PackageCardAccountID
        is not null AND c.CustomerID = #{CustomerID} AND c.Active=1
        AND NOT EXISTS (SELECT trs.ID FROM TB_RefundSaleBillProduct trs WITH (NOLOCK)
        LEFT JOIN TB_RefundSaleBill trb WITH (NOLOCK) ON trb.ID=trs.RefundBillID
        WHERE trs.ProductAccountID=c.ID AND trb.ApprovalStatus='20'
        )
        UNION
        --通用次卡
        SELECT d.PackageCardAccountID FROM TB_GeneralCardAccount d WITH(NOLOCK) WHERE d.Balance>0 AND
        d.PackageCardAccountID is not null AND d.CustomerID = #{CustomerID} AND d.Active=1
        AND NOT EXISTS (SELECT trs.ID FROM TB_RefundSaleBillGeneralCard trs WITH (NOLOCK)
        LEFT JOIN TB_RefundSaleBill trb WITH (NOLOCK) ON trb.ID=trs.RefundBillID
        WHERE trs.GeneralCardAccountID=d.ID AND trb.ApprovalStatus='20'
        )
        UNION
        --时效卡
        SELECT e.PackageCardAccountID FROM TB_TimeCardAccount e WITH(NOLOCK) WHERE e.PackageCardAccountID is not null
        AND e.CustomerID = #{CustomerID} AND e.Active=1
        AND NOT EXISTS (SELECT trs.ID FROM TB_RefundSaleBillTimeCard trs WITH (NOLOCK)
        LEFT JOIN TB_RefundSaleBill trb WITH (NOLOCK) ON trb.ID=trs.RefundBillID
        WHERE trs.TimeCardAccountID=e.ID AND trb.ApprovalStatus='20'
        )
        ) tmp ON tmp.PackageCardAccountID=w.ID
        <where>
            w.Active=1 AND w.CustomerID=#{CustomerID}
            AND w.Channel != '20'
            AND (w.ValidDate IS NULL OR w.ValidDate &gt;= convert(varchar(10), GETDATE(),
            120))
            AND EXISTS (
            --项目
            SELECT a.PackageCardAccountID FROM TB_ProjectAccount a WITH(NOLOCK) WHERE a.PackageCardAccountID=w.ID AND
            a.Balance>0 AND a.CustomerID = #{CustomerID} AND a.Active=1
            UNION
            --储值卡
            SELECT b.PackageCardAccountID FROM TB_SavingCardAccount b WITH(NOLOCK) WHERE b.PackageCardAccountID=w.ID AND
            (b.Balance>0 OR b.LargessBalance>0) AND b.CustomerID = #{CustomerID} AND b.Active=1
            UNION
            --产品
            SELECT c.PackageCardAccountID FROM TB_ProductAccount c WITH(NOLOCK) WHERE c.Balance>0 AND
            c.PackageCardAccountID=w.ID AND c.CustomerID = #{CustomerID} AND c.Active=1
            UNION
            --通用次卡
            SELECT d.PackageCardAccountID FROM TB_GeneralCardAccount d WITH(NOLOCK) WHERE d.Balance>0 AND
            d.PackageCardAccountID=w.ID AND d.CustomerID = #{CustomerID} AND d.Active=1
            UNION
            --时效卡
            SELECT e.PackageCardAccountID FROM TB_TimeCardAccount e WITH(NOLOCK) WHERE e.PackageCardAccountID=w.ID AND
            e.CustomerID = #{CustomerID} AND e.Active=1
            )
            <if test="Name!=null and Name!=''">
                AND (p.Name LIKE '%'+ #{Name} +'%' OR p.Alias LIKE '%'+ #{Name} +'%'
                OR p.NameFullPinYin LIKE '%'+ #{Name} +'%' OR p.NameAbbrPinYin LIKE '%'+ #{Name} +'%'
                OR p.AliasFullPinYin LIKE '%'+ #{Name} +'%' OR p.AliasAbbrPinYin LIKE '%'+ #{Name} +'%' )
            </if>
        </where>
        ORDER BY w.ID DESC
    </select>


    <!--转账项目账户明细-->
    <select id="transerPackageCardProjectAccount" parameterType="java.util.Map"
            resultType="com.zhelian.model.ibeauty.form.output.PackageCardProjectAccountOutputForm">
        SELECT tpa.ID,
               tpa.ProjectID,
               p.Name,
               ISNULL(p.Alias, '')                                       Alias,
               CASE
                   WHEN tpa.TotalAmount / tpa.Quantity &lt;= 0
                       THEN tpa.Balance - dbo.getRefundBillProjectQuantity(tpa.ID)
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) &lt;= 0 THEN 0
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) > 0 THEN (
                           tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity /
                                                 tpa.TotalAmount) - dbo.getRefundBillProjectQuantity(tpa.ID))
                   END                                                   ValidBalance,
               dbo.getRefundBillProjectQuantity(tpa.ID)                  RefundBalance,
               tpa.Quantity,
               tpa.ArrearAmount,
               tpa.TotalAmount,
               CAST(tpa.TotalAmount / tpa.Quantity as decimal(38, 2)) AS Amount,
               tpa.Balance,
               tpa.Price,
               tpa.IsLargess,
               CONVERT(VARCHAR (10), tpa.BuyDate, 120)                   BuyDate,
               tpa.PayPerformanceBalanceAmount + tpa.CardPerformanceBalanceAmount +
               tpa.CardLargessPerformanceBalanceAmount                   PerformanceBalanceAmount,
               tpa.Remark
        FROM TB_ProjectAccount tpa WITH(NOLOCK)
        LEFT JOIN TB_Project p
        WITH (NOLOCK)
        ON p.ID = tpa.ProjectID
        WHERE tpa.Active=1
          AND tpa.Balance
            > 0
          AND tpa.PackageCardAccountID = #{PackageCardAccountID}
        ORDER BY tpa.IsLargess, p.Name, tpa.ID DESC
    </select>

    <select id="refundPackageCardProjectAccount" parameterType="java.util.Map"
            resultType="com.zhelian.model.ibeauty.form.output.PackageCardProjectAccountOutputForm">
        SELECT tpa.ID,
               tpa.ProjectID,
               p.Name,
               ISNULL(p.Alias, '')                                       Alias,
               CASE
                   WHEN tpa.TotalAmount / tpa.Quantity &lt;= 0
                       THEN tpa.Balance - dbo.getRefundBillProjectQuantity(tpa.ID)
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) &lt;= 0 THEN 0
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) > 0 THEN (
                       tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity /
                                             tpa.TotalAmount) - dbo.getRefundBillProjectQuantity(tpa.ID))
                   END                                                   ValidBalance,
               dbo.getRefundBillProjectQuantity(tpa.ID)                  RefundBalance,
               tpa.Quantity,
               tpa.ArrearAmount,
               tpa.TotalAmount,
               CAST(tpa.TotalAmount / tpa.Quantity as decimal(38, 2)) AS Amount,
               tpa.Balance,
               tpa.Price,
               tpa.IsLargess,
               CONVERT(VARCHAR (10), tpa.BuyDate, 120)                   BuyDate,
               tpa.PayPerformanceBalanceAmount + tpa.CardPerformanceBalanceAmount +
               tpa.CardLargessPerformanceBalanceAmount                   PerformanceBalanceAmount,
               tpa.Remark
        FROM TB_ProjectAccount tpa WITH(NOLOCK)
        LEFT JOIN TB_Project p
        WITH (NOLOCK)
        ON p.ID = tpa.ProjectID
        WHERE tpa.Active=1
          AND tpa.Channel != '20'
          AND tpa.Balance
            > 0
          AND tpa.PackageCardAccountID = #{PackageCardAccountID}
        ORDER BY tpa.IsLargess, p.Name, tpa.ID DESC
    </select>

    <!--转账产品账户明细-->
    <select id="transerPackageCardProductAccount" parameterType="java.util.Map"
            resultType="com.zhelian.model.ibeauty.form.output.PackageCardProductAccountOutputForm">
        SELECT tpa.ID,
               tpa.ProductID,
               p.Name,
               ISNULL(p.Alias, '')                                       Alias,
               CASE
                   WHEN tpa.TotalAmount / tpa.Quantity &lt;= 0
                       THEN tpa.Balance - dbo.getRefundBillProductQuantity(tpa.ID)
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) &lt;= 0 THEN 0
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) > 0 THEN (
                           tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity /
                                                 tpa.TotalAmount) - dbo.getRefundBillProductQuantity(tpa.ID))
                   END                                                   ValidBalance,
               dbo.getRefundBillProductQuantity(tpa.ID)                  RefundBalance,
               tpa.Quantity,
               tpa.ArrearAmount,
               tpa.TotalAmount,
               CAST(tpa.TotalAmount / tpa.Quantity as decimal(38, 2)) AS Amount,
               tpa.Balance,
               tpa.Price,
               tpa.IsLargess,
               CONVERT(VARCHAR (10), tpa.BuyDate, 120)                   BuyDate,
               tpa.PayPerformanceBalanceAmount + tpa.CardPerformanceBalanceAmount +
               tpa.CardLargessPerformanceBalanceAmount                   PerformanceBalanceAmount,
               tpa.Remark
        FROM TB_ProductAccount tpa WITH(NOLOCK)
        LEFT JOIN TE_Product p
        WITH (NOLOCK)
        ON p.ID = tpa.ProductID
            LEFT JOIN TB_Product bp
        WITH (NOLOCK)
        ON p.ID = bp.ProductID
        WHERE
            tpa.Active=1
          AND tpa.Balance
         &gt; 0
          AND tpa.PackageCardAccountID = #{PackageCardAccountID}
        ORDER BY tpa.IsLargess, p.Name, tpa.ID DESC
    </select>

    <select id="refundPackageCardProductAccount" parameterType="java.util.Map"
            resultType="com.zhelian.model.ibeauty.form.output.PackageCardProductAccountOutputForm">
        SELECT tpa.ID,
               tpa.ProductID,
               p.Name,
               ISNULL(p.Alias, '')                                       Alias,
               CASE
                   WHEN tpa.TotalAmount / tpa.Quantity &lt;= 0
                       THEN tpa.Balance - dbo.getRefundBillProductQuantity(tpa.ID)
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) &lt;= 0 THEN 0
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) > 0 THEN (
                       tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity /
                                             tpa.TotalAmount) - dbo.getRefundBillProductQuantity(tpa.ID))
                   END                                                   ValidBalance,
               dbo.getRefundBillProductQuantity(tpa.ID)                  RefundBalance,
               tpa.Quantity,
               tpa.ArrearAmount,
               tpa.TotalAmount,
               CAST(tpa.TotalAmount / tpa.Quantity as decimal(38, 2)) AS Amount,
               tpa.Balance,
               tpa.Price,
               tpa.IsLargess,
               CONVERT(VARCHAR (10), tpa.BuyDate, 120)                   BuyDate,
               tpa.PayPerformanceBalanceAmount + tpa.CardPerformanceBalanceAmount +
               tpa.CardLargessPerformanceBalanceAmount                   PerformanceBalanceAmount,
               tpa.Remark
        FROM TB_ProductAccount tpa WITH(NOLOCK)
        LEFT JOIN TE_Product p
        WITH (NOLOCK)
        ON p.ID = tpa.ProductID
            LEFT JOIN TB_Product bp
        WITH (NOLOCK)
        ON p.ID = bp.ProductID
        WHERE
            tpa.Active=1
          AND tpa.Channel != '20'
          AND tpa.Balance
         &gt; 0
          AND tpa.PackageCardAccountID = #{PackageCardAccountID}
        ORDER BY tpa.IsLargess, p.Name, tpa.ID DESC
    </select>

    <!--转账储值卡账户明细-->
    <select id="transerPackageCardSavingCardAccount" parameterType="java.util.Map"
            resultType="com.zhelian.model.ibeauty.form.output.SavingCardAccountExtendForm">
        SELECT sca.ID,
               sca.SavingCardID,
               s.Name,
               ISNULL(s.Alias, '')                                                                 Alias,
               0 AS                                                                                Type,
               sca.ID                                                                              AccountID,
               CASE
                   WHEN sca.ValidType = 3 THEN '永久有效'
                   ELSE CONVERT(VARCHAR (10), sca.ValidDate, 120)
                   END                                                                             ValidDayName,
               sca.Balance,
               sca.LargessBalance,
               sca.Balance + sca.LargessBalance                                                    TotalBalance,
               sca.Amount                                                                          TotalAmount,
               CASE
                   WHEN sca.Amount > 0 THEN
                       CONVERT(DECIMAL (18, 2), sca.Balance - sca.ArrearAmount +
                                                sca.LargessBalance - sca.ArrearAmount * sca.LargessAmount / sca.Amount
                           - dbo.getRefundBillSavingCardAmount(sca.ID) -
                                                dbo.getRefundBillSavingCardLargessAmount(sca.ID))
                   ELSE sca.LargessBalance
                   END                                                                             ValidBalance,
               CASE
                   WHEN sca.Amount = 0 THEN 1
                   ELSE 0
                   END                                                                             IsLargess,
               sca.Amount                                                                          TotalAmount,
               sca.ArrearAmount,
               dbo.getRefundBillSavingCardTotalAmount(sca.ID)                                      RefundAmount,
               CONVERT(VARCHAR (10), sca.BuyDate, 120)                                             BuyDate,
               CONVERT(DECIMAL (18, 2),
                       sca.Balance - sca.ArrearAmount - dbo.getRefundBillSavingCardAmount(sca.ID)) AllowRefundBalance,
               CASE
                   WHEN sca.Amount > 0 THEN
                       CONVERT(DECIMAL (18, 2), sca.LargessBalance - sca.ArrearAmount * sca.LargessAmount / sca.Amount -
                                                dbo.getRefundBillSavingCardLargessAmount(sca.ID))
                   ELSE sca.LargessBalance
                   END                                                                             AllowRefundLargessBalance,
               CONVERT(DECIMAL (18, 2),
                       sca.Balance - sca.ArrearAmount - dbo.getRefundBillSavingCardAmount(sca.ID)) AllowTransferBalance,
               CONVERT(DECIMAL (18, 2), sca.LargessBalance -
                                        dbo.getRefundBillSavingCardLargessAmount(sca.ID))          AllowTransferLargessBalance,
            sca.Remark
        FROM TB_SavingCardAccount sca WITH(NOLOCK)
        LEFT JOIN TB_SavingCard s
        WITH (NOLOCK)
        ON sca.SavingCardID = s.ID
        WHERE
            sca.Active=1
          AND (sca.Balance
         &gt; 0
           OR sca.LargessBalance
            >0)
          AND sca.PackageCardAccountID = #{PackageCardAccountID}
        ORDER BY s.Name, sca.ID DESC
    </select>

    <select id="refundPackageCardSavingCardAccount" parameterType="java.util.Map"
            resultType="com.zhelian.model.ibeauty.form.output.SavingCardAccountExtendForm">
        SELECT sca.ID,
               sca.SavingCardID,
               s.Name,
               ISNULL(s.Alias, '')                                                                 Alias,
               0 AS                                                                                Type,
               sca.ID                                                                              AccountID,
               CASE
                   WHEN sca.ValidType = 3 THEN '永久有效'
                   ELSE CONVERT(VARCHAR (10), sca.ValidDate, 120)
                   END                                                                             ValidDayName,
               sca.Balance,
               sca.LargessBalance,
               sca.Balance + sca.LargessBalance                                                    TotalBalance,
               sca.Amount                                                                          TotalAmount,
               CASE
                   WHEN sca.Amount > 0 THEN
                       CONVERT(DECIMAL (18, 2), sca.Balance - sca.ArrearAmount +
                                                sca.LargessBalance - sca.ArrearAmount * sca.LargessAmount / sca.Amount
                           - dbo.getRefundBillSavingCardAmount(sca.ID) -
                                                dbo.getRefundBillSavingCardLargessAmount(sca.ID))
                   ELSE sca.LargessBalance
                   END                                                                             ValidBalance,
               CASE
                   WHEN sca.Amount = 0 THEN 1
                   ELSE 0
                   END                                                                             IsLargess,
               sca.Amount                                                                          TotalAmount,
               sca.ArrearAmount,
               dbo.getRefundBillSavingCardTotalAmount(sca.ID)                                      RefundAmount,
               CONVERT(VARCHAR (10), sca.BuyDate, 120)                                             BuyDate,
               CONVERT(DECIMAL (18, 2),
                       sca.Balance - sca.ArrearAmount - dbo.getRefundBillSavingCardAmount(sca.ID)) AllowRefundBalance,
               CASE
                   WHEN sca.Amount > 0 THEN
                       CONVERT(DECIMAL (18, 2), sca.LargessBalance - sca.ArrearAmount * sca.LargessAmount / sca.Amount -
                                                dbo.getRefundBillSavingCardLargessAmount(sca.ID))
                   ELSE sca.LargessBalance
                   END                                                                             AllowRefundLargessBalance,
               CONVERT(DECIMAL (18, 2),
                       sca.Balance - sca.ArrearAmount - dbo.getRefundBillSavingCardAmount(sca.ID)) AllowTransferBalance,
               CONVERT(DECIMAL (18, 2), sca.LargessBalance -
                                        dbo.getRefundBillSavingCardLargessAmount(sca.ID))          AllowTransferLargessBalance,
               sca.Remark
        FROM TB_SavingCardAccount sca WITH(NOLOCK)
        LEFT JOIN TB_SavingCard s
        WITH (NOLOCK)
        ON sca.SavingCardID = s.ID
        WHERE
            sca.Active=1
          AND sca.Channel != '20'
          AND (sca.Balance
         &gt; 0
           OR sca.LargessBalance
            >0)
          AND sca.PackageCardAccountID = #{PackageCardAccountID}
        ORDER BY s.Name, sca.ID DESC
    </select>
    <!--转账通用次卡账户明细-->
    <select id="transerPackageCardGeneralCardAccount" parameterType="java.util.Map"
            resultType="com.zhelian.model.ibeauty.form.output.GeneralCardAccountOutputForm">
        SELECT tpa.ID,
               tpa.GeneralCardID,
               p.Name,
               ISNULL(p.Alias, '')                                        Alias,
               CASE
                   WHEN tpa.ValidType = 3 THEN '永久有效'
                   ELSE CONVERT(VARCHAR (10), tpa.ValidDate, 120)
                   END                                                    ValidDayName,
               CASE
                   WHEN tpa.TotalAmount / tpa.CardTimes &lt;= 0
                       THEN tpa.Balance - dbo.getRefundBillGeneralCardQuantity(tpa.ID)
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.CardTimes / tpa.TotalAmount)) &lt;= 0 THEN 0
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.CardTimes / tpa.TotalAmount)) > 0 THEN (
                           tpa.Balance - CEILING(tpa.ArrearAmount * tpa.CardTimes /
                                                 tpa.TotalAmount) - dbo.getRefundBillGeneralCardQuantity(tpa.ID))
                   END                                                    ValidBalance,
               tpa.CardTimes,
               dbo.getRefundBillGeneralCardQuantity(tpa.ID)               RefundAmount,
               tpa.ArrearAmount,
               tpa.TotalAmount,
               CAST(tpa.TotalAmount / tpa.CardTimes as decimal(38, 2)) AS Amount,
               tpa.Balance,
               tpa.Price,
               tpa.IsLargess,
               CONVERT(VARCHAR (10), tpa.BuyDate, 120)                    BuyDate,
               tpa.PayPerformanceBalanceAmount + tpa.CardPerformanceBalanceAmount +
               tpa.CardLargessPerformanceBalanceAmount                    PerformanceBalanceAmount,
               tpa.Remark
        FROM TB_GeneralCardAccount tpa WITH(NOLOCK)
        LEFT JOIN TB_GeneralCard p
        WITH (NOLOCK)
        ON p.ID = tpa.GeneralCardID
        WHERE tpa.Active=1
          AND tpa.PackageCardAccountID = #{PackageCardAccountID}
          AND tpa.Balance
         &gt; 0
        ORDER BY tpa.IsLargess, p.Name, tpa.ID DESC
    </select>

    <select id="refundPackageCardGeneralCardAccount" parameterType="java.util.Map"
            resultType="com.zhelian.model.ibeauty.form.output.GeneralCardAccountOutputForm">
        SELECT tpa.ID,
               tpa.GeneralCardID,
               p.Name,
               ISNULL(p.Alias, '')                                        Alias,
               CASE
                   WHEN tpa.ValidType = 3 THEN '永久有效'
                   ELSE CONVERT(VARCHAR (10), tpa.ValidDate, 120)
                   END                                                    ValidDayName,
               CASE
                   WHEN tpa.TotalAmount / tpa.CardTimes &lt;= 0
                       THEN tpa.Balance - dbo.getRefundBillGeneralCardQuantity(tpa.ID)
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.CardTimes / tpa.TotalAmount)) &lt;= 0 THEN 0
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.CardTimes / tpa.TotalAmount)) > 0 THEN (
                       tpa.Balance - CEILING(tpa.ArrearAmount * tpa.CardTimes /
                                             tpa.TotalAmount) - dbo.getRefundBillGeneralCardQuantity(tpa.ID))
                   END                                                    ValidBalance,
               tpa.CardTimes,
               dbo.getRefundBillGeneralCardQuantity(tpa.ID)               RefundAmount,
               tpa.ArrearAmount,
               tpa.TotalAmount,
               CAST(tpa.TotalAmount / tpa.CardTimes as decimal(38, 2)) AS Amount,
               tpa.Balance,
               tpa.Price,
               tpa.IsLargess,
               CONVERT(VARCHAR (10), tpa.BuyDate, 120)                    BuyDate,
               tpa.PayPerformanceBalanceAmount + tpa.CardPerformanceBalanceAmount +
               tpa.CardLargessPerformanceBalanceAmount                    PerformanceBalanceAmount,
               tpa.Remark
        FROM TB_GeneralCardAccount tpa WITH(NOLOCK)
        LEFT JOIN TB_GeneralCard p
        WITH (NOLOCK)
        ON p.ID = tpa.GeneralCardID
        WHERE tpa.Active=1
          AND tpa.Channel != '20'
          AND tpa.PackageCardAccountID = #{PackageCardAccountID}
          AND tpa.Balance
         &gt; 0
        ORDER BY tpa.IsLargess, p.Name, tpa.ID DESC
    </select>
    <!--转账时效卡账户明细-->
    <select id="transerPackageCardTimeCardAccount" parameterType="java.util.Map"
            resultType="com.zhelian.model.ibeauty.form.output.TimeCardAccountOutputForm">
        SELECT tpa.ID,
               tpa.TimeCardID,
               p.Name,
               ISNULL(p.Alias, '')                                                               Alias,
               CASE
                   WHEN tpa.ValidType = 3 THEN '永久有效'
                   ELSE CONVERT(VARCHAR (10), tpa.ValidDate, 120)
                   END                                                                           ValidDayName,
               CASE
                   WHEN tpa.ArrearAmount = 0 THEN tpa.PerformanceBalance
                   WHEN tpa.ArrearAmount > 0 AND tpa.PerformanceTimes > 0 THEN (tpa.PerformanceBalance -
                                                                                CEILING(tpa.ArrearAmount / (tpa.TotalAmount / tpa.PerformanceTimes)))
                   ELSE 0 END                                                                    ValidBalance,
               p.ConsumeCycle,
               p.CycleLimitAmount,
               ISNULL(dbo.GetTimeCardConsumeCycleAmount(
                              (SELECT TOP 1 CONVERT (VARCHAR (10),BillDate,120) BillDate
                                  FROM TB_TreatBillTimeCard d
                                  LEFT JOIN TB_TreatBill m ON m.ID=d.TreatBillID
                                  WHERE m.BillStatus='20' and d.TimeCardAccountID =tpa.ID and m.BillType = '10'
                                  ORDER BY BillDate),p.ConsumeCycle,GETDATE(),tpa.ID), 0)        ConsumeCycleAmount,
               tpa.PerformanceTimes,
               tpa.PerformanceBalance,
               tpa.ArrearAmount,
               tpa.PayPerformanceBalanceAmount + tpa.CardPerformanceBalanceAmount +
               tpa.CardLargessPerformanceBalanceAmount                                           PerformanceBalanceAmount,
               CASE
                   WHEN tpa.PerformanceTimes = 0 THEN 0
                   ELSE CAST(tpa.TotalAmount / tpa.PerformanceTimes as decimal(38, 2))
                   END                                                                           Amount,
               tpa.Price,
               tpa.IsLargess,
               CONVERT(VARCHAR (10), tpa.BuyDate, 120)                                           BuyDate,
               tpa.TotalAmount,
               ISNULL(
                       (SELECT SUM(T.Quantity)
                        FROM (SELECT CASE
                                         WHEN
                                             a2.BillType = '10' then a1.Quantity
                                         ELSE a1.Quantity * -1
                                         END Quantity
                              from TB_TreatBillTimeCard a1 WITH(NOLOCK)
        JOIN TB_TreatBill a2
                              WITH (NOLOCK)
                              ON a1.TreatBillID=a2.ID
                              WHERE a1.TimeCardAccountID = tpa.ID AND a2.BillStatus='20') T), 0) ConsumeAmount,
               tpa.PayPerformanceBalanceAmount + tpa.CardPerformanceBalanceAmount +
               tpa.CardLargessPerformanceBalanceAmount                                           PerformanceBalanceAmount,
               CASE WHEN trb.ID is null THEN 0 ELSE 1 END                                        IsRefund,
               tpa.Remark
        FROM TB_TimeCardAccount tpa WITH(NOLOCK)
        LEFT JOIN TB_TimeCard p
        WITH (NOLOCK)
        ON p.ID = tpa.TimeCardID
            LEFT JOIN TB_RefundSaleBillTimeCard trs
        WITH (NOLOCK)
        ON tpa.ID=trs.TimeCardAccountID
            LEFT JOIN TB_RefundSaleBill trb
        WITH (NOLOCK)
        ON trb.ID=trs.RefundBillID AND trb.ApprovalStatus='20'
        WHERE tpa.Active=1
          AND tpa.PerformanceBalance=tpa.PerformanceTimes
          AND tpa.PackageCardAccountID = #{PackageCardAccountID}
          AND (tpa.ValidDate IS NULL
           OR CONVERT (varchar (10)
            , tpa.ValidDate
            , 120) &gt;= convert (varchar (10)
            , GETDATE()
            , 120))
        ORDER BY tpa.IsLargess, p.Name, tpa.ID DESC
    </select>

    <select id="refundPackageCardTimeCardAccount" parameterType="java.util.Map"
            resultType="com.zhelian.model.ibeauty.form.output.TimeCardAccountOutputForm">
        SELECT tpa.ID,
               tpa.TimeCardID,
               p.Name,
               ISNULL(p.Alias, '')                                                               Alias,
               CASE
                   WHEN tpa.ValidType = 3 THEN '永久有效'
                   ELSE CONVERT(VARCHAR (10), tpa.ValidDate, 120)
                   END                                                                           ValidDayName,
               CASE
                   WHEN tpa.ArrearAmount = 0 THEN tpa.PerformanceBalance
                   WHEN tpa.ArrearAmount > 0 AND tpa.PerformanceTimes > 0 THEN (tpa.PerformanceBalance -
                                                                                CEILING(tpa.ArrearAmount / (tpa.TotalAmount / tpa.PerformanceTimes)))
                   ELSE 0 END                                                                    ValidBalance,
               p.ConsumeCycle,
               p.CycleLimitAmount,
               ISNULL(dbo.GetTimeCardConsumeCycleAmount(
                              (SELECT TOP 1 CONVERT (VARCHAR (10),BillDate,120) BillDate
                                  FROM TB_TreatBillTimeCard d
                                  LEFT JOIN TB_TreatBill m ON m.ID=d.TreatBillID
                                  WHERE m.BillStatus='20' and d.TimeCardAccountID =tpa.ID and m.BillType = '10'
                                  ORDER BY BillDate),p.ConsumeCycle,GETDATE(),tpa.ID), 0)        ConsumeCycleAmount,
               tpa.PerformanceTimes,
               tpa.PerformanceBalance,
               tpa.ArrearAmount,
               tpa.PayPerformanceBalanceAmount + tpa.CardPerformanceBalanceAmount +
               tpa.CardLargessPerformanceBalanceAmount                                           PerformanceBalanceAmount,
               CASE
                   WHEN tpa.PerformanceTimes = 0 THEN 0
                   ELSE CAST(tpa.TotalAmount / tpa.PerformanceTimes as decimal(38, 2))
                   END                                                                           Amount,
               tpa.Price,
               tpa.IsLargess,
               CONVERT(VARCHAR (10), tpa.BuyDate, 120)                                           BuyDate,
               tpa.TotalAmount,
               ISNULL(
                       (SELECT SUM(T.Quantity)
                        FROM (SELECT CASE
                                         WHEN
                                             a2.BillType = '10' then a1.Quantity
                                         ELSE a1.Quantity * -1
                                         END Quantity
                              from TB_TreatBillTimeCard a1 WITH(NOLOCK)
        JOIN TB_TreatBill a2
                              WITH (NOLOCK)
                              ON a1.TreatBillID=a2.ID
                              WHERE a1.TimeCardAccountID = tpa.ID AND a2.BillStatus='20') T), 0) ConsumeAmount,
               tpa.PayPerformanceBalanceAmount + tpa.CardPerformanceBalanceAmount +
               tpa.CardLargessPerformanceBalanceAmount                                           PerformanceBalanceAmount,
               CASE WHEN trb.ID is null THEN 0 ELSE 1 END                                        IsRefund,
               tpa.Remark
        FROM TB_TimeCardAccount tpa WITH(NOLOCK)
        LEFT JOIN TB_TimeCard p
        WITH (NOLOCK)
        ON p.ID = tpa.TimeCardID
            LEFT JOIN TB_RefundSaleBillTimeCard trs
        WITH (NOLOCK)
        ON tpa.ID=trs.TimeCardAccountID
            LEFT JOIN TB_RefundSaleBill trb
        WITH (NOLOCK)
        ON trb.ID=trs.RefundBillID AND trb.ApprovalStatus='20'
        WHERE tpa.Active=1
          AND tpa.Channel != '20'
          AND tpa.PerformanceBalance=tpa.PerformanceTimes
          AND tpa.PackageCardAccountID = #{PackageCardAccountID}
          AND (tpa.ValidDate IS NULL
           OR CONVERT (varchar (10)
            , tpa.ValidDate
            , 120) &gt;= convert (varchar (10)
            , GETDATE()
            , 120))
        ORDER BY tpa.IsLargess, p.Name, tpa.ID DESC
    </select>

    <update id="updatePackageCardAccountPrice">
        UPDATE dbo.TB_PackageCardAccount
        SET Price = #{TotalAmount},
            TotalAmount=#{TotalAmount},
            PayAmount=#{PayAmount},
            SavingCardDeductionAmount=#{SavingCardDeductionAmount},
            SavingCardDeductionLargessAmount=#{SavingCardDeductionLargessAmount}
        WHERE ID = #{ID}
    </update>

    <select id="customerPackageCardAccount"
            resultType="com.zhelian.model.ibeauty.form.output.CustomerPackageCardAccountOutputForm">
        SELECT
        w.ID,
        w.PackageCardID,
        p.Name,
        p.Alias,
        CASE
        WHEN w.ValidType = 3 THEN '永久有效'
        ELSE CONVERT(VARCHAR(10),w.ValidDate,120)
        END ValidDayName,
        ISNULL(w.IsLargess, 0) IsLargess,
        CONVERT(VARCHAR(10),w.BuyDate,120) BuyDate,
        w.ArrearAmount,
        w.PayAmount,
        w.SavingCardDeductionAmount,
        w.SavingCardDeductionLargessAmount,
        CASE w.Channel WHEN '10' THEN '购买' WHEN '20' THEN '转账' WHEN '30' THEN '存量导入' END ChannelName,
        e.EntityName,
        w.TotalAmount,
        (
        SELECT SUM(Amount) FROM
        (
        SELECT PackageCardAccountID,PayPerformanceBalanceAmount+CardPerformanceBalanceAmount Amount FROM
        TB_ProductAccount tpa
        UNION ALL
        SELECT PackageCardAccountID,PayPerformanceBalanceAmount+CardPerformanceBalanceAmount Amount FROM
        TB_ProjectAccount tpa
        UNION ALL
        SELECT PackageCardAccountID,PayPerformanceBalanceAmount+CardPerformanceBalanceAmount Amount FROM
        TB_GeneralCardAccount tpa
        UNION ALL
        SELECT PackageCardAccountID,PayPerformanceBalanceAmount+CardPerformanceBalanceAmount Amount FROM
        TB_TimeCardAccount tpa
        UNION ALL
        SELECT PackageCardAccountID,Balance-ArrearAmount Amount FROM TB_SavingCardAccount tpa
        )tpa
        WHERE tpa.PackageCardAccountID=w.ID
        )BalanceAmount
        FROM TB_PackageCardAccount w WITH(NOLOCK)
        LEFT JOIN TK_Entity e WITH(NOLOCK) ON e.ID = w.EntityID
        LEFT JOIN TB_PackageCard p WITH(NOLOCK) ON w.PackageCardID = p.ID
        <where>
            <if test="CustomerID != null and CustomerID != ''">
                AND w.CustomerID=#{CustomerID}
            </if>
            <choose>
                <when test="Active!=null  and Active==true">
                    AND w.Active=1
                    AND EXISTS (--项目
                    SELECT
                    a.PackageCardAccountID
                    FROM
                    TB_ProjectAccount a
                    WHERE
                    a.PackageCardAccountID = w.ID
                    AND a.Balance > 0 AND a.Active=1
                    UNION--储值卡
                    SELECT
                    b.PackageCardAccountID
                    FROM
                    TB_SavingCardAccount b
                    WHERE
                    b.PackageCardAccountID =w.ID
                    AND b.Balance+b.LargessBalance> 0 and b.Active=1
                    UNION--产品
                    SELECT
                    c.PackageCardAccountID
                    FROM
                    TB_ProductAccount c
                    WHERE
                    c.PackageCardAccountID =w.ID
                    AND c.Balance > 0 and c.Active=1
                    UNION--通用次卡
                    SELECT
                    d.PackageCardAccountID
                    FROM
                    TB_GeneralCardAccount d
                    WHERE
                    d.PackageCardAccountID =w.ID
                    AND d.Balance > 0 and d.Active=1
                    UNION--時效卡
                    SELECT
                    e.PackageCardAccountID
                    FROM
                    TB_TimeCardAccount e
                    WHERE
                    e.PackageCardAccountID =w.ID
                    AND e.Active =1
                    )
                    AND (w.ValidDate IS NULL OR w.ValidDate &gt;= convert(varchar(10),
                    GETDATE(), 120))
                </when>
                <when test="Active!=null and Active==false">
                    AND (w.Active=0
                    OR NOT EXISTS (--项目
                    SELECT
                    a.PackageCardAccountID
                    FROM
                    TB_ProjectAccount a
                    WHERE
                    a.PackageCardAccountID = w.ID
                    AND a.Balance > 0 AND a.Active=1
                    UNION--储值卡
                    SELECT
                    b.PackageCardAccountID
                    FROM
                    TB_SavingCardAccount b
                    WHERE
                    b.PackageCardAccountID =w.ID
                    AND b.Balance+b.LargessBalance> 0 and b.Active=1
                    UNION--产品
                    SELECT
                    c.PackageCardAccountID
                    FROM
                    TB_ProductAccount c
                    WHERE
                    c.PackageCardAccountID =w.ID
                    AND c.Balance > 0 and c.Active=1
                    UNION--通用次卡
                    SELECT
                    d.PackageCardAccountID
                    FROM
                    TB_GeneralCardAccount d
                    WHERE
                    d.PackageCardAccountID =w.ID
                    AND d.Balance > 0 and d.Active=1
                    UNION--時效卡
                    SELECT
                    e.PackageCardAccountID
                    FROM
                    TB_TimeCardAccount e
                    WHERE
                    e.PackageCardAccountID =w.ID
                    AND e.Active =1
                    )
                    OR (w.ValidDate IS NOT NULL AND w.ValidDate &lt; convert(varchar(10),
                    GETDATE(), 120)))
                </when>
            </choose>
            <if test="Name != null and Name != ''">
                AND ( w.PackageCardID LIKE '%'+ #{Name} +'%' OR p.Name LIKE '%'+ #{Name} +'%' OR p.Alias LIKE '%'+
                #{Name} +'%')
            </if>
            <if test="ID != null and ID != ''">
                AND w.ID=#{ID}
            </if>
        </where>
        ORDER BY w.BuyDate DESC, p.Name,w.ID DESC
    </select>

    <select id="customerPackageCardAccountSum"
            resultType="com.zhelian.model.ibeauty.form.output.CustomerPackageCardAccountSumOutputForm">
        SELECT
        ISNULL(SUM(w.ArrearAmount),0) ArrearAmount,
        ISNULL(SUM(w.PayAmount),0) PayAmount,
        ISNULL(SUM(w.SavingCardDeductionAmount),0) SavingCardDeductionAmount,
        ISNULL(SUM(w.SavingCardDeductionLargessAmount),0) SavingCardDeductionLargessAmount,
        ISNULL(SUM(w.TotalAmount),0) TotalAmount,
        ISNULL(SUM(w.BalanceAmount),0) BalanceAmount
        from
        (
        SELECT
        w.ArrearAmount,
        w.PayAmount,
        w.SavingCardDeductionAmount,
        w.SavingCardDeductionLargessAmount,
        w.TotalAmount,
        (
        SELECT SUM(Amount) FROM
        (
        SELECT PackageCardAccountID,PayPerformanceBalanceAmount+CardPerformanceBalanceAmount Amount FROM
        TB_ProductAccount tpa
        UNION ALL
        SELECT PackageCardAccountID,PayPerformanceBalanceAmount+CardPerformanceBalanceAmount Amount FROM
        TB_ProjectAccount tpa
        UNION ALL
        SELECT PackageCardAccountID,PayPerformanceBalanceAmount+CardPerformanceBalanceAmount Amount FROM
        TB_GeneralCardAccount tpa
        UNION ALL
        SELECT PackageCardAccountID,PayPerformanceBalanceAmount+CardPerformanceBalanceAmount Amount FROM
        TB_TimeCardAccount tpa
        UNION ALL
        SELECT PackageCardAccountID,Balance-ArrearAmount Amount FROM TB_SavingCardAccount tpa
        )tpa
        WHERE tpa.PackageCardAccountID=w.ID
        )BalanceAmount
        FROM TB_PackageCardAccount w WITH(NOLOCK)
        LEFT JOIN TK_Entity e WITH(NOLOCK) ON e.ID = w.EntityID
        LEFT JOIN TB_PackageCard p WITH(NOLOCK) ON w.PackageCardID = p.ID
        <where>
            <if test="CustomerID != null and CustomerID != ''">
                AND w.CustomerID=#{CustomerID}
            </if>
            <choose>
                <when test="Active!=null  and Active==true">
                    AND w.Active=1
                    AND EXISTS (--项目
                    SELECT
                    a.PackageCardAccountID
                    FROM
                    TB_ProjectAccount a
                    WHERE
                    a.PackageCardAccountID = w.ID
                    AND a.Balance > 0 AND a.Active=1
                    UNION--储值卡
                    SELECT
                    b.PackageCardAccountID
                    FROM
                    TB_SavingCardAccount b
                    WHERE
                    b.PackageCardAccountID =w.ID
                    AND b.Balance+b.LargessBalance> 0 and b.Active=1
                    UNION--产品
                    SELECT
                    c.PackageCardAccountID
                    FROM
                    TB_ProductAccount c
                    WHERE
                    c.PackageCardAccountID =w.ID
                    AND c.Balance > 0 and c.Active=1
                    UNION--通用次卡
                    SELECT
                    d.PackageCardAccountID
                    FROM
                    TB_GeneralCardAccount d
                    WHERE
                    d.PackageCardAccountID =w.ID
                    AND d.Balance > 0 and d.Active=1
                    UNION--時效卡
                    SELECT
                    e.PackageCardAccountID
                    FROM
                    TB_TimeCardAccount e
                    WHERE
                    e.PackageCardAccountID =w.ID
                    AND e.Active =1
                    )
                    AND (w.ValidDate IS NULL OR w.ValidDate &gt;= convert(varchar(10),
                    GETDATE(), 120))
                </when>
                <when test="Active!=null and Active==false">
                    AND (w.Active=0
                    OR NOT EXISTS (--项目
                    SELECT
                    a.PackageCardAccountID
                    FROM
                    TB_ProjectAccount a
                    WHERE
                    a.PackageCardAccountID = w.ID
                    AND a.Balance > 0 AND a.Active=1
                    UNION--储值卡
                    SELECT
                    b.PackageCardAccountID
                    FROM
                    TB_SavingCardAccount b
                    WHERE
                    b.PackageCardAccountID =w.ID
                    AND b.Balance+b.LargessBalance> 0 and b.Active=1
                    UNION--产品
                    SELECT
                    c.PackageCardAccountID
                    FROM
                    TB_ProductAccount c
                    WHERE
                    c.PackageCardAccountID =w.ID
                    AND c.Balance > 0 and c.Active=1
                    UNION--通用次卡
                    SELECT
                    d.PackageCardAccountID
                    FROM
                    TB_GeneralCardAccount d
                    WHERE
                    d.PackageCardAccountID =w.ID
                    AND d.Balance > 0 and d.Active=1
                    UNION--時效卡
                    SELECT
                    e.PackageCardAccountID
                    FROM
                    TB_TimeCardAccount e
                    WHERE
                    e.PackageCardAccountID =w.ID
                    AND e.Active =1
                    )
                    OR (w.ValidDate IS NOT NULL AND w.ValidDate &lt; convert(varchar(10),
                    GETDATE(), 120)))
                </when>
            </choose>
            <if test="Name != null and Name != ''">
                AND ( w.PackageCardID LIKE '%'+ #{Name} +'%' OR p.Name LIKE '%'+ #{Name} +'%' OR p.Alias LIKE '%'+
                #{Name} +'%')
            </if>
            <if test="ID != null and ID != ''">
                AND w.ID=#{ID}
            </if>
        </where>
        )w

    </select>

    <!--项目账户明细-->
    <select id="custPackageCardProjectAccount"
            resultType="com.zhelian.model.ibeauty.form.output.CustomerPackageCardProjectAccountOutputForm">
        SELECT tpa.ID,
               tpa.ProjectID,
               p.Name,
               ISNULL(p.Alias, '')                                                Alias,
               tpa.Quantity,
               tpa.ArrearAmount,
               tpa.Balance,
               tpa.TotalAmount,
               tpa.PayAmount,
               tpa.SavingCardDeductionAmount,
               tpa.SavingCardDeductionLargessAmount,
               tpa.PayPerformanceBalanceAmount + tpa.CardPerformanceBalanceAmount BalanceAmount,
               tpa.IsLargess,
               CONVERT(VARCHAR (10), tpa.BuyDate, 120)                            BuyDate,
               CASE
                   WHEN tpa.TotalAmount / tpa.Quantity &lt;= 0
                       THEN tpa.Balance - dbo.getRefundBillProjectQuantity(tpa.ID)
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) &lt;= 0 THEN 0
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) > 0 AND
                        (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity /
                                               tpa.TotalAmount) - dbo.getRefundBillProjectQuantity(tpa.ID)) > 0 THEN (
                           tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity /
                                                 tpa.TotalAmount) - dbo.getRefundBillProjectQuantity(tpa.ID))
                   ELSE 0
                   END                                                            ValidBalance,
               dbo.getRefundBillProjectQuantity(tpa.ID)                           RefundBalance,
               CAST(tpa.TotalAmount / tpa.Quantity as decimal(38, 2)) AS          Amount
        FROM TB_ProjectAccount tpa WITH(NOLOCK)
                 LEFT JOIN TB_Project p WITH(NOLOCK)
        ON p.ID = tpa.ProjectID
        WHERE tpa.PackageCardAccountID = #{PackageCardAccountID}
        ORDER BY tpa.IsLargess, p.Name, tpa.ID DESC
    </select>

    <!--产品账户明细-->
    <select id="custPackageCardProductAccount"
            resultType="com.zhelian.model.ibeauty.form.output.CustomerPackageCardProductAccountOutputForm">
        SELECT tpa.ID,
               tpa.ProductID,
               p.Name,
               ISNULL(p.Alias, '')                                                Alias,
               CASE
                   WHEN tpa.TotalAmount / tpa.Quantity &lt;= 0
                       THEN tpa.Balance - dbo.getRefundBillProductQuantity(tpa.ID)
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) &lt;= 0 THEN 0
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity / tpa.TotalAmount)) > 0 AND
                        (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity /
                                               tpa.TotalAmount) - dbo.getRefundBillProductQuantity(tpa.ID)) > 0 THEN (
                           tpa.Balance - CEILING(tpa.ArrearAmount * tpa.Quantity /
                                                 tpa.TotalAmount) - dbo.getRefundBillProductQuantity(tpa.ID))
                   ELSE 0
                   END                                                            ValidBalance,
               dbo.getRefundBillProductQuantity(tpa.ID)                           RefundBalance,
               tpa.Quantity,
               tpa.ArrearAmount,
               tpa.TotalAmount,
               tpa.PayAmount,
               tpa.SavingCardDeductionAmount,
               tpa.SavingCardDeductionLargessAmount,
               tpa.PayPerformanceBalanceAmount + tpa.CardPerformanceBalanceAmount BalanceAmount,
               tpa.Balance,
               tpa.IsLargess,
               CONVERT(VARCHAR (10), tpa.BuyDate, 120)                            BuyDate,
               CAST(tpa.TotalAmount / tpa.Quantity as decimal(38, 2)) AS          Amount
        FROM TB_ProductAccount tpa WITH(NOLOCK)
                 LEFT JOIN TE_Product p WITH(NOLOCK)
        ON p.ID = tpa.ProductID
            LEFT JOIN TB_Product bp
        WITH (NOLOCK)
        ON p.ID = bp.ProductID
        WHERE
            tpa.PackageCardAccountID = #{PackageCardAccountID}
        ORDER BY tpa.IsLargess, p.Name, tpa.ID DESC
    </select>

    <!--储值卡账户明细-->
    <select id="custPackageCardSavingCardAccount"
            resultType="com.zhelian.model.ibeauty.form.output.CustomerSavingCardAccountForm">
        SELECT sca.ID,
               sca.SavingCardID,
               s.Name,
               ISNULL(s.Alias, '')                            Alias,
               CASE
                   WHEN sca.ValidType = 3 THEN '永久有效'
                   ELSE CONVERT(VARCHAR (10), sca.ValidDate, 120)
                   END                                        ValidDayName,
               sca.Balance,
               sca.LargessBalance,
               sca.Balance + sca.LargessBalance               TotalBalance,
               sca.Balance - sca.ArrearAmount                 BalanceAmount,
               sca.Amount,
               sca.LargessAmount,
               sca.ArrearAmount,
               CONVERT(VARCHAR (10), sca.BuyDate, 120)        BuyDate,
               CASE
                   WHEN sca.Amount > 0 THEN
                       CONVERT(DECIMAL (18, 2), sca.Balance - sca.ArrearAmount +
                                                sca.LargessBalance - sca.ArrearAmount * sca.LargessAmount / sca.Amount
                           - dbo.getRefundBillSavingCardAmount(sca.ID) -
                                                dbo.getRefundBillSavingCardLargessAmount(sca.ID))
                   ELSE sca.LargessBalance
                   END                                        ValidBalance,
               dbo.getRefundBillSavingCardTotalAmount(sca.ID) RefundAmount
        FROM TB_SavingCardAccount sca WITH(NOLOCK)
                 LEFT JOIN TB_SavingCard s WITH(NOLOCK)
        ON sca.SavingCardID = s.ID
        WHERE sca.PackageCardAccountID = #{PackageCardAccountID}
        ORDER BY s.Name, sca.ID DESC
    </select>

    <!--通用次卡账户明细-->
    <select id="custPackageCardGeneralCardAccount"
            resultType="com.zhelian.model.ibeauty.form.output.CustomerGeneralCardAccountOutputForm">
        SELECT tpa.ID,
               tpa.GeneralCardID,
               p.Name,
               ISNULL(p.Alias, '')                                                Alias,
               tpa.CardTimes,
               tpa.ArrearAmount,
               tpa.TotalAmount,
               tpa.PayAmount,
               tpa.SavingCardDeductionAmount,
               tpa.SavingCardDeductionLargessAmount,
               tpa.PayPerformanceBalanceAmount + tpa.CardPerformanceBalanceAmount BalanceAmount,
               tpa.Balance,
               tpa.IsLargess,
               CONVERT(VARCHAR (10), tpa.BuyDate, 120)                            BuyDate,
               CASE
                   WHEN tpa.TotalAmount / tpa.CardTimes &lt;= 0
                       THEN tpa.Balance - dbo.getRefundBillGeneralCardQuantity(tpa.ID)
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.CardTimes / tpa.TotalAmount)) &lt;= 0 THEN 0
                   WHEN (tpa.Balance - CEILING(tpa.ArrearAmount * tpa.CardTimes / tpa.TotalAmount)) > 0 THEN (
                           tpa.Balance - CEILING(tpa.ArrearAmount * tpa.CardTimes /
                                                 tpa.TotalAmount) - dbo.getRefundBillGeneralCardQuantity(tpa.ID))
                   END                                                            ValidBalance,
               dbo.getRefundBillGeneralCardQuantity(tpa.ID)                       RefundAmount,
               CAST(tpa.TotalAmount / tpa.CardTimes as decimal(38, 2)) AS         Amount
        FROM TB_GeneralCardAccount tpa
                 LEFT JOIN TB_GeneralCard p WITH(NOLOCK)
        ON p.ID = tpa.GeneralCardID
        WHERE
            tpa.PackageCardAccountID = #{PackageCardAccountID}
        ORDER BY tpa.IsLargess, p.Name, tpa.ID DESC
    </select>

    <!--时效卡账户明细-->
    <select id="custPackageCardTimeCardAccount"
            resultType="com.zhelian.model.ibeauty.form.output.CustomerTimeCardAccountOutputForm">
        SELECT tpa.ID,
               tpa.TimeCardID,
               p.Name,
               ISNULL(p.Alias, '')                                                Alias,
               p.ConsumeCycle,
               p.CycleLimitAmount,
               tpa.PerformanceTimes,
               tpa.PerformanceBalance,
               tpa.ArrearAmount,
               tpa.PayAmount,
               tpa.SavingCardDeductionAmount,
               tpa.SavingCardDeductionLargessAmount,
               tpa.PayPerformanceBalanceAmount + tpa.CardPerformanceBalanceAmount BalanceAmount,
               tpa.IsLargess,
               CONVERT(VARCHAR (10), tpa.BuyDate, 120)                            BuyDate,
               tpa.TotalAmount,
               CASE
                   WHEN tpa.ArrearAmount = 0 THEN tpa.PerformanceBalance
                   WHEN tpa.ArrearAmount > 0 AND tpa.PerformanceTimes > 0 THEN (tpa.PerformanceBalance -
                                                                                CEILING(tpa.ArrearAmount / (tpa.TotalAmount / tpa.PerformanceTimes)))
                   ELSE 0 END                                                     ValidBalance,
               tpa.PayPerformanceBalanceAmount + tpa.CardPerformanceBalanceAmount +
               tpa.CardLargessPerformanceBalanceAmount                            PerformanceBalanceAmount,
               ISNULL(
                       (SELECT SUM(T.Quantity)
                        FROM (SELECT CASE
                                         WHEN
                                             a2.BillType = '10' then a1.Quantity
                                         ELSE a1.Quantity * -1
                                         END Quantity
                              from TB_TreatBillTimeCard a1
                                       JOIN TB_TreatBill a2 ON a1.TreatBillID = a2.ID
                              WHERE a1.TimeCardAccountID = tpa.ID
                                AND a2.BillStatus = '20') T), 0)                  ConsumeAmount
        FROM TB_TimeCardAccount tpa
                 LEFT JOIN TB_TimeCard p ON p.ID = tpa.TimeCardID
        WHERE tpa.PackageCardAccountID = #{PackageCardAccountID}
        ORDER BY tpa.IsLargess, p.Name, tpa.ID DESC
    </select>


    <resultMap id="PackageCard" type="com.zhelian.model.micromall.form.output.MicromallPackageCardAccountOutputForm">
        <id column="AccountID" property="AccountID" jdbcType="VARCHAR"/>
        <result column="Name" property="Name" jdbcType="VARCHAR"/>
        <result column="Alias" property="Alias" jdbcType="INTEGER"/>
        <result column="Price" property="Price" jdbcType="DECIMAL"/>
        <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
        <result column="ArrearAmount" property="ArrearAmount" jdbcType="DECIMAL"/>
        <result column="ValidDayName" property="ValidDayName" jdbcType="VARCHAR"/>
        <result column="ChannelName" property="ChannelName" jdbcType="VARCHAR"/>
        <result column="BillDate" property="BillDate" jdbcType="VARCHAR"/>
        <result column="Count" property="Count" jdbcType="INTEGER"/>
        <result column="ValidCount" property="ValidCount" jdbcType="INTEGER"/>
        <result column="IsLargess" property="IsLargess" jdbcType="BOOLEAN"/>
        <collection property="Product" column="AccountID" select="getPackageProduct"
                    ofType="com.zhelian.model.micromall.form.output.MicromallProductAccountOutputForm">
            <result column="AccountID" property="AccountID" jdbcType="INTEGER"/>
            <result column="ProductID" property="ProductID" jdbcType="INTEGER"/>
            <result column="Name" property="Name" jdbcType="VARCHAR"/>
            <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
            <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
            <result column="ValidBalance" property="ValidBalance" jdbcType="DECIMAL"/>
            <result column="Balance" property="Balance" jdbcType="DECIMAL"/>
            <result column="RefundBalance" property="RefundBalance" jdbcType="DECIMAL"/>
            <result column="Amount" property="Amount" jdbcType="DECIMAL"/>
            <result column="ArrearAmount" property="ArrearAmount" jdbcType="DECIMAL"/>
            <result column="Specification" property="Specification" jdbcType="VARCHAR"/>
            <result column="BuyDate" property="BuyDate" jdbcType="VARCHAR"/>
            <result column="ChannelName" property="ChannelName" jdbcType="VARCHAR"/>
            <result column="Description" property="Description" jdbcType="VARCHAR"/>
        </collection>
        <collection property="Project" column="AccountID" select="getPackageProject"
                    ofType="com.zhelian.model.micromall.form.output.MicromallProjectAccountOutputForm">
            <result column="AccountID" property="AccountID" jdbcType="INTEGER"/>
            <result column="ProjectID" property="ProjectID" jdbcType="INTEGER"/>
            <result column="Name" property="Name" jdbcType="VARCHAR"/>
            <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
            <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
            <result column="ValidBalance" property="ValidBalance" jdbcType="DECIMAL"/>
            <result column="Balance" property="Balance" jdbcType="DECIMAL"/>
            <result column="RefundBalance" property="RefundBalance" jdbcType="DECIMAL"/>
            <result column="Amount" property="Amount" jdbcType="DECIMAL"/>
            <result column="ArrearAmount" property="ArrearAmount" jdbcType="DECIMAL"/>
            <result column="Specification" property="Specification" jdbcType="VARCHAR"/>
            <result column="BuyDate" property="BuyDate" jdbcType="VARCHAR"/>
            <result column="ChannelName" property="ChannelName" jdbcType="VARCHAR"/>
            <result column="Description" property="Description" jdbcType="VARCHAR"/>
        </collection>
        <collection property="GeneralCard" column="AccountID" select="getPackageGeneralCard"
                    ofType="com.zhelian.model.micromall.form.output.MicromallGeneralCardAccountOutputForm">
            <result column="AccountID" property="AccountID" jdbcType="INTEGER"/>
            <result column="GeneralCardID" property="GeneralCardID" jdbcType="INTEGER"/>
            <result column="Name" property="Name" jdbcType="VARCHAR"/>
            <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
            <result column="ValidDayName" property="ValidDayName" jdbcType="VARCHAR"/>
            <result column="ValidBalance" property="ValidBalance" jdbcType="DECIMAL"/>
            <result column="CardTimes" property="CardTimes" jdbcType="DECIMAL"/>
            <result column="Balance" property="Balance" jdbcType="DECIMAL"/>
            <result column="RefundAmount" property="RefundAmount" jdbcType="DECIMAL"/>
            <result column="Amount" property="Amount" jdbcType="DECIMAL"/>
            <result column="ArrearAmount" property="ArrearAmount" jdbcType="DECIMAL"/>
            <result column="BuyDate" property="BuyDate" jdbcType="VARCHAR"/>
            <result column="ChannelName" property="ChannelName" jdbcType="VARCHAR"/>
        </collection>
        <collection property="TimeCard" column="AccountID" select="getPackageTimeCard"
                    ofType="com.zhelian.model.micromall.form.output.MicromallTimeCardAccountOutputForm">
            <result column="AccountID" property="AccountID" jdbcType="INTEGER"/>
            <result column="TimeCardID" property="TimeCardID" jdbcType="INTEGER"/>
            <result column="Name" property="Name" jdbcType="VARCHAR"/>
            <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
            <result column="ValidDayName" property="ValidDayName" jdbcType="VARCHAR"/>
            <result column="ConsumeCycle" property="ConsumeCycle" jdbcType="DECIMAL"/>
            <result column="CycleLimitAmount" property="CycleLimitAmount" jdbcType="DECIMAL"/>
            <result column="ConsumeCycleAmount" property="ConsumeCycleAmount" jdbcType="DECIMAL"/>
            <result column="ArrearAmount" property="ArrearAmount" jdbcType="DECIMAL"/>
            <result column="Amount" property="Amount" jdbcType="DECIMAL"/>
            <result column="TotalAmount" property="TotalAmount" jdbcType="VARCHAR"/>
            <result column="Amount" property="Amount" jdbcType="VARCHAR"/>
            <result column="ConsumeAmount" property="ConsumeAmount" jdbcType="VARCHAR"/>
            <result column="ChannelName" property="ChannelName" jdbcType="VARCHAR"/>
        </collection>
        <collection property="SavingCard" column="AccountID" select="getPackageSavingCard"
                    ofType="com.zhelian.model.micromall.form.output.MicromallSavingCardAccountOutputForm">
            <result column="AccountID" property="AccountID" jdbcType="INTEGER"/>
            <result column="SavingCardID" property="SavingCardID" jdbcType="INTEGER"/>
            <result column="Name" property="Name" jdbcType="VARCHAR"/>
            <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
            <result column="ValidDayName" property="ValidDayName" jdbcType="VARCHAR"/>
            <result column="ValidBalance" property="ValidBalance" jdbcType="DECIMAL"/>
            <result column="TotalBalance" property="TotalBalance" jdbcType="DECIMAL"/>
            <result column="Balance" property="Balance" jdbcType="DECIMAL"/>
            <result column="LargessBalance" property="LargessBalance" jdbcType="DECIMAL"/>
            <result column="TotalAmount" property="TotalAmount" jdbcType="DECIMAL"/>
            <result column="RefundAmount" property="RefundAmount" jdbcType="DECIMAL"/>
            <result column="ArrearAmount" property="ArrearAmount" jdbcType="DECIMAL"/>
            <result column="BuyDate" property="BuyDate" jdbcType="VARCHAR"/>
            <result column="ChannelName" property="ChannelName" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <select id="micromallPackageCardAccount" resultMap="PackageCard">
        SELECT
        tpa.ID AccountID,
        p.Name,
        tpa.IsLargess,
        p.Alias,
        P.Price,
        tpa.TotalAmount,
        tpa.ArrearAmount,
        CASE
        WHEN tpa.ValidType = 3 THEN '永久有效'
        ELSE CONVERT(VARCHAR(10),tpa.ValidDate,120)
        END ValidDayName,
        CONVERT(VARCHAR(10),tpa.BuyDate,120) BuyDate,
        CASE tpa.Channel WHEN '10' THEN '购买' WHEN '20' THEN '转账' WHEN '30' THEN '存量导入' WHEN '40' THEN '线上商城' END
        ChannelName,
        T.Count,T.ValidCount
        FROM TB_PackageCardAccount tpa WITH(NOLOCK)
        LEFT JOIN TB_PackageCard p WITH(NOLOCK) ON p.ID=tpa.PackageCardID
        LEFT JOIN (
        SELECT T.PackageCardAccountID,sum(Count) Count,SUM(ValidCount) ValidCount FROM

        (
        SELECT a.PackageCardAccountID,1 as Count,CASE WHEN (a.Active=0 OR a.Balance=0 ) THEN 0 ELSE 1 END ValidCount
        FROM TB_ProductAccount a WITH(NOLOCK) WHERE PackageCardAccountID IS NOT NULL
        UNION ALL
        SELECT a.PackageCardAccountID,1 as Count,CASE WHEN (a.Active=0 OR a.Balance=0 ) THEN 0 ELSE 1 END ValidCount
        FROM TB_ProjectAccount a WITH(NOLOCK) WHERE PackageCardAccountID IS NOT NULL
        UNION ALL
        SELECT a.PackageCardAccountID,1 as Count,CASE WHEN (a.Active=0 OR a.Balance=0 OR
        CONVERT(VARCHAR(10),GETDATE(),120)>CONVERT(VARCHAR(10),a.ValidDate,120)) THEN 0 ELSE 1 END ValidCount
        FROM TB_GeneralCardAccount a WITH(NOLOCK) WHERE PackageCardAccountID IS NOT NULL
        UNION ALL
        SELECT a.PackageCardAccountID,1 as Count,CASE WHEN (a.Active=0 OR
        CONVERT(VARCHAR(10),GETDATE(),120)>CONVERT(VARCHAR(10),a.ValidDate,120)) THEN 0 ELSE 1 END ValidCount
        FROM TB_TimeCardAccount a WITH(NOLOCK) WHERE PackageCardAccountID IS NOT NULL
        UNION ALL
        SELECT a.PackageCardAccountID,1 as Count,CASE WHEN (a.Active=0 OR a.Balance+A.LargessBalance=0 OR
        CONVERT(VARCHAR(10),GETDATE(),120)>CONVERT(VARCHAR(10),a.ValidDate,120)) THEN 0 ELSE 1 END ValidCount
        FROM TB_SavingCardAccount a WITH(NOLOCK) WHERE PackageCardAccountID IS NOT NULL
        )T GROUP BY PackageCardAccountID
        )T ON tpa.ID=T.PackageCardAccountID
        <where>
            tpa.CustomerID = #{CustomerID}
            <choose>
                <when test="Active!=null  and Active==true">
                    AND tpa.Active=1
                    AND EXISTS (--项目
                    SELECT
                    a.PackageCardAccountID
                    FROM
                    TB_ProjectAccount a WITH(NOLOCK)
                    WHERE
                    a.PackageCardAccountID = tpa.ID
                    AND a.Balance > 0 AND a.Active=1
                    UNION--储值卡
                    SELECT
                    b.PackageCardAccountID
                    FROM
                    TB_SavingCardAccount b WITH(NOLOCK)
                    WHERE
                    b.PackageCardAccountID =tpa.ID
                    AND b.Balance+b.LargessBalance> 0 and b.Active=1
                    UNION--产品
                    SELECT
                    c.PackageCardAccountID
                    FROM
                    TB_ProductAccount c WITH(NOLOCK)
                    WHERE
                    c.PackageCardAccountID =tpa.ID
                    AND c.Balance > 0 and c.Active=1
                    UNION--通用次卡
                    SELECT
                    d.PackageCardAccountID
                    FROM
                    TB_GeneralCardAccount d WITH(NOLOCK)
                    WHERE
                    d.PackageCardAccountID =tpa.ID
                    AND d.Balance > 0 and d.Active=1
                    UNION--時效卡
                    SELECT
                    e.PackageCardAccountID
                    FROM
                    TB_TimeCardAccount e WITH(NOLOCK)
                    WHERE
                    e.PackageCardAccountID =tpa.ID
                    AND e.Active =1
                    )
                    AND (tpa.ValidDate IS NULL OR DATEDIFF(DAY,GETDATE(),tpa.ValidDate)>=0)
                </when>
                <when test="Active!=null and Active==false">
                    AND (tpa.Active=0
                    OR NOT EXISTS (--项目
                    SELECT
                    a.PackageCardAccountID
                    FROM
                    TB_ProjectAccount a
                    WHERE
                    a.PackageCardAccountID = tpa.ID
                    AND a.Balance > 0 AND a.Active=1
                    UNION--储值卡
                    SELECT
                    b.PackageCardAccountID
                    FROM
                    TB_SavingCardAccount b
                    WHERE
                    b.PackageCardAccountID =tpa.ID
                    AND b.Balance+b.LargessBalance> 0 and b.Active=1
                    UNION--产品
                    SELECT
                    c.PackageCardAccountID
                    FROM
                    TB_ProductAccount c
                    WHERE
                    c.PackageCardAccountID =tpa.ID
                    AND c.Balance > 0 and c.Active=1
                    UNION--通用次卡
                    SELECT
                    d.PackageCardAccountID
                    FROM
                    TB_GeneralCardAccount d
                    WHERE
                    d.PackageCardAccountID =tpa.ID
                    AND d.Balance > 0 and d.Active=1
                    UNION--時效卡
                    SELECT
                    e.PackageCardAccountID
                    FROM
                    TB_TimeCardAccount e
                    WHERE
                    e.PackageCardAccountID =tpa.ID
                    AND e.Active =1
                    )
                    OR (tpa.ValidDate IS NOT NULL AND  tpa.ValidDate &lt;
                    convert(varchar(10), GETDATE(), 120)))
                </when>
            </choose>
            <if test="Name != null and Name != ''">
                ( p.Name like '%'+ #{Name} +'%' or p.Alias like '%'+ #{Name} +'%'
                OR p.NameFullPinYin LIKE '%'+ #{Name} +'%' OR p.NameAbbrPinYin LIKE '%'+ #{Name} +'%'
                OR p.AliasFullPinYin LIKE '%'+ #{Name} +'%' OR p.AliasAbbrPinYin LIKE '%'+ #{Name} +'%')
            </if>
        </where>
        ORDER BY tpa.BuyDate DESC, tpa.ID
    </select>

    <select id="getPackageProduct" parameterType="java.lang.Integer"
            resultType="com.zhelian.model.micromall.form.output.MicromallProductAccountOutputForm">
        SELECT
        tpa.ID AccountID,
        tpa.ProductID,
        p.Name,
        p.Alias,
        tpa.Quantity,
        CASE
        WHEN tpa.TotalAmount / tpa.Quantity &lt;= 0 THEN tpa.Balance - dbo.getRefundBillProductQuantity(tpa.ID)
        WHEN (tpa.Balance - CEILING(tpa.ArrearAmount*tpa.Quantity / tpa.TotalAmount)) &lt;= 0 THEN 0
        WHEN (tpa.Balance - CEILING(tpa.ArrearAmount*tpa.Quantity / tpa.TotalAmount)) > 0 AND (tpa.Balance -
        CEILING(tpa.ArrearAmount*tpa.Quantity /
        tpa.TotalAmount) - dbo.getRefundBillProductQuantity(tpa.ID))>0 THEN (tpa.Balance -
        CEILING(tpa.ArrearAmount*tpa.Quantity /
        tpa.TotalAmount) - dbo.getRefundBillProductQuantity(tpa.ID))
        ELSE 0
        END ValidBalance,
        tpa.Balance,
        dbo.getRefundBillProductQuantity(tpa.ID) RefundBalance,
        CAST(tpa.TotalAmount/tpa.Quantity as decimal(38, 2)) AS Amount,
        tpa.ArrearAmount,
        p.Specification,
        tpa.IsLargess,
        CONVERT(VARCHAR(10),tpa.BuyDate,120) BuyDate,
        CASE tpa.Channel WHEN '10' THEN '购买' WHEN '20' THEN '转账' WHEN '30' THEN '存量导入' WHEN '40' THEN '线上商城' END
        ChannelName
        FROM
        TB_ProductAccount tpa WITH(NOLOCK)
        LEFT JOIN TE_Product p WITH(NOLOCK) ON p.ID = tpa.ProductID
        LEFT JOIN TB_Product bp WITH(NOLOCK) ON p.ID = bp.ProductID
        LEFT JOIN TK_Entity e WITH(NOLOCK) ON e.ID = tpa.EntityID
        <where>
            tpa.PackageCardAccountID =#{AccountID}
        </where>
        ORDER BY tpa.BuyDate DESC, tpa.ID
    </select>

    <select id="getPackageProject" parameterType="java.lang.Integer"
            resultType="com.zhelian.model.micromall.form.output.MicromallProjectAccountOutputForm">
        SELECT
        tpa.ID AccountID,
        tpa.ProjectID,
        p.Name,
        p.Alias,
        tpa.Quantity,
        CASE
        WHEN tpa.TotalAmount / tpa.Quantity &lt;= 0 THEN tpa.Balance - dbo.getRefundBillProjectQuantity(tpa.ID)
        WHEN (tpa.Balance - CEILING(tpa.ArrearAmount*tpa.Quantity / tpa.TotalAmount)) &lt;= 0 THEN 0
        WHEN (tpa.Balance - CEILING(tpa.ArrearAmount*tpa.Quantity / tpa.TotalAmount)) > 0 THEN (tpa.Balance -
        CEILING(tpa.ArrearAmount*tpa.Quantity /
        tpa.TotalAmount) - dbo.getRefundBillProjectQuantity(tpa.ID))
        END ValidBalance,
        tpa.Balance,
        dbo.getRefundBillProjectQuantity(tpa.ID) RefundBalance,
        CAST(tpa.TotalAmount/tpa.Quantity as decimal(38, 2)) AS Amount,
        tpa.ArrearAmount,
        tpa.IsLargess,
        CONVERT(VARCHAR(10),tpa.BuyDate,120) BuyDate,
        CASE tpa.Channel WHEN '10' THEN '购买' WHEN '20' THEN '转账' WHEN '30' THEN '存量导入' WHEN '40' THEN '线上商城' END
        ChannelName
        FROM
        TB_ProjectAccount tpa WITH(NOLOCK)
        LEFT JOIN TB_Project p WITH(NOLOCK) ON p.ID = tpa.ProjectID
        LEFT JOIN TK_Entity e WITH(NOLOCK) ON e.ID = tpa.EntityID
        <where>
            tpa.PackageCardAccountID =#{AccountID}
        </where>
        ORDER BY tpa.BuyDate DESC, tpa.ID
    </select>

    <select id="getPackageGeneralCard" parameterType="java.lang.Integer"
            resultType="com.zhelian.model.micromall.form.output.MicromallGeneralCardAccountOutputForm">
        SELECT
        tpa.ID AccountID,
        tpa.GeneralCardID,
        p.Name,
        ISNULL(p.Alias,'') Alias,
        CASE
        WHEN tpa.ValidType = 3 THEN '永久有效'
        ELSE CONVERT(VARCHAR(10),tpa.ValidDate,120)
        END ValidDayName,
        CASE
        WHEN tpa.TotalAmount / tpa.CardTimes &lt;= 0 THEN tpa.Balance - dbo.getRefundBillGeneralCardQuantity(tpa.ID)
        WHEN (tpa.Balance - CEILING(tpa.ArrearAmount*tpa.CardTimes / tpa.TotalAmount)) &lt;= 0 THEN 0
        WHEN (tpa.Balance - CEILING(tpa.ArrearAmount*tpa.CardTimes / tpa.TotalAmount)) > 0 THEN (tpa.Balance -
        CEILING(tpa.ArrearAmount*tpa.CardTimes /
        tpa.TotalAmount) - dbo.getRefundBillGeneralCardQuantity(tpa.ID))
        END ValidBalance,
        tpa.CardTimes,
        dbo.getRefundBillGeneralCardQuantity(tpa.ID) RefundAmount,
        CAST(tpa.TotalAmount/tpa.CardTimes as decimal(38, 2)) AS Amount,
        tpa.ArrearAmount,
        tpa.Balance,
        tpa.IsLargess,
        CONVERT(VARCHAR(10),tpa.BuyDate,120) BuyDate,
        CASE tpa.Channel WHEN '10' THEN '购买' WHEN '20' THEN '转账' WHEN '30' THEN '存量导入' WHEN '40' THEN '线上商城' END
        ChannelName
        FROM
        TB_GeneralCardAccount tpa
        LEFT JOIN TB_GeneralCard p WITH(NOLOCK) ON p.ID = tpa.GeneralCardID
        LEFT JOIN TK_Entity e WITH(NOLOCK) ON e.ID = tpa.EntityID
        <where>
            tpa.PackageCardAccountID=#{AccountID}
        </where>
        ORDER BY tpa.BuyDate DESC, tpa.ID
    </select>

    <select id="getPackageTimeCard" parameterType="java.lang.Integer"
            resultType="com.zhelian.model.micromall.form.output.MicromallTimeCardAccountOutputForm">
        SELECT tpa.ID                                                                     AccountID,
               tpa.TimeCardID,
               p.Name,
               p.Alias,
               CASE
                   WHEN tpa.ValidType = 3 THEN '永久有效'
                   ELSE CONVERT(VARCHAR (10), tpa.ValidDate, 120)
                   END                                                                    ValidDayName,
               p.Memo,
               p.ConsumeCycle,
               p.CycleLimitAmount,
               ISNULL(dbo.GetTimeCardConsumeCycleAmount(
                              (SELECT TOP 1 CONVERT (VARCHAR (10),BillDate,120) BillDate
                                  FROM TB_TreatBillTimeCard d
                                  LEFT JOIN TB_TreatBill m ON m.ID=d.TreatBillID
                                  WHERE m.BillStatus='20' and d.TimeCardAccountID =tpa.ID and m.BillType = '10'
                                  ORDER BY BillDate),p.ConsumeCycle,GETDATE(),tpa.ID), 0) ConsumeCycleAmount,
               tpa.ArrearAmount,
               tpa.IsLargess,
               CASE
                   WHEN tpa.PerformanceTimes = 0 THEN 0
                   ELSE CAST(tpa.TotalAmount / tpa.PerformanceTimes as decimal(18, 2))
                   END                                                                    Amount,
               CONVERT(VARCHAR (10), tpa.BuyDate, 120)                                    BuyDate,
               tpa.TotalAmount,
               ISNULL(
                       (SELECT SUM(T.Quantity)
                        FROM (SELECT CASE
                                         WHEN
                                             a2.BillType = '10' then a1.Quantity
                                         ELSE a1.Quantity * -1
                                         END Quantity
                              from TB_TreatBillTimeCard a1
                                       JOIN TB_TreatBill a2 ON a1.TreatBillID = a2.ID
                              WHERE a1.TimeCardAccountID = tpa.ID
                                AND a2.BillStatus = '20') T), 0)                          ConsumeAmount,
               CASE tpa.Channel
                   WHEN '10' THEN '购买'
                   WHEN '20' THEN '转账'
                   WHEN '30' THEN '存量导入'
                   WHEN '40' THEN '线上商城' END                                              ChannelName
        FROM TB_TimeCardAccount tpa
                 LEFT JOIN TB_TimeCard p WITH (NOLOCK)
        ON p.ID = tpa.TimeCardID
        WHERE
            tpa.PackageCardAccountID=#{AccountID}
        ORDER BY tpa.BuyDate DESC, tpa.ID
    </select>

    <select id="getPackageSavingCard" parameterType="java.lang.Integer"
            resultType="com.zhelian.model.micromall.form.output.MicromallSavingCardAccountOutputForm">
        SELECT
        sca.ID AccountID,
        sca.SavingCardID,
        s.Name,
        s.Alias,
        s.Memo,
        CASE
        WHEN sca.ValidType = 3 THEN '永久有效'
        ELSE CONVERT(VARCHAR(10),sca.ValidDate,120)
        END ValidDayName,
        sca.Balance,
        sca.LargessBalance,
        sca.Balance+sca.LargessBalance TotalBalance,
        dbo.getRefundBillSavingCardTotalAmount(sca.ID) RefundAmount,
        CASE WHEN sca.Amount >0 THEN
        CONVERT(DECIMAL(18,2),sca.Balance - sca.ArrearAmount +
        sca.LargessBalance -sca.ArrearAmount*sca.LargessAmount/sca.Amount
        -dbo.getRefundBillSavingCardAmount(sca.ID)-dbo.getRefundBillSavingCardLargessAmount(sca.ID))
        ELSE sca.LargessBalance
        END ValidBalance,
        sca.ArrearAmount,
        CONVERT(VARCHAR(10),sca.BuyDate,120) BuyDate,
        CASE sca.Channel WHEN '10' THEN '购买' WHEN '20' THEN '转账' WHEN '30' THEN '存量导入' WHEN '40' THEN '线上商城' END
        ChannelName,
        sca.Amount TotalAmount
        FROM
        TB_SavingCardAccount sca WITH (NOLOCK)
        LEFT JOIN TB_SavingCard s WITH (NOLOCK) ON sca.SavingCardID = s.ID
        LEFT JOIN TK_Entity e WITH(NOLOCK) ON e.ID = sca.EntityID
        <where>
            sca.PackageCardAccountID=#{AccountID}
        </where>
        ORDER BY sca.BuyDate DESC,s.Name,sca.ID
    </select>

    <update id="updatePackageCardRemark">
        UPDATE TB_PackageCardAccount
        SET Remark = #{Remark}
        WHERE SaleBillPackageCardID = #{SaleBillGoodID}
    </update>

    <update id="updatePackageCardBillDate">
        UPDATE R
        SET R.BuyDate = #{BillDate} FROM TB_PackageCardAccount  R
        INNER JOIN TB_SaleBillPackageCard P
        ON R.SaleBillPackageCardID = P.ID
        WHERE P.BillID = #{SaleBillID}
    </update>


    <update id="updateArrearPackageCardAccountTotalAmount" parameterType="Integer">
        UPDATE t1
        SET t1.TotalAmount                     = t2.TotalAmount,
            t1.PayAmount                       = t2.PayAmount,
            t1.SavingCardDeductionAmount=t2.SavingCardDeductionAmount,
            t1.SavingCardDeductionLargessAmount=t2.SavingCardDeductionLargessAmount,
            t1.ArrearAmount=t2.ArrearAmount  FROM TB_PackageCardAccount t1
        INNER JOIN
        (
        SELECT PackageCardAccountID,SUM(TotalAmount) TotalAmount,SUM(PayAmount) PayAmount, SUM(SavingCardDeductionAmount) SavingCardDeductionAmount, SUM(SavingCardDeductionLargessAmount) SavingCardDeductionLargessAmount,SUM(ArrearAmount) ArrearAmount  FROM (
        SELECT a1.PackageCardAccountID,a1.TotalAmount TotalAmount,a1.PayAmount,a1.SavingCardDeductionAmount,a1.SavingCardDeductionLargessAmount,a1.ArrearAmount FROM TB_GeneralCardAccount a1,TB_PackageCardAccount a2
        WHERE a1.PackageCardAccountID=a2.ID and a1.IsLargess=0
        and  EXISTS (
        SELECT
        1
        FROM
        TB_PackageCardAccount tpca
        WHERE EXISTS (
        SELECT
        1
        FROM
        TB_SaleBillPackageCard tsbpc
        WHERE tsbpc.PackageCardAccountID = tpca.ID AND tsbpc.ID = #{SaleBillPackageCardID}
            ) AND tpca.SaleBillPackageCardID = a2.SaleBillPackageCardID
            )
            UNION ALL
            SELECT a1.PackageCardAccountID, a1.TotalAmount TotalAmount, a1.PayAmount, a1.SavingCardDeductionAmount, a1.SavingCardDeductionLargessAmount, a1.ArrearAmount FROM TB_ProductAccount a1, TB_PackageCardAccount a2
            WHERE a1.PackageCardAccountID=a2.ID and a1.IsLargess=0
            and  EXISTS (
            SELECT
            1
            FROM
            TB_PackageCardAccount tpca
            WHERE EXISTS (
            SELECT
            1
            FROM
            TB_SaleBillPackageCard tsbpc
            WHERE tsbpc.PackageCardAccountID = tpca.ID AND tsbpc.ID = #{SaleBillPackageCardID}
            ) AND tpca.SaleBillPackageCardID = a2.SaleBillPackageCardID
            )
            UNION ALL
            SELECT a1.PackageCardAccountID, a1.TotalAmount TotalAmount, a1.PayAmount, a1.SavingCardDeductionAmount, a1.SavingCardDeductionLargessAmount, a1.ArrearAmount FROM TB_ProjectAccount a1, TB_PackageCardAccount a2
            WHERE a1.PackageCardAccountID=a2.ID and a1.IsLargess=0
            and  EXISTS (
            SELECT
            1
            FROM
            TB_PackageCardAccount tpca
            WHERE EXISTS (
            SELECT
            1
            FROM
            TB_SaleBillPackageCard tsbpc
            WHERE tsbpc.PackageCardAccountID = tpca.ID AND tsbpc.ID = #{SaleBillPackageCardID}
            ) AND tpca.SaleBillPackageCardID = a2.SaleBillPackageCardID
            )
            UNION ALL
            SELECT a1.PackageCardAccountID, a1.TotalAmount TotalAmount, a1.PayAmount, a1.SavingCardDeductionAmount, a1.SavingCardDeductionLargessAmount, a1.ArrearAmount FROM TB_TimeCardAccount a1, TB_PackageCardAccount a2
            WHERE a1.PackageCardAccountID=a2.ID and a1.IsLargess=0
            and  EXISTS (
            SELECT
            1
            FROM
            TB_PackageCardAccount tpca
            WHERE EXISTS (
            SELECT
            1
            FROM
            TB_SaleBillPackageCard tsbpc
            WHERE tsbpc.PackageCardAccountID = tpca.ID AND tsbpc.ID = #{SaleBillPackageCardID}
            ) AND tpca.SaleBillPackageCardID = a2.SaleBillPackageCardID
            )
            UNION ALL
            SELECT a1.PackageCardAccountID, a1.Amount TotalAmount, a1.Amount-a1.ArrearAmount PayAmount, 0 SavingCardDeductionAmount, 0 SavingCardDeductionLargessAmount, a1.ArrearAmount FROM TB_SavingCardAccount a1, TB_PackageCardAccount a2
            WHERE a1.PackageCardAccountID=a2.ID
            and  EXISTS (
            SELECT
            1
            FROM
            TB_PackageCardAccount tpca
            WHERE EXISTS (
            SELECT
            1
            FROM
            TB_SaleBillPackageCard tsbpc
            WHERE tsbpc.PackageCardAccountID = tpca.ID AND tsbpc.ID = #{SaleBillPackageCardID}
            ) AND tpca.SaleBillPackageCardID = a2.SaleBillPackageCardID
            )
            ) a GROUP BY PackageCardAccountID) t2
            on t1.ID=t2.PackageCardAccountID and  EXISTS (
            SELECT
            1
            FROM
            TB_SaleBillPackageCard tsbpc
            WHERE tsbpc.PackageCardAccountID = t1.ID AND tsbpc.ID = #{SaleBillPackageCardID}
            )
    </update>

    <update id="updatePackageCardAccountArrear"
            parameterType="com.zhelian.model.ibeauty.entity.PackageCardAccountEntity">
        UPDATE TB_PackageCardAccount
        <set>
            <if test="ArrearAmount != null">ArrearAmount = #{ArrearAmount},</if>
            <if test="PayAmount != null">PayAmount = #{PayAmount},</if>
            <if test="SavingCardDeductionAmount != null">SavingCardDeductionAmount = #{SavingCardDeductionAmount},</if>
            <if test="SavingCardDeductionLargessAmount != null">SavingCardDeductionLargessAmount = #{SavingCardDeductionLargessAmount}</if>
        </set>
        WHERE ID = #{ID}
    </update>
</mapper>
