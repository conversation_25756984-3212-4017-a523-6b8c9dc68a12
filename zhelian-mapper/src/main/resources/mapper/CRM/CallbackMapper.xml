<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.CRM.CallbackMapper">

    <resultMap id="Callback" type="com.zhelian.model.CRM.form.output.CallbackOutputForm">
        <id column="ID" property="ID" jdbcType="INTEGER"/>
        <result column="IsCallback" property="IsCallback" jdbcType="BOOLEAN"/>
        <result column="MethodName" property="MethodName" jdbcType="VARCHAR"/>
        <result column="Status" property="Status" jdbcType="VARCHAR"/>
        <result column="PlannedOn" property="PlannedOn" jdbcType="VARCHAR"/>
        <result column="CallbackOn" property="CallbackOn" jdbcType="VARCHAR"/>
        <result column="CallbackEmployeeName" property="CallbackEmployeeName" jdbcType="VARCHAR"/>
        <result column="CustomerID" property="CustomerID" jdbcType="INTEGER"/>
        <result column="CustomerName" property="CustomerName" jdbcType="VARCHAR"/>
        <result column="PhoneNumber" property="PhoneNumber" jdbcType="VARCHAR"/>
        <result column="Gender" property="Gender" jdbcType="VARCHAR"/>
        <result column="CustomerSourceName" property="CustomerSourceName" jdbcType="VARCHAR"/>
        <result column="CustomerLevelName" property="CustomerLevelName" jdbcType="VARCHAR"/>
        <result column="Code" property="Code" jdbcType="VARCHAR"/>
        <result column="CustomerCreatedOn" property="CustomerCreatedOn" jdbcType="VARCHAR"/>
        <result column="Birthday" property="Birthday" jdbcType="VARCHAR"/>
        <result column="Avatar" property="Avatar" jdbcType="VARCHAR"/>
        <result column="EntityName" property="EntityName" jdbcType="VARCHAR"/>
        <result column="BirthdayType" property="BirthdayType" jdbcType="VARCHAR"/>
        <result column="State" property="State" jdbcType="VARCHAR"/>
        <result column="ChannelName" property="ChannelName" jdbcType="VARCHAR"/>

        <collection property="ServicerEmployee" column="{CustomerID=CustomerID}" select="getServicer"
                    ofType="com.zhelian.model.CRM.form.output.ServicerOutputForm"/>
    </resultMap>

    <resultMap id="ServicerEmployee" type="com.zhelian.model.CRM.form.output.ServicerOutputForm">
        <result column="ServicerEmployeeID" property="ID" jdbcType="INTEGER"/>
        <result column="ServicerEmployeeName" property="Name" jdbcType="VARCHAR"/>
        <collection property="ServicerEmpList" ofType="com.zhelian.model.CRM.form.output.ServicerEmpListOutputForm">
            <result column="EmployeeID" property="ID" jdbcType="INTEGER"/>
            <result column="ServicerID" property="ServicerID" jdbcType="INTEGER"/>
            <result column="EmployeeName" property="Name" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <select id="getServicer" resultMap="ServicerEmployee">
        SELECT r.ServicerID ServicerEmployeeID,
               ts.Name      ServicerEmployeeName,
               r.EmployeeID EmployeeID,
               r.ServicerID,
               e.Name       EmployeeName
        FROM TB_CustomerServicer r WITH(NOLOCK)
                LEFT JOIN TB_Servicer ts
        WITH (NOLOCK)
        ON ts.ID = r.ServicerID
            LEFT JOIN TK_Employee e
        WITH (NOLOCK)
        ON e.ID = r.EmployeeID
        WHERE
            r.CustomerID =#{CustomerID}
          AND ts.Active=1
        order by ts.Sequence DESC
    </select>

    <select id="getCallbackList" resultMap="Callback">
        SELECT
        tf.ID,
        tf.IsCallback,
        tfc.Name MethodName,
        tfs.Name Status,
        CONVERT(VARCHAR(19),tf.PlannedOn,120) PlannedOn,
        CONVERT(VARCHAR(19),tf.CallbackOn,120) CallbackOn,
        te.Name CallbackEmployeeName,
        tc.ID CustomerID,
        tc.Name CustomerName,
        tc.PhoneNumber,
        tc.Gender,
        tcs.Name CustomerSourceName,
        tcl.Name CustomerLevelName,
        tc.Code,
        tc.Birthday,
        tc.Avatar,
        CONVERT(VARCHAR(19),tc.CreatedOn,120) CustomerCreatedOn,
        tc.BirthdayType,
        tke.EntityName,
        te.State

        FROM TB_CallbackRecord tf WITH(NOLOCK)
        LEFT JOIN TB_CallbackMethod tfc WITH(NOLOCK) ON tf.CallbackMethodID=tfc.ID
        LEFT JOIN TB_CallbackStatus tfs WITH(NOLOCK) ON tf.CallbackStatusID=tfs.ID
        LEFT JOIN TK_Employee te WITH(NOLOCK) ON tf.CallbackBy=te.ID
        LEFT JOIN TB_Customer tc WITH(NOLOCK) ON tf.CustomerID=tc.ID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tce WITH(NOLOCK) ON tce.CustomerID=tf.CustomerID AND tce.IsBelongEntity=1
        LEFT JOIN TK_Entity tke WITH(NOLOCK) ON tke.ID=tce.EntityID
        WHERE tf.CallbackBy=#{EmployeeID}
        <if test="Name != null and Name != ''">
            and ( tc.Name like '%'+ #{Name} +'%' or tc.PhoneNumber like '%'+ #{Name} +'%' or tc.Code like '%'+ #{Name}
            +'%')
        </if>
        <if test="CallbackMethodID != null and CallbackMethodID != ''">
            and tf.CallbackMethodID=#{CallbackMethodID}
        </if>
        <if test="CallbackStatusID != null and CallbackStatusID != ''">
            and tf.CallbackStatusID=#{CallbackStatusID}
        </if>
        <if test="IsCallback != null ">
            and tf.IsCallback=#{IsCallback}
        </if>
        <if test="StartDate != null and StartDate != '' and EndDate != null and EndDate != ''">
            AND tf.PlannedOn &gt;= #{StartDate} AND tf.PlannedOn &lt; DATEADD(DAY,1,#{EndDate})
        </if>
        ORDER BY IsCallback, PlannedOn
    </select>

    <select id="allCallbackList" resultMap="Callback">
        SELECT
        tf.ID,
        tf.IsCallback,
        tfc.Name MethodName,
        tfs.Name Status,
        CONVERT(VARCHAR(19),tf.PlannedOn,120) PlannedOn,
        CONVERT(VARCHAR(19),tf.CallbackOn,120) CallbackOn,
        te.Name CallbackEmployeeName,
        tc.ID CustomerID,
        tc.Name CustomerName,
        tc.PhoneNumber,
        tc.Gender,
        tcs.Name CustomerSourceName,
        tcl.Name CustomerLevelName,
        tc.Code,
        tc.Birthday,
        tc.Avatar,
        CONVERT(VARCHAR(19),tc.CreatedOn,120) CustomerCreatedOn,
        tc.BirthdayType,
        tke.EntityName,
        te.State,
        tccl.Name ChannelName

        FROM TB_CallbackRecord tf WITH(NOLOCK)
        LEFT JOIN TB_CallbackMethod tfc WITH(NOLOCK) ON tf.CallbackMethodID=tfc.ID
        LEFT JOIN TB_CallbackStatus tfs WITH(NOLOCK) ON tf.CallbackStatusID=tfs.ID
        LEFT JOIN TK_Employee te WITH(NOLOCK) ON tf.CallbackBy=te.ID
        LEFT JOIN TB_Customer tc WITH(NOLOCK) ON tf.CustomerID=tc.ID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_Channel tccl WITH(NOLOCK) ON tccl.ID=tc.ChannelID
        LEFT JOIN TB_CustomerEntity tce WITH(NOLOCK) ON tce.CustomerID=tf.CustomerID AND tce.IsBelongEntity=1
        LEFT JOIN TK_Entity tke WITH(NOLOCK) ON tke.ID=tce.EntityID
        <where>
            <choose>
                <when test="IsShowOwnCallback != null">
                    and tf.CallbackBy=#{EmployeeID}
                </when>
                <otherwise>
                    AND (EXISTS (
                    SELECT 1 FROM TK_EmployeeEntity tey WITH(NOLOCK)
                    WHERE IsPrimaryEntity=1 AND tey.EmployeeID=tf.CallbackBy
                    AND EXISTS (
                    SELECT
                    1
                    FROM
                    TB_EmployeeEntityPermission tepn WITH(NOLOCK)
                    left join TK_Entity tty WITH(NOLOCK) on tty.ID=tepn.EntityID
                    where tty.Active=1 and tepn.EmployeeID = #{EmployeeID} AND tey.EntityID=tepn.EntityID
                    )
                    ) OR tf.CallbackBy=#{EmployeeID})
                </otherwise>
            </choose>
            <if test="Name != null and Name != ''">
                and ( tc.Name like '%'+ #{Name} +'%' or tc.PhoneNumber like '%'+ #{Name} +'%' or tc.Code like '%'+
                #{Name} +'%')
            </if>
            <if test="CallbackEmployeeID != null and CallbackEmployeeID != ''">
                and tf.CallbackBy=#{CallbackEmployeeID}
            </if>
            <if test="CallbackMethodID != null and CallbackMethodID != ''">
                and tf.CallbackMethodID=#{CallbackMethodID}
            </if>
            <if test="CallbackStatusID != null and CallbackStatusID != ''">
                and tf.CallbackStatusID=#{CallbackStatusID}
            </if>
            <if test="IsCallback != null ">
                and tf.IsCallback=#{IsCallback}
            </if>
            <if test="StartDate != null and StartDate != '' and EndDate != null and EndDate != ''">
                AND tf.PlannedOn &gt;= #{StartDate} AND tf.PlannedOn &lt; DATEADD(DAY,1,#{EndDate})
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND tccl.Name like '%'+ #{ChannelName} +'%'
            </if>
            <if test="CustomerLevelID != null ">
                <choose>
                    <when test="CustomerLevelID !=0 ">
                        and tc.CustomerLevelID =#{CustomerLevelID}
                    </when>
                    <otherwise>
                        and tc.CustomerLevelID IS NULL
                    </otherwise>
                </choose>
            </if>
        </where>

        ORDER BY IsCallback, PlannedOn
    </select>

    <insert id="createCallback" parameterType="com.zhelian.model.CRM.entity.CallbackRecordEntity"
            useGeneratedKeys="true" keyColumn="ID" keyProperty="ID">
        INSERT INTO TB_CallbackRecord
        (     CustomerID
            , CallbackMethodID
            , CallbackStatusID
            , IsCallback
            , PlannedOn
            , CallbackOn
            , CallbackBy)
        VALUES
            ( #{CustomerID}
                , #{CallbackMethodID}
                , #{CallbackStatusID}
                , #{IsCallback}
                , #{PlannedOn}
                , #{CallbackOn}
                , #{CallbackBy})
    </insert>
    <insert id="createCallbackProject" parameterType="com.zhelian.model.CRM.entity.CallbackRecordProjectEntity">
        INSERT INTO TB_CallbackRecordProject
        (     CallbackRecordID
            , TreatBillID
            , ProjectID
            , CallbackCycle
            , CallbackRemark
            , CallbackContent)
        VALUES
            ( #{CallbackRecordID}
                , #{TreatBillID}
                , #{ProjectID}
                , #{CallbackCycle}
                , #{CallbackRemark}
                , #{CallbackContent}
            )
    </insert>

    <select id="allEntity"
            resultType="com.zhelian.model.khs.entity.EntityEntity">
        SELECT a1.*
        FROM TK_Entity a1 WITH (NOLOCK)
        WHERE
            a1.Active = 1
          AND a1.IsStore = 1
    </select>


    <select id="getCustomerServicer" resultType="java.lang.String">
        SELECT ServicerID
        FROM TB_CustomerServicer a1 WITH (NOLOCK)
        WHERE
            a1.CustomerID =#{CustomerID}
    </select>

    <select id="getCustomerServicerEmployee" resultType="java.lang.String">
        SELECT EmployeeID
        FROM TB_CustomerServicer a1 WITH (NOLOCK)
        WHERE
            a1.CustomerID =#{CustomerID}
          AND a1.ServicerID = #{ServicerID}
    </select>

    <select id="getCallbackRecord" resultType="com.zhelian.model.CRM.entity.CallbackRecordEntity">
        SELECT *
        FROM TB_CallbackRecord
        WHERE ID = #{CallbackRecordID}
    </select>

    <select id="getCallbackRecordProject" resultType="com.zhelian.model.CRM.entity.CallbackRecordProjectEntity">
        SELECT *
        FROM TB_CallbackRecordProject
        WHERE ID = #{CallbackRecordProjectID}
    </select>

    <resultMap id="Handler" type="com.zhelian.model.CRM.form.output.CallbackTreatBillProjectOutputForm">
        <id column="TreatBillProjectID" property="TreatBillProjectID" jdbcType="INTEGER"/>
        <result column="Type" property="Type" jdbcType="VARCHAR"/>
        <result column="CustomerID" property="CustomerID" jdbcType="INTEGER"/>
        <result column="BillID" property="BillID" jdbcType="VARCHAR"/>
        <result column="ProjectID" property="ProjectID" jdbcType="INTEGER"/>
        <result column="BillDate" property="BillDate" jdbcType="VARCHAR"/>
        <collection property="TreatBillHandler"
                    ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerOutputForm">
            <id column="TreatHandlerID" property="TreatHandlerID" jdbcType="INTEGER"/>
            <result column="TreatHandlerName" property="TreatHandlerName" jdbcType="VARCHAR"/>
            <collection property="Employee"
                        ofType="com.zhelian.model.ibeauty.form.output.TreatBillHandlerEmployeeOutputForm">
                <result column="EmployeeID" property="EmployeeID" jdbcType="INTEGER"/>
                <result column="EmployeeName" property="EmployeeName" jdbcType="VARCHAR"/>
                <result column="Scale" property="Scale" jdbcType="DECIMAL"/>
            </collection>
        </collection>
    </resultMap>


    <select id="getTreatBillByCallbackProject" resultMap="Handler">
        SELECT '10'                                   Type,
               t.CustomerID,
               t.ID                                   BillID,
               tt.ProjectID,
               tt.ID                                  TreatBillProjectID,
               CONVERT(VARCHAR (10), t.BillDate, 120) BillDate,
               tsh.ProjectTreatHandlerID              TreatHandlerID,
               tph.Name                               TreatHandlerName,
               tsh.EmployeeID,
               tee.Name                               EmployeeName,
               tsh.Scale * 100                        Scale
        FROM TB_TreatBillProject tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t
        WITH (NOLOCK)
        ON tt.TreatBillID=t.ID
            LEFT JOIN TB_Project tp
        WITH (NOLOCK)
        ON tt.ProjectID=tp.ID
            LEFT JOIN TB_ProjectCategory tpcp
        WITH (NOLOCK)
        ON tp.ProjectCategoryID=tpcp.ID
            LEFT JOIN TB_TreatBillHandlerProject tsh
        WITH (NOLOCK)
        ON tsh.TreatBillProjectID=tt.ID
            LEFT JOIN TB_ProjectTreatHandler tph
        WITH (NOLOCK)
        ON tph.ID=tsh.ProjectTreatHandlerID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tsh.EmployeeID
        WHERE t.ID=#{BillID}
          AND t.BillStatus='20'
          AND t.BillType='10'
          and (EXISTS (
            SELECT 1 FROM TB_CallbackRuleProjectCategory tcp WITH (NOLOCK) WHERE tcp.CallbackRuleID=#{ID}
          AND (tcp.ProjectCategoryID=tp.ProjectCategoryID
           OR tcp.ProjectCategoryID=tpcp.ParentID)
            )
           OR
            EXISTS (
            SELECT 1 FROM TB_CallbackRuleProject tcp WITH (NOLOCK) WHERE tcp.CallbackRuleID=#{ID}
          AND tcp.ProjectID=tt.ProjectID
            ))
        UNION ALL
        SELECT '20'                                   Type,
               t.CustomerID,
               t.ID                                   BillID,
               tt.ProjectID,
               tt.ID                                  TreatBillGeneralCardID,
               CONVERT(VARCHAR (10), t.BillDate, 120) BillDate,
               tsh.GeneralCardTreatHandlerID          TreatHandlerID,
               tph.Name                               TreatHandlerName,
               tsh.EmployeeID,
               tee.Name                               EmployeeName,
               tsh.Scale * 100                        Scale
        FROM TB_TreatBillGeneralCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t
        WITH (NOLOCK)
        ON tt.TreatBillID=t.ID
            LEFT JOIN TB_Project tp
        WITH (NOLOCK)
        ON tt.ProjectID=tp.ID
            LEFT JOIN TB_ProjectCategory tpcp
        WITH (NOLOCK)
        ON tp.ProjectCategoryID=tpcp.ID
            LEFT JOIN TB_TreatBillHandlerGeneralCard tsh
        WITH (NOLOCK)
        ON tsh.TreatBillGeneralCardID=tt.ID
            LEFT JOIN TB_ProjectTreatHandler tph
        WITH (NOLOCK)
        ON tph.ID=tsh.GeneralCardTreatHandlerID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tsh.EmployeeID
        WHERE t.ID=#{BillID}
          AND t.BillStatus='20'
          AND t.BillType='10'
          and (EXISTS (
            SELECT 1 FROM TB_CallbackRuleProjectCategory tcp WITH (NOLOCK) WHERE tcp.CallbackRuleID=#{ID}
          AND (tcp.ProjectCategoryID=tp.ProjectCategoryID
           OR tcp.ProjectCategoryID=tpcp.ParentID)
            )
           OR
            EXISTS (
            SELECT 1 FROM TB_CallbackRuleProject tcp WITH (NOLOCK) WHERE tcp.CallbackRuleID=#{ID}
          AND tcp.ProjectID=tt.ProjectID
            ))
        UNION ALL
        SELECT '30'                                   Type,
               t.CustomerID,
               t.ID                                   BillID,
               tt.ProjectID,
               tt.ID                                  TreatBillTimeCardID,
               CONVERT(VARCHAR (10), t.BillDate, 120) BillDate,
               tsh.TimeCardTreatHandlerID             TreatHandlerID,
               tph.Name                               TreatHandlerName,
               tsh.EmployeeID,
               tee.Name                               EmployeeName,
               tsh.Scale * 100                        Scale
        FROM TB_TreatBillTimeCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t
        WITH (NOLOCK)
        ON tt.TreatBillID=t.ID
            LEFT JOIN TB_Project tp
        WITH (NOLOCK)
        ON tt.ProjectID=tp.ID
            LEFT JOIN TB_ProjectCategory tpcp
        WITH (NOLOCK)
        ON tp.ProjectCategoryID=tpcp.ID
            LEFT JOIN TB_TreatBillHandlerTimeCard tsh
        WITH (NOLOCK)
        ON tsh.TreatBillTimeCardID=tt.ID
            LEFT JOIN TB_ProjectTreatHandler tph
        WITH (NOLOCK)
        ON tph.ID=tsh.TimeCardTreatHandlerID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tsh.EmployeeID
        WHERE t.ID=#{BillID}
          AND t.BillStatus='20'
          AND t.BillType='10'
          and (EXISTS (
            SELECT 1 FROM TB_CallbackRuleProjectCategory tcp WITH (NOLOCK) WHERE tcp.CallbackRuleID=#{ID}
          AND (tcp.ProjectCategoryID=tp.ProjectCategoryID
           OR tcp.ProjectCategoryID=tpcp.ParentID)
            )
           OR
            EXISTS (
            SELECT 1 FROM TB_CallbackRuleProject tcp WITH (NOLOCK) WHERE tcp.CallbackRuleID=#{ID}
          AND tcp.ProjectID=tt.ProjectID
            ))
        UNION ALL
        SELECT '40'                                   Type,
               t.CustomerID,
               t.ID                                   BillID,
               tt.ProjectID,
               tt.ID                                  TreatBillSavingCardID,
               CONVERT(VARCHAR (10), t.BillDate, 120) BillDate,
               tsh.SavingCardTreatHandlerID           TreatHandlerID,
               tph.Name                               TreatHandlerName,
               tsh.EmployeeID,
               tee.Name                               EmployeeName,
               tsh.Scale * 100                        Scale
        FROM TB_TreatBillSavingCard tt WITH(NOLOCK)
        LEFT JOIN TB_TreatBill t
        WITH (NOLOCK)
        ON tt.TreatBillID=t.ID
            LEFT JOIN TB_Project tp
        WITH (NOLOCK)
        ON tt.ProjectID=tp.ID
            LEFT JOIN TB_ProjectCategory tpcp
        WITH (NOLOCK)
        ON tp.ProjectCategoryID=tpcp.ID
            LEFT JOIN TB_TreatBillHandlerSavingCard tsh
        WITH (NOLOCK)
        ON tsh.TreatBillSavingCardID=tt.ID
            LEFT JOIN TB_SavingCardProjectTreatHandler tph
        WITH (NOLOCK)
        ON tph.ID=tsh.SavingCardTreatHandlerID
            LEFT JOIN TK_Employee tee
        WITH (NOLOCK)
        ON tee.ID=tsh.EmployeeID
        WHERE t.ID=#{BillID}
          AND t.BillStatus='20'
          AND t.BillType='10'
          and (EXISTS (
            SELECT 1 FROM TB_CallbackRuleProjectCategory tcp WITH (NOLOCK) WHERE tcp.CallbackRuleID=#{ID}
          AND (tcp.ProjectCategoryID=tp.ProjectCategoryID
           OR tcp.ProjectCategoryID=tpcp.ParentID)
            )
           OR
            EXISTS (
            SELECT 1 FROM TB_CallbackRuleProject tcp WITH (NOLOCK) WHERE tcp.CallbackRuleID=#{ID}
          AND tcp.ProjectID=tt.ProjectID
            ))
    </select>


    <update id="updateCallbackRecord" parameterType="com.zhelian.model.CRM.entity.CallbackRecordEntity">
        UPDATE TB_CallbackRecord
        SET CallbackMethodID = #{CallbackMethodID}
                , CallbackStatusID = #{CallbackStatusID}
                , IsCallback = #{IsCallback}
                , CallbackOn = #{CallbackOn}
                , CallbackBy = #{CallbackBy}
        WHERE ID = #{ID}
    </update>

    <update id="updateCallbackRecordProject" parameterType="com.zhelian.model.CRM.entity.CallbackRecordProjectEntity">
        UPDATE TB_CallbackRecordProject
        SET CallbackContent = #{CallbackContent}
        WHERE ID = #{ID}
    </update>

    <insert id="insertCallbackProjectAttachment" parameterType="java.util.List">
        INSERT INTO TB_CallbackRecordProjectAttachment (CallbackRecordProjectID,AttachmentURL,AttachmentType) VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.CallbackRecordProjectID},#{item.AttachmentURL},#{item.AttachmentType})
        </foreach>
    </insert>

    <resultMap id="Detail" type="com.zhelian.model.CRM.form.output.CallbackRecordDetailOutputForm">
        <result column="CallbackMethodID" property="CallbackMethodID" jdbcType="INTEGER"/>
        <result column="CallbackStatusID" property="CallbackStatusID" jdbcType="INTEGER"/>
        <collection property="Content" ofType="com.zhelian.model.CRM.form.output.CallbackRecordProjectDetailOutputForm">
            <id column="CallbackRecordProjectID" property="CallbackRecordProjectID" jdbcType="INTEGER"/>
            <result column="ProjectName" property="ProjectName" jdbcType="VARCHAR"/>
            <result column="CallbackCycle" property="CallbackCycle" jdbcType="INTEGER"/>
            <result column="CallbackRemark" property="CallbackRemark" jdbcType="VARCHAR"/>
            <result column="CallbackContent" property="CallbackContent" jdbcType="VARCHAR"/>
            <collection property="Attachment"
                        ofType="com.zhelian.model.CRM.form.output.CallbackRecordProjectDetaiAttachmentAddForm">
                <result column="AttachmentType" property="AttachmentType" jdbcType="INTEGER"/>
                <result column="AttachmentURL" property="AttachmentURL" jdbcType="VARCHAR"/>
            </collection>
        </collection>
    </resultMap>


    <select id="callbackRecordDetail" resultMap="Detail">
        SELECT tf.CallbackMethodID,
               tf.CallbackStatusID,
               tcb.ID  CallbackRecordProjectID,
               tp.Name ProjectName,
               tcb.CallbackCycle,
               tcb.CallbackRemark,
               tcb.CallbackContent,
               tcp.AttachmentType,
               tcp.AttachmentURL
        FROM TB_CallbackRecord tf WITH(NOLOCK)
        LEFT JOIN TB_CallbackRecordProject tcb
        WITH (NOLOCK)
        ON tf.ID=tcb.CallbackRecordID
            LEFT JOIN TB_Project tp
        WITH (NOLOCK)
        ON tp.ID=tcb.ProjectID
            LEFT JOIN TB_CallbackRecordProjectAttachment tcp
        WITH (NOLOCK)
        ON tcp.CallbackRecordProjectID=tcb.ID
        WHERE tf.ID=#{CallbackRecordID}
        ORDER BY tcb.ID
    </select>

    <delete id="deleteCallbackRecordProjectAttachment">
        DELETE
        TB_CallbackRecordProjectAttachment FROM  TB_CallbackRecordProjectAttachment  tsp
        INNER JOIN TB_CallbackRecordProject   tp  on  tsp.CallbackRecordProjectID=tp.ID
        WHERE tp.CallbackRecordID=
        #{CallbackRecordID}
    </delete>

    <select id="getCallbackRecordProjectAttachment"
            resultType="com.zhelian.model.CRM.entity.CallbackRecordProjectAttachmentEntity">
        SELECT tsp.*
        FROM TB_CallbackRecordProjectAttachment tsp
                 INNER JOIN TB_CallbackRecordProject tp on tsp.CallbackRecordProjectID = tp.ID
        WHERE tp.CallbackRecordID = #{CallbackRecordID}
    </select>


    <select id="getCallbackRecordByDateAndCallback" resultType="com.zhelian.model.CRM.entity.CallbackRecordEntity">
        SELECT *
        FROM TB_CallbackRecord
        WHERE CustomerID = #{CustomerID}
         AND DATEDIFF(DAY,PlannedOn,#{PlannedOn})=0
          AND CallbackBy = #{EmployeeID}
    </select>

    <delete id="deleteCallbackRecord">
        DELETE
        FROM TB_CallbackRecord
        WHERE ID = #{CallbackRecordID}
    </delete>

    <delete id="deleteCallbackRecordProject">
        DELETE
        FROM TB_CallbackRecordProject
        WHERE CallbackRecordID = #{CallbackRecordID}
    </delete>

    <select id="getCallbackRecordProjectByRecordIDAndBillIDAndProjectIDAndCalbackCycle" resultType="java.lang.Integer">
        SELECT Count(*)
        FROM TB_CallbackRecordProject
        WHERE CallbackRecordID = #{RecordID}
          AND TreatBillID = #{BillID}
          AND ProjectID = #{ProjectID}
          AND CallbackCycle = #{CallbackCycle}
    </select>


    <select id="getCallbackRecordList" resultType="com.zhelian.model.CRM.entity.CallbackRecordEntity">
        SELECT
        a1.*
        FROM TB_CallbackRecord a1 WITH(NOLOCK)
        <where>
            <if test="array != null and array != ''">
                AND a1.ID IN
                <foreach collection="array" item="code" open="(" separator="," close=")" index="index">
                    #{code}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getTreatBillEmployee" resultType="java.lang.String">
        SELECT ttbhp.EmployeeID
        FROM TB_TreatBillHandlerProject ttbhp WITH(NOLOCK)
        INNER JOIN TB_TreatBillProject ttbp
        WITH (NOLOCK)
        ON ttbp.ID =ttbhp.TreatBillProjectID
            INNER JOIN TB_TreatBill t
        WITH (NOLOCK)
        ON t.ID =ttbp.TreatBillID
        WHERE ttbhp.ProjectTreatHandlerID =#{ProjectTreatHandlerID}
          AND EXISTS (
            SELECT 1 FROM TB_CallbackRecordProject tba WHERE tba.CallbackRecordID=#{ID}
          AND tba.TreatBillID=t.ID
            )
        UNION ALL
        SELECT ttbhp.EmployeeID
        FROM TB_TreatBillHandlerGeneralCard ttbhp WITH(NOLOCK)
        INNER JOIN TB_TreatBillGeneralCard ttbp
        WITH (NOLOCK)
        ON ttbp.ID =ttbhp.TreatBillGeneralCardID
            INNER JOIN TB_TreatBill t
        WITH (NOLOCK)
        ON t.ID =ttbp.TreatBillID
        WHERE ttbhp.GeneralCardTreatHandlerID =#{ProjectTreatHandlerID}
          AND EXISTS (
            SELECT 1 FROM TB_CallbackRecordProject tba WHERE tba.CallbackRecordID=#{ID}
          AND tba.TreatBillID=t.ID
            )
        UNION ALL
        SELECT ttbhp.EmployeeID
        FROM TB_TreatBillHandlerTimeCard ttbhp WITH(NOLOCK)
        INNER JOIN TB_TreatBillTimeCard ttbp
        WITH (NOLOCK)
        ON ttbp.ID =ttbhp.TreatBillTimeCardID
            INNER JOIN TB_TreatBill t
        WITH (NOLOCK)
        ON t.ID =ttbp.TreatBillID
        WHERE ttbhp.TimeCardTreatHandlerID =#{ProjectTreatHandlerID}
          AND EXISTS (
            SELECT 1 FROM TB_CallbackRecordProject tba WHERE tba.CallbackRecordID=#{ID}
          AND tba.TreatBillID=t.ID
            )
        UNION ALL
        SELECT ttbhp.EmployeeID
        FROM TB_TreatBillHandlerSavingCard ttbhp WITH(NOLOCK)
        INNER JOIN TB_TreatBillSavingCard ttbp
        WITH (NOLOCK)
        ON ttbp.ID =ttbhp.TreatBillSavingCardID
            INNER JOIN TB_TreatBill t
        WITH (NOLOCK)
        ON t.ID =ttbp.TreatBillID
        WHERE ttbhp.SavingCardTreatHandlerID =#{SavingCardProjectTreatHandlerID}
          AND EXISTS (
            SELECT 1 FROM TB_CallbackRecordProject tba WHERE tba.CallbackRecordID=#{ID}
          AND tba.TreatBillID=t.ID
            )
    </select>


    <resultMap id="YXYDetail" type="com.zhelian.model.yingxiaoyun.form.output.YXYCallBackOutListProjectPutForm">
        <result column="ProjectName" property="ProjectName" jdbcType="VARCHAR"/>
        <result column="CallbackCycle" property="CallbackCycle" jdbcType="INTEGER"/>
        <result column="CallbackRemark" property="CallbackRemark" jdbcType="VARCHAR"/>
        <result column="CallbackContent" property="CallbackContent" jdbcType="VARCHAR"/>
        <collection property="Attachment" ofType="com.zhelian.model.yingxiaoyun.form.output.YXYAttachmentOutPutForm">
            <result column="AttachmentURL" property="AttachmentURL" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>


    <select id="yxyCallbackRecordDetail" resultMap="YXYDetail">
        SELECT tp.Name ProjectName,
               tcb.CallbackCycle,
               tcb.CallbackRemark,
               tcb.CallbackContent,
               tcp.AttachmentURL
        FROM TB_CallbackRecord tf WITH(NOLOCK)
        LEFT JOIN TB_CallbackRecordProject tcb
        WITH (NOLOCK)
        ON tf.ID=tcb.CallbackRecordID
            LEFT JOIN TB_Project tp
        WITH (NOLOCK)
        ON tp.ID=tcb.ProjectID
            LEFT JOIN TB_CallbackRecordProjectAttachment tcp
        WITH (NOLOCK)
        ON tcp.CallbackRecordProjectID=tcb.ID
        WHERE tf.ID=#{CallbackRecordID}
        ORDER BY tcb.ID
    </select>

    <select id="excelDisPlayPhone" resultType="com.zhelian.model.CRM.form.output.CallbackExcelOutputForm">
        SELECT
        tf.ID,
        case when tf.IsCallback = 1 then '已回访' else '待回访' end as isCallback,
        CONVERT(VARCHAR(19),tf.PlannedOn,120) as plannedOn,
        CONVERT(VARCHAR(19),tf.CallbackOn,120) as callbackOn,
        te.Name as callbackEmployeeName,
        tfc.Name as methodName,
        tfs.Name as status,
        tc.Name as customerName,
        case when tc.Gender=0 then '未知' when tc.Gender=1 then '男'  when tc.Gender=2 then '女'  end as gender,
        tc.PhoneNumber as phoneNumber,
        tc.Code as code,
        servicerEmployee = stuff(
        (
        SELECT
        ','+ts.Name +':'+ e.Name
        FROM TB_CustomerServicer r WITH(NOLOCK)
        LEFT JOIN TB_Servicer ts WITH (NOLOCK) ON ts.ID = r.ServicerID
        LEFT JOIN TK_Employee e WITH (NOLOCK) ON e.ID = r.EmployeeID
        where r.CustomerID=tf.CustomerID
        FOR xml path('')
        ) , 1 , 1 , ''),
        tcs.Name as customerSourceName,
        tc.Birthday as birthday,
        CONVERT(VARCHAR(19),tc.CreatedOn,120) as customerCreatedOn,
        tccl.Name channelName,
        tcl.Name customerLevelName
        FROM TB_CallbackRecord tf WITH(NOLOCK)
        LEFT JOIN TB_CallbackMethod tfc WITH(NOLOCK) ON tf.CallbackMethodID=tfc.ID
        LEFT JOIN TB_CallbackStatus tfs WITH(NOLOCK) ON tf.CallbackStatusID=tfs.ID
        LEFT JOIN TK_Employee te WITH(NOLOCK) ON tf.CallbackBy=te.ID
        LEFT JOIN TB_Customer tc WITH(NOLOCK) ON tf.CustomerID=tc.ID
        LEFT JOIN TB_Channel tccl WITH(NOLOCK) ON tccl.ID=tc.ChannelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tce WITH(NOLOCK) ON tce.CustomerID=tf.CustomerID AND tce.IsBelongEntity=1
        LEFT JOIN TK_Entity tke WITH(NOLOCK) ON tke.ID=tce.EntityID
        <where>
            <choose>
                <when test="IsShowOwnCallback != null">
                    and tf.CallbackBy=#{EmployeeID}
                </when>
                <otherwise>
                    AND (EXISTS (
                    SELECT 1 FROM TK_EmployeeEntity tey WITH(NOLOCK)
                    WHERE IsPrimaryEntity=1 AND tey.EmployeeID=tf.CallbackBy
                    AND EXISTS (
                    SELECT
                    1
                    FROM
                    TB_EmployeeEntityPermission tepn WITH(NOLOCK)
                    left join TK_Entity tty WITH(NOLOCK) on tty.ID=tepn.EntityID
                    where tty.Active=1 and tepn.EmployeeID = #{EmployeeID} AND tey.EntityID=tepn.EntityID
                    )
                    ) OR tf.CallbackBy=#{EmployeeID})
                </otherwise>
            </choose>
            <if test="Name != null and Name != ''">
                and ( tc.Name like '%'+ #{Name} +'%' or tc.PhoneNumber like '%'+ #{Name} +'%' or tc.Code like '%'+
                #{Name} +'%')
            </if>
            <if test="CallbackEmployeeID != null and CallbackEmployeeID != ''">
                and tf.CallbackBy=#{CallbackEmployeeID}
            </if>
            <if test="CallbackMethodID != null and CallbackMethodID != ''">
                and tf.CallbackMethodID=#{CallbackMethodID}
            </if>
            <if test="CallbackStatusID != null and CallbackStatusID != ''">
                and tf.CallbackStatusID=#{CallbackStatusID}
            </if>
            <if test="IsCallback != null ">
                and tf.IsCallback=#{IsCallback}
            </if>
            <if test="StartDate != null and StartDate != '' and EndDate != null and EndDate != ''">
                AND tf.PlannedOn &gt;= #{StartDate} AND tf.PlannedOn &lt; DATEADD(DAY,1,#{EndDate})
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND tccl.Name like '%'+ #{ChannelName} +'%'
            </if>
            <if test="CustomerLevelID != null ">
                <choose>
                    <when test="CustomerLevelID !=0 ">
                        and tc.CustomerLevelID =#{CustomerLevelID}
                    </when>
                    <otherwise>
                        and tc.CustomerLevelID IS NULL
                    </otherwise>
                </choose>
            </if>
        </where>

        ORDER BY IsCallback, PlannedOn
    </select>

    <select id="excelNoDisPlayPhone" resultType="com.zhelian.model.CRM.form.output.CallbackExcelOutputForm">
        SELECT
        tf.ID,
        case when tf.IsCallback = 1 then '已回访' else '待回访' end as isCallback,
        CONVERT(VARCHAR(19),tf.PlannedOn,120) as plannedOn,
        CONVERT(VARCHAR(19),tf.CallbackOn,120) as callbackOn,
        te.Name as callbackEmployeeName,
        tfc.Name as methodName,
        tfs.Name as status,
        tc.Name as customerName,
        case when tc.Gender=0 then '未知' when tc.Gender=1 then '男'  when tc.Gender=2 then '女'  end as gender,
        ISNULL(REPLACE(tc.PhoneNumber, SUBSTRING (tc.PhoneNumber,4,4), '****'),'') phoneNumber,
        tc.Code as code,
        servicerEmployee = stuff(
        (
        SELECT
        ','+ts.Name +':'+ e.Name
        FROM TB_CustomerServicer r WITH(NOLOCK)
        LEFT JOIN TB_Servicer ts WITH (NOLOCK) ON ts.ID = r.ServicerID
        LEFT JOIN TK_Employee e WITH (NOLOCK) ON e.ID = r.EmployeeID
        where r.CustomerID=tf.CustomerID
        FOR xml path('')
        ) , 1 , 1 , ''),
        tcs.Name as customerSourceName,
        tc.Birthday as birthday,
        CONVERT(VARCHAR(19),tc.CreatedOn,120) as customerCreatedOn,
        tccl.Name channelName,
        tcl.Name customerLevelName
        FROM TB_CallbackRecord tf WITH(NOLOCK)
        LEFT JOIN TB_CallbackMethod tfc WITH(NOLOCK) ON tf.CallbackMethodID=tfc.ID
        LEFT JOIN TB_CallbackStatus tfs WITH(NOLOCK) ON tf.CallbackStatusID=tfs.ID
        LEFT JOIN TK_Employee te WITH(NOLOCK) ON tf.CallbackBy=te.ID
        LEFT JOIN TB_Customer tc WITH(NOLOCK) ON tf.CustomerID=tc.ID
        LEFT JOIN TB_Channel tccl WITH(NOLOCK) ON tccl.ID=tc.ChannelID
        LEFT JOIN TB_CustomerSource tcs WITH(NOLOCK) ON tcs.ID=tc.CustomerSourceID
        LEFT JOIN TB_CustomerLevel tcl WITH(NOLOCK) ON tcl.ID=tc.CustomerLevelID
        LEFT JOIN TB_CustomerEntity tce WITH(NOLOCK) ON tce.CustomerID=tf.CustomerID AND tce.IsBelongEntity=1
        LEFT JOIN TK_Entity tke WITH(NOLOCK) ON tke.ID=tce.EntityID
        <where>
            <choose>
                <when test="IsShowOwnCallback != null">
                    and tf.CallbackBy=#{EmployeeID}
                </when>
                <otherwise>
                    AND (EXISTS (
                    SELECT 1 FROM TK_EmployeeEntity tey WITH(NOLOCK)
                    WHERE IsPrimaryEntity=1 AND tey.EmployeeID=tf.CallbackBy
                    AND EXISTS (
                    SELECT
                    1
                    FROM
                    TB_EmployeeEntityPermission tepn WITH(NOLOCK)
                    left join TK_Entity tty WITH(NOLOCK) on tty.ID=tepn.EntityID
                    where tty.Active=1 and tepn.EmployeeID = #{EmployeeID} AND tey.EntityID=tepn.EntityID
                    )
                    ) OR tf.CallbackBy=#{EmployeeID})
                </otherwise>
            </choose>
            <if test="Name != null and Name != ''">
                and ( tc.Name like '%'+ #{Name} +'%' or tc.PhoneNumber like '%'+ #{Name} +'%' or tc.Code like '%'+
                #{Name} +'%')
            </if>
            <if test="CallbackEmployeeID != null and CallbackEmployeeID != ''">
                and tf.CallbackBy=#{CallbackEmployeeID}
            </if>
            <if test="CallbackMethodID != null and CallbackMethodID != ''">
                and tf.CallbackMethodID=#{CallbackMethodID}
            </if>
            <if test="CallbackStatusID != null and CallbackStatusID != ''">
                and tf.CallbackStatusID=#{CallbackStatusID}
            </if>
            <if test="IsCallback != null ">
                and tf.IsCallback=#{IsCallback}
            </if>
            <if test="StartDate != null and StartDate != '' and EndDate != null and EndDate != ''">
                AND tf.PlannedOn &gt;= #{StartDate} AND tf.PlannedOn &lt; DATEADD(DAY,1,#{EndDate})
            </if>
            <if test="ChannelName != null and ChannelName != ''">
                AND tccl.Name like '%'+ #{ChannelName} +'%'
            </if>
            <if test="CustomerLevelID != null ">
                <choose>
                    <when test="CustomerLevelID !=0 ">
                        and tc.CustomerLevelID =#{CustomerLevelID}
                    </when>
                    <otherwise>
                        and tc.CustomerLevelID IS NULL
                    </otherwise>
                </choose>
            </if>
        </where>

        ORDER BY IsCallback, PlannedOn
    </select>
</mapper>