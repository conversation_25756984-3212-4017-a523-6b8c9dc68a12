<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.CRM.ChannelLevelMapper">

    <select id="getPageList" resultType="com.zhelian.model.CRM.form.output.ChannelTypeOutputForm">
        SELECT
            e.ID,
            e.Name,
            e.Sequence,
            e.Active
        FROM
            TB_ChannelLevel e  WITH(NOLOCK)
        <where>
            <if test="Name != null and Name != ''">
                AND e.Name like '%'+ #{Name} +'%'
            </if>
            <if test="Active != null">
                AND e.Active = #{Active}
            </if>
        </where>
    ORDER BY e.Sequence DESC
    </select>

    <select id="all" resultType="com.zhelian.model.CRM.form.output.ChannelTypeOutputForm">
        SELECT
        e.ID,
        e.Name,
        e.Sequence,
        e.Active
        FROM
        TB_ChannelLevel e  WITH(NOLOCK)
        WHERE  e.Active = 1
    </select>


    <insert id="create" parameterType="com.zhelian.model.CRM.entity.ChannelLevelEntity" >
        INSERT INTO dbo.TB_ChannelLevel (  Name,Active,Sequence )
        VALUES
            (#{Name},1,#{Sequence} )
    </insert>

    <update id="updateChannelLevel" parameterType="com.zhelian.model.CRM.entity.ChannelLevelEntity">
        UPDATE dbo.TB_ChannelLevel
        SET Name = #{Name},Active=#{Active}
        WHERE ID = #{ID}
    </update>

    <select id="getMaxSequence" resultType="Integer">
        SELECT top 1 Sequence
        FROM
            TB_ChannelLevel WITH ( NOLOCK )
        ORDER BY Sequence desc
    </select>

    <select id="getIDOrderBySequenceASC" resultType="Integer">
        SELECT
            c.ID
        FROM TB_ChannelLevel c WITH(NOLOCK)
        WHERE c.ID!=#{ID}
        ORDER BY c.Sequence ASC
    </select>

    <update id="updateSequence" parameterType="java.util.List">
        update TB_ChannelLevel
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="Sequence =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.Sequence!=null">
                        WHEN ID = #{item.ID} then #{item.Sequence}
                    </if>
                </foreach>
            </trim>
        </trim>
        where
        <foreach collection="list" separator="or" item="item" index="index">
            ID = #{item.ID}
        </foreach>
    </update>

    <select id="getChannelLevel" resultType="com.zhelian.model.CRM.entity.ChannelLevelEntity">
        SELECT * FROM TB_ChannelLevel WITH(NOLOCK) WHERE ID=#{ID}
    </select>

    <select id="channelByChannelLevelCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM TB_Channel p WITH(NOLOCK)
        WHERE p.Active=1 AND p.ChannelLevelID=#{ID}
    </select>
</mapper>