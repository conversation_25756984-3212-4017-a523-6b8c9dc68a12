<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.yingxiaoyun.YingXiaoYunAppointmentBillMapper">


    <insert id="insertYingXiaoYunCustomer"
            parameterType="com.zhelian.model.yingxiaoyun.entity.YingXiaoYunCustomerEntity">
        INSERT INTO TB_YingXiaoYunCustomer
        (PhoneNumber)
        VALUES (#{PhoneNumber})
    </insert>

    <insert id="insertYingXiaoYunAppointmentBill"
            parameterType="com.zhelian.model.yingxiaoyun.entity.YingXiaoYunAppointmentBillEntity">
        INSERT INTO TB_YingXiaoYunAppointmentBill
        (ID,
         YinXiaoYunID)
        VALUES (#{ID},
                #{YinXiaoYunID})
    </insert>

    <select id="getYingXiaoYunCustomer" resultType="com.zhelian.model.yingxiaoyun.entity.YingXiaoYunCustomerEntity">
        SELECT *
        FROM TB_YingXiaoYunCustomer
        WHERE PhoneNumber = #{PhoneNumber}
    </select>

    <select id="getYingXiaoYunAppointmentBill"
            resultType="com.zhelian.model.yingxiaoyun.entity.YingXiaoYunAppointmentBillEntity">
        SELECT *
        FROM TB_YingXiaoYunAppointmentBill
        WHERE YinXiaoYunID = #{Code}
    </select>

    <select id="getYingXiaoYunCode" resultType="com.zhelian.model.yingxiaoyun.entity.YingXiaoYunAppointmentBillEntity">
        SELECT *
        FROM TB_YingXiaoYunAppointmentBill
        WHERE ID = #{AppointmentillID}
    </select>


    <insert id="insertTagEntity" parameterType="java.util.List">
        INSERT INTO dbo.TB_YingXiaoYunCustomerTag (
        PhoneNumber,
        Label,
        CreatedOn
        )
        VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.PhoneNumber},
            #{item.Label},
            #{item.CreatedOn}
            )
        </foreach>
    </insert>

    <update id="deleteYingXiaoYunTag">
        DELETE FROM TB_YingXiaoYunCustomerTag
        WHERE PhoneNumber=#{Phone} and Label IN
        <foreach collection="Array" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>
</mapper>