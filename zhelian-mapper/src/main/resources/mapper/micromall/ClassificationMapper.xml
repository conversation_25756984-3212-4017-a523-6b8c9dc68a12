<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.micromall.ClassificationMapper">


    <insert id="addClassification" parameterType="com.zhelian.model.micromall.entity.GoodsCategoryEntity"
            useGeneratedKeys="true"
            keyProperty="ID" keyColumn="ID">
        INSERT INTO TM_GoodsCategory
            (Name, ParentID,  Sequence, Active)
        VALUES (#{Name}, #{ParentID}, #{Sequence}, #{Active})
    </insert>


    <select id="getMaxSequence" resultType="Integer">
        SELECT top 1 Sequence
        FROM TM_GoodsCategory WITH(NOLOCK)
        ORDER BY Sequence desc
    </select>


    <select id="getIDOrderBySequenceASC" resultType="Integer">
        SELECT p.ID
        FROM TM_GoodsCategory p WITH(NOLOCK)
        WHERE
            p.ParentID=#{ ParentID }
          AND p.ID!=#{ ID }
        ORDER BY
            p.Sequence ASC
    </select>


    <update id="updateSequence" parameterType="java.util.List">
        update TM_GoodsCategory
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="Sequence =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.Sequence!=null">
                        WHEN ID = #{item.ID} then #{item.Sequence}
                    </if>
                </foreach>
            </trim>
        </trim>
        where
        <foreach collection="list" separator="or" item="item" index="index">
            ID = #{item.ID}
        </foreach>
    </update>


    <select id="getTopProjectList" resultType="com.zhelian.model.micromall.entity.GoodsCategoryEntity">
        SELECT p.ID,
               p.Name,
               p.Active,
               p.ParentID,
               p.Sequence
        FROM TM_GoodsCategory p WITH(NOLOCK)
        WHERE p.ParentID =0
        ORDER BY p.Sequence desc
    </select>


    <select id="inquireClassification" resultType="com.zhelian.model.micromall.entity.GoodsCategoryEntity">
        SELECT
        ID,
        Name,
        Active,
        ParentID,
        Sequence
        FROM
        TM_GoodsCategory TMG WITH ( NOLOCK )
        WHERE
        1 = 1
        <if test="Name != null and Name != ''">
            and Name like concat('%',#{Name},'%')
        </if>

        <if test="Active != null">
            and Active =#{Active}
        </if>
        ORDER BY
        Sequence desc
    </select>


    <update id="updateClassification" parameterType="com.zhelian.model.micromall.entity.GoodsCategoryEntity">
        update TM_GoodsCategory
        set Name=#{Name},
            ParentID=#{ParentID},
            Active=#{Active}
        where ID = #{ID}
    </update>

    <select id="inquireClassificationValid" resultType="com.zhelian.model.micromall.entity.GoodsCategoryEntity">
        SELECT ID,
               Name,
               Active,
               ParentID,
               Sequence
        FROM TM_GoodsCategory WITH(NOLOCK)
        WHERE Active = 1
          AND (ParentID != 0
           OR ID IN (
            SELECT ParentID FROM TM_GoodsCategory WITH (NOLOCK)
            WHERE Active = 1
          AND ParentID !=0))
        ORDER BY Sequence DESC
    </select>

    <select id="getClassification" resultType="com.zhelian.model.micromall.entity.GoodsCategoryEntity">
        SELECT p.ID,
               p.Name,
               p.Active,
               p.ParentID,
               p.Sequence
        FROM TM_GoodsCategory p WITH(NOLOCK)
        WHERE p.ID=#{ID}
        ORDER BY p.Sequence ASC
    </select>

    <select id="childActiveCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM TM_GoodsCategory tp WITH(NOLOCK)
        WHERE tp.ParentID=#{ID} AND tp.Active=1
    </select>

    <select id="projectByCategoryCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM TM_Goods p WITH(NOLOCK)
        WHERE p.ID=#{ID}
    </select>

    <resultMap id="goodsDetail"
               type="com.zhelian.model.micromall.form.output.GoodsCategoryOutputForm">
        <id column="PID" property="ID" jdbcType="INTEGER"/>
        <result column="PParentID" property="ParentID" jdbcType="INTEGER"/>
        <result column="PName" property="Name" jdbcType="VARCHAR"/>
        <collection property="Child" ofType="com.zhelian.model.micromall.form.output.GoodsCategoryChildOutputForm">
            <id column="ID" property="ID" jdbcType="INTEGER"/>
            <result column="ParentID" property="ParentID" jdbcType="INTEGER"/>
            <result column="Name" property="Name" jdbcType="VARCHAR"/>
            <collection property="Goods" ofType="com.zhelian.model.micromall.form.output.GoodsAllOutputForm">
                <result column="GoodsID" property="GoodsID" jdbcType="INTEGER"/>
                <result column="GoodsType" property="GoodsType" jdbcType="VARCHAR"/>
                <result column="GoodsTypeName" property="GoodsTypeName" jdbcType="VARCHAR"/>
                <result column="GoodsName" property="GoodsName" jdbcType="VARCHAR"/>
                <result column="Price" property="Price" jdbcType="DECIMAL"/>
                <result column="OriginalText" property="OriginalText" jdbcType="VARCHAR"/>
                <result column="ImageURL" property="ImageURL" jdbcType="VARCHAR"/>
            </collection>
        </collection>
    </resultMap>

    <select id="saleGoods" resultMap="goodsDetail">
        SELECT
            pcc.ID PID,
            pcc.Name PName,
            pcc.ParentID PParentID,
            pc.ID,
            pc.Name,
            pcc.ParentID,
            P.GoodsType,
            CASE P.GoodsType
                WHEN '10' THEN '产品'
                WHEN '20' THEN '项目'
                WHEN '30' THEN '通用次卡'
                WHEN '40' THEN '时效卡'
                WHEN '50' THEN '储值卡'
                WHEN '60' THEN '套餐卡' END GoodsTypeName,
            P.GoodsID,
            T.Name                       GoodsName,
            T.Price,
            T.OriginalText,
            T.ImageURL
            FROM TM_Goods P WITH(NOLOCK)
            INNER JOIN  TM_GoodsCategory pc WITH (NOLOCK) ON P.CategoryID=pc.ID
            INNER JOIN  TM_GoodsCategory pcc WITH (NOLOCK) ON pc.ParentID=pcc.ID

            LEFT JOIN
            (
            SELECT '10' Type,t.ID,t.Name,tp.Price,tp.OriginalText,tpe.ImageURL FROM TE_Product t WITH (NOLOCK)
            LEFT JOIN TB_Product tp WITH (NOLOCK) ON t.ID=tp.ProductID
            LEFT JOIN TB_ProductImage tpe WITH (NOLOCK) ON t.ID=tpe.ProductID AND tpe.Sequence=1
            UNION ALL
            SELECT '20' Type,t.ID,t.Name,t.Price,t.OriginalText,tpe.ImageURL FROM TB_Project t WITH (NOLOCK)
            LEFT JOIN TB_ProjectImage tpe WITH (NOLOCK) ON t.ID=tpe.ProjectID AND tpe.Sequence=1
            UNION ALL
            SELECT '30' Type,t.ID,t.Name,t.Price,'' OriginalText,tpe.ImageURL FROM TB_GeneralCard t WITH (NOLOCK)
            LEFT JOIN TB_GeneralCardImage tpe WITH (NOLOCK) ON t.ID=tpe.GeneralCardID AND tpe.Sequence=1
            UNION ALL
            SELECT '40' Type,t.ID,t.Name,t.Price,'' OriginalText,tpe.ImageURL FROM TB_TimeCard t WITH (NOLOCK)
            LEFT JOIN TB_TimeCardImage tpe WITH (NOLOCK) ON t.ID=tpe.TimeCardID AND tpe.Sequence=1
            UNION ALL
            SELECT '50' Type,t.ID,t.Name,t.Price,'' OriginalText,tpe.ImageURL FROM TB_SavingCard t WITH (NOLOCK)
            LEFT JOIN TB_SavingCardImage tpe WITH (NOLOCK) ON t.ID=tpe.SavingCardID AND tpe.Sequence=1
            UNION ALL
            SELECT '60' Type,t.ID,t.Name,t.Price,'' OriginalText,tpe.ImageURL FROM TB_PackageCard t WITH (NOLOCK)
            LEFT JOIN TB_PackageCardImage tpe WITH (NOLOCK) ON t.ID=tpe.PackageCardID AND tpe.Sequence=1
            ) T ON T.Type=P.GoodsType AND T.ID=P.GoodsID
        WHERE
            pc.Active=1 AND pc.Active=1
        order by pcc.Sequence,pc.Sequence,P.Sequence
    </select>

    <select id="getTopGoodsList" parameterType="java.util.List"
            resultType="com.zhelian.model.micromall.form.output.GoodsCategoryOutputForm">
        SELECT
        p.ID,
        p.Name,
        p.ParentID
        FROM TM_GoodsCategory p WITH(NOLOCK)
        WHERE p.ParentID =0 AND p.Active=1 AND p.ID in (
        <foreach collection="list" item="item" index="index"
                 separator=",">
            #{item}
        </foreach>
        )
        ORDER BY p.Sequence
    </select>

    <insert id="createGoods" parameterType="java.util.List">
        INSERT INTO TM_Goods
        (GoodsType,GoodsID,CategoryID,Sequence)
        VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.GoodsType},#{item.GoodsID},#{item.CategoryID},#{item.Sequence})
        </foreach>
    </insert>

    <delete id="deleteGoodsCategory">
        DELETE
        FROM TM_GoodsCategory
    </delete>

    <delete id="deleteGoods">
        DELETE
        FROM TM_Goods
    </delete>

    <select id="getProduct" resultType="com.zhelian.model.micromall.form.output.OfflineGoodsProductOutputForm">
        SELECT
        '10' GoodsType,
        '产品' GoodsTypeName,
        t.ID,
        t.Name,
        tp.Price,
        tp.OriginalText,
        tpe.ImageURL
        FROM TE_Product t WITH(NOLOCK)
        LEFT JOIN TB_Product tp WITH(NOLOCK) ON t.ID=tp.ProductID
        LEFT JOIN TB_ProductImage tpe WITH(NOLOCK) ON t.ID=tpe.ProductID AND tpe.Sequence=1
        <where>
            t.Active=1
            <if test="Name != null and Name != ''">
                AND ( t.Name like '%'+ #{Name} +'%' or t.Alias like '%'+ #{Name} +'%'
                OR t.NameFullPinYin LIKE '%'+ #{Name} +'%' OR t.NameAbbrPinYin LIKE '%'+ #{Name} +'%'
                OR t.AliasFullPinYin LIKE '%'+ #{Name} +'%' OR t.AliasAbbrPinYin LIKE '%'+ #{Name} +'%')
            </if>
            <if test="IsAllowSell != null ">
                AND tp.IsAllowSell=#{IsAllowSell}
            </if>
        </where>
    </select>

    <select id="getProject" resultType="com.zhelian.model.micromall.form.output.OfflineGoodsProjectOutputForm">
        SELECT
        '20' GoodsType,
        '项目' GoodsTypeName,
        t.ID,
        t.Name,
        t.Price,
        t.OriginalText,
        tpe.ImageURL FROM TB_Project t WITH(NOLOCK)
        LEFT JOIN TB_ProjectImage tpe WITH(NOLOCK) ON t.ID=tpe.ProjectID AND tpe.Sequence=1
        <where>
            <if test="Name != null and Name != ''">
                ( t.Name like '%'+ #{Name} +'%' or t.Alias like '%'+ #{Name} +'%'
                OR t.NameFullPinYin LIKE '%'+ #{Name} +'%' OR t.NameAbbrPinYin LIKE '%'+ #{Name} +'%'
                OR t.AliasFullPinYin LIKE '%'+ #{Name} +'%' OR t.AliasAbbrPinYin LIKE '%'+ #{Name} +'%')
            </if>
            <if test="IsAllowSell != null ">
                AND t.IsAllowSell=#{IsAllowSell}
            </if>
        </where>
    </select>

    <select id="getGeneralCard" resultType="com.zhelian.model.micromall.form.output.OfflineGoodsGeneralCardOutputForm">
        SELECT
        '30' GoodsType,
        '通用次卡' GoodsTypeName,
        t.ID,
        t.Name,
        t.Amount,
        t.Price,
        case t.ValidType WHEN 1 then concat('销售后',t.ValidDay,'天有效')
        WHEN 2 THEN concat('消耗后',t.ValidDay,'天有效')
        ELSE '永久有效' END ValidDayName
        FROM TB_GeneralCard t
        LEFT JOIN TB_GeneralCardImage tpe WITH(NOLOCK) ON t.ID=tpe.GeneralCardID AND tpe.Sequence=1
        <where>
            <if test="Name != null and Name != ''">
                ( t.Name like '%'+ #{Name} +'%' or t.Alias like '%'+ #{Name} +'%'
                OR t.NameFullPinYin LIKE '%'+ #{Name} +'%' OR t.NameAbbrPinYin LIKE '%'+ #{Name} +'%'
                OR t.AliasFullPinYin LIKE '%'+ #{Name} +'%' OR t.AliasAbbrPinYin LIKE '%'+ #{Name} +'%')
            </if>
            <if test="IsAllowSell != null ">
                AND t.IsAllowSell=#{IsAllowSell}
            </if>
        </where>
    </select>

    <select id="getTimeCard" resultType="com.zhelian.model.micromall.form.output.OfflineGoodsTimeCardOutputForm">
        SELECT
        '40' GoodsType,
        '时效卡' GoodsTypeName,
        t.ID,
        t.Name,
        t.Price,
        t.ConsumeCycle,
        t.CycleLimitAmount,
        t.PerformanceAmount,
        case t.ValidType WHEN 1 then concat('销售后',t.ValidDay,'天有效')
        WHEN 2 THEN concat('消耗后',t.ValidDay,'天有效')
        ELSE '永久有效' END ValidDayName
        FROM TB_TimeCard t WITH(NOLOCK)
        LEFT JOIN TB_TimeCardImage tpe WITH(NOLOCK) ON t.ID=tpe.TimeCardID AND tpe.Sequence=1
        <where>
            <if test="Name != null and Name != ''">
                ( t.Name like '%'+ #{Name} +'%' or t.Alias like '%'+ #{Name} +'%'
                OR t.NameFullPinYin LIKE '%'+ #{Name} +'%' OR t.NameAbbrPinYin LIKE '%'+ #{Name} +'%'
                OR t.AliasFullPinYin LIKE '%'+ #{Name} +'%' OR t.AliasAbbrPinYin LIKE '%'+ #{Name} +'%')
            </if>
            <if test="IsAllowSell != null ">
                AND t.IsAllowSell=#{IsAllowSell}
            </if>
        </where>
    </select>

    <select id="getSavingCard" resultType="com.zhelian.model.micromall.form.output.OfflineGoodsSavingCardOutputForm">
        SELECT
        '50' GoodsType,
        '储值卡' GoodsTypeName,
        t.ID,
        t.Name,
        t.Price,
        t.LargessPrice,
        case t.ValidType WHEN 1 then concat('销售后',t.ValidDay,'天有效')
        WHEN 2 THEN concat('消耗后',t.ValidDay,'天有效')
        ELSE '永久有效' END ValidDayName
        FROM TB_SavingCard t WITH(NOLOCK)
        LEFT JOIN TB_SavingCardImage tpe WITH(NOLOCK) ON t.ID=tpe.SavingCardID AND tpe.Sequence=1
        <where>
            <if test="Name != null and Name != ''">
                ( t.Name like '%'+ #{Name} +'%' or t.Alias like '%'+ #{Name} +'%'
                OR t.NameFullPinYin LIKE '%'+ #{Name} +'%' OR t.NameAbbrPinYin LIKE '%'+ #{Name} +'%'
                OR t.AliasFullPinYin LIKE '%'+ #{Name} +'%' OR t.AliasAbbrPinYin LIKE '%'+ #{Name} +'%')
            </if>
            <if test="IsAllowSell != null ">
                AND t.IsAllowSell=#{IsAllowSell}
            </if>
        </where>
    </select>

    <select id="getPackageCard" resultType="com.zhelian.model.micromall.form.output.OfflineGoodsPackageCardOutputForm">
        SELECT
        '60' GoodsType,
        '套餐卡' GoodsTypeName,
        t.ID,
        t.Name,
        t.Price,
        case t.ValidType WHEN 1 then concat('销售后',t.ValidDay,'天有效')
        WHEN 2 THEN concat('消耗后',t.ValidDay,'天有效')
        ELSE '永久有效' END ValidDayName
        FROM TB_PackageCard t WITH(NOLOCK)
        LEFT JOIN TB_PackageCardImage tpe WITH(NOLOCK) ON t.ID=tpe.PackageCardID AND tpe.Sequence=1
        <where>
            <if test="Name != null and Name != ''">
                ( t.Name like '%'+ #{Name} +'%' or t.Alias like '%'+ #{Name} +'%'
                OR t.NameFullPinYin LIKE '%'+ #{Name} +'%' OR t.NameAbbrPinYin LIKE '%'+ #{Name} +'%'
                OR t.AliasFullPinYin LIKE '%'+ #{Name} +'%' OR t.AliasAbbrPinYin LIKE '%'+ #{Name} +'%')
            </if>
            <if test="IsAllowSell != null ">
                AND t.IsAllowSell=#{IsAllowSell}
            </if>
        </where>
    </select>
</mapper>