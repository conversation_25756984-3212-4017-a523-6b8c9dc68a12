<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.micromall.GoodsMapper">

    <select id="getProductByGoodsID" parameterType="java.lang.Integer"
            resultType="com.zhelian.model.micromall.form.output.GoodsDetailOutputForm">
        SELECT et.ID,
               et.Name,
               t.OriginalText,
               t.Price,
               t.Memo,
               et.Specification,
               u.Name UnitName
        FROM TB_Product t
                 LEFT JOIN TE_Product et ON et.ID = t.ProductID
                 LEFT JOIN TE_ProductBarCode e ON e.ProductID = et.ID
                 LEFT JOIN TE_Unit u ON u.ID = e.UnitID
        where et.ID = #{GoodsID}
          AND e.IsMinimumUnit = 1
    </select>

    <select id="getProductImageList" resultType="java.lang.String">
        SELECT e.ImageURL
        FROM TB_ProductImage e
        where e.ProductID = #{GoodsID}
        ORDER BY e.Sequence ASC
    </select>

    <select id="getProjectByGoodsID" parameterType="java.lang.Integer"
            resultType="com.zhelian.model.micromall.form.output.GoodsDetailOutputForm">
        SELECT t.ID,
               t.Name,
               t.OriginalText,
               t.Price,
               t.Memo,
               (SELECT COUNT(ts.EntityID)
                FROM TB_ProjectConsumeEntity ts
        WITH (NOLOCK)
            LEFT JOIN TK_Entity tk
        WITH (NOLOCK)
        ON tk.ID = ts.EntityID
        WHERE
            tk.Active = 1
          AND ts.ProjectID = #{GoodsID}
            ) ApplyNum
        FROM
            TB_Project t
        where t.ID = #{GoodsID}
    </select>

    <select id="getProjectImageList" resultType="java.lang.String">
        SELECT e.ImageURL
        FROM TB_ProjectImage e
        where e.ProjectID = #{GoodsID}
        ORDER BY e.Sequence ASC
    </select>

    <select id="getGeneralCardByGoodsID" parameterType="java.lang.Integer"
            resultType="com.zhelian.model.micromall.form.output.GoodsDetailOutputForm">
        SELECT t.ID,
               t.Name,
               t.Price,
               t.Memo,
               CASE t.ValidType
                   WHEN 1 then concat('购买后', t.ValidDay, '天有效')
                   WHEN 2 THEN concat('消耗后', t.ValidDay, '天有效')
                   ELSE '永久有效' END ValidDay,
               t.Amount,
               (SELECT COUNT(ts.EntityID)
                FROM TB_GeneralCardConsumeEntity ts
        WITH (NOLOCK)
            LEFT JOIN TK_Entity tk
        WITH (NOLOCK)
        ON tk.ID = ts.EntityID
        WHERE
            tk.Active = 1
          AND ts.GeneralCardID = #{GoodsID}
            ) ApplyNum
        FROM
            TB_GeneralCard t
        where t.ID = #{GoodsID}
    </select>

    <select id="getGeneralCardImageList" resultType="java.lang.String">
        SELECT e.ImageURL
        FROM TB_GeneralCardImage e
        where e.GeneralCardID = #{GoodsID}
        ORDER BY e.Sequence ASC
    </select>

    <select id="getTimeCardByGoodsID" parameterType="java.lang.Integer"
            resultType="com.zhelian.model.micromall.form.output.GoodsDetailOutputForm">
        SELECT t.ID,
               t.Name,
               t.Price,
               t.Memo,
               CASE t.ValidType
                   WHEN 1 then concat('购买后', t.ValidDay, '天有效')
                   WHEN 2 THEN concat('消耗后', t.ValidDay, '天有效')
                   ELSE '永久有效' END ValidDay,
               t.ConsumeCycle,
               t.CycleLimitAmount,
               (SELECT COUNT(ts.EntityID)
                FROM TB_TimeCardConsumeEntity ts
        WITH (NOLOCK)
            LEFT JOIN TK_Entity tk
        WITH (NOLOCK)
        ON tk.ID = ts.EntityID
        WHERE
            tk.Active = 1
          AND ts.TimeCardID = #{GoodsID}
            ) ApplyNum
        FROM
            TB_TimeCard t
        where t.ID = #{GoodsID}
    </select>

    <select id="getTimeCardImageList" resultType="java.lang.String">
        SELECT e.ImageURL
        FROM TB_TimeCardImage e
        where e.TimeCardID = #{GoodsID}
        ORDER BY e.Sequence ASC
    </select>

    <select id="getSavingCardByGoodsID" parameterType="java.lang.Integer"
            resultType="com.zhelian.model.micromall.form.output.GoodsDetailOutputForm">
        SELECT t.ID,
               t.Name,
               t.Price,
               t.Memo,
               t.IsSettingLargess,
               t.IsGoodsRange,
               t.IsLargessGoodsRange,
               CASE t.ValidType
                   WHEN 1 then concat('购买后', t.ValidDay, '天有效')
                   WHEN 2 THEN concat('消耗后', t.ValidDay, '天有效')
                   ELSE '永久有效' END ValidDay,
               (SELECT COUNT(ts.EntityID)
                FROM TB_SavingCardConsumeEntity ts
        WITH (NOLOCK)
            LEFT JOIN TK_Entity tk
        WITH (NOLOCK)
        ON tk.ID = ts.EntityID
        WHERE
            tk.Active = 1
          AND ts.SavingCardID = #{GoodsID}
            ) ApplyNum
        FROM
            TB_SavingCard t
        where t.ID = #{GoodsID}
    </select>

    <select id="getSavingCardImageList" resultType="java.lang.String">
        SELECT e.ImageURL
        FROM TB_SavingCardImage e
        where e.SavingCardID = #{GoodsID}
        ORDER BY e.Sequence ASC
    </select>

    <select id="getPackageCardByGoodsID" parameterType="java.lang.Integer"
            resultType="com.zhelian.model.micromall.form.output.GoodsDetailOutputForm">
        SELECT t.ID,
               t.Name,
               t.Price,
               t.Memo,
               CASE t.ValidType
                   WHEN 1 then concat('购买后', t.ValidDay, '天有效')
                   WHEN 2 THEN concat('消耗后', t.ValidDay, '天有效')
                   ELSE '永久有效' END ValidDay
        FROM TB_PackageCard t
        where t.ID = #{GoodsID}
    </select>

    <select id="getPackageCardImageList" resultType="java.lang.String">
        SELECT e.ImageURL
        FROM TB_PackageCardImage e
        where e.PackageCardID = #{GoodsID}
        ORDER BY e.Sequence ASC
    </select>

    <select id="getGeneralCardConsumeNum" resultType="java.lang.Integer">
        SELECT COUNT(*) ConsumeNum
        FROM TB_GeneralCardProject gcp WITH (NOLOCK)
        LEFT JOIN TB_Project p
        WITH (NOLOCK)
        ON gcp.ProjectID = p.ID
            LEFT JOIN TB_ProjectCategory pc
        WITH (NOLOCK)
        ON p.ProjectCategoryID = pc.ID
            LEFT JOIN TB_ProjectCategory fpc
        WITH (NOLOCK)
        ON pc.ParentID = fpc.ID
        WHERE
            1 = 1
          AND gcp.GeneralCardID = #{GoodsID}
    </select>

    <select id="getTimeCardConsumeNum" resultType="java.lang.Integer">
        SELECT COUNT(*) ConsumeNum
        FROM TB_TimeCardProject tcp WITH(NOLOCK)
        LEFT JOIN TB_Project p
        WITH (NOLOCK)
        ON tcp.ProjectID = p.ID
            LEFT JOIN TB_ProjectCategory pc
        WITH (NOLOCK)
        ON p.ProjectCategoryID = pc.ID
            LEFT JOIN TB_ProjectCategory fpc
        WITH (NOLOCK)
        ON pc.ParentID = fpc.ID
        WHERE 1=1
          and tcp.TimeCardID =#{GoodsID}
    </select>

    <select id="packageCardProductNum" resultType="java.lang.Integer">
        SELECT count(*) ConsumeNum
        FROM TB_PackageCardProduct tpp WITH(NOLOCK)
           LEFT JOIN  TE_Product te
        WITH (NOLOCK)
        ON tpp.ProductID=te.ID
            LEFT JOIN TB_Product tp
        WITH (NOLOCK)
        ON tp.ProductID=te.ID
        WHERE tpp.PackageCardID=#{GoodsID}
          AND tpp.IsLargess=#{IsLargess}
    </select>

    <select id="packageCardProjectNum" resultType="java.lang.Integer">
        SELECT count(*) ConsumeNum
        FROM TB_PackageCardProject tpp WITH(NOLOCK)
            LEFT JOIN  TB_Project te
        WITH (NOLOCK)
        ON tpp.ProjectID=te.ID
        where tpp.PackageCardID=#{GoodsID}
          AND tpp.IsLargess=#{IsLargess}
    </select>

    <select id="packageCardGeneralCardNum" resultType="java.lang.Integer">
        SELECT count(*) ConsumeNum
        FROM TB_PackageCardGeneralCard tpp WITH(NOLOCK)
            LEFT JOIN  TB_GeneralCard te
        WITH (NOLOCK)
        ON tpp.GeneralCardID=te.ID
        WHERE tpp.PackageCardID=#{GoodsID}
          AND tpp.IsLargess=#{IsLargess}
    </select>

    <select id="packageCardTimeCardNum" resultType="java.lang.Integer">
        SELECT count(*) ConsumeNum
        FROM TB_PackageCardTimeCard tpp WITH(NOLOCK)
            LEFT JOIN  TB_TimeCard te
        WITH (NOLOCK)
        ON tpp.TimeCardID=te.ID
        WHERE tpp.PackageCardID=#{GoodsID}
          AND tpp.IsLargess=#{IsLargess}
    </select>

    <select id="packageCardSavingCardNum" resultType="java.lang.Integer">
        SELECT count(*) ConsumeNum
        FROM TB_PackageCardSavingCard tpp WITH(NOLOCK)
            LEFT JOIN  TB_SavingCard te
        WITH (NOLOCK)
        ON tpp.SavingCardID=te.ID
        WHERE tpp.PackageCardID=#{GoodsID}
          AND tpp.IsLargess=#{IsLargess}
    </select>

    <select id="savingCardProjectNum" resultType="java.lang.Integer">
        SELECT count(*) ConsumeNum
        FROM TB_SavingCardProject a1 WITH(NOLOCK)
            LEFT JOIN TB_Project a2
        WITH (NOLOCK)
        on a1.ProjectID=a2.ID
        where a1.SavingCardID=#{GoodsID}
          and a1.IsLargess=#{IsLargess}
    </select>

    <select id="savingCardProductNum" resultType="java.lang.Integer">
        SELECT count(*) ConsumeNum
        FROM TB_SavingCardProduct a1 WITH(NOLOCK)
            LEFT JOIN TE_Product a2
        WITH (NOLOCK)
        on a1.ProductID=a2.ID
        where a1.SavingCardID=#{GoodsID}
          and a1.IsLargess=#{IsLargess}
    </select>

    <resultMap type="com.zhelian.model.micromall.form.output.EntityListOutForm" id="applyEntityMap">
        <id column="ID" jdbcType="INTEGER" property="ID"/>
        <result column="EntityName" jdbcType="VARCHAR" property="EntityName"/>
        <result column="AddressDetail" jdbcType="VARCHAR" property="AddressDetail"/>
        <result column="BusinessStartTime" jdbcType="VARCHAR" property="BusinessStartTime"/>
        <result column="BusinessEndTime" jdbcType="VARCHAR" property="BusinessEndTime"/>
        <result column="EntityLogoURL" jdbcType="VARCHAR" property="EntityLogoURL"/>
        <result column="Description" jdbcType="VARCHAR" property="Description"/>
        <result column="ServiceTelephoneNumber" jdbcType="VARCHAR" property="ServiceTelephoneNumber"/>
        <result column="Longitude" jdbcType="DECIMAL" property="Longitude"/>
        <result column="Latitude" jdbcType="DECIMAL" property="Latitude"/>
        <collection property="EntityBusinessDate"
                    ofType="com.zhelian.model.khs.form.output.EntityBusinessDateOutputForm"
                     column="ID">
            <result column="Data" jdbcType="INTEGER" property="Data"/>
        </collection>

    </resultMap>

    <select id="getOnlineEntityPhoto"
            resultType="com.zhelian.model.khs.form.output.EntityPhotoUrlOutputForm">
        SELECT s.ID,
               s.EntityID,
               s.PhotoURL,
               s.Sequence
        FROM TM_EntityPhoto s
        order by s.Sequence asc
    </select>

    <select id="getOnlineEntityBusinessDate" parameterType="Integer"
            resultType="com.zhelian.model.khs.form.output.EntityBusinessDateOutputForm">
        SELECT Data
        FROM TM_EntityBusinessDate
        WHERE EntityID = #{ID}
    </select>

    <select id="getApplyEntityList" resultMap="applyEntityMap">
        SELECT * FROM GetApplyEntity(#{ID}) y
        LEFT JOIN TK_Entity ty WITH(NOLOCK) ON y.ID=ty.ID
        LEFT JOIN TM_EntityBusinessDate tme WITH(NOLOCK) ON tme.EntityID=ty.ID
        WHERE ty.IsStore=1
        <if test="SearchValue != null and SearchValue != '' ">
            AND (y.EntityName like concat('%',#{SearchValue},'%') OR y.AddressDetail like
            concat('%',#{SearchValue},'%'))
        </if>
    </select>
    <select id="projectApplyEntity" resultType="java.lang.Integer"
            parameterType="com.zhelian.model.micromall.form.input.GoodsQueryForm">
        SELECT y.ID
        FROM TK_Entity y WITH (NOLOCK)
        LEFT JOIN TB_ProjectConsumeEntity cy
        WITH (NOLOCK)
        ON cy.EntityID = y.ID
        WHERE
            y.Active = 1
          AND
            cy.ProjectID = #{GoodsID}
    </select>

    <select id="generalCardApplyEntity" resultType="java.lang.Integer"
            parameterType="com.zhelian.model.micromall.form.input.GoodsQueryForm">
        SELECT y.ID
        FROM TK_Entity y WITH (NOLOCK)
        LEFT JOIN TB_GeneralCardConsumeEntity cy
        WITH (NOLOCK)
        ON cy.EntityID = y.ID
        WHERE
            y.Active = 1
          AND cy.GeneralCardID = #{GoodsID}
    </select>

    <select id="timeCardApplyEntity" resultType="java.lang.Integer"
            parameterType="com.zhelian.model.micromall.form.input.GoodsQueryForm">
        SELECT y.ID
        FROM TK_Entity y WITH (NOLOCK)
        LEFT JOIN TB_TimeCardConsumeEntity cy
        WITH (NOLOCK)
        ON cy.EntityID = y.ID
        WHERE
            y.Active = 1
          AND cy.TimeCardID = #{GoodsID}
    </select>

    <select id="savingCardApplyEntity" resultType="java.lang.Integer"
            parameterType="com.zhelian.model.micromall.form.input.GoodsQueryForm">
        SELECT y.ID
        FROM TK_Entity y WITH (NOLOCK)
        LEFT JOIN TB_SavingCardConsumeEntity cy
        WITH (NOLOCK)
        ON cy.EntityID = y.ID
        WHERE
            y.Active = 1
          AND cy.SavingCardID = #{GoodsID}
    </select>

    <select id="generalCardApplyProject" resultType="com.zhelian.model.micromall.form.output.ApplyProjectOutForm">
        SELECT
        p.ID,
        p.Name,
        p.Price,
        '项目'GoodsTypeName,
        (
        SELECT
        TOP 1 ImageURL
        FROM
        TB_ProjectImage
        WHERE
        ProjectID = p.ID
        ORDER BY
        Sequence ASC
        ) ImageURL
        FROM
        TB_GeneralCardProject gcp WITH (NOLOCK)
        LEFT JOIN TB_Project p WITH (NOLOCK) ON gcp.ProjectID = p.ID
        LEFT JOIN TB_ProjectCategory pc WITH (NOLOCK) ON p.ProjectCategoryID = pc.ID
        LEFT JOIN TB_ProjectCategory fpc WITH (NOLOCK) ON pc.ParentID = fpc.ID
        WHERE
        1 = 1
        AND gcp.GeneralCardID = #{GoodsID}
        <if test="Name != null and Name != '' ">
            AND (p.Name like concat('%',#{Name},'%'))
        </if>
    </select>

    <select id="timeCardApplyProject" resultType="com.zhelian.model.micromall.form.output.ApplyProjectOutForm">
        SELECT
        p.ID,
        p.Name,
        p.Price,
        '项目'GoodsTypeName,
        (
        SELECT
        TOP 1 ImageURL
        FROM
        TB_ProjectImage
        WHERE
        ProjectID = p.ID
        ORDER BY
        Sequence ASC
        ) ImageURL
        FROM
        TB_TimeCardProject tcp WITH(NOLOCK)
        LEFT JOIN TB_Project p WITH(NOLOCK) ON tcp.ProjectID = p.ID
        LEFT JOIN TB_ProjectCategory pc WITH(NOLOCK) ON p.ProjectCategoryID = pc.ID
        LEFT JOIN TB_ProjectCategory fpc WITH(NOLOCK) ON pc.ParentID = fpc.ID
        WHERE 1=1
        and tcp.TimeCardID =#{GoodsID}
        <if test="Name != null and Name != '' ">
            AND (p.Name like concat('%',#{Name},'%'))
        </if>
    </select>

    <select id="savingCardProject" resultType="com.zhelian.model.micromall.form.output.ApplyProjectOutForm">
        SELECT
        a2.ID,
        a2.Name,
        pt.Price,
        '产品'GoodsTypeName,
        (
        SELECT
        TOP 1 ImageURL
        FROM
        TB_ProductImage
        WHERE
        ProductID = a2.ID
        ORDER BY
        Sequence ASC
        ) ImageURL
        FROM
        TB_SavingCardProduct a1 WITH(NOLOCK)
        LEFT JOIN TE_Product a2 WITH(NOLOCK) ON a1.ProductID=a2.ID
        LEFT JOIN TB_Product pt WITH (NOLOCK) ON pt.ProductID = a2.ID
        where a1.SavingCardID=#{GoodsID}
        and a1.IsLargess=#{IsLargess}
        <if test="Name != null and Name != '' ">
            AND (a2.Name like concat('%',#{Name},'%'))
        </if>
        UNION ALL
        SELECT
        a2.ID,
        a2.Name,
        a2.Price,
        '项目'GoodsTypeName,
        (
        SELECT
        TOP 1 ImageURL
        FROM
        TB_ProjectImage
        WHERE
        ProjectID = a2.ID
        ORDER BY
        Sequence ASC
        ) ImageURL
        FROM
        TB_SavingCardProject a1 WITH(NOLOCK)
        LEFT JOIN TB_Project a2 WITH(NOLOCK) on a1.ProjectID=a2.ID
        where a1.SavingCardID=#{GoodsID}
        and a1.IsLargess=#{IsLargess}
        <if test="Name != null and Name != '' ">
            AND (a2.Name like concat('%',#{Name},'%'))
        </if>
    </select>

    <select id="packageCardDetails" resultType="com.zhelian.model.micromall.form.output.ApplyProjectOutForm">
        SELECT
        te.ID,
        te.Name,
        tpp.Price,
        tpp.Amount,
        0 Times,
        0 ConsumeCycle,
        0 CycleLimitAmount,
        te.Specification,
        u.Name UnitName,
        '产品' GoodsTypeName,
        (
        SELECT
        TOP 1 ImageURL
        FROM
        TB_ProductImage
        WHERE
        ProductID = te.ID
        ORDER BY
        Sequence ASC
        ) ImageURL,
        0 ApplyNum,
        CASE t.ValidType WHEN 1 then concat('购买后',t.ValidDay,'天有效')
        WHEN 2 THEN concat('消耗后',t.ValidDay,'天有效')
        ELSE '永久有效' END ValidDay
        FROM
        TB_PackageCardProduct tpp WITH (NOLOCK)
        LEFT JOIN TB_PackageCard t WITH (NOLOCK) ON tpp.PackageCardID = t.ID
        LEFT JOIN TE_Product te WITH (NOLOCK) ON tpp.ProductID = te.ID
        LEFT JOIN TB_Product tp WITH (NOLOCK) ON tp.ProductID = te.ID
        LEFT JOIN TE_ProductBarCode e ON e.ProductID = te.ID
        LEFT JOIN TE_Unit u ON u.ID = e.UnitID
        WHERE
        tpp.PackageCardID = #{GoodsID} AND e.IsMinimumUnit = 1
        AND tpp.IsLargess = #{IsLargess}
        <if test="Name != null and Name != '' ">
            AND (te.Name like concat('%',#{Name},'%'))
        </if>
        UNION ALL
        SELECT
        te.ID,
        te.Name,
        tpp.Price,
        tpp.Amount,
        0 Times,
        0 ConsumeCycle,
        0 CycleLimitAmount,
        '' Specification,
        '' UnitName,
        '项目' GoodsTypeName,
        (
        SELECT
        TOP 1 ImageURL
        FROM
        TB_ProjectImage
        WHERE
        ProjectID = te.ID
        ORDER BY
        Sequence ASC
        ) ImageURL,
        (
        SELECT
        COUNT (ts.EntityID)
        FROM
        TB_ProjectConsumeEntity ts WITH (NOLOCK)
        LEFT JOIN TK_Entity tk WITH (NOLOCK) ON tk.ID = ts.EntityID
        WHERE
        tk.Active = 1
        AND ts.ProjectID = te.ID
        ) ApplyNum,
        CASE t.ValidType WHEN 1 then concat('购买后',t.ValidDay,'天有效')
        WHEN 2 THEN concat('消耗后',t.ValidDay,'天有效')
        ELSE '永久有效' END ValidDay
        FROM
        TB_PackageCardProject tpp WITH (NOLOCK)
        LEFT JOIN TB_PackageCard t WITH (NOLOCK) ON tpp.PackageCardID = t.ID
        LEFT JOIN TB_Project te WITH (NOLOCK) ON tpp.ProjectID = te.ID
        WHERE
        tpp.PackageCardID = #{GoodsID}
        AND tpp.IsLargess = #{IsLargess}
        <if test="Name != null and Name != '' ">
            AND (te.Name like concat('%',#{Name},'%'))
        </if>
        UNION ALL
        SELECT
        te.ID,
        te.Name,
        tpp.Price,
        tpp.Amount,
        te.Amount Times,
        0 ConsumeCycle,
        0 CycleLimitAmount,
        '' Specification,
        '' UnitName,
        '通用次卡' GoodsTypeName,
        (
        SELECT
        TOP 1 ImageURL
        FROM
        TB_GeneralCardImage
        WHERE
        GeneralCardID = te.ID
        ORDER BY
        Sequence ASC
        ) ImageURL,
        (
        SELECT
        COUNT (ts.EntityID)
        FROM
        TB_GeneralCardConsumeEntity ts WITH (NOLOCK)
        LEFT JOIN TK_Entity tk WITH (NOLOCK) ON tk.ID = ts.EntityID
        WHERE
        tk.Active = 1
        AND ts.GeneralCardID = te.ID
        ) ApplyNum,
        CASE t.ValidType WHEN 1 then concat('购买后',t.ValidDay,'天有效')
        WHEN 2 THEN concat('消耗后',t.ValidDay,'天有效')
        ELSE '永久有效' END ValidDay
        FROM
        TB_PackageCardGeneralCard tpp WITH (NOLOCK)
        LEFT JOIN TB_PackageCard t WITH (NOLOCK) ON tpp.PackageCardID = t.ID
        LEFT JOIN TB_GeneralCard te WITH (NOLOCK) ON tpp.GeneralCardID = te.ID
        WHERE
        tpp.PackageCardID = #{GoodsID}
        AND tpp.IsLargess = #{IsLargess}
        <if test="Name != null and Name != '' ">
            AND (te.Name like concat('%',#{Name},'%'))
        </if>
        UNION ALL
        SELECT
        te.ID,
        te.Name,
        tpp.Price,
        tpp.Amount,
        0 Times,
        te.ConsumeCycle,
        te.CycleLimitAmount,
        '' Specification,
        '' UnitName,
        '时效卡' GoodsTypeName,
        (
        SELECT
        TOP 1 ImageURL
        FROM
        TB_TimeCardImage
        WHERE
        TimeCardID = te.ID
        ORDER BY
        Sequence ASC
        ) ImageURL,
        (
        SELECT
        COUNT (ts.EntityID)
        FROM
        TB_TimeCardConsumeEntity ts WITH (NOLOCK)
        LEFT JOIN TK_Entity tk WITH (NOLOCK) ON tk.ID = ts.EntityID
        WHERE
        tk.Active = 1
        AND ts.TimeCardID = te.ID
        ) ApplyNum,
        CASE t.ValidType WHEN 1 then concat('购买后',t.ValidDay,'天有效')
        WHEN 2 THEN concat('消耗后',t.ValidDay,'天有效')
        ELSE '永久有效' END ValidDay
        FROM
        TB_PackageCardTimeCard tpp WITH (NOLOCK)
        LEFT JOIN TB_PackageCard t WITH (NOLOCK) ON tpp.PackageCardID = t.ID
        LEFT JOIN TB_TimeCard te WITH (NOLOCK) ON tpp.TimeCardID = te.ID
        WHERE
        tpp.PackageCardID = #{GoodsID}
        AND tpp.IsLargess = #{IsLargess}
        <if test="Name != null and Name != '' ">
            AND (te.Name like concat('%',#{Name},'%'))
        </if>
        UNION ALL
        SELECT
        te.ID,
        te.Name,
        tpp.Price,
        tpp.Amount,
        0 Times,
        0 ConsumeCycle,
        0 CycleLimitAmount,
        '' Specification,
        '' UnitName,
        '储值卡' GoodsTypeName,
        (
        SELECT
        TOP 1 ImageURL
        FROM
        TB_SavingCardImage
        WHERE
        SavingCardID = te.ID
        ORDER BY
        Sequence ASC
        ) ImageURL,
        (
        SELECT
        COUNT (ts.EntityID)
        FROM
        TB_SavingCardConsumeEntity ts WITH (NOLOCK)
        LEFT JOIN TK_Entity tk WITH (NOLOCK) ON tk.ID = ts.EntityID
        WHERE
        tk.Active = 1
        AND ts.SavingCardID = te.ID
        ) ApplyNum,
        CASE t.ValidType WHEN 1 then concat('购买后',t.ValidDay,'天有效')
        WHEN 2 THEN concat('消耗后',t.ValidDay,'天有效')
        ELSE '永久有效' END ValidDay
        FROM
        TB_PackageCardSavingCard tpp WITH (NOLOCK)
        LEFT JOIN TB_PackageCard t WITH (NOLOCK) ON tpp.PackageCardID = t.ID
        LEFT JOIN TB_SavingCard te WITH (NOLOCK) ON tpp.SavingCardID = te.ID
        WHERE
        tpp.PackageCardID = #{GoodsID}
        AND tpp.IsLargess = #{IsLargess}
        <if test="Name != null and Name != '' ">
            AND (te.Name like concat('%',#{Name},'%'))
        </if>
    </select>

    <select id="searchGoods" resultType="com.zhelian.model.micromall.form.output.SearchGoodsOutputForm">
        SELECT
        tab.GoodsID,
        tab.GoodsType,
        tab.Name,
        tab.Price,
        tab.GoodsTypeName,
        tab.ImageURL
        FROM
        (SELECT
        s.GoodsID,
        s.GoodsType,
        t.Name,
        pt.Price,
        '产品' GoodsTypeName,
        (SELECT TOP 1 ImageURL FROM TB_ProductImage WHERE ProductID = t.ID ORDER BY Sequence ASC)ImageURL
        FROM
        TM_Goods s
        LEFT JOIN TE_Product t ON t.ID = s.GoodsID
        LEFT JOIN TB_Product pt ON pt.ProductID = t.ID
        WHERE s.GoodsType = '10'
        <if test="Name != null and Name != ''">
            AND ( t.Name LIKE '%'+ #{Name} +'%' )
        </if>
        UNION
        SELECT
        s.GoodsID,
        s.GoodsType,
        t.Name,
        t.Price,
        '项目' GoodsTypeName,
        (SELECT TOP 1 ImageURL FROM TB_ProjectImage WHERE ProjectID = t.ID ORDER BY Sequence ASC)ImageURL
        FROM
        TM_Goods s
        LEFT JOIN TB_Project t ON t.ID = s.GoodsID
        WHERE s.GoodsType = '20'
        <if test="Name != null and Name != ''">
            AND ( t.Name LIKE '%'+ #{Name} +'%' )
        </if>
        UNION
        SELECT
        s.GoodsID,
        s.GoodsType,
        t.Name,
        t.Price,
        '通用次卡' GoodsTypeName,
        (SELECT TOP 1 ImageURL FROM TB_GeneralCardImage WHERE GeneralCardID = t.ID ORDER BY Sequence ASC)ImageURL
        FROM
        TM_Goods s
        LEFT JOIN TB_GeneralCard t ON t.ID = s.GoodsID
        WHERE s.GoodsType = '30'
        <if test="Name != null and Name != ''">
            AND ( t.Name LIKE '%'+ #{Name} +'%' )
        </if>
        UNION
        SELECT
        s.GoodsID,
        s.GoodsType,
        t.Name,
        t.Price,
        '时效卡' GoodsTypeName,
        (SELECT TOP 1 ImageURL FROM TB_TimeCardImage WHERE TimeCardID = t.ID ORDER BY Sequence ASC)ImageURL
        FROM
        TM_Goods s
        LEFT JOIN TB_TimeCard t ON t.ID = s.GoodsID
        WHERE s.GoodsType = '40'
        <if test="Name != null and Name != ''">
            AND ( t.Name LIKE '%'+ #{Name} +'%' )
        </if>
        UNION
        SELECT
        s.GoodsID,
        s.GoodsType,
        t.Name,
        t.Price,
        '储值卡' GoodsTypeName,
        (SELECT TOP 1 ImageURL FROM TB_SavingCardImage WHERE SavingCardID = t.ID ORDER BY Sequence ASC)ImageURL
        FROM
        TM_Goods s
        LEFT JOIN TB_SavingCard t ON t.ID = s.GoodsID
        WHERE s.GoodsType = '50'
        <if test="Name != null and Name != ''">
            AND ( t.Name LIKE '%'+ #{Name} +'%' )
        </if>
        UNION
        SELECT
        s.GoodsID,
        s.GoodsType,
        t.Name,
        t.Price,
        '套餐卡' GoodsTypeName,
        (SELECT TOP 1 ImageURL FROM TB_PackageCardImage WHERE PackageCardID = t.ID ORDER BY Sequence ASC)ImageURL
        FROM
        TM_Goods s
        LEFT JOIN TB_PackageCard t ON t.ID = s.GoodsID
        WHERE s.GoodsType = '60'
        <if test="Name != null and Name != ''">
            AND ( t.Name LIKE '%'+ #{Name} +'%' )
        </if>)tab
        <if test="Type ==null">
            ORDER BY
            tab.Price,
            tab.GoodsType ASC
        </if>
        <if test="Type=='Price'.toString() ">
            <if test="Sort=='down'.toString() ">
                ORDER BY tab.Price DESC
            </if>
            <if test="Sort=='up'.toString() ">
                ORDER BY tab.Price ASC
            </if>
        </if>
        <if test="Type=='GoodsType'.toString() ">
            <if test="Sort=='down'.toString() ">
                ORDER BY tab.GoodsType DESC
            </if>
            <if test="Sort=='up'.toString() ">
                ORDER BY tab.GoodsType ASC
            </if>
        </if>
    </select>

    <select id="getProjectCustomerDiscount" resultType="com.zhelian.model.micromall.form.output.GoodsCustomerDiscountOutputForm">
        SELECT TOP 1
                PriceType,
                DiscountPrice
        FROM
            (
                SELECT
                    1 AS Priority,
                    tcpp.PriceType,
                    tcpp.DiscountPrice
                FROM
                    TB_CustomerPriceProject tcpp WITH(NOLOCK)
                WHERE tcpp.ProjectID = #{GoodsID}
                  AND EXISTS (
                    SELECT
                    1
                    FROM
                    TB_Customer tc WITH(NOLOCK)
                    WHERE tcpp.CustomerLevelID = tc.CustomerLevelID
                  AND tc.ID = #{CustomerID}
                    )
                UNION ALL
                SELECT
                    2 AS Priority,
                    1 PriceType,
                    tcpp.DiscountPrice
                FROM
                    TB_CustomerPriceProjectCategory tcpp WITH(NOLOCK)
                WHERE
                    EXISTS (
                    SELECT
                    1
                    FROM
                    TB_Project tp WITH(NOLOCK)
                    WHERE (tp.ProjectCategoryID = tcpp.ProjectCategoryID
                   OR EXISTS (
                    SELECT
                    1
                    FROM
                    TB_ProjectCategory tpc WITH(NOLOCK)
                    WHERE tpc.ID = tp.ProjectCategoryID AND tpc.ParentID = tcpp.ProjectCategoryID
                    )
                    )
                  AND tp.ID = #{GoodsID}
                    )
                  AND EXISTS (
                    SELECT
                    1
                    FROM
                    TB_Customer tc WITH(NOLOCK)
                    WHERE tcpp.CustomerLevelID = tc.CustomerLevelID
                  AND tc.ID = #{CustomerID}
                    )
                UNION ALL
                SELECT
                    3 AS Priority,
                    1 PriceType,
                    tcpp.DiscountPrice
                FROM
                    TB_CustomerPriceProjectScheme tcpp WITH(NOLOCK)
                WHERE
                    EXISTS (
                    SELECT
                    1
                    FROM
                    TB_Customer tc WITH(NOLOCK)
                    WHERE tcpp.CustomerLevelID = tc.CustomerLevelID
                  AND tc.ID = #{CustomerID}
                    )
            ) A
        WHERE DiscountPrice IS NOT NULL
        ORDER BY Priority ASC
    </select>

    <select id="getProductCustomerDiscount" resultType="com.zhelian.model.micromall.form.output.GoodsCustomerDiscountOutputForm">
        SELECT TOP 1
                PriceType,
                DiscountPrice
        FROM
            (
                SELECT
                    1 AS Priority,
                    tcpp.PriceType,
                    tcpp.DiscountPrice
                FROM
                    TB_CustomerPriceProduct tcpp WITH(NOLOCK)
                WHERE tcpp.ProductID = #{GoodsID}
                  AND EXISTS (
                    SELECT
                    1
                    FROM
                    TB_Customer tc WITH(NOLOCK)
                    WHERE tcpp.CustomerLevelID = tc.CustomerLevelID
                  AND tc.ID = #{CustomerID}
                    )
                UNION ALL
                SELECT
                    2 AS Priority,
                    1 PriceType,
                    tcpp.DiscountPrice
                FROM
                    TB_CustomerPriceProductCategory tcpp WITH(NOLOCK)
                WHERE
                    EXISTS (
                    SELECT
                    1
                    FROM
                    TE_Product tp WITH(NOLOCK)
                    WHERE (tp.PCategoryID = tcpp.ProductCategoryID
                   OR
                    EXISTS (
                    SELECT
                    1
                    FROM
                    TE_ProductCategory tpc WITH(NOLOCK)
                    WHERE tpc.ID = tp.PCategoryID AND tpc.ParentID = tcpp.ProductCategoryID
                    )
                    )
                  AND tp.ID = #{GoodsID}
                    )
                  AND EXISTS (
                    SELECT
                    1
                    FROM
                    TB_Customer tc WITH(NOLOCK)
                    WHERE tcpp.CustomerLevelID = tc.CustomerLevelID
                  AND tc.ID = #{CustomerID}
                    )
                UNION ALL
                SELECT
                    3 AS Priority,
                    1 PriceType,
                    tcpp.DiscountPrice
                FROM
                    TB_CustomerPriceProductScheme tcpp WITH(NOLOCK)
                WHERE
                    EXISTS (
                    SELECT
                    1
                    FROM
                    TB_Customer tc WITH(NOLOCK)
                    WHERE tcpp.CustomerLevelID = tc.CustomerLevelID
                  AND tc.ID = #{CustomerID}
                    )
            ) A
        WHERE DiscountPrice IS NOT NULL
        ORDER BY Priority ASC
    </select>

    <select id="getGeneralCardCustomerDiscount" resultType="com.zhelian.model.micromall.form.output.GoodsCustomerDiscountOutputForm">
        SELECT TOP 1
                PriceType,
                DiscountPrice
        FROM
            (
                SELECT
                    1 AS Priority,
                    tcpp.PriceType,
                    tcpp.DiscountPrice
                FROM
                    TB_CustomerPriceGeneralCard tcpp WITH(NOLOCK)
                WHERE tcpp.GeneralCardID = #{GoodsID}
                  AND EXISTS (
                    SELECT
                    1
                    FROM
                    TB_Customer tc WITH(NOLOCK)
                    WHERE tcpp.CustomerLevelID = tc.CustomerLevelID
                  AND tc.ID = #{CustomerID}
                    )
                UNION ALL
                SELECT
                    2 AS Priority,
                    1 PriceType,
                    tcpp.DiscountPrice
                FROM
                    TB_CustomerPriceGeneralCardCategory tcpp WITH(NOLOCK)
                WHERE
                    EXISTS (
                    SELECT
                    1
                    FROM
                    TB_GeneralCard tp WITH(NOLOCK)
                    WHERE tp.GeneralCardCategoryID = tcpp.GeneralCardCategoryID
                  AND tp.ID = #{GoodsID}
                    )
                  AND EXISTS (
                    SELECT
                    1
                    FROM
                    TB_Customer tc WITH(NOLOCK)
                    WHERE tcpp.CustomerLevelID = tc.CustomerLevelID
                  AND tc.ID = #{CustomerID}
                    )
                UNION ALL
                SELECT
                    3 AS Priority,
                    1 PriceType,
                    tcpp.DiscountPrice
                FROM
                    TB_CustomerPriceGeneralCardScheme tcpp WITH(NOLOCK)
                WHERE
                    EXISTS (
                    SELECT
                    1
                    FROM
                    TB_Customer tc WITH(NOLOCK)
                    WHERE tcpp.CustomerLevelID = tc.CustomerLevelID
                  AND tc.ID = #{CustomerID}
                    )
            ) A
        WHERE DiscountPrice IS NOT NULL
        ORDER BY Priority ASC
    </select>

    <select id="getTimeCardCustomerDiscount" resultType="com.zhelian.model.micromall.form.output.GoodsCustomerDiscountOutputForm">
        SELECT TOP 1
                PriceType,
                DiscountPrice
        FROM
            (
                SELECT
                    1 AS Priority,
                    tcpp.PriceType,
                    tcpp.DiscountPrice
                FROM
                    TB_CustomerPriceTimeCard tcpp WITH(NOLOCK)
                WHERE tcpp.TimeCardID = #{GoodsID}
                  AND EXISTS (
                    SELECT
                    1
                    FROM
                    TB_Customer tc WITH(NOLOCK)
                    WHERE tcpp.CustomerLevelID = tc.CustomerLevelID
                  AND tc.ID = #{CustomerID}
                    )
                UNION ALL
                SELECT
                    2 AS Priority,
                    1 PriceType,
                    tcpp.DiscountPrice
                FROM
                    TB_CustomerPriceTimeCardCategory tcpp WITH(NOLOCK)
                WHERE
                    EXISTS (
                    SELECT
                    1
                    FROM
                    TB_TimeCard tp WITH(NOLOCK)
                    WHERE tp.TimeCardCategoryID = tcpp.TimeCardCategoryID
                  AND tp.ID = #{GoodsID}
                    )
                  AND EXISTS (
                    SELECT
                    1
                    FROM
                    TB_Customer tc WITH(NOLOCK)
                    WHERE tcpp.CustomerLevelID = tc.CustomerLevelID
                  AND tc.ID = #{CustomerID}
                    )
                UNION ALL
                SELECT
                    3 AS Priority,
                    1 PriceType,
                    tcpp.DiscountPrice
                FROM
                    TB_CustomerPriceTimeCardScheme tcpp WITH(NOLOCK)
                WHERE
                    EXISTS (
                    SELECT
                    1
                    FROM
                    TB_Customer tc WITH(NOLOCK)
                    WHERE tcpp.CustomerLevelID = tc.CustomerLevelID
                  AND tc.ID = #{CustomerID}
                    )
            ) A
        WHERE DiscountPrice IS NOT NULL
        ORDER BY Priority ASC
    </select>

    <select id="getPackageCardCustomerDiscount" resultType="com.zhelian.model.micromall.form.output.GoodsCustomerDiscountOutputForm">
        SELECT TOP 1
                PriceType,
                DiscountPrice
        FROM
            (
                SELECT
                    1 AS Priority,
                    tcpp.PriceType,
                    tcpp.DiscountPrice
                FROM
                    TB_CustomerPricePackageCard tcpp WITH(NOLOCK)
                WHERE tcpp.PackageCardID = #{GoodsID}
                  AND EXISTS (
                    SELECT
                    1
                    FROM
                    TB_Customer tc WITH(NOLOCK)
                    WHERE tcpp.CustomerLevelID = tc.CustomerLevelID
                  AND tc.ID = #{CustomerID}
                    )
                UNION ALL
                SELECT
                    2 AS Priority,
                    1 PriceType,
                    tcpp.DiscountPrice
                FROM
                    TB_CustomerPricePackageCardCategory tcpp WITH(NOLOCK)
                WHERE
                    EXISTS (
                    SELECT
                    1
                    FROM
                    TB_PackageCard tp WITH(NOLOCK)
                    WHERE tp.PackageCardCategoryID = tcpp.PackageCardCategoryID
                  AND tp.ID = #{GoodsID}
                    )
                  AND EXISTS (
                    SELECT
                    1
                    FROM
                    TB_Customer tc WITH(NOLOCK)
                    WHERE tcpp.CustomerLevelID = tc.CustomerLevelID
                  AND tc.ID = #{CustomerID}
                    )
                UNION ALL
                SELECT
                    3 AS Priority,
                    1 PriceType,
                    tcpp.DiscountPrice
                FROM
                    TB_CustomerPricePackageCardScheme tcpp WITH(NOLOCK)
                WHERE
                    EXISTS (
                    SELECT
                    1
                    FROM
                    TB_Customer tc WITH(NOLOCK)
                    WHERE tcpp.CustomerLevelID = tc.CustomerLevelID
                  AND tc.ID = #{CustomerID}
                    )
            ) A
        WHERE DiscountPrice IS NOT NULL
        ORDER BY Priority ASC
    </select>

</mapper>