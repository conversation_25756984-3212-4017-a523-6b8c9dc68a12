<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.psi.InventoryTreatCancelInboundMapper">

    <insert id="createInventoryTreatCancelInbound" parameterType="com.zhelian.model.psi.entity.InventoryTreatCancelInboundEntity" >
        INSERT INTO TE_InventoryTreatCancelInbound (ID,TreatBillID, EntityID, InDate, CreatedBy,Remark)
        VALUES
            (#{ID},
             #{TreatBillID},
            #{EntityID},
            #{InDate},
            #{CreatedBy},
            #{Remark}
            )
    </insert>

    <insert id="createInventoryTreatProjectProduct" parameterType="java.util.List">
        INSERT INTO TE_InventoryTreatCancelProjectInboundDetail (InventoryTreatCancelInboundID, TreatBillProjectID, ProductID, UnitID,Quantity,MinimumUnitID,MinimumUnitQuantity,StockQuantity)
        VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.InventoryTreatCancelInboundID},#{item.TreatBillGoodID},#{item.ProductID},#{item.UnitID},#{item.Quantity},#{item.MinimumUnitID},
            #{item.MinimumUnitQuantity},#{item.StockQuantity}
            )
        </foreach>
    </insert>

    <insert id="createInventoryTreatGeneralCardProduct" parameterType="java.util.List">
        INSERT INTO TE_InventoryTreatCancelGeneralCardInboundDetail (InventoryTreatCancelInboundID, TreatBillGeneralCardID, ProductID, UnitID,Quantity,MinimumUnitID,MinimumUnitQuantity,StockQuantity)
        VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.InventoryTreatCancelInboundID},#{item.TreatBillGoodID},#{item.ProductID},#{item.UnitID},#{item.Quantity},#{item.MinimumUnitID},
            #{item.MinimumUnitQuantity},#{item.StockQuantity}
            )
        </foreach>
    </insert>

    <insert id="createInventoryTreatTimeCardProduct" parameterType="java.util.List">
        INSERT INTO TE_InventoryTreatCancelTimeCardInboundDetail (InventoryTreatCancelInboundID, TreatBillTimeCardID, ProductID, UnitID,Quantity,MinimumUnitID,MinimumUnitQuantity,StockQuantity)
        VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.InventoryTreatCancelInboundID},#{item.TreatBillGoodID},#{item.ProductID},#{item.UnitID},#{item.Quantity},#{item.MinimumUnitID},
            #{item.MinimumUnitQuantity},#{item.StockQuantity}
            )
        </foreach>
    </insert>

    <insert id="createInventoryTreatSavingCardProduct" parameterType="java.util.List">
        INSERT INTO TE_InventoryTreatCancelSavingCardInboundDetail (InventoryTreatCancelInboundID, TreatBillSavingCardID, ProductID, UnitID,Quantity,MinimumUnitID,MinimumUnitQuantity,StockQuantity)
        VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.InventoryTreatCancelInboundID},#{item.TreatBillGoodID},#{item.ProductID},#{item.UnitID},#{item.Quantity},#{item.MinimumUnitID},
            #{item.MinimumUnitQuantity},#{item.StockQuantity}
            )
        </foreach>
    </insert>
</mapper>