<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.psi.InventoryProductOutboundMapper">
    <resultMap id="inventoryProductOutboundInfoOutputForm"
               type="com.zhelian.model.psi.form.output.InventoryProductOutboundInfoOutputForm">
        <id column="ID" property="ID" jdbcType="VARCHAR"/>
        <result column="OutDate" property="OutDate" jdbcType="VARCHAR"/>
        <result column="InventoryType" property="InventoryType" jdbcType="VARCHAR"/>
        <result column="CreatedByName" property="CreatedByName" jdbcType="VARCHAR"/>
        <result column="CreatedOn" property="CreatedOn" jdbcType="VARCHAR"/>
        <result column="EntityName" property="EntityName" jdbcType="VARCHAR"/>
        <result column="Remark" property="Remark" jdbcType="VARCHAR"/>
        <result column="EntityID" property="EntityID" jdbcType="INTEGER"/>
        <collection property="Detail"
                    ofType="com.zhelian.model.psi.form.output.InventoryProductOutboundInfoDetailOutputForm">
            <id column="OutboundDetailBillID" property="ID" jdbcType="INTEGER"/>
            <result column="ProductID" property="ProductID" jdbcType="INTEGER"/>
            <result column="ProductName" property="ProductName" jdbcType="VARCHAR"/>
            <result column="UnitID" property="UnitID" jdbcType="INTEGER"/>
            <result column="UnitName" property="UnitName" jdbcType="VARCHAR"/>
            <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
            <result column="MinimumUnitID" property="MinimumUnitID" jdbcType="INTEGER"/>
            <result column="MinimumUnitName" property="MinimumUnitName" jdbcType="VARCHAR"/>
            <result column="MinimumUnitQuantity" property="MinimumUnitQuantity" jdbcType="INTEGER"/>
            <result column="StockQuantity" property="StockQuantity" jdbcType="INTEGER"/>
            <result column="BeforeStockQuantity" property="BeforeStockQuantity" jdbcType="INTEGER"/>
            <result column="PCategoryName" property="PCategoryName" jdbcType="VARCHAR"/>
            <result column="Specification" property="Specification" jdbcType="VARCHAR"/>
            <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <resultMap id="inventoryOtherOutboundInfoOutputForm"
               type="com.zhelian.model.psi.form.output.InventoryOtherOutboundInfoOutputForm">
        <id column="ID" property="ID" jdbcType="VARCHAR"/>
        <result column="OutDate" property="OutDate" jdbcType="VARCHAR"/>
        <result column="InventoryType" property="InventoryType" jdbcType="VARCHAR"/>
        <result column="CreatedByName" property="CreatedByName" jdbcType="VARCHAR"/>
        <result column="CreatedOn" property="CreatedOn" jdbcType="VARCHAR"/>
        <result column="Remark" property="Remark" jdbcType="VARCHAR"/>
        <result column="EntityName" property="EntityName" jdbcType="VARCHAR"/>
        <result column="EntityID" property="EntityID" jdbcType="INTEGER"/>
        <collection property="Detail"
                    ofType="com.zhelian.model.psi.form.output.InventoryOtherOutboundInfoDetailOutputForm">
            <id column="OutboundDetailBillID" property="ID" jdbcType="INTEGER"/>
            <result column="ProductID" property="ProductID" jdbcType="INTEGER"/>
            <result column="ProductName" property="ProductName" jdbcType="VARCHAR"/>
            <result column="UnitID" property="UnitID" jdbcType="INTEGER"/>
            <result column="UnitName" property="UnitName" jdbcType="VARCHAR"/>
            <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
            <result column="MinimumUnitID" property="MinimumUnitID" jdbcType="INTEGER"/>
            <result column="MinimumUnitName" property="MinimumUnitName" jdbcType="VARCHAR"/>
            <result column="MinimumUnitQuantity" property="MinimumUnitQuantity" jdbcType="INTEGER"/>
            <result column="StockQuantity" property="StockQuantity" jdbcType="INTEGER"/>
            <result column="PCategoryName" property="PCategoryName" jdbcType="VARCHAR"/>
            <result column="Specification" property="Specification" jdbcType="VARCHAR"/>
            <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <insert id="insertInventoryProductOutboundEntity"
            parameterType="com.zhelian.model.psi.entity.InventoryProductOutboundEntity">
        INSERT INTO dbo.TE_InventoryProductOutbound (ID, OutDate, OutType, EntityID, CreatedOn,
                                                          CreatedBy, Remark)
        VALUES
            (#{ID}, #{OutDate}, #{OutType}, #{EntityID}, #{CreatedOn}, #{CreatedBy}, #{Remark})
    </insert>

    <insert id="insertInventoryProductOutboundDetailEntity" parameterType="java.util.List">
        INSERT INTO dbo.TE_InventoryProductOutboundDetail (OutboundBillID, ProductID, UnitID, Quantity,
        MinimumUnitID, MinimumUnitQuantity, StockQuantity )
        VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.OutboundBillID},
            #{item.ProductID},
            #{item.UnitID},
            #{item.Quantity},
            #{item.MinimumUnitID},
            #{item.MinimumUnitQuantity},
            #{item.StockQuantity}
            )
        </foreach>
    </insert>

    <select id="inventoryProductOutboundList"
            resultType="com.zhelian.model.psi.form.output.InventoryProductOutboundOutputForm">
        SELECT a1.ID,a1.CreatedOn,a1.OutDate,a1.EntityID,a1.EntityName,a1.Remark,a1.CreatedByName,a1.InventoryType
        FROM (
        SELECT
        a1.ID,
        convert(varchar(20),a1.CreatedOn,120) CreatedOn,
        convert(varchar(20),a1.OutDate,120) OutDate,
        a1.EntityID,
        a2.EntityName,
        a1.Remark,
        a3.Name CreatedByName,
        CASE a1.OutType WHEN '10' THEN '领料出库'
        WHEN '20' THEN '报损出库'
        WHEN '40' THEN '销售出库'
        ELSE '其他出库'
        END InventoryType
        FROM
        dbo.TE_InventoryProductOutbound a1 WITH(NOLOCK)
        LEFT JOIN TK_Entity a2 WITH(NOLOCK) ON a1.EntityID= a2.ID
        LEFT JOIN TK_Employee a3 WITH(NOLOCK) ON a1.CreatedBy= a3.ID
        WHERE 1=1
        <if test="ProductName != null and ProductName != ''">
            AND EXISTS (
            SELECT d1.ID FROM TE_InventoryProductOutboundDetail d1 WITH(NOLOCK)
            LEFT JOIN TE_Product p1 WITH(NOLOCK) ON d1.ProductID=p1.ID
            WHERE d1.OutboundBillID=a1.ID
            AND ( p1.Name LIKE '%'+ #{ProductName} +'%' OR p1.Alias LIKE '%'+ #{ProductName} +'%'
            OR p1.NameFullPinYin LIKE '%'+ #{ProductName} +'%' OR p1.NameAbbrPinYin LIKE '%'+ #{ProductName} +'%'
            OR p1.AliasFullPinYin LIKE '%'+ #{ProductName} +'%' OR p1.AliasAbbrPinYin LIKE '%'+ #{ProductName} +'%' )
            )
        </if>
        UNION ALL
        SELECT
        a1.ID,
        convert(varchar(20),a1.CreatedOn,120) CreatedOn,
        convert(varchar(20),a1.OutDate,120) OutDate,
        a1.EntityID,
        a2.EntityName,
        a1.Remark ,
        a3.Name CreatedByName,
        '配送出库' InventoryType
        FROM
        TE_InventoryOutbound a1 WITH(NOLOCK)
        LEFT JOIN TK_Entity a2 WITH(NOLOCK) ON a1.EntityID= a2.ID
        LEFT JOIN TK_Employee a3 WITH(NOLOCK) ON a1.CreatedBy= a3.ID
        WHERE 1=1
        <if test="ProductName != null and ProductName != ''">
            AND EXISTS (
            SELECT d1.ID FROM TE_InventoryOutboundDetail d1 WITH(NOLOCK)
            LEFT JOIN TE_Product p1 WITH(NOLOCK) ON d1.ProductID=p1.ID
            WHERE d1.OutboundBillID=a1.ID
            AND ( p1.Name LIKE '%'+ #{ProductName} +'%' OR p1.Alias LIKE '%'+ #{ProductName} +'%'
            OR p1.NameFullPinYin LIKE '%'+ #{ProductName} +'%' OR p1.NameAbbrPinYin LIKE '%'+ #{ProductName} +'%'
            OR p1.AliasFullPinYin LIKE '%'+ #{ProductName} +'%' OR p1.AliasAbbrPinYin LIKE '%'+ #{ProductName} +'%' )
            )
        </if>
        UNION ALL
        SELECT
        a1.ID,
        convert(varchar(20),a1.CreatedOn,120) CreatedOn,
        convert(varchar(20),a1.OutDate,120) OutDate,
        a1.EntityID,
        a2.EntityName,
        '' Remark ,
        a3.Name CreatedByName,
        '调拨出库' InventoryType
        FROM
        TE_InventoryAllocationOutbound a1 WITH(NOLOCK)
        LEFT JOIN TK_Entity a2 WITH(NOLOCK) ON a1.EntityID= a2.ID
        LEFT JOIN TK_Employee a3 WITH(NOLOCK) ON a1.CreatedBy= a3.ID
        WHERE 1=1
        <if test="ProductName != null and ProductName != ''">
            AND EXISTS (
            SELECT d1.ID FROM TE_InventoryAllocationOutboundDetail d1 WITH(NOLOCK)
            LEFT JOIN TE_Product p1 WITH(NOLOCK) ON d1.ProductID=p1.ID
            WHERE d1.OutboundBillID=a1.ID
            AND ( p1.Name LIKE '%'+ #{ProductName} +'%' OR p1.Alias LIKE '%'+ #{ProductName} +'%'
            OR p1.NameFullPinYin LIKE '%'+ #{ProductName} +'%' OR p1.NameAbbrPinYin LIKE '%'+ #{ProductName} +'%'
            OR p1.AliasFullPinYin LIKE '%'+ #{ProductName} +'%' OR p1.AliasAbbrPinYin LIKE '%'+ #{ProductName} +'%' )
            )
        </if>
        UNION ALL
        SELECT
        a1.ID,
        convert(varchar(20),a1.CreatedOn,120) CreatedOn,
        convert(varchar(20),a1.OutDate,120) OutDate,
        a1.EntityID,
        a2.EntityName,
        '' Remark ,
        a3.Name CreatedByName,
        '要货退货出库' InventoryType
        FROM
        TE_InventoryRefundOutbound a1 WITH(NOLOCK)
        LEFT JOIN TK_Entity a2 WITH(NOLOCK) ON a1.EntityID= a2.ID
        LEFT JOIN TK_Employee a3 WITH(NOLOCK) ON a1.CreatedBy= a3.ID
        WHERE 1=1
        <if test="ProductName != null and ProductName != ''">
            AND EXISTS (
            SELECT d1.ID FROM TE_InventoryRefundOutboundDetail d1 WITH(NOLOCK)
            LEFT JOIN TE_Product p1 WITH(NOLOCK) ON d1.ProductID=p1.ID
            WHERE d1.RefundOutboundBillID=a1.ID
            AND ( p1.Name LIKE '%'+ #{ProductName} +'%' OR p1.Alias LIKE '%'+ #{ProductName} +'%'
            OR p1.NameFullPinYin LIKE '%'+ #{ProductName} +'%' OR p1.NameAbbrPinYin LIKE '%'+ #{ProductName} +'%'
            OR p1.AliasFullPinYin LIKE '%'+ #{ProductName} +'%' OR p1.AliasAbbrPinYin LIKE '%'+ #{ProductName} +'%' )
            )
        </if>
        UNION ALL
        SELECT
        a1.ID,
        convert(varchar(20),a1.CreatedOn,120) CreatedOn,
        convert(varchar(20),a1.OutDate,120) OutDate,
        a1.EntityID,
        a2.EntityName,
        '' Remark ,
        a3.Name CreatedByName,
        '采购退货' InventoryType
        FROM
        TE_PurchaseRefund a1 WITH(NOLOCK)
        LEFT JOIN TK_Entity a2 WITH(NOLOCK) ON a1.EntityID= a2.ID
        LEFT JOIN TK_Employee a3 WITH(NOLOCK) ON a1.CreatedBy= a3.ID
        WHERE 1=1
        <if test="ProductName != null and ProductName != ''">
            AND EXISTS (
            SELECT d1.ID FROM TE_PurchaseRefundDetail d1 WITH(NOLOCK)
            LEFT JOIN TE_Product p1 WITH(NOLOCK) ON d1.ProductID=p1.ID
            WHERE d1.PurchaseRefundID=a1.ID
            AND ( p1.Name LIKE '%'+ #{ProductName} +'%' OR p1.Alias LIKE '%'+ #{ProductName} +'%'
            OR p1.NameFullPinYin LIKE '%'+ #{ProductName} +'%' OR p1.NameAbbrPinYin LIKE '%'+ #{ProductName} +'%'
            OR p1.AliasFullPinYin LIKE '%'+ #{ProductName} +'%' OR p1.AliasAbbrPinYin LIKE '%'+ #{ProductName} +'%' )
            )
        </if>
        UNION ALL
        SELECT
        a1.ID,
        convert(varchar(20),a1.CreatedOn,120) CreatedOn,
        convert(varchar(20),a1.OutDate,120) OutDate,
        a1.EntityID,
        a2.EntityName,
        a1.Remark ,
        a3.Name CreatedByName,
        '消耗出库' InventoryType
        FROM
        TE_InventoryTreatOutbound a1 WITH(NOLOCK)
        LEFT JOIN TK_Entity a2 WITH(NOLOCK) ON a1.EntityID= a2.ID
        LEFT JOIN TK_Employee a3 WITH(NOLOCK) ON a1.CreatedBy= a3.ID
        WHERE 1=1
        <if test="ProductName != null and ProductName != ''">
            AND EXISTS (
                SELECT 1
                FROM(SELECT d1.PRODUCTID
                     FROM TE_InventoryTreatProjectOutboundDetail d1 WITH(NOLOCK)
                     WHERE d1.InventoryTreatOutboundID = a1.ID
                     UNION ALL
                     SELECT d2.PRODUCTID
                     FROM TE_InventoryTreatSavingCardOutboundDetail d2 WITH(NOLOCK)
                     WHERE d2.InventoryTreatOutboundID = a1.ID
                     UNION ALL
                     SELECT d3.PRODUCTID
                     FROM TE_InventoryTreatTimeCardOutboundDetail d3 WITH(NOLOCK)
                     WHERE d3.InventoryTreatOutboundID = a1.ID
                     UNION ALL
                     SELECT d4.PRODUCTID
                     FROM TE_InventoryTreatGeneralCardOutboundDetail d4 WITH(NOLOCK)
                     WHERE d4.InventoryTreatOutboundID = a1.ID) T
                LEFT JOIN TE_Product p1 ON p1.ID = T.ProductID
                WHERE p1.Name LIKE '%'+ #{ProductName} +'%'
                   OR p1.Alias LIKE '%'+ #{ProductName} +'%'
                   OR p1.NameFullPinYin LIKE '%'+ #{ProductName} +'%'
                   OR p1.NameAbbrPinYin LIKE '%'+ #{ProductName} +'%'
                   OR p1.AliasFullPinYin LIKE '%'+ #{ProductName} +'%'
                   OR p1.AliasAbbrPinYin LIKE '%'+ #{ProductName} +'%'
            )
        </if>
        ) a1
        LEFT JOIN TB_EmployeeEntityPermission a2 WITH(NOLOCK) ON a1.EntityID=a2.EntityID
        WHERE a2.EmployeeID = #{EmployeeID}
        <if test="ID != null and ID != ''">
            AND a1.ID=#{ID}
        </if>
        <if test="EntityID != null and EntityID != ''">
            AND a1.EntityID = #{EntityID}
        </if>
        <if test="InventoryType != null and InventoryType != ''">
            AND a1.InventoryType = #{InventoryType}
        </if>
        <if test="StartDate != null and StartDate != ''and EndDate != null and EndDate != ''">
            AND a1.CreatedOn >= #{StartDate} AND a1.CreatedOn &lt; DATEADD(DAY,1,#{EndDate})
        </if>
        ORDER BY a1.CreatedOn DESC,a1.ID DESC
    </select>

    <select id="inventoryProductOutboundInfo" resultMap="inventoryProductOutboundInfoOutputForm">
        SELECT a1.ID,
               convert(varchar (20), a1.CreatedOn, 120) CreatedOn,
               convert(varchar (20), a1.OutDate, 120)   OutDate,
               a1.EntityID,
               a2.EntityName,
               CASE a1.OutType
                   WHEN '10' THEN '领料出库'
                   WHEN '20' THEN '报损出库'
                   WHEN '40' THEN '销售出库'
                   ELSE '其他出库'
                   END                                  InventoryType,
               a1.Remark,
               a3.Name                                  CreatedByName,
               a4.ID                                    OutboundDetailBillID,
               a4.ProductID,
               a5.Name                                  ProductName,
               a4.UnitID,
               a6.Name                                  UnitName,
               a4.Quantity,
               a4.MinimumUnitID,
               a7.Name                                  MinimumUnitName,
               a4.MinimumUnitQuantity,
               a4.StockQuantity,
               a4.StockQuantity - a4.Quantity           BeforeStockQuantity,
               a5.Specification,
               a5.Alias,
               a8.Name                                  PCategoryName
        FROM
            dbo.TE_InventoryProductOutbound a1
        WITH (NOLOCK)
            LEFT JOIN TK_Entity a2
        WITH (NOLOCK)
        ON a1.EntityID= a2.ID
            LEFT JOIN TK_Employee a3
        WITH (NOLOCK)
        ON a1.CreatedBy= a3.ID
            LEFT JOIN TE_InventoryProductOutboundDetail a4
        WITH ( NOLOCK )
        ON a1.ID= a4.OutboundBillID
            LEFT JOIN TE_Product a5
        WITH ( NOLOCK )
        ON a4.ProductID= a5.ID
            LEFT JOIN TE_Unit a6
        WITH ( NOLOCK )
        ON a4.UnitID= a6.ID
            LEFT JOIN TE_Unit a7
        WITH ( NOLOCK )
        ON a4.MinimumUnitID= a7.ID
            LEFT JOIN TE_ProductCategory a8
        WITH (NOLOCK)
        on a5.PCategoryID=a8.ID
        WHERE
            a1.ID=#{ID}
        ORDER BY a4.ID ASC
    </select>

    <select id="inventoryAllocationOutboundInfo" resultMap="inventoryOtherOutboundInfoOutputForm">
        SELECT a1.ID,
               convert(varchar (20), a1.CreatedOn, 120) CreatedOn,
               convert(varchar (20), a1.OutDate, 120)   OutDate,
               a1.EntityID,
               a2.EntityName,
               '调拨出库'                                   InventoryType,
               a3.Name                                  CreatedByName,
               a4.ID                                    OutboundDetailBillID,
               a4.ProductID,
               a5.Name                                  ProductName,
               a4.UnitID,
               a6.Name                                  UnitName,
               a4.MinimumUnitID,
               a7.Name                                  MinimumUnitName,
               a4.MinimumUnitQuantity,
               a4.Quantity,
               a4.StockQuantity,
               a5.Specification,
               a5.Alias,
               a8.Name                                  PCategoryName,
               a1.Remark
        FROM TE_InventoryAllocationOutbound a1 WITH(NOLOCK)
        LEFT JOIN TK_Entity a2
        WITH (NOLOCK)
        ON a1.EntityID= a2.ID
            LEFT JOIN TK_Employee a3
        WITH (NOLOCK)
        ON a1.CreatedBy= a3.ID
            LEFT JOIN TE_InventoryAllocationOutboundDetail a4
        WITH ( NOLOCK )
        ON a1.ID= a4.OutboundBillID
            LEFT JOIN TE_Product a5
        WITH ( NOLOCK )
        ON a4.ProductID= a5.ID
            LEFT JOIN TE_Unit a6
        WITH ( NOLOCK )
        ON a4.UnitID= a6.ID
            LEFT JOIN TE_Unit a7
        WITH ( NOLOCK )
        ON a4.MinimumUnitID= a7.ID
            LEFT JOIN TE_ProductCategory a8
        WITH (NOLOCK)
        on a5.PCategoryID=a8.ID
        WHERE
            a1.ID=#{ID}
        ORDER BY a4.ID ASC
    </select>


    <select id="inventoryOutboundInfo" resultMap="inventoryOtherOutboundInfoOutputForm">
        SELECT a1.ID,
               convert(varchar (20), a1.CreatedOn, 120) CreatedOn,
               convert(varchar (20), a1.OutDate, 120)   OutDate,
               a1.EntityID,
               a2.EntityName,
               '配送出库'                                   InventoryType,
               a3.Name                                  CreatedByName,
               a4.ID                                    OutboundDetailBillID,
               a4.ProductID,
               a5.Name                                  ProductName,
               a4.UnitID,
               a6.Name                                  UnitName,
               a4.Quantity,
               a4.MinimumUnitID,
               a7.Name                                  MinimumUnitName,
               a4.MinimumUnitQuantity,
               a4.StockQuantity,
               a5.Specification,
               a5.Alias,
               a8.Name                                  PCategoryName,
               a1.Remark
        FROM TE_InventoryOutbound a1 WITH(NOLOCK)
        LEFT JOIN TK_Entity a2
        WITH (NOLOCK)
        ON a1.EntityID= a2.ID
            LEFT JOIN TK_Employee a3
        WITH (NOLOCK)
        ON a1.CreatedBy= a3.ID
            LEFT JOIN TE_InventoryOutboundDetail a4
        WITH ( NOLOCK )
        ON a1.ID= a4.OutboundBillID
            LEFT JOIN TE_Product a5
        WITH ( NOLOCK )
        ON a4.ProductID= a5.ID
            LEFT JOIN TE_Unit a6
        WITH ( NOLOCK )
        ON a4.UnitID= a6.ID
            LEFT JOIN TE_Unit a7
        WITH ( NOLOCK )
        ON a4.MinimumUnitID= a7.ID
            LEFT JOIN TE_ProductCategory a8
        WITH (NOLOCK)
        on a5.PCategoryID=a8.ID
        WHERE
            a1.ID=#{ID}
        ORDER BY a4.ID ASC
    </select>

    <select id="inventoryRefundOutboundInfo" resultMap="inventoryOtherOutboundInfoOutputForm">
        SELECT a1.ID,
               convert(varchar (20), a1.CreatedOn, 120) CreatedOn,
               convert(varchar (20), a1.OutDate, 120)   OutDate,
               a1.EntityID,
               a2.EntityName,
               '要货退货出库'                                 InventoryType,
               a3.Name                                  CreatedByName,
               a4.ID                                    OutboundDetailBillID,
               a4.ProductID,
               a5.Name                                  ProductName,
               a4.UnitID,
               a6.Name                                  UnitName,
               a4.Quantity,
               a4.MinimumUnitID,
               a7.Name                                  MinimumUnitName,
               a4.MinimumUnitQuantity,
               a4.StockQuantity,
               a5.Specification,
               a5.Alias,
               a8.Name                                  PCategoryName,
               a1.Remark
        FROM TE_InventoryRefundOutbound a1 WITH(NOLOCK)
        LEFT JOIN TK_Entity a2
        WITH (NOLOCK)
        ON a1.EntityID= a2.ID
            LEFT JOIN TK_Employee a3
        WITH (NOLOCK)
        ON a1.CreatedBy= a3.ID
            LEFT JOIN TE_InventoryRefundOutboundDetail a4
        WITH ( NOLOCK )
        ON a1.ID= a4.RefundOutboundBillID
            LEFT JOIN TE_Product a5
        WITH ( NOLOCK )
        ON a4.ProductID= a5.ID
            LEFT JOIN TE_Unit a6
        WITH ( NOLOCK )
        ON a4.UnitID= a6.ID
            LEFT JOIN TE_Unit a7
        WITH ( NOLOCK )
        ON a4.MinimumUnitID= a7.ID
            LEFT JOIN TE_ProductCategory a8
        WITH (NOLOCK)
        on a5.PCategoryID=a8.ID
        WHERE
            a1.ID=#{ID}
        ORDER BY a4.ID ASC
    </select>

    <select id="PurchaseRefundInfo" resultMap="inventoryOtherOutboundInfoOutputForm">
        SELECT a1.ID,
               convert(varchar (20), a1.CreatedOn, 120) CreatedOn,
               convert(varchar (20), a1.OutDate, 120)   OutDate,
               a1.EntityID,
               a2.EntityName,
               '采购退货出库'                                 InventoryType,
               a3.Name                                  CreatedByName,
               a4.ID                                    OutboundDetailBillID,
               a4.ProductID,
               a5.Name                                  ProductName,
               a4.UnitID,
               a6.Name                                  UnitName,
               a4.Quantity,
               a4.MinimumUnitID,
               a7.Name                                  MinimumUnitName,
               a4.MinimumUnitQuantity,
               a4.StockQuantity,
               a5.Specification,
               a5.Alias,
               a8.Name                                  PCategoryName,
               a1.Remark
        FROM TE_PurchaseRefund a1 WITH(NOLOCK)
        LEFT JOIN TK_Entity a2
        WITH (NOLOCK)
        ON a1.EntityID= a2.ID
            LEFT JOIN TK_Employee a3
        WITH (NOLOCK)
        ON a1.CreatedBy= a3.ID
            LEFT JOIN TE_PurchaseRefundDetail a4
        WITH ( NOLOCK )
        ON a1.ID= a4.PurchaseRefundID
            LEFT JOIN TE_Product a5
        WITH ( NOLOCK )
        ON a4.ProductID= a5.ID
            LEFT JOIN TE_Unit a6
        WITH ( NOLOCK )
        ON a4.UnitID= a6.ID
            LEFT JOIN TE_Unit a7
        WITH ( NOLOCK )
        ON a4.MinimumUnitID= a7.ID
            LEFT JOIN TE_ProductCategory a8
        WITH (NOLOCK)
        on a5.PCategoryID=a8.ID
        WHERE
            a1.ID=#{ID}
        ORDER BY a4.ID ASC
    </select>

    <select id="inventoryTreatOutboundInfo" resultMap="inventoryOtherOutboundInfoOutputForm">
        SELECT a1.ID,
               convert(varchar (20), a1.CreatedOn, 120) CreatedOn,
               convert(varchar (20), a1.OutDate, 120)   OutDate,
               a1.EntityID,
               a2.EntityName,
               '消耗出库'                                 InventoryType,
               a3.Name                                  CreatedByName,
               a4.ID                                    OutboundDetailBillID,
               a4.ProductID,
               a5.Name                                  ProductName,
               a4.UnitID,
               a6.Name                                  UnitName,
               a4.MinimumUnitID,
               a7.Name                                  MinimumUnitName,
               a4.MinimumUnitQuantity,
               a4.Quantity,
               a4.StockQuantity,
               a5.Specification,
               a5.Alias,
               a8.Name                                  PCategoryName,
               a1.Remark
        FROM TE_InventoryTreatOutbound a1 WITH(NOLOCK)
                 LEFT JOIN TK_Entity a2 WITH (NOLOCK) ON a1.EntityID= a2.ID
                 LEFT JOIN TK_Employee a3 WITH (NOLOCK) ON a1.CreatedBy= a3.ID
                 LEFT JOIN (SELECT d1.ID, d1.InventoryTreatOutboundID, d1.ProductID, d1.UnitID, d1.Quantity, d1.MinimumUnitID, d1.MinimumUnitQuantity, d1.StockQuantity
                            FROM TE_InventoryTreatProjectOutboundDetail d1 WITH (NOLOCK)
                            UNION ALL
                            SELECT d2.ID, d2.InventoryTreatOutboundID, d2.ProductID, d2.UnitID, d2.Quantity, d2.MinimumUnitID, d2.MinimumUnitQuantity, d2.StockQuantity
                            FROM TE_InventoryTreatSavingCardOutboundDetail d2 WITH (NOLOCK)
                            UNION ALL
                            SELECT d3.ID, d3.InventoryTreatOutboundID, d3.ProductID, d3.UnitID, d3.Quantity, d3.MinimumUnitID, d3.MinimumUnitQuantity, d3.StockQuantity
                            FROM TE_InventoryTreatTimeCardOutboundDetail d3 WITH (NOLOCK)
                            UNION ALL
                            SELECT d4.ID, d4.InventoryTreatOutboundID, d4.ProductID, d4.UnitID, d4.Quantity, d4.MinimumUnitID, d4.MinimumUnitQuantity, d4.StockQuantity
                            FROM TE_InventoryTreatGeneralCardOutboundDetail d4 WITH (NOLOCK)
        ) a4 ON a1.ID= a4.InventoryTreatOutboundID
                 LEFT JOIN TE_Product a5 WITH ( NOLOCK ) ON a4.ProductID= a5.ID
                 LEFT JOIN TE_Unit a6 WITH ( NOLOCK ) ON a4.UnitID= a6.ID
                 LEFT JOIN TE_Unit a7 WITH ( NOLOCK ) ON a4.MinimumUnitID= a7.ID
                 LEFT JOIN TE_ProductCategory a8 WITH (NOLOCK) ON a5.PCategoryID=a8.ID
        WHERE a1.ID=#{ID}
        ORDER BY a4.ID ASC
    </select>

    <update id="updateRemark">
        UPDATE TE_InventoryOutbound SET Remark=#{Remark} WHERE ID=#{ID}
    </update>
</mapper>