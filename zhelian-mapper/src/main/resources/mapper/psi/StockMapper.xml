<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.psi.StockMapper">

    <resultMap id="productMap" type="com.zhelian.model.psi.form.output.EntityStockOutputForm">
        <id column="ID" property="ID" jdbcType="VARCHAR"/>
        <result column="ProductName" property="ProductName" jdbcType="VARCHAR"/>
        <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
        <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
        <result column="Specification" property="Specification" jdbcType="VARCHAR"/>
        <result column="PCategoryName" property="PCategoryName" jdbcType="VARCHAR"/>
        <result column="DeliveryPrice" property="DeliveryPrice" jdbcType="DECIMAL"/>
        <result column="Price" property="Price" jdbcType="DECIMAL"/>
        <result column="IsLock" property="IsLock" jdbcType="BOOLEAN"/>
        <collection property="Unit" ofType="com.zhelian.model.psi.form.output.EntityStockProductUnitOutputForm"
                    select="getProductUnit" column="ID">
        </collection>
    </resultMap>

    <resultMap id="unitMap" type="com.zhelian.model.psi.form.output.EntityStockProductUnitOutputForm">
        <id column="UnitID" property="UnitID" jdbcType="INTEGER"/>
        <result column="UnitName" property="UnitName" jdbcType="VARCHAR"/>
        <result column="Amount" property="Amount" jdbcType="DECIMAL"/>
        <result column="IsMinimumUnit" property="IsMinimumUnit" jdbcType="BIT"/>
        <result column="IsDefautSendReceive" property="IsDefautSendReceive" jdbcType="BIT"/>
    </resultMap>

    <insert id="insertStockEntity" parameterType="com.zhelian.model.psi.entity.StockEntity">
        INSERT INTO dbo.TE_Stock (EntityID, ProductID, Quantity)
        VALUES
            (#{EntityID}, #{ProductID}, #{Quantity})
    </insert>

    <update id="updateStockEntity" parameterType="com.zhelian.model.psi.entity.StockEntity">
        UPDATE dbo.TE_Stock
        SET Quantity = #{Quantity}
        WHERE
            EntityID =#{EntityID}
          AND ProductID =#{ProductID}
    </update>

    <select id="stockInfo" resultType="com.zhelian.model.psi.entity.StockEntity"
            parameterType="com.zhelian.model.psi.entity.StockEntity">
        SELECT
            EntityID, ProductID, Quantity
        FROM
            TE_Stock a1
        WITH (NOLOCK)
        WHERE
            EntityID =#{EntityID}
          AND ProductID =#{ProductID}
    </select>

    <select id="stockList" resultType="com.zhelian.model.psi.form.output.StockOutputForm">
        SELECT
        a1.ID ProductID,
        a1.Name ProductName,
        a1.Alias,
        a2.UnitID,
        a3.Name UnitName,
        SUM(ISNULL(s1.Quantity,0)) Quantity,
        a1.Specification,
        a8.Name PCategoryName
        FROM
        TE_Product a1 WITH(NOLOCK)
        LEFT JOIN TE_Stock s1 WITH(NOLOCK) ON s1.ProductID= a1.ID
        AND EXISTS (SELECT 1 FROM TB_EmployeeEntityPermission a4 WITH(NOLOCK) WHERE s1.EntityID = a4.EntityID AND
        a4.EmployeeID = #{EmployeeID}
        <if test="EntityID != null and EntityID != ''">
            AND s1.EntityID=#{EntityID}
        </if>)
        LEFT JOIN TE_ProductBarCode a2 WITH(NOLOCK) ON a1.ID= a2.ProductID AND a2.IsMinimumUnit= 1
        LEFT JOIN TE_Unit a3 WITH(NOLOCK) ON a2.UnitID= a3.ID
        LEFT JOIN TE_ProductCategory a8 WITH(NOLOCK) on a1.PCategoryID=a8.ID
        <where>
            <if test="ProductName != null and ProductName != ''">
                AND ( a1.Name LIKE '%'+ #{ProductName} +'%' OR a1.Alias LIKE '%'+ #{ProductName} +'%'
                OR a1.NameFullPinYin LIKE '%'+ #{ProductName} +'%' OR a1.NameAbbrPinYin LIKE '%'+ #{ProductName} +'%'
                OR a1.AliasFullPinYin LIKE '%'+ #{ProductName} +'%' OR a1.AliasAbbrPinYin LIKE '%'+ #{ProductName} +'%' )
            </if>
            <if test="PCategoryID != null and PCategoryID != ''">
                AND a1.PCategoryID=#{PCategoryID}
            </if>
            <if test="IsDisplayInvalid != null and IsDisplayInvalid == false ">
                AND a1.Active=1
            </if>
        </where>
        GROUP BY a1.ID,a1.Name,a1.Alias,a2.UnitID,a3.Name,a1.Specification,a8.Name
        <if test="IsDisplayZero != null and IsDisplayZero == false ">
            HAVING SUM(ISNULL(s1.Quantity,0))>0
        </if>

        ORDER BY a1.Name
    </select>
    <select id="stockListEasyExcel" resultType="com.zhelian.model.psi.form.output.StockEasyExcelOutputForm">
        SELECT
        a1.Name productName,
        a8.Name pcategoryName,
        a1.Specification specification,
        a3.Name unitName,
        SUM(ISNULL(s1.Quantity,0)) quantity
        FROM
        TE_Product a1 WITH(NOLOCK)
        LEFT JOIN TE_Stock s1 WITH(NOLOCK) ON s1.ProductID= a1.ID
        AND EXISTS (SELECT 1 FROM TB_EmployeeEntityPermission a4 WITH(NOLOCK) WHERE s1.EntityID = a4.EntityID AND
        a4.EmployeeID = #{EmployeeID}
        <if test="EntityID != null and EntityID != ''">
            AND s1.EntityID=#{EntityID}
        </if>)
        LEFT JOIN TE_ProductBarCode a2 WITH(NOLOCK) ON a1.ID= a2.ProductID AND a2.IsMinimumUnit= 1
        LEFT JOIN TE_Unit a3 WITH(NOLOCK) ON a2.UnitID= a3.ID
        LEFT JOIN TE_ProductCategory a8 WITH(NOLOCK) on a1.PCategoryID=a8.ID
        <where>
            <if test="ProductName != null and ProductName != ''">
                AND ( a1.Name LIKE '%'+ #{ProductName} +'%' OR a1.Alias LIKE '%'+ #{ProductName} +'%'
                OR a1.NameFullPinYin LIKE '%'+ #{ProductName} +'%' OR a1.NameAbbrPinYin LIKE '%'+ #{ProductName} +'%'
                OR a1.AliasFullPinYin LIKE '%'+ #{ProductName} +'%' OR a1.AliasAbbrPinYin LIKE '%'+ #{ProductName} +'%' )
            </if>
            <if test="PCategoryID != null and PCategoryID != ''">
                AND a1.PCategoryID=#{PCategoryID}
            </if>
            <if test="IsDisplayInvalid != null and IsDisplayInvalid == false ">
                AND a1.Active=1
            </if>
        </where>
        GROUP BY a1.ID,a1.Name,a1.Alias,a2.UnitID,a3.Name,a1.Specification,a8.Name
        <if test="IsDisplayZero != null and IsDisplayZero == false ">
            HAVING SUM(ISNULL(s1.Quantity,0))>0
        </if>
        ORDER BY a1.Name
    </select>

    <select id="productEntity" resultType="com.zhelian.model.psi.form.output.ProductEntityStockOutputForm">
        SELECT
        a1.ID EntityID,ISNULL(s1.Quantity,0) Quantity,a1.EntityName
        FROM
        TK_Entity a1 WITH(NOLOCK)
        LEFT JOIN TE_Stock s1 WITH(NOLOCK) ON s1.EntityID=a1.ID AND s1.ProductID=#{ProductID}
        LEFT JOIN TB_EmployeeEntityPermission a2 WITH(NOLOCK) ON a1.ID=a2.EntityID
        WHERE a2.EmployeeID = #{EmployeeID}
        AND a1.IsWarehouse=1
        AND a1.Active=1
        <if test="IsDisplayZero != null and IsDisplayZero == false ">
            AND s1.Quantity>0
        </if>
        ORDER BY a1.Sequence DESC
    </select>

    <select id="getProductUnit" resultMap="unitMap">
        SELECT a2.UnitID,
               a3.Name UnitName,
               a2.Amount,
               a2.IsMinimumUnit,
               a2.IsDefautSendReceive
        FROM TE_ProductBarCode a2 WITH(NOLOCK)
        LEFT JOIN TE_Unit a3
        WITH (NOLOCK)
        ON a2.UnitID=a3.ID
        WHERE a2.ProductID=#{ID}
    </select>

    <select id="entityProductList" resultMap="productMap">
        SELECT
        a1.ID,
        a1.Name ProductName,
        a1.Alias,
        s1.Quantity,
        a1.Specification,
        a8.Name PCategoryName,
        CASE WHEN a7.ProductID IS NULL THEN 0
        ELSE 1
        END IsLock
        FROM
        TE_Stock s1 WITH(NOLOCK)
        LEFT JOIN TE_Product a1 WITH(NOLOCK) ON s1.ProductID= a1.ID
        LEFT JOIN (
        SELECT a5.ProductID FROM TE_InventoryCheckDetail a5 WITH(NOLOCK)
        JOIN TE_InventoryCheck a6 WITH(NOLOCK) ON a5.InventoryCheckID=a6.ID
        WHERE a6.BillStatus='10' AND a6.EntityID=#{EntityID}
        GROUP BY a5.ProductID
        )a7 ON a7.ProductID=a1.ID
        LEFT JOIN TE_ProductCategory a8 WITH(NOLOCK) on a1.PCategoryID=a8.ID
        WHERE s1.EntityID=#{EntityID}
        AND a1.Active=1
        <if test="ProductName != null and ProductName != ''">
            AND ( a1.Name LIKE '%'+ #{ProductName} +'%' OR a1.Alias LIKE '%'+ #{ProductName} +'%'
            OR a1.NameFullPinYin LIKE '%'+ #{ProductName} +'%' OR a1.NameAbbrPinYin LIKE '%'+ #{ProductName} +'%'
            OR a1.AliasFullPinYin LIKE '%'+ #{ProductName} +'%' OR a1.AliasAbbrPinYin LIKE '%'+ #{ProductName} +'%' )
        </if>
        <if test="PCategoryID != null and PCategoryID != ''">
            AND a1.PCategoryID=#{PCategoryID}
        </if>
        ORDER BY a1.Name,a1.ID DESC
    </select>

    <select id="entityProductListAll" resultMap="productMap">
        SELECT
        a1.ID,
        a1.Name ProductName,
        a1.Alias,
        isnull(s1.Quantity,0) Quantity,
        a8.Name PCategoryName,
        a1.Specification,
        CASE WHEN a7.ProductID IS NULL THEN 0
        ELSE 1
        END IsLock,
        a1.DeliveryPrice,
        a9.Price
        FROM
        TE_Product a1 WITH(NOLOCK)
        LEFT JOIN TE_Stock s1 WITH(NOLOCK) ON s1.ProductID= a1.ID AND s1.EntityID=#{EntityID}
        LEFT JOIN (
        SELECT a5.ProductID FROM TE_InventoryCheckDetail a5 WITH(NOLOCK)
        JOIN TE_InventoryCheck a6 WITH(NOLOCK) ON a5.InventoryCheckID=a6.ID
        WHERE a6.BillStatus='10' AND a6.EntityID=#{EntityID}
        GROUP BY a5.ProductID
        )a7 ON a7.ProductID=a1.ID
        LEFT JOIN TE_ProductCategory a8 WITH(NOLOCK) on a1.PCategoryID=a8.ID
        LEFT JOIN TB_Product a9 WITH(NOLOCK) on a1.ID=a9.ProductID
        WHERE
        a1.Active=1
        <if test="ProductName != null and ProductName != ''">
            AND ( a1.Name LIKE '%'+ #{ProductName} +'%' OR a1.Alias LIKE '%'+ #{ProductName} +'%'
            OR a1.NameFullPinYin LIKE '%'+ #{ProductName} +'%' OR a1.NameAbbrPinYin LIKE '%'+ #{ProductName} +'%'
            OR a1.AliasFullPinYin LIKE '%'+ #{ProductName} +'%' OR a1.AliasAbbrPinYin LIKE '%'+ #{ProductName} +'%' )
        </if>
        <if test="PCategoryID != null and PCategoryID != ''">
            AND a1.PCategoryID=#{PCategoryID}
        </if>
        ORDER BY a1.Name,a1.ID DESC
    </select>

    <select id="entityProductDeliveryPrice" resultMap="productMap">
        SELECT
        a1.ID,
        a1.Name ProductName,
        a1.Alias,
        isnull(a2.Quantity,0) Quantity,
        a6.Name PCategoryName,
        a1.Specification,
        CASE WHEN a5.ProductID IS NULL THEN 0
        ELSE 1
        END IsLock,
        CASE
        WHEN a9.ProductDispatchPriceSchemeID is not null THEN a9.DispatchPrice
        WHEN a10.ProductDispatchPriceSchemeID is not null THEN CAST(a10.Discount * a7.Price as decimal(18, 2))
        WHEN a11.ProductDispatchPriceSchemeID is not null THEN CAST(a11.Discount * a7.Price as decimal(18, 2))
        WHEN (SELECT Discount FROM TE_ProductDispatchPriceSchemeEntity aa JOIN TE_ProductDispatchPriceScheme bb ON
        aa.ProductDispatchPriceSchemeID=bb.ID WHERE aa.EntityID=#{EntityID} AND bb.Active=1) is not null THEN (SELECT
        CAST(Discount * a7.Price as decimal(18, 2)) FROM TE_ProductDispatchPriceScheme a1e LEFT JOIN
        TE_ProductDispatchPriceSchemeEntity tt ON a1e.ID=tt.ProductDispatchPriceSchemeID WHERE tt.EntityID=#{EntityID}
        AND a1e.Active=1)
        ELSE a1.DeliveryPrice
        END DeliveryPrice,
        a7.Price
        FROM
        TE_Product a1 WITH(NOLOCK)
        LEFT JOIN TE_Stock a2 WITH(NOLOCK) ON a2.ProductID= a1.ID AND a2.EntityID=#{EntityID}
        LEFT JOIN (
        SELECT a3.ProductID FROM TE_InventoryCheckDetail a3 WITH(NOLOCK)
        JOIN TE_InventoryCheck a4 WITH(NOLOCK) ON a3.InventoryCheckID=a4.ID
        WHERE a4.BillStatus='10' AND a4.EntityID=#{EntityID}
        GROUP BY a3.ProductID
        )a5 ON a5.ProductID=a1.ID
        LEFT JOIN TE_ProductCategory a6 WITH(NOLOCK) on a1.PCategoryID=a6.ID
        LEFT JOIN TB_Product a7 WITH(NOLOCK) on a1.ID=a7.ProductID
        LEFT JOIN TE_ProductCategory a8 WITH(NOLOCK) on a1.PCategoryID=a8.ID
        LEFT JOIN TE_ProductDispatchPrice a9 WITH(NOLOCK) ON a9.ProductID= a1.ID AND
        a9.ProductDispatchPriceSchemeID=(SELECT ProductDispatchPriceSchemeID FROM TE_ProductDispatchPriceSchemeEntity aa
        JOIN TE_ProductDispatchPriceScheme bb ON aa.ProductDispatchPriceSchemeID=bb.ID WHERE aa.EntityID=#{EntityID} AND
        bb.Active=1 )
        LEFT JOIN TE_ProductCategoryDispatchPrice a10 WITH(NOLOCK) ON a10.ProductCategoryID=a1.PCategoryID AND
        a10.ProductDispatchPriceSchemeID=(SELECT ProductDispatchPriceSchemeID FROM TE_ProductDispatchPriceSchemeEntity
        aa JOIN TE_ProductDispatchPriceScheme bb ON aa.ProductDispatchPriceSchemeID=bb.ID WHERE aa.EntityID=#{EntityID}
        AND bb.Active=1 )
        LEFT JOIN TE_ProductCategoryDispatchPrice a11 WITH(NOLOCK) ON a11.ProductCategoryID=a8.ParentID AND
        a11.ProductDispatchPriceSchemeID=(SELECT ProductDispatchPriceSchemeID FROM TE_ProductDispatchPriceSchemeEntity
        aa JOIN TE_ProductDispatchPriceScheme bb ON aa.ProductDispatchPriceSchemeID=bb.ID WHERE aa.EntityID=#{EntityID}
        AND bb.Active=1 )
        WHERE
        a1.Active=1
        <if test="ProductName != null and ProductName != ''">
            AND ( a1.Name LIKE '%'+ #{ProductName} +'%' OR a1.Alias LIKE '%'+ #{ProductName} +'%'
            OR a1.NameFullPinYin LIKE '%'+ #{ProductName} +'%' OR a1.NameAbbrPinYin LIKE '%'+ #{ProductName} +'%'
            OR a1.AliasFullPinYin LIKE '%'+ #{ProductName} +'%' OR a1.AliasAbbrPinYin LIKE '%'+ #{ProductName} +'%' )
        </if>
        <if test="PCategoryID != null and PCategoryID != ''">
            AND a1.PCategoryID=#{PCategoryID}
        </if>
        ORDER BY a1.Name,a1.ID DESC
    </select>

    <select id="inventoryCheckProductList"
            resultType="com.zhelian.model.psi.form.output.InventoryCheckProductOutputForm">
        SELECT
        a1.ID,
        a1.Name ProductName,
        a1.Alias,
        isnull(s1.Quantity,0) Quantity,
        a8.Name PCategoryName,
        a1.Specification,
        CASE WHEN a7.ProductID IS NULL THEN 0
        ELSE 1
        END IsLock,
        a2.UnitID,
        a3.Name UnitName
        FROM
        TE_Product a1 WITH(NOLOCK)
        LEFT JOIN TE_ProductBarCode a2 WITH(NOLOCK) ON a1.ID= a2.ProductID AND a2.IsMinimumUnit= 1
        LEFT JOIN TE_Unit a3 WITH(NOLOCK) ON a2.UnitID= a3.ID
        LEFT JOIN TE_Stock s1 WITH(NOLOCK) ON s1.ProductID= a1.ID AND s1.EntityID=#{EntityID}
        LEFT JOIN (
        SELECT a5.ProductID FROM TE_InventoryCheckDetail a5 WITH(NOLOCK)
        JOIN TE_InventoryCheck a6 WITH(NOLOCK) ON a5.InventoryCheckID=a6.ID
        WHERE a6.BillStatus='10' AND a6.EntityID=#{EntityID}
        GROUP BY a5.ProductID
        )a7 ON a7.ProductID=a1.ID
        LEFT JOIN TE_ProductCategory a8 WITH(NOLOCK) on a1.PCategoryID=a8.ID
        LEFT JOIN TE_ProductCategory a9 WITH(NOLOCK) on a9.ID=a8.ParentID
        WHERE
        a1.Active=1
        <if test="ProductName != null and ProductName != ''">
            AND ( a1.Name LIKE '%'+ #{ProductName} +'%' OR a1.Alias LIKE '%'+ #{ProductName} +'%'
            OR a1.NameFullPinYin LIKE '%'+ #{ProductName} +'%' OR a1.NameAbbrPinYin LIKE '%'+ #{ProductName} +'%'
            OR a1.AliasFullPinYin LIKE '%'+ #{ProductName} +'%' OR a1.AliasAbbrPinYin LIKE '%'+ #{ProductName} +'%' )
        </if>
        <if test="PCategoryID != null and PCategoryID != ''">
            AND ( a1.PCategoryID=#{PCategoryID} or a9.ID=#{PCategoryID})
        </if>
        ORDER BY a1.Name,a1.ID DESC
    </select>

    <select id="ProductStock" resultType="com.zhelian.model.psi.form.output.ProductStockOutputForm">
        SELECT
        a1.ID ProductID,
        a1.Name ProductName,
        a2.UnitID,
        a4.Price,
        a3.Name UnitName,
        ISNULL(s1.Quantity,0) Quantity
        FROM TE_Product a1 WITH(NOLOCK)
        LEFT JOIN TB_Product a4 WITH(NOLOCK) ON a4.ProductID= a1.ID
        LEFT JOIN TE_Stock s1 WITH(NOLOCK) ON s1.ProductID= a1.ID AND s1.EntityID=#{EntityID}
        LEFT JOIN TE_ProductBarCode a2 WITH(NOLOCK) ON a1.ID= a2.ProductID AND a2.IsMinimumUnit= 1
        LEFT JOIN TE_Unit a3 WITH(NOLOCK) ON a2.UnitID= a3.ID
        <where>
            <if test="array != null and array != ''">
                AND a1.ID IN
                <foreach collection="array" item="code" open="(" separator="," close=")" index="index">
                    #{code}
                </foreach>
            </if>
            ORDER BY a1.Name,a1.ID DESC
        </where>
    </select>

    <select id="inventoryDetail" resultType="com.zhelian.model.psi.form.output.InventoryDetailOutputForm">
        SELECT a1.ProductID,
        a3.Name ProductName,
        a3.Alias,
        a1.UnitID,
        a4.Name UnitName,
        a1.ID BillID,
        a1.EntityID,
        a5.EntityName,
        a1.InventoryType,
        a1.StockQuantity,
        a1.Quantity,
        a6.Name EmployeeName,
        a3.Specification,
        a7.Name PCategoryName,
        a1.MinimumUnitID,
        a8.Name MinimumUnitName,
        a1.MinimumUnitQuantity,
        convert(varchar(20),a1.BillDate,120) BillDate
        FROM (
        --采购入库
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,'采购入库' InventoryType,a2.CreatedOn
        BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy
        ,a1.MinimumUnitID,a1.ActualMinimumUnitQuantity as MinimumUnitQuantity,a1.ID DetailID
        FROM TE_PurchaseStorageDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_PurchaseStorage a2 WITH(NOLOCK) ON a1.PurchaseStorageID=a2.ID
        UNION ALL
        --门店要货入库单
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,'要货入库' InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy
        ,a1.MinimumUnitID,a1.MinimumUnitQuantity,a1.ID DetailID
        FROM TE_InventoryInboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryInbound a2 WITH(NOLOCK) ON a1.InboundBillID=a2.ID
        UNION ALL
        --门店配送出库单
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,'配送出库' InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy
        ,a1.MinimumUnitID,-a1.MinimumUnitQuantity,a1.ID DetailID
        FROM TE_InventoryOutboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryOutbound a2 WITH(NOLOCK) ON a1.OutboundBillID=a2.ID
        UNION ALL
        --库存调拨入库
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,'调拨入库' InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy
        ,a1.MinimumUnitID,a1.MinimumUnitQuantity,a1.ID DetailID
        FROM TE_InventoryAllocationInboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryAllocationInbound a2 WITH(NOLOCK) ON a1.InboundBillID=a2.ID
        UNION ALL
        --调拨出库
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,'调拨出库' InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy
        ,a1.MinimumUnitID,-a1.MinimumUnitQuantity,a1.ID DetailID
        FROM TE_InventoryAllocationOutboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryAllocationOutbound a2 WITH(NOLOCK) ON a1.OutboundBillID=a2.ID
        UNION ALL
        --其他入库 退货入库
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        CASE WHEN InType='10' THEN '其他入库'
        ELSE '退货入库'
        END InventoryType,
        a2.CreatedOn BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy,
        a1.MinimumUnitID,a1.MinimumUnitQuantity,a1.ID DetailID
        FROM TE_InventoryProductInboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryProductInbound a2 WITH(NOLOCK) ON a1.InboundBillID=a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '消耗取消入库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryTreatCancelProjectInboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryTreatCancelInbound a2 ON a1.InventoryTreatCancelInboundID =a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '消耗取消入库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryTreatCancelSavingCardInboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryTreatCancelInbound a2 ON a1.InventoryTreatCancelInboundID =a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '消耗取消入库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryTreatCancelGeneralCardInboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryTreatCancelInbound a2 ON a1.InventoryTreatCancelInboundID =a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '消耗取消入库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryTreatCancelTimeCardInboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryTreatCancelInbound a2 ON a1.InventoryTreatCancelInboundID =a2.ID

        UNION ALL
        --产品出库
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        CASE OutType WHEN '10' THEN '领料出库'
        WHEN '20' THEN '报损出库'
        WHEN '40' THEN '销售出库'
        ELSE '其他出库'
        END InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,-a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryProductOutboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryProductOutbound a2 ON a1.OutboundBillID=a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '采购退货'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,-a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_PurchaseRefundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_PurchaseRefund a2 ON a1.PurchaseRefundID=a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '要货退货出库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,-a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryRefundOutboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryRefundOutbound a2 ON a1.RefundOutboundBillID=a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '要货退货入库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryRefundInboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryRefundInbound a2 ON a1.RefundInboundBillID=a2.ID
        UNION ALL
        --库存盘点
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        CASE WHEN a1.Quantity=0 THEN '盘平'
        WHEN a1.Quantity>0 THEN '盘盈入库'
        ELSE '盘亏出库'
        END InventoryType ,a2.CreatedOn BillDate,a1.StockQuantity,
        CASE WHEN a1.Quantity=0 THEN 0
        WHEN a1.Quantity>0 THEN a1.Quantity
        ELSE -a1.Quantity
        END Quantity,
        a2.CreatedBy,a1.UnitID
        MinimumUnitID,
        CASE WHEN a1.Quantity=0 THEN 0
        WHEN a1.Quantity>0 THEN a1.Quantity
        ELSE -a1.Quantity
        END MinimumUnitQuantity,
        a1.ID DetailID
        FROM TE_InventoryCheckDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryCheck a2 WITH(NOLOCK) ON a1.InventoryCheckID=a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '消耗出库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,-a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryTreatGeneralCardOutboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryTreatOutbound a2 ON a1.InventoryTreatOutboundID  =a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '消耗出库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,-a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryTreatTimeCardOutboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryTreatOutbound a2 ON a1.InventoryTreatOutboundID  =a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '消耗出库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,-a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryTreatSavingCardOutboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryTreatOutbound a2 ON a1.InventoryTreatOutboundID  =a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '消耗出库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,-a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryTreatProjectOutboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryTreatOutbound a2 ON a1.InventoryTreatOutboundID  =a2.ID
        ) a1
        LEFT JOIN TB_EmployeeEntityPermission a2 WITH(NOLOCK) ON a1.EntityID=a2.EntityID
        LEFT JOIN TE_Product a3 WITH(NOLOCK) ON a1.ProductID=a3.ID
        LEFT JOIN TE_Unit a4 WITH(NOLOCK) ON a1.UnitID=a4.ID
        LEFT JOIN TK_Entity a5 WITH(NOLOCK) ON a1.EntityID=a5.ID
        LEFT JOIN TK_Employee a6 WITH(NOLOCK) ON a1.CreatedBy=a6.ID
        LEFT JOIN TE_ProductCategory a7 WITH(NOLOCK) on a3.PCategoryID=a7.ID
        LEFT JOIN TE_Unit a8 WITH(NOLOCK) ON a1.MinimumUnitID=a8.ID
        WhERE a2.EmployeeID=#{EmployeeID}
        <if test="EntityID != null and EntityID != ''">
            AND a1.EntityID=#{EntityID}
        </if>
        <if test="ProductID != null and ProductID != ''">
            AND a1.ProductID=#{ProductID}
        </if>
        <if test="InventoryType != null and InventoryType != ''">
            AND a1.InventoryType=#{InventoryType}
        </if>
        <if test="PCategoryID != null and PCategoryID != ''">
            AND a3.PCategoryID=#{PCategoryID}
        </if>
        <if test="ProductName != null and ProductName != ''">
            AND ( a3.Name LIKE '%'+ #{ProductName} +'%' OR a3.Alias LIKE '%'+ #{ProductName} +'%'
            OR a3.NameFullPinYin LIKE '%'+ #{ProductName} +'%' OR a3.NameAbbrPinYin LIKE '%'+ #{ProductName} +'%'
            OR a3.AliasFullPinYin LIKE '%'+ #{ProductName} +'%' OR a3.AliasAbbrPinYin LIKE '%'+ #{ProductName} +'%' )
        </if>
        <if test="StartDate != null and StartDate != ''and EndDate != null and EndDate != ''">
            AND a1.BillDate >= #{StartDate} AND a1.BillDate &lt; DATEADD(DAY,1,#{EndDate})
        </if>
        ORDER BY a1.BillDate DESC,a1.DetailID DESC
    </select>

    <select id="inventoryDetailExcel" resultType="com.zhelian.model.psi.form.output.InventoryDetailOutputExcelForm">
        SELECT
        a3.Name ProductName,
        a4.Name UnitName,
        a1.ID BillID,
        a5.EntityName,
        a1.InventoryType,
        a1.Quantity,
        a1.StockQuantity,
        convert(varchar(20),a1.BillDate,120) BillDate,
        a6.Name EmployeeName
        FROM (
        --采购入库
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,'采购入库' InventoryType,a2.CreatedOn
        BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy
        ,a1.MinimumUnitID,a1.MinimumUnitQuantity,a1.ID DetailID
        FROM TE_PurchaseStorageDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_PurchaseStorage a2 WITH(NOLOCK) ON a1.PurchaseStorageID=a2.ID
        UNION ALL
        --门店要货入库单
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,'要货入库' InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy
        ,a1.MinimumUnitID,a1.MinimumUnitQuantity,a1.ID DetailID
        FROM TE_InventoryInboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryInbound a2 WITH(NOLOCK) ON a1.InboundBillID=a2.ID
        UNION ALL
        --门店配送出库单
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,'配送出库' InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy
        ,a1.MinimumUnitID,-a1.MinimumUnitQuantity,a1.ID DetailID
        FROM TE_InventoryOutboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryOutbound a2 WITH(NOLOCK) ON a1.OutboundBillID=a2.ID
        UNION ALL
        --库存调拨入库
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,'调拨入库' InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy
        ,a1.MinimumUnitID,a1.MinimumUnitQuantity,a1.ID DetailID
        FROM TE_InventoryAllocationInboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryAllocationInbound a2 WITH(NOLOCK) ON a1.InboundBillID=a2.ID
        UNION ALL
        --调拨出库
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,'调拨出库' InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy
        ,a1.MinimumUnitID,-a1.MinimumUnitQuantity,a1.ID DetailID
        FROM TE_InventoryAllocationOutboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryAllocationOutbound a2 WITH(NOLOCK) ON a1.OutboundBillID=a2.ID
        UNION ALL
        --其他入库 退货入库
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        CASE WHEN InType='10' THEN '其他入库'
        ELSE '退货入库'
        END InventoryType,
        a2.CreatedOn BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy,
        a1.MinimumUnitID,a1.MinimumUnitQuantity,a1.ID DetailID
        FROM TE_InventoryProductInboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryProductInbound a2 WITH(NOLOCK) ON a1.InboundBillID=a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '消耗取消入库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryTreatCancelProjectInboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryTreatCancelInbound a2 ON a1.InventoryTreatCancelInboundID =a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '消耗取消入库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryTreatCancelSavingCardInboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryTreatCancelInbound a2 ON a1.InventoryTreatCancelInboundID =a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '消耗取消入库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryTreatCancelGeneralCardInboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryTreatCancelInbound a2 ON a1.InventoryTreatCancelInboundID =a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '消耗取消入库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryTreatCancelTimeCardInboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryTreatCancelInbound a2 ON a1.InventoryTreatCancelInboundID =a2.ID

        UNION ALL
        --产品出库
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        CASE OutType WHEN '10' THEN '领料出库'
        WHEN '20' THEN '报损出库'
        WHEN '40' THEN '销售出库'
        ELSE '其他出库'
        END InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,-a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryProductOutboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryProductOutbound a2 ON a1.OutboundBillID=a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '采购退货'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,-a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_PurchaseRefundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_PurchaseRefund a2 ON a1.PurchaseRefundID=a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '要货退货出库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,-a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryRefundOutboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryRefundOutbound a2 ON a1.RefundOutboundBillID=a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '要货退货入库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryRefundInboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryRefundInbound a2 ON a1.RefundInboundBillID=a2.ID
        UNION ALL
        --库存盘点
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        CASE WHEN a1.Quantity=0 THEN '盘平'
        WHEN a1.Quantity>0 THEN '盘盈入库'
        ELSE '盘亏出库'
        END InventoryType ,a2.CreatedOn BillDate,a1.StockQuantity,
        CASE WHEN a1.Quantity=0 THEN 0
        WHEN a1.Quantity>0 THEN a1.Quantity
        ELSE -a1.Quantity
        END Quantity,
        a2.CreatedBy,a1.UnitID
        MinimumUnitID,
        CASE WHEN a1.Quantity=0 THEN 0
        WHEN a1.Quantity>0 THEN a1.Quantity
        ELSE -a1.Quantity
        END MinimumUnitQuantity,
        a1.ID DetailID
        FROM TE_InventoryCheckDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryCheck a2 WITH(NOLOCK) ON a1.InventoryCheckID=a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '消耗出库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,-a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryTreatGeneralCardOutboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryTreatOutbound a2 ON a1.InventoryTreatOutboundID  =a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '消耗出库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,-a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryTreatTimeCardOutboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryTreatOutbound a2 ON a1.InventoryTreatOutboundID  =a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '消耗出库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,-a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryTreatSavingCardOutboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryTreatOutbound a2 ON a1.InventoryTreatOutboundID  =a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '消耗出库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,-a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryTreatProjectOutboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryTreatOutbound a2 ON a1.InventoryTreatOutboundID  =a2.ID
        ) a1
        LEFT JOIN TB_EmployeeEntityPermission a2 WITH(NOLOCK) ON a1.EntityID=a2.EntityID
        LEFT JOIN TE_Product a3 WITH(NOLOCK) ON a1.ProductID=a3.ID
        LEFT JOIN TE_Unit a4 WITH(NOLOCK) ON a1.UnitID=a4.ID
        LEFT JOIN TK_Entity a5 WITH(NOLOCK) ON a1.EntityID=a5.ID
        LEFT JOIN TK_Employee a6 WITH(NOLOCK) ON a1.CreatedBy=a6.ID
        LEFT JOIN TE_ProductCategory a7 WITH(NOLOCK) on a3.PCategoryID=a7.ID
        LEFT JOIN TE_Unit a8 WITH(NOLOCK) ON a1.MinimumUnitID=a8.ID
        WhERE a2.EmployeeID=#{EmployeeID}
        <if test="EntityID != null and EntityID != ''">
            AND a1.EntityID=#{EntityID}
        </if>
        <if test="ProductID != null and ProductID != ''">
            AND a1.ProductID=#{ProductID}
        </if>
        <if test="InventoryType != null and InventoryType != ''">
            AND a1.InventoryType=#{InventoryType}
        </if>
        <if test="PCategoryID != null and PCategoryID != ''">
            AND a3.PCategoryID=#{PCategoryID}
        </if>
        <if test="ProductName != null and ProductName != ''">
            AND ( a3.Name LIKE '%'+ #{ProductName} +'%' OR a3.Alias LIKE '%'+ #{ProductName} +'%'
            OR a3.NameFullPinYin LIKE '%'+ #{ProductName} +'%' OR a3.NameAbbrPinYin LIKE '%'+ #{ProductName} +'%'
            OR a3.AliasFullPinYin LIKE '%'+ #{ProductName} +'%' OR a3.AliasAbbrPinYin LIKE '%'+ #{ProductName} +'%' )
        </if>
        <if test="StartDate != null and StartDate != ''and EndDate != null and EndDate != ''">
            AND a1.BillDate >= #{StartDate} AND a1.BillDate &lt; DATEADD(DAY,1,#{EndDate})
        </if>
        ORDER BY a1.BillDate DESC,a1.DetailID DESC
    </select>

    <select id="inventoryDetailEasyExcel" resultType="com.zhelian.model.psi.form.output.InventoryDetailOutputEasyExcelForm">
        SELECT
        a3.Name productName,
        a4.Name unitName,
        a1.ID billID,
        a5.EntityName entityName,
        a1.InventoryType inventoryType,
        a1.Quantity quantity,
        a1.StockQuantity stockQuantity,
        convert(varchar(20),a1.BillDate,120) billDate,
        a6.Name employeeName
        FROM (
        --采购入库
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,'采购入库' InventoryType,a2.CreatedOn
        BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy
        ,a1.MinimumUnitID,a1.MinimumUnitQuantity,a1.ID DetailID
        FROM TE_PurchaseStorageDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_PurchaseStorage a2 WITH(NOLOCK) ON a1.PurchaseStorageID=a2.ID
        UNION ALL
        --门店要货入库单
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,'要货入库' InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy
        ,a1.MinimumUnitID,a1.MinimumUnitQuantity,a1.ID DetailID
        FROM TE_InventoryInboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryInbound a2 WITH(NOLOCK) ON a1.InboundBillID=a2.ID
        UNION ALL
        --门店配送出库单
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,'配送出库' InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy
        ,a1.MinimumUnitID,-a1.MinimumUnitQuantity,a1.ID DetailID
        FROM TE_InventoryOutboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryOutbound a2 WITH(NOLOCK) ON a1.OutboundBillID=a2.ID
        UNION ALL
        --库存调拨入库
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,'调拨入库' InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy
        ,a1.MinimumUnitID,a1.MinimumUnitQuantity,a1.ID DetailID
        FROM TE_InventoryAllocationInboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryAllocationInbound a2 WITH(NOLOCK) ON a1.InboundBillID=a2.ID
        UNION ALL
        --调拨出库
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,'调拨出库' InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy
        ,a1.MinimumUnitID,-a1.MinimumUnitQuantity,a1.ID DetailID
        FROM TE_InventoryAllocationOutboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryAllocationOutbound a2 WITH(NOLOCK) ON a1.OutboundBillID=a2.ID
        UNION ALL
        --其他入库 退货入库
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        CASE WHEN InType='10' THEN '其他入库'
        ELSE '退货入库'
        END InventoryType,
        a2.CreatedOn BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy,
        a1.MinimumUnitID,a1.MinimumUnitQuantity,a1.ID DetailID
        FROM TE_InventoryProductInboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryProductInbound a2 WITH(NOLOCK) ON a1.InboundBillID=a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '消耗取消入库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryTreatCancelProjectInboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryTreatCancelInbound a2 ON a1.InventoryTreatCancelInboundID =a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '消耗取消入库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryTreatCancelSavingCardInboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryTreatCancelInbound a2 ON a1.InventoryTreatCancelInboundID =a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '消耗取消入库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryTreatCancelGeneralCardInboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryTreatCancelInbound a2 ON a1.InventoryTreatCancelInboundID =a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '消耗取消入库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryTreatCancelTimeCardInboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryTreatCancelInbound a2 ON a1.InventoryTreatCancelInboundID =a2.ID

        UNION ALL
        --产品出库
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        CASE OutType WHEN '10' THEN '领料出库'
        WHEN '20' THEN '报损出库'
        WHEN '40' THEN '销售出库'
        ELSE '其他出库'
        END InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,-a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryProductOutboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryProductOutbound a2 ON a1.OutboundBillID=a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '采购退货'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,-a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_PurchaseRefundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_PurchaseRefund a2 ON a1.PurchaseRefundID=a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '要货退货出库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,-a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryRefundOutboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryRefundOutbound a2 ON a1.RefundOutboundBillID=a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '要货退货入库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryRefundInboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryRefundInbound a2 ON a1.RefundInboundBillID=a2.ID
        UNION ALL
        --库存盘点
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        CASE WHEN a1.Quantity=0 THEN '盘平'
        WHEN a1.Quantity>0 THEN '盘盈入库'
        ELSE '盘亏出库'
        END InventoryType ,a2.CreatedOn BillDate,a1.StockQuantity,
        CASE WHEN a1.Quantity=0 THEN 0
        WHEN a1.Quantity>0 THEN a1.Quantity
        ELSE -a1.Quantity
        END Quantity,
        a2.CreatedBy,a1.UnitID
        MinimumUnitID,
        CASE WHEN a1.Quantity=0 THEN 0
        WHEN a1.Quantity>0 THEN a1.Quantity
        ELSE -a1.Quantity
        END MinimumUnitQuantity,
        a1.ID DetailID
        FROM TE_InventoryCheckDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryCheck a2 WITH(NOLOCK) ON a1.InventoryCheckID=a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '消耗出库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,-a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryTreatGeneralCardOutboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryTreatOutbound a2 ON a1.InventoryTreatOutboundID  =a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '消耗出库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,-a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryTreatTimeCardOutboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryTreatOutbound a2 ON a1.InventoryTreatOutboundID  =a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '消耗出库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,-a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryTreatSavingCardOutboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryTreatOutbound a2 ON a1.InventoryTreatOutboundID  =a2.ID
        UNION ALL
        SELECT a1.ProductID,a1.UnitID,a2.ID,a2.EntityID,
        '消耗出库'
        InventoryType ,a2.CreatedOn
        BillDate,a1.StockQuantity,-a1.Quantity,a2.CreatedBy,a1.MinimumUnitID,-a1.MinimumUnitQuantity
        ,a1.ID DetailID
        FROM TE_InventoryTreatProjectOutboundDetail a1 WITH(NOLOCK)
        LEFT JOIN TE_InventoryTreatOutbound a2 ON a1.InventoryTreatOutboundID  =a2.ID
        ) a1
        LEFT JOIN TB_EmployeeEntityPermission a2 WITH(NOLOCK) ON a1.EntityID=a2.EntityID
        LEFT JOIN TE_Product a3 WITH(NOLOCK) ON a1.ProductID=a3.ID
        LEFT JOIN TE_Unit a4 WITH(NOLOCK) ON a1.UnitID=a4.ID
        LEFT JOIN TK_Entity a5 WITH(NOLOCK) ON a1.EntityID=a5.ID
        LEFT JOIN TK_Employee a6 WITH(NOLOCK) ON a1.CreatedBy=a6.ID
        LEFT JOIN TE_ProductCategory a7 WITH(NOLOCK) on a3.PCategoryID=a7.ID
        LEFT JOIN TE_Unit a8 WITH(NOLOCK) ON a1.MinimumUnitID=a8.ID
        WhERE a2.EmployeeID=#{EmployeeID}
        <if test="EntityID != null and EntityID != ''">
            AND a1.EntityID=#{EntityID}
        </if>
        <if test="ProductID != null and ProductID != ''">
            AND a1.ProductID=#{ProductID}
        </if>
        <if test="InventoryType != null and InventoryType != ''">
            AND a1.InventoryType=#{InventoryType}
        </if>
        <if test="PCategoryID != null and PCategoryID != ''">
            AND a3.PCategoryID=#{PCategoryID}
        </if>
        <if test="ProductName != null and ProductName != ''">
            AND ( a3.Name LIKE '%'+ #{ProductName} +'%' OR a3.Alias LIKE '%'+ #{ProductName} +'%'
            OR a3.NameFullPinYin LIKE '%'+ #{ProductName} +'%' OR a3.NameAbbrPinYin LIKE '%'+ #{ProductName} +'%'
            OR a3.AliasFullPinYin LIKE '%'+ #{ProductName} +'%' OR a3.AliasAbbrPinYin LIKE '%'+ #{ProductName} +'%' )
        </if>
        <if test="StartDate != null and StartDate != ''and EndDate != null and EndDate != ''">
            AND a1.BillDate >= #{StartDate} AND a1.BillDate &lt; DATEADD(DAY,1,#{EndDate})
        </if>
        ORDER BY a1.BillDate DESC,a1.DetailID DESC
    </select>

    <select id="productDispatchPrice" resultType="java.math.BigDecimal">
        SELECT CASE
                   WHEN tpd.ProductDispatchPriceSchemeID is not null THEN tpd.DispatchPrice
                   WHEN t.ProductDispatchPriceSchemeID is not null THEN CAST(t.Discount * a4.Price as decimal(18, 2))
                   WHEN tp.ProductDispatchPriceSchemeID is not null THEN CAST(tp.Discount * a4.Price as decimal(18, 2))
                   WHEN (SELECT Discount
                         FROM TE_ProductDispatchPriceSchemeEntity aa
                                  JOIN TE_ProductDispatchPriceScheme bb ON aa.ProductDispatchPriceSchemeID = bb.ID
                         WHERE aa.EntityID = #{EntityID}
                           AND bb.Active = 1) is not null THEN (SELECT CAST(Discount * a4.Price as decimal(18, 2))
                                                                FROM TE_ProductDispatchPriceScheme a1e
                                                                         LEFT JOIN TE_ProductDispatchPriceSchemeEntity tt
                                                                                   ON a1e.ID = tt.ProductDispatchPriceSchemeID
                                                                WHERE tt.EntityID = #{EntityID}
                                                                  AND a1e.Active = 1)
                   ELSE a1.DeliveryPrice
                   END DeliveryPrice
        FROM TE_Product a1 WITH(NOLOCK)
        LEFT JOIN TB_Product a4
        WITH (NOLOCK)
        ON a4.ProductID= a1.ID
            LEFT JOIN TE_ProductDispatchPrice tpd
        WITH (NOLOCK)
        ON tpd.ProductID= a1.ID AND tpd.ProductDispatchPriceSchemeID=(SELECT ProductDispatchPriceSchemeID FROM TE_ProductDispatchPriceSchemeEntity aa JOIN TE_ProductDispatchPriceScheme bb ON aa.ProductDispatchPriceSchemeID=bb.ID WHERE aa.EntityID=#{EntityID} AND bb.Active=1 )
            LEFT JOIN TE_ProductCategory tpc
        WITH (NOLOCK)
        ON tpc.ID=a1.PCategoryID
            LEFT JOIN TE_ProductCategoryDispatchPrice t
        WITH (NOLOCK)
        ON t.ProductCategoryID=a1.PCategoryID AND t.ProductDispatchPriceSchemeID=(SELECT ProductDispatchPriceSchemeID FROM TE_ProductDispatchPriceSchemeEntity aa JOIN TE_ProductDispatchPriceScheme bb ON aa.ProductDispatchPriceSchemeID=bb.ID WHERE aa.EntityID=#{EntityID} AND bb.Active=1 )
            LEFT JOIN TE_ProductCategoryDispatchPrice tp
        WITH (NOLOCK)
        ON tp.ProductCategoryID=tpc.ParentID AND tp.ProductDispatchPriceSchemeID=(SELECT ProductDispatchPriceSchemeID FROM TE_ProductDispatchPriceSchemeEntity aa JOIN TE_ProductDispatchPriceScheme bb ON aa.ProductDispatchPriceSchemeID=bb.ID WHERE aa.EntityID=#{EntityID} AND bb.Active=1 )
        WHERE a1.ID =#{ProductID}
    </select>

    <resultMap id="productDetail"
               type="com.zhelian.model.psi.form.output.EntityStockCategoryOutputForm">
        <id column="ID" property="ID" jdbcType="INTEGER"/>
        <result column="ParentID" property="ParentID" jdbcType="INTEGER"/>
        <result column="Name" property="Name" jdbcType="VARCHAR"/>
        <collection property="Product" ofType="com.zhelian.model.psi.form.output.EntityStockOutputExtendForm">
            <id column="productID" property="ID" jdbcType="INTEGER"/>
            <result column="productName" property="ProductName" jdbcType="VARCHAR"/>
            <result column="Quantity" property="Quantity" jdbcType="INTEGER"/>
            <result column="Alias" property="Alias" jdbcType="VARCHAR"/>
            <result column="Specification" property="Specification" jdbcType="VARCHAR"/>
            <result column="PCategoryName" property="PCategoryName" jdbcType="VARCHAR"/>
            <result column="DeliveryPrice" property="DeliveryPrice" jdbcType="DECIMAL"/>
            <result column="Price" property="Price" jdbcType="DECIMAL"/>
            <result column="IsLock" property="IsLock" jdbcType="BOOLEAN"/>
            <result column="ImageURL" property="ImageURL" jdbcType="VARCHAR"/>

            <collection property="Unit" ofType="com.zhelian.model.psi.form.output.EntityStockProductUnitOutputForm">

                <result column="UnitID" property="UnitID" jdbcType="INTEGER"/>
                <result column="UnitName" property="UnitName" jdbcType="VARCHAR"/>
                <result column="Amount" property="Amount" jdbcType="INTEGER"/>
                <result column="IsMinimumUnit" property="IsMinimumUnit" jdbcType="BOOLEAN"/>
                <result column="IsDefautSendReceive" property="IsDefautSendReceive" jdbcType="BOOLEAN"/>
            </collection>
        </collection>
    </resultMap>

    <select id="getUnit" resultMap="unitMap">
        SELECT a2.UnitID,
               a3.Name UnitName,
               a2.Amount,
               a2.IsMinimumUnit,
               a2.IsDefautSendReceive
        FROM TE_ProductBarCode a2 WITH(NOLOCK)
        LEFT JOIN TE_Unit a3
        WITH (NOLOCK)
        ON a2.UnitID=a3.ID
        WHERE a2.ProductID=#{productID}
    </select>
    <select id="entityCategoryProductList" resultMap="productDetail">
        SELECT
        a8.ID,
        a8.Name,
        a8.ParentID,
        a1.ID productID,
        a1.Name productName,
        a1.Alias,
        isnull(s1.Quantity,0) Quantity,
        a8.Name PCategoryName,
        a1.Specification,
        CASE WHEN a7.ProductID IS NULL THEN 0
        ELSE 1
        END IsLock,
        a9.Price,
        a10.ImageURL,
        CASE
        WHEN tpd.ProductDispatchPriceSchemeID is not null THEN tpd.DispatchPrice
        WHEN t.ProductDispatchPriceSchemeID is not null THEN CAST(t.Discount * a9.Price as decimal(18, 2))
        WHEN tp.ProductDispatchPriceSchemeID is not null THEN CAST(tp.Discount * a9.Price as decimal(18, 2))
        WHEN (SELECT Discount FROM TE_ProductDispatchPriceSchemeEntity aa JOIN TE_ProductDispatchPriceScheme bb ON
        aa.ProductDispatchPriceSchemeID=bb.ID WHERE aa.EntityID=#{EntityID} AND bb.Active=1) is not null THEN (SELECT
        CAST(Discount * a9.Price as decimal(18, 2)) FROM TE_ProductDispatchPriceScheme a1e LEFT JOIN
        TE_ProductDispatchPriceSchemeEntity tt ON a1e.ID=tt.ProductDispatchPriceSchemeID WHERE tt.EntityID=#{EntityID}
        AND a1e.Active=1)
        ELSE a1.DeliveryPrice
        END DeliveryPrice,
        a2.UnitID,
        a3.Name UnitName,
        a2.Amount,
        a2.IsMinimumUnit,
        a2.IsDefautSendReceive
        FROM
        TE_Product a1 WITH(NOLOCK)
        LEFT JOIN TE_Stock s1 WITH(NOLOCK) ON s1.ProductID= a1.ID AND s1.EntityID=#{EntityID}
        LEFT JOIN (
        SELECT a5.ProductID FROM TE_InventoryCheckDetail a5 WITH(NOLOCK)
        JOIN TE_InventoryCheck a6 WITH(NOLOCK) ON a5.InventoryCheckID=a6.ID
        WHERE a6.BillStatus='10' AND a6.EntityID=#{EntityID}
        GROUP BY a5.ProductID
        )a7 ON a7.ProductID=a1.ID
        LEFT JOIN TE_ProductCategory a8 WITH(NOLOCK) on a1.PCategoryID=a8.ID
        LEFT JOIN TB_Product a9 WITH(NOLOCK) on a1.ID=a9.ProductID
        LEFT JOIN TB_ProductImage a10 WITH(NOLOCK) on a1.ID=a10.ProductID AND a10.Sequence=1

        LEFT JOIN TE_ProductBarCode a2 WITH(NOLOCK) on a1.ID=a2.ProductID
        LEFT JOIN TE_Unit a3 WITH (NOLOCK)  ON a2.UnitID=a3.ID

        LEFT JOIN TE_ProductDispatchPrice tpd WITH(NOLOCK) ON tpd.ProductID= a1.ID AND
        tpd.ProductDispatchPriceSchemeID=(SELECT ProductDispatchPriceSchemeID FROM TE_ProductDispatchPriceSchemeEntity
        aa JOIN TE_ProductDispatchPriceScheme bb ON aa.ProductDispatchPriceSchemeID=bb.ID WHERE aa.EntityID=#{EntityID}
        AND bb.Active=1 )
        LEFT JOIN TE_ProductCategory tpc WITH(NOLOCK) ON tpc.ID=a1.PCategoryID
        LEFT JOIN TE_ProductCategoryDispatchPrice t WITH(NOLOCK) ON t.ProductCategoryID=a1.PCategoryID AND
        t.ProductDispatchPriceSchemeID=(SELECT ProductDispatchPriceSchemeID FROM TE_ProductDispatchPriceSchemeEntity aa
        JOIN TE_ProductDispatchPriceScheme bb ON aa.ProductDispatchPriceSchemeID=bb.ID WHERE aa.EntityID=#{EntityID} AND
        bb.Active=1 )
        LEFT JOIN TE_ProductCategoryDispatchPrice tp WITH(NOLOCK) ON tp.ProductCategoryID=tpc.ParentID AND
        tp.ProductDispatchPriceSchemeID=(SELECT ProductDispatchPriceSchemeID FROM TE_ProductDispatchPriceSchemeEntity aa
        JOIN TE_ProductDispatchPriceScheme bb ON aa.ProductDispatchPriceSchemeID=bb.ID WHERE aa.EntityID=#{EntityID} AND
        bb.Active=1 )
        WHERE
        a1.Active=1
        <if test="ProductName != null and ProductName != ''">
            AND ( a1.Name LIKE '%'+ #{ProductName} +'%' OR a1.Alias LIKE '%'+ #{ProductName} +'%'
            OR a1.NameFullPinYin LIKE '%'+ #{ProductName} +'%' OR a1.NameAbbrPinYin LIKE '%'+ #{ProductName} +'%'
            OR a1.AliasFullPinYin LIKE '%'+ #{ProductName} +'%' OR a1.AliasAbbrPinYin LIKE '%'+ #{ProductName} +'%' )
        </if>
        ORDER BY a8.Sequence desc,a1.Name,a1.ID DESC
    </select>

    <select id="getTopProductList" parameterType="java.util.List"
            resultType="com.zhelian.model.psi.form.output.EntityStockTopCategoryOutputForm">
        SELECT
        p.ID,
        p.Name,
        p.ParentID
        FROM TE_ProductCategory p WITH(NOLOCK)
        WHERE p.ParentID =0 AND p.Active=1 AND p.ID in (
        <foreach collection="list" item="item" index="index"
                 separator=",">
            #{item}
        </foreach>
        )
        ORDER BY p.Sequence desc
    </select>

    <select id="warehouseList" resultType="com.zhelian.model.psi.form.output.EntityPrepayOutputForm">
        SELECT a1.ID,
               a1.EntityName,
               a3.Balance,
               a3.LargessBalance,
               a3.Balance + a3.LargessBalance TotalBalance
        FROM TK_Entity a1 WITH(NOLOCK)
        LEFT JOIN TB_EmployeeEntityPermission a2
        WITH (NOLOCK)
        ON a1.ID=a2.EntityID
            LEFT JOIN TE_Prepay a3
        WITH (NOLOCK)
        ON a1.ID=a3.EntityID
        WHERE a1.Active=1 AND a1.IsWarehouse=1 AND a2.EmployeeID = #{EmployeeID}
        ORDER BY a1.Sequence DESC
    </select>

    <select id="productQuantity" resultType="java.lang.Integer">
        SELECT Quantity from TE_Stock a1 WITH (NOLOCK)
        WHERE a1.ProductID =#{ProductID} AND a1.EntityID =#{EntityID}
    </select>
</mapper>