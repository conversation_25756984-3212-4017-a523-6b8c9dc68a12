<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.khs.TreatProjectHandlerPerformanceMapper">
    <resultMap id="ProjectHandlerPerformance"
               type="com.zhelian.model.khs.form.output.TreatGoodCategoryHandlerPerformanceOutputForm">
        <id column="TreatHandlerID" property="ID" jdbcType="INTEGER"/>
        <result column="TreatHandlerName" property="Name" jdbcType="VARCHAR"/>
        <result column="PayRate" property="PayRate" jdbcType="DECIMAL"/>
        <result column="CardRate" property="CardRate" jdbcType="DECIMAL"/>
        <result column="CardLargessRate" property="CardLargessRate" jdbcType="DECIMAL"/>
        <result column="LargessRate" property="LargessRate" jdbcType="DECIMAL"/>
        <collection property="HandlerJobTypeCommission"
                    ofType="com.zhelian.model.khs.form.output.TreatGoodHandlerJobTypePerformanceOutputForm">
            <id column="JobTypeID" property="JobTypeID" jdbcType="INTEGER"/>
            <result column="HandlerJobTypeID" property="ID" jdbcType="VARCHAR"/>
            <result column="JobName" property="Name" jdbcType="VARCHAR"/>
            <result column="JobTypePayRate" property="PayRate" jdbcType="DECIMAL"/>
            <result column="JobTypeCardRate" property="CardRate" jdbcType="DECIMAL"/>
            <result column="JobTypeCardLargessRate" property="CardLargessRate" jdbcType="DECIMAL"/>
            <result column="JobTypeLargessRate" property="LargessRate" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>

    <select id="allTreatProjectHandlerPerformance" resultMap="ProjectHandlerPerformance">
        SELECT tsh.EntityID,
               tsh.ID                                                           TreatHandlerID,
               tsh.Name                                                         TreatHandlerName,
               tsp.PayRate * 100                                                PayRate,
               tsp.CardRate * 100                                               CardRate,
               tsp.CardLargessRate * 100                                        CardLargessRate,
               tsp.LargessRate * 100                                            LargessRate,
               tpj.JobTypeID,
               (cast(tsh.ID as varchar) + '-' + cast(tpj.JobTypeID as varchar)) HandlerJobTypeID,
               tj.JobName,
               tsj.PayRate * 100                                                JobTypePayRate,
               tsj.CardRate * 100                                               JobTypeCardRate,
               tsj.CardLargessRate * 100                                        JobTypeCardLargessRate,
               tsj.LargessRate * 100                                            JobTypeLargessRate
        FROM TB_ProjectTreatHandler tsh WITH(NOLOCK)
        LEFT JOIN   TB_TreatProjectHandlerPerformance tsp
        WITH (NOLOCK)
        ON tsp.ProjectTreatHandlerID=tsh.ID AND tsh.EntityID=tsp.EntityID
            AND tsp.ProjectID=#{ProjectID}
            LEFT JOIN TB_ProjectTreatHandlerJobType tpj
        WITH (NOLOCK)
        ON tsh.ID=tpj.ProjectTreatHandlerID
            LEFT JOIN TB_TreatProjectHandlerJobTypePerformance tsj
        WITH (NOLOCK)
        ON tsh.EntityID=tsj.EntityID
            AND tsh.ID=tsj.ProjectTreatHandlerID AND tsj.JobTypeID=tpj.JobTypeID AND tsj.ProjectID=#{ProjectID}
            LEFT JOIN TK_JobType tj
        WITH (NOLOCK)
        ON tpj.JobTypeID=tj.ID
        WHERE tsh.EntityID=#{EntityID} AND tsh.Active=1
        ORDER BY tsh.Sequence DESC
    </select>

    <delete id="deleteTreatProjectHandler">
        DELETE
        FROM TB_TreatProjectHandlerPerformance
        WHERE EntityID = #{EntityID}
          AND ProjectID = #{ProjectID}
    </delete>

    <delete id="deleteTreatProjectHandlerJobType">
        DELETE
        FROM TB_TreatProjectHandlerJobTypePerformance
        WHERE EntityID = #{EntityID}
          AND ProjectID = #{ProjectID}
    </delete>

    <delete id="deleteTreatProjectHandlerPerformanceByEntity">
        DELETE
        FROM TB_TreatProjectHandlerPerformance
        WHERE EntityID = #{EntityID}
    </delete>

    <delete id="deleteTreatProjectHandlerJobTypePerformanceByEntity">
        DELETE
        FROM TB_TreatProjectHandlerJobTypePerformance
        WHERE EntityID = #{EntityID}
    </delete>


    <insert id="updateTreatProjectHandlerPerformance" parameterType="java.util.List">
        INSERT INTO TB_TreatProjectHandlerPerformance (
        EntityID,
        ProjectID,
        ProjectTreatHandlerID,
        PayRate,
        CardRate,
        CardLargessRate,
        LargessRate
        ) VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.EntityID},
            #{item.ProjectID},
            #{item.ProjectTreatHandlerID},
            cast(#{item.PayRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.CardRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.CardLargessRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.LargessRate,jdbcType=DECIMAL}/100 as decimal(5,4))
            )
        </foreach>

    </insert>

    <insert id="insertTreatProjectHandlerJobTypePerformance" parameterType="java.util.List">
        INSERT INTO TB_TreatProjectHandlerJobTypePerformance (
        EntityID,
        ProjectID,
        ProjectTreatHandlerID,
        JobTypeID,
        PayRate,
        CardRate,
        CardLargessRate,
        LargessRate
        ) VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.EntityID},
            #{item.ProjectID},
            #{item.ProjectTreatHandlerID},
            #{item.JobTypeID},
            cast(#{item.PayRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.CardRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.CardLargessRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.LargessRate,jdbcType=DECIMAL}/100 as decimal(5,4))
            )
        </foreach>
    </insert>
</mapper>