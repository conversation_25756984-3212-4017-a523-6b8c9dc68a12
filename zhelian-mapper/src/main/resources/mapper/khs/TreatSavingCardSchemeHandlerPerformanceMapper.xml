<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.khs.TreatSavingCardSchemeHandlerPerformanceMapper">
    <resultMap id="SavingCardHandlerPerformance"
               type="com.zhelian.model.ibeauty.form.output.TreatSavingCardProjectHandlerCommissionOutputForm">
        <id column="TreatHandlerID" property="ID" jdbcType="INTEGER"/>
        <result column="TreatHandlerName" property="Name" jdbcType="VARCHAR"/>
        <result column="Rate" property="Rate" jdbcType="DECIMAL"/>
        <result column="LargessRate" property="LargessRate" jdbcType="DECIMAL"/>
        <collection property="HandlerJobTypeCommission"
                    ofType="com.zhelian.model.ibeauty.form.output.TreatSavingCardProjectHandlerJobTypeCommissionOutputForm">
            <id column="JobTypeID" property="JobTypeID" jdbcType="INTEGER"/>
            <result column="HandlerJobTypeID" property="ID" jdbcType="VARCHAR"/>
            <result column="JobName" property="Name" jdbcType="VARCHAR"/>
            <result column="JobTypeRate" property="Rate" jdbcType="DECIMAL"/>
            <result column="JobTypeLargessRate" property="LargessRate" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>

    <select id="allTreatSavingCardSchemeHandlerPerformance" resultMap="SavingCardHandlerPerformance">
        SELECT tsh.EntityID,
               tsh.ID                                                           TreatHandlerID,
               tsh.Name                                                         TreatHandlerName,
               tsp.CardRate * 100                                               Rate,
               tsp.CardLargessRate * 100                                        LargessRate,
               tpj.JobTypeID,
               (cast(tsh.ID as varchar) + '-' + cast(tpj.JobTypeID as varchar)) HandlerJobTypeID,
               tj.JobName,
               tsj.CardRate * 100                                               JobTypeRate,
               tsj.CardLargessRate * 100                                        JobTypeLargessRate
        FROM TB_SavingCardProjectTreatHandler tsh WITH(NOLOCK)
            LEFT JOIN   TB_TreatSavingCardSchemeHandlerPerformance tsp
        WITH (NOLOCK)
        ON tsp.SavingCardProjectTreatHandlerID=tsh.ID AND tsh.EntityID=tsp.EntityID
            LEFT JOIN TB_SavingCardProjectTreatHandlerJobType tpj
        WITH (NOLOCK)
        ON tsh.ID=tpj.SavingCardProjectTreatHandlerID
            LEFT JOIN TB_TreatSavingCardSchemeHandlerJobTypePerformance tsj
        WITH (NOLOCK)
        ON tsh.EntityID=tsj.EntityID
            AND tsh.ID=tsj.SavingCardProjectTreatHandlerID AND tsj.JobTypeID=tpj.JobTypeID
            LEFT JOIN TK_JobType tj
        WITH (NOLOCK)
        ON tpj.JobTypeID=tj.ID
        WHERE tsh.EntityID=#{EntityID} AND tsh.Active=1
        ORDER BY tsh.Sequence DESC
    </select>


    <delete id="deleteTreatSavingCardSchemeHandler">
        DELETE
        FROM TB_TreatSavingCardSchemeHandlerPerformance
        WHERE EntityID = #{EntityID}
    </delete>

    <delete id="deleteTreatSavingCardSchemeHandlerJobType">
        DELETE
        FROM TB_TreatSavingCardSchemeHandlerJobTypePerformance
        WHERE EntityID = #{EntityID}
    </delete>


    <insert id="insertTreatSavingCardSchemeHandlerPerformance" parameterType="java.util.List">
        INSERT INTO TB_TreatSavingCardSchemeHandlerPerformance (
        EntityID,
        SavingCardProjectTreatHandlerID,
        CardRate,
        CardLargessRate
        ) VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.EntityID},
            #{item.SavingCardProjectTreatHandlerID},
            cast(#{item.CardRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.CardLargessRate,jdbcType=DECIMAL}/100 as decimal(5,4))
            )
        </foreach>

    </insert>

    <insert id="insertTreatSavingCardSchemeHandlerJobTypePerformance" parameterType="java.util.List">
        INSERT INTO TB_TreatSavingCardSchemeHandlerJobTypePerformance (
        EntityID,
        SavingCardProjectTreatHandlerID,
        JobTypeID,
        CardRate,
        CardLargessRate
        ) VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.EntityID},
            #{item.SavingCardProjectTreatHandlerID},
            #{item.JobTypeID},
            cast(#{item.CardRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.CardLargessRate,jdbcType=DECIMAL}/100 as decimal(5,4))
            )
        </foreach>
    </insert>
</mapper>