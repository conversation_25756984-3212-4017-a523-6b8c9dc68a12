<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.khs.TreatProductCategoryHandlerPerformanceMapper">

    <resultMap id="ProductCategoryHandlerPerformance"
               type="com.zhelian.model.khs.form.output.TreatGoodCategoryHandlerPerformanceOutputForm">
        <id column="EntityID" property="EntityID" jdbcType="INTEGER"/>
        <id column="TreatHandlerID" property="ID" jdbcType="INTEGER"/>
        <result column="TreatHandlerName" property="Name" jdbcType="VARCHAR"/>
        <result column="PayRate" property="PayRate" jdbcType="DECIMAL"/>
        <result column="CardRate" property="CardRate" jdbcType="DECIMAL"/>
        <result column="CardLargessRate" property="CardLargessRate" jdbcType="DECIMAL"/>
        <result column="LargessRate" property="LargessRate" jdbcType="DECIMAL"/>

        <collection property="HandlerJobTypeCommission"
                    ofType="com.zhelian.model.khs.form.output.TreatGoodHandlerJobTypePerformanceOutputForm">
            <id column="JobTypeID" property="JobTypeID" jdbcType="INTEGER"/>
            <result column="HandlerJobTypeID" property="ID" jdbcType="VARCHAR"/>
            <result column="JobName" property="Name" jdbcType="VARCHAR"/>
            <result column="JobTypePayRate" property="PayRate" jdbcType="DECIMAL"/>
            <result column="JobTypeCardRate" property="CardRate" jdbcType="DECIMAL"/>
            <result column="JobTypeCardLargessRate" property="CardLargessRate" jdbcType="DECIMAL"/>
            <result column="JobTypeLargessRate" property="LargessRate" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>
    <select id="allTreatProductCategoryHandlerPerformance" resultMap="ProductCategoryHandlerPerformance">
        SELECT tsh.EntityID,
               tsh.ID                                                           TreatHandlerID,
               tsh.Name                                                         TreatHandlerName,
               tsp.PayRate * 100                                                PayRate,
               tsp.CardRate * 100                                               CardRate,
               tsp.CardLargessRate * 100                                        CardLargessRate,
               tsp.LargessRate * 100                                            LargessRate,
               (cast(tsh.ID as varchar) + '-' + cast(tpj.JobTypeID as varchar)) HandlerJobTypeID,
               tpj.JobTypeID,
               tj.JobName,
               tsj.PayRate * 100                                                JobTypePayRate,
               tsj.CardRate * 100                                               JobTypeCardRate,
               tsj.CardLargessRate * 100                                        JobTypeCardLargessRate,
               tsj.LargessRate * 100                                            JobTypeLargessRate
        FROM TB_ProductTreatHandler tsh WITH(NOLOCK)
        LEFT JOIN   TB_TreatProductCategoryHandlerPerformance tsp
        WITH (NOLOCK)
        ON tsp.ProductTreatHandlerID=tsh.ID AND tsh.EntityID=tsp.EntityID
            AND tsp.ProductCategoryID=#{CategoryID}
            LEFT JOIN TB_ProductTreatHandlerJobType tpj
        WITH (NOLOCK)
        ON tsh.ID=tpj.ProductTreatHandlerID
            LEFT JOIN TB_TreatProductCategoryHandlerJobTypePerformance tsj
        WITH (NOLOCK)
        ON tsh.EntityID=tsj.EntityID
            AND tsj.ProductTreatHandlerID = tsh.ID AND tsj.JobTypeID=tpj.JobTypeID AND tsj.ProductCategoryID=#{CategoryID}
            LEFT JOIN TK_JobType tj
        WITH (NOLOCK)
        ON tpj.JobTypeID=tj.ID
        WHERE tsh.EntityID=#{EntityID} AND tsh.Active=1
        ORDER BY tsh.Sequence DESC
    </select>

    <delete id="deleteTreatProductCategoryHandler">
        DELETE
        FROM TB_TreatProductCategoryHandlerPerformance
        WHERE EntityID = #{EntityID}
          AND ProductCategoryID = #{CategoryID}
    </delete>

    <delete id="deleteTreatProductCategoryHandlerJobType">
        DELETE
        FROM TB_TreatProductCategoryHandlerJobTypePerformance
        WHERE EntityID = #{EntityID}
          AND ProductCategoryID = #{CategoryID}
    </delete>

    <delete id="deleteTreatProductCategoryHandlerPerformanceByEntity">
        DELETE
        FROM TB_TreatProductCategoryHandlerPerformance
        WHERE EntityID = #{EntityID}
    </delete>

    <delete id="deleteTreatProductCategoryHandlerJobTypePerformanceByEntity">
        DELETE
        FROM TB_TreatProductCategoryHandlerJobTypePerformance
        WHERE EntityID = #{EntityID}
    </delete>

    <insert id="updateTreatProductCategoryHandlerPerformance" parameterType="java.util.List">
        INSERT INTO TB_TreatProductCategoryHandlerPerformance (
        EntityID
        ,ProductCategoryID
        ,ProductTreatHandlerID
        ,PayRate
        ,CardRate
        ,CardLargessRate
        ,LargessRate

        ) VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.EntityID},
            #{item.ProductCategoryID},
            #{item.ProductTreatHandlerID},
            cast(#{item.PayRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.CardRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.CardLargessRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.LargessRate,jdbcType=DECIMAL}/100 as decimal(5,4))
            )
        </foreach>
    </insert>


    <insert id="insertTreatProductCategoryHandlerJobTypePerformance" parameterType="java.util.List">
        INSERT INTO TB_TreatProductCategoryHandlerJobTypePerformance (
        EntityID,
        ProductCategoryID,
        ProductTreatHandlerID,
        JobTypeID,
        PayRate,
        CardRate,
        CardLargessRate,
        LargessRate
        ) VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.EntityID},
            #{item.ProductCategoryID},
            #{item.ProductTreatHandlerID},
            #{item.JobTypeID},
            cast(#{item.PayRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.CardRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.CardLargessRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.LargessRate,jdbcType=DECIMAL}/100 as decimal(5,4))
            )
        </foreach>
    </insert>

</mapper>