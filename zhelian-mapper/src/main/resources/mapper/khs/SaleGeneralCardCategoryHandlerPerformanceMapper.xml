<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.khs.SaleGeneralCardCategoryHandlerPerformanceMapper">

    <resultMap id="GeneralCardCategoryHandlerPerformance"
               type="com.zhelian.model.khs.form.output.SaleGoodCategoryHandlerPerformanceOutputForm">
        <id column="EntityID" property="EntityID" jdbcType="INTEGER"/>
        <id column="SaleHandlerID" property="ID" jdbcType="INTEGER"/>
        <result column="SaleHandlerName" property="Name" jdbcType="VARCHAR"/>
        <result column="PayRate" property="PayRate" jdbcType="DECIMAL"/>
        <result column="SavingCardRate" property="SavingCardRate" jdbcType="DECIMAL"/>
        <result column="SavingCardLargessRate" property="SavingCardLargessRate" jdbcType="DECIMAL"/>

        <collection property="HandlerJobTypeCommission"
                    ofType="com.zhelian.model.khs.form.output.SaleGoodHandlerJobTypeCommissionOutputForm">
            <id column="JobTypeID" property="JobTypeID" jdbcType="INTEGER"/>
            <result column="HandlerJobTypeID" property="ID" jdbcType="VARCHAR"/>
            <result column="JobName" property="Name" jdbcType="VARCHAR"/>
            <result column="JobTypePayRate" property="PayRate" jdbcType="DECIMAL"/>
            <result column="JobTypeSavingCardRate" property="SavingCardRate" jdbcType="DECIMAL"/>
            <result column="JobTypeSavingCardLargessRate" property="SavingCardLargessRate" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>
    <select id="allSaleGeneralCardCategoryHandlerPerformance" resultMap="GeneralCardCategoryHandlerPerformance">
        SELECT tsh.EntityID,
               tsh.ID                                                           SaleHandlerID,
               tsh.Name                                                         SaleHandlerName,
               tsp.PayRate * 100                                                PayRate,
               tsp.SavingCardRate * 100                                         SavingCardRate,
               tsp.SavingCardLargessRate * 100                                  SavingCardLargessRate,
               (cast(tsh.ID as varchar) + '-' + cast(tpj.JobTypeID as varchar)) HandlerJobTypeID,
               tpj.JobTypeID,
               tj.JobName,
               tsj.PayRate * 100                                                JobTypePayRate,
               tsj.SavingCardRate * 100                                         JobTypeSavingCardRate,
               tsj.SavingCardLargessRate * 100                                  JobTypeSavingCardLargessRate
        FROM TB_GeneralCardSaleHandler tsh WITH(NOLOCK)
        LEFT JOIN   TB_SaleGeneralCardCategoryHandlerPerformance tsp
        WITH (NOLOCK)
        ON tsp.GeneralCardSaleHandlerID=tsh.ID AND tsh.EntityID=tsp.EntityID
            AND tsp.GeneralCardCategoryID=#{CategoryID}
            LEFT JOIN TB_GeneralCardSaleHandlerJobType tpj
        WITH (NOLOCK)
        ON tsh.ID=tpj.GeneralCardSaleHandlerID
            LEFT JOIN TB_SaleGeneralCardCategoryHandlerJobTypePerformance tsj
        WITH (NOLOCK)
        ON tsh.EntityID=tsj.EntityID
            AND tsj.GeneralCardSaleHandlerID = tsh.ID AND tsj.JobTypeID=tpj.JobTypeID AND tsj.GeneralCardCategoryID=#{CategoryID}
            LEFT JOIN TK_JobType tj
        WITH (NOLOCK)
        ON tpj.JobTypeID=tj.ID
        WHERE tsh.EntityID=#{EntityID} AND tsh.Active=1
        ORDER BY tsh.Sequence DESC
    </select>

    <delete id="deleteSaleGeneralCardCategoryHandler">
        DELETE
        FROM TB_SaleGeneralCardCategoryHandlerPerformance
        WHERE EntityID = #{EntityID}
          AND GeneralCardCategoryID = #{CategoryID}
    </delete>

    <delete id="deleteSaleGeneralCardCategoryHandlerJobType">
        DELETE
        FROM TB_SaleGeneralCardCategoryHandlerJobTypePerformance
        WHERE EntityID = #{EntityID}
          AND GeneralCardCategoryID = #{CategoryID}
    </delete>

    <insert id="insertSaleGeneralCardCategoryHandlerPerformance" parameterType="java.util.List">
        INSERT INTO TB_SaleGeneralCardCategoryHandlerPerformance
        (EntityID
        ,GeneralCardCategoryID
        ,GeneralCardSaleHandlerID
        ,PayRate
        ,SavingCardRate
        ,SavingCardLargessRate
        )
        VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.EntityID},
            #{item.GeneralCardCategoryID},
            #{item.GeneralCardSaleHandlerID},
            cast(#{item.PayRate,jdbcType=DECIMAL} /100 as decimal(5,4)),
            cast(#{item.SavingCardRate,jdbcType=DECIMAL} /100 as decimal(5,4)),
            cast(#{item.SavingCardLargessRate,jdbcType=DECIMAL} /100 as decimal(5,4))
            )
        </foreach>

    </insert>

    <insert id="insertSaleGeneralCardCategoryHandlerJobTypePerformance" parameterType="java.util.List">
        INSERT INTO TB_SaleGeneralCardCategoryHandlerJobTypePerformance (
        EntityID,
        GeneralCardCategoryID,
        GeneralCardSaleHandlerID,
        JobTypeID,
        PayRate,
        SavingCardRate,
        SavingCardLargessRate
        ) VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.EntityID},
            #{item.GeneralCardCategoryID},
            #{item.GeneralCardSaleHandlerID},
            #{item.JobTypeID},
            cast(#{item.PayRate,jdbcType=DECIMAL} /100 as decimal(5,4)),
            cast(#{item.SavingCardRate,jdbcType=DECIMAL} /100 as decimal(5,4)),
            cast(#{item.SavingCardLargessRate,jdbcType=DECIMAL} /100 as decimal(5,4))
            )
        </foreach>
    </insert>

    <delete id="deleteSaleGeneralCardCategoryHandlerJobTypePerformance">
        DELETE
        FROM TB_SaleGeneralCardCategoryHandlerJobTypePerformance
        WHERE EntityID = #{EntityID}
    </delete>

    <delete id="deleteSaleGeneralCardCategoryHandlerPerformance">
        DELETE
        FROM TB_SaleGeneralCardCategoryHandlerPerformance
        WHERE EntityID = #{EntityID}
    </delete>
</mapper>