<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.khs.SalePackageCardProjectHandlerPerformanceMapper">
    <resultMap id="ProjectHandlerPerformance"
               type="com.zhelian.model.khs.form.output.SaleGoodCategoryHandlerPerformanceOutputForm">
        <id column="SaleHandlerID" property="ID" jdbcType="INTEGER"/>
        <result column="SaleHandlerName" property="Name" jdbcType="VARCHAR"/>
        <result column="PayRate" property="PayRate" jdbcType="DECIMAL"/>
        <result column="SavingCardRate" property="SavingCardRate" jdbcType="DECIMAL"/>
        <result column="SavingCardLargessRate" property="SavingCardLargessRate" jdbcType="DECIMAL"/>
        <collection property="HandlerJobTypeCommission"
                    ofType="com.zhelian.model.khs.form.output.SaleGoodHandlerJobTypeCommissionOutputForm">
            <id column="JobTypeID" property="JobTypeID" jdbcType="INTEGER"/>
            <result column="HandlerJobTypeID" property="ID" jdbcType="VARCHAR"/>
            <result column="JobName" property="Name" jdbcType="VARCHAR"/>
            <result column="JobTypePayRate" property="PayRate" jdbcType="DECIMAL"/>
            <result column="JobTypeSavingCardRate" property="SavingCardRate" jdbcType="DECIMAL"/>
            <result column="JobTypeSavingCardLargessRate" property="SavingCardLargessRate" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>

    <select id="allSalePackageCardProjectHandlerPerformance" resultMap="ProjectHandlerPerformance">
        SELECT tsh.EntityID,
               tsh.ID                                                           SaleHandlerID,
               tsh.Name                                                         SaleHandlerName,
               tsp.PayRate * 100                                                PayRate,
               tsp.SavingCardRate * 100                                         SavingCardRate,
               tsp.SavingCardLargessRate * 100                                  SavingCardLargessRate,
               (cast(tsh.ID as varchar) + '-' + cast(tpj.JobTypeID as varchar)) HandlerJobTypeID,
               tpj.JobTypeID,
               tj.JobName,
               tsj.PayRate * 100                                                JobTypePayRate,
               tsj.SavingCardRate * 100                                         JobTypeSavingCardRate,
               tsj.SavingCardLargessRate * 100                                  JobTypeSavingCardLargessRate
        FROM TB_PackageCardSaleHandler tsh WITH(NOLOCK)
            LEFT JOIN   TB_SalePackageCardProjectHandlerPerformance tsp
        WITH (NOLOCK)
        ON tsp.PackageCardSaleHandlerID=tsh.ID AND tsh.EntityID=tsp.EntityID
            AND tsp.ProjectID=#{ProjectID}
            LEFT JOIN TB_PackageCardSaleHandlerJobType tpj
        WITH (NOLOCK)
        ON tsh.ID=tpj.PackageCardSaleHandlerID
            LEFT JOIN TB_SalePackageCardProjectHandlerJobTypePerformance tsj
        WITH (NOLOCK)
        ON tsh.EntityID=tsj.EntityID
            AND tsj.PackageCardSaleHandlerID = tsh.ID AND tsj.JobTypeID=tpj.JobTypeID AND tsj.ProjectID=#{ProjectID}
            LEFT JOIN TK_JobType tj
        WITH (NOLOCK)
        on tpj.JobTypeID=tj.ID
        WHERE tsh.EntityID=#{EntityID} AND tsh.Active=1
    </select>


    <delete id="deleteSalePackageCardProjectHandler">
        DELETE
        FROM TB_SalePackageCardProjectHandlerPerformance
        WHERE EntityID = #{EntityID}
          AND ProjectID = #{ProjectID}
    </delete>

    <delete id="deleteSalePackageCardProjectHandlerJobType">
        DELETE
        FROM TB_SalePackageCardProjectHandlerJobTypePerformance
        WHERE EntityID = #{EntityID}
          AND ProjectID = #{ProjectID}
    </delete>

    <delete id="deleteSalePackageCardProjectHandlerPerformanceByEntity">
        DELETE
        FROM TB_SalePackageCardProjectHandlerPerformance
        WHERE EntityID = #{EntityID}
    </delete>

    <delete id="deleteSalePackageCardProjectHandlerJobTypePerformanceByEntity">
        DELETE
        FROM TB_SalePackageCardProjectHandlerJobTypePerformance
        WHERE EntityID = #{EntityID}
    </delete>


    <insert id="updateSalePackageCardProjectHandlerPerformance" parameterType="java.util.List">
        INSERT INTO TB_SalePackageCardProjectHandlerPerformance (
        EntityID,
        ProjectID,
        PackageCardSaleHandlerID,
        PayRate,
        SavingCardRate,
        SavingCardLargessRate
        ) VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.EntityID},
            #{item.ProjectID},
            #{item.PackageCardSaleHandlerID},
            cast(#{item.PayRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.SavingCardRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.SavingCardLargessRate,jdbcType=DECIMAL}/100 as decimal(5,4))
            )
        </foreach>

    </insert>

    <insert id="insertSalePackageCardProjectHandlerJobTypePerformance" parameterType="java.util.List">
        INSERT INTO TB_SalePackageCardProjectHandlerJobTypePerformance (
        EntityID,
        ProjectID,
        PackageCardSaleHandlerID,
        JobTypeID,
        PayRate,
        SavingCardRate,
        SavingCardLargessRate
        ) VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.EntityID},
            #{item.ProjectID},
            #{item.PackageCardSaleHandlerID},
            #{item.JobTypeID},
            cast(#{item.PayRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.SavingCardRate,jdbcType=DECIMAL}/100 as decimal(5,4)),
            cast(#{item.SavingCardLargessRate,jdbcType=DECIMAL}/100 as decimal(5,4))
            )
        </foreach>
    </insert>
</mapper>