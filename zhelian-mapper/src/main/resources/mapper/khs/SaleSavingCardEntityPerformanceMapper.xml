<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhelian.mapper.khs.SaleSavingCardEntityPerformanceMapper">

    <select id="allSaleSavingCardEntityPerformance"
            resultType="com.zhelian.model.khs.form.output.SaleSavingCardPerformanceOutputForm">
        SELECT te.ID             GoodID,
               te.Name           GoodName,
               tsp.PayRate * 100 PayRate
        FROM TB_SavingCard te WITH(NOLOCK)
           LEFT JOIN TB_SaleSavingCardEntityPerformance  tsp
        WITH (NOLOCK)
        ON tsp.SavingCardID=te.ID AND tsp.EntityID=#{EntityID}
        WHERE te.SavingCardCategoryID=#{CategoryID}
    </select>

    <delete id="deleteSaleSavingCardEntityPerformance">
        DELETE FROM TB_SaleSavingCardEntityPerformance WHERE EntityID=#{EntityID}
        AND SavingCardID IN
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteSaleSavingCardEntityPerformanceByEntityID">
        DELETE
        FROM TB_SaleSavingCardEntityPerformance
        WHERE EntityID = #{EntityID}
    </delete>

    <delete id="deleteSaleSavingCardEntityPerformanceByEntityIDAndCategory">
        DELETE
        TB_SaleSavingCardEntityPerformance FROM  TB_SaleSavingCardEntityPerformance  tsp
        INNER JOIN TB_SavingCard   tp  on  tsp.SavingCardID=tp.ID
        WHERE tsp.EntityID=
        #{EntityID}
        AND
        tp
        .
        SavingCardCategoryID
        =
        #{CategoryID}
    </delete>

    <insert id="insertSaleSavingCardEntityPerformance" parameterType="java.util.List">
        INSERT INTO TB_SaleSavingCardEntityPerformance (
        EntityID,
        SavingCardID,
        PayRate
        ) VALUES
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.EntityID},
            #{item.GoodID},
            cast(#{item.PayRate,jdbcType=DECIMAL}/100 as decimal(5,4))
            )
        </foreach>

    </insert>

</mapper>