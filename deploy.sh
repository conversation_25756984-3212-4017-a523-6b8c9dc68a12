#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

# --- Configuration ---
# Get the directory where the script is located
SCRIPT_DIR=$(cd -- "$(dirname -- "${BASH_SOURCE[0]}")" &> /dev/null && pwd)

# Local project paths (relative to the script's location)
# VUE_PROJECT_PATH="${SCRIPT_DIR}/../sqm-erp-vue"
JAVA_PROJECT_BASE_PATH="${SCRIPT_DIR}"

# Project-specific paths
JAVA_PROJECT_WEB_PATH="${JAVA_PROJECT_BASE_PATH}/zhelian-web"
JAVA_PROJECT_JOB_PATH="${JAVA_PROJECT_BASE_PATH}/zhelian-web-job"
JAVA_PROJECT_MINIPROGRAM_PATH="${JAVA_PROJECT_BASE_PATH}/zhelian-web-miniprogram"
JAVA_PROJECT_MINCROMALL_PATH="${JAVA_PROJECT_BASE_PATH}/zhelian-web-miniprogram-mincroMall"

# Service file paths
SERVICE_BASE_PATH="${SCRIPT_DIR}/deploy/service"
SERVICE_WEB_PATH="${SERVICE_BASE_PATH}/erp.service"
SERVICE_JOB_PATH="${SERVICE_BASE_PATH}/job.service"
SERVICE_MINIPROGRAM_PATH="${SERVICE_BASE_PATH}/mini.service"
SERVICE_MINCROMALL_PATH="${SERVICE_BASE_PATH}/mall.service"

# Nginx configuration paths
NGINX_CONF_BASE_PATH="${SCRIPT_DIR}/deploy/conf"
NGINX_CONF_ERP_PATH="${NGINX_CONF_BASE_PATH}/erp.conf"
NGINX_CONF_MINI_PATH="${NGINX_CONF_BASE_PATH}/mini.conf"
NGINX_CONF_MALL_PATH="${NGINX_CONF_BASE_PATH}/mall.conf"

# SSL certificate path
SSL_CERT_PATH="${JAVA_PROJECT_BASE_PATH}/deploy/ssl"

# Remote server details
REMOTE_USER="root"
REMOTE_HOST="**************"
REMOTE_SSH_TARGET="${REMOTE_USER}@${REMOTE_HOST}"

# Remote application paths
# REMOTE_FRONTEND_PATH="/var/www/erp.37mei.com/html"
REMOTE_BACKEND_BASE_PATH="/opt/zhelian"
REMOTE_SSL_PATH="/etc/nginx/ssl"

# --- Deployment Steps ---

echo "🚀 Starting deployment to erp.37mei.com..."

# 1. Build the frontend application
# echo "
# [1/5] Building frontend application..."
# cd "$VUE_PROJECT_PATH"
# npm run build:prod
# echo "✅ Frontend build complete."

# 2. Build backend applications
echo "
[2/5] Building all backend applications..."
cd "$JAVA_PROJECT_BASE_PATH"
mvn clean package -P prod -DskipTests
echo "✅ All backend applications built."

# Find JARs
JAR_WEB=$(find "$JAVA_PROJECT_WEB_PATH/target" -name "*.jar" | head -n 1)
JAR_JOB=$(find "$JAVA_PROJECT_JOB_PATH/target" -name "*.jar" | head -n 1)
JAR_MINIPROGRAM=$(find "$JAVA_PROJECT_MINIPROGRAM_PATH/target" -name "*.jar" | head -n 1)
JAR_MINCROMALL=$(find "$JAVA_PROJECT_MINCROMALL_PATH/target" -name "*.jar" | head -n 1)

# 3. Prepare directories on the server
echo "
[3/5] Connecting to server and preparing directories..."
# ssh "$REMOTE_SSH_TARGET" "mkdir -p $REMOTE_FRONTEND_PATH && mkdir -p $REMOTE_BACKEND_BASE_PATH && mkdir -p $REMOTE_SSL_PATH"
ssh "$REMOTE_SSH_TARGET" "mkdir -p $REMOTE_BACKEND_BASE_PATH && mkdir -p $REMOTE_SSL_PATH"
echo "✅ Remote directories are ready."

# 4. Deploy frontend files
# echo "
# [4/5] Deploying frontend files..."
# rsync -avz --delete "$VUE_PROJECT_PATH/dist/" "${REMOTE_SSH_TARGET}:${REMOTE_FRONTEND_PATH}/"
# ssh "$REMOTE_SSH_TARGET" "chown -R nginx:nginx ${REMOTE_FRONTEND_PATH}"
# echo "✅ Frontend deployed and permissions updated successfully."

# 5. Deploy backend applications and configure systemd services
echo "
[5/5] Deploying backend and configuring systemd services..."

# Deploy zhelian-web
scp "$JAR_WEB" "${REMOTE_SSH_TARGET}:${REMOTE_BACKEND_BASE_PATH}/zhelian-web.jar"
scp "$SERVICE_WEB_PATH" "${REMOTE_SSH_TARGET}:/etc/systemd/system/erp.service"

# Deploy zhelian-job
scp "$JAR_JOB" "${REMOTE_SSH_TARGET}:${REMOTE_BACKEND_BASE_PATH}/zhelian-web-job.jar"
scp "$SERVICE_JOB_PATH" "${REMOTE_SSH_TARGET}:/etc/systemd/system/job.service"

# Deploy zhelian-web-miniprogram
scp "$JAR_MINIPROGRAM" "${REMOTE_SSH_TARGET}:${REMOTE_BACKEND_BASE_PATH}/zhelian-web-miniprogram.jar"
scp "$SERVICE_MINIPROGRAM_PATH" "${REMOTE_SSH_TARGET}:/etc/systemd/system/mini.service"

# Deploy zhelian-web-miniprogram-mincroMall
scp "$JAR_MINCROMALL" "${REMOTE_SSH_TARGET}:${REMOTE_BACKEND_BASE_PATH}/zhelian-web-miniprogram-mincroMall.jar"
scp "$SERVICE_MINCROMALL_PATH" "${REMOTE_SSH_TARGET}:/etc/systemd/system/mall.service"

# Deploy SSL certificates
echo "Deploying SSL certificates..."
scp "${SSL_CERT_PATH}/erp.37mei.com.pem" "${SSL_CERT_PATH}/erp.37mei.com.key" "${SSL_CERT_PATH}/mini.37mei.com.pem" "${SSL_CERT_PATH}/mini.37mei.com.key" "${REMOTE_SSH_TARGET}:${REMOTE_SSL_PATH}/"

# Deploy Nginx configurations
echo "Deploying Nginx configurations..."
scp "${NGINX_CONF_ERP_PATH}" "${NGINX_CONF_MINI_PATH}" "${NGINX_CONF_MALL_PATH}" "${REMOTE_SSH_TARGET}:/etc/nginx/conf.d/"

# Reload systemd, enable and restart all services
ssh "$REMOTE_SSH_TARGET" << 'EOF'
  echo "Setting permissions for SSL certificates..."
  chown root:root /etc/nginx/ssl/erp.37mei.com.pem /etc/nginx/ssl/erp.37mei.com.key /etc/nginx/ssl/mini.37mei.com.pem /etc/nginx/ssl/mini.37mei.com.key
  chmod 644 /etc/nginx/ssl/erp.37mei.com.pem /etc/nginx/ssl/mini.37mei.com.pem
  chmod 600 /etc/nginx/ssl/erp.37mei.com.key /etc/nginx/ssl/mini.37mei.com.key

  echo "Reloading Nginx..."
  systemctl reload nginx

  echo "Reloading systemd daemon..."
  systemctl daemon-reload

  SERVICES=("erp" "job" "mini" "mall")
  for SERVICE in "${SERVICES[@]}"; do
    echo "Enabling ${SERVICE}.service..."
    systemctl enable ${SERVICE}.service
    echo "Restarting ${SERVICE}.service..."
    systemctl restart ${SERVICE}.service
    echo "Checking ${SERVICE}.service status..."
    sleep 3
    systemctl status ${SERVICE}.service --no-pager
  done

  echo "✅ All backend services managed by systemd."
EOF

echo "
🎉 Deployment finished successfully!"
echo "
---
IMPORTANT: Please ensure your Nginx configuration on the server is set up correctly.

---"